<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the correct mirror image of the given figure which will be formed when mirror PQ is placed on the right side of the figure.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796401.png\" alt=\"rId4\" width=\"100\" height=\"119\"></p>",
                    question_hi: "<p>1. दी गई आकृति के उस सही दर्पण प्रतिबिंब का चयन कीजिए, जो दर्पण PQ को उस आकृति के दाईं ओर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796401.png\" alt=\"rId4\" width=\"100\" height=\"120\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796512.png\" alt=\"rId5\" width=\"101\" height=\"102\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796654.png\" alt=\"rId6\" width=\"100\" height=\"101\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796761.png\" alt=\"rId7\" width=\"101\" height=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796869.png\" alt=\"rId8\" width=\"101\" height=\"100\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796512.png\" alt=\"rId5\" width=\"107\" height=\"108\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796654.png\" alt=\"rId6\" width=\"101\" height=\"102\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796761.png\" alt=\"rId7\" width=\"100\" height=\"99\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796869.png\" alt=\"rId8\" width=\"101\" height=\"100\"></p>"],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796512.png\" alt=\"rId5\" width=\"101\" height=\"102\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773796512.png\" alt=\"rId5\" width=\"101\" height=\"102\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different.<br>(<strong>Note :</strong> The odd one out is not based on the number of consonants/vowels or their&nbsp;position in the letter cluster.)</p>",
                    question_hi: "<p>2. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक समान हैं और एक उनसे असंगत है। उस असंगत अक्षर-समूह का चयन कीजिए।</p>",
                    options_en: ["<p>KPQJ</p>", "<p>TGHS</p>", 
                                "<p>NMNM</p>", "<p>ZABX</p>"],
                    options_hi: ["<p>KPQJ</p>", "<p>TGHS</p>",
                                "<p>NMNM</p>", "<p>ZABX</p>"],
                    solution_en: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773797018.png\" alt=\"rId9\" width=\"101\" height=\"106\">,&nbsp; &nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773797307.png\" alt=\"rId10\" width=\"102\" height=\"104\">,&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773797449.png\" alt=\"rId11\" width=\"109\" height=\"109\"><br>but,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773797660.png\" alt=\"rId12\" width=\"106\" height=\"123\"></p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773797825.png\" alt=\"rId13\" width=\"122\" height=\"129\">,&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773797969.png\" alt=\"rId14\" width=\"127\" height=\"131\">,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798141.png\" alt=\"rId15\" width=\"128\" height=\"127\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798304.png\" alt=\"rId16\" width=\"125\" height=\"146\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. A paper is folded and cut as shown below. How will it appear when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798414.png\" alt=\"rId17\" width=\"309\" height=\"102\"></p>",
                    question_hi: "<p>3. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोले जाने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798414.png\" alt=\"rId17\" width=\"309\" height=\"102\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798579.png\" alt=\"rId18\" width=\"121\" height=\"116\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798683.png\" alt=\"rId19\" width=\"121\" height=\"116\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798781.png\" alt=\"rId20\" width=\"121\" height=\"112\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798922.png\" alt=\"rId21\" width=\"121\" height=\"125\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798579.png\" alt=\"rId18\" width=\"121\" height=\"116\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798683.png\" alt=\"rId19\" width=\"120\" height=\"115\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798781.png\" alt=\"rId20\" width=\"121\" height=\"112\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798922.png\" alt=\"rId21\" width=\"119\" height=\"123\"></p>"],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798922.png\" alt=\"rId21\" width=\"120\" height=\"124\"></p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773798922.png\" alt=\"rId21\" width=\"120\" height=\"124\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different. <br><strong>Note: </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>4. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एकसमान हैं और एक असमान है। निम्नलिखित में से असमान विकल्प को चुनिए।<br><strong>नोट: </strong>अक्षर समूह में, असंगत व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: ["<p>WUQ</p>", "<p>YWT</p>", 
                                "<p>ZXU</p>", "<p>XVS</p>"],
                    options_hi: ["<p>WUQ</p>", "<p>YWT</p>",
                                "<p>ZXU</p>", "<p>XVS</p>"],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799092.png\" alt=\"rId22\" width=\"138\" height=\"72\">,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799219.png\" alt=\"rId23\" width=\"140\" height=\"73\">,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799335.png\" alt=\"rId24\" width=\"140\" height=\"73\"><br>But, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799522.png\" alt=\"rId25\" width=\"136\" height=\"68\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799092.png\" alt=\"rId22\" width=\"138\" height=\"72\">,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799219.png\" alt=\"rId23\" width=\"140\" height=\"73\">,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799335.png\" alt=\"rId24\" width=\"140\" height=\"73\"><br>लेकिन ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799522.png\" alt=\"rId25\" width=\"136\" height=\"68\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/ deleting/ multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>186 : 103<br>156 : 73</p>",
                    question_hi: "<p>5. उस विकल्&zwj;प का चयन कीजिए जिसमें संख्&zwj;याओं के मध्&zwj;य वही संबंध है जो नीचे दिए गए युग्&zwj;म की संख्&zwj;याओं के मध्&zwj;य है।<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/ घटाना/ गुणा करना आदि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>186 : 103<br>156 : 73</p>",
                    options_en: ["<p>83 : 1</p>", "<p>208 : 127</p>", 
                                "<p>119 : 35</p>", "<p>95 : 12</p>"],
                    options_hi: ["<p>83 : 1</p>", "<p>208 : 127</p>",
                                "<p>119 : 35</p>", "<p>95 : 12</p>"],
                    solution_en: "<p>5.(d) <strong>Logic :- </strong>(1st number - 2nd number ) = 83<br>(186 : 103) :- (186 - 103) = 83<br>(156 : 73) :- (156 - 73) = 83<br>Similarly,<br>(95 : 12) :- (95 - 12) = 83</p>",
                    solution_hi: "<p>5.(d)<strong> तर्क :-</strong> (पहली संख्या - दूसरी संख्या) = 83<br>(186 : 103) :- (186 - 103) = 83<br>(156 : 73) :- (156 - 73) = 83<br>इसी प्रकार,<br>(95 : 12) :- (95 - 12) = 83</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the set in which the numbers are related in the same way as are the numbers of the following set.<br>(34, 15, 64)<br>(29, 17, 63)<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>6. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित समुच्चयों की संख्याएँ संबंधित हैं।<br>(34, 15, 64)<br>(29, 17, 63)<br>(<strong>नोट :</strong> पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(44, 32, 98)</p>", "<p>(31, 23, 54)</p>", 
                                "<p>(25, 14, 53)</p>", "<p>(24, 28, 76)</p>"],
                    options_hi: ["<p>(44, 32, 98)</p>", "<p>(31, 23, 54)</p>",
                                "<p>(25, 14, 53)</p>", "<p>(24, 28, 76)</p>"],
                    solution_en: "<p>6.(c) <strong>Logic :</strong>- (3rd number - 1st number) &divide; 2 = 2nd number<br>(34, 15, 64) :- (64 - 34) &divide; 2 &rArr; (30) &divide; 2 = 15<br>(29, 17, 63) :- (63 - 29) &divide; 2 &rArr; (34) &divide; 2 = 17<br>Similarly,<br>(25, 14, 53) :- (53 - 25) &divide; 2 &rArr; (28) &divide; 2 = 14</p>",
                    solution_hi: "<p>6.(c) <strong>तर्क:-</strong> (तीसरी संख्या - पहली संख्या) &divide; 2 = दूसरी संख्या<br>(34, 15, 64) :- (64 - 34) &divide; 2 &rArr; (30) &divide; 2 = 15<br>(29, 17, 63) :- (63 - 29) &divide; 2 &rArr; (34) &divide; 2 = 17<br>इसी प्रकार,<br>(25, 14, 53) :- (53 - 25) &divide; 2 &rArr; (28) &divide; 2 = 14</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language, \'like my pet\' is written as \'nj kj lk\' and \'pet is wet\' is written as \'Ik mk bt\'. How is \'pet\' written in the given language?</p>",
                    question_hi: "<p>7. एक निश्चित कूट भाषा में &lsquo;like my pet&rsquo; को &lsquo;nj kj lk&rsquo; के रूप में लिखा जाता है और &lsquo;pet is wet&rsquo; को &lsquo;lk mk bt&rsquo; के रूप में लिखा जाता है। उसी कूट भाषा में &lsquo;pet&rsquo; को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>Ik</p>", "<p>kj</p>", 
                                "<p>nj</p>", "<p>mk</p>"],
                    options_hi: ["<p>Ik</p>", "<p>kj</p>",
                                "<p>nj</p>", "<p>mk</p>"],
                    solution_en: "<p>7.(a)<br>\'like my pet\' &rarr;&nbsp;\'nj kj lk\'....... (i)<br>\'pet is wet\' &rarr; \'Ik mk bt\' &hellip;&hellip;..(ii)<br>From (i) and (ii) &rsquo;pet&rsquo; and &lsquo;lk&rsquo; are common.<br>So, the code of &lsquo;pet&rsquo; is &lsquo;lk&rsquo;.</p>",
                    solution_hi: "<p>7.(a)<br>\'like my pet\' &rarr; \'nj kj lk\'....... (i)<br>\'pet is wet\' &rarr; \'Ik mk bt\' &hellip;&hellip;..(ii)<br>(i) और (ii) से \'pet\' और \'lk\' उभयनिष्ठ हैं।<br>तो, \'pet\' का कोड \'lk\' है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain code language, &lsquo;FLOAT&rsquo; is coded as &lsquo;14238&rsquo; and &lsquo;BLOAT&rsquo; is coded as &lsquo;23849&rsquo;. What is the code for &lsquo;F&rsquo; in the given code language?</p>",
                    question_hi: "<p>8. एक निश्चित कूट भाषा में, &lsquo;FLOAT&rsquo; को &lsquo;14238&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;BLOAT&rsquo; को &lsquo;23849&rsquo; के रूप में कूटबद्ध किया जाता है। इसी कूट भाषा में &lsquo;F&rsquo; के लिए कूट क्या होगा?</p>",
                    options_en: ["<p>4</p>", "<p>2</p>", 
                                "<p>1</p>", "<p>9</p>"],
                    options_hi: ["<p>4</p>", "<p>2</p>",
                                "<p>1</p>", "<p>9</p>"],
                    solution_en: "<p>8.(c)<br>&lsquo;FLOAT&rsquo; &rarr; &lsquo;14238&rsquo; &hellip;&hellip;&hellip; (i)<br>&lsquo;BLOAT&rsquo; &rarr; &lsquo;23849&rsquo; ........(ii)<br>From (i) and (ii) L, O, A, T and 2, 3, 4, 8 are common. <br>So, the code of &lsquo;F&rsquo; is &lsquo;1&rsquo;</p>",
                    solution_hi: "<p>8.(c)<br>&lsquo;FLOAT&rsquo; &rarr; &lsquo;14238&rsquo; &hellip;&hellip;&hellip; (i)<br>&lsquo;BLOAT&rsquo; &rarr; &lsquo;23849&rsquo; ........(ii)<br>(i) और (ii) से L, O, A, T और 2, 3, 4, 8 उभयनिष्ठ हैं। <br>तो, \'F\' का कोड \'1\' है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. What should come in place of the question mark (?) in the given series based on the English alphabetical order? <br>EBF, IGG, MLH, QQI ,?</p>",
                    question_hi: "<p>9. अंग्रेजी वर्णानुक्रम के आधार पर दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>EBF, IGG, MLH, QQI ,?</p>",
                    options_en: ["<p>SUI</p>", "<p>TUJ</p>", 
                                "<p>SUJ</p>", "<p>UVJ</p>"],
                    options_hi: ["<p>SUI</p>", "<p>TUJ</p>",
                                "<p>SUJ</p>", "<p>UVJ</p>"],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799707.png\" alt=\"rId26\" width=\"243\" height=\"85\"></p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799707.png\" alt=\"rId26\" width=\"243\" height=\"85\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. What should come in place of the question mark (?) in the given series based on the English alphabetical order?<br>CVF, GTJ, KRN, ?, SNV</p>",
                    question_hi: "<p>10. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई शृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>CVF, GTJ, KRN, ?, SNV</p>",
                    options_en: ["<p>OPR</p>", "<p>ROQ</p>", 
                                "<p>OPS</p>", "<p>PRO</p>"],
                    options_hi: ["<p>OPR</p>", "<p>ROQ</p>",
                                "<p>OPS</p>", "<p>PRO</p>"],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799829.png\" alt=\"rId27\" width=\"399\" height=\"101\"></p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773799829.png\" alt=\"rId27\" width=\"399\" height=\"101\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>Some lights are fans.<br>Some fans are beds.<br><strong>Conclusions:</strong><br>(I) Some lights are beds.<br>(II) All beds are fans.</p>",
                    question_hi: "<p>11. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन:</strong><br>कुछ लाइट, पंखे हैं।<br>कुछ पंखे, पलंग हैं।<br><strong>निष्कर्ष:</strong><br>(I) कुछ लाइट, पलंग हैं।<br>(II) सभी पलंग, पंखे हैं।</p>",
                    options_en: ["<p>Neither conclusion I nor II follows</p>", "<p>Both conclusion I and II follow</p>", 
                                "<p>Only conclusion I follows</p>", "<p>Only conclusion II follows</p>"],
                    options_hi: ["<p>न तो निष्&zwj;कर्ष I और न ही II अनुसरण करता है</p>", "<p>निष्&zwj;कर्ष I और II दोनों अनुसरण करतेहैं</p>",
                                "<p>केवल निष्&zwj;कर्ष I अनुसरण करता है</p>", "<p>केवल निष्&zwj;कर्ष II अनुसरण करता है</p>"],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800039.png\" alt=\"rId28\" width=\"281\" height=\"82\"><br>Neither conclusion I nor II follows</p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800210.png\" alt=\"rId29\" width=\"333\" height=\"97\"><br>न तो निष्&zwj;कर्ष I और न ही II अनुसरण करता है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. In the following question some statements are given and some conclusions based on those statements. You have to take the given statements to be true even if they seem to be at variance from commonly known facts. Read all the conclusions carefully and then decide which of the given conclusions logically follows from the given statements.<br><strong>Statements :</strong><br>I. All L are V.<br>II. No L is J.<br><strong>Conclusions :</strong><br>I. All V are L.<br>II. All J are V.</p>",
                    question_hi: "<p>12. निम्नलिखित प्रश्न में कुछ कथन और उन कथनों के आधार पर कुछ निष्कर्ष दिए गए हैं। आपको दिए गए कथनों को सत्य मानना है, चाहे वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हो। सभी निष्कर्षों को ध्यानपूर्वक पढ़िए और निश्चय कीजिए कि दिए गए निष्कर्षों में से कौन-सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन :</strong><br>I. सभी L, V हैं।<br>II. कोई L, J नहीं है।<br><strong>निष्कर्ष :</strong><br>I. सभी V, L हैं।<br>II. सभी J, V हैं।</p>",
                    options_en: ["<p>Only conclusion II follows</p>", "<p>Only conclusion I follows</p>", 
                                "<p>Both conclusions I and II follow</p>", "<p>None of the conclusions follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष ॥ अनुसरण करता है</p>", "<p>केवल निष्कर्ष । अनुसरण करता है</p>",
                                "<p>निष्कर्ष । और॥ दोनों अनुसरण करते हैं</p>", "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>"],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800356.png\" alt=\"rId30\" width=\"165\" height=\"150\"><br>Neither conclusion follows.</p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800356.png\" alt=\"rId30\" width=\"181\" height=\"165\"><br>कोई भी निष्कर्ष अनुसरण नहीं करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. &lsquo;A &times; B&rsquo; means &lsquo;A is B&rsquo;s mother&rsquo;. <br>&lsquo;A &divide; B&rsquo; means &lsquo;A is B&rsquo;s son&rsquo;. <br>&lsquo;A + B&rsquo; means &lsquo;A is B&rsquo;s husband&rsquo;. <br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is B&rsquo;s sister&rsquo;. <br>Using the same meaning of the mathematical operators as given above, in &lsquo;D + E &times; F &ndash; G&rsquo; what is the relation of F with D?</p>",
                    question_hi: "<p>13. A &times; B\' का अर्थ है \'A, B की माँ है\'। <br>\'A &divide; B\' का अर्थ है \'A, B का पुत्र है\'। <br>\'A + B\' का अर्थ है \'A, B का पति है\'। <br>\'A - B\' का अर्थ है \'A, B की बहन है\'। <br>ऊपर दिए गए गणितीय संकारकों के समान अर्थ का उपयोग करते हुए ज्ञात करें कि \'D + E &times; F - G\' में F का D से क्या संबंध है?</p>",
                    options_en: ["<p>Daughter</p>", "<p>Mother</p>", 
                                "<p>Son</p>", "<p>Sister&rsquo;s daughter</p>"],
                    options_hi: ["<p>पुत्री</p>", "<p>माँ</p>",
                                "<p>पुत्र</p>", "<p>बहन की पुत्री</p>"],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800480.png\" alt=\"rId31\" width=\"203\" height=\"115\"><br>F is D&rsquo;s daughter.</p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800480.png\" alt=\"rId31\" width=\"203\" height=\"115\"><br>F, D की बेटी है.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. How many people like either only cars or only Volkswagen vehicles as per the given Venn diagram? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800627.png\" alt=\"rId32\" width=\"182\" height=\"204\"></p>",
                    question_hi: "<p>14. दिए गए वेन आरेख के अनुसार कितने लोग या तो केवल कार या केवल फॉक्सवैगन वाहन पसंद करते हैं? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800756.png\" alt=\"rId33\" width=\"125\" height=\"146\"> <br>Black Vehicles - काला वाहन, Car कार, Volkswagon Vehicle - फॉक्सवैगन वाहन</p>",
                    options_en: ["<p>85</p>", "<p>66</p>", 
                                "<p>28</p>", "<p>75</p>"],
                    options_hi: ["<p>85</p>", "<p>66</p>",
                                "<p>28</p>", "<p>75</p>"],
                    solution_en: "<p>14.(a) The number of people likes either only cars or only Volkswagon = 47 + 38 = 85</p>",
                    solution_hi: "<p>14.(a) या तो केवल कारें या केवल वोक्सवैगन पसंद करने वाले लोगों की संख्या = 47 + 38 = 85</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. From the option figures given below, select the figure in which the question figure is hidden/embedded. (Rotation is not allowed)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800879.png\" alt=\"rId34\" width=\"116\" height=\"18\"></p>",
                    question_hi: "<p>15. नीचे दी गई विकल्प आकृतियों में से, उस आकृति का चयन कीजिए जिसमें प्रश्न आकृति छिपी/निहित है। (घूर्णन की अनुमति नहीं है)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800879.png\" alt=\"rId34\" width=\"116\" height=\"18\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800992.png\" alt=\"rId35\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801158.png\" alt=\"rId36\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801268.png\" alt=\"rId37\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801409.png\" alt=\"rId38\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773800992.png\" alt=\"rId35\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801158.png\" alt=\"rId36\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801268.png\" alt=\"rId37\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801409.png\" alt=\"rId38\"></p>"],
                    solution_en: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801516.png\" alt=\"rId39\" width=\"100\" height=\"102\"></p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801516.png\" alt=\"rId39\" width=\"100\" height=\"102\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In a certain code language <br>A + B means &lsquo;A is the mother of B&rsquo; <br>A &ndash; B means &lsquo;A is the sister of B&rsquo; <br>A &times; B means &lsquo;A is the wife of B&rsquo; <br>A &divide; B means &lsquo;A is the son of B&rsquo; <br>Based on the above, how is Z related to W if &lsquo;Z &times; Y &divide; X &minus; V + W&rsquo;?</p>",
                    question_hi: "<p>16. एक निश्चित कूट भाषा में<br>A + B का अर्थ A, B की मां है <br>A &ndash; B का अर्थ A, B की बहन है <br>A &times; B का अर्थ A, B की पत्नी है <br>A &divide; B का अर्थ A, B का बेटा है <br>उपरोक्त के आधार पर, यदि Z &times; Y &divide; X - V + W है, तो Z का W से क्या संबंध है?</p>",
                    options_en: ["<p>Mother&rsquo;s brother&rsquo;s son&rsquo;s wife</p>", "<p>Mother&rsquo;s sister&rsquo;s daughter</p>", 
                                "<p>Mother&rsquo;s sister&rsquo;s son&rsquo;s wife</p>", "<p>Mother&rsquo;s sister&rsquo;s son&rsquo;s daughter</p>"],
                    options_hi: ["<p>मां के भाई के बेटे की पत्नी</p>", "<p>मां की बहन की बेटी</p>",
                                "<p>मां की बहन के बेटे की पत्नी</p>", "<p>मां की बहन के बेटे की बेटी</p>"],
                    solution_en: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801660.png\" alt=\"rId40\" width=\"198\" height=\"106\"><br>Z is the wife of W&rsquo;s mother&rsquo;s sister&rsquo;s son.</p>",
                    solution_hi: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801660.png\" alt=\"rId40\" width=\"198\" height=\"106\"><br>Z, W की माँ की बहन के बेटे की पत्नी है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Eight persons from Reena, Kavita, Raman, Umesh, Rohit, Vikas, Monika and Priya sit around a square table such that two persons sit on each of the sides. All the persons are sitting facing the centre of the table. Reena sits on the immediate right of Rohit and they both sit on the same side. Monika faces the one who is second to the left of Kavita. Monika is sitting fourth to the right of Reena. Two persons sit between Vikas and Umesh (when counted from one side only) and Umesh is adjacent to Rohit. Only one person sits between Monika and Raman (when counted from one side only). Who is sitting second to the left of Raman ?</p>",
                    question_hi: "<p>17. आठ व्यक्ति रीना, कविता, रमन, उमेश, रोहित, विकास, मोनिका और प्रिया, एक वर्गाकार मेज के चारों ओर इस प्रकार बैठे हैं कि दो व्यक्ति प्रत्येक भुजाओं पर बैठे हैं। सभी व्यक्ति मेज के केंद्र की ओर मुख करके बैठे हैं। रीना रोहित के ठीक दाईं ओर बैठी है और वे दोनों एक ही तरफ बैठे हैं। मोनिका उस व्यक्ति की ओर मुख करके बैठी है जो कविता के बाईं ओर से दूसरे स्थान पर बैठा/बैठी है। मोनिका, रीना के दाईं ओर से चौथे स्थान पर बैठी है। विकास और उमेश के बीच में दो व्यक्ति बैठे हैं (जब केवल एक तरफ से गिना जाता है) और उमेश, रोहित के आसन्न बैठा है। मोनिका और रमन के बीच में केवल एक व्यक्ति बैठा है (जब केवल एक तरफ से गिना जाता है)। रमन के बाईं ओर से दूसरे स्थान पर कौन बैठा/बैठी है ?</p>",
                    options_en: ["<p>Rohit</p>", "<p>Reena</p>", 
                                "<p>Monika</p>", "<p>Priya</p>"],
                    options_hi: ["<p>रोहित</p>", "<p>रीना</p>",
                                "<p>मोनिका</p>", "<p>प्रिया</p>"],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801779.png\" alt=\"rId41\" width=\"244\" height=\"173\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773801911.png\" alt=\"rId42\" width=\"235\" height=\"161\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Three different positions of the same dice are given below. Find the number opposite of 1.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802098.png\" alt=\"rId43\" width=\"257\" height=\"86\"></p>",
                    question_hi: "<p>18. नीचे एक ही पासे की तीन स्थितियां दी गई हैं। \'1\' वाले फलक के विपरीत फलक पर क्या आएगा?<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802098.png\" alt=\"rId43\" width=\"257\" height=\"86\"></p>",
                    options_en: ["<p>4</p>", "<p>6</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>4</p>", "<p>6</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>18.(b)<br>In figure I and II , 2 and 3 are common on both dice.<br>So, the opposite of 1 is 6.</p>",
                    solution_hi: "<p>18.(b)<br>आकृति I और II में, दोनों पासों पर 2 और 3 उभयनिस्ट हैं।<br>इसलिए, 1 का विपरीत 6 है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. What should come in place of the question mark (?) in the given series?<br>6, ?, 7, 10, 8, 11</p>",
                    question_hi: "<p>19. दी गई शृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए?<br>6, ?, 7, 10, 8, 11</p>",
                    options_en: ["<p>5</p>", "<p>8</p>", 
                                "<p>12</p>", "<p>9</p>"],
                    options_hi: ["<p>5</p>", "<p>8</p>",
                                "<p>12</p>", "<p>9</p>"],
                    solution_en: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802284.png\" alt=\"rId44\" width=\"310\" height=\"65\"></p>",
                    solution_hi: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802284.png\" alt=\"rId44\" width=\"310\" height=\"65\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. If 67 L 50 M 15 N 6 = 27 and 100 L 50 M 13 N 5 = 85, then 6 L 8 M 4 N 3 = ?</p>",
                    question_hi: "<p>20. यदि 67 L 50 M 15 N 6 = 27 और 100 L 50 M 13 N 5 = 85 है, तो 6 L 8 M 4 N 3 = ?</p>",
                    options_en: ["<p>8</p>", "<p>5</p>", 
                                "<p>7</p>", "<p>2</p>"],
                    options_hi: ["<p>8</p>", "<p>5</p>",
                                "<p>7</p>", "<p>2</p>"],
                    solution_en: "<p>20.(d)<br><strong>Logic:</strong> Interchanging L, M and N with +, -, and &times; respectively, we get <br>67 L 50 M 15 N 6 = 27<br>&rArr; 67 + 50 - 15 &times; 6 = 27<br>100 L 50 M 13 N 5 = 85<br>&rArr; 100 + 50 - 13 &times; 5 = 85<br>Similarly,<br>6 L 8 M 4 N 3<br>&rArr; 6 + 8 - 4 &times; 3 = 2</p>",
                    solution_hi: "<p>20.(d)<br><strong>तर्क: </strong>L, M और N को क्रमशः +, -, और &times; के साथ बदलने पर, हमें मिलता है<br>67 L 50 M 15 N 6 = 27<br>&rArr; 67 + 50 - 15 &times; 6 = 27<br>100 L 50 M 13 N 5 = 85<br>&rArr; 100 + 50 - 13 &times; 5 = 85<br>इसी प्रकार,<br>6 L 8 M 4 N 3<br>&rArr; 6 + 8 - 4 &times; 3 = 2</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. If 8 R 4 S 3 T 2 = 31 and 19 R 11 S 30 T 8 = 187, then 6 R 3 S 8 T 5 = ?</p>",
                    question_hi: "<p>21. यदि 8 R 4 S 3 T 2 = 31 और 19 R 11 S 30 T 8 = 187, तो 6 R 3 S 8 T 5 = ?</p>",
                    options_en: ["<p>15</p>", "<p>18</p>", 
                                "<p>20</p>", "<p>21</p>"],
                    options_hi: ["<p>15</p>", "<p>18</p>",
                                "<p>20</p>", "<p>21</p>"],
                    solution_en: "<p>21.(a) <strong>Logic :- </strong>By replacing R , S and T with &times; , - and + respectively we get,<br>8 R 4 S 3 T 2&nbsp;&rarr; 8 &times; 4 - 3 + 2 = 31<br>19 R 11 S 30 T 8&nbsp;&rarr; 19 &times; 11 - 30 + 8 = 187<br>Similarly,<br>6 R 3 S 8 T 5&nbsp;&rarr; 6 &times; 3 - 8 + 5 = 15</p>",
                    solution_hi: "<p>21.(a)<br><strong>तर्क:-</strong> R, S और T को क्रमशः &times;, - और + से बदलने पर हमें मिलता है,<br>8 R 4 S 3 T 2&nbsp;&rarr; 8 &times; 4 - 3 + 2 = 31<br>19 R 11 S 30 T 8&nbsp;&rarr;19 &times; 11 - 30 + 8 = 187<br>इसी प्रकार,<br>6 R 3 S 8 T 5&nbsp;&rarr; 6 &times; 3 - 8 + 5 = 15</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. After arranging the given words according to dictionary order, which word will come at \'Third\' position ?<br>1. Small<br>2. Smart<br>3. Smack<br>4. Smash<br>5. Smatter</p>",
                    question_hi: "<p>22. नीचे दिए गए शब्दों को अंग्रेजी शब्दकोश के क्रम के अनुसार व्यवस्थित करने के बाद, कोन-सा शब्द \'तीसरे\' स्थान पर आएगा ? <br>1. Small<br>2. Smart<br>3. Smack<br>4. Smash<br>5. Smatter</p>",
                    options_en: ["<p>Smash</p>", "<p>Smart</p>", 
                                "<p>Small</p>", "<p>Smack</p>"],
                    options_hi: ["<p>Smash</p>", "<p>Smart</p>",
                                "<p>Small</p>", "<p>Smack</p>"],
                    solution_en: "<p>22.(b)<br>The correct dictionary sequence is ;<br>Smack &rarr; Small &rarr; Smart &rarr; Smash &rarr; Smatter<br>Hence, the word which will come at 3rd position is &lsquo;Smart&rsquo;</p>",
                    solution_hi: "<p>22.(b)<br>सही शब्दकोश अनुक्रम है;<br>Smack &rarr; Small &rarr; Smart &rarr; Smash &rarr; Smatter<br>अतः वह शब्द जो तीसरे स्थान पर आएगा वह &lsquo;Smart&rsquo; है I</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Study the given pattern carefully and select the number from among the given options that can replace the question mark (?) in it<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802431.png\" alt=\"rId45\" width=\"177\" height=\"88\"></p>",
                    question_hi: "<p>23. दिए गए पैटर्न का सावधानीपूर्वक अध्ययन करें और दिए गए विकल्पों में से संख्या का चयन करें जो इसमें प्रश्न चिह्न (?) को बदल सकते हैं | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802431.png\" alt=\"rId45\" width=\"169\" height=\"84\"></p>",
                    options_en: ["<p>184</p>", "<p>148</p>", 
                                "<p>146</p>", "<p>164</p>"],
                    options_hi: ["<p>184</p>", "<p>148</p>",
                                "<p>146</p>", "<p>164</p>"],
                    solution_en: "<p>23.(a) <strong>Logic :- </strong>(1st num &times; 4) - 2 = 2nd num , (2nd num &times; 2) + 4 = 3rd num <br>(9, 34, 72) : (9 &times; 4) - 2 = 36 - 2 = 34 and (34 &times; 2) + 4 = 68 + 4 = 72<br>(7, 26, 56) : (7 &times; 4) - 2 = 28 - 2 = 26 and&nbsp;(26 &times; 2) + 4 = 52 + 4 = 56 <br>Similarly, (23 &times; 4) - 2 = 92 - 2 = 90 and&nbsp;(90 &times; 2) + 4 = 180 + 4 = 184</p>",
                    solution_hi: "<p>23.(a) <strong>तर्क :-</strong> (पहली संख्या &times; 4) - 2 =&nbsp;दूसरी संख्या , (दूसरी संख्या &times; 2) + 4 = तीसरी संख्या <br>(9, 34, 72) : (9 &times; 4) - 2 = 36 - 2 = 34 और (34 &times; 2) + 4 = 68 + 4 = 72<br>(7, 26, 56) : (7 &times; 4) - 2 = 28 - 2 = 26 और&nbsp;(26 &times; 2) + 4 = 52 + 4 = 56 <br>इसी प्रकार , (23 &times; 4) - 2 = 92 - 2 = 90 और&nbsp;(90 &times; 2) + 4 = 180 + 4 = 184</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the figure that will replace the question mark (?) in the following figure series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802603.png\" alt=\"rId46\" width=\"330\" height=\"84\"></p>",
                    question_hi: "<p>24. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जिसे दी गई आकृति श्रृंखला में प्रश्न चिह्न (?) के स्थान पर रखा जा सकता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802603.png\" alt=\"rId46\" width=\"330\" height=\"84\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802737.png\" alt=\"rId47\" width=\"99\" height=\"101\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802884.png\" alt=\"rId48\" width=\"100\" height=\"101\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803036.png\" alt=\"rId49\" width=\"101\" height=\"103\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803142.png\" alt=\"rId50\" width=\"101\" height=\"102\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802737.png\" alt=\"rId47\" width=\"100\" height=\"102\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773802884.png\" alt=\"rId48\" width=\"100\" height=\"101\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803036.png\" alt=\"rId49\" width=\"100\" height=\"103\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803142.png\" alt=\"rId50\" width=\"99\" height=\"100\"></p>"],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803036.png\" alt=\"rId49\" width=\"99\" height=\"101\"></p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803036.png\" alt=\"rId49\" width=\"99\" height=\"101\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Which answer figure will complete the pattern in the question figure ? (rotation is not allowed)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803309.png\" alt=\"rId51\" width=\"125\" height=\"123\"></p>",
                    question_hi: "<p>25. कौन सी उत्तर आकृति प्रश्न आकृति के पैटर्न को पूरा करेगी? (घुमाव की अनुमति नहीं है)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803309.png\" alt=\"rId51\" width=\"125\" height=\"123\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803510.png\" alt=\"rId52\" width=\"99\" height=\"98\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803644.png\" alt=\"rId53\" width=\"101\" height=\"100\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803800.png\" alt=\"rId54\" width=\"100\" height=\"99\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803927.png\" alt=\"rId55\" width=\"100\" height=\"99\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803510.png\" alt=\"rId52\" width=\"100\" height=\"99\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803644.png\" alt=\"rId53\" width=\"100\" height=\"99\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803800.png\" alt=\"rId54\" width=\"100\" height=\"99\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773803927.png\" alt=\"rId55\" width=\"102\" height=\"101\"></p>"],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773804042.png\" alt=\"rId56\"></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773804042.png\" alt=\"rId56\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Who among the following invented the induction motor?</p>",
                    question_hi: "<p>26. निम्नलिखित में से किसने इंडक्शन मोटर का आविष्कार किया था?</p>",
                    options_en: ["<p>Frank Shuman</p>", "<p>Nikola Tesla</p>", 
                                "<p>John L Baird</p>", "<p>Thomas Savery</p>"],
                    options_hi: ["<p>फ्रैंक शुमन</p>", "<p>निकोला टेस्ला</p>",
                                "<p>जॉन एल बेयर्ड</p>", "<p>थॉमस सेवरी</p>"],
                    solution_en: "<p>26.(b) The first induction motor was invented by the famed Nikola Tesla in 1887 at his workshop on 89 Liberty Street, New York. An induction motor (also known as an asynchronous motor) is a commonly used AC electric motor. It works on the principle of electromagnetic induction. Frank Shuman invented wire glass safety glass in 1892. John L Baird invented the first live working television system on 26 January 1926. Thomas Savery invented the steam engine.</p>",
                    solution_hi: "<p>26.(b) पहली प्रेरण मोटर का आविष्कार प्रसिद्ध निकोला टेस्ला ने 1887 में 89 लिबर्टी स्ट्रीट, न्यूयॉर्क में अपनी कार्यशाला में किया था। एक इंडक्शन मोटर (जिसे एसिंक्रोनस मोटर के रूप में भी जाना जाता है) आमतौर पर इस्तेमाल की जाने वाली एसी इलेक्ट्रिक मोटर है। यह विद्युत चुम्बकीय प्रेरण के सिद्धांत पर काम करता है। फ्रैंक शुमन ने 1892 में वायर ग्लास सेफ्टी ग्लास का आविष्कार किया। जॉन एल बेयर्ड ने 26 जनवरी 1926 को पहली लाइव वर्किंग टेलीविज़न प्रणाली का आविष्कार किया। थॉमस सेवरी ने भाप इंजन का आविष्कार किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. In which year was the Central Board of Revenue Act in India promulgated?</p>",
                    question_hi: "<p>27. भारत में केंद्रीय राजस्व बोर्ड अधिनियम किस वर्ष प्रवर्तित किया गया था ?</p>",
                    options_en: ["<p>1983</p>", "<p>1963</p>", 
                                "<p>1973</p>", "<p>1953</p>"],
                    options_hi: ["<p>1983</p>", "<p>1963</p>",
                                "<p>1973</p>", "<p>1953</p>"],
                    solution_en: "<p>27.(b) <strong>1963.</strong> It provides separate Boards of Revenue for Direct Taxes and for Excise and Customs and to amend certain enactments for the purpose of conferring powers and imposing duties.</p>",
                    solution_hi: "<p>27.(b) <strong>1963.</strong> यह प्रत्यक्ष करों और उत्पाद शुल्क और सीमा शुल्क के लिए राजस्व के अलग-अलग बोर्ड प्रदान करता है और शक्तियों को प्रदान करने और कर्तव्यों को लागू करने के उद्देश्य से कुछ अधिनियमों में संशोधन करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which plateau, with an average elevation of about 600 meters (2000 feet), is bounded by three mountain ranges: the Satpura range to the north and the Eastern and Western Ghats on either side ?</p>",
                    question_hi: "<p>28. लगभग 600 m (2000 फुट) की औसत ऊंचाई वाला कौन सा पठार तीन पर्वत श्रृंखलाओं से घिरा है : उत्तर में सतपुड़ा श्रेणी और दोनों ओर पूर्वी और पश्चिमी घाट?</p>",
                    options_en: ["<p>Marwar Plateau</p>", "<p>Meghalaya Plateau</p>", 
                                "<p>Deccan Plateau</p>", "<p>Chota Nagpur Plateau</p>"],
                    options_hi: ["<p>मारवाड़ का पठार</p>", "<p>मेघालय का पठार</p>",
                                "<p>दक्कन का पठार</p>", "<p>छोटा नागपुर का पठार</p>"],
                    solution_en: "<p>28.(c) <strong>Deccan Plateau </strong>(Peninsular Plateau). <strong>Marwar plateau</strong> (Jodhpur region) is a region of southwestern Rajasthan state in North Western India. <strong>Chota Nagpur plateau</strong> (Jharkhand) is a storehouse of mineral resources such as mica, bauxite, copper, limestone, iron ore and coal. <strong>Meghalaya plateau</strong> is traditionally divided into Garo, Khasi and Jaintia Hills.</p>",
                    solution_hi: "<p>28.(c) <strong>डेक्कन पठार (प्रायद्वीपीय पठार)। मारवाड़ पठार</strong> (जोधपुर क्षेत्र) उत्तर पश्चिमी भारत में दक्षिण-पश्चिमी राजस्थान राज्य का एक क्षेत्र है। <strong>छोटा नागपुर पठार</strong> (झारखंड) अभ्रक, बॉक्साइट, तांबा, चूना पत्थर, लौह अयस्क और कोयले जैसे खनिज संसाधनों का भंडार है।<strong> मेघालय का पठार</strong> परंपरागत रूप से गारो, खासी और जयंतिया पहाड़ियों में विभाजित है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who among the following declares the Minimum Support Price (MSP) for the crops?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन फसलों के लिए न्यूनतम समर्थन मूल्य (MSP) की घोषणा करता है?</p>",
                    options_en: ["<p>Ministry of Rural Development</p>", "<p>Ministry of Consumer Affairs, Food and Public distribution</p>", 
                                "<p>Ministry of Agriculture and Farmers Welfare</p>", "<p>Ministry of Food Processing Industries</p>"],
                    options_hi: ["<p>ग्रामीण विकास मंत्रालय</p>", "<p>उपभोक्ता मामलों के खाद्य और सार्वजनिक वितरण मंत्रालय,</p>",
                                "<p>कृषि और किसान कल्याण मंत्रालय</p>", "<p>खाद्य प्रसंस्करण उद्योग मंत्रालय</p>"],
                    solution_en: "<p>29.(c) MSP is a &ldquo;minimum price&rdquo; for any crop that the government considers as remunerative for farmers and hence deserving of &ldquo;support&rdquo;. The Commission for Agricultural Costs &amp; Prices (CACP) recommends MSPs for 22 mandated crops (14 Kharif, 6 Rabi and 2 other commercial crops) and fair and remunerative price (FRP) for sugarcane. CACP is an attached office of the Ministry of Agriculture and Farmers Welfare.</p>",
                    solution_hi: "<p>29.(c) MSP किसी भी फसल के लिए एक \"न्यूनतम मूल्य\" है जिसे सरकार किसानों के लिए लाभकारी मानती है और इसलिए \"समर्थन\" की पात्र है। कृषि लागत &amp; मूल्य आयोग (CACP) 22 अनिवार्य फसलों (14 खरीफ, 6 रबी और 2 अन्य वाणिज्यिक फसलों) के लिए MSP और गन्ने के लिए उचित और लाभकारी मूल्य (FRP) की सिफारिश करता है। CACP कृषि और किसान कल्याण मंत्रालय का एक संबद्ध कार्यालय है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. ISRO has decommissioned which of the following satellites?</p>",
                    question_hi: "<p>30. ISRO ने निम्नलिखित में से किस उपग्रह को सेवामुक्त किया है?</p>",
                    options_en: ["<p>INSPIRE sat 1</p>", "<p>EOS-04</p>", 
                                "<p>INSAT-4B</p>", "<p>INS-2TD</p>"],
                    options_hi: ["<p>INSPIRE sat 1</p>", "<p>EOS-04</p>",
                                "<p>INSAT-4B</p>", "<p>INS-2TD</p>"],
                    solution_en: "<p>30.(c) <strong>INSAT-4B</strong>. INSAT-4B carried out Post Mission Disposal (PMD) at the end of its service, after which it was discontinued on 24 January. ISRO successfully launched the Polar Satellite Launch Vehicle PSLV-C52 on 14 February. PSLV-C52 mission has placed three&nbsp;satellites in orbit. EOS-04 Radar Imaging Satellite, INS-2TD Technology Demonstrator Satellite, and INSPIRE Sat 1.</p>",
                    solution_hi: "<p>30.(c) <strong>INSAT-4B। </strong>भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO) ने INSAT-4B को सेवामुक्त कर दिया है, इसने अपनी सेवा के अंत में पोस्ट मिशन डिस्पोजल (PMD) किया, जिसके बाद इसे 24 जनवरी को बंद कर दिया गया।इसरो ने 14 फरवरी को ध्रुवीय उपग्रह प्रक्षेपण यान PSLV-C52 का सफलतापूर्वक प्रक्षेपण किया। PSLV-C52 मिशन ने तीन उपग्रहों को कक्षा में स्थापित किया है। EOS-04 रडार इमेजिंग उपग्रह, INS-2TD प्रौद्योगिकी प्रदर्शक उपग्रह, और INSPIRE सैट 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which Act allows persecution of a person who, being abroad, refuses to return to India to face criminal prosecution?</p>",
                    question_hi: "<p>31. कौन सा अधिनियम एक ऐसे व्यक्ति के उत्पीड़न की अनुमति देता है, जो विदेश में होने के कारण आपराधिक मुकदमे का सामना करने के लिए भारत लौटने से इनकार करता है?</p>",
                    options_en: ["<p>Bankers Book Evidence Act, 1891</p>", "<p>Fugitive Economic Offenders (FEO) Act, 2018</p>", 
                                "<p>The Depositories Act, 1996</p>", "<p>International Financial Services Centres Authority Act, 2019</p>"],
                    options_hi: ["<p>बैंकर्स बुक एविडेंस एक्ट, 1891</p>", "<p>भगोड़ा आर्थिक अपराधी (FEO)अधिनियम, 2018</p>",
                                "<p>डिपॉजिटरी एक्ट, 1996</p>", "<p>अंतर्राष्ट्रीय वित्तीय सेवाए केंद्र प्राधिकरण अधिनियम, 2019</p>"],
                    solution_en: "<p>31.(b) Fugitive Economic Offenders (FEO) Act, 2018 &rarr; It seeks to confiscate properties of economic offenders who have left the country to avoid facing criminal prosecution or refuse to return to the country to face prosecution. Fugitive Economic Offender (FEO) &rarr; A person against whom an arrest warrant has been issued for committing an offense listed in the Act and the value of the offense is at least Rs. 100 crore.</p>",
                    solution_hi: "<p>31.(b) भगोड़ा आर्थिक अपराधी (FEO)) अधिनियम, 2018 &rarr; यह उन आर्थिक अपराधियों की संपत्तियों को जब्त करने का प्रयास करता है, जो आपराधिक मुकदमे का सामना करने से बचने के लिए देश छोड़ चुके हैं या अभियोजन का सामना करने के लिए देश लौटने से इनकार करते हैं। भगोड़ा आर्थिक अपराधी (FEO)) &rarr; एक व्यक्ति जिसके खिलाफ अधिनियम में सूचीबद्ध अपराध करने के लिए गिरफ्तारी वारंट जारी किया गया है और अपराध का मूल्य कम से कम 100 करोड़ रुपये है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which district of Odisha has the highest peak of the Eastern Ghats?</p>",
                    question_hi: "<p>32. ओडिशा के किस जिले में पूर्वी घाट की सबसे ऊँची चोटी है?</p>",
                    options_en: ["<p>Koraput</p>", "<p>Balangir</p>", 
                                "<p>Boudh</p>", "<p>Angul</p>"],
                    options_hi: ["<p>कोरापुट</p>", "<p>बलांगीर</p>",
                                "<p>बौध</p>", "<p>अंगुल</p>"],
                    solution_en: "<p>32.(a) Deomali (1672 m), the highest peak of Eastern Ghat, is a mountain peak in the Chandragiri-Pottangi subrange of the Eastern Ghats. It is located near Koraput town in the Koraput district of Odisha, India. Mahendragiri peak (1501 m) situated in the Gajapati district of Odisha is the second highest peak in Odisha. The Eastern Ghats stretch from West Bengal to Tamil Nadu in the South, passing through parts of Karnataka.</p>",
                    solution_hi: "<p>32.(a) देवमाली (1672 मी), पूर्वी घाट की सबसे ऊँची चोटी, पूर्वी घाट की चंद्रगिरि-पोटांगी उपश्रेणी में एक पर्वत शिखर है। यह भारत के ओडिशा के कोरापुट जिले में कोरापुट शहर के पास स्थित है। ओडिशा के गजपति जिले में स्थित महेंद्रगिरि चोटी (1501 मीटर) ओडिशा की दूसरी सबसे ऊंची चोटी है। पूर्वी घाट कर्नाटक के कुछ हिस्सों से गुजरते हुए दक्षिण में पश्चिम बंगाल से तमिलनाडु तक फैला हुआ है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. ________ is a specialized segment of banking operations that helps individuals or organizations to raise capital and provide financial advisory services to them.</p>",
                    question_hi: "<p>33. ________ बैंकिंग संचालन का एक विशेष खंड है जो व्यक्तियों या संगठनों को पूंजी जुटाने और उन्हें वित्तीय सलाहकार सेवाएं प्रदान करने में मदद करता है।</p>",
                    options_en: ["<p>Commercial banking</p>", "<p>Investment banking</p>", 
                                "<p>Corporate finance</p>", "<p>Private Equity</p>"],
                    options_hi: ["<p>वाणिज्यिक बैंकिंग</p>", "<p>निवेश बैंकिंग</p>",
                                "<p>कॉर्पोरेट वित्त</p>", "<p>निजी इक्विटी</p>"],
                    solution_en: "<p>33.(b) <strong>Investment banking</strong> is a type of banking that organizes large, complex financial transactions such as mergers or initial public offer (IPO) underwriting. A <strong>commercial bank</strong> is a financial institution that accepts deposits, offers checking and savings account services, and makes loans. <strong>Corporate finance</strong> refers to activities and transactions related to raising capital to create, develop and acquire a business. <strong>Private equity</strong> typically refers to investment funds, generally organized as limited partnerships, that buy and restructure companies.</p>",
                    solution_hi: "<p>33.(b) <strong>निवेश बैंकिंग</strong> एक प्रकार का बैंकिंग है जो बड़े, जटिल वित्तीय लेनदेन जैसे विलय या प्रारंभिक सार्वजनिक प्रस्ताव (IPO) अंडरराइटिंग का आयोजन करता है। <strong>एक वाणिज्यिक बैंक</strong> एक वित्तीय संस्थान है जो जमा स्वीकार करता है, चेकिंग और बचत खाता सेवाएं प्रदान करता है और ऋण बनाता है। कॉर्पोरेट वित्त एक व्यवसाय बनाने, विकसित करने और प्राप्त करने के लिए पूंजी जुटाने से संबंधित गतिविधियों और लेनदेन को संदर्भित करता है।<strong>निजी इक्विटी</strong> आम तौर पर सीमित भागीदारी के रूप में आयोजित निवेश फंडों को संदर्भित करता है, जो कंपनियों को खरीदते हैं और उनका पुनर्गठन करते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. What special name is given to the frictional force exerted by a fluid?</p>",
                    question_hi: "<p>34. द्रव द्वारा लगाए गए घर्षण बल को क्या विशेष नाम दिया गया है?</p>",
                    options_en: ["<p>Surface tension</p>", "<p>Buoyancy</p>", 
                                "<p>Shear</p>", "<p>Drag</p>"],
                    options_hi: ["<p>पृष्ठ तनाव</p>", "<p>उत्प्लावकता</p>",
                                "<p>अपरूपक बल</p>", "<p>कर्षण</p>"],
                    solution_en: "<p>34.(d) Drag is an example of mechanical force, when a solid body interacts with a fluid (liquid or gas), a force is produced on the solid body which is known as Drag. Surface tension is the tendency of liquid surfaces at rest to shrink into the minimum surface area possible. Buoyancy is the tendency of an object to float in a fluid. Shear force is a force acting in a direction that\'s parallel to (over the top of) a surface or cross section of a body.</p>",
                    solution_hi: "<p>34.(d)<strong> कर्षण</strong> यांत्रिक बल का एक उदाहरण है, जब एक ठोस शरीर तरल (तरल या गैस) के साथ संपर्क करता है, तो ठोस शरीर पर एक बल उत्पन्न होता है जिसे कर्षण के रूप में जाना जाता है। <strong>पृष्ठ तनाव</strong> तरल सतहों की प्रवृत्ति है जो आराम से न्यूनतम संभव सतह क्षेत्र में सिकुड़ जाती है। <strong>उत्प्लावकता</strong> किसी वस्तु की द्रव में तैरने की प्रवृत्ति है। <strong>अपरूपक बल</strong> एक बल है जो एक दिशा में कार्य करता है जो किसी शरीर की सतह या क्रॉस सेक्शन के समानांतर (शीर्ष पर) होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. The Indian revolt started from Meerut on May 10, 1857, and when did it end?</p>",
                    question_hi: "<p>35. भारतीय विद्रोह 10 मई, 1857 को मेरठ से शुरू हुआ और कब समाप्त हुआ?</p>",
                    options_en: ["<p>May 13, 1858 in Calcutta</p>", "<p>At Gwalior on June 20, 1858</p>", 
                                "<p>In Delhi on 18th September, 1859</p>", "<p>15th August, 1860 at Jhansi</p>"],
                    options_hi: ["<p>13 मई, 1858 को कलकत्ता में</p>", "<p>20 जून, 1858 को ग्वालियर में</p>",
                                "<p>18 सितंबर, 1859 को दिल्ली में</p>", "<p>15 अगस्त, 1860 को झांसी में</p>"],
                    solution_en: "<p>35.(b) Indian Revolt began in Meerut on 10 May 1857 and ended in Gwalior on 20 June 1858 by Indian troops (sepoys) in the service of the British East India Company. Important leaders associated with the revolt: Delhi(Bahadur Shah II, General Bakht Khan), Lucknow (Begum Hazrat Mahal, Birjis Qadir, Ahmadullah), Kanpur (Nana Sahib, Rao Sahib, Tantia Tope, Azimullah Khan), Jhansi (Rani Laxmibai), Bihar (Kunwar Singh, Amar Singh), etc.</p>",
                    solution_hi: "<p>35.(b) भारतीय विद्रोह 10 मई 1857 को मेरठ में शुरू हुआ और ब्रिटिश ईस्ट इंडिया कंपनी की सेवा में भारतीय सैनिकों (सिपाहियों) द्वारा 20 जून 1858 को ग्वालियर में समाप्त हुआ। विद्रोह से जुड़े महत्वपूर्ण नेता: दिल्ली (बहादुर शाह द्वितीय, जनरल बख्त खान), लखनऊ (बेगम हजरत महल, बिरजिस कादिर, अहमदुल्ला), कानपुर (नाना साहिब, राव साहिब, तात्या टोपे, अजीमुल्ला खान), झांसी (रानी लक्ष्मीबाई) , बिहार (कुंवर सिंह, अमर सिंह), आदि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which team won the ICC Women&rsquo;s Championship Trophy in 2024 ?</p>",
                    question_hi: "<p>36. 2024 में ICC महिला चैंपियनशिप ट्रॉफी जीतने वाली टीम कौन सी है?</p>",
                    options_en: ["<p>England</p>", "<p>India</p>", 
                                "<p>Australia</p>", "<p>New Zealand</p>"],
                    options_hi: ["<p>इंग्लैंड</p>", "<p>भारत</p>",
                                "<p>ऑस्ट्रेलिया</p>", "<p>न्यूजीलैंड</p>"],
                    solution_en: "<p>36.(c) <strong>Australia.</strong><br>Australia won the ICC Women&rsquo;s Championship Trophy in 2024. The Australian women\'s team delivered an outstanding performance throughout the tournament, securing the championship title. The tournament was played in the 2022-2025 cycle, where Australia finished at the top.</p>",
                    solution_hi: "<p>36.(c) <strong>ऑस्ट्रेलिया ।</strong><br>ऑस्ट्रेलिया ने 2024 में ICC महिला चैंपियनशिप ट्रॉफी जीती। इस टूर्नामेंट में ऑस्ट्रेलियाई महिला टीम ने शानदार प्रदर्शन किया और खिताब अपने नाम किया। यह टूर्नामेंट 2022-2025 के चक्र में खेला गया था, और ऑस्ट्रेलिया ने इसमें शीर्ष स्थान हासिल किया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. In which year were the islands of Laccadive, Minicoy, and Amindivi changed into Union Territory of Lakshadweep by an Act of Parliament?</p>",
                    question_hi: "<p>37. लक्षद्वीप, मिनिकॉय और अमिनदीवी के द्वीपों को किस वर्ष संसद के एक अधिनियम द्वारा लक्षद्वीप के केंद्र शासित प्रदेश में बदल दिया गया था?</p>",
                    options_en: ["<p>1972</p>", "<p>1971</p>", 
                                "<p>1974</p>", "<p>1973</p>"],
                    options_hi: ["<p>1972</p>", "<p>1971</p>",
                                "<p>1974</p>", "<p>1973</p>"],
                    solution_en: "<p>37.(d) Formerly the Union Territory of Lakshadweep was known as Laccadive, Minicoy, and Amindivi Islands, a name that was changed to Lakshadweep by an act of Parliament in 1973. Lakshadweep &rarr; India&rsquo;s smallest Union Territory Lakshadweep. An archipelago consisting of 36 islands with an area of 32 sq km. The capital is Kavaratti. Situated in the South-West of India in the Arabian Sea.</p>",
                    solution_hi: "<p>37.(d) पूर्व में लक्षद्वीप के केंद्र शासित प्रदेश को लक्षद्वीप, मिनिकॉय और अमीनदीवी द्वीप समूह के रूप में जाना जाता था, एक नाम जिसे 1973 में संसद के एक अधिनियम द्वारा लक्षद्वीप में बदल दिया गया था। लक्षद्वीप &rarr; भारत का सबसे छोटा केंद्र शासित प्रदेश लक्षद्वीप। 32 वर्ग किमी के क्षेत्र के साथ 36 द्वीपों वाला एक द्वीपसमूह। राजधानी कवारत्ती है। भारत के दक्षिण-पश्चिम में अरब सागर में स्थित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Mekhela Chador\' is a traditional dress from the state of:</p>",
                    question_hi: "<p>38. \'मेखला चादर\' किस राज्य की एक पारंपरिक पोशाक है ?</p>",
                    options_en: ["<p>Tripura</p>", "<p>Odisha</p>", 
                                "<p>West Bengal</p>", "<p>Assam</p>"],
                    options_hi: ["<p>त्रिपुरा</p>", "<p>ओडिशा</p>",
                                "<p>पश्चिम बंगाल</p>", "<p>असम</p>"],
                    solution_en: "<p>38.(d) <strong>Assam. Traditional dresses:- Assam</strong> &rarr; Dhoti Gamosa (men), Mekhela Chador (women). <strong>Odisha </strong>&rarr; Dhoti Kurta, Ghamcha (men), Bomkai Sari, Ikat Sari, Sambalpuri Sari (women).<strong> West Bengal</strong> &rarr; Dhoti-Kurta (men), Baluchari Sari, Kantha Sari, Jamdani Sari, Tant Saree (women). <strong>Tripura</strong> &rarr; Duti borok (men), Rignai (women).</p>",
                    solution_hi: "<p>38.(d) <strong>असम। पारंपरिक परिधान (dresses):- असम</strong> &rarr; धोती गमोसा (पुरुष) मेखला चादोर (महिला)। <strong>ओडिशा</strong> &rarr; धोती कुर्ता, घमचा (पुरुष), बोमकाई साड़ी, इकत साड़ी, संबलपुरी साड़ी (महिला)। <strong>पश्चिम बंगाल </strong>&rarr; धोती-कुर्ता (पुरुष), बलूचरी। साड़ी, कांथा साड़ी, जामदानी साड़ी, तांत साड़ी (महिलाएं)।<strong> त्रिपुरा</strong> &rarr; दुती बोरोक (पुरुष), रिगनाई (महिलाएं)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which of the following conditions could harm tea plantations?</p>",
                    question_hi: "<p>39.निम्नलिखित में से कौन-सी स्थितियाँ चाय बागानों को नुकसान पहुँचा सकती हैं?</p>",
                    options_en: ["<p>Temperature above 35&deg;C</p>", "<p>Rainfall ranging between 150-300 cm</p>", 
                                "<p>Hot and wet climate</p>", "<p>Plantations along shady trees</p>"],
                    options_hi: ["<p>35 डिग्री सेल्सियस से ऊपर तापमान</p>", "<p>150-300 सेमी के बीच वर्षा</p>",
                                "<p>गर्म और आर्द्र जलवायु</p>", "<p>छायादार पेड़ के साथ वृक्षारोपण</p>"],
                    solution_en: "<p>39.(a) Tea grows in a moderately hot and humid climate. Temperature above 32&deg;C is disastrous for the crop if it is accompanied by low humidity. An ambient temperature between 13&deg;C and 28-32&deg;C is conducive to the growth of tea. The Acidic Soil with around 4.5-5.5 pH is most suitable for Tea. India is the largest producer and consumer of black tea in the world. Assam, West Bengal, Tamil Nadu, and Kerala account for about 95 percent of total tea production.</p>",
                    solution_hi: "<p>39.(a) चाय मामूली गर्म और नम जलवायु में उगती है। 32 डिग्री सेल्सियस से ऊपर तापमान फसल के लिए विनाशकारी होता है अगर यह कम आर्द्रता के साथ हो। 13 डिग्री सेल्सियस और 28-32 डिग्री सेल्सियस के बीच परिवेश का तापमान चाय के विकास के लिए अनुकूल होता है। लगभग 4.5-5.5 pH वाली अम्लीय मिट्टी चाय के लिए सबसे उपयुक्त होती है। भारत दुनिया में काली चाय का सबसे बड़ा उत्पादक और उपभोक्ता है। असम, पश्चिम बंगाल, तमिलनाडु और केरल में कुल चाय उत्पादन का लगभग 95 प्रतिशत हिस्सा है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which cell organelle has its own DNA and Ribosomes ?</p>",
                    question_hi: "<p>40. किस कोशिकांग के स्वयं के डीएनए और राइबोसोम होते हैं ?</p>",
                    options_en: ["<p>Leukoplast</p>", "<p>Chromoplast</p>", 
                                "<p>Cell Vacuole</p>", "<p>Mitochondria</p>"],
                    options_hi: ["<p>अवर्णी लवक (ल्यूकोप्लास्ट )</p>", "<p>वर्ण लवक (क्रोमोप्लास्ट)</p>",
                                "<p>कोशिका धानी (Cell Vacuole )</p>", "<p>सूत्रकणिका (माइटोकांड्रिया)</p>"],
                    solution_en: "<p>40.(d) <strong>Mitochondria. </strong>They have 70S ribosomes and single circular chromosomes just like prokaryotes. <strong>Leukoplast &rarr;</strong> A colorless plastid in the cytoplasm of plant cells that makes and stores starch.<strong> Chromoplasts </strong>&rarr; It contains plastids and carotenoids(like yellow, orange and red). <strong>Cell Vacuole &rarr;</strong> a membrane-bound cell organelle.</p>",
                    solution_hi: "<p>40.(d)<strong> माइटोकॉन्ड्रिया।</strong> उनके पास प्रोकैरियोट्स की तरह 70S राइबोसोम और सिंगल सर्कुलर क्रोमोसोम हैं। <strong>ल्यूकोप्लास्ट</strong> &rarr; पादप कोशिकाओं के साइटोप्लाज्म में एक रंगहीन प्लास्टिड जो स्टार्च बनाता और संग्रहीत करता है। <strong>क्रोमोप्लास्ट्स</strong> &rarr; इसमें प्लास्टिड्स और कैरोटीनॉयड (जैसे पीला, नारंगी और लाल) होते हैं। <strong>सेल वैक्यूल</strong> &rarr; एक झिल्ली-बाउंड सेल ऑर्गेनेल।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which amendment limited the size of the council of ministers to 15% of the total size of the house ?</p>",
                    question_hi: "<p>41. किस संशोधन ने मंत्रिपरिषद के आकार को सदन के कुल आकार के 15 % तक सीमित कर दिया ?</p>",
                    options_en: ["<p>74th</p>", "<p>86th</p>", 
                                "<p>91st</p>", "<p>61st</p>"],
                    options_hi: ["<p>74th</p>", "<p>86th</p>",
                                "<p>91st</p>", "<p>61st</p>"],
                    solution_en: "<p>41.(c) <strong>91st. </strong><br><strong>61st &rarr; </strong>The voting age was decreased from 21 to 18 for both Lok Sabha and Legislative Assemblies elections. <strong>86th</strong> &rarr; Elementary Education was made a fundamental right &ndash; Free and compulsory education to children between 6 and 14 years. <strong>74th</strong> &rarr;A new Part IX-A and 12th Schedule were added to the Indian Constitution.</p>",
                    solution_hi: "<p>41.(c) <strong>91वां। </strong><br><strong>61वां </strong>&rarr; लोकसभा और विधान सभा दोनों चुनावों के लिए मतदान की आयु 21 से घटाकर 18 कर दी गई। <strong>86वां </strong>&rarr; प्राथमिक शिक्षा को मौलिक अधिकार बनाया गया &ndash; 6 से 14 वर्ष के बच्चों को निःशुल्क और अनिवार्य शिक्षा। <strong>74वां</strong> &rarr;भारतीय संविधान में एक नया भाग IX-A और 12वीं अनुसूची जोड़ी गई।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Hydroelectric power stations generate electricity by using the force of water that falls into the turbines and spins the shaft. By rotating the shaft of the turbine, the potential energy of water is converted into ________ energy.</p>",
                    question_hi: "<p>42. जल - वैद्युत शक्ति केंद्र (हाइड्रोइलेक्ट्रिक पावर स्टेशन) टर्बाइनों पर पड़ने वाली और शाफ्ट को घुमाने वाली जल की शक्ति का इस्तेमाल करके विद्युत उत्पन्न करते हैं। टर्बाइन के शाफ्ट को घुमाने से पानी की स्थितिज ऊर्जा ________ ऊर्जा में परिवर्तित हो जाती है।</p>",
                    options_en: ["<p>chemical</p>", "<p>thermal</p>", 
                                "<p>gravitational</p>", "<p>kinetic</p>"],
                    options_hi: ["<p>रासायनिक</p>", "<p>तापीय</p>",
                                "<p>गुरुत्वीय</p>", "<p>गतिज</p>"],
                    solution_en: "<p>42.(d) kinetic. Some hydroelectric power stations in India:- Koyna Hydroelectric Project (Maharashtra), Sidrapong Hydroelectric Power Station (West Bengal). Nathpa Jhakri (Himachal Pradesh)Hydroelectric Power plant Salal (Jammu and Kashmir).</p>",
                    solution_hi: "<p>42.(d) <strong>गतिज ।</strong> भारत में कुछ हाइड्रोइलेक्ट्रिक पावर स्टेशन: - कोयना हाइड्रोइलेक्ट्रिक प्रोजेक्ट (महाराष्ट्र), सिद्रपोंग हाइड्रोइलेक्ट्रिक पावर स्टेशन (पश्चिम बंगाल)। नाथपा झाकरी (हिमाचल प्रदेश) हाइड्रोइलेक्ट्रिक पावर प्लांट सलाल (जम्मू और कश्मीर)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Padma Shri awardee Gopal Prasad Dubey was awarded for his contribution to which of the following dances?</p>",
                    question_hi: "<p>43. पद्म श्री पुरस्कार से सम्मानित गोपाल प्रसाद दुबे को निम्नलिखित में से किस नृत्य में उनके योगदान के लिए सम्मानित किया गया?</p>",
                    options_en: ["<p>Sattriya</p>", "<p>Panthi</p>", 
                                "<p>Chhau</p>", "<p>Mohiniyattam</p>"],
                    options_hi: ["<p>सत्त्रिया</p>", "<p>पंथी</p>",
                                "<p>छऊ</p>", "<p>मोहिनीअट्टम</p>"],
                    solution_en: "<p>43.(c) <strong>Chhau Dance</strong>. Chau or Chhau is a semi-classical Indian dance with martial, tribal, and folk origins from Orissa, Jharkhand, and West Bengal. Panthi Dance (Chattisgarh), Sattriya (Assam), Mohiniyattam (Kerala).</p>",
                    solution_hi: "<p>43.(c) <strong>छऊ नृत्य।</strong> चाऊ या छऊ उड़ीसा, झारखंड और पश्चिम बंगाल के मार्शल, आदिवासी और लोक मूल के साथ एक अर्ध-शास्त्रीय भारतीय नृत्य है। पंथी नृत्य (छत्तीसगढ़), सत्त्रिया (असम), मोहिनीअट्टम (केरल)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Who started the Tamil weekly magazine &lsquo;Kudi Arasu&rsquo;?</p>",
                    question_hi: "<p>44. तमिल साप्ताहिक पत्रिका &lsquo;कुडी अरासु&rsquo; की शुरुआत किसने की थी ?</p>",
                    options_en: ["<p>Chinnaswami Subramanya Bharathi</p>", "<p>T M Nair</p>", 
                                "<p>Periyar EV Ramasamy</p>", "<p>C Natesa Mudaliar</p>"],
                    options_hi: ["<p>चिन्नास्वामी सुब्रमण्यम भारती</p>", "<p>टी एम नैयर</p>",
                                "<p>पेरियार ईवी रामास्वामी</p>", "<p>सी नातेसा मुदालियार</p>"],
                    solution_en: "<p>44.(c) <strong>Periyar EV Ramasamy.</strong> Chinnaswami Subramanya Bharathi &rarr; Kuyil Pattu, Panchali Sapatham and Kannan Pattu etc.<strong>T. M. Nair</strong> &rarr; activist of the Dravidian Movement from the Madras Presidency.<strong>C Natesa Mudaliar</strong> &rarr; Founder of the Justice Party and Father of the Dravidian movement.</p>",
                    solution_hi: "<p>44.(c) <strong>पेरियार ईवी रामासामी। </strong><br><strong>चिन्नास्वामी सुब्रमण्य भारती</strong> &rarr; कुयिल पट्टू, पांचाली सपथम और कन्नन पट्टू आदि । <strong>टी एम. नायर</strong> &rarr; मद्रास प्रेसीडेंसी से द्रविड़ आंदोलन के कार्यकर्ता। <strong>सी नटसा मुदलियार</strong> &rarr; ​​जस्टिस पार्टी के संस्थापक और द्रविड़ आंदोलन के जनक।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Who won the men\'s singles title at the India Open 2025 ?</p>",
                    question_hi: "<p>45. भारत ओपन 2025 में पुरुष एकल का खिताब किसने जीता?</p>",
                    options_en: ["<p>Lee Cheuk Yiu</p>", "<p>Kento Momota</p>", 
                                "<p>Viktor Axelsen</p>", "<p>Anthony Sinisuka Ginting</p>"],
                    options_hi: ["<p>ली चुक यिउ</p>", "<p>केंटो मोमोटा</p>",
                                "<p>विक्टर एक्सेलसन</p>", "<p>एंथनी सिनीसुका गिंटिंग</p>"],
                    solution_en: "<p>45.(c) <strong>Viktor Axelsen.</strong> <br>Viktor Axelsen of Denmark clinched the men\'s singles title at the India Open 2025 by defeating Hong Kong\'s Lee Cheuk Yiu with a score of 21-16, 21-8.</p>",
                    solution_hi: "<p>45. (c) <strong>विक्टर एक्सेलसन ।</strong> <br>डेनमार्क के विक्टर एक्सेलसन ने हांगकांग के ली चुक यिउ को 21-16, 21-8 से हराकर भारत ओपन 2025 में पुरुष एकल का खिताब जीता।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. 11 July is observed as ______ every year.</p>",
                    question_hi: "<p>46. 11 जुलाई को हर वर्ष ______ के रूप में मनाया जाता है।</p>",
                    options_en: ["<p>World Health Day</p>", "<p>World Environment Day</p>", 
                                "<p>World Population Day</p>", "<p>World Water Day</p>"],
                    options_hi: ["<p>विश्व स्वास्थ्य दिवस</p>", "<p>विश्व पर्यावरण दिवस</p>",
                                "<p>विश्व जनसंख्या दिवस</p>", "<p>विश्व जल दिवस</p>"],
                    solution_en: "<p>46.(c) World Population day is celebrated on 11 July every year. The theme of World Population Day 2023 is &ldquo;Unleashing the power of gender equality: Uplifting the voices of women and girls to unlock our world\'s infinite possibilities&rdquo;. World Health Day (7 April); World Environment Day (5 June); World Water Day (22 March).</p>",
                    solution_hi: "<p>46.(c) विश्व जनसंख्या दिवस हर वर्ष 11 जुलाई को मनाया जाता है। विश्व जनसंख्या दिवस 2022 का विषय &ldquo;Unleashing the power of gender equality: Uplifting the voices of women and girls to unlock our world\'s infinite possibilities&rdquo; विश्व स्वास्थ्य दिवस (7 अप्रैल); विश्व पर्यावरण दिवस (5 जून); विश्व जल दिवस (22 मार्च)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. The National Sports University, Imphal is a central university established under an Act passed in the year ______.</p>",
                    question_hi: "<p>47. राष्ट्रीय खेल विश्वविद्यालय, इम्फाल वर्ष ______ में पारित एक अधिनियम के तहत स्थापित एक केंद्रीय विश्वविद्यालय है।</p>",
                    options_en: ["<p>2014</p>", "<p>2018</p>", 
                                "<p>2015</p>", "<p>2019</p>"],
                    options_hi: ["<p>2014</p>", "<p>2018</p>",
                                "<p>2015</p>", "<p>2019</p>"],
                    solution_en: "<p>47.(b) The first National Sports University of India is located in Imphal, the capital of Manipur, is established under an Act passed in the year 2018. Every year National Sports Day is celebrated in India on August 29.</p>",
                    solution_hi: "<p>47.(b) भारत का पहला राष्ट्रीय खेल विश्वविद्यालय मणिपुर की राजधानी इम्फाल में स्थित है, जो वर्ष 2018 में पारित एक अधिनियम के तहत स्थापित किया गया है। हर साल 29 अगस्त को भारत में राष्ट्रीय खेल दिवस मनाया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Where has China approved the construction of the world\'s largest dam?</p>",
                    question_hi: "<p>48. चीन ने दुनिया का सबसे बड़ा बांध कहां बनाने की स्वीकृति दी है?</p>",
                    options_en: ["<p>Yangtze River</p>", "<p>Mekong River</p>", 
                                "<p>Yarlung Zangbo (Brahmaputra) in Tibet</p>", "<p>Yellow River</p>"],
                    options_hi: ["<p>यांग्त्जी नदी</p>", "<p>मेकोंग नदी</p>",
                                "<p>यारलुंग ज़ांगबो (ब्रह्मपुत्र) तिब्बत में</p>", "<p>पीली नदी</p>"],
                    solution_en: "<p>48.(c) <strong>Yarlung Zangbo (Brahmaputra) in Tibet.</strong> The dam is being constructed close to the Indian border. The project is estimated to cost $137 billion. The project raises concerns over water security and ecological impact in the region.</p>",
                    solution_hi: "<p>48.(c) <strong>यारलुंग ज़ांगबो (ब्रह्मपुत्र) तिब्बत में।</strong> यह बांध भारतीय सीमा के पास बनाया जा रहा है। इस परियोजना का अनुमानित लागत $137 बिलियन है। यह परियोजना क्षेत्र में जल सुरक्षा और पारिस्थितिकी तंत्र पर प्रभाव को लेकर चिंताएँ उत्पन्न कर रही है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Who was the first person to receive the Sahitya Akademi Fellowship awarded by the Government of India in 1968?</p>",
                    question_hi: "<p>49. 1968 में भारत सरकार द्वारा साहित्य अकादमी फेलोशिप प्राप्त करने वाले पहले व्यक्ति कौन थे?</p>",
                    options_en: ["<p>C. Narayana Reddy</p>", "<p>S. Radhakrishnan</p>", 
                                "<p>V.S. Naipaul</p>", "<p>S. L. Bhyrappa</p>"],
                    options_hi: ["<p>सी नारायण रेड्डी</p>", "<p>एस राधाकृष्णन</p>",
                                "<p>वी.एस. नायपॉल</p>", "<p>एस. एल. भैरप्पा</p>"],
                    solution_en: "<p>49.(b) The first Fellow of the Akademi, S. Radhakrishnan, was elected as a Fellow in 1968, fourteen years after the Akademi was constituted. Mulk Raj Anand was the first Indian English writer to be inducted in 1989 and R. K. Narayan in 1994, the second. It is a literary honor in India bestowed by the Sahitya Akademi, India\'s National Academy of Letters.)</p>",
                    solution_hi: "<p>49.(b) अकादमी के पहले फेलो, एस. राधाकृष्णन को अकादमी के गठन के चौदह वर्ष बाद 1968 में फेलो के रूप में चुना गया था। मुल्क राज आनंद 1989 में शामिल होने वाले पहले भारतीय अंग्रेजी लेखक थे और दूसरे 1994 में आर के नारायण थे। यह साहित्य अकादमी, भारत की राष्ट्रीय साहित्य अकादमी द्वारा भारत में दिया जाने वाला एक साहित्यिक सम्मान है।)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which of the given Articles of the Constitution of India mentions the following? \"There shall be a council of Ministers with the Prime Minister at the head to aid and advise the President who shall in the exercise of his functions, act in accordance with such advice.\"</p>",
                    question_hi: "<p>50. भारत के संविधान के दिए गए किस अनुच्छेद में निम्न का वर्णन है? \"राष्ट्रपति की सहायता करने और उसे सलाह देने के लिए मंत्रिपरिषद होगा, राष्ट्रपति इस मंत्रिपरिषद की सलाह के अनुसार अपने कार्यो का निष्पादन करेंगे\"</p>",
                    options_en: ["<p>Article 71(1)</p>", "<p>Article 76</p>", 
                                "<p>Article 74(1)</p>", "<p>Article 75</p>"],
                    options_hi: ["<p>अनुच्छेद 71(1)</p>", "<p>अनुच्छेद 76</p>",
                                "<p>अनुच्छेद 74(1)</p>", "<p>अनुच्छेद 75</p>"],
                    solution_en: "<p>50.(c) <strong>Articles 74(1). Article 71(1)</strong>- All doubts and disputes relating to the election of the President or the Vice-President shall be inquired into and decided by the Supreme Court. <strong>Article 76</strong>- Attorney-General for India. <strong>Article 75</strong> - Other provisions as to Ministers.</p>",
                    solution_hi: "<p>50.(c)<strong> अनुच्छेद74(1). अनुच्छेद 71(1) </strong>&rarr; राष्ट्रपति या उपराष्ट्रपति के चुनाव से संबंधित सभी संदेहों और विवादों की जांच और निर्णय सर्वोच्च न्यायालय द्वारा किया जाएगा।<strong> अनुच्छेद 76</strong> &rarr; भारत के महान्यायवादी। <strong>अनुच्छेद 75</strong> &rarr; मंत्रियों के बारे में अन्य प्रावधान।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A&rsquo;s marks in Mathematics are directly proportional to practice time. In 6 hours of&nbsp;practice, A gets 70 marks. What should be the practice time (approximately) to get 90&nbsp;Marks ?</p>",
                    question_hi: "<p>51. गणित में A के अंक उसके अभ्यास समय के अनुक्रमानुपाती (directly proportional) है। 6 घंटे&nbsp;के अभ्यास में A को 70 अंक मिलते हैं। 90 अंक प्राप्त करने के लिए उसका अभ्यास समय (लगभग) क्या&nbsp;होना चाहिए ?</p>",
                    options_en: ["<p>7.7 hours</p>", "<p>7 hours</p>", 
                                "<p>8.3 hours</p>", "<p>8 hours</p>"],
                    options_hi: ["<p>7.7 घंटा</p>", "<p>7 घंटा</p>",
                                "<p>8.3 घंटा</p>", "<p>8 घंटा</p>"],
                    solution_en: "<p>51.(a)<br>Practice time to get 70 marks = 6 hours<br>Practice time to get 90 marks = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> &times; 90 = 7.7 hours (approx)</p>",
                    solution_hi: "<p>51.(a)<br>70 अंक प्राप्त करने के लिए अभ्यास का समय = 6 घंटे<br>90 अंक प्राप्त करने के लिए अभ्यास का समय = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> &times; 90 = 7.7 घंटे (लगभग)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. If &theta; is an acute angle and cot&theta; + tan&theta; = 2, then find the value of tan<sup>12</sup>&theta; + cot<sup>12</sup>&theta; + 2 tan<sup>5</sup>&theta;cot<sup>7</sup>&theta;.</p>",
                    question_hi: "<p>52. यदि &theta; न्यून कोण है और cot&theta; + tan&theta; = 2 है, तो tan<sup>12</sup>&theta; + cot<sup>12</sup>&theta; + 2 tan<sup>5</sup>&theta;cot<sup>7</sup>&theta; का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>52.(c)<br>cot&theta; + tan&theta; = 2<br>Let &theta; = 45&deg; <br>cot 45&deg; + tan 45&deg; = 2<br>1 + 1 = 2 &rArr;&nbsp;2 = 2 (LHS = RHS)<br>now, tan<sup>12</sup>&theta; + cot<sup>12</sup>&theta; + 2 tan<sup>5</sup>&theta;cot<sup>7</sup>&theta;<br>&rArr; (tan 45&deg;)<sup>12</sup> + (cot 45&deg;)<sup>12</sup> + 2 (tan 45&deg;)<sup>5</sup> (cot 45&deg;)<sup>7</sup><br>&rArr; 1 + 1 + 2(1)(1) = 4</p>",
                    solution_hi: "<p>52.(c)<br>cot&theta; + tan&theta; = 2<br>माना &theta; = 45&deg; <br>cot 45&deg; + tan 45&deg; = 2<br>1 + 1 = 2 &rArr; 2 = 2 (LHS = RHS)<br>अब , tan<sup>12</sup>&theta; + cot<sup>12</sup>&theta; + 2 tan<sup>5</sup>&theta;cot<sup>7</sup>&theta;<br>&rArr; (tan 45&deg;)<sup>12</sup> + (cot 45&deg;)<sup>12</sup> + 2 (tan 45&deg;)<sup>5</sup> (cot 45&deg;)<sup>7</sup><br>&rArr; 1 + 1 + 2(1)(1) = 4</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Two circles touch each other externally at M. PQ is a direct common tangent to the two&nbsp;circles, P and Q are points of contact and &ang;MPQ = 38&deg;. &ang;MQP is:</p>",
                    question_hi: "<p>53. दो वृत्त M पर एक दूसरे को बाह्य रूप से स्पर्श करते हैं। PQ दोनों वृत्तो पर सीधी उभयनिष्ठ अनुस्पर्श रेखा (direct common tangent) है , P और Q संपर्क बिंदु हैं और &ang;MPQ = 38&deg; है। &ang;MQP ज्ञात करें।</p>",
                    options_en: ["<p>38&deg;</p>", "<p>42&deg;</p>", 
                                "<p>52&deg;</p>", "<p>48&deg;</p>"],
                    options_hi: ["<p>38&deg;</p>", "<p>42&deg;</p>",
                                "<p>52&deg;</p>", "<p>48&deg;</p>"],
                    solution_en: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773804214.png\" alt=\"rId57\" width=\"246\" height=\"136\"><br>Concept used :- if two circle touch each other externally at some point and a direct common tangent is drawn to both circle, the angle subtended by the direct common tangent at the point where two circles touch each other is 90&deg;<br>According to the concept, &ang;PMQ = 90&deg;<br>Hence the &ang;PQM = 90&deg; - 38&deg; = 52&deg;</p>",
                    solution_hi: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773804214.png\" alt=\"rId57\" width=\"248\" height=\"137\"><br>प्रयुक्त अवधारणा:- यदि दो वृत्त किसी बिंदु पर एक-दूसरे को बाह्य रूप से स्पर्श करते हैं और दोनों वृत्तों पर एक सीधी उभयनिष्ठ स्पर्शरेखा खींची जाती है, तो उस बिंदु पर जहां दो वृत्त एक-दूसरे को स्पर्श करते हैं, सीधी उभयनिष्ठ स्पर्शरेखा द्वारा बनाया गया कोण 90&deg; होता है।<br>अवधारणा के अनुसार, &ang;PMQ = 90&deg;<br>अतः, &ang;PQM = 90&deg; - 38&deg; = 52&deg;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The length of the shadow of a vertical pole on the ground is 18m. If the angle of elevation of the sun at that time is &theta;, such that cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math>, then what is the height (in m) of the pole ?</p>",
                    question_hi: "<p>54. भूतल पर एक अधोलंब खम्भे की परछाईं की लंबाई 18 मी है। यदि उस समय सूर्य की ऊँचाई का उन्नयन कोण &theta; है , जैसे कि cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math> है, तो खम्भे की ऊँचाई (मीटर में) क्या है?</p>",
                    options_en: ["<p>18</p>", "<p>9</p>", 
                                "<p>7.5</p>", "<p>12</p>"],
                    options_hi: ["<p>18</p>", "<p>9</p>",
                                "<p>7.5</p>", "<p>12</p>"],
                    solution_en: "<p>54.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773804375.png\" alt=\"rId58\" width=\"156\" height=\"129\"><br>It is given that <br>cos&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>base</mi><mi>hypotenuse</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math><br>Using pythagoras theorem, <br>we have AB = 5 unit<br>ATQ, 12 unit &rarr; 18 m<br>Then, 5 unit &rarr; <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 5 = 7.5 m</p>",
                    solution_hi: "<p>54.(c)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773804375.png\" alt=\"rId58\" width=\"156\" height=\"129\"><br>यह दिया गया है कि <br>cos&theta; = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;</mo></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math><br>पाइथागोरस प्रमेय का उपयोग करने पर, हमारे पास AB = 5 इकाई है<br>प्रश्न के अनुसार, 12 इकाई ----------- 18 मीटर<br>तब, 5 इकाई ----------- <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 5 = 7.5 m</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Simplify the following : <br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>44</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math> = ?</p>",
                    question_hi: "<p>55. निम्नलिखित व्यंजक को सरल कीजिए।<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>44</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math> = ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>55.(b)<br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>44</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math> = ?<br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>33</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>55.(b)<br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>44</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math> = ?<br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>33</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. If the brick size is 25 cm &times; 12 cm &times; 9 cm, then how many bricks are required to construct a wall of length 10 m, breadth 22.5 cm and height 6 m?</p>",
                    question_hi: "<p>56. यदि ईंट का आकार 25 cm &times; 12 cm &times; 9 cm है, तो 10 m लंबी, 22.5 cm चौड़ी और 6 m ऊंची दीवार बनाने के लिए कितनी ईंटों की आवश्यकता होगी?</p>",
                    options_en: ["<p>4500</p>", "<p>6000</p>", 
                                "<p>8000</p>", "<p>5000</p>"],
                    options_hi: ["<p>4500</p>", "<p>6000</p>",
                                "<p>8000</p>", "<p>5000</p>"],
                    solution_en: "<p>56.(d)<br>Required bricks = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mo>(</mo><mn>6</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>9</mn></mrow></mfrac></math> <br>= 40 &times; 2.5 &times; 50 <br>= 5000</p>",
                    solution_hi: "<p>56.(d)<br>आवश्यक ईंटें = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo><mo>&#215;</mo><mn>22</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mo>(</mo><mn>6</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>&#215;</mo><mn>12</mn><mo>&#215;</mo><mn>9</mn></mrow></mfrac></math> <br>= 40 &times; 2.5 &times; 50 <br>= 5000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. From a circle, a sector with a central angle of 30&deg; is cut off. If the length of the arc of this sector is 5.5 cm, then find the radius (in cm) of the circle. <br>[Use &pi;&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>]</p>",
                    question_hi: "<p>57. एक वृत्त से 30&deg; के केंद्रीय कोण वाला त्रिज्यखंड काटा जाता है। यदि इस त्रिज्यखंड के चाप की लंबाई 5.5 cm है, तो वृत्त की त्रिज्या (cm में) ज्ञात कीजिए।<br>[&pi;&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;का उपयोग कीजिए | ]</p>",
                    options_en: ["<p>12</p>", "<p>10</p>", 
                                "<p>11</p>", "<p>10.5</p>"],
                    options_hi: ["<p>12</p>", "<p>10</p>",
                                "<p>11</p>", "<p>10.5</p>"],
                    solution_en: "<p>57.(d)<br>Length of the arc = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2&pi;r<br>5.5 = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; r<br>r = <math display=\"inline\"><mfrac><mrow><mn>42</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> = 10.5 cm</p>",
                    solution_hi: "<p>57.(d)<br>त्रिज्यखंड के चाप की लंबाई = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2&pi;r<br>5.5 = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; r<br>r = <math display=\"inline\"><mfrac><mrow><mn>42</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> = 10.5 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. If the sides of a triangle are 7, 12 and x, and x is an integer, then find the number of possible values of x.</p>",
                    question_hi: "<p>58. यदि किसी त्रिभुज की भुजाएँ 7, 12 और x&nbsp;हैं, और x एक पूर्णांक है, तो x के संभावित मानों की संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>14</p>", "<p>13</p>", 
                                "<p>15</p>", "<p>12</p>"],
                    options_hi: ["<p>14</p>", "<p>13</p>",
                                "<p>15</p>", "<p>12</p>"],
                    solution_en: "<p>58.(b)<br>The sum of two sides is always greater than the 3rd side and the difference is always smaller than the 3rd side.<br>Hence, <br>(7 + 12) &gt;&nbsp;x &gt; (12 - 7)<br>19 &gt;&nbsp;x &gt; 5<br>So, possible values of x&nbsp;= 6,7,8,9,10,11,12,13,14,15,16,17,18 (total 13 values)</p>",
                    solution_hi: "<p>58.(b)<br>दो भुजाओं का योग हमेशा तीसरी भुजा से अधिक होता है और अंतर हमेशा तीसरी भुजा से छोटा होता है।<br>इस तरह, <br>(7 + 12) &gt;&nbsp;x &gt; (12 - 7)<br>19 &gt;&nbsp;x &gt; 5<br>तो, x&nbsp;के संभावित मान = 6,7,8,9,10,11,12,13,14,15,16,17,18 (कुल 13 मान)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. If a + b + c = 0, then the value of (a<sup>2</sup> + b<sup>2</sup> + 2ab) is equal to:</p>",
                    question_hi: "<p>59. यदि a + b + c = 0 है, तो (a<sup>2</sup> + b<sup>2</sup> + 2ab) का मान क्या है ?</p>",
                    options_en: ["<p>c<sup>2</sup></p>", "<p>- c<sup>2</sup></p>", 
                                "<p>c</p>", "<p>- c</p>"],
                    options_hi: ["<p>c<sup>2</sup></p>", "<p>- c<sup>2</sup></p>",
                                "<p>c</p>", "<p>- c</p>"],
                    solution_en: "<p>59.(a)<strong> Given: </strong>a + b + c = 0<br>a + b = -c<br>On squaring both side we get,<br>a<sup>2</sup> + b<sup>2</sup> + 2ab = (-c)<sup>2</sup><br>a<sup>2</sup> + b<sup>2</sup> + 2ab = c<sup>2</sup></p>",
                    solution_hi: "<p>59.(a) <strong>दिया गया है </strong>: a + b + c = 0<br>a + b = -c<br>दोनों पक्षों का वर्ग करने पर हमें प्राप्त होता है,<br>a<sup>2</sup> + b<sup>2</sup> + 2ab = (-c)<sup>2</sup><br>a<sup>2</sup> + b<sup>2</sup> + 2ab = c<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Divide some money in the ratio Ravi, Reeta and Rahul that 5 (Part of Ravi) = 3 (Part of Reeta) = 11 (Part of Rahul). The money ratio of Ravi : Reeta : Rahul is equal to:</p>",
                    question_hi: "<p>60. कुछ धनराशि को रवि, रीता और राहुल के बीच इस अनुपात में बांटिए कि 5 (रवि का हिस्सा) = 3 (रीता का हिस्सा) = 11 (राहुल का हिस्सा) हो। रवि : रीता : राहुल की धनराशि का अनुपात _________ के बराबर है।</p>",
                    options_en: ["<p>11 : 5 : 3</p>", "<p>11 : 33 : 15</p>", 
                                "<p>5 : 11 : 3</p>", "<p>33 : 55 : 15</p>"],
                    options_hi: ["<p>11 : 5 : 3</p>", "<p>11 : 33 : 15</p>",
                                "<p>5 : 11 : 3</p>", "<p>33 : 55 : 15</p>"],
                    solution_en: "<p>60.(d)<br>5 (Part of Ravi) = 3 (Part of Reeta) =11 (Part of Rahul)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>ravi</mi><mi>reeta</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>reeta</mi><mi>rahul</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math><br>Ratio - ravi : reeta : rahul<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; &nbsp; :&nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;<strong>5</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<strong> 11&nbsp;</strong> &nbsp;:&nbsp; &nbsp;11&nbsp; &nbsp; :&nbsp; &nbsp;3<br>-------------------------------------<br>Final -&nbsp; 33&nbsp; &nbsp;:&nbsp; &nbsp;55&nbsp; &nbsp;: 15<br><strong>Short tricks </strong>:- 5 (Part of Ravi) = 3 (Part of Reeta) = 11 (Part of Rahul)<br>LCM of 5, 3, 11 = 5 &times; 3 &times; 11<br>Required ratio = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>3</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>11</mn></mfrac></math> = 33 : 55 : 15</p>",
                    solution_hi: "<p>60.(d)<br>5 (रवि का भाग) = 3 (रीता का भाग) = 11 (राहुल का भाग)<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2352;&#2357;&#2367;</mi><mi>&#2352;&#2368;&#2340;&#2366;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>, <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2352;&#2368;&#2340;&#2366;</mi><mi>&#2352;&#2366;&#2361;&#2369;&#2354;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math><br>अनुपात - रवि : रीता&nbsp; : राहुल<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; :&nbsp; &nbsp; 5&nbsp; &nbsp;:&nbsp; &nbsp; <strong>5</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong>11</strong>&nbsp; :&nbsp; 11&nbsp; &nbsp;:&nbsp; &nbsp; 3<br>-------------------------------------<br>अंतिम -&nbsp; 33&nbsp; :&nbsp; &nbsp;55&nbsp; &nbsp;:&nbsp; 15<br><strong>शॉर्ट ट्रिक्स :-</strong> 5 (रवि का हिस्सा) = 3 (रीता का हिस्सा) = 11 (राहुल का हिस्सा)<br>5, 3, 11 का LCM = 5 &times; 3 &times; 11<br>आवश्यक अनुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>3</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>11</mn></mfrac></math> = 33 : 55 : 15</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Ramu mixes 2 litres of water at 100&deg; C and 18 litres of water at 32&deg; C. What temperature will the water have after mixing?</p>",
                    question_hi: "<p>61. रामू 2 लीटर पानी को 100&deg;C पर और 18 लीटर पानी को 32&deg;C पर मिलाता है। दोनों को मिलाने के बाद पानी का तापमान क्या होगा?</p>",
                    options_en: ["<p>40&deg;C</p>", "<p>20&deg;C</p>", 
                                "<p>38.8&deg;C</p>", "<p>66&deg;C</p>"],
                    options_hi: ["<p>40&deg;C</p>", "<p>20&deg;C</p>",
                                "<p>38.8&deg;C</p>", "<p>66&deg;C</p>"],
                    solution_en: "<p>61.(c)<br>Required temperature = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>18</mn><mo>&#215;</mo><mn>32</mn><mo>)</mo></mrow><mrow><mn>2</mn><mo>+</mo><mn>18</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>+</mo><mn>576</mn></mrow><mn>20</mn></mfrac></math> = 38.8&deg; C</p>",
                    solution_hi: "<p>61.(c)<br>आवश्यक तापमान = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>100</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>18</mn><mo>&#215;</mo><mn>32</mn><mo>)</mo></mrow><mrow><mn>2</mn><mo>+</mo><mn>18</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>+</mo><mn>576</mn></mrow><mn>20</mn></mfrac></math> = 38.8&deg; C</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. P completes a work in 12 days and Q in 24 days. In how many days P and Q working together can complete the same work ?</p>",
                    question_hi: "<p>62. P किसी काम को 12 दिनों में और Q उसी काम को 24 दिनों में पूरा करता है। P और Q साथ मिलकर काम करते हुए उसी काम को कितने दिनों में पूरा कर सकते हैं?</p>",
                    options_en: ["<p>16 days</p>", "<p>12 days</p>", 
                                "<p>6 days</p>", "<p>8 days</p>"],
                    options_hi: ["<p>16 दिन</p>", "<p>12 दिन</p>",
                                "<p>6 दिन</p>", "<p>8 दिन</p>"],
                    solution_en: "<p>62.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773804572.png\" alt=\"rId59\" width=\"153\" height=\"129\"><br>Time taken by (P + Q) to complete the whole work = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 8 days</p>",
                    solution_hi: "<p>62.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773804740.png\" alt=\"rId60\" width=\"165\" height=\"162\"><br>पूरे कार्य को पूरा करने में (P + Q) द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 8 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Let t = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>, then the value of the expression t<sup>3</sup> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>)<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> t is:</p>",
                    question_hi: "<p>63. मान लीजिए t = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> है, तो व्यंजक t<sup>3</sup> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>)<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math>t का मान कितना है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>1</p>", 
                                "<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>1</p>",
                                "<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>63.(b) <br>t<sup>3</sup> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>)<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math>t<br>= (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>)<sup>3</sup> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>)<sup>3 </sup>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>125</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>125</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>125</mn></mfrac></math> = 1</p>",
                    solution_hi: "<p>63.(b) <br>t<sup>3</sup> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>)<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math>t<br>= (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>)<sup>3</sup> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>)<sup>3 </sup>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>125</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>125</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>125</mn></mfrac></math> = 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Pipe A usually fills a tank in 6 hours. But due to a leak at the bottom of the tank, it takes extra 2 hours to fill the tank. If the tank is full, then how much time will it take to get emptied due to the leak?</p>",
                    question_hi: "<p>64. पाइप A आमतौर पर एक टैंक को 6 घंटे में भरता है। लेकिन टैंक के निचले भाग में रिसाव के कारण, टैंक को भरने में 2 घंटे अतिरिक्त लगते हैं। यदि टैंक भरा हुआ है तो रिसाव के कारण इसे खाली होने में कितना समय लगेगा?</p>",
                    options_en: ["<p>16 hours</p>", "<p>20 hours</p>", 
                                "<p>12 hours</p>", "<p>24 hours</p>"],
                    options_hi: ["<p>16 घंटे</p>", "<p>20 घंटे</p>",
                                "<p>12 घंटे</p>", "<p>24 घंटे</p>"],
                    solution_en: "<p>64.(d)<br>Let L be the leakage pipe.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773804920.png\" alt=\"rId61\" width=\"206\" height=\"175\"><br>Efficiency of L = 3 - 4 = - 1 unit<br>Time taken to empty the tank due to leak = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 hrs</p>",
                    solution_hi: "<p>64.(d)<br>माना , लीकेज पाइप = L<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773805087.png\" alt=\"rId62\" width=\"166\" height=\"146\"><br>L की दक्षता = 3 - 4 = - 1 इकाई<br>रिसाव के कारण टैंक को खाली करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Study the given table and answer the question that follows.<br>Percentage of marks obtained by 5 students in different subjects<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773805276.png\" alt=\"rId63\" width=\"391\" height=\"157\"> <br>The marks obtained by Sohan in maths are _______ than that obtained by Shyam in maths.</p>",
                    question_hi: "<p>65. दी गई तालिका का अध्ययन कीजिए और दिए गए प्रश्न का उत्तर दीजिए। <br>5 विद्यार्थियों द्वारा विभिन्न विषयों में प्राप्त अंकों का प्रतिशत<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773805446.png\" alt=\"rId64\" width=\"371\" height=\"199\"> <br>सोहन द्वारा गणित में प्राप्त किया गया अंक, श्याम द्वारा गणित में प्राप्त किए गए अंकों से ____ है।</p>",
                    options_en: ["<p>40 more</p>", "<p>40 less</p>", 
                                "<p>30 less</p>", "<p>30 more</p>"],
                    options_hi: ["<p>40 अधिक</p>", "<p>40 कम</p>",
                                "<p>30 कम</p>", "<p>30 अधिक</p>"],
                    solution_en: "<p>65.(a)<br>Marks obtained by Sohan in maths = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> &times; 200 = 180<br>Marks obtained by Shyam in maths = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>100</mn></mfrac></math> &times; 200 = 140<br>required difference = 180 - 140 = 40 (more)</p>",
                    solution_hi: "<p>65.(a)<br>सोहन द्वारा गणित में प्राप्त अंक = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> &times; 200 = 180<br>श्याम द्वारा गणित में प्राप्त अंक = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>100</mn></mfrac></math> &times; 200 = 140<br>आवश्यक अंतर = 180 - 140 = 40(अधिक)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Find the HCF of 24, 36 and 60.</p>",
                    question_hi: "<p>66. 24, 36 और 60 का एच.सी.एफ (HCF) ज्ञात कीजिए।</p>",
                    options_en: ["<p>12</p>", "<p>360</p>", 
                                "<p>4</p>", "<p>6</p>"],
                    options_hi: ["<p>12</p>", "<p>360</p>",
                                "<p>4</p>", "<p>6</p>"],
                    solution_en: "<p>66.(a) Factor of 24 = 2 &times; 2 &times; 2 &times; 3<br>Factor of 36 = 2 &times; 2 &times; 3 &times; 3<br>Factor of 60 = 2 &times; 2 &times; 3 &times; 5<br>HCF of (24, 36 and 60) = 2 &times; 2 &times; 3 = 12</p>",
                    solution_hi: "<p>66.(a) 24 का गुणनखंड = 2 &times; 2 &times; 2 &times; 3<br>36 का गुणनखंड = 2 &times; 2 &times; 3 &times; 3<br>60 का गुणनखंड = 2 &times; 2 &times; 3 &times; 5<br>(24, 36 और 60) का HCF = 2 &times; 2 &times; 3 = 12</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Mohan gets 12% increase in his sale amount in the first year and 15% increase in the<br>second year, with that his present sale is ₹1,28,800. What was his sale two years ago?</p>",
                    question_hi: "<p>67. मोहन को पहले वर्ष में उसकी बिक्री राशि में 12% की वृद्धि प्राप्त होती है और दूसरे वर्ष में 15% की वृद्धि प्राप्त होती है, और अब उसकी वर्तमान बिक्री ₹1,28,800 है। दो वर्ष पूर्व उसकी बिक्री कितनी थी?</p>",
                    options_en: ["<p>₹1,75,000</p>", "<p>₹1,25,000</p>", 
                                "<p>₹1,00,000</p>", "<p>₹1,50,000</p>"],
                    options_hi: ["<p>₹1,75,000</p>", "<p>₹1,25,000</p>",
                                "<p>₹1,00,000</p>", "<p>₹1,50,000</p>"],
                    solution_en: "<p>67.(c) 12 % = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, 15 % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>20</mn></mfrac></math><br>Let the sale of 2 years ago = ₹x<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math> = 128800<br>x = 100,000</p>",
                    solution_hi: "<p>67.(c) 12 % = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, 15 % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>20</mn></mfrac></math><br>माना 2 वर्ष पहले की बिक्री = ₹ <math display=\"inline\"><mi>x</mi></math><br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math> = 128800<br>x = 100,000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. In an election between two candidates, one candidate gets 72% of the total votes cast. If the total votes are 1000, by how many votes does the winner win the election?</p>",
                    question_hi: "<p>68. दो उम्मीदवारों के बीच एक चुनाव में एक उम्मीदवार को डाले गए कुल मतों का 72% प्राप्त होता है। यदि कुल मत 1000 हैं, तो विजेता कितने मतों से चुनाव जीतता है?</p>",
                    options_en: ["<p>360</p>", "<p>720</p>", 
                                "<p>250</p>", "<p>440</p>"],
                    options_hi: ["<p>360</p>", "<p>720</p>",
                                "<p>250</p>", "<p>440</p>"],
                    solution_en: "<p>68.(d)<br>Votes got by looser candidate = 28%<br>Votes got by winner candidate = 72%<br>Difference = 72% - 28% = 44%<br>(total number of votes) 100% = 1000 votes<br>(no. of votes by which winner won the election) 44% = 440 votes</p>",
                    solution_hi: "<p>68.(d)<br>हारे हुए उम्मीदवार को मिले वोट = 28%<br>विजेता उम्मीदवार को मिले वोट = 72%<br>अंतर = 72% - 28% = 44%<br>(मतों की कुल संख्या) 100% = 1000 मत<br>(वोटों की संख्या जिनसे विजेता ने चुनाव जीता) 44% = 440 वोट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A dealer made some profit by selling a laptop for ₹88,000, which is equal to the amount of loss incurred on selling the same laptop for ₹72,000. If he had sold the laptop for ₹96,000, then his profit percentage would have been:</p>",
                    question_hi: "<p>69. एक व्यापारी ने एक लैपटॉप को ₹88,000 में बेचकर कुछ लाभ अर्जित किया, जो उसी लैपटॉप को ₹72,000 में बेचने पर हुई हानि के बराबर है। यदि वह लैपटॉप को ₹96,000 में बेचता, तो उसका लाभ प्रतिशत कितना होता?</p>",
                    options_en: ["<p>31.7%</p>", "<p>25%</p>", 
                                "<p>20%</p>", "<p>37.5%</p>"],
                    options_hi: ["<p>31.7%</p>", "<p>25%</p>",
                                "<p>20%</p>", "<p>37.5%</p>"],
                    solution_en: "<p>69.(c) According to question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>88000</mn><mo>-</mo><mi>CP</mi></mrow><mn>100</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CP</mi><mo>-</mo><mn>72000</mn></mrow><mn>100</mn></mfrac></math><br>2CP = 160000<br>CP = 80000<br>Now,<br>Profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>96000</mn><mo>-</mo><mn>80000</mn></mrow><mn>80000</mn></mfrac></math> &times; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16000</mn><mn>80000</mn></mfrac></math> &times; 100 = 20%</p>",
                    solution_hi: "<p>69.(c) प्रश्न के अनुसार,<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>88000</mn><mo>-</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mn>100</mn></mfrac></math> = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mn>72000</mn></mrow><mn>100</mn></mfrac></math><br>2 &times; क्रय मूल्य = 160000<br>क्रय मूल्य = 80000<br>अब,<br>लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>96000</mn><mo>-</mo><mn>80000</mn></mrow><mn>80000</mn></mfrac></math> &times; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16000</mn><mn>80000</mn></mfrac></math> &times; 100 = 20%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A tradesman marks his goods 30% more than the cost price. If he allows a discount of 6<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>% . then his gain percentage is:</p>",
                    question_hi: "<p>70. एक व्यापारी अपने माल को क्रय मूल्य से 30% अधिक मूल्य पर अंकित करता है। यदि वह 6<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> % की छूट देता है, तो उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>22<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>", "<p>21%</p>", 
                                "<p>21<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>%</p>", "<p>22%</p>"],
                    options_hi: ["<p>22<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>", "<p>21%</p>",
                                "<p>21<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>%</p>", "<p>22%</p>"],
                    solution_en: "<p>70.(c) We know that,<br>Let cost price = 100 unit <br>Marked price = 130 unit <br>Selling price = 130 &times; <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>975</mn><mn>8</mn></mfrac></math><br>Profit %= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>975</mn><mn>8</mn></mfrac><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac></math> &times; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>175</mn><mn>8</mn></mfrac></math> = 21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math>%</p>",
                    solution_hi: "<p>70.(c) हम जानते हैं कि<br>माना क्रय मूल्य = 100 इकाई <br>अंकित मूल्य = 130 इकाई <br>विक्रय मूल्य = 130 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>975</mn><mn>8</mn></mfrac></math><br>लाभ % =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>975</mn><mn>8</mn></mfrac><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac></math> &times; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>175</mn><mn>8</mn></mfrac></math> = 21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math>%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A person has to cover a distance of 8 km in 1 hour. If he covers one-fourth of the distance in one-third of the total time, then what should his speed (in km/h) be to cover the remaining distance in the remaining time so that the person reaches the destination exactly on time?</p>",
                    question_hi: "<p>71. एक व्यक्ति को 1 घंटे में 8 km की दूरी तय करनी है। यदि वह कुल समय के एक-तिहाई समय में एक-चौथाई दूरी तय करता है, तो शेष दूरी को, शेष समय में तय करने के लिए उसकी चाल (km/h में) कितनी होनी चाहिए ताकि वह व्यक्ति ठीक समय पर गंतव्य पर पहुंच जाए?</p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>7</p>", "<p>9</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>7</p>", "<p>9</p>"],
                    solution_en: "<p>71.(d) According to question,<br>one-fourth of the distance = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 2 km<br>Remaining distance = 8 - 2 = 6 km<br>Remaining time = 1 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> hours<br>Now,<br>Required speed = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>2</mn></mfrac></math> = 9 km/h</p>",
                    solution_hi: "<p>71.(d) प्रश्न के अनुसार,<br>दूरी का एक-चौथाई = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 2 km<br>शेष दूरी = 8 - 2 = 6 km<br>शेष समय = 1 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> घंटे<br>अब,<br>आवश्यक गति = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>2</mn></mfrac></math> = 9 km/h</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The simple interest on a certain sum for 4 years at 23% per annum is ₹11,960 less than the simple interest on the same sum for 8 years. Find the sum.</p>",
                    question_hi: "<p>72. एक निश्चित धनराशि पर 4 वर्षों के लिए 23% की वार्षिक दर पर साधारण ब्याज, उसी धनराशि पर 8 वर्षों के साधारण ब्याज से ₹11,960 कम है। धनराशि ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹12,000</p>", "<p>₹12,500</p>", 
                                "<p>₹13,500</p>", "<p>₹13,000</p>"],
                    options_hi: ["<p>₹12,000</p>", "<p>₹12,500</p>",
                                "<p>₹13,500</p>", "<p>₹13,000</p>"],
                    solution_en: "<p>72.(d)<br>Interest obtain in 4 year = 4 &times; 23% = 92%<br>Interest obtain in 8 year = 8 &times; 23% = 184%<br>According to the question,<br>(184 - 92)% = ₹11,960<br>92% = ₹11,960<br>Hence, sum (100%) = <math display=\"inline\"><mfrac><mrow><mn>11960</mn></mrow><mrow><mn>92</mn></mrow></mfrac></math> &times; 100 = ₹13,000</p>",
                    solution_hi: "<p>72.(d)<br>4 वर्ष में प्राप्त ब्याज = 4 &times; 23% = 92%<br>8 वर्ष में प्राप्त ब्याज = 8 &times; 23% = 184%<br>प्रश्न के अनुसार,<br>(184 - 92)% = ₹11,960<br>92% = ₹11,960<br>अतः, मूलधन (100%) = <math display=\"inline\"><mfrac><mrow><mn>11960</mn></mrow><mrow><mn>92</mn></mrow></mfrac></math> &times; 100 = ₹13,000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The difference between the compound interest (compounding annually) and simple interest on a sum at the rate of 50 percent per annum for 2 years is Rs. 7200. What is the sum?</p>",
                    question_hi: "<p>73. किसी धनराशि पर 2 वर्ष के लिए 50 प्रतिशत वार्षिक की दर से चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) और साधारण ब्याज के बीच का अंतर 7200 रुपए है। वह धनराशि कितनी है?</p>",
                    options_en: ["<p>Rs. 28800</p>", "<p>Rs. 28500</p>", 
                                "<p>Rs. 25700</p>", "<p>Rs. 24900</p>"],
                    options_hi: ["<p>28800 रुपए</p>", "<p>28500 रुपए</p>",
                                "<p>25700 रुपए</p>", "<p>24900 रुपए</p>"],
                    solution_en: "<p>73.(a)<br><strong>Formula :</strong>-&nbsp;(CI - SI)<sub>2year</sub>&nbsp;= P (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>100</mn></mfrac></math>)<sup>2</sup><br>According to the question,<br>7200 = P (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac></math>)<sup>2</sup><br>7200 = P (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2500</mn><mn>10000</mn></mfrac></math>)<br>P = <math display=\"inline\"><mfrac><mrow><mn>10000</mn></mrow><mrow><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mn>2500</mn><mi>&#160;</mi><mi>&#160;</mi></mrow></mfrac></math> &times; 7200 = Rs. 28800</p>",
                    solution_hi: "<p>73.(a)<br>फॉर्मूला:- (CI - SI)<sub>2वर्ष&nbsp; </sub>= P (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>100</mn></mfrac></math>)<sup>2</sup><br>प्रश्न के अनुसार,<br>7200 = P (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac></math>)<sup>2</sup><br>7200 = P (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2500</mn><mn>10000</mn></mfrac></math>)<br>P = <math display=\"inline\"><mfrac><mrow><mn>10000</mn></mrow><mrow><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi><mn>2500</mn><mi>&#160;</mi><mi>&#160;</mi></mrow></mfrac></math> &times; 7200 = रु.28800</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The following bar graph shows the production of cement (in lakh tonnes) of four companies-W, X, Y, and Z-over the years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773805548.png\" alt=\"rId65\" width=\"286\" height=\"209\"> <br>The production of cement by company X in 2020 and production of cement by company Z in 2022 together is what percentage of the production by company W in 2021?</p>",
                    question_hi: "<p>74. नीचे दिया गया दंड आरेख पिछले कुछ वर्षों में चार कंपनियों - W, X, Y और Z - के सीमेंट के उत्पादन (लाख टन में) को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744773805671.png\" alt=\"rId66\" width=\"300\" height=\"217\"> <br>सीमेंट का उत्पादन (लाख टन में)&nbsp;2020 में कंपनी X द्वारा सीमेंट का उत्पादन और 2022 में कंपनी Z द्वारा सीमेंट का उत्पादन कुल मिलाकर 2021 में कंपनी W द्वारा उत्पादन का कितना प्रतिशत है?</p>",
                    options_en: ["<p>275</p>", "<p>250</p>", 
                                "<p>220</p>", "<p>180</p>"],
                    options_hi: ["<p>275</p>", "<p>250</p>",
                                "<p>220</p>", "<p>180</p>"],
                    solution_en: "<p>74.(c)<br>Combine production by company X and Z in 2020 and 2022 respectively <br>= (30 + 25 ) = 55<br>Production by company W in 2021 = 25<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>+</mo><mn>25</mn></mrow><mn>25</mn></mfrac></math> &times; 100 = 55 &times; 4 = 220</p>",
                    solution_hi: "<p>74.(c)<br>कंपनी X और Z द्वारा क्रमशः 2020 और 2022 में कुल उत्पादन = (30 + 25 ) = 55<br>2021 में कंपनी W द्वारा उत्पादन = 25<br>आवश्यक % =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>+</mo><mn>25</mn></mrow><mn>25</mn></mfrac></math> &times; 100 = 55 &times; 4 = 220</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The average weight of a certain number of apples in a basket was 0.5 kg. If 10 apples with an average weight of 0.7 kg are removed from the basket and 5 apples with an average weight of 0.6 kg are added to the basket, then the average weight of the apples in the basket decreases by 0.1 kg. What was the initial number of apples in the basket?</p>",
                    question_hi: "<p>75. एक टोकरी में एक निश्चित संख्या में मौजूद सेबों का औसत वजन 0.5 kg था। यदि टोकरी से 0.7 kg औसत वजन वाले 10 सेब निकाल दिए जाएं और टोकरी में 0.6 kg औसत वजन वाले 5 सेब डाल दिए जाएं, तो टोकरी में सेबों के औसत वजन में 0.1 kg की कमी हो जाती है। टोकरी में शुरुआत में सेबों की संख्या कितनी थी?</p>",
                    options_en: ["<p>21</p>", "<p>20</p>", 
                                "<p>19</p>", "<p>18</p>"],
                    options_hi: ["<p>21</p>", "<p>20</p>",
                                "<p>19</p>", "<p>18</p>"],
                    solution_en: "<p>75.(b) Let total number of apples in the basket = x <br>Total weight of x&nbsp;number of apples = 0.5 &times; x = 0.5xkg<br>Now according to question , <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>5</mn><mi>x</mi><mo>-</mo><mn>7</mn><mo>+</mo><mn>3</mn></mrow><mrow><mi>x</mi><mo>-</mo><mn>10</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = 0.5 - 0.1<br>&rArr; 0.5x - 4 = 0.4 (x - 5 )<br>&rArr; 0.5x - 4 = 0.4x - 2<br>&rArr; 0.1x = 2 <br>&rArr; x = 20</p>",
                    solution_hi: "<p>75.(b) माना टोकरी में सेबों की कुल संख्या = x <br>x सेबों की संख्या का कुल वजन = 0.5 &times; x = 0.5xkg<br>अब प्रश्न के अनुसार, <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>5</mn><mi>x</mi><mo>-</mo><mn>7</mn><mo>+</mo><mn>3</mn></mrow><mrow><mi>x</mi><mo>-</mo><mn>10</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = 0.5 - 0.1<br>&rArr; 0.5x - 4 = 0.4 (x - 5 )<br>&rArr; 0.5x - 4 = 0.4x - 2<br>&rArr; 0.1x = 2 <br>&rArr; x = 20</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>Raveena has made / up her mind to / carried minimum / luggage with her.</p>",
                    question_hi: "<p>76. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>Raveena has made / up her mind to / carried minimum / luggage with her.</p>",
                    options_en: ["<p>luggage with her</p>", "<p>carried minimum</p>", 
                                "<p>Raveena has made</p>", "<p>up her mind to</p>"],
                    options_hi: ["<p>luggage with her</p>", "<p>carried minimum</p>",
                                "<p>Raveena has made</p>", "<p>up her mind to</p>"],
                    solution_en: "<p>76.(b) carried minimum<br>&lsquo;To + V<sub>1</sub>&rsquo;, also known as infinitive, is the correct grammatical structure. Hence, &lsquo;carry(V<sub>1</sub>) minimum&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(b) carried minimum<br>&lsquo;To + V<sub>1</sub>&rsquo;, जिसे infinitive भी कहा जाता है, सही grammatical structure है। इसलिए, &lsquo;carry(V<sub>1</sub>) minimal&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the option that can be used as a one-word substitute for the given group of words. <br>The study of coins</p>",
                    question_hi: "<p>77. Select the option that can be used as a one-word substitute for the given group of words. <br>The study of coins</p>",
                    options_en: ["<p>Numismatics</p>", "<p>Choreography</p>", 
                                "<p>Informatics</p>", "<p>Cartography</p>"],
                    options_hi: ["<p>Numismatics</p>", "<p>Choreography</p>",
                                "<p>Informatics</p>", "<p>Cartography</p>"],
                    solution_en: "<p>77.(a) <strong>Numismatics-</strong> the study of coins.<br><strong>Choreography-</strong> the sequence of steps and movements in dance or figure skating, especially in a ballet or other staged dance.<br><strong>Informatics-</strong> the science of processing data for storage and retrieval.<br><strong>Cartography-</strong> the science or practice of drawing maps.</p>",
                    solution_hi: "<p>77.(a) <strong>Numismatics</strong> (मुद्राशास्त्र)- the study of coins.<br><strong>Choreography</strong> (नृत्यकला)- the sequence of steps and movements in dance or figure skating, especially in a ballet or other staged dance.<br><strong>Informatics </strong>(सूचना प्रौद्योगिकी)- the science of processing data for storage and retrieval.<br><strong>Cartography (</strong>मानचित्रण)- the science or practice of drawing maps.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option with correct usage of the preposition to substitute the underlined segment in the given sentence.<br>The Centre of Excellence for Data Analytics (CEDA) provides quality data analytic services to various <span style=\"text-decoration: underline;\">government departments on all levels</span> as well as to public sector units at the Centre and in states.</p>",
                    question_hi: "<p>78. Select the most appropriate option with correct usage of the preposition to substitute the underlined segment in the given sentence.<br>The Centre of Excellence for Data Analytics (CEDA) provides quality data analytic services to various <span style=\"text-decoration: underline;\">government departments on all levels</span> as well as to public sector units at the Centre and in states.</p>",
                    options_en: ["<p>various government departments at all levels</p>", "<p>various government departments from all levels</p>", 
                                "<p>various government departments in all levels</p>", "<p>various government departments through all levels</p>"],
                    options_hi: ["<p>various government departments at all levels</p>", "<p>various government departments from all levels</p>",
                                "<p>various government departments in all levels</p>", "<p>various government departments through all levels</p>"],
                    solution_en: "<p>78.(a) various government departments at all levels<br>&lsquo;At&rsquo; is used to indicate the position, rank or level of something. Hence, &lsquo;various government departments at all levels&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(a) various government departments at all levels<br>&lsquo;At&rsquo; का प्रयोग किसी चीज़ की position, rank या level को इंगित करने के लिए किया जाता है। इसलिए, &lsquo;various government departments at all levels&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence.<br>He asked me why I was late.</p>",
                    question_hi: "<p>79. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence.<br>He asked me why I was late.</p>",
                    options_en: ["<p>He asked me, \"Why are you late?&rdquo;</p>", "<p>He asked me, &ldquo;Why I was late?&rdquo;</p>", 
                                "<p>He asked me, &ldquo;Why you are late?&rdquo;</p>", "<p>He asked me, &ldquo;Why am I late?\"</p>"],
                    options_hi: ["<p>He asked me, \"Why are you late?&rdquo;</p>", "<p>He asked me, &ldquo;Why I was late?&rdquo;</p>",
                                "<p>He asked me, &ldquo;Why you are late?&rdquo;</p>", "<p>He asked me, &ldquo;Why am I late?\"</p>"],
                    solution_en: "<p>79.(a) He asked me, &ldquo;Why are you late ?\"<br>(b) He asked me, &ldquo;Why <strong>I was</strong> late?&rdquo;(Incorrect words)<br>(c) He asked me, &ldquo;Why <strong>you ar</strong>e late?&rdquo;(Incorrect words)<br>(d) He asked me, &ldquo;Why<strong> am</strong> I late?\"(Incorrect words)</p>",
                    solution_hi: "<p>79.(a) He asked me, &ldquo;Why are you late ?\"<br>(b) He asked me, &ldquo;Why <strong>I was</strong> late?&rdquo;(गलत शब्द)<br>(c) He asked me, &ldquo;Why <strong>you are</strong> late?&rdquo;(गलत शब्द)<br>(d) He asked me, &ldquo;Why <strong>am</strong> I late?\"(गलत शब्द)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate meaning of the given idiom.<br>bread and butter</p>",
                    question_hi: "<p>80. Select the most appropriate meaning of the given idiom.<br>bread and butter</p>",
                    options_en: ["<p>An activity you do to get money for basic needs</p>", "<p>An activity you do to get help others</p>", 
                                "<p>An activity you do to improve your culinary skills</p>", "<p>An activity you do with determination</p>"],
                    options_hi: ["<p>An activity you do to get money for basic needs</p>", "<p>An activity you do to get help others</p>",
                                "<p>An activity you do to improve your culinary skills</p>", "<p>An activity you do with determination</p>"],
                    solution_en: "<p>80.(a) <strong>Bread and butter- </strong>an activity you do to get money for basic needs.<br>E.g.- Farming has been their bread and butter for generations.</p>",
                    solution_hi: "<p>80.(a) <strong>Bread and butter</strong>- an activity you do to get money for basic needs./एक गतिविधि जो आप बुनियादी जरूरतों के लिए धन प्राप्त करने के लिए करते हैं।<br>E.g.- Farming has been their bread and butter for generations.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>He made us work.</p>",
                    question_hi: "<p>81. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>He made us work.</p>",
                    options_en: ["<p>We were made to work by him.</p>", "<p>We would work by him.</p>", 
                                "<p>We might work by him.</p>", "<p>We have to work by him.</p>"],
                    options_hi: ["<p>We were made to work by him.</p>", "<p>We would work by him.</p>",
                                "<p>We might work by him.</p>", "<p>We have to work by him</p>"],
                    solution_en: "<p>81.(a) We were made to work by him.<br>(b) We <strong>would</strong> work by him.(Wrong Word)<br>(c) We <strong>might</strong> work by him.(Wrong Word)<br>(d) We <strong>have to</strong> work by him.(Wrong Word)</p>",
                    solution_hi: "<p>81.(a) We were made to work by him.<br>(b) We <strong>would</strong> work by him.(गलत शब्द का प्रयोग)<br>(c) We <strong>might</strong> work by him.(गलत शब्द का प्रयोग)<br>(d) We <strong>have to</strong> work by him.(गलत शब्द का प्रयोग)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the most appropriate spelling of the underlined word in the given sentence. Nobody could move on after such a <span style=\"text-decoration: underline;\">scandlous</span> allegation is made on them.</p>",
                    question_hi: "<p>82. Select the most appropriate spelling of the underlined word in the given sentence. Nobody could move on after such a <span style=\"text-decoration: underline;\">scandlous</span> allegation is made on them.</p>",
                    options_en: ["<p>skandelous</p>", "<p>scandalous</p>", 
                                "<p>scendolous</p>", "<p>scandelous</p>"],
                    options_hi: ["<p>skandelous</p>", "<p>scandalous</p>",
                                "<p>scendolous</p>", "<p>scandelous</p>"],
                    solution_en: "<p>82.(b) Scandalous<br>&lsquo;Scandalous&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>82.(b) Scandalous<br>&lsquo;Scandalous&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate meaning of the underlined idiom. <br>The United Nations is working to eliminate <span style=\"text-decoration: underline;\">the vicious cycle</span> of poverty.</p>",
                    question_hi: "<p>83. Select the most appropriate meaning of the underlined idiom. <br>The United Nations is working to eliminate <span style=\"text-decoration: underline;\">the vicious cycle</span> of poverty.</p>",
                    options_en: ["<p>One problem causes other</p>", "<p>Strength of something</p>", 
                                "<p>Loophole of something</p>", "<p>The barrier of poverty</p>"],
                    options_hi: ["<p>One problem causes other</p>", "<p>Strength of something</p>",
                                "<p>Loophole of something</p>", "<p>The barrier of poverty</p>"],
                    solution_en: "<p>83.(a) <strong>The vicious cycle</strong>- one problem causes other.</p>",
                    solution_hi: "<p>83.(a) <strong>The vicious cycle - </strong>one problem causes other./एक समस्या दूसरे का कारण बनती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate ANTONYM of the given word.<br>Important</p>",
                    question_hi: "<p>84. Select the most appropriate ANTONYM of the given word.<br>Important</p>",
                    options_en: ["<p>Productive</p>", "<p>Trivial</p>", 
                                "<p>Recurring</p>", "<p>Significant</p>"],
                    options_hi: ["<p>Productive</p>", "<p>Trivial</p>",
                                "<p>Recurring</p>", "<p>Significant</p>"],
                    solution_en: "<p>84.(b) <strong>Trivial - </strong>of little value.<br><strong>Important-</strong> of great significance or value.<br><strong>Productive-</strong> something that produces a good result.<br><strong>Recurring- </strong>occurring again periodically or repeatedly.<br><strong>Significant-</strong> having a major impact.</p>",
                    solution_hi: "<p>84.(b) <strong>Trivial (तुच्छ) -</strong> of little value.<br><strong>Important (महत्वपूर्ण) - </strong>of great significance or value.<br><strong>Productive (उत्पादक) </strong>- something that produces a good result.<br><strong>Recurring (पुनरावर्ती) </strong>- occurring again periodically or repeatedly.<br><strong>Significant (महत्वपूर्ण)</strong> - having a major impact.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the option that can be used as a one-word substitute for the given phrase. <br>An important person who represents his/her country in a foreign country</p>",
                    question_hi: "<p>85. Select the option that can be used as a one-word substitute for the given phrase. <br>An important person who represents his/her country in a foreign country</p>",
                    options_en: ["<p>Amateur</p>", "<p>Ambassador</p>", 
                                "<p>Altruist</p>", "<p>Anonymous</p>"],
                    options_hi: ["<p>Amateur</p>", "<p>Ambassador</p>",
                                "<p>Altruist</p>", "<p>Anonymous</p>"],
                    solution_en: "<p>85.(b) <strong>Ambassador-</strong> an important person who represents his/her country in a foreign country.<br><strong>Amateur- </strong>a person who takes part in an activity for pleasure, not as a job.<br><strong>Altruist- </strong>a person who cares about others and helps them despite not gaining anything by doing this.<br><strong>Anonymous- </strong>of unknown authorship or origin.</p>",
                    solution_hi: "<p>85.(b) <strong>Ambassador</strong> (राजदूत)- an important person who represents his/her country in a foreign country.<br><strong>Amateur </strong>(शौकिया/शौकीन)- a person who takes part in an activity for pleasure, not as a job.<br><strong>Altruist</strong> (परोपकारी)- a person who cares about others and helps them despite not gaining anything by doing this.<br><strong>Anonymous </strong>(अज्ञात/गुमनाम)- of unknown authorship or origin.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: In some countries like Malaysia there are kite festivals.<br>Q: These experts are mostly adult amateur kite flyers.<br>R: Kite flying is a good leisure activity for parents with their children.<br>S: Self designed kites are flown by experts during such times.</p>",
                    question_hi: "<p>86. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: In some countries like Malaysia there are kite festivals.<br>Q: These experts are mostly adult amateur kite flyers.<br>R: Kite flying is a good leisure activity for parents with their children.<br>S: Self designed kites are flown by experts during such times.</p>",
                    options_en: ["<p>PSQR</p>", "<p>SRPQ</p>", 
                                "<p>QSPR</p>", "<p>RPSQ</p>"],
                    options_hi: ["<p>PSQR</p>", "<p>SRPQ</p>",
                                "<p>QSPR</p>", "<p>RPSQ</p>"],
                    solution_en: "<p>86.(d) <strong>RPSQ</strong><br>R will be the first part. Here it is given that children fly kites with their parents. P will be immediately followed by S. &ldquo;Such times&rdquo; refers to the time of kite festivals. S will be immediately followed by Q Here experts are being talked about.&nbsp;So the answer is (d) RPSQ</p>",
                    solution_hi: "<p>86.(d)<strong> RPSQ</strong><br>R पहला भाग होगा। यहाँ यह दिया गया है कि बच्चे अपने माता-पिता के साथ पतंग उड़ाते हैं। P के तुरंत बाद S होगा। &ldquo;Such times&rdquo; kite festivals के समय को संदर्भित करता है। S के तुरंत बाद Q होगा। यहां विशेषज्ञों की बात की जा रही है। तो उत्तर (d) RPSQ है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate synonym of the given word.<br>Change</p>",
                    question_hi: "<p>87. Select the most appropriate synonym of the given word.<br>Change</p>",
                    options_en: ["<p>Alter</p>", "<p>Adjust</p>", 
                                "<p>Resolute</p>", "<p>Renew</p>"],
                    options_hi: ["<p>Alter</p>", "<p>Adjust</p>",
                                "<p>Resolute</p>", "<p>Renew</p>"],
                    solution_en: "<p>87.(a) <strong>Alter-</strong> to change or modify something.<br><strong>Adjust- </strong>to change something slightly so that it works better, fits better, or is more suitable.<br><strong>Resolute- </strong>determined in character, action, or ideas.<br>Renew- to resume an activity after an interruption.</p>",
                    solution_hi: "<p>87.(a) <strong>Alter </strong>(परिवर्तन) - to change or modify something.<br><strong>Adjust</strong> (समायोजित करना) - to change something slightly so that it works better, fits better, or is more suitable.<br><strong>Resolute</strong> (दृढ़संकल्पित) - determined in character, action, or ideas.<br><strong>Renew</strong> (नवीनीकृत करना) - to resume an activity after an interruption.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Fill in the blank with the most appropriate ANTONYM of the underlined word. <br>Gentlemen always go for a <span style=\"text-decoration: underline;\">lawful</span> approach, but rouges choose ______ ways to solve problems.</p>",
                    question_hi: "<p>88. Fill in the blank with the most appropriate ANTONYM of the underlined word. <br>Gentlemen always go for a <span style=\"text-decoration: underline;\">lawful</span> approach, but rouges choose ______ ways to solve problems.</p>",
                    options_en: ["<p>sophisticated</p>", "<p>legitimate</p>", 
                                "<p>legal</p>", "<p>illicit</p>"],
                    options_hi: ["<p>sophisticated</p>", "<p>legitimate</p>",
                                "<p>legal</p>", "<p>illicit</p>"],
                    solution_en: "<p>88.(d) <strong>Illicit- </strong>something that is illegal.<br><strong>Lawful-</strong> something that is permitted by the law.<br><strong>Sophisticated- </strong>well-educated and having experience of the world or knowledge of culture.<br>Legitimate- something that is lawful or valid.<br><strong>Legal- </strong>something that is according to the law.</p>",
                    solution_hi: "<p>88.(d) Illicit (अवैध) - something that is illegal.<br>Lawful (क़ानूनी) - something that is permitted by the law.<br><strong>Sophisticated </strong>(परिष्कृत) - well-educated and having experience of the world or knowledge of culture.<br><strong>Legitimate </strong>(वैध) - something that is lawful or valid.<br><strong>Legal </strong>(वैध) - something that is according to the law.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate option to fill in the blank.<br>Many developing nations are having a ___________ economy these days.</p>",
                    question_hi: "<p>89. Select the most appropriate option to fill in the blank.<br>Many developing nations are having a ___________ economy these days.</p>",
                    options_en: ["<p>booming</p>", "<p>boosting</p>", 
                                "<p>resounding</p>", "<p>ringing</p>"],
                    options_hi: ["<p>booming</p>", "<p>boosting</p>",
                                "<p>resounding</p>", "<p>ringing</p>"],
                    solution_en: "<p>89.(a) booming<br>&lsquo;Booming&rsquo; means experiencing rapid growth. The given sentence states that many developing nations are having a booming economy these days. Hence &lsquo;booming&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(a) booming<br>&lsquo;Booming&rsquo; का अर्थ है तेज़ी से विकास का अनुभव करना। दिए गए sentence में कहा गया है कि इन दिनों कई विकासशील देशों (developing nations) की अर्थव्यवस्था (economy) तेज़ी से बढ़ रही है। अतः &lsquo;booming&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate synonym of the given word.<br>Injustice</p>",
                    question_hi: "<p>90. Select the most appropriate synonym of the given word.<br>Injustice</p>",
                    options_en: ["<p>Complacency</p>", "<p>Demand</p>", 
                                "<p>Discrimination</p>", "<p>Equality</p>"],
                    options_hi: ["<p>Complacency</p>", "<p>Demand</p>",
                                "<p>Discrimination</p>", "<p>Equality</p>"],
                    solution_en: "<p>90.(c)<strong> Discriminate-</strong> to treat someone unfairly.<br><strong>Injustice-</strong> lack of fairness or justice.<br><strong>Complacency-</strong> feeling of self satisfaction.<br><strong>Demand- </strong>strong request or demand for something.<br><strong>Equality</strong>- state of being equal in rights.</p>",
                    solution_hi: "<p>90.(c) <strong>Discriminate </strong>(भेदभाव) - to treat someone unfairly.<br><strong>Injustice</strong> (अन्याय) - lack of fairness or justice.<br><strong>Complacency </strong>(आत्मसंतुष्टि) - feeling of self satisfaction.<br><strong>Demand </strong>(मांग) - strong request or demand for something.<br><strong>Equality</strong> (समानता) - state of being equal in rights.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91.<strong>Cloze test:</strong> <br>Eating white bread and ready meals could be killing us, according to the first major study linking &ldquo;ultra-processed&rdquo; food with (91)______death. The study of 45,000 middle-aged people found that every 10 percent increase in (92)_____of &ldquo;ultra-processed food&rdquo; was linked to a 14 per cent (93)______risk of death within the next eight years. Previous research has linked consumption (94)______foods like white bread, ready meals, sausages, sugary cereals, fizzy drinks to a higher risk of high blood (95)______ and cancer.<br>Select the most appropriate option to fill in the blank no 91.</p>",
                    question_hi: "<p>91.<strong>Cloze test:</strong> <br>Eating white bread and ready meals could be killing us, according to the first major study linking &ldquo;ultra-processed&rdquo; food with (91)______death. The study of 45,000 middle-aged people found that every 10 percent increase in (92)_____of &ldquo;ultra-processed food&rdquo; was linked to a 14 per cent (93)______risk of death within the next eight years. Previous research has linked consumption (94)______foods like white bread, ready meals, sausages, sugary cereals, fizzy drinks to a higher risk of high blood (95)______ and cancer.<br>Select the most appropriate option to fill in the blank no 91.</p>",
                    options_en: ["<p>quiet</p>", "<p>easy</p>", 
                                "<p>early</p>", "<p>late</p>"],
                    options_hi: ["<p>quiet</p>", "<p>easy</p>",
                                "<p>early</p>", "<p>late</p>"],
                    solution_en: "<p>91.(c) <strong>Early -</strong> happening or done before the usual or expected time.<br><strong>Quiet -</strong> without being disturbed or interrupted.<br><strong>Late </strong>- doing something or taking place after the expected, proper, or usual time.<br>According to the passage people are&nbsp;dying before the average age of humans so option (c) is the answer.</p>",
                    solution_hi: "<p>91.(c) <strong>Early-</strong> सामान्य या अपेक्षित समय से पहले हो रहा या किया हुआ।<br><strong>Quiet-</strong> बिना परेशान या बाधित हुए।<br><strong>Late- </strong>अपेक्षित, उचित या सामान्य समय के बाद कुछ करना या होना।<br>Passage के अनुसार लोग मनुष्यों की औसत आयु से पहले मर रहे हैं इसलिए विकल्प (C) उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92.<strong>Cloze test:</strong> <br>Eating white bread and ready meals could be killing us, according to the first major study linking &ldquo;ultra-processed&rdquo; food with (91)______death. The study of 45,000 middle-aged people found that every 10 percent increase in (92)_____of &ldquo;ultra-processed food&rdquo; was linked to a 14 per cent (93)______risk of death within the next eight years. Previous research has linked consumption (94)______foods like white bread, ready meals, sausages, sugary cereals, fizzy drinks to a higher risk of high blood (95)______ and cancer.<br>Select the most appropriate option to fill in the blank no 92.</p>",
                    question_hi: "<p>92. <strong>Cloze test:</strong> <br>Eating white bread and ready meals could be killing us, according to the first major study linking &ldquo;ultra-processed&rdquo; food with (91)______death. The study of 45,000 middle-aged people found that every 10 percent increase in (92)_____of &ldquo;ultra-processed food&rdquo; was linked to a 14 per cent (93)______risk of death within the next eight years. Previous research has linked consumption (94)______foods like white bread, ready meals, sausages, sugary cereals, fizzy drinks to a higher risk of high blood (95)______ and cancer.<br>Select the most appropriate option to fill in the blank no 92.</p>",
                    options_en: ["<p>input</p>", "<p>digestion</p>", 
                                "<p>intake</p>", "<p>making</p>"],
                    options_hi: ["<p>input</p>", "<p>digestion</p>",
                                "<p>intake</p>", "<p>making</p>"],
                    solution_en: "<p>92.(c) <strong>Intake -</strong> an amount of food, air, or another substance taken into the body<br>(a) <strong>Input-</strong>what is put in, taken in, or operated on by any process or system.<br>(b) <strong>Digestion-</strong>the process of digesting food.<br>(d) <strong>Making-</strong>the process of making or producing something.</p>",
                    solution_hi: "<p>92.(c)<strong> Intake - </strong>शरीर में भोजन, वायु या किसी अन्य पदार्थ की मात्रा।<br>(a) <strong>Input- </strong>किसी प्रक्रिया या प्रणाली द्वारा क्या डाला, लिया या संचालित किया गया।<br>(b) <strong>Digestion- </strong>भोजन पचाने की प्रक्रिया।<br>(d) <strong>Making- </strong>कुछ बनाने की प्रक्रिया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93.<strong>Cloze test:</strong> <br>Eating white bread and ready meals could be killing us, according to the first major study linking &ldquo;ultra-processed&rdquo; food with (91)______death. The study of 45,000 middle-aged people found that every 10 percent increase in (92)_____of &ldquo;ultra-processed food&rdquo; was linked to a 14 per cent (93)______risk of death within the next eight years. Previous research has linked consumption (94)______foods like white bread, ready meals, sausages, sugary cereals, fizzy drinks to a higher risk of high blood (95)______ and cancer.<br>Select the most appropriate option to fill in the blank no 93.</p>",
                    question_hi: "<p>93.<strong>Cloze test:</strong> <br>Eating white bread and ready meals could be killing us, according to the first major study linking &ldquo;ultra-processed&rdquo; food with (91)______death. The study of 45,000 middle-aged people found that every 10 percent increase in (92)_____of &ldquo;ultra-processed food&rdquo; was linked to a 14 per cent (93)______risk of death within the next eight years. Previous research has linked consumption (94)______foods like white bread, ready meals, sausages, sugary cereals, fizzy drinks to a higher risk of high blood (95)______ and cancer.<br>Select the most appropriate option to fill in the blank no 93.</p>",
                    options_en: ["<p>increased</p>", "<p>declined</p>", 
                                "<p>decreased</p>", "<p>improved</p>"],
                    options_hi: ["<p>increased</p>", "<p>declined</p>",
                                "<p>decreased</p>", "<p>improved</p>"],
                    solution_en: "<p>93.(a) increased<br>According to the passage the rate of risk of death had increased.</p>",
                    solution_hi: "<p>93.(a) increased<br>Passage के अनुसार मृत्यु के जोखिम की दर बढ़ गई थी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94.<strong>Cloze test:</strong> <br>Eating white bread and ready meals could be killing us, according to the first major study linking &ldquo;ultra-processed&rdquo; food with (91)______death. The study of 45,000 middle-aged people found that every 10 percent increase in (92)_____of &ldquo;ultra-processed food&rdquo; was linked to a 14 per cent (93)______risk of death within the next eight years. Previous research has linked consumption (94)______foods like white bread, ready meals, sausages, sugary cereals, fizzy drinks to a higher risk of high blood (95)______ and cancer.<br>Select the most appropriate option to fill in the blank no 94.</p>",
                    question_hi: "<p>94.<strong>Cloze test:</strong> <br>Eating white bread and ready meals could be killing us, according to the first major study linking &ldquo;ultra-processed&rdquo; food with (91)______death. The study of 45,000 middle-aged people found that every 10 percent increase in (92)_____of &ldquo;ultra-processed food&rdquo; was linked to a 14 per cent (93)______risk of death within the next eight years. Previous research has linked consumption (94)______foods like white bread, ready meals, sausages, sugary cereals, fizzy drinks to a higher risk of high blood (95)______ and cancer.<br>Select the most appropriate option to fill in the blank no 94.</p>",
                    options_en: ["<p>of</p>", "<p>on</p>", 
                                "<p>in</p>", "<p>from</p>"],
                    options_hi: ["<p>of</p>", "<p>on</p>",
                                "<p>in</p>", "<p>from</p>"],
                    solution_en: "<p>94.(a) of <br>The preposition &ldquo;of&rdquo; should be used with consumption.</p>",
                    solution_hi: "<p>94.(a) of , Consumption के साथ <br>Preposition \"of\" प्रयोग किया जाना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95.<strong>Cloze test:</strong> <br>Eating white bread and ready meals could be killing us, according to the first major study linking &ldquo;ultra-processed&rdquo; food with (91)______death. The study of 45,000 middle-aged people found that every 10 percent increase in (92)_____of &ldquo;ultra-processed food&rdquo; was linked to a 14 per cent (93)______risk of death within the next eight years. Previous research has linked consumption (94)______foods like white bread, ready meals, sausages, sugary cereals, fizzy drinks to a higher risk of high blood (95)______ and cancer.<br>Select the most appropriate option to fill in the blank no 95.</p>",
                    question_hi: "<p>95.<strong>Cloze test:</strong> <br>Eating white bread and ready meals could be killing us, according to the first major study linking &ldquo;ultra-processed&rdquo; food with (91)______death. The study of 45,000 middle-aged people found that every 10 percent increase in (92)_____of &ldquo;ultra-processed food&rdquo; was linked to a 14 per cent (93)______risk of death within the next eight years. Previous research has linked consumption (94)______foods like white bread, ready meals, sausages, sugary cereals, fizzy drinks to a higher risk of high blood (95)______ and cancer.<br>Select the most appropriate option to fill in the blank no 95.</p>",
                    options_en: ["<p>pressure</p>", "<p>infection</p>", 
                                "<p>clotting</p>", "<p>coagulation</p>"],
                    options_hi: ["<p>pressure</p>", "<p>infection</p>",
                                "<p>clotting</p>", "<p>coagulation</p>"],
                    solution_en: "<p>95.(a) pressure<br>&lsquo;Blood pressure&rsquo; is a disease</p>",
                    solution_hi: "<p>95.(a) pressure<br>&lsquo;Blood pressure&rsquo; यह एक बीमारी है |</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Comprehension :-</strong><br>Helping others physically, by removing their physical needs, is indeed a good thing, but spiritual help is more substantial, as it is more far reaching because the need is greater. ... It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him. He who gives spiritual knowledge is the greatest benefactor of mankind. A spiritually strong man can be powerful in every other respect, if he so wishes. Until there is spiritual strength in man, even physical needs cannot be well satisfied. Next to spiritual comes intellectual help. The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man, because real life of a man consists of knowledge. Ignorance is death, knowledge is life. <br>According to the author, ignorance is _____ and knowledge is _______.</p>",
                    question_hi: "<p>96. <strong>Comprehension :-</strong><br>Helping others physically, by removing their physical needs, is indeed a good thing, but spiritual help is more substantial, as it is more far reaching because the need is greater. ... It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him. He who gives spiritual knowledge is the greatest benefactor of mankind. A spiritually strong man can be powerful in every other respect, if he so wishes. Until there is spiritual strength in man, even physical needs cannot be well satisfied. Next to spiritual comes intellectual help. The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man, because real life of a man consists of knowledge. Ignorance is death, knowledge is life. <br>According to the author, ignorance is _____ and knowledge is _______.</p>",
                    options_en: ["<p>death, life</p>", "<p>bliss, painful</p>", 
                                "<p>painful, bliss</p>", "<p>life, avoidable</p>"],
                    options_hi: ["<p>death, life</p>", "<p>bliss, painful</p>",
                                "<p>painful, bliss</p>", "<p>life, avoidable</p>"],
                    solution_en: "<p>96.(a) death, life<br>(Line/s from the passage- Ignorance is death, knowledge is life.)</p>",
                    solution_hi: "<p>96.(a) death, life<br>(Passage से ली गई line/s - Ignorance is death, knowledge is life./अज्ञानता मृत्यु है, ज्ञान जीवन है।)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Comprehension :-</strong><br>Helping others physically, by removing their physical needs, is indeed a good thing, but spiritual help is more substantial, as it is more far reaching because the need is greater. ... It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him. He who gives spiritual knowledge is the greatest benefactor of mankind. A spiritually strong man can be powerful in every other respect, if he so wishes. Until there is spiritual strength in man, even physical needs cannot be well satisfied. Next to spiritual comes intellectual help. The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man, because real life of a man consists of knowledge. Ignorance is death, knowledge is life. <br>According to the author, which of the following is a far higher gift than food and clothes, or even of life?</p>",
                    question_hi: "<p>97. <strong>Comprehension :-</strong><br>Helping others physically, by removing their physical needs, is indeed a good thing, but spiritual help is more substantial, as it is more far reaching because the need is greater. ... It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him. He who gives spiritual knowledge is the greatest benefactor of mankind. A spiritually strong man can be powerful in every other respect, if he so wishes. Until there is spiritual strength in man, even physical needs cannot be well satisfied. Next to spiritual comes intellectual help. The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man, because real life of a man consists of knowledge. Ignorance is death, knowledge is life. <br>According to the author, which of the following is a far higher gift than food and clothes, or even of life?</p>",
                    options_en: ["<p>Jobs</p>", "<p>Food</p>", 
                                "<p>Knowledge</p>", "<p>Books</p>"],
                    options_hi: ["<p>Jobs</p>", "<p>Food</p>",
                                "<p>Knowledge</p>", "<p>Books</p>"],
                    solution_en: "<p>97.(c) knowledge<br>(Line/s from the passage- The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man)</p>",
                    solution_hi: "<p>97.(c) knowledge<br>(Passage से ली गई line/s - The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man./ ज्ञान का दान भोजन और वस्त्र से कहीं अधिक महान है, यह मनुष्य को जीवन देने से भी अधिक महान है।)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Comprehension :-</strong><br>Helping others physically, by removing their physical needs, is indeed a good thing, but spiritual help is more substantial, as it is more far reaching because the need is greater. ... It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him. He who gives spiritual knowledge is the greatest benefactor of mankind. A spiritually strong man can be powerful in every other respect, if he so wishes. Until there is spiritual strength in man, even physical needs cannot be well satisfied. Next to spiritual comes intellectual help. The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man, because real life of a man consists of knowledge. Ignorance is death, knowledge is life. <br>What according to the author is the highest good that one can do?</p>",
                    question_hi: "<p>98. <strong>Comprehension :-</strong><br>Helping others physically, by removing their physical needs, is indeed a good thing, but spiritual help is more substantial, as it is more far reaching because the need is greater. ... It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him. He who gives spiritual knowledge is the greatest benefactor of mankind. A spiritually strong man can be powerful in every other respect, if he so wishes. Until there is spiritual strength in man, even physical needs cannot be well satisfied. Next to spiritual comes intellectual help. The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man, because real life of a man consists of knowledge. Ignorance is death, knowledge is life. <br>What according to the author is the highest good that one can do?</p>",
                    options_en: ["<p>Extending spiritual help to others</p>", "<p>Doing intellectual good to people</p>", 
                                "<p>Monitory help</p>", "<p>Helping others physically</p>"],
                    options_hi: ["<p>Extending spiritual help to others</p>", "<p>Doing intellectual good to people</p>",
                                "<p>Monitory help</p>", "<p>Helping others physically</p>"],
                    solution_en: "<p>98.(a) Extending spiritual help to others<br>(Line/s from the passage - It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him.)</p>",
                    solution_hi: "<p>98.(a) Extending spiritual help to others<br>(Passage से ली गई line/s - It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him./ केवल आत्मा के ज्ञान (knowledge of the spirit) से ही अभिलाषा की शक्ति सदा के लिए नष्ट हो जाती है, इसलिए मनुष्य की आध्यात्मिक(spiritually) सहायता करना उसके लिए दी जा सक्ने वाली सर्वोत्तम सहायता है।)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Comprehension :-</strong><br>Helping others physically, by removing their physical needs, is indeed a good thing, but spiritual help is more substantial, as it is more far reaching because the need is greater. ... It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him. He who gives spiritual knowledge is the greatest benefactor of mankind. A spiritually strong man can be powerful in every other respect, if he so wishes. Until there is spiritual strength in man, even physical needs cannot be well satisfied. Next to spiritual comes intellectual help. The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man, because real life of a man consists of knowledge. Ignorance is death, knowledge is life. <br>What does the word &lsquo;annihilated&rsquo; mean in the context of the above passage?</p>",
                    question_hi: "<p>99. <strong>Comprehension :-</strong><br>Helping others physically, by removing their physical needs, is indeed a good thing, but spiritual help is more substantial, as it is more far reaching because the need is greater. ... It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him. He who gives spiritual knowledge is the greatest benefactor of mankind. A spiritually strong man can be powerful in every other respect, if he so wishes. Until there is spiritual strength in man, even physical needs cannot be well satisfied. Next to spiritual comes intellectual help. The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man, because real life of a man consists of knowledge. Ignorance is death, knowledge is life. <br>What does the word &lsquo;annihilated&rsquo; mean in the context of the above passage?</p>",
                    options_en: ["<p>Obliterated</p>", "<p>Omitted</p>", 
                                "<p>Occluded</p>", "<p>Obstructed</p>"],
                    options_hi: ["<p>Obliterated</p>", "<p>Omitted</p>",
                                "<p>Occluded</p>", "<p>Obstructed</p>"],
                    solution_en: "<p>99.(a) <strong>Obliterated-</strong> to remove all signs of something.<br><strong>Annihilated-</strong> to destroy completely, leaving nothing.</p>",
                    solution_hi: "<p>99.(a) <strong>Obliterated </strong>(नामो-निशान मिटाना) - to remove all signs of something.<br><strong>Annihilated</strong> (सर्वनाश करना) - to destroy completely, leaving nothing.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Comprehension :-</strong><br>Helping others physically, by removing their physical needs, is indeed a good thing, but spiritual help is more substantial, as it is more far reaching because the need is greater. ... It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him. He who gives spiritual knowledge is the greatest benefactor of mankind. A spiritually strong man can be powerful in every other respect, if he so wishes. Until there is spiritual strength in man, even physical needs cannot be well satisfied. Next to spiritual comes intellectual help. The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man, because real life of a man consists of knowledge. Ignorance is death, knowledge is life. <br>How can the faculty of want be destroyed forever?</p>",
                    question_hi: "<p>100. <strong>Comprehension :-</strong><br>Helping others physically, by removing their physical needs, is indeed a good thing, but spiritual help is more substantial, as it is more far reaching because the need is greater. ... It is only with the knowledge of the spirit that the faculty of want is annihilated forever, so helping man spiritually is the best help that can be extended to him. He who gives spiritual knowledge is the greatest benefactor of mankind. A spiritually strong man can be powerful in every other respect, if he so wishes. Until there is spiritual strength in man, even physical needs cannot be well satisfied. Next to spiritual comes intellectual help. The gift of knowledge is far higher than that of food and clothes, it is even higher than giving life to a man, because real life of a man consists of knowledge. Ignorance is death, knowledge is life. <br>How can the faculty of want be destroyed forever?</p>",
                    options_en: ["<p>With the knowledge of the spirit</p>", "<p>With physical assistance</p>", 
                                "<p>With knowledge provided in books</p>", "<p>With a charitable spirit</p>"],
                    options_hi: ["<p>With the knowledge of the spirit</p>", "<p>With physical assistance</p>",
                                "<p>With knowledge provided in books</p>", "<p>With a charitable spirit</p>"],
                    solution_en: "<p>100.(a) With the knowledge of the spirit<br>(Line/s from the passage- It is only with the knowledge of the spirit that the faculty of want is annihilated forever.)</p>",
                    solution_hi: "<p>100.(a) With the knowledge of the spirit<br>(Passage से ली गई line/s - It is only with the knowledge of the spirit that the faculty of want is annihilated forever./ केवल आत्मा के ज्ञान से ही अभिलाषा की शक्ति हमेशा के लिए नष्ट हो जाती है।)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>