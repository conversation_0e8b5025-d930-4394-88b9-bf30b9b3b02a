<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language, &lsquo;DIVERT&rsquo; is coded as &lsquo;36&rsquo; and &lsquo;DOLDRUMS&rsquo; is coded as &lsquo;64&rsquo;. What is the code for &lsquo;DOGMA&rsquo; in the given code language?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, \'DIVERT\' को \'36\' लिखा जाता है और DOLDRUMS को 64\' लिखा जाता है। उस कूट भाषा में \'DOGMA\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>64</p>",
                        "<p>49</p>",
                        "<p>25</p>",
                        "<p>36</p>"
                    ],
                    options_hi: [
                        "<p>64</p>",
                        "<p>49</p>",
                        "<p>25</p>",
                        "<p>36</p>"
                    ],
                    solution_en: "<p>1.(c) <strong>Logic</strong> :- (Number of letters)<sup>2</sup> <br>DIVERT :- (6)<sup>2</sup> = 36<br>DOLDRUMS :- (8)<sup>2</sup> = 64<br>Similarly,<br>DOGMA :- (5)<sup>2</sup> = 25</p>",
                    solution_hi: "<p>1.(c) <strong>तर्क</strong> :- (अक्षरों की संख्या)<sup>2</sup><br>DIVERT :- (6)<sup>2</sup> = 36<br>DOLDRUMS :- (8)<sup>2</sup> = 64<br>इसी प्रकार,<br>DOGMA :- (5)<sup>2</sup> = 25</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Identify the figure given in the options which when put in place of the question mark (?) will logically complete the series?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218321.png\" alt=\"rId4\" height=\"85\"></p>",
                    question_hi: "<p>2. विकल्पों में दिए गए उस चित्र की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूर्ण हो जाएगी?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218321.png\" alt=\"rId4\" height=\"85\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218444.png\" alt=\"rId5\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218556.png\" alt=\"rId6\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218665.png\" alt=\"rId7\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218812.png\" alt=\"rId8\" height=\"85\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218444.png\" alt=\"rId5\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218556.png\" alt=\"rId6\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218665.png\" alt=\"rId7\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218812.png\" alt=\"rId8\" height=\"85\"></p>"
                    ],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218665.png\" alt=\"rId7\" height=\"85\"></p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218665.png\" alt=\"rId7\" height=\"85\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. &lsquo;LISP&rsquo; is related to &lsquo;ORHK&rsquo; in a certain way based on the English alphabetical order. In the same way, &lsquo;WAND&rsquo; is related to &lsquo;DZMW&rsquo;. To which of the following is &lsquo;BOTE&rsquo; related, following the same logic?</p>",
                    question_hi: "<p>3. अँग्रेजी वर्णमाला-क्रम के आधार पर &lsquo;LISP&rsquo; एक निश्चित तरीके से &lsquo;ORHK&rsquo; से संबंधित है। उसी तरह &lsquo;WAND&rsquo; का संबंध &lsquo;DZMW&rsquo; से है। उसी तर्क के अनुसार &lsquo;BOTE&rsquo; का संबंध निम्नलिखित में से किससे है?</p>",
                    options_en: [
                        "<p>YKFU</p>",
                        "<p>YMHV</p>",
                        "<p>YKFW</p>",
                        "<p>YLGV</p>"
                    ],
                    options_hi: [
                        "<p>YKFU</p>",
                        "<p>YMHV</p>",
                        "<p>YKFW</p>",
                        "<p>YLGV</p>"
                    ],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013218967.png\" alt=\"rId9\" height=\"100\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013219076.png\" alt=\"rId10\" height=\"100\"><br>, Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013219221.png\" alt=\"rId11\" height=\"125\"></p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013219381.png\" alt=\"rId12\" height=\"100\">,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013219490.png\" alt=\"rId13\" height=\"100\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013219594.png\" alt=\"rId14\" height=\"125\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the correct mirror image of the given combination when the mirror is placed at&nbsp;MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013219692.png\" alt=\"rId15\"></p>",
                    question_hi: "<p>4. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन&nbsp;कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013219692.png\" alt=\"rId15\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013219793.png\" alt=\"rId16\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220006.png\" alt=\"rId17\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220122.png\" alt=\"rId18\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220220.png\" alt=\"rId19\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013219793.png\" alt=\"rId16\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220006.png\" alt=\"rId17\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220122.png\" alt=\"rId18\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220220.png\" alt=\"rId19\"></p>"
                    ],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013219793.png\" alt=\"rId16\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013219793.png\" alt=\"rId16\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Three of the following word pairs are alike in some manner and hence form a group.&nbsp;Which word pair does not belong to that group?<br>(The words must be considered as meaningful English words and must not be&nbsp;grouped based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>5. निम्नलिखित चार शब्द युग्मों में से तीन शब्द युग्म किसी प्रकार से एक समान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा शब्द युग्म उस समूह से संबंधित नहीं है?<br>(शब्दों को अर्थपूर्ण हिंदी शब्द माना जाना चाहिए और इन्हें शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर समूहीकृत नहीं किया जाना चाहिए।)</p>",
                    options_en: [
                        "<p>Ball - Sphere</p>",
                        "<p>Birthday hat - Cone</p>",
                        "<p>Television - Triangle</p>",
                        "<p>Ring - Circle</p>"
                    ],
                    options_hi: [
                        "<p>गेंद - गोला</p>",
                        "<p>जन्मदिन की टोपी - शंकु</p>",
                        "<p>टेलीविजन - त्रिभुज</p>",
                        "<p>अँगूठी - वृत्त</p>"
                    ],
                    solution_en: "<p>5.(c) As Ball is sphere , birthday hat is cone , ring is circle but television is not of triangle shape.</p>",
                    solution_hi: "<p>5.(c)) जैसे गेंद गोलाकार है, जन्मदिन की टोपी शंकु है, अंगूठी वृत्त है लेकिन टेलीविजन त्रिभुज आकार का नहीं है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What should come in place of the question mark (?) in the following series based on the English alphabetical order ?<br>YDY, WGT ,UJO ,SMJ, ?</p>",
                    question_hi: "<p>6. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई शृंखला में प्रश्न-चिह्न (?) के स्&zwj;थान पर क्या आएगा?<br>YDY , WGT , UJO , SMJ , ?</p>",
                    options_en: [
                        "<p>PPD</p>",
                        "<p>PPF</p>",
                        "<p>PPE</p>",
                        "<p>QPE</p>"
                    ],
                    options_hi: [
                        "<p>PPD</p>",
                        "<p>PPF</p>",
                        "<p>PPE</p>",
                        "<p>QPE</p>"
                    ],
                    solution_en: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220416.png\" alt=\"rId20\" height=\"100\"></p>",
                    solution_hi: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220416.png\" alt=\"rId20\" height=\"100\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the lettercluster that is different.<br>Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>7. चार अक्षर-समूह दिए गए हैं जिनमें से तीन किसी न किसी रूप में एक समान हैं, और एक उनसे असंगत है। उस असंगत अक्षर-समूह का चयन कीजिए।&nbsp;<br>नोट: अक्षर समूह में, असंगत व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: [
                        "<p>TWZ</p>",
                        "<p>MPS</p>",
                        "<p>DGJ</p>",
                        "<p>KNP</p>"
                    ],
                    options_hi: [
                        "<p>TWZ</p>",
                        "<p>MPS</p>",
                        "<p>DGJ</p>",
                        "<p>KNP</p>"
                    ],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220621.png\" alt=\"rId21\" height=\"60\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220720.png\" alt=\"rId22\" height=\"60\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220829.png\" alt=\"rId23\" height=\"60\"><br>But <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220939.png\" alt=\"rId24\" height=\"60\"></p>",
                    solution_hi: "<p>7. (d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220621.png\" alt=\"rId21\" height=\"60\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220720.png\" alt=\"rId22\" height=\"60\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220829.png\" alt=\"rId23\" height=\"60\"><br>परंतु <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013220939.png\" alt=\"rId24\" height=\"60\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Read the given statements and conclusions carefully. Assuming that the information&nbsp;given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements</strong> :<br>All rings are bangles.<br>Some bangles are anklets.<br>No anklet is a pendant.<br><strong>Conclusions</strong> :<br>(I) All rings being anklets is a possibility.<br>(II) All bangles can never be pendants.</p>",
                    question_hi: "<p>8. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है,&nbsp;भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्णय लें कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करतेहैं।<br><strong>कथन</strong> :<br>सभी अंगूठियाँ, चूड़ियाँ हैं।<br>कुछ चूड़ियाँ, पायल हैं।<br>कोई भी पायल, झुमका नहीं है।<br><strong>निष्कर्ष</strong> :<br>(I) सभी अंगूठियों के पायल होने की संभावना है।<br>(II) सभी चूड़ियाँ कभी भी झुमके नहीं हो सकती हैं।</p>",
                    options_en: [
                        "<p>Only conclusion II follows</p>",
                        "<p>None of the conclusions follow</p>",
                        "<p>Only conclusion I follows</p>",
                        "<p>Both conclusions I and II follow</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                        "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है</p>",
                        "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं</p>"
                    ],
                    solution_en: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013221124.png\" alt=\"rId25\" height=\"95\"><br>Both conclusion I and II follow.</p>",
                    solution_hi: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013221402.png\" alt=\"rId26\" height=\"95\"><br>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last. How would the paper look when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013221692.png\" alt=\"rId27\" height=\"85\"></p>",
                    question_hi: "<p>9. कागज की एक वर्गाकार शीट को दर्शाई गई दिशाओ में बिंदीदार रेखा पर अनुक्रमशः मोड़ा जाता है और आखिर में उसमें छेद किया जाता है। खोलने पर यह कागज कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013221692.png\" alt=\"rId27\" height=\"85\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013221911.png\" alt=\"rId28\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222083.png\" alt=\"rId29\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222258.png\" alt=\"rId30\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222358.png\" alt=\"rId31\" height=\"85\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013221911.png\" alt=\"rId28\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222083.png\" alt=\"rId29\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222258.png\" alt=\"rId30\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222358.png\" alt=\"rId31\" height=\"85\"></p>"
                    ],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013221911.png\" alt=\"rId28\" height=\"85\"></p>",
                    solution_hi: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013221911.png\" alt=\"rId28\" height=\"85\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10. In a certain language, \'protect your health\' is written as \'Df Bk Tu\' and \'health is wealth\' is written as \'Po Tu Le\'. How is \'health\' written in the given language?",
                    question_hi: "10. एक निश्चित कूट भाषा में, \'protect your health\' को Df Bk Tu\' लिखा जाता है और \'health is wealth\' को \'Po Tu Le\' लिखा जाता है। उस कूट भाषा में \'health\' को कैसे लिखा जाएगा? ",
                    options_en: [
                        " Po",
                        " Tu",
                        " Df",
                        " Bk"
                    ],
                    options_hi: [
                        " Po",
                        " Tu",
                        " Df",
                        " Bk"
                    ],
                    solution_en: "<p>10. (b)&nbsp;Protect your health &rarr; Df Bk Tu&hellip;.. (i)<br>Health is wealth &rarr; Po Tu Le&hellip;&hellip; (ii)<br>From (i) and (ii) &lsquo;Health&rsquo; and &lsquo;Tu&rsquo; are common. Hence Code of Health = &lsquo;Tu&rsquo;.</p>",
                    solution_hi: "<p>10.(b)&nbsp;Protect your health &rarr; Df Bk Tu&hellip;.. (i)<br>Health is wealth &rarr; Po Tu Le&hellip;&hellip; (ii)<br>(i) और (ii) से \'Health\' और \'Tu\' उभयनिष्ठ हैं। अतः &lsquo;Health&rsquo; का कोड = \'Tu\' है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language,<br>&lsquo;M &amp; N&rsquo; means &lsquo;M is the wife of N&rsquo;,<br>&lsquo;M @ N&rsquo; means &lsquo;M is the son of N&rsquo;,<br>&lsquo;M $ N&rsquo; means &lsquo;M is the mother of N&rsquo;.<br>Based on the above, how is R related to O if &lsquo;N $ O @ P @ Q &amp; R&rsquo;?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में,<br>M &amp; N का अर्थ, M, N की पत्नी है\',<br>\'M @ N\' का अर्थ, \'M, N का बेटा है\',<br>M $ N का अर्थ, M, N की माँ है\'।<br>उपरोक्त के आधार पर, यदि \'N $ O @ P @ Q &amp; R\' है, तो R का O से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Father&rsquo;s brother</p>",
                        "<p>Mother&rsquo;s brother</p>",
                        "<p>Mother&rsquo;s father</p>",
                        "<p>Father&rsquo;s father</p>"
                    ],
                    options_hi: [
                        "<p>पिता का भाई</p>",
                        "<p>माँ का भाई</p>",
                        "<p>माँ के पिता</p>",
                        "<p>पिता के पिता</p>"
                    ],
                    solution_en: "<p>11.(d)<br><strong id=\"docs-internal-guid-d1a4200c-7fff-bc54-5446-5c9dda41cad7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfAvBxY-b5rJfH8nwu2v5tZXNvWt6R35C-990uGDgfRU6FUrHWydHR1hvOsL5cpSda6AoBrYRFQ95c-uG3Qzb9ULX8gslpXDeniwQI5ZAgJJKag5NPapoaRXx7aTlcuWDZBxiOO_g?key=zrUD8u50uQAVwURkRI-hrFUA\" width=\"166\" height=\"158\"></strong><br>R is the father&rsquo;s father of O.</p>",
                    solution_hi: "<p>11.(d) <br><strong id=\"docs-internal-guid-d1a4200c-7fff-bc54-5446-5c9dda41cad7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfAvBxY-b5rJfH8nwu2v5tZXNvWt6R35C-990uGDgfRU6FUrHWydHR1hvOsL5cpSda6AoBrYRFQ95c-uG3Qzb9ULX8gslpXDeniwQI5ZAgJJKag5NPapoaRXx7aTlcuWDZBxiOO_g?key=zrUD8u50uQAVwURkRI-hrFUA\" width=\"166\" height=\"158\"></strong><br>R, O के पिता का पिता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Two different positions of the same dice with faces Z, G, M, F, H and S are shown below. Select the letter that will be on the face opposite to the one having G. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222620.png\" alt=\"rId33\" width=\"179\" height=\"107\"></p>",
                    question_hi: "<p>12. Z, G, M, F, H और S फलकों वाले एक ही पासे की दो अलग-अलग स्थितियाँ नीचे दिखाई गई हैं। उस अक्षर का चयन कीजिए जो G अक्षर वाले फलक के विपरीत फलक पर होगा। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222620.png\" alt=\"rId33\" width=\"179\" height=\"107\"></p>",
                    options_en: [
                        "<p>H</p>",
                        "<p>M</p>",
                        "<p>S</p>",
                        "<p>F</p>"
                    ],
                    options_hi: [
                        "<p>H</p>",
                        "<p>M</p>",
                        "<p>S</p>",
                        "<p>F</p>"
                    ],
                    solution_en: "<p>12.(d)&nbsp;From both the dice the opposite faces are <br>Z &harr; S , M &harr; H, G &harr; F</p>",
                    solution_hi: "<p>12.(d)&nbsp;दोनों पासों के विपरीत फलक हैं <br>Z &harr; S , M &harr; H, G &harr; F</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option in which the given figure is embedded (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222730.png\" alt=\"rId34\" height=\"90\"></p>",
                    question_hi: "<p>13. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति सन्निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222730.png\" alt=\"rId34\" height=\"90\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222872.png\" alt=\"rId35\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223025.png\" alt=\"rId36\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223162.png\" alt=\"rId37\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223346.png\" alt=\"rId38\" height=\"85\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013222872.png\" alt=\"rId35\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223025.png\" alt=\"rId36\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223162.png\" alt=\"rId37\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223346.png\" alt=\"rId38\" height=\"85\"></p>"
                    ],
                    solution_en: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223500.png\" alt=\"rId39\" height=\"85\"></p>",
                    solution_hi: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223500.png\" alt=\"rId39\" height=\"85\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the option that represents the letters that when sequentially placed from left to right in the blanks below will complete the letter series.<br>_ P _ J _ P _ P _ _ P _</p>",
                    question_hi: "<p>14. उस विकल्प का चयन कीजिए जो उन अक्षरों को प्रदर्शित करता है जिन्हें नीचे दिए गए रिक्&zwj;त स्&zwj;थानों में बाएं से दाएं क्रमिक रूप से रखने पर अक्षर श्रृंखला पूर्ण हो जाएगी।<br>_ P _ J _ P _ P _ _ P _</p>",
                    options_en: [
                        "<p>JPPJPJP</p>",
                        "<p>PPJJJJJ</p>",
                        "<p>JPJPJPJ</p>",
                        "<p>JJPPPPJ</p>"
                    ],
                    options_hi: [
                        "<p>JPPJPJP</p>",
                        "<p>PPJJJJJ</p>",
                        "<p>JPJPJPJ</p>",
                        "<p>JJPPPPJ</p>"
                    ],
                    solution_en: "<p>14.(a) <strong><span style=\"text-decoration: underline;\">J</span></strong> P <strong><span style=\"text-decoration: underline;\">P</span></strong> /J <strong><span style=\"text-decoration: underline;\">P</span></strong> P/ <strong><span style=\"text-decoration: underline;\">J</span></strong> P <strong><span style=\"text-decoration: underline;\">P</span></strong> /<strong><span style=\"text-decoration: underline;\">J</span></strong> P <strong><span style=\"text-decoration: underline;\">P</span></strong></p>",
                    solution_hi: "<p>14.(a) <strong><span style=\"text-decoration: underline;\">J</span></strong> P <strong><span style=\"text-decoration: underline;\">P</span></strong> /J <strong><span style=\"text-decoration: underline;\">P</span></strong> P/ <strong><span style=\"text-decoration: underline;\">J</span></strong> P <strong><span style=\"text-decoration: underline;\">P</span></strong> /<strong><span style=\"text-decoration: underline;\">J</span></strong> P <strong><span style=\"text-decoration: underline;\">P</span></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Seven people R, S, T, D, E, F and G are sitting around a circular table, facing the centre (but not necessarily in the same order). G is sitting second to the right of E. Only one person is sitting between G and R. Only one person is sitting between R and T. F is sitting second to the left of D. D is an immediate neighbour of T. How many people are sitting between S and T when counted from the left of S ?</p>",
                    question_hi: "<p>15. सात व्यक्ति R, S, T, D, E, F और G एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। G, E के दाएँ से दूसरे स्थान पर बैठा है। G और R के बीच में केवल एक व्यक्ति बैठा है। R और T के बीच में केवल एक व्यक्ति बैठा है। F, D के बाएँ से दूसरे स्थान पर बैठा है। D, T का निकटतम पड़ोसी है। S के बाईं ओर से गिनती करने पर S और T के बीच में कितने व्यक्ति बैठे हैं?</p>",
                    options_en: [
                        "<p>Three</p>",
                        "<p>Two</p>",
                        "<p>One</p>",
                        "<p>Four</p>"
                    ],
                    options_hi: [
                        "<p>तीन</p>",
                        "<p>दो</p>",
                        "<p>एक</p>",
                        "<p>चार</p>"
                    ],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223668.png\" alt=\"rId40\" width=\"153\" height=\"150\"></p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223668.png\" alt=\"rId40\" width=\"153\" height=\"150\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. If &lsquo;A&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&ndash;&rsquo;, then the&nbsp;resultant of which of the following will be 1049 ?</p>",
                    question_hi: "<p>16. यदि &lsquo;A&rsquo; का अर्थ &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; का अर्थ &lsquo;&times;&rsquo;, &lsquo;C&rsquo; का अर्थ &lsquo;+&rsquo; और &lsquo;D&rsquo; का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित में से&nbsp;किसका परिणाम 1049 होगा ?</p>",
                    options_en: [
                        "<p>94 D 11 B 180 A 4 C 60</p>",
                        "<p>94 A 11 D 180 B 4 C 60</p>",
                        "<p>94 B 11 D 180 A 4 C 60</p>",
                        "<p>94 C 11 D 180 A 4 B 60</p>"
                    ],
                    options_hi: [
                        "<p>94 D 11 B 180 A 4 C 60</p>",
                        "<p>94 A 11 D 180 B 4 C 60</p>",
                        "<p>94 B 11 D 180 A 4 C 60</p>",
                        "<p>94 C 11 D 180 A 4 B 60</p>"
                    ],
                    solution_en: "<p>16.(c) After going through all the options, option c satisfies. <br>94 B 11 D 180 A 4 C 60<br>As per given instruction after interchanging the letter with sign we get,<br>94 &times; 11 - 180 &divide;&nbsp;4 + 60<br>1034 - 45 + 60 = 1049</p>",
                    solution_hi: "<p>16.(c) सभी विकल्पों जांच करने पर विकल्प c संतुष्ट करता है।<br>94 B 11 D 180 A 4 C 60<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने के बाद हमें प्राप्त होता है,<br>94 &times; 11 - 180 &divide;&nbsp;4 + 60<br>1034 - 45 + 60 = 1049</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. What should come in place of the question mark (?) in the given series?<br>647&nbsp; &nbsp;614&nbsp; &nbsp;581&nbsp; &nbsp;548&nbsp; &nbsp;515&nbsp; &nbsp;?</p>",
                    question_hi: "<p>17. दी गई श्रृंखला में प्रश्नचिन्ह (?) के स्थान पर क्या आएगा?<br>647&nbsp; &nbsp;614&nbsp; &nbsp;581&nbsp; &nbsp;548&nbsp; &nbsp;515&nbsp; &nbsp;?</p>",
                    options_en: [
                        "<p>491</p>",
                        "<p>505</p>",
                        "<p>482</p>",
                        "<p>402</p>"
                    ],
                    options_hi: [
                        "<p>491</p>",
                        "<p>505</p>",
                        "<p>482</p>",
                        "<p>402</p>"
                    ],
                    solution_en: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223857.png\" alt=\"rId41\" width=\"328\" height=\"62\"></p>",
                    solution_hi: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013223857.png\" alt=\"rId41\" width=\"328\" height=\"62\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. The position of how many letters will remain unchanged if each of the letter in the word &lsquo;JANITOR&rsquo; is arranged in English alphabetical order ?</p>",
                    question_hi: "<p>18. यदि शब्द \'JANITOR\' के प्रत्येक अक्षर को अंग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित रहेगी ?</p>",
                    options_en: [
                        "<p>Three</p>",
                        "<p>Two</p>",
                        "<p>One</p>",
                        "<p>Zero</p>"
                    ],
                    options_hi: [
                        "<p>तीन</p>",
                        "<p>दो</p>",
                        "<p>एक</p>",
                        "<p>शून्&zwj;य</p>"
                    ],
                    solution_en: "<p>18.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013224108.png\" alt=\"rId42\" width=\"278\" height=\"144\"><br>The position of all the letters are changed.</p>",
                    solution_hi: "<p>18.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013224108.png\" alt=\"rId42\" width=\"278\" height=\"144\"><br>सभी अक्षरों का स्थान बदल दिया गया है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In a certain code language, &lsquo;SING&rsquo; is written as &lsquo;22121710&rsquo; and &lsquo;FOUR&rsquo; is written as &lsquo;9182421&rsquo;. How will &lsquo;BEAN&rsquo; be written in that language ?</p>",
                    question_hi: "<p>19. एक निश्चित कूट भाषा में &lsquo;SING&rsquo; को &lsquo;22121710&rsquo; और &lsquo;FOUR&rsquo; को &lsquo;9182421&rsquo; के रूप में लिखा जाता है। उस भाषा में &lsquo;BEAN&rsquo; कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>68418</p>",
                        "<p>58417</p>",
                        "<p>69516</p>",
                        "<p>57413</p>"
                    ],
                    options_hi: [
                        "<p>68418</p>",
                        "<p>58417</p>",
                        "<p>69516</p>",
                        "<p>57413</p>"
                    ],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013224253.png\" alt=\"rId43\" height=\"100\">,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013224443.png\" alt=\"rId44\" height=\"100\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013224584.png\" alt=\"rId45\" height=\"100\"></p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013224253.png\" alt=\"rId43\" height=\"100\">,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013224443.png\" alt=\"rId44\" height=\"100\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013224584.png\" alt=\"rId45\" height=\"100\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. The average age of Ruby and Soni is 40 years. The ratio of their ages is 11: 5, respectively. What is the age (in years) of Soni ?</p>",
                    question_hi: "<p>20. रूबी और सोनी की औसत आयु 40 वर्ष है। उनकी आयु का अनुपात क्रमशः 11 : 5 है। सोनी की आयु (वर्षों में) कितनी है ?</p>",
                    options_en: [
                        "<p>25</p>",
                        "<p>15</p>",
                        "<p>55</p>",
                        "<p>30</p>"
                    ],
                    options_hi: [
                        "<p>25</p>",
                        "<p>15</p>",
                        "<p>55</p>",
                        "<p>30</p>"
                    ],
                    solution_en: "<p>20.(a)&nbsp;Sum of the age of Ruby and Soni = 40 &times; 2 = 80 yrs<br>ATQ, <br>11x + 5x = 80<br>16x = 80<br>&rArr; x = 5<br>So, the age of Soni = 5x = 5 &times; 5 = 25 yrs</p>",
                    solution_hi: "<p>20.(a)&nbsp;रूबी और सोनी की आयु का योग = 40 &times; 2 = 80 वर्ष<br>प्रश्न के अनुसार,<br>11x + 5x = 80<br>16x = 80<br>&rArr; x = 5<br>तो, सोनी की उम्र = 5x = 5 &times; 5 = 25 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. &lsquo;Tame&rsquo; is related to &lsquo;Wild&rsquo; in the same way as &lsquo;Adjacent&rsquo; is related to &lsquo;________&rsquo;. <br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>21. \'पालतू\' (Tame), \'जंगली\' (Wild) से उसी प्रकार संबंधित है, जिस प्रकार \'निकटवर्ती\' (Adjacent) \'________\' से संबंधित है।<br>(शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्&zwj;दों को अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)</p>",
                    options_en: [
                        "<p>Close</p>",
                        "<p>Distant</p>",
                        "<p>Accommodating</p>",
                        "<p>Neighbouring</p>"
                    ],
                    options_hi: [
                        "<p>नजदीक (Close)</p>",
                        "<p>सुदूरवर्ती (Distant)</p>",
                        "<p>मिलनसार (Accommodating)</p>",
                        "<p>समीपवर्ती (Neighbouring)</p>"
                    ],
                    solution_en: "<p>21.(b)&nbsp; As &lsquo;Tame&rsquo; is the opposite of &lsquo;Wild&rsquo; similarly &lsquo;Adjacent&rsquo; is the opposite of &lsquo;Distant&rsquo;.</p>",
                    solution_hi: "<p>21.(b)&nbsp;जैसे \'पालतू\' (Tame) \'जंगली\' (Wild) का विपरीत है, उसी प्रकार \'निकटवर्ती\' (Adjacent) ,&lsquo;सुदूरवर्ती &lsquo; (Distant) का विपरीत है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. If 19 July 2000 is Wednesday, then what will be the day of the week on 17 March 2015 ?</p>",
                    question_hi: "<p>22. यदि 19 जुलाई 2000 को बुधवार है, तो 17 मार्च 2015 को सप्ताह का कौन-सा दिन होगा ?</p>",
                    options_en: [
                        "<p>Wednesday</p>",
                        "<p>Sunday</p>",
                        "<p>Friday</p>",
                        "<p>Tuesday</p>"
                    ],
                    options_hi: [
                        "<p>बुधवार</p>",
                        "<p>रविवार</p>",
                        "<p>शुक्रवार</p>",
                        "<p>मंगलवार</p>"
                    ],
                    solution_en: "<p>22.(d) 19 July 2000 is Wednesday. On moving to 2015 the number of odd days = <br>+ 1 + 1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 +1 + 2 + 1 + 1 + 1 = 18. On dividing 18 by 7 the remainder = 4. Wednesday + 4 = Sunday . We have reached till 19 July 2015, but we have to go to 17 March 2015, number of days in between = 19 + 30 + 31 + 30 + 14 = 124.On dividing 124 by 7 the remainder = 5. Sunday - 5 = Tuesday.</p>",
                    solution_hi: "<p>22.(d) 19 जुलाई 2000 को बुधवार है. 2015 में जाने पर विषम दिनों की संख्या = <br>+ 1 + 1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 +1 + 2 + 1 + 1 + 1 = 18. 18 को 7 से विभाजित करने पर शेषफल = 4. बुधवार + 4 = रविवार. हम 19 जुलाई 2015 तक पहुंच गए हैं, लेकिन हमें 17 मार्च 2015 तक जाना है, बीच में दिनों की संख्या = 19 + 30 + 31 + 30 + 14 = 124. 124 को 7 से विभाजित करने पर शेषफल = 5. रविवार - 5 = मंगलवार।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. The second number in the given number-pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) is/are followed in all the number-pairs, except one. Find that odd number-pair.<br>(NOTE: The relation should be found without breaking down the numbers into its constituent digits)</p>",
                    question_hi: "<p>23. दिए गए संख्या-युग्मों में दूसरी संख्या, पहली संख्या पर कुछ गणितीय संक्रिया/संक्रियाएँ करके प्राप्त की गई है। एक को छोड़कर सभी संख्या-युग्मों में समान संक्रिया/संक्रियाएं की गई है/हैं। वह असंगत संख्या-युग्म ज्ञात कीजिए। <br>(नोट: संख्याओं को उसके घटक अंकों में विभाजित किए बिना संबंध ज्ञात किया जाना चाहिए)</p>",
                    options_en: [
                        "<p>18 : 306</p>",
                        "<p>25 : 650</p>",
                        "<p>23 : 552</p>",
                        "<p>13 : 182</p>"
                    ],
                    options_hi: [
                        "<p>18 : 306</p>",
                        "<p>25 : 650</p>",
                        "<p>23 : 552</p>",
                        "<p>13 : 182</p>"
                    ],
                    solution_en: "<p>23.(a)<br><strong>Logic:-</strong> (1st no.) &times; (1st no.+ 1) = 3rd no.<br>(25 : 650):- (25) &times; (25 + 1) &rArr; 25 &times; 26 = 650<br>(23 : 552):- (23) &times; (23 + 1) &rArr; 23 &times; 24 = 552<br>(13 : 182):- (13) &times; (13 + 1) &rArr; 13 &times; 14 = 182<br>but<br>(18 : 306):- (18) &times; (18 + 1) &rArr; 18 &times; 19 = 342 &ne; 306</p>",
                    solution_hi: "<p>23.(a)<br><strong>तर्क:-</strong> (पहली संख्या) &times;&nbsp;(पहली संख्या+1) = तीसरी संख्या<br>(25 : 650):- (25) &times; (25 + 1) &rArr; 25 &times; 26 = 650<br>(23 : 552):- (23) &times; (23 + 1) &rArr; 23 &times; 24 = 552<br>(13 : 182):- (13) &times; (13 + 1) &rArr; 13 &times; 14 = 182<br>लेकिन<br>(18 : 306):- (18) &times; (18 + 1) &rArr; 18 &times; 19 = 342 &ne; 306</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013224940.png\" alt=\"rId46\" width=\"156\" height=\"123\"></p>",
                    question_hi: "<p>24. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013224940.png\" alt=\"rId46\" width=\"156\" height=\"123\"></p>",
                    options_en: [
                        "<p>14</p>",
                        "<p>13</p>",
                        "<p>16</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>14</p>",
                        "<p>13</p>",
                        "<p>16</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013225065.png\" alt=\"rId47\" width=\"178\" height=\"181\"><br>JKI, KLB, KML, KCB, KFB, KIF ,LMB, BCF, ABF, GEF, DEF, GDF, FGH, IGH, IHF<br>There are 15 triangles.</p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013225065.png\" alt=\"rId47\" width=\"178\" height=\"181\"><br>JKI, KLB, KML, KCB, KFB, KIF ,LMB, BCF, ABF, GEF, DEF, GDF, FGH, IGH, IHF<br>15 त्रिभुज हैं.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. 19 is related to 76 following a certain logic. Following the same logic, 27 is related to 108. To which of the following is 49 related, following the same logic? <br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>25. एक निश्चित तर्क का अनुसरण करते हुए 19, 76 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 27, 108 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 49 निम्नलिखित में से किससे संबंधित है?<br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: [
                        "<p>190</p>",
                        "<p>198</p>",
                        "<p>202</p>",
                        "<p>196</p>"
                    ],
                    options_hi: [
                        "<p>190</p>",
                        "<p>198</p>",
                        "<p>202</p>",
                        "<p>196</p>"
                    ],
                    solution_en: "<p>25.(d) <strong>Logic:-</strong> &nbsp;(1st no.)&nbsp;&times; 4 = 2nd no.<br>(19, 76) :- 19 &times;&nbsp;4 = 76<br>(27, 108) :- 27 &times;&nbsp;4 = 108<br>similarly<br>(49, ?) :- 49 &times;&nbsp;4 = 196</p>",
                    solution_hi: "<p>25.(d)&nbsp;<strong>तर्क:-</strong> (पहली संख्या ) &times;&nbsp;4 = दूसरी संख्या <br>(19, 76) :- 19 &times;&nbsp;4 = 76<br>(27, 108) :- 27 &times;&nbsp;4 = 108<br>इसी प्रकार, <br>(49, ?) :- 49 &times;&nbsp;4 = 196</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. In 1883, which of the following allowed Indians to try Europeans in courts of law?</p>",
                    question_hi: "<p>26. 1883 में, निम्नलिखित में से किसने भारतीयों को न्यायालयों में यूरोपीय लोगों पर मुकदमा चलाने की अनुमति दी थी?</p>",
                    options_en: [
                        "<p>Christian Personal Law</p>",
                        "<p>Ilbert Bill</p>",
                        "<p>Indian Penal Code</p>",
                        "<p>Murderous Outrage Regulation</p>"
                    ],
                    options_hi: [
                        "<p>ईसाई पर्सनल लॉ</p>",
                        "<p>इल्बर्ट बिल</p>",
                        "<p>भारतीय दंड संहिता</p>",
                        "<p>जानलेवा आक्रोश विनियमन</p>"
                    ],
                    solution_en: "<p>26.(b) <strong>Ilbert Bill.</strong> The Ilbert Bill, introduced by Sir Courtenay Ilbert in 1883, during the viceroyship of Lord Ripon. It sought to empower Indian judges to try British and European citizens in criminal cases.</p>",
                    solution_hi: "<p>26.(b) <strong>इल्बर्ट बिल,</strong> 1883 में लॉर्ड रिपन के वायसराय काल के दौरान सर कोर्टेने इल्बर्ट द्वारा पेश किया गया था । इसने भारतीय न्यायाधीशों को ब्रिटिश और यूरोपीय नागरिकों के खिलाफ आपराधिक मामलों में मुकदमा चलाने का अधिकार देने का प्रयास किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. The Socio-Economic and Caste Census (SECC) 2011 conducted by the government of India ranked households in how many categories ?</p>",
                    question_hi: "<p>27. भारत सरकार द्वारा आयोजित सामाजिक-आर्थिक और जातिगत जनगणना (Socio-Economic and Caste Census, SECC) 2011 ने परिवारों को कितनी श्रेणियों में स्थान दिया।</p>",
                    options_en: [
                        "<p>Five</p>",
                        "<p>Six</p>",
                        "<p>Three</p>",
                        "<p>Four</p>"
                    ],
                    options_hi: [
                        "<p>पांच</p>",
                        "<p>छः</p>",
                        "<p>तीन</p>",
                        "<p>चार</p>"
                    ],
                    solution_en: "<p>27.(c) <strong>Three.</strong> SECC 2011 included the following three components of the census: Rural census - It was conducted by the department of rural development. Urban census - It was conducted by the Ministry of Housing and Urban Poverty Alleviation. Caste census - It was conducted by the Office of the Registrar General and Census Commissioner of India, under the Ministry of Home Affairs.</p>",
                    solution_hi: "<p>27.(c) <strong>तीन।</strong> SECC 2011 में जनगणना के निम्नलिखित तीन घटक शामिल थे: ग्रामीण जनगणना - यह ग्रामीण विकास विभाग द्वारा आयोजित की गई थी। शहरी जनगणना - यह आवास और शहरी गरीबी उपशमन मंत्रालय द्वारा आयोजित की गई थी। जाति जनगणना - यह गृह मंत्रालय के रजिस्ट्रार जनरल और भारत के जनगणना आयुक्त द्वारा आयोजित की गई थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who among the following was the Viceroy of British India when the Shimla conference was held in 1945?</p>",
                    question_hi: "<p>28. 1945 में शिमला सम्मेलन आयोजित होने के समय ब्रिटिश भारत का वायसराय निम्नलिखित में से कौन था?</p>",
                    options_en: [
                        "<p>Lord Linlithgow</p>",
                        "<p>Lord Willingdon</p>",
                        "<p>Lord Irwin</p>",
                        "<p>Lord Wavell</p>"
                    ],
                    options_hi: [
                        "<p>लॉर्ड लिनलिथगो (Lord Linlithgow)</p>",
                        "<p>लॉर्ड विलिंगडन (Lord Willingdon)</p>",
                        "<p>लॉर्ड इरविन (Lord Irwin)</p>",
                        "<p>लॉर्ड वेवेल (Lord Wavell)</p>"
                    ],
                    solution_en: "<p>28.(d) <strong>Lord Wavell</strong> (1944 to 1947). The Shimla Conference was held from June 25 to July 14, 1945, at Viceregal Lodge in Shimla, India. Lord Linlithgow served as Viceroy from 1936 to 1944. Lord Willingdon served as Viceroy from 1931 to 1936. Lord Irwin served as Viceroy from 1926 to 1931.</p>",
                    solution_hi: "<p>28.(d) <strong>लॉर्ड वेवेल</strong> (Lord Wavell - 1944 से 1947 तक)। शिमला सम्मेलन 25 जून से 14 जुलाई, 1945 तक भारत के शिमला में वाइसरीगल लॉज में आयोजित किया गया था। लॉर्ड लिनलिथगो ने 1936 से 1944 तक वायसराय के रूप में कार्य किया। लॉर्ड विलिंग्डन ने 1931 से 1936 तक वायसराय के रूप में कार्य किया। लॉर्ड इरविन ने 1926 से 1931 तक वायसराय के रूप में कार्य किया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. A housemaid gets her 12-year-old daughter to help in her work as a house cleaner at a residential colony instead of sending her to school. Which fundamental duty is violated by the housemaid ?</p>",
                    question_hi: "<p>29. एक घरेलू नौकरानी अपनी 12 वर्षीय बेटी को स्कूल भेजने के बजाय एक आवासीय कॉलोनी में घर की सफ़ाई के काम में मदद करवाती है। नौकरानी द्वारा किस मौलिक कर्तव्य का उल्लंघन किया जाता है?</p>",
                    options_en: [
                        "<p>To provide opportunities for education to his/her child</p>",
                        "<p>To promote harmony and the spirit of common brotherhood</p>",
                        "<p>To defend the country and render national service</p>",
                        "<p>To safeguard public property and to abjure violence</p>"
                    ],
                    options_hi: [
                        "<p>अपने बच्चे को शिक्षा के अवसर प्रदान करना</p>",
                        "<p>समरसता और समान भ्रातृत्व की भावना को बढ़ावा देना</p>",
                        "<p>देश की रक्षा करना और राष्ट्र की सेवा करना</p>",
                        "<p>सार्वजनिक संपत्ति की सुरक्षा करना और हिंसा से दूर रहना</p>"
                    ],
                    solution_en: "<p>29.(a) <strong>To provide opportunities for education to his/her child</strong>. Fundamental duty (Part IV-A, Article 51-A) incorporated by the 42nd Constitutional Amendment Act, 1976 on the recommendations of the Swaran Singh Committee. It mandates that parents should provide educational opportunities for their children between the ages of six and fourteen years (added by the 86th Constitutional Amendment Act, 2002).</p>",
                    solution_hi: "<p>29.(a) <strong>अपने बच्चे को शिक्षा के अवसर प्रदान करना</strong>। मौलिक कर्तव्य (भाग IV-A, अनुच्छेद 51-A) स्वर्ण सिंह समिति की सिफारिशों पर 42वें संविधान संशोधन अधिनियम, 1976 द्वारा शामिल किया गया। इसमें यह प्रावधान किया गया है कि माता-पिता को अपने छह से चौदह वर्ष की आयु के बच्चों के लिए शिक्षा के अवसर उपलब्ध कराने चाहिए (86वें संविधान संशोधन अधिनियम, 2002 द्वारा जोड़ा गया)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which Article of the Constitution explains that the executive power of every state shall be so exercised as not to impede or prejudice the exercise of the executive power of the Union, and the executive power of the union shall extend to the giving of such directions to a state as may appear to the Government of India to be necessary for that purpose?</p>",
                    question_hi: "<p>30. संविधान का कौन-सा अनुच्छेद स्पष्ट करता है कि प्रत्येक राज्य की कार्यकारी शक्ति का प्रयोग इस प्रकार किया जाएगा कि संघ की कार्यकारी शक्ति के प्रयोग में बाधा या प्रतिकूल प्रभाव न पड़े, और संघ की कार्यकारी शक्ति किसी राज्य को ऐसे निर्देश देने तक विस्तारित होगी जो भारत सरकार को उस उद्देश्य के लिए आवश्यक प्रतीत होता हो?</p>",
                    options_en: [
                        "<p>Article 261</p>",
                        "<p>Article 247</p>",
                        "<p>Article 257</p>",
                        "<p>Article 123</p>"
                    ],
                    options_hi: [
                        "<p>अनुच्छेद 261</p>",
                        "<p>अनुच्छेद 247</p>",
                        "<p>अनुच्छेद 257</p>",
                        "<p>अनुच्छेद 123</p>"
                    ],
                    solution_en: "<p>30.(c) <strong>Article 257</strong> - Control of the Union over States in certain cases. Other Articles: Article 123 - Power of President to promulgate Ordinances during recess of Parliament. Article 247 - Power of Parliament to provide for the establishment of certain additional courts. Article 261 - Public acts, records and judicial proceedings.</p>",
                    solution_hi: "<p>30.(c) <strong>अनुच्छेद 257</strong> - कुछ मामलों में राज्यों पर संघ का नियंत्रण। अन्य अनुच्छेद: अनुच्छेद 123 - संसद के अवकाश के दौरान अध्यादेश जारी करने की राष्ट्रपति की शक्ति। अनुच्छेद 247 - कुछ अतिरिक्त न्यायालयों की स्थापना के लिए संसद की शक्ति। अनुच्छेद 261 - सार्वजनिक कार्य, अभिलेख और न्यायिक कार्यवाही।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following statements most accurately describes the planet Saturn?</p>",
                    question_hi: "<p>31. निम्नलिखित में से कौन-सा कथन शनि ग्रह का सबसे सटीक वर्णन करता है?</p>",
                    options_en: [
                        "<p>It is a gas giant made up mostly of hydrogen and helium and the density is less than that of water</p>",
                        "<p>Its surface is reddish in colour.</p>",
                        "<p>It was the seventh planet discovered in the Solar System.</p>",
                        "<p>Its cloud-like outer regions consist of methane in the gaseous form and ammonia in crystalline form.</p>"
                    ],
                    options_hi: [
                        "<p>यह एक गैस का बना विशाल पिंड है, जो मुख्य रूप से हाइड्रोजन और हीलियम से बना है और जिसका घनत्व पानी की तुलना में कम है।</p>",
                        "<p>इसकी सतह का रंग लाल होता है।</p>",
                        "<p>यह सौरमंडल में खोजा गया सातवां ग्रह था।</p>",
                        "<p>इसके बादल जैसे बाहरी क्षेत्रों में गैसीय रूप में मीथेन और क्रिस्टलीय रूप में अमोनिया पाई जाती है।</p>"
                    ],
                    solution_en: "<p>31.(a) Saturn\'s density (0.70 gm/cm<sup>3</sup>) is less than that of water, making it the least dense planet in the solar system. If there were a body of water large enough, Saturn would theoretically float. The reddish color is associated with the planet Mars. The presence of methane and ammonia is more characteristic of Uranus and Neptune.</p>",
                    solution_hi: "<p>31.(a) शनि का घनत्व (0.70 ग्राम/सेमी<sup>3</sup>) पानी से भी कम है, जिससे यह सौरमंडल का सबसे कम घनत्व वाला ग्रह बन गया है। यदि वहाँ पानी का कोई बड़ा भंडार होता तो सैद्धांतिक रूप से शनि ग्रह तैरता। लाल रंग का संबंध मंगल ग्रह से है। अरुण और वरुण में मीथेन और अमोनिया की उपस्थिति अधिक विशिष्ट है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. What is the term used for a group of people in India, who are always poor and those who are usually poor but who may sometimes have a little more money?</p>",
                    question_hi: "<p>32. भारत में लोगों के उस समूह के लिए किस शब्द उपयोग किया जाता है, जो हमेशा या सामान्य तौर पर निर्धन होते हैं लेकिन जिनके पास कभी-कभी थोड़ा-बहुत धन आ जाता है?</p>",
                    options_en: [
                        "<p>Relative poor</p>",
                        "<p>Churning poor</p>",
                        "<p>Chronic poor</p>",
                        "<p>Transient poor</p>"
                    ],
                    options_hi: [
                        "<p>सापेक्ष निर्धन (Relative poor)</p>",
                        "<p>निरंतर निर्धन (Churning poor)</p>",
                        "<p>चिरकालिक निर्धन (Chronic poor)</p>",
                        "<p>अल्पकालिक निर्धन (Transient poor)</p>"
                    ],
                    solution_en: "<p>32.(c) <strong>Chronic poor.</strong> Churning poor - People who regularly move in and out of poverty. Example - Small farmers and seasonal workers. Occasionally poor - Who are rich most of the time but may sometimes have a patch of bad luck. They are called the transient poor.</p>",
                    solution_hi: "<p>32.(c) <strong>चिरकालिक निर्धन</strong> I निरंतर निर्धन - जो लोग नियमित रूप से गरीबी के अंदर और बाहर आते रहते हैं। उदाहरण - छोटे किसान और मौसमी श्रमिक। कभी-कभी गरीब - जो ज्यादातर समय अमीर रहते हैं लेकिन कभी-कभी दुर्भाग्य का सामना कर सकते हैं। उन्हें अल्पकालिक निर्धन कहा जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. The Gross Domestic Product of a country is derived from the different sectors of the economy, namely ___________.</p>",
                    question_hi: "<p>33. किसी देश का सकल घरेलू उत्पाद अर्थव्यवस्था के विभिन्न क्षेत्रों, अर्थात् _________ से प्राप्त होता है।</p>",
                    options_en: [
                        "<p>commercial and trading sector</p>",
                        "<p>rural and urban sectors</p>",
                        "<p>agricultural sector, and the industrial sector only</p>",
                        "<p>agricultural sector, the industrial sector and the service sector</p>"
                    ],
                    options_hi: [
                        "<p>वाणिज्यिक और व्यापारिक क्षेत्रों</p>",
                        "<p>ग्रामीण और शहरी क्षेत्रों</p>",
                        "<p>केवल कृषि क्षेत्र, और औद्योगिक क्षेत्र</p>",
                        "<p>कृषि क्षेत्र, औद्योगिक क्षेत्र और सेवा क्षेत्र</p>"
                    ],
                    solution_en: "<p>33.(d) <strong>agricultural sector, the industrial sector and the service sector.</strong> Gross Domestic Product (GDP) is the monetary value of all finished goods and services made within a country during a specific period. Three Sectors of Economy : Primary Sector (agriculture, forestry, animal husbandry, fishing, poultry farming and mining), Secondary Sector (Quarrying and manufacturing of goods), Tertiary Sector (Trade, transport, banks, health, education and all types of services).</p>",
                    solution_hi: "<p>33.(d) <strong>कृषि क्षेत्र, औद्योगिक क्षेत्र और सेवा क्षेत्र</strong>। सकल घरेलू उत्पाद (GDP) एक निश्चित अवधि के दौरान किसी देश के भीतर बनाए गए सभी तैयार वस्तुओं और सेवाओं का मौद्रिक मूल्य है। अर्थव्यवस्था के तीन क्षेत्र: प्राथमिक क्षेत्र (कृषि, वानिकी, पशुपालन, मछली पकड़ना, मुर्गी पालन और खनन), द्वितीयक क्षेत्र (वस्तुओं का उत्खनन और विनिर्माण), तृतीयक क्षेत्र (व्यापार, परिवहन, बैंक, स्वास्थ्य, शिक्षा और सभी प्रकार की सेवाएँ)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which Indian singer wrote &lsquo;Raag Sarita&rsquo;?</p>",
                    question_hi: "<p>34. किस भारतीय गायक/गायिका ने \'राग सरिता\' लिखा था?</p>",
                    options_en: [
                        "<p>Kesarbai Kerkar</p>",
                        "<p>Balasaheb Poonchwale</p>",
                        "<p>Kumar Gandharva</p>",
                        "<p>Chintaman Raghunath Vyas</p>"
                    ],
                    options_hi: [
                        "<p>केसरबाई केरकर</p>",
                        "<p>बालासाहेब पूंछवाले</p>",
                        "<p>कुमार गंधर्व</p>",
                        "<p>चिंतामन रघुनाथ व्यास</p>"
                    ],
                    solution_en: "<p>34.(d) <strong>Chintaman Raghunath Vyas</strong> was an Indian classical singer, renowned for his khyal style. He received the Padma Bhushan in 1992 and the Sangeet Natak Akademi Award in 1987. He researched traditional ragas and bandishes, composing over 200 bandishes in various ragas under the pen name Gunijaan. In tribute to his guru, Gunidas, he founded the Gunidas Sangeet Sammelan in 1977.</p>",
                    solution_hi: "<p>34.(d) <strong>चिंतामन रघुनाथ व्यास</strong> एक भारतीय शास्त्रीय गायक थे, जो अपनी ख्याल शैली के लिए प्रसिद्ध थे। उन्हें 1992 में पद्म भूषण और 1987 में संगीत नाटक अकादमी पुरस्कार मिला। उन्होंने पारंपरिक रागों और बंदिशों पर शोध किया, और गुनीजान नाम से विभिन्न रागों में 200 से अधिक बंदिशों की रचना की। अपने गुरु गुनीदास को श्रद्धांजलि देने के लिए उन्होंने 1977 में गुनीदास संगीत सम्मेलन की स्थापना की।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. At absolute zero (0 K or -273.15&deg;C), the particles of matter:</p>",
                    question_hi: "<p>35. परम शून्य ताप (absolute zero - 0 K या -273.15&deg;C) पर, पदार्थ के कण______________|</p>",
                    options_en: [
                        "<p>Move at the speed of light</p>",
                        "<p>Vibrate in fixed positions</p>",
                        "<p>Undergo fusion</p>",
                        "<p>Stop moving</p>"
                    ],
                    options_hi: [
                        "<p>प्रकाश की चाल से गति करते हैं</p>",
                        "<p>निश्चित स्थितियों में कंपन करते हैं</p>",
                        "<p>विखंडित होते हैं</p>",
                        "<p>गति करना बंद कर देते हैं</p>"
                    ],
                    solution_en: "<p>35.(d) <strong>Stop moving.</strong> The entropy of any pure crystalline substance approaches zero as the temperature approaches absolute zero. This is called the third law of thermodynamics. This is so because there is perfect order in a crystal at absolute zero. The statement is&nbsp;confined to pure crystalline solids because theoretical arguments and practical evidence have shown that entropy of solutions and supercooled liquids is not zero at 0 K.</p>",
                    solution_hi: "<p>35.(d) <strong>गति करना बंद कर देते हैं।</strong> जैसे-जैसे तापमान परम शून्य के करीब पहुंचता है, किसी भी शुद्ध क्रिस्टलीय पदार्थ की एन्ट्रापी भी शून्य के करीब पहुंच जाती है। इसे ऊष्मागतिकी का तीसरा नियम कहा जाता है। ऐसा इसलिए है क्योंकि परम शून्य पर क्रिस्टल में पूर्ण क्रम (व्यवस्था) होता है। यह कथन शुद्ध क्रिस्टलीय ठोस पदार्थों तक ही सीमित है क्योंकि सैद्धांतिक तर्कों और व्यावहारिक साक्ष्यों से पता चलता है कि 0 K पर विलयनों और अतिशीतित द्रवों की एन्ट्रॉपी शून्य नहीं होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. National Sports University is situated in ________.</p>",
                    question_hi: "<p>36. राष्ट्रीय खेल विश्वविद्यालय ________ में स्थित है।</p>",
                    options_en: [
                        "<p>Mizoram</p>",
                        "<p>Tripura</p>",
                        "<p>Meghalaya</p>",
                        "<p>Manipur</p>"
                    ],
                    options_hi: [
                        "<p>मिज़ोरम</p>",
                        "<p>त्रिपुरा</p>",
                        "<p>मेघालय</p>",
                        "<p>मणिपुर</p>"
                    ],
                    solution_en: "<p>36.(d) <strong>Manipur.</strong> National Sports University: Established in 2018. Other Sports Universities : Netaji Subhas National Institute of Sports (Patiala), Lakshmibai National College of Physical Education (Kerala), and Tamil Nadu Physical Education and Sports University (Chennai).</p>",
                    solution_hi: "<p>36.(d) <strong>मणिपुर।</strong> राष्ट्रीय खेल विश्वविद्यालय: 2018 में स्थापित किया गया था। अन्य खेल विश्वविद्यालय: नेताजी सुभाष राष्ट्रीय खेल संस्थान (पटियाला), लक्ष्मीबाई राष्ट्रीय शारीरिक शिक्षा महाविद्यालय (केरल), और तमिलनाडु शारीरिक शिक्षा एवं खेल विश्वविद्यालय (चेन्नई)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Consider the following statements<br>A) A gas that follows Boyle\'s law, Charles\' law and Avogadro law is called an ideal gas.<br>B) As per Gay Lussac\'s law, at constant volume, pressure of a fixed amount of a gas varies directly with the temperature.<br>C) Silicon is present in group 14 and period 3 of the periodic table.</p>",
                    question_hi: "<p>37. निम्नलिखित कथनों पर विचार कीजिए।<br>A) वह गैस जो बॉयल के नियम, चार्ल्स के नियम और एवोगेड्रो नियम का पालन करती है, आदर्श गैस कहलाती है।<br>B) गे लुसाक के नियम (Gay Lussac\'s law) के अनुसार, स्थिर आयतन पर किसी निश्चित मात्रा वाली गैस का दाब, उसके ताप के व्युत्क्रमानुपाती होता है।<br>C) सिलिकॉन आवर्त सारणी के वर्ग 14 और आवर्त 3 में मौजूद होता है।</p>",
                    options_en: [
                        "<p>Both A and C is true</p>",
                        "<p>Both A and B is true</p>",
                        "<p>Only B and C is true</p>",
                        "<p>A, B, C are true</p>"
                    ],
                    options_hi: [
                        "<p>A और C दोनों सत्य हैं</p>",
                        "<p>A और B दोनों सत्य हैं</p>",
                        "<p>केवल B और C सत्य हैं</p>",
                        "<p>A, B, C तीनों सत्य हैं</p>"
                    ],
                    solution_en: "<p>37.(d) <strong>A, B, C are true.</strong> The ideal gas law (PV = nRT) combines all the gas laws, including: Boyle&rsquo;s Law: Pressure (P) is inversely proportional to the volume (V) of a gas at constant temperature (T) and a fixed number of moles (n). Charles\' Law: The volume of a gas is directly proportional to its temperature at constant pressure (P) and number of moles (n). Gay-Lussac\'s Law: The pressure of a given amount of gas varies directly with the absolute temperature when the volume is held constant. Avogadro&rsquo;s Law: At constant temperature and pressure, the volume of a gas is directly proportional to the number of moles of the gas.</p>",
                    solution_hi: "<p>37.(d) <strong>A, B, C तीनों सत्य हैं।</strong> आदर्श गैस नियम (PV = nRT) सभी गैस नियमों को जोड़ता है, जिसमें शामिल हैं: बॉयल का नियम: दाब (P) स्थिर तापमान (T) और मोल्स की एक निश्चित संख्या (n) पर गैस के आयतन (V) के व्युत्क्रमानुपाती होता है। चार्ल्स का नियम: स्थिर दाब (P) और मोल्स की संख्या (n) पर गैस का आयतन उसके तापमान के सीधे आनुपातिक होता है। गे-लुसाक का नियम: जब आयतन स्थिर रखा जाता है, तो गैस की दी गई मात्रा का दाब निरपेक्ष तापमान के साथ सीधे बदलता है। आवोगाद्रो का नियम: स्थिर तापमान और दाब पर, गैस का आयतन गैस के मोल्स की संख्या के सीधे आनुपातिक होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following awards was won by Lata Mangeshkar in the year 2001?</p>",
                    question_hi: "<p>38. लता मंगेशकर को वर्ष 2001 में निम्नलिखित में से किस पुरस्कार से सम्&zwj;मानित किया गया था?</p>",
                    options_en: [
                        "<p>Filmfare Lifetime Achievement Award</p>",
                        "<p>Padma Vibhushan</p>",
                        "<p>Dadasaheb Phalke Award</p>",
                        "<p>Bharat Ratna</p>"
                    ],
                    options_hi: [
                        "<p>फिल्मफेयर लाइफटाइम अचीवमेंट अवार</p>",
                        "<p>पद्म विभूषण</p>",
                        "<p>दादा साहेब फाल्के पुरस्कार</p>",
                        "<p>भारत रत्न</p>"
                    ],
                    solution_en: "<p>38.(d) <strong>Bharat Ratna.</strong> Lata Mangeshkar was an Indian playback singer born in Indore. She received the Padma Bhushan in 1969, the Padma Vibhushan in 1999, the Dadasaheb Phalke Award in 1989, and the \'Officier de la Legion d\'Honneur,\' France\'s highest civilian award, in 2009. She is also renowned for singing the famous song \'Aye mere watan ke logo&rsquo;.</p>",
                    solution_hi: "<p>38.(d) <strong>भारत रत्न।</strong> लता मंगेशकर इंदौर में जन्मी एक भारतीय पार्श्व गायिका थीं। उन्हें 1969 में पद्म भूषण, 1999 में पद्म विभूषण, 1989 में दादा साहब फाल्के पुरस्कार और 2009 में फ्रांस के सर्वोच्च नागरिक सम्मान \'ऑफिसियर डे ला लीजन डी\'होनूर\' से सम्मानित किया गया था। वह प्रसिद्ध गीत \'ऐ मेरे वतन के लोगों\' गाने के लिए भी प्रसिद्ध हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which is the weakest intermolecular force, considered as the Van der Waals force, often found in halogens, noble gases and other non-polar molecules ?</p>",
                    question_hi: "<p>39. वान डेर वाल्स बल के रूप में माना जाने वाला सबसे दुर्बल अंतर-आणविक बल कौन-सा है, जो अक्सर&nbsp;हैलोजन, आदर्श गैसों और अन्य गैर-ध्रुवीय अणुओं में पाया जाता है?</p>",
                    options_en: [
                        "<p>London dispersion forces</p>",
                        "<p>Ion-dipole forces</p>",
                        "<p>Dipole-induced dipole forces</p>",
                        "<p>Dipole - dipole forces</p>"
                    ],
                    options_hi: [
                        "<p>लंदन प्रकीर्णन बल</p>",
                        "<p>आयन-द्विध्रुव बल</p>",
                        "<p>द्विध्रुव-प्रेरित द्विध्रुव बल</p>",
                        "<p>द्विध्रुव-द्विध्रुव बल</p>"
                    ],
                    solution_en: "<p>39.(a) <strong>London dispersion forces. </strong>These forces are always attractive and interaction energy is inversely proportional to the sixth power of the distance between two interacting particles (i.e., 1/r<sup>6</sup> where r is the distance between two particles). These forces are important only at short distances (~500 pm) and their magnitude depends on the polarisability of the particle.</p>",
                    solution_hi: "<p>39.(a) <strong>लंदन प्रकीर्णन बल।</strong> ये बल हमेशा आकर्षक होते हैं और परस्पर क्रिया ऊर्जा, दो कणों के बीच की दूरी के छठे घात (अर्थात 1/r⁶, जहाँ r दो कणों के बीच की दूरी है) के व्युत्क्रमानुपाती होती है। ये बल केवल छोटी दूरी (~500 pm) पर ही महत्वपूर्ण होते हैं और उनका परिमाण कण की ध्रुवीकरण क्षमता पर निर्भर करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. In which of the following cities of Manipur is the Jawaharlal Nehru Manipur Dance Academy located ?</p>",
                    question_hi: "<p>40. मणिपुर के निम्नलिखित में से किस शहर में जवाहरलाल नेहरू मणिपुर नृत्य अकादमी स्थित है?</p>",
                    options_en: [
                        "<p>Imphal</p>",
                        "<p>Ukhrul</p>",
                        "<p>Chandel</p>",
                        "<p>Thoubal</p>"
                    ],
                    options_hi: [
                        "<p>इंफाल</p>",
                        "<p>उखरूल</p>",
                        "<p>चंदेल</p>",
                        "<p>थौबल</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Imphal.</strong> Jawaharlal Nehru Manipur Dance Academy, a unit of the Sangeet Natak Academy in New Delhi, is dedicated to teaching Manipuri dance and music. Established in 1954. Other renowned Dance Institutes : National Institute of Kathak Dance (New Delhi), Nalanda Nritya Kala Mahavidyalaya (Mumbai), Ballet Repertoire Academy of India (Mumbai), Sri Thyagaraja College of Music and Dance (Hyderabad), and Nrityanjali Institute of Performing Arts (Mumbai).</p>",
                    solution_hi: "<p>40.(a) <strong>इंफाल।</strong> जवाहरलाल नेहरू मणिपुर नृत्य अकादमी, नई दिल्ली में संगीत नाटक अकादमी की एक इकाई है, जो मणिपुरी नृत्य और संगीत सिखाने के लिए समर्पित है। जिसकी स्थापना 1954 में हुई थी। अन्य प्रसिद्ध नृत्य संस्थान: राष्ट्रीय कथक नृत्य संस्थान (नई दिल्ली), नालंदा नृत्य कला महाविद्यालय (मुंबई), बैले रिपर्टरी एकेडमी ऑफ इंडिया (मुंबई), श्री त्यागराज कॉलेज ऑफ म्यूजिक एंड डांस (हैदराबाद) और नृत्यांजलि इंस्टीट्यूट ऑफ परफॉर्मिंग आर्ट्स (मुंबई)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following options indicates the size of PPLO (Pleuro Pneumonia Like Organisms)?</p>",
                    question_hi: "<p>41. निम्नलिखित में से कौन सा विकल्प, PPLO (प्ल्यूरो न्यूमोनिया जैसे जीव) के आकार को इंगित करता है?</p>",
                    options_en: [
                        "<p>About 30 &mu;m</p>",
                        "<p>About 20 &mu;m</p>",
                        "<p>About 0.1 &mu;m</p>",
                        "<p>About 10 &mu;m</p>"
                    ],
                    options_hi: [
                        "<p>लगभग 30 &mu;m</p>",
                        "<p>लगभग 20 &mu;m</p>",
                        "<p>लगभग 0.1 &mu;m</p>",
                        "<p>लगभग 10 &mu;m</p>"
                    ],
                    solution_en: "<p>41.(c) <strong>About 0.1 &mu;m</strong>. Pleuropneumonia-like organisms (PPLO), or mycoplasmas, are among the smallest bacteria, ranging from 0.1 to 0.3 mm. They lack a cell wall, making them resistant to antibiotics like penicillin, and can thrive without oxygen, forming characteristic \"fried egg\"-shaped colonies on agar. PPLOs are responsible for respiratory infections in humans, such as Mycoplasma pneumonia, and are associated with pleuropneumonia in livestock, particularly in sheep and cows.</p>",
                    solution_hi: "<p>41.(c) <strong>लगभग 0.1 &mu;m.</strong> प्ल्यूरो न्यूमोनिया जैसे जीव (PPLO), या माइकोप्लाज्मा, सबसे छोटे जीवाणु में से हैं, जिनका आकार 0.1 से 0.3 मिमी तक होता है। उनमें कोशिका भित्ति नहीं होती है, जिससे वे पेनिसिलिन जैसे एंटीबायोटिक के प्रति प्रतिरोधी हो जाते हैं, और ऑक्सीजन के बिना भी जीवित रह सकते हैं, अगर (agar) पर विशिष्ट \"फ्राइड एग\" आकार की कॉलोनियाँ बनाते हैं। PPLO मानव में श्वसन संक्रमण, जैसे माइकोप्लाज्मा निमोनिया के लिए जिम्मेदार होते हैं, और पशुओं, विशेषकर भेड़ों और गायों में प्लेरोन्यूमोनिया से जुड़े होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following is related to neem?</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन-सा नीम से संबंधित है?</p>",
                    options_en: [
                        "<p>Tendril</p>",
                        "<p>Palmately</p>",
                        "<p>Pinnately</p>",
                        "<p>Spines</p>"
                    ],
                    options_hi: [
                        "<p>प्रतान (Tendril)</p>",
                        "<p>हस्ताकार (Palmately)</p>",
                        "<p>पंखाकार (Pinnately)</p>",
                        "<p>कंटक (Spines)</p>"
                    ],
                    solution_en: "<p>42.(c) <strong>Pinnately.</strong> Compound leaves are classified into two types: pinnately and palmately compound leaves. In pinnately compound leaves, leaflets are arranged along a common axis called the rachis, as seen in neem. In palmately compound leaves, leaflets attach at a common point at the tip of the petiole, such as in silk cotton.</p>",
                    solution_hi: "<p>42.(c) <strong>पंखाकार</strong> (Pinnately)। संयुक्त पत्तियों को दो प्रकारों में वर्गीकृत किया जाता है: पिनेटली और पामेटली संयुक्त पत्तियां। पिनेटली संयुक्त पत्तियों में, पत्रक एक सामान्य अक्ष के साथ व्यवस्थित होते हैं जिसे रेकिस कहा जाता है, जैसा कि नीम में देखा जाता है। पामेटली संयुक्त पत्तियों में, पत्रक डंठल की नोक पर एक सामान्य बिंदु पर जुड़ते हैं, जैसे कि रेशमी कपास में।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. What is \'Padam\' in Carnatic Music ?</p>",
                    question_hi: "<p>43. कर्नाटक संगीत में \'पदम\' क्या है?</p>",
                    options_en: [
                        "<p>A love poem</p>",
                        "<p>A poem about courage</p>",
                        "<p>A patriotic poem</p>",
                        "<p>A lullaby</p>"
                    ],
                    options_hi: [
                        "<p>एक प्रेम कविता</p>",
                        "<p>साहस के बारे में एक कविता</p>",
                        "<p>एक देशभक्ति कविता</p>",
                        "<p>एक लोरी</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>A love poem</strong>. A type of lyrical composition that often deals with themes of love, devotion, or emotions. It is known for its expressive and emotive qualities. Some key terminologies: raga, tala, krithi, varnam, pallavi, anupallavi, charanam, alapana, javali, tillana, swaram. Some Carnatic singers: M. Balamuralikrishna, M. S. Subbulakshmi, Aruna Sairam, Nithyasree Mahadevan, Sudha Ragunathan, Semmangudi Srinivasa Iyer, Bombay Jayashri.</p>",
                    solution_hi: "<p>43.(a) <strong>एक प्रेम कविता</strong>। एक प्रकार की काव्यात्मक रचना जो अक्सर प्रेम, भक्ति, या भावनाओं से संबंधित होती है। इसे इसके व्यक्तिपरक और भावनात्मक गुणों के लिए जाना जाता है। कुछ प्रमुख शब्दावली: राग, ताल, कृति, वर्णम, पल्लवी, अनुपल्लवी, चरणम, अलापना, जावली, तिल्लाना, स्वरम। कुछ कर्नाटक गायक: एम. बालमुरलीकृष्ण, एम. एस. सुब्बुलक्ष्मी, अरुणा साईराम, नित्याश्री महादेवन, सुधा रघुनाथन, सेम्मनगुडी श्रीनिवास अय्यर, बॉम्बे जयश्री आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. In Charles\'s law _____.<br>(A) pressure is kept constant.<br>(B) The number of moles of gas is constant.<br>(C) Volume is directly proportional to temperature.</p>",
                    question_hi: "<p>44. चार्ल्स के नियम में _____|<br>(A) दाब स्थिर रखा जाता है<br>(B) गैस के मोल की संख्या स्थिर है<br>(C) आयतन तापमान के अनुक्रमानुपाती होता है</p>",
                    options_en: [
                        "<p>A, B, C all three are true</p>",
                        "<p>Both A and C are true</p>",
                        "<p>Only B and C are true</p>",
                        "<p>Both A and B are true</p>"
                    ],
                    options_hi: [
                        "<p>A, B, C तीनों सत्य हैं</p>",
                        "<p>A और C दोनों सत्य हैं</p>",
                        "<p>केवल B और C सत्य हैं</p>",
                        "<p>A और B दोनों सत्य हैं</p>"
                    ],
                    solution_en: "<p>44.(a) <strong>A, B, C all three are true.</strong> In 1787, French Scientist Jacques Charles formulated Charles\' Law or the Law of Volume. According to this law, The volume of an ideal gas at constant pressure is directly proportional to its absolute temperature. Formula: V/T = Constant, at constant pressure.</p>",
                    solution_hi: "<p>44.(a) <strong>A, B, C तीनों सत्य हैं।</strong> 1787 में, फ्रांसीसी वैज्ञानिक जैक्स चार्ल्स ने चार्ल्स का नियम या आयतन का नियम दिया था। इस नियम के अनुसार, स्थिर दाब पर एक आदर्श गैस का आयतन उसके परम ताप के समानुपाती होता है। सूत्र: V/T = नियतांक, स्थिर दाब पर।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Manipur\'s ____________ tribe celebrates the \'Gaan-Ngai\' festival, which is a post-harvest festival of the tribe living in the regions of Manipur.</p>",
                    question_hi: "<p>45. मणिपुर की ______ जनजाति \'गान-नगाई (Gaan-Ngai)\' त्योहार मनाती है, जो मणिपुर के क्षेत्रों में रहने वाली जनजाति का फसल कटाई के बाद का त्योहार है।</p>",
                    options_en: [
                        "<p>Chothe</p>",
                        "<p>Zeliangrong</p>",
                        "<p>Inpui</p>",
                        "<p>Gangte</p>"
                    ],
                    options_hi: [
                        "<p>चोथे (Chothe)</p>",
                        "<p>ज़ेलियानग्रोंग (Zeliangrong)</p>",
                        "<p>इनपुई (Inpui)</p>",
                        "<p>गंगटे (Gangte)</p>"
                    ],
                    solution_en: "<p>45.(b) <strong>Zeliangrong.</strong> Gaan-Ngai is celebrated by the Zeliangrong tribe of Manipur, Nagaland, and Assam. The festival usually falls in December or January and is a time of feasting, dancing, and merry-making. Some harvest festivals celebrated in India: Uttarayan (Gujarat), Maghi (Punjab), Magha Saaji (Himachal Pradesh), Khichdi (Uttar Pradesh), Pongal (Tamil Nadu).</p>",
                    solution_hi: "<p>45.(b) <strong>ज़ेलियानग्रोंग।</strong> गान-नगाई मणिपुर, नागालैंड और असम की ज़ेलियानग्रोंग जनजाति द्वारा मनाया जाने वाला एक त्योहार है। यह त्योहार आम तौर पर दिसंबर या जनवरी में पड़ता है और यह दावत, नृत्य और मौज-मस्ती का समय होता है। भारत में मनाए जाने वाले कुछ प्रमुख फ़सल त्योहार : उत्तरायण (गुजरात), माघी (पंजाब), माघ साजी (हिमाचल प्रदेश), खिचड़ी (उत्तर प्रदेश), पोंगल (तमिलनाडु)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. What is the target number of solar installations under the Pradhan Mantri Surya Ghar Muft Bijli Yojana by 2027 ?</p>",
                    question_hi: "<p>46. प्रधानमंत्री सूर्या घर मुफ्त बिजली योजना के तहत 2027 तक कितनी सौर ऊर्जा प्रणालियों की स्थापना का लक्ष्य है?</p>",
                    options_en: [
                        "<p>50 lakh</p>",
                        "<p>75 lakh</p>",
                        "<p>1 crore</p>",
                        "<p>1.5 crore</p>"
                    ],
                    options_hi: [
                        "<p>50 लाख</p>",
                        "<p>75 लाख</p>",
                        "<p>1 करोड़</p>",
                        "<p>1.5 करोड़</p>"
                    ],
                    solution_en: "<p>46.(c) <strong>1 crore</strong>. The Pradhan Mantri Surya Ghar Muft Bijli Yojana aims to achieve 1 crore solar installations by 2027, providing up to 300 units of free electricity to households.</p>",
                    solution_hi: "<p>46.(c) <strong>1 करोड़।</strong> प्रधानमंत्री सूर्या घर मुफ्त बिजली योजना का उद्देश्य 2027 तक 1 करोड़ सौर ऊर्जा प्रणालियों की स्थापना करना है, जिससे घरों को 300 यूनिट तक मुफ्त बिजली मिल सके।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following airports is being built as India\'s first \'carbon-neutral airport\'?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन सा हवाई अड्डा भारत के पहले \'कार्बन-न्यूट्रल हवाई अड्डे\' के रूप में बनाया जा रहा है?</p>",
                    options_en: [
                        "<p>Kangra airport</p>",
                        "<p>Leh airport</p>",
                        "<p>Jammu airport</p>",
                        "<p>Srinagar airport</p>"
                    ],
                    options_hi: [
                        "<p>कांगड़ा हवाई अड्डा</p>",
                        "<p>लेह हवाई अड्डा</p>",
                        "<p>जम्मू हवाई अड्डा</p>",
                        "<p>श्रीनगर हवाई अड्डा</p>"
                    ],
                    solution_en: "<p>47.(b) <strong>Leh airport.</strong> Carbon neutrality means that the amount of carbon released into the atmosphere is equal to the amount of carbon that is absorbed by carbon sinks. Indira Gandhi International Airport (IGIA) in Delhi is the first airport in India to achieve net zero carbon emission status. The Airports Authority of India (AAI) is a statutory body constituted under the Airports Authority of India Act, 1994. Founded - 1 April 1995. Headquarters - Rajiv Gandhi Bhawan (New Delhi).</p>",
                    solution_hi: "<p>47.(b) <strong>लेह हवाई अड्डा</strong>। कार्बन तटस्थता का अर्थ है कि वायुमंडल में मुक्त कार्बन की मात्रा कार्बन सिंक (sinks) द्वारा अवशोषित कार्बन की मात्रा के बराबर है। दिल्ली में इंदिरा गांधी अंतर्राष्ट्रीय हवाई अड्डा (IGIA) भारत का प्रथम हवाई अड्डा है जिसने शुद्ध शून्य कार्बन उत्सर्जन का दर्जा प्राप्त किया है। भारतीय विमानपत्तन प्राधिकरण (AAI) भारतीय विमानपत्तन प्राधिकरण अधिनियम, 1994 के तहत गठित एक वैधानिक निकाय है। स्थापना - 1 अप्रैल 1995. मुख्यालय - राजीव गांधी भवन (नई दिल्ली)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which state became the first Indian state to implement a dedicated semiconductor policy in November 2024 ?</p>",
                    question_hi: "<p>48. नवंबर 2024 में कौन सा राज्य समर्पित सेमीकंडक्टर नीति लागू करने वाला पहला भारतीय राज्य बना ?</p>",
                    options_en: [
                        "<p>Assam</p>",
                        "<p>Gujarat</p>",
                        "<p>Rajasthan</p>",
                        "<p>Haryana</p>"
                    ],
                    options_hi: [
                        "<p>असम</p>",
                        "<p>गुजरात</p>",
                        "<p>राजस्थान</p>",
                        "<p>हरियाणा</p>"
                    ],
                    solution_en: "<p>48.(b) <strong>Gujarat.</strong> Bhupendra Patel, the Chief Minister of Gujarat, launched the &lsquo;Gujarat Semiconductor Policy 2022-2027.&rsquo; Gujarat has also established the &lsquo;Gujarat State Electronics Mission&rsquo; to ensure the effective implementation of this policy.</p>",
                    solution_hi: "<p>48.(b) <strong>गुजरात।</strong> गुजरात के मुख्यमंत्री भूपेंद्र पटेल ने \'गुजरात सेमीकंडक्टर नीति 2022-2027\' का शुभारंभ किया। गुजरात ने इस नीति के प्रभावी कार्यान्वयन के लिए \'गुजरात राज्य इलेक्ट्रॉनिक्स मिशन\' की भी स्थापना की है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. As per Olympic rules, in the third game, badminton players change ends when a side scores _____ points.</p>",
                    question_hi: "<p>49. ओलंपिक के नियमों के अनुसार, मैच की तीसरी पारी में, जब कोई पक्ष _____ पॉइंट बनाता है तो बैडमिंटन खिलाड़ी कोर्ट की साइड (ends) बदल लेते हैं।</p>",
                    options_en: [
                        "<p>15</p>",
                        "<p>11</p>",
                        "<p>12</p>",
                        "<p>13</p>"
                    ],
                    options_hi: [
                        "<p>15</p>",
                        "<p>11</p>",
                        "<p>12</p>",
                        "<p>13</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>11.</strong> The International Olympic Committee is the guardian of the Olympic Games and the leader of the Olympic Movement. A truly global organisation. Badminton had its debut as an official event at the 1992 Summer Olympics and has been contested in eight Olympiads. Saina Nehwal became India\'s first female world no. 1 in April 2015 and the first Indian to win an Olympic badminton medal.</p>",
                    solution_hi: "<p>49.(b) <strong>11.</strong> अंतर्राष्ट्रीय ओलंपिक समिति, ओलंपिक खेलों की संरक्षक और ओलंपिक कार्यान्वयन का नेतृत्व करती है। यह वास्तव में एक वैश्विक संगठन है। बैडमिंटन ने 1992 के ग्रीष्मकालीन ओलंपिक में आधिकारिक आयोजन के रूप में अपनी शुरुआत की थी और आठ ओलंपियाड में भाग लिया। साइना नेहवाल अप्रैल 2015 में भारत की पहली महिला विश्व नंबर 1 खिलाड़ी और ओलंपिक बैडमिंटन पदक जीतने वाली पहली भारतीय बनीं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. ISRO has recently signed an agreement with which organization to collaborate on advancements in human space exploration ?</p>",
                    question_hi: "<p>50. इसरो ने हाल ही में मानव अंतरिक्ष अन्वेषण में प्रगति पर सहयोग करने के लिए किस संगठन के साथ एक समझौते पर हस्ताक्षर किया हैं ?</p>",
                    options_en: [
                        "<p>NASA</p>",
                        "<p>JAXA</p>",
                        "<p>ESA</p>",
                        "<p>Roscosmos</p>"
                    ],
                    options_hi: [
                        "<p>NASA</p>",
                        "<p>JAXA</p>",
                        "<p>ESA</p>",
                        "<p>Roscosmos</p>"
                    ],
                    solution_en: "<p>50.(c) <strong>ESA (European Space Agency).</strong> The agreement was signed by ISRO Chairman S. Somanath and ESA Director General Dr. Josef Aschbacher. ISRO (Indian Space Research Organisation) was founded in 1969 by Dr. Vikram Sarabhai, known as the father of the Indian space program, with its headquarters in Bengaluru, India.</p>",
                    solution_hi: "<p>50.(c) <strong>ESA (यूरोपीय अंतरिक्ष एजेंसी)</strong>। यह समझौता ISRO के अध्यक्ष एस. सोमनाथ और ESA के महानिदेशक डॉ. जोसेफ एशबैकर द्वारा हस्ताक्षरित किया गया। ISRO (भारतीय अंतरिक्ष अनुसंधान संगठन) की स्थापना 1969 में डॉ. विक्रम साराभाई द्वारा की गई थी, जिन्हें भारतीय अंतरिक्ष कार्यक्रम के जनक के रूप में जाना जाता है, और इसका मुख्यालय बेंगलुरु, भारत में स्थित है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Find the number when successively divided by 3, 5 and 7 leaves remainder 2, 1 and 3,&nbsp;respectively, and the last quotient is 3.</p>",
                    question_hi: "<p>51. वह संख्&zwj;या ज्ञात कीजिए जिसे क्रमिक रूप से 3, 5 और 7 से भाग देने पर क्रमशः 2, 1 और 3 शेषफल&nbsp;प्राप्&zwj;त होता है, और अंतिम भागफल 3 प्राप्त होता है।</p>",
                    options_en: [
                        "<p>367</p>",
                        "<p>365</p>",
                        "<p>362</p>",
                        "<p>360</p>"
                    ],
                    options_hi: [
                        "<p>367</p>",
                        "<p>365</p>",
                        "<p>362</p>",
                        "<p>360</p>"
                    ],
                    solution_en: "<p>51.(b)&nbsp;In this type of questions, we start the calculation from the last and come towards the initial position.<br>The number which when divided by 7 leaves remainder 3 and quotient is 3 <br>= 7 &times; 3 + 3 = 24<br>When a number is divided by 5 , the remainder is 1<br>= 24 &times; 5 + 1 = 121<br>When a number is divided by 3, the remainder is 2 <br>= 121 &times; 3 + 2 = 365<br>Hence, required number = 365<br><strong>Short trick :-</strong> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013225263.png\" alt=\"rId48\" width=\"355\" height=\"159\"></p>",
                    solution_hi: "<p>51.(b)&nbsp;इस प्रकार के प्रश्नों में हम गणना आखिरी से शुरू करके प्रारंभिक स्थिति की ओर आते हैं।&nbsp;वह संख्या जिसे 7 से विभाजित करने पर शेषफल 3 और भागफल 3 आता है <br>= 7 &times; 3 + 3 = 24<br>जब किसी संख्या को 5 से विभाजित किया जाता है, तो शेषफल 1 आता है<br>= 24 &times; 5 + 1 = 121<br>जब किसी संख्या को 3 से विभाजित किया जाता है, तो शेषफल 2 आता है <br>= 121 &times; 3 + 2 = 365<br>अत: अभीष्ट संख्या = 365<br><strong>शॉर्ट ट्रिक :-</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013225457.png\" alt=\"rId49\" height=\"159\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Which of the following is true when x&nbsp;= SinA + CosA; y = SecA + CosecA ?</p>",
                    question_hi: "<p>52. x = SinA + CosA; y = SecA + CosecA होने पर निम्नलिखित में से कौन-सा विकल्प सत्य होगा ?</p>",
                    options_en: [
                        "<p>y(1 + x<sup>2</sup> ) = 2x</p>",
                        "<p>y - 2x = x<sup>2</sup>y</p>",
                        "<p>y + 2x = x<sup>2</sup>y</p>",
                        "<p>y(1 - 2x<sup>2</sup>) = x&nbsp;</p>"
                    ],
                    options_hi: [
                        "<p>y(1 + x<sup>2</sup>) = 2x</p>",
                        "<p>y - 2x&nbsp;= x<sup>2</sup>y</p>",
                        "<p>y + 2x = x<sup>2</sup>y</p>",
                        "<p>y(1 - 2x<sup>2</sup>) = x</p>"
                    ],
                    solution_en: "<p>52.(c)&nbsp;x = SinA + CosA &hellip; (i)<br>On squaring both side <br>x<sup>2</sup> = Sin<sup>2</sup>A + Cos<sup>2</sup>A + 2SinA.CosA<br>x<sup>2</sup> = 1 + 2SinA.CosA<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math> = SinA.CosA &hellip; (ii)<br>Now,<br>y = SecA + CosecA <br>y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>SinA</mi><mo>+</mo><mi>CosA</mi></mrow><mrow><mi>SinA</mi><mo>.</mo><mi>CosA</mi></mrow></mfrac></math> <br>y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mstyle displaystyle=\"true\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></mstyle></mfrac></math> from (i) and (ii)<br>y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math> <br>(x<sup>2</sup> - 1)y = 2x<br>(x<sup>2</sup>y - y) = 2x<br>x<sup>2</sup>y = 2x + y</p>",
                    solution_hi: "<p>52.(c)&nbsp;x = SinA + CosA &hellip; (i)<br>दोनों तरफ वर्ग करने पर<strong id=\"docs-internal-guid-18270c85-7fff-e690-5fb1-4c2d526fa3fb\"> </strong><br>x<sup>2</sup> = Sin<sup>2</sup>A + Cos<sup>2</sup>A + 2SinA.CosA<br>x<sup>2</sup> = 1 + 2SinA.CosA<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math> = SinA.CosA &hellip; (ii)<br>अब,<br>y = SecA + CosecA <br>y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>SinA</mi><mo>+</mo><mi>CosA</mi></mrow><mrow><mi>SinA</mi><mo>.</mo><mi>CosA</mi></mrow></mfrac></math> <br>y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mstyle displaystyle=\"true\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></mstyle></mfrac></math> (i) और (ii) से<br>y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math> <br>(x<sup>2</sup> - 1)y = 2x<br>(x<sup>2</sup>y - y) = 2x<br>x<sup>2</sup>y = 2x + y</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The speed of a thief is <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> times the speed of a policeman. The thief covers a distance of 6 km before he was caught by the policeman in 30 minutes. Initially, what was the distance between the policeman and the thief in km ?</p>",
                    question_hi: "<p>53. एक चोर की चाल एक पुलिसकर्मी की चाल की <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> गुना है। पुलिसकर्मी द्वारा 30 मिनट में पकड़े जाने से पहले चोर 6 km की दूरी तय करता है। शुरुआत में पुलिसकर्मी और चोर के बीच km में दूरी कितनी थी?</p>",
                    options_en: [
                        "<p>1.5</p>",
                        "<p>1.2</p>",
                        "<p>1.0</p>",
                        "<p>0.8</p>"
                    ],
                    options_hi: [
                        "<p>1.5</p>",
                        "<p>1.2</p>",
                        "<p>1.0</p>",
                        "<p>0.8</p>"
                    ],
                    solution_en: "<p>53.(a)<br>Ratio - thief&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;police<br>Speed -&nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;5<br>Let the distance between police and thief = x&nbsp;km<br>Time is constant so,<br>Time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>distance</mi><mi>speed</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>4</mn></mfrac></math><br>x = 1.5 km</p>",
                    solution_hi: "<p>53.(a)<br>अनुपात - चोर&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;पुलिस<br>चाल -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;5<br>माना शुरुआत मे चोर और पुलिस के बीच की दूरी = x&nbsp;km<br>समय स्थिर है इसलिए,<br>समय = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi>&#2330;&#2366;&#2354;</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>4</mn></mfrac></math><br>x = 1.5 km</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Find the equation of the sphere having centre (-1, 2, -3) and the radius of 3 units.</p>",
                    question_hi: "<p>54. केंद्र (-1, 2, -3) और 3 इकाइयों की त्रिज्या वाले गोले का समीकरण ज्ञात करें।</p>",
                    options_en: [
                        "<p>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2x - 4y + 6z + 5 = 0</p>",
                        "<p>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> - x+ 2y - 3z + 5 = 0</p>",
                        "<p>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> - 2x + 4y - 6z + 5 = 0</p>",
                        "<p>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + x - 2y + 3z + 5 = 0</p>"
                    ],
                    options_hi: [
                        "<p>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2x - 4y + 6z + 5 = 0</p>",
                        "<p>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> - x + 2y - 3z + 5 = 0</p>",
                        "<p>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> - 2x + 4y - 6z + 5 = 0</p>",
                        "<p>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + x - 2y + 3z + 5 = 0</p>"
                    ],
                    solution_en: "<p>54.(a)&nbsp;The equation of a sphere with center (h, k, l) and radius r is given by.<br>(x - h)<sup>2</sup> + (y - k)<sup>2</sup> + (z - l)<sup>2</sup> = r<sup>2</sup><br>According to the question,<br>[(x - (- 1)]<sup>2</sup> + (y - 2)<sup>2</sup> + [z - (- 3)]<sup>2</sup> = 3<sup>2</sup><br>x<sup>2</sup> + 1 + 2x + y<sup>2</sup> + 4 - 4y + z<sup>2</sup> + 9 + 6z = 9<br>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2x - 4y + 6z + 14 - 9 = 0<br>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2x - 4y + 6z + 5 = 0</p>",
                    solution_hi: "<p>54.(a)&nbsp;केंद्र (h, k, l) और त्रिज्या r वाले एक गोले का समीकरण इस प्रकार दिया गया है।<br>(x - h)<sup>2</sup> + (y - k)<sup>2</sup> + (z - l)<sup>2</sup> = r<sup>2</sup><br>प्रश्न के अनुसार,<br>[(x - (- 1)]<sup>2</sup> + (y - 2)<sup>2</sup> + [z - (- 3)]<sup>2</sup> = 3<sup>2</sup><br>x<sup>2</sup> + 1 + 2x + y<sup>2</sup> + 4 - 4y + z<sup>2</sup> + 9 + 6z = 9<br>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2x - 4y + 6z + 14 - 9 = 0<br>x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2x - 4y + 6z + 5 = 0</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. A glass jar contains 6 white, 8 black, 4 red and 3 blue marbles. If a single marble is chosen at random from the jar, what is the probability that it is black or&nbsp;blue ?</p>",
                    question_hi: "<p>55. एक कांच के पात्र में 6 सफेद, 8 काले, 4 लाल और 3 नीले कंचे हैं। यदि पात्र से एक कंचा यादृच्छिक रूप से चुना जाता है, तो इसके काले या नीले रंग के होने की क्या प्रायिकता है ?</p>",
                    options_en: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>21</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>55.(b)&nbsp;6 white, 8 black, 4 red and 3 blue<br>Total no. of marbles = 21<br>Favorable outcomes = (8 + 3) = 11<br>Required Probability <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Number</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>Favorable</mi><mi mathvariant=\"normal\">&#160;</mi><mi>Outcomes</mi></mrow><mrow><mi>Total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>Number</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>possible</mi><mi mathvariant=\"normal\">&#160;</mi><mi>outcomes</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>55.(b) 6 सफेद, 8 काले, 4 लाल और 3 नीले<br>कंचो की कुल संख्या = 21<br>अनुकूल परिणाम = (8 + 3) = 11<br>आवश्यक प्रायिकता <br>= <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2344;&#2369;&#2325;&#2370;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;&#2379;&#2306;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow><mrow><mi>&#2360;&#2306;&#2349;&#2366;&#2357;&#2367;&#2340;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;&#2379;&#2306;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A secant PAB is drawn from an external point P to the circle with the centre at O, intersecting it at A and B. If OP = 17 cm, PA = 12 cm and PB = 22.5 cm, then the radius of the circle is:</p>",
                    question_hi: "<p>56. O केंद्र वाले एक वृत्त के बाहरी बिंदु P से एक छेदक PAB खींचा खीं गया है, जो इसे A और B पर काटता है। यदि OP = 17 cm, PA = 12 cm और PB = 22.5 cm है, तो वृत्त की त्रिज्या ज्ञात करें।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>23</mn></msqrt></math> cm</p>",
                        "<p><math display=\"inline\"><msqrt><mn>21</mn></msqrt></math> cm</p>",
                        "<p><math display=\"inline\"><msqrt><mn>17</mn></msqrt></math> cm</p>",
                        "<p><math display=\"inline\"><msqrt><mn>19</mn></msqrt></math> cm</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>23</mn></msqrt></math> cm</p>",
                        "<p><math display=\"inline\"><msqrt><mn>21</mn></msqrt></math> cm</p>",
                        "<p><math display=\"inline\"><msqrt><mn>17</mn></msqrt></math> cm</p>",
                        "<p><math display=\"inline\"><msqrt><mn>19</mn></msqrt></math> cm</p>"
                    ],
                    solution_en: "<p>56.(d)<br><strong id=\"docs-internal-guid-a7075bd6-7fff-1982-900f-15e1886442b0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcF0UVmJuDVoegGWrA21cSb5W8pd7stGurwFOXlUsZMc0BkB2IqhLLhzPh0QEzbETmgFhAxLTPP9IxFNxAewKf4Ga5ThHTfw-Ngy7_a0njGUOjoGE4ctRdE6zQWrSsYTdvQSpE_dQ?key=zrUD8u50uQAVwURkRI-hrFUA\" width=\"274\" height=\"165\"></strong><br>PM<sup>2</sup> = 12 &times; 22.5<br>PM<sup>2</sup> = 12 &times; 22.5 = 270<br>Radius <math display=\"inline\"><mo>&#8869;</mo></math> tangent <br>therefore, <br>OM = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>PO</mi><mn>2</mn></msup><mo>-</mo><msup><mi>PM</mi><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>289</mn><mo>-</mo><mn>270</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>19</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>56.(d)<br><strong id=\"docs-internal-guid-a7075bd6-7fff-1982-900f-15e1886442b0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcF0UVmJuDVoegGWrA21cSb5W8pd7stGurwFOXlUsZMc0BkB2IqhLLhzPh0QEzbETmgFhAxLTPP9IxFNxAewKf4Ga5ThHTfw-Ngy7_a0njGUOjoGE4ctRdE6zQWrSsYTdvQSpE_dQ?key=zrUD8u50uQAVwURkRI-hrFUA\" width=\"274\" height=\"165\"></strong><br>PM<sup>2</sup> = 12 &times; 22.5<br>PM<sup>2</sup> = 12 &times; 22.5 = 270<br>त्रिज्या <math display=\"inline\"><mo>&#8869;</mo></math> स्पर्शरेखा<br>इसलिए, <br>OM = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>PO</mi><mn>2</mn></msup><mo>-</mo><msup><mi>PM</mi><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>289</mn><mo>-</mo><mn>270</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>19</mn></msqrt></math> cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If (x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>) = 7, x &gt; 0. The positive value of (x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>) is :</p>",
                    question_hi: "<p>57. यदि (x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>) = 7, x &gt; 0 है। (x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>) का धनात्मक मान कितना है ?</p>",
                    options_en: [
                        "<p>5<math display=\"inline\"><msqrt><mn>4</mn></msqrt></math></p>",
                        "<p>5<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>",
                        "<p>4<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p>5<math display=\"inline\"><msqrt><mn>4</mn></msqrt></math></p>",
                        "<p>5<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>",
                        "<p>4<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>57.(c) (x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>) = 7<br>(x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>-</mo><mn>4</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>45</mn></msqrt></math> = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>",
                    solution_hi: "<p>57.(c) (x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>) = 7<br>(x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>-</mo><mn>4</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>45</mn></msqrt></math> = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Divide some money in the ratio Ravi, Reeta and Rahul that 5 (Part of Ravi) = 3 (Part of Reeta) =11 (Part of Rahul). The money ratio of Ravi : Reeta : Rahul is equal to:</p>",
                    question_hi: "<p>58. कुछ धनराशि को रवि, रीता और राहुल के बीच इस अनुपात में बांटिए कि 5 (रवि का हिस्सा) = 3 (रीता का हिस्सा) = 11 (राहुल का हिस्सा) हो। रवि : रीता : राहुल की धनराशि का अनुपात _________ के बराबर है।</p>",
                    options_en: [
                        "<p>11 : 5 : 3</p>",
                        "<p>11 : 33 : 15</p>",
                        "<p>5 : 11 : 3</p>",
                        "<p>33 : 55 : 15</p>"
                    ],
                    options_hi: [
                        "<p>11 : 5 : 3</p>",
                        "<p>11 : 33 : 15</p>",
                        "<p>5 : 11 : 3</p>",
                        "<p>33 : 55 : 15</p>"
                    ],
                    solution_en: "<p>58.(d)<br>5 (Part of Ravi) = 3 (Part of Reeta) =11 (Part of Rahul)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>ravi</mi><mi>reeta</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>reeta</mi><mi>rahul</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math><br>Ratio - ravi : reeta : rahul<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; :&nbsp; &nbsp;<strong>5</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong>11</strong>&nbsp; &nbsp; :&nbsp; &nbsp; 11&nbsp; &nbsp; :&nbsp; &nbsp;3<br>-------------------------------------<br>Final - 33&nbsp; &nbsp;:&nbsp; &nbsp; 55&nbsp; &nbsp; :&nbsp; &nbsp; 15<br><strong>Short tricks :</strong>- 5 (Part of Ravi) = 3 (Part of Reeta) = 11 (Part of Rahul)<br>LCM of 5, 3, 11 = 5 &times; 3 &times; 11<br>Required ratio = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>3</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>11</mn></mfrac></math> = 33 : 55 : 15</p>",
                    solution_hi: "<p>58.(d)<br>5 (रवि का भाग) = 3 (रीता का भाग) =11 (राहुल का भाग)<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2352;&#2357;&#2367;</mi><mi>&#2352;&#2368;&#2340;&#2366;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>,&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2352;&#2368;&#2340;&#2366;</mi><mi>&#2352;&#2366;&#2361;&#2369;&#2354;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math><br>अनुपात - रवि&nbsp; &nbsp;:&nbsp; &nbsp;रीता&nbsp; &nbsp;:&nbsp; &nbsp;राहुल<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;<strong>5</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong>11</strong>&nbsp; &nbsp; :&nbsp; &nbsp; 11&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;3<br>&nbsp; &nbsp; &nbsp;-------------------------------------<br>अंतिम -&nbsp; &nbsp;33&nbsp; &nbsp; :&nbsp; &nbsp; 55&nbsp; &nbsp; :&nbsp; &nbsp; 15<br><strong>शॉर्ट ट्रिक्स :- </strong>5 (रवि का हिस्सा) = 3 (रीता का हिस्सा) = 11 (राहुल का हिस्सा)<br>5, 3, 11 का LCM = 5 &times; 3 &times; 11<br>आवश्यक अनुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>3</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>11</mn></mfrac></math> = 33 : 55 : 15</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The circumference of the base of a conical tent of height 8 m is 32 &pi;m. Find its curved surface area.</p>",
                    question_hi: "<p>59. 8 m ऊँचाई वाले एक शंक्वाकार तम्बू के आधार की परिधि 32 &pi;m है। इसका वक्र पृष्ठीय क्षेत्रफल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>164<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> &pi;m<sup>2</sup></p>",
                        "<p>162<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> &pi;m<sup>2</sup></p>",
                        "<p>132<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> &pi;m<sup>2</sup></p>",
                        "<p>128<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> &pi;m<sup>2</sup></p>"
                    ],
                    options_hi: [
                        "<p>164<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> &pi;m<sup>2</sup></p>",
                        "<p>162<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> &pi;m<sup>2</sup></p>",
                        "<p>132<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> &pi;m<sup>2</sup></p>",
                        "<p>128<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> &pi;m<sup>2</sup></p>"
                    ],
                    solution_en: "<p>59.(d) Given height = 8m<br>Circumference of the base of a conical tent = 2&pi;r = 32 &pi;m&nbsp;<br>&there4; r = 16m<br>Slant height l&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">r</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">h</mi><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>256</mn><mo>+</mo><mn>64</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>320</mn></msqrt></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>m<br>Now, curved surface area(&pi;rl) = &pi; &times; 16 &times; 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> = 128<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> &pi;m<sup>2</sup></p>",
                    solution_hi: "<p>59.(d)&nbsp;दी गई ऊंचाई = 8 मी.<br>शंक्वाकार तंबू के आधार की परिधि = 2&pi;r = 32 &pi;m&nbsp;<br>&there4; r = 16 मी.<br>तिरछी ऊँचाई (l) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">r</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">h</mi><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>256</mn><mo>+</mo><mn>64</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>320</mn></msqrt></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>m<br>अब, वक्र पृष्ठ क्षेत्रफल(&pi;rl) = &pi; &times; 16 &times; 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> = 128<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> &pi;m<sup>2</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Arjun mixed three different oils to form a mixture costing ₹25 per litre. The ratio of the three varieties of oil was 1 : 5 : 2. What is the cost (per litre in ₹) of the second variety if the cost per litre of the first and third varieties was ₹20 and ₹30, respectively ?</p>",
                    question_hi: "<p>60. अर्जुन ने ₹25 प्रति लीटर के मूल्य वाला मिश्रण बनाने के लिए तीन भिन्न-भिन्न तेलों को मिलाया। तेल की तीनों किस्मों का अनुपात 1 : 5 : 2 था। दूसरी किस्म का मूल्य (प्रति लीटर ₹ में) कितना है, यदि पहली और तीसरी किस्म का प्रति लीटर मूल्य क्रमशः ₹20 और ₹30 था ?</p>",
                    options_en: [
                        "<p>25</p>",
                        "<p>28</p>",
                        "<p>26</p>",
                        "<p>24</p>"
                    ],
                    options_hi: [
                        "<p>25</p>",
                        "<p>28</p>",
                        "<p>26</p>",
                        "<p>24</p>"
                    ],
                    solution_en: "<p>60.(d) Let the cost of the second variety = ₹x<br>According to the question , <br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>30</mn><mo>&#215;</mo><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>8</mn></mfrac></math> = 25<br>&rArr; 20 + 5x + 60 = 25 &times; 8<br>&rArr; 5x = 200 - 80 = 120 &rArr; x = 24<br>&there4; Cost price of the second price = ₹24</p>",
                    solution_hi: "<p>60.(d) माना दूसरी किस्म की लागत = ₹x<br>प्रश्न के अनुसार, <br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>30</mn><mo>&#215;</mo><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>8</mn></mfrac></math> = 25<br>&rArr; 20 + 5x + 60 = 25 &times; 8<br>&rArr; 5x = 200 - 80 = 120 &rArr; x = 24<br>&there4; दूसरी कीमत का क्रय मूल्य = ₹24</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. For two circles of radius 7 units and <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> units, whose centres are 15 units apart, what is the length of the direct common tangent in units? (Correct to 3 decimal places.)</p>",
                    question_hi: "<p>61. 7 इकाई और <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math>&nbsp;इकाई की त्रिज्या वाले उन दो वृत्तों के लिए, जिनके केंद्र 15 इकाई की दूरी पर हैं, सीधी उभयनिष्ठ स्पर्श रेखा की लंबाई इकाई में कितनी है? (दशमलव के 3 स्थानों तक शुद्ध।)</p>",
                    options_en: [
                        "<p>14.586</p>",
                        "<p>18.654</p>",
                        "<p>15.486</p>",
                        "<p>16.584</p>"
                    ],
                    options_hi: [
                        "<p>14.586</p>",
                        "<p>18.654</p>",
                        "<p>15.486</p>",
                        "<p>16.584</p>"
                    ],
                    solution_en: "<p>61.(a)<br><strong id=\"docs-internal-guid-6f1e2fb6-7fff-3db5-e27a-6509ae7c2480\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc_1-yxSGoxvDAd2qfeRg095n3A_8BSJDzo10JayfdSoQcX4BkqJbpN5Uo2lIyIHPwWqvrLxry6ls4KikmOWMVGKqNhnd0AueWU3EmQH4LacNNpBt7cv0FfK6qo6ujAqHjkslJApQ?key=zrUD8u50uQAVwURkRI-hrFUA\" width=\"285\" height=\"121\"></strong><br>Length of the common tangent = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>15</mn><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>7</mn><mo>-</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>225</mn><mo>-</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>225</mn><mo>-</mo><mn>12</mn><mo>.</mo><mn>35</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>212</mn><mo>.</mo><mn>65</mn></msqrt></math> = 14.586</p>",
                    solution_hi: "<p>61.(a)<br><strong id=\"docs-internal-guid-09d78c72-7fff-d6d5-4cf4-27bb4549fd5a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeWJdETFGcqWoyCq5344wg_Cy39Myy9MbBQbE1ajyM92dy5lvBAgh-WIW-lZUGd5b7fWSh-xTkMduRPq9CaBUM_l4MmF8uLEiA0r1a2GrDWchhurf1Givga93tvfcwtGF6bipXELA?key=zrUD8u50uQAVwURkRI-hrFUA\" width=\"317\" height=\"133\"></strong><br>उभयनिष्ठ स्पर्शरेखा की लंबाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>15</mn><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>7</mn><mo>-</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>225</mn><mo>-</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>225</mn><mo>-</mo><mn>12</mn><mo>.</mo><mn>35</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>212</mn><mo>.</mo><mn>65</mn></msqrt></math> = 14.586</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. X and Y can do a job in 18 days; Y and Z can do the same in 24 days. If all the three can finish the job in 12 days, in how many days can X and Z can complete the job ?</p>",
                    question_hi: "<p>62. X और Y एक काम को 18 दिनों में पूरा कर सकते हैं; Y और Z उसी काम को 24 दिनों में पूरा कर सकते हैं। यदि वह तीनों उसी काम को 12 दिनों में पूरा कर सकते हैं, तो X और Z कितने दिनों में काम पूरा कर सकते हैं?</p>",
                    options_en: [
                        "<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days</p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days</p>",
                        "<p>7 days</p>",
                        "<p>5 days</p>"
                    ],
                    options_hi: [
                        "<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन</p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन</p>",
                        "<p>7 दिन</p>",
                        "<p>5 दिन</p>"
                    ],
                    solution_en: "<p>62.(a) <br><strong id=\"docs-internal-guid-6451fbf4-7fff-8c18-c3c1-7a8b36f6045b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdbGhUQmbwzcLaKVMMVi3lI58R0AEuiU1GnX0wxlpmDt2LOa5TKgO8VCG15dswLH4W9kgYp3Nq44HH6NFTj-HoN_y3h8FGf6q5yh-Kuk5OqywFigcODULobt3KBa92y0q2wv-dQFw?key=zrUD8u50uQAVwURkRI-hrFUA\" width=\"283\" height=\"144\"></strong><br>Efficiency of X = 6 - 3 = 3 units<br>Efficiency of Z = 6 - 4 = 2 units<br>Efficiency of (X + Z) = 5 units<br>So, Work done by (X + Z) = <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 14<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> days</p>",
                    solution_hi: "<p>62.(a) <br><strong id=\"docs-internal-guid-bcc40a2f-7fff-2c95-5b00-c75e8115a3dd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfrk0sanz0-qVvlIYpoLmcSGvA5mcEmfSklihQ5DNYvwpfnld8YrmfBcTzSCGb2j7UTvvGDk60kPkrNBOrFwRnJ90ZWhOKtGAyr0OFX8ojEPHaXcd1MqP8t2NYLvLVk547BpFnp?key=zrUD8u50uQAVwURkRI-hrFUA\" width=\"296\" height=\"166\"></strong><br>X की दक्षता = 6 - 3 = 3 इकाई<br>Z की दक्षता = 6 - 4 = 2 इकाई<br>(X + Z) की दक्षता = 5 इकाई<br>इसलिए, (X + Z) द्वारा किया गया कार्य= <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 14<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. A hollow sphere of external and internal diameters of 10 cm and 6 cm, respectively, is melted and made into another solid in the shape of a right circular cone of base diameter 10 cm. Find the height of the cone.</p>",
                    question_hi: "<p>63. क्रमशः 10 cm और 6 cm के बाहरी और आंतरिक व्यास के एक खोखले गोले को पिघलाया जाता है और 10 cm के आधार व्यास वाले एक लम्ब वृत्तीय शंकु के आकार में ढालकर एक और ठोस बनाया जाता है। शंकु की ऊँचाई ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>13.68 cm</p>",
                        "<p>14.68 cm</p>",
                        "<p>16.68 cm</p>",
                        "<p>15.68 cm</p>"
                    ],
                    options_hi: [
                        "<p>13.68 cm</p>",
                        "<p>14.68 cm</p>",
                        "<p>16.68 cm</p>",
                        "<p>15.68 cm</p>"
                    ],
                    solution_en: "<p>63.(d)&nbsp;Let R&nbsp;be external radius and r be internal radius of a hollow sphere and radius of a cone be r<sub>1</sub><br>According to question,<br>Volume of cone = volume of hollow cylinder<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;r<sub>1</sub><sup>2</sup>h = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;(R<sup>3</sup> - r<sup>3</sup>)<br>5<sup>2</sup> &times; h = 4 (5<sup>3</sup> - 3<sup>3</sup>)<br>25 &times; h = 4 &times; 98<br>h = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>98</mn></mrow><mn>25</mn></mfrac></math> = 15.68 cm</p>",
                    solution_hi: "<p>63.(d)&nbsp;मान लीजिए R एक खोखले गोले की बाहरी त्रिज्या है और r आंतरिक त्रिज्या है और एक शंकु की त्रिज्या r<sub>1</sub>&nbsp;है।<br>प्रश्न के अनुसार,<br>शंकु का आयतन = खोखले बेलन का आयतन<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;r<sub>1</sub><sup>2</sup>h = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;(R<sup>3</sup> - r<sup>3</sup>)<br>5<sup>2</sup> &times; h = 4 (5<sup>3</sup> - 3<sup>3</sup>)<br>25 &times; h = 4 &times; 98<br>h = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>98</mn></mrow><mn>25</mn></mfrac></math> = 15.68 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A man walks to a viewpoint and returns to the starting point by his car maintaining constant speed and thus takes a total time of 8 hours 45 minutes. He would have gained 5 hours by driving both ways. How long would it have taken for him to walk both ways with the same walking speed?</p>",
                    question_hi: "<p>64. एक व्यक्ति एक व्यूपॉइंट तक पैदल जाता है और अपनी कार से एक निश्चित चाल से वापस लौटता है और इस प्रकार उसे कुल 8 घंटे 45 मिनट का समय लगता है। दोनों तरफ कार से यात्रा करने पर उसे 5 घंटे कम लगते । दोनों ओर एकसमान चाल से पैदल यात्रा करने में उसे कुल कितना समय लगता?</p>",
                    options_en: [
                        "<p>13 hours 45 minutes</p>",
                        "<p>14 hours 15 minutes</p>",
                        "<p>13 hours 30 minutes</p>",
                        "<p>12 hours 45 minutes</p>"
                    ],
                    options_hi: [
                        "<p>13 घंटे 45 मिनट</p>",
                        "<p>14 घंटे 15 मिनट</p>",
                        "<p>13 घंटे 30 मिनट</p>",
                        "<p>12 घंटे 45 मिनट</p>"
                    ],
                    solution_en: "<p>64.(a)&nbsp;Let time taken by men on one side be x and car be y<br>According to the question,<br>x + y = 8 hr 45 min. = <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> hr.---------(i)<br>y + y = 3 hr 45 min. = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> hr.---------(ii)<br>Now, 2y = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> hr.<br>Then, y = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> hr.<br>Now, putting the value of y in equation (i) we get;<br>x + <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>4</mn></mfrac></math> <br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>4</mn></mfrac></math> - <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>8</mn></mfrac></math> hr<br>Time taken to men for walk both ways (2x) = <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 2 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>4</mn></mfrac></math> hr<br>&rArr; (13 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>)hr = 13 hour + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; 60 = 13 hour 45 min.</p>",
                    solution_hi: "<p>64.(a)&nbsp;माना एक ओर व्यक्ति द्वारा पैदल और कार द्वारा लिया जाने वाला समय क्रमशः x और y घंटे है <br>प्रश्न के अनुसार,<br>x + y = 8 घंटे 45 मिनट = <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटे ---------(i)<br>y + y = 3 घंटे 45 मिनट = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटे ---------(ii)<br>अब, 2y = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटे <br>तो, y = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> घंटे <br>अब, y का मान समीकरण (i) में रखने पर हमे प्राप्त होता है;<br>x + <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>4</mn></mfrac></math><br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>4</mn></mfrac></math> - <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>8</mn></mfrac></math>घंटे&nbsp;<br>आदमी द्वारा दोनों और पैदल जाने में लगा समय (2x) = <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 2 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>4</mn></mfrac></math> घंटे&nbsp;<br>&rArr; (13 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>)घंटे = 13 घंटे + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; 60 = 13 घंटे 45 मिनट</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. In an engineering college, there are four branches namely Computer Science (CS),&nbsp;Electronics (EC), Civil (CE), and Mechanical (ME). The respective ratio of the number&nbsp;of students in these branches is 11 : 5 : 6 : 7. What is the percentage of students&nbsp;belonging to CS and ME branches in the college (correct up to two decimal places)?</p>",
                    question_hi: "<p>65. एक इंजीनियरिंग कॉलेज में, कंप्यूटर साइंस (CS), इलेक्ट्रॉनिक्स (EC), सिविल (CE) और मैकेनिकल (ME) नाम की चार शाखाएँ हैं। इन शाखाओं में छात्रों की संख्या का क्रमशः अनुपात 11 : 5 : 6 : 7 है। कॉलेज में CS और ME शाखाओं के छात्रों का प्रतिशत क्या है (दशमलव के दो स्थानों तक सही) ?</p>",
                    options_en: [
                        "<p>62.37%</p>",
                        "<p>62.07%</p>",
                        "<p>61.37%</p>",
                        "<p>61.07%</p>"
                    ],
                    options_hi: [
                        "<p>62.37%</p>",
                        "<p>62.07%</p>",
                        "<p>61.37%</p>",
                        "<p>61.07%</p>"
                    ],
                    solution_en: "<p>65.(b)&nbsp;Total students in all branches = (11x&nbsp;+ 5x + 6x + 7x) = 29x<br>Number of students in CS and ME branches = (11x&nbsp;+ 7x) = 18x<br>Hence, required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>29</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> &times; 100 = 62.07%</p>",
                    solution_hi: "<p>65.(b)&nbsp;सभी शाखाओं में कुल छात्र = (11x&nbsp;+ 5x + 6x + 7x) = 29x<br>कंप्यूटर साइंस (CS) और मैकेनिकल (ME) शाखाओं में छात्रों की संख्या = (11x&nbsp;+ 7x) = 18x<br>अतः, आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>29</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math>&nbsp;&times; 100 = 62.07%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If tan A = a tan B and sin A = b sin B, then the value of cos<sup>2</sup>A is:</p>",
                    question_hi: "<p>66. यदि tan A = a tan B और sin A = b sin B है, तो cos<sup>2</sup>A का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>66.(b) <strong>Given</strong> : tan A = a tan B and sin A = b sin B<br>We put the value of A = 60&deg; and B = 30&deg;<br>tan 60&deg; = a tan 30&deg;<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = a &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> &rArr; a = 3<br>sin 60&deg; = b sin 30&deg;<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> = b &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &rArr; b = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>So, cos<sup>2</sup>A = (cos 60&deg;)<sup>2</sup>&nbsp;= (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>Now, put the value a and b in all options one by one only option (b) satisfied. <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>-</mo><mn>1</mn></mrow><mrow><mn>9</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>66.(b) <strong>दिया गया है </strong>: tan A = a tan B और sin A = b sin B<br>अब, A = 60&deg; और B = 30&deg; रखने पर,<br>tan 60&deg; = a tan 30&deg;<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = a &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> &rArr; a = 3<br>sin 60&deg; = b sin 30&deg;<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> = b &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &rArr; b = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>अब, cos<sup>2</sup>A = (cos 60&deg;)<sup>2</sup>&nbsp;= (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>अब, सभी विकल्पों में एक-एक करके a और b का मान रखने पर, केवल विकल्प (b) संतुष्ट होता है।<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>-</mo><mn>1</mn></mrow><mrow><mn>9</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. By selling 30 metres of cloth a shopkeeper makes a profit equivalent to the selling&nbsp;price of 10 metres of cloth. Find the selling price of 1 metre of cloth when the cost&nbsp;price of 1 metre of cloth is ₹480.</p>",
                    question_hi: "<p>67. 30 मीटर कपड़ा बेचकर एक दुकानदार 10 मीटर कपड़े के विक्रय मूल्य के बराबर लाभ अर्जित करता है। 1 मीटर कपड़े का विक्रय मूल्य ज्ञात कीजिए, जब 1 मीटर कपड़े का क्रय मूल्य ₹480 है।</p>",
                    options_en: [
                        "<p>₹520</p>",
                        "<p>₹960</p>",
                        "<p>₹720</p>",
                        "<p>₹820</p>"
                    ],
                    options_hi: [
                        "<p>₹520</p>",
                        "<p>₹960</p>",
                        "<p>₹720</p>",
                        "<p>₹820</p>"
                    ],
                    solution_en: "<p>67.(c)&nbsp;According to the question,<br>(30 S.P. - 30 C.P.) = 10 S.P.<br>C.P. : S.P. = 2 : 3<br>Now, C.P. (2 unit) = ₹480<br>1 unit = ₹240<br>So, S.P. (3 unit) = 3 &times; 240 = ₹720</p>",
                    solution_hi: "<p>67.(c)&nbsp;प्रश्न के अनुसार,<br>(30 S.P. - 30 C.P.) = 10 S.P.<br>C.P. : S.P.= 2 : 3<br>अब, C.P. (2 इकाई) = ₹480<br>1 इकाई = ₹240<br>तो, S.P. (3 इकाई) = 3 &times; 240 = ₹720</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. If p = {(- 1.5) &times;&nbsp;(-8.2) - 3.3} then find the sum of the digits in p<sup>3</sup>.</p>",
                    question_hi: "<p>68. यदि p = ((-1.5) &times; (-8.2) -3.3) तो p<sup>3</sup> के अंकों का योग ज्ञात करें।</p>",
                    options_en: [
                        "<p>16</p>",
                        "<p>17</p>",
                        "<p>18</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>16</p>",
                        "<p>17</p>",
                        "<p>18</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>68.(c)&nbsp;p = ((-1.5) &times; (-8.2) -3.3)<br>= (12.3 - 3.3) = 9<br>p<sup>3</sup> = (9)<sup>3</sup> = 729 <br>= 7 + 2 + 9 = 18</p>",
                    solution_hi: "<p>68.(c)&nbsp;p = ((-1.5) &times; (-8.2) -3.3)<br>= (12.3 - 3.3) = 9<br>p<sup>3</sup> = (9)<sup>3</sup> = 729 <br>= 7 + 2 + 9 = 18</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The marked price on an item was Rs 4800/- but the shopkeeper offered a double discount of 18% and 20%. How much did he finally sell the item for ?</p>",
                    question_hi: "<p>69. एक वस्तु पर अंकित मूल्य 4800/- रुपए था, लेकिन दुकानदार उस वस्तु पर 18% और 20% की दो छूट की पेशकश करता है, अंततः वह वस्तु को किस मूल्य पर बेचता है ?</p>",
                    options_en: [
                        "<p>Rs. 3642</p>",
                        "<p>Rs. 3148.8</p>",
                        "<p>Rs. 2976</p>",
                        "<p>Rs. 3258.8</p>"
                    ],
                    options_hi: [
                        "<p>3642 रुपए</p>",
                        "<p>3148.8 रुपए</p>",
                        "<p>2976 रुपए</p>",
                        "<p>3258.8 रुपए</p>"
                    ],
                    solution_en: "<p>69.(b)<br>SP of the item = 4800 &times; <math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>,</mo><mn>744</mn></mrow><mn>5</mn></mfrac></math> = ₹3148.8</p>",
                    solution_hi: "<p>69.(b)<br>वस्तु का विक्रय मूल्य = 4800 &times; <math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>,</mo><mn>744</mn></mrow><mn>5</mn></mfrac></math> = ₹3148.8</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A certain sum of money amounts to ₹1,860 in 2 years and to ₹2,130 in 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> years at simple interest. Find the sum and the rate of interest, respectively.</p>",
                    question_hi: "<p>70. साधारण ब्याज पर एक निश्चित धनराशि 2 वर्षों में ₹1,860 और 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्षों में ₹2,130 हो जाती है। क्रमशः धनराशि (मूलधन) और ब्याज दर ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>₹1,500, 12%</p>",
                        "<p>₹1,500, 15%</p>",
                        "<p>₹1,200, 10%</p>",
                        "<p>₹1,200, 12%</p>"
                    ],
                    options_hi: [
                        "<p>₹1,500, 12%</p>",
                        "<p>₹1,500, 15%</p>",
                        "<p>₹1,200, 10%</p>",
                        "<p>₹1,200, 12%</p>"
                    ],
                    solution_en: "<p>70.(a)&nbsp;SI for 1.5yrs = 2130 - 1860 = ₹270<br>SI for 1 yr = <math display=\"inline\"><mfrac><mrow><mn>270</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = ₹180<br>Then, SI for 2 yrs = 180 &times; 2 = ₹360<br>Principal amount = 1860 - 360 = ₹1500<br>Rate% = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>1500</mn></mrow></mfrac></math> &times; 100 = 12%</p>",
                    solution_hi: "<p>70.(a)&nbsp;1.5 साल के लिए साधारण ब्याज = 2130 - 1860 = ₹270<br>1 वर्ष के लिए साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>270</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = ₹180<br>फिर, 2 साल के लिए साधारण ब्याज = 180 &times; 2 = ₹360<br>मूल राशि = 1860 - 360 = ₹1500<br>दर% = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>1500</mn></mrow></mfrac></math> &times; 100 = 12%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Three rectangular fields having areas of 70 m<sup>2</sup>, 84 m<sup>2</sup>, and 112 m<sup>2</sup> are to be divided into identical rectangular flower beds, each having a length of 7 m. Find the breadth of each flower bed.</p>",
                    question_hi: "<p>71. 70 m<sup>2</sup>, 84 m<sup>2</sup> और 112 m<sup>2</sup> क्षेत्रफल वाले तीन आयताकार मैदानों को समान आयताकार फूलों की क्यारियों में बांटा जाना है, जिनमें से प्रत्येक की लंबाई 7m है। प्रत्येक फूलों की क्यारी की चौड़ाई ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>4 m</p>",
                        "<p>3 m</p>",
                        "<p>2 m</p>",
                        "<p>6 m</p>"
                    ],
                    options_hi: [
                        "<p>4 m</p>",
                        "<p>3 m</p>",
                        "<p>2 m</p>",
                        "<p>6 m</p>"
                    ],
                    solution_en: "<p>71.(c)<br>Maximum area of identical rectangular flower beds = HCF of (70, 84, 112) = 14<br>So, the breadth of each flower bed = <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 2 m</p>",
                    solution_hi: "<p>71.(c)<br>समान आयताकार फूलों की क्यारियों का अधिकतम क्षेत्रफल = (70, 84, 112) का HCF = 14<br>अतः, प्रत्येक फूलों की क्यारी की चौड़ाई = <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 2 मीटर</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A tunnel elevator can carry a maximum of 15 passengers with an average weight of 81 kg. However, four persons beyond the maximum capacity entered the elevator, resulting in an average weight of 82 kg and causing it to be overloaded. Find the average weight (in kg) of the additional four persons.</p>",
                    question_hi: "<p>72. एक सुरंग एलिवेटर 81 kg के औसत वजन क्षमता के साथ अधिकतम 15 यात्रियों को ले जा सकता है। हालाँकि, अधिकतम क्षमता से अधिक के चार व्यक्तियों ने एलिवेटर में प्रवेश किया, जिसके परिणामस्वरूप औसत वजन 82 kg हो गया और एलिवेटर ओवरलोड हो गया। अतिरिक्त चार व्यक्तियों का औसत वजन (kg में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>90.41</p>",
                        "<p>100</p>",
                        "<p>102.37</p>",
                        "<p>85.75</p>"
                    ],
                    options_hi: [
                        "<p>90.41</p>",
                        "<p>100</p>",
                        "<p>102.37</p>",
                        "<p>85.75</p>"
                    ],
                    solution_en: "<p>72.(d)&nbsp;Weight of 4 persons = 81 &times; 4 + (15 + 4) &times; 1 = 343 Kg<br>So, average weight of these 4 persons = <math display=\"inline\"><mfrac><mrow><mn>343</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 85.75 kg</p>",
                    solution_hi: "<p>72.(d)&nbsp;4 व्यक्तियों का वजन = 81 &times; 4 + (15 + 4) &times; 1 = 343 Kg<br>तो, इन 4 व्यक्तियों का औसत वजन = <math display=\"inline\"><mfrac><mrow><mn>343</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 85.75 kg</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The given line graph shows the number of scooters manufactured (in thousands) by companies X and Z, over the years. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013226843.png\" alt=\"rId55\" width=\"408\" height=\"271\"> <br>What is the average number of scooters manufactured by company X over the given period?</p>",
                    question_hi: "<p>73. दिया गया लाइन ग्राफ पिछले वर्षों में कंपनी x और z द्वारा निर्मित स्कूटरों की संख्या (हजार में) दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013226967.png\" alt=\"rId56\" height=\"271\"> <br>दी गई अवधि में कंपनी X द्वारा निर्मित स्कूटरों की औसत संख्या कितनी है ?</p>",
                    options_en: [
                        "<p>256000</p>",
                        "<p>213000</p>",
                        "<p>116500</p>",
                        "<p>126500</p>"
                    ],
                    options_hi: [
                        "<p>256000</p>",
                        "<p>213000</p>",
                        "<p>116500</p>",
                        "<p>126500</p>"
                    ],
                    solution_en: "<p>73.(c)<br>Required average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>128</mn><mo>+</mo><mn>110</mn><mo>+</mo><mn>102</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>109</mn><mo>+</mo><mn>170</mn></mrow><mn>6</mn></mfrac></math> = 116500 (because all values in thousands)</p>",
                    solution_hi: "<p>73.(c)<br>आवश्यक औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>128</mn><mo>+</mo><mn>110</mn><mo>+</mo><mn>102</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>109</mn><mo>+</mo><mn>170</mn></mrow><mn>6</mn></mfrac></math>&nbsp;= 116500 (क्योंकि सभी मान हजारों में हैं)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Identify a single-digit number other than 1, which may exactly divide the number<br>17<sup>3</sup> + 18<sup>3</sup> &minus; 16<sup>3</sup> &minus; 15<sup>3</sup></p>",
                    question_hi: "<p>74. 1 के अलावा एक-अंकीय संख्या ज्ञात करें, जो संख्या 17<sup>3</sup> + 18<sup>3</sup> &minus; 16<sup>3</sup> &minus; 15<sup>3</sup> को पूर्णतः विभाजित कर सकती है।</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>5</p>",
                        "<p>7</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>5</p>",
                        "<p>7</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>74.(d)&nbsp;17<sup>3</sup> + 18<sup>3</sup> &minus; 16<sup>3</sup> &minus; 15<sup>3</sup><br>4913 + 5832 &minus; 4096 &minus; 3375 = 3274<br>Clearly, the number is divisible by 2.</p>",
                    solution_hi: "<p>74.(d)&nbsp;17<sup>3</sup> + 18<sup>3</sup> &minus; 16<sup>3</sup> &minus; 15<sup>3</sup><br>4913 + 5832 &minus; 4096 &minus; 3375 = 3274<br>स्पष्टतः, संख्या 2 से विभाज्य है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Study the given bar-graph and answer the question that follows.<br>The bar-graph shows the production (in lakh) of kitchen appliances manufactured by three companies A, B, and C over a period of six years from 2012 to 2017.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013227089.png\" alt=\"rId57\" height=\"250\"> <br>80% of the combined average production of companies B and C is what percentage (to the nearest integer) more/less than 90% of the average production of company A. for the given period of 2012 to 2017 ?</p>",
                    question_hi: "<p>75. दिए गए बार ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br>बार ग्राफ में 2012 से 2017 तक छः वर्षों की अवधि में तीन कंपनियों A, B और C द्वारा निर्मित रसोई उपकरणों के उत्पादन (लाख में) को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742013227207.png\" alt=\"rId58\" height=\"250\"> <br>वर्ष 2012 से 2017 की दी गई अवधि में कंपनी B और C के संयुक्त औसत उत्पादन का 80%, कंपनी A के औसत उत्पादन के 90% से कितना प्रतिशत (निकटतम पूर्णांक तक) अधिक / कम है ?</p>",
                    options_en: [
                        "<p>21% more</p>",
                        "<p>19% more</p>",
                        "<p>19% less</p>",
                        "<p>21% less</p>"
                    ],
                    options_hi: [
                        "<p>21% अधिक</p>",
                        "<p>19% अधिक</p>",
                        "<p>19% कम</p>",
                        "<p>21% कम</p>"
                    ],
                    solution_en: "<p>75.(c)&nbsp;Production of company B = 130 + 80 + 70 + 90 + 180 + 80 = 630<br>Production of company C = 70 + 90 + 120 + 80 + 110 + 140 = 610<br>Average production of company B and C = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>630</mn><mo>+</mo><mn>610</mn></mrow><mn>12</mn></mfrac></math> = 103.33<br>80% of average production of company B and C = 103.33 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 82.66<br>Average production of company A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>160</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>70</mn><mo>+</mo><mn>110</mn></mrow><mn>6</mn></mfrac></math> = 113.33<br>90% of average production of company A = 113.33 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 102<br>required% = <math display=\"inline\"><mfrac><mrow><mn>102</mn><mo>-</mo><mn>82</mn><mo>.</mo><mn>66</mn></mrow><mrow><mn>102</mn></mrow></mfrac></math> &times; 100 = 18.95 (approx 19% less)</p>",
                    solution_hi: "<p>75.(c)&nbsp;कंपनी B का उत्पादन = 130 + 80 + 70 + 90 + 180 + 80 = 630<br>कंपनी C का उत्पादन = 70 + 90 + 120 + 80 + 110 + 140 = 610<br>कंपनी B और C का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>630</mn><mo>+</mo><mn>610</mn></mrow><mn>12</mn></mfrac></math>&nbsp;= 103.33<br>कंपनी B और C के औसत उत्पादन का 80% = 103.33 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 82.66<br>कंपनी A का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>160</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>70</mn><mo>+</mo><mn>110</mn></mrow><mn>6</mn></mfrac></math>&nbsp;= 113.33<br>कंपनी A के औसत उत्पादन का 90% = 113.33 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 102<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>102</mn><mo>-</mo><mn>82</mn><mo>.</mo><mn>66</mn></mrow><mrow><mn>102</mn></mrow></mfrac></math> &times; 100 = 18.95 (लगभग 19% कम)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has been divided into four segments. Identify the segment that has a grammatical error.<br>Soldiers in / the Indian Armed Forces are / trained never to / fight cowardly.</p>",
                    question_hi: "<p>76.The following sentence has been divided into four segments. Identify the segment that has a grammatical error.<br>Soldiers in / the Indian Armed Forces are / trained never to / fight cowardly.</p>",
                    options_en: [
                        "<p>fight cowardly.</p>",
                        "<p>the Indian Armed Forces are</p>",
                        "<p>trained never to</p>",
                        "<p>Soldiers in</p>"
                    ],
                    options_hi: [
                        "<p>fight cowardly.</p>",
                        "<p>the Indian Armed Forces are</p>",
                        "<p>trained never to</p>",
                        "<p>Soldiers in</p>"
                    ],
                    solution_en: "<p>76.(c) trained never to<br>The position of &lsquo;never&rsquo; is incorrect in this sentence. Here, &lsquo;never&rsquo; is used to modify the infinitive &lsquo;to fight&rsquo;, therefore it will be used between &lsquo;to&rsquo; and &lsquo;fight&rsquo;. Hence, &lsquo;trained to never&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(c) trained never to<br>इस sentence में &lsquo;never&rsquo; का position गलत है। यहाँ, &lsquo;never&rsquo; का प्रयोग infinitive &lsquo;to fight&rsquo; को modify करने के लिए किया गया है, इसलिए इसका प्रयोग &lsquo;to&rsquo; और &lsquo;fight&rsquo; के बीच किया जाएगा। इसलिए, &lsquo;trained to never&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate meaning of idiom in the sentence.<br>He worked hard to <strong><span style=\"text-decoration: underline;\">feather his nest</span></strong>.</p>",
                    question_hi: "<p>77. Select the most appropriate meaning of idiom in the sentence.<br>He worked hard to <strong><span style=\"text-decoration: underline;\">feather his nest</span></strong>.</p>",
                    options_en: [
                        "<p>decorate his home</p>",
                        "<p>make money for oneself in an opportunistic way</p>",
                        "<p>trap birds</p>",
                        "<p>looked for help</p>"
                    ],
                    options_hi: [
                        "<p>decorate his home</p>",
                        "<p>make money for oneself in an opportunistic way</p>",
                        "<p>trap birds</p>",
                        "<p>looked for help</p>"
                    ],
                    solution_en: "<p>77.(b) make money for oneself in an opportunistic way.<br><strong>Example</strong> - He used the free internet in office to feather his own nest.</p>",
                    solution_hi: "<p>77.(b) अवसरवादी तरीके से अपने लिए पैसा कमाना। <br><strong>Example</strong> - He used the free internet in office to feather his own nest.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution&rsquo;.<br>This is the group of leaders <span style=\"text-decoration: underline;\">in whom the ruling government</span> has placed its trust.</p>",
                    question_hi: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution&rsquo;.<br>This is the group of leaders <span style=\"text-decoration: underline;\">in whom the ruling government</span> has placed its trust.</p>",
                    options_en: [
                        "<p>no substitution</p>",
                        "<p>in who the ruling government</p>",
                        "<p>on who the ruling government</p>",
                        "<p>with who the ruling government</p>"
                    ],
                    options_hi: [
                        "<p>no substitution</p>",
                        "<p>in who the ruling government</p>",
                        "<p>on who the ruling government</p>",
                        "<p>with who the ruling government</p>"
                    ],
                    solution_en: "<p>78.(a) no substitution<br>The given sentence is grammatically correct.</p>",
                    solution_hi: "<p>78.(a) no substitution<br>दिया गया sentence grammatically सही है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the option that can be used as a one-word substitute for the given group of words.<br>A film shown during the day, especially in the afternoon</p>",
                    question_hi: "<p>79. Select the option that can be used as a one-word substitute for the given group of words.<br>A film shown during the day, especially in the afternoon</p>",
                    options_en: [
                        "<p>Theatre</p>",
                        "<p>Dramatics</p>",
                        "<p>Matinee</p>",
                        "<p>Cinema</p>"
                    ],
                    options_hi: [
                        "<p>Theatre</p>",
                        "<p>Dramatics</p>",
                        "<p>Matinee</p>",
                        "<p>Cinema</p>"
                    ],
                    solution_en: "<p>79.(c) <strong>Matinee-</strong> a film shown during the day, especially in the afternoon<br><strong>Theatre-</strong> a building or outdoor area in which plays and other dramatic performances are given.<br><strong>Dramatics-</strong> the study or practice of acting in and producing plays.<br><strong>Cinema-</strong> a theatre where films are shown for public entertainment.</p>",
                    solution_hi: "<p>79.(c) <strong>Matinee</strong> (तीसरे पहर के नाटक का गायन) - a film shown during the day, especially in the afternoon<br><strong>Theatre</strong> (नृत्यशाला) - a building or outdoor area in which plays and other dramatic performances are given.<br><strong>Dramatics</strong> (नाटक) - the study or practice of acting in and producing plays.<br><strong>Cinema</strong> (सिनेमा-घर) - a theatre where films are shown for public entertainment.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence. <br>My friend requested me to bring him a sandwich.</p>",
                    question_hi: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence. <br>My friend requested me to bring him a sandwich.</p>",
                    options_en: [
                        "<p>He said, &lsquo;My friend, please bring me a sandwich.\'</p>",
                        "<p>My friend said, &lsquo;Will you bring me a sandwich.&rsquo;</p>",
                        "<p>&lsquo;Please bring me a sandwich&rsquo;, said my friend.</p>",
                        "<p>&lsquo;Please bring my friend a sandwich.\', said he.</p>"
                    ],
                    options_hi: [
                        "<p>He said, &lsquo;My friend, please bring me a sandwich.\'</p>",
                        "<p>My friend said, &lsquo;Will you bring me a sandwich.&rsquo;</p>",
                        "<p>&lsquo;Please bring me a sandwich&rsquo;, said my friend.</p>",
                        "<p>&lsquo;Please bring my friend a sandwich.\', said he.</p>"
                    ],
                    solution_en: "<p>80.(c) &lsquo;Please bring me a sandwich&rsquo;, said my friend. <br>a. He <strong>said,</strong> &lsquo;My friend, please bring me a sandwich.\'(Incorrect reporting verb)<br>b. My friend said, &lsquo;Will you bring me a sandwich.&rsquo; (Meaning of sentence changed) <br>d. &lsquo;Please bring my friend a sandwich.\', said he.(My friend is missing)</p>",
                    solution_hi: "<p>80.(c) &lsquo;Please bring me a sandwich&rsquo;, said my friend. <br>a. He <strong>said,</strong> &lsquo;My friend, please bring me a sandwich.\'(गलत reporting verb)<br>b. My friend said, &lsquo;Will you bring me a sandwich.&rsquo; (वाक्य का अर्थ बदल गया) <br>d. &lsquo;Please bring my friend a sandwich.\', said he.(My friend नहीं है)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Choose the word opposite in meaning to the given word. <br>FORBEARANCE</p>",
                    question_hi: "<p>81. Choose the word opposite in meaning to the given word. <br>FORBEARANCE</p>",
                    options_en: [
                        "<p>patience</p>",
                        "<p>self-control</p>",
                        "<p>intolerance</p>",
                        "<p>preference</p>"
                    ],
                    options_hi: [
                        "<p>patience</p>",
                        "<p>self-control</p>",
                        "<p>intolerance</p>",
                        "<p>preference</p>"
                    ],
                    solution_en: "<p>81.(c) intolerance. <br><strong>Forbearance</strong> - patience, self-control; restraint and tolerance.<br><strong>Intolerance</strong> - unwillingness to accept views, beliefs, or behaviour that differ from one\'s own.</p>",
                    solution_hi: "<p>81. (c) intolerance. <br><strong>Forbearance</strong> - धैर्य, संयम और सहनशीलता।<br><strong>Intolerance</strong> - स्वयं से भिन्न (different) विचारों, विश्वासों या व्यवहार को स्वीकार करने की अनिच्छा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the option that expresses the following sentence in passive voice.<br>I am going to ask all employees about the incident that took place in the canteen yesterday.</p>",
                    question_hi: "<p>82.Select the option that expresses the following sentence in passive voice. <br>I am going to ask all employees about the incident that took place in the canteen yesterday.</p>",
                    options_en: [
                        "<p>All the employees are gone to be asked about the incident that took place in the canteen yesterday.</p>",
                        "<p>All the employees are going to be asked about the incident that took place in the canteen yesterday.</p>",
                        "<p>All the employees are asked by me about the incident that took place in the canteen yesterday.</p>",
                        "<p>All the employees will go to be questioned by me about the incident that took place in the canteen yesterday.</p>"
                    ],
                    options_hi: [
                        "<p>All the employees are gone to be asked about the incident that took place in the canteen yesterday.</p>",
                        "<p>All the employees are going to be asked about the incident that took place in the canteen yesterday.</p>",
                        "<p>All the employees are asked by me about the incident that took place in the canteen yesterday.</p>",
                        "<p>All the employees will go to be questioned by me about the incident that took place in the canteen yesterday.</p>"
                    ],
                    solution_en: "<p>82.(b) All the employees are going to be asked about the incident that took place in the canteen yesterday. (Correct)<br>(a) All the employees are <span style=\"text-decoration: underline;\">gone</span> to be asked about the incident that took place in the canteen yesterday.(Incorrect Verb)<br>(c) All the employees are <span style=\"text-decoration: underline;\">asked</span> by me about the incident that took place in the canteen Yesterday. (Incorrect Verb)<br>(d) All the employees will go to be questioned by me about the incident that took place in the canteen yesterday. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>82.(b) All the employees are going to be asked about the incident that took place in the canteen yesterday. (Correct) <br>(a) All the employees are <span style=\"text-decoration: underline;\">gone</span> to be asked about the incident that took place in the canteen yesterday.(गलत verb <span style=\"text-decoration: underline;\">(gone)</span> का प्रयोग किया गया है। (going) का प्रयोग होगा | )<br>(c) All the employees are <span style=\"text-decoration: underline;\">asked</span> by me about the incident that took place in the canteen Yesterday. (गलत verb <span style=\"text-decoration: underline;\">(asked)</span> का प्रयोग किया गया है। (going) का प्रयोग होगा | )<br>(d) All the employees will go to be questioned by me about the&nbsp;incident that took place in the canteen yesterday. (Sentence Structure सही नहीं है)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate homonym to fill in the blank.<br>__________is a red-coloured root vegetable.</p>",
                    question_hi: "<p>83. Select the most appropriate homonym to fill in the blank.<br>__________is a red-coloured root vegetable.</p>",
                    options_en: [
                        "<p>Beat</p>",
                        "<p>Beet</p>",
                        "<p>Bit</p>",
                        "<p>Bead</p>"
                    ],
                    options_hi: [
                        "<p>Beat</p>",
                        "<p>Beet</p>",
                        "<p>Bit</p>",
                        "<p>Bead</p>"
                    ],
                    solution_en: "<p>83.(b) <strong>Beet</strong> - a round, dark red vegetable that grows in the ground and is usually cooked and eaten cold<br><strong>Beat</strong> - to defeat somebody<br><strong>Bit</strong> - a small amount or piece of something.<br><strong>Bead</strong> - a small, round ball of glass, plastic, or wood that is used for making jewellery.</p>",
                    solution_hi: "<p>83.(b) <strong>Beet</strong> (चुकंदर) - a round, dark red vegetable that grows in the ground and is usually cooked and eaten cold<br><strong>Beat</strong> (हराना ) - to defeat somebody<br><strong>Bit</strong> (छोटा अंश) - a small amount or piece of something.<br><strong>Bead</strong> (मनका/मोती) - a small, round ball of glass, plastic, or wood that is used for making jewellery.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. It also leaves some students who do not have access to the internet behind.<br>Q. Not every child in the school system has the ability to have access to the internet.<br>R. For such children once they leave the school property it becomes difficult for students to complete their work at home.<br>S. Technology presents an absence of interaction in the classroom.</p>",
                    question_hi: "<p>84. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. It also leaves some students who do not have access to the internet behind.<br>Q. Not every child in the school system has the ability to have access to the internet.<br>R. For such children once they leave the school property it becomes difficult for students to complete their work at home.<br>S. Technology presents an absence of interaction in the classroom.</p>",
                    options_en: [
                        "<p>QSPR</p>",
                        "<p>SQRP</p>",
                        "<p>QRSP</p>",
                        "<p>SPQR</p>"
                    ],
                    options_hi: [
                        "<p>QSPR</p>",
                        "<p>SQRP</p>",
                        "<p>QRSP</p>",
                        "<p>SPQR</p>"
                    ],
                    solution_en: "<p>84. (d) SPQR<br>Sentence S will be the starting line as it contains the main idea of the parajumble i.e. Technology and its disadvantage. And, Sentence P states one more disadvantage of it. So, P will follow S. Further, Sentence Q states that every child doesn&rsquo;t have access to the internet and Sentence R states the difficulty of the children. So, R will follow Q. Going through the options, option (d) has the correct sequence.</p>",
                    solution_hi: "<p>84. (d) SPQR<br>Sentence S starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;Technology and its disadvantage&rsquo; शामिल हैं। Sentence P इसका एक और नुकसान बताता है। तो, S के बाद P आएगा । आगे, Sentence Q कहता है कि हर बच्चे की इंटरनेट तक पहुंच नहीं है और Sentence R बच्चों की कठिनाई को बताता है। इसलिए, Q के बाद R आएगा । Options के माध्यम से जाने पर , option (d) में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Find the correctly spelt word.</p>",
                    question_hi: "<p>85. Find the correctly spelt word.</p>",
                    options_en: [
                        "<p>Fascination</p>",
                        "<p>Vindicasion</p>",
                        "<p>Assemilation</p>",
                        "<p>Compitation</p>"
                    ],
                    options_hi: [
                        "<p>Fascination</p>",
                        "<p>Vindicasion</p>",
                        "<p>Assemilation</p>",
                        "<p>Compitation</p>"
                    ],
                    solution_en: "<p>85.(a) Fascination <br>Other words- Vindication, Assimilation, Competition</p>",
                    solution_hi: "<p>85. (a) Fascination <br>अन्य शब्द - Vindication, Assimilation, Competition</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Identify the meaning of the idiom in the given situation. <br>I will always stand by my friend.</p>",
                    question_hi: "<p>86. Identify the meaning of the idiom in the given situation. <br>I will always stand by my friend.</p>",
                    options_en: [
                        "<p>Support in difficult situation</p>",
                        "<p>Stand next to</p>",
                        "<p>Work together</p>",
                        "<p>Be in a queue</p>"
                    ],
                    options_hi: [
                        "<p>Support in difficult situation</p>",
                        "<p>Stand next to</p>",
                        "<p>Work together</p>",
                        "<p>Be in a queue</p>"
                    ],
                    solution_en: "<p>86.(a) <strong>Stand by someone</strong> - support in difficult situation.</p>",
                    solution_hi: "<p>86.(a) <strong>Stand by someone</strong> - support in difficult situation./कठिन परिस्थिति में सहायता</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. She spent two days floating in the sea, holding on to a wooden door.<br>Q. She was brought to the shore by a wave and was found walking on the seashore in a daze<br>R. Eleven times she saw relief helicopters overhead, but they did not see her. <br>S. Thirteen year-old Meghna was swept away along with her parents.</p>",
                    question_hi: "<p>87. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. She spent two days floating in the sea, holding on to a wooden door.<br>Q. She was brought to the shore by a wave and was found walking on the seashore in a daze<br>R. Eleven times she saw relief helicopters overhead, but they did not see her. <br>S. Thirteen year-old Meghna was swept away along with her parents.</p>",
                    options_en: [
                        "<p>RQSP</p>",
                        "<p>SPRQ</p>",
                        "<p>SRQP</p>",
                        "<p>SQPR</p>"
                    ],
                    options_hi: [
                        "<p>RQSP</p>",
                        "<p>SPRQ</p>",
                        "<p>SRQP</p>",
                        "<p>SQPR</p>"
                    ],
                    solution_en: "<p>87.(b) SPRQ<br>Sentence S will be the starting line as it contains the main subject of the parajumble i.e. Meghna who was swept away. And, Sentence P states how she managed to survive. So, P will follow S. Further, Sentence R states although she saw relief helicopters but didn&rsquo;t receive any help and Sentence Q states that she was brought to seashore by a wave. So, Q will follow R. Going through the options, option (b) has the correct sequence.</p>",
                    solution_hi: "<p>87.(b) SPRQ<br>Sentence S प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विषय शामिल है - मेघना, जो बह गई थी। Sentence P बताता है कि वह कैसे जीवित रही। इसलिए, S के बाद P आएगा।आगे, वाक्य R कहता है कि उसने राहत हेलीकॉप्टर देखे, लेकिन उसे कोई मदद नहीं मिली और Sentence Q कहता है कि उसे एक लहर द्वारा समुद्र के किनारे लाया गया था। तो, R के बाद Q आएगा। विकल्पों के माध्यम से, विकल्प (b) में सही क्रम है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Identify the segment in the sentence, which contains the error.<br>The contrast between / Britain and other countries / of Europe / are striking.</p>",
                    question_hi: "<p>88. Identify the segment in the sentence, which contains the error.<br>The contrast between / Britain and other countries / of Europe / are striking.</p>",
                    options_en: [
                        "<p>The contrast between</p>",
                        "<p>are striking</p>",
                        "<p>Britain and other countries</p>",
                        "<p>of Europe</p>"
                    ],
                    options_hi: [
                        "<p>The contrast between</p>",
                        "<p>are striking</p>",
                        "<p>Britain and other countries</p>",
                        "<p>of Europe</p>"
                    ],
                    solution_en: "<p>88.(b) According to the &ldquo;<strong><span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span></strong>&rdquo;, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;contrast&rsquo; is a singular subject that will take &lsquo;is&rsquo; as a singular verb. Hence, &lsquo;is striking&rsquo; is the most appropriate structure.</p>",
                    solution_hi: "<p>88.(b) &ldquo;<strong><span style=\"text-decoration: underline;\">Subject-Verb Agreement Rule</span></strong>&rdquo;, के अनुसार, एक singular subject के साथ हमेशा singular verb आती है और plural subject के साथ हमेशा plural verb आती है। दिए गए वाक्य में, &lsquo;contrast&rsquo; एक singular subject है जो \'is\' को singular verb के रूप लेगा । इसलिए, &lsquo;is striking&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate one word substitution for the given sentence.<br>A seat for a passenger on a bicycle or motorbike</p>",
                    question_hi: "<p>89. Select the most appropriate one word substitution for the given sentence.<br>A seat for a passenger on a bicycle or motorbike</p>",
                    options_en: [
                        "<p>Pillion</p>",
                        "<p>Girdle</p>",
                        "<p>Bridle</p>",
                        "<p>Cushion</p>"
                    ],
                    options_hi: [
                        "<p>Pillion</p>",
                        "<p>Girdle</p>",
                        "<p>Bridle</p>",
                        "<p>Cushion</p>"
                    ],
                    solution_en: "<p>89.(a) <strong>Pillion</strong> - a seat for a passenger on a bicycle or motorbike<br><strong>Girdle</strong> - a belt or cord worn around the waist<br><strong>Bridle</strong> - the leather straps that you put on a horse&rsquo;s head so that you can control it when you are riding it<br><strong>Cushion</strong> - a bag filled with soft material, for example, feathers, which you put on a chair, etc</p>",
                    solution_hi: "<p>89.(a) <strong>Pillion</strong> (पीछे की सीट)-a seat for a passenger on a bicycle or motorbike<br><strong>Girdle</strong> (कमरबंद)- a belt or cord worn around the waist<br><strong>Bridle</strong> (लगाम)- the leather straps that you put on a horse&rsquo;s head so that you can control it when you are riding it<br><strong>Cushion</strong> (तकिया)- a bag filled with soft material, for example, feathers, which you put on a chair, etc</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that expresses the meaning of the underlined segment.<br>The regulation bans any direct or indirect promotion through radio or television of <span style=\"text-decoration: underline;\">supernatural</span> practices.</p>",
                    question_hi: "<p>90. Select the option that expresses the meaning of the underlined segment.<br>The regulation bans any direct or indirect promotion through radio or television of <span style=\"text-decoration: underline;\">supernatural</span> practices.</p>",
                    options_en: [
                        "<p>Questionable</p>",
                        "<p>Evident</p>",
                        "<p>Obtuse</p>",
                        "<p>Occult</p>"
                    ],
                    options_hi: [
                        "<p>Questionable</p>",
                        "<p>Evident</p>",
                        "<p>Obtuse</p>",
                        "<p>Occult</p>"
                    ],
                    solution_en: "<p>90.(d) <strong>Occult</strong> - relating to mystical, magical, or supernatural phenomena.<br><strong>Supernatural-</strong> attributed to some force beyond scientific understanding or the laws of nature.<br><strong>Questionable-</strong> doubtful or open to question.<br><strong>Evident-</strong> clearly seen or understood.<br><strong>Obtuse-</strong> slow to understand or difficult to comprehend.</p>",
                    solution_hi: "<p>90.(d) <strong>Occult</strong> (रहस्यमय) - relating to mystical, magical, or supernatural phenomena.<br><strong>Supernatural</strong> (अलौकिक) - attributed to some force beyond scientific understanding or the laws of nature.<br><strong>Questionable</strong> (प्रश्नवाचक) - doubtful or open to question.<br><strong>Evident</strong> (स्पष्ट) - clearly seen or understood.<br><strong>Obtuse</strong> (कुंठित) - slow to understand or difficult to comprehend.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Find a word that is the synonym of <br>tenet</p>",
                    question_hi: "<p>91. Find a word that is the synonym of <br>tenet</p>",
                    options_en: [
                        "<p>belief</p>",
                        "<p>provision</p>",
                        "<p>perspective</p>",
                        "<p>view</p>"
                    ],
                    options_hi: [
                        "<p>belief</p>",
                        "<p>provision</p>",
                        "<p>perspective</p>",
                        "<p>view</p>"
                    ],
                    solution_en: "<p>91.(a) belief.<br>Tenet - a principle or belief, especially one of the main principles of a religion or philosophy.</p>",
                    solution_hi: "<p>91.(a) belief.<br>Tenet - विशेष रूप से एक धर्म या दर्शन के मुख्य सिद्धांतों में से एक।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate ANTONYM of the underlined word in the following sentence.<br>The government is proposing to <span style=\"text-decoration: underline;\">incinerate</span> cattle carcasses at many sites, some of which are in populated areas.</p>",
                    question_hi: "<p>92. Select the most appropriate ANTONYM of the underlined word in the following sentence.<br>The government is proposing to <span style=\"text-decoration: underline;\">incinerate</span> cattle carcasses at many sites, some of which are in populated areas.</p>",
                    options_en: [
                        "<p>Combust</p>",
                        "<p>Blaze</p>",
                        "<p>Extinguish</p>",
                        "<p>Oxidise</p>"
                    ],
                    options_hi: [
                        "<p>Combust</p>",
                        "<p>Blaze</p>",
                        "<p>Extinguish</p>",
                        "<p>Oxidise</p>"
                    ],
                    solution_en: "<p>92.(c) <strong>Extinguish-</strong> to put out or bring to an end (a fire or light).<br><strong>Incinerate-</strong> to destroy something by burning it to ashes.<br><strong>Combust-</strong> to catch fire or burn.<br><strong>Blaze-</strong> a large or intense fire.<br><strong>Oxidise-</strong> to combine chemically with oxygen.</p>",
                    solution_hi: "<p>92.(c) <strong>Extinguish</strong> (बुझाना) - to put out or bring to an end (a fire or light).<br><strong>Incinerate</strong> (जला देना) - to destroy something by burning it to ashes.<br><strong>Combust</strong> (दहन) - to catch fire or burn.<br><strong>Blaze</strong> (चमकना) - a large or intense fire.<br><strong>Oxidise</strong> (ऑक्सीकरण) - to combine chemically with oxygen.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>For many tourists, keeping a tourist guide during tours <strong><span style=\"text-decoration: underline;\">help to satisfy their hunger</span></strong> for knowledge.</p>",
                    question_hi: "<p>93. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>For many tourists, keeping a tourist guide during tours <strong><span style=\"text-decoration: underline;\">help to satisfy their hunger</span></strong> for knowledge.</p>",
                    options_en: [
                        "<p>Helps to satisfy his hunger</p>",
                        "<p>Helps satisfy their hunger</p>",
                        "<p>Help to satisfy hunger</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>Helps to satisfy his hunger</p>",
                        "<p>Helps satisfy their hunger</p>",
                        "<p>Help to satisfy hunger</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>93.(b) Helps satisfy their hunger<br>&ldquo;Helps&rdquo; should be used as the sentence is in &ldquo;simple present tense&rdquo; and the singular form of verb is used for &ldquo;Tourist guide&rdquo; <br>&ldquo;Their&rdquo; should be used for &ldquo;Tourists&rdquo;<br>The word &ldquo;Help&rdquo; can be used with both bare infinitive (to + V<sub>1</sub>) as well as infinitive (V<sub>1</sub>).</p>",
                    solution_hi: "<p>93.(b) Helps satisfy their hunger<br>&ldquo;Helps&rdquo; का प्रयोग किया जाना चाहिए क्योंकि वाक्य &ldquo;simple present tense&rdquo; में है और &ldquo;Tourist guide&rdquo; के लिए singular form of verb प्रयोग किया जाता है ।<br>&ldquo;Tourists&rdquo; के लिए &ldquo;Their&rdquo; का उपयोग किया जाना चाहिए।<br>&ldquo;Help&rdquo; शब्द का उपयोग bare infinitive (to + V<sub>1</sub>) के साथ-साथ infinitive (V<sub>1</sub>) दोनों के लिए किया जा सकता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank.<br>The noise of the drum beats _____________ and frightened the tiger.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank.<br>The noise of the drum beats _____________ and frightened the tiger.</p>",
                    options_en: [
                        "<p>perplexed</p>",
                        "<p>pertained</p>",
                        "<p>prevented</p>",
                        "<p>persisted</p>"
                    ],
                    options_hi: [
                        "<p>perplexed</p>",
                        "<p>pertained</p>",
                        "<p>prevented</p>",
                        "<p>persisted</p>"
                    ],
                    solution_en: "<p>94.(a) perplexed<br>&ldquo;Perplex&rdquo; means to baffle. <br>Pertain means applicable to.<br>Prevent means to keep {something} from happening. <br>Persist means to not given up.</p>",
                    solution_hi: "<p>94. (a) perplexed<br>&rsquo;Perplex&rdquo; का अर्थ है भ्रमित करना। <br>Pertain का मतलब लागू होना। <br>Prevent का अर्थ है कुछ होने से रोकना।<br>Persist का अर्थ है हार न मानना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the option that expresses the following sentence in active voice.<br>The flyer for the international symposium is being sent by students to institutes all over the country.</p>",
                    question_hi: "<p>95. Select the option that expresses the following sentence in active voice.<br>The flyer for the international symposium is being sent by students to institutes all over the country.</p>",
                    options_en: [
                        "<p>Students are being sent the flyer for the international symposium to institutes all over the country.</p>",
                        "<p>The flyer for the international symposium is sent by students to institutes all over the country.</p>",
                        "<p>Students are sending the flyer for the international symposium to institutes all over the country.</p>",
                        "<p>The flyer is sending the students to institutes all over the country for the international symposium.</p>"
                    ],
                    options_hi: [
                        "<p>Students are being sent the flyer for the international symposium to institutes all over the country.</p>",
                        "<p>The flyer for the international symposium is sent by students to institutes all over the country.</p>",
                        "<p>Students are sending the flyer for the international symposium to institutes all over the country.</p>",
                        "<p>The flyer is sending the students to institutes all over the country for the international symposium.</p>"
                    ],
                    solution_en: "<p>95.(c) Students are sending the flyer for the international symposium to institutes all over the country. (Correct)<br>(a) Students <span style=\"text-decoration: underline;\">are being sent</span> the flyer for the international symposium to institutes all over the country. (Incorrect Tense)<br>(b) The flyer for the international symposium is sent by students to institutes all over the Country. (Incorrect Sentence Structure)<br>(d) The flyer is sending the students to institutes all over the country for the international Symposium. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>95.(c) Students are sending the flyer for the international symposium to institutes all over the country. (Correct)<br>(a) Students <span style=\"text-decoration: underline;\">are being sent</span> the flyer for the international symposium to institutes all over the country. (गलत verb (<span style=\"text-decoration: underline;\">are being sent</span>) का प्रयोग किया गया है। (are sending) का प्रयोग होगा। )<br>(b) The flyer for the international symposium is sent by students to institutes all over the Country. (Sentence Structure सही नहीं है)<br>(d) The flyer is sending the students to institutes all over the country for the international Symposium. (Sentence Structure सही नहीं है)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :</strong><br>Human activities are changing Earth\'s natural greenhouse___96___ Burning fossil fuels like coal and oil puts___97 ___ carbon dioxide into our atmosphere. NASA has ___98____ increase in the amount of carbon dioxide and some other greenhouse gases in ___99____ atmosphere. Too much of these greenhouse gases can ____100____ Earth\'s atmosphere to trap more and more heat. This causes Earth to warm up.<br>Select the most appropriate option to fill in the blank No.96.</p>",
                    question_hi: "<p>96.<strong> Cloze Test :</strong><br>Human activities are changing Earth\'s natural greenhouse___96___ Burning fossil fuels like coal and oil puts___97 ___ carbon dioxide into our atmosphere. NASA has ___98____ increase in the amount of carbon dioxide and some other greenhouse gases in ___99____ atmosphere. Too much of these greenhouse gases can ____100____ Earth\'s atmosphere to trap more and more heat. This causes Earth to warm up.<br>Select the most appropriate option to fill in the blank No.96.</p>",
                    options_en: [
                        "<p>Effect</p>",
                        "<p>Affect</p>",
                        "<p>Aspect</p>",
                        "<p>Impact</p>"
                    ],
                    options_hi: [
                        "<p>Effect</p>",
                        "<p>Affect</p>",
                        "<p>Aspect</p>",
                        "<p>Impact</p>"
                    ],
                    solution_en: "<p>96.(a) Effect<br>&lsquo;Effect&rsquo; means a change that is caused by something. The given passage states that Human activities are changing Earth\'s natural greenhouse effect. Hence, &lsquo;effect&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(a) Effect<br>&lsquo;Effect&rsquo; का अर्थ है- ऐसा परिवर्तन जो किसी चीज के कारण होता है। दिए गए passage में कहा गया है कि मानवीय गतिविधियाँ पृथ्वी के प्राकृतिक ग्रीनहाउस प्रभाव को बदल रही हैं। इसलिए, &lsquo;effect&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test</strong> :<br>Human activities are changing Earth\'s natural greenhouse___96___ Burning fossil fuels like coal and oil puts___97 ___ carbon dioxide into our atmosphere. NASA has ___98____ increase in the amount of carbon dioxide and some other greenhouse gases in ___99____ atmosphere. Too much of these greenhouse gases can ____100____ Earth\'s atmosphere to trap more and more heat. This causes Earth to warm up.<br>Select the most appropriate option to fill in the blank No.97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test :</strong><br>Human activities are changing Earth\'s natural greenhouse___96___ Burning fossil fuels like coal and oil puts___97 ___ carbon dioxide into our atmosphere. NASA has ___98____ increase in the amount of carbon dioxide and some other greenhouse gases in ___99____ atmosphere. Too much of these greenhouse gases can ____100____ Earth\'s atmosphere to trap more and more heat. This causes Earth to warm up.<br>Select the most appropriate option to fill in the blank No.97.</p>",
                    options_en: [
                        "<p>Much</p>",
                        "<p>More</p>",
                        "<p>Many</p>",
                        "<p>Most</p>"
                    ],
                    options_hi: [
                        "<p>Much</p>",
                        "<p>More</p>",
                        "<p>Many</p>",
                        "<p>Most</p>"
                    ],
                    solution_en: "<p>97.(b) More<br>&lsquo;More&rsquo; means something extra as well as what you have. The given passage states that burning fossil fuels like coal and oil puts more carbon dioxide into our atmosphere. Hence, &lsquo;more&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) More<br>&lsquo;More&rsquo; का अर्थ है - कुछ अतिरिक्त और साथ ही आपके पास क्या है। दिए गए passage में कहा गया है कि कोयले और तेल जैसे जीवाश्म ईंधन को जलाने से हमारे वातावरण में अधिक कार्बन डाइऑक्साइड होता है। अतः&lsquo;more&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :</strong><br>Human activities are changing Earth\'s natural greenhouse___96___ Burning fossil fuels like coal and oil puts___97 ___ carbon dioxide into our atmosphere. NASA has ___98____ increase in the amount of carbon dioxide and some other greenhouse gases in ___99____ atmosphere. Too much of these greenhouse gases can ____100____ Earth\'s atmosphere to trap more and more heat. This causes Earth to warm up.<br>Select the most appropriate option to fill in the blank No.98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test :</strong><br>Human activities are changing Earth\'s natural greenhouse___96___ Burning fossil fuels like coal and oil puts___97 ___ carbon dioxide into our atmosphere. NASA has ___98____ increase in the amount of carbon dioxide and some other greenhouse gases in ___99____ atmosphere. Too much of these greenhouse gases can ____100____ Earth\'s atmosphere to trap more and more heat. This causes Earth to warm up.<br>Select the most appropriate option to fill in the blank No.98.</p>",
                    options_en: [
                        "<p>to observe</p>",
                        "<p>Observing</p>",
                        "<p>Observe</p>",
                        "<p>Observed</p>"
                    ],
                    options_hi: [
                        "<p>to observe</p>",
                        "<p>Observing</p>",
                        "<p>Observe</p>",
                        "<p>Observed</p>"
                    ],
                    solution_en: "<p>98.(d) Observed<br>&ldquo;Has + V<sub>3</sub>(third form of the verb)&rdquo; is the correct grammatical structure for the given sentence. Hence, &lsquo;observed(V<sub>3</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) Observed<br>दिए गए वाक्य के लिए &ldquo;Has + V<sub>3</sub>(third form of the verb)&rdquo; सही व्याकरणिक संरचना है। इसलिए, &lsquo;observed(V<sub>3</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :</strong><br>Human activities are changing Earth\'s natural greenhouse___96___ Burning fossil fuels like coal and oil puts___97 ___ carbon dioxide into our atmosphere. NASA has ___98____ increase in the amount of carbon dioxide and some other greenhouse gases in ___99____ atmosphere. Too much of these greenhouse gases can ____100____ Earth\'s atmosphere to trap more and more heat. This causes Earth to warm up.<br>Select the most appropriate option to fill in the blank No.99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test :</strong><br>Human activities are changing Earth\'s natural greenhouse___96___ Burning fossil fuels like coal and oil puts___97 ___ carbon dioxide into our atmosphere. NASA has ___98____ increase in the amount of carbon dioxide and some other greenhouse gases in ___99____ atmosphere. Too much of these greenhouse gases can ____100____ Earth\'s atmosphere to trap more and more heat. This causes Earth to warm up.<br>Select the most appropriate option to fill in the blank No .99.</p>",
                    options_en: [
                        "<p>its</p>",
                        "<p>our</p>",
                        "<p>their</p>",
                        "<p>ours</p>"
                    ],
                    options_hi: [
                        "<p>its</p>",
                        "<p>our</p>",
                        "<p>their</p>",
                        "<p>ours</p>"
                    ],
                    solution_en: "<p>99.(b) our<br>The given passage states that NASA has observed increase in the amount of carbon dioxide and some other greenhouse gases in our atmosphere. Hence, &lsquo;our&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b) our<br>दिए गए passage में कहा गया है कि NASA ने हमारे वातावरण में कार्बन डाइऑक्साइड और कुछ अन्य ग्रीनहाउस गैसों की मात्रा में वृद्धि देखी है। इसलिए, &lsquo;our&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :</strong><br>Human activities are changing Earth\'s natural greenhouse___96___ Burning fossil fuels like coal and oil puts___97 ___ carbon dioxide into our atmosphere. NASA has ___98____ increase in the amount of carbon dioxide and some other greenhouse gases in ___99____ atmosphere. Too much of these greenhouse gases can ____100____ Earth\'s atmosphere to trap more and more heat. This causes Earth to warm up.<br>Select the most appropriate option to fill in the blank No.100.</p>",
                    question_hi: "<p>100. Cloze Test :<br>Human activities are changing Earth\'s natural greenhouse___96___ Burning fossil fuels like coal and oil puts___97 ___ carbon dioxide into our atmosphere. NASA has ___98____ increase in the amount of carbon dioxide and some other greenhouse gases in ___99____ atmosphere. Too much of these greenhouse gases can ____100____ Earth\'s atmosphere to trap more and more heat. This causes Earth to warm up.<br>Select the most appropriate option to fill in the blank No.100.</p>",
                    options_en: [
                        "<p>effect</p>",
                        "<p>reason</p>",
                        "<p>source</p>",
                        "<p>cause</p>"
                    ],
                    options_hi: [
                        "<p>effect</p>",
                        "<p>reason</p>",
                        "<p>source</p>",
                        "<p>cause</p>"
                    ],
                    solution_en: "<p>100.(d) cause<br>&lsquo;Cause&rsquo; means a thing or person that makes something happen. The given passage states that too much of these greenhouse gases can cause Earth\'s atmosphere to trap more and more heat. Hence, &lsquo;cause&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) cause<br>&lsquo;Cause&rsquo; का अर्थ है - कारण बनना । दिए गए passage में कहा गया है कि इन ग्रीनहाउस गैसों की बहुत अधिक मात्रा पृथ्वी के वातावरण को अधिक से अधिक गर्मी में फँसाने का कारण बन सकती है। इसलिए, &lsquo;cause&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>