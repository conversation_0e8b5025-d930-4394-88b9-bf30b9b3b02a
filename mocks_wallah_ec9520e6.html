<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Who among the following ended the Kakatiya dynasty rule?</p>",
                    question_hi: "<p>1. निम्नलिखित में से किसने काकतीय वंश के शासन को समाप्त किया?</p>",
                    options_en: ["<p>Cholas</p>", "<p>Delhi Sultanate</p>", 
                                "<p>Chalukyas</p>", "<p>Guptas</p>"],
                    options_hi: ["<p>चोल</p>", "<p>दिल्ली सल्तनत</p>",
                                "<p>चालुक्य</p>", "<p>गुप्त</p>"],
                    solution_en: "<p>1.(b) <strong>Delhi Sultanate. The Kakatiya dynasty </strong>(Telugu dynasty) ruled parts of what is now Andhra Pradesh and Telangana from the.The Kakatiya rule came to an end in 1323 A.D. when Warangal was conquered by Ghiyasuddin Tughlaq . <strong>Founder -</strong> Chola II and Rudra I. Prataparudra (Rudradeva II) - Last ruler of the Kakatiya dynasty.</p>",
                    solution_hi: "<p>1.(b) <strong>दिल्ली सल्तनत। काकतीय राजवंश</strong> (तेलुगु राजवंश) ने अब के आंध्र प्रदेश और तेलंगाना के कुछ हिस्सों पर शासन किया था। काकतीय शासन 1323 ई. में समाप्त हो गया जब वारंगल पर गयासुद्दीन तुगलक ने कब्जा कर लिया। <strong>संस्थापक </strong>- चोल द्वितीय और रुद्र प्रथम। प्रतापरुद्र (रुद्रदेव द्वितीय) - काकतीय वंश के अंतिम शासक।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. During which period did the Sultanate ruler, Ghiyas-ud-din Balban, take over and rule the throne of Delhi?</p>",
                    question_hi: "<p>2. सल्तनत शासक, गयासुद्दीन बलबन ने किस अवधि के दौरान दिल्ली के सिंहासन को संभाला और शासन किया?</p>",
                    options_en: ["<p>1290-1322</p>", "<p>1206-1223</p>", 
                                "<p>1321-1334</p>", "<p>1266-1287</p>"],
                    options_hi: ["<p>1290-1322</p>", "<p>1206-1223</p>",
                                "<p>1321-1334</p>", "<p>1266-1287</p>"],
                    solution_en: "<p>2.(d) <strong>1266-1287.</strong> Ghiyash ud din Balban (Ulug Khan) was a sultans of the \"Mamluk\" dynasty (1206 to 1290) .<strong> Famous Sultan of Mamluk dynasty</strong> <strong>(Slave dynasty) - </strong>Qutb ud-Din Aibak (founder, 1206-1210), Iltutmish (1210 -1236), Razia sultan (first female Muslim ruler from 1236 to 1240), Muiz-ud-din Muhammad Qaiqabad (last ruler, 1287-1290).</p>",
                    solution_hi: "<p>2.(d) <strong>1266-1287 ।</strong> गयासुद्दीन बलबन (उलुग खान) \"मामलुक\" राजवंश (1206 से 1290) का सुल्तान था । <strong>मामलुक वंश (गुलाम वंश) के प्रसिद्ध सुल्तान - </strong>कुतुबुद्दीन ऐबक (संस्थापक, 1206-1210), इल्तुतमिश (1210 -1236), रजिया सुल्तान (1236 - 1240 तक प्रथम महिला मुस्लिम शासिका), मुइज़-उद-दीन मुहम्मद क़ैकाबाद (अंतिम शासक, 1287-1290)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The famous Moroccan traveller Ibn Battuta came to India during which period?</p>",
                    question_hi: "<p>3. मोरक्को का प्रसिद्ध यात्री इब्न-बतूता किसके काल में भारत आया था ?</p>",
                    options_en: ["<p>Chandragupta Morya</p>", "<p>British</p>", 
                                "<p>Mughal</p>", "<p>Delhi Sultanate</p>"],
                    options_hi: ["<p>चंद्रगुप्त मौर्य</p>", "<p>ब्रिटिश</p>",
                                "<p>मुगल</p>", "<p>दिल्ली सल्तनत</p>"],
                    solution_en: "<p>3.(d) <strong>Delhi Sultanate.</strong> He came to India in 1334. <strong>Ibn Battuta </strong>arrived in India all the way through the mountains of Afghanistan during the time of the Tughlaq dynasty (Muḥammad bin Tughluq). <strong>Foreign Travellers who visited Ancient India -</strong> Hiuen Tsang (Harshavardhana), Megasthenes (Chandragupta), Vasco Da Gama (Zamorin), William Hawkins (Jahangeer).</p>",
                    solution_hi: "<p>3.(d) <strong>दिल्ली सल्तनत।</strong> यह 1334 में भारत आया था। <strong>इब्न बतूता</strong> तुगलक वंश (मुअम्मद बिन तुगलक) के समय में अफगानिस्तान के पहाड़ों से होते हुए भारत आया था। <strong>प्राचीन भारत </strong>का दौरा करने वाले <strong>विदेशी यात्री</strong> - ह्वेन त्सांग (हर्षवर्धन), मेगस्थनीज (चंद्रगुप्त), वास्को-डी-गामा (ज़मोरिन), विलियम हॉकिन्स (जहाँगीर)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. &lsquo;Amir-al- Khayl&rsquo; is an Arabic title that is usually translated &lsquo;Commander of the Faithful&rsquo; or &lsquo;Leader of the Faithful&rsquo;. Who among the following was given this designation?</p>",
                    question_hi: "<p>4. \'अमीर-अल-खयल\' एक अरबी उपाधि है जिसका अनुवाद आमतौर पर \'वफादार का कमांडर\' या \'वफादारो का नेता\' किया जाता है। निम्नलिखित में से किसे यह उपाधि दी गयी थी?</p>",
                    options_en: ["<p>Jamal-ud-Din Yaqut</p>", "<p>Malik Ikhtiar-ud-din Altunia</p>", 
                                "<p>Muiz ud din Bahram</p>", "<p>Naseeruddin Mohd</p>"],
                    options_hi: ["<p>जमाल-उद-दीन याकूत</p>", "<p>मलिक इख्तियार-उद-दीन अल्तुनिया</p>",
                                "<p>मुइज़-उद-दीन बहराम</p>", "<p>नसीरुद्दीन मुहम्मद</p>"],
                    solution_en: "<p>4.(a) <strong>Jamal-ud-Din Yaqut- </strong>He was an African Siddi slave-turned-nobleman who was a close confidant of Razia Sultana, the first and only female monarch of the Delhi Sultanate in India. <strong>Muiz ud-Din Bahram</strong> was the sixth sultan of the Mamluk Dynasty. <strong>Malik Ikhtiyar-ud-din Altunia</strong> was the governor of Bhatinda (Punjab) in India under the rule of the Delhi Sultanate under the Mamluk dynasty.</p>",
                    solution_hi: "<p>4.(a) <strong>जमाल-उद-दीन याकूत- </strong>वह एक अफ्रीकी सिद्दी गुलाम से कुलीन व्यक्ति बना था, जो भारत में दिल्ली सल्तनत की पहली और एकमात्र महिला सम्राट रजिया सुल्तान का करीबी विश्वासपात्र था। <strong>मुइज़-उद-दीन बहराम </strong>मामलुक राजवंश का छठा सुल्तान था। <strong>मलिक इख्तियार-उद-दीन अल्तुनिया</strong> मामलुक वंश के तहत दिल्ली सल्तनत के शासन के तहत भारत में भटिंडा (पंजाब) का गवर्नर था ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. The office of Diwan-i-Insha dealt with which affair in administration?</p>",
                    question_hi: "<p>5. प्रशासन में दीवान-ए-इंशा का कार्यालय किस मामले से संबंधित था?</p>",
                    options_en: ["<p>Military</p>", "<p>Religious affairs</p>", 
                                "<p>Finance</p>", "<p>State correspondence</p>"],
                    options_hi: ["<p>सैन्य</p>", "<p>धार्मिक मामले</p>",
                                "<p>वित्त</p>", "<p>राज्&zwj;य पत्राचार</p>"],
                    solution_en: "<p>5.(d) <strong>State correspondence. </strong>Delhi Sultanate Administration : Diwan -i-Wizarat (Department of Revenue and Finance), Diwan-i-Arz (Department of Military), Diwan-i-Risalat (Department of Religious affairs), Diwan-i-bandagan (Department of slaves), Diwan-i-Khairat (Charity department), Diwan-i-mustakhraj (to realise arrears), Diwan- i-kohi (Department of agriculture).</p>",
                    solution_hi: "<p>5.(d) <strong>राज्य पत्राचार।</strong> दिल्ली सल्तनत प्रशासन: दीवान-ए-विज़ारत (राजस्व और वित्त विभाग), दीवान-ए-अर्ज़ (सैन्य विभाग), दीवान-ए-रिसालत (धार्मिक मामलों का विभाग), दीवान-ए-बंदगान (दासों का विभाग) , दीवान-ए-खैरात (दान विभाग), दीवान-ए-मुस्तखराज (बकाया राशि वसूलने के लिए), दीवान-ए-कोही (कृषि विभाग)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Who was the Sultan of Delhi at the time of the establishment of Vijayanagara Empire?</p>",
                    question_hi: "<p>6. विजयनगर साम्राज्य की स्थापना के समय दिल्ली के सुल्तान कौन थे ?</p>",
                    options_en: ["<p>Nasiruddin Mahmud</p>", "<p>Iltutmish</p>", 
                                "<p>Mohammad bin Tughlaq</p>", "<p>Alauddin Khilji</p>"],
                    options_hi: ["<p>नासिरुद्दीन महमूद</p>", "<p>इल्तुतमिश</p>",
                                "<p>मुहम्मद-बिन-तुगलक</p>", "<p>अलाउद्दीन खिलजी</p>"],
                    solution_en: "<p>6.(c) <strong>Mohammad bin Tughlaq . </strong>Vijaynagar Empire: Established - 1336 by Harihara and Bukka. Capital - Hampi. The Empire stretched from Krishna river in the north to extreme south of the peninsula. Dynasties with first and last rulers - Sangama Dynasty (Harihara I, Rajasekhara), Saluva Dynasty (Saluva Narasimha, Sadasiva), Tuluva Dynasty (Rama, Venkata), Aravidu Dynasty (Tirumala Raya, Sriranga III).</p>",
                    solution_hi: "<p>6.(c) <strong>मोहम्मद बिन तुगलक।&nbsp;</strong>विजयनगर साम्राज्य की स्थापना हरिहर और बुक्का द्वारा 1336 में की गयी थी। राजधानी - हम्पी। साम्राज्य उत्तर में कृष्णा नदी से लेकर प्रायद्वीप के सुदूर दक्षिण तक फैला हुआ था। वंश एवं उनके प्रथम और अंतिम शासक - संगम वंश (हरिहर प्रथम, राजशेखर), सालुव वंश (तुलुवा नरसा नायक, सदाशिव), तुलुव राजवंश (राम, वेंकट), अराविडु वंश (तिरुमाला राय, श्रीरंगा III)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following systems of the Delhi sultanate had a influence on the Bahmani and Vijayanagar kingdoms?</p>",
                    question_hi: "<p>7. दिल्ली सल्तनत की निम्नलिखित में से किस प्रणाली/व्यवस्था का बहमनी और विजयनगर साम्राज्यों पर प्रभाव था?</p>",
                    options_en: ["<p>Walis</p>", "<p>Chahalgani</p>", 
                                "<p>Bitikchi</p>", "<p>Iqtadari</p>"],
                    options_hi: ["<p>वाली</p>", "<p>चहलगनी</p>",
                                "<p>बिटिकची</p>", "<p>इक्तादारी</p>"],
                    solution_en: "<p>7.(d) <strong>Iqtadari System:-</strong> Introduced by Shamsuddin IIltutmish (3rd Sultan of Delhi). Provision - Under this system, the land of the empire was divided into several large and small tracts called Iqta and assigned these Iqtas to his soldiers, officers and nobles.<strong> Administrative Terms used in Sultanate Period</strong>: Dabir (Secretary), Faujdar (Commander of army), Sipahsalar (Commander), Amil (Revenue officer), Diwan (Office: the central secretariat).</p>",
                    solution_hi: "<p>7.(d) <strong>इक्तादारी प्रणाली:-</strong> शम्सुद्दीन इल्तुतमिश (दिल्ली के तीसरे सुल्तान) द्वारा शुरू की गई। प्रावधान - इस व्यवस्था के तहत साम्राज्य की भूमि को कई छोटे-बड़े भूभागों में विभाजित किया जाता था जिन्हें इक्ता कहा जाता था और इन इक्तों को अपने सैनिकों, अधिकारियों और सरदारों को सौंप दिया जाता था। <strong>सल्तनत काल में प्रयुक्त प्रशासनिक शब्द:</strong> दबीर (सचिव), फौजदार (सेना के सेनापति), सिपहसलार (सेनापति), आमिल (राजस्व अधिकारी), दीवान (कार्यालय: केंद्रीय सचिवालय)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Who among the following was the head of the Diwan-i-Insha department under the Delhi sultanate?</p>",
                    question_hi: "<p>8. निम्नलिखित में से कौन दिल्ली सल्तनत के अधीन दीवान-ए-इंशा विभाग का प्रमुख था?</p>",
                    options_en: ["<p>Dabir-i-Khas</p>", "<p>Barid-i-Mumalik</p>", 
                                "<p>Amir-i-Dad</p>", "<p>Wakil-i-Dar</p>"],
                    options_hi: ["<p>दबीर-ए- खास</p>", "<p>बरीद-ए-मुमालिक</p>",
                                "<p>अमीर-ए-दाद</p>", "<p>वकील-ए-दर</p>"],
                    solution_en: "<p>8.(a) <strong>Dabir-i-Khas. </strong>Diwan -i-Bandagan - A slave department. Diwan-i-kohi - Agricultural department. Diwan-i-Mustakhraj - Arrears department. <strong>Barid-i-Mumalik -</strong> Head of the information and intelligence department. <strong>Amir-i-Dad -</strong> The law officer. <strong>Wakil-i-Dar -</strong> Controller of the royal household.</p>",
                    solution_hi: "<p>8.(a) <strong>दबीर-ए-खास।</strong> दीवान -ए-बंदगान - एक दास विभाग। दीवान -ए-कोही - कृषि विभाग। दीवान-ए-मुस्तखराज- बकाया विभाग। <strong>बरीद -ए-मुमालिक -</strong> सूचना एवं गुप्तचर विभाग का प्रमुख। <strong>अमीर-ए-दाद -</strong> कानून अधिकारी। <strong>वकील -ए-दार - </strong>शाही घराने का नियंत्रक।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. The Tomb of Razia Sultan in the state of Haryana is made of which building material?</p>",
                    question_hi: "<p>9. हरियाणा राज्य में रजिया सुल्तान का मकबरा किस निर्माण सामग्री से निर्मित है?</p>",
                    options_en: ["<p>Red sandstone</p>", "<p>Granite</p>", 
                                "<p>Marble</p>", "<p>Baked bricks</p>"],
                    options_hi: ["<p>लाल बलुआ पत्थर</p>", "<p>ग्रेनाइट</p>",
                                "<p>संगमरमर</p>", "<p>पकी हुई ईंटें</p>"],
                    solution_en: "<p>9.(d) <strong>Baked bricks.</strong> Raziya sultan (1236 - 1240): Her tomb is situated in Kaithal (Haryana). She is the only female ruler ever to rule in the Delhi sultanate. Monuments built by Red sandstone: Qutub Minar, Red Fort and Humayun\'s Tomb. Taj mahal (white marble) - Tomb of Mumtaz Mahal.</p>",
                    solution_hi: "<p>9.(d) <strong>पकी हुई ईंटें।</strong> रजिया सुल्तान (1236 - 1240): उनका मकबरा कैथल (हरियाणा) में स्थित है। वह दिल्ली सल्तनत पर शासन करने वाली एकमात्र महिला शासक हैं। लाल बलुआ पत्थर से निर्मित स्मारक: कुतुब मीनार, लाल किला और हुमायूँ का मकबरा। ताज महल (सफ़ेद संगमरमर) - मुमताज महल का मकबरा।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. During the Sultanate of Delhi, the term \"Ulema\" was associated with ______.</p>",
                    question_hi: "<p>10. दिल्ली सल्तनत के दौरान, \"उलेमा (Ulema) \" शब्द का संबंध से था |</p>",
                    options_en: ["<p>Learned theologians and Jurists</p>", "<p>Commander-in-chief</p>", 
                                "<p>Postal officer</p>", "<p>Village Revenue officer</p>"],
                    options_hi: ["<p>विद्वान धर्मशास्त्री और न्याय-शास्त्री</p>", "<p>प्रमुख कमांडर</p>",
                                "<p>डाक अधिकारी</p>", "<p>ग्राम राजस्व अधिकारी</p>"],
                    solution_en: "<p>10.(a) <strong>Learned theologians and Jurists. </strong>Officers in Delhi Sultanate - State Level: Amil (Revenue Officer), Barid (Intelligence agents), Khwaja (Accounts Officer), Kotwal (Head of city law and order), Muhatasib (Head of municipality law and order), Muftis (Expounder of law).</p>",
                    solution_hi: "<p>10.(a) <strong>विद्वान धर्मशास्त्री और न्याय-शास्त्री ।</strong> दिल्ली सल्तनत में अधिकारी - राज्य स्तर: आमिल (राजस्व अधिकारी), बारिद (खुफिया अधिकारी), ख्वाजा (लेखा अधिकारी), कोतवाल (शहर कानून और व्यवस्था का प्रमुख), मुहतसिब (नगरपालिका कानून व्यवस्था के प्रमुख), मुफ्ती (कानून का प्रतिपादक)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Who among the following was the daughter of Iltumish, a ruler of Slave Mamluk dynasty?</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन गुलाम मामलुक वंश के शासक इल्तुतमिश की बेटी थी?</p>",
                    options_en: ["<p>Kubra Noorzai</p>", "<p>Harkha</p>", 
                                "<p>Razia Sultan</p>", "<p>Shafiqa Ziayee</p>"],
                    options_hi: ["<p>कुबरा नूरजई</p>", "<p>हरखा</p>",
                                "<p>रजिया सुल्तान</p>", "<p>शफीका ज़ियायी</p>"],
                    solution_en: "<p>11.(c) <strong>Razia Sultan</strong>. In 1236, Razia Sultan (Mamluk dynasty), succeeded her father to the Sultanate of Delhi. She sent an expedition against Ranthambore to control the Rajputs.<strong> Mamluk dynasty</strong> (1206 to 1290) - Founded by Qutbuddin Aibak (Lakh Baksh). He was succeeded by Iltutmish who was his son-in-law.</p>",
                    solution_hi: "<p>11.(c) <strong>रजिया सुल्तान ।</strong> 1236 में, रज़िया सुल्तान (मामलुक वंश), अपने पिता के बाद दिल्ली की सल्तनत पर आसीन हुई। इसने राजपूतों को नियंत्रित करने के लिए रणथंभौर के खिलाफ एक अभियान भेजा। <strong>मामलुक वंश</strong> (1206 से 1290) - कुतुबुद्दीन ऐबक (लाख बख्श) द्वारा स्थापित। उसका उत्तराधिकारी इल्तुतमिश हुआ जो उसका दामाद था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Under the Delhi Sultanate, a religious tax ________ was imposed on the wealthy and rich Muslims in India.</p>",
                    question_hi: "<p>12. दिल्ली सल्तनत के अधीन, भारत में धनवान मुसलमानों पर एक धार्मिक कर ________ लगाया गया था।</p>",
                    options_en: ["<p>Firuz</p>", "<p>Mamluk</p>", 
                                "<p>Zakat</p>", "<p>Kismat</p>"],
                    options_hi: ["<p>फिरूज़ (Firuz)</p>", "<p>मामलुक (Mamluk)</p>",
                                "<p>ज़कात (Zakat)</p>", "<p>किस्मत (Kismat)</p>"],
                    solution_en: "<p>12.(c) <strong>Zakat</strong>. Zakat tax was abolished by Sikander lodi. Firoz Shah Tughlaq introduced four types of taxes based on the Quran. They were Kharaj (Land tax), Khams (Tax on booty captured in war), Jizya (Imposed on non muslim), and Zakat.</p>",
                    solution_hi: "<p>12.(c) <strong>ज़कात </strong>(Zakat)। ज़कात कर को सिकंदर लोदी ने समाप्त कर दिया था। फ़िरोज़ शाह तुगलक ने कुरान के आधार पर चार प्रकार के कर लागू किये। वे थे खराज (भूमि कर), खुम्स (युद्ध में लूट का माल), जजिया (गैर मुस्लिमों पर लगाया जाने वाला कर) और ज़कात।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. In which year was Razia removed from the throne of the Delhi Sultanate?</p>",
                    question_hi: "<p>13. रज़िया को दिल्ली सल्तनत के सिंहासन से किस वर्ष हटाया गया था?</p>",
                    options_en: ["<p>1244</p>", "<p>1240</p>", 
                                "<p>1238</p>", "<p>1236</p>"],
                    options_hi: ["<p>1244</p>", "<p>1240</p>",
                                "<p>1238</p>", "<p>1236</p>"],
                    solution_en: "<p>13.(b) <strong>1240. Razia Sultana </strong>(1236 -1240) married Malik Ikhtiar-ud-Altunia (the governor of Bhatinda under the rule of the Delhi Sultanate under Mamluk dynasty). She was killed on 15 October 1240 at Kaithal (Haryana). <strong>Tomb </strong>- Kaithal (Haryana).</p>",
                    solution_hi: "<p>13.(b)<strong> 1240 । रज़िया सुल्तान</strong> (1236 - 1240) ने मलिक इख्तियार-उद-अल्तुनिया (ममलुक राजवंश के तहत दिल्ली सल्तनत के शासन में भटिंडा के राज्यपाल) से शादी की। 15 अक्टूबर 1240 को कैथल (हरियाणा) में रज़िया की हत्या कर दी गई। <strong>मकबरा</strong> - कैथल (हरियाणा)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Who composed Tabaqat-i-Nasiri in the Delhi Sultanate period?</p>",
                    question_hi: "<p>14. दिल्ली सल्तनत काल में तबाकत-ए -नसिरी की रचना किसने की थी?</p>",
                    options_en: ["<p>Hasan Nizami</p>", "<p>Amir Khusrau</p>", 
                                "<p>Minhaj-us-Siraj</p>", "<p>Ziauddin Barani</p>"],
                    options_hi: ["<p>हसन निजामी</p>", "<p>अमीर खुसरो</p>",
                                "<p>मिन्हाज-ए-सिराज</p>", "<p>जियाउद्दीन बरानी</p>"],
                    solution_en: "<p>14.(c) \"Tabaqat-i-Nasiri\'\' (1260) was written by <strong>Minhaj-i-Siraj</strong> in Persian language. Hasan Nizami (Persian language poet) wrote <strong>Tajul-Maasir</strong> (the first official history of the Delhi Sultanate). Sufi poet<strong> Amir Khusro </strong>was the court poet of Alauddin Khilji. His famous works: Tughluq Nama, Wast-ul-Hayat, Nuh Sipihr, Ashiqa, Khamsa, Baqia-Naqia. Ziauddin Barani (Muslim political thinker of Delhi Sultanate) wrote Tarikh-i-Firozshahi and Fatwa-i-Jahandari.</p>",
                    solution_hi: "<p>14.(c) &lsquo;&rsquo;तबकात-ए-नासिरी (1260)&rsquo; फ़ारसी में <strong>मिन्हाज-ए-सिराज</strong> द्वारा लिखी गयी थी । हसन निजामी (फारसी भाषा के कवि) ने <strong>ताजुल-मासीर</strong> (दिल्ली सल्तनत का पहला आधिकारिक इतिहास) लिखा था। सूफी कवि <strong>अमीर खुसरो</strong> अलाउद्दीन खिलजी के दरबारी कवि थे । उनकी प्रसिद्ध कृतियां:- तुगलक नामा, वास्त-उल-हयात, नूंह सिपिहर, आशिका, खम्सा, बाकिया-नकिया। जियाउद्दीन बरनी (दिल्ली सल्तनत के मुस्लिम राजनीतिक विचारक) ने तारीख-ए-फिरोजशाही और फतवा-ए-जहांदारी लिखा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Zafar khan was a famous General of which of the following rulers of the Delhi Sultanate?</p>",
                    question_hi: "<p>15. जफर खाँ दिल्ली सल्तनत के निम्नलिखित में से किस शासक का प्रसिद्ध सेनापति था?</p>",
                    options_en: ["<p>Alauddin Khilji</p>", "<p>Iltutmish<strong id=\"docs-internal-guid-0e269f98-7fff-9408-e1d2-95110972c092\"><br></strong></p>", 
                                "<p>Muhammad bin Tughluq</p>", "<p>Balban</p>"],
                    options_hi: ["<p>अलाउद्दीन खिलज़ी</p>", "<p>इल्तुतमिश<strong id=\"docs-internal-guid-089c74cc-7fff-1858-a18f-eb6603a6c9fc\"><br></strong></p>",
                                "<p>&nbsp;मुहम्मद बिन तुगलक</p>", "<p>बलबन</p>"],
                    solution_en: "<p>15.(a) <strong>Alauddin Khilji&rsquo;s Generals :</strong> Hizabruddin (Zafar Khan), Almas Beg (Ulugh Khan), Malik Kafur (Taj al-Din Izz al-Dawla), Nusrat Khan (Malik Nusrat Jalesari). <strong>Muhammad bin Tughlaq</strong> (Jauna Khan): Introduced token currency.</p>",
                    solution_hi: "<p>15.(a) <strong>अलाउद्दीन खिलजी के सेनापति:</strong> हिजाबुद्दीन (जफर खान), अल्मास बेग (उलुग खान), मलिक काफूर (ताज अल-दीन इज़ अल-दावला), नुसरत खान (मलिक नुसरत जलेसरी)। <strong>मुहम्मद बिन तुगलक</strong> (जौना खान): सांकेतिक मुद्रा का प्रचलन किया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>