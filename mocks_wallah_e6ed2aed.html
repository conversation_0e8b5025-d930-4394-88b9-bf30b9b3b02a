<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the most appropriate ANTONYM of the given word.<br>Astute</p>",
                    question_hi: "<p>1. Select the most appropriate ANTONYM of the given word.<br>Astute</p>",
                    options_en: ["<p>Stupid</p>", "<p>Temporary</p>", 
                                "<p>Illegal</p>", "<p>Dispute</p>"],
                    options_hi: ["<p>Stupid</p>", "<p>Temporary</p>",
                                "<p>Illegal</p>", "<p>Dispute</p>"],
                    solution_en: "<p>1.(a) <strong>Stupid</strong>- lacking intelligence or common sense.<br><strong>Astute</strong>- having the ability to accurately assess situations or people.<br><strong>Temporary</strong>- lasting for a limited period; not permanent.<br><strong>Illegal</strong>- contrary to or forbidden by law.<br><strong>Dispute</strong>- a disagreement or argument about something.</p>",
                    solution_hi: "<p>1.(a) <strong>Stupid </strong>(बेवकूफ़)- lacking intelligence or common sense.<br><strong>Astute </strong>(चतुर)- having the ability to accurately assess situations or people.<br><strong>Temporary </strong>(अस्थायी)- lasting for a limited period; not permanent.<br><strong>Illegal </strong>(अवैध)- contrary to or forbidden by law.<br><strong>Dispute </strong>(विवाद)- a disagreement or argument about something.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Sentences of a paragraph are given below in jumbled order. Select the option that arranges the sentences in the correct logical sequence to form a meaningful and coherent paragraph. <br>a. This figure is predicted to climb as the percentage of Americans residing in rural areas falls by an average of 1.6% annually. <br>b. The United Nations reports that India\'s urban population is growing at a rate of 1.1 percent annually, while the country\'s rural population is shrinking at a rate of 0.37 percent annually.<br>c. While 82% of Americans live in urban settings, only 30% of Indians do so. <br>d. It\'s still a considerably smaller percentage of the Indian population than it is in the US that resides in urban areas,</p>",
                    question_hi: "<p>2. Sentences of a paragraph are given below in jumbled order. Select the option that arranges the sentences in the correct logical sequence to form a meaningful and coherent paragraph. <br>a. This figure is predicted to climb as the percentage of Americans residing in rural areas falls by an average of 1.6% annually. <br>b. The United Nations reports that India\'s urban population is growing at a rate of 1.1 percent annually, while the country\'s rural population is shrinking at a rate of 0.37 percent annually.<br>c. While 82% of Americans live in urban settings, only 30% of Indians do so. <br>d. It\'s still a considerably smaller percentage of the Indian population than it is in the US that resides in urban areas,</p>",
                    options_en: ["<p>d, b, a, c</p>", "<p>a, c, b, d</p>", 
                                "<p>b, d, c, a</p>", "<p>b, d, a, c</p>"],
                    options_hi: ["<p>d, b, a, c</p>", "<p>a, c, b, d</p>",
                                "<p>b, d, c, a</p>", "<p>b, d, a, c</p>"],
                    solution_en: "<p>2.(c) b, d, c, a<br>Sentence &lsquo;b&rsquo; will be the starting line as it introduces the main idea of the parajumble i.e. &ldquo;growth rate of India&rsquo;s population as reported by the United Nations&rdquo;. And, Sentence &lsquo;d&rsquo; states that the percentage of Indian population in urban areas is still smaller than it is in the US. So, &lsquo;d&rsquo; will follow &lsquo;b&rsquo;. Further, Sentence &lsquo;c&rsquo; states the percentage of urban population in America and India &amp; Sentence &lsquo;a&rsquo; states that this figure is expected to increase as the percentage of Americans in rural areas falls by an average of 1.6% annually. So, &lsquo;a&rsquo; will follow &lsquo;c&rsquo;. Going through the options, option (c) has the correct sequence.</p>",
                    solution_hi: "<p>2.(c) b, d, c, a<br>Sentence &lsquo;b&rsquo; प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार अर्थात् &ldquo;growth rate of India&rsquo;s population as reported by the United Nations&rdquo; का परिचय देता है। और, Sentence &lsquo;d&rsquo; बताता है कि शहरी क्षेत्रों में India की आबादी का percentage अभी भी US की तुलना में कम है। इसलिए, &lsquo;b&rsquo; के बाद &lsquo;d&rsquo; आएगा। इसके अलावा, Sentence &lsquo;c&rsquo; America और India में शहरी आबादी(urban population) का percentage बताता है और Sentence &lsquo;a&rsquo; बताता है कि यह आंकड़ा बढ़ने की उम्मीद है क्योंकि ग्रामीण क्षेत्रों (rural areas) में Americans का प्रतिशत प्रत्येक वर्ष औसतन 1.6% कम हो रहा है। इसलिए, &lsquo;c&rsquo; के बाद &lsquo;a&rsquo; आएगा। अतः options के माध्यम से जाने पर option (c) में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />(A) to become severely seasick<br />(B) a world tour, but he was<br />(C) a nervous flyer and tended<br />(D) he wanted to go on",
                    question_hi: "3. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />(A) to become severely seasick<br />(B) a world tour, but he was<br />(C) a nervous flyer and tended<br />(D) he wanted to go on",
                    options_en: [" BADC ", " ADBC ", 
                                " CADB ", " DBCA"],
                    options_hi: [" BADC ", " ADBC ",
                                " CADB ", " DBCA"],
                    solution_en: "3.(d) DBCA<br />The given sentence starts with Part D as it introduces the main idea of the sentence, i.e. ‘He wanted to go on.’ Part D will be followed by Part B as it tells us that he wanted to go on a tour. Further, Part C states that he was a nervous flyer & Part A states another problem that he tended to become overly seasick. So, A will follow C. Going through the options, option ‘d’ has the correct sequence.",
                    solution_hi: "3.(d) DBCA<br />दिया गया sentence, Part D से प्रारंभ होगा क्योंकि यह sentence के मुख्य विचार ‘He wanted to go on’ का परिचय देता है। Part D के बाद Part B आएगा क्योंकि यह हमें बताता है कि वह एक tour पर जाना चाहता था। इसके अलावा, Part C बताता है कि वह एक nervous flyer था और Part A एक अन्य समस्या बताता है कि वह अक्सर समुद्र रोग (seasick) से ग्रस्त हो जाता था। इसलिए, C के बाद A आएगा। अतः options के माध्यम से जाने पर option ‘d’  में सही sequence है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the option that can be used as a one-word substitute for the given group of words. <br>Study of flying airplanes</p>",
                    question_hi: "<p>4. Select the option that can be used as a one-word substitute for the given group of words. <br>Study of flying airplanes</p>",
                    options_en: ["<p>Aviation</p>", "<p>Alchemy</p>", 
                                "<p>Astronomy</p>", "<p>Acoustics</p>"],
                    options_hi: ["<p>Aviation</p>", "<p>Alchemy</p>",
                                "<p>Astronomy</p>", "<p>Acoustics</p>"],
                    solution_en: "<p>4.(a) <strong>Aviation</strong>- study of flying airplanes.<br><strong>Alchemy</strong>- a seemingly magical process of transformation, creation, or combination.<br><strong>Astronomy</strong>- the branch of science that deals with celestial objects, space, and the physical universe as a whole.<br><strong>Acoustics</strong>- the branch of physics concerned with the properties of sound.</p>",
                    solution_hi: "<p>4.(a) <strong>Aviation </strong>(उड्डयन विज्ञान)- study of flying airplanes.<br><strong>Alchemy </strong>(रसायन विद्या)- a seemingly magical process of transformation, creation, or combination.<br><strong>Astronomy </strong>(खगोलशास्त्र)- the branch of science that deals with celestial objects, space, and the physical universe as a whole.<br><strong>Acoustics </strong>(ध्वनि-विज्ञान)- the branch of physics concerned with the properties of sound.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the option that expresses the given sentence in passive voice. <br>A famous company conducted the workshop for its customers.</p>",
                    question_hi: "<p>5. Select the option that expresses the given sentence in passive voice. <br>A famous company conducted the workshop for its customers.</p>",
                    options_en: ["<p>The workshop were conducted by a famous company for its customers.</p>", "<p>The workshop had been conducted by a famous company for its customers.</p>", 
                                "<p>The workshop was conducted by a famous company for its customers.</p>", "<p>The workshop is conducted by a famous company for its customers.</p>"],
                    options_hi: ["<p>The workshop were conducted by a famous company for its customers.</p>", "<p>The workshop had been conducted by a famous company for its customers.</p>",
                                "<p>The workshop was conducted by a famous company for its customers.</p>", "<p>The workshop is conducted by a famous company for its customers.</p>"],
                    solution_en: "<p>5.(c) The workshop was conducted by a famous company for its customers. (Correct)<br>(a) The workshop <span style=\"text-decoration: underline;\">were</span> conducted by a famous company for its customers. (Incorrect Helping Verb)<br>(b) The workshop <span style=\"text-decoration: underline;\">had been conducted</span> by a famous company for its customers. (Incorrect Tense)<br>(d) The workshop <span style=\"text-decoration: underline;\">is conducted</span> by a famous company for its customers. (Incorrect Tense)</p>",
                    solution_hi: "<p>5.(c) The workshop was conducted by a famous company for its customers. (Correct)<br>(a) The workshop <span style=\"text-decoration: underline;\">were</span> conducted by a famous company for its customers. (गलत Helping Verb)<br>(b) The workshop <span style=\"text-decoration: underline;\">had been conducted</span> by a famous company for its customers. (गलत Tense)<br>(d) The workshop <span style=\"text-decoration: underline;\">is conducted</span> by a famous company for its customers. (गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the option that expresses the given sentence in active voice. <br>How have they been ordered by the minister ?</p>",
                    question_hi: "<p>6. Select the option that expresses the given sentence in active voice. <br>How have they been ordered by the minister ?</p>",
                    options_en: ["<p>How have the minister ordered them?</p>", "<p>How had the minister ordered them?</p>", 
                                "<p>How did the minister order them?</p>", "<p>How has the minister ordered them?</p>"],
                    options_hi: ["<p>How have the minister ordered them?</p>", "<p>How had the minister ordered them?</p>",
                                "<p>How did the minister order them?</p>", "<p>How has the minister ordered them?</p>"],
                    solution_en: "<p>6.(d) How has the minister ordered them? (Correct)<br>(a) How <span style=\"text-decoration: underline;\">have</span> the minister ordered them? (Incorrect Helping Verb)<br>(b) How <span style=\"text-decoration: underline;\">had</span> the minister ordered them? (Incorrect Tense)<br>(c) How <span style=\"text-decoration: underline;\">did</span> the minister order them? (Incorrect Tense)</p>",
                    solution_hi: "<p>6.(d) How has the minister ordered them? (Correct)<br>(a) How <span style=\"text-decoration: underline;\">have</span> the minister ordered them? (गलत Helping Verb)<br>(b) How <span style=\"text-decoration: underline;\">had</span> the minister ordered them? (गलत Tense)<br>(c) How <span style=\"text-decoration: underline;\">did </span>the minister order them? (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate option to substitute the underlined part of the following sentence. <br>The playwright has faced <span style=\"text-decoration: underline;\">many ups and downs</span> in his literary career, which spanned for almost 30 years.</p>",
                    question_hi: "<p>7. Select the most appropriate option to substitute the underlined part of the following sentence. <br>The playwright has faced <span style=\"text-decoration: underline;\">many ups and downs</span> in his literary career, which spanned for almost 30 years.</p>",
                    options_en: ["<p>much truth and falsehood</p>", "<p>many agreements and disagreements</p>", 
                                "<p>much light and darkness</p>", "<p>many good and bad times</p>"],
                    options_hi: ["<p>much truth and falsehood</p>", "<p>many agreements and disagreements</p>",
                                "<p>much light and darkness</p>", "<p>many good and bad times</p>"],
                    solution_en: "<p>7.(d) <strong>Many ups and downs -</strong> many good and bad times.</p>",
                    solution_hi: "<p>7.(d) <strong>Many ups and downs - </strong>many good and bad times./कई अच्छे और बुरे दौर।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate option that can substitute the underlined segment in the given sentence. If no substitution is required, select \'No substitution\'.<br>The chief guest concluded his speech as <span style=\"text-decoration: underline;\">he requested</span> to stop by the organisers.</p>",
                    question_hi: "<p>8. Select the most appropriate option that can substitute the underlined segment in the given sentence. If no substitution is required, select \'No substitution\'.<br>The chief guest concluded his speech as <span style=\"text-decoration: underline;\">he requested</span> to stop by the organisers.</p>",
                    options_en: ["<p>no substitution</p>", "<p>he was requested</p>", 
                                "<p>he is requested</p>", "<p>he will be requested</p>"],
                    options_hi: ["<p>no substitution</p>", "<p>he was requested</p>",
                                "<p>he is requested</p>", "<p>he will be requested</p>"],
                    solution_en: "<p>8.(b) he was requested<br>The second clause of the sentence is in the passive voice as the doer of the action is given using the preposition &lsquo;by&rsquo;. &lsquo;Was + V<sub>3</sub>&rsquo; is the correct structure for the passive voice of simple past tense. Hence, &lsquo;he was requested&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(b) he was requested<br>Sentence का second clause, passive voice में है क्योंकि action का doer, \'preposition &lsquo;by&rsquo; का प्रयोग करके दिया गया है। \'Singular Sub + was + V<sub>3</sub>\', simple past tense के passive voice का सही structure है। अतः, &lsquo;he was requested&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "9. The following sentence has been split into four segments. Identify the segment that contains an error. He was / bad injured / in the / last match.",
                    question_hi: "9. The following sentence has been split into four segments. Identify the segment that contains an error. He was / bad injured / in the / last match.",
                    options_en: [" last match. ", " in the  ", 
                                " bad injured ", " He was"],
                    options_hi: [" last match. ", " in the  ",
                                " bad injured ", " He was"],
                    solution_en: "9.(c) bad injured<br />The given sentence needs an adverb ‘badly’ to modify the adjective ‘injured’, not the adjective ‘bad’. Hence, ‘badly injured’ is the most appropriate answer.",
                    solution_hi: "9.(c) bad injured<br />दिए गए sentence में adjective ‘injured’ को modify करने के लिए adjective ‘bad’ की नहीं बल्कि adverb ‘badly’ की आवश्यकता है। अतः, ‘badly injured’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate synonym of the word in brackets to fill in the blank. <br>She______requested him to pay the bills. (persistently)</p>",
                    question_hi: "<p>10. Select the most appropriate synonym of the word in brackets to fill in the blank. <br>She ______requested him to pay the bills. (persistently)</p>",
                    options_en: ["<p>impetuously</p>", "<p>obliviously</p>", 
                                "<p>tremendously</p>", "<p>continuously</p>"],
                    options_hi: ["<p>impetuously</p>", "<p>obliviously</p>",
                                "<p>tremendously</p>", "<p>continuously</p>"],
                    solution_en: "<p>10.(d) <strong>Continuously</strong>- without interruption or pause.<br><strong>Persistently</strong>- in a continuous or determined manner, despite difficulty.<br><strong>Impetuously</strong>- acting quickly without thought or care.<br><strong>Obliviously</strong>- unaware or not concerned about what is happening around.<br><strong>Tremendously</strong>- to a great or extreme degree.</p>",
                    solution_hi: "<p>10.(d) <strong>Continuously </strong>(निरंतर)- without interruption or pause.<br><strong>Persistently </strong>(लगातार)- in a continuous or determined manner, despite difficulty.<br><strong>Impetuously </strong>(उतावलेपन से)- acting quickly without thought or care.<br><strong>Obliviously </strong>(अनजाने में)- unaware or not concerned about what is happening around.<br><strong>Tremendously </strong>(अत्यधिक)- to a great or extreme degree.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate ANTONYM of the given word.<br>Coy</p>",
                    question_hi: "<p>11. Select the most appropriate ANTONYM of the given word.<br>Coy</p>",
                    options_en: ["<p>Shy</p>", "<p>Uptight</p>", 
                                "<p>Recent</p>", "<p>Extroverted</p>"],
                    options_hi: ["<p>Shy</p>", "<p>Uptight</p>",
                                "<p>Recent</p>", "<p>Extroverted</p>"],
                    solution_en: "<p>11.(d) <strong>Extroverted</strong>- outgoing and socially confident.<br><strong>Coy</strong>- pretending to be shy or modest, often to attract attention.<br><strong>Shy</strong>- being reserved or nervous in the company of others.<br><strong>Uptight</strong>- anxious or overly concerned.<br><strong>Recent</strong>- having happened or appeared not long ago.</p>",
                    solution_hi: "<p>11.(d) <strong>Extroverted </strong>(बहिर्मुखी)- outgoing and socially confident.<br><strong>Coy </strong>(शर्मीला)- pretending to be shy or modest, often to attract attention.<br><strong>Shy </strong>(संकोची)- being reserved or nervous in the company of others.<br><strong>Uptight </strong>(तनावग्रस्त)- anxious or overly concerned.<br><strong>Recent </strong>(हालिया)- having happened or appeared not long ago.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Complete the sentence with the most appropriate set of ANTONYMS of the highlighted word.<br>\'Grisly\'.<br>The other day Jimmy behaved ____with Jammy and that was quite ____for me.</p>",
                    question_hi: "<p>12. Complete the sentence with the most appropriate set of ANTONYMS of the highlighted word.<br>\'Grisly\'.<br>The other day Jimmy behaved ____with Jammy and that was quite ____for me.</p>",
                    options_en: ["<p>rudely, attractive</p>", "<p>disgusting, atrocious</p>", 
                                "<p>ghastly, shameless</p>", "<p>pleasantly, comforting</p>"],
                    options_hi: ["<p>rudely, attractive</p>", "<p>disgusting, atrocious</p>",
                                "<p>ghastly, shameless</p>", "<p>pleasantly, comforting</p>"],
                    solution_en: "<p>12.(d) <strong>Pleasantly</strong>- in an enjoyable or agreeable manner.<br><strong>Comforting</strong>- making you feel calmer and less worried.<br><strong>Grisly</strong>- extremely unpleasant or disgusting.</p>",
                    solution_hi: "<p>12.(d) <strong>Pleasantly </strong>(सुखदपूर्ण)- in an enjoyable or agreeable manner.<br><strong>Comforting </strong>(आरामदायक)- making you feel calmer and less worried.<br><strong>Grisly </strong>(भयावह)- extremely unpleasant or disgusting.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate ANTONYM of the given word.<br>Customary</p>",
                    question_hi: "<p>13. Select the most appropriate ANTONYM of the given word.<br>Customary</p>",
                    options_en: ["<p>Fashion</p>", "<p>Protection</p>", 
                                "<p>Habitual</p>", "<p>Unusual</p>"],
                    options_hi: ["<p>Fashion</p>", "<p>Protection</p>",
                                "<p>Habitual</p>", "<p>Unusual</p>"],
                    solution_en: "<p>13.(d) <strong>Unusual</strong>- not common, rare, or unexpected.<br><strong>Customary</strong>- according to the usual practices or traditions.<br><strong>Fashion</strong>- a popular trend or style in clothing or behavior.<br><strong>Protection</strong>- the act of keeping someone or something safe from harm.<br><strong>Habitual</strong>- done regularly or repeatedly as a habit.</p>",
                    solution_hi: "<p>13.(d) <strong>Unusual </strong>(असामान्य)- not common, rare, or unexpected.<br><strong>Customary </strong>(परंपरागत)- according to the usual practices or traditions.<br><strong>Fashion </strong>(लोकप्रिय वेश भूषा)- a popular trend or style in clothing or behavior.<br><strong>Protection </strong>(संरक्षण)- the act of keeping someone or something safe from harm.<br><strong>Habitual </strong>(आदतन)- done regularly or repeatedly as a habit.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "14. Select the most appropriate homophone to fill in the blank. <br />The ______ of the flowers spreads everywhere when they bloom.",
                    question_hi: "14. Select the most appropriate homophone to fill in the blank. <br />The ______ of the flowers spreads everywhere when they bloom.",
                    options_en: [" sent ", " saint ", 
                                " scent ", " cent"],
                    options_hi: [" sent ", " saint ",
                                " scent ", " cent"],
                    solution_en: "14.(c) scent<br />‘Scent’ means a distinctive smell, especially one that is pleasant. The given sentence states that the scent of the flowers spreads everywhere when they bloom. Hence, \'scent\' is the most appropriate answer.",
                    solution_hi: "14.(c) scent<br />‘Scent’ का अर्थ है एक विशिष्ट गंध, विशेष रूप से जो सुखद हो। दिए गए sentence में कहा गया है कि जब फूल खिलते हैं तो उनकी खुशबू (scent) हर जगह फैल जाती है। अतः, \'scent\' सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "15. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.<br />My sister and I / work very hard / nevertheless my brother does not do / any task assigned to him.",
                    question_hi: "15. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.<br />My sister and I / work very hard / nevertheless my brother does not do / any task assigned to him.",
                    options_en: [" nevertheless my brother does not do ", " work very hard ", 
                                " any task assigned to him. ", " My sister and I"],
                    options_hi: [" nevertheless my brother does not do ", " work very hard ",
                                " any task assigned to him. ", " My sister and I"],
                    solution_en: "15.(a) nevertheless my brother does not do<br />‘Nevertheless’ must be replaced with ‘but’ as we generally use ‘but’ when two statements differ from or contrast with each other. There is a contrast between working hard and not doing any task. Hence, ‘but my brother does not do’ is the most appropriate answer.",
                    solution_hi: "15.(a) nevertheless my brother does not do<br />‘Nevertheless’  के स्थान पर  ‘but’ का प्रयोग होगा क्योंकि हम generally ‘but’ का प्रयोग तब करते हैं जब दो statements एक दूसरे से भिन्न या विरोधाभाषी होते हैं। ‘Working very hard’ और ‘not doing any task’ के बीच एक विषमता (contrast) है। अतः, ‘but my brother does not do’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the correct meaning of the underlined idiom in the following sentence.<br>Lucy\'s conceited conduct with her colleagues has left her <span style=\"text-decoration: underline;\">high and dry.</span></p>",
                    question_hi: "<p>16. Select the correct meaning of the underlined idiom in the following sentence.<br>Lucy\'s conceited conduct with her colleagues has left her <span style=\"text-decoration: underline;\">high and dry</span>.</p>",
                    options_en: ["<p>Helpless</p>", "<p>Superseding</p>", 
                                "<p>Impoverished</p>", "<p>Surrounded by people</p>"],
                    options_hi: ["<p>Helpless</p>", "<p>Superseding</p>",
                                "<p>Impoverished</p>", "<p>Surrounded by people</p>"],
                    solution_en: "<p>16.(a) <strong>High and dry- </strong>helpless.</p>",
                    solution_hi: "<p>16.(a) <strong>High and dry -</strong> helpless./असहाय।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "17. Select the correct spelling of the word that means \'to replace\'.",
                    question_hi: "17. Select the correct spelling of the word that means \'to replace\'.",
                    options_en: [" Superceed ", " Supersed ", 
                                " Superseed", " Supersede"],
                    options_hi: [" Superceed ", " Supersed ",
                                " Superseed", " Supersede"],
                    solution_en: "17.(d) Supersede<br />\'Supersede\' is the correct spelling.",
                    solution_hi: "17.(d) Supersede<br />\'Supersede\' सही spelling है। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "18. Select the word segment that can substitute the bracketed word segment correctly and complete the sentence meaningfully. <br />We will have to (put the meeting away) as the Director is not well.",
                    question_hi: "18. Select the word segment that can substitute the bracketed word segment correctly and complete the sentence meaningfully. <br />We will have to (put the meeting away) as the Director is not well.",
                    options_en: [" put the meeting off", " put the meeting up", 
                                " put the meeting on", " put the meeting again"],
                    options_hi: [" put the meeting off", " put the meeting up",
                                " put the meeting on", " put the meeting again"],
                    solution_en: "18.(a) put the meeting off<br />The phrasal verb ‘put off’ means to postpone or delay a meeting. The given sentence states that we will have to put the meeting off as the Director is not well. Hence, ‘put the meeting off’ is the most appropriate answer.",
                    solution_hi: "18.(a) put the meeting off<br /> Phrasal verb ‘put off’ का अर्थ है किसी बैठक को स्थगित करना या विलंबित करना। दिए गए sentence में बताया गया है कि हमें बैठक (meeting) स्थगित करनी होगी क्योंकि Director की तबीयत ठीक नहीं है। अतः, ‘put the meeting off’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "19. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />(a) one of the primary obstacles encountered<br />(b) is the risk of sample contamination<br />(c) during the analysis of controlled dangerous substance (CDS)<br />(d) from improper handling and collecting methodologies <br />(e) proof bloodstain patterns and DNA evidence",
                    question_hi: "19. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />(a) one of the primary obstacles encountered<br />(b) is the risk of sample contamination<br />(c) during the analysis of controlled dangerous substance (CDS)<br />(d) from improper handling and collecting methodologies <br />(e) proof bloodstain patterns and DNA evidence",
                    options_en: [" eacdb ", " acebd ", 
                                " aecdb ", " abcde"],
                    options_hi: [" eacdb ", " acebd ",
                                " aecdb ", " abcde"],
                    solution_en: "19.(b) acebd<br />The given sentence starts with Part ‘a’ as it introduces the main idea of the sentence, i.e. ‘one of the primary reasons’. Part ‘c’ tells us the process in which obstacles are encountered & Part ‘e’ continues to tell us other processes. So, ‘c’ will follow ‘e’. Further, Part ‘b’ states that there is a risk of sample contamination & Part ‘d’ tells us the reason for contamination. So, ‘d’ will follow ‘b’. Going through the options, option ‘b’ has the correct sequence.",
                    solution_hi: "19.(b) acebd<br />दिया गया sentence, Part ‘a’ से प्रारंभ होगा क्योंकि यह sentence के मुख्य विचार, ‘one of the primary reasons’ का परिचय देता है। Part ‘c’ हमें उस process के बारे में बताता है जिसमें बाधाओं (obstacles) का सामना करना पड़ता है, और Part ‘e’ हमें अन्य process के बारे में बताता है। इसलिए, ‘c’ के बाद ‘e’ आएगा। इसके अलावा, Part ‘b’ बताता है कि sample contamination का खतरा है और Part ‘d’ हमें contamination का कारण बताता है। इसलिए, ‘b’ के बाद ‘d’आएगा। अतः options के माध्यम से जाने पर option ‘b’  में सही sequence है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "20. Select the INCORRECTLY spelt word.",
                    question_hi: "20. Select the INCORRECTLY spelt word.",
                    options_en: [" Convinient ", " Embarrass ", 
                                " National ", " Discipline"],
                    options_hi: [" Convinient ", " Embarrass ",
                                " National ", " Discipline "],
                    solution_en: "20.(a) Convinient<br />\'Convenient\' is the correct spelling.",
                    solution_hi: "20.(a) Convinient<br />\'Convenient\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:</strong><br>Global warming is a major problem. The result will be the problems created by the rising temperatures of the Arctic region. As a result of global warming, (21) ______in rainfall and plant distribution affect the migration patterns, feeding behaviour, and breeding preferences of numerous bird species. The increase in the temperature may also alter ocean currents, which may alter the (22) ________ ecology as a whole. Greenhouse gas emissions are responsible for the ongoing melting of the Arctic. This will result in a (23) _______loop since greenhouse gases trap the heat in the atmosphere and the additional emissions may (24) _______ the global warming process. Now, the only way to reverse the harm is for every person to be environmentally sensitive. This may offer us hope that we will be able to leave a green planet to our future generations, who presently appear to face (25)________.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21. <strong>Cloze Test:</strong><br>Global warming is a major problem. The result will be the problems created by the rising temperatures of the Arctic region. As a result of global warming, (21) ______in rainfall and plant distribution affect the migration patterns, feeding behaviour, and breeding preferences of numerous bird species. The increase in the temperature may also alter ocean currents, which may alter the (22) ________ ecology as a whole. Greenhouse gas emissions are responsible for the ongoing melting of the Arctic. This will result in a (23) _______loop since greenhouse gases trap the heat in the atmosphere and the additional emissions may (24) _______ the global warming process. Now, the only way to reverse the harm is for every person to be environmentally sensitive. This may offer us hope that we will be able to leave a green planet to our future generations, who presently appear to face (25)________.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>stabilisation</p>", "<p>stagnation</p>", 
                                "<p>fixation</p>", "<p>alterations</p>"],
                    options_hi: ["<p>stabilisation</p>", "<p>stagnation</p>",
                                "<p>fixation</p>", "<p>alterations</p>"],
                    solution_en: "<p>21.(d) alterations<br>&lsquo;Alteration&rsquo; means the process of changing or altering something. The given passage states that alterations in rainfall and plant distribution affect the migration patterns, feeding behaviour, and breeding preferences of numerous bird species. Hence, &lsquo;alterations&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(d) alterations<br>&lsquo;Alteration&rsquo; का अर्थ है किसी चीज़ को बदलने या संशोधित करने की प्रक्रिया। दिए गए passage में कहा गया है कि वर्षा (rainfall) और पौधों के वितरण (plant distribution) में परिवर्तन कई पक्षी प्रजातियों के प्रवासन पैटर्न(migration patterns), feeding behaviour, और प्रजनन प्राथमिकताएँ (breeding preferences) को प्रभावित करते हैं। अतः, &lsquo;alterations&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong><br>Global warming is a major problem. The result will be the problems created by the rising temperatures of the Arctic region. As a result of global warming, (21) ______in rainfall and plant distribution affect the migration patterns, feeding behaviour, and breeding preferences of numerous bird species. The increase in the temperature may also alter ocean currents, which may alter the (22) ________ ecology as a whole. Greenhouse gas emissions are responsible for the ongoing melting of the Arctic. This will result in a (23) _______loop since greenhouse gases trap the heat in the atmosphere and the additional emissions may (24) _______ the global warming process. Now, the only way to reverse the harm is for every person to be environmentally sensitive. This may offer us hope that we will be able to leave a green planet to our future generations, who presently appear to face (25)________.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze Test:</strong><br>Global warming is a major problem. The result will be the problems created by the rising temperatures of the Arctic region. As a result of global warming, (21) ______in rainfall and plant distribution affect the migration patterns, feeding behaviour, and breeding preferences of numerous bird species. The increase in the temperature may also alter ocean currents, which may alter the (22) ________ ecology as a whole. Greenhouse gas emissions are responsible for the ongoing melting of the Arctic. This will result in a (23) _______loop since greenhouse gases trap the heat in the atmosphere and the additional emissions may (24) _______ the global warming process. Now, the only way to reverse the harm is for every person to be environmentally sensitive. This may offer us hope that we will be able to leave a green planet to our future generations, who presently appear to face (25)________.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>grassland</p>", "<p>biosphere</p>", 
                                "<p>marine</p>", "<p>landscape</p>"],
                    options_hi: ["<p>grassland</p>", "<p>biosphere</p>",
                                "<p>marine</p>", "<p>landscape</p>"],
                    solution_en: "<p>22.(c) marine<br>&lsquo;Marine&rsquo; means found in the sea. The given passage states that the increase in the temperature may also alter ocean currents, which may alter the marine ecology as a whole. Hence, &lsquo;marine&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(c) marine<br>&lsquo;Marine&rsquo; का अर्थ है समुद्र में पाया जाने वाला। दिए गए passage में कहा गया है कि तापमान में वृद्धि से समुद्री धाराओं(ocean currents) में भी परिवर्तन हो सकता है, जो संपूर्ण समुद्री पारिस्थितिकी (marine ecology) को बदल सकता है। अतः, &lsquo;marine&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:</strong><br>Global warming is a major problem. The result will be the problems created by the rising temperatures of the Arctic region. As a result of global warming, (21) ______in rainfall and plant distribution affect the migration patterns, feeding behaviour, and breeding preferences of numerous bird species. The increase in the temperature may also alter ocean currents, which may alter the (22) ________ ecology as a whole. Greenhouse gas emissions are responsible for the ongoing melting of the Arctic. This will result in a (23) _______loop since greenhouse gases trap the heat in the atmosphere and the additional emissions may (24) _______ the global warming process. Now, the only way to reverse the harm is for every person to be environmentally sensitive. This may offer us hope that we will be able to leave a green planet to our future generations, who presently appear to face (25)________.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23. <strong>Cloze Test:</strong><br>Global warming is a major problem. The result will be the problems created by the rising temperatures of the Arctic region. As a result of global warming, (21) ______in rainfall and plant distribution affect the migration patterns, feeding behaviour, and breeding preferences of numerous bird species. The increase in the temperature may also alter ocean currents, which may alter the (22) ________ ecology as a whole. Greenhouse gas emissions are responsible for the ongoing melting of the Arctic. This will result in a (23) _______loop since greenhouse gases trap the heat in the atmosphere and the additional emissions may (24) _______ the global warming process. Now, the only way to reverse the harm is for every person to be environmentally sensitive. This may offer us hope that we will be able to leave a green planet to our future generations, who presently appear to face (25)________.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>feeble</p>", "<p>gentle</p>", 
                                "<p>vicious</p>", "<p>moderate</p>"],
                    options_hi: ["<p>feeble</p>", "<p>gentle</p>",
                                "<p>vicious</p>", "<p>moderate</p>"],
                    solution_en: "<p>23.(c) vicious<br>&lsquo;Vicious loop&rsquo; means a continuing unpleasant situation, created when one problem causes another problem that then makes the first problem worse. The given passage states that this will result in a vicious loop. Hence, &lsquo;vicious&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(c) vicious<br>&lsquo;Vicious loop&rsquo; का अर्थ है एक निरंतर असुखद (unpleasant) स्थिति, जो तब उत्पन्न होती है जब एक समस्या दूसरी समस्या का कारण बनती है, जो फिर पहली समस्या को और बदतर (worse) बना देती है। दिए गए passage में कहा गया है कि इसका परिणाम एक दुष्चक्र (vicious loop) होगा। अतः, \'vicious&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong><br>Global warming is a major problem. The result will be the problems created by the rising temperatures of the Arctic region. As a result of global warming, (21) ______in rainfall and plant distribution affect the migration patterns, feeding behaviour, and breeding preferences of numerous bird species. The increase in the temperature may also alter ocean currents, which may alter the (22) ________ ecology as a whole. Greenhouse gas emissions are responsible for the ongoing melting of the Arctic. This will result in a (23) _______loop since greenhouse gases trap the heat in the atmosphere and the additional emissions may (24) _______ the global warming process. Now, the only way to reverse the harm is for every person to be environmentally sensitive. This may offer us hope that we will be able to leave a green planet to our future generations, who presently appear to face (25)________.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. Cloze Test:<br>Global warming is a major problem. The result will be the problems created by the rising temperatures of the Arctic region. As a result of global warming, (21) ______in rainfall and plant distribution affect the migration patterns, feeding behaviour, and breeding preferences of numerous bird species. The increase in the temperature may also alter ocean currents, which may alter the (22) ________ ecology as a whole. Greenhouse gas emissions are responsible for the ongoing melting of the Arctic. This will result in a (23) _______loop since greenhouse gases trap the heat in the atmosphere and the additional emissions may (24) _______ the global warming process. Now, the only way to reverse the harm is for every person to be environmentally sensitive. This may offer us hope that we will be able to leave a green planet to our future generations, who presently appear to face (25)________.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>hinder</p>", "<p>repress</p>", 
                                "<p>restrain</p>", "<p>accelerate</p>"],
                    options_hi: ["<p>hinder</p>", "<p>repress</p>",
                                "<p>restrain</p>", "<p>accelerate</p>"],
                    solution_en: "<p>24.(d) accelerate<br>&lsquo;Accelerate&rsquo; means increase in rate, amount, or extent. The given passage states that the additional emissions may accelerate the global warming process. Hence, \'accelerate\' is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(d) accelerate<br>&lsquo;Accelerate&rsquo; का अर्थ है दर, मात्रा या सीमा में वृद्धि। दिए गए passage में कहा गया है कि अतिरिक्त उत्सर्जन(emission), global warming प्रक्रिया को तेज (accelerate) कर सकता है। अतः, \'accelerate\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong><br>Global warming is a major problem. The result will be the problems created by the rising temperatures of the Arctic region. As a result of global warming, (21) ______in rainfall and plant distribution affect the migration patterns, feeding behaviour, and breeding preferences of numerous bird species. The increase in the temperature may also alter ocean currents, which may alter the (22) ________ ecology as a whole. Greenhouse gas emissions are responsible for the ongoing melting of the Arctic. This will result in a (23) _______loop since greenhouse gases trap the heat in the atmosphere and the additional emissions may (24) _______ the global warming process. Now, the only way to reverse the harm is for every person to be environmentally sensitive. This may offer us hope that we will be able to leave a green planet to our future generations, who presently appear to face (25)________.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25.<strong> Cloze Test:</strong><br>Global warming is a major problem. The result will be the problems created by the rising temperatures of the Arctic region. As a result of global warming, (21) ______in rainfall and plant distribution affect the migration patterns, feeding behaviour, and breeding preferences of numerous bird species. The increase in the temperature may also alter ocean currents, which may alter the (22) ________ ecology as a whole. Greenhouse gas emissions are responsible for the ongoing melting of the Arctic. This will result in a (23) _______loop since greenhouse gases trap the heat in the atmosphere and the additional emissions may (24) _______ the global warming process. Now, the only way to reverse the harm is for every person to be environmentally sensitive. This may offer us hope that we will be able to leave a green planet to our future generations, who presently appear to face (25)________.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>renovation</p>", "<p>extinction</p>", 
                                "<p>redemption</p>", "<p>salvation</p>"],
                    options_hi: ["<p>renovation</p>", "<p>extinction</p>",
                                "<p>redemption</p>", "<p>salvation</p>"],
                    solution_en: "<p>25.(b) extinction<br>&lsquo;Extinction&rsquo; means a situation in which something no longer exists. The given passage states that this may offer us hope that we will be able to leave a green planet to our future generations, who presently appear to face extinction. Hence, \'extinction\' is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(b) extinction<br>&lsquo;Extinction&rsquo; का अर्थ है ऐसी स्थिति जिसमें कोई चीज़ अब अस्तित्व(exist) में नहीं है। दिए गए passage में बताया गया है कि इससे हमें उम्मीद मिल सकती है कि हम अपनी आने वाली पीढ़ियों ( future generations) के लिए एक हरा-भरा ग्रह (green planet) छोड़ पाएंगे, जो वर्तमान में विलुप्त होने(extinction) का सामना कर रहे हैं। अतः, \'extinction\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>