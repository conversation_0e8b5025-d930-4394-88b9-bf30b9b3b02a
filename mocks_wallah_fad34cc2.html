<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 12</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">12</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 10
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 11,
                end: 11
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A man bought an equal number of oranges at the rate of 3 for one rupee and at the rate of 2 for one rupee. At what price per dozen should he sell them to earn a profit of 20%?</p>",
                    question_hi: "<p>1. एक व्यक्ति ने एक रुपए में 3 की दर से और एक रुपए में 2 की दर से समान संख्या में संतरे खरीदे। 20% का लाभ अर्जित के लिए उसे उन्हें प्रति दर्जन किस मूल्य पर बेचना चाहिए?</p>",
                    options_en: ["<p>₹7</p>", "<p>₹4</p>", 
                                "<p>₹5</p>", "<p>₹6</p>"],
                    options_hi: ["<p>₹7</p>", "<p>₹4</p>",
                                "<p>₹5</p>", "<p>₹6</p>"],
                    solution_en: "<p>1(d)<br>Let the number of oranges in both cases be 12 i.e. LCM of (3,2)<br>CP of 12 oranges at the rate of 3 = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = ₹4<br>CP of 12 oranges at the rate of 2 = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹6<br>CP of 24 oranges = 4 + 6 = ₹10<br>Then, CP of 12 oranges = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹5<br>Required SP to earn a profit of 20% = 5 &times;<math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹6</p>",
                    solution_hi: "<p>1(d)<br>माना दोनों मामलों में संतरों की संख्या 12 (3 और 2 का LCM) है। <br>3 की दर से 12 संतरों का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = ₹4<br>2 की दर से 12 संतरों का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹6<br>24 संतरों का क्रय मूल्य = 4 + 6 = ₹10<br>फिर, 12 संतरों का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹5<br>20% का लाभ अर्जित करने के लिए आवश्यक क्रय मूल्य = 5 &times;<math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹6</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A man sold an article for ₹247.50, there by gaining 12.5%. The cost of the article was:</p>",
                    question_hi: "<p>2. एक व्यक्ति 12.5% लाभ अर्जित करने के लिए एक वस्तु को ₹247.50 में बेचता है। वस्तु का मूल्य कितना था?</p>",
                    options_en: ["<p>₹ 220</p>", "<p>₹ 225</p>", 
                                "<p>₹ 210</p>", "<p>₹ 224</p>"],
                    options_hi: ["<p>₹ 220</p>", "<p>₹ 225</p>",
                                "<p>₹ 210</p>", "<p>₹ 224</p>"],
                    solution_en: "<p>2(a)<br>Let the cost of article be x<br>According to question,<br>x &times; 112.5% = 247.50<br>x = 2.2 &times; 100 = ₹220</p>",
                    solution_hi: "<p>2(a)<br>माना वस्तु की लागत x है। <br>प्रश्न के अनुसार,<br>x &times; 112.5% = 247.50<br>x = 2.2 &times; 100 = ₹220</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A shopkeeper, on selling a pan for ₹10, loses <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> of what it cost him. The cost price of a pan is:</p>",
                    question_hi: "<p>3. किसी दुकानदार को ₹10 में एक पैन बेचने पर, उसके मूल्य के <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> भाग की हानि होती है। एक पैन का क्रय मूल्य कितना है?</p>",
                    options_en: ["<p>₹10</p>", "<p>₹11</p>", 
                                "<p>₹9</p>", "<p>₹12</p>"],
                    options_hi: ["<p>₹10</p>", "<p>₹11</p>",
                                "<p>₹9</p>", "<p>₹12</p>"],
                    solution_en: "<p>3(b) Selling Price = Cost Price - Loss<br><math display=\"inline\"><mo>&#8658;</mo></math> 10 = 11x - x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = ₹ 1<br>Hence, Cost Price = 11<math display=\"inline\"><mo>&#215;</mo></math>1 = ₹11</p>",
                    solution_hi: "<p>3(b) विक्रय मूल्य = क्रय मूल्य - हानि <br><math display=\"inline\"><mo>&#8658;</mo></math> 10 = 11x - x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = ₹ 1<br>अतः , क्रय मूल्य = 11<math display=\"inline\"><mo>&#215;</mo></math>1 = ₹11</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A sells a bicycle to B at a profit of 20% and B sells it to C at a profit of 25%. If C pays ₹1,500, then what did A pay for it?</p>",
                    question_hi: "<p>4. A, B को 20% के लाभ पर एक साइकिल बेचता है और B इसे 25% के लाभ पर C को बेचता है। यदि C, ₹1,500 का भुगतान करता है, तो A ने इसके लिए कितना भुगतान किया था?</p>",
                    options_en: ["<p>₹1,125</p>", "<p>₹1,000</p>", 
                                "<p>₹1,100</p>", "<p>₹825</p>"],
                    options_hi: ["<p>₹1,125</p>", "<p>₹1,000</p>",
                                "<p>₹1,100</p>", "<p>₹825</p>"],
                    solution_en: "<p>4(b)<br>Fractional value of 20% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>, 25% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>Ratio <math display=\"inline\"><mo>&#8658;</mo></math>&nbsp; &nbsp; &nbsp;CP :&nbsp; SP<br>A : B&nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp;:&nbsp; &nbsp;6<br>B : C&nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; :&nbsp; &nbsp;5<br>-----------------------------<br>Final Ratio <math display=\"inline\"><mo>&#8658;</mo></math> A : C = 2 : 3 <br>According to the question , <br>Cost Price of C (3 units) = 1,500 ₹.<br>1 unit <math display=\"inline\"><mo>&#8594;</mo></math> 500 Rs.<br>Cost Price of A (2 units) = 1,000 ₹</p>",
                    solution_hi: "<p>4(b)<br>20% भिन्नात्मक मान = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>, 25% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>अनुपात <math display=\"inline\"><mo>&#8658;</mo></math> क्रय मूल्य : विक्रय मूल्य<br>A&nbsp; : B&nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;6<br>B&nbsp; : C&nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;5<br>-----------------------------------------<br>अंतिम अनुपात <math display=\"inline\"><mo>&#8658;</mo></math> A : C = 2 : 3 <br>प्रश्न के अनुसार, <br>C (3 इकाई ) का क्रय मूल्य = 1,500 ₹.<br>1 इकाई <math display=\"inline\"><mo>&#8594;</mo></math> 500 Rs.<br>A (2 इकाई) का क्रय मूल्य = 1,000 ₹</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A trader lost 20% by selling a watch for ₹1,024. What percentage will he gain by selling it for ₹1,472 ?</p>",
                    question_hi: "<p>5. एक व्यापारी को एक घड़ी ₹1,024 में बेचने पर 20% की हानि होती है। इसे ₹1,472 में बेचने पर उसे कितने प्रतिशत का लाभ होगा ?</p>",
                    options_en: ["<p>15%</p>", "<p>10%</p>", 
                                "<p>20%</p>", "<p>12%</p>"],
                    options_hi: ["<p>15%</p>", "<p>10%</p>",
                                "<p>20%</p>", "<p>12%</p>"],
                    solution_en: "<p>5.(a)<br>CP = 1024<math display=\"inline\"><mo>&#215;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> = 1280<br>SP = 1472<br>Profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1472</mn><mo>-</mo><mn>1280</mn></mrow><mn>1280</mn></mfrac></math> &times; 100 = 15%</p>",
                    solution_hi: "<p>5.(a)<br>क्रय मूल्य = 1024 <math display=\"inline\"><mo>&#215;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>= 1280<br>विक्रय मूल्य = 1472<br>लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1472</mn><mo>-</mo><mn>1280</mn></mrow><mn>1280</mn></mfrac></math>&nbsp;&times; 100 = 15%</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A dishonest shopkeeper pretends to sell his goods at cost price. However, he uses a false weight on which 984 gm is written, but actually weighs lesser. Using this false weight, the shopkeeper makes a gain of 23%. The actual measure of the weight used is:</p>",
                    question_hi: "<p>6. एक बेईमान दुकानदार अपना माल क्रय मूल्य पर बेचने का दिखावा करता है। हालाँकि, वह गलत वजन का उपयोग करता है जिस पर 984 gm लिखा होता है, लेकिन वास्तव में इसका वजन कम होता है। इस गलत वजन का उपयोग करके, दुकानदार को 23% का लाभ होता है। प्रयुक्त वजन का वास्तविक माप कितना है?</p>",
                    options_en: ["<p>935 gm</p>", "<p>800 gm</p>", 
                                "<p>850 gm</p>", "<p>900 gm</p>"],
                    options_hi: ["<p>935 gm</p>", "<p>800 gm</p>",
                                "<p>850 gm</p>", "<p>900 gm</p>"],
                    solution_en: "<p>6(b)<br>We know that , <br>Price &prop; <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi></mrow><mrow><mi>W</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi><mi>&#160;</mi><mi>&#160;</mi></mrow></mfrac></math><br>A/Q<br>P% <math display=\"inline\"><mo>&#8594;</mo></math> 100 : 123<br>Weight <math display=\"inline\"><mo>&#8594;</mo></math> 123 : 100<br>So, <br>123 unit <math display=\"inline\"><mo>&#8594;</mo></math> 984 gm<br>100 unit <math display=\"inline\"><mo>&#8594;</mo></math> 800 gm<br>Actual weight = 800 gm</p>",
                    solution_hi: "<p>6(b)<br>जैसा की हम जानते है , <br>कीमत &prop; <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi></mrow><mrow><mi>&#2357;</mi><mi>&#2332;</mi><mi>&#2344;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi></mrow></mfrac></math><br>प्रश्न के अनुसार <br>लाभ % <math display=\"inline\"><mo>&#8594;</mo></math> 100 : 123<br>वज़न <math display=\"inline\"><mo>&#8594;</mo></math> 123 : 100<br>इसलिए, <br>123 इकाई <math display=\"inline\"><mo>&#8594;</mo></math> 984 ग्राम<br>100 इकाई <math display=\"inline\"><mo>&#8594;</mo></math> 800 ग्राम<br>वास्तविक वज़न = 800 ग्राम</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. A retailer would have made a profit of 18% if he had sold an article at its marked price. If the marked price of the article is ₹826, then the cost price of the article is:</p>",
                    question_hi: "<p>7. यदि एक फुटकर विक्रेता ने एक वस्तु को उसके अंकित मूल्य पर बेचा होता, तो उसे 18% का लाभ होता। यदि वस्तु का अंकित मूल्य ₹826 है, तो वस्तु का क्रय मूल्य क्या है ?</p>",
                    options_en: ["<p>₹800</p>", "<p>₹750</p>", 
                                "<p>₹700</p>", "<p>₹650</p>"],
                    options_hi: ["<p>₹800</p>", "<p>₹750</p>",
                                "<p>₹700</p>", "<p>₹650</p>"],
                    solution_en: "<p>7(c)<br>Cost Price <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>18</mn></mrow><mn>100</mn></mfrac></math> = 826<br><math display=\"inline\"><mo>&#8658;</mo></math> Cost Price = 826 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>118</mn></mfrac></math>= ₹ 700</p>",
                    solution_hi: "<p>7(c)<br>क्रय मूल्य<math display=\"inline\"><mo>&#215;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>18</mn></mrow><mn>100</mn></mfrac></math> = 826<br><math display=\"inline\"><mo>&#8658;</mo></math> क्रय मूल्य = 826 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>118</mn></mfrac></math>= ₹ 700</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. A dishonest shopkeeper pretends to sell his goods at cost price. However, he uses a false weight on which 200 gm is written, but which actually weighs lesser. Using this false weight, the shopkeeper makes a gain of 25%. The actual measure of the weight used is:</p>",
                    question_hi: "<p>8. एक बेईमान दुकानदार अपना माल क्रय मूल्य पर बेचने का दिखावा करता है। हालांकि, वह गलत भार का उपयोग करता है जिस पर 200 gm लिखा होता है, लेकिन वास्तव में उसका भार कम होता है। इस गलत भार का उपयोग करके, दुकानदार को 25% का लाभ होता है। प्रयुक्त भार का वास्तविक माप कितना है ?</p>",
                    options_en: ["<p>150 gm</p>", "<p>200 gm</p>", 
                                "<p>160 gm</p>", "<p>180 gm</p>"],
                    options_hi: ["<p>150 gm</p>", "<p>200 gm</p>",
                                "<p>160 gm</p>", "<p>180 gm</p>"],
                    solution_en: "<p>8(c)<br>Let the false weight be <math display=\"inline\"><mi>x</mi></math>.<br>According to question,<br><math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac></math>= 200gm<br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>125</mn></mfrac><mo>&#160;</mo></math> = 160 gm</p>",
                    solution_hi: "<p>8(c)<br>माना आभाषी वजन <math display=\"inline\"><mi>x</mi></math> है।<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>100</mn></mfrac></math>= 200gm<br><math display=\"inline\"><mi>x</mi></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>125</mn></mfrac><mo>&#160;</mo></math>= 160 gm</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. A man sells an article at a profit of 25%. If he had bought it at 20% less and sold it for ₹10.50 less, he would have gained 30%. Find the cost price of the article.</p>",
                    question_hi: "<p>9. एक व्यक्ति एक वस्तु को 25% के लाभ पर बेचता है। यदि उसने इसे 20% कम मूल्य पर खरीदा होता और ₹10.50 कम मूल्य पर बेचा होता, तो उसे 30% का लाभ होता। वस्तु का क्रय मूल्य ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹25</p>", "<p>₹125</p>", 
                                "<p>₹50</p>", "<p>₹100</p>"],
                    options_hi: ["<p>₹25</p>", "<p>₹125</p>",
                                "<p>₹50</p>", "<p>₹100</p>"],
                    solution_en: "<p>9(c)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742795165567.png\" alt=\"rId5\" width=\"221\" height=\"135\"></p>",
                    solution_hi: "<p>9(c)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742795165806.png\" alt=\"rId6\" width=\"214\" height=\"123\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A shopkeeper bought an article for ₹319.60. Approximately, at what price should he sell the article to make a 25% profit ?</p>",
                    question_hi: "<p>10. एक दुकानदार ₹319.60 में एक वस्तु खरीदता है। 25% लाभ अर्जित करने के लिए उसे वस्तु किस अनुमानित मूल्य पर बेचनी चाहिए ?</p>",
                    options_en: ["<p>₹450</p>", "<p>₹400</p>", 
                                "<p>₹600</p>", "<p>₹500</p>"],
                    options_hi: ["<p>₹450</p>", "<p>₹400</p>",
                                "<p>₹600</p>", "<p>₹500</p>"],
                    solution_en: "<p>10(b)</p>\n<p>SP = CP&nbsp;<math display=\"inline\"><mo>&#215;</mo></math> (1+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mn>100</mn></mfrac></math>)</p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> SP = 319.60 (1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>100</mn></mfrac></math>) = 319.60 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>= 399.5 &asymp; ₹400</p>",
                    solution_hi: "<p>10(b)</p>\n<p>विक्रय मूल्य = क्रय मूल्य<math display=\"inline\"><mo>&#215;</mo></math> (1+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mn>100</mn></mfrac></math>)</p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> विक्रय मूल्य = 319.60 (1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>100</mn></mfrac></math>) = 319.60 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>= 399.5 &asymp; ₹400</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. A man sells 320 mangoes at the cost price of 400 mangoes (Cost price of all the articles is the same. The selling price of all the articles is the same). His gain percentage is:</p>",
                    question_hi: "<p>11. एक व्यक्ति 400 आमों के क्रय मूल्य पर 320 आम बेचता है (सभी वस्तुओं का क्रय मूल्य समान है। सभी वस्तुओं का विक्रय मूल्य समान है)। उसका लाभ प्रतिशत कितना है?</p>",
                    options_en: ["<p>10%</p>", "<p>25%</p>", 
                                "<p>20%</p>", "<p>15%</p>"],
                    options_hi: ["<p>10%</p>", "<p>25%</p>",
                                "<p>20%</p>", "<p>15%</p>"],
                    solution_en: "<p>11(b)<br>320 &times; SP = 400 &times; CP<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>SP</mi><mi>CP</mi></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>Hence<br>Gain % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>&times; 100 = 25%</p>",
                    solution_hi: "<p>11(b)</p>\n<p>320 &times; विक्रय मूल्य = 400 &times; क्रय मूल्य</p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>अतः , लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>&times; 100 = 25%</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "misc",
                    question_en: "<p>12. A vendor sold 10 toffees for a rupee, thereby gaining 20%. How many did he buy for a rupee ?</p>",
                    question_hi: "<p>12. एक विक्रेता ने एक रुपए में 10 टॉफियां बेची, जिससे उसे 20% का लाभ हुआ। उसने एक रुपए में कितनी टॉफियां खरीदी ?</p>",
                    options_en: ["<p>6</p>", "<p>12</p>", 
                                "<p>10</p>", "<p>8</p>"],
                    options_hi: ["<p>6</p>", "<p>12</p>",
                                "<p>10</p>", "<p>8</p>"],
                    solution_en: "<p>12(b)<br>20% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>\n<p>SP of 10 toffees = ₹1</p>\n<p>Then, SP of 1 toffee =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>\n<p>Let CP of x toffees be ₹1</p>\n<p>Then, CP of 1 toffee =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math></p>\n<p>Now,&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#215;</mo><mfrac><mn>6</mn><mn>5</mn></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mrow><mn>5</mn><mi>x</mi></mrow></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>5</mn></mfrac></math> = 12</p>",
                    solution_hi: "<p>12(b)</p>\n<p>20% =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>\n<p>10 टॉफ़ियों की विक्रय मूल्य (SP) = ₹1&nbsp;</p>\n<p>तब, 1 टॉफ़ी का SP = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>\n<p>माना , x टॉफ़ियों का क्रय मूल्य (CP) = ₹1</p>\n<p>अब , <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&#215;</mo><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mrow><mn>5</mn><mi>x</mi></mrow></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>5</mn></mfrac></math> = 12</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>