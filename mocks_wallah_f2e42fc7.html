<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">90:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 50</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">50</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 47
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 48,
                end: 49
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Times New Roman;\"> The line graph shows the production (in tonnes) and the sales (in tonnes) of a company.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">What percentage (approximately) of the total sales of the company is the total production of the company in all the years together? (correct to 2 decimal places)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image1.png\"></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">1.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2354;&#2366;&#2311;&#2344; &#2327;&#2381;&#2352;&#2366;&#2347; &#2319;&#2325; &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2375; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; (&#2335;&#2344; &#2350;&#2375;&#2306;) &#2324;&#2352; &#2348;&#2367;&#2325;&#2381;&#2352;&#2368; (&#2335;&#2344; &#2350;&#2375;&#2306;) &#2325;&#2379; &#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image2.png\"></p>\r\n<p><span style=\"font-family: Baloo;\">&#2360;&#2349;&#2368; &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2350;&#2375;&#2306; &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2375; &#2325;&#2369;&#2354; &#2348;&#2367;&#2325;&#2381;&#2352;&#2368; &#2325;&#2366; &#2325;&#2367;&#2340;&#2344;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; (&#2354;&#2327;&#2349;&#2327;) &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2366; &#2325;&#2369;&#2354; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2361;&#2376;? (&#2342;&#2379; &#2342;&#2358;&#2350;&#2354;&#2357; &#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306; &#2340;&#2325; &#2360;&#2361;&#2368;)</span></p>\n",
                    options_en: ["<p>75</p>\n", "<p>72.55</p>\n", 
                                "<p>68.12</p>\n", "<p>78.44</p>\n"],
                    options_hi: ["<p>75</p>\n", "<p>72.55</p>\n",
                                "<p>68.12</p>\n", "<p>78.44</p>\n"],
                    solution_en: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Production of the company = 600 + 628 + 750 + 500 + 570 = 3048</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total sales of the company = 450 + 525 + 500 + 400 + 516 = 2391 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required percentage =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2391</mn><mn>3048</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 78.44%</span></p>\n",
                    solution_hi: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">&#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Times New Roman;\"> = 600 + 628 + 750 + 500 + 570 = 3048</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2368; &#2325;&#2369;&#2354; &#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Times New Roman;\"> = 450 + 525 + 500 + 400 + 516 = 2391 </span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2391</mn><mn>3048</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 78.44%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Times New Roman;\"> The line graph shows the production (in tonnes) and the sales (in tonnes) of a company.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">What percentage (approximately) of the total sales of the company is the total production of the company in all the years together? (correct to 2 decimal places)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image1.png\"></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">1.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2354;&#2366;&#2311;&#2344; &#2327;&#2381;&#2352;&#2366;&#2347; &#2319;&#2325; &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2375; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; (&#2335;&#2344; &#2350;&#2375;&#2306;) &#2324;&#2352; &#2348;&#2367;&#2325;&#2381;&#2352;&#2368; (&#2335;&#2344; &#2350;&#2375;&#2306;) &#2325;&#2379; &#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image2.png\"></p>\r\n<p><span style=\"font-family: Baloo;\">&#2360;&#2349;&#2368; &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2350;&#2375;&#2306; &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2375; &#2325;&#2369;&#2354; &#2348;&#2367;&#2325;&#2381;&#2352;&#2368; &#2325;&#2366; &#2325;&#2367;&#2340;&#2344;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; (&#2354;&#2327;&#2349;&#2327;) &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2366; &#2325;&#2369;&#2354; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2361;&#2376;? (&#2342;&#2379; &#2342;&#2358;&#2350;&#2354;&#2357; &#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306; &#2340;&#2325; &#2360;&#2361;&#2368;)</span></p>\n",
                    options_en: ["<p>75</p>\n", "<p>72.55</p>\n", 
                                "<p>68.12</p>\n", "<p>78.44</p>\n"],
                    options_hi: ["<p>75</p>\n", "<p>72.55</p>\n",
                                "<p>68.12</p>\n", "<p>78.44</p>\n"],
                    solution_en: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Production of the company = 600 + 628 + 750 + 500 + 570 = 3048</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total sales of the company = 450 + 525 + 500 + 400 + 516 = 2391 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required percentage =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2391</mn><mn>3048</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 78.44%</span></p>\n",
                    solution_hi: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">&#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Times New Roman;\"> = 600 + 628 + 750 + 500 + 570 = 3048</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2368; &#2325;&#2369;&#2354; &#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Times New Roman;\"> = 450 + 525 + 500 + 400 + 516 = 2391 </span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2391</mn><mn>3048</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 78.44%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Times New Roman;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">,</span><span style=\"font-family: Times New Roman;\"> then what will be the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mi>&nbsp;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>)</mo></math></span><span style=\"font-family: Times New Roman;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">2.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2340;&#2379;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mi>&nbsp;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>)</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2361;&#2376;</span><span style=\"font-family: Times New Roman;\"> </span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><mrow><mn>4</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><msqrt><mn>5</mn></msqrt></math></p>\n", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><mrow><mn>2</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><mrow><mn>4</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac></math> </span></p>\n", "<p><span style=\"text-decoration: line-through;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><msqrt><mn>5</mn></msqrt></math></span></p>\n",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><mrow><mn>2</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac></math></p>\n"],
                    solution_en: "<p>2.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac><mo>,</mo><mo>&nbsp;</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>2</mn><msqrt><mn>5</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>1</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>2</mn><mn>1</mn></mfrac><mo>,</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mi>&nbsp;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>1</mn></mfrac><mo>+</mo><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mfrac><mn>2</mn><msqrt><mn>5</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>+</mo><mfrac><mrow><mn>4</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    solution_hi: "<p>2.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac><mo>,</mo><mo>&nbsp;</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>2</mn><msqrt><mn>5</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>1</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>2</mn><mn>1</mn></mfrac><mo>,</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mi>&nbsp;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>1</mn></mfrac><mo>+</mo><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mfrac><mn>2</mn><msqrt><mn>5</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>+</mo><mfrac><mrow><mn>4</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Times New Roman;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">,</span><span style=\"font-family: Times New Roman;\"> then what will be the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mi>&nbsp;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>)</mo></math></span><span style=\"font-family: Times New Roman;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">2.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2340;&#2379;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mi>&nbsp;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>)</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2361;&#2376;</span><span style=\"font-family: Times New Roman;\"> </span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><mrow><mn>4</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><msqrt><mn>5</mn></msqrt></math></p>\n", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><mrow><mn>2</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><mrow><mn>4</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac></math> </span></p>\n", "<p><span style=\"text-decoration: line-through;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><msqrt><mn>5</mn></msqrt></math></span></p>\n",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>+</mo><mfrac><mrow><mn>2</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac></math></p>\n"],
                    solution_en: "<p>2.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac><mo>,</mo><mo>&nbsp;</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>2</mn><msqrt><mn>5</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>1</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>2</mn><mn>1</mn></mfrac><mo>,</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mi>&nbsp;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>1</mn></mfrac><mo>+</mo><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mfrac><mn>2</mn><msqrt><mn>5</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>+</mo><mfrac><mrow><mn>4</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    solution_hi: "<p>2.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>2</mn></mfrac><mo>,</mo><mo>&nbsp;</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>2</mn><msqrt><mn>5</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>1</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>2</mn><mn>1</mn></mfrac><mo>,</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>(</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mi>&nbsp;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><msqrt><mn>5</mn></msqrt><mn>1</mn></mfrac><mo>+</mo><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mfrac><mn>2</mn><msqrt><mn>5</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>+</mo><mfrac><mrow><mn>4</mn><msqrt><mn>5</mn></msqrt></mrow><mn>5</mn></mfrac><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Times New Roman;\"> One diagonal of a rhombus is&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msqrt><mn>3</mn></msqrt></math>&nbsp;</span><span style=\"font-family: Times New Roman;\"> If the other diagonal is equal to its side, then the area of rhombus is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">3.</span><span style=\"font-family: Baloo;\"> &#2319;&#2325; &#2360;&#2350;&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2366; &#2319;&#2325; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msqrt><mn>3</mn></msqrt></math> &#2360;&#2375;&#2350;&#2368;. &#2351;&#2342;&#2367; &#2342;&#2370;&#2360;&#2352;&#2366; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; &#2313;&#2360;&#2325;&#2368; &#2349;&#2369;&#2332;&#2366; &#2325;&#2375; &#2348;&#2352;&#2366;&#2348;&#2352; &#2361;&#2376;, &#2340;&#2379; &#2360;&#2350;&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; &#2361;&#2376;:</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><msqrt><mn>3</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>24</mn><msqrt><mn>3</mn></msqrt></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><msqrt><mn>3</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>32</mn><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><msqrt><mn>3</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>24</mn><msqrt><mn>3</mn></msqrt></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><msqrt><mn>3</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>32</mn><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    solution_en: "<p>3.<span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image9.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let side of the rhombus = 2a</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Other diagonal = 2a</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OB = OD = a cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In triangle OAB</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AB = 2a, OB = a, AO =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>(2a)&sup2; = a&sup2; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>4</mn><msqrt><mn>3</mn></msqrt></mrow></mfenced><mn>2</mn></msup></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">4a&sup2; = a&sup2; + 48</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Other diagonal = 2a = 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Area of the rhombus =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>8</mn><mo>&times;</mo><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>=</mo><mn>32</mn><msqrt><mn>3</mn></msqrt></math></span></p>\n",
                    solution_hi: "<p>3.<span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image9.png\"></p>\r\n<p><span style=\"font-family: Baloo;\">&#2350;&#2366;&#2344;&#2366; &#2360;&#2350;&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366; = 2a</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2309;&#2344;&#2381;&#2351; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; = 2a</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OB = OD = a cm</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; OAB . &#2350;&#2375;&#2306;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AB= 2a, OB = a, AO = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mi>a</mi><mo>)</mo><mo>&sup2;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>a</mi><mo>&sup2;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>4</mn><mo>&radic;</mo><mn>3</mn><mo>)</mo><mo>&sup2;</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">4a&sup2; = a&sup2; + 48</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a = 4</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2309;&#2344;&#2381;&#2351; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; = 2a = 8</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2360;&#2350;&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>8</mn><mo>&times;</mo><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>=</mo><mn>32</mn><msqrt><mn>3</mn></msqrt></math></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Times New Roman;\"> One diagonal of a rhombus is&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msqrt><mn>3</mn></msqrt></math>&nbsp;</span><span style=\"font-family: Times New Roman;\"> If the other diagonal is equal to its side, then the area of rhombus is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">3.</span><span style=\"font-family: Baloo;\"> &#2319;&#2325; &#2360;&#2350;&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2366; &#2319;&#2325; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msqrt><mn>3</mn></msqrt></math> &#2360;&#2375;&#2350;&#2368;. &#2351;&#2342;&#2367; &#2342;&#2370;&#2360;&#2352;&#2366; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; &#2313;&#2360;&#2325;&#2368; &#2349;&#2369;&#2332;&#2366; &#2325;&#2375; &#2348;&#2352;&#2366;&#2348;&#2352; &#2361;&#2376;, &#2340;&#2379; &#2360;&#2350;&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; &#2361;&#2376;:</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><msqrt><mn>3</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>24</mn><msqrt><mn>3</mn></msqrt></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><msqrt><mn>3</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>32</mn><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><msqrt><mn>3</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>24</mn><msqrt><mn>3</mn></msqrt></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><msqrt><mn>3</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>32</mn><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    solution_en: "<p>3.<span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image9.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let side of the rhombus = 2a</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Other diagonal = 2a</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OB = OD = a cm</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In triangle OAB</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AB = 2a, OB = a, AO =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>(2a)&sup2; = a&sup2; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>4</mn><msqrt><mn>3</mn></msqrt></mrow></mfenced><mn>2</mn></msup></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">4a&sup2; = a&sup2; + 48</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Other diagonal = 2a = 8</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Area of the rhombus =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>8</mn><mo>&times;</mo><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>=</mo><mn>32</mn><msqrt><mn>3</mn></msqrt></math></span></p>\n",
                    solution_hi: "<p>3.<span style=\"font-family: Times New Roman;\">(d) </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image9.png\"></p>\r\n<p><span style=\"font-family: Baloo;\">&#2350;&#2366;&#2344;&#2366; &#2360;&#2350;&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366; = 2a</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2309;&#2344;&#2381;&#2351; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; = 2a</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OB = OD = a cm</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; OAB . &#2350;&#2375;&#2306;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">AB= 2a, OB = a, AO = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mi>a</mi><mo>)</mo><mo>&sup2;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>a</mi><mo>&sup2;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>4</mn><mo>&radic;</mo><mn>3</mn><mo>)</mo><mo>&sup2;</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">4a&sup2; = a&sup2; + 48</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a = 4</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2309;&#2344;&#2381;&#2351; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; = 2a = 8</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2360;&#2350;&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>8</mn><mo>&times;</mo><mn>8</mn><msqrt><mn>3</mn></msqrt><mo>=</mo><mn>32</mn><msqrt><mn>3</mn></msqrt></math></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Times New Roman;\"> The average weight of 30 persons of group A is 3 kg more than the average weight of 25 persons of group B. The average weight of 25 persons of group B is 2.5 kg more than the average weight of 20 persons of group C. If the total weight of 30 persons of group A is 1725 kg, then what will be the average weight of the persons of group A and group C taken together?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">4.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">समूह A के 30 व्यक्तियों का औसत भार समूह B के 25 व्यक्तियों के औसत भार से 3 किग्रा अधिक है। समूह B के 25 व्यक्तियों का औसत भार समूह C के 20 व्यक्तियों के औसत भार से 2.5 किग्रा अधिक है। समूह A के 30 व्यक्तियों का कुल भार 1725 किग्रा है, तो समूह A और समूह C के व्यक्तियों का मिलाकर औसत भार कितना होगा? </span></p>",
                    options_en: ["<p>55.3</p>", "<p>55.4</p>", 
                                "<p>55.1</p>", "<p>55</p>"],
                    options_hi: ["<p>55.3</p>", "<p>55.4</p>",
                                "<p>55.1</p>", "<p>55</p>"],
                    solution_en: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Total weight of 30 persons of group A = 1725</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average weight =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1725</mn><mn>30</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 57.5 kg</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average weight of Group B = 57.5-3 = 54.5 kg</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average weight of Group C = 54.5-2.5 = 52 kg</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total weight of Group C = 52&times;20 = 1040</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1725</mn><mo>+</mo><mn>1040</mn></mrow><mrow><mn>30</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 55.3</span></p>",
                    solution_hi: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Baloo;\">समूह A के</span><span style=\"font-family: Times New Roman;\"> 30 </span><span style=\"font-family: Baloo;\">व्यक्तियों का कुल भार</span><span style=\"font-family: Times New Roman;\"> = 1725</span></p>\r\n<p><span style=\"font-family: Baloo;\">औसत वजन</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1725</mn><mn>30</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 57.5 kg</span></p>\r\n<p><span style=\"font-family: Baloo;\">ग्रुप B का औसत वजन</span><span style=\"font-family: Times New Roman;\"> = 57.5-3 = 54.5 kg</span></p>\r\n<p><span style=\"font-family: Baloo;\">ग्रुप C का औसत वजन</span><span style=\"font-family: Times New Roman;\"> = 54.5-2.5 = 52 kg</span></p>\r\n<p><span style=\"font-family: Baloo;\">ग्रुप C का कुल वजन</span><span style=\"font-family: Times New Roman;\"> = 52&times;20 = 1040</span></p>\r\n<p><span style=\"font-family: Baloo;\">औसत</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1725</mn><mo>+</mo><mn>1040</mn></mrow><mrow><mn>30</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 55.3</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Times New Roman;\"> The average weight of 30 persons of group A is 3 kg more than the average weight of 25 persons of group B. The average weight of 25 persons of group B is 2.5 kg more than the average weight of 20 persons of group C. If the total weight of 30 persons of group A is 1725 kg, then what will be the average weight of the persons of group A and group C taken together?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">4.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">समूह A के 30 व्यक्तियों का औसत भार समूह B के 25 व्यक्तियों के औसत भार से 3 किग्रा अधिक है। समूह B के 25 व्यक्तियों का औसत भार समूह C के 20 व्यक्तियों के औसत भार से 2.5 किग्रा अधिक है। समूह A के 30 व्यक्तियों का कुल भार 1725 किग्रा है, तो समूह A और समूह C के व्यक्तियों का मिलाकर औसत भार कितना होगा? </span></p>",
                    options_en: ["<p>55.3</p>", "<p>55.4</p>", 
                                "<p>55.1</p>", "<p>55</p>"],
                    options_hi: ["<p>55.3</p>", "<p>55.4</p>",
                                "<p>55.1</p>", "<p>55</p>"],
                    solution_en: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Total weight of 30 persons of group A = 1725</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average weight =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1725</mn><mn>30</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 57.5 kg</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average weight of Group B = 57.5-3 = 54.5 kg</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average weight of Group C = 54.5-2.5 = 52 kg</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total weight of Group C = 52&times;20 = 1040</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Average = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1725</mn><mo>+</mo><mn>1040</mn></mrow><mrow><mn>30</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 55.3</span></p>",
                    solution_hi: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Baloo;\">समूह A के</span><span style=\"font-family: Times New Roman;\"> 30 </span><span style=\"font-family: Baloo;\">व्यक्तियों का कुल भार</span><span style=\"font-family: Times New Roman;\"> = 1725</span></p>\r\n<p><span style=\"font-family: Baloo;\">औसत वजन</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1725</mn><mn>30</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 57.5 kg</span></p>\r\n<p><span style=\"font-family: Baloo;\">ग्रुप B का औसत वजन</span><span style=\"font-family: Times New Roman;\"> = 57.5-3 = 54.5 kg</span></p>\r\n<p><span style=\"font-family: Baloo;\">ग्रुप C का औसत वजन</span><span style=\"font-family: Times New Roman;\"> = 54.5-2.5 = 52 kg</span></p>\r\n<p><span style=\"font-family: Baloo;\">ग्रुप C का कुल वजन</span><span style=\"font-family: Times New Roman;\"> = 52&times;20 = 1040</span></p>\r\n<p><span style=\"font-family: Baloo;\">औसत</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1725</mn><mo>+</mo><mn>1040</mn></mrow><mrow><mn>30</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 55.3</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>,</mo><mo>&nbsp;</mo><mi>&ang;</mi><mi>A</mi><mo>=</mo><mn>135</mn><mo>&deg;</mo><mo>,</mo><mi>&nbsp;</mi><mi>C</mi><mi>A</mi><mo>=</mo><mn>5</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>m</mi><mi>&nbsp;</mi><mi>&nbsp;</mi><mi>a</mi><mi>n</mi><mi>d</mi><mi>&nbsp;</mi><mi>&nbsp;</mi><mi>A</mi><mi>B</mi><mo>=</mo><mn>7</mn><mi>c</mi><mi>m</mi><mo>.</mo></math><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">E and F are the midpoints of sides AC and AB respectively. The length of EF is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">5.</span><span style=\"font-family: Times New Roman;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math> </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306;,</span><span style=\"font-family: Baloo;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&ang;</mi><mi>A</mi><mo>=</mo><mn>135</mn><mo>&deg;</mo><mo>,</mo><mi>&nbsp;</mi><mi>C</mi><mi>A</mi><mo>=</mo><mn>5</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>m</mi></math>&#2324;&#2352; AB = 7cm &#2361;&#2376;&#2306;&#2404; E &#2324;&#2352; F &#2325;&#2381;&#2352;&#2350;&#2358;&#2307; AC &#2324;&#2352; AB &#2349;&#2369;&#2332;&#2366;&#2323;&#2306; &#2325;&#2375; &#2350;&#2343;&#2381;&#2351; &#2348;&#2367;&#2306;&#2342;&#2369; &#2361;&#2376;&#2306;&#2404; EF &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; &#2361;&#2376;:</span></p>\n",
                    options_en: ["<p>6.5</p>\n", "<p>5.5</p>\n", 
                                "<p>6</p>\n", "<p>5</p>\n"],
                    options_hi: ["<p>6.5</p>\n", "<p>5.5</p>\n",
                                "<p>6</p>\n", "<p>5</p>\n"],
                    solution_en: "<p>5.(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image6.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">Using Cosine rule:&nbsp;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mi>&nbsp;</mi><msup><mi>c</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mi>&nbsp;</mi><mi>a</mi></mrow><mn>2</mn></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mo>&nbsp;</mo><msup><mn>135</mn><mn>0</mn></msup><mo>=</mo><mfrac><mrow><msup><mn>7</mn><mn>2</mn></msup><mo>+</mo><mo>(</mo><mn>5</mn><msqrt><mn>2</mn></msqrt><msup><mo>)</mo><mn>2</mn></msup><mo>-</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></mrow><mrow><mn>2</mn><mo>&times;</mo><mn>7</mn><mo>&times;</mo><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac><mo>=</mo><mfrac><mrow><mn>49</mn><mo>+</mo><mn>50</mn><mo>-</mo><mi>B</mi><mi>C</mi><mo>&sup2;</mo></mrow><mrow><mn>70</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mi>B</mi><mi>C</mi><mo>&sup2;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>99</mn><mo>+</mo><mn>70</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>169</mn><mspace linebreak=\"newline\"></mspace><mi>B</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>13</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>E</mi><mi>F</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>13</mn><mn>2</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    solution_hi: "<p>5.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image5.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">Cosine </span><span style=\"font-family: Baloo;\">&#2344;&#2367;&#2351;&#2350; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2375;</span><span style=\"font-family: Times New Roman;\">,: </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mi>&nbsp;</mi><msup><mi>c</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mi>&nbsp;</mi><mi>a</mi></mrow><mn>2</mn></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mo>&nbsp;</mo><msup><mn>135</mn><mn>0</mn></msup><mo>=</mo><mfrac><mrow><msup><mn>7</mn><mn>2</mn></msup><mo>+</mo><mo>(</mo><mn>5</mn><msqrt><mn>2</mn></msqrt><msup><mo>)</mo><mn>2</mn></msup><mo>-</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></mrow><mrow><mn>2</mn><mo>&times;</mo><mn>7</mn><mo>&times;</mo><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac><mo>=</mo><mfrac><mrow><mn>49</mn><mo>+</mo><mn>50</mn><mo>-</mo><mi>B</mi><mi>C</mi><mo>&sup2;</mo></mrow><mrow><mn>70</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mi>B</mi><mi>C</mi><mo>&sup2;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>99</mn><mo>+</mo><mn>70</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>169</mn><mspace linebreak=\"newline\"></mspace><mi>B</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>13</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>E</mi><mi>F</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>13</mn><mn>2</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>5</mn></math></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>,</mo><mo>&nbsp;</mo><mi>&ang;</mi><mi>A</mi><mo>=</mo><mn>135</mn><mo>&deg;</mo><mo>,</mo><mi>&nbsp;</mi><mi>C</mi><mi>A</mi><mo>=</mo><mn>5</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>m</mi><mi>&nbsp;</mi><mi>&nbsp;</mi><mi>a</mi><mi>n</mi><mi>d</mi><mi>&nbsp;</mi><mi>&nbsp;</mi><mi>A</mi><mi>B</mi><mo>=</mo><mn>7</mn><mi>c</mi><mi>m</mi><mo>.</mo></math><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">E and F are the midpoints of sides AC and AB respectively. The length of EF is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">5.</span><span style=\"font-family: Times New Roman;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math> </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306;,</span><span style=\"font-family: Baloo;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&ang;</mi><mi>A</mi><mo>=</mo><mn>135</mn><mo>&deg;</mo><mo>,</mo><mi>&nbsp;</mi><mi>C</mi><mi>A</mi><mo>=</mo><mn>5</mn><msqrt><mn>2</mn></msqrt><mi>c</mi><mi>m</mi></math>&#2324;&#2352; AB = 7cm &#2361;&#2376;&#2306;&#2404; E &#2324;&#2352; F &#2325;&#2381;&#2352;&#2350;&#2358;&#2307; AC &#2324;&#2352; AB &#2349;&#2369;&#2332;&#2366;&#2323;&#2306; &#2325;&#2375; &#2350;&#2343;&#2381;&#2351; &#2348;&#2367;&#2306;&#2342;&#2369; &#2361;&#2376;&#2306;&#2404; EF &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; &#2361;&#2376;:</span></p>\n",
                    options_en: ["<p>6.5</p>\n", "<p>5.5</p>\n", 
                                "<p>6</p>\n", "<p>5</p>\n"],
                    options_hi: ["<p>6.5</p>\n", "<p>5.5</p>\n",
                                "<p>6</p>\n", "<p>5</p>\n"],
                    solution_en: "<p>5.(a)<span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image6.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">Using Cosine rule:&nbsp;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mi>&nbsp;</mi><msup><mi>c</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mi>&nbsp;</mi><mi>a</mi></mrow><mn>2</mn></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mo>&nbsp;</mo><msup><mn>135</mn><mn>0</mn></msup><mo>=</mo><mfrac><mrow><msup><mn>7</mn><mn>2</mn></msup><mo>+</mo><mo>(</mo><mn>5</mn><msqrt><mn>2</mn></msqrt><msup><mo>)</mo><mn>2</mn></msup><mo>-</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></mrow><mrow><mn>2</mn><mo>&times;</mo><mn>7</mn><mo>&times;</mo><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac><mo>=</mo><mfrac><mrow><mn>49</mn><mo>+</mo><mn>50</mn><mo>-</mo><mi>B</mi><mi>C</mi><mo>&sup2;</mo></mrow><mrow><mn>70</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mi>B</mi><mi>C</mi><mo>&sup2;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>99</mn><mo>+</mo><mn>70</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>169</mn><mspace linebreak=\"newline\"></mspace><mi>B</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>13</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>E</mi><mi>F</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>13</mn><mn>2</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    solution_hi: "<p>5.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image5.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">Cosine </span><span style=\"font-family: Baloo;\">&#2344;&#2367;&#2351;&#2350; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2375;</span><span style=\"font-family: Times New Roman;\">,: </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mi>&nbsp;</mi><msup><mi>c</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mi>&nbsp;</mi><mi>a</mi></mrow><mn>2</mn></msup></mrow><mrow><mn>2</mn><mi>b</mi><mi>c</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mo>&nbsp;</mo><msup><mn>135</mn><mn>0</mn></msup><mo>=</mo><mfrac><mrow><msup><mn>7</mn><mn>2</mn></msup><mo>+</mo><mo>(</mo><mn>5</mn><msqrt><mn>2</mn></msqrt><msup><mo>)</mo><mn>2</mn></msup><mo>-</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></mrow><mrow><mn>2</mn><mo>&times;</mo><mn>7</mn><mo>&times;</mo><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac><mo>=</mo><mfrac><mrow><mn>49</mn><mo>+</mo><mn>50</mn><mo>-</mo><mi>B</mi><mi>C</mi><mo>&sup2;</mo></mrow><mrow><mn>70</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mi>B</mi><mi>C</mi><mo>&sup2;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>99</mn><mo>+</mo><mn>70</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>169</mn><mspace linebreak=\"newline\"></mspace><mi>B</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>13</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>E</mi><mi>F</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>13</mn><mn>2</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>5</mn></math></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Times New Roman;\">Study the given graph and answer the question that follows.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image4.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">What is the ratio of the total exports in 2014 and 2017 to the total imports in 2015 and 2018?</span></p>",
                    question_hi: " <p><span style=\"font-family:Times New Roman\">6.</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Baloo\">दिए गए ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image16.png\"/></p> <p><span style=\"font-family:Baloo\">2014 और 2017 में कुल निर्यात का 2015 और 2018 में कुल आयात से अनुपात कितना है?</span></p>",
                    options_en: ["<p>14:15</p>", "<p>3:2</p>", 
                                "<p>6:7</p>", "<p>5:6</p>"],
                    options_hi: [" <p> 14:15</span></p>", " <p> 3:2</span></p>",
                                " <p> 6:7</span></p>", " <p> 5:6</span></p>"],
                    solution_en: "<p>6.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Total exports in 2014 and 2017 = 300+420 = 720</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total imports in 2015 and 2018 = 390+450 = 840</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required ratio = 720 : 840 = 6 : 7</span></p>",
                    solution_hi: " <p>6.(c)</span></p> <p><span style=\"font-family:Baloo\">2014 और 2017 में कुल निर्यात</span><span style=\"font-family:Times New Roman\"> = 300+420 = 720</span></p> <p><span style=\"font-family:Baloo\">2015 और 2018 में कुल आयात</span><span style=\"font-family:Times New Roman\"> = 390+450 = 840</span></p> <p><span style=\"font-family:Baloo\">आवश्यक अनुपात</span><span style=\"font-family:Times New Roman\">= 720 : 840 = 6 : 7</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Times New Roman;\">Study the given graph and answer the question that follows.</span></p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image4.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">What is the ratio of the total exports in 2014 and 2017 to the total imports in 2015 and 2018?</span></p>",
                    question_hi: " <p><span style=\"font-family:Times New Roman\">6.</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Baloo\">दिए गए ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image16.png\"/></p> <p><span style=\"font-family:Baloo\">2014 और 2017 में कुल निर्यात का 2015 और 2018 में कुल आयात से अनुपात कितना है?</span></p>",
                    options_en: ["<p>14:15</p>", "<p>3:2</p>", 
                                "<p>6:7</p>", "<p>5:6</p>"],
                    options_hi: [" <p> 14:15</span></p>", " <p> 3:2</span></p>",
                                " <p> 6:7</span></p>", " <p> 5:6</span></p>"],
                    solution_en: "<p>6.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Total exports in 2014 and 2017 = 300+420 = 720</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Total imports in 2015 and 2018 = 390+450 = 840</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required ratio = 720 : 840 = 6 : 7</span></p>",
                    solution_hi: " <p>6.(c)</span></p> <p><span style=\"font-family:Baloo\">2014 और 2017 में कुल निर्यात</span><span style=\"font-family:Times New Roman\"> = 300+420 = 720</span></p> <p><span style=\"font-family:Baloo\">2015 और 2018 में कुल आयात</span><span style=\"font-family:Times New Roman\"> = 390+450 = 840</span></p> <p><span style=\"font-family:Baloo\">आवश्यक अनुपात</span><span style=\"font-family:Times New Roman\">= 720 : 840 = 6 : 7</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Times New Roman;\"> If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mi>x</mi><mo>+</mo><mn>1</mn><mo>=</mo><mn>0</mn><mo>,</mo></math> </span><span style=\"font-family: Times New Roman;\">then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">will be:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">7. </span><span style=\"font-family: Baloo;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mi>x</mi><mo>+</mo><mn>1</mn><mo>=</mo><mn>0</mn><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2361;&#2376; &#2340;&#2379; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2361;&#2376; |</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>234</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>216</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>666</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>630</mn><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>234</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>216</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>666</mn><msqrt><mn>3</mn></msqrt></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>630</mn><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    solution_en: "<p>7.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mi>x</mi><mo>+</mo><mn>1</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>a</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>3</mn></msup><mo>&minus;</mo><mn>3</mn><mo>(</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mo>&nbsp;</mo><mn>630</mn><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    solution_hi: "<p>7.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mi>x</mi><mo>+</mo><mn>1</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>a</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>3</mn></msup><mo>&minus;</mo><mn>3</mn><mo>(</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mo>&nbsp;</mo><mn>630</mn><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Times New Roman;\"> If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mi>x</mi><mo>+</mo><mn>1</mn><mo>=</mo><mn>0</mn><mo>,</mo></math> </span><span style=\"font-family: Times New Roman;\">then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">will be:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">7. </span><span style=\"font-family: Baloo;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mi>x</mi><mo>+</mo><mn>1</mn><mo>=</mo><mn>0</mn><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2361;&#2376; &#2340;&#2379; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2361;&#2376; |</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>234</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>216</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>666</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>630</mn><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>234</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>216</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>666</mn><msqrt><mn>3</mn></msqrt></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>630</mn><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    solution_en: "<p>7.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mi>x</mi><mo>+</mo><mn>1</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>a</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>3</mn></msup><mo>&minus;</mo><mn>3</mn><mo>(</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mo>&nbsp;</mo><mn>630</mn><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    solution_hi: "<p>7.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mi>x</mi><mo>+</mo><mn>1</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>a</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>3</mn></msup><mo>&minus;</mo><mn>3</mn><mo>(</mo><mn>6</mn><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mo>&nbsp;</mo><mn>630</mn><msqrt><mn>3</mn></msqrt><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Times New Roman;\"> A car covers a distance of 48 km at a speed of 40 kmph and another 52 km with a speed of 65 kmph. What is the average speed of the car (in kmph) for the total distance covered?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">8.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक कार 48 किमी की दूरी 40 किमी प्रति घंटे की गति से और 52 किमी की दूरी 65 किमी प्रति घंटे की गति से तय करती है। तय की गई कुल दूरी के लिए कार की औसत गति (किमी प्रति घंटे में) क्या है?</span></p>",
                    options_en: ["<p>52.5 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>50</p>", 
                                "<p>52<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>53</p>"],
                    options_hi: ["<p>52.5 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>50</p>",
                                "<p>52<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>53</p>"],
                    solution_en: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Average speed = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>48</mn><mo>+</mo><mn>52</mn></mrow><mrow><mfrac><mn>48</mn><mn>40</mn></mfrac><mo>+</mo><mfrac><mn>52</mn><mn>65</mn></mfrac></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 50 kmph</span></p>",
                    solution_hi: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Baloo;\">औसत चाल</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>48</mn><mo>+</mo><mn>52</mn></mrow><mrow><mfrac><mn>48</mn><mn>40</mn></mfrac><mo>+</mo><mfrac><mn>52</mn><mn>65</mn></mfrac></mrow></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 50 </span><span style=\"font-family: Baloo;\">किमी/घंटा</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Times New Roman;\"> A car covers a distance of 48 km at a speed of 40 kmph and another 52 km with a speed of 65 kmph. What is the average speed of the car (in kmph) for the total distance covered?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">8.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक कार 48 किमी की दूरी 40 किमी प्रति घंटे की गति से और 52 किमी की दूरी 65 किमी प्रति घंटे की गति से तय करती है। तय की गई कुल दूरी के लिए कार की औसत गति (किमी प्रति घंटे में) क्या है?</span></p>",
                    options_en: ["<p>52.5 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>50</p>", 
                                "<p>52<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>53</p>"],
                    options_hi: ["<p>52.5 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>50</p>",
                                "<p>52<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>53</p>"],
                    solution_en: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Average speed = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>48</mn><mo>+</mo><mn>52</mn></mrow><mrow><mfrac><mn>48</mn><mn>40</mn></mfrac><mo>+</mo><mfrac><mn>52</mn><mn>65</mn></mfrac></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 50 kmph</span></p>",
                    solution_hi: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Baloo;\">औसत चाल</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>48</mn><mo>+</mo><mn>52</mn></mrow><mrow><mfrac><mn>48</mn><mn>40</mn></mfrac><mo>+</mo><mfrac><mn>52</mn><mn>65</mn></mfrac></mrow></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 50 </span><span style=\"font-family: Baloo;\">किमी/घंटा</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Times New Roman;\"> Simplify the following expression.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>18</mn><mi>x</mi><mi>y</mi><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></math></span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">9.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2357;&#2381;&#2351;&#2306;&#2332;&#2325; &#2325;&#2379; &#2360;&#2352;&#2354; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>18</mn><mi>x</mi><mi>y</mi><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></math></span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><msup><mrow><mn>36</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi><mo>-</mo><mn>54</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><msup><mrow><mn>72</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi><mo>+</mo><mn>108</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>+</mo><mn>108</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mn>72</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><msup><mrow><mn>36</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi><mo>-</mo><mn>54</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><msup><mrow><mn>72</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi><mo>+</mo><mn>108</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>+</mo><mn>108</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mn>72</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi></math></p>\n"],
                    solution_en: "<p>9.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>18</mn><mi>x</mi><mi>y</mi><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">=8x</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 27y</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 36x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 54xy</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\"> - 36x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 54xy&sup2;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=8x</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 27y</span><span style=\"font-family: Times New Roman;\">&sup3; </span><span style=\"font-family: Times New Roman;\">- 72x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 108xy</span><span style=\"font-family: Times New Roman;\">&sup2;</span></p>\n",
                    solution_hi: "<p>9.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>18</mn><mi>x</mi><mi>y</mi><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 8x</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 27y</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 36x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 54xy</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\"> - </span><span style=\"font-family: Times New Roman;\">36x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 54xy</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 8x</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 27y</span><span style=\"font-family: Times New Roman;\">&sup3; </span><span style=\"font-family: Times New Roman;\">- 72x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 108xy</span><span style=\"font-family: Times New Roman;\">&sup2;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Times New Roman;\"> Simplify the following expression.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>18</mn><mi>x</mi><mi>y</mi><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></math></span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">9.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2357;&#2381;&#2351;&#2306;&#2332;&#2325; &#2325;&#2379; &#2360;&#2352;&#2354; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>18</mn><mi>x</mi><mi>y</mi><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></math></span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><msup><mrow><mn>36</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi><mo>-</mo><mn>54</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><msup><mrow><mn>72</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi><mo>+</mo><mn>108</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>+</mo><mn>108</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mn>72</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><msup><mrow><mn>36</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi><mo>-</mo><mn>54</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup><mo>-</mo><msup><mrow><mn>72</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi><mo>+</mo><mn>108</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>-</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mn>8</mn><mi>x</mi></mrow><mn>3</mn></msup><mo>+</mo><mn>108</mn><mi>x</mi><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mn>72</mn><mi>x</mi></mrow><mn>2</mn></msup><mi>y</mi></math></p>\n"],
                    solution_en: "<p>9.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>18</mn><mi>x</mi><mi>y</mi><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">=8x</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 27y</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 36x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 54xy</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\"> - 36x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 54xy&sup2;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=8x</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 27y</span><span style=\"font-family: Times New Roman;\">&sup3; </span><span style=\"font-family: Times New Roman;\">- 72x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 108xy</span><span style=\"font-family: Times New Roman;\">&sup2;</span></p>\n",
                    solution_hi: "<p>9.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>18</mn><mi>x</mi><mi>y</mi><mo>(</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>3</mn><mi>y</mi><mo>)</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 8x</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 27y</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 36x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 54xy</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\"> - </span><span style=\"font-family: Times New Roman;\">36x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 54xy</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 8x</span><span style=\"font-family: Times New Roman;\">&sup3;</span><span style=\"font-family: Times New Roman;\"> - 27y</span><span style=\"font-family: Times New Roman;\">&sup3; </span><span style=\"font-family: Times New Roman;\">- 72x</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">y + 108xy</span><span style=\"font-family: Times New Roman;\">&sup2;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. <span style=\"font-family: Times New Roman;\">Anamika paid Rs. 4965 as compound interest on a loan of Rs. 15000 after 3 years when compounded annually. Suman took a loan of Rs. 10000 at the same rate on simple interest. How much interest did Suman pay after 3 years?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">10.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">अनामिका ने रु. 4965 रुपये चक्रवृद्धि ब्याज के रूप में, 15000 के ऋण पर 3 साल के बाद (जब सालाना चक्रवृद्धि होती है), अदा करती है । सुमन ने 10,000 रुपये का कर्ज उसी दर पर साधारण ब्याज पर लिया। सुमन ने 3 वर्ष बाद कितना ब्याज अदा किया?</span></p>",
                    options_en: ["<p>4500</p>", "<p>4000</p>", 
                                "<p>3000</p>", "<p>3500</p>"],
                    options_hi: ["<p>4500</p>", "<p>4000</p>",
                                "<p>3000</p>", "<p>3500</p>"],
                    solution_en: "<p>10.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Total amount = 15000 + 4965 = 19965</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mfrac><mn>19965</mn><mn>15000</mn></mfrac><mn>3</mn></mroot><mo>&#160;</mo><mo>=</mo><mroot><mfrac><mn>19965</mn><mn>15000</mn></mfrac><mn>3</mn></mroot><mo>&#160;</mo><mo>=</mo><mfrac><mn>11</mn><mn>10</mn></mfrac><mspace linebreak=\"newline\"/></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">It means, Rate percent = 10%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Interest =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10,000</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 3000</span></p>",
                    solution_hi: "<p>10.(c)</p>\r\n<p><span style=\"font-family: Baloo;\">कुल राशि</span><span style=\"font-family: Times New Roman;\"> = 15000 + 4965 = 19965</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mfrac><mn>19965</mn><mn>15000</mn></mfrac><mn>3</mn></mroot><mo>&#160;</mo><mo>=</mo><mroot><mfrac><mn>19965</mn><mn>15000</mn></mfrac><mn>3</mn></mroot><mo>&#160;</mo><mo>=</mo><mfrac><mn>11</mn><mn>10</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">दर </span><span style=\"font-family: Times New Roman;\">%= 10%</span></p>\r\n<p><span style=\"font-family: Baloo;\">ब्याज </span><span style=\"font-family: Times New Roman;\">=</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10,000</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 3000</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. <span style=\"font-family: Times New Roman;\">Anamika paid Rs. 4965 as compound interest on a loan of Rs. 15000 after 3 years when compounded annually. Suman took a loan of Rs. 10000 at the same rate on simple interest. How much interest did Suman pay after 3 years?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">10.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">अनामिका ने रु. 4965 रुपये चक्रवृद्धि ब्याज के रूप में, 15000 के ऋण पर 3 साल के बाद (जब सालाना चक्रवृद्धि होती है), अदा करती है । सुमन ने 10,000 रुपये का कर्ज उसी दर पर साधारण ब्याज पर लिया। सुमन ने 3 वर्ष बाद कितना ब्याज अदा किया?</span></p>",
                    options_en: ["<p>4500</p>", "<p>4000</p>", 
                                "<p>3000</p>", "<p>3500</p>"],
                    options_hi: ["<p>4500</p>", "<p>4000</p>",
                                "<p>3000</p>", "<p>3500</p>"],
                    solution_en: "<p>10.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Total amount = 15000 + 4965 = 19965</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mfrac><mn>19965</mn><mn>15000</mn></mfrac><mn>3</mn></mroot><mo>&#160;</mo><mo>=</mo><mroot><mfrac><mn>19965</mn><mn>15000</mn></mfrac><mn>3</mn></mroot><mo>&#160;</mo><mo>=</mo><mfrac><mn>11</mn><mn>10</mn></mfrac><mspace linebreak=\"newline\"/></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">It means, Rate percent = 10%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Interest =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10,000</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 3000</span></p>",
                    solution_hi: "<p>10.(c)</p>\r\n<p><span style=\"font-family: Baloo;\">कुल राशि</span><span style=\"font-family: Times New Roman;\"> = 15000 + 4965 = 19965</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mfrac><mn>19965</mn><mn>15000</mn></mfrac><mn>3</mn></mroot><mo>&#160;</mo><mo>=</mo><mroot><mfrac><mn>19965</mn><mn>15000</mn></mfrac><mn>3</mn></mroot><mo>&#160;</mo><mo>=</mo><mfrac><mn>11</mn><mn>10</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">दर </span><span style=\"font-family: Times New Roman;\">%= 10%</span></p>\r\n<p><span style=\"font-family: Baloo;\">ब्याज </span><span style=\"font-family: Times New Roman;\">=</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10,000</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 3000</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Times New Roman;\"> Study the given graph and answer the question that follows.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_19120681411676020540896.png\" width=\"398\" height=\"249\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">In which year was the percentage increase in the revenue as compared to that in its preceding year below 6% ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">11</span><span style=\"font-family: Times New Roman;\">. </span><span style=\"font-family: Baloo;\">&#2342;&#2367;&#2319; &#2327;&#2319; &#2327;&#2381;&#2352;&#2366;&#2347; &#2325;&#2366; &#2309;&#2343;&#2381;&#2351;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2324;&#2352; &#2344;&#2368;&#2330;&#2375; &#2342;&#2367;&#2319; &#2327;&#2319; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2366; &#2313;&#2340;&#2381;&#2340;&#2352; &#2342;&#2375;&#2306;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_19120681411676020540896.png\" width=\"398\" height=\"249\"></p>\r\n<p><span style=\"font-family: Baloo;\">&#2346;&#2367;&#2331;&#2354;&#2375; &#2357;&#2352;&#2381;&#2359; &#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2350;&#2375;&#2306; &#2325;&#2367;&#2360; &#2357;&#2352;&#2381;&#2359; &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2357;&#2371;&#2342;&#2381;&#2343;&#2367; 6% &#2360;&#2375; &#2325;&#2350; &#2341;&#2368;?</span></p>\n",
                    options_en: ["<p>2015</p>\n", "<p>2019</p>\n", 
                                "<p>2016</p>\n", "<p>2017</p>\n"],
                    options_hi: ["<p>2015</p>\n", "<p>2019</p>\n",
                                "<p>2016</p>\n", "<p>2017</p>\n"],
                    solution_en: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">In 2015 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>350</mn><mo>-</mo><mn>320</mn></mrow><mn>320</mn></mfrac><mo>&times;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\">= 9.375%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In 2019 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>-</mo><mn>280</mn></mrow><mn>280</mn></mfrac><mo>&times;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\">= 7.14%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In 2016 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>380</mn><mo>-</mo><mn>350</mn></mrow><mn>350</mn></mfrac><mo>&times;</mo><mn>100</mn><mi>&nbsp;</mi><mo>=</mo><mn>8.57</mn><mi>%</mi><mi>&nbsp;</mi></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In 2017 =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>400</mn><mo>-</mo><mn>380</mn></mrow><mn>380</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>52</mn><mo>.</mo><mn>6</mn><mo>%</mo></math></span><span style=\"font-family: Times New Roman;\">(satisfy)</span></p>\n",
                    solution_hi: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">2015 </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>350</mn><mo>-</mo><mn>320</mn></mrow><mn>320</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Times New Roman;\">= 9.375%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2019 </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306; </span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>-</mo><mn>280</mn></mrow><mn>280</mn></mfrac><mo>&times;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\">= 7.14%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2016</span><span style=\"font-family: Baloo;\"> &#2350;&#2375;&#2306;</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>380</mn><mo>-</mo><mn>350</mn></mrow><mn>350</mn></mfrac><mo>&times;</mo><mn>100</mn><mi>&nbsp;</mi><mo>=</mo><mn>8.57</mn><mi>%</mi><mi>&nbsp;</mi></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2017 </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306; </span><span style=\"font-family: Times New Roman;\">=&nbsp;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>400</mn><mo>-</mo><mn>380</mn></mrow><mn>380</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>5</mn><mo>.</mo><mn>26</mn><mo>%</mo></math></span><span style=\"font-family: Times New Roman;\">(</span><span style=\"font-family: Baloo;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335; &#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Times New Roman;\">)</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Times New Roman;\"> Study the given graph and answer the question that follows.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_19120681411676020540896.png\" width=\"398\" height=\"249\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">In which year was the percentage increase in the revenue as compared to that in its preceding year below 6% ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">11</span><span style=\"font-family: Times New Roman;\">. </span><span style=\"font-family: Baloo;\">&#2342;&#2367;&#2319; &#2327;&#2319; &#2327;&#2381;&#2352;&#2366;&#2347; &#2325;&#2366; &#2309;&#2343;&#2381;&#2351;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2324;&#2352; &#2344;&#2368;&#2330;&#2375; &#2342;&#2367;&#2319; &#2327;&#2319; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2366; &#2313;&#2340;&#2381;&#2340;&#2352; &#2342;&#2375;&#2306;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_19120681411676020540896.png\" width=\"398\" height=\"249\"></p>\r\n<p><span style=\"font-family: Baloo;\">&#2346;&#2367;&#2331;&#2354;&#2375; &#2357;&#2352;&#2381;&#2359; &#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2350;&#2375;&#2306; &#2325;&#2367;&#2360; &#2357;&#2352;&#2381;&#2359; &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2357;&#2371;&#2342;&#2381;&#2343;&#2367; 6% &#2360;&#2375; &#2325;&#2350; &#2341;&#2368;?</span></p>\n",
                    options_en: ["<p>2015</p>\n", "<p>2019</p>\n", 
                                "<p>2016</p>\n", "<p>2017</p>\n"],
                    options_hi: ["<p>2015</p>\n", "<p>2019</p>\n",
                                "<p>2016</p>\n", "<p>2017</p>\n"],
                    solution_en: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">In 2015 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>350</mn><mo>-</mo><mn>320</mn></mrow><mn>320</mn></mfrac><mo>&times;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\">= 9.375%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In 2019 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>-</mo><mn>280</mn></mrow><mn>280</mn></mfrac><mo>&times;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\">= 7.14%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In 2016 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>380</mn><mo>-</mo><mn>350</mn></mrow><mn>350</mn></mfrac><mo>&times;</mo><mn>100</mn><mi>&nbsp;</mi><mo>=</mo><mn>8.57</mn><mi>%</mi><mi>&nbsp;</mi></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">In 2017 =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>400</mn><mo>-</mo><mn>380</mn></mrow><mn>380</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>52</mn><mo>.</mo><mn>6</mn><mo>%</mo></math></span><span style=\"font-family: Times New Roman;\">(satisfy)</span></p>\n",
                    solution_hi: "<p>11.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">2015 </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>350</mn><mo>-</mo><mn>320</mn></mrow><mn>320</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Times New Roman;\">= 9.375%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2019 </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306; </span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>300</mn><mo>-</mo><mn>280</mn></mrow><mn>280</mn></mfrac><mo>&times;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\">= 7.14%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2016</span><span style=\"font-family: Baloo;\"> &#2350;&#2375;&#2306;</span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>380</mn><mo>-</mo><mn>350</mn></mrow><mn>350</mn></mfrac><mo>&times;</mo><mn>100</mn><mi>&nbsp;</mi><mo>=</mo><mn>8.57</mn><mi>%</mi><mi>&nbsp;</mi></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2017 </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306; </span><span style=\"font-family: Times New Roman;\">=&nbsp;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>400</mn><mo>-</mo><mn>380</mn></mrow><mn>380</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>5</mn><mo>.</mo><mn>26</mn><mo>%</mo></math></span><span style=\"font-family: Times New Roman;\">(</span><span style=\"font-family: Baloo;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335; &#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Times New Roman;\">)</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Times New Roman;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mo>,</mo><mi>&nbsp;</mi><mn>0</mn><mo>&deg;</mo><mo>&lt;</mo><mi>&theta;</mi><mo>&lt;</mo><mn>90</mn><mo>&deg;</mo><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\">then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi></math></span><span style=\"font-family: Times New Roman;\"> will be:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">12.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mo>,</mo><mi>&nbsp;</mi><mn>0</mn><mo>&deg;</mo><mo>&lt;</mo><mi>&theta;</mi><mo>&lt;</mo><mn>90</mn><mo>&deg;</mo><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2361;&#2376; &#2340;&#2379;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2361;&#2376;: </span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>305</mn><mn>144</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>431</mn><mn>144</mn></mfrac></math> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>9</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>153</mn><mn>72</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>305</mn><mn>144</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>431</mn><mn>144</mn></mfrac></math> </span></p>\n",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>9</mn></mfrac></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>153</mn><mn>72</mn></mfrac></math></p>\n"],
                    solution_en: "<p>12.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mo>(</mo><mn>1</mn><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>)</mo><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>-</mo><mn>6</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>+</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>(</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mi>&nbsp;</mi><mi>&nbsp;</mi><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>r</mi><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><mi>n</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mo>+</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mo>-</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mo>=</mo><mfrac><mn>305</mn><mn>144</mn></mfrac><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    solution_hi: "<p>12.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mo>(</mo><mn>1</mn><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>)</mo><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>-</mo><mn>6</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>+</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>(</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mi>&nbsp;</mi><mi>&nbsp;</mi><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>r</mi><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><mi>n</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mo>+</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mo>-</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mo>=</mo><mfrac><mn>305</mn><mn>144</mn></mfrac><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Times New Roman;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mo>,</mo><mi>&nbsp;</mi><mn>0</mn><mo>&deg;</mo><mo>&lt;</mo><mi>&theta;</mi><mo>&lt;</mo><mn>90</mn><mo>&deg;</mo><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\">then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi></math></span><span style=\"font-family: Times New Roman;\"> will be:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">12.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mo>,</mo><mi>&nbsp;</mi><mn>0</mn><mo>&deg;</mo><mo>&lt;</mo><mi>&theta;</mi><mo>&lt;</mo><mn>90</mn><mo>&deg;</mo><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2361;&#2376; &#2340;&#2379;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2361;&#2376;: </span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>305</mn><mn>144</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>431</mn><mn>144</mn></mfrac></math> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>9</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>153</mn><mn>72</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>305</mn><mn>144</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>431</mn><mn>144</mn></mfrac></math> </span></p>\n",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>9</mn></mfrac></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>153</mn><mn>72</mn></mfrac></math></p>\n"],
                    solution_en: "<p>12.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mo>(</mo><mn>1</mn><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>)</mo><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>-</mo><mn>6</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>+</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>(</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mi>&nbsp;</mi><mi>&nbsp;</mi><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>r</mi><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><mi>n</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mo>+</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mo>-</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mo>=</mo><mfrac><mn>305</mn><mn>144</mn></mfrac><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    solution_hi: "<p>12.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mo>(</mo><mn>1</mn><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>)</mo><mo>+</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>-</mo><mn>6</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>+</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>(</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>-</mo><mn>3</mn><mo>)</mo><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mi>&nbsp;</mi><mi>&nbsp;</mi><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>r</mi><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>c</mi><mi>o</mi><mi>s</mi><mi>&theta;</mi><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><mi>n</mi><mo>&sup2;</mo><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>t</mi><mi>a</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mo>+</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mo>-</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mo>=</mo><mfrac><mn>305</mn><mn>144</mn></mfrac><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Times New Roman;\"> Chord AB and diameter CD of a circle meet at the point P, outside the circle when produced. If PB=8 cm, AB= 12 cm and the distance of P from the centre of the circle is 18 cm, the radius of the circle is closest to:</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">13.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक वृत्त की जीवा AB और व्यास CD, को जब बढ़ाया जाता है तो वृत्त के बाहर बिंदु P पर मिलते हैं। यदि PB = 8 सेमी, AB = 12 सेमी और वृत्त के केंद्र से P की दूरी 18 सेमी है, तो वृत्त की त्रिज्या निकटतम है:</span></p>",
                    options_en: ["<p>12.8</p>", "<p>12.4</p>", 
                                "<p>13</p>", "<p>12</p>"],
                    options_hi: ["<p>12.8</p>", "<p>12.4</p>",
                                "<p>13</p>", "<p>12</p>"],
                    solution_en: "<p>13.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image8.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">Make a tangent from P to T</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">PT&sup2; = PB &times; PA = 8 &times; 20</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">PT =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>160</mn></msqrt></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OP</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\">= OT</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> + PT</span><span style=\"font-family: Times New Roman;\">&sup2; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">18</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> = OT</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> + 160</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OT = 12.8</span></p>",
                    solution_hi: "<p>13.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image7.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">P </span><span style=\"font-family: Baloo;\">से</span><span style=\"font-family: Times New Roman;\"> T </span><span style=\"font-family: Baloo;\">पर स्पर्श रेखा बनाइए</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">PT&sup2; = PB &times; PA = 8 &times; 20</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">PT =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>160</mn></msqrt></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OP</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\">= OT</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> + PT</span><span style=\"font-family: Times New Roman;\">&sup2; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">18</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> = OT</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> + 160</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OT = 12.8</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Times New Roman;\"> Chord AB and diameter CD of a circle meet at the point P, outside the circle when produced. If PB=8 cm, AB= 12 cm and the distance of P from the centre of the circle is 18 cm, the radius of the circle is closest to:</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">13.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक वृत्त की जीवा AB और व्यास CD, को जब बढ़ाया जाता है तो वृत्त के बाहर बिंदु P पर मिलते हैं। यदि PB = 8 सेमी, AB = 12 सेमी और वृत्त के केंद्र से P की दूरी 18 सेमी है, तो वृत्त की त्रिज्या निकटतम है:</span></p>",
                    options_en: ["<p>12.8</p>", "<p>12.4</p>", 
                                "<p>13</p>", "<p>12</p>"],
                    options_hi: ["<p>12.8</p>", "<p>12.4</p>",
                                "<p>13</p>", "<p>12</p>"],
                    solution_en: "<p>13.(a)</p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image8.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">Make a tangent from P to T</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">PT&sup2; = PB &times; PA = 8 &times; 20</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">PT =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>160</mn></msqrt></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OP</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\">= OT</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> + PT</span><span style=\"font-family: Times New Roman;\">&sup2; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">18</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> = OT</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> + 160</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OT = 12.8</span></p>",
                    solution_hi: "<p>13.(a)</p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image7.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">P </span><span style=\"font-family: Baloo;\">से</span><span style=\"font-family: Times New Roman;\"> T </span><span style=\"font-family: Baloo;\">पर स्पर्श रेखा बनाइए</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">PT&sup2; = PB &times; PA = 8 &times; 20</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">PT =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>160</mn></msqrt></math> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OP</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\">= OT</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> + PT</span><span style=\"font-family: Times New Roman;\">&sup2; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">18</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> = OT</span><span style=\"font-family: Times New Roman;\">&sup2; </span><span style=\"font-family: Times New Roman;\"> + 160</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">OT = 12.8</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Times New Roman;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">=117 and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></math></span><span style=\"font-family: Times New Roman;\">, then the value of (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">) will be:</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">14.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">=117 </span><span style=\"font-family: Baloo;\">&#2324;&#2352;</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></math> &#2361;&#2376; &#2340;&#2379; </span><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup></math> &#2325;&#2366; &#2350;&#2366;&#2344; &#2361;&#2376;: </span></p>\n",
                    options_en: ["<p>9<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>12 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>13<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p>9<span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n",
                                "<p>12 <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>13<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    solution_en: "<p>14.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mn>117</mn><mspace linebreak=\"newline\"/><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mi>&#160;</mi><mn>12</mn><mi>&#160;</mi><mo>+</mo><mn>3</mn><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>(</mo><mi>i</mi><mo>)</mo><mspace linebreak=\"newline\"/><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><msup><mrow><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>)</mo><msup><mrow><mi>&#160;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mspace linebreak=\"newline\"/><mn>117</mn><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><msup><mrow><mi>&#160;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mspace linebreak=\"newline\"/><msup><mrow><mi>&#160;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mo>=</mo><mfrac><mn>39</mn><mrow><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow></mfrac><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mi>&#160;</mi><mn>12</mn><mi>&#160;</mi><mo>-</mo><mn>3</mn><msqrt><mn>3</mn></msqrt><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>(</mo><mi>i</mi><mi>i</mi><mo>)</mo><mspace linebreak=\"newline\"/><mi>O</mi><mi>n</mi><mo>&#160;</mo><mi>a</mi><mi>d</mi><mi>d</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&#160;</mo><mo>(</mo><mi>i</mi><mo>)</mo><mo>&#160;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&#160;</mo><mo>(</mo><mi>i</mi><mi>i</mi><mo>)</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>12</mn></math></p>",
                    solution_hi: "<p>14.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mn>117</mn><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mi>&nbsp;</mi><mn>12</mn><mi>&nbsp;</mi><mo>+</mo><mn>3</mn><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>(</mo><mi>i</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><msup><mrow><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>)</mo><msup><mrow><mi>&nbsp;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mn>117</mn><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><msup><mrow><mi>&nbsp;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><msup><mrow><mi>&nbsp;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mo>=</mo><mfrac><mn>39</mn><mrow><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow></mfrac><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mi>&nbsp;</mi><mn>12</mn><mi>&nbsp;</mi><mo>-</mo><mn>3</mn><msqrt><mn>3</mn></msqrt><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>(</mo><mi>i</mi><mi>i</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mi>O</mi><mi>n</mi><mo>&nbsp;</mo><mi>a</mi><mi>d</mi><mi>d</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mo>(</mo><mi>i</mi><mo>)</mo><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mo>(</mo><mi>i</mi><mi>i</mi><mo>)</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Times New Roman;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">=117 and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></math></span><span style=\"font-family: Times New Roman;\">, then the value of (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">) will be:</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">14.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup></math></span><span style=\"font-family: Times New Roman;\">=117 </span><span style=\"font-family: Baloo;\">&#2324;&#2352;</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></math> &#2361;&#2376; &#2340;&#2379; </span><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup></math> &#2325;&#2366; &#2350;&#2366;&#2344; &#2361;&#2376;: </span></p>\n",
                    options_en: ["<p>9<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>12 <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>13<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p>9<span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>\n",
                                "<p>12 <span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p>13<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    solution_en: "<p>14.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mn>117</mn><mspace linebreak=\"newline\"/><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mi>&#160;</mi><mn>12</mn><mi>&#160;</mi><mo>+</mo><mn>3</mn><msqrt><mn>3</mn></msqrt><mo>&#160;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>(</mo><mi>i</mi><mo>)</mo><mspace linebreak=\"newline\"/><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><msup><mrow><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>)</mo><msup><mrow><mi>&#160;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mspace linebreak=\"newline\"/><mn>117</mn><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><msup><mrow><mi>&#160;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mspace linebreak=\"newline\"/><msup><mrow><mi>&#160;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mo>=</mo><mfrac><mn>39</mn><mrow><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow></mfrac><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mi>&#160;</mi><mn>12</mn><mi>&#160;</mi><mo>-</mo><mn>3</mn><msqrt><mn>3</mn></msqrt><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>(</mo><mi>i</mi><mi>i</mi><mo>)</mo><mspace linebreak=\"newline\"/><mi>O</mi><mi>n</mi><mo>&#160;</mo><mi>a</mi><mi>d</mi><mi>d</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&#160;</mo><mo>(</mo><mi>i</mi><mo>)</mo><mo>&#160;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&#160;</mo><mo>(</mo><mi>i</mi><mi>i</mi><mo>)</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><msup><mi>x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>12</mn></math></p>",
                    solution_hi: "<p>14.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mn>117</mn><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mi>&nbsp;</mi><mn>12</mn><mi>&nbsp;</mi><mo>+</mo><mn>3</mn><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>(</mo><mi>i</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><msup><mi>y</mi><mn>4</mn></msup><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup><msup><mi>y</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><msup><mrow><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mi>y</mi><mo>)</mo><msup><mrow><mi>&nbsp;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mn>117</mn><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><msup><mrow><mi>&nbsp;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><msup><mrow><mi>&nbsp;</mi><mo>(</mo><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup><mo>+</mo><mi>x</mi><mi>y</mi><mo>)</mo><mo>=</mo><mfrac><mn>39</mn><mrow><mn>4</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow></mfrac><mo>=</mo><mn>3</mn><mo>(</mo><mn>4</mn><mo>-</mo><msqrt><mn>3</mn></msqrt><mo>)</mo><mo>=</mo><mi>&nbsp;</mi><mn>12</mn><mi>&nbsp;</mi><mo>-</mo><mn>3</mn><msqrt><mn>3</mn></msqrt><mo>.</mo><mo>.</mo><mo>.</mo><mo>.</mo><mo>(</mo><mi>i</mi><mi>i</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mi>O</mi><mi>n</mi><mo>&nbsp;</mo><mi>a</mi><mi>d</mi><mi>d</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mo>(</mo><mi>i</mi><mo>)</mo><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mo>(</mo><mi>i</mi><mi>i</mi><mo>)</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15.<span style=\"font-family: Times New Roman;\"> Study the given graph and answer the question that follows</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image11.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">The expenditure on Interest on Loans is by what percentage more than the expenditure on Taxes?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">15.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">दिए गए ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image12.png\" /></p>\r\n<p><span style=\"font-family: Baloo;\">ऋणों पर ब्याज पर व्यय, करों पर व्यय से कितने प्रतिशत अधिक है?</span></p>",
                    options_en: ["<p>30%</p>", "<p>40%</p>", 
                                "<p>25%</p>", "<p>50%</p>"],
                    options_hi: ["<p>30%</p>", "<p>40%</p>",
                                "<p>25%</p>", "<p>50%</p>"],
                    solution_en: "<p>15 (d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Interest on loan = 7.5%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Interest on taxes = 5%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Percentage =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>5</mn></mrow><mn>5</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>50</mn><mo>%</mo></math></span><span style=\"font-family: Times New Roman;\"> = 50%</span></p>",
                    solution_hi: "<p>15 (d)</p>\r\n<p><span style=\"font-family: Baloo;\">ऋण पर ब्याज</span><span style=\"font-family: Times New Roman;\"> = 7.5%</span></p>\r\n<p><span style=\"font-family: Baloo;\">करों पर ब्याज</span><span style=\"font-family: Times New Roman;\"> = 5%</span></p>\r\n<p><span style=\"font-family: Baloo;\">प्रतिशत</span><span style=\"font-family: Times New Roman;\"> =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>5</mn></mrow><mn>5</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>50</mn><mo>%</mo></math></span><span style=\"font-family: Times New Roman;\"> = 50%</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15.<span style=\"font-family: Times New Roman;\"> Study the given graph and answer the question that follows</span></p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image11.png\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">The expenditure on Interest on Loans is by what percentage more than the expenditure on Taxes?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">15.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">दिए गए ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें</span></p>\r\n<p><img src=\"admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image12.png\" /></p>\r\n<p><span style=\"font-family: Baloo;\">ऋणों पर ब्याज पर व्यय, करों पर व्यय से कितने प्रतिशत अधिक है?</span></p>",
                    options_en: ["<p>30%</p>", "<p>40%</p>", 
                                "<p>25%</p>", "<p>50%</p>"],
                    options_hi: ["<p>30%</p>", "<p>40%</p>",
                                "<p>25%</p>", "<p>50%</p>"],
                    solution_en: "<p>15 (d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Interest on loan = 7.5%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Interest on taxes = 5%</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Percentage =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>5</mn></mrow><mn>5</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>50</mn><mo>%</mo></math></span><span style=\"font-family: Times New Roman;\"> = 50%</span></p>",
                    solution_hi: "<p>15 (d)</p>\r\n<p><span style=\"font-family: Baloo;\">ऋण पर ब्याज</span><span style=\"font-family: Times New Roman;\"> = 7.5%</span></p>\r\n<p><span style=\"font-family: Baloo;\">करों पर ब्याज</span><span style=\"font-family: Times New Roman;\"> = 5%</span></p>\r\n<p><span style=\"font-family: Baloo;\">प्रतिशत</span><span style=\"font-family: Times New Roman;\"> =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>5</mn></mrow><mn>5</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mn>50</mn><mo>%</mo></math></span><span style=\"font-family: Times New Roman;\"> = 50%</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. <span style=\"font-family: Times New Roman;\">The altitude AD of a triangle ABC is 9 cm. If AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\">cm and CD = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\"> cm, then what will be the measure of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math>A </span><span style=\"font-family: Times New Roman;\">?</span></p>\n",
                    question_hi: "<p>1<span style=\"font-family: Times New Roman;\">6.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2319;&#2325; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; ABC &#2325;&#2366; &#2358;&#2368;&#2352;&#2381;&#2359;&#2354;&#2306;&#2348; AD = 9 &#2360;&#2375;&#2350;&#2368; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Baloo;\"> cm &#2324;&#2352; CD = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Baloo;\"> cm &#2361;&#2376;, &#2340;&#2379; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo><mi>A</mi></math></span><span style=\"font-family: Baloo;\"> &#2325;&#2366; &#2350;&#2366;&#2346; &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>90&deg;</p>\n", "<p>30&deg;</p>\n", 
                                "<p>45&deg;</p>\n", "<p>60&deg;</p>\n"],
                    options_hi: ["<p>90&deg;</p>\n", "<p>30&deg;</p>\n",
                                "<p>45&deg;</p>\n", "<p>60&deg;</p>\n"],
                    solution_en: "<p>16.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image13.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">In </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>9</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&#12581;</mo><mi>D</mi><mi>A</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>I</mi><mi>n</mi><mo>&nbsp;</mo><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>D</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>9</mn><mrow><mn>6</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&#12581;</mo><mi>B</mi><mi>A</mi><mi>D</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&ang;</mo><mi>B</mi><mi>A</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo></math></p>\n",
                    solution_hi: "<p>16.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image15.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306;, </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>9</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&#12581;</mo><mi>D</mi><mi>A</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>I</mi><mi>n</mi><mo>&nbsp;</mo><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>D</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>9</mn><mrow><mn>6</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&#12581;</mo><mi>B</mi><mi>A</mi><mi>D</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&ang;</mo><mi>B</mi><mi>A</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. <span style=\"font-family: Times New Roman;\">The altitude AD of a triangle ABC is 9 cm. If AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\">cm and CD = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\"> cm, then what will be the measure of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math>A </span><span style=\"font-family: Times New Roman;\">?</span></p>\n",
                    question_hi: "<p>1<span style=\"font-family: Times New Roman;\">6.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2319;&#2325; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; ABC &#2325;&#2366; &#2358;&#2368;&#2352;&#2381;&#2359;&#2354;&#2306;&#2348; AD = 9 &#2360;&#2375;&#2350;&#2368; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Baloo;\"> cm &#2324;&#2352; CD = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Baloo;\"> cm &#2361;&#2376;, &#2340;&#2379; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo><mi>A</mi></math></span><span style=\"font-family: Baloo;\"> &#2325;&#2366; &#2350;&#2366;&#2346; &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>90&deg;</p>\n", "<p>30&deg;</p>\n", 
                                "<p>45&deg;</p>\n", "<p>60&deg;</p>\n"],
                    options_hi: ["<p>90&deg;</p>\n", "<p>30&deg;</p>\n",
                                "<p>45&deg;</p>\n", "<p>60&deg;</p>\n"],
                    solution_en: "<p>16.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image13.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">In </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>9</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&#12581;</mo><mi>D</mi><mi>A</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>I</mi><mi>n</mi><mo>&nbsp;</mo><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>D</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>9</mn><mrow><mn>6</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&#12581;</mo><mi>B</mi><mi>A</mi><mi>D</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&ang;</mo><mi>B</mi><mi>A</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo></math></p>\n",
                    solution_hi: "<p>16.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image15.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306;, </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mn>9</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&#12581;</mo><mi>D</mi><mi>A</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>I</mi><mi>n</mi><mo>&nbsp;</mo><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>D</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>9</mn><mrow><mn>6</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&#12581;</mo><mi>B</mi><mi>A</mi><mi>D</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&ang;</mo><mi>B</mi><mi>A</mi><mi>C</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: " <p>17.</span><span style=\"font-family:Times New Roman\"> Sulekha bought 36 kg of sugar for Rs. 1040. She sold it at a profit equal to the selling price of 10 kg of it. What is the selling price (in Rs.) for 5 kg of sugar?</span></p>",
                    question_hi: " <p><span style=\"font-family:Times New Roman\">17.</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Baloo\">सुलेखा ने 36 किलो चीनी 1040 रुपये में खरीदी। उसने इसे 10 किग्रा के विक्रय मूल्य के बराबर लाभ पर बेचा। 5 किग्रा चीनी का विक्रय मूल्य (रु. में) क्या है?</span></p>",
                    options_en: [" <p> 235</span></p>", " <p> 215</span></p>", 
                                " <p> 220   </span></p>", " <p> 200</span></p>"],
                    options_hi: [" <p> 235</span></p>", " <p> 215</span></p>",
                                " <p> 220   </span></p>", " <p> 200</span></p>"],
                    solution_en: " <p>17. (d)</span></p> <p><span style=\"font-family:Times New Roman\">Let the selling price of 36 kg sugar </span></p> <p><span style=\"font-family:Times New Roman\">= 36x</span></p> <p><span style=\"font-family:Times New Roman\">selling price of 10 kg sugar = 10x</span></p> <p><span style=\"font-family:Times New Roman\">ATQ, </span></p> <p><span style=\"font-family:Times New Roman\">1040 + 10x = 36x</span></p> <p><span style=\"font-family:Times New Roman\">26x = 1040</span></p> <p><span style=\"font-family:Times New Roman\">x = 40</span></p> <p><span style=\"font-family:Times New Roman\">Selling price of 5 kg sugar </span></p> <p><span style=\"font-family:Times New Roman\">= 40 × 5 = 200</span></p>",
                    solution_hi: " <p>17 (d)</span></p> <p><span style=\"font-family:Baloo\">माना</span><span style=\"font-family:Times New Roman\"> 36 </span><span style=\"font-family:Baloo\">किग्रा चीनी का विक्रय मूल्य</span><span style=\"font-family:Times New Roman\"> = 36x</span></p> <p><span style=\"font-family:Times New Roman\">10 </span><span style=\"font-family:Baloo\">किग्रा चीनी का विक्रय मूल्य</span><span style=\"font-family:Times New Roman\"> = 10x</span></p> <p><span style=\"font-family:Baloo\">प्रश्नानुसार</span><span style=\"font-family:Times New Roman\">,</span></p> <p><span style=\"font-family:Times New Roman\">1040 + 10x = 36x</span></p> <p><span style=\"font-family:Times New Roman\">26x = 1040</span></p> <p><span style=\"font-family:Times New Roman\">x  = 40</span></p> <p><span style=\"font-family:Times New Roman\">5 </span><span style=\"font-family:Baloo\">किग्रा चीनी का विक्रय मूल्य</span><span style=\"font-family:Times New Roman\"> = 40 × 5 = 200</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: " <p>17.</span><span style=\"font-family:Times New Roman\"> Sulekha bought 36 kg of sugar for Rs. 1040. She sold it at a profit equal to the selling price of 10 kg of it. What is the selling price (in Rs.) for 5 kg of sugar?</span></p>",
                    question_hi: " <p><span style=\"font-family:Times New Roman\">17.</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Baloo\">सुलेखा ने 36 किलो चीनी 1040 रुपये में खरीदी। उसने इसे 10 किग्रा के विक्रय मूल्य के बराबर लाभ पर बेचा। 5 किग्रा चीनी का विक्रय मूल्य (रु. में) क्या है?</span></p>",
                    options_en: [" <p> 235</span></p>", " <p> 215</span></p>", 
                                " <p> 220   </span></p>", " <p> 200</span></p>"],
                    options_hi: [" <p> 235</span></p>", " <p> 215</span></p>",
                                " <p> 220   </span></p>", " <p> 200</span></p>"],
                    solution_en: " <p>17. (d)</span></p> <p><span style=\"font-family:Times New Roman\">Let the selling price of 36 kg sugar </span></p> <p><span style=\"font-family:Times New Roman\">= 36x</span></p> <p><span style=\"font-family:Times New Roman\">selling price of 10 kg sugar = 10x</span></p> <p><span style=\"font-family:Times New Roman\">ATQ, </span></p> <p><span style=\"font-family:Times New Roman\">1040 + 10x = 36x</span></p> <p><span style=\"font-family:Times New Roman\">26x = 1040</span></p> <p><span style=\"font-family:Times New Roman\">x = 40</span></p> <p><span style=\"font-family:Times New Roman\">Selling price of 5 kg sugar </span></p> <p><span style=\"font-family:Times New Roman\">= 40 × 5 = 200</span></p>",
                    solution_hi: " <p>17 (d)</span></p> <p><span style=\"font-family:Baloo\">माना</span><span style=\"font-family:Times New Roman\"> 36 </span><span style=\"font-family:Baloo\">किग्रा चीनी का विक्रय मूल्य</span><span style=\"font-family:Times New Roman\"> = 36x</span></p> <p><span style=\"font-family:Times New Roman\">10 </span><span style=\"font-family:Baloo\">किग्रा चीनी का विक्रय मूल्य</span><span style=\"font-family:Times New Roman\"> = 10x</span></p> <p><span style=\"font-family:Baloo\">प्रश्नानुसार</span><span style=\"font-family:Times New Roman\">,</span></p> <p><span style=\"font-family:Times New Roman\">1040 + 10x = 36x</span></p> <p><span style=\"font-family:Times New Roman\">26x = 1040</span></p> <p><span style=\"font-family:Times New Roman\">x  = 40</span></p> <p><span style=\"font-family:Times New Roman\">5 </span><span style=\"font-family:Baloo\">किग्रा चीनी का विक्रय मूल्य</span><span style=\"font-family:Times New Roman\"> = 40 × 5 = 200</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. <span style=\"font-family: Times New Roman;\">Geeta scored 30% and failed by 50 marks, while Sandeep who scored 45% marks, got 25 marks more than the minimum marks required to pass the examination. How many marks did Vimal get if he scored 64% marks?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">18.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">गीता ने 30% अंक प्राप्त किए और 50 अंकों से अनुत्तीर्ण हो गयी, जबकि संदीप जिसने 45% अंक प्राप्त किए, उसे परीक्षा उत्तीर्ण करने के लिए आवश्यक न्यूनतम अंकों से 25 अंक अधिक प्राप्त हुए। विमल को 64 प्रतिशत अंक प्राप्त करने पर कितने अंक प्राप्त हुए?</span></p>",
                    options_en: ["<p>500</p>", "<p>256</p>", 
                                "<p>320</p>", "<p>436</p>"],
                    options_hi: ["<p>500</p>", "<p>256</p>",
                                "<p>320</p>", "<p>436</p>"],
                    solution_en: "<p>18.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-trick:</span></p>\r\n<p>(45%-30%)&nbsp;= 15%</p>\r\n<p><span style=\"font-family: Times New Roman;\">15% = (50+25)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">15% = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">100% = 500</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">64% = 320</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required answer = 320</span></p>",
                    solution_hi: "<p>18.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Short trick:</span></p>\r\n<p>(45%-30%)&nbsp;= 15%</p>\r\n<p><span style=\"font-family: Times New Roman;\">15% = (50+25)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">15% = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">100% = 500</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">64% = 320</span></p>\r\n<p><span style=\"font-family: Baloo;\">आवश्यक उत्तर = </span><span style=\"font-family: Times New Roman;\">320</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. <span style=\"font-family: Times New Roman;\">Geeta scored 30% and failed by 50 marks, while Sandeep who scored 45% marks, got 25 marks more than the minimum marks required to pass the examination. How many marks did Vimal get if he scored 64% marks?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">18.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">गीता ने 30% अंक प्राप्त किए और 50 अंकों से अनुत्तीर्ण हो गयी, जबकि संदीप जिसने 45% अंक प्राप्त किए, उसे परीक्षा उत्तीर्ण करने के लिए आवश्यक न्यूनतम अंकों से 25 अंक अधिक प्राप्त हुए। विमल को 64 प्रतिशत अंक प्राप्त करने पर कितने अंक प्राप्त हुए?</span></p>",
                    options_en: ["<p>500</p>", "<p>256</p>", 
                                "<p>320</p>", "<p>436</p>"],
                    options_hi: ["<p>500</p>", "<p>256</p>",
                                "<p>320</p>", "<p>436</p>"],
                    solution_en: "<p>18.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Short-trick:</span></p>\r\n<p>(45%-30%)&nbsp;= 15%</p>\r\n<p><span style=\"font-family: Times New Roman;\">15% = (50+25)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">15% = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">100% = 500</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">64% = 320</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required answer = 320</span></p>",
                    solution_hi: "<p>18.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Short trick:</span></p>\r\n<p>(45%-30%)&nbsp;= 15%</p>\r\n<p><span style=\"font-family: Times New Roman;\">15% = (50+25)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">15% = 75</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">100% = 500</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">64% = 320</span></p>\r\n<p><span style=\"font-family: Baloo;\">आवश्यक उत्तर = </span><span style=\"font-family: Times New Roman;\">320</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. <span style=\"font-family: Times New Roman;\">A and B can complete a certain task in 24 days and 40 days, respectively. They worked together for 8 days. C completed the remaining task in 14 days. Working together, A and C will complete 75% of the same work in:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">19.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">A और B एक निश्चित कार्य को क्रमशः 24 दिनों और 40 दिनों में पूरा कर सकते हैं। उन्होंने 8 दिनों तक एक साथ काम किया। C ने शेष कार्य को 14 दिनों में पूरा किया। एक साथ कार्य करते हुए, A और C समान कार्य का 75% कितने दिन में पूरा करेंगे ?</span></p>",
                    options_en: ["<p>10 days</p>\n", "<p>12 days</p>\n", 
                                "<p>15 days</p>\n", "<p>9 days</p>\n"],
                    options_hi: ["<p>10 days</p>", "<p>12 days</p>",
                                "<p>15 days</p>", "<p>9 days</p>"],
                    solution_en: "<p>19.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the total work = 120</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of A = 5 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of B = 3 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Work done by A and B in 8 days = (5+3)&times;8 = 64 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Remaining work = 120-64 = 56 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of C = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>14</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 4 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Time taken by A and C =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&times;</mo><mfrac><mn>75</mn><mn>100</mn></mfrac></mrow><mrow><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 10 days</span></p>\n",
                    solution_hi: "<p>19.(a)</p>\r\n<p><span style=\"font-family: Baloo;\">माना कुल कार्य </span><span style=\"font-family: Times New Roman;\">= 120</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">की क्षमता</span><span style=\"font-family: Times New Roman;\"> = 5 </span><span style=\"font-family: Baloo;\">इकाई </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B </span><span style=\"font-family: Baloo;\">की क्षमता</span><span style=\"font-family: Times New Roman;\"> = 3 </span><span style=\"font-family: Baloo;\">इकाई </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> B</span><span style=\"font-family: Baloo;\"> द्वारा</span><span style=\"font-family: Times New Roman;\"> 8 </span><span style=\"font-family: Baloo;\">दिनों</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">में किया गया कार्य</span><span style=\"font-family: Times New Roman;\"> = (5 +3)&times;8 = 64 </span><span style=\"font-family: Baloo;\">इकाई </span></p>\r\n<p><span style=\"font-family: Baloo;\">शेष कार्य</span><span style=\"font-family: Times New Roman;\"> = 120-64 = 56 </span><span style=\"font-family: Baloo;\">इकाई </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">C </span><span style=\"font-family: Baloo;\">की क्षमता </span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>14</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 4 </span><span style=\"font-family: Baloo;\">इकाई </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> C </span><span style=\"font-family: Baloo;\">द्वारा लिया गया समय</span><span style=\"font-family: Times New Roman;\"> = </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&#215;</mo><mfrac><mn>75</mn><mn>100</mn></mfrac></mrow><mrow><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 10 </span><span style=\"font-family: Baloo;\">दिन</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. <span style=\"font-family: Times New Roman;\">A and B can complete a certain task in 24 days and 40 days, respectively. They worked together for 8 days. C completed the remaining task in 14 days. Working together, A and C will complete 75% of the same work in:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">19.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">A और B एक निश्चित कार्य को क्रमशः 24 दिनों और 40 दिनों में पूरा कर सकते हैं। उन्होंने 8 दिनों तक एक साथ काम किया। C ने शेष कार्य को 14 दिनों में पूरा किया। एक साथ कार्य करते हुए, A और C समान कार्य का 75% कितने दिन में पूरा करेंगे ?</span></p>",
                    options_en: ["<p>10 days</p>\n", "<p>12 days</p>\n", 
                                "<p>15 days</p>\n", "<p>9 days</p>\n"],
                    options_hi: ["<p>10 days</p>", "<p>12 days</p>",
                                "<p>15 days</p>", "<p>9 days</p>"],
                    solution_en: "<p>19.(a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the total work = 120</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of A = 5 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of B = 3 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Work done by A and B in 8 days = (5+3)&times;8 = 64 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Remaining work = 120-64 = 56 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Efficiency of C = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>14</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 4 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Time taken by A and C =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&times;</mo><mfrac><mn>75</mn><mn>100</mn></mfrac></mrow><mrow><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\"> = 10 days</span></p>\n",
                    solution_hi: "<p>19.(a)</p>\r\n<p><span style=\"font-family: Baloo;\">माना कुल कार्य </span><span style=\"font-family: Times New Roman;\">= 120</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">की क्षमता</span><span style=\"font-family: Times New Roman;\"> = 5 </span><span style=\"font-family: Baloo;\">इकाई </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B </span><span style=\"font-family: Baloo;\">की क्षमता</span><span style=\"font-family: Times New Roman;\"> = 3 </span><span style=\"font-family: Baloo;\">इकाई </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> B</span><span style=\"font-family: Baloo;\"> द्वारा</span><span style=\"font-family: Times New Roman;\"> 8 </span><span style=\"font-family: Baloo;\">दिनों</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">में किया गया कार्य</span><span style=\"font-family: Times New Roman;\"> = (5 +3)&times;8 = 64 </span><span style=\"font-family: Baloo;\">इकाई </span></p>\r\n<p><span style=\"font-family: Baloo;\">शेष कार्य</span><span style=\"font-family: Times New Roman;\"> = 120-64 = 56 </span><span style=\"font-family: Baloo;\">इकाई </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">C </span><span style=\"font-family: Baloo;\">की क्षमता </span><span style=\"font-family: Times New Roman;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>14</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 4 </span><span style=\"font-family: Baloo;\">इकाई </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> C </span><span style=\"font-family: Baloo;\">द्वारा लिया गया समय</span><span style=\"font-family: Times New Roman;\"> = </span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&#215;</mo><mfrac><mn>75</mn><mn>100</mn></mfrac></mrow><mrow><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 10 </span><span style=\"font-family: Baloo;\">दिन</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. <span style=\"font-family: Times New Roman;\"> In a circle with centre O, a 6 cm long chord is at a distance 4 cm from the centre. Find the length of the diameter.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">20. </span><span style=\"font-family: Baloo;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; O &#2357;&#2366;&#2354;&#2375; &#2319;&#2325; &#2357;&#2371;&#2340;&#2381;&#2340; &#2350;&#2375;&#2306;, &#2332;&#2367;&#2360;&#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; 6 &#2360;&#2375;&#2350;&#2368; &#2361;&#2376;, &#2332;&#2368;&#2357;&#2366; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2375; 4 &#2360;&#2375;&#2350;&#2368; &#2325;&#2368; &#2342;&#2370;&#2352;&#2368; &#2346;&#2352; &#2361;&#2376;&#2404; &#2357;&#2381;&#2351;&#2366;&#2360; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>14 cm</p>\n", "<p>7 cm</p>\n", 
                                "<p>5 cm</p>\n", "<p>10 cm</p>\n"],
                    options_hi: ["<p>14 cm</p>\n", "<p>7 cm</p>\n",
                                "<p>5 cm</p>\n", "<p>10 cm</p>\n"],
                    solution_en: "<p>20.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image14.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">Radius = OA =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn><mo>&sup2;</mo><mo>+</mo><mn>4</mn><mo>&sup2;</mo></msqrt></math> </span><span style=\"font-family: Times New Roman;\">= 5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Diameter = 2&times;</span><span style=\"font-family: Times New Roman;\">5 = 10</span></p>\n",
                    solution_hi: "<p>20.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image3.png\"></p>\r\n<p><span style=\"font-family: Baloo;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times New Roman;\"> = OA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn><mo>&sup2;</mo><mo>+</mo><mn>4</mn><mo>&sup2;</mo></msqrt></math></span><span style=\"font-family: Times New Roman;\">= 5</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Times New Roman;\"> = 2&times;</span><span style=\"font-family: Times New Roman;\">5 = 10</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. <span style=\"font-family: Times New Roman;\"> In a circle with centre O, a 6 cm long chord is at a distance 4 cm from the centre. Find the length of the diameter.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">20. </span><span style=\"font-family: Baloo;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352; O &#2357;&#2366;&#2354;&#2375; &#2319;&#2325; &#2357;&#2371;&#2340;&#2381;&#2340; &#2350;&#2375;&#2306;, &#2332;&#2367;&#2360;&#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; 6 &#2360;&#2375;&#2350;&#2368; &#2361;&#2376;, &#2332;&#2368;&#2357;&#2366; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2375; 4 &#2360;&#2375;&#2350;&#2368; &#2325;&#2368; &#2342;&#2370;&#2352;&#2368; &#2346;&#2352; &#2361;&#2376;&#2404; &#2357;&#2381;&#2351;&#2366;&#2360; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>14 cm</p>\n", "<p>7 cm</p>\n", 
                                "<p>5 cm</p>\n", "<p>10 cm</p>\n"],
                    options_hi: ["<p>14 cm</p>\n", "<p>7 cm</p>\n",
                                "<p>5 cm</p>\n", "<p>10 cm</p>\n"],
                    solution_en: "<p>20.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image14.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">Radius = OA =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn><mo>&sup2;</mo><mo>+</mo><mn>4</mn><mo>&sup2;</mo></msqrt></math> </span><span style=\"font-family: Times New Roman;\">= 5</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Diameter = 2&times;</span><span style=\"font-family: Times New Roman;\">5 = 10</span></p>\n",
                    solution_hi: "<p>20.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image3.png\"></p>\r\n<p><span style=\"font-family: Baloo;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Times New Roman;\"> = OA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn><mo>&sup2;</mo><mo>+</mo><mn>4</mn><mo>&sup2;</mo></msqrt></math></span><span style=\"font-family: Times New Roman;\">= 5</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Times New Roman;\"> = 2&times;</span><span style=\"font-family: Times New Roman;\">5 = 10</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21.<span style=\"font-family: Times New Roman;\"> A sum of money is divided among A, B and C in the ratio 2:3:7, respectively. If the share of B is Rs.15,000, then what will be the difference in the shares of B and C?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">21.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक राशि को क्रमशः 2:3:7 के अनुपात में A, B और C के बीच विभाजित किया जाता है। यदि B का हिस्सा 15,000 रुपये है, तो B और C के हिस्सों में क्या अंतर होगा? </span></p>",
                    options_en: ["<p>Rs. 15,000</p>", "<p>Rs. 20,000</p>", 
                                "<p>Rs. 18,000</p>", "<p>Rs. 50,000</p>"],
                    options_hi: ["<p>Rs. 15,000</p>", "<p>Rs. 20,000</p>",
                                "<p>Rs. 18,000</p>", "<p>Rs. 50,000</p>"],
                    solution_en: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">3 unit = 15000</span></p>\r\n<p>(7-3) unit = 20,000</p>",
                    solution_hi: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">3 unit = 15000</span></p>\r\n<p>&nbsp;(7-3) unit = 20,000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21.<span style=\"font-family: Times New Roman;\"> A sum of money is divided among A, B and C in the ratio 2:3:7, respectively. If the share of B is Rs.15,000, then what will be the difference in the shares of B and C?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">21.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक राशि को क्रमशः 2:3:7 के अनुपात में A, B और C के बीच विभाजित किया जाता है। यदि B का हिस्सा 15,000 रुपये है, तो B और C के हिस्सों में क्या अंतर होगा? </span></p>",
                    options_en: ["<p>Rs. 15,000</p>", "<p>Rs. 20,000</p>", 
                                "<p>Rs. 18,000</p>", "<p>Rs. 50,000</p>"],
                    options_hi: ["<p>Rs. 15,000</p>", "<p>Rs. 20,000</p>",
                                "<p>Rs. 18,000</p>", "<p>Rs. 50,000</p>"],
                    solution_en: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">3 unit = 15000</span></p>\r\n<p>(7-3) unit = 20,000</p>",
                    solution_hi: "<p>21.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">3 unit = 15000</span></p>\r\n<p>&nbsp;(7-3) unit = 20,000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: " <p>22.</span><span style=\"font-family:Times New Roman\"> What is the product of the largest and the smallest possible values of m for which a number 5m83m4m1 is divisible by 9?</span></p>",
                    question_hi: " <p><span style=\"font-family:Times New Roman\">22.</span><span style=\"font-family:Times New Roman\"> m </span><span style=\"font-family:Baloo\">के सबसे बड़े और सबसे छोटे संभावित मानों का गुणनफल क्या है जिसके लिए एक संख्या</span><span style=\"font-family:Times New Roman\"> 5m83m4m1, 9 </span><span style=\"font-family:Baloo\">से विभाज्य है?</span></p>",
                    options_en: [" <p> 80</span></p>", " <p> 10</span></p>", 
                                " <p> 40</span></p>", " <p> 16</span></p>"],
                    options_hi: [" <p> 80</span></p>", " <p> 10</span></p>",
                                " <p> 40</span></p>", " <p> 16</span></p>"],
                    solution_en: " <p>22.(d) </span><span style=\"font-family:Times New Roman\">For divisibility of 9, check digit sum</span></p> <p><span style=\"font-family:Times New Roman\">5 + m + 8 + 3 + m + 4 + m + 1 = 9 or its multiple</span></p> <p><span style=\"font-family:Times New Roman\">21 + 3m = 9 or its multiple</span></p> <p><span style=\"font-family:Times New Roman\">Possible value of m = 2, 5, 8,</span></p> <p><span style=\"font-family:Times New Roman\">Required answer = 2 × 8 = 16</span></p>",
                    solution_hi: " <p>22.(d)</span><span style=\"font-family:Times New Roman\"> 9 </span><span style=\"font-family:Baloo\">की विभाज्यता के लिए, अंकों का योग जांचें</span></p> <p><span style=\"font-family:Times New Roman\">5 + m + 8 + 3 + m + 4 + m + 1 = 9  </span><span style=\"font-family:Baloo\">या इसके गुणज</span></p> <p><span style=\"font-family:Times New Roman\">21 + 3m = 9 </span><span style=\"font-family:Baloo\">या इसके गुणज</span></p> <p><span style=\"font-family:Times New Roman\">m </span><span style=\"font-family:Baloo\">का संभावित मान </span><span style=\"font-family:Times New Roman\">= 2, 5, 8,</span></p> <p><span style=\"font-family:Baloo\">आवश्यक उत्तर =</span><span style=\"font-family:Times New Roman\"> 2 × 8 = 16</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: " <p>22.</span><span style=\"font-family:Times New Roman\"> What is the product of the largest and the smallest possible values of m for which a number 5m83m4m1 is divisible by 9?</span></p>",
                    question_hi: " <p><span style=\"font-family:Times New Roman\">22.</span><span style=\"font-family:Times New Roman\"> m </span><span style=\"font-family:Baloo\">के सबसे बड़े और सबसे छोटे संभावित मानों का गुणनफल क्या है जिसके लिए एक संख्या</span><span style=\"font-family:Times New Roman\"> 5m83m4m1, 9 </span><span style=\"font-family:Baloo\">से विभाज्य है?</span></p>",
                    options_en: [" <p> 80</span></p>", " <p> 10</span></p>", 
                                " <p> 40</span></p>", " <p> 16</span></p>"],
                    options_hi: [" <p> 80</span></p>", " <p> 10</span></p>",
                                " <p> 40</span></p>", " <p> 16</span></p>"],
                    solution_en: " <p>22.(d) </span><span style=\"font-family:Times New Roman\">For divisibility of 9, check digit sum</span></p> <p><span style=\"font-family:Times New Roman\">5 + m + 8 + 3 + m + 4 + m + 1 = 9 or its multiple</span></p> <p><span style=\"font-family:Times New Roman\">21 + 3m = 9 or its multiple</span></p> <p><span style=\"font-family:Times New Roman\">Possible value of m = 2, 5, 8,</span></p> <p><span style=\"font-family:Times New Roman\">Required answer = 2 × 8 = 16</span></p>",
                    solution_hi: " <p>22.(d)</span><span style=\"font-family:Times New Roman\"> 9 </span><span style=\"font-family:Baloo\">की विभाज्यता के लिए, अंकों का योग जांचें</span></p> <p><span style=\"font-family:Times New Roman\">5 + m + 8 + 3 + m + 4 + m + 1 = 9  </span><span style=\"font-family:Baloo\">या इसके गुणज</span></p> <p><span style=\"font-family:Times New Roman\">21 + 3m = 9 </span><span style=\"font-family:Baloo\">या इसके गुणज</span></p> <p><span style=\"font-family:Times New Roman\">m </span><span style=\"font-family:Baloo\">का संभावित मान </span><span style=\"font-family:Times New Roman\">= 2, 5, 8,</span></p> <p><span style=\"font-family:Baloo\">आवश्यक उत्तर =</span><span style=\"font-family:Times New Roman\"> 2 × 8 = 16</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23.<span style=\"font-family: Times New Roman;\"> An article is sold for Rs. 1,176 after two successive discounts of 30% and 16% on its list price. What is the difference (in Rs.) between the list price and the selling price of the article?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">23. </span><span style=\"font-family: Palanquin Dark;\">एक वस्तु अंकित मूल्य पर 30% और 16% की लगातार दो छूट के बाद 1,176 रुपये में बेचा जाता है। इस वस्तु के अंकित मूल्य और विक्रय मूल्य के बीच क्या अंतर (रुपये में) है?</span></p>",
                    options_en: ["<p>740</p>", "<p>820</p>", 
                                "<p>840</p>", "<p>824</p>"],
                    options_hi: ["<p>740</p>", "<p>820</p>",
                                "<p>840</p>", "<p>824</p>"],
                    solution_en: "<p>23.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the marked price = x</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#215;</mo><mfrac><mn>70</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>84</mn><mn>100</mn></mfrac><mo>=</mo><mn>1176</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 2000</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Difference = 2000-1176 = 824</span></p>",
                    solution_hi: "<p>23.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">माना अंकित मूल्य = x</span></p>\r\n<p><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#215;</mo><mfrac><mn>70</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>84</mn><mn>100</mn></mfrac><mo>=</mo><mn>1176</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 2000</span></p>\r\n<p><span style=\"font-family: Baloo;\">अंतर = 2000-1176 = 824</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23.<span style=\"font-family: Times New Roman;\"> An article is sold for Rs. 1,176 after two successive discounts of 30% and 16% on its list price. What is the difference (in Rs.) between the list price and the selling price of the article?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">23. </span><span style=\"font-family: Palanquin Dark;\">एक वस्तु अंकित मूल्य पर 30% और 16% की लगातार दो छूट के बाद 1,176 रुपये में बेचा जाता है। इस वस्तु के अंकित मूल्य और विक्रय मूल्य के बीच क्या अंतर (रुपये में) है?</span></p>",
                    options_en: ["<p>740</p>", "<p>820</p>", 
                                "<p>840</p>", "<p>824</p>"],
                    options_hi: ["<p>740</p>", "<p>820</p>",
                                "<p>840</p>", "<p>824</p>"],
                    solution_en: "<p>23.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the marked price = x</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#215;</mo><mfrac><mn>70</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>84</mn><mn>100</mn></mfrac><mo>=</mo><mn>1176</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 2000</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Difference = 2000-1176 = 824</span></p>",
                    solution_hi: "<p>23.(d)</p>\r\n<p><span style=\"font-family: Baloo;\">माना अंकित मूल्य = x</span></p>\r\n<p><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&#215;</mo><mfrac><mn>70</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>84</mn><mn>100</mn></mfrac><mo>=</mo><mn>1176</mn></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 2000</span></p>\r\n<p><span style=\"font-family: Baloo;\">अंतर = 2000-1176 = 824</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. <span style=\"font-family: Times New Roman;\">The value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&#160;</mo></mrow></mfenced><mo>&#160;</mo><mo>&#247;</mo><mo>(</mo><mn>9</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#247;</mo><mn>11</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>5</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#160;</mi><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">is:</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">24.&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&#160;</mo></mrow></mfenced><mo>&#160;</mo><mo>&#247;</mo><mo>(</mo><mn>9</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#247;</mo><mn>11</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>5</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#160;</mi><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Baloo;\">का मान है </span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math></span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>10</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\">-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></span></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\">-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math></span></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>10</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\">-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></span></p>"],
                    solution_en: "<p>24.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><mo>(</mo><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mn>9</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#247;</mo><mn>11</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>5</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#160;</mi><mo>&#8658;</mo><mo>(</mo><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>15</mn><mn>8</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>85</mn><mn>9</mn></mfrac><mo>&#247;</mo><mfrac><mn>34</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>85</mn><mn>9</mn></mfrac><mo>&#247;</mo><mfrac><mn>34</mn><mn>24</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mfrac><mn>20</mn><mn>3</mn></mfrac><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>80</mn><mn>9</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mo>(</mo><mfrac><mn>3</mn><mn>20</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#8658;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mo>&#160;</mo><mo>-</mo><mfrac><mn>7</mn><mn>10</mn></mfrac><mspace linebreak=\"newline\"></mspace></math></p>",
                    solution_hi: "<p>24.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><mo>(</mo><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mn>9</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#247;</mo><mn>11</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>5</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"/><mi>&#160;</mi><mo>&#8658;</mo><mo>(</mo><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>15</mn><mn>8</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>85</mn><mn>9</mn></mfrac><mo>&#247;</mo><mfrac><mn>34</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>85</mn><mn>9</mn></mfrac><mo>&#247;</mo><mfrac><mn>34</mn><mn>24</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mfrac><mn>20</mn><mn>3</mn></mfrac><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>80</mn><mn>9</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>(</mo><mfrac><mn>3</mn><mn>20</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#8658;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mo>&#160;</mo><mo>-</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. <span style=\"font-family: Times New Roman;\">The value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&#160;</mo></mrow></mfenced><mo>&#160;</mo><mo>&#247;</mo><mo>(</mo><mn>9</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#247;</mo><mn>11</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>5</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#160;</mi><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">is:</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">24.&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&#160;</mo></mrow></mfenced><mo>&#160;</mo><mo>&#247;</mo><mo>(</mo><mn>9</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#247;</mo><mn>11</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>5</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>&#160;</mi><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Baloo;\">का मान है </span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math></span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>10</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\">-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></span></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\">-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math></span></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>10</mn></mfrac></math></p>", "<p><span style=\"font-family: Times New Roman;\">-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></span></p>"],
                    solution_en: "<p>24.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><mo>(</mo><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mn>9</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#247;</mo><mn>11</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>5</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#160;</mi><mo>&#8658;</mo><mo>(</mo><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>15</mn><mn>8</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>85</mn><mn>9</mn></mfrac><mo>&#247;</mo><mfrac><mn>34</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>85</mn><mn>9</mn></mfrac><mo>&#247;</mo><mfrac><mn>34</mn><mn>24</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mfrac><mn>20</mn><mn>3</mn></mfrac><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>80</mn><mn>9</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mo>(</mo><mfrac><mn>3</mn><mn>20</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#8658;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mo>&#160;</mo><mo>-</mo><mfrac><mn>7</mn><mn>10</mn></mfrac><mspace linebreak=\"newline\"></mspace></math></p>",
                    solution_hi: "<p>24.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><mo>(</mo><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mn>9</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#247;</mo><mn>11</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>5</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"/><mi>&#160;</mi><mo>&#8658;</mo><mo>(</mo><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>15</mn><mn>8</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>85</mn><mn>9</mn></mfrac><mo>&#247;</mo><mfrac><mn>34</mn><mn>3</mn></mfrac><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>85</mn><mn>9</mn></mfrac><mo>&#247;</mo><mfrac><mn>34</mn><mn>24</mn></mfrac><mo>)</mo><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mfrac><mn>20</mn><mn>3</mn></mfrac><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#247;</mo><mo>(</mo><mfrac><mn>80</mn><mn>9</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mo>(</mo><mfrac><mn>3</mn><mn>20</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#8658;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mo>&#160;</mo><mo>-</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Times New Roman;\"> In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math></span><span style=\"font-family: Times New Roman;\">, AD is a median. If points E, F and G midpoints of AD, AE and DE, respectively, then what will be the area <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mo>&nbsp;</mo><mi>B</mi><mi>F</mi><mi>G</mi></math></span><span style=\"font-family: Times New Roman;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">25.</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math></span><span style=\"font-family: Baloo;\"> &#2350;&#2375;&#2306;, AD &#2319;&#2325; &#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2348;&#2367;&#2306;&#2342;&#2369; E, F &#2324;&#2352; G &#2325;&#2381;&#2352;&#2350;&#2358;&#2307; AD, AE &#2324;&#2352; DE &#2325;&#2375; &#2350;&#2343;&#2381;&#2351; &#2348;&#2367;&#2306;&#2342;&#2369; &#2361;&#2376;&#2306;, &#2340;&#2379; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mi>B</mi><mi>F</mi><mi>G</mi></math></span><span style=\"font-family: Baloo;\"> &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math></p>\n", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&Delta;</mi><mi>B</mi><mi>G</mi><mi>C</mi></math>)</p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math></p>\n",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&Delta;</mi><mi>B</mi><mi>G</mi><mi>C</mi></math>)</p>\n"],
                    solution_en: "<p>25.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image18.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">As Median divide the triangle into two equal area </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">And in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">ADB </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Area of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">ADB = 2 Area of </span><span style=\"font-family: Times New Roman;\">BGF(base(AD = 2GF) is half and height is same) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, Area of </span><span style=\"font-family: Times New Roman;\">ABC = 4 &times; Area of </span><span style=\"font-family: Times New Roman;\">BGF</span></p>\n",
                    solution_hi: "<p>25.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image17.png\"></p>\r\n<p><span style=\"font-family: Baloo;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; &#2325;&#2379; &#2342;&#2379; &#2348;&#2352;&#2366;&#2348;&#2352; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>ADB </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306;,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>ADB </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Times New Roman;\">= 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">BGF</span><span style=\"font-family: Baloo;\"> &#2325;&#2366; </span><span style=\"font-family: Baloo;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; </span></p>\r\n<p><span style=\"font-family: Baloo;\">((&#2310;&#2343;&#2366;&#2352; (AD = 2GF) &#2310;&#2343;&#2366; &#2361;&#2376; &#2324;&#2352; &#2314;&#2305;&#2330;&#2366;&#2312; &#2360;&#2350;&#2366;&#2344; &#2361;&#2376;)</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2309;&#2340;</span><span style=\"font-family: Times New Roman;\">: </span><span style=\"font-family: Times New Roman;\">ABC </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 4 &times; </span><span style=\"font-family: Times New Roman;\">BGF </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Times New Roman;\"> In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math></span><span style=\"font-family: Times New Roman;\">, AD is a median. If points E, F and G midpoints of AD, AE and DE, respectively, then what will be the area <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mo>&nbsp;</mo><mi>B</mi><mi>F</mi><mi>G</mi></math></span><span style=\"font-family: Times New Roman;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">25.</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math></span><span style=\"font-family: Baloo;\"> &#2350;&#2375;&#2306;, AD &#2319;&#2325; &#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2348;&#2367;&#2306;&#2342;&#2369; E, F &#2324;&#2352; G &#2325;&#2381;&#2352;&#2350;&#2358;&#2307; AD, AE &#2324;&#2352; DE &#2325;&#2375; &#2350;&#2343;&#2381;&#2351; &#2348;&#2367;&#2306;&#2342;&#2369; &#2361;&#2376;&#2306;, &#2340;&#2379; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&Delta;</mi><mi>B</mi><mi>F</mi><mi>G</mi></math></span><span style=\"font-family: Baloo;\"> &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math></p>\n", 
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&Delta;</mi><mi>B</mi><mi>G</mi><mi>C</mi></math>)</p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math><span style=\"font-family: Times New Roman;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math></p>\n",
                                "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&nbsp;</mi><mi>&Delta;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mi>A</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&Delta;</mi><mi>B</mi><mi>G</mi><mi>C</mi></math>)</p>\n"],
                    solution_en: "<p>25.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image18.png\"></p>\r\n<p><span style=\"font-family: Times New Roman;\">As Median divide the triangle into two equal area </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">And in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">ADB </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Area of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">ADB = 2 Area of </span><span style=\"font-family: Times New Roman;\">BGF(base(AD = 2GF) is half and height is same) </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, Area of </span><span style=\"font-family: Times New Roman;\">ABC = 4 &times; Area of </span><span style=\"font-family: Times New Roman;\">BGF</span></p>\n",
                    solution_hi: "<p>25.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1647674425/word/media/image17.png\"></p>\r\n<p><span style=\"font-family: Baloo;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; &#2325;&#2379; &#2342;&#2379; &#2348;&#2352;&#2366;&#2348;&#2352; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>ADB </span><span style=\"font-family: Baloo;\">&#2350;&#2375;&#2306;,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>ADB </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Times New Roman;\">= 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Times New Roman;\">BGF</span><span style=\"font-family: Baloo;\"> &#2325;&#2366; </span><span style=\"font-family: Baloo;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; </span></p>\r\n<p><span style=\"font-family: Baloo;\">((&#2310;&#2343;&#2366;&#2352; (AD = 2GF) &#2310;&#2343;&#2366; &#2361;&#2376; &#2324;&#2352; &#2314;&#2305;&#2330;&#2366;&#2312; &#2360;&#2350;&#2366;&#2344; &#2361;&#2376;)</span></p>\r\n<p><span style=\"font-family: Baloo;\">&#2309;&#2340;</span><span style=\"font-family: Times New Roman;\">: </span><span style=\"font-family: Times New Roman;\">ABC </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 4 &times; </span><span style=\"font-family: Times New Roman;\">BGF </span><span style=\"font-family: Baloo;\">&#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>