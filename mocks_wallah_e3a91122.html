<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Akhil can complete a piece of work in 24 days. Shyam can complete the same piece of work in 12 days. If both of them work together, in how many days can they complete the same piece of work ?</p>",
                    question_hi: "<p>1. अखिल एक काम 24 दिनों में पूरा कर सकता है। श्याम उसी काम को 12 दिनों में पूरा कर सकता है। यदि वे दोनों साथ मिलकर काम करते हैं, तो वे उसी काम को कितने दिनों में पूरा कर सकते हैं?</p>",
                    options_en: ["<p>12 days</p>", "<p>8 days</p>", 
                                "<p>10 days</p>", "<p>6 days</p>"],
                    options_hi: ["<p>12 दिन</p>", "<p>8 दिन</p>",
                                "<p>10 दिन</p>", "<p>6 दिन</p>"],
                    solution_en: "<p>1.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826176358.png\" alt=\"rId4\" width=\"143\" height=\"109\"><br>Total Efficiency of Akhil and Shyam = 1 + 2 = 3 units<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 8 days</p>",
                    solution_hi: "<p>1.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826176554.png\" alt=\"rId5\" width=\"130\" height=\"122\"><br>अखिल और श्याम की कुल दक्षता = 1 + 2 = 3 इकाई<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 8 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Sapna finishes a job in 12 hours working alone. If Sapna and Riya finish the same job in 8 hours working together at their respective constant rates, then, in how many hours can Riya do the job working alone?</p>",
                    question_hi: "<p>2. सपना अकेले काम करते हुए एक काम को 12 घंटे में पूरा करती है। यदि सपना और रिया अपनी-अपनी नियत चाल से साथ मिलकर काम करते हुए उसी काम को 8 घंटे में पूरा करती हैं. तो रिया अकेले काम करते हुए इसे कितने घंटों में पूरा कर सकती है?</p>",
                    options_en: ["<p>18</p>", "<p>20</p>", 
                                "<p>16</p>", "<p>24</p>"],
                    options_hi: ["<p>18</p>", "<p>20</p>",
                                "<p>16</p>", "<p>24</p>"],
                    solution_en: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826176681.png\" alt=\"rId6\" width=\"195\" height=\"145\"><br>Efficiency of Riya = 3 - 2 = 1 unit<br>Time taken by Riya = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 hours</p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826176837.png\" alt=\"rId7\" width=\"156\" height=\"137\"><br>रिया की क्षमता = 3 - 2 = 1 इकाई<br>रिया द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Sobhita can complete a painting work in 20 hours while Saroj can complete the same work in 15 hours. In how much time (in hours) can the entire work of painting be completed, if both Sobhita and Saroj work simultaneously?</p>",
                    question_hi: "<p>3. शोभिता एक पेंटिंग का काम 20 घंटे में पूरा कर सकती है जबकि सरोज उसी काम को 15 घंटे में पूरा कर सकती है। यदि शोभिता और सरोज दोनों साथ मिलकर काम करें, तो पेंटिंग का पूरा काम कितने समय (घंटों में) में पूरा हो सकता है?</p>",
                    options_en: ["<p>7</p>", "<p>8</p>", 
                                "<p>8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>7</p>", "<p>8</p>",
                                "<p>8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>3.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826177062.png\" alt=\"rId8\" width=\"160\" height=\"119\"><br>Total Efficiency of Sobhita and Saroj = 3 + 4 = 7 units<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hours or 8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math> hours</p>",
                    solution_hi: "<p>3.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826177313.png\" alt=\"rId9\" width=\"128\" height=\"118\"><br>शोभिता और सरोज की कुल दक्षता = 3 + 4 = 7 इकाई<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे या 8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math> घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A work can be done by a man and a woman in 12 days and 8 days, respectively. In how many days will the work be done by 6 men and 4 women?</p>",
                    question_hi: "<p>4. एक पुरुष और एक महिला द्वारा एक काम को क्रमशः 12 दिनों और 8 दिनों में किया जा सकता है। 6 पुरुष और 4 महिलाएं इस काम को कितने दिनों में पूरा करेंगे?</p>",
                    options_en: ["<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", 
                                "<p>1</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>2</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                                "<p>1</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>4.(c) <br>Total work = LCM (12 , 8) = 24 unit<br>Efficiency of man = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 2 unit<br>Efficiency of women = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 3 unit<br>So, time taken to 6 men and 4 women to complete the whole work <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mn>6</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>4</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>24</mn><mn>24</mn></mfrac></mstyle></math> = 1 day</p>",
                    solution_hi: "<p>4.(c) <br>कुल कार्य = LCM (12 , 8) = 24 इकाई<br>पुरुष की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 2 इकाई<br>महिला की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 3 इकाई<br>तो, 6 पुरुषों और 4 महिलाओं द्वारा कार्य पूरा करने में लगा समय<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mn>6</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>4</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>24</mn><mn>24</mn></mfrac></mstyle></math>&nbsp;= 1 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. X and Y can do a job in 18 days; Y and Z can do the same in 24 days. If all the three can finish the job in 12 days, in how many days can X and Z can complete the job?</p>",
                    question_hi: "<p>5. X और Y एक काम को 18 दिनों में पूरा कर सकते हैं; Y और Z उसी काम को 24 दिनों में पूरा कर सकते हैं। यदि वह तीनों उसी काम को 12 दिनों में पूरा कर सकते हैं, तो X और Z कितने दिनों में काम पूरा कर सकते हैं?</p>",
                    options_en: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days</p>", 
                                "<p>7 days</p>", "<p>5 days</p>"],
                    options_hi: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन</p>",
                                "<p>7 दिन</p>", "<p>5 दिन</p>"],
                    solution_en: "<p>5.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826177569.png\" alt=\"rId10\" width=\"188\" height=\"96\"><br>Efficiency of X = 6 - 3 = 3 units<br>Efficiency of Z = 6 - 4 = 2 units<br>Efficiency of (X + Z) = 5 units<br>So, Work done by (X + Z) = <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>5.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826178289.png\" alt=\"rId11\" width=\"175\" height=\"98\"><br>X की दक्षता = 6 - 3 = 3 इकाई<br>Z की दक्षता = 6 - 4 = 2 इकाई<br>(X + Z) की दक्षता = 5 इकाई<br>इसलिए, (X + Z) द्वारा किया गया कार्य= <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Antony and Julie can simultaneously complete a work of floor decoration in 15 days and Julie alone in 20 days. In how many days can Antony alone complete the work of floor decoration ?</p>",
                    question_hi: "<p>6. एंटनी और जूली साथ मिलकर फर्श की सजावट का काम 15 दिनों में पूरा कर सकते हैं और जूली अकेले इसी काम को 20 दिनों में पूरा कर सकती है। एंटनी अकेले फर्श की सजावट का काम कितने दिनों में पूरा कर सकता है?</p>",
                    options_en: ["<p>55</p>", "<p>45</p>", 
                                "<p>60</p>", "<p>50</p>"],
                    options_hi: ["<p>55</p>", "<p>45</p>",
                                "<p>60</p>", "<p>50</p>"],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826179407.png\" alt=\"rId12\" width=\"151\" height=\"117\"><br>Efficiency of Julie = 4 - 3 = 1 units<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 60 days</p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826179719.png\" alt=\"rId13\" width=\"146\" height=\"123\"><br>जूली की दक्षता = 4 - 3 = 1 इकाई<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 60 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Rohan takes 10 hours to mow a large lawn. He and Mohan together can mow it in 4 hours. How long will Mohan take to mow the lawn if he works alone?</p>",
                    question_hi: "<p>7. रोहन घास के एक बड़े मैदान को काटने में 10 घंटे लेता है। वह और मोहन मिलकर उसी को 4 घंटे में काट सकते हैं । यदि मोहन अकेले काम करता है तो उसे मैदान की घास काटने में कितना समय लगेगा?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>"],
                    solution_en: "<p>7.(c)<br>Total work = LCM(10,4) = 20 unit<br>Efficiency of Rohan = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 2 unit <br>Efficiency of (Mohan + Rohan) = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 5 unit <br>Then, efficiency of Mohan = 5 - 2 = 3 unit<br>Time taken by Mohan to do whole work alone = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>",
                    solution_hi: "<p>7.(c)<br>कुल कार्य = LCM(10,4) = 20 इकाई<br>रोहन की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 2 इकाई<br>(मोहन + रोहन) की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 5 इकाई<br>तब, मोहन की दक्षता = 5 - 2 = 3 इकाई<br>मोहन द्वारा पूरा कार्य अकेले करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. R and S can finish a work in 10 days, S and T can finish it in 12 days and T and R can finish in 8 days. In how many days will the work be finished if they all work simultaneously?</p>",
                    question_hi: "<p>8. R और S एक काम को 10 दिनों में समाप्त कर सकते हैं, S और T इसे 12 दिनों में समाप्त कर सकते हैं और T और R इसे 8 दिनों में समाप्त कर सकते हैं। यदि वे सभी एक साथ मिलकर काम करते हैं, तो काम कितने दिनों में समाप्त हो जाएगा?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>39</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>8.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826180351.png\" alt=\"rId14\" width=\"190\" height=\"100\"><br>Efficiency of R, S and T = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>15</mn></mrow><mn>2</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>2</mn></mfrac></math><br>Required time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>37</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>37</mn></mfrac></math> days</p>",
                    solution_hi: "<p>8.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826180627.png\" alt=\"rId15\" width=\"168\" height=\"103\"><br>R, S और T की दक्षता = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>15</mn></mrow><mn>2</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>2</mn></mfrac></math><br>आवश्यक समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>37</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>37</mn></mfrac></math>दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. R can finish a work in 6 days if he works 10 hours a day. S can finish it in 5 days if he works 8 hours a day. If both start a work and work daily for 6 hours, the work will be finished in _____ days.</p>",
                    question_hi: "<p>9. यदि R प्रतिदिन 10 घंटे काम करता है तो वह एक काम 6 दिनों में पूरा कर सकता है। यदि S प्रतिदिन 8 घंटे काम करता है तो वह इसे 5 दिनों में पूरा कर सकता है। यदि दोनों एक साथ मिलकर काम शुरू करते हैं और प्रतिदिन 6 घंटे काम करते हैं, तो काम कितने दिनों में पूरा हो जाएगा?</p>",
                    options_en: ["<p>5</p>", "<p>4</p>", 
                                "<p>3</p>", "<p>6</p>"],
                    options_hi: ["<p>5</p>", "<p>4</p>",
                                "<p>3</p>", "<p>6</p>"],
                    solution_en: "<p>9.(b) <br>According to the question,<br>R &times; 10 &times; 6 = S &times; 8 &times; 5<br><math display=\"inline\"><mfrac><mrow><mi>R</mi></mrow><mrow><mi>S</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>60</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>Total work = 2 &times; 10 &times; 6 = 120 units<br>Required days = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math> = 4 days</p>",
                    solution_hi: "<p>9.(b) <br>प्रश्न के अनुसार,<br>R &times; 10 &times; 6 = S &times; 8 &times; 5<br><math display=\"inline\"><mfrac><mrow><mi>R</mi></mrow><mrow><mi>S</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>60</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>कुल कार्य = 2 &times; 10 &times; 6 = 120 इकाई<br>आवश्यक दिन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math>&nbsp;= 4 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Tanvi can do a work in 25 days, and Tai can complete the same work in 30 days. They started the work together for 5 days. How much work is left?</p>",
                    question_hi: "<p>10. तन्वी एक काम को 25 दिनों में पूरा कर सकती है, और ताई उसी काम को 30 दिनों में पूरा कर सकती है। उन्होंने एक साथ मिलकर 5 दिनों तक काम किया। काम का कितना अंश शेष है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826180804.png\" alt=\"rId16\" width=\"188\" height=\"109\"><br>Work done by both in 5 days = 5 &times; (6 + 5) = 55 unit<br>Required fraction = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>-</mo><mn>55</mn></mrow><mn>150</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>30</mn></mfrac></math>unit</p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736826181044.png\" alt=\"rId17\" width=\"143\" height=\"123\"><br>दोनों द्वारा 5 दिनों में किया गया कार्य = 5 &times; (6 + 5) = 55 इकाई<br>अभीष्ट भिन्न = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>-</mo><mn>55</mn></mrow><mn>150</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>30</mn></mfrac></math> इकाई</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Rohan can alone finish a work in 12 days and Hari can alone in 16 days. They both work for 3 days and then Rohan left. After that only Hari works and finishes the work. The total work will be completed in:</p>",
                    question_hi: "<p>11. रोहन अकेले एक काम को 12 दिनों में पूरा कर सकता है और हरि अकेले 16 दिनों में पूरा कर सकता है। वे दोनों 3 दिन तक काम करते हैं और फिर रोहन काम छोड़कर चला जाता है। उसके बाद हरि अकेले काम करता है और काम पूरा करता है। कुल काम ________ में पूरा हो जाएगा।</p>",
                    options_en: ["<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>", "<p>9 days</p>", 
                                "<p>12 days</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>"],
                    options_hi: ["<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>", "<p>9 दिन</p>",
                                "<p>12 दिन</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>"],
                    solution_en: "<p>11.(c)<br>Total work = LCM (12 , 16) = 48 <br>Efficiency of Rohan = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 4<br>Efficiency of Hari = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = 3<br>Work done by Rohan + Hari in 3 days = 3 &times; (4 + 3) = 21<br>Remaining work = 48 - 21 = 27<br>Time taken by Hari to complete the remaining work = <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 9 days<br>Hence, total time taken to complete the whole work = 3 + 9 = 12 days</p>",
                    solution_hi: "<p>11.(c)<br>कुल कार्य = LCM (12 , 16) = 48 <br>रोहन की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 4<br>हरि की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = 3<br>रोहन + हरि द्वारा 3 दिन में किया गया कार्य = 3 &times; (4 + 3) = 21<br>शेष कार्य = 48 - 21 = 27<br>शेष कार्य को पूरा करने में हरि द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 9 दिन<br>अतः, पूरे कार्य को पूरा करने में लगा कुल समय = 3 + 9 = 12 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. 30 men working 5 hours a day can do work in 18 days. In how many days will 20 men working 7 hours a day do the same work?</p>",
                    question_hi: "<p>12. 30 आदमी प्रतिदिन 5 घंटे काम करके एक काम को 18 दिनों में पूरा कर सकते हैं। 20 आदमी प्रतिदिन 7 घंटे काम करके उसी काम को कितने दिनों में पूरा करेंगे?</p>",
                    options_en: ["<p>19<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p>16 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>19<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p>16 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>12.(a)<br>M<sub>1</sub>D<sub>1</sub>H<sub>1</sub> = M<sub>2</sub>D<sub>2</sub>H<sub>2</sub><br>30 &times; 18 &times; 5 = 20 &times; D<sub>2</sub>&nbsp;&times; 7<br>D<sub>2</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>18</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>20</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>135</mn><mn>7</mn></mfrac></math> = 19<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> Days</p>",
                    solution_hi: "<p>12.(a)<br>M<sub>1</sub>D<sub>1</sub>H<sub>1</sub> = M<sub>2</sub>D<sub>2</sub>H<sub>2</sub><br>30 &times; 18 &times; 5 = 20 &times; D<sub>2</sub>&nbsp;&times; 7<br>D<sub>2</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>18</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>20</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>135</mn><mn>7</mn></mfrac></math> = 19<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If 6 engineers can complete a work in 5 days and 5 skilled workers can complete the same work in 7 days, then the time taken (in days) to complete the same work by 7 engineers and 4 skilled workers will be:</p>",
                    question_hi: "<p>13. यदि 6 इंजीनियर एक काम को 5 दिनों में पूरा कर सकते हैं और 5 कुशल श्रमिक उसी काम को 7 दिनों में पूरा कर सकते हैं, तो उसी काम को 7 इंजीनियरों और 4 कुशल श्रमिकों द्वारा पूरा करने में कितना&nbsp;समय (दिनों में) लगेगा ?</p>",
                    options_en: ["<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>70</mn></mfrac></math></p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math></p>", 
                                "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>73</mn></mfrac></math></p>", "<p>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math></p>"],
                    options_hi: ["<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>70</mn></mfrac></math></p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math></p>",
                                "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>73</mn></mfrac></math></p>", "<p>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math></p>"],
                    solution_en: "<p>13.(b)<br>According to the question.<br>6 engineers &times; 5 = 5 skilled workers &times; 7<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>engineers</mi><mrow><mi>skilled</mi><mi mathvariant=\"normal\">&#160;</mi><mi>workers</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math><br>Total works = 6 &times; 7 &times; 5 units<br>Required time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mo>(</mo><mn>7</mn><mo>&#215;</mo><mn>7</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>4</mn><mo>&#215;</mo><mn>6</mn><mo>)</mo></mrow></mfrac></math> = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math> days</p>",
                    solution_hi: "<p>13.(b)<br>प्रश्न के अनुसार.<br>6 इंजीनियर &times; 5 = 5 कुशल श्रमिक &times; 7<br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2311;&#2306;&#2332;&#2368;&#2344;&#2367;&#2351;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2325;&#2369;&#2358;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2358;&#2381;&#2352;&#2350;&#2367;&#2325;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math><br>कुल कार्य = 6 &times; 7 &times; 5 इकाई<br>आवश्यक समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mo>(</mo><mn>7</mn><mo>&#215;</mo><mn>7</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>4</mn><mo>&#215;</mo><mn>6</mn><mo>)</mo></mrow></mfrac></math>&nbsp;= 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math> दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A can finish a piece of work by working alone in 4 days. B, while working alone, can finish the same work in 12 days. If both of them work together, then in how many days will the work be completed ?</p>",
                    question_hi: "<p>14. A अकेले काम करके किसी काम को 4 दिनों में पूरा कर सकता है। B, अकेले काम करते हुए, उसी काम&nbsp;को 12 दिनों में पूरा कर सकता है। यदि वे दोनों एक साथ मिलकर काम करते है, तो काम कितने दिनों में&nbsp;पूरा होगा ?</p>",
                    options_en: ["<p>8</p>", "<p>6</p>", 
                                "<p>3</p>", "<p>4</p>"],
                    options_hi: ["<p>8</p>", "<p>6</p>",
                                "<p>3</p>", "<p>4</p>"],
                    solution_en: "<p>14.(c)<br>Required days = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>12</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>12</mn></mrow></mfrac></math> = 3 days</p>",
                    solution_hi: "<p>14.(c)<br>आवश्यक दिन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>12</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>12</mn></mrow></mfrac></math>&nbsp;= 3 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. To finish a work, P takes half of the number of days required by Q to finish it. If they&nbsp;together can finish the work in 36 days, then in how many days Q alone can finish the&nbsp;Work?</p>",
                    question_hi: "<p>15. किसी काम को पूरा करने के लिए, Q को जितने दिन चाहिए, P उससे आधे दिन लेता है। यदि वे एक साथ मिलकर काम को 36 दिनों में पूरा कर सकते हैं, तो Q को अकेले काम पूरा करने में कितने दिन का समय लगेगा?</p>",
                    options_en: ["<p>84</p>", "<p>60</p>", 
                                "<p>24</p>", "<p>108</p>"],
                    options_hi: ["<p>84</p>", "<p>60</p>",
                                "<p>24</p>", "<p>108</p>"],
                    solution_en: "<p>15.(d) <br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &rarr; P : Q<br>Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr; 1 : 2<br>Efficiency &rarr;&nbsp; 2 : 1<br>--------------------------------<br>Total work = 36 &times;&nbsp;3 = 108 unit<br>Time taken by Q alone to finish work = <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 108 days</p>",
                    solution_hi: "<p>15.(d) <br>अनुपात &rarr; P : Q<br>समय&nbsp; &nbsp; &rarr; 1 : 2<br>दक्षता&nbsp; &nbsp;&rarr; 2 : 1<br>--------------------------------<br>कुल कार्य = 36 &times; 3 = 108 इकाई<br>Q द्वारा अकेले कार्य समाप्त करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 108 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>