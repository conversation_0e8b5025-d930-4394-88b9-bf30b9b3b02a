<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Which two numbers should be interchanged to make the given equation correct ?<br>136 &minus; (176 &divide; 11) &times; 8 + 22 &times; 3 = 105<br>(<strong>NOTE :</strong> Numbers must be interchanged and not the constituent digits e.g. if 2 and 3 are to be interchanged in the equation 43 &times; 3 + 4 &divide; 2, then interchanged equation is 43 &times; 2 + 4 &divide; 3)</p>",
                    question_hi: "<p>1. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए ?<br>136 &minus; (176 &divide; 11) &times; 8 + 22 &times; 3 = 105<br>(<strong>ध्यान दें : </strong>संख्याओं को आपस में बदला जाना चाहिए और घटक अंकों को नहीं बदला जाना चाहिए। उदा. यदि समीकरण 43 &times; 3 + 4 &divide; 2 में 2 और 3 को आपस में बदलना है, तो बदला हुआ समीकरण 43 &times; 2 + 4 &divide; 3 होगा)</p>",
                    options_en: [
                        "<p>8 and 11</p>",
                        "<p>11 and 22</p>",
                        "<p>8 and 3</p>",
                        "<p>22 and 176</p>"
                    ],
                    options_hi: [
                        "<p>8 और 11</p>",
                        "<p>11 और 22</p>",
                        "<p>8 और 3</p>",
                        "<p>22 और 176</p>"
                    ],
                    solution_en: "<p>1.(b) <strong>Given :- </strong>136 - (176 &divide; 11) &times; 8 + 22 &times; 3 = 105<br>After going through all the options, option (b) satisfies. After interchanging 11 and 22 we get<br>136 - (176 &divide; 22) &times; 8 + 11 &times; 3<br>136 - (8) &times; 8 + 33<br>136 - 64 + 33 <br>136 - 31 = 105<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>1.(b) <strong>दिया गया :- </strong>136 - (176 &divide; 11) &times; 8 + 22 &times; 3 = 105<br>सभी विकल्पों की जांच करने पर विकल्प (b) संतुष्ट करता है। 11 और 22 को आपस में बदलने पर हमें प्राप्त होता है<br>136 - (176 &divide; 22) &times; 8 + 11 &times; 3<br>136 - (8) &times; 8 + 33<br>136 - 64 + 33 <br>136 - 31 = 105<br>L.H.S. = R.H.S.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Each vowel in the word &lsquo;ISOLATED&rsquo; is changed to the following letter in the English alphabetical order and each consonant is changed to the preceding letter in the English alphabetical order. How many letters are there in the English alphabetical order between the letter which is the third from the left end and the second from the left end in the group of letters thus formed ?</p>",
                    question_hi: "<p>2. शब्द \'ISOLATED\' में प्रत्येक स्वर को अंग्रेजी वर्णानुक्रम में उत्तरवर्ती अक्षर से बदल दिया गया है और प्रत्येक व्यंजन को अंग्रेजी वर्णानुक्रम में पूर्ववर्ती अक्षर से बदल दिया गया है। इस प्रकार बने अक्षरों के समूह में बाएं से तीसरे और बाएं से दूसरे अक्षर के बीच अंग्रेजी वर्णानुक्रम में कितने अक्षर होते हैं ?</p>",
                    options_en: [
                        "<p>Four</p>",
                        "<p>One</p>",
                        "<p>Three</p>",
                        "<p>Two</p>"
                    ],
                    options_hi: [
                        "<p>चार</p>",
                        "<p>एक</p>",
                        "<p>तीन</p>",
                        "<p>दो</p>"
                    ],
                    solution_en: "<p>2.(b) <strong>Given :</strong> ISOLATED<br>As per instructions given in question we get - JRPKBSFC<br>Letter third from left end is &lsquo;P&rsquo; and second from left end is &lsquo;R&rsquo;<br>Letter between &lsquo;P&rsquo; and &lsquo;R&rsquo; = one</p>",
                    solution_hi: "<p>2.(b) <strong>दिया गया :</strong> ISOLATED<br>प्रश्न में दिए गए निर्देशों के अनुसार हमें मिलता है - JRPKBSFC<br>बाएं छोर से तीसरा अक्षर \'P\' है और बाएं छोर से दूसरा अक्षर \'R\' है<br>\'P\' और \'R\' के बीच अक्षर = एक</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the correct mirror image of the given combination when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554235.png\" alt=\"rId4\" width=\"107\" height=\"115\"></p>",
                    question_hi: "<p>3. दिए गए संयोजन के उस सही दर्पण प्रतिबिंब का चयन कीजिए जो नीचे दर्शाए अनुसार दर्पण को MN पर रखे जाने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554235.png\" alt=\"rId4\" width=\"115\" height=\"124\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554358.png\" alt=\"rId5\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554572.png\" alt=\"rId6\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554675.png\" alt=\"rId7\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554798.png\" alt=\"rId8\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554358.png\" alt=\"rId5\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554572.png\" alt=\"rId6\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554675.png\" alt=\"rId7\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554798.png\" alt=\"rId8\"></p>"
                    ],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554358.png\" alt=\"rId5\"></p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554358.png\" alt=\"rId5\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language, &lsquo;BOTH&rsquo; is written as &lsquo;DQVJ&rsquo; and &lsquo;DARE&rsquo; is written as &lsquo;FCTG&rsquo; How will &lsquo;WILL&rsquo; be written in that language ?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में, \'BOTH\' को \'DQVJ\' के रूप में लिखा जाता है और \'DARE\' को \'FCTG\' के रूप में लिखा जाता है। उसी भाषा में \'WILL\' को किस प्रकार लिखा जाएगा ?</p>",
                    options_en: [
                        " XJMM ",
                        " YJMN ",
                        " XKNM ",
                        " YKNN"
                    ],
                    options_hi: [
                        " XJMM ",
                        " YJMN ",
                        " XKNM ",
                        " YKNN"
                    ],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554940.png\" alt=\"rId9\" width=\"111\" height=\"87\">&nbsp; &nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555062.png\" alt=\"rId10\" width=\"115\" height=\"88\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555190.png\" alt=\"rId11\" width=\"114\" height=\"89\"></p>",
                    solution_hi: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265554940.png\" alt=\"rId9\" width=\"111\" height=\"87\">&nbsp; &nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555062.png\" alt=\"rId10\" width=\"115\" height=\"88\"><br>इसी प्रकार, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555190.png\" alt=\"rId11\" width=\"114\" height=\"89\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. A paper is folded and cut as shown below. How will it appear when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555362.png\" alt=\"rId12\" width=\"205\" height=\"113\"></p>",
                    question_hi: "<p>5. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555362.png\" alt=\"rId12\" width=\"205\" height=\"113\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555516.png\" alt=\"rId13\" width=\"88\" height=\"130\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555599.png\" alt=\"rId14\" width=\"84\" height=\"137\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555748.png\" alt=\"rId15\" width=\"85\" height=\"128\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555872.png\" alt=\"rId16\" width=\"89\" height=\"132\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555516.png\" alt=\"rId13\" width=\"87\" height=\"129\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555599.png\" alt=\"rId14\" width=\"85\" height=\"138\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555748.png\" alt=\"rId15\" width=\"84\" height=\"127\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555872.png\" alt=\"rId16\" width=\"87\" height=\"129\"></p>"
                    ],
                    solution_en: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555516.png\" alt=\"rId13\" width=\"85\" height=\"126\"></p>",
                    solution_hi: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265555516.png\" alt=\"rId13\" width=\"85\" height=\"126\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6.Three of the following numbers are alike in a certain way and one is different. Pick the odd one out.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>6. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए।<br>(<strong>नोट : </strong>गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>12 &ndash; 7 &ndash; 209</p>",
                        "<p>19 &ndash; 11 &ndash; 330</p>",
                        "<p>23 &ndash; 8 &ndash; 343</p>",
                        "<p>15 &ndash; 9 &ndash; 264</p>"
                    ],
                    options_hi: [
                        "<p>12 &ndash; 7 &ndash; 209</p>",
                        "<p>19 &ndash; 11 &ndash; 330</p>",
                        "<p>23 &ndash; 8 &ndash; 343</p>",
                        "<p>15 &ndash; 9 &ndash; 264</p>"
                    ],
                    solution_en: "<p>6.(c) <br><strong>Logic :</strong> (1st number &times; 10 + 2nd number &times; 10) + (1st number + 2nd number) = 3rd number.<br>12 &ndash; 7 &ndash; 209 :- (12 &times; 10 + 7 &times; 10) + (12 + 7) = 209<br>19 &ndash; 11 &ndash; 330 :- (19 &times; 10 + 11 &times; 10) + (19 + 11) = 330<br>15 &ndash; 9 &ndash; 264 :- (15 &times; 10 + 9 &times; 10) + (15 + 9) = 264<br>But<br>23 &ndash; 8 &ndash; 343 :- (23 &times; 10 + 8 &times; 10) + (23 + 8) = 341 (&ne;343)</p>",
                    solution_hi: "<p>6.(c)<br><strong>तर्क :</strong> (पहली संख्या &times; 10 + दूसरी संख्या &times; 10) + (पहली संख्या + दूसरी संख्या) = तीसरी संख्या।<br>12 &ndash; 7 &ndash; 209 :- (12 &times; 10 + 7 &times; 10) + (12 + 7) = 209<br>19 &ndash; 11 &ndash; 330 :- (19 &times; 10 + 11 &times; 10) + (19 + 11) = 330<br>15 &ndash; 9 &ndash; 264 :- (15 &times; 10 + 9 &times; 10) + (15 + 9) = 264<br>लेकिन<br>23 &ndash; 8 &ndash; 343 :- (23 &times; 10 + 8 &times; 10) + (23 + 8) = 341 (&ne;343)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556010.png\" alt=\"rId17\" width=\"335\" height=\"77\"></p>",
                    question_hi: "<p>7. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556010.png\" alt=\"rId17\" width=\"335\" height=\"77\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556183.png\" alt=\"rId18\" width=\"86\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556359.png\" alt=\"rId19\" width=\"86\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556465.png\" alt=\"rId20\" width=\"86\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556574.png\" alt=\"rId21\" width=\"85\" height=\"86\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556183.png\" alt=\"rId18\" width=\"87\" height=\"82\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556359.png\" alt=\"rId19\" width=\"86\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556465.png\" alt=\"rId20\" width=\"87\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556574.png\" alt=\"rId21\" width=\"85\" height=\"86\"></p>"
                    ],
                    solution_en: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556465.png\" alt=\"rId20\" width=\"87\" height=\"84\"></p>",
                    solution_hi: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556465.png\" alt=\"rId20\" width=\"87\" height=\"84\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. If 6 May 2005 is a Friday, then what will the day of the week on 25 September 2018 ?</p>",
                    question_hi: "<p>8. यदि 6 मई 2005 को शुक्रवार है, तो 25 सितंबर 2018 को सप्ताह का कौन-सा दिन होगा ?</p>",
                    options_en: [
                        "<p>Sunday</p>",
                        "<p>Tuesday</p>",
                        "<p>Thursday</p>",
                        "<p>Friday</p>"
                    ],
                    options_hi: [
                        "<p>रविवार</p>",
                        "<p>मंगलवार</p>",
                        "<p>गुरुवार</p>",
                        "<p>शुक्रवार</p>"
                    ],
                    solution_en: "<p>8.(b) 6 May 2005 is friday. The number of odd days in going from 2005 to 2018 is <br>+1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 = 16. We have reached till 6 may 2018 Now we have to go to 25 sept 2018. The number of days between = 25 + 30 + 31 + 31 + 25 = 142. On dividing 142 by 7, remainder is 2. Total number of odd days are = 16 + 2 = 18 .Dividing 18 by 7 remainder = 4.<br>Friday + 4 = Tuesday.</p>",
                    solution_hi: "<p>8.(b) 6 मई 2005 को शुक्रवार है. 2005 से 2018 तक विषम दिनों की संख्या है <br>+1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 = 16 । हम 6 मई 2018 तक पहुंच गए हैं अब हमें 25 सितंबर 2018 तक जाना है. बीच में दिनों की संख्या = 25 + 30 + 31 + 31 + 25 = 142, 142 को 7 से विभाजित करने पर शेषफल 2 है। विषम दिनों की कुल संख्या = 16 + 2 = 18 है। 18 को 7 से विभाजित करने पर शेषफल = 4 है।<br>शुक्रवार + 4 = मंगलवार.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Four years ago, Ravi was four times as old as Kavya. Seven years from now, Ravi will be three times as old as Kavya. Find the sum of their present ages.</p>",
                    question_hi: "<p>9. चार वर्ष पूर्व, रवि की आयु, काव्या की आयु की चार गुनी थी। अब से सात वर्ष बाद, रवि की आयु, काव्या की आयु की तीन गुनी होगी। उनकी वर्तमान आयु का योग ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>122 years</p>",
                        "<p>117 years</p>",
                        "<p>118 years</p>",
                        "<p>115 years</p>"
                    ],
                    options_hi: [
                        "<p>122 वर्ष</p>",
                        "<p>117 वर्ष</p>",
                        "<p>118 वर्ष</p>",
                        "<p>115 वर्ष</p>"
                    ],
                    solution_en: "<p>9.(c) Let 4 years ago age of kavya be <math display=\"inline\"><mi>x</mi></math> year.<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp; Ravi&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;Kavya<br>4 years ago&nbsp; &rarr;&nbsp; &nbsp; &nbsp; &nbsp;4x&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;x <br>Present age &rarr;&nbsp; &nbsp; &nbsp; 4x+ 4&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;x + 4<br>7 years later &rarr;&nbsp; 4x + 4 + 7)&nbsp; :&nbsp; &nbsp; x + 4 + 7<br>According to question ,<br>4<math display=\"inline\"><mi>x</mi></math> + 4 + 7 = 3(x + 4 + 7)<br>&rArr; 4x + 11 = 3(x + 11)<br>&rArr; 4x + 11 = 3x + 33<br>&rArr; x = 22 years<br>Sum of present age = 4<math display=\"inline\"><mi>x</mi></math> + 4 + x + 4 = 5x + 8<br>= 5 &times; 22 + 8 = 118 years</p>",
                    solution_hi: "<p>9.(c) माना 4 वर्ष पहले काव्या की आयु <math display=\"inline\"><mi>x</mi></math> वर्ष थी।<br>अनुपात&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;रवि&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;काव्य<br>4 साल पहले&nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;4x&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; x <br>वर्तमान आयु&nbsp; &nbsp;&rarr;&nbsp; &nbsp;4x+ 4&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; x + 4<br>7 वर्ष बाद&nbsp; &nbsp; &nbsp; &rarr; 4x + 4 + 7)&nbsp; :&nbsp; x + 4 + 7<br>प्रश्न के अनुसार,<br>4<math display=\"inline\"><mi>x</mi></math> + 4 + 7 = 3(x + 4 + 7)<br>&rArr; 4x + 11 = 3(x + 11)<br>&rArr; 4x + 11 = 3x + 33<br>&rArr; x = 22 वर्ष<br>वर्तमान आयु का योग = 4<math display=\"inline\"><mi>x</mi></math> + 4 + x + 4 = 5x + 8<br>= 5 &times; 22 + 8 = 118 वर्ष</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and also &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?&nbsp;<br>11 &divide; 12 - 78 &times; 6 + 54 = ?</p>",
                    question_hi: "<p>10. निम्नलिखित समीकरण में \'?\' के स्थान पर क्या आएगा, यदि \'+\' और \'&ndash;\' को आपस में बदल दिया जाए तथा \'&times;\' और \'&divide;\' को भी आपस में बदल दिया जाए ?<br>11 &divide; 12 - 78 &times; 6 + 54 = ?</p>",
                    options_en: [
                        " 99 ",
                        " 86 ",
                        "<p>104</p>",
                        " 91"
                    ],
                    options_hi: [
                        " 99 ",
                        " 86 ",
                        " 104",
                        " 91"
                    ],
                    solution_en: "<p>10.(d) <strong>Given :- </strong>11 &divide; 12 - 78 &times; 6 + 54<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get<br>11 &times; 12 + 78 &divide; 6 - 54<br>132 + 13 - 54<br>132 - 41 = 91</p>",
                    solution_hi: "<p>10.(d) <strong>दिया गया :-</strong> 11 &divide; 12 - 78 &times; 6 + 54<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>11 &times; 12 + 78 &divide; 6 - 54<br>132 + 13 - 54<br>132 - 41 = 91</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "11. Six students are sitting around a circular table facing the centre. Van is sitting second to the left of Luke. Luke is an immediate neighbour of both Ruth and Max. Jude is sitting second to the left of Van. Ruth is an immediate neighbour of both Luke and Van. Who is the immediate neighbour of Jude and Luke ?",
                    question_hi: "11. छह विद्यार्थी एक गोल मेज के परितः इसके केंद्र की ओर अभिमुख होकर बैठे हैं। वेन, ल्यूक के बाईं ओर दूसरे स्थान पर बैठा है। ल्यूक, रूथ और मैक्स दोनों के ठीक बगल में बैठा है। ज्यूड, वैन के बाईं ओर दूसरे स्थान पर बैठा है। रूथ, ल्यूक और वेन दोनों के ठीक बगल में बैठा है। कौन ज्यूड और ल्यूक दोनों के ठीक बगल में बैठा है ?",
                    options_en: [
                        " Tess",
                        " Max",
                        " Ruth",
                        " Van"
                    ],
                    options_hi: [
                        " टेस",
                        " मैक्स",
                        " रूथ",
                        " वेन"
                    ],
                    solution_en: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556700.png\" alt=\"rId22\" width=\"297\" height=\"204\"></p>",
                    solution_hi: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556810.png\" alt=\"rId23\" width=\"288\" height=\"189\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Six letters J, T, E, X, I and O are written on different faces of a dice. Two positions of this dice are shown in the figures below. Find the letter on the face opposite to X.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556960.png\" alt=\"rId24\" width=\"140\" height=\"84\"></p>",
                    question_hi: "<p>12. एक पासे के विभिन्न फलकों पर छह अक्षर J, T, E, X, I और O लिखे गए हैं। इस पासे की दो स्थितियाँ नीचे चित्र में दर्शाई गई हैं। X के विपरीत वाले फलक पर आने वाला अक्षर ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265556960.png\" alt=\"rId24\" width=\"140\" height=\"84\"></p>",
                    options_en: [
                        "<p>I</p>",
                        "<p>E</p>",
                        "<p>O</p>",
                        "<p>T</p>"
                    ],
                    options_hi: [
                        "<p>I</p>",
                        "<p>E</p>",
                        "<p>O</p>",
                        "<p>T</p>"
                    ],
                    solution_en: "<p>12.(d)<br>From the two dice the opposite faces are<br>J &harr; O , E &harr; I , T &harr; X</p>",
                    solution_hi: "<p>12.(d)<br>दो पासों के विपरीत फलक हैं<br>J &harr; O , E &harr; I , T &harr; X</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. While introducing Madhav, Jihaan said, &ldquo;I am the only son of one the sons of his mother.&rdquo; How is Madhav related to Jihaan ?</p>",
                    question_hi: "<p>13. माधव का परिचय देते हुए जिहान ने कहा कि \'\'मैं उसकी माँ के किसी एक पुत्र का इकलौता पुत्र हूं\'\'। माधव जिहान से किस प्रकार संबंधित है ?</p>",
                    options_en: [
                        "<p>Maternal nephew or paternal nephew</p>",
                        "<p>Father or paternal uncle</p>",
                        "<p>Cousin</p>",
                        "<p>Son or brother</p>"
                    ],
                    options_hi: [
                        "<p>भांजा या भतीजा</p>",
                        "<p>पिता या चाचा</p>",
                        "<p>चचेरा भाई</p>",
                        "<p>पुत्र या भाई</p>"
                    ],
                    solution_en: "<p>13.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265557085.png\" alt=\"rId25\" width=\"78\" height=\"176\"><br><strong>Case I :- </strong>If Madhav is the only child of his mother<br>Then Madhav will be his father<br><strong>Case II :- </strong>When Madhav is not the only son of his Mother<br>In this case Madhav will be his Paternal Uncle.</p>",
                    solution_hi: "<p>13.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265557260.png\" alt=\"rId26\" width=\"75\" height=\"169\"><br><strong>केस I :-</strong> यदि माधव अपनी माँ की इकलौती संतान है<br>तब माधव उसके पिता होंगे<br><strong>केस II :- </strong>जब माधव अपनी माँ का इकलौता बेटा नहीं है<br>इस स्थिति में माधव उसके चाचा होंगे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In this question, two statements are given, followed by two conclusions numbered I and II. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusion(s) logically follows/follow from the statements.<br><strong>Statements:</strong> <br>All cows are goats. <br>Some goats are lions.<br><strong>Conclusions:</strong><br>I. All lions are goats.<br>II. Some cows are lions.</p>",
                    question_hi: "<p>14. इस प्रश्न में दो कथन दिए गए हैं, जिसके बाद दो निष्कर्ष I और II दिए गए हैं। कथनों को सत्य मानते हुए, चाहे वह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों और निर्णय लीजिए कि दिए गए निष्कर्षों में से कौन-सा/कौन-से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन:</strong><br>सभी गाय, बकरियां हैं।<br>कुछ बकरियां, शेर हैं।<br><strong>निष्&zwj;कर्ष:</strong><br>I. सभी शेर, बकरियां हैं।<br>II. कुछ गाय, शेर हैं।</p>",
                    options_en: [
                        "<p>Neither Conclusion I nor II follow.</p>",
                        "<p>Only Conclusion II follows.</p>",
                        "<p>Only Conclusion I follows.</p>",
                        "<p>Both Conclusions I and II follow.</p>"
                    ],
                    options_hi: [
                        "<p>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है।</p>",
                        "<p>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>"
                    ],
                    solution_en: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265557411.png\" alt=\"rId27\" width=\"270\" height=\"114\"><br>Neither Conclusion I nor II follow.</p>",
                    solution_hi: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265557528.png\" alt=\"rId28\" width=\"261\" height=\"111\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. What should come in place of the question mark (?) in the given series based on the English alphabetical order ?&nbsp;<br>ECZ, HFC, KIF, NLI, ?</p>",
                    question_hi: "<p>15. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न चिह्न &lsquo;?&rsquo; के स्थान पर क्या आना चाहिए ?<br>ECZ, HFC, KIF, NLI, ?</p>",
                    options_en: [
                        "<p>QLO</p>",
                        "<p>OLQ</p>",
                        "<p>OQL</p>",
                        "<p>QOL</p>"
                    ],
                    options_hi: [
                        "<p>QLO</p>",
                        "<p>OLQ</p>",
                        "<p>OQL</p>",
                        "<p>QOL</p>"
                    ],
                    solution_en: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265557727.png\" alt=\"rId29\" width=\"271\" height=\"91\"></p>",
                    solution_hi: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265557727.png\" alt=\"rId29\" width=\"271\" height=\"91\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. How many people like either only cars or only Volkswagen vehicles as per the given Venn diagram ?&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265557900.png\" alt=\"rId30\" width=\"156\" height=\"175\"></p>",
                    question_hi: "<p>16. दिए गए वेन आरेख के अनुसार कितने लोग या तो केवल कार या केवल फॉक्सवैगन वाहन पसंद करते हैं ?&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265558035.png\" alt=\"rId31\" width=\"158\" height=\"186\"></p>",
                    options_en: [
                        "<p>85</p>",
                        "<p>66</p>",
                        "<p>28</p>",
                        "<p>75</p>"
                    ],
                    options_hi: [
                        "<p>85</p>",
                        "<p>66</p>",
                        "<p>28</p>",
                        "<p>75</p>"
                    ],
                    solution_en: "<p>16.(a) The number of people likes either only cars or only Volkswagon = 47 + 38 = 85</p>",
                    solution_hi: "<p>16.(a) या तो केवल कारें या केवल वोक्सवैगन पसंद करने वाले लोगों की संख्या = 47 + 38 = 85</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>PURE : IDGT :: COOL : VJJM :: LIPS : ?</p>",
                    question_hi: "<p>17. उस विकल्प का चयन करें जो पांचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है।<br>PURE : IDGT :: COOL : VJJM :: LIPS : ?</p>",
                    options_en: [
                        "<p>MPIF</p>",
                        "<p>PIFM</p>",
                        "<p>KHOR</p>",
                        "<p>RKHO</p>"
                    ],
                    options_hi: [
                        "<p>MPIF</p>",
                        "<p>PIFM</p>",
                        "<p>KHOR</p>",
                        "<p>RKHO</p>"
                    ],
                    solution_en: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265558157.png\" alt=\"rId32\" width=\"100\" height=\"190\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265558382.png\" alt=\"rId33\" width=\"103\" height=\"190\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265558539.png\" alt=\"rId34\" width=\"101\" height=\"194\"></p>",
                    solution_hi: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265558671.png\" alt=\"rId35\" width=\"97\" height=\"186\">&nbsp; &nbsp; ,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265558772.png\" alt=\"rId36\" width=\"99\" height=\"184\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265558895.png\" alt=\"rId37\" width=\"94\" height=\"181\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the option that represents the letters that, when sequentially placed from left to right in the blanks below, will complete the letter series.<br>SO_T_S_F_Y_ OFT_ _OFTY</p>",
                    question_hi: "<p>18. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है, जिन्हें नीचे दिए गए रिक्त स्थानों में क्रमिक रूप से बाएं से दाएं रखने पर अक्षर श्रृंखला पूर्ण हो जाएगी।<br>SO_T_S_F_Y_ OFT_ _OFTY</p>",
                    options_en: [
                        "<p>FYOTSYS</p>",
                        "<p>SOTTY FS</p>",
                        "<p>OFSTYFO</p>",
                        "<p>YFOTSYT</p>"
                    ],
                    options_hi: [
                        "<p>FYOTSYS</p>",
                        "<p>SOTTY FS</p>",
                        "<p>OFSTYFO</p>",
                        "<p>YFOTSYT</p>"
                    ],
                    solution_en: "<p>18.(a)<br>SO<span style=\"text-decoration: underline;\"><strong>F</strong></span>T<span style=\"text-decoration: underline;\"><strong>Y</strong></span> / S<span style=\"text-decoration: underline;\"><strong>O</strong></span>F<span style=\"text-decoration: underline;\"><strong>T</strong></span>Y /<span style=\"text-decoration: underline;\"><strong>S</strong></span> OFT<span style=\"text-decoration: underline;\"><strong>Y</strong></span> / <span style=\"text-decoration: underline;\"><strong>S</strong></span>OFTY</p>",
                    solution_hi: "<p>18.(a)<br>SO<span style=\"text-decoration: underline;\"><strong>F</strong></span>T<span style=\"text-decoration: underline;\"><strong>Y</strong></span> / S<span style=\"text-decoration: underline;\"><strong>O</strong></span>F<span style=\"text-decoration: underline;\"><strong>T</strong></span>Y /<span style=\"text-decoration: underline;\"><strong>S</strong></span> OFT<span style=\"text-decoration: underline;\"><strong>Y</strong></span> / <span style=\"text-decoration: underline;\"><strong>S</strong></span>OFTY</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. What should come in place of X in the given series ?<br>3&nbsp; &nbsp;9&nbsp; &nbsp;21&nbsp; &nbsp;41&nbsp; &nbsp;71&nbsp; &nbsp;X</p>",
                    question_hi: "<p>19. दी गई श्रृंखला में \'X\' के स्थान पर क्या आना चाहिए ?<br>3&nbsp; &nbsp;9&nbsp; &nbsp;21&nbsp; &nbsp;41&nbsp; &nbsp;71&nbsp; &nbsp;X</p>",
                    options_en: [
                        "<p>110</p>",
                        "<p>113</p>",
                        "<p>120</p>",
                        "<p>117</p>"
                    ],
                    options_hi: [
                        "<p>110</p>",
                        "<p>113</p>",
                        "<p>120</p>",
                        "<p>117</p>"
                    ],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559016.png\" alt=\"rId38\" width=\"274\" height=\"113\"></p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559016.png\" alt=\"rId38\" width=\"274\" height=\"113\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option in which the given figure is embedded (rotation is not allowed). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559120.png\" alt=\"rId39\" width=\"116\" height=\"115\"></p>",
                    question_hi: "<p>20. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति निहित है (घूर्णन की अनुमति नहीं है)। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559120.png\" alt=\"rId39\" width=\"116\" height=\"115\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559215.png\" alt=\"rId40\" width=\"94\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559356.png\" alt=\"rId41\" width=\"94\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559491.png\" alt=\"rId42\" width=\"94\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559626.png\" alt=\"rId43\" width=\"95\" height=\"94\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559215.png\" alt=\"rId40\" width=\"94\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559356.png\" alt=\"rId41\" width=\"93\" height=\"85\"></p>",
                        "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559491.png\" alt=\"rId42\" width=\"95\" height=\"82\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559626.png\" alt=\"rId43\" width=\"92\" height=\"91\"></p>"
                    ],
                    solution_en: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559754.png\" alt=\"rId44\" width=\"108\" height=\"106\"></p>",
                    solution_hi: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559754.png\" alt=\"rId44\" width=\"108\" height=\"106\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group ?<br>(<strong>Note: </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>21. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक&nbsp;समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है ?<br>(<strong>ध्यान दें: </strong>असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        " RVZ",
                        " LPT",
                        " GJN",
                        " BFJ"
                    ],
                    options_hi: [
                        " RVZ",
                        " LPT",
                        " GJN",
                        " BFJ"
                    ],
                    solution_en: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559846.png\" alt=\"rId45\" width=\"135\" height=\"87\">&nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559944.png\" alt=\"rId46\" width=\"144\" height=\"84\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560104.png\" alt=\"rId47\" width=\"156\" height=\"78\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560202.png\" alt=\"rId48\" width=\"136\" height=\"79\"></p>",
                    solution_hi: "<p>21.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559846.png\" alt=\"rId45\" width=\"135\" height=\"87\">&nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265559944.png\" alt=\"rId46\" width=\"144\" height=\"84\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560104.png\" alt=\"rId47\" width=\"156\" height=\"78\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560202.png\" alt=\"rId48\" width=\"136\" height=\"79\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option figure that will replace the question mark (?) in the figure given below to complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560359.png\" alt=\"rId49\" width=\"123\" height=\"122\"></p>",
                    question_hi: "<p>22. उस विकल्प आकृति का चयन कीजिए जो पैटर्न को पूरा करने के लिए नीचे दी गई आकृति में प्रश्न चिह्न (?) के स्थान पर आएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560359.png\" alt=\"rId49\" width=\"123\" height=\"122\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560477.png\" alt=\"rId50\" width=\"71\" height=\"124\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560600.png\" alt=\"rId51\" width=\"71\" height=\"130\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560743.png\" alt=\"rId52\" width=\"71\" height=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560875.png\" alt=\"rId53\" width=\"71\" height=\"136\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560477.png\" alt=\"rId50\" width=\"71\" height=\"124\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560600.png\" alt=\"rId51\" width=\"71\" height=\"130\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560743.png\" alt=\"rId52\" width=\"71\" height=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560875.png\" alt=\"rId53\" width=\"71\" height=\"136\"></p>"
                    ],
                    solution_en: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560477.png\" alt=\"rId50\" width=\"72\" height=\"126\"></p>",
                    solution_hi: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560477.png\" alt=\"rId50\" width=\"72\" height=\"126\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "23. In a certain code language, \'if you are\' is coded as \'go so lu\' and \'where are you\' is coded as \'so lu ga. What is the code for \'where\' in that language?",
                    question_hi: "23. एक निश्चित कूट भाषा में, \'if you are\' को \'go so lu\' लिखा जाता है और \'where are you\' को \'so lu ga\' लिखा जाता है। तो उस कूट भाषा में \'where\' को कैसे लिखा जाएगा?",
                    options_en: [
                        " go ",
                        " lu ",
                        " so ",
                        " ga<br /> "
                    ],
                    options_hi: [
                        " go ",
                        " lu ",
                        " so ",
                        " ga"
                    ],
                    solution_en: "23.(d) if you are → go so lu……(i)<br />               where are you → so lu ga……(ii)<br />From (i) and (ii) ‘you are’ and ‘so lu’ are common. The code of ‘where’ = ‘ga’",
                    solution_hi: "23.(d)  if you are → go so lu……(i)<br />               where are you → so lu ga……(ii)<br />(i) और (ii) से \'you are \' और \'so lu \' उभय-निष्ठ हैं। \'where\' का कूट = \'ga\'",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560977.png\" alt=\"rId54\" width=\"141\" height=\"129\"></p>",
                    question_hi: "<p>24. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265560977.png\" alt=\"rId54\" width=\"141\" height=\"129\"></p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>11</p>",
                        "<p>14</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>11</p>",
                        "<p>14</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265561088.png\" alt=\"rId55\" width=\"150\" height=\"142\"><br>There are 14 triangle<br>ADK, AKB, KBI, BIC, CIH, HGI, KJF, KEF, EDK, AKE, AEF, BCH, AEB, EBH</p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265561088.png\" alt=\"rId55\" width=\"150\" height=\"142\"><br>14 त्रिभुज हैं<br>ADK, AKB, KBI, BIC, CIH, HGI, KJF, KEF, EDK, AKE, AEF, BCH, AEB, EBH</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. In the following number-pairs, the second number is obtained by applying certain mathematical operations to the first number. Select the set in which the numbers are related in the same way as are the numbers of the following sets. <br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13-Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>(96, 29) <br>(112, 33)</p>",
                    question_hi: "<p>25. निम्नलिखित संख्या-युग्&zwj;मों में, पहली संख्या पर निश्चित गणितीय संक्रियाएँ करके दूसरी संख्या प्राप्त की जाती है। उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित समुच्&zwj;चय की संख्याएँ संबंधित हैं। <br>(<strong>नोट:</strong> संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।) <br>(96, 29) <br>(112, 33)</p>",
                    options_en: [
                        " (204, 55) ",
                        " (176, 45) ",
                        " (188, 52) ",
                        " (108, 30)"
                    ],
                    options_hi: [
                        " (204, 55) ",
                        " (176, 45) ",
                        " (188, 52) ",
                        " (108, 30)"
                    ],
                    solution_en: "<p>25.(c)<br><strong>Logic:- </strong><math display=\"inline\"><mfrac><mrow><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 5 = 2<sup>nd</sup>no. <br>(96, 29) :- <math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 5 &rArr; 24 + 5 = 29<br>(112, 33) :- <math display=\"inline\"><mfrac><mrow><mn>112</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 5 &rArr; 28 + 5 = 33<br>similarly<br>(188, 52) :- <math display=\"inline\"><mfrac><mrow><mn>188</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 5 &rArr; 47 + 5 = 52</p>",
                    solution_hi: "<p>25.(c)<br><strong>तर्क :-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow><mn>4</mn></mfrac></math> + 5 = दूसरी संख्या <br>(96, 29) :- <math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 5 &rArr; 24 + 5 = 29<br>(112, 33) :- <math display=\"inline\"><mfrac><mrow><mn>112</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 5 &rArr; 28 + 5 = 33<br>इसी प्रकार <br>(188, 52) :- <math display=\"inline\"><mfrac><mrow><mn>188</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 5 &rArr; 47 + 5 = 52</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. In which of the following styles of temple architecture the spire is called Deul ?</p>",
                    question_hi: "<p>26. मंदिर वास्तुकला की निम्नलिखित में से किस शैली में शिखर को देउल कहा जाता है ?</p>",
                    options_en: [
                        "<p>Odisha</p>",
                        "<p>Dravid</p>",
                        "<p>Nagar</p>",
                        "<p>Vesara</p>"
                    ],
                    options_hi: [
                        "<p>ओडिशा</p>",
                        "<p>द्रविड़</p>",
                        "<p>नागर</p>",
                        "<p>वेसरा</p>"
                    ],
                    solution_en: "<p>26.(a) <strong>Odisha. </strong>Odisha&rsquo;s temple architecture falls under the Kalinga style of temple architecture, named after the state\'s Kalinga dynasty. Deuls are preceded, as usual, by mandapas called jagamohana in Odisha. The main architectural features of Odisha temples are classified in three orders - Rekha deul, Pidhadeul and Khakra deul. The basic form of the Hindu temple: Sanctum, Mandapa, Shikhar, Vimana, Vahan.</p>",
                    solution_hi: "<p>26.(a) <strong>ओडिशा।</strong> ओडिशा की मंदिर वास्तुकला मंदिर वास्तुकला की कलिंग शैली के अंतर्गत आती है, जिसका नाम राज्य के कलिंग राजवंश के नाम पर रखा गया है। देउल के पहले, हमेशा की तरह, मंडप होते हैं जिन्हें ओडिशा में जगमोहन कहा जाता है। ओडिशा के मंदिरों की मुख्य स्थापत्य विशेषताओं को तीन क्रमों में वर्गीकृत किया गया है- रेखा देउल, पिधा देउल और खाकरा देउल । हिंदू मंदिर का मूल स्वरूप: गर्भगृह, मंडप, शिखर, विमान, वाहन ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Sitara Devi was a ________dancer of India.</p>",
                    question_hi: "<p>27. सितारा देवी भारत की _______नृत्यांगना थीं।</p>",
                    options_en: [
                        "<p>Kuchipudi</p>",
                        "<p>Kathak</p>",
                        "<p>Bharatanatyam</p>",
                        "<p>Kathakali</p>"
                    ],
                    options_hi: [
                        "<p>कुचिपूड़ी</p>",
                        "<p>कथक</p>",
                        "<p>भरतनाट्यम</p>",
                        "<p>कथकली</p>"
                    ],
                    solution_en: "<p>27.(b) <strong>Kathak.</strong> Sitara Devi was depicted as the Nritya Samragini by Rabindranath Tagore. Her Awards - Sangeet Natak Akademi award (1969), Padma Shri (1973), Kalidas Samman (1995), Lifetime Achievement Award (2011). Other famous Kathak dancers of India - Shambhu Maharaj, Shovna Narayan, Pandit Birju Maharaj. Kathak is a dance form that originated in Uttar Pradesh.</p>",
                    solution_hi: "<p>27.(b) <strong>कथक I</strong> सितारा देवी को रवीन्द्रनाथ टैगोर ने नृत्य साम्राज्ञी के रूप में चित्रित किया था। पुरस्कार - संगीत नाटक अकादमी पुरस्कार (1969), पद्म श्री पुरस्कार (1973), कालिदास सम्मान पुरस्कार (1995), लाइफटाइम अचीवमेंट पुरस्कार (2011)। भारत के अन्य प्रसिद्ध कथक नर्तक - शंभू महाराज, शोवना नारायण, पंडित बिरजू महाराज। कथक एक नृत्य शैली है, जिसकी उत्पत्ति उत्तर प्रदेश में हुई थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Price control and rationing are direct control measures to check _______.</p>",
                    question_hi: "<p>28. मूल्य नियंत्रण और राशनिंग (rationing) _______की जाँच के लिए प्रत्यक्ष नियंत्रण उपाय हैं।</p>",
                    options_en: [
                        "<p>deflation</p>",
                        "<p>reflation</p>",
                        "<p>inflation</p>",
                        "<p>disinflation</p>"
                    ],
                    options_hi: [
                        "<p>अपस्फीति</p>",
                        "<p>प्रत्यवस्फीति</p>",
                        "<p>मुद्रास्फ़ीति</p>",
                        "<p>विस्फीति</p>"
                    ],
                    solution_en: "<p>28.(c)<strong> Inflation:</strong> It is a decrease in the purchasing power of money, reflected in a general increase in the prices of goods and services in an economy. Lack of financial discipline by the government can lead to excess expenditure and inflation.</p>",
                    solution_hi: "<p>28.(c) <strong>मुद्रास्फीति: </strong>यह मुद्रा की क्रय शक्ति में कमी है, जो अर्थव्यवस्था में वस्तुओं और सेवाओं की कीमतों में सामान्य वृद्धि में परिलक्षित होती है। सरकार द्वारा वित्तीय अनुशासन की कमी से अत्यधिक व्यय और मुद्रास्फीति हो सकती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. During the reign of which of the Pallava kings was the shore temple at Mahabalipuram built ?</p>",
                    question_hi: "<p>29. किस पल्लव राजा के शासनकाल के दौरान महाबलीपुरम में शोर मंदिर का निर्माण कराया गया था ?</p>",
                    options_en: [
                        "<p>Paramesvaravarman I</p>",
                        "<p>Mahendravarman II</p>",
                        "<p>Narasimhavarman I</p>",
                        "<p>Narasimhavarman II</p>"
                    ],
                    options_hi: [
                        "<p>परमेश्वरवर्मन प्रथम</p>",
                        "<p>महेंद्रवर्मन द्वितीय</p>",
                        "<p>नरसिंहवर्मन प्रथम</p>",
                        "<p>नरसिंहवर्मन द्वितीय</p>"
                    ],
                    solution_en: "<p>29.(d) <strong>Narasimhavarman II, </strong>also known as Rajasimha, ruled the Pallava dynasty from 700 to 728 CE. He also constructed the Kailasanatha Temple in Kanchipuram.</p>",
                    solution_hi: "<p>29.(d) <strong>नरसिंहवर्मन द्वितीय,</strong> जिन्हें राजसिम्हा के नाम से भी जाना जाता है, ने 700 से 728 ई. तक पल्लव वंश पर शासन किया। उन्होंने कांचीपुरम में कैलाशनाथ मंदिर का भी निर्माण करवाया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Where has India&rsquo;s first diabetes biobank been launched in December 2024 ?<strong id=\"docs-internal-guid-096e1c33-7fff-7310-f626-da4dc29afc61\"><br></strong></p>",
                    question_hi: "<p>30. दिसंबर 2024 में भारत का पहला डायबिटीज बायोबैंक कहाँ शुरू किया गया है ?</p>",
                    options_en: [
                        "<p>Mumbai</p>",
                        "<p>New Delhi<strong id=\"docs-internal-guid-f494dfc8-7fff-c650-d55c-b8d285947cca\"><br></strong></p>",
                        "<p>Chennai</p>",
                        "<p>Bengaluru</p>"
                    ],
                    options_hi: [
                        "<p>मुंबई</p>",
                        "<p>नई दिल्ली</p>",
                        "<p>चेन्नई</p>",
                        "<p>बेंगलुरु</p>"
                    ],
                    solution_en: "<p>30.(c)<strong> Chennai.</strong> It is a collaboration between the Indian Council of Medical Research (ICMR) and the Madras Diabetes Research Foundation (MDRF). The facility aims to collect, process, and store biological samples for research on diabetes causes, variations, and related disorders.</p>",
                    solution_hi: "<p>30.(c)<strong> चेन्नई। </strong>यह भारतीय चिकित्सा अनुसंधान परिषद (ICMR) और मद्रास डायबिटीज रिसर्च फाउंडेशन (MDRF) के बीच एक सहयोग है। यह सुविधा मधुमेह के कारणों, विविधताओं और संबंधित विकारों पर शोध के लिए जैविक नमूनों को एकत्र, प्रसंस्करण और संग्रहीत करने का लक्ष्य रखती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. In which year did Galileo describe the regular, swinging motion of a pendulum by the action of gravity and acquired momentum ?</p>",
                    question_hi: "<p>31. गैलीलियो ने किस वर्ष गुरुत्वाकर्षण और उपार्जित संवेग की क्रिया द्वारा एक पेंडुलम की नियमित,स्विंगिंग गति का वर्णन किया ?</p>",
                    options_en: [
                        "<p>1600</p>",
                        "<p>1605</p>",
                        "<p>1602</p>",
                        "<p>1599</p>"
                    ],
                    options_hi: [
                        "<p>1600</p>",
                        "<p>1605</p>",
                        "<p>1602</p>",
                        "<p>1599</p>"
                    ],
                    solution_en: "<p>31.(c) <strong>1602. </strong>A simple pendulum consists of a small metal ball (called bob) or a mass suspended from a fixed point by a long thread such that the bob is free to swing back and forth under the influence of gravity. The pendulum is kept lift, which is accelerating in a downward direction. It is in a non-inertial frame of reference. So, the pseudo force can be assumed to bring it in an inertial frame of Reference.</p>",
                    solution_hi: "<p>31.(c)<strong> 1602. </strong>एक साधारण पेंडुलम में एक छोटी धातु की गेंद (जिसे बॉब कहा जाता है) या एक द्रव्यमान होता है जो एक लंबे धागे से एक निश्चित बिंदु से लटका होता है ताकि बॉब गुरुत्वाकर्षण के प्रभाव में आगे-पीछे स्विंग करने के लिए स्वतंत्र हो। पेंडुलम को ऊपर उठाया जाता है, जो नीचे की दिशा में त्वरित होता है। यह संदर्भ के एक अजड़त्वीय फ्रेम में है। इसलिए, छद्म बल को संदर्भ के एक जड़त्वीय फ्रेम में लाने के लिए माना जा सकता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Who among the following is a famous veena player ?&nbsp;</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन एक प्रसिद्ध वीणा वादक है ?</p>",
                    options_en: [
                        "<p>Gopal Das</p>",
                        "<p>N. Rajam</p>",
                        "<p>Sultan Khan</p>",
                        "<p>V. Dhannamal</p>"
                    ],
                    options_hi: [
                        "<p>गोपाल दास</p>",
                        "<p>एन. राजम</p>",
                        "<p>सुल्तान खान</p>",
                        "<p>वी. धन्नामल</p>"
                    ],
                    solution_en: "<p>32.(d) <strong>V. Dhannamal.&nbsp;</strong>The veena is a string instrument originating in India. The Veena play an important role in Hindustani classical music and Carnatic classical music. Other famous Veena players: Asad Ali Khan, Jayanthi Kumaresh, Meera Krishnan. N. Rajam (Violinist), Sultan Khan (sarangi).</p>",
                    solution_hi: "<p>32.(d) <strong>वी. धन्नामल I </strong>वीणा एक तार वाला वाद्य यंत्र है जिसकी उत्पत्ति भारत में हुई है। वीणा हिंदुस्तानी शास्त्रीय संगीत और कर्नाटक शास्त्रीय संगीत शैली में एक महत्वपूर्ण भूमिका निभाता है। अन्य प्रसिद्ध वीणा वादक: असद अली खान, जयंती कुमारेश, मीरा कृष्णन। एन राजम (वायलिन वादक), सुल्तान खान (सारंगी)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Ernest Rutherford used about _______thick gold foil for alpha scattering experiments.</p>",
                    question_hi: "<p>33. अर्नेस्ट रदरफोर्ड ने अल्फा प्रकीर्णन प्रयोगों के लिए लगभग ______मोटी सोने की पन्नी का इस्तेमाल किया था।</p>",
                    options_en: [
                        "<p>500 atoms</p>",
                        "<p>1000 atoms</p>",
                        "<p>900 atoms</p>",
                        "<p>700 atoms</p>"
                    ],
                    options_hi: [
                        "<p>500 परमाणु</p>",
                        "<p>1000 परमाणु</p>",
                        "<p>900 परमाणु</p>",
                        "<p>700 परमाणु</p>"
                    ],
                    solution_en: "<p>33.(b) <strong>1000 atoms.</strong> Ernest Rutherford used gold for his scattering experiment because gold is the most malleable metal and he wanted the thinnest layer of metal. He conducted an experiment to study the arrangement of electrons in an atom and designed an experiment for this. In this experiment, fast moving alpha (&alpha;)-particles were made to fall on a thin gold foil. This experiment showed that the atom is mostly empty space with a tiny, dense, positively-charged nucleus.</p>",
                    solution_hi: "<p>33.(b) <strong>1000 परमाणु।</strong> अर्नेस्ट रदरफोर्ड ने अपने प्रकीर्णन प्रयोग के लिए सोने का उपयोग किया क्योंकि सोना सबसे अधिक आघातवर्ध्यनीय धातु है और इससे आसानी से बहुत पतली शीट बनाई जा सकती है। उन्होंने एक परमाणु में इलेक्ट्रॉनों की स्थिति का अध्ययन करने के लिए एक प्रयोग किया और इसके लिए एक प्रयोग तैयार किया। इस प्रयोग में, तेज़ गति से चलने वाले अल्फा (&alpha;)-कणों को एक पतली सोने की पन्नी में प्रक्षेपित किया। इस प्रयोग से पता चला कि परमाणु में अधिकतर खाली स्थान होता है जिसमें एक छोटा, घना, धनात्मक रूप से आवेशित नाभिक होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which term describes the ability of the Constitution to adapt and grow over time to meet the changing needs of society ?</p>",
                    question_hi: "<p>34. कौन सा शब्द समाज की बदलती जरूरतों को पूरा करने के लिए समय के साथ अनुकूलन और विकास करने की संविधान की क्षमता का वर्णन करता है ?</p>",
                    options_en: [
                        "<p>flexible</p>",
                        "<p>rigid</p>",
                        "<p>semi-federal</p>",
                        "<p>unswerving</p>"
                    ],
                    options_hi: [
                        "<p>लचीलापन</p>",
                        "<p>कठोर</p>",
                        "<p>अर्ध-संघीय</p>",
                        "<p>अडिग</p>"
                    ],
                    solution_en: "<p>34.(a) <strong>flexible. </strong>The concept of flexible constitution suggests that the Constitution is not static but evolves and is interpreted in the context of contemporary values and circumstances. Article 368 of the Indian Constitution provides procedure for amendment to adapt the changing needs of Indian society. For example, the 101st Constitutional Amendment Act introduced the Goods and Services Tax (GST), establishing a uniform taxation framework across India. It was the need to create a common national market and ease of doing business to ensure higher economic growth. The Indian Constitution is considered to be both flexible and rigid, or partially flexible and partially rigid.</p>",
                    solution_hi: "<p>34.(a) <strong>लचीलापन। </strong>लचीले संविधान की अवधारणा से ज्ञात होता है कि संविधान स्थिर नहीं है, बल्कि समकालीन मूल्यों एवं परिस्थितियों के संदर्भ में विकसित होता है और उसकी व्याख्या की जाती है। भारतीय संविधान का अनुच्छेद 368 भारतीय समाज की बदलती जरूरतों के अनुकूल संशोधन की प्रक्रिया प्रदान करता है। उदाहरण के लिए, 101वें संविधान संशोधन अधिनियम ने माल और सेवा कर (GST) की शुरुआत की, जिसने पूरे भारत में एक समान कराधान ढांचा स्थापित किया। यह एक सामान्य राष्ट्रीय बाजार बनाने, व्यवसाय करने में आसानी प्रदान करने एवं उच्च आर्थिक विकास सुनिश्चित करने की आवश्यकता थी। भारतीय संविधान को लचीला और कठोर दोनों, या आंशिक रूप से लचीला और आंशिक रूप से कठोर माना जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. What is called the powerhouse of the cell ?</p>",
                    question_hi: "<p>35. किसे कोशिका का पावरहाउस कहा जाता है ?</p>",
                    options_en: [
                        " Cytoplasm ",
                        " Chloroplast ",
                        " Mitochondria ",
                        " Nucleus"
                    ],
                    options_hi: [
                        " कोशिका द्रव्य",
                        " क्लोरोप्लास्ट",
                        " माइटोकॉन्ड्रिया",
                        " केंद्रक"
                    ],
                    solution_en: "<p>35.(c)<strong> Mitochondria</strong> are known as the \"powerhouses\" of the cell because they generate most of the cell\'s energy through cellular respiration. They produce ATP (adenosine triphosphate), which is the primary energy currency of the cell, essential for various cellular functions. Mitochondria convert glucose (and other nutrients) into energy, primarily through aerobic respiration.</p>",
                    solution_hi: "<p>35.(c) <strong>माइटोकॉन्ड्रिया </strong>को कोशिका के \"पावरहाउस\" के रूप में जाना जाता है क्योंकि वे कोशिका की अधिकांश ऊर्जा कोशिकीय श्वसन के माध्यम से उत्पन्न करते हैं। वे ATP (एडेनोसिन ट्राइफॉस्फेट) का उत्पादन करते हैं, जो कोशिका की प्राथमिक ऊर्जा स्रोत है, जो विभिन्न कोशिकीय कार्यों के लिए आवश्यक है। माइटोकॉन्ड्रिया मुख्य रूप से वायुवीय श्वसन के माध्यम से ग्लूकोज (और अन्य पोषक तत्वों) को ऊर्जा में परिवर्तित करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. WACA Cricket Ground is located in_______.</p>",
                    question_hi: "<p>36. WACA क्रिकेट ग्राउंड कहाँ स्थित है ?</p>",
                    options_en: [
                        "<p>Mumbai</p>",
                        "<p>Perth</p>",
                        "<p>London</p>",
                        "<p>Sydney</p>"
                    ],
                    options_hi: [
                        "<p>मुंबई</p>",
                        "<p>पर्थ</p>",
                        "<p>लंडन</p>",
                        "<p>सिडनी</p>"
                    ],
                    solution_en: "<p>36.(b) <strong>Perth.</strong> First International men\'s Test match at the WACA (Western Australian Cricket Association) Stadium held between Australia and England in 1970. Famous cricket grounds around the world : Lord&rsquo;s in London (United Kingdom), Kensington Oval in Bridgetown (Barbados), The Oval in London (United Kingdom), Eden Gardens in Kolkata (India).</p>",
                    solution_hi: "<p>36.(b)<strong> पर्थ।</strong> WACA (वेस्टर्न ऑस्ट्रेलियन क्रिकेट एसोसिएशन) स्टेडियम में प्रथम अंतर्राष्ट्रीय पुरुष टेस्ट मैच 1970 में ऑस्ट्रेलिया और इंग्लैंड के बीच आयोजित किया गया था। विश्व के प्रसिद्ध क्रिकेट मैदान: लॉर्ड्स - लंदन (यूनाइटेड किंगडम) में, केंसिंग्टन ओवल - ब्रिजटाउन (बारबाडोस) में, ओवल - लंदन (यूनाइटेड किंगडम) में, ईडन गार्डन - कोलकाता (भारत) में ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. The average separation between the sun and the earth in terms of light years is 8.311 minutes. Here, minutes indicate:</p>",
                    question_hi: "<p>37. प्रकाश वर्ष के संदर्भ में सूर्य और पृथ्वी के बीच औसत अंतराल (average separation) 8.311 मिनट है। यहाँ, मिनट_______इंगित करते हैं।</p>",
                    options_en: [
                        "<p>speed</p>",
                        "<p>time</p>",
                        "<p>velocity</p>",
                        "<p>distance</p>"
                    ],
                    options_hi: [
                        "<p>चाल</p>",
                        "<p>समय</p>",
                        "<p>वेग</p>",
                        "<p>दूरी</p>"
                    ],
                    solution_en: "<p>37.(d) <strong>distance.</strong> The phrase \'8.311 minutes\' refers to the time it takes for light to travel from the Sun to the Earth. Light travels at a speed of 300,000 km per second, covering the distance in approximately 8 minutes and 20 seconds (or roughly 8.311 minutes). Based on this speed, the distance light travels in one year is called a light year, which equals 9.461 &times; 10<sup>12</sup> km.</p>",
                    solution_hi: "<p>37.(d) <strong>दूरी। </strong>\'8.311 मिनट\' वह समय है जो सूर्य से पृथ्वी तक प्रकाश की यात्रा में लगता है। प्रकाश 300,000 किमी प्रति सेकंड की गति से यात्रा करता है, जो लगभग 8 मिनट और 20 सेकंड (या सामान्यतः 8.311 मिनट) में दूरी तय करता है। इस गति के आधार पर, एक वर्ष में प्रकाश द्वारा तय की गई दूरी को प्रकाश वर्ष कहा जाता है, जो 9.461 &times; 10<sup>12</sup> किमी के बराबर होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. The book &ldquo;The Winner&rsquo;s Mindset&rdquo;, which shares insights into achieving peak performance, is authored by which cricketer ?</p>",
                    question_hi: "<p>38. शीर्ष प्रदर्शन प्राप्त करने के बारे में अंतर्दृष्टि साझा करने वाली पुस्तक \"द विनर्स माइंडसेट\" किस क्रिकेटर द्वारा लिखी गई है ?</p>",
                    options_en: [
                        "<p>Shane Watson</p>",
                        "<p>Ravichandran Ashwin</p>",
                        "<p>Sachin Tendulkar</p>",
                        "<p>M.S. Dhoni</p>"
                    ],
                    options_hi: [
                        "<p>शेन वॉटसन</p>",
                        "<p>रविचंद्रन अश्विन</p>",
                        "<p>सचिन तेंदुलकर</p>",
                        "<p>एम.एस. धोनी</p>"
                    ],
                    solution_en: "<p>38.(a) <strong>Shane Watson.</strong> He was an Australian international cricketer whose career ran from 1992 to 2007. In the book, Watson shares strategies that helped him unlock his potential during critical moments, using a combination of personal experiences, scientific methodology, and a mental skills framework to address stress and anxiety.</p>",
                    solution_hi: "<p>38.(a) <strong>शेन वॉटसन।</strong> वह एक ऑस्ट्रेलियाई अन्तर्राष्ट्रीय क्रिकेटर थे, जिनका करियर 1992 से 2007 तक चला। पुस्तक में, वॉटसन ने उन रणनीतियों को साझा किया है, जिन्होंने उन्हें तनाव और चिंता को दूर करने के लिए व्यक्तिगत अनुभवों, वैज्ञानिक पद्धति और मानसिक कौशल ढांचे के संयोजन का उपयोग करके महत्वपूर्ण क्षणों के दौरान अपनी क्षमता को उजागर करने में मदद की।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. The Gandak river is a tributary of which river ?</p>",
                    question_hi: "<p>39. गंडक नदी किस नदी की सहायक नदी है ?</p>",
                    options_en: [
                        "<p>Yamuna river</p>",
                        "<p>Ganga river</p>",
                        "<p>Kaveri river</p>",
                        "<p>Brahmaputra river</p>"
                    ],
                    options_hi: [
                        "<p>यमुना नदी</p>",
                        "<p>गंगा नदी</p>",
                        "<p>कावेरी नदी</p>",
                        "<p>ब्रह्मपुत्र नदी</p>"
                    ],
                    solution_en: "<p>39.(b) <strong>Ganga river.</strong> The Gandaki River, also known as the Narayani or Gandak, is a significant river in Nepal and a left-bank tributary of the Ganges in India. Major tributaries of the Ganges include the Ghaghara, Kosi, Yamuna, and Son rivers. For the Indus River, the principal tributaries are the Satluj, Beas, Ravi, Chenab, and Jhelum. The Brahmaputra River has several tributaries, including the Lohit, Subansiri, Dhansiri, Tista, and Dibang.</p>",
                    solution_hi: "<p>39.(b) <strong>गंगा नदी। </strong>गंडकी नदी, जिसे नारायणी या गंडक के नाम से भी जाना जाता है यह नेपाल की एक महत्वपूर्ण नदी है और भारत में गंगा की बायीं तटवर्ती सहायक नदी है। गंगा की प्रमुख सहायक नदियों में घाघरा, गंडक, कोसी, यमुना और सोन नदियाँ शामिल हैं। सिंधु नदी के लिए, मुख्य सहायक नदियाँ सतलुज, ब्यास, रावी, चिनाब और झेलम हैं। ब्रह्मपुत्र नदी की कई सहायक नदियाँ हैं, जिनमें लोहित, सुबनसिरी, धनसिरी, तिस्ता और दिबांग शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Atal Bhujal Yojana (Atal Jal) is targeted at sustainable ______ management.</p>",
                    question_hi: "<p>40. अटल भूजल योजना (अटल जल) को स्थायी ______प्रबंधन पर लक्षित किया गया है।</p>",
                    options_en: [
                        "<p>groundwater</p>",
                        "<p>pollution</p>",
                        "<p>green</p>",
                        "<p>waste</p>"
                    ],
                    options_hi: [
                        "<p>भूजल</p>",
                        "<p>प्रदूषण</p>",
                        "<p>हरा</p>",
                        "<p>अपशिष्ट</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>groundwater. </strong>Atal Bhujal Yojana (ABY) &ndash; It was launched by PM Narendra Modi on December 25, 2019, on the 95th birth anniversary of Atal Bihari Vajpayee.</p>",
                    solution_hi: "<p>40.(a) <strong>भूजल ।</strong> अटल भूजल योजना (ABY) - इसे 25 दिसंबर, 2019 को अटल बिहारी वाजपेयी की 95वीं जयंती पर पीएम नरेंद्र मोदी द्वारा लॉन्च किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Harrod Domar model was the main idea behind the _______Five Year Plan.</p>",
                    question_hi: "<p>41. ______पंचवर्षीय योजना के पीछे हैरोड डोमर मॉडल मुख्य विचार था।</p>",
                    options_en: [
                        "<p>Third</p>",
                        "<p>First</p>",
                        "<p>Second</p>",
                        "<p>Fourth</p>"
                    ],
                    options_hi: [
                        "<p>तृतीय</p>",
                        "<p>प्रथम</p>",
                        "<p>द्वितीय</p>",
                        "<p>चतुर्थ</p>"
                    ],
                    solution_en: "<p>41.(b) <strong>First </strong>Five-Year Plan (1951-1956) aimed for a growth rate of 2.1% but achieved 3.6%. It focused on agriculture, investing in dams and irrigation, with significant funds allocated for the Bhakra Nangal Dam. The Second Five-Year Plan (1956-1961) focused on fast industrialization and emphasized the role of the public sector. It was developed under the leadership of P.C. Mahalanobis. Third Five Year Plan (1961-66) aimed to establish India as a self-reliant and self-generating economy.</p>",
                    solution_hi: "<p>41.(b) <strong>प्रथम </strong>पंचवर्षीय योजना (1951-1956) में 2.1% की विकास दर का लक्ष्य रखा गया था, लेकिन यह 3.6% हासिल किया गया। इसमें कृषि पर ध्यान केंद्रित किया गया, बांधों और सिंचाई में निवेश किया गया, जिसमें भाखड़ा नांगल बांध के लिए पर्याप्त धनराशि आवंटित की गई थी। दूसरी पंचवर्षीय योजना (1956-1961) में तेजी से औद्योगिकीकरण पर ध्यान केंद्रित किया गया और सार्वजनिक क्षेत्र की भूमिका पर जोर दिया गया। इस योजना को पी.सी. महालनोबिस के नेतृत्व में विकसित किया गया था। तीसरी पंचवर्षीय योजना (1961-66) का उद्देश्य भारत को एक आत्मनिर्भर और स्व-उत्पादक अर्थव्यवस्था के रूप में स्थापित करना था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. In Harappa, the granary had rows of ______brick platforms for threshing the grains.</p>",
                    question_hi: "<p>42. हड़प्पा के अन्नागार में अनाज की मड़ाई के लिए _______ईंट के चबूतरों की पंक्तियाँ थीं।</p>",
                    options_en: [
                        "<p>diagonal</p>",
                        "<p>spherical</p>",
                        "<p>rectangular</p>",
                        "<p>circular</p>"
                    ],
                    options_hi: [
                        "<p>तिरछे</p>",
                        "<p>गोलाकार</p>",
                        "<p>आयताकार</p>",
                        "<p>वृत्ताकार</p>"
                    ],
                    solution_en: "<p>42.(d) <strong>Circular. </strong>Granaries of the Indus Valley Civilization were discovered in two key regions: Mohenjodaro and Harappa. Mohenjodaro, located south of the Indus River, is a major site where the Great Granary, the largest building, was found. Harappa, where six smaller granaries were uncovered. It is also known as the Indus Valley Civilization due to its early settlements being located in the Indus Valley region.</p>",
                    solution_hi: "<p>42.(d) <strong>वृत्ताकार।</strong> सिंधु घाटी सभ्यता के अन्न भंडार दो प्रमुख क्षेत्रों में खोजे गए थे: मोहनजोदड़ो और हड़प्पा। सिंधु नदी के दक्षिण में स्थित मोहनजोदड़ो एक प्रमुख स्थल है जहाँ सबसे बड़ी इमारत, महान अन्नागार, पाई गई थी। हड़प्पा, जहाँ छह छोटे अन्न भंडार पाए गए थे। इसे सिंधु घाटी सभ्यता के रूप में भी जाना जाता है क्योंकि इसकी शुरुआती बस्तियाँ सिंधु घाटी क्षेत्र में स्थित थीं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Who among the following was the author of Buddhacharita ?</p>",
                    question_hi: "<p>43. निम्नलिखित में से कौन बुद्धचरित के लेखक थे ?</p>",
                    options_en: [
                        "<p>Jinasena</p>",
                        "<p>Shudraka</p>",
                        "<p>Dandin</p>",
                        "<p>Ashvaghosha</p>"
                    ],
                    options_hi: [
                        "<p>जिनसेन</p>",
                        "<p>शूद्रक</p>",
                        "<p>दण्डी</p>",
                        "<p>अश्वघोष</p>"
                    ],
                    solution_en: "<p>43.(d) <strong>Ashvaghosha.</strong> Writers and their books : Shudraka - &ldquo;Mrichchhakatika&rdquo;. Jinasena - &ldquo;Mahapurana&rdquo;. Dandin - &ldquo;Dashakumaracharita&rdquo;. Vishakhadatta - &ldquo;Mudrarakshasa&rdquo;. Kalhana - &ldquo;Rajatarangini&rdquo;. Vatsayana - &ldquo;Kamasutra&rdquo;. Amoghavarsha - &ldquo;Prashnottar Mallika&rdquo;. Bharata Muni - &ldquo;Natyashastra&rdquo;. Vishnu Sharma - &ldquo;Panchatantra&rdquo;.</p>",
                    solution_hi: "<p>43.(d) <strong>अश्वघोष।</strong> लेखक और उनकी पुस्तकें: शूद्रक - \"मृच्छकटिक\"। जिनसेन - \"महापुराण\"। दण्डिन (Dandin) - \"दशकुमारचरित\"। विशाखदत्त - \"मुद्राराक्षस\"। कल्हण - \"राजतरंगिणी\"। वात्स्यायन - \"कामसूत्र\"। अमोघवर्ष - \"प्रश्नोत्तर मल्लिका\"। भरत मुनि - \"नाट्यशास्त्र\"। विष्णु शर्मा - \"पंचतंत्र\"।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Subansiri, Jia Bharali, Dhansiri and Puthimari are the major tributaries of which river ?</p>",
                    question_hi: "<p>44. सुबनसिरी, जिया भराली, धनसीरी और पुठिमारी किस नदी की प्रमुख सहायक नदियाँ हैं ?</p>",
                    options_en: [
                        "<p>Godavari</p>",
                        "<p>Mahanadi</p>",
                        "<p>Brahmaputra</p>",
                        "<p>Bhagirathi</p>"
                    ],
                    options_hi: [
                        "<p>गोदावरी</p>",
                        "<p>महानदी</p>",
                        "<p>ब्रह्मपुत्र</p>",
                        "<p>भागीरथी</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>Brahmaputra. </strong>The principal tributaries of the rivers are as follows: Godavari: Pravara, Purna, Manjra, Penganga, Wardha, Wainganga, Pranhita (the combined flow of Wainganga, Penganga, and Wardha), Indravati, Maner, and Sabri. Mahanadi: Seonath, Hasdeo, Mand, and Ib join Mahanadi from the left, while Ong, Tel, and Jonk join from the right. Bhagirathi: Bhilangna River, Kedar Ganga, Jadh Ganga, etc.</p>",
                    solution_hi: "<p>44.(c) <strong>ब्रह्मपुत्र। </strong>नदियों की प्रमुख सहायक नदियाँ इस प्रकार हैं: गोदावरी: प्रवरा, पूर्णा, मांजरा, पेंगंगा, वर्धा, वैनगंगा, प्राणहिता (वेनगंगा, पेंगंगा और वर्धा का संयुक्त प्रवाह), इंद्रावती, मनेर और साबरी। महानदी: सियोनाथ, हसदेव, मांड और इब बाईं ओर से महानदी में मिलती हैं, जबकि ओंग, तेल और जोंक दाईं ओर से मिलती हैं। भागीरथी: भिलंगना नदी, केदार गंगा, जाध गंगा, आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which Article deals with the election of the President ?</p>",
                    question_hi: "<p>45. कौन-सा अनुच्छेद राष्ट्रपति के निर्वाचन से संबंधित है ?</p>",
                    options_en: [
                        "<p>Article 72</p>",
                        "<p>Article 54</p>",
                        "<p>Article 74</p>",
                        "<p>Article 66</p>"
                    ],
                    options_hi: [
                        "<p>अनुच्छेद 72</p>",
                        "<p>अनुच्छेद 54</p>",
                        "<p>अनुच्छेद 74</p>",
                        "<p>अनुच्छेद 66</p>"
                    ],
                    solution_en: "<p>45.(b) <strong>Article 54 -</strong> The President shall be elected by the members of an electoral college consisting of: (a) the elected members of both Houses of Parliament; and (b) the elected members of the Legislative Assemblies of the States. Article 66 - Election of Vice-President. Article 72 : Power of the President to grant pardons, suspend, remit, or commute sentences in certain cases. Article 74 : Establishes the Council of Ministers to aid and advise the President.</p>",
                    solution_hi: "<p>45.(b) <strong>अनुच्छेद 54 - </strong>राष्ट्रपति का चुनाव एक निर्वाचक मंडल के सदस्यों द्वारा किया जाएगा, जिसमें शामिल सदस्य होंगे : (a) संसद के दोनों सदनों के निर्वाचित सदस्य; और (b) राज्यों की विधानसभाओं के निर्वाचित सदस्य। अनुच्छेद 66 - उपराष्ट्रपति का चुनाव। अनुच्छेद 72 : राष्ट्रपति को कुछ मामलों में क्षमा देने, सजा निलंबित करने, माफ करने या कम करने की शक्ति प्रदान करता है। अनुच्छेद 74 : राष्ट्रपति की सहायता और सलाह के लिए मंत्रिपरिषद की स्थापना करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. What is celebrated on December 25th in India, in honor of Atal Bihari Vajpayee\'s legacy ?</p>",
                    question_hi: "<p>46. अटल बिहारी वाजपेयी की विरासत के सम्मान में भारत में 25 दिसंबर को क्या मनाया जाता है ?</p>",
                    options_en: [
                        "<p>National Consumer Day</p>",
                        "<p>Good Governance Day</p>",
                        "<p>Earth Day</p>",
                        "<p>National Education Day</p>"
                    ],
                    options_hi: [
                        "<p>राष्ट्रीय उपभोक्ता दिवस</p>",
                        "<p>सुशासन दिवस</p>",
                        "<p>पृथ्वी दिवस</p>",
                        "<p>राष्ट्रीय शिक्षा दिवस</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>Good Governance Day</strong> (Sushashan Diwas) was officially established in 2014 to commemorate the birth anniversary of Atal Bihari Vajpayee. The day is dedicated to promoting awareness about the importance of accountability in government.\"</p>",
                    solution_hi: "<p>46.(b) <strong>सुशासन दिवस</strong> (सुशासन दिवस) की आधिकारिक तौर पर स्थापना 2014 में अटल बिहारी वाजपेयी की जयंती के उपलक्ष्य में की गई थी। यह दिन सरकार में जवाबदेही के महत्व के बारे में जागरूकता को बढ़ावा देने के लिए समर्पित है।\"</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following countries was the host of the first Asian games in 1951 ?</p>",
                    question_hi: "<p>47. 1951 में पहले एशियाई खेलों की मेजबानी निम्नलिखित में से किस देश ने की थी ?</p>",
                    options_en: [
                        "<p>China</p>",
                        "<p>Saudi Arabia</p>",
                        "<p>Japan</p>",
                        "<p>India</p>"
                    ],
                    options_hi: [
                        "<p>चीन</p>",
                        "<p>सऊदी अरब</p>",
                        "<p>जापान</p>",
                        "<p>भारत</p>"
                    ],
                    solution_en: "<p>47.(d)<strong> India.</strong> The Major Dhyan Chand National Stadium (New Delhi) was the venue for all the events at the Asian Games 1951. A total of 489 athletes from 11 nations participated. Topped - Japan. They are held every four years. Host countries (Asian Games): 1982 (India), 2010 (China), 2014 (South Korea), 2026 (Japan), 2030 (Qatar), 2034 (Saudi Arabia).</p>",
                    solution_hi: "<p>47.(d) <strong>भारत I</strong> मेजर ध्यानचंद नेशनल स्टेडियम (नई दिल्ली) एशियाई खेलों 1951 के सभी आयोजनों का स्थल था। 11 देशों के कुल 489 एथलीटों ने भाग लिया। सबसे ऊपर - जापान। इन्हें प्रत्येक चार वर्ष में आयोजित किया जाता है। मेजबान देश (एशियाई खेल): 1982 (भारत), 2010 (चीन), 2014 (दक्षिण कोरिया), 2026 (जापान), 2030 (कतर), 2034 (सऊदी अरब)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following animals can change their gender during their lifespan ?</p>",
                    question_hi: "<p>48. निम्नलिखित में से कौन सा जानवर अपने जीवन काल के दौरान अपना लिंग बदल सकता है ?</p>",
                    options_en: [
                        "<p>Humans</p>",
                        "<p>Ascaris</p>",
                        "<p>nereis</p>",
                        "<p>Snail</p>"
                    ],
                    options_hi: [
                        "<p>मनुष्य (Humans)</p>",
                        "<p>एस्केरिस (Ascaris)</p>",
                        "<p>नेरीस (nereis)</p>",
                        "<p>घोंघा (Snail)</p>"
                    ],
                    solution_en: "<p>48.(d) <strong>Snail. </strong>Most land snails are hermaphrodites, meaning they have both male and female reproductive organs. They are able to produce sperm and eggs while mating with a partner. The male reproductive organ of the snail can never fertilize the female genitals of the snail. Animals who can change their sex during their life : Clownfish, Bearded dragons, Banana slugs.</p>",
                    solution_hi: "<p>48.(d) <strong>घोंघा।</strong> अधिकांश स्थल घोंघे उभयलिंगी होते हैं, जिसका अर्थ है कि उनमें नर और मादा दोनों प्रजनन अंग होते हैं। वे साथी के साथ यौन क्रिया करते समय शुक्राणु और अंडे उत्पन्न करने में सक्षम होते हैं। घोंघे का नर प्रजनन अंग कभी भी घोंघे के मादा जननांगों को निषेचित नहीं कर सकता है। वे जानवर जो अपने जीवन के दौरान अपना लिंग परिवर्तित कर सकते हैं: क्लाउनफ़िश, बियर्डेड ड्रैगन (पोगोना विटिसेप्स), बनाना स्लग आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following is a saturated hydrocarbon ?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन-सा एक संतृप्त हाइड्रोकार्बन है ?</p>",
                    options_en: [
                        "<p>C<sub>3</sub>H<sub>8</sub></p>",
                        "<p>C<sub>6</sub>H<sub>6</sub></p>",
                        "<p>C<sub>2</sub>H<sub>4</sub></p>",
                        "<p>C<sub>4</sub>H<sub>8</sub></p>"
                    ],
                    options_hi: [
                        "<p>C<sub>3</sub>H<sub>8</sub></p>",
                        "<p>C<sub>6</sub>H<sub>6</sub></p>",
                        "<p>C<sub>2</sub>H<sub>4</sub></p>",
                        "<p>C<sub>4</sub>H<sub>8</sub></p>"
                    ],
                    solution_en: "<p>49.(a) <strong>C<sub>3</sub>H<sub>8</sub>.</strong> Saturated hydrocarbons contain only single covalent bonds while unsaturated hydrocarbons contain at least one or more double or triple carbon-carbon bonds. Hence, unsaturated hydrocarbons are more reactive than saturated hydrocarbons. Alkanes are saturated hydrocarbons while alkenes and alkynes are unsaturated hydrocarbons. The general formula for alkanes, alkenes and alkynes are C<sub>n</sub>H<sub>2n+2</sub>, C<sub>n</sub>H<sub>2n</sub>, and C<sub>n</sub>H<sub>2n-2 </sub>respectively.</p>",
                    solution_hi: "<p>49.(a) <strong>C<sub>3</sub>H<sub>8</sub>.</strong> संतृप्त हाइड्रोकार्बन में केवल एकल सहसंयोजक बंध होता हैं जबकि असंतृप्त हाइड्रोकार्बन में कम से कम एक या अधिक दोहरे या त्रिक कार्बन-कार्बन बंध होते हैं। इसलिए, असंतृप्त हाइड्रोकार्बन संतृप्त हाइड्रोकार्बन की तुलना में अधिक अभिक्रियाशील होते हैं। एल्केन संतृप्त हाइड्रोकार्बन होते हैं जबकि एल्केन और एल्काइन असंतृप्त हाइड्रोकार्बन होते हैं। एल्केन, एल्कीन और एल्काइन का सामान्य सूत्र क्रमशः C<sub>n</sub>H<sub>2n+2</sub>, C<sub>n</sub>H<sub>2n</sub>, और C<sub>n</sub>H<sub>2n-2</sub> है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Olympian boxer Vijender Singh belongs to which of the following states ?</p>",
                    question_hi: "<p>50. ओलंपियन मुक्केबाज विजेंदर सिंह निम्नलिखित में से किस राज्य से संबंध रखते हैं ?</p>",
                    options_en: [
                        "<p>Haryana</p>",
                        "<p>Chandigarh</p>",
                        "<p>Delhi</p>",
                        "<p>Punjab</p>"
                    ],
                    options_hi: [
                        "<p>हरियाणा</p>",
                        "<p>चंडीगढ़</p>",
                        "<p>दिल्ली</p>",
                        "<p>पंजाब</p>"
                    ],
                    solution_en: "<p>50.(a) <strong>Haryana. </strong>In July 2009, Vijender Singh accompanied by Sushil Kumar and boxer Mary Kom were garlanded with the Major Dhyan Chand Khel Ratna award. Indian Boxers and their states : Mary Kom (Manipur), Lovlina Borgohain (Assam), Amit Panghal (Haryana), Laishram Sarita Devi (Manipur), Akhil Kumar (Uttar Pradesh) Shiva Thapa (Assam).</p>",
                    solution_hi: "<p>50.(a) <strong>हरियाणा। </strong>जुलाई 2009 में, विजेंदर सिंह, सुशील कुमार और मुक्केबाज मैरी कॉम को भारत के सर्वोच्च खेल सम्मान - मेजर ध्यानचंद खेल रत्न पुरस्कार से सम्मानित किया गया। भारतीय मुक्केबाज और संबंधित राज्य: मैरी कॉम (मणिपुर), लवलीना बोरगोहेन (असम), अमित पंघाल (हरियाणा), लैशराम सरिता देवी (मणिपुर), अखिल कुमार (उत्तर प्रदेश) शिव थापा (असम)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Given is a circle with centre at C. A, B and D are the points on the circumference. Find &ang;ABC.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265561227.png\" alt=\"rId56\" width=\"210\" height=\"196\"></p>",
                    question_hi: "<p>51. केंद्र C पर एक वृत्त दिया गया है। A, B और D परिधि पर बिंदु हैं। &ang;ABC ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265561227.png\" alt=\"rId56\" width=\"210\" height=\"196\"></p>",
                    options_en: [
                        "<p>37&deg;</p>",
                        "<p>33&deg;</p>",
                        "<p>35&deg;</p>",
                        "<p>30&deg;</p>"
                    ],
                    options_hi: [
                        "<p>37&deg;</p>",
                        "<p>33&deg;</p>",
                        "<p>35&deg;</p>",
                        "<p>30&deg;</p>"
                    ],
                    solution_en: "<p>51.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265561383.png\" alt=\"rId57\" width=\"189\" height=\"175\"><br><strong>Construction :</strong> join A and C<br>(∵ The angle subtended by an arc at the center is twice the angle subtended at the circumference of the circle)<br>So, &ang;BCD = 2&ang;BAD <br>126&deg; = 2&ang;BAD &rArr;&nbsp;&ang;BAD = 63&deg;<br>in <math display=\"inline\"><mi>&#916;</mi></math> ACD <br>&ang;CAD = &ang;CDA = 33&deg; &hellip; {angle opposite to equal side}<br>Now, &ang;BAC = &ang;BAD - &ang;CAD <br>&ang;BAC = 63&deg; - 33&deg; = 30&deg;<br>in <math display=\"inline\"><mi>&#916;</mi></math> ACB<br>&ang;CAB = &ang;ABC = 30&deg; &hellip; {angle opposite to equal side}<br>Hence, &ang;ABC = 30&deg;</p>",
                    solution_hi: "<p>51.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265561383.png\" alt=\"rId57\" width=\"189\" height=\"175\"><br>(∵ एक चाप द्वारा केंद्र पर बनाया गया कोण वृत्त की परिधि पर बनाए गए कोण का दोगुना होता है।)<br>तो, &ang;BCD = 2&ang;BAD <br>126&deg; = 2&ang;BAD &rArr; &ang;BAD = 63&deg;<br><math display=\"inline\"><mi>&#916;</mi></math> ACD में<br>&ang;CAD = &ang;CDA = 33&deg; {सामान भुजाओं के विपरीत कोण}<br>अब , &ang;BAC = &ang;BAD - &ang;CAD <br>&ang;BAC = 63&deg; - 33&deg; = 30&deg;<br><math display=\"inline\"><mi>&#916;</mi></math> ACB में<br>&ang;CAB = &ang;ABC = 30&deg; {सामान भुजाओं के विपरीत कोण}<br>अत: , &ang;ABC = 30&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The speed of a boat in still water is 12 km/h, and the speed of the stream is 3 km/h. A man goes to a place by boat, 45 km and comes back to the starting point. Find the total time taken by him.</p>",
                    question_hi: "<p>52. स्थिर जल में एक नाव की चाल 12 km/h है, और धारा की चाल 3 km/h है। एक आदमी 45 km की दूरी तय करके प्रारंभिक बिंदु पर वापस आता है। उसके द्वारा लिया गया कुल समय ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>6 hours</p>",
                        "<p>7 hours</p>",
                        "<p>8 hours</p>",
                        "<p>9 hours</p>"
                    ],
                    options_hi: [
                        "<p>6 घंटे</p>",
                        "<p>7 घंटे</p>",
                        "<p>8 घंटे</p>",
                        "<p>9 घंटे</p>"
                    ],
                    solution_en: "<p>52.(c)<br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mrow><mn>12</mn><mi>&#160;</mi><mo>+</mo><mn>3</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mrow><mn>12</mn><mo>-</mo><mn>3</mn></mrow></mfrac></math> = time<br>Time = 3 + 5 = 8 hours</p>",
                    solution_hi: "<p>52.(c)<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mrow><mn>12</mn><mi>&#160;</mi><mo>+</mo><mn>3</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mrow><mn>12</mn><mo>-</mo><mn>3</mn></mrow></mfrac></math> = समय<br>समय = 3 + 5 = 8 घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Find the greatest value of &lsquo;k&rsquo; in a 6-digit number 6745k2 such that the number is divisible by 3.</p>",
                    question_hi: "<p>53. 6 अंकों की संख्या 6745k2 में \'k\' का वह अधिकतम मान ज्ञात कीजिए जिससे संख्या 3 से विभाज्य हो जाए।</p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>6</p>",
                        "<p>9</p>",
                        "<p>7</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>6</p>",
                        "<p>9</p>",
                        "<p>7</p>"
                    ],
                    solution_en: "<p>53.(c) Divisibility rule of 3 : Sum of the digit of the given number should be divisible by 3<br>Here, 6 + 7 + 4 + 5 + k + 2 = 24 + k<br>If k = 9, then 24 + 9 = 33 (which is divisible by 3)<br>&there4; Greatest value of k = 9</p>",
                    solution_hi: "<p>53.(c) 3 की विभाज्यता नियम: दी गई संख्या के अंकों का योग 3 से विभाज्य होना चाहिए<br>यहाँ, 6 + 7 + 4 + 5 + k + 2 = 24 + k<br>यदि k = 9 है, तो 24 + 9 = 33 (जो 3 से विभाज्य है)<br>&there4; k का अधिकतम मान = 9</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The value of 1 -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><mrow><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></mrow></mfrac></mrow></mfrac></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>54. 1 -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><mrow><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></mrow></mfrac></mrow></mfrac></mrow></mfrac></math>&nbsp;का मान कितना होगा ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>54.(c) <br>1 -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><mrow><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></mrow></mfrac></mrow></mfrac></mrow></mfrac></math><br>1 -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><mrow><mn>2</mn><mo>-</mo><mfrac><mn>3</mn><mn>7</mn></mfrac></mrow></mfrac></mrow></mfrac></math><br>1 -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>7</mn><mn>11</mn></mfrac></mrow></mfrac></math><br>1 -&nbsp;<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>18</mn></mfrac></math></p>",
                    solution_hi: "<p>54.(c) <br>1 -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><mrow><mn>2</mn><mo>-</mo><mfrac><mn>1</mn><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></mrow></mfrac></mrow></mfrac></mrow></mfrac></math><br>1 -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><mrow><mn>2</mn><mo>-</mo><mfrac><mn>3</mn><mn>7</mn></mfrac></mrow></mfrac></mrow></mfrac></math><br>1 -&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>7</mn><mn>11</mn></mfrac></mrow></mfrac></math><br>1 -&nbsp;<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>18</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The Least Common Multiple of two prime numbers x and y, (x &lt; y) is 141. Then what is the value of (y - 5x) ?</p>",
                    question_hi: "<p>55. दो अभाज्य संख्याओं &times; और y, (x &lt; y) का लघुत्तम समापवर्त्य 141 है। तो (y - 5x) का मान कितना है ?</p>",
                    options_en: [
                        "<p>32</p>",
                        "<p>30</p>",
                        "<p>38</p>",
                        "<p>40</p>"
                    ],
                    options_hi: [
                        "<p>32</p>",
                        "<p>30</p>",
                        "<p>38</p>",
                        "<p>40</p>"
                    ],
                    solution_en: "<p>55.(a)<br>LCM of (x, y) = 47 &times; 3<br>y = 47 and x&nbsp;= 3<br>y - 5x&nbsp;= 47 - 5 &times; 3 = 32</p>",
                    solution_hi: "<p>55.(a)<br>(x, y) का लघुत्तम समापवर्त्य = 47 &times; 3<br>y = 47 और x&nbsp;= 3<br>y -5x = 47 - 5 &times; 3 = 32</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> of the marked price is equal to <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> of the selling price. What is the discount percentage ?</p>",
                    question_hi: "<p>56. अंकित मूल्य का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>, विक्रय मूल्य के <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> के बराबर है। छूट प्रतिशत कितना है ?</p>",
                    options_en: [
                        "<p>65 Percent</p>",
                        "<p>62.5 Percent</p>",
                        "<p>60 Percent</p>",
                        "<p>50 Percent</p>"
                    ],
                    options_hi: [
                        "<p>65 प्रतिशत</p>",
                        "<p>62.5 प्रतिशत</p>",
                        "<p>60 प्रतिशत</p>",
                        "<p>50 प्रतिशत</p>"
                    ],
                    solution_en: "<p>56.(b)<br>According to question,<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; MP = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &times; SP<br><math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>S</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &times; 4 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math><br>Discount% = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>-</mo><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>8</mn></mfrac></math> = 62.5%</p>",
                    solution_hi: "<p>56.(b)<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; अंकित मूल्य =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &times; विक्रय मूल्य<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &times; 4 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math><br>छूट% = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>-</mo><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>500</mn><mn>8</mn></mfrac></math> = 62.5%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If sin&theta; = <math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>25</mn></mrow></mfrac></math>, then cos&theta; and tan&theta; are respectively _______. (where &theta; is in the first quadrant)</p>",
                    question_hi: "<p>57. यदि sin&theta; = <math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>25</mn></mrow></mfrac></math> है, तो cos&theta; और tan&theta; के मान क्रमशः _______हैं। (जहाँ &theta; प्रथम चतुर्थांश में है)</p>",
                    options_en: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>7</mn></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>24</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac></math> and&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>7</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>24</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>24</mn></mfrac></math> and&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>7</mn></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>7</mn></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>24</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac></math> और&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>7</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>24</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>24</mn></mfrac></math> और&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>7</mn></mfrac></math></p>"
                    ],
                    solution_en: "<p>57.(c) <br>sin&theta; = <math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>25</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow><mrow><mi>h</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>e</mi><mi>o</mi><mi>s</mi></mrow></mfrac></math><br>base = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mfenced separators=\"|\"><mn>7</mn></mfenced><mn>2</mn></msup></msqrt></math><br>base = 24<br>cos&theta; = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>tan&theta; = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>57.(c) <br>sin&theta; = <math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>25</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2354;&#2350;&#2381;&#2348;&#2381;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac></math><br>आधार = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>25</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>7</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>आधार = 24<br>cos&theta; = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>tan&theta; = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A grocer professes to sell rice at the cost price, but uses a fake weight of 870 g for 1 kg. Find his profit percentage (correct to two decimal places).</p>",
                    question_hi: "<p>58. एक पंसारी लागत मूल्य पर चावल बेचने का दावा करता है, लेकिन 1 kg के लिए 870 g के नकली वजन का उपयोग करता है। उसका लाभ प्रतिशत (दो दशमलव स्थानों तक सही) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>15.11%</p>",
                        "<p>14.94%</p>",
                        "<p>18.21%</p>",
                        "<p>11.11%</p>"
                    ],
                    options_hi: [
                        "<p>15.11%</p>",
                        "<p>14.94%</p>",
                        "<p>18.21%</p>",
                        "<p>11.11%</p>"
                    ],
                    solution_en: "<p>58.(b) As per question,<br>original weight = 1000 g<br>New weight = 870 g<br>Required Profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1000</mn><mo>-</mo><mn>870</mn></mrow><mn>870</mn></mfrac></math> &times; 100 = 14.94 %</p>",
                    solution_hi: "<p>58.(b) प्रश्न के अनुसार,<br>मूल वजन = 1000 ग्राम <br>नया वजन = 870 ग्राम<br>आवश्यक लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1000</mn><mo>-</mo><mn>870</mn></mrow><mn>870</mn></mfrac></math> &times; 100 = 14.94 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A patient in a hospital is given daily in a cylindrical cup of diameter 7 cm. If the cup is filled with tea to a height of 4 cm, how much tea the hospital has to prepare daily to serve 180 patients? (Use <math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    question_hi: "<p>59. किसी अस्पताल में एक रोगी को प्रतिदिन 7 cm व्यास वाले एक बेलनाकार कप में चाय दी जाती है। यदि कप को चाय से 4 cm की ऊँचाई तक भरा जाता है, तो 180 रोगियों को चाय देने के लिए अस्पताल को प्रतिदिन कितनी चाय तैयार करनी होगी ? (<math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> का प्रयोग करें)</p>",
                    options_en: [
                        "<p>22.27 litres</p>",
                        "<p>27.27 litres</p>",
                        "<p>27.72 litres</p>",
                        "<p>22.77 litres</p>"
                    ],
                    options_hi: [
                        "<p>22.27 litres</p>",
                        "<p>27.27 litres</p>",
                        "<p>27.72 litres</p>",
                        "<p>22.77 litres</p>"
                    ],
                    solution_en: "<p>59.(c)<br>1 litre = 1000 cm<sup>3</sup><br>Volume of cylindrical cup = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> &times; 4 = 154 cm<sup>3</sup><br>Volume of tea to be prepared = 180 &times; 154 cm<sup>3</sup><br>= 27720 cm<sup>3</sup> = 27.72 liter.</p>",
                    solution_hi: "<p>59.(c)<br>1 लीटर = 1000 cm<sup>3</sup><br>बेलनाकार कप का आयतन = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> &times; 4 = 154 cm<sup>3</sup><br>तैयार की जाने वाली चाय का आयतन = 180 &times; 154 cm<sup>3</sup><br>= 27720 cm<sup>3</sup> = 27.72 litres.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A man standing at a point &lsquo;P&rsquo; is watching the top of a tower, marked by the point &lsquo;S&rsquo;. He walks a distance of 40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m towards the foot of the tower to point &lsquo;Q&rsquo;. From &lsquo;Q&rsquo; the angle of elevation of &lsquo;S&rsquo; is 60&deg;. Find the angle of elevation of &lsquo;S&rsquo; from &lsquo;P&rsquo;, if the height of the tower is 60 m.</p>",
                    question_hi: "<p>60. बिंदु \'P\' पर खड़ा एक व्यक्ति बिंदु \'S\' द्वारा चिह्नित एक मीनार के शीर्ष को देख रहा है। वह बिंदु \'Q\' तक टावर के पैर की ओर 40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> मीटर की दूरी तय करता है। \'Q\' से \'S\' का उन्नयन कोण 60&deg; है। यदि मीनार की ऊँचाई 60 मीटर है, तो \'S\' का \'P\' से उन्नयन कोण ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>60&deg;</p>",
                        "<p>30&deg;</p>",
                        "<p>75&deg;</p>",
                        "<p>45&deg;</p>"
                    ],
                    options_hi: [
                        "<p>60&deg;</p>",
                        "<p>30&deg;</p>",
                        "<p>75&deg;</p>",
                        "<p>45&deg;</p>"
                    ],
                    solution_en: "<p>60.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265561593.png\" alt=\"rId58\" width=\"218\" height=\"148\"><br>In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>STQ<br>tan 60&deg; = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mi>T</mi><mi>Q</mi></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mi>T</mi><mi>Q</mi></mrow></mfrac></math><br>&rArr; TQ =<strong id=\"docs-internal-guid-53b064c6-7fff-6941-15ab-2d8b85240928\"> </strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><msqrt><mn>3</mn></msqrt></mfrac></math><br>In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>STP<br>tan&theta; =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mrow><mfrac><mn>60</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>60</mn><mo>+</mo><mn>120</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><msqrt><mn>3</mn></msqrt></mrow><mn>180</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> = tan 30&deg;<br>&rArr; &theta; = 30&deg;</p>",
                    solution_hi: "<p>60.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265561593.png\" alt=\"rId58\" width=\"218\" height=\"148\"><br>In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>STQ<br>tan 60&deg; = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mi>T</mi><mi>Q</mi></mrow></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mi>T</mi><mi>Q</mi></mrow></mfrac></math><br>&rArr; TQ =<strong id=\"docs-internal-guid-53b064c6-7fff-6941-15ab-2d8b85240928\"> </strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><msqrt><mn>3</mn></msqrt></mfrac></math><br>In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>STP<br>tan&theta; =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mrow><mfrac><mn>60</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>60</mn><mo>+</mo><mn>120</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><msqrt><mn>3</mn></msqrt></mrow><mn>180</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> = tan 30&deg;<br>&rArr; &theta; = 30&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. If x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math> , where x &gt; 1, then the value of (x<sup>3 </sup>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math>) is:</p>",
                    question_hi: "<p>61. यदि x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math> है, जहाँ x &gt; 1 है, तो (x<sup>3 </sup>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math>) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>221</p>",
                        "<p>234</p>",
                        "<p>198</p>",
                        "<p>216</p>"
                    ],
                    options_hi: [
                        "<p>221</p>",
                        "<p>234</p>",
                        "<p>198</p>",
                        "<p>216</p>"
                    ],
                    solution_en: "<p>61.(b)<br>x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>&nbsp;= 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math><br>x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>10</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>40</mn><mo>-</mo><mn>4</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>36</mn></msqrt></math> = 6 (as x &gt; 1)<br>So, x<sup>3&nbsp;</sup>- <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math> = (6)<sup>3 </sup>+ 3 &times; 6 = 234</p>",
                    solution_hi: "<p>61.(b) <br>x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>&nbsp;= 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math><br>x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>10</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>40</mn><mo>-</mo><mn>4</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>36</mn></msqrt></math> = 6 (as x &gt; 1)<br>अतः, x<sup>3&nbsp;</sup>- <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math> = (6)<sup>3 </sup>+ 3 &times; 6 = 234</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Two pipes A and B can fill a tank in 48 minutes and 66 minutes, respectively. If both the pipes are opened simultaneously, then after how many minutes should pipe B be closed so that the tank gets filled in 32 minutes ?</p>",
                    question_hi: "<p>62. दो पाइप A और B एक टैंक को क्रमशः 48 मिनट और 66 मिनट में भर सकते हैं। यदि दोनों पाइप एक साथ खोले जाएं, तो कितने मिनट बाद पाइप B को बंद कर देना चाहिए ताकि टैंक 32 मिनट में भर जाए ?</p>",
                    options_en: [
                        "<p>18</p>",
                        "<p>16</p>",
                        "<p>22</p>",
                        "<p>20</p>"
                    ],
                    options_hi: [
                        "<p>18</p>",
                        "<p>16</p>",
                        "<p>22</p>",
                        "<p>20</p>"
                    ],
                    solution_en: "<p>62.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265562000.png\" alt=\"rId59\" width=\"232\" height=\"114\"><br>If A worked for 32 minutes = 32 &times;&nbsp;11 = 352 <br>Remaining work = 528 - 352 = 176<br>Remaining work by B = <math display=\"inline\"><mfrac><mrow><mn>176</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 22 minutes<br>After 22 minutes, pipe B should be closed.</p>",
                    solution_hi: "<p>62.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265562267.png\" alt=\"rId60\" width=\"213\" height=\"121\"><br>A द्वारा 32 मिनट मे किया गया कुल कार्य = 32 &times; 11 = 352 <br>शेष कार्य = 528 - 352 = 176<br>B द्वारा किया गया शेष कार्य = <math display=\"inline\"><mfrac><mrow><mn>176</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 22 मिनट <br>22 मिनट के बाद, पाइप B को बंद कर देना चाहिए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Two triangles ABC and DEF are similar. The smallest side of ABC is equal to 15 units. If the sides of ABC are in the ratio 3 ∶ 4 ∶ 5, and the area of DEF is half of the area of ABC, then what is the largest side of DEF (in units) ?</p>",
                    question_hi: "<p>63. दो त्रिभुज ABC और DEF समरूप हैं। ABC की सबसे छोटी भुजा 15 इकाई के बराबर है। अगर ABC की भुजाएँ 3 ∶ 4 ∶ 5 के अनुपात में हैं, और DEF का क्षेत्रफल ABC के क्षेत्रफल का आधा है, तो DEF की सबसे बड़ी भुजा (इकाई में) क्या है ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>",
                        "<p>25<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p>25<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>",
                        "<p>25<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                        "<p>25<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>63.(b) <br><strong>Concept :-</strong> ratio of the area of similar triangles is equal to the square of corresponding sides.<br>Now, smallest side of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>ABC (3x) = 15 unit<br>x = 5 unit<br>So, other sides of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>ABC <br>4x = 20 unit , 5x = 25 unit<br>As, (15, 20, 25) are the pythagorean triplet&nbsp;<br>So, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>&nbsp;ABC is right angle triangle<br>Area of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>ABC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 15 &times; 20 = 150 square unit<br>According to question,<br>Area of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>DEF = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 150 = 75 square unit<br>So, <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>r</mi><mo>(</mo><mi>&#916;</mi><mi>&#160;</mi><mi>D</mi><mi>E</mi><mi>F</mi><mo>)</mo></mrow><mrow><mi>A</mi><mi>r</mi><mo>(</mo><mi>&#916;</mi><mi>&#160;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>150</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup><mn>2</mn></mfrac></math> &rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><msqrt><mn>2</mn></msqrt></mfrac></math> <br>Hence, largest side of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>DEF (x) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><msqrt><mn>2</mn></msqrt></mfrac></math> unit</p>",
                    solution_hi: "<p>63.(b) <br><strong>संकल्पना:- </strong>समरूप त्रिभुजों के क्षेत्रफल का अनुपात संगत भुजाओं के वर्ग के बराबर होता है।<br>अब, ABC की सबसे छोटी भुजा (3x) = 15 इकाई<br>x = 5 इकाई<br>तो, ABC की अन्य भुजाएं <br>4x = 20 इकाई, 5x = 25 इकाई<br>(15, 20, 25) पायथागॉरियन त्रिक हैं <br>अत: ABC समकोण त्रिभुज है<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>ABC का क्षेत्रफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 15 &times; 20 = 150 वर्ग इकाई<br>प्रश्न के अनुसार,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>DEF का क्षेत्रफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 150 = 75 वर्ग इकाई<br>तो, <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>r</mi><mo>(</mo><mi>&#916;</mi><mi>&#160;</mi><mi>D</mi><mi>E</mi><mi>F</mi><mo>)</mo></mrow><mrow><mi>A</mi><mi>r</mi><mo>(</mo><mi>&#916;</mi><mi>&#160;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>150</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup><mn>2</mn></mfrac></math> &rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><msqrt><mn>2</mn></msqrt></mfrac></math><br>अतः, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>DEF की सबसे बड़ी भुजा (x) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><msqrt><mn>2</mn></msqrt></mfrac></math> इकाई</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. The speeds of three trains are in the ratio 5 : 3 : 7. What is the ratio of the time taken by them to travel the same distance ?</p>",
                    question_hi: "<p>64. तीन ट्रेनों की चाल 5 : 3 : 7 के अनुपात में है। समान दूरी तय करने में उनके द्वारा लिए गए समय का अनुपात क्या होगा?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math></p>",
                        "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math></p>",
                        "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>"
                    ],
                    solution_en: "<p>64.(c)<br>When distance is constant then,Time &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>S</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi></mrow></mfrac></math><br><strong>Ratio -</strong> Speed - 5&nbsp; :&nbsp; 3&nbsp; :&nbsp; 7<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Time - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math></p>",
                    solution_hi: "<p>64.(c)<br>जब दूरी स्थिर हो तो, समय &prop; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>&#2330;&#2366;&#2354;</mi></mfrac></math><br><strong>अनुपात - </strong>गति -&nbsp; 5&nbsp; :&nbsp; 3&nbsp; :&nbsp; 7<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;समय - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>7</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. In alloys A and B, the ratio of lead to tin is 5 : 3 and 3 : 1, respectively. 80 kg of alloy A and 100 kg of alloy B are mixed together to form a new alloy. What is the amount of tin (in kg) in the new alloy ?</p>",
                    question_hi: "<p>65. मिश्र धातु Aऔर B में, लेड और टिन का अनुपात क्रमशः 5:3 और 3:1 है। एक नई मिश्र धातु बनाने के लिए, 80 kg मिश्र धातु A और 100 kg मिश्र धातु B को एक साथ मिलाया जाता है। नई मिश्र धातु में टिन की मात्रा (kg में) कितनी होगी ?</p>",
                    options_en: [
                        "<p>90</p>",
                        "<p>55</p>",
                        "<p>68</p>",
                        "<p>81.3</p>"
                    ],
                    options_hi: [
                        "<p>90</p>",
                        "<p>55</p>",
                        "<p>68</p>",
                        "<p>81.3</p>"
                    ],
                    solution_en: "<p>65.(b)<br>Tin in alloy A = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 80 = 30 kg<br>Tin in alloy B = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 25 kg<br>Total tin in new alloy = 30 + 25 = 55 kg</p>",
                    solution_hi: "<p>65.(b)<br>मिश्रधातु A में टिन = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 80 = 30 kg<br>मिश्र धातु B में टिन = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 25 kg<br>नई मिश्रधातु में कुल टिन = 30 + 25 = 55 kg</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A and B can complete a work in <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days while B and C can complete the same work in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math> days and C and A can complete the same work in&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math> days. If all three of them work together, what fraction of the same work can they complete in 2 days ?</p>",
                    question_hi: "<p>66. A और B एक काम को <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिनों में पूरा कर सकते हैं जबकि B और C उसी काम को <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math> दिनों में पूरा कर सकते हैं तथा C और A उसी काम को <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math> दिनों में पूरा कर सकते हैं। यदि वे तीनों एक साथ मिलकर काम करते हैं, तो वे समान काम का कितना हिस्सा 2 दिनों में पूरा कर सकते हैं?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>225</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>127</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>277</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>225</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>127</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>277</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>66.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265562559.png\" alt=\"rId61\" width=\"292\" height=\"192\"><br>work done by (A + B + C) in one day = <math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 137.5 units<br>According to question,<br>work done by (A + B + C) in two days = 275 units<br>Now, Required fraction of work = <math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>66.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265562741.png\" alt=\"rId62\" width=\"280\" height=\"194\"><br>(A + B + C) द्वारा एक दिन में किया गया कार्य = <math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 137.5 इकाई<br>प्रश्न के अनुसार,<br>(A + B + C) द्वारा दो दिनों में किया गया कार्य = 275 इकाई<br>अतः, कार्य का आवश्यक हिस्सा =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Mithila covers 50 km by bus in 90 minutes. After deboarding the bus, she takes rest for 15 minutes and covers another 30 km by a taxi in 35 minutes. Find the average speed (in km/h) for the whole journey.</p>",
                    question_hi: "<p>67. मिथिला बस से 50 km की दूरी 90 मिनट में तय करती है। बस से उतरने के बाद, वह 15 मिनट आराम करती है और टैक्सी द्वारा 35 मिनट में 30 km की एक और दूरी तय करती है। पूरी यात्रा के लिए औसत चाल (km/h में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>34<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>32<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>31<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>33<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>34<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>32<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>31<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>33<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>67.(a)<br>Average speed = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow></mfrac></math><br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>50</mn><mo>+</mo><mn>30</mn><mo>)</mo><mo>&#215;</mo><mn>60</mn></mrow><mrow><mo>(</mo><mn>90</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>35</mn><mo>)</mo></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>140</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>7</mn></mfrac></math> <br>= 34<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> km/h</p>",
                    solution_hi: "<p>67.(a)<br>औसत चाल = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math><br>औसत चाल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>50</mn><mo>+</mo><mn>30</mn><mo>)</mo><mo>&#215;</mo><mn>60</mn></mrow><mrow><mo>(</mo><mn>90</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>35</mn><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>140</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>7</mn></mfrac></math> <br>= 34<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> km/h</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Bhavani spends 68% of her income. Her income increases by 15% and her expenditure increases by 10%. How much is the increase in the percentage of saving ? (Use decimal up to 2 digits)</p>",
                    question_hi: "<p>68. भवानी अपनी आय का 68% खर्च करती है। उसकी आय में 15% की वृद्धि होती है और उसके व्यय में 10% की वृद्धि होती है। उसकी बचत में कितने प्रतिशत की वृद्धि हुई ? (दो दशमलव अंकों तक ज्ञात कीजिए)</p>",
                    options_en: [
                        "<p>20.57%</p>",
                        "<p>25.62%</p>",
                        "<p>28.42%</p>",
                        "<p>24.80%</p>"
                    ],
                    options_hi: [
                        "<p>20.57%</p>",
                        "<p>25.62%</p>",
                        "<p>28.42%</p>",
                        "<p>24.80%</p>"
                    ],
                    solution_en: "<p>68.(b)<br>Let income of Bhavani = 100<br>Ratio&nbsp; &nbsp; &rarr;&nbsp; income : expenditure : saving<br>Before&nbsp; &rarr;&nbsp; &nbsp; &nbsp;100&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 68&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 32<br>After&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;115&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 74.8&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;40.2<br>Percentage change in saving = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>.</mo><mn>2</mn><mo>-</mo><mn>32</mn></mrow><mn>32</mn></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>2</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = 25.62%</p>",
                    solution_hi: "<p>68.(b)<br>माना कि भवानी की आय = 100<br>अनुपात &rarr;&nbsp; आय&nbsp; &nbsp;:&nbsp; &nbsp;व्यय&nbsp; &nbsp;:&nbsp; बचत<br>पहले&nbsp; &nbsp; &rarr;&nbsp; &nbsp;100&nbsp; &nbsp;:&nbsp; &nbsp;68&nbsp; &nbsp; :&nbsp; &nbsp; 32<br>बाद मे&nbsp; &rarr;&nbsp; &nbsp;115&nbsp; &nbsp;:&nbsp; &nbsp;74.8&nbsp; :&nbsp; 40.2<br>बचत में प्रतिशत परिवर्तन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>.</mo><mn>2</mn><mo>-</mo><mn>32</mn></mrow><mn>32</mn></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>2</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = 25.62%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Evaluate the given expression. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac><mo>+</mo><mfrac><mn>3</mn><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> + 2cos<sup>2</sup>&theta;</p>",
                    question_hi: "<p>69. दिए गए व्यंजक का मान ज्ञात करें। <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac><mo>+</mo><mfrac><mn>3</mn><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> + 2cos<sup>2</sup>&theta;</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p>3</p>",
                        "<p>5</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p>3</p>",
                        "<p>5</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>69.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac><mo>+</mo><mfrac><mn>3</mn><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> + 2cos<sup>2</sup>&theta;<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> + 2cos<sup>2</sup>&theta;<br>5 sin<sup>2</sup>&theta; + 3 cos<sup>2</sup>&theta; + 2cos<sup>2</sup>&theta;<br>5 sin<sup>2</sup>&theta; + 5 cos<sup>2</sup>&theta;<br>5 (sin<sup>2</sup>&theta; + cos<sup>2</sup>&theta;) = 5</p>",
                    solution_hi: "<p>69.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac><mo>+</mo><mfrac><mn>3</mn><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> + 2cos<sup>2</sup>&theta;<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> + 2cos<sup>2</sup>&theta;<br>5 sin<sup>2</sup>&theta; + 3 cos<sup>2</sup>&theta; + 2cos<sup>2</sup>&theta;<br>5 sin<sup>2</sup>&theta; + 5 cos<sup>2</sup>&theta;<br>5 (sin<sup>2</sup>&theta; + cos<sup>2</sup>&theta;) = 5</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A sum of money becomes five times its original value in 15 years when invested at a certain simple interest rate. If the sum was invested twice the time at the same rate of interest, what would be the final amount ?</p>",
                    question_hi: "<p>70. एक निश्चित साधारण ब्याज दर पर निवेश करने पर एक धनराशि 15 वर्षों में अपने मूल मूल्य से पांच गुना हो जाती है। यदि धनराशि को समान ब्याज दर पर दुगने समय के लिए निवेश किया जाए, तो अंतिम धनराशि क्या होगी ?</p>",
                    options_en: [
                        "<p>The money becomes 9 times its original value</p>",
                        "<p>The money becomes 7 times its original value</p>",
                        "<p>The money becomes 6 times its original value</p>",
                        "<p>The money becomes 8 times its original value</p>"
                    ],
                    options_hi: [
                        "<p>धनराशि अपने मूल मूल्य से 9 गुना हो जाएगी</p>",
                        "<p>धनराशि अपने मूल मूल्य से 7 गुना हो जाएगी</p>",
                        "<p>धनराशि अपने मूल मूल्य से 6 गुना हो जाएगी</p>",
                        "<p>धनराशि अपने मूल मूल्य से 8 गुना हो जाएगी</p>"
                    ],
                    solution_en: "<p>70.(a)<br>SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo><mi>t</mi></mrow><mn>100</mn></mfrac></math><br>4 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math><br>r = <math display=\"inline\"><mfrac><mrow><mn>400</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math><br>Now,<br>SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>400</mn><mo>&#215;</mo><mn>30</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>15</mn></mrow></mfrac></math> = 8<br>So the final amount will be 1 + 8 = 9 units<br>That means the amount will be 9 times of its original value.</p>",
                    solution_hi: "<p>70.(a)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;</mi><mi>&#2352;</mi><mo>&#215;</mo><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>4 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mi>&#160;</mi><mo>&#215;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math><br>दर =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>400</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math><br>अब,<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>400</mn><mo>&#215;</mo><mn>30</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>15</mn></mrow></mfrac></math> = 8<br>तो अंतिम राशि 1 + 8 = 9 इकाई होगी<br>यानी रकम अपनी मूल कीमत से 9 गुना होगी.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. In a team of seven members, the average age of six of the members is 42 years while the age of the seventh member of the team is 36 years more than the average age of all the seven members taken together. Find the age (in years) of the seventh member of the team.</p>",
                    question_hi: "<p>71. सात सदस्यों की एक टीम में, छ: सदस्यों की औसत आयु 42 वर्ष है जबकि टीम के सातवें सदस्य की आयु, सभी सात सदस्यों की औसत आयु से 36 वर्ष अधिक है। टीम के सातवें सदस्य की आयु (वर्ष में) ज्ञात करें।</p>",
                    options_en: [
                        "<p>90</p>",
                        "<p>84</p>",
                        "<p>80</p>",
                        "<p>78</p>"
                    ],
                    options_hi: [
                        "<p>90</p>",
                        "<p>84</p>",
                        "<p>80</p>",
                        "<p>78</p>"
                    ],
                    solution_en: "<p>71.(b) Let the average age of seven member be x<br>sum of the age of the six members = 42 &times; 6 = 252<br>According to the question,<br>7<math display=\"inline\"><mi>x</mi></math> = (36 + x) + 252<br>6<math display=\"inline\"><mi>x</mi></math> = 288<br><math display=\"inline\"><mi>x</mi></math> = 48 <br>Age of the seventh member = 48 &times; 7 - 252 = 84 years</p>",
                    solution_hi: "<p>71.(b) माना कि सात सदस्यों की औसत आयु x&nbsp;है <br>छह सदस्यों की आयु का योग = 42 &times; 6 = 252<br>प्रश्न के अनुसार,<br>7<math display=\"inline\"><mi>x</mi></math> = (36 + x) + 252<br>6<math display=\"inline\"><mi>x</mi></math> = 288<br><math display=\"inline\"><mi>x</mi></math> = 48 <br>सातवें सदस्य की आयु = 48 &times; 7 - 252 = 84 वर्ष</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The given pie chart shows the number of students admitted in to different classes of a school in the academic year 2022-23.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265562998.png\" alt=\"rId63\" width=\"279\" height=\"284\"> <br>How many students were more in Class 10 than in Class 12 if 600 students were admitted in to Class 8 ?</p>",
                    question_hi: "<p>72. दिया गया पाई चार्ट शैक्षणिक वर्ष 2022-23 में एक स्कूल की विभिन्न कक्षाओं में प्रवेश पाने वाले विद्यार्थियों की संख्या को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265563228.png\" alt=\"rId64\" width=\"282\" height=\"287\"> <br>यदि कक्षा 8 में 600 विद्यार्थियों को प्रवेश दिया गया तो कक्षा 12 की तुलना में कक्षा 10 में कितने विद्यार्थी अधिक थे ?</p>",
                    options_en: [
                        "<p>280</p>",
                        "<p>298</p>",
                        "<p>240</p>",
                        "<p>295</p>"
                    ],
                    options_hi: [
                        "<p>280</p>",
                        "<p>298</p>",
                        "<p>240</p>",
                        "<p>295</p>"
                    ],
                    solution_en: "<p>72.(a) According to question , <br>&rArr; 15% = 600 <br>&rArr; 1 % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>15</mn></mfrac></math><br>&rArr; (25% - 18%) 7%<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>15</mn></mfrac></math> &times; 7 = 280</p>",
                    solution_hi: "<p>72.(a) प्रश्न के अनुसार, <br>&rArr; 15% = 600 <br>&rArr; 1 % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>15</mn></mfrac></math><br>&rArr; (25% - 18%) 7%<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>15</mn></mfrac></math> &times; 7 = 280</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The perimeter of a rectangular garden is 48 m. If the length is 6 m more than the breadth, the area (in m<sup>2</sup>) of the garden is:</p>",
                    question_hi: "<p>73. एक आयताकार बगीचे का परिमाप 48 m है। यदि लंबाई, चौड़ाई से 6 m अधिक है, तो बगीचे का क्षेत्रफल (m<sup>2</sup> में) क्या है ?</p>",
                    options_en: [
                        "<p>135</p>",
                        "<p>96</p>",
                        "<p>84</p>",
                        "<p>112</p>"
                    ],
                    options_hi: [
                        "<p>135</p>",
                        "<p>96</p>",
                        "<p>84</p>",
                        "<p>112</p>"
                    ],
                    solution_en: "<p>73.(a) Let breadth of the rectangular garden = x m <br>Length of the rectangular garden = (x + 6) m<br>Perimeter of rectangular garden [2 (l + b)] = 48 m&nbsp;<br>&rArr; 2 (x + 6 + x) = 48&nbsp;<br>&rArr; 2x + 6 = 24 <br>&rArr; 2x = 18 &rArr; x = 9m <br>Length = 15 m and breadth = 9 m <br>Area of rectangular garden = l &times; b<br>= 15 &times; 9 = 135 m<sup>2</sup></p>",
                    solution_hi: "<p>73.(a) माना , आयताकार बगीचे की चौड़ाई = x मीटर <br>आयताकार बगीचे की लंबाई = (x + 6) मीटर <br>आयताकार बगीचे का परिमाप [2 (l + b)] = 48 मीटर&nbsp;<br>&rArr; 2 (x + 6 + x) = 48&nbsp;<br>&rArr; 2x + 6 = 24 <br>&rArr; 2x = 18 &rArr; x = 9 मीटर <br>लंबाई = 15 m और चौड़ाई = 9 मीटर <br>आयताकार बगीचे का क्षेत्रफल = l &times; b<br>= 15 &times; 9 = 135 मीटर<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. After the division of a number successively by 2,3 and 5, the remainders are 1, 2 and 3, respectively. What will be the remainder, if 13 divides the same number (if the last quotient is 1) ?</p>",
                    question_hi: "<p>74. किसी संख्या को क्रमशः 2, 3 और 5 से विभाजित करने पर शेषफल क्रमशः 1, 2 और 3 प्राप्त होता हैं। यदि उसी संख्या को 13 से विभाजित किया जाए तो शेषफल क्या होगा (यदि अंतिम भागफल 1 है) ?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>0</p>",
                        "<p>3</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>0</p>",
                        "<p>3</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>74.(d) <br>In this type of questions, we start the calculation from the last and come towards the initial position.<br>The number which when divided by 5 leaves remainder 3 and quotient is 1 <br>= 5 &times; 1 + 3 = 8<br>When a number is divided by 3 , the remainder is 2<br>= 8 &times; 3 + 2 = 26<br>When a number is divided by 2, the remainder is 1 <br>= 26 &times; 2 + 1 = 53<br>So, number is 53<br>Hence, rem. <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1<br><strong>Short trick:-</strong> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265563415.png\" alt=\"rId65\" width=\"365\" height=\"177\"><br>Hence, rem. <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1</p>",
                    solution_hi: "<p>74.(d) <br>इस प्रकार के प्रश्नों में हम गणना आखिरी से शुरू करके प्रारंभिक स्थिति की ओर आते हैं।<br>वह संख्या जिसे 5 से विभाजित करने पर शेषफल 3 तथा भागफल 1 प्राप्त होता है <br>= 5 &times; 1 + 3 = 8<br>जब किसी संख्या को 3 से विभाजित किया जाता है, तो शेषफल 2 होता है<br>= 8&times; 3 + 2 = 26<br>जब किसी संख्या को 2 से विभाजित किया जाता है, तो शेषफल 1 होता है <br>= 26 &times; 2 + 1 = 53<br>तो, संख्या 53 है<br>इसलिए, शेष <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1<br><strong>शॉर्ट ट्रिक:-</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265563571.png\" alt=\"rId66\" width=\"353\" height=\"163\"><br>इसलिए, शेष <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The given table shows the expenditure in (in crores ₹) of three companies A, B and C and the percentage profit of these companies in different years. Given that : Income = Expenditure + Expenditure &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>%</mo></mrow><mn>100</mn></mfrac></math><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265563745.png\" alt=\"rId67\" width=\"474\" height=\"116\"> <br>What is the income (in crore ₹) of Company C in the year 2003 ?</p>",
                    question_hi: "<p>75. दी गई तालिका अलग-अलग वर्षों में तीन कंपनियों A, B और C के व्यय (करोड़ ₹ में) और प्रतिशत लाभ को दर्शाती है। दिया है : आय = व्यय +व्यय &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2366;&#2349;</mi><mo>%</mo></mrow><mn>100</mn></mfrac></math><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739265563949.png\" alt=\"rId68\" width=\"370\" height=\"125\"> <br>वर्ष 2003 में कंपनी C की आय (करोड़ ₹ में) कितनी है ?</p>",
                    options_en: [
                        "<p>75.7</p>",
                        "<p>77.5</p>",
                        "<p>105</p>",
                        "<p>69.8</p>"
                    ],
                    options_hi: [
                        "<p>75.7</p>",
                        "<p>77.5</p>",
                        "<p>105</p>",
                        "<p>69.8</p>"
                    ],
                    solution_en: "<p>75.(b) Income of the company C = 50 + 50 &times; <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>= 50 + 27.5 = ₹ 77.5</p>",
                    solution_hi: "<p>75.(b) कंपनी C की आय = 50 + 50 &times; <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>= 50 + 27.5 = ₹ 77.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Three segments of the following sentence have been underlined and given as options. One of them may contain an error. Select the option that contains the error. If you don\'t find any error, mark \'No error\' as your answer.<br>Since his son has not yet completed the age of eighteen, <span style=\"text-decoration: underline;\">he was reluctant to fulfil his son\'s demand of getting a motorbike.</span></p>",
                    question_hi: "<p>76. Three segments of the following sentence have been underlined and given as options. One of them may contain an error. Select the option that contains the error. If you don\'t find any error, mark \'No error\' as your answer.<br>Since his son has not yet completed the age of eighteen, <span style=\"text-decoration: underline;\">he was reluctant to fulfil his son\'s demand of getting a motorbike.</span></p>",
                    options_en: [
                        "<p>to fulfil his son\'s demand</p>",
                        "<p>No error</p>",
                        "<p>he was reluctant</p>",
                        "<p>of getting a motorbike</p>"
                    ],
                    options_hi: [
                        "<p>to fulfil his son\'s demand</p>",
                        "<p>No error</p>",
                        "<p>he was reluctant</p>",
                        "<p>of getting a motorbike</p>"
                    ],
                    solution_en: "<p>76.(c) he was reluctant<br>The given sentence is in the present tense, therefore the present form of the verb will be used here. &lsquo;Was&rsquo; must be replaced with &lsquo;is&rsquo;. Hence, &lsquo;he is reluctant&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(c) he was reluctant<br>दिया गया sentence present tense में है, इसलिए यहाँ verb की present form का प्रयोग किया जाएगा। &lsquo;Was&rsquo; के स्थान पर &lsquo;is&rsquo; का प्रयोग किया जाना चाहिए। इसलिए, &lsquo;he is reluctant&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>A lot depends on your early <strong><span style=\"text-decoration: underline;\">brought up</span></strong> in the family.</p>",
                    question_hi: "<p>77. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>A lot depends on your early <strong><span style=\"text-decoration: underline;\">brought up</span></strong> in the family.</p>",
                    options_en: [
                        "<p>bringing up</p>",
                        "<p>bringing on.</p>",
                        "<p>upbringing</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>bringing up</p>",
                        "<p>bringing on.</p>",
                        "<p>upbringing</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>77.(c) upbringing <br>Upbringing - the way in which a child is cared for and taught how to behave while it is growing up.</p>",
                    solution_hi: "<p>77.(c) upbringing<br>Upbringing - परवरिश </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. &nbsp;Four alternatives are given for the idiom/phrase underlined in the sentence. Choose the alternative which best expresses the meaning&nbsp; of the idiom/phrase.<br>With the existing management, the future of the company is <strong><span style=\"text-decoration: underline;\">in doldrums.</span></strong></p>",
                    question_hi: "<p>78. &nbsp;Four alternatives are given for the idiom/phrase underlined in the sentence. Choose the alternative which best expresses the meaning&nbsp; of the idiom/phrase.<br>With the existing management, the future of the company is <strong><span style=\"text-decoration: underline;\">in doldrums.</span></strong></p>",
                    options_en: [
                        "<p>dull</p>",
                        "<p>bright</p>",
                        "<p>uncertain</p>",
                        "<p>secure</p>"
                    ],
                    options_hi: [
                        "<p>dull</p>",
                        "<p>bright</p>",
                        "<p>uncertain</p>",
                        "<p>secure</p>"
                    ],
                    solution_en: "<p>78.(c) uncertain. <br><strong>In doldrums -</strong> uncertain</p>",
                    solution_hi: "<p>78.(c) uncertain. <br><strong>In doldrums-</strong> uncertain /अनिश्चित </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>Reeta said to the Headmistress, &ldquo;Madam, I have done my homework.\"</p>",
                    question_hi: "<p>79. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>Reeta said to the Headmistress, &ldquo;Madam, I have done my homework.\"</p>",
                    options_en: [
                        "<p>Reeta said the Headmistress that she has done her homework. .</p>",
                        "<p>Reeta informed the Headmistress that she had done her homework.</p>",
                        "<p>Reeta told Headmistress that she had done her homework.</p>",
                        "<p>Reeta told the Headmistress respectfully that she had done her homework.</p>"
                    ],
                    options_hi: [
                        "<p>Reeta said the Headmistress that she has done her homework. .</p>",
                        "<p>Reeta informed the Headmistress that she had done her homework.</p>",
                        "<p>Reeta told Headmistress that she had done her homework.</p>",
                        "<p>Reeta told the Headmistress respectfully that she had done her homework.</p>"
                    ],
                    solution_en: "<p>79.(d) Reeta told the Headmistress respectfully that she had done her homework.<br>(a) Reeta <strong>said the Headmistress</strong> that she has done her homework. (to is missing)<br>(b) Reeta <strong>informed </strong>the Headmistress that she had done her homework.(Incorrect Reporting Verb)<br>(c) Reeta told Headmistress that she had done her homework. (Respectfully is missing)</p>",
                    solution_hi: "<p>79.(d) Reeta told the Headmistress respectfully that she had done her homework.<br>(a) Reeta <strong>said the Headmistress</strong> that she has done her homework. ( &lsquo;to&rsquo; गायब है)<br>(b) Reeta <strong>informed </strong>the Headmistress that she had done her homework.(Reporting Verb गलत है)<br>(c) Reeta told Headmistress that she had done her homework (Respectfully is missing)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that expresses the given sentence in passive voice. <br>Joseph opened the Bible.</p>",
                    question_hi: "<p>80. Select the option that expresses the given sentence in passive voice. <br>Joseph opened the Bible.</p>",
                    options_en: [
                        "<p>The Bible is being opened by Joseph.</p>",
                        "<p>Joseph is opening the Bible.</p>",
                        "<p>The Bible was opened by Joseph.</p>",
                        "<p>Joseph opens the Bible.</p>"
                    ],
                    options_hi: [
                        "<p>The Bible is being opened by Joseph.</p>",
                        "<p>Joseph is opening the Bible.</p>",
                        "<p>The Bible was opened by Joseph.</p>",
                        "<p>Joseph opens the Bible.</p>"
                    ],
                    solution_en: "<p>80.(c) The Bible was opened by Joseph.(Correct)<br>(a) The Bible <span style=\"text-decoration: underline;\">is being opened</span> by Joseph.(Incorrect Tense)<br>(b) Joseph <span style=\"text-decoration: underline;\">is opening</span> the Bible.(Incorrect Sentence Structure)<br>(d) Joseph <span style=\"text-decoration: underline;\">opens</span> the Bible.(Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>80.(c) The Bible was opened by Joseph. (Correct)<br>(a) The Bible <span style=\"text-decoration: underline;\">is being opened</span> by Joseph. (गलत Tense)<br>(b) Joseph <span style=\"text-decoration: underline;\">is opening</span> the Bible. (गलत Sentence Structure)<br>(d) Joseph <span style=\"text-decoration: underline;\">opens</span> the Bible. (गलत Sentence Structure)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that can be used as a one-word substitute for the given group of words.<br>No longer in existence or use</p>",
                    question_hi: "<p>81. Select the option that can be used as a one-word substitute for the given group of words.<br>No longer in existence or use</p>",
                    options_en: [
                        "<p>Omnipotent</p>",
                        "<p>Outpour</p>",
                        "<p>Obscure</p>",
                        "<p>Obsolete</p>"
                    ],
                    options_hi: [
                        "<p>Omnipotent</p>",
                        "<p>Outpour</p>",
                        "<p>Obscure</p>",
                        "<p>Obsolete</p>"
                    ],
                    solution_en: "<p>81.(d) <strong>Obsolete</strong>- no longer in existence or use.<br><strong>Omnipotent-</strong> one who has unlimited power or authority.<br><strong>Outpour-</strong> an expression of strong feeling that is difficult to control.<br><strong>Obscure-</strong> not clearly expressed or easily understood.</p>",
                    solution_hi: "<p>81.(d) <strong>Obsolete</strong> (अप्रचलित) - no longer in existence or use.<br><strong>Omnipotent</strong> (सर्वशक्तिमान) - one who has unlimited power or authority.<br><strong>Outpour</strong> (उमड़ना) - an expression of strong feeling that is difficult to control.<br><strong>Obscure</strong> (अस्पष्ट) - not clearly expressed or easily understood. </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order. <br>P.&nbsp; It should distribute essential commodities at fair price shops.<br>Q. These greedy traders raise prices to get more profit.<br>R. The Government should help the people.<br>S. The hoarders are chiefly responsible for the rising prices.</p>",
                    question_hi: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order. <br>P.&nbsp; It should distribute essential commodities at fair price shops.<br>Q. These greedy traders raise prices to get more profit.<br>R. The Government should help the people.<br>S. The hoarders are chiefly responsible for the rising prices.</p>",
                    options_en: [
                        "<p>SQRP</p>",
                        "<p>QPRS</p>",
                        "<p>SPQR</p>",
                        "<p>QRPS</p>"
                    ],
                    options_hi: [
                        "<p>SQRP</p>",
                        "<p>QPRS</p>",
                        "<p>SPQR</p>",
                        "<p>QRPS</p>"
                    ],
                    solution_en: "<p>82.(a) SQRP<br>Sentence S will be the starting line as it contains the main idea of the parajumble i.e. hoarders are responsible for the rising prices. However, Sentence Q states that these greedy traders raise prices to get more profit. So, Q will follow S. Further, Sentence R states that the Government should help the people &amp; Sentence P states that it should distribute essential commodities at fair price shops. So, P will follow R. Going through the options, only option a has the correct sequence.</p>",
                    solution_hi: "<p>82.(a) SQRP<br>वाक्य S शुरुआती line होगी क्योंकि इसमें parajumble का मुख्य विचार है अर्थात बढ़ती कीमतों के लिए जमाखोर जिम्मेदार हैं। वाक्य Q बताता है कि ये लालची व्यापारी अधिक लाभ प्राप्त करने के लिए कीमतें बढ़ाते हैं। तो, S के बाद Q आयेगा । आगे, वाक्य R कहता है कि सरकार को लोगों की मदद करनी चाहिए और वाक्य P कहता है कि उचित मूल्य की दुकानों पर आवश्यक वस्तुओं का वितरण करे। अतः R के बाद P आयेगा। विकल्पों के माध्यम से जाने पर, केवल विकल्प a में सही क्रम है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Choose the word opposite in meaning to the given word.<br>PALTRY</p>",
                    question_hi: "<p>83. Choose the word opposite in meaning to the given word.<br>PALTRY</p>",
                    options_en: [
                        "<p>obsolete</p>",
                        "<p>cautious</p>",
                        "<p>random</p>",
                        "<p>plentiful</p>"
                    ],
                    options_hi: [
                        "<p>obsolete</p>",
                        "<p>cautious</p>",
                        "<p>random</p>",
                        "<p>plentiful</p>"
                    ],
                    solution_en: "<p>83.(d) <strong>plentiful</strong><br><strong>Paltry</strong> - (of an amount) very small or meagre.<br><strong>Obsolete</strong> - no longer produced or used; out of date.<br><strong>Cautious</strong> - (of a person) careful to avoid potential problems or dangers.</p>",
                    solution_hi: "<p>83.(d) <strong>Plentiful</strong> - प्रचुर। <br><strong>Paltry</strong> - बहुत छोटा या अल्प।<br><strong>Obsolete</strong> - अब उत्पादित या उपयोग न होने वाला ; अप्रचलित।<br><strong>Cautious</strong> - (किसी व्यक्ति की) संभावित समस्याओं या खतरों से बचने के लिए सावधानी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84.&nbsp;Find the correctly spelt word.</p>",
                    question_hi: "<p>84.&nbsp;Find the correctly spelt word.</p>",
                    options_en: [
                        "<p>spoonsful</p>",
                        "<p>pneumetic</p>",
                        "<p>incense</p>",
                        "<p>marketted</p>"
                    ],
                    options_hi: [
                        "<p>spoonsful</p>",
                        "<p>pneumetic</p>",
                        "<p>incense</p>",
                        "<p>marketted</p>"
                    ],
                    solution_en: "<p>84.(c) &lsquo;Incense&rsquo;.</p>",
                    solution_hi: "<p>84.(c) &lsquo;Incense&rsquo;- अत्&zwj;यधिक क्रोधित करना</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "85. Identify the segment in the sentence which contains grammatical error. Mark ‘no error’ in case the sentence given is correct. <br />Some leader feels that the use of English language will hamper the progress of the nation. ",
                    question_hi: "85. Identify the segment in the sentence which contains grammatical error. Mark ‘no error’ in case the sentence given is correct. <br />Some leader feels that the use of English language will hamper the progress of the nation. ",
                    options_en: [
                        " Some leader feels",
                        " that the use of English language will  hamper",
                        " the progress of the nation.",
                        " No error"
                    ],
                    options_hi: [
                        " Some leader feels",
                        " that the use of English language will  hamper",
                        " the progress of the nation.",
                        " No error"
                    ],
                    solution_en: "85.(a) Some leader feels<br />A Plural noun and plural verb have to be used after ‘some’. Hence, we will replace ‘leader’ with ‘leaders’ and ‘feels’ with ‘feel’ to grammatically correct the given sentence.",
                    solution_hi: "85.(a) Some leader feels<br />\'Some\' के बाद Plural Noun का प्रयोग करना होता है। इसलिए, हम दिए गए वाक्य को grammatically correct करने के लिए \'leader\' को \'leaders\' में और ‘feels’ को ‘feel’  में बदल देंगे।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate meaning of the underlined idiom in the following sentence.<br>Dinisha visits her mother <span style=\"text-decoration: underline;\">once in a blue moon.</span></p>",
                    question_hi: "<p>86. Select the most appropriate meaning of the underlined idiom in the following sentence.<br>Dinisha visits her mother <span style=\"text-decoration: underline;\">once in a blue moon.</span></p>",
                    options_en: [
                        "<p>Very frequently</p>",
                        "<p>Every week</p>",
                        "<p>Very rarely</p>",
                        "<p>Every month</p>"
                    ],
                    options_hi: [
                        "<p>Very frequently</p>",
                        "<p>Every week</p>",
                        "<p>Very rarely</p>",
                        "<p>Every month</p>"
                    ],
                    solution_en: "<p>86.(c) <strong>Once in a blue moon-</strong> very rarely.</p>",
                    solution_hi: "<p>86.(c) <strong>Once in a blue moon-</strong> very rarely./बहुत कम ही।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the word which means the same as the group of words given.<br>The cry of a goat</p>",
                    question_hi: "<p>87. Select the word which means the same as the group of words given.<br>The cry of a goat</p>",
                    options_en: [
                        "<p>Bleat</p>",
                        "<p>Bray</p>",
                        "<p>Croak</p>",
                        "<p>Yelp</p>"
                    ],
                    options_hi: [
                        "<p>Bleat</p>",
                        "<p>Bray</p>",
                        "<p>Croak</p>",
                        "<p>Yelp</p>"
                    ],
                    solution_en: "<p>87.(a) Bleat <br><strong>Bleat </strong>- The cry of a goat <br><strong>Bray</strong> - the loud, harsh cry of a donkey or mule <br><strong>Croak</strong> - to make a harsh low noise like a particular animal (a frog) <br><strong>Yelp</strong> - to give a sudden short cry, especially of pain</p>",
                    solution_hi: "<p>87.(a) Bleat <br><strong>Bleat </strong>(मिमियाना) - The cry of a goat <br><strong>Bray</strong> (गधे की आवाज) - the loud, harsh cry of a donkey or mule <br><strong>Croak</strong> (टरटर करना) - to make a harsh low noise like a particular animal (a frog) <br><strong>Yelp</strong> (चीखना) - to give a sudden short cry, especially of pain</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate idiom that can substitute the underlined words in the given sentence.<br>I have been <span style=\"text-decoration: underline;\">living without a lot of money</span> since I lost my job.</p>",
                    question_hi: "<p>88. Select the most appropriate idiom that can substitute the underlined words in the given sentence.<br>I have been <span style=\"text-decoration: underline;\">living without a lot of money</span> since I lost my job.</p>",
                    options_en: [
                        "<p>living hand to mouth</p>",
                        "<p>receiving a kickback</p>",
                        "<p>keeping my chin up</p>",
                        "<p>as genuine as a three-dollar bill</p>"
                    ],
                    options_hi: [
                        "<p>living hand to mouth</p>",
                        "<p>receiving a kickback</p>",
                        "<p>keeping my chin up</p>",
                        "<p>as genuine as a three-dollar bill</p>"
                    ],
                    solution_en: "<p>88.(a)<strong> living hand to mouth- </strong>living without a lot of money.</p>",
                    solution_hi: "<p>88.(a) <strong>living hand to mouth -</strong> living without a lot of money./बहुत कम पैसे में जीवन यापन करना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Choose the most appropriate synonym for the word given.<br>Refurbished</p>",
                    question_hi: "<p>89. Choose the most appropriate synonym for the word given.<br>Refurbished</p>",
                    options_en: [
                        "<p>white-washed</p>",
                        "<p>painted</p>",
                        "<p>renovated</p>",
                        "<p>repaired</p>"
                    ],
                    options_hi: [
                        "<p>white-washed</p>",
                        "<p>painted</p>",
                        "<p>renovated</p>",
                        "<p>repaired</p>"
                    ],
                    solution_en: "<p>89.(c) renovated<br>Refurbished - renovate and redecorate (something, especially a building).</p>",
                    solution_hi: "<p>89.(c) renovated<br>Refurbished - नवीनीकरण या फिर से सजाना (विशेष रूप से एक इमारत)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate synonym of the underlined word in the following sentence.<br>He behaved <span style=\"text-decoration: underline;\">ruthlessly</span> with his junior, to say the least.</p>",
                    question_hi: "<p>90. Select the most appropriate synonym of the underlined word in the following sentence.<br>He behaved <span style=\"text-decoration: underline;\">ruthlessly</span> with his junior, to say the least.</p>",
                    options_en: [
                        "<p>Weirdly</p>",
                        "<p>Politely</p>",
                        "<p>Inhumanly</p>",
                        "<p>Unexpectedly</p>"
                    ],
                    options_hi: [
                        "<p>Weirdly</p>",
                        "<p>Politely</p>",
                        "<p>Inhumanly</p>",
                        "<p>Unexpectedly</p>"
                    ],
                    solution_en: "<p>90.(c) <strong>Inhumanly</strong>- lacking compassion or showing cruelty.<br><strong>Ruthlessly-</strong> acting without pity or compassion.<br><strong>Weirdly-</strong> behaving strangely or unusually.<br><strong>Politely-</strong> showing good manners or respect.<br><strong>Unexpectedly-</strong> in a way that was not expected or regarded as likely.</p>",
                    solution_hi: "<p>90.(c) <strong>Inhumanly</strong> (अमानवीयतापूर्वक) - lacking compassion or showing cruelty.<br><strong>Ruthlessly</strong> (निर्दयतापूर्वक) - acting without pity or compassion.<br><strong>Weirdly</strong> (अजीब तरह से) - behaving strangely or unusually.<br><strong>Politely</strong> (विनीत भाव से) - showing good manners or respect.<br><strong>Unexpectedly</strong> (अप्रत्याशित रूप से) - in a way that was not expected or regarded as likely.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the option that expresses the given sentence in active voice.<br>Wedding invitations will be sent by them.</p>",
                    question_hi: "<p>91. Select the option that expresses the given sentence in active voice.<br>Wedding invitations will be sent by them.</p>",
                    options_en: [
                        "<p>They will be sent wedding invitations.</p>",
                        "<p>He will sent wedding invitations.</p>",
                        "<p>They will send wedding invitations.</p>",
                        "<p>He will be send wedding invitations.</p>"
                    ],
                    options_hi: [
                        "<p>They will be sent wedding invitations.</p>",
                        "<p>He will sent wedding invitations.</p>",
                        "<p>They will send wedding invitations.</p>",
                        "<p>He will be send wedding invitations.</p>"
                    ],
                    solution_en: "<p>91.(c) They will send wedding invitations. (Correct)<br>(a) They <span style=\"text-decoration: underline;\">will be</span> sent wedding invitations. (Incorrect Helping Verb)<br>(b) <span style=\"text-decoration: underline;\">He</span> will <span style=\"text-decoration: underline;\">sent</span> wedding invitations. (Incorrect Subject and form of verb)<br>(d) <span style=\"text-decoration: underline;\">He</span> <span style=\"text-decoration: underline;\">will be</span> send wedding invitations. (Incorrect Subject and Helping Verb)</p>",
                    solution_hi: "<p>91.(c) They will send wedding invitations. (Correct)<br>(a) They <span style=\"text-decoration: underline;\">will be</span> sent wedding invitations. (गलत Helping Verb)<br>(b) <span style=\"text-decoration: underline;\">He</span> will <span style=\"text-decoration: underline;\">sent</span> wedding invitations. (Subject और form of verb गलत है)<br>(d) <span style=\"text-decoration: underline;\">He</span> <span style=\"text-decoration: underline;\">will be</span> send wedding invitations. (Subject और Helping Verb गलत है)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. In the question four alternatives are given for the underlined word in the sentence. Choose the alternative which best expresses the opposite meaning of the word. <br>We <span style=\"text-decoration: underline;\">embraced</span> each other overjoyed at our success, then I took photograph of Tenzing holding aloft the flags of Great Britain, Nepal, the United Nations and India.</p>",
                    question_hi: "<p>92. In the question four alternatives are given for the underlined word in the sentence. Choose the alternative which best expresses the opposite meaning of the word. <br>We <span style=\"text-decoration: underline;\">embraced</span> each other overjoyed at our success, then I took photograph of Tenzing holding aloft the flags of Great Britain, Nepal, the United Nations and India.</p>",
                    options_en: [
                        "<p>Limited</p>",
                        "<p>Cradled</p>",
                        "<p>Contained</p>",
                        "<p>Junked</p>"
                    ],
                    options_hi: [
                        "<p>Limited</p>",
                        "<p>Cradled</p>",
                        "<p>Contained</p>",
                        "<p>Junked</p>"
                    ],
                    solution_en: "<p>92.(d) <strong>Junked</strong>- got rid of something because it is of no value.<br><strong>Embraced-</strong> accepted or held something enthusiastically.<br><strong>Limited-</strong> restricted in size, amount, or extent.<br><strong>Cradled-</strong> held gently or protectively.<br><strong>Contained-</strong> restrained or kept within limits.</p>",
                    solution_hi: "<p>92.(d) <strong>Junked</strong> (कूड़ा-कर्कट) - got rid of something because it is of no value.<br><strong>Embraced</strong> (आलिंगन किया) - accepted or held something enthusiastically.<br><strong>Limited</strong> (सीमित) - restricted in size, amount, or extent.<br><strong>Cradled</strong> (पालना) - held gently or protectively.<br><strong>Contained</strong> (समाहित) - restrained or kept within limits.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "93. Select the most appropriate option to fill in the blank.<br />It was _______ gave away the prizes.",
                    question_hi: "93. Select the most appropriate option to fill in the blank.<br />It was _______ gave away the prizes.",
                    options_en: [
                        " me who            ",
                        " I who",
                        " myself         ",
                        " mine whom"
                    ],
                    options_hi: [
                        " me who            ",
                        " I who",
                        " myself         ",
                        " mine whom"
                    ],
                    solution_en: "93.(b) I who<br />For sentences beginning with ‘it’ followed by any form of be (is/am/are/was/were etc) we use the nominative pronoun(who). ",
                    solution_hi: "93.(b) I who<br />\'It\' से शुरू होने वाले वाक्य के बाद  be (is/am/are/was/were आदि)   form  का उपयोग होता है।इसके लिए हम<br /> nominative pronoun(who) का उपयोग करते है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate homophones to fill in the blanks. <br>It was against her ______to earn more ______than that decided by the board of directors.</p>",
                    question_hi: "<p>94. Select the most appropriate homophones to fill in the blanks. <br>It was against her ______to earn more ______than that decided by the board of directors.</p>",
                    options_en: [
                        "<p>principle; profit</p>",
                        "<p>principal; prophet</p>",
                        "<p>principal; profit</p>",
                        "<p>principle; prophet</p>"
                    ],
                    options_hi: [
                        "<p>principle; profit</p>",
                        "<p>principal; prophet</p>",
                        "<p>principal; profit</p>",
                        "<p>principle; prophet</p>"
                    ],
                    solution_en: "<p>94.(a) principle; profit <br>&lsquo;Principle&rsquo; means a moral rule of good behaviour. &lsquo;Profit&rsquo; is a financial gain. The given sentence states that it was against her principle to earn more profit than that decided by the board of directors. Hence, &lsquo;principle; profit&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>94.(a) principle; profit <br>&lsquo;Principle&rsquo; का अर्थ है अच्छे व्यवहार का एक नैतिक नियम (moral rule)। &lsquo;Profit&rsquo; एक वित्तीय लाभ (financial gain) है। दिए गए sentence में कहा गया है कि निदेशक मंडल (board of directors) द्वारा तय किए गए लाभ (profit) से अधिक लाभ कमाना उसके सिद्धांत (principle) के विरुद्ध था। इसलिए, &lsquo;principle; profit&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "95. Parts of a sentence are given below in jumbled order. Select the option that arranges the parts in the correct sequence to form a meaningful sentence.<br />(P) kindness and empathy<br />(Q) in a world that can be<br />(O) bring light and positivity<br />(R) harsh and challenging",
                    question_hi: "95. Parts of a sentence are given below in jumbled order. Select the option that arranges the parts in the correct sequence to form a meaningful sentence.<br />(P) kindness and empathy<br />(Q) in a world that can be<br />(O) bring light and positivity<br />(R) harsh and challenging",
                    options_en: [
                        " PORQ",
                        " ROQP",
                        " QRPO",
                        " QROP"
                    ],
                    options_hi: [
                        " PORQ",
                        " ROQP",
                        " QRPO",
                        " QROP"
                    ],
                    solution_en: "95.(c) QRPO<br />The given sentence starts with Part Q as it introduces the main idea of the sentence, i.e. ‘possible nature of the world’. Part Q will be followed by Part R as it states that the world can be harsh and challenging. Further, Part P talks about kindness and empathy and Part O states that kindness and empathy bring light and positivity. So, O will follow P. Going through the options, option ‘c’ has the correct sequence.",
                    solution_hi: "95.(c) QRPO<br />दिया गया sentence, Part Q से प्रारंभ होता है क्योंकि यह sentence के मुख्य विचार, ‘possible nature of the world’ का परिचय देता है। Part Q के बाद Part R आएगा क्योंकि यह बताता है कि दुनिया कठोर और चुनौतीपूर्ण हो सकती है। इसके अलावा, Part P दयालुता (kindness) और सहानुभूति (empathy) के बारे में बात करता है और Part O बताता है कि दयालुता और सहानुभूति, light और positivity लाती है। इसलिए, P के बाद O आएगा। अतः options के माध्यम से जाने पर, option ‘c’ में सही sequence है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96.<strong> Cloze Test:</strong><br>A city is the (96) ______man&rsquo;s battleground where he fights for (97)______. There is a constant competition for jobs, houses and support (98) ______ health and education. This cut-throat competition results in lack of sensitivity (99) _____ fellow-citizens and a feeling of hatred for (100)______ who are more successful.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:</strong><br>A city is the (96) ______man&rsquo;s battleground where he fights for (97)______. There is a constant competition for jobs, houses and support (98) ______ health and education. This cut-throat competition results in lack of sensitivity (99) _____ fellow-citizens and a feeling of hatred for (100)______ who are more successful.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    options_en: [
                        "<p>modern</p>",
                        "<p>historic</p>",
                        "<p>primitive</p>",
                        "<p>early</p>"
                    ],
                    options_hi: [
                        "<p>modern</p>",
                        "<p>historic</p>",
                        "<p>primitive</p>",
                        "<p>early</p>"
                    ],
                    solution_en: "<p>96.(a) <strong>Modern </strong>- relating to the present or recent times as opposed to the remote past.<br><strong>Historic</strong> - famous or important in history, or potentially so.<br><strong>Primitive</strong> - relating to, denoting, or preserving the character of an early stage in the evolutionary or historical development of something.<br><strong>Early</strong> - before the usual or expected time, Option (a) is fit to the context of the sentence.</p>",
                    solution_hi: "<p>96.(a) <strong>Modern</strong> - वर्तमान या हाल के समय से संबंधित।<br><strong>Historic</strong> - इतिहास में प्रसिद्ध या महत्वपूर्ण।<br><strong>Primitive</strong> - विकास के प्रारंभिक चरण में मानव समाज से संबंधित, बिना मशीनों या लेखन प्रणाली के सरल तरीके से रहने वाले लोगों के साथ।<br><strong>Early</strong> - सामान्य या अपेक्षित समय से पहले।<br>विकल्प (a) वाक्य के संदर्भ में उपयुक्त है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong><br>A city is the (96) ______man&rsquo;s battleground where he fights for (97)______. There is a constant competition for jobs, houses and support (98) ______ health and education. This cut-throat competition results in lack of sensitivity (99) _____ fellow-citizens and a feeling of hatred for (100)______ who are more successful.Select the most appropriate option to fill in the blank no 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong><br>A city is the (96) ______man&rsquo;s battleground where he fights for (97)______. There is a constant competition for jobs, houses and support (98) ______ health and education. This cut-throat competition results in lack of sensitivity (99) _____ fellow-citizens and a feeling of hatred for (100)______ who are more successful.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    options_en: [
                        "<p>win</p>",
                        "<p>live</p>",
                        "<p>death</p>",
                        "<p>survival</p>"
                    ],
                    options_hi: [
                        "<p>win</p>",
                        "<p>live</p>",
                        "<p>death</p>",
                        "<p>survival</p>"
                    ],
                    solution_en: "<p>97.(d) survival<br><strong>Survival </strong>- to remain alive<br>Read the passage carefully. It is about the survival of the modern man. So option (d) is the answer.</p>",
                    solution_hi: "<p>97.(d) survival<br><strong>Survival </strong>- to remain alive/ जिंदा रहने के लिए<br>पैसेज को ध्यान से पढ़ें। यह आधुनिक मनुष्य के अस्तित्व के बारे में है। अतः option (d) उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong><br>A city is the (96) ______man&rsquo;s battleground where he fights for (97)______. There is a constant competition for jobs, houses and support (98) ______ health and education. This cut-throat competition results in lack of sensitivity (99) _____ fellow-citizens and a feeling of hatred for (100)______ who are more successful.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong><br>A city is the (96) ______man&rsquo;s battleground where he fights for (97)______. There is a constant competition for jobs, houses and support (98) ______ health and education. This cut-throat competition results in lack of sensitivity (99) _____ fellow-citizens and a feeling of hatred for (100)______ who are more successful.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    options_en: [
                        "<p>like</p>",
                        "<p>dislike</p>",
                        "<p>unlike</p>",
                        "<p>likely</p>"
                    ],
                    options_hi: [
                        "<p>like</p>",
                        "<p>dislike</p>",
                        "<p>unlike</p>",
                        "<p>likely</p>"
                    ],
                    solution_en: "<p>98.(a) like<br>Likely - probable or expected<br>Option (a) is fit to the context of the sentence.</p>",
                    solution_hi: "<p>98.(a) like<br>likely - सम्भावित या प्रत्याशित<br>विकल्प (a) वाक्य के संदर्भ में उपयुक्त है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:</strong><br>A city is the (96) ______man&rsquo;s battleground where he fights for (97)______. There is a constant competition for jobs, houses and support (98) ______ health and education. This cut-throat competition results in lack of sensitivity (99) _____ fellow-citizens and a feeling of hatred for (100)______ who are more successful.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong><br>A city is the (96) ______man&rsquo;s battleground where he fights for (97)______. There is a constant competition for jobs, houses and support (98) ______ health and education. This cut-throat competition results in lack of sensitivity (99) _____ fellow-citizens and a feeling of hatred for (100)______ who are more successful.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    options_en: [
                        "<p>at</p>",
                        "<p>over</p>",
                        "<p>towards</p>",
                        "<p>along</p>"
                    ],
                    options_hi: [
                        "<p>at</p>",
                        "<p>over</p>",
                        "<p>towards</p>",
                        "<p>along</p>"
                    ],
                    solution_en: "<p>99.(c) towards<br>The preposition &ldquo;towards&rdquo; should be used with &ldquo;sensitive&rdquo;</p>",
                    solution_hi: "<p>99.(c) towards<br>\"Sensitive\" के साथ \"towards\" preposition का उपयोग किया जाना चाहिए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:</strong><br>A city is the (96) ______man&rsquo;s battleground where he fights for (97)______. There is a constant competition for jobs, houses and support (98) ______ health and education. This cut-throat competition results in lack of sensitivity (99) _____ fellow-citizens and a feeling of hatred for (100)______ who are more successful.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong><br>A city is the (96) ______man&rsquo;s battleground where he fights for (97)______. There is a constant competition for jobs, houses and support (98) ______ health and education. This cut-throat competition results in lack of sensitivity (99) _____ fellow-citizens and a feeling of hatred for (100)______ who are more successful.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    options_en: [
                        "<p>those</p>",
                        "<p>these</p>",
                        "<p>their</p>",
                        "<p>them</p>"
                    ],
                    options_hi: [
                        "<p>those</p>",
                        "<p>these</p>",
                        "<p>their</p>",
                        "<p>them</p>"
                    ],
                    solution_en: "<p>100.(a) those<br>The demonstrative pronoun &ldquo;those&rdquo; should be used before &ldquo;who are more successful&rdquo;.</p>",
                    solution_hi: "<p>100.(a) those<br>Demonstrative pronoun \"those\" का उपयोग \"who are more successful\" से पहले किया जाना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>