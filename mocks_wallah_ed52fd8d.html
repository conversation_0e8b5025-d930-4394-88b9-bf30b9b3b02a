<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Who among the following became India\'s 76th Chess Grandmaster in the year 2022?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 76</span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2381;&#2352;&#2376;&#2306;&#2337;&#2350;&#2366;&#2360;&#2381;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Viswanathan Anand</p>\n", "<p>Pranav Anand</p>\n", 
                                "<p>Pentala Harikrishna</p>\n", "<p>Rameshbabu Praggnanandhaa</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2381;&#2357;&#2344;&#2366;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2344;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2339;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2344;&#2306;&#2342;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2306;&#2335;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2352;&#2367;&#2325;&#2371;&#2359;&#2381;&#2339;&#2366;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2350;&#2375;&#2358;&#2348;&#2366;&#2348;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2332;&#2381;&#2334;&#2366;&#2344;&#2366;&#2344;&#2306;&#2342;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-weight: 400;\">&nbsp;</span><strong>Pranav Anand. </strong><span style=\"font-weight: 400;\">Grandmaster is a title awarded to chess players by the world chess organization FIDE (International Chess Federation). </span><strong>Vishwanathan Anand:</strong><span style=\"font-weight: 400;\"> He became the first grandmaster from India in 1988. In 2022, he was elected the deputy president of FIDE.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2346;&#2381;&#2352;&#2339;&#2357; &#2310;&#2344;&#2306;&#2342;</strong><span style=\"font-weight: 400;\">&#2404; &#2327;&#2381;&#2352;&#2376;&#2306;&#2337;&#2350;&#2366;&#2360;&#2381;&#2335;&#2352;, &#2357;&#2367;&#2358;&#2381;&#2357; &#2358;&#2340;&#2352;&#2306;&#2332; &#2360;&#2306;&#2327;&#2336;&#2344; FIDE (&#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2358;&#2340;&#2352;&#2306;&#2332; &#2350;&#2361;&#2366;&#2360;&#2306;&#2328;) &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2358;&#2340;&#2352;&#2306;&#2332; &#2325;&#2375; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2342;&#2368; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2313;&#2346;&#2366;&#2343;&#2367; &#2361;&#2376;&#2404; </span><strong>&#2357;&#2367;&#2358;&#2381;&#2357;&#2344;&#2366;&#2341;&#2344; &#2310;&#2344;&#2306;&#2342;</strong><span style=\"font-weight: 400;\">: 1988 &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2346;&#2361;&#2354;&#2375; &#2327;&#2381;&#2352;&#2376;&#2306;&#2337;&#2350;&#2366;&#2360;&#2381;&#2335;&#2352; &#2348;&#2344;&#2375;&#2404;&nbsp; 2022 &#2350;&#2375;&#2306;, &#2311;&#2344;&#2381;&#2361;&#2375;&#2306; FIDE &#2325;&#2366; &#2313;&#2346;&#2366;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359; &#2330;&#2369;&#2344;&#2366; &#2327;&#2351;&#2366;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Who among the following was the first-ever badminton player from I</span><span style=\"font-family: Cambria Math;\">ndia to clinch an Olympic medal?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2323;&#2354;&#2306;&#2346;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2368;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> / </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Srikanth Kidambi</p>\n", "<p>Saina Nehwal</p>\n", 
                                "<p>Chirag Shetty</p>\n", "<p>PV Sindhu</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2358;&#2381;&#2352;&#2368;&#2325;&#2366;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2342;&#2366;&#2306;&#2348;&#2368;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2311;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;&#2361;&#2357;&#2366;&#2354;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2367;&#2352;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2335;&#2381;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2306;&#2343;&#2369;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>Saina Nehwal (2012). </strong><span style=\"font-weight: 400;\">She is the first Indian badminton player to have won an Olympic medal, and the first Indian to have reached the final of the BWF World Championships.&nbsp; &nbsp; </span><strong>Awards:- </strong><span style=\"font-weight: 400;\">Arjuna Award (2009), Padma Shri (2010), Major Dhyan Chand Khel Ratna (2010), Padma Bhushan (2016).</span><strong> PV Sindhu:</strong><span style=\"font-weight: 400;\"> won a silver at Rio 2016 and a bronze at Tokyo 2020.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b) </span><strong>&#2360;&#2366;&#2311;&#2344;&#2366; &#2344;&#2375;&#2361;&#2357;&#2366;&#2354; (2012)</strong><span style=\"font-weight: 400;\">&#2404; &#2351;&#2361; &#2323;&#2354;&#2306;&#2346;&#2367;&#2325; &#2346;&#2342;&#2325; &#2332;&#2368;&#2340;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2346;&#2361;&#2354;&#2368; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368; &#2361;&#2376;&#2306; &#2324;&#2352; BWF &#2357;&#2367;&#2358;&#2381;&#2357; &#2330;&#2376;&#2306;&#2346;&#2367;&#2351;&#2344;&#2358;&#2367;&#2346; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2346;&#2361;&#2369;&#2306;&#2330;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2346;&#2361;&#2354;&#2368; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2361;&#2376;&#2306;&#2404;&nbsp; </span><strong>&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; : -</strong><span style=\"font-weight: 400;\"> &#2309;&#2352;&#2381;&#2332;&#2369;&#2344; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; (2009), &#2346;&#2342;&#2381;&#2350; &#2358;&#2381;&#2352;&#2368; (2010), &#2350;&#2375;&#2332;&#2352; &#2343;&#2381;&#2351;&#2366;&#2344;&#2330;&#2306;&#2342; &#2326;&#2375;&#2354; &#2352;&#2340;&#2381;&#2344; (2010), &#2346;&#2342;&#2381;&#2350; &#2349;&#2370;&#2359;&#2339; (2016)&#2404;</span><strong> &#2346;&#2368;. &#2357;&#2368;. &#2360;&#2367;&#2306;&#2343;&#2369;:</strong><span style=\"font-weight: 400;\"> &#2352;&#2367;&#2351;&#2379; &#2323;&#2354;&#2306;&#2346;&#2367;&#2325; 2016 &#2350;&#2375;&#2306; &#2352;&#2332;&#2340; &#2324;&#2352; &#2335;&#2379;&#2325;&#2381;&#2351;&#2379; &#2323;&#2354;&#2306;&#2346;&#2367;&#2325;&nbsp; 2020 &#2350;&#2375;&#2306; &#2325;&#2366;&#2306;&#2360;&#2381;&#2351; &#2346;&#2342;&#2325; &#2332;&#2368;&#2340;&#2366;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Which of</span><span style=\"font-family: Cambria Math;\"> the following disease is caused by fungi?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2357;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Covid-19</p>\n", "<p>Ring Worm</p>\n", 
                                "<p>Tuberculosis</p>\n", "<p>Cholera</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2357;&#2367;&#2337;</span><span style=\"font-family: Cambria Math;\">- 19(<span style=\"font-weight: 400;\">Covid-19)</span>&nbsp;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2342;&#2366;&#2342; (<span style=\"font-weight: 400;\">Ring Worm)</span></span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2340;&#2346;&#2375;&#2342;&#2367;&#2325;(<span style=\"font-weight: 400;\">Tuberculosis)</span></span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2332;&#2366;(<span style=\"font-weight: 400;\">Cholera)</span></span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>Ring Worm. Fungi </strong><span style=\"font-weight: 400;\">are responsible for various diseases in human beings. Aspergillus infection, Athlete\'s foot, Jock itch, Ringworm, Coccidioidomycosis, Sporotrichosis, valley fever, histoplasmosis are a few of the many deadly diseases caused by fungi. Tuberculosis, Anthrax, Tetanus, Leptospirosis, Pneumonia, Cholera, Botulism&nbsp; are the diseases caused by </span><strong>bacteria</strong><span style=\"font-weight: 400;\">.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>&#2342;&#2366;&#2342; &#2404; &#2325;&#2357;&#2325; ,</strong><span style=\"font-weight: 400;\">&#2350;&#2366;&#2344;&#2357; &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2352;&#2379;&#2327;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2340;&#2381;&#2340;&#2352;&#2342;&#2366;&#2351;&#2368; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2319;&#2360;&#2381;&#2346;&#2352;&#2327;&#2367;&#2354;&#2360; &#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2339;, &#2319;&#2341;&#2354;&#2368;&#2335; &#2347;&#2369;&#2335;, &#2332;&#2377;&#2325; &#2311;&#2330; , &#2342;&#2366;&#2342;, &#2325;&#2379;&#2325;&#2381;&#2360;&#2368;&#2337;&#2337;&#2367;&#2323;&#2311;&#2337;&#2368;&#2360; (coccidioidomycosis) , &#2360;&#2381;&#2346;&#2379;&#2352;&#2379;&#2335;&#2381;&#2352;&#2367;&#2325;&#2379;&#2360;&#2367;&#2360; (Sporotrichosis), &#2357;&#2376;&#2354;&#2368; &#2347;&#2368;&#2357;&#2352;, &#2361;&#2367;&#2360;&#2381;&#2335;&#2379;&#2346;&#2381;&#2354;&#2366;&#2360;&#2381;&#2350;&#2379;&#2360;&#2367;&#2360;&nbsp; &#2310;&#2342;&#2367; &#2325;&#2357;&#2325; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2328;&#2366;&#2340;&#2325; &#2352;&#2379;&#2327; &#2361;&#2376;&#2306;&#2404; &#2340;&#2346;&#2375;&#2342;&#2367;&#2325;, &#2319;&#2306;&#2341;&#2381;&#2352;&#2375;&#2325;&#2381;&#2360;, &#2335;&#2375;&#2335;&#2344;&#2360;, &#2354;&#2375;&#2346;&#2381;&#2335;&#2379;&#2360;&#2381;&#2346;&#2366;&#2351;&#2352;&#2379;&#2360;&#2367;&#2360;, &#2344;&#2367;&#2350;&#2379;&#2344;&#2367;&#2351;&#2366;, &#2361;&#2376;&#2332;&#2366;, &#2348;&#2379;&#2335;&#2369;&#2354;&#2367;&#2332;&#2364;&#2381;&#2350; </span><strong>&#2348;&#2376;&#2325;&#2381;&#2335;&#2368;&#2352;&#2367;&#2351;&#2366;</strong><span style=\"font-weight: 400;\"> &#2360;&#2375; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2352;&#2379;&#2327; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">The city of Fatehpur Sikri was built by ______</span><span style=\"font-family: Cambria Math;\">_ .</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">&#2347;&#2340;&#2375;&#2361;&#2346;&#2369;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2368;&#2325;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2361;&#2352;</span><span style=\"font-family: Cambria Math;\"> __________ </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;&#2404;</span></p>\n",
                    options_en: ["<p>Akbar</p>\n", "<p>Aurangzeb</p>\n", 
                                "<p>Shah Jahan</p>\n", "<p>Jahangir</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2309;&#2325;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2324;&#2352;&#2306;&#2327;&#2332;&#2375;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2361;&#2332;&#2361;&#2366;&#2305;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2361;&#2366;&#2306;&#2327;&#2368;&#2352;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>Akbar. </strong><span style=\"font-weight: 400;\">He </span><span style=\"font-weight: 400;\">founded the city of </span><strong>Fatehpur Sikri </strong><span style=\"font-weight: 400;\">(city of victory) in the honour of a great sufi saint, Shaikh Salim Chisti. </span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">It was constructed using red sandstone. Akbar built several </span><strong>forts and monuments </strong><span style=\"font-weight: 400;\">like Fort of Agra, Jama Masjid, Jodhabai Mahal, Panch Mahal, Diwan-e-aam, Diwan-e-khas, Jyotish Mahal, the tomb of Sheikh Salim Chisti, and Buland Darwaza.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>&#2309;&#2325;&#2348;&#2352;&#2404; </strong><span style=\"font-weight: 400;\">&#2311;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2319;&#2325; &#2350;&#2361;&#2366;&#2344; &#2360;&#2370;&#2347;&#2368; &#2360;&#2306;&#2340;, &#2358;&#2375;&#2326; &#2360;&#2354;&#2368;&#2350; &#2330;&#2367;&#2358;&#2381;&#2340;&#2368; &#2325;&#2375; &#2360;&#2350;&#2381;&#2350;&#2366;&#2344; &#2350;&#2375;&#2306;</span><strong> &#2347;&#2340;&#2375;&#2361;&#2346;&#2369;&#2352; &#2360;&#2368;&#2325;&#2352;&#2368; </strong><span style=\"font-weight: 400;\">(&#2360;&#2367;&#2335;&#2368; &#2321;&#2347;&#2364; &#2357;&#2367;&#2325;&#2381;&#2335;&#2381;&#2352;&#2368;) &#2358;&#2361;&#2352; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2325;&#2368;&#2404; &#2311;&#2360;&#2325;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2354;&#2366;&#2354; &#2348;&#2354;&#2369;&#2310; &#2346;&#2340;&#2381;&#2341;&#2352; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2325;&#2375; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2309;&#2325;&#2348;&#2352; &#2344;&#2375; &#2310;&#2327;&#2352;&#2366; &#2325;&#2366; &#2325;&#2367;&#2354;&#2366;, &#2332;&#2366;&#2350;&#2366; &#2350;&#2360;&#2381;&#2332;&#2367;&#2342;, &#2332;&#2379;&#2343;&#2366;&#2348;&#2366;&#2312; &#2350;&#2361;&#2354;, &#2346;&#2306;&#2330; &#2350;&#2361;&#2354;, &#2342;&#2368;&#2357;&#2366;&#2344;-&#2319;-&#2310;&#2350;, &#2342;&#2368;&#2357;&#2366;&#2344;-&#2319;-&#2326;&#2366;&#2360;, &#2332;&#2381;&#2351;&#2379;&#2340;&#2367;&#2359; &#2350;&#2361;&#2354;, &#2358;&#2375;&#2326; &#2360;&#2354;&#2368;&#2350; &#2330;&#2367;&#2358;&#2381;&#2340;&#2368; &#2325;&#2366; &#2350;&#2325;&#2348;&#2352;&#2366; &#2324;&#2352; &#2348;&#2369;&#2354;&#2306;&#2342; &#2342;&#2352;&#2357;&#2366;&#2332;&#2366; &#2332;&#2376;&#2360;&#2375; </span><strong>&#2325;&#2312; &#2325;&#2367;&#2354;&#2375; &#2324;&#2352; &#2360;&#2381;&#2350;&#2366;&#2352;&#2325;</strong><span style=\"font-weight: 400;\"> &#2348;&#2344;&#2357;&#2366;&#2319;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> How many silver medals did Indian athletes win in the 2022 Commonwealth Games?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2350;&#2339;&#2381;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2375;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2325;&#2377;&#2350;&#2344;&#2357;&#2375;&#2354;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2375;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2341;&#2354;&#2368;&#2335;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2332;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2368;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>14</p>\n", "<p>18</p>\n", 
                                "<p>20</p>\n", "<p>16</p>\n"],
                    options_hi: ["<p>14</p>\n", "<p>18</p>\n",
                                "<p>20</p>\n", "<p>16</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\"> </span><span>2022 Commonwealth Games {</span><strong>host</strong><span> (Birmingham, England), </span><strong>motto </strong><span>(Sport is just the beginning),&nbsp; India finished fourth in the medals tally with 61 medals comprising </span><strong>22 gold,</strong> <strong>16 silver </strong><span>and </span><strong>23 bronze</strong><span> in the event}.</span><strong> Commonwealth Games 2026</strong><span> - Victoria, Australia.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp;</span><strong>16 </strong><span style=\"font-weight: 400;\">&#2404; 2022 &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2350;&#2306;&#2337;&#2354; &#2326;&#2375;&#2354; {</span><strong>&#2350;&#2375;&#2332;&#2348;&#2366;&#2344;</strong><span style=\"font-weight: 400;\"> (&#2348;&#2352;&#2381;&#2350;&#2367;&#2306;&#2328;&#2350;, &#2311;&#2306;&#2327;&#2381;&#2354;&#2376;&#2306;&#2337;), </span><strong>&#2310;&#2342;&#2352;&#2381;&#2358; &#2357;&#2366;&#2325;&#2381;&#2351;</strong><span style=\"font-weight: 400;\"> (&#2360;&#2381;&#2346;&#2379;&#2352;&#2381;&#2335; &#2311;&#2332; &#2332;&#2360;&#2381;&#2335; &#2342; &#2348;&#2367;&#2327;&#2367;&#2344;&#2367;&#2306;&#2327;), &#2349;&#2366;&#2352;&#2340; &#2311;&#2360; &#2310;&#2351;&#2379;&#2332;&#2344; &#2350;&#2375;&#2306;</span><strong> 22 &#2360;&#2381;&#2357;&#2352;&#2381;&#2339;, 16 &#2352;&#2332;&#2340;</strong><span style=\"font-weight: 400;\"> &#2324;&#2352; </span><strong>23 &#2325;&#2366;&#2306;&#2360;&#2381;&#2351;</strong><span style=\"font-weight: 400;\"> &#2360;&#2361;&#2367;&#2340; 61 &#2346;&#2342;&#2325;&#2379;&#2306; &#2325;&#2375; &#2360;&#2366;&#2341; &#2346;&#2342;&#2325; &#2340;&#2366;&#2354;&#2367;&#2325;&#2366; &#2350;&#2375;&#2306; </span><strong>&#2330;&#2380;&#2341;&#2375; </strong><span style=\"font-weight: 400;\">&#2360;&#2381;&#2341;&#2366;&#2344; &#2346;&#2352; &#2352;&#2361;&#2366;}&#2404; </span><strong>&#2325;&#2377;&#2350;&#2344;&#2357;&#2375;&#2354;&#2381;&#2341; &#2327;&#2375;&#2350;&#2381;&#2360; 2026</strong><span style=\"font-weight: 400;\"> - &#2357;&#2367;&#2325;&#2381;&#2335;&#2379;&#2352;&#2367;&#2351;&#2366;, &#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2366;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> What is the approximate literacy rate of India as per the 2011 census?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> 2011 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2344;&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2350;&#2366;&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2325;&#2381;&#2359;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;</span><span style=\"font-family: Cambria Math;\">&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p><span style=\"font-weight: 400;\">70.01 percent</span></p>\n", "<p><span style=\"font-weight: 400;\">78.2 percent</span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">74.04 percent</span></p>\n", "<p><span style=\"font-weight: 400;\">83.35 percent</span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;70.01 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n", "<p><span style=\"font-weight: 400;\">78.2 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">74.04 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n", "<p><span style=\"font-weight: 400;\">83.35 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>74.04 percent </strong><span style=\"font-weight: 400;\">(82.14% for males and 65.46% for females)</span><strong>. Literacy rate in India :</strong><span style=\"font-weight: 400;\"> Bihar lowest at 61.8% (Male: 71.20%, Female: 51.50%), Kerala highest at 94% (Male: 96.11%, Female: 92.07%.</span></p>\n",
                    solution_hi: "<p>6.(c)&nbsp;<strong>74.04 &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</strong><span style=\"font-weight: 400;\"> (&#2346;&#2369;&#2352;&#2369;&#2359;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; 82.14 % &#2324;&#2352; &#2350;&#2361;&#2367;&#2354;&#2366;&#2323;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; 65.46 %)&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2360;&#2366;&#2325;&#2381;&#2359;&#2352;&#2340;&#2366; &#2342;&#2352; :</strong><span style=\"font-weight: 400;\"> &#2348;&#2367;&#2361;&#2366;&#2352; &#2350;&#2375;&#2306; &#2360;&#2348;&#2360;&#2375; &#2325;&#2350; 61.8% (&#2346;&#2369;&#2352;&#2369;&#2359; : 71.20%, &#2350;&#2361;&#2367;&#2354;&#2366; : 51.50%), &#2325;&#2375;&#2352;&#2354; &#2350;&#2375;&#2306; &#2360;&#2348;&#2360;&#2375; &#2309;&#2343;&#2367;&#2325; 94% (&#2346;&#2369;&#2352;&#2369;&#2359; : 96.11% , &#2350;&#2361;&#2367;&#2354;&#2366; : 92.07%)&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">At which Indian institution did President of India, Smt Droupadi Murmu, inaugurate \'PARAM KAMRUPA\' Supercomputer facility in October 2022?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">&#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2381;&#2352;&#2368;&#2350;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2352;&#2380;&#2346;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2352;&#2381;&#2350;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> \'</span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2350;&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> (PARAM KAMRUPA ) \' </span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2346;&#2352;&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2357;&#2367;&#2343;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2381;&#2328;&#2366;&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-weight: 400;\">&nbsp;IIT Guwahati</span></p>\n", "<p><span style=\"font-weight: 400;\">University of Mumbai</span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">&nbsp;IIT Kharagpur</span></p>\n", "<p><span style=\"font-weight: 400;\">NIT Warangal</span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2310;&#2312;&#2310;&#2312;&#2335;&#2368; &#2327;&#2369;&#2357;&#2366;&#2361;&#2366;&#2335;&#2368;</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2369;&#2306;&#2348;&#2312; &#2357;&#2367;&#2358;&#2381;&#2357;&#2357;&#2367;&#2342;&#2381;&#2351;&#2366;&#2354;&#2351;</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2310;&#2312;&#2310;&#2312;&#2335;&#2368; &#2326;&#2337;&#2364;&#2327;&#2346;&#2369;&#2352;</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2344;&#2310;&#2312;&#2335;&#2368; &#2357;&#2366;&#2352;&#2306;&#2327;&#2354;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a) </span><strong>IIT Guwahati. </strong><span style=\"font-weight: 400;\">The </span><strong>Param Kamrupa,</strong><span style=\"font-weight: 400;\"> one of its kind supercomputers in the NorthEast region, installed under the National Supercomputing Mission. </span><strong>Param 8000</strong><span style=\"font-weight: 400;\"> is considered as India\'s </span><strong>first supercomputer</strong><span style=\"font-weight: 400;\">.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7</span><span style=\"font-family: Cambria Math;\">.(</span><span style=\"font-family: Cambria Math;\">a) </span><strong>IIT &#2327;&#2369;&#2357;&#2366;&#2361;&#2366;&#2335;&#2368;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2346;&#2352;&#2350; &#2325;&#2366;&#2350;&#2352;&#2370;&#2346;</strong><span style=\"font-weight: 400;\">, &#2346;&#2370;&#2352;&#2381;&#2357;&#2379;&#2340;&#2381;&#2340;&#2352; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2319;&#2325; &#2320;&#2360;&#2366; &#2360;&#2369;&#2346;&#2352;&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2361;&#2376;, &#2332;&#2367;&#2360;&#2375; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2360;&#2369;&#2346;&#2352;&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2367;&#2306;&#2327; &#2350;&#2367;&#2358;&#2344; &#2325;&#2375; &#2340;&#2361;&#2340; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2346;&#2352;&#2350; 8000 </strong><span style=\"font-weight: 400;\">&#2325;&#2379; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; </span><strong>&#2346;&#2361;&#2354;&#2366; &#2360;&#2369;&#2346;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; </strong><span style=\"font-weight: 400;\">&#2350;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> The S-400 Triumf (Russian for Triumph) Missile System is </span><span style=\"font-family: Cambria Math;\">_______</span><span style=\"font-family: Cambria Math;\"> missile </span><span style=\"font-family: Cambria Math;\">defense system</span><span style=\"font-family: Cambria Math;\"> designed in Russia. </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> S-400 Triumf (Triumph </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;&#2366;&#2306;&#2340;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2360;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2376;&#2351;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2312; ___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2360;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>air<span style=\"font-family: Cambria Math;\"> - to - underwater</span></p>\n", "<p>surface<span style=\"font-family: Cambria Math;\"> - to - air</span></p>\n", 
                                "<p>surface - to - surface</p>\n", "<p>air - to - air</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2361;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2340;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2340;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2340;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2361;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>surface - to - air. </strong><span style=\"font-weight: 400;\">This System is an anti-aircraft missile defense system developed by Russia\'s Almaz Central Design Bureau. </span><span style=\"font-weight: 400;\">The Integrated Guided Missile Development Program </span><strong>(IGMDP) </strong><span style=\"font-weight: 400;\">was launched in </span><strong>1983</strong><span style=\"font-weight: 400;\">. This program was launched with an agenda to develop five missile systems in the country (Trishul, Akash, Nag, Prithvi, and Agni).</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>&#2360;&#2340;&#2361; &#2360;&#2375; &#2361;&#2357;&#2366; &#2350;&#2375;&#2306; &#2350;&#2366;&#2352; &#2325;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368;</strong><span style=\"font-weight: 400;\"> &#2404;&nbsp; &#2351;&#2361; &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368; &#2352;&#2370;&#2360; &#2325;&#2375; &#2309;&#2354;&#2381;&#2350;&#2366;&#2332;&#2364; &#2360;&#2375;&#2306;&#2335;&#2381;&#2352;&#2354; &#2337;&#2367;&#2332;&#2364;&#2366;&#2311;&#2344; &#2348;&#2381;&#2351;&#2370;&#2352;&#2379; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2357;&#2367;&#2325;&#2360;&#2367;&#2340; &#2319;&#2325; &#2357;&#2367;&#2350;&#2366;&#2344; &#2349;&#2375;&#2342;&#2368; &#2350;&#2367;&#2360;&#2366;&#2311;&#2354; &#2352;&#2325;&#2381;&#2359;&#2366; &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368; &#2361;&#2376;&#2404; &#2311;&#2306;&#2335;&#2368;&#2327;&#2381;&#2352;&#2375;&#2335;&#2375;&#2337; &#2327;&#2366;&#2311;&#2337;&#2375;&#2337; &#2350;&#2367;&#2360;&#2366;&#2311;&#2354; &#2337;&#2375;&#2357;&#2354;&#2346;&#2350;&#2375;&#2306;&#2335; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; (</span><strong>IGMDP</strong><span style=\"font-weight: 400;\">), </span><strong>1983 </strong><span style=\"font-weight: 400;\">&#2350;&#2375;&#2306; &#2358;&#2369;&#2352;&#2370; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2351;&#2361; &#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2381;&#2352;&#2350; &#2342;&#2375;&#2358; &#2350;&#2375;&#2306; &#2346;&#2366;&#2306;&#2330; &#2350;&#2367;&#2360;&#2366;&#2311;&#2354; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; (&#2340;&#2381;&#2352;&#2367;&#2358;&#2370;&#2354;, &#2310;&#2325;&#2366;&#2358;, &#2344;&#2366;&#2327;, &#2346;&#2371;&#2341;&#2381;&#2357;&#2368; &#2324;&#2352; &#2309;&#2327;&#2381;&#2344;&#2367;) &#2357;&#2367;&#2325;&#2360;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2360;&#2375;&nbsp; &#2358;&#2369;&#2352;&#2370; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">A type of loan that allows borrowers to make low payments in the initial phases but repayment of the balance amount in lump sum at maturity is known as </span><span style=\"font-family: Cambria Math;\">________</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2315;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2343;&#2366;&#2352;&#2325;&#2352;&#2381;&#2340;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2352;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2350;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2346;&#2325;&#2381;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2350;&#2369;&#2358;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> (lump sum) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2344;&#2352;&#2381;&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> ____________ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Balloon Mortgage</p>\n", "<p>English Mortgage</p>\n", 
                                "<p>Income - Sensitive Payment</p>\n", "<p>Payday Loa<span style=\"font-family: Cambria Math;\">ns</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2354;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2377;&#2352;&#2381;&#2335;&#2327;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2327;&#2381;&#2354;&#2367;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2377;&#2352;</span><span style=\"font-family: Cambria Math;\">&#2381;&#2335;&#2327;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2306;&#2360;&#2335;&#2367;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2350;&#2375;&#2306;&#2335;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2337;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2379;&#2344;&#2381;&#2360;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>Balloon mortgage</strong><span style=\"font-weight: 400;\">. </span><strong>Types of mortgage</strong><span style=\"font-weight: 400;\">: Simple Mortgage, Mortgage by Conditional Sale, English Mortgage, Fixed-Rate Mortgage, Usufructuary Mortgage, Anomalous Mortgage, Reverse Mortgage, Equitable Mortgage. The </span><strong>Income-Sensitive Repayment</strong><span style=\"font-weight: 400;\"> Plan is available to low-income borrowers who have Federal Family Education Loan (FFEL) Program loans. A </span><strong>payday loan </strong><span style=\"font-weight: 400;\">is a type of short-term borrowing where a lender will extend high-interest credit based on income.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>&#2348;&#2376;&#2354;&#2370;&#2344; &#2350;&#2377;&#2352;&#2381;&#2335;&#2327;&#2375;&#2332; &#2404; &#2350;&#2377;&#2352;&#2381;&#2335;&#2327;&#2375;&#2332; &#2325;&#2375; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;: </strong><span style=\"font-weight: 400;\">&#2360;&#2352;&#2354; &#2350;&#2377;&#2352;&#2381;&#2335;&#2327;&#2375;&#2332; , &#2360;&#2358;&#2352;&#2381;&#2340; &#2348;&#2367;&#2325;&#2381;&#2352;&#2368; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2350;&#2377;&#2352;&#2381;&#2335;&#2327;&#2375;&#2332; , &#2311;&#2306;&#2327;&#2381;&#2354;&#2367;&#2358; &#2350;&#2377;&#2352;&#2381;&#2335;&#2327;&#2375;&#2332; , &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;-&#2342;&#2352; &#2350;&#2377;&#2352;&#2381;&#2335;&#2327;&#2375;&#2332; , &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2350;&#2377;&#2352;&#2381;&#2335;&#2327;&#2375;&#2332; , &#2357;&#2367;&#2359;&#2350; &#2350;&#2377;&#2352;&#2381;&#2335;&#2327;&#2375;&#2332; , &#2352;&#2367;&#2357;&#2352;&#2381;&#2360; &#2350;&#2377;&#2352;&#2381;&#2335;&#2327;&#2375;&#2332; , &#2360;&#2350;&#2340;&#2369;&#2354;&#2381;&#2351; &#2350;&#2377;&#2352;&#2381;&#2335;&#2327;&#2375;&#2332; &#2404; </span><strong>&#2310;&#2351;-&#2360;&#2306;&#2357;&#2375;&#2342;&#2344;&#2358;&#2368;&#2354; &#2346;&#2369;&#2344;&#2352;&#2381;&#2349;&#2369;&#2327;&#2340;&#2366;&#2344; &#2351;&#2379;&#2332;&#2344;&#2366;</strong><span style=\"font-weight: 400;\"> &#2344;&#2367;&#2350;&#2381;&#2344;-&#2310;&#2351; &#2357;&#2366;&#2354;&#2375; &#2313;&#2343;&#2366;&#2352;&#2325;&#2352;&#2381;&#2340;&#2366;&#2323;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2346;&#2354;&#2348;&#2381;&#2343; &#2361;&#2376; &#2332;&#2367;&#2344;&#2325;&#2375; &#2346;&#2366;&#2360; &#2360;&#2306;&#2328;&#2368;&#2351; &#2346;&#2352;&#2367;&#2357;&#2366;&#2352; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2315;&#2339; (FFEL) &#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2381;&#2352;&#2350; &#2315;&#2339; &#2361;&#2376;&#2306;&#2404;</span><span style=\"font-weight: 400;\"> </span><strong>Payday &#2354;&#2379;&#2344;, </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2309;&#2354;&#2381;&#2346;&#2325;&#2366;&#2354;&#2367;&#2325; &#2313;&#2343;&#2366;&#2352; &#2361;&#2376; &#2332;&#2361;&#2366;&#2306; &#2319;&#2325; &#2315;&#2339;&#2342;&#2366;&#2340;&#2366; &#2313;&#2343;&#2366;&#2352; &#2354;&#2375;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2325;&#2368; &#2310;&#2351; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2346;&#2352; &#2313;&#2330;&#2381;&#2330;-&#2348;&#2381;&#2351;&#2366;&#2332; &#2325;&#2381;&#2352;&#2375;&#2337;&#2367;&#2335; &#2325;&#2366; &#2357;&#2367;&#2360;&#2381;&#2340;&#2366;&#2352; &#2325;&#2352;&#2375;&#2327;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Which among the following is a unit of measurement that describes the rate at which the un</span><span style=\"font-family: Cambria Math;\">iverse is expanding?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2366;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2360;&#2381;&#2340;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2366;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Faraday constant</p>\n", "<p>Planck\'s constant</p>\n", 
                                "<p>Electric constant</p>\n", "<p>Hubble constant</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2347;&#2376;&#2352;&#2366;&#2337;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2340;&#2366;&#2306;&#2325;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2354;&#2366;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2340;&#2366;&#2306;&#2325;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2342;&#2381;&#2351;&#2369;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2340;&#2366;&#2306;&#2325;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2361;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2340;&#2366;&#2306;&#2325;</span></p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(d) </span><strong>Hubble constant.</strong><span style=\"font-weight: 400;\">The Faraday constant represents the amount of electric charge carried by one mole, or Avogadro\'s number, of electrons. 1 Faraday = F = e &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">N</mi><mi mathvariant=\"normal\">A</mi></msub></math></span><span style=\"font-weight: 400;\"> &rArr; 9.64853321 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>10</mn><mn>4</mn></msup></math></span><span style=\"font-weight: 400;\">&nbsp;Coulomb per mole.&nbsp; The </span><strong>Planck constant (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"bold\">ML</mi><mn mathvariant=\"bold\">2</mn></msup><msup><mi mathvariant=\"bold\">T</mi><mrow><mo mathvariant=\"bold\">-</mo><mn mathvariant=\"bold\">1</mn></mrow></msup></math></strong><strong>) </strong><span style=\"font-weight: 400;\">and </span><strong>Angular momentum (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"bold\">ML</mi><mn mathvariant=\"bold\">2</mn></msup><msup><mi mathvariant=\"bold\">T</mi><mrow><mo mathvariant=\"bold\">-</mo><mn mathvariant=\"bold\">1</mn></mrow></msup></math></strong><strong>) </strong><span style=\"font-weight: 400;\">have the same dimension.&nbsp;</span></p>\n",
                    solution_hi: "<p>10.(d)&nbsp;<strong>&#2361;&#2348;&#2354; &#2344;&#2367;&#2351;&#2340;&#2366;&#2306;&#2325;</strong><span style=\"font-weight: 400;\">&#2404;&nbsp; &#2347;&#2376;&#2352;&#2366;&#2337;&#2375; &#2344;&#2367;&#2351;&#2340;&#2366;&#2306;&#2325; &#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2379;&#2306; &#2325;&#2375; &#2319;&#2325; &#2350;&#2379;&#2354;, &#2351;&#2366; &#2309;&#2357;&#2379;&#2327;&#2366;&#2342;&#2381;&#2352;&#2379; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2325;&#2367;&#2319; &#2327;&#2319; &#2357;&#2367;&#2342;&#2381;&#2351;&#2369;&#2340; &#2310;&#2357;&#2375;&#2358; &#2325;&#2368; &#2350;&#2366;&#2340;&#2381;&#2352;&#2366; &#2325;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367;&#2340;&#2381;&#2357; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; 1 &#2347;&#2376;&#2352;&#2366;&#2337;&#2375; = F = e &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">N</mi><mi mathvariant=\"normal\">A</mi></msub></math></span><span style=\"font-weight: 400;\">&rArr; 9.64853321 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mn>10</mn><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></msup></math></span><span style=\"font-weight: 400;\">&#2325;&#2370;&#2354;&#2377;&#2350; &#2346;&#2381;&#2352;&#2340;&#2367; &#2350;&#2379;&#2354;&#2404; </span><strong>&#2346;&#2381;&#2354;&#2366;&#2306;&#2325; &#2344;&#2367;&#2351;&#2340;&#2366;&#2306;&#2325;</strong><span style=\"font-weight: 400;\"> </span><strong>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"bold\">ML</mi><mn mathvariant=\"bold\">2</mn></msup><mi mathvariant=\"bold\">T</mi><mo mathvariant=\"bold\">-</mo><mn mathvariant=\"bold\">1</mn></math></strong><strong>) </strong><span style=\"font-weight: 400;\">&nbsp;&#2324;&#2352; </span><strong>&#2325;&#2379;&#2339;&#2368;&#2351; &#2360;&#2306;&#2357;&#2375;&#2327;</strong><span style=\"font-weight: 400;\"> </span><strong>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"bold\">ML</mi><mn mathvariant=\"bold\">2</mn></msup><msup><mi mathvariant=\"bold\">T</mi><mrow><mo mathvariant=\"bold\">-</mo><mn mathvariant=\"bold\">1</mn></mrow></msup></math></strong><strong>)</strong><span style=\"font-weight: 400;\"> &#2325;&#2368; &#2357;&#2367;&#2350;&#2366; &#2360;&#2350;&#2366;&#2344; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Which political organization was banned in September 2022 by Government of India under the Unlawful Activities (Prevention) Act?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2343;&#2367;&#2357;&#2367;&#2352;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2325;&#2354;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2357;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2361;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2340;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2327;&#2336;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2327;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Khalistan Zindabad Force</p>\n", "<p><span style=\"font-weight: 400;\">Popular Front of India (PFI)</span></p>\n", 
                                "<p>All Tripura Tiger Force</p>\n", "<p>Jammu and Kashmir Islamic F<span style=\"font-family: Cambria Math;\">ront</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2326;&#2366;&#2354;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2306;&#2342;&#2366;&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2379;&#2352;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2377;&#2346;&#2369;&#2354;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2381;&#2352;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2321;&#2347;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> (PFI) </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2321;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2346;&#2369;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2366;&#2311;&#2327;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2379;&#2352;&#2381;&#2360;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2332;&#2350;&#2381;&#2350;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2358;&#2381;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2381;&#2354;&#2366;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2381;&#2352;&#2306;&#2335;</span></p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>Popular Front of India (PFI). </strong><span style=\"font-weight: 400;\">This political party was</span><strong> </strong><span style=\"font-weight: 400;\">Founded on 22 November 2006, Headquarters (</span><strong>New Delhi)</strong><span style=\"font-weight: 400;\">.</span><strong> The Ministry of Home Affairs </strong><span style=\"font-weight: 400;\">(MHA) banned</span><strong> PFI </strong><span style=\"font-weight: 400;\">for </span><strong>five </strong><span style=\"font-weight: 400;\">years.</span><strong> All Tripura Tiger Force</strong><span style=\"font-weight: 400;\"> {founded on 11 July 1990, Headquarters (Bangladesh)}. </span><strong>Jammu Kashmir Liberation Front </strong><span style=\"font-weight: 400;\">{founded in June 1976, Headquarters (Muzaffarabad, Azad Jammu and Kashmir (Pakistan)}.&nbsp;</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(b) </span><strong>&#2346;&#2377;&#2346;&#2369;&#2354;&#2352; &#2347;&#2381;&#2352;&#2306;&#2335; &#2321;&#2347; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; (PFI) &#2404;&nbsp; </strong><span style=\"font-weight: 400;\">&#2311;&#2360; &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2342;&#2354; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; 22 &#2344;&#2357;&#2306;&#2348;&#2352; 2006 &#2325;&#2379; &#2361;&#2369;&#2312; &#2341;&#2368;, &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; </span><strong>(&#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;)</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2327;&#2371;&#2361; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351; </strong><span style=\"font-weight: 400;\">(MHA) &#2344;&#2375; </span><strong>PFI </strong><span style=\"font-weight: 400;\">&#2325;&#2379; </span><strong>&#2346;&#2366;&#2306;&#2330; </strong><span style=\"font-weight: 400;\">&#2360;&#2366;&#2354; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2340;&#2367;&#2348;&#2306;&#2343;&#2367;&#2340; &#2325;&#2352; &#2342;&#2367;&#2351;&#2366;&#2404; </span><strong>&#2321;&#2354; &#2340;&#2381;&#2352;&#2367;&#2346;&#2369;&#2352;&#2366; &#2335;&#2366;&#2311;&#2327;&#2352; &#2347;&#2364;&#2379;&#2352;&#2381;&#2360;</strong><span style=\"font-weight: 400;\"> {11 &#2332;&#2369;&#2354;&#2366;&#2312; 1990 &#2325;&#2379; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;, </span><strong>&#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; </strong><span style=\"font-weight: 400;\">(&#2348;&#2366;&#2306;&#2327;&#2381;&#2354;&#2366;&#2342;&#2375;&#2358;)}&#2404; </span><strong>&#2332;&#2350;&#2381;&#2350;&#2370; &#2325;&#2358;&#2381;&#2350;&#2368;&#2352; &#2354;&#2367;&#2348;&#2352;&#2375;&#2358;&#2344; &#2347;&#2381;&#2352;&#2306;&#2335;</strong><span style=\"font-weight: 400;\"> {&#2332;&#2370;&#2344; 1976 &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;, </span><strong>&#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; </strong><span style=\"font-weight: 400;\">(&#2350;&#2369;&#2332;&#2347;&#2381;&#2347;&#2352;&#2366;&#2348;&#2366;&#2342;, &#2310;&#2332;&#2366;&#2342; &#2332;&#2350;&#2381;&#2350;&#2370; &#2324;&#2352; &#2325;&#2358;&#2381;&#2350;&#2368;&#2352; (&#2346;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344;)})&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Which country\'s space agency has launched project \"DART Mission\"</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;&#2367;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2332;&#2375;&#2306;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> \"DART </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\">\" </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2369;&#2352;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>USA</p>\n", "<p>Russia</p>\n", 
                                "<p>India</p>\n", "<p>China</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2309;&#2350;&#2375;&#2352;&#2368;&#2325;&#2366; (U.S.A.)</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2368;&#2344;</span></p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(a)&nbsp;</span><strong>USA.</strong><span style=\"font-weight: 400;\"> The </span><strong>DART </strong><span style=\"font-weight: 400;\">(Double Asteroid Redirection Test) mission is NASA\'s demonstration of kinetic impactor technology, impacting an asteroid to adjust its speed and path.</span><strong> </strong><span style=\"font-weight: 400;\">Russia\'s space agency - </span><strong>Roscosmos</strong><span style=\"font-weight: 400;\">. India&rsquo;s space agency</span><strong> - Indian Space Research Organisation (ISRO).&nbsp;</strong></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(a)&nbsp;</span><strong>USA</strong><span style=\"font-weight: 400;\"> &#2404; </span><strong>&nbsp;DART </strong><span style=\"font-weight: 400;\">(&#2337;&#2348;&#2354; &#2319;&#2360;&#2381;&#2335;&#2375;&#2352;&#2377;&#2351;&#2337; &#2352;&#2367;&#2337;&#2366;&#2351;&#2352;&#2375;&#2325;&#2381;&#2358;&#2344; &#2335;&#2375;&#2360;&#2381;&#2335;) &#2350;&#2367;&#2358;&#2344; &#2344;&#2366;&#2360;&#2366; &#2325;&#2368; &#2325;&#2366;&#2311;&#2344;&#2375;&#2335;&#2367;&#2325; &#2311;&#2350;&#2381;&#2346;&#2376;&#2325;&#2381;&#2335;&#2352; &#2340;&#2325;&#2344;&#2368;&#2325;(</span><span style=\"font-weight: 400;\"> kinetic impactor technology</span><span style=\"font-weight: 400;\">) &#2325;&#2366; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2361;&#2376;, &#2332;&#2379; &#2325;&#2367;&#2360;&#2368; &#2325;&#2381;&#2359;&#2369;&#2342;&#2381;&#2352;&#2327;&#2381;&#2352;&#2361; &#2325;&#2379; &#2313;&#2360;&#2325;&#2368; &#2327;&#2340;&#2367; &#2324;&#2352; &#2346;&#2341; &#2325;&#2379; &#2360;&#2350;&#2366;&#2351;&#2379;&#2332;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2352;&#2370;&#2360; &#2325;&#2368; &#2309;&#2306;&#2340;&#2352;&#2367;&#2325;&#2381;&#2359; &#2319;&#2332;&#2375;&#2306;&#2360;&#2368; - </span><strong>&#2352;&#2377;&#2360;&#2325;&#2377;&#2360;&#2350;&#2377;&#2360;(</strong><strong>Roscosmos</strong><strong>)</strong><span style=\"font-weight: 400;\">&#2404; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2309;&#2306;&#2340;&#2352;&#2367;&#2325;&#2381;&#2359; &#2319;&#2332;&#2375;&#2306;&#2360;&#2368;</span><strong> - &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2309;&#2306;&#2340;&#2352;&#2367;&#2325;&#2381;&#2359; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2360;&#2306;&#2327;&#2336;&#2344; (</strong><strong>ISRO</strong><strong>)&#2404;</strong></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">_______</span><span style=\"font-family: Cambria Math;\">Ambedkar started a temple entry movement, in which his Mahar caste followers participated.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2360;&#2344;&#2381;</span><span style=\"font-family: Cambria Math;\"> __________________</span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2309;&#2350;&#2381;&#2348;&#2375;&#2337;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2306;&#2342;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2306;&#2342;&#2379;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2369;&#2352;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;&#2404;</span></p>\n",
                    options_en: ["<p>1923</p>\n", "<p>1927</p>\n", 
                                "<p>1925</p>\n", "<p>1921</p>\n"],
                    options_hi: ["<p>1923</p>\n", "<p>1927</p>\n",
                                "<p>1925</p>\n", "<p>1921</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Cambria Math;\">1927.&nbsp;</span></strong><span style=\"font-weight: 400;\">Ambedkar led </span><strong>three </strong><span style=\"font-weight: 400;\">such movements for temple entry between </span><strong>1927 and 1935. </strong><span style=\"font-weight: 400;\">His aim was to make everyone see the power of caste prejudices within society. India\'s First Minister for Law and Justice (Ambedkar).</span><strong> Autobiography: &ldquo;</strong><span style=\"font-weight: 400;\">Waiting for a Visa&rdquo;.</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>1927</strong><span style=\"font-weight: 400;\">&#2404; &#2309;&#2350;&#2381;&#2348;&#2375;&#2337;&#2325;&#2352; &#2344;&#2375; </span><strong>1927 </strong><span style=\"font-weight: 400;\">&#2324;&#2352;</span><strong> 1935</strong><span style=\"font-weight: 400;\"> &#2325;&#2375; &#2348;&#2368;&#2330; &#2350;&#2306;&#2342;&#2367;&#2352; &#2346;&#2381;&#2352;&#2357;&#2375;&#2358; &#2325;&#2375; &#2354;&#2367;&#2319; &#2320;&#2360;&#2375;</span><strong> &#2340;&#2368;&#2344; </strong><span style=\"font-weight: 400;\">&#2310;&#2306;&#2342;&#2379;&#2354;&#2344;&#2379;&#2306; &#2325;&#2366; &#2344;&#2375;&#2340;&#2371;&#2340;&#2381;&#2357; &#2325;&#2367;&#2351;&#2366;&#2404; &#2313;&#2344;&#2325;&#2366; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2360;&#2350;&#2366;&#2332; &#2325;&#2375; &#2349;&#2368;&#2340;&#2352; &#2332;&#2366;&#2340;&#2367;&#2327;&#2340; &#2346;&#2370;&#2352;&#2381;&#2357;&#2366;&#2327;&#2381;&#2352;&#2361;&#2379;&#2306; &#2325;&#2368; &#2358;&#2325;&#2381;&#2340;&#2367;&#2323;&#2306;&nbsp; &#2325;&#2379; &#2360;&#2349;&#2368; &#2325;&#2379; &#2342;&#2367;&#2326;&#2366;&#2344;&#2366; &#2341;&#2366;&#2404; &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2346;&#2361;&#2354;&#2375; &#2325;&#2366;&#2344;&#2370;&#2344; &#2324;&#2352; &#2344;&#2381;&#2351;&#2366;&#2351; &#2350;&#2306;&#2340;&#2381;&#2352;&#2368; (&#2309;&#2306;&#2348;&#2375;&#2337;&#2325;&#2352;)&#2404; </span><strong>&#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366; : &ldquo;</strong><span style=\"font-weight: 400;\">&#2357;&#2375;&#2335;&#2367;&#2306;&#2327; &#2347;&#2377;&#2352; &#2319; &#2357;&#2368;&#2332;&#2366;&rdquo;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">What is the p</span><span style=\"font-family: Cambria Math;\">ublic healthcare spending percentage as GDP of India, as per Economic Survey 2020-21?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2310;&#2352;&#2381;&#2341;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2368;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> 2020 - 21 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2352;&#2375;&#2354;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> (GDP) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>2.5<span style=\"font-family: Cambria Math;\">%</span></p>\n", "<p>1<span style=\"font-family: Cambria Math;\">%</span></p>\n", 
                                "<p>4.5<span style=\"font-family: Cambria Math;\">%</span></p>\n", "<p>5.5%</p>\n"],
                    options_hi: ["<p>2.<span style=\"font-family: Cambria Math;\">5%</span></p>\n", "<p>1%</p>\n",
                                "<p>4.5%</p>\n", "<p>5.5%</p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>1%. Gross Domestic Product (</strong><span style=\"font-weight: 400;\">GDP) is the final value of the goods and services produced within the geographic boundaries of a country during a specified period of time, normally a year.</span><strong> GDP = C + I + G + (X-M)</strong><span style=\"font-weight: 400;\">. A country\'s GDP is the total of consumer spending (C) plus business investment (I) and government spending (G), plus net income from foreign (X &ndash; M).</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>1%</strong><span style=\"font-weight: 400;\"> &#2404;&nbsp; </span><strong>&#2360;&#2325;&#2354; &#2328;&#2352;&#2375;&#2354;&#2370; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</strong><span style=\"font-weight: 400;\"> (GDP), &#2325;&#2367;&#2360;&#2368; &#2342;&#2375;&#2358; &#2325;&#2368; &#2349;&#2380;&#2327;&#2379;&#2354;&#2367;&#2325; &#2360;&#2368;&#2350;&#2366;&#2323;&#2306; &#2325;&#2375; &#2349;&#2368;&#2340;&#2352; &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2309;&#2357;&#2343;&#2367; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2307;&nbsp; &#2319;&#2325; &#2357;&#2352;&#2381;&#2359; &#2350;&#2375;&#2306; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2367;&#2340; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2324;&#2352; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2366; &#2309;&#2306;&#2340;&#2367;&#2350; &#2350;&#2370;&#2354;&#2381;&#2351; &#2361;&#2376;&#2404;</span><strong> GDP = C + I + G + (X-M)</strong><span style=\"font-weight: 400;\">&#2404; &#2319;&#2325; &#2342;&#2375;&#2358; &#2325;&#2366; &#2360;&#2325;&#2354; &#2328;&#2352;&#2375;&#2354;&#2370; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;, &#2313;&#2346;&#2349;&#2379;&#2325;&#2381;&#2340;&#2366; &#2326;&#2352;&#2381;&#2330; (C), &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2344;&#2367;&#2357;&#2375;&#2358; (I), &#2360;&#2352;&#2325;&#2366;&#2352;&#2368; &#2326;&#2352;&#2381;&#2330; (G), &#2324;&#2352; &#2357;&#2367;&#2342;&#2375;&#2358;&#2379;&#2306; &#2360;&#2375; &#2309;&#2352;&#2381;&#2332;&#2367;&#2340; &#2358;&#2369;&#2342;&#2381;&#2343; &#2310;&#2351; (X &ndash; M) &#2325;&#2375; &#2325;&#2369;&#2354; &#2351;&#2379;&#2327; &#2325;&#2375; &#2348;&#2352;&#2366;&#2348;&#2352; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404;&nbsp;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Wh</span><span style=\"font-family: Cambria Math;\">ich Indian was awarded the Nobel Prize in Physics in the year 1930?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 1930 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2380;&#2340;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2379;&#2348;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2381;&#2350;&#2366;&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Vikram Sarabhai</p>\n", "<p>C. V. Raman</p>\n", 
                                "<p>Rabindranath Tagore</p>\n", "<p>Amartya Sen</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2352;&#2366;&#2349;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2352;&#2350;&#2344;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2357;&#2368;&#2344;&#2381;&#2342;&#2381;&#2352;&#2344;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2376;&#2327;&#2379;&#2352;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2309;&#2350;&#2352;&#2381;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2344;</span></p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(b)<strong> C .</strong></span><strong>V. Raman</strong> (India\'s first physicist, awarded for his work on the scattering of light). <strong>Vikram Sarabhai: </strong>Father of the Indian space program.&nbsp; <strong>Amartya Sen</strong> (winner of Nobel prize <strong>1998 </strong>for his contributions to <strong>Welfare Economics</strong>). <strong>Rabindranath Tagore</strong> (1913, first Asian to win Nobel prize in Literature).&nbsp;</p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>&#2360;&#2368;. &#2357;&#2368;. &#2352;&#2350;&#2344;</strong><span style=\"font-weight: 400;\"> (&#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2346;&#2361;&#2354;&#2375; &#2349;&#2380;&#2340;&#2367;&#2325; &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;, &#2346;&#2381;&#2352;&#2325;&#2366;&#2358; &#2325;&#2375; &#2346;&#2381;&#2352;&#2325;&#2368;&#2352;&#2381;&#2339;&#2344; &#2346;&#2352; &#2309;&#2346;&#2344;&#2375; &#2325;&#2366;&#2350; &#2325;&#2375; &#2354;&#2367;&#2319; &#2360;&#2350;&#2381;&#2350;&#2366;&#2344;&#2367;&#2340;)&#2404; </span><strong>&#2357;&#2367;&#2325;&#2381;&#2352;&#2350; &#2360;&#2366;&#2352;&#2366;&#2349;&#2366;&#2312;</strong><span style=\"font-weight: 400;\">: &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2309;&#2306;&#2340;&#2352;&#2367;&#2325;&#2381;&#2359; &#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2381;&#2352;&#2350; &#2325;&#2375; &#2332;&#2344;&#2325;&#2404; </span><strong>&#2309;&#2350;&#2352;&#2381;&#2340;&#2381;&#2351; &#2360;&#2375;&#2344;</strong><span style=\"font-weight: 400;\"> (</span><strong>&#2325;&#2354;&#2381;&#2351;&#2366;&#2339; &#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;</strong><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; &#2313;&#2344;&#2325;&#2375; &#2351;&#2379;&#2327;&#2342;&#2366;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; </span><strong>1998 </strong><span style=\"font-weight: 400;\">&#2325;&#2375;</span><strong> </strong><span style=\"font-weight: 400;\">&#2344;&#2379;&#2348;&#2375;&#2354; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</span><strong> </strong><span style=\"font-weight: 400;\">&#2357;&#2367;&#2332;&#2375;&#2340;&#2366;)&#2404; </span><strong>&#2352;&#2357;&#2368;&#2306;&#2342;&#2381;&#2352;&#2344;&#2366;&#2341; &#2335;&#2376;&#2327;&#2379;&#2352;</strong><span style=\"font-weight: 400;\"> (1913, &#2360;&#2366;&#2361;&#2367;&#2340;&#2381;&#2351; &#2350;&#2375;&#2306; &#2344;&#2379;&#2348;&#2375;&#2354; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; &#2332;&#2368;&#2340;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2346;&#2361;&#2354;&#2375; &#2319;&#2358;&#2367;&#2351;&#2366;&#2312;)&#2404;&nbsp;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Which of the following statements regarding ocean currents is correct?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">16</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2360;&#2366;&#2327;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2366;&#2352;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2342;&#2352;&#2381;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>The <span style=\"font-family: Cambria Math;\">Labrador Ocean current is warm current.</span></p>\n", "<p>The areas where the warm and cold currents meet provide the best fishing grounds.</p>\n", 
                                "<p>The cold ocean currents move towards the poles.</p>\n", "<p>The warm ocean currents move towards the equator.</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2348;&#2381;&#2352;&#2366;&#2337;&#2379;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2360;&#2366;&#2327;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2366;&#2352;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2327;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2354;&#2343;&#2366;&#2352;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2357;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2354;&#2343;&#2366;&#2352;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2354;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2381;&#2357;&#2349;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2381;&#2357;&#2379;&#2340;&#2381;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2340;&#2381;&#2360;&#2381;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2358;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2327;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2366;&#2352;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2381;&#2352;&#2369;&#2357;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2357;&#2366;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2327;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2366;&#2360;&#2366;&#2327;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2366;&#2352;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2370;&#2350;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2357;&#2366;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n"],
                    solution_en: "<p>16<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>The areas where the warm and cold currents meet provide the best fishing grounds. Ocean currents</strong><span style=\"font-weight: 400;\"> are the continuous, predictable, directional movement of seawater driven by gravity, wind (Coriolis Effect), and water density. Largest ocean current on Earth: </span><strong>Antarctic Circumpolar Current (ACC). </strong><span style=\"font-weight: 400;\">The </span><strong>Labrador current </strong><span style=\"font-weight: 400;\">is a well-known cold ocean current. </span><strong>Warm currents </strong><span style=\"font-weight: 400;\">are those that travel from the Equator to the poles and are therefore warmer than the surrounding water.</span></p>\n",
                    solution_hi: "<p>16<span style=\"font-family: Cambria Math;\">.(b) </span><strong>&#2332;&#2367;&#2360; &#2360;&#2381;&#2341;&#2366;&#2344; &#2346;&#2352; &#2327;&#2352;&#2381;&#2350; &#2319;&#2357;&#2306; &#2358;&#2368;&#2340; &#2332;&#2354;&#2343;&#2366;&#2352;&#2366;&#2319;&#2305; &#2350;&#2367;&#2354;&#2340;&#2368; &#2361;&#2376;&#2306;, &#2357;&#2361; &#2360;&#2381;&#2341;&#2366;&#2344; &#2357;&#2367;&#2358;&#2381;&#2357;&#2349;&#2352; &#2350;&#2375;&#2306; &#2360;&#2352;&#2381;&#2357;&#2379;&#2340;&#2381;&#2340;&#2350; &#2350;&#2340;&#2381;&#2360;&#2381;&#2351;&#2344; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2306;</strong><span style=\"font-weight: 400;\"> </span><strong>&#2404; </strong><span style=\"font-weight: 400;\">&#2350;&#2361;&#2366;&#2360;&#2366;&#2327;&#2352;&#2368;&#2351; </span><span style=\"font-weight: 400;\">&#2343;&#2366;&#2352;&#2366;&#2319;&#2306; &#2327;&#2369;&#2352;&#2369;&#2340;&#2381;&#2357;&#2366;&#2325;&#2352;&#2381;&#2359;&#2339;, &#2361;&#2357;&#2366; (&#2325;&#2379;&#2352;&#2367;&#2323;&#2354;&#2367;&#2360; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;) &#2324;&#2352; &#2332;&#2354; &#2328;&#2344;&#2340;&#2381;&#2357; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2360;&#2306;&#2330;&#2366;&#2354;&#2367;&#2340; &#2360;&#2350;&#2369;&#2342;&#2381;&#2352;&#2368; &#2332;&#2354; &#2325;&#2368; &#2344;&#2367;&#2352;&#2306;&#2340;&#2352;, &#2346;&#2370;&#2352;&#2381;&#2357;&#2366;&#2344;&#2369;&#2350;&#2375;&#2351;, &#2342;&#2367;&#2358;&#2366;&#2340;&#2381;&#2350;&#2325; &#2327;&#2340;&#2367; &#2361;&#2376;&#2306;&#2404; &#2346;&#2371;&#2341;&#2381;&#2357;&#2368; &#2346;&#2352; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2350;&#2361;&#2366;&#2360;&#2366;&#2327;&#2352;&#2368;&#2351; &#2343;&#2366;&#2352;&#2366; : </span><strong>&#2309;&#2306;&#2335;&#2366;&#2352;&#2381;&#2325;&#2335;&#2367;&#2325; &#2360;&#2352;&#2381;&#2325;&#2350;&#2381;&#2346;&#2379;&#2354;&#2352; &#2343;&#2366;&#2352;&#2366; (ACC)</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2354;&#2376;&#2348;&#2381;&#2352;&#2366;&#2337;&#2379;&#2352; &#2343;&#2366;&#2352;&#2366; </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343; &#2336;&#2306;&#2337;&#2368; &#2350;&#2361;&#2366;&#2360;&#2366;&#2327;&#2352;&#2368;&#2351; &#2343;&#2366;&#2352;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2327;&#2352;&#2381;&#2350; &#2343;&#2366;&#2352;&#2366;&#2319;&#2305;</strong><span style=\"font-weight: 400;\"> &#2357;&#2375; &#2343;&#2366;&#2352;&#2366;&#2319;&#2305; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2306; &#2332;&#2379; &#2349;&#2370;&#2350;&#2343;&#2381;&#2351; &#2352;&#2375;&#2326;&#2366; &#2360;&#2375; &#2343;&#2381;&#2352;&#2369;&#2357;&#2379;&#2306; &#2325;&#2368; &#2323;&#2352; &#2330;&#2354;&#2340;&#2368; &#2361;&#2376;&#2306; &#2324;&#2352; &#2311;&#2360;&#2354;&#2367;&#2319; &#2310;&#2360;&#2346;&#2366;&#2360; &#2325;&#2375; &#2332;&#2354; &#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2350;&#2375;&#2306; &#2327;&#2352;&#2381;&#2350; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Which among the following is database software?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">17</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>MS - PowerPoint</p>\n", "<p>MS - Word</p>\n", 
                                "<p>dBase</p>\n", "<p>Word Pad</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2319;&#2350;&#2319;&#2360; &#2346;&#2366;&#2357;&#2352;&#2346;&#2377;&#2311;&#2306;&#2335; (MS PowerPoint)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2350;&#2319;&#2360; &#2357;&#2352;&#2381;&#2337; (MS-Word)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2337;&#2368;&#2348;&#2375;&#2360; (dBase)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2337;&#2346;&#2376;&#2337; (Word Pad)</span></p>\n"],
                    solution_en: "<p>17<span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><strong>dBase. Database software</strong><span style=\"font-weight: 400;\">&nbsp; or DBMSs is used to create, edit, and maintain database files and records, enabling easier file and record creation, data entry, data editing, updating, and reporting. </span><strong>Examples : </strong><span style=\"font-weight: 400;\">MySQL, Microsoft Access, Microsoft SQL Server, FileMaker Pro, Oracle Database, and dBASE.</span></p>\n",
                    solution_hi: "<p>17.(c)&nbsp;<strong>&#2337;&#2368;&#2348;&#2375;&#2360; (dBase) </strong><span style=\"font-weight: 400;\">&#2404;&nbsp; </span><strong>&#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2360;&#2377;&#2347;&#2364;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</strong><span style=\"font-weight: 400;\"> &#2351;&#2366; DBMS &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2347;&#2364;&#2366;&#2311;&#2354;&#2379;&#2306; &#2324;&#2352; &#2352;&#2367;&#2325;&#2377;&#2352;&#2381;&#2337; &#2325;&#2379; &#2348;&#2344;&#2366;&#2344;&#2375;, &#2360;&#2306;&#2346;&#2366;&#2342;&#2367;&#2340; (edit) &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2348;&#2344;&#2366;&#2319; &#2352;&#2326;&#2344;&#2375; (maintain)&nbsp; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2367;&#2360;&#2360;&#2375; &#2311;&#2395;&#2367;&#2351;&#2352; &#2347;&#2364;&#2366;&#2311;&#2354; (easier file) &#2324;&#2352; &#2352;&#2367;&#2325;&#2377;&#2352;&#2381;&#2337; &#2325;&#2381;&#2352;&#2367;&#2319;&#2358;&#2344; (record creation), &#2337;&#2366;&#2335;&#2366; &#2319;&#2306;&#2335;&#2381;&#2352;&#2368; (data entry), &#2337;&#2366;&#2335;&#2366; &#2319;&#2337;&#2367;&#2335;&#2367;&#2306;&#2327; (data editing), &#2309;&#2346;&#2337;&#2375;&#2335;&#2367;&#2306;&#2327; (updating) &#2324;&#2352; &#2352;&#2367;&#2346;&#2379;&#2352;&#2381;&#2335;&#2367;&#2306;&#2327; (reporting) &#2325;&#2379; &#2360;&#2325;&#2381;&#2359;&#2350; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2375;&#2404; </span><strong>&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; </strong><span style=\"font-weight: 400;\">: MySQL, Microsoft Access, &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2360;&#2377;&#2347;&#2381;&#2335; SQL &#2360;&#2352;&#2381;&#2357;&#2352;, &#2347;&#2366;&#2311;&#2354;&#2350;&#2375;&#2325;&#2352; &#2346;&#2381;&#2352;&#2379;, &#2323;&#2352;&#2375;&#2325;&#2354; &#2337;&#2366;&#2335;&#2366;&#2348;&#2375;&#2360; &#2324;&#2352; dBase .</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Which among the following Indian cities received the World Green City Aw</span><span style=\"font-family: Cambria Math;\">ard 2022?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">18</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2361;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2354;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2381;&#2352;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>New Delhi</p>\n", "<p>Hyderabad</p>\n", 
                                "<p>Mumbai</p>\n", "<p>Pune</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2344;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2342;&#2352;&#2366;&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2306;&#2348;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2339;&#2375;</span></p>\n"],
                    solution_en: "<p>18<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>Hyderabad. </strong><strong>The AIPH (International Association of Horticultural Producers)</strong><span style=\"font-weight: 400;\"> World Green City Awards are given to champion ambitious nature-oriented approaches to city design and operation.</span><span style=\"font-weight: 400;\"> It has been the best across all six categories. Hyderabad beats cities like Paris, Montreal, Fortaleza, Mexico City, and Bogota.&nbsp;</span></p>\n",
                    solution_hi: "<p>18<span style=\"font-family: Cambria Math;\">.(b) </span><strong>&#2361;&#2376;&#2342;&#2352;&#2366;&#2348;&#2366;&#2342;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>AIPH (&#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2358;&#2344;&#2354; &#2319;&#2360;&#2379;&#2360;&#2367;&#2319;&#2358;&#2344; &#2321;&#2347; &#2361;&#2377;&#2352;&#2381;&#2335;&#2367;&#2325;&#2354;&#2381;&#2330;&#2352;&#2354; &#2346;&#2381;&#2352;&#2379;&#2337;&#2381;&#2351;&#2370;&#2360;&#2352;&#2381;&#2360;) </strong><span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2354;&#2381;&#2337; &#2327;&#2381;&#2352;&#2368;&#2344; &#2360;&#2367;&#2335;&#2368; &#2309;&#2357;&#2366;&#2352;&#2381;&#2337;&#2381;&#2360;, &#2358;&#2361;&#2352; &#2325;&#2375; &#2337;&#2367;&#2332;&#2366;&#2311;&#2344; &#2324;&#2352; &#2360;&#2306;&#2330;&#2366;&#2354;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; &#2330;&#2376;&#2306;&#2346;&#2367;&#2351;&#2344; &#2350;&#2361;&#2340;&#2381;&#2357;&#2366;&#2325;&#2366;&#2306;&#2325;&#2381;&#2359;&#2368; &#2346;&#2381;&#2352;&#2325;&#2371;&#2340;&#2367;-&#2313;&#2344;&#2381;&#2350;&#2369;&#2326; &#2342;&#2371;&#2359;&#2381;&#2335;&#2367;&#2325;&#2379;&#2339;&#2379;&#2306; &#2325;&#2379; &#2342;&#2367;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><span style=\"font-weight: 400;\">&#2351;&#2361; &#2360;&#2349;&#2368; &#2331;&#2361; &#2358;&#2381;&#2352;&#2375;&#2339;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404; &#2361;&#2376;&#2342;&#2352;&#2366;&#2348;&#2366;&#2342; &#2344;&#2375; &#2346;&#2375;&#2352;&#2367;&#2360;, &#2350;&#2377;&#2344;&#2381;&#2335;&#2381;&#2352;&#2367;&#2351;&#2354;, &#2347;&#2379;&#2352;&#2381;&#2335;&#2366;&#2354;&#2375;&#2332;&#2366;, &#2350;&#2376;&#2325;&#2381;&#2360;&#2367;&#2325;&#2379; &#2360;&#2367;&#2335;&#2368; &#2324;&#2352; &#2348;&#2379;&#2327;&#2379;&#2335;&#2366; &#2332;&#2376;&#2360;&#2375; &#2358;&#2361;&#2352;&#2379;&#2306; &#2325;&#2379; &#2346;&#2368;&#2331;&#2375; &#2325;&#2352; &#2342;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404;&nbsp;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Article 143 of the Indi</span><span style=\"font-family: Cambria Math;\">an constitution is related with which of the following?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342;</span><span style=\"font-family: Cambria Math;\"> 143 </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Power of the President to enlarge jurisdiction of the Supreme Court</p>\n", "<p>Power of the President to consult the Supreme Court</p>\n", 
                                "<p>Power of the President to appoint National Judicial Appointments Commission</p>\n", "<p>Power of the President to transfer certain cases from High Courts to Supreme Court</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2313;&#2330;&#2381;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\">&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2330;&#2381;&#2330;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2366;&#2350;&#2352;&#2381;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2366;&#2351;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2369;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2330;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2350;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> (cases) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2330;&#2381;&#2330;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368; </span><span style=\"font-family: Cambria Math;\">&#2358;&#2325;&#2381;&#2340;&#2367;</span></p>\n"],
                    solution_en: "<p>19<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>Power of the President to consult the Supreme Court. Article-138:</strong><span style=\"font-weight: 400;\"> Enlargement of the jurisdiction of the Supreme Court. </span><strong>Article-143: </strong><span style=\"font-weight: 400;\">Power of the President to consult the Supreme Court. </span><strong>Article-218: </strong><span style=\"font-weight: 400;\">Application of certain provisions relating to the Supreme Court to High Courts.</span></p>\n",
                    solution_hi: "<p>19<span style=\"font-family: Cambria Math;\">.(b)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2313;&#2330;&#2381;&#2330;&#2340;&#2350; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351; &#2360;&#2375; &#2346;&#2352;&#2366;&#2350;&#2352;&#2381;&#2358; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2325;&#2368; &#2358;&#2325;&#2381;&#2340;&#2367;</strong><strong>&#2404; &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342;-138 :</strong><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&#2313;&#2330;&#2381;&#2330;&#2340;&#2350; </span><span style=\"font-weight: 400;\">&#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351; &#2325;&#2375; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2357;&#2371;&#2342;&#2381;&#2343;&#2367;&#2404; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342;-143 :</strong><span style=\"font-weight: 400;\"> &#2313;&#2330;&#2381;&#2330;&#2340;&#2350; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351; &#2360;&#2375; &#2346;&#2352;&#2366;&#2350;&#2352;&#2381;&#2358; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2325;&#2368; &#2358;&#2325;&#2381;&#2340;&#2367;&#2404; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342;-218 :</strong><span style=\"font-weight: 400;\"> &#2313;&#2330;&#2381;&#2330;&#2340;&#2350; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2325;&#2369;&#2331; &#2346;&#2381;&#2352;&#2366;&#2357;&#2343;&#2366;&#2344;&#2379;&#2306; &#2325;&#2366; &#2313;&#2330;&#2381;&#2330; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351;&#2379;&#2306; &#2346;&#2352; &#2354;&#2366;&#2327;&#2370; &#2361;&#2379;&#2344;&#2366;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Article 72 of the Indian constitution is related with the power of the ______to grant pardons, etc. and to sus</span><span style=\"font-family: Cambria Math;\">pend, remit or commute sentences in certain cases.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">20</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342;</span><span style=\"font-family: Cambria Math;\"> 72, </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2350;&#2366;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2350;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2354;&#2306;&#2348;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2328;&#2369;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> __________ </span><span style=\"font-family: Cambria Math;\">&#2325;</span><span style=\"font-family: Cambria Math;\">&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Chief Minister</p>\n", "<p>Governor</p>\n", 
                                "<p>President</p>\n", "<p>Prime Minister</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2326;&#2381;&#2351;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2332;&#2381;&#2351;&#2346;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368;</span></p>\n"],
                    solution_en: "<p>20<span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><strong>President. </strong><span style=\"font-weight: 400;\">This pardoning power of the President is independent of the Judiciary.&nbsp; </span><strong>Article-52: </strong><span style=\"font-weight: 400;\">&nbsp;There shall be a President of India. </span><strong>Article-60: </strong><span style=\"font-weight: 400;\">Oath or affirmation by the President. </span><strong>Article 111</strong><span style=\"font-weight: 400;\">- Veto Power of the President of India. </span><strong>Article 123</strong><span style=\"font-weight: 400;\">- Ordinance making power of the president. </span><strong>Emergency powers of the President:</strong><span style=\"font-weight: 400;\"> (three types) National Emergency (Article 352), President\'s Rule (Article 356), Financial Emergency (Article 360).&nbsp;</span></p>\n",
                    solution_hi: "<p>20<span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><strong>&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367;</strong><span style=\"font-weight: 400;\">&#2404; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2325;&#2368; &#2351;&#2361; &#2325;&#2381;&#2359;&#2350;&#2366;&#2342;&#2366;&#2344; &#2358;&#2325;&#2381;&#2340;&#2367; &#2344;&#2381;&#2351;&#2366;&#2351;&#2346;&#2366;&#2354;&#2367;&#2325;&#2366; &#2360;&#2375; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352; &#2361;&#2376;&#2404;</span><strong> &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342;-52 : </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2319;&#2325; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2361;&#2379;&#2327;&#2366;&#2404; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342;-60 :</strong><span style=\"font-weight: 400;\"> &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2358;&#2346;&#2341; &#2351;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;&#2404; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 111</strong><span style=\"font-weight: 400;\">- &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2325;&#2368; &#2357;&#2368;&#2335;&#2379; &#2358;&#2325;&#2381;&#2340;&#2367;&#2404; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 123</strong><span style=\"font-weight: 400;\"> - &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2325;&#2379; &#2309;&#2343;&#2381;&#2351;&#2366;&#2342;&#2375;&#2358; &#2348;&#2344;&#2366;&#2344;&#2375; &#2325;&#2368; &#2358;&#2325;&#2381;&#2340;&#2367;&#2404;</span><strong> &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2325;&#2368; &#2310;&#2346;&#2366;&#2340;&#2325;&#2366;&#2354;&#2368;&#2344; &#2358;&#2325;&#2381;&#2340;&#2367;&#2351;&#2366;&#2305;</strong><span style=\"font-weight: 400;\"> (&#2340;&#2368;&#2344; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;): &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2310;&#2346;&#2366;&#2340;&#2325;&#2366;&#2354; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 352), &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2358;&#2366;&#2360;&#2344; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 356), &#2357;&#2367;&#2340;&#2381;&#2340;&#2368;&#2351; &#2310;&#2346;&#2366;&#2340;&#2325;&#2366;&#2354; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 360)</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">When did Indigo revolt, a peasant movement, star</span><span style=\"font-family: Cambria Math;\">t in India?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">21</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2342;&#2381;&#2352;&#2379;&#2361;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2371;&#2359;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2344;&#2381;&#2342;&#2379;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2369;&#2352;&#2369;&#2310;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>1857</p>\n", "<p>1861</p>\n", 
                                "<p>1859</p>\n", "<p>1889</p>\n"],
                    options_hi: ["<p>1857</p>\n", "<p>1861</p>\n",
                                "<p>1859</p>\n", "<p>1889</p>\n"],
                    solution_en: "<p>21<span style=\"font-family: Cambria Math;\">.(c)</span><strong>1859 (March). </strong><span style=\"font-weight: 400;\">The </span><strong>Indigo Rebellio</strong><span style=\"font-weight: 400;\">n (Neel Bidroh) started in </span><strong>Bengal </strong><span style=\"font-weight: 400;\">and was a revolt by the farmers against British planters who had forced them to grow indigo under terms that were greatly unfavorable to the farmers.&nbsp;</span></p>\n",
                    solution_hi: "<p>21<span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><strong>1859 (</strong><span style=\"font-weight: 400;\">&#2350;&#2366;&#2352;&#2381;&#2330; </span><strong>) </strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&nbsp;</strong><strong>&#2344;&#2368;&#2354; &#2357;&#2367;&#2342;&#2381;&#2352;&#2379;&#2361;</strong><span style=\"font-weight: 400;\">, </span><strong>&#2348;&#2306;&#2327;&#2366;&#2354; </strong><span style=\"font-weight: 400;\">&#2350;&#2375;&#2306; &#2358;&#2369;&#2352;&#2370; &#2361;&#2369;&#2310; &#2324;&#2352; &#2325;&#2367;&#2360;&#2366;&#2344;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2348;&#2366;&#2327;&#2366;&#2344; &#2350;&#2366;&#2354;&#2367;&#2325;&#2379;&#2306; &#2325;&#2375; &#2326;&#2367;&#2354;&#2366;&#2347; &#2319;&#2325; &#2357;&#2367;&#2342;&#2381;&#2352;&#2379;&#2361; &#2341;&#2366;, &#2332;&#2367;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2313;&#2344;&#2381;&#2361;&#2375;&#2306; &#2313;&#2344; &#2358;&#2352;&#2381;&#2340;&#2379;&#2306; &#2325;&#2375; &#2340;&#2361;&#2340; &#2344;&#2368;&#2354; &#2313;&#2327;&#2366;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2350;&#2332;&#2348;&#2370;&#2352; &#2325;&#2367;&#2351;&#2366; &#2341;&#2366; &#2332;&#2379; &#2325;&#2367;&#2360;&#2366;&#2344;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2361;&#2369;&#2340; &#2346;&#2381;&#2352;&#2340;&#2367;&#2325;&#2370;&#2354; &#2341;&#2375;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">What is the official language of Bhutan, a neighbouring country of India?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">22</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2337;&#2364;&#2379;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2370;&#2335;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Kyat</p>\n", "<p>Dzongkha</p>\n", 
                                "<p>Yuan</p>\n", "<p>Ngultrum</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> (Kyat ) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2332;&#2379;&#2306;&#2327;&#2326;&#2366; (Dzongkha)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2351;&#2369;&#2310;&#2344; (Yuan)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2344;&#2379;&#2306;&#2327;&#2381;&#2354;&#2369;&#2350; ( Ngultrum)</span></p>\n"],
                    solution_en: "<p>22<span style=\"font-family: Cambria Math;\">.(b)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Dzongkha. Bhutan: </strong><span style=\"font-weight: 400;\">&nbsp;Dzongkha or Bhutanese is a Sino- Tibetan language spoken by over half a million people in Bhutan and it is the sole official and national language of the Kingdom of Bhutan. </span><strong>Capital </strong><span style=\"font-weight: 400;\">(Thimphu), </span><strong>Currency </strong><span style=\"font-weight: 400;\">(Ngultrum), </span><strong>Boundary</strong><span style=\"font-weight: 400;\">: Sikkim, West Bengal, Assam, Arunachal Pradesh,</span><strong> National Sport: </strong><span style=\"font-weight: 400;\">Archery</span></p>\n",
                    solution_hi: "<p>22<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>&#2332;&#2379;&#2306;&#2327;&#2326;&#2366;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2349;&#2370;&#2335;&#2366;&#2344; :</strong><span style=\"font-weight: 400;\"> &#2332;&#2364;&#2379;&#2306;&#2327;&#2326;&#2366; &#2351;&#2366; &#2349;&#2370;&#2335;&#2366;&#2344;&#2368; &#2319;&#2325; &#2330;&#2368;&#2344;-&#2340;&#2367;&#2348;&#2381;&#2348;&#2340;&#2368; &#2349;&#2366;&#2359;&#2366; &#2361;&#2376; &#2332;&#2379; &#2349;&#2370;&#2335;&#2366;&#2344; &#2350;&#2375;&#2306; &#2310;&#2343;&#2375; &#2350;&#2367;&#2354;&#2367;&#2351;&#2344; &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2354;&#2379;&#2327;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2379;&#2354;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376; &#2324;&#2352; &#2351;&#2361; &#2349;&#2370;&#2335;&#2366;&#2344; &#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2368; &#2319;&#2325;&#2350;&#2366;&#2340;&#2381;&#2352; &#2310;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2325; &#2324;&#2352; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2349;&#2366;&#2359;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2352;&#2366;&#2332;&#2343;&#2366;&#2344;&#2368; </strong><span style=\"font-weight: 400;\">(&#2341;&#2367;&#2350;&#2381;&#2347;&#2370;), </span><strong>&#2350;&#2369;&#2342;&#2381;&#2352;&#2366; </strong><span style=\"font-weight: 400;\">(&#2344;&#2327;&#2369;&#2354;&#2335;&#2381;&#2352;&#2350;), </span><strong>&#2360;&#2368;&#2350;&#2366; :</strong><span style=\"font-weight: 400;\"> &#2360;&#2367;&#2325;&#2381;&#2325;&#2367;&#2350;, &#2346;&#2358;&#2381;&#2330;&#2367;&#2350; &#2348;&#2306;&#2327;&#2366;&#2354;, &#2309;&#2360;&#2350;, &#2309;&#2352;&#2369;&#2339;&#2366;&#2330;&#2354; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;, </span><strong>&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2326;&#2375;&#2354; : </strong><span style=\"font-weight: 400;\">&#2340;&#2368;&#2352;&#2306;&#2342;&#2366;&#2332;&#2368; &#2404;&nbsp;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">What is the zone between the </span><span style=\"font-family: Cambria Math;\">arctic circle</span><span style=\"font-family: Cambria Math;\"> and north pole called?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2381;&#2352;&#2369;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2381;&#2352;&#2369;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Torrid <span style=\"font-family: Cambria Math;\">zone</span></p>\n", "<p>Frigid <span style=\"font-family: Cambria Math;\">zone</span></p>\n", 
                                "<p>North <span style=\"font-family: Cambria Math;\">temperate zone</span></p>\n", "<p>South <span style=\"font-family: Cambria Math;\">temperate zone</span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2313;&#2359;&#2381;&#2339; &#2325;&#2335;&#2367;&#2348;&#2306;&#2343; (Torrid zone)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2358;&#2368;&#2340; &#2325;&#2335;&#2367;&#2348;&#2306;&#2343; (Frigid zone)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2313;&#2340;&#2381;&#2340;&#2352;&#2368; &#2358;&#2368;&#2340;&#2379;&#2359;&#2381;&#2339; &#2325;&#2335;&#2367;&#2348;&#2306;&#2343; (North temperate zone)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;&#2368; &#2358;&#2368;&#2340;&#2379;&#2359;&#2381;&#2339; &#2325;&#2335;&#2367;&#2348;&#2306;&#2343; (South temperate Zone)</span></p>\n"],
                    solution_en: "<p>23.(b) <strong>Frigid zone. </strong><span style=\"font-weight: 400;\">The </span><strong>North Frigid Zone</strong><span style=\"font-weight: 400;\">, between the North Pole at 90&deg; N and the Arctic Circle at 66&deg;33&prime;48.7\" N, covers</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">4.12% of Earth\'s surface. The</span><strong> torrid zone</strong><span style=\"font-weight: 400;\"> refers to the area of the earth between the Tropic of Cancer and the Tropic of Capricorn. The </span><strong>north temperate zone </strong><span style=\"font-weight: 400;\">is located between the Arctic Circle at 66&deg; 33&prime; N and the Tropic of Cancer at 23&deg; 27&prime; N. The </span><strong>south temperate zone</strong><span style=\"font-weight: 400;\"> is located between the Tropic of Capricorn at 23&deg; 27&prime; S and the Antarctic Circle at 66&deg; 33&prime; S.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>23.(b)&nbsp;<strong>&#2358;&#2368;&#2340; &#2325;&#2335;&#2367;&#2348;&#2306;&#2343; (Frigid zone) I &#2313;&#2340;&#2381;&#2340;&#2352;&#2368; &#2358;&#2368;&#2340; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</strong><span style=\"font-weight: 400;\">, &#2313;&#2340;&#2381;&#2340;&#2352;&#2368; &#2343;&#2381;&#2352;&#2369;&#2357; 90&deg; N&nbsp; &#2324;&#2352; &#2310;&#2352;&#2381;&#2325;&#2335;&#2367;&#2325; &#2357;&#2371;&#2340; 66&deg;33&prime;48.7\" N &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2361;&#2376;, &#2346;&#2371;&#2341;&#2381;&#2357;&#2368; &#2325;&#2368; &#2360;&#2340;&#2361; &#2325;&#2366; 4.12% &#2361;&#2376;&#2404; </span><strong>&#2313;&#2359;&#2381;&#2339; &#2325;&#2335;&#2367;&#2348;&#2306;&#2343;, </strong><span style=\"font-weight: 400;\">&#2346;&#2371;&#2341;&#2381;&#2357;&#2368; &#2325;&#2375; &#2325;&#2352;&#2381;&#2325; &#2352;&#2375;&#2326;&#2366; &#2324;&#2352; &#2350;&#2325;&#2352; &#2352;&#2375;&#2326;&#2366; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2379; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;&nbsp; </span><strong>&#2313;&#2340;&#2381;&#2340;&#2352;&#2368; &#2360;&#2350;&#2358;&#2368;&#2340;&#2379;&#2359;&#2381;&#2339; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</strong><span style=\"font-weight: 400;\"> &#2310;&#2352;&#2381;&#2325;&#2335;&#2367;&#2325; &#2357;&#2371;&#2340; 66&deg; 33\' N &#2324;&#2352; &#2325;&#2352;&#2381;&#2325; &#2352;&#2375;&#2326;&#2366; 23&deg; 27\' N &#2325;&#2375; &#2348;&#2368;&#2330; &#2360;&#2381;&#2341;&#2367;&#2340; &#2361;&#2376;&#2404; </span><strong>&#2342;&#2325;&#2381;&#2359;&#2367;&#2339; &#2360;&#2350;&#2358;&#2368;&#2340;&#2379;&#2359;&#2381;&#2339; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</strong><span style=\"font-weight: 400;\"> &#2350;&#2325;&#2352; &#2352;&#2375;&#2326;&#2366; 23&deg; 27\' S &#2324;&#2352; &#2309;&#2306;&#2335;&#2366;&#2352;&#2381;&#2325;&#2335;&#2367;&#2325; &#2357;&#2371;&#2340;&#2381;&#2340; 66&deg; 33\' S &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Sir Tim Berners - Lee a British computer scientist invented the revolutionary World Wide Web</span><span style=\"font-family: Cambria Math;\"> in ______</span><span style=\"font-family: Cambria Math;\">_ .</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">24</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2381;&#2344;&#2352;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> (Sir Tim Berners Lee) &ndash; </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2376;&#2332;&#2381;&#2334;&#2366;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2354;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2311;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2367;&#2359;&#2381;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> _______________</span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;&#2404;</span></p>\n",
                    options_en: ["<p>1982</p>\n", "<p>1995</p>\n", 
                                "<p>1990</p>\n", "<p>1989</p>\n"],
                    options_hi: ["<p>1982</p>\n", "<p>1995</p>\n",
                                "<p>1990</p>\n", "<p>1989</p>\n"],
                    solution_en: "<p>24<span style=\"font-family: Cambria Math;\">.(c) </span><strong>1990</strong><strong>. </strong><span style=\"font-weight: 400;\">Tim Berners-Lee submitted a proposal to The European Organization for Nuclear Research </span><strong>(CERN)</strong><span style=\"font-weight: 400;\"> in May 1989, without giving the system a name. He got a working system implemented by the end of 1990, including a browser called WorldWideWeb. Other Invention of </span><strong>Tim Berners-Lee</strong><span style=\"font-weight: 400;\"> HTML, HTTP, web browser.</span></p>\n",
                    solution_hi: "<p>24<span style=\"font-family: Cambria Math;\">.(c) </span><strong>1990</strong><span style=\"font-weight: 400;\"> &#2404;&nbsp; &#2335;&#2367;&#2350; &#2348;&#2352;&#2381;&#2344;&#2352;&#2381;&#2360;-&#2354;&#2368; &#2344;&#2375; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2379; &#2325;&#2379;&#2312; &#2344;&#2366;&#2350; &#2342;&#2367;&#2319; &#2348;&#2367;&#2344;&#2366;, &#2350;&#2312; 1989 &#2350;&#2375;&#2306; &#2351;&#2370;&#2352;&#2379;&#2346;&#2368;&#2351; &#2344;&#2366;&#2349;&#2367;&#2325;&#2368;&#2351; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2360;&#2306;&#2327;&#2336;&#2344; </span><strong>(CERN)</strong><span style=\"font-weight: 400;\"> &#2325;&#2379; &#2319;&#2325; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2369;&#2340; &#2325;&#2367;&#2351;&#2366;&#2404; &#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; 1990 &#2325;&#2375; &#2309;&#2306;&#2340; &#2340;&#2325; &#2319;&#2325; &#2325;&#2366;&#2352;&#2381;&#2351; &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368; &#2354;&#2366;&#2327;&#2370; &#2325;&#2352;&#2357;&#2366;&#2312;, &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; WorldWideWeb &#2344;&#2366;&#2350;&#2325; &#2319;&#2325; &#2348;&#2381;&#2352;&#2366;&#2313;&#2332;&#2364;&#2352; &#2349;&#2368; &#2358;&#2366;&#2350;&#2367;&#2354; &#2341;&#2366; &#2404; </span><strong>&#2335;&#2367;&#2350; &#2348;&#2352;&#2381;&#2344;&#2352;&#2381;&#2360;-&#2354;&#2368; </strong><span style=\"font-weight: 400;\">&#2325;&#2375; &#2309;&#2344;&#2381;&#2351; &#2310;&#2357;&#2367;&#2359;&#2381;&#2325;&#2366;&#2352; HTML, HTTP, &#2357;&#2375;&#2348; &#2348;&#2381;&#2352;&#2366;&#2313;&#2332;&#2364;&#2352; &#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> How many Vedas are there in Indian literature of classical Hinduism?</span></p>\n",
                    question_hi: "<p>25<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2352;&#2350;&#2381;&#2346;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2367;&#2306;&#2342;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2361;&#2367;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Th<span style=\"font-family: Cambria Math;\">ree</span></p>\n", "<p>Four</p>\n", 
                                "<p>One</p>\n", "<p>Two</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2340;&#2368;&#2344;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2352;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span></p>\n"],
                    solution_en: "<p>25<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>Four. </strong><span style=\"font-weight: 400;\">Types of Vedas: Rigveda, Samaveda, Yajurveda, and Atharvaveda. Each Veda has four subdivisions - The </span><strong>Samhitas </strong><span style=\"font-weight: 400;\">(mantras and benedictions), the </span><strong>Aranyakas </strong><span style=\"font-weight: 400;\">(text on rituals, ceremonies, sacrifices and symbolic-sacrifices), the </span><strong>Brahmanas </strong><span style=\"font-weight: 400;\">(commentaries on rituals, ceremonies and sacrifices), and the </span><strong>Upanishads </strong><span style=\"font-weight: 400;\">(texts discussing meditation, philosophy and spiritual knowledge).</span></p>\n",
                    solution_hi: "<p>25<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>&#2330;&#2366;&#2352; &#2404;&nbsp; </strong><span style=\"font-weight: 400;\">&#2357;&#2375;&#2342;&#2379;&#2306; &#2325;&#2375; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; : &#2315;&#2327;&#2381;&#2357;&#2375;&#2342;, &#2360;&#2366;&#2350;&#2357;&#2375;&#2342;, &#2351;&#2332;&#2369;&#2352;&#2381;&#2357;&#2375;&#2342; &#2324;&#2352; &#2309;&#2341;&#2352;&#2381;&#2357;&#2357;&#2375;&#2342;&#2404; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2357;&#2375;&#2342; &#2325;&#2375; &#2330;&#2366;&#2352; &#2313;&#2346;&#2326;&#2306;&#2337; &#2361;&#2376;&#2306; -</span><strong> &#2360;&#2306;&#2361;&#2367;&#2340;&#2366;</strong><span style=\"font-weight: 400;\"> (&#2350;&#2306;&#2340;&#2381;&#2352; &#2324;&#2352; &#2310;&#2358;&#2368;&#2352;&#2381;&#2357;&#2366;&#2342;), </span><strong>&#2310;&#2352;&#2339;&#2381;&#2351;&#2325;</strong><span style=\"font-weight: 400;\"> (&#2309;&#2344;&#2369;&#2359;&#2381;&#2336;&#2366;&#2344;, &#2360;&#2350;&#2366;&#2352;&#2379;&#2361;, &#2348;&#2354;&#2367;&#2342;&#2366;&#2344; &#2324;&#2352; &#2346;&#2381;&#2352;&#2340;&#2368;&#2325;&#2366;&#2340;&#2381;&#2350;&#2325;-&#2348;&#2354;&#2367;&#2342;&#2366;&#2344; &#2346;&#2352; &#2346;&#2366;&#2336;),</span><strong> &#2348;&#2381;&#2352;&#2366;&#2361;&#2381;&#2350;&#2339;</strong><span style=\"font-weight: 400;\"> (&#2309;&#2344;&#2369;&#2359;&#2381;&#2336;&#2366;&#2344;, &#2360;&#2350;&#2366;&#2352;&#2379;&#2361; &#2324;&#2352; &#2348;&#2354;&#2367;&#2342;&#2366;&#2344; &#2346;&#2352; &#2335;&#2367;&#2346;&#2381;&#2346;&#2339;&#2368;) &#2324;&#2352; </span><strong>&#2313;&#2346;&#2344;&#2367;&#2359;&#2342;</strong><span style=\"font-weight: 400;\"> (&#2343;&#2381;&#2351;&#2366;&#2344;, &#2342;&#2352;&#2381;&#2358;&#2344; &#2324;&#2352; &#2310;&#2343;&#2381;&#2351;&#2366;&#2340;&#2381;&#2350;&#2367;&#2325; &#2332;&#2381;&#2334;&#2366;&#2344; &#2346;&#2352; &#2330;&#2352;&#2381;&#2330;&#2366; &#2325;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2327;&#2381;&#2352;&#2306;&#2341;) |</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>