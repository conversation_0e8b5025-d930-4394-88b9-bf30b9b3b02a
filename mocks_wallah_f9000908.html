<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Resentment </span></strong></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word. </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Resentment </span></strong></p>\n",
                    options_en: ["<p>Calmness</p>\n", "<p>Happiness</p>\n", 
                                "<p>Anger</p>\n", "<p>Relaxation</p>\n"],
                    options_hi: ["<p>Calmness</p>\n", "<p>Happiness</p>\n",
                                "<p>Anger</p>\n", "<p>Relaxation</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Anger</strong>- </span><span style=\"font-family: Cambria Math;\">a strong feeling of annoyance, displeasure, or hostility.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Resentment</strong>-</span><span style=\"font-family: Cambria Math;\"> a feeling of angry displeasure at something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Calmness</strong>-</span><span style=\"font-family: Cambria Math;\"> the state of being free from disturbance or violent activity.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Happiness</strong>-</span><span style=\"font-family: Cambria Math;\"> the state of be</span><span style=\"font-family: Cambria Math;\">ing happy.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Relaxation </strong>-</span><span style=\"font-family: Cambria Math;\"> the state of being free from tension and anxiety.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Anger </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2379;&#2343;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">a strong feeling of annoyance, displeasure, or hostility.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Resentment</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2379;&#2343;</span><span style=\"font-family: Cambria Math;\">) - a feeling of angry displeasure at something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Calmness </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">) - the state </span><span style=\"font-family: Cambria Math;\">of being free from disturbance or violent activity.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Happiness </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2360;&#2344;&#2381;&#2344;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">the state of being happy.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Relaxation </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\">) - the state of being free from tension and anxiety.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym for the </span><span style=\"font-family: Cambria Math;\"><strong>underlined</strong> </span><span style=\"font-family: Cambria Math;\">word in the following </span><span style=\"font-family: Cambria Math;\">sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The politician\'s </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">pompous</span> </span><span style=\"font-family: Cambria Math;\">rhetoric during the campaign failed to garner much support from the public.</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym for the </span><span style=\"font-family: Cambria Math;\"><strong>underlined</strong> </span><span style=\"font-family: Cambria Math;\">word in the following sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The politician\'s </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">pompous</span> </span><span style=\"font-family: Cambria Math;\">rhetoric during the campaign failed to garner much support from the public.</span></p>\n",
                    options_en: ["<p>Conciliatory</p>\n", "<p>Benign</p>\n", 
                                "<p>Supercilious</p>\n", "<p>Diplomatic</p>\n"],
                    options_hi: ["<p>Conciliatory</p>\n", "<p>Benign</p>\n",
                                "<p>Supercilious</p>\n", "<p>Diplomatic</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Supercilious</strong>- </span><span style=\"font-family: Cambria Math;\">behaving as if you think you are superior to others.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Pompous</strong>-</span><span style=\"font-family: Cambria Math;\"> acting in a way that makes them look better than others.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Conciliatory</strong>-</span><span style=\"font-family: Cambria Math;\"> intended to pacify someone.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Benign</strong>-</span><span style=\"font-family: Cambria Math;\"> gentle and kindly.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Diplomatic</strong>-</span><span style=\"font-family: Cambria Math;\"> smoothness and skill in handling matters.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c) <strong>S</strong></span><strong><span style=\"font-family: Cambria Math;\">upercilious</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2328;&#2350;&#2306;&#2337;&#2368;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2309;&#2349;&#2367;&#2350;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\">) - behaving as if you think you are superior to others.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Pompous</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2337;&#2306;&#2348;&#2352;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\">) - acting in a way that makes them look better than others.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Conciliatory</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2350;&#2376;&#2340;&#2381;&#2352;&#2368;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\">) - intended to pacify someone.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Benign</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2351;&#2366;&#2354;&#2369;</span><span style=\"font-family: Cambria Math;\">) - gentle and kindly.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Diplomatic</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2332;&#2344;&#2351;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">) - smoothness and skill in handling matters.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the word &lsquo;Keen&rsquo; from the given sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I am eager and happy to visit the beautiful city of Darjeeling next month with my family.</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the word &lsquo;Keen&rsquo; from the given sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I am eager and happy to visit the beautiful city of Darjeeling next month with my family.</span></p>\n",
                    options_en: ["<p>beautiful</p>\n", "<p>visit</p>\n", 
                                "<p>eager</p>\n", "<p>happy</p>\n"],
                    options_hi: ["<p>beautiful</p>\n", "<p>visit</p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"> eager</span></p>\n", "<p>happy</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Eager</strong>- </span><span style=\"font-family: Cambria Math;\">very enthusiastic or excited.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Keen</strong>- </span><span style=\"font-family: Cambria Math;\">very interested or enthusiastic.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Beautiful</strong>- </span><span style=\"font-family: Cambria Math;\">attractive or pleasing to the senses.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Visit</strong>- </span><span style=\"font-family: Cambria Math;\">go</span><span style=\"font-family: Cambria Math;\"> somewhere to see someone or spend time.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Happy</strong>- </span><span style=\"font-family: Cambria Math;\">feeling or showing pleasure.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Eager </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2360;&#2369;&#2325;</span><span style=\"font-family: Cambria Math;\">) - very enthusiastic or excited.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Keen </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2311;&#2330;&#2381;&#2331;&#2369;&#2325;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">very interested or enthusiastic.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Beautiful </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2306;&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\">) - attractive or pleasing to the senses.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Visit</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> go somewhere to see someone or spend time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Happy </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2360;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">feeling or showing pleasure.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym for the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The guest was </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">heckled</span></span><span style=\"font-family: Cambria Math;\"> by the spectators.</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym for the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The guest was </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">heckled</span></span><span style=\"font-family: Cambria Math;\"> by the spectators.</span></p>\n",
                    options_en: ["<p>Calmed</p>\n", "<p>Aided</p>\n", 
                                "<p>Taunted</p>\n", "<p>Helped</p>\n"],
                    options_hi: ["<p>Calmed</p>\n", "<p>Aided</p>\n",
                                "<p>Taunted</p>\n", "<p>Helped</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Taunted</strong>-</span><span style=\"font-family: Cambria Math;\"> teased or provoked someone mockingly.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Heckled</strong>- </span><span style=\"font-family: Cambria Math;\">unplea</span><span style=\"font-family: Cambria Math;\">santly interrupted or harassed.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Calmed</strong>- </span><span style=\"font-family: Cambria Math;\">made peaceful or relaxed.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Aided</strong>- </span><span style=\"font-family: Cambria Math;\">provided assistance or support.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Helped</strong>- </span><span style=\"font-family: Cambria Math;\">assisted or gave support to someone.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Taunted</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2361;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)- teased or provoked someone mockingly.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Heckled </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2340;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2375;&#2358;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - un</span><span style=\"font-family: Cambria Math;\">pleasantly interrupted or harassed.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Calmed</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\">) - made peaceful or relaxed.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Aided</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2366;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">provided assistance or support.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Helped</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2366;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;</span><span style=\"font-family: Cambria Math;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> )</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">assisted or gave support to someone.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym for the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Compel</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym for the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Compel</span></p>\n",
                    options_en: ["<p>Allow</p>\n", "<p>Stop</p>\n", 
                                "<p>Give</p>\n", "<p>Obligate</p>\n"],
                    options_hi: ["<p>Allow</p>\n", "<p>Stop</p>\n",
                                "<p>Give</p>\n", "<p>Obligate</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Obligate</strong>- </span><span style=\"font-family: Cambria Math;\">to make someone feel morally or legally forced to do something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Compel</strong>-</span><span style=\"font-family: Cambria Math;\"> to force or oblige someone to do something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Stop</strong>-</span><span style=\"font-family: Cambria Math;\"> to prevent something from happening.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Give</strong>-</span><span style=\"font-family: Cambria Math;\"> to offer something to someone.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Obligate</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">to make someone feel mor</span><span style=\"font-family: Cambria Math;\">ally or legally forced to do something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Compel </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2350;&#2332;&#2348;&#2370;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Cambria Math;\"> to force or oblige someone to do something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Stop </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2352;&#2379;&#2325;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to prevent something from happening.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Give </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to offer something to someone.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the un</span><span style=\"font-family: Cambria Math;\">derlined word in the following </span><span style=\"font-family: Cambria Math;\">sentence</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Although the new fashion trend was quite popular, some critics were quick to </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">deride</span></span><span style=\"font-family: Cambria Math;\"> it </span><span style=\"font-family: Cambria Math;\">as</span><span style=\"font-family: Cambria Math;\"> a passing fad.</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the following </span><span style=\"font-family: Cambria Math;\">sentence</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Although the new fashion trend was quite popular, some critics were quick to </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">deride</span></span><span style=\"font-family: Cambria Math;\"> it </span><span style=\"font-family: Cambria Math;\">as</span><span style=\"font-family: Cambria Math;\"> a passing fad.</span></p>\n",
                    options_en: ["<p>Ridicule</p>\n", "<p>Hail</p>\n", 
                                "<p>Detest</p>\n", "<p>Applaud</p>\n"],
                    options_hi: ["<p>Ridicule</p>\n", "<p>Hail</p>\n",
                                "<p>Detest</p>\n", "<p>Applaud</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">a) <strong>Ridicule</strong>- </span><span style=\"font-family: Cambria Math;\">to make fun of something or someone in a hurtful way.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Deride</strong>-</span><span style=\"font-family: Cambria Math;\"> to mock someone, often in a disrespectful manner.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Hail</strong>-</span><span style=\"font-family: Cambria Math;\"> to publicly praise or show approval for a person</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Detest</strong>-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">to dislike</span><span style=\"font-family: Cambria Math;\"> intensely.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Applaud</strong>-</span><span style=\"font-family: Cambria Math;\"> to show approval or praise by clappi</span><span style=\"font-family: Cambria Math;\">ng.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Ridicule </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2361;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to make fun of something or someone in a hurtful way.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Deride </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2361;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to mock someone, often in a disrespectful manner.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Hail</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2306;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to publicly praise or show approval for a person</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Detest</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2328;&#2371;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> - to dislike intensely.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Applaud</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2366;&#2361;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to show approval or praise by clapping.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym for the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">colossal</span></span><span style=\"font-family: Cambria Math;\"> amount of money has been wasted on the construction of a new administrative building.</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym for the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">colossal</span></span><span style=\"font-family: Cambria Math;\"> amount of money has been wasted on the construction of a new administrative building.</span></p>\n",
                    options_en: ["<p>huge</p>\n", "<p>miniature</p>\n", 
                                "<p>small</p>\n", "<p>micro</p>\n"],
                    options_hi: ["<p>huge</p>\n", "<p>miniature</p>\n",
                                "<p>small</p>\n", "<p>micro</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Huge</strong>- </span><span style=\"font-family: Cambria Math;\">extremely large.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Colossal</strong>-</span><span style=\"font-family: Cambria Math;\"> extremely large or great.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Miniature</strong>-</span><span style=\"font-family: Cambria Math;\"> very small of its kind.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Micro</strong>- </span><span style=\"font-family: Cambria Math;\">e</span><span style=\"font-family: Cambria Math;\">xtremely small.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Huge</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2348;&#2361;&#2369;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">extremely large.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Colossa</span></strong><span style=\"font-family: Cambria Math;\"><strong>l </strong>(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\">) - extremely large or great.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Miniature</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2354;&#2328;&#2369;</span><span style=\"font-family: Cambria Math;\">) - very small of its kind.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Micro </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2370;&#2325;&#2381;&#2359;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> extremely small.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the bracketed word in the following sentence to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The security cameras can automatically ___________ (rotate) to monitor the entire hallway.</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">S</span><span style=\"font-family: Cambria Math;\">elect the most appropriate synonym of the bracketed word in the following sentence to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The security cameras can automatically ___________ (rotate) to monitor the entire hallway.</span></p>\n",
                    options_en: ["<p>evolve</p>\n", "<p>resolve</p>\n", 
                                "<p>revolve</p>\n", "<p>devolve</p>\n"],
                    options_hi: ["<p>evolve</p>\n", "<p>resolve</p>\n",
                                "<p>revolve</p>\n", "<p>devolve</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Revolve</strong>- </span><span style=\"font-family: Cambria Math;\">move in a circle on a central axis.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Rotate</strong>-</span><span style=\"font-family: Cambria Math;\"> move in a circle around an axis or centre.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Evolve</strong>-</span><span style=\"font-family: Cambria Math;\"> develop gradually.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Resolve</strong>-</span><span style=\"font-family: Cambria Math;\"> to settle or find a solution to a problem or dispute.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Devolve</strong>-</span><span style=\"font-family: Cambria Math;\"> to pass into a different state, especially a worse one.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Revolve </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2325;&#2381;&#2352;&#2350;&#2339;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">move in a circle on a central axis.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Rotate</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2328;&#2369;&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - move in a circle around an axis or centre.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Evolve </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2360;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - develop gradually.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Resolve </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> to </span><span style=\"font-family: Cambria Math;\">settle or find a solution to a problem or dispute.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Devolve</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2327;&#2367;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to pass into a different state, especially a worse one.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the word \'Scrutiny\' from the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The company\'s strategic plan included a comprehensive analysis of market trends and consumer </span><span style=\"font-family: Cambria Math;\">behaviour</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the word \'Scrutiny\' from the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">T</span><span style=\"font-family: Cambria Math;\">he company\'s strategic plan included a comprehensive analysis of market trends and consumer </span><span style=\"font-family: Cambria Math;\">behaviour</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Behaviour</span></p>\n", "<p>Comprehensive</p>\n", 
                                "<p>Trends</p>\n", "<p>Analysis</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">Behaviour</span></p>\n", "<p>Comprehensive</p>\n",
                                "<p>Trends</p>\n", "<p>Analysis</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Analysis</strong>- </span><span style=\"font-family: Cambria Math;\">detailed examination of the structure of something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Scrutiny</strong>-</span><span style=\"font-family: Cambria Math;\"> c</span><span style=\"font-family: Cambria Math;\">ritical observation or examination.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Behaviour</span></strong><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> the way in which one acts or conducts oneself, especially towards others</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Comprehensive</strong>-</span><span style=\"font-family: Cambria Math;\"> dealing with all or nearly all aspects of something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Trends</strong>-</span><span style=\"font-family: Cambria Math;\"> a general direction in which something is developing or chan</span><span style=\"font-family: Cambria Math;\">ging.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Analysis </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2381;&#2354;&#2375;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">detailed examination of the structure of something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Scrutiny</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2368;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> critical observation or examination.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Behaviour</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2357;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">) - the way in which one acts or conducts oneself, especially towards others</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Comprehensive </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2360;&#2381;&#2340;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\">) - dealing with all or nearly all aspects of something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Trends</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2330;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> a general direction in which something is developing or changing.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the word \'Capable\' from the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jayesh</span><span style=\"font-family: Cambria Math;\"> was competent and the best candidate for the post of clerk in the interview, whereas others were inefficient and irritating.</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the word \'Capable\' </span><span style=\"font-family: Cambria Math;\">from the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Jayesh</span><span style=\"font-family: Cambria Math;\"> was competent and the best candidate for the post of clerk in the interview, whereas others were inefficient and irritating.</span></p>\n",
                    options_en: ["<p>best</p>\n", "<p>irritating</p>\n", 
                                "<p>competent</p>\n", "<p>inefficient</p>\n"],
                    options_hi: ["<p>best</p>\n", "<p>irritating</p>\n",
                                "<p>competent</p>\n", "<p>inefficient</p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(c) <strong>Competent</strong>- </span><span style=\"font-family: Cambria Math;\">having th</span><span style=\"font-family: Cambria Math;\">e necessary ability, knowledge, or skill to do something successfully.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Capable</strong>-</span><span style=\"font-family: Cambria Math;\"> having the ability, fitness, or quality necessary to do or achieve a specified thing.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Best</strong>-</span><span style=\"font-family: Cambria Math;\"> of the most excellent or desirable type or quality.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Irritating</strong>-</span><span style=\"font-family: Cambria Math;\"> causing annoyance, i</span><span style=\"font-family: Cambria Math;\">mpatience, or mild anger.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Inefficient</strong>-</span><span style=\"font-family: Cambria Math;\"> failing to make best use of time or resources.</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(c) <strong>Competent</strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2381;&#2359;&#2350;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">having the necessary ability, knowledge, or skill to do something successfully.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Capable </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\">) - having the ability, fitness, or q</span><span style=\"font-family: Cambria Math;\">uality necessary to do or achieve a specified thing.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Best </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2381;&#2357;&#2379;&#2340;&#2381;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\">) - of the most excellent or desirable type or quality.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Irritating</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2330;&#2367;&#2337;&#2364;&#2330;&#2367;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\">) - causing annoyance, impatience, or mild anger.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Inefficient</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\">) - failing to make best use of time or resources.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Caves like Ajanta lie in </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">perpetual</span></span><span style=\"font-family: Cambria Math;\"> darkness.</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Caves like Ajanta lie in </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">perpetual</span></span><span style=\"font-family: Cambria Math;\"> darkness.</span></p>\n",
                    options_en: ["<p>Permanent</p>\n", "<p>Extreme</p>\n", 
                                "<p>Brief</p>\n", "<p>Huge</p>\n"],
                    options_hi: ["<p>Permanent</p>\n", "<p>Extreme</p>\n",
                                "<p>Brief</p>\n", "<p>Huge</p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(a) <strong>Permanent</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>- lasting forever.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Perpetual</strong>- </span><span style=\"font-family: Cambria Math;\">lasting for a very long time.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Extreme</strong>-</span><span style=\"font-family: Cambria Math;\"> v</span><span style=\"font-family: Cambria Math;\">ery high or intense.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Brief</strong>- </span><span style=\"font-family: Cambria Math;\">short in duration.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Huge</strong>- </span><span style=\"font-family: Cambria Math;\">extremely large.</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(a) <strong>Permanent</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2351;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- lasting forever.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Perpetual</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2330;&#2367;&#2352;&#2360;&#2381;&#2341;&#2366;&#2351;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">lasting for a very long time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Extreme</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2330;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\">) - very high or intense.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Brief</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2325;&#2381;&#2359;&#2367;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\">) - short in duration.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Huge</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">extremely large.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the word \'Pervasive\' from the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The scientist\'s hypothesis was substantiated by extensive research and experimentation.</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the word \'Pervasive\' from the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The scientist\'s hypothesis was substantiated by extensive research and experimentation.</span></p>\n",
                    options_en: ["<p>Extensive</p>\n", "<p>Substantiated</p>\n", 
                                "<p>Exp<span style=\"font-family: Cambria Math;\">erimentation</span></p>\n", "<p>Hypothesis</p>\n"],
                    options_hi: ["<p>Extensive</p>\n", "<p>Substantiated</p>\n",
                                "<p>Experimentation</p>\n", "<p>Hypothesis</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(a) <strong>Extensive </strong>- </span><span style=\"font-family: Cambria Math;\">covering a large area or having a wide scope.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Pervasive </strong>- </span><span style=\"font-family: Cambria Math;\">that is present in all parts of something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Substantiated </strong>- </span><span style=\"font-family: Cambria Math;\">supported with evidence or proven to be true.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Experimentation </strong>- </span><span style=\"font-family: Cambria Math;\">the a</span><span style=\"font-family: Cambria Math;\">ct of conducting tests or trials.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Hypothesis </strong>- </span><span style=\"font-family: Cambria Math;\">an assumption made based on some evidence. </span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(a) <strong>Extensive </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2325;</span><span style=\"font-family: Cambria Math;\">) - covering a large area or having a wide scope.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Pervasive</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2381;&#2357;&#2357;&#2381;&#2351;&#2366;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">that is present in all parts of something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Substantiated</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2359;&#2381;&#2335;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - supported with evidence or proven to be true.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Experimentation </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">the act of conducting tests or trials.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Hypothesis </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2325;&#2354;&#2381;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">an assumption made based on some evidence. </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym o</span><span style=\"font-family: Cambria Math;\">f the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The event, in a cramped space, was totally </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">bizarre</span></span><span style=\"font-family: Cambria Math;\"> in my view.</span></p>\n",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The event, </span><span style=\"font-family: Cambria Math;\">in a cramped space, was totally </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">bizarre</span></span><span style=\"font-family: Cambria Math;\"> in my view.</span></p>\n",
                    options_en: ["<p>Usual</p>\n", "<p>Customary</p>\n", 
                                "<p>Odd</p>\n", "<p>Normal</p>\n"],
                    options_hi: ["<p>Usual</p>\n", "<p>Customary</p>\n",
                                "<p>Odd</p>\n", "<p>Normal</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(c) <strong>Odd </strong>- </span><span style=\"font-family: Cambria Math;\">different from what is expected.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Bizarre</strong>- </span><span style=\"font-family: Cambria Math;\">very strange or unusual.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Usual</strong>- </span><span style=\"font-family: Cambria Math;\">typical or common.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Customary</strong>- </span><span style=\"font-family: Cambria Math;\">following established practices or customs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Normal</strong>- </span><span style=\"font-family: Cambria Math;\">usual or expected.</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(c) <strong>Odd </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2330;&#2367;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">different from what is expected.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Bizarre </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2330;&#2367;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">) - very strange or unusual.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Usual </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">typical or common.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Customary </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2341;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">following established practices or customs.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Normal </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">usual or expected.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tranquil</span></p>\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the</span><span style=\"font-family: Cambria Math;\"> given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tranquil</span></p>\n",
                    options_en: ["<p>Anxious</p>\n", "<p>Dilemma</p>\n", 
                                "<p>Fierce</p>\n", "<p>Peaceful</p>\n"],
                    options_hi: ["<p>Anxious</p>\n", "<p>Dilemma</p>\n",
                                "<p>Fierce</p>\n", "<p>Peaceful</p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(d) <strong>Peaceful</strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Tranquil</strong>-</span><span style=\"font-family: Cambria Math;\"> calm and peaceful.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Anxious</strong>- </span><span style=\"font-family: Cambria Math;\">feeling or showing worry, nervousness or unease about something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Dilemma</strong>-</span><span style=\"font-family: Cambria Math;\"> a difficult situation or problem.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Fierce</strong>-</span><span style=\"font-family: Cambria Math;\"> having or displaying an intense aggressiveness.</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(d) <strong>Peaceful</strong></span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Tranquil</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2344;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\">) - calm and peaceful.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Anxious </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2330;&#2367;&#2306;&#2340;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">feeling or showing worry, nervousness or unease about something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Dilemma</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2357;&#2367;&#2343;&#2366;</span><span style=\"font-family: Cambria Math;\">) - a difficult situation or problem.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Fi</span><span style=\"font-family: Cambria Math;\">erce</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">( </span><span style=\"font-family: Cambria Math;\">&#2313;&#2327;</span><span style=\"font-family: Cambria Math;\">&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">/ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2370;&#2352;</span><span style=\"font-family: Cambria Math;\">)- having or displaying an intense aggressiveness.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the underlined word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">He was the </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">discreet</span></span><span style=\"font-family: Cambria Math;\"> boy in our group but more hardworking than all of us.</span></p>\n",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the underlined word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">He was the </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">discreet</span></span><span style=\"font-family: Cambria Math;\"> boy in our group but more hardworking than all of us.</span></p>\n",
                    options_en: ["<p>loud</p>\n", "<p>outspoken</p>\n", 
                                "<p>reserved</p>\n", "<p>noisy</p>\n"],
                    options_hi: ["<p>loud</p>\n", "<p>outspoken</p>\n",
                                "<p>reserved</p>\n", "<p>noisy</p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(c) <strong>Reserved</strong>- </span><span style=\"font-family: Cambria Math;\">slow to reveal emotio</span><span style=\"font-family: Cambria Math;\">n or opinions.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Discreet</strong>-</span><span style=\"font-family: Cambria Math;\"> respectful of privacy and secrecy.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Loud</strong>-</span><span style=\"font-family: Cambria Math;\"> making a lot of noise.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Outspoken</strong>-</span><span style=\"font-family: Cambria Math;\"> frank in stating one\'s opinions, especially if they are shocking or controversial.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Noisy</strong>-</span><span style=\"font-family: Cambria Math;\"> making a lot of noise.</span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(c) <strong>Reserved </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2350;&#2367;&#2354;&#2344;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2325;&#2379;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\">) - slow to reveal emotion or opinions.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Discreet</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2344;&#2351;&#2358;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2344;&#2350;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">) - respectful of privacy and secrecy.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Loud</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2313;&#2306;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\">) - making a lot of noise.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Outspoken </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">) - frank in stating one\'s opinions, especially if they are shocking or c</span><span style=\"font-family: Cambria Math;\">ontroversial.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Noisy</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2358;&#2379;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2330;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\">) - making a lot of noise. </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>