<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">20:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">If a : b : c = 3 : 2 : 4 and b : d = 2 : 3, then find the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mrow><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mo>+</mo><mo>&nbsp;</mo><mi>d</mi></mrow><mrow><mi>c</mi><mo>&nbsp;</mo><mo>-</mo><mi>b</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math> </span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> a : b : c = 3 : 2 : 4 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> b : d = 2 : 3 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mrow><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mi>c</mi><mo>+</mo><mi>d</mi></mrow><mrow><mi>c</mi><mo>&nbsp;</mo><mo>-</mo><mi>b</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math> &nbsp;</span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>729</mn><mn>8</mn></mfrac></math></p>\n", "<p>216</p>\n", 
                                "<p>125</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>27</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>729</mn><mn>8</mn></mfrac></math></p>\n", "<p>216</p>\n",
                                "<p>125</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>27</mn></mfrac></math></p>\n"],
                    solution_en: "<p>1.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">The ratio of a, b, c and d respectively = 3 : 2 : 4 : 3</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>S</mi><mi>o</mi><mo>,</mo><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>v</mi><mi>a</mi><mi>l</mi><mi>u</mi><mi>e</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mrow><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>d</mi></mrow><mrow><mi>c</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mrow><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn></mrow><mrow><mn>4</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mn>10</mn><mn>2</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mn>5</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>125</mn></math></p>\n",
                    solution_hi: "<p>1.(c)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> a, b, c </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> d </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 3: 2: 4: 3</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mrow><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>d</mi></mrow><mrow><mi>c</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mrow><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn></mrow><mrow><mn>4</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mn>10</mn><mn>2</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mn>5</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>125</mn></math></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">The ratio between the volumes of Sphere 1 and Sphere 2 is 27 : 64. Find the ratio of the surface areas of Sphere 1 and Sphere2.</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Kokila;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> 1 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2340;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 27 : 64 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> 1 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>9 : 16</p>\n", "<p>16 : 9</p>\n", 
                                "<p>3 : 4</p>\n", "<p>1 : 3</p>\n"],
                    options_hi: ["<p>9 : 16</p>\n", "<p>16 : 9</p>\n",
                                "<p>3 : 4</p>\n", "<p>1 : 3</p>\n"],
                    solution_en: "<p>2.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>v</mi><mi>o</mi><mi>l</mi><mi>u</mi><mi>m</mi><mi>e</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>s</mi><mi>p</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><mi>v</mi><mi>o</mi><mi>l</mi><mi>u</mi><mi>m</mi><mi>e</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>s</mi><mi>p</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&pi;</mi><msup><msub><mi>r</mi><mn>1</mn></msub><mn>3</mn></msup></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle><mi>&pi;</mi><msup><msub><mi>r</mi><mn>2</mn></msub><mn>3</mn></msup></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><msup><msub><mi>r</mi><mn>1</mn></msub><mn>3</mn></msup><msup><msub><mi>r</mi><mn>2</mn></msub><mn>3</mn></msup></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>27</mn><mn>64</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>S</mi><mi>o</mi><mo>,</mo><mfrac><mrow><mo>&nbsp;</mo><msub><mi>r</mi><mn>1</mn></msub></mrow><msub><mi>r</mi><mn>2</mn></msub></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>u</mi><mi>r</mi><mi>f</mi><mi>a</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>s</mi><mi>p</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>&pi;</mi><mi>r</mi><msup><mn>1</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>&pi;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msup><mn>3</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>18</mn><mi>&pi;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>S</mi><mi>u</mi><mi>r</mi><mi>f</mi><mi>a</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>s</mi><mi>p</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>&pi;</mi><mi>r</mi><msup><mn>2</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>&pi;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msup><mn>4</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>32</mn><mi>&pi;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>o</mi><mo>,</mo><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>r</mi><mi>e</mi><mi>q</mi><mi>u</mi><mi>i</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>r</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>18</mn><mi>&pi;</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>32</mn><mi>&pi;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>16</mn></math></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>2.(a)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>v</mi><mi>o</mi><mi>l</mi><mi>u</mi><mi>m</mi><mi>e</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>s</mi><mi>p</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><mi>v</mi><mi>o</mi><mi>l</mi><mi>u</mi><mi>m</mi><mi>e</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>s</mi><mi>p</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&pi;</mi><msup><msub><mi>r</mi><mn>1</mn></msub><mn>3</mn></msup></mrow><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&pi;</mi><msup><msub><mi>r</mi><mn>2</mn></msub><mn>3</mn></msup></mstyle></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><msup><msub><mi>r</mi><mn>1</mn></msub><mn>3</mn></msup><msup><msub><mi>r</mi><mn>2</mn></msub><mn>3</mn></msup></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>27</mn><mn>64</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>&nbsp;</mo><mo>,</mo><mfrac><mrow><mo>&nbsp;</mo><msub><mi>r</mi><mn>1</mn></msub></mrow><msub><mi>r</mi><mn>2</mn></msub></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2327;&#2379;&#2354;&#2375;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</mi><mo>&nbsp;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>&pi;</mi><mi>r</mi><msup><mn>1</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>&pi;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msup><mn>3</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>18</mn><mi>&pi;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2327;&#2379;&#2354;&#2375;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</mi><mo>&nbsp;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>&pi;</mi><mi>r</mi><msup><mn>2</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>&pi;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msup><mn>4</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>32</mn><mi>&pi;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2340;&#2307;</mi><mo>&nbsp;</mo><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>18</mn><mi>&pi;</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>32</mn><mi>&pi;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>16</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>=</mo><mfrac><mi>B</mi><mn>3</mn></mfrac><mo>=</mo><mfrac><mi>C</mi><mn>7</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">, then what is the value of the ratio (4A + 3B - C) : (A + B + C)?</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>=</mo><mfrac><mi>B</mi><mn>3</mn></mfrac><mo>=</mo><mfrac><mi>C</mi><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> (4A + 3B - C) : (A + B + C) </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>7 : 6</p>\n", "<p>5 : 6</p>\n", 
                                "<p>9 : 8</p>\n", "<p>8 : 5</p>\n"],
                    options_hi: ["<p>7 : 6</p>\n", "<p>5 : 6</p>\n",
                                "<p>9 : 8</p>\n", "<p>8 : 5</p>\n"],
                    solution_en: "<p>3.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mi>B</mi></mrow><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mi>C</mi></mrow><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>K</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then, A : B : C = 2K : 3K : 7K = 2 : 3 : 7</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, 4A + 3B - C = 4&times;2 + 3&times;3 - 7 = 8 + 9 - 7 = 10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">and A + B + C = 2 + 3 + 7 = 12</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, the required ratio is 10 : 12 or 5 : 6</span></p>\n",
                    solution_hi: "<p>3.(b)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>A</mi><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mi>B</mi></mrow><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mi>C</mi></mrow><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>K</mi></math></span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\">, A : B : C = 2K : 3K : 7K = 2 : 3 : 7</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, 4A + 3B - C = 4&times;2 + 3&times;3 - 7 = 8 + 9 - 7 = 10</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\">A + B + C = 2 + 3 + 7 = 12</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2368;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> 10 : 12 </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 5 : 6</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> In a municipal election three candidates, Santosh, Ravi, and Vipin, contested and the valid votes they got were in the ratio of 11: 4: 9. If the winner defeated his nearest rival by 8350 votes, then how many valid votes were polled in that election?</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2327;&#2352;&#2346;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2369;&#2344;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2350;&#2381;&#2350;&#2368;&#2342;&#2357;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2340;&#2379;&#2359;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2352;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2346;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2369;&#2344;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2367;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2376;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2379;&#2335;</span><span style=\"font-family: Cambria Math;\"> 11: 4: 9 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2332;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2325;&#2335;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2342;&#2381;&#2357;&#2306;&#2342;&#2381;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 8350 </span><span style=\"font-family: Kokila;\">&#2357;&#2379;&#2335;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2352;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2376;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2379;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2369;&#2344;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2337;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>1,02,000</p>\n", "<p>2,10,000</p>\n", 
                                "<p>1,90,000</p>\n", "<p>1,00,200</p>\n"],
                    options_hi: ["<p>1,02,000</p>\n", "<p>2,10,000</p>\n",
                                "<p>1,90,000</p>\n", "<p>1,00,200</p>\n"],
                    solution_en: "<p>4.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the valid votes got by Santosh , Ravi and Vipin be 11x , 4x and 9x respectively</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total valid votes = 11x + 4x + 9x = 24x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">11x - 9x = 8350 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x = 8350</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8350</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 4175</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the total valid votes = 24x = 24 &times; 4175 = 1,00,200</span></p>\n",
                    solution_hi: "<p>4.(d)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2340;&#2379;&#2359;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2352;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2346;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2367;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2376;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2379;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 11x, 4x </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 9x </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2376;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2379;&#2335;</span><span style=\"font-family: Cambria Math;\"> = 11x + 4x + 9x = 24x</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">11x - 9x = 8350 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x = 8350</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8350</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 4175</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2376;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2379;&#2335;</span><span style=\"font-family: Cambria Math;\"> = 24x = 24 &times; 4175 = 1,00,200</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. &nbsp;<span style=\"font-weight: 400;\">In an office with 40 officers, the average salaries of class-A, class-B and class-C officers are &#8377;600, &#8377;750 and &#8377;1,000 a day, respectively. The number of class-A, class-B and class-C officers in the office are in the ratio 5 : 4 : 1, respectively. Find the monthly average salary (in &#8377;) of an officers, all the 40 of them taken together. [Assume the number of days in a month to be 30.]</span></p>\n",
                    question_hi: "<p>5.&nbsp;<strong>&nbsp;</strong><span style=\"font-weight: 400;\">40 &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2357;&#2366;&#2354;&#2375; &#2325;&#2366;&#2352;&#2381;&#2351;&#2366;&#2354;&#2351; &#2350;&#2375;&#2306;, &#2325;&#2381;&#2354;&#2366;&#2360;-A, &#2325;&#2381;&#2354;&#2366;&#2360;-B &#2324;&#2352; &#2325;&#2381;&#2354;&#2366;&#2360;-C &#2325;&#2375; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; &#2357;&#2375;&#2340;&#2344; &#2325;&#2381;&#2352;&#2350;&#2358;&#2307; &#8377;600, &#8377;750 &#2324;&#2352; &#8377;1,000 &#2346;&#2381;&#2352;&#2340;&#2367;&#2342;&#2367;&#2344; &#2361;&#2376;&#2404; &#2325;&#2366;&#2352;&#2381;&#2351;&#2366;&#2354;&#2351; &#2350;&#2375;&#2306; &#2357;&#2352;&#2381;&#2327;-A, &#2357;&#2352;&#2381;&#2327;-B &#2324;&#2352; &#2357;&#2352;&#2381;&#2327;-C &#2325;&#2375; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2381;&#2352;&#2350;&#2358;: 5:4:1 &#2325;&#2375; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2368;&#2351;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; 40 &#2361;&#2376;, &#2340;&#2379; &#2360;&#2349;&#2368; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2368;&#2351;&#2379;&#2306; &#2325;&#2366; &#2350;&#2366;&#2360;&#2367;&#2325; &#2324;&#2360;&#2340; &#2357;&#2375;&#2340;&#2344; (&#2350;&#2375;&#2306;) &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404; [&#2350;&#2366;&#2344; &#2354;&#2368;&#2332;&#2367;&#2319; &#2325;&#2367; &#2313;&#2360; &#2350;&#2361;&#2368;&#2344;&#2375; &#2350;&#2375;&#2306; &#2342;&#2367;&#2344;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; 30 &#2361;&#2376;&#2404;]</span></p>\n",
                    options_en: ["<p>25,800</p>\n", "<p>21,000</p>\n", 
                                "<p>24,030</p>\n", "<p>24,600</p>\n"],
                    options_hi: ["<p>25,800</p>\n", "<p>21,000</p>\n",
                                "<p>24,030</p>\n", "<p>24,600</p>\n"],
                    solution_en: "<p>5.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>C</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>N</mi><mi>o</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mi>f</mi><mi>i</mi><mi>c</mi><mi>e</mi><mi>r</mi><mi>s</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>A</mi><mi>v</mi><mi>e</mi><mi>r</mi><mi>a</mi><mi>g</mi><mi>e</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>250</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>500</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>S</mi><mi>o</mi><mo>,</mo><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>a</mi><mi>v</mi><mi>e</mi><mi>r</mi><mi>a</mi><mi>g</mi><mi>e</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mn>40</mn><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mi>f</mi><mi>i</mi><mi>c</mi><mi>e</mi><mi>r</mi><mi>s</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1000</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>500</mn></mrow><mn>10</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>2000</mn></mrow><mn>10</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>200</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>700</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>o</mi><mo>,</mo><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>m</mi><mi>o</mi><mi>n</mi><mi>t</mi><mi>h</mi><mi>l</mi><mi>y</mi><mo>&nbsp;</mo><mi>s</mi><mi>a</mi><mi>l</mi><mi>a</mi><mi>r</mi><mi>y</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mi>f</mi><mi>i</mi><mi>c</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>700</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>30</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&#8377;</mo><mn>21</mn><mo>,</mo><mn>000</mn></math></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>5.(b)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>C</mi><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>&#2324;&#2360;&#2340;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>250</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>500</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>&#2309;&#2340;</mi><mo>:</mo><mo>&nbsp;</mo><mn>40</mn><mo>&nbsp;</mo><mi>&#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2324;&#2360;&#2340;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1000</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>500</mn></mrow><mn>10</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>2000</mn></mrow><mn>10</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>500</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>200</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>700</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2340;</mi><mo>:</mo><mo>&nbsp;</mo><mi>&#2319;&#2325;</mi><mo>&nbsp;</mo><mi>&#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2368;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2350;&#2366;&#2360;&#2367;&#2325;</mi><mo>&nbsp;</mo><mi>&#2357;&#2375;&#2340;&#2344;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>700</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>30</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&#8377;</mo><mn>21</mn><mo>,</mo><mn>000</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">A and B currently have an age ratio of 7 : 3. After 7 years this age ratio will become 2 : 1.After how many years from now will the age ratio between the ages of A and B become 3 : 2?</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 7:3 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 2 :1 </span><span style=\"font-family: Kokila;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 3:2 </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>49 years</p>\n", "<p>42 years</p>\n", 
                                "<p>35 years</p>\n", "<p>28 years</p>\n"],
                    options_hi: ["<p>49 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>42 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>35 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>28 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>6.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>B</mi><mi>a</mi><mi>l</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>g</mi><mi>i</mi><mi>v</mi><mi>e</mi><mi>n</mi><mo>&nbsp;</mo><mi>r</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mo>,</mo><mo>&nbsp;</mo><mi>w</mi><mi>e</mi><mo>&nbsp;</mo><mi>h</mi><mi>a</mi><mi>v</mi><mi>e</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>B</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>T</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>r</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>C</mi><mi>u</mi><mi>r</mi><mi>r</mi><mi>e</mi><mi>n</mi><mi>t</mi><mo>&nbsp;</mo><mi>a</mi><mi>g</mi><mi>e</mi><mi>s</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>A</mi><mi>f</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mi>y</mi><mi>e</mi><mi>a</mi><mi>r</mi><mi>s</mi><mo>,</mo><mo>&nbsp;</mo><mi>a</mi><mi>g</mi><mi>e</mi><mo>&nbsp;</mo><mi>w</mi><mi>i</mi><mi>l</mi><mi>l</mi><mo>&nbsp;</mo><mi>b</mi><mi>e</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mn>8</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>7</mn><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>r</mi><mi>r</mi><mi>e</mi><mi>s</mi><mi>p</mi><mi>o</mi><mi>n</mi><mi>d</mi><mi>s</mi><mo>&nbsp;</mo><mi>t</mi><mi>o</mi><mo>&nbsp;</mo><mn>7</mn><mi>y</mi><mi>r</mi><mi>s</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>T</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>c</mi><mi>u</mi><mi>r</mi><mi>r</mi><mi>e</mi><mi>n</mi><mi>t</mi><mo>&nbsp;</mo><mi>a</mi><mi>g</mi><mi>e</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>7</mn><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>7</mn><mo>&times;</mo><mn>7</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>49</mn><mo>&nbsp;</mo><mi>y</mi><mi>r</mi><mi>s</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>T</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>c</mi><mi>u</mi><mi>r</mi><mi>r</mi><mi>e</mi><mi>n</mi><mi>t</mi><mo>&nbsp;</mo><mi>a</mi><mi>g</mi><mi>e</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&times;</mo><mn>7</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>21</mn><mo>&nbsp;</mo><mi>y</mi><mi>r</mi><mi>s</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>L</mi><mi>e</mi><mi>t</mi><mo>&nbsp;</mo><mi>r</mi><mi>e</mi><mi>q</mi><mi>u</mi><mi>i</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>a</mi><mi>g</mi><mi>e</mi><mo>&nbsp;</mo><mi>b</mi><mi>e</mi><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mi>y</mi><mi>e</mi><mi>a</mi><mi>r</mi><mi>s</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>A</mi><mi>T</mi><mi>Q</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>49</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi></mrow><mrow><mn>21</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>3</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>98</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>63</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>98</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>63</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>35</mn><mo>&nbsp;</mo><mi>y</mi><mi>r</mi><mi>s</mi></math></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>6.(c)</p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2342;&#2367;&#2319;</mi><mo>&nbsp;</mo><mi>&#2327;&#2319;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</mi><mo>&nbsp;</mo><mi>&#2325;&#2379;</mi><mo>&nbsp;</mo><mi>&#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340;</mi><mo>&nbsp;</mo><mi>&#2325;&#2352;&#2340;&#2375;</mi><mo>&nbsp;</mo><mi>&#2361;&#2369;&#2319;</mi><mo>,</mo><mo>&nbsp;</mo><mi>&#2361;&#2350;&#2366;&#2352;&#2375;</mi><mo>&nbsp;</mo><mi>&#2346;&#2366;&#2360;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mo>:</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>B</mi><mspace linebreak=\"newline\"></mspace><mi>&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</mi><mo>&nbsp;</mo><mi>&#2310;&#2351;&#2369;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>7</mn><mo>&nbsp;</mo><mi>&#2357;&#2352;&#2381;&#2359;</mi><mo>&nbsp;</mo><mi>&#2348;&#2366;&#2342;</mi><mo>&nbsp;</mo><mi>&#2310;&#2351;&#2369;</mi><mo>&nbsp;</mo><mi>&#2361;&#2379;&#2327;&#2368;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mn>8</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>7</mn><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mi>&#2311;&#2325;&#2366;&#2312;</mi><mo>,</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mi>&#2357;&#2352;&#2381;&#2359;</mi><mo>&nbsp;</mo><mi>&#2360;&#2375;</mi><mo>&nbsp;</mo><mi>&#2350;&#2375;&#2354;</mi><mo>&nbsp;</mo><mi>&#2326;&#2366;&#2340;&#2368;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>A</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</mi><mo>&nbsp;</mo><mi>&#2310;&#2351;&#2369;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>7</mn><mi>&#2311;&#2325;&#2366;&#2312;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>7</mn><mo>&times;</mo><mn>7</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>49</mn><mo>&nbsp;</mo><mi>&#2357;&#2352;&#2381;&#2359;</mi><mspace linebreak=\"newline\"></mspace><mi>B</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</mi><mo>&nbsp;</mo><mi>&#2310;&#2351;&#2369;</mi><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mi>&#2311;&#2325;&#2366;&#2312;</mi><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&times;</mo><mn>7</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>21</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2357;&#2352;&#2381;&#2359;</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>&#2350;&#2366;&#2344;&#2366;</mi><mo>&nbsp;</mo><mi>&#2309;&#2346;&#2375;&#2325;&#2381;&#2359;&#2367;&#2340;</mi><mo>&nbsp;</mo><mi>&#2310;&#2351;&#2369;</mi><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mi>&#2357;&#2352;&#2381;&#2359;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>49</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi></mrow><mrow><mn>21</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>3</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>98</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>63</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>98</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>63</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>35</mn><mo>&nbsp;</mo><mi>&#2357;&#2352;&#2381;&#2359;</mi></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">If m : n = 5 : 4, then what is the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>(</mo><msup><mi>m</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>n</mi><mn>2</mn></msup><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>m</mi><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi>n</mi><mn>2</mn></msup><mo>)</mo><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp;?</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> m : n = 5 : 4, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>(</mo><msup><mi>m</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mi>n</mi><mn>2</mn></msup><mo>)</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>m</mi><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mi>n</mi><mn>2</mn></msup><mo>)</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>16 : 9</p>\n", "<p>25 : 9</p>\n", 
                                "<p>32: 5</p>\n", "<p>41 : 9</p>\n"],
                    options_hi: ["<p>16 : 9</p>\n", "<p>25 : 9</p>\n",
                                "<p>32: 5</p>\n", "<p>41 : 9</p>\n"],
                    solution_en: "<p>7.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mi>m</mi></mrow><mrow><mo>&nbsp;</mo><mi>n</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>5</mn></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mi>g</mi><mi>i</mi><mi>v</mi><mi>e</mi><mi>n</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>A</mi><mi>f</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mi>s</mi><mi>q</mi><mi>u</mi><mi>a</mi><mi>r</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>b</mi><mi>o</mi><mi>t</mi><mi>h</mi><mo>&nbsp;</mo><mi>s</mi><mi>i</mi><mi>d</mi><mi>e</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>&nbsp;</mo><mi>m</mi><mo>&sup2;</mo></mrow><mrow><mi>n</mi><mo>&sup2;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>25</mn></mrow><mn>16</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>B</mi><mi>y</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>m</mi><mi>p</mi><mi>o</mi><mi>n</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>o</mi><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>d</mi><mi>i</mi><mi>v</mi><mi>i</mi><mi>d</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>o</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mrow><mi>m</mi><mo>&sup2;</mo><mo>+</mo><mi>n</mi><mo>&sup2;</mo></mrow><mrow><mi>m</mi><mo>&sup2;</mo><mo>-</mo><mo>&nbsp;</mo><mi>n</mi><mo>&sup2;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>25</mn><mo>+</mo><mn>16</mn></mrow><mrow><mo>&nbsp;</mo><mn>25</mn><mo>-</mo><mn>16</mn></mrow></mfrac><mo>=</mo><mfrac><mn>41</mn><mrow><mo>&nbsp;</mo><mn>9</mn></mrow></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>R</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>41</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>9</mn></math></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>7.(d)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mi>m</mi></mrow><mrow><mo>&nbsp;</mo><mi>n</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>5</mn></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mi>&#2342;&#2367;&#2351;&#2366;</mi><mo>&nbsp;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>&#2342;&#2379;&#2344;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2357;&#2352;&#2381;&#2327;</mi><mo>&nbsp;</mo><mi>&#2325;&#2352;&#2344;&#2375;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2348;&#2366;&#2342;</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>&nbsp;</mo><mi>m</mi><mo>&sup2;</mo></mrow><mrow><mi>n</mi><mo>&sup2;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>25</mn></mrow><mn>16</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2351;&#2379;&#2327;&#2366;&#2344;&#2381;&#2340;&#2352;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;</mi><mo>&nbsp;</mo><mi>&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mrow><mi>m</mi><mo>&sup2;</mo><mo>+</mo><mi>n</mi><mo>&sup2;</mo></mrow><mrow><mi>m</mi><mo>&sup2;</mo><mo>-</mo><mo>&nbsp;</mo><mi>n</mi><mo>&sup2;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>25</mn><mo>+</mo><mn>16</mn></mrow><mrow><mo>&nbsp;</mo><mn>25</mn><mo>-</mo><mn>16</mn></mrow></mfrac><mo>=</mo><mfrac><mn>41</mn><mrow><mo>&nbsp;</mo><mn>9</mn></mrow></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>41</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>9</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> A sum of money is distributed among A, B and C in the ratio 8 : 4 : 3. If A divides his part between D and E in the ratio 9 : 7, then what is the ratio of the share of C to that of D?</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> A, B </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> 8:4:3 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2340;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> E </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> 9:7 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>4 : 5</p>\n", "<p>3 : 4</p>\n", 
                                "<p>2 : 3</p>\n", "<p>6 : 7</p>\n"],
                    options_hi: ["<p>4 : 5</p>\n", "<p>3 : 4</p>\n",
                                "<p>2 : 3</p>\n", "<p>6 : 7</p>\n"],
                    solution_en: "<p>8.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>16</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>6</mn></math></span><span style=\"font-family: Cambria Math;\">&nbsp;&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A divides his parts </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674884339/word/media/image1.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Therefore, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C : D = 6 : 9 = 2 : 3 </span></p>\n",
                    solution_hi: "<p>8.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> A : B : C</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 8 : 4 : 3 </span><span style=\"font-family: Cambria Math;\"> ( &times; 2)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr;16 : 8 : 6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2306;&#2335;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1674884339/word/media/image1.png\" width=\"300\" height=\"110\"></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C : D = 6 : 9 = 2 : 3</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">The average age of 3 girls is 24 years and their ages are in the proportion of 7 : 8 : 9. What is the age of the eldest girl?</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> 24 </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> 7: 8: 9 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>24 years</p>\n", "<p>25 years</p>\n", 
                                "<p>21 years</p>\n", "<p>27 years</p>\n"],
                    options_hi: ["<p>24 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p>25 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n",
                                "<p>21 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p>27 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> Sum of the age of the 3 girls = 24 &times; 3 = 72</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Ration between them = 7 : 8 : 9</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let common factor = k</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7k + 8k + 9k = 72</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">24k = 72 so, k = 3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Eldest girl&rsquo;s age = 9k &rArr; </span><span style=\"font-family: Cambria Math;\">9 &times; 3 = 27 years</span></p>\n",
                    solution_hi: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> = 24 &times; 3 = 72</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 7 : 8 : 9</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2349;&#2351;&#2344;&#2367;&#2359;&#2381;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2369;&#2339;&#2344;&#2326;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\">r = k</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> &rArr;7k + 8k + 9k = 72</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> &rArr;24k = 72 so, k = 3</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> = 9k&nbsp; &rArr;</span><span style=\"font-family: Cambria Math;\">9 &times; 3 = 27 </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> If 0.04 : 0.06 :: 0.06 : A, then what is the value of A ?</span></p>\n",
                    question_hi: "<p>10. <span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 0.04 : 0.06 :: 0.06 : A, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>0.05</p>\n", "<p>0.08</p>\n", 
                                "<p>0.06</p>\n", "<p>0.09</p>\n"],
                    options_hi: ["<p>0.05</p>\n", "<p>0.08</p>\n",
                                "<p>0.06</p>\n", "<p>0.09</p>\n"],
                    solution_en: "<p>10.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>04</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>06</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>0</mn><mo>.</mo><mn>06</mn></mrow><mi>A</mi></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>0</mn><mo>.</mo><mn>06</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><mn>06</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>04</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>0</mn><mo>.</mo><mn>0036</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>04</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><mn>09</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>10.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>04</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>06</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>0</mn><mo>.</mo><mn>06</mn></mrow><mi>A</mi></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>0</mn><mo>.</mo><mn>06</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><mn>06</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>04</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>0</mn><mo>.</mo><mn>0036</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>04</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><mn>09</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> The ratio of the numbers of boys and girls in a school is 5 : 8. If there are 260 students in the school, then what is the difference between the numbers of girls and boys in the school?</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 5 : 8 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 260 </span><span style=\"font-family: Kokila;\">&#2331;&#2366;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>50</p>\n", "<p>80</p>\n", 
                                "<p>40</p>\n", "<p>60</p>\n"],
                    options_hi: ["<p>50</p>\n", "<p>80</p>\n",
                                "<p>40</p>\n", "<p>60</p>\n"],
                    solution_en: "<p>11.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mi>T</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>s</mi><mi>t</mi><mi>u</mi><mi>d</mi><mi>e</mi><mi>n</mi><mi>t</mi><mi>s</mi><mo>&nbsp;</mo><mi>i</mi><mi>n</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>s</mi><mi>c</mi><mi>h</mi><mi>o</mi><mi>o</mi><mi>l</mi><mo>&nbsp;</mo><mi>i</mi><mi>s</mi><mo>,</mo><mo>&nbsp;</mo><mn>260</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>B</mi><mi>o</mi><mi>y</mi><mi>s</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>G</mi><mi>i</mi><mi>r</mi><mi>l</mi><mi>s</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>T</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mi>f</mi><mi>o</mi><mi>r</mi><mi>e</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>5</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>8</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>260</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>13</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>260</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>X</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>T</mi><mi>h</mi><mi>e</mi><mi>n</mi><mo>,</mo><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>d</mi><mi>i</mi><mi>f</mi><mi>f</mi><mi>e</mi><mi>r</mi><mi>e</mi><mi>n</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo><mi>b</mi><mi>e</mi><mi>t</mi><mi>w</mi><mi>e</mi><mi>e</mi><mi>n</mi><mo>&nbsp;</mo><mi>g</mi><mi>i</mi><mi>r</mi><mi>l</mi><mi>s</mi><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>b</mi><mi>o</mi><mi>y</mi><mi>s</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mn>8</mn><mi>x</mi><mo>-</mo><mo>&nbsp;</mo><mn>5</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn></math></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>11.(d)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2357;&#2367;&#2342;&#2381;&#2351;&#2366;&#2354;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2375;&#2306;</mi><mo>&nbsp;</mo><mi>&#2357;&#2367;&#2342;&#2381;&#2351;&#2366;&#2352;&#2381;&#2341;&#2367;&#2351;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;</mi><mo>&nbsp;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&nbsp;</mo><mn>260</mn><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2354;&#2396;&#2325;&#2375;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mi>&#2354;&#2396;&#2325;&#2367;&#2351;&#2366;&#2306;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>5</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>8</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>260</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>13</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>260</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>X</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2347;&#2367;&#2352;</mi><mo>,</mo><mo>&nbsp;</mo><mi>&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mi>&#2354;&#2337;&#2364;&#2325;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2348;&#2368;&#2330;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2309;&#2306;&#2340;&#2352;</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mn>8</mn><mi>x</mi><mo>-</mo><mo>&nbsp;</mo><mn>5</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">A number when subtracted from 3 times the second number, gives a result equal to </span><span style=\"font-family: Cambria Math;\">of the first number. What is the ratio of the first number to the second number?</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Kokila;\">&#2327;&#2369;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2335;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>11 : 6<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">12 : 7</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">9 : 5</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">10 : 7</span></p>\n"],
                    options_hi: ["<p>11 : 6</p>\n", "<p><span style=\"font-family: Cambria Math;\"> 12 : 7</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">9 : 5</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> 10 : 7</span></p>\n"],
                    solution_en: "<p>12.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>l</mi><mi>e</mi><mi>t</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>f</mi><mi>i</mi><mi>r</mi><mi>s</mi><mi>t</mi><mo>&nbsp;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>=</mo><mo>&nbsp;</mo><mi>a</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>A</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>o</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>b</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>A</mi><mo>/</mo><mi>c</mi><mo>&nbsp;</mo><mi>t</mi><mi>o</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>q</mi><mi>u</mi><mi>e</mi><mi>s</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>&nbsp;</mo><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mi>b</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mi>a</mi></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mn>3</mn><mi>a</mi></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mi>a</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mn>7</mn><mi>a</mi></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mi>a</mi><mo>&nbsp;</mo></mrow><mi>b</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>12</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mi>f</mi><mi>o</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mi>r</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo></math></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>12.(b)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2366;&#2344;&#2366;</mi><mo>&nbsp;</mo><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&nbsp;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>=</mo><mo>&nbsp;</mo><mi>a</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mi>&#2342;&#2370;&#2360;&#2352;&#2368;</mi><mo>&nbsp;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>b</mi><mspace linebreak=\"newline\"></mspace><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2354;&#2367;&#2319;</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mi>b</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mi>a</mi></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mn>3</mn><mi>a</mi></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mi>a</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mn>7</mn><mi>a</mi></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mi>a</mi><mo>&nbsp;</mo></mrow><mi>b</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>12</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mi>f</mi><mi>o</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mi>r</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> &#8377;4,500 is divided among four friends in the ratio of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;What is the amount received by the person who got the smallest share?</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">&#8377;4,500 </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2360;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2305;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2379;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>&#8377;1,100</p>\n", "<p>&#8377;600</p>\n", 
                                "<p>&#8377;500</p>\n", "<p>&#8377;800</p>\n"],
                    options_hi: ["<p>&#8377;1,100</p>\n", "<p>&#8377;600</p>\n",
                                "<p>&#8377;500</p>\n", "<p>&#8377;800</p>\n"],
                    solution_en: "<p>13.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>r</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mo>&nbsp;</mo><mi>a</mi><mi>m</mi><mi>o</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>f</mi><mi>o</mi><mi>u</mi><mi>r</mi><mo>&nbsp;</mo><mi>f</mi><mi>r</mi><mi>i</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>s</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>L</mi><mi>C</mi><mi>M</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>(</mo><mn>6</mn><mo>,</mo><mo>&nbsp;</mo><mn>4</mn><mo>,</mo><mo>&nbsp;</mo><mn>3</mn><mo>,</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>A</mi><mi>c</mi><mi>t</mi><mi>u</mi><mi>a</mi><mi>l</mi><mo>&nbsp;</mo><mi>r</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mo>&nbsp;</mo><mi>i</mi><mi>s</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>o</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>6</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4500</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mn>15</mn><mi>x</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4500</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>300</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>T</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mi>f</mi><mi>o</mi><mi>r</mi><mi>e</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>H</mi><mi>a</mi><mi>v</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>s</mi><mi>m</mi><mi>a</mi><mi>l</mi><mi>l</mi><mi>e</mi><mi>s</mi><mi>t</mi><mo>&nbsp;</mo><mi>s</mi><mi>h</mi><mi>a</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mi>i</mi><mi>s</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>300</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&#8377;</mo><mo>&nbsp;</mo><mn>600</mn></math></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>13.(b)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2330;&#2366;&#2352;</mi><mo>&nbsp;</mo><mi>&#2342;&#2379;&#2360;&#2381;&#2340;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2348;&#2368;&#2330;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</mi><mspace linebreak=\"newline\"></mspace><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>L</mi><mi>C</mi><mi>M</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>(</mo><mn>6</mn><mo>,</mo><mo>&nbsp;</mo><mn>4</mn><mo>,</mo><mo>&nbsp;</mo><mn>3</mn><mo>,</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><mi>&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>6</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4500</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mn>15</mn><mi>x</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4500</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>300</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>,</mo><mo>&nbsp;</mo><mi>&#2360;&#2348;&#2360;&#2375;</mi><mo>&nbsp;</mo><mi>&#2331;&#2379;&#2335;&#2366;</mi><mo>&nbsp;</mo><mi>&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;</mi><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>300</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&#8377;</mo><mo>&nbsp;</mo><mn>600</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> If a : b : c = 2 : 3 : 7, then what is 2a : 3b : 7c?</span></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> a:b:c=2:3:7, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 2a:3b:7c </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>4 : 9 : 49</p>\n", "<p>5 : 10 : 52</p>\n", 
                                "<p>6 : 10 : 49</p>\n", "<p>6 : 10 : 47</p>\n"],
                    options_hi: ["<p>4 : 9 : 49</p>\n", "<p>5 : 10 : 52</p>\n",
                                "<p>6 : 10 : 49</p>\n", "<p>6 : 10 : 47</p>\n"],
                    solution_en: "<p>14.(a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>A</mi><mi>c</mi><mi>o</mi><mi>r</mi><mi>d</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>t</mi><mi>o</mi><mo>&nbsp;</mo><mi>q</mi><mi>u</mi><mi>e</mi><mi>s</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>c</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>o</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mi>a</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>7</mn><mi>c</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&times;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&times;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>49</mn></math></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>14.(a)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>c</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mi>a</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>7</mn><mi>c</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&times;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&times;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>49</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> The difference of two numbers is 24 while the sum of those two numbers is 120. Which of the options below can be the ratio of those two numbers?</span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> 24 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2348;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> 120 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>3 : 5</p>\n", "<p>3 : 2</p>\n", 
                                "<p>4 : 5</p>\n", "<p>5 : 8</p>\n"],
                    options_hi: ["<p>3 : 5</p>\n", "<p>3 : 2</p>\n",
                                "<p>4 : 5</p>\n", "<p>5 : 8</p>\n"],
                    solution_en: "<p>15.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mi>L</mi><mi>e</mi><mi>t</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>s</mi><mo>&nbsp;</mo><mi>a</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>b</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>A</mi><mi>c</mi><mi>c</mi><mi>o</mi><mi>r</mi><mi>d</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>t</mi><mi>o</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>q</mi><mi>u</mi><mi>e</mi><mi>s</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>120</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>24</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mi>a</mi><mo>+</mo><mo>&nbsp;</mo><mi>b</mi></mrow><mrow><mo>&nbsp;</mo><mi>a</mi><mo>-</mo><mi>b</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>120</mn></mrow><mn>24</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>5</mn></mrow><mn>1</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>B</mi><mi>y</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>m</mi><mi>p</mi><mi>o</mi><mi>n</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>o</mi><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>d</mi><mi>i</mi><mi>v</mi><mi>i</mi><mi>d</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>o</mi><mo>&rsquo;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mi>a</mi></mrow><mi>b</mi></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>5</mn><mo>+</mo><mn>1</mn></mrow><mrow><mn>5</mn><mo>-</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>6</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mi>s</mi><mi>o</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn></math></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>15.(b)</p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2350;&#2366;&#2344;</mi><mo>&nbsp;</mo><mi>&#2354;&#2368;&#2332;&#2367;&#2319;</mi><mo>&nbsp;</mo><mi>&#2325;&#2367;</mi><mo>&nbsp;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</mi><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;&#2306;</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>120</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>24</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mi>a</mi><mo>+</mo><mo>&nbsp;</mo><mi>b</mi></mrow><mrow><mo>&nbsp;</mo><mi>a</mi><mo>-</mo><mi>b</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>120</mn></mrow><mn>24</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>5</mn></mrow><mn>1</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>B</mi><mi>y</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>m</mi><mi>p</mi><mi>o</mi><mi>n</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>o</mi><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>d</mi><mi>i</mi><mi>v</mi><mi>i</mi><mi>d</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>o</mi><mo>&rsquo;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mi>a</mi></mrow><mi>b</mi></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>5</mn><mo>+</mo><mn>1</mn></mrow><mrow><mn>5</mn><mo>-</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>6</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>,</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>a</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>