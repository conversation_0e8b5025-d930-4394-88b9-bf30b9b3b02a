<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 10</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">10</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 8
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 9,
                end: 9
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A vendor claims to sell wheat at a loss of 25%. But he cheats by using weights that weigh 35% less than what is mentioned on them. What is his profit percentage (rounded off to 2 decimal places)?</p>",
                    question_hi: "<p>1. एक विक्रेता 25% हानि पर गेहूँ बेचने का दावा करता है। लेकिन वह ऐसे बाटों का उपयोग करके धोखा देता है जिनका वजन उन पर अंकित वजन से 35% कम है। उसका लाभ प्रतिशत (दशमलव के दो स्थानों तक पूर्णांकित) कितना है?</p>",
                    options_en: ["<p>20.31</p>", "<p>14.41</p>", 
                                "<p>19.85</p>", "<p>15.38</p>"],
                    options_hi: ["<p>20.31</p>", "<p>14.41</p>",
                                "<p>19.85</p>", "<p>15.38</p>"],
                    solution_en: "<p>1.(d)<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; -&nbsp; &nbsp; CP&nbsp; :&nbsp; SP <br>25% loss&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; -&nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp;:&nbsp; &nbsp;3<br>Due to weight&nbsp; &nbsp; &nbsp;-&nbsp; &nbsp; 13&nbsp; &nbsp;: 20<br>------------------------------------------<br>Overall&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;-&nbsp; &nbsp; &nbsp;52&nbsp; &nbsp;:&nbsp; 60<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>52</mn></mrow></mfrac></math> &times; 100 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>200</mn><mo>&#160;</mo></mrow><mn>13</mn></mfrac></math> = 15.38%</p>",
                    solution_hi: "<p>1.(d)<br>अनुपात&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; -&nbsp; CP&nbsp; :&nbsp; &nbsp;SP <br>25% हानि&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; -&nbsp; &nbsp; 4&nbsp; &nbsp;:&nbsp; &nbsp;3&nbsp; &nbsp;<br>वजन के कारण&nbsp; &nbsp; - 13&nbsp; &nbsp; : 20<br>------------------------------------------<br>कुल&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; -&nbsp; &nbsp;52&nbsp; &nbsp;: 60<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>52</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>13</mn></mfrac></math> = 15.38%</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The selling price of 50 books is equal to the cost price of 25 books. Find the loss or gain percentage.</p>",
                    question_hi: "<p>2. 50 पुस्तकों का विक्रय मूल्य, 25 पुस्तकों के क्रय मूल्य के बराबर है। हानि या लाभ प्रतिशत ज्ञात कीजिए |</p>",
                    options_en: ["<p>50 % gain</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> % loss</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> % gain</p>", "<p>50 % loss</p>"],
                    options_hi: ["<p>50 % लाभ</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> %हानि</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> % लाभ</p>", "<p>50 % हानि</p>"],
                    solution_en: "<p>2.(d) SP &times; 50 = CP&times; 25 <br>&rArr; <math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>50</mn></mfrac></math><br>Loss percentage = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>25</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 100 = 50%</p>",
                    solution_hi: "<p>2.(d) विक्रय मूल्य &times; 50 = क्रय मूल्य &times; 25 <br>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>50</mn></mfrac></math><br>हानि प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>25</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 100 = 50%</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Find the gain percentage, given that Bushra sold her scooter for <math display=\"inline\"><mi>&#8377;</mi></math> 59112 gaining <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>th of the selling price.</p>",
                    question_hi: "<p>3. बुशरा अपना स्कूटर ₹59112 में बेचकर विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> वाँ भाग के बराबर लाभ प्राप्त करती है। उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>10%</p>", "<p>35%</p>", 
                                "<p>20%</p>", "<p>5%</p>"],
                    options_hi: ["<p>10%</p>", "<p>35%</p>",
                                "<p>20%</p>", "<p>5%</p>"],
                    solution_en: "<p>3.(c) Given , <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>th of the selling price<br>Let selling price = 6 unit <br>Gain = 1 unit <br>Cost price = 5 unit <br>Gain percentage = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; 100 = 20%</p>",
                    solution_hi: "<p>3.(c) दिया है , विक्रय मूल्य का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> वां<br>माना विक्रय मूल्य = 6 इकाई <br>लाभ = 1 इकाई <br>लागत मूल्य = 5 इकाई <br>लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; 100 = 20%</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Pramod sold 171 chairs and had a gain equal to the selling price of 57 chairs. What is his profit<br>percentage?</p>",
                    question_hi: "<p>4. प्रमोद ने 171 कुर्सियाँ बेचीं और उसे 57 \'कुर्सियों के विक्रय मूल्य के बराबर लाभ प्राप्त हुआ। उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>55%</p>", "<p>45%</p>", 
                                "<p>60%</p>", "<p>50%</p>"],
                    options_hi: ["<p>55%</p>", "<p>45%</p>",
                                "<p>60%</p>", "<p>50%</p>"],
                    solution_en: "<p>4.(d)<br>S.P. = 171 chair<br>Gain = 57 chair<br>C.P. = 171 - 57 = 114 chairs<br>Profit % = <math display=\"inline\"><mfrac><mrow><mi>g</mi><mi>a</mi><mi>i</mi><mi>n</mi></mrow><mrow><mi>C</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow></mfrac></math> &times; 100 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>57</mn><mo>&#160;</mo></mrow><mn>114</mn></mfrac></math> &times; 100 = 50%</p>",
                    solution_hi: "<p>4.(d)<br>विक्रय मूल्य = 171 कुर्सियाँ <br>लाभ = 57 कुर्सियाँ <br>क्रय मूल्य = 171 - 57 = 114 कुर्सियाँ <br>लाभ % =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2354;&#2366;&#2349;</mi><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math>&nbsp;&times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>57</mn><mn>114</mn></mfrac></math> &times; 100 = 50%</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Find the gain percentage, given that Anubha sold her scooter for ₹97830 gaining <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>th of the selling price.</p>",
                    question_hi: "<p>5. अनुभा अपना स्कूटर ₹97830 में बेचकर विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>भाग केबराबर लाभ प्राप्त करती है। उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>40%</p>", "<p>35%</p>", 
                                "<p>30%</p>", "<p>25%</p>"],
                    options_hi: ["<p>40%</p>", "<p>35%</p>",
                                "<p>30%</p>", "<p>25%</p>"],
                    solution_en: "<p>5.(d) S.P. = ₹97830<br>Gain = S.P. <math display=\"inline\"><mo>&#215;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 97830 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 19,566<br>C.P. = S.P. - Gain = 97830 - 19566 = 78264<br>Gain percentage = <math display=\"inline\"><mfrac><mrow><mi>G</mi><mi>a</mi><mi>i</mi><mi>n</mi></mrow><mrow><mi>C</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19566</mn><mn>78264</mn></mfrac></math> &times; 100 = 25 %</p>",
                    solution_hi: "<p>5.(d) वि. मू. = ₹97830<br>लाभ = वि. मू. <math display=\"inline\"><mo>&#215;</mo></math> -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 97830 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 19,566<br>क्र.मू .= वि. मू. - लाभ = 97830 - 19566 = 78264<br>लाभ प्रतिशत =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2354;&#2366;&#2349;</mi><mrow><mi>&#2325;&#2381;&#2352;</mi><mo>.</mo><mi>&#2350;&#2370;</mi></mrow></mfrac></math>&nbsp;&times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19566</mn><mn>78264</mn></mfrac></math> &times; 100 = 25 %</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. By selling an article at <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math> of its actual selling price, Sharad incurs a loss of 12%. If he sells it at 90% of its actual selling price, then the profit percentage is:</p>",
                    question_hi: "<p>6. किसी वस्तु को इसके मूल विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math> पर बेचने पर शरद को 12% की हानि होती है। यदि वह इसे मूल विक्रय मूल्य के 90% पर बेचता है, तो लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>85.9%</p>", "<p>85.4%</p>", 
                                "<p>87.9%</p>", "<p>84.8%</p>"],
                    options_hi: ["<p>85.9%</p>", "<p>85.4%</p>",
                                "<p>87.9%</p>", "<p>84.8%</p>"],
                    solution_en: "<p>6.(d) Let the actual selling price of the article be <math display=\"inline\"><mi>x</mi></math>.<br>If Sharad sells it at <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math> of its actual selling price, the S.P. = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>14</mn></mfrac></math>x <br>At this selling price, Sharad incurs a loss of 12%.<br>C.P. = S.P. &times; (<math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>L</mi><mi>o</mi><mi>s</mi><mi>s</mi><mi>&#160;</mi><mi>P</mi><mi>e</mi><mi>r</mi><mi>c</mi><mi>e</mi><mi>n</mi><mi>t</mi><mi>a</mi><mi>g</mi><mi>e</mi></mrow></mfrac></math>) <br>C.P. = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn></mrow></mfrac><mi>x</mi></math> &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mrow><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>12</mn></mrow></mfrac></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac><mi>x</mi></math> &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>88</mn></mfrac></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>75</mn><mi>x</mi><mo>&#8203;</mo></mrow><mn>154</mn></mfrac></math><br>If Sharad sells the article at 90% of its actual selling price, the new S.P. = <math display=\"inline\"><mn>0</mn><mo>.</mo><mn>9</mn></math>x <br>Profit Percentage = <math display=\"inline\"><mfrac><mrow><mi>n</mi><mi>e</mi><mi>w</mi><mi>&#160;</mi><mi>S</mi><mo>.</mo><mi>P</mi><mo>.</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>C</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow><mrow><mi>C</mi><mo>.</mo><mi>P</mi><mo>.</mo><mi>&#160;</mi></mrow></mfrac></math> &times; 100 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>9</mn><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>75</mn><mi>x</mi><mo>&#8203;</mo></mrow><mn>154</mn></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>75</mn><mi>x</mi><mo>&#8203;</mo><mo>&#160;</mo></mrow><mn>154</mn></mfrac></mstyle></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>63</mn><mo>.</mo><mn>6</mn><mi>x</mi><mo>&#160;</mo></mrow><mrow><mn>75</mn><mi>x</mi></mrow></mfrac></math>&times; 100<br>Profit Percentage = <math display=\"inline\"><mfrac><mrow><mn>63600</mn></mrow><mrow><mn>750</mn></mrow></mfrac></math> = 84.8%</p>",
                    solution_hi: "<p>6.(d) मान लीजिए, वस्तु का वास्तविक विक्रय मूल्य = <math display=\"inline\"><mi>x</mi></math> <br>शरद वस्तु को उसके वास्तविक विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math>​ पर बेचता है, तो विक्रय मूल्य = 614x <br>इस कीमत पर बेचने पर शरद को 12% का नुकसान होता है<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2325;&#2381;&#2352;</mi><mo>.</mo><mi>&#2350;&#2370;</mi><mo>.</mo></math>&nbsp;= वि. मू. &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mrow><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>&#2361;&#2366;&#2344;&#2367;</mi><mo>&#160;</mo><mi>&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</mi></mrow></mfrac></math> ) <br>क्र.मू.=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>14</mn></mfrac><mi>x</mi></math> &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mrow><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>12</mn></mrow></mfrac></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac><mi>x</mi></math> &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>88</mn></mfrac></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>75</mn><mi>x</mi><mo>&#8203;</mo></mrow><mn>154</mn></mfrac></math><br>यदि शरद वस्तु को उसके वास्तविक विक्रय मूल्य के 90% पर बेचता है, तो नया विक्रय मूल्य = <math display=\"inline\"><mn>0</mn><mo>.</mo><mn>9</mn></math>x <br>लाभ प्रतिशत =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2344;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math>&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>9</mn><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>75</mn><mi>x</mi><mo>&#8203;</mo></mrow><mn>154</mn></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>75</mn><mi>x</mi><mo>&#8203;</mo><mo>&#160;</mo></mrow><mn>154</mn></mfrac></mstyle></mfrac></math> &times; 100 =&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>63</mn><mo>.</mo><mn>6</mn><mi>x</mi><mo>&#160;</mo></mrow><mrow><mn>75</mn><mi>x</mi></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math><br>लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>63600</mn></mrow><mrow><mn>750</mn></mrow></mfrac></math> = 84.8%</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. A dishonest vendor claims that he is selling goods at their cost price. But he is weighing 918 grams for 1000 grams. What is his profit percentage (rounded off to 2 decimal places)?</p>",
                    question_hi: "<p>7. एक बेईमान विक्रेता का दावा है कि वह सामानों को उनके क्रय मूल्य पर बेच रहा है। लेकिन वह 1000 ग्राम की जगह 918 ग्राम वजन तौल रहा है। उसका लाभ प्रतिशत (दो दशमलव स्थानों तक पूर्णांकित) क्या है?</p>",
                    options_en: ["<p>8.93</p>", "<p>5</p>", 
                                "<p>5.68</p>", "<p>14.5</p>"],
                    options_hi: ["<p>8.93</p>", "<p>5</p>",
                                "<p>5.68</p>", "<p>14.5</p>"],
                    solution_en: "<p>7.(a) Difference = 1000 &minus; 918 = 82 grams, Actual weight = 918 grams.<br>Profit % = (<math display=\"inline\"><mfrac><mrow><mi>D</mi><mi>i</mi><mi>f</mi><mi>f</mi><mi>e</mi><mi>r</mi><mi>e</mi><mi>n</mi><mi>c</mi><mi>e</mi><mi>&#8203;</mi></mrow><mrow><mi>a</mi><mi>c</mi><mi>t</mi><mi>u</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>w</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi></mrow></mfrac></math>) &times; 100<br>Profit % = (<math display=\"inline\"><mfrac><mrow><mn>82</mn></mrow><mrow><mn>918</mn><mi>&#8203;</mi></mrow></mfrac></math>) &times; 100 = 8.93%</p>",
                    solution_hi: "<p>7.(a) अंतर = 1000 &minus; 918 = 82 ग्राम <br>वास्तविक वजन = 918 <br>लाभ % = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2309;&#2306;&#2340;&#2352;</mi><mrow><mi>&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</mi><mo>&#160;</mo><mi>&#2357;&#2332;&#2344;</mi><mo>&#8203;</mo></mrow></mfrac></math>) &times; 100<br>लाभ % = (<math display=\"inline\"><mfrac><mrow><mn>82</mn></mrow><mrow><mn>918</mn><mi>&#8203;</mi></mrow></mfrac></math>) &times; 100 = 8.93%</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. By selling an article at <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> of its actual selling price, Nirbhay incurs a loss of 17%. If he sells it at 88% of its actual selling price, then the profit percentage is:</p>",
                    question_hi: "<p>8. किसी वस्तु को इसके मूल विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> पर बेचने पर निर्भय को 17% की हानि होती है। यदि वह इसे मूल विक्रय मूल्य के 88% पर बेचता है, तो लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>81.3%</p>", "<p>84.4%</p>", 
                                "<p>82.6%</p>", "<p>83.4%</p>"],
                    options_hi: ["<p>81.3%</p>", "<p>84.4%</p>",
                                "<p>82.6%</p>", "<p>83.4%</p>"],
                    solution_en: "<p>8.(c) According to question,<br>CP of article = 4 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>83</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mn>83</mn></mfrac></math><br>Actual selling price = 10<br>88 % of its actual selling price then,<br>SP = 10 &times; <math display=\"inline\"><mfrac><mrow><mn>88</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 8.8<br>Required profit % = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>8</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mn>400</mn></mrow><mrow><mn>83</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>400</mn></mrow><mrow><mn>83</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>730</mn><mo>.</mo><mn>4</mn><mo>&#160;</mo><mo>-</mo><mn>400</mn></mrow><mn>400</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>330</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>4</mn><mo>&#160;</mo></mrow></mfrac></math>= 82.6%</p>",
                    solution_hi: "<p>8.(c) प्रश्न के अनुसार,<br>वस्तु का क्रय मूल्य = 4 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>83</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mn>83</mn></mfrac></math><br>वास्तविक बिक्री मूल्य = 10<br>इसकी वास्तविक विक्रय मूल्य का 88 %,तो<br>विक्रय मूल्य = 10 &times; <math display=\"inline\"><mfrac><mrow><mn>88</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 8.8<br>आवश्यक लाभ % = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>8</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mn>400</mn></mrow><mrow><mn>83</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>400</mn></mrow><mrow><mn>83</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>730</mn><mo>.</mo><mn>4</mn><mo>&#160;</mo><mo>-</mo><mn>400</mn><mo>&#160;</mo></mrow><mn>400</mn></mfrac></math>&times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>330</mn><mo>.</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> = 82.6%</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. 400 guavas were bought at ₹1420 per hundred and were sold at a profit of ₹820. Find the selling price (in ₹) per dozen of guavas.</p>",
                    question_hi: "<p>9. ₹1420 प्रति सैकड़े की दर से 400 अमरूद खरीदे गए और ₹820 के लाभ पर बेचे गए। प्रति दर्जन अमरूदों का विक्रय मूल्य (₹ में) ज्ञात करें।</p>",
                    options_en: ["<p>205</p>", "<p>210</p>", 
                                "<p>195</p>", "<p>185</p>"],
                    options_hi: ["<p>205</p>", "<p>210</p>",
                                "<p>195</p>", "<p>185</p>"],
                    solution_en: "<p>9.(c) Cost of 100 guava = ₹ 1420<br>Cost of 400 guava = <math display=\"inline\"><mfrac><mrow><mn>1420</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 400 = ₹ 5680 <br>SP of 400 guava = 5680 + 820 = ₹ 6500<br>Now,<br>SP of 1 dozen guava = <math display=\"inline\"><mfrac><mrow><mn>6500</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math> &times; 12 = ₹ 195</p>",
                    solution_hi: "<p>9.(c) 100 अमरूद की लागत = ₹ 1420<br>400 अमरूद की लागत = <math display=\"inline\"><mfrac><mrow><mn>1420</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 400 = ₹ 5680 <br>400 अमरूद का विक्रय मूल्य = 5680 + 820 = ₹ 6500<br>अब,<br>1 दर्जन अमरूद का विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>6500</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math> &times; 12 = ₹ 195</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "<p>10. A dishonest vendor claims that he is selling goods at their cost price. But he is weighing 812 grams for 1000 grams. What is his profit percentage (rounded off to 2 decimal places)?</p>",
                    question_hi: "<p>10. एक बेईमान विक्रेता का दावा है कि वह सामानों को उनके क्रय मूल्य पर बेच रहा है। लेकिन वह 1000 ग्राम की जगह 812 ग्राम वजन तौल रहा है। उसका लाभ प्रतिशत (दो दशमलव स्थानों तक पूर्णांकित) क्या है?</p>",
                    options_en: ["<p>20.91</p>", "<p>24.85</p>", 
                                "<p>26.29</p>", "<p>23.15</p>"],
                    options_hi: ["<p>20.91</p>", "<p>24.85</p>",
                                "<p>26.29</p>", "<p>23.15</p>"],
                    solution_en: "<p>10.(d) <br>Ratio&nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math> CP : SP<br>Price&nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math> 100 : 100<br>Weight <math display=\"inline\"><mo>&#8594;</mo></math> 812 : 1000<br>---------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;203 : 250<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>250</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>203</mn></mrow><mrow><mn>203</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4700</mn><mn>203</mn></mfrac></math> = 23.15</p>",
                    solution_hi: "<p>10.(d) <br>अनुपात&nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math> क्रय मूल्य&nbsp; :&nbsp; विक्रय मूल्य<br>कीमत&nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp;: 100<br>भार&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 812&nbsp; &nbsp; : 1000<br>-----------------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;203&nbsp; &nbsp;: 250<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>250</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>203</mn></mrow><mrow><mn>203</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4700</mn><mn>203</mn></mfrac></math> = 23.15</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>