<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 27</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">27</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 25
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 26,
                end: 26
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "1. Pick the odd pair of words from the below options",
                    question_hi: "1. नीचे दिए गए विकल्पों में से विषम शब्द युग्म चुनिए?",
                    options_en: [" Steadfast : irresolute ", " Monotonous  : exciting ", 
                                " Stringent  : ﬂexible ", " Docile : submissive "],
                    options_hi: [" स्थिर : अनिश्चित", " नीरस : उत्तेजक",
                                " कठोर : लचीला", " विनम्र : आज्ञाकारी"],
                    solution_en: "1.(d)<br />In every pair of words the 2nd word is opposite of the first.",
                    solution_hi: "1.(d)<br />शब्दों के प्रत्येक जोड़े में दूसरा शब्द पहले के विपरीत है.।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "2. Read the following information carefully and answer the questions that follow:<br />A Word-number arrangement machine, when given input as set of words and numbers, rearranges them following a particular rule and generates a stepwise output until the rearrangement is complete following that rule.<br />Following is an illustration of the input and steps of rearrangement until the last step.<br />Input : Tokyo 7 Rio @ S Salva We<br />Step 1: Tokyo 7 Rio @ We 5 Salva<br />Step 2: Tokyo 7 Rio We @ 5 Salva<br />Step 3: 7 Rio We @ 5 Salva Tokyo<br />Step 4: Rio 7 We @ 5 Salva Tokyo<br />In addition, step 4 is the last step <br />For the input “When % SSR 2 meet shall you\", What is Step 2\'?",
                    question_hi: "2. निम्नलिखित जानकारी को ध्यान से पढ़ें और नीचे दिए गए प्रश्नों के उत्तर दीजिये:<br />एक शब्द-संख्या व्यवस्थित करने वाली मशीन जब शब्दों और संख्याओं के समूह के रूप में इनपुट दिया जाता है तो उन्हें एक विशेष नियम का पालन करते हुए पुनर्व्यवस्थित करती है और उस नियम का पालन करने तक पुनर्व्यवस्था पूरी होने तक एक चरणबद्ध आउटपुट उत्पन्न करता है। अंतिम चरण तक इनपुट और पुनर्व्यवस्था के चरणों का एक उदाहरण निम्नलिखित है।<br />इनपुट : Tokyo 7 Rio @ S Salva We<br />चरण 1: Tokyo 7 Rio @ We 5 Salva<br />चरण 2: Tokyo 7 Rio We @ 5 Salva<br />चरण 3: 7 Rio We @ 5 Salva Tokyo<br />चरण 4: Rio 7 We @ 5 Salva Tokyo<br />चरण 4 अंतिम चरण है<br />इनपुट “When % SSR 2 meet shall you\" के लिए चरण 2 क्या होगा ?",
                    options_en: [" SSR 2 When % you meet shall", " SSR When % you 2 meet shall", 
                                " When % SSR 2 you meet shall", " When %SSR you 2 meet shall"],
                    options_hi: [" SSR 2 When % you meet shall", " SSR When % you 2 meet shall",
                                " When % SSR 2 you meet shall", " When %SSR you 2 meet shall"],
                    solution_en: "2.(d)<br />Input: When % SSR 2 meet shall you<br />Step 1: When % SSR 2 you meet shall<br />Step 2: When % SSR you 2 meet shall",
                    solution_hi: "2.(d)<br />इनपुट: When % SSR 2 meet shall you<br />चरण 1: When % SSR 2 you meet shall<br />चरण 2: When % SSR you 2 meet shall",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Two statements are followed by two given conclusions. Choose the correct option considering the statement to be true irrespective of the commonly known facts.<br><strong>Statement 1:</strong> Only cows are mammals.<br><strong>Statement 2: </strong>No scientists are mammals<br><strong>Conclusion 1:</strong> Some cows are scientists.<br><strong>Conclusion 2:</strong> Some scientists are mammals.</p>",
                    question_hi: "<p>3. दो कथनों के बाद दो निष्कर्ष दिए गए हैं। सामान्य ज्ञात तथ्यों पर ध्यान दिए बिना कथन को सत्य मानते हुए सही विकल्प का चयन कीजिये <br><strong>कथन 1: </strong>केवल गाय स्तनधारी हैं।<br><strong>कथन 2:</strong> कोई भी वैज्ञानिक स्तनधारी नहीं है<br><strong>निष्कर्ष 1:</strong> कुछ गाय वैज्ञानिक हैं।<br><strong>निष्कर्ष 2:</strong> कुछ वैज्ञानिक स्तनधारी हैं।</p>",
                    options_en: ["<p>Only conclusion 2 follows</p>", "<p>Both conclusion 1 and conclusion 2 follow</p>", 
                                "<p>Only conclusion 1 follows</p>", "<p>Neither conclusion 1 nor conclusion 2 follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष 2 अनुसरण करता है</p>", "<p>निष्कर्ष 1 और निष्कर्ष 2 दोनों अनुसरण करते हैं</p>",
                                "<p>केवल निष्कर्ष 1 अनुसरण करता है</p>", "<p>न तो निष्कर्ष 1 और न ही निष्कर्ष 2 अनुसरण करते हैं</p>"],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727953281018.png\" alt=\"rId5\" width=\"325\" height=\"114\"></p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727953281111.png\" alt=\"rId6\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "4. If one substitutes @ with either “+\" or “<math display=\"inline\"><mo>×</mo></math>\" (multiplication), in the expression l@2@3@4@5@6. what is the maximum value that can be derived assuming that the correct order of mathematical operations are followed.",
                    question_hi: "4. यदि कोई व्यंजक l@2@3@4@5@6 में @ को \"+\" या “<math display=\"inline\"><mo>×</mo></math>\"  (गुणा) से प्रतिस्थापित करता है। यह मानते हुए कि गणितीय संक्रियाओं के सही क्रम का पालन किया जाता है, अधिकतम मान क्या प्राप्त किया जा सकता है।",
                    options_en: [" 720", " 732", 
                                " 612", " 721"],
                    options_hi: [" 720", " 732",
                                " 612", " 721"],
                    solution_en: "4.(d)<br />1 + 2 <math display=\"inline\"><mo>×</mo></math> 3 × 4 × 5 × 6<br />=1 + 720 = 721",
                    solution_hi: "4.(d)<br />1 + 2 <math display=\"inline\"><mo>×</mo></math> 3 × 4 × 5 × 6<br />=1 + 720 = 721",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "5. Select the number from among the given options that can replace the question mark (?) in the following series.<br />1, 5, 29, ?",
                    question_hi: "5. दिए गए विकल्पों में से उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है?<br />1, 5, 29, ?",
                    options_en: [" 196", " 259", 
                                " 256", " 324"],
                    options_hi: [" 196", " 259",
                                " 256", " 324"],
                    solution_en: "5.(b)<br />1 <math display=\"inline\"><mo>×</mo></math> 4 + 1 = 5<br />5 <math display=\"inline\"><mo>×</mo></math> 6 - 1 =29<br />29 <math display=\"inline\"><mo>×</mo></math> 9 - 2 = 259",
                    solution_hi: "5.(b)<br />1 <math display=\"inline\"><mo>×</mo></math> 4 + 1 = 5<br />5 <math display=\"inline\"><mo>×</mo></math> 6 - 1 =29<br />29 <math display=\"inline\"><mo>×</mo></math> 9 - 2 = 259",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Assuming that the given statement is true: ﬁnd out which of the four alternative (s) is definitely true <br><strong>Statement :</strong> <br>No newspaper is read by an illiterate <br>A. All newspapers that are read, are read by post graduates <br>B. Some newspapers that are read, are read by post graduates <br>C. Some newspapers are not read by illiterates <br>D. No illiterate ever reads a newspaper.</p>",
                    question_hi: "<p>6. यह मानते हुए कि दिया गया कथन सत्य है: ज्ञात कीजिए कि चार विकल्पों में से कौन सा विकल्प निश्चित रूप से सत्य है।<br><strong>कथन :</strong><br>निरक्षर द्वारा कोई अखबार किसी नहीं पढ़ा जाता है<br>A. पढ़े जाने वाले सभी समाचार पत्र स्नातकोत्तर द्वारा पढ़े जाते हैं<br>B. कुछ अखबार जो पढ़े जाते हैं, उन्हें स्नातकोत्तर द्वारा पढ़ा जाता है<br>C. कुछ समाचार पत्र निरक्षरों द्वारा नहीं पढ़े जाते हैं।<br>D. कोई भी निरक्षर कभी अखबार नहीं पढ़ता है</p>",
                    options_en: ["<p>Only A &amp; B</p>", "<p>Only C &amp; D</p>", 
                                "<p>Only A</p>", "<p>Only B</p>"],
                    options_hi: ["<p>केवल A और B</p>", "<p>केवल C और D</p>",
                                "<p>केवल A</p>", "<p>केवल B</p>"],
                    solution_en: "<p>6.(b)<br>According to the given statements it is clear that an illiterate can not read newspapers.<br>So only conclusion C and D follows.</p>",
                    solution_hi: "<p>6.(b)<br>दिए गए कथनों के अनुसार यह स्पष्ट है कि एक निरक्षर समाचार पत्र नहीं पढ़ सकता है।<br>अतः केवल निष्कर्ष C और D अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "7. Which of the numbers given below would come next in the following series of numbers? <br />1, 9, 25, 49,______.",
                    question_hi: "7. नीचे दी गई संख्याओं में से कौन सी संख्य निम्नलिखित श्रृंखला में अगले स्थान पर आएगी?<br />1, 9, 25, 49,______.",
                    options_en: [" 16", " 81", 
                                " 64", " 36"],
                    options_hi: [" 16", " 81",
                                " 64", " 36"],
                    solution_en: "7.(b)<br /><math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></math> = 1<br /><math display=\"inline\"><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup></math> = 9<br /><math display=\"inline\"><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></math> = 25<br /><math display=\"inline\"><msup><mrow><mn>7</mn></mrow><mrow><mn>2</mn></mrow></msup></math> = 49<br /><math display=\"inline\"><msup><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></msup></math> = 81",
                    solution_hi: "7.(b)<br /><math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></math> = 1<br /><math display=\"inline\"><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup></math> = 9<br /><math display=\"inline\"><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></math> = 25<br /><math display=\"inline\"><msup><mrow><mn>7</mn></mrow><mrow><mn>2</mn></mrow></msup></math> = 49<br /><math display=\"inline\"><msup><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></msup></math> = 81",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. A statement is given followed by an immediate conclusion. Choose the correct option regarding the conclusion.<br><strong>Statement :</strong> <br>All farmers are very hardworking <br><strong>Conclusion :</strong> <br>Some very hardworking persons are farmers</p>",
                    question_hi: "<p>8. एक कथन दिया गया है जिसके बाद तत्काल निष्कर्ष निकाला गया है। निष्कर्ष के संबंध में सही विकल्प चुनिए ।<br><strong>कथन :</strong> <br>सभी किसान बहुत मेहनती हैं<br><strong>निष्कर्ष :</strong> <br>कुछ बहुत मेहनती व्यक्ति किसान हैं।</p>",
                    options_en: ["<p>Information irrelevant</p>", "<p>True</p>", 
                                "<p>False</p>", "<p>Probably false but not sure</p>"],
                    options_hi: ["<p>जानकारी अप्रासंगिक</p>", "<p>सत्य</p>",
                                "<p>असत्य</p>", "<p>सम्भवतः असत्य है परन्तु निश्चित नही है</p>"],
                    solution_en: "<p>8.(b)<br>A/Q, All farmers are very hardworking so Some very hardworking persons will be farmers</p>",
                    solution_hi: "<p>8.(b)<br>प्रश्न के अनुसार, सभी किसान बहुत मेहनती हैं इसलिए कुछ बहुत मेहनती व्यक्ति किसान होंगे ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. A statement is followed by two assumptions. You have to consider the statement and the following assumptions and decide the correct option.<br><strong>Statement :</strong> <br>\"If you are a computer engineer, we want you as our senior consultant\" - an advertisement by company XYZ.<br><strong>Assumption :</strong> <br>1. Engineers are expected to be better performers by company XYZ.<br>2. The company XYZ needs a senior consultant</p>",
                    question_hi: "<p>9. एक कथन के बाद दो अवधारणाएँ हैं। आपको कथन और निम्नलिखित अवधारणाओं पर विचार करना है और सही विकल्प चुनना है।<br><strong>कथन :</strong><br>यदि आप एक कंप्यूटर इंजीनियर हैं, तो हम आपको हमारे वरिष्ठ सलाहकार के रूप में लेना चाहते हैं\" - कंपनी XYZ का एक विज्ञापन।<br><strong>अवधारणा :</strong><br>1. कंपनी XYZ द्वारा इंजीनियरों से बेहतर प्रदर्शन की उम्मीद की जाती है<br>2. कंपनी XYZ को एक वरिष्ठ सलाहकार की जरूरत है।</p>",
                    options_en: ["<p>Both Assumption 1 &amp; Assumption 2 are Implicit</p>", "<p>Only Assumption 2 is Implicit</p>", 
                                "<p>Neither Assumption 1 nor Assumption 2 are Implicit</p>", "<p>Only Assumption 1 is Implicit</p>"],
                    options_hi: ["<p>अवधारणा 1 और अवधारणा 2 दोनों निहित हैं</p>", "<p>केवल अवधारणा 2 निहित है</p>",
                                "<p>न तो अवधारणा 1 और न ही अवधारणा 2 निहित हैं</p>", "<p>केवल अवधारणा 1 निहित है</p>"],
                    solution_en: "<p>9.(b)<br>According to given statements only assumption 2 follows.</p>",
                    solution_hi: "<p>9.(b) <br>दिए गए कथनों के अनुसार केवल धारणा 2 अनुसरण करती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10. Read the following information carefully and answer the questions that follow:<br />A Word-number arrangement machine when given input as set of words and numbers rearranges them following a particular rule and generates a stepwise output until the rearrangement is complete following that rule.<br />Following is an illustration of the input and steps of rearrangement until the last step.<br />Input: Tokyo 7 Rio @ 5 Salva We<br />Step 1: Tokyo 7 Rio @ We 5 Salva<br />Step 2 : Tokyo 7 Rio We @ 5 Salva<br />Step 3 : 7 Rio @ We 5 Salva Tokyo<br />Step 4 : Rio 7 @ We 5 Salva Tokyo<br />In addition, step 4 is the last step.<br />For the input. “Let 1 mail & check me and\". What is Step 4?",
                    question_hi: "10. निम्नलिखित जानकारी को ध्यान से पढ़ें और नीचे दिए गए प्रश्नों के उत्तर दीजिये <br />एक शब्द-संख्या व्यवस्था मशीन को जब शब्दों और संख्याओं के समूह के रूप में इनपुट दिया जाता है तो यह उन्हें एक विशेष नियम का पालन करते हुए पुनर्व्यवस्थित करती है और उस नियम का पालन करने तक पुनर्व्यवस्था पूर्ण होने तक एक चरणबद्ध आउटपुट उत्पन्न करती है।<br />अंतिम चरण तक इनपुट और पुनर्व्यवस्था के चरणों का एक उदाहरण निम्नलिखित है।<br />इनपुट: Tokyo 7 Rio @ 5 Salva We<br />चरण 1: Tokyo 7 Rio @ We 5 Salva<br />चरण 2: Tokyo 7 Rio We @ 5 Salva<br />चरण 3: 7 Rio @ We 5 Salva Tokyo<br />चरण 4: Rio 7 @ We 5 Salva Tokyo<br />इसके अलावा, चरण 4 अंतिम चरण है।<br />इनपुट \"Let 1 mail & check me and के लिए, चरण 4 क्या है?",
                    options_en: [" mail 1 & and check me Let", " and check me Let mail 1 &", 
                                " mail 1 me Let 8: and check", " l & and check me Let mail"],
                    options_hi: [" mail 1 & and check me Let", " and check me Let mail 1 &",
                                " mail 1 me Let 8: and check", " l & and check me Let mail"],
                    solution_en: "10.(a)<br />Input: Let 1 mail & check me and<br />Step1: Let 1 mail & and check me<br />Step2: Let 1 mail and & check me<br />Step3: 1 mail & and check me Let<br />Step4: mail 1 & and check me Let",
                    solution_hi: "10.(a)<br />इनपुट: Let 1 mail & check me and<br />चरण 1:Let 1 mail & and check me<br />चरण 2:Let 1 mail and & check me<br />चरण 3:1 mail & and check me Let<br />चरण 4:mail 1 & and check me Let",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "11. Anuj plants 5,625 plants in his garden in such a way that the number of rows and the number of plants in a row remains the same. The number of plants in each row will be:",
                    question_hi: "11. अनुज अपने बगीचे में 5,625 पौधे इस प्रकार लगाते हैं कि पंक्तियों की संख्या और एक पंक्ति में पौधों की संख्या समान रहती है। प्रत्येक पंक्ति में पौधों की संख्या कितनी होगी?",
                    options_en: [" 75", " 2812", 
                                " 11250", " 2813"],
                    options_hi: [" 75", " 2812",
                                " 11250", " 2813"],
                    solution_en: "11.(a)<br />A/Q,<br />Number of rows = no. of plants in a row<br />It means that the garden is square shaped<br />No. of plants in each row = <math display=\"inline\"><msqrt><mn>5</mn><mo>,</mo><mn>625</mn></msqrt></math>  = 75",
                    solution_hi: "11.(a)<br />प्रश्न के अनुसार,<br />पंक्तियों की संख्या = पौधों की संख्या एक पंक्ति में                 <br />इसका मतलब है कि बगीचा चौकोर आकार का है<br />प्रत्येक पंक्ति में पौधों की संख्या= <math display=\"inline\"><msqrt><mn>5</mn><mo>,</mo><mn>625</mn></msqrt></math>  = 75",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "12. In a certain code, MAD = 56, CAT = 80. then DEN = ?",
                    question_hi: "12. एक निश्चित कोड में, MAD = 56, CAT = 80 तो DEN = ?",
                    options_en: [" 29", " 126", 
                                " 100", " 208"],
                    options_hi: [" 29", " 126",
                                " 100", " 208"],
                    solution_en: "12.(b)<br />MAD = (13+1)<math display=\"inline\"><mo>×</mo></math>4 = 56<br />CAT = (3+1)<math display=\"inline\"><mo>×</mo></math>20 = 80<br />DEN = (4+5)<math display=\"inline\"><mo>×</mo></math>14 = 126",
                    solution_hi: "12.(b)<br />MAD = (13+1)<math display=\"inline\"><mo>×</mo></math>4 = 56<br />CAT = (3+1)<math display=\"inline\"><mo>×</mo></math>20 = 80<br />DEN = (4+5)<math display=\"inline\"><mo>×</mo></math>14 = 126",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13. There are six toys arranged one above the other in the form of a stack. the lowermost is numbered 1 and the toy above it as 2 and so on. Each toy is of a different colour Purple, Pink, White. Yellow, Red and Grey. Purple toy is at the even position but not at the top. Red toy is not adjacent to white or yellow toy. Grey toy is at the 5th position. There are two toys between pink and white toys where the pink toy is above the white one. White toy is not at the bottommost position. At which position is the Pink toy?",
                    question_hi: "13. छह खिलौनों को एक दूसरे के ऊपर ढेर बनाते हुए रखा गया है, सबसे नीचले पर 1 अंक लिखा गया है और इसके उपर वाले पर 2 और इसी प्रकार आगे लिखा गया है। प्रत्येक खिलौना एक अलग रंग बैंगनी, गुलाबी, सफेद, पीला, लाल और भूरे रंग का है। बैंगनी खिलौना सम स्थिति में है, लेकिन शीर्ष पर नहीं है । लाल खिलौना सफेद या पीले रंग के खिलौने से सटा हुआ नहीं है। भूरा  खिलौना 5वें स्थान पर है। गुलाबी और सफेद खिलौनों के बीच दो खिलौने हैं जहां गुलाबी खिलौना सफेद एक से ऊपर है। सफेद खिलौना सबसे निचले पायदान पर नहीं है। गुलाबी खिलौना किस स्थिति में है?",
                    options_en: [" 3rd", " 6th", 
                                " 2nd", " 4th"],
                    options_hi: [" 3rd", " 6th",
                                " 2nd", " 4th"],
                    solution_en: "13.(b)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727953281230.png\" alt=\"rId7\" />",
                    solution_hi: "13.(b)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727953281413.png\" alt=\"rId8\" />",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "14. There is a group of six persons, A, B, C, D, E, and F in a room. In the group, there are two married couples. B is Italian and is the mother of C. F is the grandmother of C. F is from canada. D is the grandfather of E and D is from Australia. There is one Italian, one Canadian, one Australian, one French and two Irish people in the group. The French person is male and is married. Nobody who is a grandchild is married. What is the nationality of A and who is his wife?",
                    question_hi: "14. एक कमरे में छह व्यक्तियों, A, B, C, D, E, और F  का एक समूह है। समूह में दो विवाहित जोड़े हैं। B इतालवी है और C की माता है। F, C की दादी है। F कनाडा से है। D, E का दादा है और D ऑस्ट्रेलिया से है। समूह में एक इतालवी, एक कनाडाई, एक ऑस्ट्रेलियाई, एक फ्रांसीसी और दो आयरिश लोग हैं। फ्रांसीसी व्यक्ति पुरुष है और विवाहित है। कोई भी जो पोता है विवाहित नहीं है। A की राष्ट्रीयता क्या है और उसकी पत्नी कौन है?",
                    options_en: [" French, F ", " French, B ", 
                                " Irish, F  ", " Italian, B  "],
                    options_hi: [" फ्रेंच, F", " फ्रेंच, B",
                                " आयरिश, F", " इतालवी, B"],
                    solution_en: "14.(b)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727953281514.png\" alt=\"rId9\" />",
                    solution_hi: "14.(b)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727953281636.png\" alt=\"rId10\" />",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "15. FInd the missing term in the given series: A2B, C12D, E30F, _____, I90J",
                    question_hi: "15. दी गई श्रृंखला में लुप्त पद ज्ञात कीजिए: A2B, C12D, E30F, _____, I90J",
                    options_en: [" G64H", " G48H", 
                                " G56H", " G49H"],
                    options_hi: [" G64H", " G48H",
                                " G56H", " G49H"],
                    solution_en: "15.(c)<br /> A2B, C12D, E30F, _____, I90J<br />From the given series we observe that two consecutive alphabets and the product of their place value is written in the middle. So the missing term = G56H",
                    solution_hi: "15.(c)<br />A2B, C12D, E30F, _____, I90J<br />दी गई श्रृंखला से हम देखते हैं कि दो क्रमागत अक्षर और उनके स्थानीय मान का गुणनफल बीच में लिखा गया है। अतः लुप्त पद = G56H",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "16. Select the option that is related to the third term in the same way as the first term is related to the second term. <br />China : Beijing :: Brazil : ?",
                    question_hi: "16. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे पहला पद दूसरे पद से संबंधित है।<br />चीन : बीजिंग :: ब्राजील : ?",
                    options_en: [" Brasilia ", " Shanghai  ", 
                                " Rio de janeiro  ", " Buenos Aires "],
                    options_hi: [" ब्रासीलिया", " शंघाई",
                                " रियो डी जनेरियो", " ब्यूनस आयर्स"],
                    solution_en: "16.(a)<br />The 2nd is the capital of the first .",
                    solution_hi: "16.(a)<br />दूसरा पहले की राजधानी है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "17. There are six toys arranged one above the other in the form of a stack, the lowermost is numbered 1 and the toy above it as 2 and so on. Each toy is of a different colour Purple, Pink, White, Red and Grey. Purple toy is at the even position but not at the top. Red toy is not adjacent to white or yellow toy. Grey toy is the 5th position. There are two toys between pink and white toys where the pink toy is above the White one. White toy is not at the bottommost position. Which toy among the following options is at an odd position? ",
                    question_hi: "17. छह खिलौनों को एक के ऊपर एक ढेर के रूप में व्यवस्थित किया गया है, सबसे नीचे की संख्या 1 है और इसके ऊपर के खिलौने को 2 और इसी तरह आगे रखा गया है। प्रत्येक खिलौना एक अलग रंग बैंगनी, गुलाबी, सफेद, लाल और भूरा है। बैंगनी खिलौना सम स्थिति में है लेकिन शीर्ष पर नहीं है। लाल खिलौना सफेद या पीले खिलौने के निकट नहीं है। भूरा खिलौना 5वें  स्थान पर है। गुलाबी और सफेद खिलौने के बीच दो खिलौने हैं जहां गुलाबी खिलौना सफेद के ऊपर है। सफेद खिलौना सबसे नीचे की स्थिति में नहीं है। निम्नलिखित विकल्पों में से कौन सा खिलौना विषम स्थिति में है?",
                    options_en: [" White toy ", " Pink toy  ", 
                                " Yellow toy ", " Purple toy "],
                    options_hi: [" सफेद खिलौना", " गुलाबी खिलौना",
                                " पीला खिलौना", " बैंगनी खिलौना"],
                    solution_en: "17.(a)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727953281230.png\" alt=\"rId7\" />",
                    solution_hi: "17.(a)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727953281413.png\" alt=\"rId8\" />",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Given below is a statement followed by two courses of actions. Assuming everything in the statement to be true, irrespective of the commonly known facts, decide which of the two suggested course(s) of action logically follow for pursuing.<br><strong>Statement :</strong><br>There is a shortage of water in Delhi during summers. <br><strong>Course of action 1 :</strong><br>There should be more water conservation projects initiated by the government of Delhi <br><strong>Course of action 2 :</strong> <br>The government should encourage rain water harvesting in all new and existing residential complexes</p>",
                    question_hi: "<p>18. नीचे एक कथन दिया गया है जिसके बाद दो कार्यप्रणाली हैं। कथन में सब कुछ सत्य मानते हुए, सामान्य रूप से ज्ञात तथ्यों के बावजूद, बताइये कि दो सुझाए गए तरीकों में से कौन सा तार्किक रूप से अनुसरण करता है।<br><strong>कथन :</strong> <br>गर्मी के दिनों में दिल्ली में पानी की किल्लत हो जाती है<br><strong>कार्यप्रणाली 1 :</strong><br>दिल्ली सरकार द्वारा और अधिक जल संरक्षण परियोजनाएं शुरू की जानी चाहिए।<br><strong>कार्यप्रणाली 2 :</strong> <br>सरकार को सभी नए और मौजूदा आवासीय परिसरों में वर्षा जल संचयन को प्रोत्साहित करना चाहिए</p>",
                    options_en: ["<p>Both course of action 1 and course of action 2 follow</p>", "<p>Only course of action 1 follows</p>", 
                                "<p>Only course of action 2 follows</p>", "<p>Neither course of action 1 nor course of action 2 follows</p>"],
                    options_hi: ["<p>कार्यप्रणाली 1 और कार्यप्रणाली 2 दोनों का अनुसरण करती है</p>", "<p>केवल कार्यप्रणाली 1 का अनुसरण करती है।</p>",
                                "<p>केवल कार्यप्रणाली 2 अनुसरण करती है।</p>", "<p>न तो कार्यप्रणाली 1 और न ही कार्यप्रणाली 2 का अनुसरण करती है।</p>"],
                    solution_en: "<p>18.(a)<br>From the given statement it is clear that from both Course of action 1 and course of action 2 the shortage of water can be minimised.</p>",
                    solution_hi: "<p>18.(a)<br>दिए गए कथन से यह स्पष्ट है कि कार्रवाई 1 और कार्रवाई 2 दोनों से पानी की कमी को कम किया जा सकता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "19. If aPb implies a+b, aSb implies a-b, aMb implies a<math display=\"inline\"><mo>×</mo></math>b and aDb implies a÷b, find the value of 5P6S3M4D8",
                    question_hi: "19. यदि aPb का अर्थ a+b है, aSb का अर्थ a-b है, aMb का अर्थ ab है और aDb का अर्थ ab है, तो 5P6S3M4D8 का मान ज्ञात कीजिए।",
                    options_en: [" 10", " 8.5", 
                                " 9.5", " 8"],
                    options_hi: [" 10", " 8.5",
                                " 9.5", " 8"],
                    solution_en: "19.(c)<br />     5P6S3M4D8<br />= 5 + 6 - 3 <math display=\"inline\"><mo>×</mo></math> 4 ÷ 8<br />= 5 + 6 - 3 <math display=\"inline\"><mo>×</mo></math> 0.5<br />= 11 - 1.5 = 9.5",
                    solution_hi: "19.(c)<br />     5P6S3M4D8<br />= 5 + 6 - 3 <math display=\"inline\"><mo>×</mo></math> 4 ÷ 8<br />= 5 + 6 - 3 <math display=\"inline\"><mo>×</mo></math> 0.5<br />= 11 - 1.5 = 9.5",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Choose the odd option among the following.</p>",
                    question_hi: "<p>20. निम्नलिखित में से विजातीय विकल्प का चयन कीजिए।</p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>10</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>13</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>10</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>13</mn></mfrac></math></p>"],
                    solution_en: "<p>20.(a)<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> is not in simplest form. Rest are in simplest form.</p>",
                    solution_hi: "<p>20.(a)<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> सरलतम रूप में नहीं है। शेष सरलतम रूप में हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "21. In a class of 47 students. Anurag’s roll number is eighteenth from the end. What is his roll number from the beginning? ",
                    question_hi: "21. 47 छात्रों की एक कक्षा में अनुराग का रोल नंबर अंत से अठारहवां है। उसका रोल नंबर शुरू से कितना है?",
                    options_en: [" 30", " 29", 
                                " 27", " 28"],
                    options_hi: [" 30", " 29",
                                " 27", " 28"],
                    solution_en: "21.(a)<br />Anurag’s roll no. from the beginning = 47 - 18 + 1 = 30",
                    solution_hi: "21.(a)<br />अनुराग रोल नं. प्रारंभ से= 47 - 18 + 1= 30<br />        ",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option that is related to the third term in the same way as the second term is related to the first term.<br>Ten : Decade :: Thousand : ___________</p>",
                    question_hi: "<p>22. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है<br>दश : दशक :: हज़ार : ___________</p>",
                    options_en: ["<p>Century</p>", "<p>Millennium</p>", 
                                "<p>Triennium</p>", "<p>Lustrum</p>"],
                    options_hi: ["<p>शताब्दी</p>", "<p>सहस्त्राब्दी</p>",
                                "<p>त्रयाब्द</p>", "<p>पंचाब्द</p>"],
                    solution_en: "<p>22.(b)<br>As 10 years is known as decade similarly 1000 years is known as Millenium.</p>",
                    solution_hi: "<p>22.(b)<br>जिस प्रकार 10 वर्ष को दशक कहा जाता है उसी प्रकार 1000 वर्ष को सहस्त्राब्दी के रूप में जाना जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. A Statement is followed by two conclusions. Choose the correct options considering the statement to be true irrespective of the commonly known facts.<br><strong>Statement :</strong><br>Don&rsquo;t throw garbage here. If you violate this non littering sign, a fine of Rs. 5000 will be imposed.<br><strong>Conclusion :</strong> <br>1. The same fine is charged for littering offence everywhere. <br>2. This warning is written at every public place of non littering zone</p>",
                    question_hi: "<p>23. एक कथन के बाद दो निष्कर्ष दिए गए हैं। सामान्य ज्ञात तथ्यों पर ध्यान दिए बिना कथन को सत्य मानते हुए सही विकल्प का चयन कीजिए।<br><strong>कथन :</strong><br>यहां कूड़ा न फेंके यदि आप इस कूड़ा-करकट न फेंकने वाले संकेत उल्लंघन करते हैं तो 5000 रुपये का जुर्माना लगाया जा सकता है।<br><strong>निष्कर्ष :</strong><br>1. हर जगह कूड़ा फेंकने पर एक जैसा जुर्माना वसूला जाता है<br>2. यह चेतावनी कूड़ा-करकट न करने वाले क्षेत्र के प्रत्येक सार्वजनिक स्थान पर लिखी हुई है</p>",
                    options_en: ["<p>Neither conclusion 1 nor conclusion 2 follow</p>", "<p>Only conclusion 1 follows</p>", 
                                "<p>Only conclusion 2 follows</p>", "<p>Both conclusion 1 and conclusion 2 follow</p>"],
                    options_hi: ["<p>न तो निष्कर्ष 1 और न ही निष्कर्ष 2 अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष 1 अनुसरण करता है।</p>",
                                "<p>केवल निष्कर्ष 2 अनुसरण करता है</p>", "<p>निष्कर्ष 1 और निष्कर्ष 2 दोनों अनुसरण करते हैं।</p>"],
                    solution_en: "<p>23.(a)<br>From the given statements it is clear that the charge is only for throwing garbage not for every littering offence and also it is not written at every public place of non littering zone.</p>",
                    solution_hi: "<p>23.(a)<br>दिए गए कथनों से स्पष्ट है कि आरोप केवल कूड़ा फेंकने का है, न कि प्रत्येक कूड़ा-करकट करने वाले अपराध के लिए और साथ ही यह गैर कूड़ा-करकट क्षेत्र के प्रत्येक सार्वजनिक स्थान पर नहीं लिखा गया है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Choose the conclusion(s) that logically follow from the given statement, disregarding commonly known facts.<br><strong>Statement :</strong><br>No country is free from terrorism these days.<br><strong>Conclusion A : </strong>It has become impossible for countries to control terrorism these days.<br><strong>Conclusion B :</strong> The Countries and their people in general have become lazy.</p>",
                    question_hi: "<p>24. सामान्य ज्ञात तथ्यों की अवहेलना करते हुए, दिए गए कथन का तार्किक रूप से अनुसरण करने वाले निष्कर्ष को चुनिए ।<br><strong>कथन :</strong> <br>आज के समय में कोई भी देश आतंकवाद से मुक्त नहीं है<br><strong>निष्कर्ष A : </strong>इन दिनों देशों के लिए आतंकवाद को नियंत्रित करना असंभव हो गया है।<br><strong>निष्कर्ष B : </strong>देश और उनके लोग सामान्य रूप से आलसी हो गए हैं।</p>",
                    options_en: ["<p>Only conclusion B follows.</p>", "<p>Both conclusions A and B follow</p>", 
                                "<p>Only conclusion A follows</p>", "<p>Neither conclusion A nor conclusion B follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष B अनुसरण करता है।</p>", "<p>निष्कर्ष A और B दोनों अनुसरण करते हैं</p>",
                                "<p>केवल निष्कर्ष A अनुसरण करता है</p>", "<p>न तो निष्कर्ष A और न ही निष्कर्ष B अनुसरण करता है</p>"],
                    solution_en: "<p>24.(c)<br>From the given statement it is clear that terrorism is in every country so Conclusion A follows and true.</p>",
                    solution_hi: "<p>24.(c)<br>दिए गए कथन से यह स्पष्ट है कि आतंकवाद हर देश में है इसलिए निष्कर्ष A अनुसरण करता है और सत्य है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "25. In which of the given options, is the letters skipped between adjacent letters in the order 1, 2, 3.",
                    question_hi: "25. दिए गए विकल्पों में से किसमें, क्रम 1, 2, 3 में आसन्न अक्षरों के बीच छोड़ दिया अक्षर है।",
                    options_en: [" EFJN ", " FHKO", 
                                " ACDE", " FHLP"],
                    options_hi: [" EFJN ", " FHKO",
                                " ACDE", " FHLP"],
                    solution_en: "25.(b)<br />In  FHKO , the letters are skipped between adjacent letters in the order 1, 2, 3.",
                    solution_hi: "25.(b)<br />FHKO में, अक्षरों को क्रम 1, 2, 3 में आसन्न अक्षरों के बीच छोड़ दिया जाता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "6",
                    question_en: "<p>26. Pradeep says &rdquo;Vishal&rsquo;s mother is the only daughter of my mother&rdquo;. How is Pradeep related to Vishal?</p>",
                    question_hi: "<p>26. प्रदीप कहता है कि \"विशाल की माँ मेरी माँ की इकलौती पुत्री है\"। प्रदीप, विशाल से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Brother</p>", "<p>Maternal uncle</p>", 
                                "<p>Father</p>", "<p>Grandfather</p>"],
                    options_hi: ["<p>भाई</p>", "<p>मामा</p>",
                                "<p>पिता</p>", "<p>दादा</p>"],
                    solution_en: "<p>26.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727953281780.png\" alt=\"rId11\" width=\"185\" height=\"183\"></p>",
                    solution_hi: "<p>26.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727953281907.png\" alt=\"rId12\" width=\"182\" height=\"198\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "misc",
                    question_en: "27. Four elements have been listed, out of which three are alike in some manner and one is different. Select the odd one.",
                    question_hi: "27. चार तत्वों को सूचीबद्ध किया गया है, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है । विजातीय का चयन कीजिये ।",
                    options_en: [" Sodium ", " Helium ", 
                                " Neon ", " Argon "],
                    options_hi: [" सोडियम", " हीलियम",
                                " नियोन", " आर्गन"],
                    solution_en: "27.(a)<br />Except Sodium, all others are noble gases.",
                    solution_hi: "27.(a)<br />सोडियम को छोड़कर, अन्य सभी उत्कृष्ट गैसें हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>