<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the underlined word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Majority of the people give importance to<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">transient</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"> </span>things.</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the underlined word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Majority of the people give importance to </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">transient</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"> </span>things.</span></p>\n",
                    options_en: ["<p>Permanent</p>\n", "<p>Temporary</p>\n", 
                                "<p>Material</p>\n", "<p>Monetary</p>\n"],
                    options_hi: ["<p>Permanent</p>\n", "<p>Temporary</p>\n",
                                "<p>Material</p>\n", "<p>Monetary</p>\n"],
                    solution_en: "<p>1.(a)</p>\r\n<p><strong>Permanent</strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> not temporary or changing.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Transient</span></strong><span style=\"font-family: Cambria Math;\">- lasting for only a short time; temporary.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Temporary</span></strong><span style=\"font-family: Cambria Math;\">- lasting for a l</span><span style=\"font-family: Cambria Math;\">imited time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Material</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> relating to physical substances or things.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Monetary</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> relating to money or currency.</span></p>\n",
                    solution_hi: "<p>1.(a)</p>\r\n<p><strong>Permanent</strong> <span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2351;&#2368;</span><span style=\"font-family: Cambria Math;\">) - not temporary or changing.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Transient</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2339;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">) - lasting for only a short time; temporary.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Temporary </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2381;&#2341;&#2366;&#2351;&#2368;</span><span style=\"font-family: Cambria Math;\">) - las</span><span style=\"font-family: Cambria Math;\">ting for a limited time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Material </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2342;&#2366;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\">) - relating to physical substances or things.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Monetary</span></strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2342;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\">) - relating to money or currency.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the word in brackets to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Although she was tired, she kept working on her project, ______ (indefinite) to finish it before the deadline.</span></p>\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the word in brackets to fill in the blan</span><span style=\"font-family: Cambria Math;\">k.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Although she was tired, she kept working on her project, ______ (indefinite) to finish it before the deadline.</span></p>\n",
                    options_en: ["<p>bounded</p>\n", "<p>believed</p>\n", 
                                "<p>contingently</p>\n", "<p>incensed</p>\n"],
                    options_hi: ["<p>bounded</p>\n", "<p>believed</p>\n",
                                "<p>contingently</p>\n", "<p>incensed</p>\n"],
                    solution_en: "<p>2.(a)</p>\r\n<p><strong>bounded</strong><span style=\"font-family: Cambria Math;\"><strong>- </strong>confined within certain limits or boundaries.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Indefinite</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> not clear, fixed, or certain.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Believed</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> accepted as true or existing.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Contingently</span></strong><span style=\"font-family: Cambria Math;\">- in a way that depends on something that may or may not happen.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Incensed</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> extremely angry or enraged.</span></p>\n",
                    solution_hi: "<p>2.(a)</p>\r\n<p><strong>bounded </strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2368;&#2350;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) - confined within certain limi</span><span style=\"font-family: Cambria Math;\">ts or boundaries.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Indefinite</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) - not clear, fixed, or certain.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Believed </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2381;&#2357;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">) - accepted as true or existing.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Contingently</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">) - in a way that depends on something that may or may not happen.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Incensed </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2379;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) - extremely a</span><span style=\"font-family: Cambria Math;\">ngry or enraged.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Intensive</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Intensive</span></p>\n",
                    options_en: ["<p>Real</p>\n", "<p>Dare</p>\n", 
                                "<p>Fierce</p>\n", "<p>Superficial</p>\n"],
                    options_hi: ["<p>Real</p>\n", "<p>Dare</p>\n",
                                "<p>Fierce</p>\n", "<p>Superficial</p>\n"],
                    solution_en: "<p>3.(d)</p>\r\n<p><strong>Superficial -</strong> <span style=\"font-family: Cambria Math;\">only on the surface, not deep.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Intensive -</strong> </span><span style=\"font-family: Cambria Math;\">very focused and thorough.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Real -</strong> </span><span style=\"font-family: Cambria Math;\">genuine or authentic.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Dare </strong>- </span><span style=\"font-family: Cambria Math;\">to have the courage to do something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Fierce </strong>- </span><span style=\"font-family: Cambria Math;\">very strong or aggressive.</span></p>\n",
                    solution_hi: "<p>3.(d)</p>\r\n<p><strong>Superficial </strong><span style=\"font-weight: 400;\">(&#2313;&#2341;&#2354;&#2366;/&#2360;&#2340;&#2361;&#2368;) - only on the surface, not deep.</span></p>\r\n<p><strong>Intensive </strong><span style=\"font-weight: 400;\">(&#2327;&#2361;&#2344;) - very focused and thorough.</span></p>\r\n<p><strong>Real </strong><span style=\"font-weight: 400;\">(&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;) -</span><strong> </strong><span style=\"font-weight: 400;\">genuine or authentic.</span></p>\r\n<p><strong>Dare</strong><span style=\"font-weight: 400;\"> (&#2360;&#2366;&#2361;&#2360; &#2325;&#2352;&#2344;&#2366;) -</span><strong> </strong><span style=\"font-weight: 400;\">to have the courage to do something.</span></p>\r\n<p><strong>Fierce </strong><span style=\"font-weight: 400;\">(&#2325;&#2381;&#2352;&#2370;&#2352;) -</span><strong> </strong><span style=\"font-weight: 400;\">very strong or aggressive.</span></p>\r\n<p><br><br></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM to substitute the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The preparations for the picnic have been<span style=\"text-decoration: underline;\"> </span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">meticulously</span> </span><span style=\"font-family: Cambria Math;\">completed by the programme committee on time.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM to substitute the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The preparations for the picnic have been </span><span style=\"font-family: Cambria Math;\">meticulously </span><span style=\"font-family: Cambria Math;\">completed by the programme committee on time.</span></p>\n",
                    options_en: ["<p>Mesmerisingly</p>\n", "<p>Prudently</p>\n", 
                                "<p>Chaotically</p>\n", "<p>Serenely</p>\n"],
                    options_hi: ["<p>Mesmerisingly</p>\n", "<p>Prudently</p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">Chaotically </span></p>\n", "<p>Serenely</p>\n"],
                    solution_en: "<p>4.(c)</p>\r\n<p><strong>Chaotically -</strong> <span style=\"font-family: Cambria Math;\">in a disordered and random way.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Meticulously </strong>- </span><span style=\"font-family: Cambria Math;\">very carefully and precisely.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Mesmerisingly </strong>- </span><span style=\"font-family: Cambria Math;\">in a way that fascinates.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Prudently -</strong> </span><span style=\"font-family: Cambria Math;\">with careful and wise judgment.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Serenely </strong>- </span><span style=\"font-family: Cambria Math;\">in a calm and pea</span><span style=\"font-family: Cambria Math;\">ceful manner.</span></p>\n",
                    solution_hi: "<p>4.(c)</p>\r\n<p><strong>Chaotically </strong><span style=\"font-weight: 400;\">(&#2309;&#2360;&#2381;&#2340;-&#2357;&#2381;&#2351;&#2360;&#2381;&#2340;) -</span><strong> </strong><span style=\"font-weight: 400;\">in a disordered and random way.</span></p>\r\n<p><strong>Meticulously</strong><span style=\"font-weight: 400;\"> (&#2360;&#2366;&#2357;&#2343;&#2366;&#2344;&#2368;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325;) - very carefully and precisely.</span></p>\r\n<p><strong>Mesmerisingly </strong><span style=\"font-weight: 400;\">(&#2350;&#2306;&#2340;&#2381;&#2352;&#2350;&#2369;&#2327;&#2381;&#2343; &#2325;&#2352; &#2342;&#2375;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366;) -</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">in a way that fascinates.</span></p>\r\n<p><strong>Prudently </strong><span style=\"font-weight: 400;\">(&#2357;&#2367;&#2357;&#2375;&#2325;&#2346;&#2369;&#2352;&#2381;&#2357;&#2325;) -</span><strong> </strong><span style=\"font-weight: 400;\">with careful and wise judgment.</span></p>\r\n<p><strong>Serenely </strong><span style=\"font-weight: 400;\">(&#2358;&#2366;&#2306;&#2340;&#2367;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325;) -</span><strong> </strong><span style=\"font-weight: 400;\">in a calm and peaceful manner.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Extract</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Extract</span></p>\n",
                    options_en: ["<p>Insert</p>\n", "<p>Fry</p>\n", 
                                "<p>Cut</p>\n", "<p>Pull</p>\n"],
                    options_hi: ["<p>Insert</p>\n", "<p>Fry</p>\n",
                                "<p>Cut</p>\n", "<p>Pull</p>\n"],
                    solution_en: "<p>5.(a)&nbsp;</p>\r\n<p><strong>Insert</strong><span style=\"font-weight: 400;\">- to put something into or between other things.</span></p>\r\n<p><strong>Extract</strong><span style=\"font-weight: 400;\">- to remove or take something out.</span></p>\r\n<p><strong>Fry</strong><span style=\"font-weight: 400;\">- to cook food in hot oil.</span></p>\r\n<p><strong>Cut</strong><span style=\"font-weight: 400;\">- to divide or separate something into two or more pieces.</span></p>\r\n<p><strong>Pull</strong><span style=\"font-weight: 400;\">- to exert force to bring something towards oneself.</span></p>\n",
                    solution_hi: "<p>5.(a)</p>\r\n<p><strong>Insert </strong><span style=\"font-weight: 400;\">(&#2346;&#2381;&#2352;&#2357;&#2367;&#2359;&#2381;&#2335; &#2325;&#2352;&#2344;&#2366;)- to put something into or between other things.</span></p>\r\n<p><strong>Extract </strong><span style=\"font-weight: 400;\">(&#2344;&#2367;&#2325;&#2366;&#2354;&#2344;&#2366;)- to remove or take something out.</span></p>\r\n<p><strong>Fry </strong><span style=\"font-weight: 400;\">(&#2340;&#2354;&#2344;&#2366;)</span><strong> </strong><span style=\"font-weight: 400;\">- to cook food in hot oil.</span></p>\r\n<p><strong>Cut</strong><span style=\"font-weight: 400;\"> (&#2325;&#2366;&#2335;&#2344;&#2366;)</span><strong> </strong><span style=\"font-weight: 400;\">- to divide or separate something into two or more pieces.</span></p>\r\n<p><strong>Pull</strong><span style=\"font-weight: 400;\"> (&#2326;&#2368;&#2306;&#2330;&#2344;&#2366;)</span><strong> </strong><span style=\"font-weight: 400;\">- to exert force to bring something towards oneself.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the underlined word in the given sente</span><span style=\"font-family: Cambria Math;\">nce.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">One needs to be free of<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">prejudices</span></span><span style=\"font-family: Cambria Math;\"> while dealing with judicial cases.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">One needs to be free of<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">prejudices</span></span><span style=\"font-family: Cambria Math;\"> while dealing with judicial cases.</span></p>\n",
                    options_en: ["<p>Favours</p>\n", "<p>Discriminations</p>\n", 
                                "<p>Bigotries</p>\n", "<p>Equalities</p>\n"],
                    options_hi: ["<p>Favours</p>\n", "<p>Discriminations</p>\n",
                                "<p>Bigotries</p>\n", "<p>Equalities</p>\n"],
                    solution_en: "<p>6.(d)</p>\r\n<p><strong>Equalities- </strong><span style=\"font-weight: 400;\">The state or quality of being equal.</span></p>\r\n<p><strong>Prejudices</strong><span style=\"font-weight: 400;\">- Preconceived opinions or judgments about a person or group.</span></p>\r\n<p><strong>Favours</strong><span style=\"font-weight: 400;\">- Acts of kindness or assistance provided to someone.</span></p>\r\n<p><strong>Discriminations</strong><span style=\"font-weight: 400;\">- Unfair or prejudiced treatment of individuals.</span></p>\r\n<p><strong>Bigotries</strong><span style=\"font-weight: 400;\">- unreasonable or unfair attachment to a particular set of beliefs.</span></p>\r\n<p><br><br></p>\n",
                    solution_hi: "<p>6.(d)</p>\r\n<p><strong>Equalities </strong><span style=\"font-weight: 400;\">(&#2360;&#2350;&#2366;&#2344;&#2340;&#2366;) </span><strong>- </strong><span style=\"font-weight: 400;\">The state or quality of being equal.</span></p>\r\n<p><strong>Prejudices </strong><span style=\"font-weight: 400;\">(&#2346;&#2370;&#2352;&#2381;&#2357;&#2343;&#2366;&#2352;&#2339;&#2366;)</span><strong> </strong><span style=\"font-weight: 400;\">- Preconceived opinions or judgments about a person or group.</span></p>\r\n<p><strong>Favours</strong><span style=\"font-weight: 400;\"> (&#2319;&#2361;&#2360;&#2366;&#2344;) - Acts of kindness or assistance provided to someone.</span></p>\r\n<p><strong>Discriminations</strong><span style=\"font-weight: 400;\"> (&#2349;&#2375;&#2342;&#2349;&#2366;&#2357;) - Unfair or prejudiced treatment of individuals.</span></p>\r\n<p><strong>Bigotries</strong><span style=\"font-weight: 400;\"> (&#2325;&#2335;&#2381;&#2335;&#2352;&#2340;&#2366;)</span><strong> </strong><span style=\"font-weight: 400;\">- unreasonable or unfair attachment to a particular set of beliefs.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">It is a<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">misconce</span><span style=\"font-family: Cambria Math;\">p</span><span style=\"font-family: Cambria Math;\">tion</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"> </span>that the more you learn, the more likely you are to succeed.</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">It is a </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">misconce</span><span style=\"font-family: Cambria Math;\">p</span><span style=\"font-family: Cambria Math;\">tion</span></span><span style=\"font-family: Cambria Math;\"> that the more you learn, the more likely you are to succeed.</span></p>\n",
                    options_en: ["<p>Illusion</p>\n", "<p>Hallucination</p>\n", 
                                "<p>Fallacy</p>\n", "<p>Certainty</p>\n"],
                    options_hi: ["<p>Illusion</p>\n", "<p>Halluc<span style=\"font-family: Cambria Math;\">ination</span></p>\n",
                                "<p>Fallacy</p>\n", "<p>Certainty</p>\n"],
                    solution_en: "<p>7.(d)</p>\r\n<p><strong>Certainty</strong><span style=\"font-weight: 400;\">- the state of being completely sure or confident about something.</span></p>\r\n<p><strong>Misconception</strong><span style=\"font-weight: 400;\">- a mistaken or incorrect belief or idea about something.</span></p>\r\n<p><strong>Illusion</strong><span style=\"font-weight: 400;\">- a false or misleading perception or appearance.</span></p>\r\n<p><strong>Hallucination</strong><span style=\"font-weight: 400;\">- when someone sees, hears, feels, or smells something that isn\'t really there.&nbsp;</span></p>\r\n<p><strong>Fallacy</strong><span style=\"font-weight: 400;\">-&nbsp; a false belief that can lead to incorrect conclusions.</span></p>\n",
                    solution_hi: "<p>7.(d)</p>\r\n<p><strong>&nbsp;</strong><strong>Certainty </strong><span style=\"font-weight: 400;\">(&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;&#2340;&#2366;) - the state of being completely sure or confident about something.</span></p>\r\n<p><strong>Misconception </strong><span style=\"font-weight: 400;\">(&#2327;&#2364;&#2354;&#2340;&#2347;&#2364;&#2361;&#2350;&#2368;) - a mistaken or incorrect belief or idea about something.</span></p>\r\n<p><strong>Illusion </strong><span style=\"font-weight: 400;\">(&#2349;&#2381;&#2352;&#2350;) - a false or misleading perception or appearance.</span></p>\r\n<p><strong>Hallucination </strong><span style=\"font-weight: 400;\">(&#2350;&#2340;&#2367;&#2349;&#2381;&#2352;&#2350;) - when someone sees, hears, feels, or smells something that isn\'t really there.&nbsp;</span></p>\r\n<p><strong>Fallacy</strong><span style=\"font-weight: 400;\"> (&#2349;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;) -&nbsp; a false belief that can lead to incorrect conclusions.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the underlined word in the following </span><span style=\"font-family: Cambria Math;\">sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The doctor assured the patient that the tumour was </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">beni</span><span style=\"font-family: Cambria Math;\">g</span><span style=\"font-family: Cambria Math;\">n</span></span><span style=\"font-family: Cambria Math;\"> and could be easily </span><span style=\"font-family: Cambria Math;\">Removed</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the underlined word in the following </span><span style=\"font-family: Cambria Math;\">sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The doctor assured the patient that the tumour was </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">beni</span><span style=\"font-family: Cambria Math;\">g</span><span style=\"font-family: Cambria Math;\">n</span></span><span style=\"font-family: Cambria Math;\"> and could be easily </span><span style=\"font-family: Cambria Math;\">Removed</span></p>\n",
                    options_en: ["<p>Favourable</p>\n", "<p>Malignant</p>\n", 
                                "<p>Sterile</p>\n", "<p>Kind</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Favourable</p>\n", "<p>Malignant</p>\n",
                                "<p>Sterile</p>\n", "<p>Kind</p>\n"],
                    solution_en: "<p>8.(b)</p>\r\n<p><strong>&nbsp;Malignant</strong><span style=\"font-weight: 400;\">- harmful or cancerous.</span><span style=\"font-weight: 400;\"><br></span><strong>Benign</strong><span style=\"font-weight: 400;\">- harmless.</span></p>\r\n<p><strong>Favourable</strong><span style=\"font-weight: 400;\">- advantageous or positive.</span></p>\r\n<p><strong>Sterile</strong><span style=\"font-weight: 400;\">- lacking microorganisms or incapable of producing offspring.</span></p>\n",
                    solution_hi: "<p>8.(b)</p>\r\n<p><strong>Malignant </strong><span style=\"font-weight: 400;\">(&#2328;&#2366;&#2340;&#2325;) - harmful or cancerous.</span><span style=\"font-weight: 400;\"><br></span><strong>Benign </strong><span style=\"font-weight: 400;\">(&#2360;&#2380;&#2350;&#2381;&#2351;) - harmless.</span></p>\r\n<p><strong>Favourable </strong><span style=\"font-weight: 400;\">(&#2309;&#2344;&#2369;&#2325;&#2370;&#2354;) - advantageous or positive.</span></p>\r\n<p><strong>Sterile </strong><span style=\"font-weight: 400;\">(&#2348;&#2366;&#2305;&#2333;) - lacking microorganisms or incapable of producing offspring.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Select the ANTONYM of the word dulcet to fill in the blank. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">He has _______ words for his opponents.</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">Select the ANTONYM of the word dulcet to fill in the blank. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">He has _______ words for his opponents.</span></p>\n",
                    options_en: ["<p>sweet</p>\n", "<p>penurious</p>\n", 
                                "<p>enliven</p>\n", "<p>harsh</p>\n"],
                    options_hi: ["<p>sweet</p>\n", "<p>penurious</p>\n",
                                "<p>enliven</p>\n", "<p>harsh</p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><strong>Harsh - </strong><span style=\"font-weight: 400;\">severe or unpleasant in manner or sound.</span></p>\r\n<p><strong>Dulcet - </strong><span style=\"font-weight: 400;\">sweet and soothing in sound or tone.</span></p>\r\n<p><strong>Sweet - </strong><span style=\"font-weight: 400;\">having a pleasant taste.</span></p>\r\n<p><strong>Penurious - </strong><span style=\"font-weight: 400;\">extremely poor.</span></p>\r\n<p><strong>Enliven - </strong><span style=\"font-weight: 400;\">make something more lively or cheerful.</span></p>\n",
                    solution_hi: "<p>9.(d)</p>\r\n<p><strong>Harsh </strong><span style=\"font-weight: 400;\">(&#2325;&#2352;&#2381;&#2325;&#2358;) -</span><strong> </strong><span style=\"font-weight: 400;\">severe or unpleasant in manner or sound.</span></p>\r\n<p><strong>Dulcet </strong><span style=\"font-weight: 400;\">(&#2350;&#2343;&#2369;&#2352;) -</span><strong> </strong><span style=\"font-weight: 400;\">sweet and soothing in sound or tone.</span></p>\r\n<p><strong>Sweet </strong><span style=\"font-weight: 400;\">(&#2350;&#2368;&#2336;&#2366;) - having a pleasant taste.</span></p>\r\n<p><strong>Penurious</strong><span style=\"font-weight: 400;\"> (&#2342;&#2352;&#2367;&#2342;&#2381;&#2352;) - extremely poor.</span></p>\r\n<p><strong>Enliven </strong><span style=\"font-weight: 400;\">(&#2332;&#2367;&#2306;&#2342;&#2366;&#2342;&#2367;&#2354;) - make something more lively or cheerful.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pernicious</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pernicious</span></p>\n",
                    options_en: ["<p>Malicious</p>\n", "<p>Wicked</p>\n", 
                                "<p>Injurious</p>\n", "<p>Compassionate</p>\n"],
                    options_hi: ["<p>Malicious</p>\n", "<p>Wicked</p>\n",
                                "<p>Injurious</p>\n", "<p>Compassionate</p>\n"],
                    solution_en: "<p>10.(d)</p>\r\n<p><strong>Compassionate - </strong><span style=\"font-weight: 400;\">showing sympathy and concern for others\' suffering.</span></p>\r\n<p><strong>Pernicious - </strong><span style=\"font-weight: 400;\">having a harmful effect, especially in a gradual way.</span></p>\r\n<p><strong>Malicious - </strong><span style=\"font-weight: 400;\">characterized by a desire to harm others.</span></p>\r\n<p><strong>Wicked -</strong><span style=\"font-weight: 400;\"> morally wrong, evil, or mischievous.</span></p>\r\n<p><strong>Injurious - </strong><span style=\"font-weight: 400;\">causing harm or damage.</span></p>\n",
                    solution_hi: "<p>10.(d)</p>\r\n<p><strong>Compassionate</strong><span style=\"font-weight: 400;\"> (&#2309;&#2344;&#2369;&#2325;&#2306;&#2346;&#2366;) -</span><strong> </strong><span style=\"font-weight: 400;\">showing sympathy and concern for others\' suffering.</span></p>\r\n<p><strong>Pernicious </strong><span style=\"font-weight: 400;\">(&#2328;&#2366;&#2340;&#2325;) - having a harmful effect, especially in a gradual way.</span></p>\r\n<p><strong>Malicious </strong><span style=\"font-weight: 400;\">(&#2342;&#2381;&#2357;&#2375;&#2359;&#2346;&#2370;&#2352;&#2381;&#2339;) - characterized by a desire to harm others.</span></p>\r\n<p><strong>Wicked </strong><span style=\"font-weight: 400;\">(&#2342;&#2369;&#2359;&#2381;&#2335;) - morally wrong, evil, or mischievous.</span></p>\r\n<p><strong>Injurious </strong><span style=\"font-weight: 400;\">(&#2361;&#2366;&#2344;&#2367;&#2325;&#2366;&#2352;&#2325;) - causing harm or damage.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word from the following sentence. Suspicious</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Although the web server was problematic, Sudha was certain that she will submit her form on the portal by evening.</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word from the following sentence. Suspicious</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Although the web server was problematic, Sudha was certain that she will submit her form on the portal by evening</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    options_en: ["<p>certain</p>\n", "<p>problematic</p>\n", 
                                "<p>submit</p>\n", "<p>portal</p>\n"],
                    options_hi: ["<p>certain</p>\n", "<p>problematic</p>\n",
                                "<p>submit</p>\n", "<p>portal</p>\n"],
                    solution_en: "<p>11.(a)</p>\r\n<p><strong>Certain - </strong><span style=\"font-weight: 400;\">having no doubt; sure.</span></p>\r\n<p><strong>Suspicious - </strong><span style=\"font-weight: 400;\">feeling doubt or uncertainty about something.</span></p>\r\n<p><strong>Problematic -</strong><span style=\"font-weight: 400;\"> involving problems or difficulties.</span></p>\r\n<p><strong>Submit - </strong><span style=\"font-weight: 400;\">to present or hand over something for a decision or approval.</span></p>\r\n<p><strong>Portal - </strong><span style=\"font-weight: 400;\">a doorway or entrance.</span></p>\n",
                    solution_hi: "<p>11.(a)</p>\r\n<p><strong>&nbsp;Certain </strong><span style=\"font-weight: 400;\">(&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;) -</span><strong> </strong><span style=\"font-weight: 400;\">having no doubt; sure.</span></p>\r\n<p><strong>Suspicious </strong><span style=\"font-weight: 400;\">(&#2360;&#2306;&#2342;&#2367;&#2327;&#2381;&#2343;) -</span><strong> </strong><span style=\"font-weight: 400;\">feeling doubt or uncertainty about something.</span></p>\r\n<p><strong>Problematic </strong><span style=\"font-weight: 400;\">(&#2360;&#2350;&#2360;&#2381;&zwj;&#2351;&#2366;&#2346;&#2370;&#2352;&#2381;&#2339;) - involving problems or difficulties.</span></p>\r\n<p><strong>Submit </strong><span style=\"font-weight: 400;\">(&#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2369;&#2340;&nbsp; &#2325;&#2352;&#2344;&#2366;) - to present or hand over something for a decision or approval.</span></p>\r\n<p><strong>Portal </strong><span style=\"font-weight: 400;\">(&#2342;&#2381;&#2357;&#2366;&#2352;) - a doorway or entrance.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the word given in brackets to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: \'Cambria Math\';\">sound of rain tapping against the window was a______ (stimulating) melody, lulling him to sleep.</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the word given in brackets to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: \'Cambria Math\';\">sound of rain tapping against the window was a______ (stimulating) melody, lulling him to sleep.</span></p>\n",
                    options_en: ["<p>Stressful</p>\n", "<p>Troubling</p>\n", 
                                "<p>Subduing</p>\n", "<p>Harsh</p>\n"],
                    options_hi: ["<p>Stressful</p>\n", "<p>Troubling</p>\n",
                                "<p>Subduing</p>\n", "<p>Harsh</p>\n"],
                    solution_en: "<p>12.(c)</p>\r\n<p><strong>Subduing - </strong><span style=\"font-weight: 400;\">bringing under control or calming.</span></p>\r\n<p><strong>Stimulating - </strong><span style=\"font-weight: 400;\">causing excitement or interest.</span></p>\r\n<p><strong>Stressful - </strong><span style=\"font-weight: 400;\">causing mental or emotional strain.</span></p>\r\n<p><strong>Troubling - </strong><span style=\"font-weight: 400;\">causing worry or concern.</span></p>\r\n<p><strong>Harsh - </strong><span style=\"font-weight: 400;\">severe or unpleasant.</span></p>\n",
                    solution_hi: "<p>12.(c)</p>\r\n<p><strong>Subduing </strong><span style=\"font-weight: 400;\">(&#2309;&#2343;&#2368;&#2344; &#2325;&#2352;&#2344;&#2366;) - bringing under control or calming.</span></p>\r\n<p><strong>Stimulating </strong><span style=\"font-weight: 400;\">(&#2313;&#2340;&#2381;&#2340;&#2375;&#2332;&#2344;&#2366;&#2346;&#2370;&#2352;&#2381;&#2339;) -</span><strong> </strong><span style=\"font-weight: 400;\">causing excitement or interest.</span></p>\r\n<p><strong>Stressful </strong><span style=\"font-weight: 400;\">(&#2340;&#2344;&#2366;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339;) -</span><strong> </strong><span style=\"font-weight: 400;\">causing mental or emotional strain.</span></p>\r\n<p><strong>Troubling</strong><span style=\"font-weight: 400;\"> (&#2346;&#2352;&#2375;&#2358;&#2366;&#2344;) -</span><strong> </strong><span style=\"font-weight: 400;\">causing worry or concern.</span></p>\r\n<p><strong>Harsh </strong><span style=\"font-weight: 400;\">(&#2309;&#2346;&#2381;&#2352;&#2367;&#2351;/&#2325;&#2336;&#2379;&#2352;) - severe or unpleasant.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Witty</span></strong></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Witty</span></strong></p>\n",
                    options_en: ["<p>Serious</p>\n", "<p>Cynical</p>\n", 
                                "<p>Callous</p>\n", "<p>Giddy</p>\n"],
                    options_hi: ["<p>Serious</p>\n", "<p>Cynical</p>\n",
                                "<p>Callous</p>\n", "<p>Giddy</p>\n"],
                    solution_en: "<p>13.(a)<span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><strong>Serious</strong><span style=\"font-weight: 400;\">- focused and not playful or joking.</span><span style=\"font-weight: 400;\"><br></span><strong>Witty</strong><span style=\"font-weight: 400;\">- clever and funny with quick remarks.</span></p>\r\n<p><strong>Cynical</strong><span style=\"font-weight: 400;\">- not trusting or respecting the goodness of other people and their actions.</span></p>\r\n<p><strong>Callous</strong><span style=\"font-weight: 400;\">- one who is insensitive or not showing concern to others&rsquo; feelings.</span></p>\r\n<p><strong>Giddy</strong><span style=\"font-weight: 400;\">- to be excessively happy or excited.</span></p>\n",
                    solution_hi: "<p>13.(a)<span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;</span><strong>Serious </strong><span style=\"font-weight: 400;\">(&#2327;&#2306;&#2349;&#2368;&#2352;) - focused and not playful or joking.</span><span style=\"font-weight: 400;\"><br></span><strong>Witty </strong><span style=\"font-weight: 400;\">(&#2350;&#2332;&#2366;&#2325;&#2367;&#2351;&#2366;/&#2346;&#2352;&#2367;&#2361;&#2366;&#2360; &#2351;&#2369;&#2325;&#2381;&#2340;) - clever and funny with quick remarks.</span></p>\r\n<p><strong>Cynical </strong><span style=\"font-weight: 400;\">(&#2350;&#2366;&#2344;&#2357;&#2342;&#2381;&#2357;&#2375;&#2359;&#2368;) - not trusting or respecting the goodness of other people and their actions.</span></p>\r\n<p><strong>Callous </strong><span style=\"font-weight: 400;\">(&#2360;&#2306;&#2357;&#2375;&#2342;&#2344;&#2361;&#2368;&#2344;) - one who is insensitive or not showing concern to others&rsquo; feelings.</span></p>\r\n<p><strong>Giddy </strong><span style=\"font-weight: 400;\">(&#2330;&#2306;&#2330;&#2354;) - to be excessively happy or excited.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM to replace the underlined word in the following sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Darwin drew our attention to the gradual accumulation of </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">unfavourable</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"> </span>traits and its direct relationship with the survival of those presumed lucky species, which then serve as breeding grounds for more desirable traits.</span></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM to replace the underlined word in the following sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Darwin drew our attention to the gradual accumulation of </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">unfavourable</span></span><span style=\"font-family: Cambria Math;\"> traits and its direct relationship with the survival of thos</span><span style=\"font-family: Cambria Math;\">e presumed lucky species, which then serve as breeding grounds for more desirable traits.</span></p>\n",
                    options_en: ["<p>advantageous</p>\n", "<p>fulfilling</p>\n", 
                                "<p>steady</p>\n", "<p>negative</p>\n"],
                    options_hi: ["<p>advantageous</p>\n", "<p>fulfilling</p>\n",
                                "<p>steady</p>\n", "<p>negative</p>\n"],
                    solution_en: "<p>14.(a)</p>\r\n<p><strong>Advantageous</strong></p>\r\n<p><strong>Unfavorable</strong><span style=\"font-weight: 400;\">- something that is not in your favor, negative, or disadvantageous.</span></p>\r\n<p><strong>Fulfilling</strong><span style=\"font-weight: 400;\">- something that is satisfying.&nbsp;</span></p>\r\n<p><strong>Steady</strong><span style=\"font-weight: 400;\">- something that is stable, constant.</span></p>\n",
                    solution_hi: "<p>14.(a)</p>\r\n<p><strong>Advantageous </strong><span style=\"font-weight: 400;\">(&#2354;&#2366;&#2349;&#2342;&#2366;&#2351;&#2325;)&nbsp;</span></p>\r\n<p><strong>Unfavorable </strong><span style=\"font-weight: 400;\">(&#2346;&#2381;&#2352;&#2340;&#2367;&#2325;&#2370;&#2354;) - something that is not in your favor, negative, or disadvantageous.</span></p>\r\n<p><strong>Fulfilling </strong><span style=\"font-weight: 400;\">(&#2360;&#2306;&#2340;&#2379;&#2359;&#2346;&#2381;&#2352;&#2342;) - something that is satisfying.&nbsp;</span></p>\r\n<p><strong>Steady </strong><span style=\"font-weight: 400;\">(&#2360;&#2381;&#2341;&#2367;&#2352;) - something that is stable, constant.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Select the appropriate ANTONYM of the underlined word to fill in the blank. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Our administration uses ________</span><span style=\"font-family: Cambria Math;\">_ techniques to get their work done. They do not rely on </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">overt</span></span><span style=\"font-family: Cambria Math;\"> tactics. </span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">Select the appropriate ANTONYM of the underlined word to fill in the blank. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Our administration uses _________ techniques to get their work done. They do not rely on<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">overt</span></span><span style=\"font-family: Cambria Math;\"> tactics. </span></p>\n",
                    options_en: ["<p>covert</p>\n", "<p>apparent</p>\n", 
                                "<p>clear</p>\n", "<p>patent</p>\n"],
                    options_hi: ["<p>covert</p>\n", "<p>apparent</p>\n",
                                "<p>clear</p>\n", "<p>patent</p>\n"],
                    solution_en: "<p>15.(a)<span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;</span><strong>Covert</strong><span style=\"font-weight: 400;\">- hidden or not easily seen.</span></p>\r\n<p><strong>Overt</strong><span style=\"font-weight: 400;\">- open and observable, not hidden.</span></p>\r\n<p><strong>Apparent</strong><span style=\"font-weight: 400;\">- easily seen or understood; clear or obvious.</span></p>\r\n<p><strong>Clear</strong><span style=\"font-weight: 400;\">- easily seen, heard, or understood.</span></p>\r\n<p><strong>Patent</strong><span style=\"font-weight: 400;\">- an official right to be the only person or company allowed to make or sell a new product.</span></p>\n",
                    solution_hi: "<p>15.(a)<span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><strong>Covert</strong><span style=\"font-weight: 400;\"> (&#2327;&#2369;&#2346;&#2381;&#2340;) - hidden or not easily seen.</span></p>\r\n<p><strong>Overt </strong><span style=\"font-weight: 400;\">(&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2325;&#2381;&#2359;) - open and observable, not hidden.</span></p>\r\n<p><strong>Apparent </strong><span style=\"font-weight: 400;\">(&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;) - easily seen or understood; clear or obvious.</span></p>\r\n<p><strong>Clear </strong><span style=\"font-weight: 400;\">(&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;) - easily seen, heard, or understood.</span></p>\r\n<p><strong>Patent </strong><span style=\"font-weight: 400;\">(&#2319;&#2325;&#2360;&#2381;&#2357; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;) - an official right to be the only person or company allowed to make or sell a new product.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>