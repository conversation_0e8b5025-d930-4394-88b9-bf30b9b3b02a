<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">Rohit buys an old bat for Rs. 1280 and spends Rs. 120 on its repairing. If he sells it for Rs. 1050, then what is the loss percentage ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Kokila;\">&#2352;&#2379;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> Rs. 1280 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2352;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2354;&#2381;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2352;&#2350;&#2381;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> Rs. 120 </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> Rs. 1050 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>40 %</p>\n", "<p>25 %</p>\n", 
                                "<p>20 %</p>\n", "<p>30 %</p>\n"],
                    options_hi: ["<p>40 %</p>\n", "<p>25 %</p>\n",
                                "<p>20 %</p>\n", "<p>30 %</p>\n"],
                    solution_en: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Total Cost price of bat = 1280 + 120 </span><span style=\"font-family: \'Cambria Math\';\">= Rs. 1400</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Selling price = Rs. 1050</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Loss % =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1400</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1050</mn></mrow><mn>1400</mn></mfrac></math> &times; 100&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 25 %</span></p>\n",
                    solution_hi: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2348;&#2354;&#2381;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 1280 + 120 </span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Kokila;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">. 1400</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = &#2352;&#2369;. 1050</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2361;&#2366;&#2344;&#2367; </span><span style=\"font-family: Cambria Math;\">% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1400</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1050</mn></mrow><mn>1400</mn></mfrac></math> &times; 100&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 25 %</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">A refrigerator and a washing machine were sold for Rs. 24000 each. If the refrigerator was sold at a gain of 20% and the washing machine at a loss of 20%. What is the overall profit or loss percent in the whole transaction?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2375;&#2347;&#2381;&#2352;&#2367;&#2332;&#2352;&#2375;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2377;&#2358;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> Rs. 24000 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2375;&#2347;&#2381;&#2352;&#2367;&#2332;&#2352;&#2375;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2377;&#2358;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">&#2342;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>4% profit</p>\n", "<p>5% profit</p>\n", 
                                "<p>4% loss</p>\n", "<p>5% loss</p>\n"],
                    options_hi: ["<p>4% <span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>5% <span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span></p>\n",
                                "<p>4% <span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>5% <span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n"],
                    solution_en: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Net profit / loss % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>20</mn><mo>)</mo></mrow><mn>2</mn></msup><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 4 % loss </span></p>\n",
                    solution_hi: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>20</mn><mo>)</mo></mrow><mn>2</mn></msup><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 4 % </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">Shivam purchased two watches, first for Rs. 12000 and the second for Rs. 10000. He sold both the watches, first one at the profit of 10 percent and the second at a loss of 10 percent. What is the overall profit or loss?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Kokila;\">&#2358;&#2367;&#2357;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2337;&#2364;&#2367;&#2351;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2368;&#2342;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> Rs. 12000 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> Rs.10000 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>2.25 percent profit</p>\n", "<p>2.25 percent loss</p>\n", 
                                "<p>0.909 percent profit</p>\n", "<p>0.909 percent loss</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>2.25 <span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>2.25 <span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n",
                                "<p>0.909 <span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span></p>\n", "<p>0.909 <span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n"],
                    solution_en: "<p>3.(c) <span style=\"font-family: Cambria Math;\">Total Cost price of the watches = 12000 + 10000 = Rs. 22000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total selling price of the watches = 12000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + 10000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 13200 + 9000 = Rs. 22200</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit % =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>22200</mn><mo>-</mo><mn>22000</mn></mrow><mn>22000</mn></mfrac></math> &times; 100 </span><span style=\"font-family: \'Cambria Math\';\">= 0.909 %</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>3.(c) <span style=\"font-family: Kokila;\">&#2328;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 12000 + 10000 = </span><span style=\"font-family: Kokila;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">. 22000</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2328;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2325;&#2369;&#2354; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;&nbsp;</span><span style=\"font-family: Cambria Math;\">= 12000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + 10000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-weight: 400;\">= 13200 + 9000 = &#2352;&#2369;. 22200</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2354;&#2366;&#2349; %&nbsp;</span><span style=\"font-family: Cambria Math;\">&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>22200</mn><mo>-</mo><mn>22000</mn></mrow><mn>22000</mn></mfrac></math> &times; 100 </span><span style=\"font-family: \'Cambria Math\';\">= 0.909 %</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Ajay buys a scooter for Rs. 7560 and spends Rs. 540 on its repair. He sells it for Rs. 9000. What is his gain percent?</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Kokila;\">&#2309;&#2332;&#2351;</span><span style=\"font-family: Cambria Math;\"> Rs. 7560 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2325;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2352;&#2350;&#2381;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> Rs. 540 </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> Rs. 9000 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>8.33%</p>\n", "<p>9.09%</p>\n", 
                                "<p>11.11%</p>\n", "<p>6.25%</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>8.33%</p>\n", "<p>9.09%</p>\n",
                                "<p>11.11%</p>\n", "<p>6.25%</p>\n"],
                    solution_en: "<p>4.(c) <span style=\"font-family: Cambria Math;\">Cost price of scooter </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= (7560 + 540) = Rs. 8100</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Gain % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8100</mn></mrow><mn>8100</mn></mfrac></math>&times; 100</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 11.11 %</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>4.(c)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2325;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = (7560 + 540)&nbsp; </span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Kokila;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">. 8100</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; </span><span style=\"font-family: Cambria Math;\">&nbsp;% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8100</mn></mrow><mn>8100</mn></mfrac></math>&times; 100</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 11.11 %</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">A fruit seller sells</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">th part of fruits at a profit of 10% and remaining at a loss of 5%. If the total profit is Rs.1850, then what is the total cost price of fruits?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2347;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 5% </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> Rs. 1850 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2347;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs.198000</p>\n", "<p>Rs.205000</p>\n", 
                                "<p>Rs.185000</p>\n", "<p>Rs.176000</p>\n"],
                    options_hi: ["<p>Rs.198000</p>\n", "<p>Rs.205000</p>\n",
                                "<p>Rs.185000</p>\n", "<p>Rs.176000</p>\n"],
                    solution_en: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio &rarr;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp;quantity A&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;quantity B</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Quantity &rarr;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">P/L %&rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; + 10%&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;- </span><span style=\"font-family: Cambria Math;\">5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Overall profit/ loss = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>-</mo><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 1% (profit)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1% = &#8377; 1850</span></p>\r\n<p><span style=\"font-weight: 400;\">(Total C.P of fruits)</span>100% = 1850 &times; 100 <span style=\"font-family: Cambria Math;\">= &#8377; 185000</span></p>\n",
                    solution_hi: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style=\"font-family: Kokila;\">&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;</span> A&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> B</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2340;&#2381;&#2352;&#2366; <span style=\"font-family: Cambria Math;\">&rarr;</span></span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 3</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> % &rarr;</span><span style=\"font-family: Cambria Math;\"> + 10%&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;- </span><span style=\"font-family: Cambria Math;\">5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Overall profit/ loss = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>-</mo><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 1% (profit)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1% = &#8377; 1850</span></p>\r\n<p><span style=\"font-weight: 400;\">(&#2347;&#2354;&#2379;&#2306; &#2325;&#2366; &#2325;&#2369;&#2354; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;)</span>100% <span style=\"font-family: \'Cambria Math\';\">= 1850 &times; 100 = &#8377; 185000</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">Selling price of an article is Rs. 520. If loss percentage is 20 percent, then what is the cost price of article?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> Rs.520 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs.600</p>\n", "<p>Rs.650</p>\n", 
                                "<p>Rs.700</p>\n", "<p>Rs.720</p>\n"],
                    options_hi: ["<p>Rs.600</p>\n", "<p>Rs.650</p>\n",
                                "<p>Rs.700</p>\n", "<p>Rs.720</p>\n"],
                    solution_en: "<p>6.(b) <span style=\"font-family: Cambria Math;\">C.P of the article&nbsp; </span><span style=\"font-family: Cambria Math;\">= 520 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= &#8377; 650</span></p>\n",
                    solution_hi: "<p>6.(b)</p>\r\n<p><span style=\"font-weight: 400;\">&#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; </span><span style=\"font-family: Cambria Math;\">= 520 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= &#8377; 650</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> A person earns a profit of 30 percent by selling an article for Rs. 325. How much percent should he increase his selling price to earn a profit of 40 percent?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> Rs. 325 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2330;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> 30 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> 40 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\n",
                    options_en: ["<p>6.66 percent</p>\n", "<p>8.25 percent</p>\n", 
                                "<p>7.69 percent</p>\n", "<p>5.25 percent</p>\n"],
                    options_hi: ["<p>6.66 <span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n", "<p>8.25 <span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>7.69 <span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>5.25 <span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n"],
                    solution_en: "<p>7.(c) <span style=\"font-family: Cambria Math;\">Selling price = </span><span style=\"font-family: Cambria Math;\">Rs. 325</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cost price of the article&nbsp; </span><span style=\"font-family: Cambria Math;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>325</mn><mo>&times;</mo><mfrac><mn>100</mn><mn>130</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = Rs. 250</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For the profit of 40% </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New Selling price = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>250</mn><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>140</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = Rs. 350 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>350</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>325</mn></mrow><mn>325</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 7.69%</span></p>\n",
                    solution_hi: "<p>7.(c) <span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Kokila;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">. 325</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351;</span> &nbsp;</span><span style=\"font-family: Cambria Math;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>325</mn><mo>&times;</mo><mfrac><mn>100</mn><mn>130</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = Rs. 250</span></p>\r\n<p><span style=\"font-weight: 400;\">40% &#2325;&#2375; &#2354;&#2366;&#2349; &#2325;&#2375; &#2354;&#2367;&#2319;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2344;&#2351;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>250</mn><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>140</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = Rs. 350</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; </span>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>350</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>325</mn></mrow><mn>325</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 7.69%</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">A man sold his bike for Rs. 18,600 at 55% profit. At what price would he have sold to incur a loss of 10%?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2311;&#2325;</span><span style=\"font-family: Cambria Math;\"> 55% </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> Rs. 18,600 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2330;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2311;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>Rs. 10,800</p>\n", "<p>Rs. 15,000</p>\n", 
                                "<p>Rs. 11,200</p>\n", "<p>Rs. 16,600</p>\n"],
                    options_hi: ["<p>Rs. 10,800</p>\n", "<p>Rs. 15,000</p>\n",
                                "<p>Rs. 11,200</p>\n", "<p>Rs. 16,600</p>\n"],
                    solution_en: "<p>8.(a) <span style=\"font-family: Cambria Math;\">Required price&nbsp; </span><span style=\"font-family: Cambria Math;\">=<span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">18600 &times;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>155</mn></mfrac><mo>&times;</mo><mfrac><mn>90</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= Rs. 10,800</span></p>\n",
                    solution_hi: "<p>8.(a) <span style=\"font-family: Kokila;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\">=<span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">18600 &times;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>155</mn></mfrac><mo>&times;</mo><mfrac><mn>90</mn><mn>100</mn></mfrac></math> &nbsp;</span><span style=\"font-family: Cambria Math;\">= <span style=\"font-weight: 400;\">&#2352;&#2369;.</span>10,800</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">A trader allowed a discount of 30 percent on a trolley bag having list price of Rs. 1800 and earns a profit of 20 percent. What is the cost price of the trolley bag?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> Rs. 1800 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2335;&#2381;&#2352;&#2377;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2376;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 30 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2335;&#2381;&#2352;&#2377;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2376;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs. 1050</p>\n", "<p>Rs. 1230</p>\n", 
                                "<p>Rs. 1440 <span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Rs. 1160</p>\n"],
                    options_hi: ["<p>Rs. 1050</p>\n", "<p>Rs. 1230</p>\n",
                                "<p>Rs. 1440</p>\n", "<p>Rs. 1160</p>\n"],
                    solution_en: "<p>9.(a) Formula used : -</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>&nbsp;</mo><mo>%</mo></mrow><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>c</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi><mo>&nbsp;</mo><mo>%</mo></mrow></mfrac></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1800</mn><mrow><mi>C</mi><mi>P</mi><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>20</mn></mrow><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>30</mn></mrow></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;CP&nbsp;<span style=\"font-weight: 400;\"> &nbsp;</span>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1800</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>70</mn></mrow><mn>120</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = Rs. 1050</span></p>\n",
                    solution_hi: "<p>9.(a)&nbsp; &#2346;&#2381;&#2352;&#2351;&#2369;&#2325;&#2381;&#2340; &#2360;&#2370;&#2340;&#2381;&#2352; :</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&nbsp;</mo></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>&#2354;&#2366;&#2349;</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>&#2331;&#2370;&#2335;</mi><mo>&nbsp;</mo><mo>%</mo></mrow></mfrac></math></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1800</mn><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>20</mn></mrow><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>30</mn></mrow></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;&nbsp; &nbsp;</span>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1800</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>70</mn></mrow><mn>120</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = <span style=\"font-weight: 400;\">&#2352;&#2369;.&nbsp; </span>1050</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Cambria Math;\">A shopkeeper sells a book at a loss of 60 percent. Had he sold the book for Rs. 150 more, then he would have suffered a loss of 30 percent. To earn a profit of 20 percent, what should be the selling price of the book?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 60 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> Rs. 150 </span><span style=\"font-family: Kokila;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 30 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2340;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2350;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs. 650</p>\n", "<p>Rs. 600</p>\n", 
                                "<p>Rs. 700</p>\n", "<p>Rs. 550</p>\n"],
                    options_hi: ["<p>Rs. 650</p>\n", "<p>Rs. 600</p>\n",
                                "<p>Rs. 700</p>\n", "<p>Rs. 550</p>\n"],
                    solution_en: "<p>10.(b) <span style=\"font-family: Cambria Math;\">Cost price of book is 100%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to question,</span></p>\r\n<p><span style=\"font-weight: 400;\">(60</span><span style=\"font-weight: 400;\"> -</span><span style=\"font-weight: 400;\"> 30)</span>% = 150</p>\r\n<p><span style=\"font-family: Cambria Math;\">Cost Price of book = 150 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>30</mn></mfrac></math> = Rs. 500</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; Profit = 20%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Selling price = 500 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math>= Rs.600</span></p>\n",
                    solution_hi: "<p>10.(b) <span style=\"font-family: Kokila;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 100% </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-weight: 400;\">(60</span><span style=\"font-weight: 400;\"> -</span><span style=\"font-weight: 400;\"> 30)</span>% = 150</p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2325;&#2366; &#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; </span>&nbsp;= 150 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>30</mn></mfrac></math> </span><span style=\"font-weight: 400;\">= &#2352;&#2369;. 500</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; = 20%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; </span>&nbsp;= 500 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math>= <span style=\"font-weight: 400;\">&nbsp;&#2352;&#2369;.</span>600</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">Cost price of a watch is Rs. 750. If the loss percentage is 40 percent, then what is the value of loss?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> Rs.750 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> 40 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs. 300</p>\n", "<p>Rs. 280</p>\n", 
                                "<p>Rs. 420</p>\n", "<p>Rs. 220</p>\n"],
                    options_hi: ["<p>Rs. 300</p>\n", "<p>Rs. 280</p>\n",
                                "<p>Rs. 420</p>\n", "<p>Rs. 220</p>\n"],
                    solution_en: "<p>11.(a) <span style=\"font-family: Cambria Math;\">According to question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Loss = 750 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= Rs. 300</span></p>\n",
                    solution_hi: "<p>11.(a) <span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2361;&#2366;&#2344;&#2367; </span>= 750 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= <span style=\"font-weight: 400;\">&#2352;&#2369;. </span>&nbsp;300</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">Selling price of a glass is Rs. 1287 and loss percentage is 22 percent. If selling price is Rs. 1848, then what will be the profit percentage?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2367;&#2354;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> Rs. 1287 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> 22 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> Rs.1848 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>12 % <span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>15 %</p>\n", 
                                "<p>10 %</p>\n", "<p>8 %</p>\n"],
                    options_hi: ["<p>12 %</p>\n", "<p>15 %<span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>10 %</p>\n", "<p>8 %</p>\n"],
                    solution_en: "<p>12.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;C.P&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; S.P</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Amount&rarr;&nbsp; &nbsp; 100x</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;78x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">78x</span><span style=\"font-family: Cambria Math;\"> = &#8377; 1287</span></p>\r\n<p><span style=\"font-weight: 400;\">(C.P) 100</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>1287</mn><mn>78</mn></mfrac><mo>&nbsp;</mo></math><span style=\"font-weight: 400;\">&times; 100 = &#8377; 1650</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required percentage =</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1848</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1650</mn></mrow><mn>1650</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 100 = 12%</span></p>\n",
                    solution_hi: "<p>12.(a)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> &rarr;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> &rarr; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-weight: 400;\">100</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\">&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 78</span><span style=\"font-weight: 400;\">x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">78x</span><span style=\"font-family: Cambria Math;\"> = &#8377; 1287</span></p>\r\n<p><span style=\"font-weight: 400;\">(&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;) 100</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>1287</mn><mn>78</mn></mfrac><mo>&nbsp;</mo></math><span style=\"font-weight: 400;\">&times; 100 = &#8377; 1650</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span> =</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1848</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1650</mn></mrow><mn>1650</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 100 = 12%</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">If 2 times of selling price is equal of 3 times of cost price, then what is the profit percentage?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Kokila;\">&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Kokila;\">&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>4 %</p>\n", "<p>40 % <span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>25 %</p>\n", "<p>50 %</p>\n"],
                    options_hi: ["<p>4 %</p>\n", "<p>40 %</p>\n",
                                "<p>25 %<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>50 %</p>\n"],
                    solution_en: "<p>13.(d) <span style=\"font-family: Cambria Math;\">2 &times; S.P = 3 &times; C.P</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mo>.</mo><mi>P</mi></mrow><mrow><mi>C</mi><mo>.</mo><mi>P</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit % =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 100 = 50%</span></p>\n",
                    solution_hi: "<p>13.(d)&nbsp; <strong>&nbsp;</strong><span style=\"font-weight: 400;\">2 &times; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;&nbsp; = 3 &times; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; </span>% =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 100 = 50%</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">A single discount of 48 percent is given on Rs. 1080, If two successive discounts of 25 percent and 20 percent are given, then what will be the difference between both the selling prices?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">Rs. 1080 </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 48 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2335;&#2381;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 25 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2350;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2335;&#2381;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs. 72.5</p>\n", "<p>Rs. 65.8</p>\n", 
                                "<p>Rs. 80.8</p>\n", "<p>Rs. 86.4</p>\n"],
                    options_hi: ["<p>Rs. 72.5</p>\n", "<p>Rs. 65.8</p>\n",
                                "<p>Rs. 80.8</p>\n", "<p>Rs. 86.4</p>\n"],
                    solution_en: "<p>14.(d) <span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1080 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = &#8377; 561.6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1080 &times; </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = &#8377; 648</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required difference = 648 - </span><span style=\"font-family: Cambria Math;\">561.6 =&nbsp; </span><span style=\"font-family: Cambria Math;\">&#8377; 86.4</span></p>\n",
                    solution_hi: "<p>14.(d) <span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1080 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = &#8377; 561.6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1080 &times; </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = &#8377; 648</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 648 - </span><span style=\"font-family: Cambria Math;\">561.6 = &#8377; 86.4 </span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">The marked price of one pen is Rs. 20. What will be the total selling price (in Rs) of 5 such pens after the discount of 5% ?</span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> Rs. 20 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> 5% </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2335;&#2381;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2320;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Kokila;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">(Rs </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> ) </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\n",
                    options_en: ["<p>100 <span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>90</p>\n", 
                                "<p>95 <span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>105<span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>100 <span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>90 <span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>95 <span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>105<span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>15.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Selling price of 1 pen </span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\">20 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>95</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">=</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Rs.</span><span style=\"font-family: Cambria Math;\">19</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, S.P of 5 pens = 19 &times; 5 = Rs.95 </span></p>\n",
                    solution_hi: "<p>15.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Kokila;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 20 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>95</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Kokila;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">. 19</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, 5 </span><span style=\"font-family: Kokila;\">&#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 19 &times; 5 = </span><span style=\"font-family: Kokila;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">. 95 </span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>