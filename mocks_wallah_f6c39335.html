<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option that represents the letters which, when sequentially placed from left to right in the blanks below, will complete the letter series. <br>f h _ h _ g f _ h g _ h _ f f _ g _ h _ f</p>",
                    question_hi: "<p>1. उस विकल्प का चयन कीजिए, जो उन अक्षरों को दर्शाता है, जिन्हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ क्रमिक रूप से रखने पर अक्षर शृंखला पूरी होगी। <br>f h _ h _ g f _ h g _ h _ f f _ g _ h _ f</p>",
                    options_en: ["<p>g h f h g f f g</p>", "<p>g h f h g h f g</p>", 
                                "<p>g h f f g h f g</p>", "<p>g h f h g h h g</p>"],
                    options_hi: ["<p>g h f h g f f g</p>", "<p>g h f h g h f g</p>",
                                "<p>g h f f g h f g</p>", "<p>g h f h g h h g</p>"],
                    solution_en: "<p>1.(d)<br>f h <strong>g</strong> h <strong><span style=\"text-decoration: underline;\">h</span></strong> g f/ <span style=\"text-decoration: underline;\"><strong>f</strong></span> h g <span style=\"text-decoration: underline;\"><strong>h</strong></span> h <span style=\"text-decoration: underline;\"><strong>g</strong></span> f/ f <span style=\"text-decoration: underline;\"><strong>h</strong></span> g <span style=\"text-decoration: underline;\"><strong>h</strong></span> h <span style=\"text-decoration: underline;\"><strong>g</strong></span> f</p>",
                    solution_hi: "<p>1.(d)<br>f h <strong>g</strong> h <strong><span style=\"text-decoration: underline;\">h</span></strong> g f/ <span style=\"text-decoration: underline;\"><strong>f</strong></span> h g <span style=\"text-decoration: underline;\"><strong>h</strong></span> h <span style=\"text-decoration: underline;\"><strong>g</strong></span> f/ f <span style=\"text-decoration: underline;\"><strong>h</strong></span> g <span style=\"text-decoration: underline;\"><strong>h</strong></span> h <span style=\"text-decoration: underline;\"><strong>g</strong></span> f</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Which of the following terms will replace the question mark (?) in the given series?<br>RLYV, PJWT, NHUR, ? , JDQN</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगा?<br>RLYV, PJWT, NHUR, ? , JDQN</p>",
                    options_en: ["<p>LFTP</p>", "<p>LGTP</p>", 
                                "<p>LGSP</p>", "<p>LFSP</p>"],
                    options_hi: ["<p>LFTP</p>", "<p>LGTP</p>",
                                "<p>LGSP</p>", "<p>LFSP</p>"],
                    solution_en: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060010564.png\" alt=\"rId6\" width=\"320\" height=\"74\"></p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060010564.png\" alt=\"rId6\" width=\"320\" height=\"74\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, <br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;, <br>&lsquo;A - B&rsquo; means &lsquo;A is the father of B&rsquo;,<br>&lsquo;A $ B&rsquo; means &lsquo;A is the son of B&rsquo;, <br>&lsquo;A % B&rsquo; means &lsquo;A is the sister of B&rsquo;, <br>&lsquo;A / B&rsquo; means &lsquo;A is the wife of B&rsquo; and <br>&lsquo;A * B&rsquo; means &lsquo;A is the husband of B&rsquo;.<br>Which of the following means P is the Son&rsquo;s wife of T? <br>i. T / B &ndash; K * P + O <br>ii. T + K * P + B % O<br>iii. K * P + B % T + P<br>iv. P + T * K + B % O</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में,<br>&lsquo;A + B&rsquo; का अर्थ है &lsquo;A, B की माँ है&rsquo;, <br>&lsquo;A &ndash; B&rsquo; का अर्थ है &lsquo;A, B का पिता है&rsquo;, <br>&lsquo;A $ B&rsquo; का अर्थ है &lsquo;A, B का पुत्र है&rsquo;, <br>&lsquo;A % B&rsquo; का अर्थ है &lsquo;A, B की बहन है&rsquo;, <br>&lsquo;A / B&rsquo; का अर्थ है &lsquo;A, B की पत्नी है&rsquo; और <br>&lsquo;A * B&rsquo; का अर्थ है &lsquo;A, B का पति है&rsquo;। <br>निम्नलिखित में से किसका अर्थ है कि P, T की पुत्रवधू है ?<br>i. T / B &ndash; K * P + O <br>ii. T + K * P + B % O<br>iii. K * P + B % T + P<br>iv. P + T * K + B % O</p>",
                    options_en: ["<p>Only i</p>", "<p>Both i and ii</p>", 
                                "<p>iii and iv</p>", "<p>Only ii</p>"],
                    options_hi: ["<p>केवल i</p>", "<p>i और ii दोनों</p>",
                                "<p>iii और iv</p>", "<p>केवल ii</p>"],
                    solution_en: "<p>3.(b)<br>After going through all the options satisfy only (i) and (ii).<br>(i) <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060010744.png\" alt=\"rId7\" width=\"96\" height=\"149\"> (ii)&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060010886.png\" alt=\"rId8\" width=\"108\" height=\"153\"></p>",
                    solution_hi: "<p>3.(b)<br>सभी विकल्पों पर विचार करने के बाद केवल (i) और (ii) को संतुष्ट करते है।.<br>(i) <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060010744.png\" alt=\"rId7\" width=\"96\" height=\"149\"> (ii)&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060010886.png\" alt=\"rId8\" width=\"108\" height=\"153\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the triad in which the numbers are related to each other in the same way as the numbers in the following triads. <br>16 - 40 - 100<br>8 - 20 - 50<br>(<strong>NOTE: </strong>Operation should be performed on the whole numbers, Without breaking down the numbers into its constituent digits. E.g. 13- Operation on 13 such as adding/multiplying itc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>4. उस त्रिक का चयन करें जिसमें दी गई संख्याएं एक - दूसरे से उसी प्रकार संबंधित हैं, जिस प्रकार निम्&zwj;नलिखित त्रिकों की संख्याएं एक - दूसरे से संबंधित हैं। <br>16 - 40 - 100<br>8 - 20 - 50<br><strong>नोट: </strong>संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>12-30-120</p>", "<p>18-50-110</p>", 
                                "<p>22-74-185</p>", "<p>24-60-150</p>"],
                    options_hi: ["<p>12-30-120</p>", "<p>18-50-110</p>",
                                "<p>22-74-185</p>", "<p>24-60-150</p>"],
                    solution_en: "<p>4.(d) <strong>Logic: </strong>1st number &times; 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>st</mi><mi mathvariant=\"normal\">&#160;</mi><mi>number</mi></mrow><mn>2</mn></mfrac></math> = 2nd number,&nbsp;2nd number &times; 2 +&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>nd</mi><mi mathvariant=\"normal\">&#160;</mi><mi>number</mi></mrow><mn>3</mn></mfrac></math> = 3rd number<br>16 - 40 - 100 :- 16 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 40, 40 &times; 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math> = 100<br>8 - 20 - 50 :- 8 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 20, 20 &times; 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>2</mn></mfrac></math> = 50<br>Similarly<br>24-60-150 :- 24 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 60, 60 &times; 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>2</mn></mfrac></math> = 150</p>",
                    solution_hi: "<p>4.(d) तर्क: पहला नंबर &times; 2 + <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2366;</mi><mo>&#160;</mo><mi>&#2344;&#2306;&#2348;&#2352;</mi></mrow><mn>2</mn></mfrac></math> = दूसरा नंबर, दूसरा नंबर &times; 2 + <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2360;&#2352;&#2366;</mi><mo>&#160;</mo><mi>&#2344;&#2306;&#2348;&#2352;</mi></mrow><mn>3</mn></mfrac></math> = तीसरा नंबर<br>16 - 40 - 100 :- 16 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 40, 40 &times; 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math> = 100<br>8 - 20 - 50 :- 8 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 20, 20 &times; 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>2</mn></mfrac></math> = 50<br>उसी प्रकार<br>24 - 60 - 150 :- 24 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 60, 60 &times; 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>2</mn></mfrac></math> = 150</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Six symbols %, &amp;, ?, +, $ and @ are written on different faces of a dice. Two positions of this dice are sown in the figure below. Find the symbol on the face opposite to the one having @.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060011042.png\" alt=\"rId9\" width=\"165\" height=\"81\"></p>",
                    question_hi: "<p>5. एक पासे के विभिन्न फलकों पर छह प्रतीक %, &amp;, ?, +, $ और @ अंकित हैं। नीचे दी गई आकृति में इस पासे की दो स्थितियाँ दर्शाई गई हैं। @ वाले फलक के विपरीत फलक पर प्रतीक ज्ञात कीजिए ।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060011042.png\" alt=\"rId9\" width=\"165\" height=\"81\"></p>",
                    options_en: ["<p>$</p>", "<p>&amp;</p>", 
                                "<p>%</p>", "<p>?</p>"],
                    options_hi: ["<p>$</p>", "<p>&amp;</p>",
                                "<p>%</p>", "<p>?</p>"],
                    solution_en: "<p>5.(d) From both the dice the opposite face are <br>&amp; &harr; $ , % &harr; + , ? &harr; @</p>",
                    solution_hi: "<p>5.(d) दोनों पासों के विपरीत फलक हैं <br>&amp; &harr; $ , % &harr; + , ? &harr; @</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Read the given statements and conclusions carefully. Assuming that the information given in the information give in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements. <br><strong>Statements:</strong> <br>Some Toys are Plastic.<br>Some Plastic are Metal. <br>No Metal is Chemical. <br><strong>Conclusions:</strong> <br>(I) Some Chemicals are Plastic. <br>(II) No Toy is a Chemical.</p>",
                    question_hi: "<p>6. दिए गए कथनों और निष्कर्षों का ध्यानपूर्वक अध्ययन कीजिए। कथनों में दी गई जानकारी को सत्य मानते हुए, भले ही यह सर्वज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्धारित कीजिए कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष कथनों का तर्कसंगत रूप से अनुसरण करता है/करते हैं। <br><strong>कथन:</strong> <br>कुछ खिलौने, प्लास्टिक हैं। <br>कुछ प्लास्टिक, धातु हैं। <br>कोई धातु, रसायन नहीं है। <br><strong>निष्कर्ष : </strong><br>(I) कुछ रसायन, प्लास्टिक हैं। <br>(II) कोई खिलौना, रसायन नहीं है।</p>",
                    options_en: ["<p>Both conclusions (I) and (II) follow.</p>", "<p>Only conclusion (II) follows.</p>", 
                                "<p>Neither conclusion (I) nor (II) follows.</p>", "<p>Only conclusion (I) follows.</p>"],
                    options_hi: ["<p>निष्कर्ष (I) और (II) दोनों का अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>",
                                "<p>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है।</p>", "<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>"],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060011290.png\" alt=\"rId10\" width=\"298\" height=\"48\"><br>Neither conclusion (I) nor (II) follows.</p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060011686.png\" alt=\"rId11\" width=\"312\" height=\"52\"><br>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the triad in which the numbers are related to each other in the same way as are the numbers of the given triads. <br>(148, 126, 104), (98, 76, 54) <br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>7. उस त्रिक का चयन कीजिए, जिसमें संख्याएँ एक-दूसरे से उसी तरह संबंधित हैं, जैसे दिए गए त्रिकों की संख्याएँ हैं। <br>(148, 126, 104), (98, 76, 54) <br>(ध्यान दीजिए: संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 - 13 पर संक्रिया जैसे जोड़ना / हटाना / गुणा करना आदि 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>(157, 135, 113)</p>", "<p>(149, 137, 115)</p>", 
                                "<p>(128, 104, 82)</p>", "<p>(134, 102, 90)</p>"],
                    options_hi: ["<p>(157, 135, 113)</p>", "<p>(149, 137, 115)</p>",
                                "<p>(128, 104, 82)</p>", "<p>(134, 102, 90)</p>"],
                    solution_en: "<p>7.(a) <strong>Logic :</strong> 1st number - 2nd number = 2nd number - third number = 22<br>(148, 126, 104) :- 148 - 126 = 126 - 104 = 22<br>(98, 76, 54) :- 98 - 76 = 76 - 54 = 22<br>Similarly<br>(157, 135, 113) :- 157 - 135 = 135 - 113 = 22</p>",
                    solution_hi: "<p>7.(a) <strong>तर्क :</strong> पहली संख्या - दूसरी संख्या = दूसरी संख्या - तीसरी संख्या = 22<br>(148, 126, 104) :- 148 - 126 = 126 - 104 = 22<br>(98, 76, 54) :- 98 - 76 = 76 - 54 = 22<br>इसी प्रकार, <br>(157, 135, 113) :- 157 - 135 = 135 - 113 = 22</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. The sequence of folding a paper and the manner in which the folded paper is cut is shown in the following figures. How would this paper look when unfolded? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060011847.png\" alt=\"rId12\" width=\"158\" height=\"82\"></p>",
                    question_hi: "<p>8. निम्नलिखित आकृतियों में एक कागज़ को मोड़ने का क्रम और मुड़े हुए कागज़ को काटने का तरीका दर्शाया गया है। खोले जाने पर यह कागज़ कैसा दिखाई देगा?</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060011847.png\" alt=\"rId12\" width=\"158\" height=\"82\"></p>\n<p>&nbsp;</p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060011984.png\" alt=\"rId13\" width=\"70\" height=\"119\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060012101.png\" alt=\"rId14\" width=\"67\" height=\"114\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060012206.png\" alt=\"rId15\" width=\"67\" height=\"114\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060012324.png\" alt=\"rId16\" width=\"67\" height=\"114\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060011984.png\" alt=\"rId13\" width=\"67\" height=\"114\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060012101.png\" alt=\"rId14\" width=\"66\" height=\"113\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060012206.png\" alt=\"rId15\" width=\"68\" height=\"116\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060012324.png\" alt=\"rId16\" width=\"73\" height=\"125\"></p>"],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060012479.png\" alt=\"rId17\" width=\"65\" height=\"111\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060012479.png\" alt=\"rId17\" width=\"65\" height=\"111\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. The position of how many letters will remain unchanged if each of the letters in the word &lsquo;SIGHTED&rsquo; is arranged in the English alphabetical order?</p>",
                    question_hi: "<p>9. यदि शब्द \'SIGHTED\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा?</p>",
                    options_en: ["<p>None</p>", "<p>One</p>", 
                                "<p>Three</p>", "<p>Two</p>"],
                    options_hi: ["<p>कोई नहीं</p>", "<p>एक</p>",
                                "<p>तीन</p>", "<p>दो</p>"],
                    solution_en: "<p>9.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060012624.png\" alt=\"rId18\" width=\"152\" height=\"77\"><br>The position of two letters will be unchanged.</p>",
                    solution_hi: "<p>9.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060012624.png\" alt=\"rId18\" width=\"152\" height=\"77\"><br>दो अक्षरों की स्थिति अपरिवर्तित रहेगी.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Read the given statements and conclusions carefully. You have to take the given statements to be true if they seem to be at variance from commonly known facts. You have to decide which conclusion(s) logically follow(s) from the given statements. <br><strong>Statements:</strong> <br>All fruit are sour. <br>Some sour are vegetables. <br>Some vegetables are green. <br><strong>Conclusions:</strong><br>(I) All vegetables are sour.<br>(II) Some sour are green.</p>",
                    question_hi: "<p>10. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। आपको दिए गए कथनों को सत्य मानना है, भले ही वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों। आपको यह तय करना है कि कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं। <br><strong>कथन:</strong> <br>सभी फल, खट्टे हैं। <br>कुछ खट्टे, सब्जियाँ हैं। <br>कुछ सब्जियाँ, हरी हैं। <br><strong>निष्कर्ष:</strong><br>(I) सभी सब्जियाँ, खट्टे हैं। <br>(II) कुछ खट्टे, हरे हैं।</p>",
                    options_en: ["<p>Only conclusion (II) follows</p>", "<p>Neither conclusion (I) nor (II) follows</p>", 
                                "<p>Only conclusion (I) follows</p>", "<p>Both conclusions (I) and (II) follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (II) अनुसरण करता हैं</p>", "<p>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है</p>",
                                "<p>केवल निष्कर्ष (I) अनुसरण करता है</p>", "<p>निष्कर्ष (I) और (II) दोनों अनुसरण करते हैं</p>"],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060012810.png\" alt=\"rId19\" width=\"256\" height=\"66\"><br>Neither conclusion (I) nor (II) follows</p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013160.png\" alt=\"rId20\" width=\"264\" height=\"71\"><br>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. What should come in place of the question mark (?) in the given series ?<br>9 17 33 65 129 ?</p>",
                    question_hi: "<p>11. दी गई शृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए?<br>9 17 33 65 129 ?</p>",
                    options_en: ["<p>259</p>", "<p>254</p>", 
                                "<p>257</p>", "<p>258</p>"],
                    options_hi: ["<p>259</p>", "<p>254</p>",
                                "<p>257</p>", "<p>258</p>"],
                    solution_en: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013430.png\" alt=\"rId21\" width=\"305\" height=\"59\"></p>",
                    solution_hi: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013430.png\" alt=\"rId21\" width=\"305\" height=\"59\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the figure from among the given options that can replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013589.png\" alt=\"rId22\" width=\"305\" height=\"58\"></p>",
                    question_hi: "<p>12. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो निम्नलिखित शृंखला में प्रश्नचिह्न (?) को प्रतिस्थापित कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013589.png\" alt=\"rId22\" width=\"305\" height=\"58\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013731.png\" alt=\"rId23\" width=\"87\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013859.png\" alt=\"rId24\" width=\"91\" height=\"93\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013959.png\" alt=\"rId25\" width=\"89\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014075.png\" alt=\"rId26\" width=\"91\" height=\"93\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013731.png\" alt=\"rId23\" width=\"89\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013859.png\" alt=\"rId24\" width=\"91\" height=\"93\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013959.png\" alt=\"rId25\" width=\"92\" height=\"94\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014075.png\" alt=\"rId26\" width=\"89\" height=\"91\"></p>"],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013859.png\" alt=\"rId24\" width=\"92\" height=\"94\"></p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060013859.png\" alt=\"rId24\" width=\"92\" height=\"94\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014250.png\" alt=\"rId27\" width=\"274\" height=\"73\"></p>",
                    question_hi: "<p>13. एक कागज को नीचे दिए गए चित्र के अनुसार मोड़ा और काटा जाता है। यह कागज खोलने पर कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014250.png\" alt=\"rId27\" width=\"274\" height=\"73\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014387.png\" alt=\"rId28\" width=\"87\" height=\"79\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014612.png\" alt=\"rId29\" width=\"92\" height=\"84\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014772.png\" alt=\"rId30\" width=\"89\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014883.png\" alt=\"rId31\" width=\"90\" height=\"82\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014387.png\" alt=\"rId28\" width=\"90\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014612.png\" alt=\"rId29\" width=\"89\" height=\"81\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014772.png\" alt=\"rId30\" width=\"91\" height=\"82\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014883.png\" alt=\"rId31\" width=\"90\" height=\"82\"></p>"],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014387.png\" alt=\"rId28\" width=\"91\" height=\"82\"></p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060014387.png\" alt=\"rId28\" width=\"91\" height=\"82\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, &lsquo;RETAINED&rsquo; is written as &lsquo;ENIATERD&rsquo; and &lsquo;MATURITY&rsquo; is written as &lsquo;TIRUTAMY&rsquo;. How will &lsquo;FIREWALL&rsquo; be written in that language?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में, \'RETAINED\' को \'ENIATERD\' के रूप में लिखा जाता है और \'MATURITY\' को \'TIRUTAMY\' के रूप में लिखा जाता है। उसी भाषा में &lsquo;FIREWALL&rsquo; को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>LAWEFIFR</p>", "<p>LAWERIFL</p>", 
                                "<p>FLLAWERI</p>", "<p>FLIREWAL</p>"],
                    options_hi: ["<p>LAWEFIFR</p>", "<p>LAWERIFL</p>",
                                "<p>FLLAWERI</p>", "<p>FLIREWAL</p>"],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060015180.png\" alt=\"rId32\" width=\"155\" height=\"76\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060015483.png\" alt=\"rId33\" width=\"156\" height=\"76\"><br>Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060015887.png\" alt=\"rId34\" width=\"149\" height=\"73\"></p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060015180.png\" alt=\"rId32\" width=\"155\" height=\"76\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060015483.png\" alt=\"rId33\" width=\"156\" height=\"76\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060015887.png\" alt=\"rId34\" width=\"149\" height=\"73\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. In a certain code language, \'GLOBAL\' is written as \'221714272817\', \'FOREST\' is written as \'23141124109\', how will \'IMPACT\' be written in that language?</p>",
                    question_hi: "<p>15. एक निश्चित कूट भाषा में, \'GLOBAL\' को \'221714272817\' लिखा जाता है, \'FOREST\' को \'23141124109\' लिखा जाता है, उसी कूट भाषा में \'IMPACT\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>20161327259</p>", "<p>20161328269</p>", 
                                "<p>18141126247</p>", "<p>20161226259</p>"],
                    options_hi: ["<p>20161327259</p>", "<p>20161328269</p>",
                                "<p>18141126247</p>", "<p>20161226259</p>"],
                    solution_en: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060016214.png\" alt=\"rId35\" width=\"107\" height=\"123\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060016515.png\" alt=\"rId36\" width=\"111\" height=\"131\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060016712.png\" alt=\"rId37\" width=\"112\" height=\"132\"></p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060016915.png\" alt=\"rId38\" width=\"128\" height=\"151\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060017129.png\" alt=\"rId39\" width=\"133\" height=\"160\"><br>इसी प्रकार, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060017317.png\" alt=\"rId40\" width=\"129\" height=\"155\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Three of the following four number-pairs are alike in a certain way and thus form a group. Which number-pair does NOT belong to that group? <br>(NOTE: The relation should be found without breaking down the numbers into its constituent digits)</p>",
                    question_hi: "<p>16. निम्नलिखित चार संख्या-युग्मों में से तीन एक निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा संख्या-युग्म उस समूह से संबंधित नहीं है? <br>(नोट: संख्याओं को उसके घटक अंकों में विभाजित किए बिना संबंध ज्ञात किया जाना चाहिए</p>",
                    options_en: ["<p>83, 97</p>", "<p>73, 79</p>", 
                                "<p>61,71</p>", "<p>31, 41</p>"],
                    options_hi: ["<p>83, 97</p>", "<p>73, 79</p>",
                                "<p>61,71</p>", "<p>31, 41</p>"],
                    solution_en: "<p>16.(b) <strong>Logic:</strong>There is a prime number between both the prime numbers but in option (b) the prime numbers are consecutive.</p>",
                    solution_hi: "<p>16.(b)<strong> तर्क: </strong>दोनों अभाज्य संख्याओं के बीच एक अभाज्य संख्या है लेकिन विकल्प (b) में अभाज्य संख्याएँ क्रमागत हैं</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged? <br>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?</p>",
                    question_hi: "<p>17. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?</p>",
                    options_en: ["<p>2758</p>", "<p>2268</p>", 
                                "<p>2785</p>", "<p>2578</p>"],
                    options_hi: ["<p>2758</p>", "<p>2268</p>",
                                "<p>2785</p>", "<p>2578</p>"],
                    solution_en: "<p>17.(a) <strong>Given: </strong>107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?<br>As per the instructions after interchanging the symbol &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; and &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; we get.<br>107 - 2028 &divide; 6 + 427 &times; 7 = ?<br>107 - 338 + 2989<br>3096 - 338 = 2758</p>",
                    solution_hi: "<p>17.(a) <strong>Given: </strong>दिया गया है: 107 + 2028 &times; 6 &ndash; 427 &divide; 7 = ?<br>निर्देशों के अनुसार प्रतीक \'&times;\' और \'&divide;\' तथा \'+\' और \'-\' को आपस में बदलने पर हमें प्राप्त होता है।<br>107 - 2028 &divide; 6 + 427 &times; 7 = ?<br>107 - 338 + 2989<br>3096 - 338 = 2758</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Based on the position in the English alphabetical order, three of the following letter-clusters are alike in some manner and one is different. Select the odd letter-cluster.<br>Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>18. अंग्रेजी वर्णमाला क्रम में स्थिति के आधार पर, निम्नलिखित में से तीन अक्षर-समूह किसी न किसी रूप में संगत हैं और एक असंगत है। असंगत अक्षर-समूह का चयन करें। <br>(नोट: असंगत, अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।).</p>",
                    options_en: ["<p>SVXZ</p>", "<p>EHJK</p>", 
                                "<p>YBDE</p>", "<p>TWYZ</p>"],
                    options_hi: ["<p>SVXZ</p>", "<p>EHJK</p>",
                                "<p>YBDE</p>", "<p>TWYZ</p>"],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060017500.png\" alt=\"rId41\" width=\"150\" height=\"68\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060017663.png\" alt=\"rId42\" width=\"151\" height=\"69\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060017798.png\" alt=\"rId43\" width=\"153\" height=\"70\"><br>but<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060017976.png\" alt=\"rId44\" width=\"144\" height=\"66\"></p>",
                    solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060017500.png\" alt=\"rId41\" width=\"150\" height=\"68\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060017663.png\" alt=\"rId42\" width=\"151\" height=\"69\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060017798.png\" alt=\"rId43\" width=\"153\" height=\"70\"><br>लेकिन<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060017976.png\" alt=\"rId44\" width=\"144\" height=\"66\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "19. ‘Vacant’ is related to ‘Empty’ in the same way as ‘Crowd’ is related to ‘________’. ",
                    question_hi: "19. \'रिक्त\' का संबंध \'खाली\' से उसी प्रकार है, जैसे \'भीड़\' का संबंध \'________\' से है।",
                    options_en: [" Throes ", " Loner  ", 
                                " Throng ", " Disband "],
                    options_hi: [" कष्ट ", " अकेला  ",
                                " जमघट", " उखड़ना"],
                    solution_en: "19.(c) ‘Vacant’ and ‘Empty’ are synonyms, Similarly, ‘Crowd’ and ‘Throng’ are synonyms.",
                    solution_hi: "19.(c)<br />‘रिक्त’ और ‘खाली’ पर्यायवाची शब्द हैं, इसी प्रकार, ‘भीड़’ और’जमघट’ पर्यायवाची शब्द हैं.<br /> ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option that is related to the fifth term in the same way as the second term is related to the first term and the fourth term is related to the third term. <br>READER : ERZWRE :: SERIAL : ESIRLA :: JINGLE : ?</p>",
                    question_hi: "<p>20. उस विकल्प का चयन करें जो पांचवें पद से उसी प्रकार संबंधित है जिस प्रकार दूसरा पद, पहले पद से संबंधित है और चौथा पद, तीसरे पद से संबंधित है।<br>READER : ERZWRE :: SERIAL : ESIRLA :: JINGLE : ?</p>",
                    options_en: ["<p>IJGNEL</p>", "<p>JIMTEL</p>", 
                                "<p>JIGNEL</p>", "<p>IJMTEL</p>"],
                    options_hi: ["<p>IJGNEL</p>", "<p>JIMTEL</p>",
                                "<p>JIGNEL</p>", "<p>IJMTEL</p>"],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060018225.png\" alt=\"rId45\" width=\"138\" height=\"77\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060018477.png\" alt=\"rId46\" width=\"139\" height=\"78\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060018666.png\" alt=\"rId47\" width=\"129\" height=\"73\"></p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060018225.png\" alt=\"rId45\" width=\"138\" height=\"77\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060018477.png\" alt=\"rId46\" width=\"139\" height=\"78\"><br>उसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060018666.png\" alt=\"rId47\" width=\"129\" height=\"73\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. What is the number of triangles in the following figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060018939.png\" alt=\"rId48\" width=\"104\" height=\"114\"></p>",
                    question_hi: "<p>21. निम्न आकृति में त्रिभुजों की संख्या कितनी है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060018939.png\" alt=\"rId48\" width=\"111\" height=\"122\"></p>",
                    options_en: ["<p>9</p>", "<p>8</p>", 
                                "<p>6</p>", "<p>7</p>"],
                    options_hi: ["<p>9</p>", "<p>8</p>",
                                "<p>6</p>", "<p>7</p>"],
                    solution_en: "<p>21.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019159.png\" alt=\"rId49\" width=\"123\" height=\"118\"><br>ABC, ADE, AHF, AHG, AHK, AKG, FHG, JIK<br>There are 8 triangles.</p>",
                    solution_hi: "<p>21.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019159.png\" alt=\"rId49\" width=\"123\" height=\"118\"><br>ABC, ADE, AHF, AHG, AHK, AKG, FHG, JIK<br>8 त्रिभुज हैं.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option figure that will replace the question mark (?) in the figure given below to complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019353.png\" alt=\"rId50\" width=\"102\" height=\"88\"></p>",
                    question_hi: "<p>22. उस विकल्प आकृति का चयन कीजिए , जो पैटर्न को पूरा करने के लिए नीचे दी गई आकृति में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019353.png\" alt=\"rId50\" width=\"102\" height=\"88\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019534.png\" alt=\"rId51\" width=\"92\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019674.png\" alt=\"rId52\" width=\"92\" height=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019832.png\" alt=\"rId53\" width=\"90\" height=\"79\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060020036.png\" alt=\"rId54\" width=\"91\" height=\"80\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019534.png\" alt=\"rId51\" width=\"90\" height=\"79\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019674.png\" alt=\"rId52\" width=\"89\" height=\"78\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019832.png\" alt=\"rId53\" width=\"90\" height=\"79\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060020036.png\" alt=\"rId54\" width=\"90\" height=\"79\"></p>"],
                    solution_en: "<p>22.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019534.png\" alt=\"rId51\" width=\"91\" height=\"80\"></p>",
                    solution_hi: "<p>22.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060019534.png\" alt=\"rId51\" width=\"91\" height=\"80\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option that is related to the third figure in the same way as the second figure is related to the first figure. Follow the analogy-<br>Figure 1 : Figure 2 :: Figure 3 : ? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060020330.png\" alt=\"rId55\" width=\"368\" height=\"87\"></p>",
                    question_hi: "<p>23. उस विकल्प का चयन कीजिए जो तीसरी आकृति से उसी प्रकार संबंधित है जिस प्रकार दूसरी आकृति पहली आकृति से संबंधित है। सादृश्य का पालन कीजिए-<br>आकृति 1 : आकृति 2 :: आकृति 3 : ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060021317.png\" alt=\"rId60\" width=\"328\" height=\"79\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060020639.png\" alt=\"rId56\" width=\"95\" height=\"68\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060020778.png\" alt=\"rId57\" width=\"95\" height=\"68\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060020927.png\" alt=\"rId58\" width=\"94\" height=\"67\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060021063.png\" alt=\"rId59\" width=\"95\" height=\"68\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060020639.png\" alt=\"rId56\" width=\"101\" height=\"72\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060020778.png\" alt=\"rId57\" width=\"96\" height=\"68\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060020927.png\" alt=\"rId58\" width=\"96\" height=\"68\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060021063.png\" alt=\"rId59\" width=\"96\" height=\"69\"></p>"],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060020778.png\" alt=\"rId57\" width=\"95\" height=\"68\"></p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060020778.png\" alt=\"rId57\" width=\"95\" height=\"68\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. What will come in place of the question mark (?) in the following equation, if &lsquo;&times;&rsquo; is replaced by &lsquo;&divide;&rsquo;, &lsquo;&divide;&rsquo; is replaced by &lsquo;+&rsquo;, &lsquo;+&rsquo; is replaced by &lsquo;&times;&rsquo; and &lsquo;&minus;&rsquo; remains unchanged? <br>42 &times; 3 &divide; 4 + 9 &minus; 17 = ?</p>",
                    question_hi: "<p>24. यदि \'&times;\' को \'&divide;\' से बदल दिया जाए, \'&divide;\' को \'+\' से बदल दिया जाए, \'+\' को \'&times;\' से बदल दिया जाए और \'&minus;\' अपरिवर्तित रहे, तो निम्नलिखित समीकरण में प्रश्नवाचक चिह्न (?) के स्थान पर क्या आएगा? <br>42 &times; 3 &divide; 4 + 9 &minus; 17 = ?</p>",
                    options_en: ["<p>23</p>", "<p>33</p>", 
                                "<p>29</p>", "<p>30</p>"],
                    options_hi: ["<p>23</p>", "<p>33</p>",
                                "<p>29</p>", "<p>30</p>"],
                    solution_en: "<p>24.(b) <strong>Given:</strong> 42 &times; 3 &divide; 4 + 9 &minus; 17 = ?<br>As per the instructions after interchanging the symbol , we get.<br>42 &divide; 3 + 4 &times; 9 &minus; 17 = ? <br>14 + 4 &times; 9 - 17 <br>14 + 36 - 17<br>50 - 17 = 33</p>",
                    solution_hi: "<p>24.(b) <strong>दिया गया है:</strong> 42 &times; 3 &divide; 4 + 9 &minus; 17 = ?<br>निर्देशों के अनुसार, चिन्हों को बदलने के बाद हमें प्राप्त होता है।<br>42 &divide; 3 + 4 &times; 9 &minus; 17 = ? <br>14 + 4 &times; 9 - 17 <br>14 + 36 - 17<br>50 - 17 = 33</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. 16 is related to 30 following a certain logic. Following the same logic, 81 is related to 110. To which of the following is 49 related, following the same logic? (NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>25. एक निश्चित तर्क के अनुसार 16 का संबंध 30 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 81 का संबंध 110 से है। उसी तर्क का अनुसरण करते हुए, 49 का संबंध निम्नलिखित में से किससे है? <br>नोट: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- - 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>64</p>", "<p>60</p>", 
                                "<p>72</p>", "<p>90</p>"],
                    options_hi: ["<p>64</p>", "<p>60</p>",
                                "<p>72</p>", "<p>90</p>"],
                    solution_en: "<p>25.(c) <strong>Logic: </strong>(<math display=\"inline\"><msqrt><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></msqrt></math> + 1) &times; (<math display=\"inline\"><msqrt><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></msqrt></math>+2) = 2<sup>nd</sup>no.<br>(16&nbsp;: 30) :- (<math display=\"inline\"><msqrt><mn>16</mn></msqrt></math> + 1) &times; (<math display=\"inline\"><msqrt><mn>16</mn></msqrt></math>+ 2) &rArr; 5 &times; 6 = 30<br>(81&nbsp;: 110) :- (<math display=\"inline\"><msqrt><mn>81</mn></msqrt></math> + 1) &times; (<math display=\"inline\"><msqrt><mn>81</mn></msqrt></math> + 2) &rArr; 10 &times; 11 = 110<br>Similarly<br>(49 : x) :- (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 1) &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 2) &rArr; 8 &times; 9 = 72</p>",
                    solution_hi: "<p>25.(c) <strong>तर्क :</strong> (<math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></msqrt></math> + 1) &times; (<math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></msqrt></math> +2) = दूसरी संख्या <br>(16&nbsp;: 30) :- (<math display=\"inline\"><msqrt><mn>16</mn></msqrt></math> + 1) &times; (<math display=\"inline\"><msqrt><mn>16</mn></msqrt></math>+ 2) &rArr; 5 &times; 6 = 30<br>(81&nbsp;: 110) :- (<math display=\"inline\"><msqrt><mn>81</mn></msqrt></math> + 1) &times; (<math display=\"inline\"><msqrt><mn>81</mn></msqrt></math> + 2) &rArr; 10 &times; 11 = 110<br>उसी प्रकार&nbsp;<br>(49 : x) :- (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 1) &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 2) &rArr; 8 &times; 9 = 72</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. After the fall of the Guptas, different kingdoms emerged in various parts of India. Among them, the Maukharis emerged in which of the following areas?</p>",
                    question_hi: "<p>26. गुप्तों के पतन के बाद, भारत के विभिन्न भागों में विभिन्न साम्राज्यों का उदय हुआ। उनमें से, मौखरियों का उदय निम्नलिखित में से किस क्षेत्र में हुआ था?</p>",
                    options_en: ["<p>Thanesar</p>", "<p>Kunnur</p>", 
                                "<p>Valabhi</p>", "<p>Kannauj</p>"],
                    options_hi: ["<p>थानेसर</p>", "<p>कुन्नूर</p>",
                                "<p>वल्लभी</p>", "<p>कन्नौज</p>"],
                    solution_en: "<p>26.(d) <strong>Kannauj.</strong> The decline of the Gupta empire resulted in the emergence of numerous ruling dynasties in different parts of northern India. The prominent among them were the Pushyabhutis of Thanesar and the Maitrakas of Valabhi. Maukharies ruled over Kannauj, a city in western Uttar Pradesh. They were also the subordinate rulers of the Guptas and used the title of samanta.</p>",
                    solution_hi: "<p>26.(d) <strong>कन्नौज।</strong> गुप्त साम्राज्य के पतन के परिणामस्वरूप उत्तर भारत के विभिन्न भागों में कई शासक राजवंशों का उदय हुआ। उनमें से प्रमुख थानेसर के पुष्यभूति और वल्लभी के मैत्रक शामिल थे। मौखरियों ने पश्चिमी उत्तर प्रदेश के एक शहर कन्नौज पर शासन किया। वे गुप्तों के अधीनस्थ शासक भी थे और सामंत की उपाधि धारण करते थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which of the following nations won the ICC World Test Championship, defeating India, in 2023 ?</p>",
                    question_hi: "<p>27. निम्नलिखित में से किस देश ने 2023 में भारत को हराकर आई.सी.सी. (ICC) विश्व टेस्ट चैम्पियनशिप जीती?</p>",
                    options_en: ["<p>New Zealand</p>", "<p>Australia</p>", 
                                "<p>South Africa</p>", "<p>England</p>"],
                    options_hi: ["<p>न्यूजीलैंड</p>", "<p>ऑस्ट्रेलिया</p>",
                                "<p>दक्षिण अफ्रीका</p>", "<p>इंग्&zwj;लैंड</p>"],
                    solution_en: "<p>27.(b) <strong>Australia.</strong> The ICC World Test Championship 2021-2023 final was held in June 2023 at the Oval in London. The ICC World Test Championship (WTC), is the biennial Cricket tournament organised by the International Cricket Council (ICC). The ICC was founded in 1909, and its headquarters are located in Dubai, United Arab Emirates.</p>",
                    solution_hi: "<p>27.(b) <strong>ऑस्ट्रेलिया। </strong>ICC विश्व टेस्ट चैंपियनशिप 2021-2023 का फाइनल जून 2023 में लंदन के ओवल में आयोजित किया गया था। ICC विश्व टेस्ट चैंपियनशिप (WTC), अंतर्राष्ट्रीय क्रिकेट परिषद (ICC) द्वारा आयोजित द्विवार्षिक क्रिकेट टूर्नामेंट है। ICC की स्थापना 1909 में हुई थी और इसका मुख्यालय दुबई, संयुक्त अरब अमीरात में स्थित है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Presently, which of the following countries is NOT a beneficiary of India&rsquo;s Duty-Free Tariff Preference (DFTP) Scheme?</p>",
                    question_hi: "<p>28. वर्तमान में, निम्नलिखित में से कौन-सा देश भारत की शुल्क-मुक्त टैरिफ वरीयता (DFTP) योजना का लाभार्थी नहीं है?</p>",
                    options_en: ["<p>Rwanda</p>", "<p>Burundi</p>", 
                                "<p>Myanmar</p>", "<p>Angola</p>"],
                    options_hi: ["<p>रवांडा (Rwanda)</p>", "<p>बुरूंडी (Burundi)</p>",
                                "<p>म्यांमार (Myanmar)</p>", "<p>अंगोला (Angola)</p>"],
                    solution_en: "<p>28.(d) <strong>Angola.</strong> India\'s Duty-Free Tariff Preference (DFTP) Scheme, introduced in 2008, grants Least Developed Countries (LDCs) duty-free access to the Indian market for a wide range of products. As of June 2023, 35 Least Developed Countries (LDCs) have been notified as beneficiaries of the scheme. Some of these countries include Benin, Liberia, Togo, Burkina Faso, Madagascar, Zambia, Burundi, Malawi, Afghanistan, Chad, Mali, Bangladesh, Uganda, Sierra Leone, Lesotho, and Tanzania.</p>",
                    solution_hi: "<p>28.(d) <strong>अंगोला (Angola)</strong>। भारत की शुल्क मुक्त टैरिफ वरीयता (DFTP) योजना, जिसे 2008 में शुरू किया गया था, यह अल्प विकसित देशों (LDC) को भारतीय बाजार में कई उत्पादों के लिए शुल्क मुक्त पहुंच प्रदान करती है। जून 2023 तक, 35 कम विकसित देशों (LDC) को इस योजना के लाभार्थियों के रूप में अधिसूचित किया गया है। इनमें से कुछ देशों में बेनिन, लाइबेरिया, टोगो, बुर्किना फासो, मेडागास्कर, जाम्बिया, बुरुंडी, मलावी, अफगानिस्तान, चाड, माली, बांग्लादेश, युगांडा, सिएरा लियोन, लेसोथो और तंजानिया शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following Articles deals with the composition of the Rajya Sabha ?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन-सा अनुच्छेद राज्यसभा के गठन से संबंधित है?</p>",
                    options_en: ["<p>Article 76</p>", "<p>Article 80</p>", 
                                "<p>Article 84</p>", "<p>Article 92</p>"],
                    options_hi: ["<p>अनुच्छेद 76</p>", "<p>अनुच्छेद 80</p>",
                                "<p>अनुच्छेद 84</p>", "<p>अनुच्छेद 92</p>"],
                    solution_en: "<p>29.(b) <strong>Article 80 </strong>of the Constitution lays down the maximum strength of Rajya Sabha as 250, out of which 12 members are nominated by the President and 238 are representatives of the States and of the three Union Territories. Article 76 - Attorney General of India. Article 84 - Qualification for membership of Parliament. Article 92 - The Chairman or the Deputy Chairman not to preside while a resolution for his removal from office is under consideration.</p>",
                    solution_hi: "<p>29.(b) संविधान के <strong>अनुच्छेद 80</strong> में राज्य सभा की अधिकतम सदस्य संख्या 250 निर्धारित की गई है, जिसमें से 12 सदस्य राष्ट्रपति द्वारा मनोनीत किए जाते हैं और 238 सदस्य राज्यों और तीन केंद्र शासित प्रदेशों के प्रतिनिधि होते हैं। अनुच्छेद 76 - भारत का महान्यायवादी। अनुच्छेद 84 - संसद की सदस्यता के लिए योग्यता। अनुच्छेद 92 - जब सभापति या उपसभापति को पद से हटाने का कोई संकल्प विचाराधीन हैं तब उसका पीठासीन न होना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. The birth anniversary of Mahatma Gandhi is celebrated as the national festival of India. In which of the following years was Gandhi ji born?</p>",
                    question_hi: "<p>30. महात्मा गांधी की जयंती भारत में राष्ट्रीय पर्व के रूप में मनाई जाती है। निम्नलिखित में से किस वर्ष गांधी जी का जन्म हुआ था?</p>",
                    options_en: ["<p>1885</p>", "<p>1869</p>", 
                                "<p>1890</p>", "<p>1877</p>"],
                    options_hi: ["<p>1885</p>", "<p>1869</p>",
                                "<p>1890</p>", "<p>1877</p>"],
                    solution_en: "<p>30.(b) <strong>1869.</strong> 2nd October is also observed globally as the International Day of Non-Violence in recognition of his contributions to peace and non-violent movements. India\'s three national festivals are: Republic Day (January 26) - Marks the adoption of the Indian Constitution in 1950. Independence Day (August 15) - Commemorates India\'s independence from British rule in 1947. Gandhi Jayanti (October 2) - Celebrates the birth anniversary of Mahatma Gandhi.</p>",
                    solution_hi: "<p>30.(b) <strong>1869</strong>. शांति और अहिंसक आंदोलनों में उनके योगदान के सम्मान में 2 अक्टूबर को विश्व स्तर पर अंतर्राष्ट्रीय अहिंसा दिवस के रूप में भी मनाया जाता है। भारत के तीन राष्ट्रीय त्योहार हैं: गणतंत्र दिवस (26 जनवरी) - 1950 में भारतीय संविधान को अपनाए जाने का प्रतीक है। स्वतंत्रता दिवस (15 अगस्त) - 1947 में ब्रिटिश शासन से भारत की स्वतंत्रता का स्मरण करता है। गांधी जयंती (2 अक्टूबर) - महात्मा गांधी की जयंती मनाई जाती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. What kind of phyllotaxy was examined in the Guava plant?</p>",
                    question_hi: "<p>31. अमरूद के पौधे में किस प्रकार का पर्णविन्यास पाया जाता है?</p>",
                    options_en: ["<p>Superimposed</p>", "<p>Whorled</p>", 
                                "<p>Alternate</p>", "<p>Opposite </p>"],
                    options_hi: ["<p>अध्यारोपित</p>", "<p>चक्करदार</p>",
                                "<p>एकांतर</p>", "<p>विपरीत</p>"],
                    solution_en: "<p>31.(d) <strong>Opposite.</strong> Phyllotaxy refers to the arrangement of leaves on the stem or branch of a plant. In an opposite leaf arrangement, two leaves arise at the same point, with the leaves connecting opposite each other along the branch. If there are three or more leaves connected at a node, the leaf arrangement is classified as whorled.</p>",
                    solution_hi: "<p>31.(d) <strong>विपरीत</strong>। पर्णविन्यास (Phyllotaxy) का तात्पर्य पौधे के तने या शाखा पर पत्तियों की व्यवस्था से है। विपरीत पत्ती व्यवस्था में, दो पत्तियाँ एक ही बिंदु पर उगती हैं, जिसमें पत्तियाँ शाखा के साथ एक दूसरे के विपरीत जुड़ती हैं। यदि एक नोड पर तीन या अधिक पत्तियाँ जुड़ी हुई हैं, तो पत्ती व्यवस्था को चक्राकार (whorled) के रूप में वर्गीकृत किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. The Nandi Award for Best Male Playback Singer was instituted by which state in the year 1977?</p>",
                    question_hi: "<p>32. 1977 में सर्वश्रेष्ठ पुरुष पार्श्वगायक के लिए नंदी पुरस्कार की स्थापना किस राज्य द्वारा की गई थी?</p>",
                    options_en: ["<p>Gujarat</p>", "<p>Andhra Pradesh</p>", 
                                "<p>Tripura</p>", "<p>Maharashtra</p>"],
                    options_hi: ["<p>गुजरात</p>", "<p>आंध्र प्रदेश</p>",
                                "<p>त्रिपुरा</p>", "<p>महाराष्ट्र</p>"],
                    solution_en: "<p>32.(b) <strong>Andhra Pradesh.</strong> S. P. Balasubrahmanyam holds the successful record of winning the most Nandi Awards in this category. The Nandi Awards recognize excellence in Telugu cinema, theatre, television, and lifetime achievements in Indian cinema. They are presented annually by the Government of Andhra Pradesh and were first awarded in 1964.</p>",
                    solution_hi: "<p>32.(b)<strong> आंध्र प्रदेश। </strong>एस. पी. बालासुब्रमण्यम के नाम इस श्रेणी में सबसे अधिक नंदी पुरस्कार जीतने का सफल रिकॉर्ड है। नंदी पुरस्कार तेलुगु सिनेमा, रंगमंच, टेलीविज़न और भारतीय सिनेमा में आजीवन उपलब्धियों के लिए दिए जाते हैं। इन्हें आंध्र प्रदेश सरकार द्वारा प्रतिवर्ष प्रदान किया जाता है और पहली बार 1964 में प्रदान किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Indian Olympic Association was established in which of the following years ?</p>",
                    question_hi: "<p>33. निम्नलिखित में से किस वर्ष में भारतीय ओलंपिक संघ की स्थापना की गई थी?</p>",
                    options_en: ["<p>1927</p>", "<p>1931</p>", 
                                "<p>1933</p>", "<p>1929</p>"],
                    options_hi: ["<p>1927</p>", "<p>1931</p>",
                                "<p>1933</p>", "<p>1929</p>"],
                    solution_en: "<p>33.(a)<strong> 1927. </strong>The Indian Olympic Association is headquartered in New Delhi. The first president of the association was Sir Dorabji Tata, and the current president (as of September 2024) is PT Usha. The International Olympic Committee was founded on June 23, 1894, in Paris, France. Its founders are Pierre de Coubertin and Demetrios Vikelas. The headquarters are located in Lausanne, Switzerland.</p>",
                    solution_hi: "<p>33.(a)<strong> 1927.</strong> भारतीय ओलंपिक संघ का मुख्यालय नई दिल्ली में है। संघ के पहले अध्यक्ष सर दोराबजी टाटा थे और वर्तमान अध्यक्ष (सितंबर 2024 तक) पीटी उषा हैं। अंतर्राष्ट्रीय ओलंपिक समिति की स्थापना 23 जून, 1894 को पेरिस, फ्रांस में हुई थी। इसके संस्थापक पियरे डी कुबर्टिन और डेमेट्रियोस विकेलस हैं। मुख्यालय लुसाने, स्विटज़रलैंड में स्थित है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following is a public sector industry ?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन-सा सार्वजनिक क्षेत्र का उद्योग है?</p>",
                    options_en: ["<p>BHEL</p>", "<p>Reliance</p>", 
                                "<p>Tata</p>", "<p>Adani</p>"],
                    options_hi: ["<p>भेल (BHEL)</p>", "<p>रिलायंस (Reliance)</p>",
                                "<p>टाटा (Tata)</p>", "<p>अडानी (Adani)</p>"],
                    solution_en: "<p>34.(a)<strong> BHEL</strong> (Bharat Heavy Electricals Limited) was established in 1956 and is headquartered in New Delhi. In contrast, Reliance, Tata, and Adani are private sector companies.</p>",
                    solution_hi: "<p>34.(a)<strong> BHEL</strong> (भारत हेवी इलेक्ट्रिकल्स लिमिटेड) की स्थापना 1956 में हुई थी और इसका मुख्यालय नई दिल्ली में है। इसके विपरीत, रिलायंस, टाटा और अडानी निजी क्षेत्र की कंपनियाँ हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which of following is NOT a device of parliamentary proceedings?</p>",
                    question_hi: "<p>35. निम्नलिखित में से कौन-सा विकल्प संसदीय कार्यवाही की विधि नहीं है?</p>",
                    options_en: ["<p>Half-an-hour discussion</p>", "<p>Zero-hour discussion</p>", 
                                "<p>Two-hour (short) discussion</p>", "<p>Full-day discussion</p>"],
                    options_hi: ["<p>आधे घंटे की चर्चा</p>", "<p>शून्य काल चर्चा</p>",
                                "<p>दो घंटे की (लघु) चर्चा</p>", "<p>पूरे दिन की चर्चा</p>"],
                    solution_en: "<p>35.(d) <strong>Full-day discussion</strong>. Devices of Parliamentary Proceedings refers to the various procedural tools, mechanisms, and practices employed within a parliamentary system to facilitate the conduct of the business of the legislature. Half-an-Hour Discussion - It is meant for discussing a matter of sufficient public importance, which has been subjected to a lot of debate and the answer to which needs elucidation on a matter of fact. Zero-Hour Discussion - This occurs immediately after Question Hour, allowing members to raise urgent matters without prior notice. Short Duration Discussion - It is also known as two-hour discussion as the time allotted for such a discussion should not exceed two hours.</p>",
                    solution_hi: "<p>35.(d) <strong>पूरे दिन की चर्चा।</strong> संसदीय कार्यवाही के उपकरण संसदीय प्रणाली के भीतर विधानमंडल के व्यवसाय के संचालन को सुविधाजनक बनाने के लिए नियोजित विभिन्न प्रक्रियात्मक उपकरणों, तंत्रों और प्रथाओं को संदर्भित करते हैं। आधे घंटे की चर्चा - इसका उद्देश्य पर्याप्त सार्वजनिक महत्व के विषय पर चर्चा करना है, जिस पर बहुत बहस हुई है और जिसका उत्तर तथ्यात्मक मामले पर स्पष्टीकरण की आवश्यकता है। शून्य-काल चर्चा - यह प्रश्नकाल के तुरंत बाद होती है, जिससे सदस्यों को बिना किसी पूर्व सूचना के तत्काल मामलों को उठाने की अनुमति मिलती है। लघु अवधि की चर्चा - इसे दो घंटे की चर्चा के रूप में भी जाना जाता है क्योंकि इस तरह की चर्चा के लिए आवंटित समय दो घंटे से अधिक नहीं होना चाहिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following rivers marks the eastern-most boundary of the Himalayas ?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन-सी नदी हिमालय की सबसे पूर्वी सीमा बनाती है?</p>",
                    options_en: ["<p>Kali</p>", "<p>Teesta</p>", 
                                "<p>Satluj</p>", "<p>Brahmaputra </p>"],
                    options_hi: ["<p>काली</p>", "<p>तीस्ता</p>",
                                "<p>सतलुज</p>", "<p>ब्रह्मपुत्र</p>"],
                    solution_en: "<p>36.(d) <strong>Brahmaputra </strong>River flows through the northeastern part of India, originating from the Angsi Glacier in Tibet and passing through Arunachal Pradesh before entering Assam. The Teesta River rises in the Pauhunri Mountain of the eastern Himalayas and flows through the Indian states of Sikkim and West Bengal. The Satluj River originates in Raksas Tal near Mansarovar in Tibet. The Kali River flows through the Uttara Kannada district of Karnataka.</p>",
                    solution_hi: "<p>36.(d) <strong>ब्रह्मपुत्र नदी </strong>का उद्गम भारत के पूर्वोत्तर भाग से होता है, जो तिब्बत के अंगसी ग्लेशियर से निकलती है और असम में प्रवेश करने से पहले अरुणाचल प्रदेश से होकर गुजरती है। तीस्ता नदी का उद्गम पूर्वी हिमालय के पौहुनरी पर्वत से होता है और सिक्किम एवं पश्चिम बंगाल के भारतीय राज्यों से होकर प्रवाहित होती है। सतलुज नदी का उद्गम तिब्बत में मानसरोवर के निकट राक्षस ताल से होता है। काली नदी का उद्गम कर्नाटक के उत्तर कन्नड़ जिले से होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. What is the empirically fitted relationship between the rate of change of money, wage, and rate of unemployment known as ?</p>",
                    question_hi: "<p>37. धन, मजदूरी में परिवर्तन की दर और बेरोजगारी की दर के बीच अनुभवजन्य रूप से स्थापित संबंध को किस रूप में जाना जाता है?</p>",
                    options_en: ["<p>Friedman&rsquo;s model</p>", "<p>Keynesian model</p>", 
                                "<p>Baumol hypothesis</p>", "<p>Phillips curve</p>"],
                    options_hi: ["<p>फ्रीडमैन मॉडल</p>", "<p>केनेसियन मॉडल</p>",
                                "<p>बॉमोल परिकल्पना</p>", "<p>फिलिप्स वक्र</p>"],
                    solution_en: "<p>37.(d) <strong>Phillips</strong> curve states that inflation and unemployment have an inverse relationship; higher inflation is associated with lower unemployment and vice versa. This concept was first proposed by Alban William Phillips. Friedman&rsquo;s model: Milton Friedman\'s monetary policy model, focusing on monetary supply and demand. Keynesian model: John Maynard Keynes\' macroeconomic model, emphasizing government intervention and aggregate demand. Baumol hypothesis: William Baumol\'s theory on the relationship between inflation and economic growth.</p>",
                    solution_hi: "<p>37.(d) <strong>फिलिप्स </strong>वक्र बताता है कि मुद्रास्फीति और बेरोजगारी में विपरीत संबंध है, उच्च मुद्रास्फीति निम्न बेरोजगारी से जुड़ी होती और निम्न मुद्रास्फीति उच्च बेरोजगारी से जुड़ी होती है। इस अवधारणा को सबसे पहले एल्बन विलियम फिलिप्स ने प्रस्तावित किया था। फ्राइडमैन का मॉडल: मिल्टन फ्राइडमैन का मौद्रिक नीति मॉडल, जो मौद्रिक आपूर्ति और मांग पर केंद्रित है। कीनेसियन मॉडल: जॉन मेनार्ड कीन्स का व्यापक आर्थिक मॉडल, जो सरकारी हस्तक्षेप और समग्र मांग पर जोर देता है। बॉमोल परिकल्पना: मुद्रास्फीति और आर्थिक विकास के बीच संबंधों पर विलियम बॉमोल का सिद्धांत।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following is India&rsquo;s national academy of dance ?</p>",
                    question_hi: "<p>38. निम्नलिखित में से कौन सी भारत की राष्ट्रीय नृत्य अकादमी है?</p>",
                    options_en: ["<p>Lalit Kala Akademi</p>", "<p>Centre for Cultural Resources and Training</p>", 
                                "<p>Centre for Indian Classical Dances</p>", "<p>Sangeet Natak Akademi</p>"],
                    options_hi: ["<p>ललित कला अकादमी</p>", "<p>सांस्कृतिक स्रोत एवं प्रशिक्षण केंद्र</p>",
                                "<p>भारतीय शास्त्रीय नृत्य केंद्र</p>", "<p>संगीत नाटक अकादमी</p>"],
                    solution_en: "<p>38.(d) <strong>Sangeet Natak Akademi</strong> was set up in 1953. It has three constituent units, two of these being dance-teaching institutions: the Jawaharlal Nehru Manipur Dance Academy (JNMDA) at Imphal, and Kathak Kendra in Delhi. Lalit Kala Akademi is India\'s national academy of fine arts established in 1954. Centre for Cultural Resources and Training, established in 1979, works in the field of linking education with culture.</p>",
                    solution_hi: "<p>38.(d) <strong>संगीत नाटक अकादमी</strong> की स्थापना 1953 में हुई थी। इसकी तीन घटक इकाइयाँ हैं, जिनमें से दो नृत्य-शिक्षण संस्थान हैं: इम्फाल में जवाहरलाल नेहरू मणिपुर नृत्य अकादमी (JNMDA) और दिल्ली में कथक केंद्र। ललित कला अकादमी 1954 में स्थापित भारत की राष्ट्रीय ललित कला अकादमी है। 1979 में स्थापित सांस्कृतिक संसाधन और प्रशिक्षण केंद्र, शिक्षा को संस्कृति से जोड़ने के क्षेत्र में काम करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Padma Bhushan awardee, Ustad Sabri Khan played which of the following musical instruments?</p>",
                    question_hi: "<p>39. पद्म भूषण पुरस्कार से सम्मानित उस्ताद साबरी खान निम्नलिखित में से कौन-सा संगीत वाद्ययंत्र बजाते थे?</p>",
                    options_en: ["<p>Sarod</p>", "<p>Santoor</p>", 
                                "<p>Tabla</p>", "<p>Sarangi</p>"],
                    options_hi: ["<p>सरोद</p>", "<p>संतूर</p>",
                                "<p>तबला</p>", "<p>सारंगी</p>"],
                    solution_en: "<p>39.(d) <strong>Sarangi.</strong> Ustad Sabri Khan was awarded the Padma Bhushan in 2006. Musical instruments and exponents: Sarod - Ali Akbar Khan, Amjad Ali Khan, Radhika Mohan Maitra, Buddhadev Das Gupta. Santoor - Tarun Bhattacharya, Ulhas Bapat. Tabla - Zakir Hussain, Ustad Alla Rakha, Shaikh Dawood Khan.</p>",
                    solution_hi: "<p>39.(d) <strong>सारंगी। </strong>उस्ताद साबरी खान को 2006 में पद्म भूषण से सम्मानित किया गया था। संगीत वाद्ययंत्र एवं वादक: सरोद - अली अकबर खान, अमजद अली खान, राधिका मोहन मैत्रा, बुद्धदेव दास गुप्ता। संतूर - तरूण भट्टाचार्य, उल्हास बापट। तबला - जाकिर हुसैन, उस्ताद अल्ला रक्खा, शेख दाऊद खान।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Transition elements are the elements that are found in Groups 3-12 of the modern periodic table, that constitute the _____</p>",
                    question_hi: "<p>40. संक्रमण तत्व वे तत्व हैं जो आधुनिक आवर्त सारणी के समूह 3 - 12 में पाए जाते हैं, जो _____ का निर्माण करते हैं।</p>",
                    options_en: ["<p>d-block</p>", "<p>s-block</p>", 
                                "<p>p-block</p>", "<p>f-block</p>"],
                    options_hi: ["<p>d-ब्लॉक</p>", "<p>s-ब्लॉक</p>",
                                "<p>p-ब्लॉक</p>", "<p>f-ब्लॉक</p>"],
                    solution_en: "<p>40.(a) <strong>d-block. </strong>Transition metals are defined as metals that have an incomplete d subshell in either their neutral atom or in their ions. p-block elements are located in groups 13&ndash;18 of the periodic table. S-block elements are in groups 1 and 2 of the periodic table. The f-block consists of elements in which 4 f and 5 f orbitals are progressively filled. They are placed in a separate panel at the bottom of the periodic table.</p>",
                    solution_hi: "<p>40.(a) <strong>d-ब्लॉक।</strong> संक्रमण तत्व को उन तत्व के रूप में परिभाषित किया जाता है जिनके उदासीन परमाणु या आयनों में एक अपूर्ण d उपकोश होता है। p-ब्लॉक तत्व आवर्त सारणी के समूह 13-18 में स्थित हैं। s-ब्लॉक तत्व आवर्त सारणी के समूह 1 और 2 में हैं। f-ब्लॉक में ऐसे तत्व होते हैं जिनमें 4 f और 5 f ऑर्बिटल्स क्रमिक रूप से भरे जाते हैं। उन्हें आवर्त सारणी के निचले भाग में एक अलग पैनल में रखा गया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. A small object is placed on the focus on the left side of a convex lens. Where will be the image formed?</p>",
                    question_hi: "<p>41. किसी उत्तल लेंस के बायीं ओर फोकस पर एक छोटी वस्तु रखे जाने पर, निम्नलिखित में से कौन-से स्थान पर प्रतिबिम्ब बनेगा?</p>",
                    options_en: ["<p>At the centre on the right side of the lens.</p>", "<p>At infinity on the left side of the lens.</p>", 
                                "<p>At infinity on the right side of the lens.</p>", "<p>At the focus on the right side of the lens.</p>"],
                    options_hi: ["<p>लेंस के दायीं ओर, केंद्र पर</p>", "<p>लेंस के बायीं ओर, अनंत पर</p>",
                                "<p>लेंस के दायीं ओर, अनंत पर</p>", "<p>लेंस के दायीं ओर, फोकस पर</p>"],
                    solution_en: "<p>41.(c) <strong>At infinity on the right side of the lens.</strong> Object Position Image Formation by Convex Lens: When an object is placed at the focus (F) of a convex lens, its image is formed at infinity. If the object is between F and 2F, the image is real, inverted, and magnified. At 2F, the image is real, inverted, and the same size as the object. Beyond 2F, the image is real, inverted, and reduced. An object at infinity forms an image at the focus (F) of the lens.</p>",
                    solution_hi: "<p>41.(c) <strong>लेंस के दायीं ओर, अनंत पर।</strong> उत्तल लेंस द्वारा वस्तु की स्थिति का प्रतिबिंब निर्माण: जब किसी वस्तु को उत्तल लेंस के फोकस (F) पर रखा जाता है, तो उसका प्रतिबिंब अनंत पर बनता है। यदि वस्तु F और 2F के मध्य है, तो प्रतिबिंब वास्तविक, उल्टा एवं बड़ा होता है। 2F पर, प्रतिबिंब वास्तविक, उल्टा एवं वस्तु के समान आकार की होती है। 2F से दूर, प्रतिबिंब वास्तविक, उल्टा एवं छोटा होता है। अनंत पर स्थित वस्तु लेंस के फोकस (F) पर प्रतिबिंब बनाती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. One of the greatest of Chola ruler Rajaraja I ruled from 985 AD to ______.</p>",
                    question_hi: "<p>42. सबसे महान चोल शासकों में से एक राजाराज प्रथम ने 985 ईस्वी से _______ तक शासन किया।</p>",
                    options_en: ["<p>1015 AD</p>", "<p>1017 AD</p>", 
                                "<p>1014 AD</p>", "<p>1018 AD</p>"],
                    options_hi: ["<p>1015 ईस्वी</p>", "<p>1017 ईस्वी</p>",
                                "<p>1014 ईस्वी</p>", "<p>1018 ईस्वी</p>"],
                    solution_en: "<p>42.(c) <strong>1014 AD.</strong> The Chola Empire was founded by Vijayalaya. Rajaraja I was known for his conquests of South India and parts of Sri Lanka. He built the great Brihadisvara Temple at the Chola capital Thanjavur. He initiated a project of land survey and assessment in 1000 CE which led to the reorganisation of Tamil country into individual units known as valanadus. He was succeeded by his son Rajendra Chola I.</p>",
                    solution_hi: "<p>42.(c) <strong>1014 ई।</strong> चोल साम्राज्य की स्थापना विजयालय ने की थी। राजराज प्रथम को दक्षिण भारत और श्रीलंका के कुछ हिस्सों पर विजय के लिए जाना जाता था। उन्होंने चोल राजधानी तंजावुर में महान बृहदीश्वर मंदिर का निर्माण कराया। उन्होंने 1000 ई. में भूमि सर्वेक्षण और मूल्यांकन की एक परियोजना शुरू की, जिसके कारण तमिल देश को अलग-अलग इकाइयों में पुनर्गठित किया गया, जिन्हें वलनाडस के नाम से जाना जाता है। उनका उत्तराधिकारी उनका पुत्र राजेंद्र चोल प्रथम बना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Who asked James Rennel to prepare the map of Hindustan?</p>",
                    question_hi: "<p>43. जेम्स रेनेल को हिंदुस्तान का मानचित्र तैयार करने के लिए किसने कहा था?</p>",
                    options_en: ["<p>Lord Ripen</p>", "<p>Warren Hastings</p>", 
                                "<p>Lord Dalhousie</p>", "<p>Robert Clive</p>"],
                    options_hi: ["<p>लॉर्ड रिपेन</p>", "<p>वारेन हेस्टिंग्स</p>",
                                "<p>लॉर्ड डलहौजी</p>", "<p>रॉबर्ट क्लाइव</p>"],
                    solution_en: "<p>43.(d) <strong>Robert Clive </strong>served twice as Governor of Bengal (1758-60 and 1764-67) and also led the Battle of Plassey on behalf of the British East India Company in 1757. James Rennel was the first Surveyor General of Bengal (1767-1777) and conducted the first comprehensive geographical survey of much of India. He is best known for his \"Bengal Atlas\" (1779) and \"Memoir of a Map of Hindoostan\" (1782).</p>",
                    solution_hi: "<p>43.(d) <strong>रॉबर्ट क्लाइव</strong> ने दो बार (1758-60 और 1764-67) बंगाल के गवर्नर के रूप में कार्य किया और 1757 में ब्रिटिश ईस्ट इंडिया कंपनी की ओर से प्लासी के युद्ध का नेतृत्व भी किया। जेम्स रेनेल बंगाल के पहले सर्वेयर जनरल (1767-1777) थे और उन्होंने भारत के अधिकांश भाग का पहला व्यापक भौगोलिक सर्वेक्षण किया था। उन्हें उनके \"बंगाल एटलस\" (1779) और \"मेमोयर ऑफ़ ए मैप ऑफ़ हिंदोस्तान\" (1782) के लिए जाना जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Who among the following is appointed as the new Chairman of Pension Fund Regulatory and Development Authority (PFRDA) in March 2023?</p>",
                    question_hi: "<p>44. मार्च 2023 में निम्नलिखित में से किसे पेंशन फंड नियामक और विकास प्राधिकरण (PFRDA) के नए अध्यक्ष के रूप में नियुक्त किया गया है?</p>",
                    options_en: ["<p>Supratim Bandopadhyay</p>", "<p>Shasikanta Das</p>", 
                                "<p>Mamta Shankar</p>", "<p>Deepak Mohanty</p>"],
                    options_hi: ["<p>सुप्रतिम बंदोपाध्याय</p>", "<p>शशिकांत दास</p>",
                                "<p>ममता शंकर</p>", "<p>दीपक मोहंती</p>"],
                    solution_en: "<p>44.(d) <strong>Deepak Mohanty.</strong> PFRDA was established on 23rd August, 2003 for overall supervision and regulation of pensions in India. It operates under the jurisdiction of the Ministry of Finance, Government of India, with its headquarters in New Delhi.</p>",
                    solution_hi: "<p>44.(d) <strong>दीपक मोहंती। </strong>भारत में पेंशन के समग्र पर्यवेक्षण और विनियमन के लिए 23 अगस्त, 2003 को PFRDA की स्थापना की गई थी। यह भारत सरकार के वित्त मंत्रालय के अधिकार क्षेत्र के अंतर्गत कार्य करता है, जिसका मुख्यालय नई दिल्ली में है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Indian National Army was formed in which of the following countries?</p>",
                    question_hi: "<p>45. भारतीय राष्ट्रीय सेना का गठन निम्नलिखित में से किस देश में किया गया था?</p>",
                    options_en: ["<p>Singapore</p>", "<p>Germany</p>", 
                                "<p>London</p>", "<p>Japan</p>"],
                    options_hi: ["<p>सिंगापुर</p>", "<p>जर्मनी</p>",
                                "<p>लंदन</p>", "<p>जापान</p>"],
                    solution_en: "<p>45.(a)<strong> Singapore</strong>. The Indian National Army (INA), also known as the Azad Hind Fauj, was a collaborationist armed unit of Indian supporters that fought under the command of the Japanese Empire. It was founded by Mohan Singh in September 1942 during World War II.</p>",
                    solution_hi: "<p>45.(a) <strong>सिंगापुर।</strong> भारतीय राष्ट्रीय सेना (INA), जिसे आज़ाद हिंद फ़ौज के नाम से भी जाना जाता है, भारतीय समर्थकों की एक सहयोगी सशस्त्र इकाई थी जो जापानी साम्राज्य की कमान के तहत लड़ी थी। इसकी स्थापना मोहन सिंह ने सितंबर 1942 में द्वितीय विश्व युद्ध के दौरान की थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Match the following neighbouring countries with their official languages correctly. <br><img src=\"data:image/png;base64,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\" width=\"211\" height=\"135\"></p>",
                    question_hi: "<p>46. निम्नलिखित पड़ोसी देशों का उनकी आधिकारिक भाषाओं के साथ सही मिलान कीजिए।<br><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAYYAAAD8CAIAAAD8LJShAAAgAElEQVR4Ae19fVBUV5r+6Ts0Lk2Zai20/ChgC0OFomvyUSODWWVYi3JJCOVmJSqbSjqmkImOocgkWybluDGplAykNJlIzchmTLkZnaj7g52FciUL68SP4KohwkalEolAhRTCCLEJvc3mdtc9v2zenbPv3I/+pvt298sf7elzz3nP+z7nuc89X7dlnP4IAUKAEDANAsw0npAjhAAhQAhwkiQiASFACJgIAZIkE3UGuUIIEAJ/IkmM/ggBQoAQiBMCIMckSXGCn5olBAiBP0XAUJIU+kt5BIAqKQ8DARAjBIBvhpJEs1lCAFOE0CAE5hoBzDedidtcN0/2zY8Apoj5vSUPEx0BzDeSpETvzTnxH1NkThogo4QAQgDzjSQJAUPJPyKAKfLHPPqXEJgrBDDfSJLmCuWEtospktCBkPMJgQDmG0lSQnRZrJ3EFIl129Re6iGA+UaSlHr9H0TEmCJBFKcihEBECGC+kSRFBGWyVsYUSdYYKS7zIID5FgdJmpmZKS0tBScYY6tWrbpy5Yp50CFPOOeYIvECxOPxHDhwYMWKFeCMJEnf//739+3bNzk5GS+Xpqen6+vrrVYrYywzM3Pr1q2jo6PxciaZ2sV8i78kMcbsdvulS5eSCeJEjwVTJC6xjI6Orly5EtzQfj777LPT09MxdmxiYuKBBx5QOWO1Wt99911FUWLsTJI1h/kWB0nCaE5OTj7++OOMsZKSEpfLhS9ROo4IYIrE3g1FUerr68GH7du3j4+Pc85nZ2fPnTu3bt06yM/Ozj5z5kwsfWtuboam9+7dK8vy0NDQ3/zN30DOvn37UlOVFEV54403vve97z3yyCOR3L8AI/RmnCWJc+5yuUpKShhj+/btiyXDqC0/CGCK+Ck2R5du375dWFjIGCsuLlYRXVGU06dPw2zOarW2tbXNkQ8qs16vt6qqijG2fPlyMVmTZXnPnj2MMUmSWltbVVVS4atYhLHZbL29vWGHjPkWf0ninJ87dy49PX3p0qWDg4NhR0UVo4gApkgUzQZpamxsLCcnhzHmdDp1q0xNTW3YsIExFjNVEvdeaWnpzMyM8EqW5draWsZYfn7+yMiIyE+RBIySJEl66KGHVA+PkBDAfDOFJHm93pqaGsZYfX19ag6AQ+q/GBTGFIlBc6omvvrqK1hIevzxx4344PF4gDMxW4iE5nJycsbGxrDDo6OjBQUFjLFt27Z5vV58idJBIoD5ZgpJ4pz39vbavvuLZPgXZPxULCACmCIBC0e9gKIoO3bsYIwVFBTAQhI04Xa7n3vuOdjwKioqOnfu3GOPPcYYKyoqisE2XHt7O2PMYrF0dnaqQj5y5AhjLD09/dy5c6pL9DUYBDDfYipJYvQLHvj/tNvt/gsYXVWN9hsbG41KBsyXJElbJor2wTjcY9qGjHK0D+pgej2kMtB0SFVE4aNHj37/+98H6KxWK94pFzMyo9AYY/PmzdNedTqdsiz/7d/+Lb6EZ/pOpxNfim5at4N0/Yxiux0dHQJSkRgdHd20aRNg63A4Tp8+rRpFBsRBNxYjtzMzM40uBcwPaXUJrEGYJEn+sCVJEjdD8ImjR4+qMBVzq0gk6cKFC+np6YyxLVu2DA8P33PPPYyx/fv3g2MBb0WVSyF91b2NYy9JYoYonJckqb29HXdNQBx0YxEGVYnklySMnSrd0dEBcDQ2NuJLQ0NDy5cvZ4zdd999w8PDbrd79+7dUFIsPIlxkO6DBVtTpaH/Asp52PY55+ImxK2ITNVaqRhFqsZBMKv1s9yriivyr4BweHb+8Ic//Pu///vs7KzP53vvvfdA1qurq71er27gutFpMd+/fz941dHRIYASbIluVwbvp4BIluWNGzfCIburV6+KfN2ENjrdYqpM0Pr09PTTp09fvHjRZrMxxqqqqvAClhYH/7Ewxnbs2CGGWrqOaW3qdplupioEo6+YbzEdJRk5xDk3kiRtnJOTk3BiLTc398svv+Sc6+KobQsfvV28ePHOnTsrKysZYzab7Ze//CUcRGCMbdy4UezygpEg7WtbTE1JwjiI+6GsrMztdouvWIu1Xazbp0KS2travF7vm2++mZGRcfToUWhOe9twzj0ez+uvvw4rAHDYevv27cD+ffv2VVVVgVyuWrXq+vXrum4H9BPXgsUmxpgQSrgathvYOOccJMlut1+9elUXSc65FgfdkgJzWMAVQy1dqmttiup4EUM3UxWC0ddEkiQxSsrPz//0008hJBVGujiqgne5XGVlZRB5wE/Vhm4w9lXNia+CEKkzShKxu1wuMaQ9ePAgFuiAt7oW8+7ubovFgp/q33zzzbcng6A5FSU452J7PmB3a98fEL0W0E8RLOd8YGBg4cKFqpFLJG5g45zzL7744u6772aMPffcc6Ojo3BOAnsYhiQ9+eSTu3fvFoTXwq5rU1d9dDNVIRh9NYUkTU9PNzU15ebmqhijesJwzt98801RBq6q+KeLoyp4cfT28ccfn56eFqfGheWf/vSnHo+ntbUVnpwBR7Mq+0ZfBblTSpJE1ADva6+9Btoh8vGNpEtlbZ/OzMzA0W1JkhoaGka/+xOjGxUlOOednZ0gYSUlJaOjo3jKD15t2LBhamrq0qVLMIwqLy/3eDzQj8H7iftdt1YkbmDjkL58+XJtba3L5dJtS1c+dEtizOGs8pYtW2RZ1sKuaxNXF07qZoqr/hPQI1AmPhO3kZERh8Mh5AAntJKkKMrZs2dXr14tSVJjY+P4+DgcAyksLLx9+7buIF8Vv9vthiESDHrhqjg1zhirrKwEOn799ddr1qxhjAnjwdh3uVxr167FUWjTSS9JX3zxxeuvvz48PIxHQ4BDU1OToiizs7Pvv/8+3P9hSBIMQ/Ly8jC2YiVFK0lwjAjv2cuyvGXLFqjucDgmJiY454qiwCtNCxcuHBgYAG4EvI0FwYaGhl588UUwpTtKisQN0Yo2oeuhrnzollTJx6VLlxYsWHDw4MEUlSRxMBLWa/HBEy30qhyfz/faa68Bq2AuEIxkiNXQlStXfvXVV8Im8DgtLe38+fOQKQiKF5h1+0kY4ZyPj4+rbhV820A6uSVJbIc1NzcLZHw+3z//8z/DKqwKkPAkiXM+NTUFK4BgcPXq1fD+rVaSIGfJkiU3b94ULomuFItQnPO9e/fCkqI4ExfMbcw5b2trAzd27949OzsrmIlBiMQN4bY2oeth2JLEOd+3b9+CBQvEW3t4p0iLrUrRYAb9+9//Pi0tjTFWU1Ojddh/DsAIZeIwShodHYVNtIAn3GRZ3rdvn91uz8zMfOSRR7q7uwVk+AC74BnGEUMgJAmPfYT6YLEQ54ZDkiSv11tdXS3uOjzQE9TBrYhMcWdOTk7ee++9woJRAq8m4gCjnsYUCca4y+UqLi7WvpUmDj2qIhKBi1Oyqv1Eoz6dmJiA8bXdbn///ffFVpH2toEcPPYR6sMYE1QRL6/57yBdP4UzOLqysjL8akUkbugi/8EHH+ieTcE+BIxFPETFdFXsGIIdgY+uzKkkSexNwet+XV1dup77yYRGoUAcJEkVj3DU7Xa/+OKLP/jBD1pbW4Fq/f398+fPx1hDesuWLW63W1Q0oq8o8O3Tta6uDs7XXrhwAfLFBBD3n5j5i64KZhTGOb9y5Yo424m7U6gPbkVkijtTiKY2WJxjWkninMMJZovFonoVVvTO3r17+/r64GkkAte91f1gfurUKQAE677ubdPS0gIljx07Bj3u8XjKy8shU/TR1atXoePwSXFtBxn5CVGDTavVWl9fr/rVlEjcwAQWaeEwNKr76Z9sMF2F8/GSJL333ns+n8/tdr/yyiuw+oYlWxdb1S3c3d0Nx50WLVr0zjvviOeE8DlgAqKAYmaRJPx7FGLF59NPP128eLEK9JycnGvXruEgBekFz/BVSF+7di0rKwt+BWVoaMjj8bz88stgWfTfyMhIfn4+ZB45ckQYCcY+5/zy5csrVqyQJOmDDz4QdQW5RSt4qQXfme3t7eLph8doRjeDaGIuEpgiQdp3/fEXHcT2DVTctWsXWOvo6BBo4MBV/IZaRpiLB3JLS4twTDSNQRZDmLy8vL6+Pq/Xe+jQIYEwUAXvw+7evVvcS8H7KRbdv33gNTQ0CAvCt0jcEEZwwuv1btu2DSBljGEkdeVDNxbOOWa7sCYS+FbSjkBv3ry5ZMmSKL7KA+1CmHGQJN2fnhAjSXBOIHLmzBmHw2GxWHJzc8UPDNpstpMnT4p+MqKvKAAJsZsmcIcE8PjWrVviaNK6devw295B2lc1B18FIfDdIjIxnxRFaWpqApcSUZI452L3qqCgoK+vT1GU/v5+2Kv2c5omJEkSj5b77rvvxo0biqL853/+Z1FREe5K0RHCH1WPwyjA7XY/8cQTcKmgoAAfRtPtIF0/cdRG77iF7YYIRJXASoopFJIkcc6np6d37twJg0RJklavXv3www8DIOIG1LU5Ozv76KOPQsn169dj6FSuBvkVTEHhOEiSGBBZLJbjx4+DH6K/VdMrHJIsy2IFEatS8JJx/fp1oTurVq2CH+UBsRgcHFy6dCljbOXKlSqIg7ePvYW0IHdASYKjNE1NTZIk5eXl4VV/AY6ZJ24Q78mTJ3XXs2HTTaCBbyTd6IwwVxRl3759wGDtJwYZ/MEvhd1zzz2i9zs6OiYnJ0HLsrOz+/r6cN8F7ydMgsSzRDVCFDbDc0NU1yampqbgsHhFRcXs7KwooB3R6MYiyqsSurBrbWoHWdu2bVP9QILKsv+vcZYkHI/NZjty5Igsyzdu3Fi0aBF4JrbktWEoinL48GEYfos3p3Rx1NbV5qiwbmlp2bx589TUlKpk2PbxHA3fLf5ZMjMzc+fOHeyD7k2LC0Q9jSkSqvGxsbHnn38eP3vPnj0LMxrdwHWj84M5HApZtWoVOAnHsv/yL/9StWum67bKbFdX19q1a+HgAi4fvJ9QC5+H1D7SsGVIB+mGtiLOURRlcnISD+d1RzS6sWA7OK1yDC6pbhNR/tvzfc8++yz0Aixs19XVqZbSRGH/Ccy3OIySwLm+vr7s7GwRj0jk5eVpKYLjwapUXV39zTff6OKIqxiljbBWlQ/bfniSpGo9UdaStG5rc3Rvj1AlSWtW91bULRZkVwbvp2gFT6YCqlKQbgjjwSe0lNaNxcigrmNam7j60NAQjNfgFs7Kyjp9+jQuEEzaFJIEU9mmpqZly5aBxGZkZMCD7uLFi/7DgNH7zp074XyjLo7+LcBV/1gLC2HbJ0kSGEJC9/ZIDkmCsT8cUDCavgk0ImGUMKKb0FJaF3PdukYbnVqb2ur4l8jF9EVbzCjHLJKk8k+sXGZlZX300Ueqq/Q1lghgisSy3URv69atW48++mhq/nc7eH9GnKoPskMx3+I2cdP1FVYuHQ5HCv6MsS4g8crEFImXD9RuwiFw/vx5OMANP/wQvP+Yb+aSJJ/P99vf/pb0KPi+nKOSmCJz1ASZTT4ExM9j7dmzJ6ToMN/MJUkhhUGF5w4BTJG5a4UsJx8Cly9frqysVB2jCRgm5htJUkC4UrEApkgqxk8xxxYBzDeSpNhinyCtYYokiMvkZgIjgPlGkpTAHTl3rmOKzF0rZJkQAAQw30iSiBU6CGCK6FymLEIgqghgvpEkRRXaZDGGKZIsMVEc5kUA840kybz9FEfPMEXi6AY1nSIIYL6RJKVIp4cWJqZIaDWpNCEQOgKYbyRJoeOXAjUwRVIgXAoxzghgvpEkxbkzzNk8pog5PSSvkgkBzDeSpGTq2ajFgikSNaNkiBAwQADzTUeSFPpLeQSAIikPAwEQIwQCSBJcpk9CgBAgBGKJAAyhdEZJsXSC2iIECAFCABAwlCSD6R5lpxACmCIpFDaFGicEMN90Rklx8oqaNRECmCImcotcSVIEMN9IkpK0kyMLC1MkMktUmxAIjADmG0lSYLxSsASmSAqGTyHHGAHMN5KkGIOfGM1hiiSGx+RlIiOA+UaSlMg9OWe+Y4rMWSNkmBD4XwQw30iSiBY6CGCK6FymLEIgqghgvpEkRRXaZDGGKZIsMVEc5kUA840kybz9FEfPMEXi6AY1nSIIYL6RJKVIp4cWJqZIaDWpNCEQOgKYbyRJQeE3Ojq6detWq9UK2K1YseJnP/vZF198EVTlBCyEKZJA7nd0dIDnkiRVVFR89tlnCeR8KruK+UaSFJgJV65csdvtJSUlg4ODiqL4fL6BgYH6+vrMzMz169d//vnngU0kWglMkUTz/X/8dbvdDQ0Ny5cv/+ijjzjnMzMzDz/8cFZWVn9/fyKGk/Q+Y76RJAXu7rq6OpvN1tvbqyo6PT397LPPZmRkvPXWWz6fT3U1ob9iiiRoIIqiNDU13XvvvRMTE729vTabjTG2f//+BA0nud3GfCNJCtzXTqczJydnbGxMt+iZM2eys7Mfe+wxl8ulWyARMzFFEtF/8Nnj8VRWVm7btu3OnTs0SjJzP2K+kSQF7qm6urqFCxcODAwYFR0dHV25cmVJScmtW7eMyiRWPqZIYnmu8ra3tzcrK6u9vV2VT19NhQDmG0lS4K7p7OyUJOnYsWN+it66daukpMThcIyMjPgpliiXMEUSxWddPxVFqa+vdzgcExMTugUo0wwIYL6FL0k+n+/ixYtOp/Ov/uqvZmZmwgtMUZTOzs7Vq1dLksQYczgcp0+fVhRFWHM6neBuJJ92uz286tqK2pzwLAdTCzDBJYNsXXflS0AaTAIaDaZk2GXGxsZycnJwdFFPa+HS5kS30Xnz5qkMNjY2hg1R6lQE0CDekCVJUZTr168/88wzGRkZYKi0tDQSSXr22We3b98+OTkpy/LevXvT0tLwMFu1jgPrlE6nE/eWbiYuINJwG6iqw85xR0cHFIMyOKgg7c/MzJSWluquOl24cCE9PX3Hjh1YbYVXOKFtvbGxkTEm3MOFcVpbEV8NNY0pEmrdKJYPMnbcInQWFgJdI9pinPPGxkatmnu93urq6vT09AsXLuCGRFqLvG6LojwltAhgvoUsSdCXjLEVK1Y8/fTTaWlp+O7VNhYwx+fziRv19u3bhYWFFRUVs7OzUDE5JMnj8ZSXl+fm5n755Zf+AQmb39qK/hvyfxVTxH/JOb0axr2t1RpdI9piRpLEOT9//nxaWprRE0WLvG6LcwpUohvHfAtZkq5fv/7KK68MDw8rigL9GqEkYTS//vrrNWvWYIPJIUmc871791oslu7ubhyvNh02v7UVtcaDz8EUCb5W1EuGcW9rtUbXiLaYH0n66quvVq5cWVhYePv2bW2MWuR1W9RWpByBAOZbyJIkrHDOoy5JFy9etNls1dXVXq8XGgpJkq5fv/7QQw8xxiRJ2rRpk3b/C9gT+4kb5xymh0ePHsUAatNh81tbUWs8+BxMET+1fD7fiRMnHA4HlM/NzW1ubpZl2U8Vo0uyLO/atUucjweD8CkmrTMzM3//93//+uuvGxkRnIzixI1zrijK448/LnZdP/zww6KiIvBt1apVLS0ty5Ytw89RkiQ/HaR7CcCES2aRJJ/P19XVlf3d3yeffCL8Dl6ScnNzs7KyXnnlFbfbPTExsXbtWu0+S4wlSVEUt9sNsYAktbS0iNCmp6d37ty5ePFifPtBOgx+x16SZFmura1ljC1atMjpdG7atAmWF7dt2yaeKCLYgAl4vD311FMej8eosNvtLisrw+C89dZbWvQYY9GVJM650+mcP39+f3//+fPnbTbbhg0bJiYmZFk+cuQIHMLEXpEkGfWgUb4pJEmWZXG7Ah0ZYxaLpbGxEUjpcrkaGhokScILxlBSNcwR1ZuamsSyFMz/hQQoijI5Odnf379s2TJV9VCXt/v7+7/VPt07gTEmvJ2dnd26dSucHlYUZefOnXjiBqf4bDbbu+++KxbOOOdaZVHxu6OjQ3coodUyo+4PJh9TxKh8W1ubxWIpKysTZ0SHh4fz8vK0K8RGFnB+f3///Pnz9+7dizO16V27di1ZsuTmzZtwyefzNTU1McbWrVsHeyxABj+S5PP5JiYm3n///YyMDFxMO3Gbnp6Ghrxeb1VVVWFh4djYWFVVFXaAc/7hhx/abDaSJG1nBZ+D+RafUdLU1NQDDzywZcsWoSCc89nZ2bfffhvfb/fcc092dra4yTnnQFyVpgALi4qKJicnBQqqAdGpU6eEiKiqhypJIHaMMdW9p91xO3jwIAwiiouLRes4UVdXJxyGREBJOnr0KFjAsOhqmcpySF+hCf9VTp06VVtbe+rUKVwMDm2IqRa+5D8NGn3fffcNDw/7Kdnd3W2xWPD81+v1btu2jTF28ODBYCZu+/fvF13gR5KAovn5+YODgx9++OH8+fP37dsHXYzVRxd51VPETzh0CRDAfIuPJH3rx549e5YuXTo4OKjqlT179uA9b9XEDbbkqqqqxNQAXmVijKmEBoRDZA4NDS1fvhwiF5nQdKiSNDMzs27dumAkaXJyEhYdrFbrL3/5S6y/0KjqlgiG36OjowUFBXg4BlFotUwFbEhfMUVCqhiGJMGbzJzzW7dubdy4UegFJBwOx5kzZ4QPLperuLi4vLwcz++uXr1qt9vLysrcbnfAURIcyADjKvxVhwDgiQJLky+99JIsy4qi7NixY9GiRTdu3BAudXZ2WiwWrFMkSQKcIBOYb3GTpGvXrmVlZe3evRvfqGIZWDxmVZLEOT948GBaWtp7773n8/nE5E4lSbIsb9myhTHW3NwsQPntb38LkUcoSZzzvr6+7Oxssd4JTWhHSZzz9vZ2SZJKSkrE7AYKw9NeO0/RKouW3ydPnrTZbKoNIG1FEXgYCUwRP9Wnp6ebmppWrFgB5cWn6D4/deHSxYsXly9f3tnZqVvS7XY/+eSTWVlZ165dEwWOHDmSkZFx7tw5kQPI5+XljY+PB5Skb/kGjz3VkpN24iaeKPjg/sjISH5+PqwliQVQxhhJkuiOMBKYb3GTJBCXjIyM06dP4xh27dplt9uvXr0KmVpJUhTl+PHjy5YtgzAWLFjgcDgsFosQGp/P19DQwBi7++678U8aKYrS3NwsSZJquhTqKAkcm52dnZycxHqqK0liWtHQ0IAL37x5c8mSJaqnfTCjJGh9Zmbmzp07GLfYSxLcnIyxNWvWNDU1/b/v/kpKSvAgF3uom4bR68aNG4326aB38EwNRqlY5WGTHo6zBZQkzrksyy+88AJjTCw1gm+qURLnvKurS5IkLDec89HR0U2bNsHZ+mXLlj333HN2ux2X0T5FdGOnTIGAWSQJ1ibvuusuWOL1+XynTp1asGDB888/L+5erSSJMEQCWAiS9N///d9bt25ljFmt1ra2NlFGJO7cuYOXk7XjMu29je0LO9qEriRxzuFhK0lSa2urqDU7O1tRUZGeno6f9sFLkrAjElq3xaUwEpgiRtXr6uoYYyqpDXXi9u2sbffu3ZIkdXV16TakO8MdGRlxOBxlZWVDQ0M+n+8f/uEf5s2bB4f+g5Ek2NefmppS6aBWksQT5ciRI7ruRdJlRgZTMB/zLZ6jJGDGxYsXf/SjH4lnDszIRK+EKkkw/M7KylINvoRBbSK8UZLWjpEkcc4vXbpkt9ttNtvJkydFRXgC46d9JPyOsSRBsKoFfji/E9IoSUh2fn6+7hvLzc3NjDHVIjrnHI5QwDtry5YtO378ODzGgpQk0Qs4oZUkzrnL5SorK7Pb7ZcuXcKFRVqLPI2SBDhBJmIhSWNjYz09PaqnUJD+4WJhSJIsy9988w024j8dA0ninPf09KhUSZzrwb+1FDa/tRX9R+3/KqaIbkl484sx9tprr8HP1/l8vnfeeQc2TMVaks/n6+npwdNnXWsDAwN5eXnaX3eBAab2fJmuEciMuiRxzmGKCrtvqqa9Xq8WeZIkFUoBv2K+zckoCbZFtHP1gJ5pC4QhSVoj/nNiI0mc8+7ubrvdLknSr3/9a3DJ5XLBcfPq6mo4pRU2v7UV/Uft/yqmiFFJGPoxxhYsWJCdnZ2ZmQknHvCPNx47dowxplqJ1zU4PDz84IMP3nXXXYcOHYKtgKGhofXr1/sZnujamQtJgt3AsrIyq9X66quvjo+Pw+jp5z//+bZt27TIkyTpdo2fTMy3OZEkj8ezYcMGq9UqnpZ+vPF/KZkkCbbqHnnkETxDcbvdP/7xj8WyV9j81lb0D6z/q5gifkpev34d1rPFD8vAxrk4cdbd3W21Wh9++OFgfivC5/OdPHlyzZo1MNSyWq1bt24dHR3144D20hxJEufc5/OdPn26vLwcxJcxtnjx4jfeeEOLPEmStl/852C+RSRJ/puhq4mLAKZI4kZBnicKAphvJEmJ0msx9RNTJKYNU2MpiQDmG0lSSlIgUNCYIoHK0nVCIFIEMN9IkiJFMynrY4okZYAUlKkQwHwjSTJV15jFGUwRs/hEfiQvAphvJEnJ288RRIYpEoEZqkoIBIUA5htJUlCQpVohTJFUi53ijT0CmG8kSbHHPwFaxBRJAHfJxQRHAPONJCnBO3Nu3McUmZsWyCoh8H8IYL6RJP0fLpQSCGCKiExKEAJzhADmG0nSHIGc2GYxRRI7EvI+ERDAfCNJSoQei7mPmCIxb5waTDkEMN9IklKu+4MJGFMkmPJUhhCIBAHMN5KkSJBM2rqYIkkbJAVmGgQw30iSTNMtZnIEU8RMfpEvyYkA5puOJMFl+iQECAFCIJYIgNySJMUSc2qLECAEDBEwlKTkHBpSVKEgAKwJpQaVJQTCRwDzTWeUFL5hqpksCGCKJEtMFId5EcB8I0kybz/F0TNMkTi6QU2nCAKYbyRJKdLpoYWJKRJaTSpNCISOAOYbSVLo+KVADUyRFAiXQowzAphvJElx7gxzNo8pYk4PyatkQgDzjSQpmXo2arFgikTNKBkiBAwQwHwjSTIAKbWzMUVSGwmKPhYIYL6RJMUC8YRrA1Mk4fXCGY0AAB+aSURBVJwnhxMOAcw3kqSE675YOIwpEov2ot2Gx+N55ZVXMjIyGGN2u33nzp3T09PRboTsRQ0BzDeSpKjBmkyGMEXME5fP5ztx4sQ999wD7mVmZpaXl3d0dMiyjJ2UZbm2tjYnJ6e/v9/n8x06dKimpsbtduMylDYVAphvJEmm6hqzOIMpYhKfvF7vtm3bJElqaWmZnZ3lnLtcrn/6p38qKiqy2+2HDx8WwtTf3z9//vxdu3aZxHNyIyACmG8kSQHhSsUCmCImiX9gYGDhwoVbtmxRFAW7pCjK6dOnV6xY8eCDDw4PD3POOzo6GGONjY24GKXNjADmG0mSmXsqbr5hisTNiT9tuLe312azGQmN2+3+yU9+kpmZefLkSRgl1dXV/akB+mZeBDDfSJLM209x9AxTJI5u4KZhlORHaBRFaW5uttlsLS0tpaWlBQUF4+Pj2AKlTYsA5htJkmm7KZ6OYYoY+dHY2BjL+ZHH4ykvLy8uLna5XEYuKYpy+PBhq9X64x//WJKkhoYG1SzPqCLlxxcBzLdwJEmW5d/85jdi42Px4sVhbLJ+9tlnq1atgj3a3/zmNyrqjI2N5eTkgKPz5s2DhP9Pu92uKqDNURWArzabrbe3N75dYrbWARn/XkUuSWBBt1NUmUFyQFULvlL/+u9HM1yFngJPQpYkl8v12GOPMcasVutf//Vfb9q0CU5/lJWV+Xl8qcJ2uVzFxcUlJSVfffXVzp077Xb71atXcRmQJKfTiTNVaShTWlo6MzMjLgHLOzo6RI5uQreubsnUzMQUMUIgckkysgz5sHiEOeB0OrX6otvjMzMzpaWlOTk5Y2Nj/luhq2ZAAPMtZEk6ePAgY2zt2rVTU1MQzNTU1Nq1axlje/bsCTK8Tz75xG63P/7444qi6FKKJClIJOeoGKaIURMkSUbIUH6oCGC+hSZJ8PBJT0+/cOECbrWzs9NisVRUVMCBEXxJNw2jJEmS/vVf/5UkSRei+GZiihh5QpJkhAzlh4oA5ltokjQxMbHmu7+JiQncKoyxVXMoXECb7uvrq62tdblcJElacOKegyli5Ax03N69e8WJakmSNm3aNDo6alTFKF9RlLNnz8LaImMsMzNz8+bN77zzTkZGBk3cjEBLpnzMt9AkyQiF9vZ2xpifDVqjipzz4CXJ4/EcOHAgNzcXAoBPlQ7qWtO2TmtJWkxwDqYIzsdpgHrBggUWiyU3N7e8vDwzM5Mxlp+fPzIygkv6T3u93tdeew1a3LNnjyzLfX19999/P+SQJPlHLzmuYr5FQZI8Hk9lZWV6evq5c+fCAEhXRLRrSS6Xq6ysDHZ2/ayjq6x1dHRYrVYIWPupkrMwnE/WKpgiRjEC1Ha7vaenB8p4PJ6amhrGWHV1tdfrNaqoyu/q6pIkKTs7Oz8/X2ySQHczxkiSVHAl5VfMt0glSVGUpqYmxlhtba14ySgk1FQiAnW1kgTL6s3Nzf6Nq6wdPXoUolVtvtAoyT+MmCJGJQFq1atkQ0NDy5cvX7Jkyc2bN40qqvJramosFktnZ+fFixfnz5/f1NQEJ0Ju3LixaNEikiQVXEn5FfMtUklqbW2VJCnUsTrA+uabb4IrRp+Cjl6vt6qqKhiiqyRpdHS0oKCAMUaSFBKVMUWMKgLUqjc8oKcsFkt3d7dRRVW+0+lcvnz56OiooigNDQ12u/3SpUucc+1jCR8C8Hq9Tz75pBFzIF/V6ap26at5EMB8i0iSLl26ZP/uDzgUaoTAafBGdZJIRcfgj5moJIlzfvLkSZvNVlhYePv2beEhjZIEFLoJTBHdAmIRUCVJnHOn08kYU3WokRHO+a5duxYuXDgwMAAv95eUlMD0LeC5pLq6OvBT21zwhPHjGF2KGQKYb+FL0uDgYH5+vt1uD/55qIpwYmLC4XAEI0nw7A3moaeVJM75zMzMnTt3cOskSRgNbRpTRHsVcnRHSWFI0rVr17KysoS0wfTttddeg2UpMVIGy/io5ODg4NKlSxlj2kEZSZJRr5kzH/MtTEkaGRnJz8+XJKm1tTWSIAcHB0GVTp06he2oRkmc8+bmZlhxwMW0aV1J0hYjSdJignMwRXA+TutK0uzsbEVFhVYjcEVtuq2t7a677mpoaHC73T6fb9euXeBAwOXt7u5uu92OdQqMkyRpQTZzDuZbOJIkNr8OHz6sejctjLA9Hs+lS5c8Hg+uq5UkGFKJHRlc+JtvvhFukCRhZMJOY4oYGQGoI1/eBvuffvrpli1bYHtUkqQf/vCHL730UjDnksbGxj744APV1gpJklGvmTMf8y1kSRLvuImdEd0gx8bGenp6VETRLambqZUkznlfX192dnZRUVFfX5/vu7/PP//8pz/9aUVFhXjNjSRJF89QMzFFjOoC1HjmLg4B1NTUiEMAYTMh4FqSkWMwVad33PzgY7ZLmG8hS9Lzzz8Pr+8/9dRTtZo/mH/B+yKMsZaWlvCC15Ukzvn09HRTU5M4Lbl48eJnnnnm008/Fa2QJAkoIklgihjZAaiXLFni56hkJEwgSTJCPvnyMd9CliTYTwET2k9YpPR4PBs2bLBarcFvuwiU3W73iy++eNdddzHGbDbbgQMHVHM6UVI3QZKkC0uomZgiRnUB6vb29tbWVvilGu0LJZEwgSTJCPnky8d8C1mS5hQOOAi+dOnSnp4eRVGGhobWr1/vcDgGBwfntF0yrkIAU0R1ib4SAlFHAPPNXJJ04cKF9PR0fEQb/gOc8I5iRh241DGIKZI6UVOk8UIA881cktTR0ZGRkXH58mUMzeTkZFFR0caNG8NeLMfWKB0MApgiwZSnMoRAJAhgvplLkrq7u61Wq/bsZVtbmyRJR44ciSRsqhs8ApgiwdeikoRAeAhgvplLkmZmZtatWwc/WSnLsvgfSuGn4B0Oh+p3msKLn2oFRABTJGBhKkAIRIgA5pu5JIlzfuvWrU2bNkmSBL9x8c0330C077//fnV19R/+8IcIg6fqwSCAKRJMeSpDCESCAOab6SQpksCobrQQwBSJlk2yQwgYIYD5RpJkhFJK52OKpDQQFHxMEMB8I0mKCeSJ1gimSKL5Tv4mHgKYbyRJidd/MfAYUyQGzVETKY4A5htJUoqTQT98TBH9EpRLCEQPAcw3kqTo4ZpEljBFkigsCsWkCGC+kSSZtJPi6xamSHw9odZTAQHMN5KkVOjxkGPEFAm5MlUgBEJEAPONJClE8FKjOKZIakRMUcYTAcw3HUmCy/RJCBAChEAsEQBRJEmKJebUFiFACBgiYChJCv2lPALAmpSHgQCIEQLAN0NJiueckto2BwKYIubwiLxIZgQw33QmbskcOsUWHAKYIsHVoFKEQPgIYL6RJIWPYxLXxBRJ4jApNJMggPlGkmSSTjGXG5gi5vKMvElGBDDfSJKSsYcjjglTJGJjZIAQCIAA5htJUgCwUvMypkhqIkBRxxIBzDeSpFginzBtYYokjNPkaMIigPlGkpSw3TiXjmOKzGU7ZJsQ+B8EMN9IkogTOghgiuhcpixCIKoIYL4lpCSNjo5u3brVarVCJCtWrPjZz372xRdfRBWllDaGKWJCIGRZbm5uXrZsGWNMkqQf/ehH169fN6Gf5FKQCGC+JZ4kXblyxW63l5SUDA4OKori8/kGBgbq6+szMzPXr1//+eefB4kCFfODAKaIn2JxufRtp+/evVuSpEOHDvl8vsnJye3bt6elpbW2tsbFH2o0cgQw3xJPkurq6mw2W29vrwqI6enpZ599NiMj46233vL5fKqr9DUkBDBFQqoYg8K3b98uLCxcs2bN119/Dc3JslxbW7t06dLBwcEYOEBNRB0BzLfEkySn05mTkzM2NqaLy5kzZ7Kzsx977DGXy6VbgDKDQQBTJJjysSyjlSTO+dWrV+12e2NjYyw9obaihQDmW+JJUl1d3cKFCwcGBozgGB0dXblyZUlJya1bt4zKUL5/BDBF/JeM/VVFUXbs2KEaKc/OzlZUVJSVlYn/tD32jlGLYSOA+ZZ4ktTZ2SlJ0rFjx/zEf+vWrZKSEofDMTIy4qcYXTJCAFPEqEwc80dGRvLz88vKyvBYeO/evX6Gz3H0lpoOiADmWziSJMvy4cOHc3NzwVBubu6BAwc8Hk/AhqFAY2MjVAz10263q6rMmzdPlRPk18zMzCBLBl9M655RXafTGSRW8SoGns9p68HTICCw2t7s6OiYU+fJeHQRwHwLWZJcLtdjjz3GGLPb7Zu++wPG1NbWyrIcvKO9vb02m017czY2NuIx+djYWE5OTmlp6czMDBg3qoibDqaMKO9yuYqLiwsKCsbHx0WmUcLpdPp3z6gi3IGJcqtgihhFNHf5/rEaHBxcunRpcXExHiJxzlVdM3fukeWoI4D5FrIkHTx4kDGGx8wul6usrIwx5n8ypQrDSDViL0mc8+bmZsZYe3u7ykntVxXvtYqprQI5/m8zo1rxyscUib0P/rGCQwAWi6WzsxP7puoafInSJkcA8y00SYJFxLS0tPPnz+Mgjx49yhirqanBmf7TppKky5cvZ2Rk1NXV+fdZ+ygmSQqIWBgF/EsS5/z8+fNpaWmq/iJJCgNqk1QJX5K8Xu8HH3zwL//yL1NTUziYjo4Oxph2FobLqNJRlCSPx3PgwIFFixYxxqxW6yOPPHLgwIGMjIyqqqoDBw7AGV+r1frcc88ZbceAslRVVXm9XpWfqq8q3qe4JCmKcvbs2VWrVgGlcnNzDx8+HNL8XQUvfA0oSQC7an9N1TW6linTnAiEL0m68cBAmjHW3NysW0A3M1qSBMfkJEl6++23ZVmenJx85ZVXJEmCIK1W66FDh2RZ/vTTT/Pz8ysrK/Ey/H/913/BoUqgeEVFxezsLHjr8/lOnDjhcDjADv6ktSSASFGUpqYmeKVj/fr1mzZtysjIYIyFuqro8/lOnjy5evVq0WuAtp91N90nAUmS7o2WEJnQ4+BqaBM3bXhut/utt96SJOmBBx6YmJjQFjDKiZYkXbhwIT09fceOHYqiiLaOHz9usVgkScIvGRw9etRisXR3d0OxY8eO3XXXXV1dXZzz3//+96qJwMGDBy0Wy/bt21Vr3ireq+6NmZmZhx9+GOuXKu3nNhPOmyGBKWLkT1dXlyRJ+fn54sz01NTU2rVrQ1pV9Hq9O3futFgsL774ogpq3O7NmzdXr17d0tICmefOnUtPT6eJG4YoodOYb2FK0szMTGlpqbjfHn300VDPJfb398+fP1871wt1eRvmjG1tbbhLQO8effRRPIkA8RIHfK9du5aVlSVJUklJCTzeRTiQKCwsvH37NjYbcC3pq6++WrlyJVQXDYGFgJMRVUPx/Qoh+PHB6/VWV1dr1QdWebTdamQKOqWmpsb/lHliYsLhcNhstl/84hf/+I//mJ2dnZ6efuHCBWxW9bTAlyhtcgQw38KUpNnZ2ddff722trasrAzeyN+5cye+/wNCAFvveK7EOYfHrP+ZkWp4BZJ09OhR3GJ7e7t2bQvW4IVSiPkmY2zz5s14R1k19sGWVbzXlty3b18qSBK81bFkyZKbN29ifGRZHh8fn5ycxINWXECV3r9/P2NM9URRlYGvR44cAWBhxfDdd99VNaHqGl0jlGlOBKIgSTgwMVw/ePAgzg+Ybm1ttdlssAYky/L777+/YsUKxlhIkjQ0NLR8+fKNGzcKQZRleePGjSpJcrlcJSUleOLGOR8dHS0oKLDZbBcvXsTe6r5FBQVUvNdKkji3JWYZUDHJRkkQeOSnpUGSVPNZWZa/ZcWuXbvw0HtmZmbdunWMsRdffFEcUsO9puoafInSJkcgypIkNmVVOyABUVAU5fTp0/fffz84tGDBgs2bN6uIpb3nVaMkzvmvfvWrtLS0l19+2ePxwE9VgEExffB4PDU1NYyxdevWqdjc3t4Oczc8SvJ6vVVVVXa7/erVq6ooArrHOff5fLdv3xYSCRZIklRIwtfu7m6LxaKS72PHjkEPqo6VwO/S5Ofn674npOoa3eYo05wIhC9JHo/n1Hd/eN+Kcw7Cce+9905OTkYYc6hrSZxz7Va00+lMS0sDSRobG1u9ejVjLC8v78aNGyr3xPRNtU/U0tLCGNu9e7f/2YFWMVX2xdckkySjiZuIN8gELEqWlJTgR4LT6dSVJM45TN/wSV3REEmSgCLhEuFLEizfaocPsKgZ6ihJF7gwJElrB4+kPB5PZWXlgw8+ODw8rC3JORenz1944QUxtBHrqao5nYr3KStJRsvb/f39hYWFP/nJT4L/yaq+vr7s7OyioqK+vj7fd3+bN2+GswWwH4p7Dc58wLlc1XNR1TW4FqVNjkD4kqQoSn19vYoQHo/nqaeeUp1LGhsb6+npEXd48IhEXZJg1VxFX5U/YjkMq1Jra6skSdnZ2X19faK8ivcpK0mcc+0hALfbDWoiXi0KkgbT09Mvv/yyeJF7wYIFjLE9e/aohqjQC2Ia/sQTT+Czr6quEV1GCfMjEL4kwZIw7HOrXrt96KGHxNgbdtMYY6o1gmCgmQtJCqZd+D0Txlh1dTUQXVEU2D7LyckRP+2s4n0qS5L2qKTqBeywaQADW6M1I8652+1+4oknGGPFxcXiNJyqa4LpdCpjEgQikiTOObzAIZ5p2h8n8Xg8GzZssFqtqp2UYOKPlyTBDM7pdOINaUVR3n333fr6ejHcU/E+lSVJu4p3zz33nDhxQkzZIqEBDMFWrlxpNN2WZfmll17at2+fGEmpuiYYslEZkyAQqSSZJAxyY+4QwBSZu1b8WPZ6vTt27Fi0aNFHH33kpxhdSg4EMN/CPCqZHEBQFEYIYIoYlZnrfFmW79y5M9etkH0zIID5RpJkhh4xnQ+YIqZzjhxKOgQw30iSkq57oxEQpkg07JENQsAfAphvJEn+kErZa5giKQsCBR4zBDDfSJJiBnsiNYQpkkh+k6+JiQDmG0lSYvbhHHuNKTLHTZF5QoBjvpEkESF0EMAU0blMWYRAVBHAfCNJiiq0yWIMUyRZYqI4zIsA5htJknn7KY6eYYrE0Q1qOkUQwHwjSUqRTg8tTEyR0GpSaUIgdAQw30iSQscvBWpgiqRAuBRinBHAfCNJinNnmLN5TBFzekheJRMCmG8kScnUs1GLBVMkakbJECFggADmm44kKfSX8ggARVIeBgIgRggEkCS4TJ+EACFACMQSARhC6YySYukEtUUIEAKEACBgKEkG0z3KTiEEMEVSKGwKNU4IYL7pjJLi5BU1ayIEMEVM5Ba5kqQIYL6RJCVpJ0cWFqZIZJaoNiEQGAHMN5KkwHilYAlMkRQMn0KOMQKYbyRJMQY/MZrDFEkMj8nLREYA840kKZF7cs58xxSZs0bIMCHwvwhgvpEkES10EMAU0blMWYRAVBHAfCNJiiq0yWIMUyRZYqI4zIsA5htJknn7KY6eYYrE0Y0gmx4eHq6srASfVf/jbpAWqFh8EcB8I0mKb1+YtHVMEZO6+Ee3RkZG8vPzN27cOP3d39NPP3348GHxv3L/sRT9a2oEMN8SVZLgP2ieN29eR0eHqcFOTOcwRUwewf79+9PS0s6fP29yP8k9PwhgviWqJI2NjeXk5DDGampq/IRKl8JDAFMkPAsxq+V0Om02W29vb8xapIaijgDmW6JKEo2Sok4LbBBTBOebML1//36LxdLd3W1C38ilIBHAfEtUSQoyVCoWHgKYIuFZiFmt3t5em822Y8cOWj+KGeZRbwjzjSQp6vAmg0FMkZDiAYEoLS2dmZkJqWLYhRVFqa+vt9lsFy9eDNsIVYwvAphvkUqSLMsbN25kjDmdziCjamxsBA/C/rRaraq6drtdlePna2Zmpp+r2kspuFQBIATZobhYSJIUKhOC7zja9MCdYv405lukknT8+HGLxRKSJGGAdNcmZ2ZmSktLly9fPjQ0JApDZk5OztjYGGQC+1VSCMve2qd0R0cHY6yxsVEY1G2ac64oyu7duxljx44dE4VTLYEpElLsIUlSQMvHjh1jjDU3N/svadTp/mvRVfMggPkWkSRNTEw4HA4wp5KGIKM10oWWlhbG2NGjR4WdmEkS53xwcHDp0qXl5eUej0c4kFIJTJGQAo+uJH355Ze5ubkVFRWzs7N+3CBJ8gNOQlzCfAtfkmA0IUnSM888E91REuf88uXLGRkZdXV1AtBYSpLX662qqlq4cOHAwIBwIKUSmCIhBR5dSYJO1455VS6RJKkASbivmG/hS9LFixdtNtuWLVtaW1ujLknaSVksJYlzDsscp06dSrjejYrDmCJ+DPp8vrfffnvZsmWMMavV+swzz/zbv/2bzWYLKCJ+bOJLt2/fLiwsDGiNJAmDlohpzLcwJcnj8VRWVmZlZV27dg2WaaI7cYNFBLz0ExdJStlVUkwRI4rLsrxt2zYo6XA4SktLrVbr9773PcZYQBExsqnKb2trs1gse/bsUeWPjY09//zzixcvhtbhM1qNqtqirzFAAPMtTEk6cuSIWC2OriT5fL7Ozs6srCy73X7lyhUBx5xKktfrffPNN9esWTMxMcE5h21EmrgJ8HUTXV1dkiTl5ORcu3YNCkxNTa1duzYqkuRyud5++22bzZafnz8yMoId+OSTT7KzswsKCs6cOSPLMr5E6QRFIFJJGh4ezsvLKyoqmpyc5JxHV5JqampgFvDuu+/iw29zKkmc84MHDzLGKioqOjo6nn76acZYdXW11+tN0D6O0G1MEV1TiqJs2bJFux0Gi4DBD1ju3Lmzbt26hoYG0QrM2cGBxYsXf/zxx+IS59zr9VZXV6fgsQwMQvKlMd9CHiV5vd5t27ZJktTe3g7QhCFJLperpKQE/ND9PHHihAr3gJL0H//xHzabTdeayMQzQdVm3+TkZFFRkShZVlZ269YtlQ+p8xVw8BPv119/vWbNGq00hLq87Xa7y8rKtHtqsix/+OGH999/f1ZW1kcffSQ8GR8fz8vLW7Nmzddffy0yKZHoCGC+hSxJ586dS09P37hxoxgzhyFJHo+nvLwc/FDR+osvvrj77rsrKytVG/Dd3d12u93PuaSBgYGFCxeCTe1TGpz0I0mc8/b2dkmSsrOzP/74Y5/Pl+jdHIn/mCK6dmBFGXcHFAtVkmAnwWiODD88Ulxc7HK5wD5IkrZ/P/vss7q6ut/97nd4ZK3rOWWaEAHMt9AkCUY3sKotAgtDksT9zxibP39+f3+/sMY5b2trmzdv3vbt22FiOD4+/uqrr8KJbXwPqHblxBFHmH+pTrIEI0kwAGSMNTU1pTizMUVw14h0FCXp6tWrdrsdPy1EK9+u6+3atQv/9sjs7GxFRUVhYeHt27dFMZfLVVxcrMslUYYSZkYA8y00STp06BBUNvrUPr6MgFAUpampiTGGVUYUvn79ekVFhSRJsK503333vfrqq/fffz8urJIkzrnb7X7iiSd0TyQEI0mcc5i+SZLU2toqnEnBBKaIbvgwcdM+TsIYJXm93pqamry8vOHhYW1bcBoDb322tbVZrdbjx4+LwtAoSZIAJOESmG+hSdKpU6dqNX/r1q1jjP35n/95bW3t66+/rhqe+EFHUZTf/e53b7zxhpgD+ikccC0J6vp8vvPnz6v2aMQaPH4Uq9aSRNOXLl2yf/fX09MjMlMtgSmiG3u0lrfBOByXr62t1TKhsbFR9dsjsiy/8MIL8+bN+/nPfw4Tup6enoyMDMaYdr6v6zxlmg0BzLfQJEk3Et2J29jYWE9Pj5ZhuhaCyQxSkoxMBTlKguo9PT2gSin7KzyYIkaQwqpiwEMAQTKhtbU1LS2toaEBr+LBmmNBQcH4+Dh2Q1GUs2fPlpeXw3TearVmZmZmZ2ffuHEDF6N0oiCA+TYnkiTm9i0tLdECJZaSxDmH1XRJkn79619HK4QEsoMpYuS2oijPP/88lHQ4HD/4wQ+sVmt6ejo+lxQ8ExRFaW9vz8zMXL9+/fXr1xVFcbvdu3fvTktLC2YS3dXVlZaWRouARp1l8nzMtzmRJI/Hs2HDBqvVipcAIgQlxpLEOe/r63vkkUe0c8AIA0mI6pgifhz2+XzvvfeeeKFk69atH3/8cU5OjlhSDJUJk5OTO3fuFMeyV61adfbsWaOths8++0wsOP7whz/8i7/4C0mSfvGLX+Bxlh/n6ZJ5EMB8i4IkmScw8iRaCGCKRMtmdO1cu3YtKyursrJyYmLC5/N1dXUtXbr0z/7sz7Zu3RrF5YLo+kzWjBDAfCNJMkIppfMxRcwJxJ49e+x2+9WrV4V7IyMjK1asqKmpIUkSmCRKAvONJClRei2mfmKKxLThoBtzOp3aM9znzp3LyMg4cuRI0GaooCkQwHwjSTJFl5jNCUwRs/kG/tTV1akOTMIbcDU1NUuXLh0cHDSn2+SVLgKYbyRJuhCleiamiDmxuHLlyoIFC+DQgNvtFpM1OAteX19vtChuznBS3CvMN5KkFCeDfviYIvolTJB75swZ8TvLYrL27emBv/u7v9u/f3/K/oqDCXomZBcw30iSQoYvFSpgiqRCvBRjfBHAfCNJim9fmLR1TBGTukhuJRECmG8kSUnUsdELBVMkelbJEiGgjwDmG0mSPkYpnospkuJQUPgxQADzjSQpBoAnXhOYIonnPXmcaAhgvpEkJVrvxcRfTJGYNEiNpDQCmG8kSSlNBaPgMUWMylA+IRAtBDDfSJKihWpS2cEUSarAKBhTIoD5piNJCv2lPAJAkZSHgQCIEQIBJAku0ychQAgQArFEAAZwOqOkWDpBbREChAAhAAjoSJIpp5nkFCFACKQQAn8ySkqhuClUQoAQMCUC/x9dBmVTSStgIQAAAABJRU5ErkJggg==\" width=\"263\" height=\"170\"></p>",
                    options_en: ["<p>1-c, 2-a, 3-b, 4-d</p>", "<p>1-c, 2-a, 3-d, 4-b</p>", 
                                "<p>1-c, 2-d, 3-a, 4-b</p>", "<p>1-a, 2-c, 3-d, 4-b</p>"],
                    options_hi: ["<p>1-c, 2-a, 3-b, 4-d</p>", "<p>1-c, 2-a, 3-d, 4-b</p>",
                                "<p>1-c, 2-d, 3-a, 4-b</p>", "<p>1-a, 2-c, 3-d, 4-b</p>"],
                    solution_en: "<p>46.(b) <strong>1-c, 2-a, 3-d, 4-b.</strong> Country and Official language: Bangladesh(Bengali), Israel (Hebrew), Maldives (Dhivehi), Netherlands (Dutch), Portugal (Portuguese), Saudi Arabia (Arabic), Sri Lanka (Sinhala and Tamil).</p>",
                    solution_hi: "<p>46.(b) <strong>1-c, 2-a, 3-d, 4-b.</strong> देश और आधिकारिक भाषा: बांग्लादेश (बंगाली), इज़राइल (हिब्रू), मालदीव (धिवेही), नीदरलैंड (डच), पुर्तगाल (पुर्तगाली), सऊदी अरब (अरबी), श्रीलंका (सिंहली और तमिल)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which Article in the fundamental duty applies, when a construction worker takes his 8-year son to the site rather than sending him to school?</p>",
                    question_hi: "<p>47. जब एक निर्माण श्रमिक अपने 8 वर्षीय बेटे को स्कूल भेजने के बजाय उसे निर्माण स्थल पर ले जाता है, तो मौलिक कर्त्तव्य का कौन-सा अनुच्छेद लागू होता है?</p>",
                    options_en: ["<p>Article 51A (h)</p>", "<p>Article 51A (k)</p>", 
                                "<p>Article 51A (j)</p>", "<p>Article 51A (i)</p>"],
                    options_hi: ["<p>अनुच्छेद 51A (h)</p>", "<p>अनुच्छेद 51A (k)</p>",
                                "<p>अनुच्छेद 51A (j)</p>", "<p>अनुच्छेद 51A (i)</p>"],
                    solution_en: "<p>47.(b) <strong>Article 51A (k)</strong> states that \"Who is a parent or guardian, to provide opportunities for education to his child or, as the case may be, ward between the age of six and fourteen years.\" It was added by the 86th Amendment Act, 2002. The Right to Education (RTE) Act, 2009, makes education compulsory for children between 6-14 years. Child labor is prohibited under the Child Labour (Prohibition and Regulation) Act, 1986.</p>",
                    solution_hi: "<p>47.(b) <strong>अनुच्छेद 51A (k)</strong> में कहा गया है कि \"यदि माता-पिता या संरक्षक है, छह वर्ष से चौदह वर्ष तक की आयु वाले अपने, यथास्थिति, बालक या प्रतिपाल्य के लिए शिक्षा के अवसर प्रदान करे&rsquo;&rsquo;। इसे 86वें संशोधन अधिनियम, 2002 द्वारा जोड़ा गया था। शिक्षा का अधिकार (RTE) अधिनियम, 2009, 6-14 वर्ष के बच्चों के लिए शिक्षा को अनिवार्य बनाता है। बाल श्रम (निषेध और विनियमन) अधिनियम, 1986 के तहत बाल श्रम निषिद्ध है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Exfoliation is a form of _________.</p>",
                    question_hi: "<p>48. अपशल्कन (Exfoliation)_________ का एक रूप है।</p>",
                    options_en: ["<p>physical weathering</p>", "<p>chemical weathering</p>", 
                                "<p>mass wasting</p>", "<p>biochemical weathering</p>"],
                    options_hi: ["<p>भौतिक अपक्षय</p>", "<p>रासायनिक अपक्षय</p>",
                                "<p>बृहत् क्षरण</p>", "<p>जैव रासायनिक अपक्षय</p>"],
                    solution_en: "<p>48.(a) <strong>Physical weathering</strong> is caused by the effects of changing temperature on rocks, causing the rock to break apart. The process is sometimes assisted by water. Exfoliation occurs as cracks develop parallel to the land surface, a consequence of the reduction in pressure during uplift and erosion.</p>",
                    solution_hi: "<p>48.(a) <strong>भौतिक अपक्षय</strong> चट्टानों पर तापमान में परिवर्तन के प्रभाव के कारण होता है, जिससे चट्टानें टूट जाती हैं। इस प्रक्रिया में कभी-कभी पानी भी सहायक होता है। अपशल्कन (Exfoliation) तब होता है जब भूमि की सतह के समानांतर दरारें विकसित होती हैं, जो उत्थान और अपरदन के दौरान दाब में कमी का परिणाम है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which Bill was passed by the Parliament, in July 2021, to help in the availability of working capital for the micro, small and medium enterprises sector?</p>",
                    question_hi: "<p>49. सूक्ष्म, लघु और मध्यम उद्यम क्षेत्र के लिए कार्यशील पूंजी की उपलब्धता में मदद करने के लिए जुलाई 2021 में संसद द्वारा कौन सा विधेयक पारित किया गया था?</p>",
                    options_en: ["<p>The Essential Commodities (Amendment) Bill, 2020</p>", "<p>The Minerals Laws (Amendment) Bill, 2020</p>", 
                                "<p>The Factoring Regulation (Amendment) Bill, 2021</p>", "<p>The Insolvency and Bankruptcy Code (Amendment) Bill, 2021</p>"],
                    options_hi: ["<p>आवश्यक वस्तु (संशोधन) विधेयक, 2020</p>", "<p>खनिज कानून (संशोधन) विधेयक, 2020</p>",
                                "<p>फैक्टरिंग विनियमन (संशोधन) विधेयक, 2021</p>", "<p>दीवाला और दीवालियापन सहिंता (संशोधन) विधेयक, 2021</p>"],
                    solution_en: "<p>49.(c) The Factoring Regulation (Amendment) Bill, 2021 amends the definitions of \"receivables\", \"assignment\", and \"factoring business\" to bring them at par with international definitions. The bill seeks to amend the Factoring Regulation Act, 2011 to widen the scope of entities which can engage in factoring business.</p>",
                    solution_hi: "<p>49.(c) फैक्टरिंग विनियमन (संशोधन) विधेयक, 2021 \"प्राप्तियों\", \"असाइनमेंट\" और \"फैक्टरिंग व्यवसाय\" की परिभाषाओं में संशोधन करता है ताकि उन्हें अंतरराष्ट्रीय परिभाषाओं के बराबर लाया जा सके। यह विधेयक फैक्टरिंग विनियमन अधिनियम, 2011 में संशोधन करने का प्रयास करता है ताकि फैक्टरिंग व्यवसाय में संलग्न संस्थाओं के दायरे को बढ़ाया जा सके।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Identify the structural formula for magnesium hydroxide.</p>",
                    question_hi: "<p>50. मैग्नीशियम हाइड्रॉक्साइड के संरचनात्मक सूत्र की पहचान करें।</p>",
                    options_en: ["<p>MgOH</p>", "<p>MgO<sub>2</sub></p>", 
                                "<p>Mg<sub>2</sub>H<sub>2</sub></p>", "<p>Mg(OH)<sub>2</sub></p>"],
                    options_hi: ["<p>MgOH</p>", "<p>MgO<sub>2</sub></p>",
                                "<p>Mg<sub>2</sub>H<sub>2</sub></p>", "<p>Mg(OH)<sub>2</sub></p>"],
                    solution_en: "<p>50.(d) <strong>Mg(OH)<sub>2</sub>.</strong> Magnesium hydroxide is an inorganic compound. It is naturally found as the mineral brucite. Magnesium hydroxide can be used as an antacid or a laxative in either an oral liquid suspension or chewable tablet form. Additionally, magnesium hydroxide has smoke suppressing and flame retardant properties and is thus used commercially as a fire retardant.</p>",
                    solution_hi: "<p>50.(d) <strong>Mg(OH)<sub>2</sub>.</strong> मैग्नीशियम हाइड्रॉक्साइड एक अकार्बनिक यौगिक है। यह प्राकृतिक रूप से खनिज ब्रुसाइट के रूप में पाया जाता है। मैग्नीशियम हाइड्रॉक्साइड को एंटासिड या रेचक के रूप में मौखिक तरल निलंबन (oral liquid suspension) या चबाने योग्य टैबलेट के रूप में इस्तेमाल किया जा सकता है। इसके अतिरिक्त, मैग्नीशियम हाइड्रॉक्साइड में धुंध शमन और ज्वाला मंदक गुण होते हैं और इस प्रकार इसे अग्निरोधी के रूप में व्यावसायिक रूप से उपयोग किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The following table indicates the number of students studying in three subjects in four colleges.<br><img src=\"data:image/png;base64,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\" width=\"191\" height=\"115\"><br>What is the ratio of the total number of students studying in the Physics to that of studying in Mathematics in all four colleges taken together?</p>",
                    question_hi: "<p>51. निम्नलिखित तालिका चार महाविद्यालयों में तीन विषयों में पढ़ने वाले विद्यार्थियों की संख्या को दर्शाती है।<br><img src=\"data:image/png;base64,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\" width=\"184\" height=\"111\"><br>सभी चार महाविद्यालयों में मिलाकर भौतिक विज्ञान में पढ़ने वाले विद्यार्थियों की कुल संख्या और गणित में पढ़ने वाले विद्यार्थियों की कुल संख्या का अनुपात क्या है?</p>",
                    options_en: ["<p>781 : 585</p>", "<p>775 : 601</p>", 
                                "<p>576 : 731</p>", "<p>776 : 603</p>"],
                    options_hi: ["<p>781 : 585</p>", "<p>775 : 601</p>",
                                "<p>576 : 731</p>", "<p>776 : 603</p>"],
                    solution_en: "<p>51.(d)<br>Required ratio = (420 + 435 + 412 + 285) : (256 + 310 + 295 + 345)</p>\n<p>= 1552 : 1206</p>\n<p>= 776 : 603</p>",
                    solution_hi: "<p>51.(d)<br>आवश्यक अनुपात = (420 + 435 + 412 + 285) : (256 + 310 + 295 + 345)</p>\n<p>= 1552 : 1206</p>\n<p>= 776 : 603</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. A chord of the larger among two concentric circles is of length 20 cm and it is tangent to the smaller circle. What is the area (in cm<sup>2</sup>) of the annular portion between the two circles?</p>",
                    question_hi: "<p>52. दो संकेंद्रित वृतों में से बड़े वृत कि एक जीवा कि लंबाई 20 cm है और यह छोटे वृत कि स्पर्शरेखा है दोनों वृतों के बीच के वलयाकार भाग का क्षेत्रफल (cm<sup>2</sup> में) कितना है ?</p>",
                    options_en: ["<p>100<math display=\"inline\"><mi>&#960;</mi></math></p>", "<p>164<math display=\"inline\"><mi>&#960;</mi></math></p>", 
                                "<p>122<math display=\"inline\"><mi>&#960;</mi></math></p>", "<p>175<math display=\"inline\"><mi>&#960;</mi></math></p>"],
                    options_hi: ["<p>100<math display=\"inline\"><mi>&#960;</mi></math></p>", "<p>164<math display=\"inline\"><mi>&#960;</mi></math></p>",
                                "<p>122<math display=\"inline\"><mi>&#960;</mi></math></p>", "<p>175<math display=\"inline\"><mi>&#960;</mi></math></p>"],
                    solution_en: "<p>52.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060021638.png\" alt=\"rId61\" width=\"143\" height=\"143\"><br>In the right angled &Delta;OCA<br>R<sup>2</sup> = (10)<sup>2</sup> + r<sup>2</sup><br>R<sup>2</sup> - r<sup>2</sup> = 100<br>Area of annular portion = &pi;(R<sup>2 </sup>- r<sup>2</sup>) = 100&pi;</p>",
                    solution_hi: "<p>52.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060021638.png\" alt=\"rId61\" width=\"153\" height=\"153\"><br>समकोण &Delta;OCA में<br>R<sup>2</sup> = (10)<sup>2</sup> + r<sup>2</sup><br>R<sup>2</sup> - r<sup>2</sup> = 100<br>वलयाकार भाग का क्षेत्रफल = &pi;(R<sup>2 </sup>- r<sup>2</sup>) = 100&pi;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The given pie-chart shows the percentage distribution of runs scored by a batsman in a test innings. Study the chart and answer the question that follows. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060021848.png\" alt=\"rId62\" width=\"224\" height=\"204\"> <br>If the batsman has scored a total of 440 runs, how many runs did he score by hitting fours?</p>",
                    question_hi: "<p>53. दिया गया पाई-चार्ट एक टेस्ट पारी में बल्लेबाज द्वारा बनाए गए रनों का प्रतिशत वितरण दर्शाता है। चार्ट का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060022018.png\" alt=\"rId63\" width=\"220\" height=\"206\"> <br>Ones :एक, Twos:दो, Threes:तीन, Fours:चौके, Sixes:छक्के <br>यदि बल्लेबाज ने कुल 440 रन बनाए हैं, तो उसने चौके मारकर कितने रन बनाए ?</p>",
                    options_en: ["<p>88</p>", "<p>172</p>", 
                                "<p>224</p>", "<p>132</p>"],
                    options_hi: ["<p>88</p>", "<p>172</p>",
                                "<p>224</p>", "<p>132</p>"],
                    solution_en: "<p>53.(d)<br>Required runs = 440 &times; 30% = 132</p>",
                    solution_hi: "<p>53.(d)<br>आवश्यक रन = 440 &times; 30% = 132</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Let a + b = 1, then the value of (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math>) is:</p>",
                    question_hi: "<p>54. मान लीजिए a + b = 1 है, तो (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math>) का मान कितना है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>b</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>b</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>"],
                    solution_en: "<p>54.(a)<br><strong>Given :-</strong> a + b = 1<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mi>a</mi><mi>b</mi></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                    solution_hi: "<p>54.(a)<br>a + b = 1 ( दिया गया है )</p>\n<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mi>a</mi><mi>b</mi></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. What is the volume in cubic units of a cylinder with a height equal to the diameter and a radius is 4 units?</p>",
                    question_hi: "<p>55. व्यास के बराबर ऊँचाई और 4 इकाई त्रिज्या वाले बेलन का घन इकाई में आयतन क्या है?</p>",
                    options_en: ["<p>128&pi;</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn><mi>&#960;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p>32&pi;</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn><mi>&#960;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>128&pi;</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn><mi>&#960;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>32&pi;</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn><mi>&#960;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>55.(a)<br>According to the question,<br>Radius = 4 unit<br>Diameter (2r) = height (h) = 8 unit<br>Volume of cylinder = &pi;r<sup>2</sup>h<br>&rArr; &pi;(4)<sup>2</sup>8 = 128 &pi;</p>",
                    solution_hi: "<p>55.(a)<br>प्रश्न के अनुसार,<br>त्रिज्या = 4 इकाई<br>व्यास (2r) = ऊँचाई (h) = 8 इकाई<br>बेलन का आयतन = &pi;r<sup>2</sup>h<br>&rArr; &pi;(4)<sup>2</sup>8 = 128 &pi;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A trader owes a merchant ₹9,810 due in 1 year, but the trader wants to settle the account after 6 months. If the rate of simple interest is 9% per annum, how much cash (in ₹) should he pay?</p>",
                    question_hi: "<p>56. एक बनिया पर एक व्यापारी का 1 वर्ष में ₹9,810 बकाया है, लेकिन बनिया 6 महीने के बाद हिसाब चुका देना चाहता है। यदि साधारण ब्याज दर 9% वार्षिक है, तो उसे कितना नकद (₹ में) भुगतान करना चाहिए?</p>",
                    options_en: ["<p>9,550</p>", "<p>9,450</p>", 
                                "<p>9,540</p>", "<p>9,405</p>"],
                    options_hi: ["<p>9,550</p>", "<p>9,450</p>",
                                "<p>9,540</p>", "<p>9,405</p>"],
                    solution_en: "<p>56.(d)<br>Cash should he pay = <math display=\"inline\"><mfrac><mrow><mn>9810</mn></mrow><mrow><mn>109</mn></mrow></mfrac></math> &times; 104.5 = Rs. 9405</p>",
                    solution_hi: "<p>56.(d)<br>नगद भुगतान की गई राशि = <math display=\"inline\"><mfrac><mrow><mn>9810</mn></mrow><mrow><mn>109</mn></mrow></mfrac></math> &times; 104.5 = Rs. 9405</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mo>(</mo><mi>x</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>, then the value of (sin(x) + cos(x))<sup>2</sup> is _____.</p>",
                    question_hi: "<p>57. यदि <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mo>(</mo><mi>x</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> है, तो (sin(x) + cos(x))<sup>2</sup> _____. का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>-</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>-</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>-</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>-</mo><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>57.(b) <br><math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mo>(</mo><mi>x</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>x</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>x</mi></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>x</mi></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>sin x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>sin x = sin 30&deg;</p>\n<p>&rArr; x = 30&deg;<br>Now,<br>(sin x&nbsp;+ cos x)<sup>2</sup><br>sin<sup>2</sup> x + cos<sup>2</sup> x + 2sin x.cos x<br>1 + 2sin x.cos x<br>1 + 2 &times; sin30&deg; &times; cos30&deg;<br>1 + 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></p>\n<p>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>57.(b) </p>\n<p><math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>x</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mo>(</mo><mi>x</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>x</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>x</mi></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>x</mi></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>sin x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>sin x = sin 30&deg;</p>\n<p>&rArr; x = 30&deg;</p>\n<p>अब,</p>\n<p>(sin x + cos x)<sup>2</sup><br>sin<sup>2</sup> x + cos<sup>2</sup> x + 2sin x.cos x<br>1 + 2sin x.cos x<br>1 + 2 &times; sin30&deg; &times; cos30&deg;<br>1 + 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></p>\n<p>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A circle\'s centre is connected to its 50 cm long chord by a perpendicular that is 21 cm long. Find the circle\'s radius.</p>",
                    question_hi: "<p>58. किसी वृत्त का केंद्र, उसकी 50 cm लंबी जीवा से 21 cm लंबे लंब द्वारा जुड़ा हुआ है। वृत्त की त्रिज्या ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>1065</mn></msqrt></math> cm</p>", "<p><math display=\"inline\"><msqrt><mn>1068</mn></msqrt></math> cm</p>", 
                                "<p><math display=\"inline\"><msqrt><mn>1064</mn></msqrt></math> cm</p>", "<p><math display=\"inline\"><msqrt><mn>1066</mn></msqrt></math> cm</p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>1065</mn></msqrt></math> cm</p>", "<p><math display=\"inline\"><msqrt><mn>1068</mn></msqrt></math> cm</p>",
                                "<p><math display=\"inline\"><msqrt><mn>1064</mn></msqrt></math> cm</p>", "<p><math display=\"inline\"><msqrt><mn>1066</mn></msqrt></math> cm</p>"],
                    solution_en: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060022255.png\" alt=\"rId64\" width=\"108\" height=\"105\"><br>Radius (OP) = <math display=\"inline\"><msqrt><mn>2</mn><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn><mo>+</mo><mn>441</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1066</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060022255.png\" alt=\"rId64\" width=\"108\" height=\"105\"><br>त्रिज्या (OP) = <math display=\"inline\"><msqrt><mn>2</mn><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn><mo>+</mo><mn>441</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1066</mn></msqrt></math> cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The radius of the circle is 8 cm. The distance of a point lying outside the circle from the centre is 17 cm. The length of the tangent drawn from the outside point to the circle is:</p>",
                    question_hi: "<p>59. एक वृत्त की त्रिज्या 8 cm है। वृत्त के बाहर एक बिंदु की वृत्&zwj;त के केंद्र से दूरी 17 cm है। बाहर वाले बिंदु से वृत्त तक खींची गई स्पर्श रेखा की लंबाई _______ होगी।</p>",
                    options_en: ["<p>18 cm</p>", "<p>15 cm</p>", 
                                "<p>16 cm</p>", "<p>19 cm</p>"],
                    options_hi: ["<p>18 cm</p>", "<p>15 cm</p>",
                                "<p>16 cm</p>", "<p>19 cm</p>"],
                    solution_en: "<p>59.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060022548.png\" alt=\"rId65\" width=\"201\" height=\"115\"><br>TQ = 17 - 8 = 9 cm<br>PT<sup>2</sup> = TQ &times; TO = 9 &times; 25 = 225 cm<sup>2</sup><br>PT = 15 cm</p>",
                    solution_hi: "<p>59.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060022548.png\" alt=\"rId65\" width=\"201\" height=\"115\"><br>TQ = 17 - 8 = 9 cm<br>PT<sup>2</sup> = TQ &times; TO = 9 &times; 25 = 225 cm<sup>2</sup><br>PT = 15 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Simplify <math display=\"inline\"><mfrac><mrow><msup><mrow><mn>25</mn><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>10</mn><mi>a</mi><mi>b</mi><mo>-</mo><mn>48</mn><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>5</mn><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi></mrow></mfrac></math> &times; (5a + 8b)</p>",
                    question_hi: "<p>60. <math display=\"inline\"><mfrac><mrow><msup><mrow><mn>25</mn><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>10</mn><mi>a</mi><mi>b</mi><mo>-</mo><mn>48</mn><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>5</mn><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi></mrow></mfrac></math> &times; (5a +8b) का मान क्या होगा ?</p>",
                    options_en: ["<p>25a<sup>2</sup> - 64b<sup>2</sup></p>", "<p>(5a - 8b)<sup>2</sup></p>", 
                                "<p>(5a + 8b)<sup>2</sup></p>", "<p>25a<sup>2</sup> - 36b<sup>2</sup></p>"],
                    options_hi: ["<p>25a<sup>2</sup> - 64b<sup>2</sup></p>", "<p>(5a - 8b)<sup>2</sup></p>",
                                "<p>(5a + 8b)<sup>2</sup></p>", "<p>25a<sup>2</sup> - 36b<sup>2</sup></p>"],
                    solution_en: "<p>60.(a)<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mn>25</mn><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>10</mn><mi>a</mi><mi>b</mi><mo>-</mo><mn>48</mn><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>5</mn><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi></mrow></mfrac></math> &times; (5a + 8b)<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mn>25</mn><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>40</mn><mi>a</mi><mi>b</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn><mi>a</mi><mi>b</mi><mo>-</mo><mn>48</mn><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>5</mn><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi></mrow></mfrac></math> &times; (5a + 8b)<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mn>5</mn><mi>a</mi><mo>(</mo><mn>5</mn><mi>a</mi></mrow><mrow></mrow></msup><mo>-</mo><mi>&#160;</mi><mn>8</mn><mi>b</mi><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi><mi>&#160;</mi><mo>(</mo><mn>5</mn><mi>b</mi><mo>-</mo><mn>8</mn><mi>b</mi><mo>)</mo></mrow><mrow><mn>5</mn><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi></mrow></mfrac></math> &times; (5a + 8b)<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>5</mn><mi>a</mi></mrow><mrow></mrow></msup><mo>-</mo><mi>&#160;</mi><mn>8</mn><mi>b</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mn>5</mn><mi>a</mi><mo>+</mo><mn>6</mn><mi>b</mi><mo>)</mo></mrow><mrow><mn>5</mn><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi></mrow></mfrac></math> &times; (5a + 8b)<br>(5a - 8b) &times; (5a + 8b)<br>(5a)<sup>2</sup> - (8b)<sup>2</sup> = 25a<sup>2</sup> - 64b<sup>2</sup></p>",
                    solution_hi: "<p>60.(a)<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mn>25</mn><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>10</mn><mi>a</mi><mi>b</mi><mo>-</mo><mn>48</mn><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>5</mn><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi></mrow></mfrac></math> &times; (5a + 8b)<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mn>25</mn><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>40</mn><mi>a</mi><mi>b</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn><mi>a</mi><mi>b</mi><mo>-</mo><mn>48</mn><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>5</mn><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi></mrow></mfrac></math> &times; (5a + 8b)<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mn>5</mn><mi>a</mi><mo>(</mo><mn>5</mn><mi>a</mi></mrow><mrow></mrow></msup><mo>-</mo><mi>&#160;</mi><mn>8</mn><mi>b</mi><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi><mi>&#160;</mi><mo>(</mo><mn>5</mn><mi>b</mi><mo>-</mo><mn>8</mn><mi>b</mi><mo>)</mo></mrow><mrow><mn>5</mn><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi></mrow></mfrac></math> &times; (5a + 8b)<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>5</mn><mi>a</mi></mrow><mrow></mrow></msup><mo>-</mo><mi>&#160;</mi><mn>8</mn><mi>b</mi><mo>)</mo><mi>&#160;</mi><mo>(</mo><mn>5</mn><mi>a</mi><mo>+</mo><mn>6</mn><mi>b</mi><mo>)</mo></mrow><mrow><mn>5</mn><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>b</mi></mrow></mfrac></math> &times; (5a + 8b)<br>(5a - 8b) &times; (5a + 8b)<br>(5a)<sup>2</sup> - (8b)<sup>2</sup> = 25a<sup>2</sup> - 64b<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. If tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>, then find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sin</mi><mi>A</mi><mo>-</mo><mn>3</mn><mi>cos</mi><mi>A</mi></mrow><mrow><mn>7</mn><mi>sin</mi><mi>A</mi><mo>+</mo><mn>3</mn><mi>cos</mi><mi>A</mi></mrow></mfrac></math> + 4.</p>",
                    question_hi: "<p>61. यदि tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sin</mi><mi>A</mi><mo>-</mo><mn>3</mn><mi>cos</mi><mi>A</mi></mrow><mrow><mn>7</mn><mi>sin</mi><mi>A</mi><mo>+</mo><mn>3</mn><mi>cos</mi><mi>A</mi></mrow></mfrac></math> + 4 का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>61.(a)<br>tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math><br>Now,<br><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mn>7</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> + 4<br>On dividing by <math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi></math> in fraction we get,<br><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> + 4 = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>61.(a)<br>tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math><br>अब,<br><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mn>7</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math> + 4<br>भिन्न में <math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi></math> से भाग देने पर हमें प्राप्त होता है, <br><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> + 4 = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The given table shows the number of runs scored in IPL matches by four Indian batsmen in a particular year. <br><img src=\"data:image/png;base64,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\" width=\"118\" height=\"123\"><br>What is the total number of runs scored by all four batsmen?</p>",
                    question_hi: "<p>62. दी गई तालिका किसी विशेष वर्ष में चार भारतीय बल्लेबाजों द्वारा IPL मैचों में बनाए गए रनों की संख्या को दर्शाती है।<br><img src=\"data:image/png;base64,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\" width=\"123\" height=\"123\"><br>सभी चार बल्लेबाजों द्वारा बनाए गए रनों की कुल संख्या कितनी है?</p>",
                    options_en: ["<p>3270</p>", "<p>2900</p>", 
                                "<p>3760</p>", "<p>3300</p>"],
                    options_hi: ["<p>3270</p>", "<p>2900</p>",
                                "<p>3760</p>", "<p>3300</p>"],
                    solution_en: "<p>62.(a)<br>Total number of runs scored by all four batsmen = 800 + 620 + 1200 + 650 = 3270</p>",
                    solution_hi: "<p>62.(a)<br>चारों बल्लेबाजों द्वारा बनाए गए रनों की कुल संख्या = 800 + 620 + 1200 + 650 = 3270</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. 2 varieties of tea powders worth ₹250/kg and ₹300/kg are mixed with a third variety of tea powder in the ratio 1 : 1 : 2. If the mixture is worth ₹350/kg, find the price of the third variety tea powder.</p>",
                    question_hi: "<p>63. ₹250/kg और ₹300/kg मूल्य के दो प्रकार के चाय के पाउडरों को तीसरे प्रकार के चाय के पाउडर के साथ 1 : 1 : 2 के अनुपात में मिलाया जाता है। यदि मिश्रण का मूल्य ₹350/kg है, तो तीसरे प्रकार के चाय के पाउडर का मूल्य ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹450/kg</p>", "<p>₹400/kg</p>", 
                                "<p>₹425/kg</p>", "<p>₹500/kg</p>"],
                    options_hi: ["<p>₹450/kg</p>", "<p>₹400/kg</p>",
                                "<p>₹425/kg</p>", "<p>₹500/kg</p>"],
                    solution_en: "<p>63.(c) Let the third variety of tea powder be x<br>According to the question,<br>250 &times; 1 + 300 &times; 1 + 2 &times; x&nbsp;= 350 &times; 4<br>550 + 2x&nbsp;= 1400<br>2x&nbsp;= 850<br>x = ₹ 425 /kg</p>",
                    solution_hi: "<p>63.(c) माना कि चाय पाउडर की तीसरी किस्म x&nbsp;है<br>प्रश्न के अनुसार,<br>250 &times; 1 + 300 &times; 1 + 2 &times; x&nbsp;= 350 &times; 4<br>550 + 2x&nbsp;= 1400<br>2x&nbsp;= 850<br>x = ₹ 425 /kg</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If sinB = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>, what is the value of cosB(sec B - tan B)? Given that 0 &lt; B &lt; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#960;</mi><mn>2</mn></mfrac></math></p>",
                    question_hi: "<p>64. यदि sinB = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> है, तो cosB(sec B - tan B) का मान कितना होगा? दिया गया है कि 0 &lt; B &lt; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#960;</mi><mn>2</mn></mfrac></math> है।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>64.(d)<br>sinB = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math><br>now,<br>cos B(sec B - tan B)<br>cos B &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>B</mi></mrow></mfrac></math> - cos B &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>B</mi></mrow><mrow><mi>cos</mi><mi>B</mi></mrow></mfrac></math><br>= 1 - sin B<br>= 1 - <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>17</mn></mfrac></math></p>",
                    solution_hi: "<p>64.(d)<br>sinB = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math><br>अब,<br>cos B(sec B - tan B)<br>cos B &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>B</mi></mrow></mfrac></math> - cos B &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>B</mi></mrow><mrow><mi>cos</mi><mi>B</mi></mrow></mfrac></math><br>= 1 - sin B<br>= 1 - <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>17</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A discount series of 10% and 16% on an invoice is the same as a single discount of:</p>",
                    question_hi: "<p>65. किसी इन्&zwj;वॉइस पर 10% और 16% की क्रमिक छूटें ______ की एकल छूट के बराबर है।</p>",
                    options_en: ["<p>24.4%</p>", "<p>22.4%</p>", 
                                "<p>21.4%</p>", "<p>23.4%</p>"],
                    options_hi: ["<p>24.4%</p>", "<p>22.4%</p>",
                                "<p>21.4%</p>", "<p>23.4%</p>"],
                    solution_en: "<p>65.(a)<br>Net discount = 10 + 16 - <math display=\"inline\"><mfrac><mrow><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>16</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 24.4 %</p>",
                    solution_hi: "<p>65.(a)<br>शुद्ध छूट = 10 + 16 - <math display=\"inline\"><mfrac><mrow><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>16</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 24.4 %</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. ABC is an equilateral triangle. P, Q and R are the mid-points of sides AB, BC and CA, respectively. If the length of the side of the triangle ABC is 11 cm, then the area (in cm<sup>2</sup>) of &Delta;PQR is:</p>",
                    question_hi: "<p>66. ABC एक समबाहु त्रिभुज है। P, Q और R क्रमशः भुजा AB, BC और CA के मध्य-बिंदु हैं। यदि त्रिभुज ABC की भुजा की लंबाई 11 cm है, तो &Delta;PQR का क्षेत्रफल (cm<sup>2</sup> में) ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>21</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>121</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>111</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>21</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>121</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>111</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>66.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060022946.png\" alt=\"rId66\" width=\"150\" height=\"136\"><br>&Delta;ABC is an equilateral triangle.<br>Area of &Delta;PQR = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> &times; area of &Delta;ABC<br>Area of &Delta;PQR = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> &times; 11 &times; 11</p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>121</mn><msqrt><mn>3</mn></msqrt></mrow><mn>16</mn></mfrac></math> cm<sup>2</sup></p>",
                    solution_hi: "<p>66.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060022946.png\" alt=\"rId66\" width=\"150\" height=\"136\"><br>&Delta;ABC एक समबाहु त्रिभुज है.<br>&Delta;PQR का क्षेत्रफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> &times; &Delta;ABC का क्षेत्रफल<br>&Delta;PQR का क्षेत्रफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> &times; 11 &times; 11</p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>121</mn><msqrt><mn>3</mn></msqrt></mrow><mn>16</mn></mfrac></math> cm<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. P and Q start running in opposite directions on a circular track from the same point. If their speeds are 10 m/s and 8 m/s, respectively, then after what time will they meet if the length of the track is 1620 m?</p>",
                    question_hi: "<p>67. P और Q एक ही बिंदु से एक वृत्ताकार ट्रैक पर विपरीत दिशाओं में दौड़ना शुरू करते हैं। यदि उनकी चाल क्रमशः 10 m/s और 8 m/s है, यदि ट्रैक की लंबाई 1620 m है, तो वे कितने समय बाद मिलेंगे?</p>",
                    options_en: ["<p>110 seconds</p>", "<p>70 seconds</p>", 
                                "<p>120 seconds</p>", "<p>90 seconds</p>"],
                    options_hi: ["<p>110 सेकंड</p>", "<p>70 सेकंड</p>",
                                "<p>120 सेकंड</p>", "<p>90 सेकंड</p>"],
                    solution_en: "<p>67.(d) <br>Relative speed of P and Q running opposite direction = 10 + 8 = 18 m/s<br>Time taken to meet = <math display=\"inline\"><mfrac><mrow><mn>1620</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 90 seconds</p>",
                    solution_hi: "<p>67.(d) <br>विपरीत दिशा में चलने पर P और Q की सापेक्ष गति = 10 + 8 = 18 m/s<br>मिलने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>1620</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 90 सेकंड</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Simplify <br>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + 1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; 1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &divide; 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>",
                    question_hi: "<p>68. निम्नलिखित को सरल कीजिए।<br>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + 1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; 1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &divide; 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>",
                    options_en: ["<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", 
                                "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                                "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>68.(b) <br>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + 1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; 1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &divide; 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> &divide; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>+</mo><mn>5</mn></mrow><mn>6</mn></mfrac></math></p>\n<p>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac></math> = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>",
                    solution_hi: "<p>68.(b) </p>\n<p>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + 1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; 1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> &divide; 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> &divide; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>+</mo><mn>5</mn></mrow><mn>6</mn></mfrac></math></p>\n<p>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac></math> = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Two different quantities of the same solution having ingredients A and B are stored in two different containers. In the first container, there are 252 litre of A and 441 litre of B. In the second container, the total quantity of the solution was 1188 litre. How much of the solution in the second container was made up of ingredient B?</p>",
                    question_hi: "<p>69. सामग्रियों A और B वाले समान विलयन की दो भिन्न मात्राओं को दो भिन्न पात्रों में रखा जाता है। पहले पात्र में 252 लीटर A और 441 लीटर B है। दूसरे पात्र में विलयन की कुल मात्रा 1188 लीटर थी। दूसरे पात्र का कितना विलयन सामग्री B से बनाया गया था?</p>",
                    options_en: ["<p>765 litre</p>", "<p>756 litre</p>", 
                                "<p>752 litre</p>", "<p>760 litre</p>"],
                    options_hi: ["<p>765 लीटर</p>", "<p>756 लीटर</p>",
                                "<p>752 लीटर</p>", "<p>760 लीटर</p>"],
                    solution_en: "<p>69.(b) <br>In 1<sup>st</sup>&nbsp;container - <br>Ratio of ingredients A and B = 252 : 441 = 12 : 21<br>In 2<sup>nd</sup>&nbsp;container - <br>Quantity of ingredient B = <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math> &times; 1188 = 756 litre</p>",
                    solution_hi: "<p>69.(b) <br>पहले कंटेनर में - <br>सामग्री A और B का अनुपात = 252 : 441 = 12 : 21<br>दूसरे कंटेनर में - <br>सामग्री B की मात्रा = <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math> &times; 1188 = 756 लीटर</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. P and Q are two fixed points 10 cm apart, and R is a point on PQ such that PR = 6 cm. By what percentage is the length of QR decreased when the length of PR is increased by 5%?</p>",
                    question_hi: "<p>70. P और Q एक-दूसरे से 10 cm की दूरी पर दो नियत बिंदु (fixed points) हैं, और R, PQ पर ऐसा बिंदु है कि PR = 6 cm है। जब PR की लंबाई 5% बढ़ाई जाती है तो QR की लंबाई कितने प्रतिशत कम हो जाती है?</p>",
                    options_en: ["<p>8.5%</p>", "<p>7.5%</p>", 
                                "<p>7%</p>", "<p>8%</p>"],
                    options_hi: ["<p>8.5%</p>", "<p>7.5%</p>",
                                "<p>7%</p>", "<p>8%</p>"],
                    solution_en: "<p>70.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060023217.png\" alt=\"rId67\" width=\"271\" height=\"72\"><br>Length&nbsp; &rarr; PR&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; QR<br>Initial&nbsp; &nbsp; &rarr;&nbsp; 6&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;4<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &darr; + 5%<br>Final&nbsp; &nbsp; &nbsp;&rarr; 6.30 then QR = 3.7<br>Decreased % = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 0.30 &times; 25 = 7.5 %</p>",
                    solution_hi: "<p>70.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060023217.png\" alt=\"rId67\" width=\"301\" height=\"80\"><br>लंबाई&nbsp; &nbsp; &nbsp;&rarr; PR&nbsp; &nbsp; &nbsp; &nbsp; : QR<br>प्रारंभिक&nbsp; &rarr;&nbsp; 6&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;4<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &darr; + 5%<br>अंतिम&nbsp; &nbsp; &nbsp;&rarr; 6.30 तब QR = 3.7<br>% कमी = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 0.30 &times; 25 = 7.5 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A pipe can fill an empty tank in 5 minutes and another pipe can empty it in 6 minutes. If both pipes are opened simultaneously, how much time (in minutes) will it take to fill the empty tank?</p>",
                    question_hi: "<p>71. एक पाइप एक खाली टंकी को 5 मिनट में भर सकता है और दूसरा पाइप इसे 6 मिनट में खाली कर सकता है। यदि दोनों पाइप को एक साथ खोल दिया जाए, तो खाली टंकी को भरने में कितना समय (मिनट में) लगेगा?</p>",
                    options_en: ["<p>33 min</p>", "<p>30 min</p>", 
                                "<p>35 min</p>", "<p>25 min</p>"],
                    options_hi: ["<p>33 मिनट</p>", "<p>30 मिनट</p>",
                                "<p>35 मिनट</p>", "<p>25 मिनट</p>"],
                    solution_en: "<p>71.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060023474.png\" alt=\"rId68\" width=\"203\" height=\"107\"><br>Net efficiency of A and B = 6 - 5 = 1 unit<br>Time taken together to fill tank = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 30 min.</p>",
                    solution_hi: "<p>71.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060023654.png\" alt=\"rId69\" width=\"174\" height=\"100\"><br>A और B की शुद्ध दक्षता = 6 - 5 = 1 इकाई<br>टंकी को भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 30 मिनट</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The given graph represents the data on the ages of various children. <br>Study the given graph and answer the question that follows. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060023861.png\" alt=\"rId70\" width=\"198\" height=\"137\"> <br>What is the difference between the mean and mode of the ages?</p>",
                    question_hi: "<p>72. दिया गया ग्राफ़ विभिन्न बच्चों की आयु का डेटा दर्शाता है। <br>दिए गए ग्राफ़ का अध्ययन कीजिए और दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731060024036.png\" alt=\"rId71\" width=\"216\" height=\"171\"> <br>आयु के माध्य और बहुलक के बीच कितना अंतर है?</p>",
                    options_en: ["<p>2</p>", "<p>0.5</p>", 
                                "<p>1</p>", "<p>0</p>"],
                    options_hi: ["<p>2</p>", "<p>0.5</p>",
                                "<p>1</p>", "<p>0</p>"],
                    solution_en: "<p>72.(d)<br><img src=\"data:image/png;base64,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\" width=\"155\" height=\"144\"><br>Mean = <math display=\"inline\"><mfrac><mrow><mi>&#931;</mi><msub><mrow><msub><mrow><mi>x</mi></mrow><mrow><mi>i</mi></mrow></msub><mi>f</mi></mrow><mrow><mi>i</mi></mrow></msub></mrow><mrow><mi>&#931;</mi><msub><mrow><mi>f</mi></mrow><mrow><mi>i</mi></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>630</mn><mn>210</mn></mfrac></math> = 3<br>Mode is the age having highest no. of children<br>Hence, mode = 3<br>Required difference = 3 - 3 = 0</p>",
                    solution_hi: "<p>72.(d)<br><img src=\"data:image/png;base64,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\" width=\"160\" height=\"149\"><br>माध्य = <math display=\"inline\"><mfrac><mrow><mi>&#931;</mi><msub><mrow><msub><mrow><mi>x</mi></mrow><mrow><mi>i</mi></mrow></msub><mi>f</mi></mrow><mrow><mi>i</mi></mrow></msub></mrow><mrow><mi>&#931;</mi><msub><mrow><mi>f</mi></mrow><mrow><mi>i</mi></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>630</mn><mn>210</mn></mfrac></math> = 3<br>बहुलक वह आयु है जिसमे बच्चों की संख्या सबसे अधिक है। <br>अत: बहुलक = 3<br>आवश्यक अंतर = 3 - 3 = 0</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Ramesh sells rice at ₹36 per kg, which he purchased for ₹30 per kg. Moreover, he gives only 800 g of rice instead of 1 kg while selling. Find the actual profit percentage of Ramesh.</p>",
                    question_hi: "<p>73. रमेश ₹36 प्रति kg की दर से चावल बेचता है, जिसे उसने ₹30 प्रति kg की दर से खरीदा था। इसके अतिरिक्त, वह बेचते समय 1 kg की जगह केवल 800 g चावल देता है। रमेश का वास्तविक लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>46%</p>", "<p>50%</p>", 
                                "<p>48%</p>", "<p>52%</p>"],
                    options_hi: ["<p>46%</p>", "<p>50%</p>",
                                "<p>48%</p>", "<p>52%</p>"],
                    solution_en: "<p>73.(b)<br>Ratio&nbsp; &nbsp; &nbsp;&rarr; Initial :&nbsp; final <br>Weight&nbsp; &rarr;&nbsp; 800&nbsp; &nbsp;: 1000<br>Price&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp;30&nbsp; &nbsp; :&nbsp; 36<br>-------------------------------------<br>Final&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp;20&nbsp; &nbsp; &nbsp;:&nbsp; 30<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 50%</p>",
                    solution_hi: "<p>73.(b)<br>अनुपात&nbsp; &rarr;&nbsp; आरंभिक : अंतिम <br>वज़न&nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp;800&nbsp; &nbsp; &nbsp; : 1000<br>कीमत&nbsp; &nbsp; &rarr;&nbsp; &nbsp; 30&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; 36<br>-------------------------------------<br>अंतिम&nbsp; &nbsp; &rarr;&nbsp; &nbsp; 20&nbsp; &nbsp; &nbsp; &nbsp;: 30<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 50%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Which digits should come in the place of x and y, respectively, if the number 62684xy is divisible by both 8 and 5?</p>",
                    question_hi: "<p>74. यदि संख्या 62684xy, 8 और 5 दोनों से विभाज्य है, तो क्रमशः x और y के स्थान पर कौन से अंक आने चाहिए?</p>",
                    options_en: ["<p>5 and 0</p>", "<p>4 and 0</p>", 
                                "<p>2 and 0</p>", "<p>0 and 5</p>"],
                    options_hi: ["<p>5 और 0</p>", "<p>4 और 0</p>",
                                "<p>2 और 0</p>", "<p>0 और 5</p>"],
                    solution_en: "<p>74.(b)<br>Given number = 62684xy<br>For divisibility of 5 :- last digit of the number must be 0 or 5.<br>Hence, y&nbsp;= 0 or 5<br>For divisibility of 8 :- last 3 digit must be divisible by 8.<br>therefore,<br>When, y&nbsp;= 0 then x = 0 or 4 or 8<br>From the option we can see that only option (b) satisfies the condition.</p>",
                    solution_hi: "<p>74.(b)<br>संख्या = 62684xy<br>5 की विभाज्यता के लिए:- संख्या का अंतिम अंक 0 या 5 होना चाहिए।<br>अतः, y&nbsp;= 0 या 5<br>8 से विभाज्य होने के लिए :- अंतिम 3 अंक 8 से विभाज्य होने चाहिए।<br>इसलिए,<br>जब, y&nbsp;= 0 तब x = 0 या 4 या 8<br>विकल्प से हम देख सकते हैं कि केवल विकल्प (b) ही शर्त को पूरा करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. For which of the following values of a and b do the given equations, have NO solution? <br>x - ay = 2 - a <br>(1 - a)x + 6y = a + b</p>",
                    question_hi: "<p>75. निम्नलिखित में से a और b के किन मानों के लिए दिए गए समीकरणों में कोई हल नहीं है? <br>x - ay = 2 - a <br>(1 - a)x + 6y = a + b</p>",
                    options_en: ["<p>a = -3,b &ne; 1</p>", "<p>a = 3,b &ne;-1</p>", 
                                "<p>a = -3,b &ne; -1</p>", "<p>a = 3,b &ne; 1</p>"],
                    options_hi: ["<p>a = -3,b &ne; 1</p>", "<p>a = 3,b &ne;-1</p>",
                                "<p>a = -3,b &ne; -1</p>", "<p>a = 3,b &ne; 1</p>"],
                    solution_en: "<p>75.(b) <br>x - ay = 2 - a<br>x - ay - (2 - a) = 0 &hellip; (i)<br>(1 - a)x + 6y - (a + b) &hellip; (ii)<br>For No solution<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> &ne; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math><br><img src=\"data:image/png;base64,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\" width=\"233\" height=\"150\"></p>",
                    solution_hi: "<p>75.(b) <br>x - ay = 2 - a<br>x - ay - (2 - a) = 0 &hellip; (i)<br>(1 - a)x + 6y - (a + b) &hellip; (ii)<br>कोई हल नहीं के लिए<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> &ne; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math><br><img src=\"data:image/png;base64,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\" width=\"226\" height=\"141\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Parts of the following sentence have been given as options. Select the option that contains an error. <br>I have the hundred reasons to not attend the meeting tomorrow.</p>",
                    question_hi: "<p>76. Parts of the following sentence have been given as options. Select the option that contains an error. <br>I have the hundred reasons to not attend the meeting tomorrow.</p>",
                    options_en: ["<p>I have the hundred reasons</p>", "<p>the meeting</p>", 
                                "<p>tomorrow</p>", "<p>to not attend</p>"],
                    options_hi: ["<p>I have the hundred reasons</p>", "<p>the meeting</p>",
                                "<p>tomorrow</p>", "<p>to not attend</p>"],
                    solution_en: "<p>76.(a) I have the hundred reasons<br>The reasons given in the sentence are not specific and we use indefinite article for a non-specific noun. Article &lsquo;a&rsquo; will be used as the word &lsquo;hundred&rsquo; begins with a consonant sound. Hence, &lsquo;I have a hundred reasons&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(a) I have the hundred reasons<br>Sentence में दिए गए reasons, specific नहीं हैं और हम non-specific noun के लिए indefinite article का use करते हैं। Article &lsquo;a&rsquo; का use किया जाएगा क्योंकि &lsquo;hundred&rsquo; शब्द consonant sound से शुरू होता है। अतः, &lsquo;I have a hundred reasons&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate ANTONYM of the word in brackets to fill in the blank. <br>Sheila ________ (failed) all her classes.</p>",
                    question_hi: "<p>77. Select the most appropriate ANTONYM of the word in brackets to fill in the blank. <br>Sheila ________ (failed) all her classes.</p>",
                    options_en: ["<p>passed</p>", "<p>flunked</p>", 
                                "<p>fizzled</p>", "<p>flopped</p>"],
                    options_hi: ["<p>passed</p>", "<p>flunked</p>",
                                "<p>fizzled</p>", "<p>flopped</p>"],
                    solution_en: "<p>77.(a) <strong>Passed-</strong> succeeded in an examination or test.<br><strong>Failed-</strong> did not succeed in achieving a desired goal.<br><strong>Flunked- </strong>failed to achieve a passing grade in a course or test.<br><strong>Fizzled</strong>- gradually failed or ended without success.<br><strong>Flopped</strong>- failed completely, especially in a performance or venture.</p>",
                    solution_hi: "<p>77.(a) <strong>Passed</strong> (उत्तीर्ण)- succeeded in an examination or test.<br><strong>Failed </strong>(असफल)- did not succeed in achieving a desired goal.<br><strong>Flunked</strong> (अनुत्तीर्ण)- failed to achieve a passing grade in a course or test.<br><strong>Fizzled</strong> (असफल)- gradually failed or ended without success.<br><strong>Flopped</strong> (पूरी तरह से असफल होना)- failed completely, especially in a performance or venture.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate meaning of the underlined idiom. <br>All his friends admired him because he was a <span style=\"text-decoration: underline;\">straight shooter</span>.</p>",
                    question_hi: "<p>78. Select the most appropriate meaning of the underlined idiom. <br>All his friends admired him because he was a <span style=\"text-decoration: underline;\">straight shooter</span>.</p>",
                    options_en: ["<p>Excellent shooter</p>", "<p>Complicated and confusing person</p>", 
                                "<p>Miserly and pessimistic individual</p>", "<p>A thoroughly upright straightforward person</p>"],
                    options_hi: ["<p>Excellent shooter</p>", "<p>Complicated and confusing person</p>",
                                "<p>Miserly and pessimistic individual</p>", "<p>A thoroughly upright straightforward person</p>"],
                    solution_en: "<p>78.(d) <strong>Straight shooter-</strong> a thoroughly upright straightforward person.</p>",
                    solution_hi: "<p>78.(d) <strong>Straight shooter-</strong> a thoroughly upright straightforward person./ पूर्णतया ईमानदार और सीधा-सादा व्यक्ति।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the option that can be used as a one-word substitute for the given group of words. <br>a large, flat area surrounded by seats used for sports or entertainment</p>",
                    question_hi: "<p>79. Select the option that can be used as a one-word substitute for the given group of words. <br>a large, flat area surrounded by seats used for sports or entertainment</p>",
                    options_en: ["<p>Senate</p>", "<p>Arena</p>", 
                                "<p>Department</p>", "<p>Garden</p>"],
                    options_hi: ["<p>Senate</p>", "<p>Arena</p>",
                                "<p>Department</p>", "<p>Garden</p>"],
                    solution_en: "<p>79.(b) <strong>Arena-</strong> a large, flat area surrounded by seats used for sports or entertainment.<br><strong>Senate-</strong> the group of politicians who have the most power to make laws in a government.<br><strong>Department- </strong>a division of a large organization such as a government, university, or business, dealing with a specific area of activity.<br><strong>Garden- </strong>a piece of ground adjoining a house, in which grass, flowers, and shrubs may be grown.</p>",
                    solution_hi: "<p>79.(b) <strong>Arena</strong> (अखाड़ा)- a large, flat area surrounded by seats used for sports or entertainment.<br><strong>Senate</strong> (मंत्रिसभा)- the group of politicians who have the most power to make laws in a government.<br><strong>Department</strong> (विभाग)- a division of a large organization such as a government, university, or business, dealing with a specific area of activity.<br><strong>Garden </strong>(उद्यान)- a piece of ground adjoining a house, in which grass, flowers, and shrubs may be grown.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>After I retire, I started a second career / as a writer and published several books / which allowed me to share my stories / and insights with a wider audience.</p>",
                    question_hi: "<p>80. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>After I retire, I started a second career / as a writer and published several books / which allowed me to share my stories / and insights with a wider audience.</p>",
                    options_en: ["<p>and insights with a wider audience</p>", "<p>After I retire, I started a second career</p>", 
                                "<p>as a writer and published several books</p>", "<p>which allowed me to share my stories</p>"],
                    options_hi: ["<p>and insights with a wider audience</p>", "<p>After I retire, I started a second career</p>",
                                "<p>as a writer and published several books</p>", "<p>which allowed me to share my stories</p>"],
                    solution_en: "<p>80.(b) After I retire, I started a second career<br>If two actions took place in the past then the 1st action must be in the Past perfect tense(Had + V<sub>3</sub>) and the 2nd action must be in the Simple Past tense(V<sub>2</sub>). However, the V<sub>3</sub> form of &lsquo;retire&rsquo; is &lsquo;retired&rsquo;. Hence, &lsquo;After I had retired (V<sub>3</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>80.(b) After I retire, I started a second career<br>यदि दो action, past में हुए हैं, तो पहला action, Past Perfect Tense (Had + V<sub>3</sub>) में होगा और दूसरा action, Simple Past Tense (V<sub>2</sub>) में होगा। हालाँकि, &lsquo;retire&rsquo; का V<sub>3</sub> form &lsquo;retired&rsquo; है। अतः, &lsquo;After I had retired (V<sub>3</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate synonym of the given word. <br>Celebrate</p>",
                    question_hi: "<p>81. Select the most appropriate synonym of the given word. <br>Celebrate</p>",
                    options_en: ["<p>Daunt</p>", "<p>Rejoice</p>", 
                                "<p>Bitch</p>", "<p>Assure</p>"],
                    options_hi: ["<p>Daunt</p>", "<p>Rejoice</p>",
                                "<p>Bitch</p>", "<p>Assure</p>"],
                    solution_en: "<p>81.(b) <strong>Rejoice-</strong> to feel or show great joy.<br><strong>Celebrate- </strong>to mark an occasion with festivities or joyful activities.<br><strong>Daunt- </strong>to make someone feel intimidated or apprehensive.<br><strong>Bitch-</strong> to complain or talk about someone in a negative way.<br><strong>Assure- </strong>to tell someone something positively to dispel any doubts.</p>",
                    solution_hi: "<p>81.(b) <strong>Rejoice </strong>(आनंदित होना)- to feel or show great joy.<br><strong>Celebrate </strong>(जश्न मनाना)- to mark an occasion with festivities or joyful activities.<br><strong>Daunt </strong>(भयभीत करना)- to make someone feel intimidated or apprehensive.<br><strong>Bitch</strong> (निंदा करना)- to complain or talk about someone in a negative way.<br><strong>Assure</strong> (आश्वासन देना)- to tell someone something positively to dispel any doubts.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "82. Select the most appropriate homophone to fill in the blank. <br />The ________ was decorated with candles and chocolates everywhere.",
                    question_hi: "82. Select the most appropriate homophone to fill in the blank. <br />The ________ was decorated with candles and chocolates everywhere.",
                    options_en: [" flour ", " flower ", 
                                " floor ", " flor"],
                    options_hi: [" flour ", " flower ",
                                " floor ", " flor"],
                    solution_en: "82.(c) floor<br />‘Floor’ is the lower surface of a room on which one may walk. The given sentence states that the floor was decorated with candles and chocolates everywhere. Hence, ‘floor’ is the most appropriate answer.",
                    solution_hi: "82.(c) floor<br />‘Floor’ कमरे की निचली सतह है जिस पर कोई चल सकता है। दिए गए sentence में कहा गया है कि फर्श (floor) को हर जगह candles और chocolates से सजाया गया था। अतः, ‘floor’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "83. Select the INCORRECTLY spelt word ",
                    question_hi: "83. Select the INCORRECTLY spelt word ",
                    options_en: [" Tangiable  ", " Terracotta", 
                                " Tertial ", " Terracide"],
                    options_hi: [" Tangiable  ", " Terracotta",
                                " Tertial ", " Terracide"],
                    solution_en: "83.(a) Tangiable <br />\'Tangible\' is the correct spelling.",
                    solution_hi: "83.(a) Tangiable <br />\'Tangible\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "84. Select the INCORRECTLY spelt word. ",
                    question_hi: "84. Select the INCORRECTLY spelt word. ",
                    options_en: [" Occurence ", " Harassment ", 
                                " Supersede ", " Exaggerate"],
                    options_hi: [" Occurence ", " Harassment ",
                                " Supersede ", " Exaggerate"],
                    solution_en: "84.(a) Occurence<br />\'Occurrence\' is the correct spelling.",
                    solution_hi: "84.(a) Occurence<br />\'Occurrence\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank. <br>She _______ the target by an inch and lost the gold medal. [hit]</p>",
                    question_hi: "<p>85. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank. <br>She _______ the target by an inch and lost the gold medal. [hit]</p>",
                    options_en: ["<p>touched</p>", "<p>bashed</p>", 
                                "<p>missed</p>", "<p>cleaned</p>"],
                    options_hi: ["<p>touched</p>", "<p>bashed</p>",
                                "<p>missed</p>", "<p>cleaned</p>"],
                    solution_en: "<p>85.(c) <strong>Missed-</strong> failed to hit, reach, or come into contact with.<br><strong>Hit-</strong> come into contact forcefully.<br><strong>Touched- </strong>made physical contact with.<br><strong>Bashed-</strong> hit hard.<br><strong>Cleaned-</strong> removed dirt, marks, or unwanted items from.</p>",
                    solution_hi: "<p>85.(c) <strong>Missed</strong> (चूकना)- failed to hit, reach, or come into contact with.<br><strong>Hit</strong> (प्रहार करना)- come into contact forcefully.<br><strong>Touched </strong>(स्पर्श करना)- made physical contact with.<br><strong>Bashed</strong> (जोर से मारना)- hit hard.<br><strong>Cleaned </strong>(साफ़ करना)- removed dirt, marks, or unwanted items from.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that can be used as a one-word substitute for the given group of words. <br>A period of ten years</p>",
                    question_hi: "<p>86. Select the option that can be used as a one-word substitute for the given group of words. <br>A period of ten years</p>",
                    options_en: ["<p>Century</p>", "<p>Decade</p>", 
                                "<p>Biennium</p>", "<p>lustrum</p>"],
                    options_hi: ["<p>Century</p>", "<p>Decade</p>",
                                "<p>Biennium</p>", "<p>lustrum</p>"],
                    solution_en: "<p>86.(b) <strong>Decade-</strong> a period of ten years.<br><strong>Century</strong>- a period of a hundred years.<br><strong>Biennium-</strong> a specified period of two years.<br><strong>Lustrum-</strong> a period of five years.</p>",
                    solution_hi: "<p>86.(b) <strong>Decade</strong> (दशक)- a period of ten years.<br><strong>Century </strong>(शतक)- a period of a hundred years.<br><strong>Biennium</strong> (द्विवर्षीय)- a specified period of two years.<br><strong>Lustrum</strong> (पंचवर्षीय)- a period of five years.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of the underlined idiom. <br>My uncle lives in New York, so I only see him <span style=\"text-decoration: underline;\">once in a blue moon</span>.</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of the underlined idiom. <br>My uncle lives in New York, so I only see him <span style=\"text-decoration: underline;\">once in a blue moon.</span></p>",
                    options_en: ["<p>Not very often</p>", "<p>Monthly</p>", 
                                "<p>Weekly</p>", "<p>Very often</p>"],
                    options_hi: ["<p>Not very often</p>", "<p>Monthly</p>",
                                "<p>Weekly</p>", "<p>Very often</p>"],
                    solution_en: "<p>87.(a) <strong>Once in a blue moon-</strong> not very often.</p>",
                    solution_hi: "<p>87.(a) <strong>Once in a blue moon</strong>- not very often./कभी-कभार।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the option that can be used as a one-word substitute for the given group of words.<br>One who eats a lot</p>",
                    question_hi: "<p>88. Select the option that can be used as a one-word substitute for the given group of words.<br>One who eats a lot</p>",
                    options_en: ["<p>Chauffeur</p>", "<p>Steward</p>", 
                                "<p>Glutton</p>", "<p>Chef</p>"],
                    options_hi: ["<p>Chauffeur</p>", "<p>Steward</p>",
                                "<p>Glutton</p>", "<p>Chef</p>"],
                    solution_en: "<p>88.(c) <strong>Glutton</strong>- one who eats a lot.<br><strong>Chauffeur-</strong> a person employed to drive a private or hired car.<br><strong>Steward</strong>- a person employed to look after the passengers on a ship, aircraft, or train.<br><strong>Chef-</strong> a professional cook, typically the chief cook in a restaurant or hotel.</p>",
                    solution_hi: "<p>88.(c) <strong>Glutton</strong> (पेटू/भुक्खड़)- one who eats a lot.<br><strong>Chauffeur </strong>(कार चालक)- a person employed to drive a private or hired car.<br><strong>Steward</strong> (वायुयान-परिचालक)- a person employed to look after the passengers on a ship, aircraft, or train.<br><strong>Chef</strong> (बावर्ची)- a professional cook, typically the chief cook in a restaurant or hotel.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate ANTONYM of the underlined word. <br>I was <span style=\"text-decoration: underline;\">happy</span> to know that Sheila passed her final exams.</p>",
                    question_hi: "<p>89. Select the most appropriate ANTONYM of the underlined word. <br>I was <span style=\"text-decoration: underline;\">happy</span> to know that Sheila passed her final exams.</p>",
                    options_en: ["<p>Sad</p>", "<p>Joyful</p>", 
                                "<p>upbeat</p>", "<p>Excited</p>"],
                    options_hi: ["<p>Sad</p>", "<p>Joyful</p>",
                                "<p>upbeat</p>", "<p>Excited</p>"],
                    solution_en: "<p>89.(a) <strong>Sad</strong><br><strong>Joyful- </strong>feeling, expressing, or causing great pleasure and happiness<br><strong>Upbeat-</strong> cheerful, optimistic, and positive<br><strong>Excited-</strong> very enthusiastic and eager</p>",
                    solution_hi: "<p>89.(a) <strong>Sad</strong><br><strong>Joyful</strong> (हर्षित)- feeling, expressing, or causing great pleasure and happiness<br><strong>Upbeat </strong>(जोशपूर्ण/आशावादी)- cheerful, optimistic, and positive<br><strong>Excited</strong> (उत्साहित)- very enthusiastic and eager</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate synonym for the underlined word in the given sentence. <br>One of Ram&rsquo;s <span style=\"text-decoration: underline;\">leisure</span> activities is tennis.</p>",
                    question_hi: "<p>90. Select the most appropriate synonym for the underlined word in the given sentence. <br>One of Ram&rsquo;s <span style=\"text-decoration: underline;\">leisure</span> activities is tennis.</p>",
                    options_en: ["<p>Main</p>", "<p>Lead</p>", 
                                "<p>Languish</p>", "<p>Recreation</p>"],
                    options_hi: ["<p>Main</p>", "<p>Lead</p>",
                                "<p>Languish</p>", "<p>Recreation</p>"],
                    solution_en: "<p>90.(d) <strong>Recreation-</strong> activity done for enjoyment when one is not working.<br><strong>Leisure-</strong> free time when one is not working or occupied.<br><strong>Main</strong>- chief or most important part.<br><strong>Lead</strong>- to guide or direct.<br><strong>Languish-</strong> to lose or lack vitality.</p>",
                    solution_hi: "<p>90.(d) <strong>Recreation</strong> (मनोरंजन)- activity done for enjoyment when one is not working.<br><strong>Leisure</strong> (अवकाश)- free time when one is not working or occupied.<br><strong>Main</strong> (प्रमुख)- chief or most important part.<br><strong>Lead</strong> (नेतृत्व/मार्गदर्शन करना)- to guide or direct.<br><strong>Languish</strong> (दुर्बल/शक्तिहीन)- to lose or lack vitality.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate option to substitute the underlined word segment in the given sentence.<br>The wedding was<span style=\"text-decoration: underline;\"> put on</span> until January.</p>",
                    question_hi: "<p>91. Select the most appropriate option to substitute the underlined word segment in the given sentence.<br>The wedding was <span style=\"text-decoration: underline;\">put on</span> until January.</p>",
                    options_en: ["<p>put in</p>", "<p>put off</p>", 
                                "<p>put down</p>", "<p>put up</p>"],
                    options_hi: ["<p>put in</p>", "<p>put off</p>",
                                "<p>put down</p>", "<p>put up</p>"],
                    solution_en: "<p>91.(b) put off<br>The phrasal verb &lsquo;put off&rsquo; means to delay something until a later time. The given sentence states that the wedding was put off until January. Hence, &lsquo;put off&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>91.(b) put off<br>Phrasal verb &lsquo;put off&rsquo; का अर्थ है किसी चीज़ को बाद के लिए टालना। दिए गए sentence में कहा गया है कि शादी (wedding) जनवरी तक टाल दी गई। अतः, &lsquo;put off&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the option that expresses the given sentence in passive voice. <br>The children have made the box.</p>",
                    question_hi: "<p>92. Select the option that expresses the given sentence in passive voice. <br>The children have made the box.</p>",
                    options_en: ["<p>The box is being made by the children.</p>", "<p>Children had made the box.</p>", 
                                "<p>The box had been made by the children.</p>", "<p>The box has been made by the children.</p>"],
                    options_hi: ["<p>The box is being made by the children.</p>", "<p>Children had made the box.</p>",
                                "<p>The box had been made by the children.</p>", "<p>The box has been made by the children.</p>"],
                    solution_en: "<p>92.(d) The box has been made by the children. (Correct)<br>(a) The box <span style=\"text-decoration: underline;\">is being</span> made by the children. (Incorrect Verb)<br>(b) Children had made the box. (Incorrect Sentence Structure)<br>(c) The box <span style=\"text-decoration: underline;\">had been made</span> by the children. (Incorrect Tense)</p>",
                    solution_hi: "<p>92.(d) The box has been made by the children. (Correct)<br>(a) The box <span style=\"text-decoration: underline;\">is being</span> made by the children. (गलत Verb)<br>(b) Children had made the box. (गलत Sentence Structure)<br>(c) The box <span style=\"text-decoration: underline;\">had been made</span> by the children. (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br>A. The upper part of the river <br>B. Continuous rain on <br>C. Week, there had been <br>D. During the previous</p>",
                    question_hi: "<p>93. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br>A. The upper part of the river <br>B. Continuous rain on <br>C. Week, there had been <br>D. During the previous</p>",
                    options_en: ["<p>DBAC</p>", "<p>DCBA</p>", 
                                "<p>ADCB</p>", "<p>ABCD</p>"],
                    options_hi: ["<p>DBAC</p>", "<p>DCBA</p>",
                                "<p>ADCB</p>", "<p>ABCD</p>"],
                    solution_en: "<p>93.(b) <strong>DCBA</strong><br>The given sentence starts with Part D as it introduces the main idea of the sentence, i.e. the time when something happened. Part D will be followed by Part C as it contains the verb of the sentence, &lsquo;had been&rsquo;. Further, Part B talks about continuous rain &amp; Part A states the area in which there had been rain. So, A will follow B. Going through the options, option &lsquo;b&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>93.(b) <strong>DCBA</strong><br>दिया गया Sentence Part D से शुरू होता है क्योंकि यह sentence के मुख्य विचार &lsquo;the time when something happened&rsquo; का परिचय देता है। Part D के बाद Part C आएगा क्योंकि इसमें sentence की verb \'had been\' है। इसके अलावा, Part B निरंतर बारिश (continuous rain) के बारे में बात करता है और Part A उस क्षेत्र को बताता है जिसमें बारिश हुई थी। इसलिए, B के बाद A आएगा। अतः options के माध्यम से जाने पर, option &lsquo;b&rsquo; में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the option that expresses the given sentence in passive voice. <br>They are organising a concert for charity.</p>",
                    question_hi: "<p>94. Select the option that expresses the given sentence in passive voice. <br>They are organising a concert for charity.</p>",
                    options_en: ["<p>A concert is being organised by them for charity.</p>", "<p>They are organising a charity concert.</p>", 
                                "<p>They are being organised a concert for charity.</p>", "<p>A concert for charity is organising by them.</p>"],
                    options_hi: ["<p>A concert is being organised by them for charity.</p>", "<p>They are organising a charity concert.</p>",
                                "<p>They are being organised a concert for charity.</p>", "<p>A concert for charity is organising by them.</p>"],
                    solution_en: "<p>94.(a) A concert is being organised by them for charity. (Correct)<br>(b) They are organising a charity concert. (Incorrect Sentence Structure)<br>(c) They are being organised a concert for charity. (Incorrect Sentence Structure)<br>(d) A concert for charity is organising by them. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>94.(a) A concert is being organised by them for charity. (Correct)<br>(b) They are organising a charity concert. (गलत Sentence Structure)<br>(c) They are being organised a concert for charity. (गलत Sentence Structure)<br>(d) A concert for charity is organising by them. (गलत Sentence Structure)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Given below are four jumbled sentences. Select the option that gives their correct logical sequence. <br>A. When we connect this design with a wire, electricity flows. <br>B. These metal strips were put in a sulphuric acid solution. <br>C. He made two strips of different metals. <br>D. Alessandro Volta discovered electric battery.</p>",
                    question_hi: "<p>95. Given below are four jumbled sentences. Select the option that gives their correct logical sequence. <br>A. When we connect this design with a wire, electricity flows. <br>B. These metal strips were put in a sulphuric acid solution. <br>C. He made two strips of different metals. <br>D. Alessandro Volta discovered electric battery.</p>",
                    options_en: ["<p>CBDA</p>", "<p>DCBA</p>", 
                                "<p>BCDA</p>", "<p>ACDB</p>"],
                    options_hi: ["<p>CBDA</p>", "<p>DCBA</p>",
                                "<p>BCDA</p>", "<p>ACDB</p>"],
                    solution_en: "<p>95.(b) <strong>DCBA</strong><br>Sentence D will be the starting line as it introduces the main idea of the parajumble i.e. &lsquo;the discovery of electric battery by Alessandro Volta&rsquo;. And, Sentence C begins to explain the process by stating that he made two strips of different metals. So, C will follow D. Further, Sentence B states that these metal strips were put in a sulphuric acid solution &amp; Sentence A states that electricity flows when we connect this design with a wire. So, A will follow B. Going through the options, option &lsquo;b&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>95.(b) <strong>DCBA</strong><br>Sentence D प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार &lsquo;the discovery of electric battery by Alessandro Volta&rsquo; का परिचय देता है। और, Sentence C यह बताकर process को explain करना शुरू करता है कि उसने अलग-अलग metals की दो strips बनाईं। इसलिए, D के बाद C आएगा। इसके अलावा, Sentence B बताता है कि इन metal की strips को sulphuric acid solution में डाला गया था और Sentence A बताता है कि जब हम इस design को wire से connect करते हैं तो electricity प्रवाहित होती है। इसलिए, B के बाद A आएगा। अतः options के माध्यम से जाने पर, option &lsquo;b&rsquo; में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:</strong> <br>Earthquake is the (96)_________ breaking and displacement of substantial portions of the earth\'s outer rocky crust. During an earthquake, rock disturbances can cause rivers to change their flow. Landslides (97)__________ by earthquakes can cause significant destruction and loss of life. Tsunamis are a (98)_________ of damaging waves caused by large earthquakes that occur under the ocean. Earthquakes rarely directly kill anyone. Many (99)__________ and injuries are caused by falling debris and the fall of structures. The (100)_________ of an earthquake relies on how much and how far rock cracks and shifts.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:</strong> <br>Earthquake is the (96)_________ breaking and displacement of substantial portions of the earth\'s outer rocky crust. During an earthquake, rock disturbances can cause rivers to change their flow. Landslides (97)__________ by earthquakes can cause significant destruction and loss of life. Tsunamis are a (98)_________ of damaging waves caused by large earthquakes that occur under the ocean. Earthquakes rarely directly kill anyone. Many (99)__________ and injuries are caused by falling debris and the fall of structures. The (100)_________ of an earthquake relies on how much and how far rock cracks and shifts.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: ["<p>innovative</p>", "<p>expansive</p>", 
                                "<p>deliberate</p>", "<p>abrupt</p>"],
                    options_hi: ["<p>innovative</p>", "<p>expansive</p>",
                                "<p>deliberate</p>", "<p>abrupt</p>"],
                    solution_en: "<p>96.(d) <strong>abrupt</strong><br>&lsquo;Abrupt&rsquo; means sudden and unexpected. The given passage states that Earthquake is the abrupt breaking and displacement of substantial portions of the earth&rsquo;s outer rocky crust. Hence, &lsquo;abrupt&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) <strong>abrupt</strong><br>&lsquo;Abrupt&rsquo; का अर्थ है अचानक और अप्रत्याशित। दिए गए passage में कहा गया है कि भूकंप(earthquake), पृथ्वी की बाहरी चट्टानी परत(outer crust) के बड़े हिस्से का अचानक टूटना और विस्थापित (displacement) होना है। अतः, &lsquo;abrupt&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong> <br>Earthquake is the (96)_________ breaking and displacement of substantial portions of the earth\'s outer rocky crust. During an earthquake, rock disturbances can cause rivers to change their flow. Landslides (97)__________ by earthquakes can cause significant destruction and loss of life. Tsunamis are a (98)_________ of damaging waves caused by large earthquakes that occur under the ocean. Earthquakes rarely directly kill anyone. Many (99)__________ and injuries are caused by falling debris and the fall of structures. The (100)_________ of an earthquake relies on how much and how far rock cracks and shifts.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong> <br>Earthquake is the (96)_________ breaking and displacement of substantial portions of the earth\'s outer rocky crust. During an earthquake, rock disturbances can cause rivers to change their flow. Landslides (97)__________ by earthquakes can cause significant destruction and loss of life. Tsunamis are a (98)_________ of damaging waves caused by large earthquakes that occur under the ocean. Earthquakes rarely directly kill anyone. Many (99)__________ and injuries are caused by falling debris and the fall of structures. The (100)_________ of an earthquake relies on how much and how far rock cracks and shifts.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: ["<p>triggered</p>", "<p>subdued</p>", 
                                "<p>blocked</p>", "<p>prevented</p>"],
                    options_hi: ["<p>triggered</p>", "<p>subdued</p>",
                                "<p>blocked</p>", "<p>prevented</p>"],
                    solution_en: "<p>97.(a) <strong>triggered</strong><br>&lsquo;Triggered&rsquo; means caused by a particular process. The given passage states that landslides triggered by earthquakes can cause significant destruction and loss of life. Hence, &lsquo;triggered&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(a) <strong>triggered</strong><br>&lsquo;Triggered&rsquo; का अर्थ है किसी विशेष प्रक्रिया के कारण उत्पन्न होना। दिए गए passage में कहा गया है कि भूकंप से होने वाले भूस्खलन (landslides) से बहुत अधिक विनाश और जीवन की हानि हो सकती है। अतः, &lsquo;triggered&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong> <br>Earthquake is the (96)_________ breaking and displacement of substantial portions of the earth\'s outer rocky crust. During an earthquake, rock disturbances can cause rivers to change their flow. Landslides (97)__________ by earthquakes can cause significant destruction and loss of life. Tsunamis are a (98)_________ of damaging waves caused by large earthquakes that occur under the ocean. Earthquakes rarely directly kill anyone. Many (99)__________ and injuries are caused by falling debris and the fall of structures. The (100)_________ of an earthquake relies on how much and how far rock cracks and shifts.<br>Select the most Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong> <br>Earthquake is the (96)_________ breaking and displacement of substantial portions of the earth\'s outer rocky crust. During an earthquake, rock disturbances can cause rivers to change their flow. Landslides (97)__________ by earthquakes can cause significant destruction and loss of life. Tsunamis are a (98)_________ of damaging waves caused by large earthquakes that occur under the ocean. Earthquakes rarely directly kill anyone. Many (99)__________ and injuries are caused by falling debris and the fall of structures. The (100)_________ of an earthquake relies on how much and how far rock cracks and shifts.<br>Select the most Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: ["<p>interruption</p>", "<p>arrest</p>", 
                                "<p>disorder</p>", "<p>succession</p>"],
                    options_hi: ["<p>interruption</p>", "<p>arrest</p>",
                                "<p>disorder</p>", "<p>succession</p>"],
                    solution_en: "<p>98.(d) <strong>succession</strong><br>&lsquo;Succession&rsquo; means coming one after the other. The given passage states that tsunamis are a succession of damaging waves caused by large earthquakes that occur under the ocean. Hence, &lsquo;succession&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) <strong>succession</strong><br>&lsquo;Succession&rsquo; का अर्थ है एक के बाद एक दूसरा आना। दिए गए passage में कहा गया है कि सुनामी (tsunami) समुद्र के नीचे आने वाले बड़े भूकंपों के कारण होने वाली विनाशकारी तरंगों(waves) का एक सिलसिला (succession) है। अतः, &lsquo;succession&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <em>Cloze Test:</em> <br>Earthquake is the (96)_________ breaking and displacement of substantial portions of the earth\'s outer rocky crust. During an earthquake, rock disturbances can cause rivers to change their flow. Landslides (97)__________ by earthquakes can cause significant destruction and loss of life. Tsunamis are a (98)_________ of damaging waves caused by large earthquakes that occur under the ocean. Earthquakes rarely directly kill anyone. Many (99)__________ and injuries are caused by falling debris and the fall of structures. The (100)_________ of an earthquake relies on how much and how far rock cracks and shifts.<br>Select the most Select Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong> <br>Earthquake is the (96)_________ breaking and displacement of substantial portions of the earth\'s outer rocky crust. During an earthquake, rock disturbances can cause rivers to change their flow. Landslides (97)__________ by earthquakes can cause significant destruction and loss of life. Tsunamis are a (98)_________ of damaging waves caused by large earthquakes that occur under the ocean. Earthquakes rarely directly kill anyone. Many (99)__________ and injuries are caused by falling debris and the fall of structures. The (100)_________ of an earthquake relies on how much and how far rock cracks and shifts.<br>Select the most Select Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: ["<p>survival</p>", "<p>fatalities</p>", 
                                "<p>durability</p>", "<p>viability</p>"],
                    options_hi: ["<p>survival</p>", "<p>fatalities</p>",
                                "<p>durability</p>", "<p>viability</p>"],
                    solution_en: "<p>99.(b) <strong>fatalities</strong><br>&lsquo;Fatalities&rsquo; means a death caused by an accident. The given passage states that many fatalities and injuries are caused by falling debris and the fall of structures. Hence, &lsquo;fatalities&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b)<strong> fatalities</strong><br>&lsquo;Fatalities&rsquo; का अर्थ है दुर्घटना के कारण हुई मृत्यु। दिए गए passage में कहा गया है कि कई मौतें(fatalities) और चोटें गिरते मलबे (debris) और ढांचों के गिरने के कारण होती हैं। अतः , &lsquo;fatalities&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. Cloze Test: <br>Earthquake is the (96)_________ breaking and displacement of substantial portions of the earth\'s outer rocky crust. During an earthquake, rock disturbances can cause rivers to change their flow. Landslides (97)__________ by earthquakes can cause significant destruction and loss of life. Tsunamis are a (98)_________ of damaging waves caused by large earthquakes that occur under the ocean. Earthquakes rarely directly kill anyone. Many (99)__________ and injuries are caused by falling debris and the fall of structures. The (100)_________ of an earthquake relies on how much and how far rock cracks and shifts.<br>Select the most Select Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong> <br>Earthquake is the (96)_________ breaking and displacement of substantial portions of the earth\'s outer rocky crust. During an earthquake, rock disturbances can cause rivers to change their flow. Landslides (97)__________ by earthquakes can cause significant destruction and loss of life. Tsunamis are a (98)_________ of damaging waves caused by large earthquakes that occur under the ocean. Earthquakes rarely directly kill anyone. Many (99)__________ and injuries are caused by falling debris and the fall of structures. The (100)_________ of an earthquake relies on how much and how far rock cracks and shifts.<br>Select the most Select Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: ["<p>depth</p>", "<p>corpse</p>", 
                                "<p>discipline</p>", "<p>magnitude</p>"],
                    options_hi: ["<p>depth</p>", "<p>corpse</p>",
                                "<p>discipline</p>", "<p>magnitude</p>"],
                    solution_en: "<p>100.(d) <strong>magnitude</strong><br>&lsquo;Magnitude&rsquo; means the intensity or the extent. The given passage states that the magnitude of an earthquake relies on how much and how far rock cracks and shifts. Hence, &lsquo;magnitude&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) <strong>magnitude</strong><br>&lsquo;Magnitude&rsquo; का अर्थ है तीव्रता या सीमा। दिए गए passage में कहा गया है कि भूकंप की तीव्रता इस बात पर निर्भर करती है कि चट्टान कितनी मात्रा में और कितनी दूर तक टूटती और खिसकती है। अतः, &lsquo;Magnitude&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>