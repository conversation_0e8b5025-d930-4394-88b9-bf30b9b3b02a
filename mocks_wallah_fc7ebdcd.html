<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">Which of the following expressions is equal to the expression <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    question_hi: "<p>1. <span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow><mrow><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo></mrow><mrow><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo></mrow><mrow><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo></mrow><mrow><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac></math> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2</mn></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    solution_en: "<p>1.(b) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math></p>\\r\\n<p>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow><mrow><mo>&nbsp;</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math></p>\\n",
                    solution_hi: "<p>1.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn></mrow><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow><mrow><mo>&nbsp;</mo><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math></span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">What is the fourth proportional of 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\"> , 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>8</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\"> and 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\"> ? </span></p>\\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\"> , 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>8</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> <span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p>40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\\n", 
                                "<p>30</p>\\n", "<p>20</p>\\n"],
                    options_hi: ["<p>10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math> <span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p>40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>\\n",
                                "<p>30</p>\\n", "<p>20</p>\\n"],
                    solution_en: "<p>2.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Fourth proportional =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>10</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>5</mn><msqrt><mn>8</mn></msqrt></mrow><mrow><mn>3</mn><msqrt><mn>5</mn></msqrt></mrow></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>5</mn><msqrt><mn>8</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 5&times;4 = 20</span></p>\\n",
                    solution_hi: "<p>2.(d)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2344;&#2369;&#2346;&#2366;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>10</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>5</mn><msqrt><mn>8</mn></msqrt></mrow><mrow><mn>3</mn><msqrt><mn>5</mn></msqrt></mrow></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>5</mn><msqrt><mn>8</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 5&times;4 = 20</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> If tan&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>S</mi><mi>i</mi><mi>n</mi><mo>&nbsp;</mo><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi></mrow></mfrac></msqrt></math></span><span style=\"font-family: Cambria Math;\"> is:</span></p>\\n",
                    question_hi: "<p>3. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> tan&theta; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>S</mi><mi>i</mi><mi>n</mi><mo>&nbsp;</mo><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>S</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi></mrow></mfrac></msqrt></math></span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>\\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>\\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\\n"],
                    solution_en: "<p>3.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">tan&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>P</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow><mrow><mi>B</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Hypotenuse = 17</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Now ,&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mi>&theta;</mi></mrow></mfrac></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mfrac><mrow><mn>1</mn><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><mn>17</mn></mfrac></mstyle></mrow><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><mn>17</mn></mfrac></mstyle></mrow></mfrac></msqrt></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>9</mn><mn>25</mn></mfrac></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>3.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp; tan&theta;&nbsp; =&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>&nbsp;</mo></mrow><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> = 17</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">,&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mi>&theta;</mi></mrow></mfrac></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mfrac><mrow><mn>1</mn><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><mn>17</mn></mfrac></mstyle></mrow><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><mn>17</mn></mfrac></mstyle></mrow></mfrac></msqrt></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">=&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>9</mn><mn>25</mn></mfrac></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math></span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Study the given table and answer the question that follows.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The table represents the mathematics marks of five students.</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image1.png\" width=\"404\" height=\"58\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">What is the average marks?</span></p>\\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_2274973411701670467681.png\" width=\"419\" height=\"79\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    options_en: ["<p>77</p>\\n", "<p>80</p>\\n", 
                                "<p>81</p>\\n", "<p>78</p>\\n"],
                    options_hi: ["<p>77</p>\\n", "<p>80</p>\\n",
                                "<p>81</p>\\n", "<p>78</p>\\n"],
                    solution_en: "<p>4.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Average mark = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>85</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>72</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>84</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>94</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 81</span></p>\\n",
                    solution_hi: "<p>4.(c)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>85</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>72</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>84</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>94</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 81</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\"> A man buys an article for &#8377;80 and marks it at &#8377;125. He then allows a discount of 40%. What is his percentage of loss or gain?</span></p>\\n",
                    question_hi: "<p>5. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> &#8377;80 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;125 </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 40% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>12% gain</p>\\n", "<p>10% gain</p>\\n", 
                                "<p>6.25% loss</p>\\n", "<p>12% loss</p>\\n"],
                    options_hi: ["<p>12% <span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span></p>\\n", "<p>10% <span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span></p>\\n",
                                "<p>6.25% <span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span></p>\\n", "<p>12% <span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span></p>\\n"],
                    solution_en: "<p>5.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Selling price = 125 &times; 60% = 75 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required loss % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>75</mn></mrow><mn>80</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 6.25%</span></p>\\n",
                    solution_hi: "<p>5.(c)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 125 &times; 60% = 75 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>75</mn></mrow><mrow><mo>&nbsp;</mo><mn>80</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 6.25%</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> Subhas, a 3.15 m tall tree, and an 11.25 m tall building are positioned such that their feet on the ground are collinear and the tree is located between Subhas and the building. </span><span style=\"font-family: Cambria Math;\">The tree is located at a distance of 7.5 m from Subhas and at a distance of 45 m from the building. Further, the eyes of Subhas, the top of the tree, and the top of the building fall in one line. Find the height (in m) from the ground at which Subhas\' eyes</span><span style=\"font-family: Cambria Math;\"> are situated.</span></p>\\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2349;&#2366;&#2359;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> 3.15 m </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2375;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> 11.25 m </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2306;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2350;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2326;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2350;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> (feet) </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;&#2352;&#2375;&#2326;&#2360;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2375;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2349;&#2366;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2350;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2375;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2349;&#2366;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 7.5 m </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2350;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 45 m </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2349;&#2366;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2306;&#2326;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2375;&#2396;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2368;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2350;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2368;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2350;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2306;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> (m </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2306;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2349;&#2366;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2306;&#2326;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\\n",
                    options_en: ["<p>1.75</p>\\n", "<p>1.8</p>\\n", 
                                "<p>1.6</p>\\n", "<p>1.5</p>\\n"],
                    options_hi: ["<p>1.75</p>\\n", "<p>1.8</p>\\n",
                                "<p>1.6</p>\\n", "<p>1.5</p>\\n"],
                    solution_en: "<p>6.(b)</p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image3.png\" width=\"208\" height=\"142\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">EC = SP = 7.5 + 45 = 52.5 metre</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">BP = 11.25 - x </span><span style=\"font-family: Cambria Math;\">metre</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&Delta;</span><span style=\"font-family: Cambria Math;\">SOT &#8765; &Delta;SPB</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>O</mi></mrow><mrow><mi>S</mi><mi>P</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>T</mi><mi>O</mi></mrow><mrow><mi>B</mi><mi>P</mi></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mfrac><mrow><mn>7</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>52</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>T</mi><mi>O</mi></mrow><mrow><mi>B</mi><mi>P</mi></mrow></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>T</mi><mi>O</mi></mrow><mrow><mi>B</mi><mi>P</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>7</mn></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">6 units =(<span style=\"font-weight: 400;\">11.25 - 3.15)</span>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 8.1 metre</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">TO (1 unit) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>1</mn></mrow><mn>6</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 1.35 metre</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">SE = OD =(<span style=\"font-weight: 400;\">11.25 - 3.15)</span>&nbsp; </span><span style=\"font-family: Cambria Math;\">= 1.8 meter</span></p>\\n",
                    solution_hi: "<p>6.(b)</p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image3.png\" width=\"201\" height=\"137\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">EC = SP = 7.5 + 45 = 52.5 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">BP = 11.25 - X</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&Delta;</span><span style=\"font-family: Cambria Math;\">SOT &#8765; &Delta;SPB</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>O</mi></mrow><mrow><mi>S</mi><mi>P</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>T</mi><mi>O</mi></mrow><mrow><mi>B</mi><mi>P</mi></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mfrac><mrow><mn>7</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>52</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>T</mi><mi>O</mi></mrow><mrow><mi>B</mi><mi>P</mi></mrow></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>T</mi><mi>O</mi></mrow><mrow><mi>B</mi><mi>P</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>7</mn></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">6 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = (<span style=\"font-weight: 400;\">11.25 - 3.15)</span> </span><span style=\"font-family: Cambria Math;\">= 8.1 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">TO (1 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>1</mn></mrow><mn>6</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 1.35 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">SE = OD = (<span style=\"font-weight: 400;\">11.25 - 3.15)</span>&nbsp; </span><span style=\"font-family: Cambria Math;\"> = 1.8 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">The lengths of two parallel chords of a circle are 10 cm and 24 cm lie on the opposite </span><span style=\"font-family: Cambria Math;\">sides of the centre. If the smaller chord is 12 cm from the centre, what is the distance (in cm) between the two chords ?</span></p>\\n",
                    question_hi: "<p>7. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2357;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 10cm </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 24 cm </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2346;&#2352;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2358;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2379;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 12 cm </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2357;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> (cm </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> ) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>13</p>\\n", "<p>5</p>\\n", 
                                "<p>17</p>\\n", "<p>12</p>\\n"],
                    options_hi: ["<p>13</p>\\n", "<p>5</p>\\n",
                                "<p>17</p>\\n", "<p>12</p>\\n"],
                    solution_en: "<p>7.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Pythagorean triplets :- (5,12,13)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image4.png\" width=\"200\" height=\"178\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Distance between the chords = 12+5 = 17 cm</span></p>\\n",
                    solution_hi: "<p>7.(c)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2351;&#2341;&#2366;&#2327;&#2377;&#2352;&#2367;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> :- (5,12,13)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image4.png\" width=\"207\" height=\"184\"></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2357;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> = 12+5 = 17 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2350;&#2368;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> If </span><span style=\"font-family: Cambria Math;\"> is an &theta; acute angle and cos&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>89</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> what is the value of Sin&theta;</span></p>\\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367; <span style=\"font-family: Cambria Math;\">&theta;</span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2381;&#2351;&#2370;&#2344;&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> cos&theta;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>89</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> Sin&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>89</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>79</mn><mn>89</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>89</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>89</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>89</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>79</mn><mn>89</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>89</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>89</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    solution_en: "<p>8.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Pythagorean triplets :- (39,80,89)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;cos&theta; =&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>89</mn></mfrac><mo>=</mo><mfrac><mrow><mi>B</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow><mrow><mi>H</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi></mrow></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr; Perpendicular = 80</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;Sin&theta; =&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow><mrow><mi>H</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>80</mn><mn>89</mn></mfrac></math></span></p>\\n",
                    solution_hi: "<p>8.(c)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2351;&#2341;&#2366;&#2327;&#2377;&#2352;&#2367;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> :- (39,80,89)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;cos&theta; = <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>89</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac></math></span><span style=\"font-family: \'Cambria Math\';\">&nbsp;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr;</span><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&#2354;&#2306;&#2348; </span>= 80</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;Sin&theta; =&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>80</mn><mn>89</mn></mfrac></math></span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">Study the given pie chart and answer the question that follows. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The pie chart shows the distribution (in terms of central angles) of the number of professors in six different departments of a college.</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image5.png\" width=\"250\" height=\"184\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The number of professors in Economics and Mathematics is approximately (correct up to two decimal places) what per cent more than the number of professors in English and Statistics?</span></p>\\n",
                    question_hi: "<p>9.<span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2357;&#2367;&#2342;&#2381;&#2351;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;&#2347;&#2375;&#2360;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2340;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2342;&#2352;&#2381;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image6.png\" width=\"203\" height=\"184\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Reference: Total number of professors - </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;&#2347;&#2375;&#2360;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, Zoology - </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">, Botany - </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2344;&#2360;&#2381;&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">, Economics - </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">, English - </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\">, Mathematics - </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">, Statistics - </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2306;&#2326;&#2381;&#2351;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> &#9679; </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;&#2347;&#2375;&#2360;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;</span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2306;&#2326;&#2381;&#2351;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;&#2347;&#2375;&#2360;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2349;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>69.23%</p>\\n", "<p>59.09%</p>\\n", 
                                "<p>40.91%</p>\\n", "<p>70.91%</p>\\n"],
                    options_hi: ["<p>69.23%</p>\\n", "<p>59.09%</p>\\n",
                                "<p>40.91%</p>\\n", "<p>70.91%</p>\\n"],
                    solution_en: "<p>9.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Number of professors in Economics and maths = </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>86</mn><mo>.</mo><mn>4</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>72</mn><mo>&deg;</mo></mrow><mrow><mn>360</mn><mo>&deg;</mo></mrow></mfrac><mo>&times;</mo><mn>100</mn></math> </span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>158</mn><mo>.</mo><mn>4</mn><mo>&deg;</mo></mrow><mrow><mn>360</mn><mo>&deg;</mo></mrow></mfrac><mo>&times;</mo><mn>100</mn></math> = 44</span><span style=\"font-family: Cambria Math;\">%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Number of professors in English and statistics = </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>.</mo><mn>4</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>43</mn><mo>.</mo><mn>2</mn><mo>&deg;</mo></mrow><mrow><mn>360</mn><mo>&deg;</mo></mrow></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>93</mn><mo>.</mo><mn>6</mn><mo>&deg;</mo></mrow><mrow><mn>360</mn><mo>&deg;</mo></mrow></mfrac><mo>&times;</mo><mn>100</mn></math>= 26%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>44</mn><mo>-</mo><mn>26</mn></mrow><mn>26</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 69.23%</span></p>\\n",
                    solution_hi: "<p>9.(a)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2357;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;&#2347;&#2375;&#2360;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>86</mn><mo>.</mo><mn>4</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>72</mn><mo>&deg;</mo></mrow><mrow><mn>360</mn><mo>&deg;</mo></mrow></mfrac><mo>&times;</mo><mn>100</mn></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>158</mn><mo>.</mo><mn>4</mn><mo>&deg;</mo></mrow><mrow><mn>360</mn><mo>&deg;</mo></mrow></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 44%</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2357;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2306;&#2326;&#2381;</span><span style=\"font-family: Nirmala UI;\">&#2351;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;&#2347;&#2375;&#2360;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>.</mo><mn>4</mn><mo>&deg;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>43</mn><mo>.</mo><mn>2</mn><mo>&deg;</mo></mrow><mrow><mn>360</mn><mo>&deg;</mo></mrow></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>93</mn><mo>.</mo><mn>6</mn><mo>&deg;</mo></mrow><mrow><mn>360</mn><mo>&deg;</mo></mrow></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 26%</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>44</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>26</mn></mrow><mn>26</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 69.23%</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> The marked price of a sunglass is &#8377;1,350. It is sold at &#8377;1,188 after allowing a certain discount. Find the rate of discount</span></p>\\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2344;&#2327;&#2381;&#2354;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,350 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,188 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p>14%</p>\\n", "<p>12%</p>\\n", 
                                "<p>16%</p>\\n", "<p>10%</p>\\n"],
                    options_hi: ["<p>14%</p>\\n", "<p>12%</p>\\n",
                                "<p>16%</p>\\n", "<p>10%</p>\\n"],
                    solution_en: "<p>10.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Discount% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1350</mn><mo>-</mo><mn>1188</mn></mrow><mn>1350</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1620</mn><mn>135</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 12%</span></p>\\n",
                    solution_hi: "<p>10.(b)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> % =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1350</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1188</mn></mrow><mn>1350</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1620</mn><mn>135</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 12%</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11.</span><span style=\"font-family: Cambria Math;\"> If a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>a</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 5 then determine the value of&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></math></span></p>\\n",
                    question_hi: "<p>11.<span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>a</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 5 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span></p>\\n",
                    options_en: ["<p>90</p>\\n", "<p>125</p>\\n", 
                                "<p>105</p>\\n", "<p>110</p>\\n"],
                    options_hi: ["<p>90</p>\\n", "<p>125</p>\\n",
                                "<p>105</p>\\n", "<p>110</p>\\n"],
                    solution_en: "<p>11.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>a</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 5</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></math>= (5)&sup3; - 3 &times; <span style=\"font-family: Cambria Math;\">(5)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 110</span></p>\\n",
                    solution_hi: "<p>11.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>+</mo><mfrac><mn>1</mn><mi>a</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 5</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></math> = <span style=\"font-family: Cambria Math;\">(5)&sup3; - 3 &times; (5)</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 110</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\"> If tan&theta;</span><span style=\"font-family: Cambria Math;\"> .tan2&theta;</span><span style=\"font-family: Cambria Math;\"> = 1 then the value of cot 5&theta;</span><span style=\"font-family: Cambria Math;\"> is:</span></p>\\n",
                    question_hi: "<p>12. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> tan&theta; .tan2&theta; = 1,</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> cot 5&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\\n",
                    options_en: ["<p>-1</p>\\n", "<p>1</p>\\n", 
                                "<p>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn><mo>&nbsp;</mo></msqrt></math></p>\\n"],
                    options_hi: ["<p>-1</p>\\n", "<p>1</p>\\n",
                                "<p>-&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\\n"],
                    solution_en: "<p>12.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">tan&theta;</span><span style=\"font-weight: 400;\">.tan2&theta;<strong> </strong></span>= 1</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Put , &theta;</span><span style=\"font-family: Cambria Math;\"> = 30&deg; , Then </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">tan30&deg;.tan60&deg;</span> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&times;</mo><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\">= 1 &hellip;.(satisfied)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">cot5&theta;</span>= cot</span><span style=\"font-family: Cambria Math;\">(150&deg;) = cot</span><span style=\"font-family: Cambria Math;\">(90&deg; + 60&deg;) </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr; <span style=\"font-weight: 400;\">- tan 60&deg;</span><span style=\"font-weight: 400;\">&nbsp; </span></span><span style=\"font-family: Cambria Math;\"> = -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span></p>\\n",
                    solution_hi: "<p>12.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">tan&theta;</span><span style=\"font-weight: 400;\">.tan2&theta;<strong> </strong></span>= 1&nbsp;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">&theta; </span>= 30&deg; </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2352;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">tan30&deg;.tan60&deg;</span>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>&times;</mo><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> = 1 &hellip;.(</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">cot5&theta;</span>= cot</span><span style=\"font-family: Cambria Math;\">(150&deg;) = cot</span><span style=\"font-family: Cambria Math;\">(90&deg; + 60&deg;)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&rArr; <span style=\"font-weight: 400;\">- tan 60&deg;</span><span style=\"font-weight: 400;\">&nbsp; </span></span><span style=\"font-family: Cambria Math;\"> = -</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">A man sells rice at 8% profit and uses weight 25% less than the actual measure. What is his percen</span><span style=\"font-family: Cambria Math;\">tage profit?</span></p>\\n",
                    question_hi: "<p>13. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>44%</p>\\n", "<p>41%</p>\\n", 
                                "<p>23%</p>\\n", "<p>36%</p>\\n"],
                    options_hi: ["<p>44%</p>\\n", "<p>41%</p>\\n",
                                "<p>23%</p>\\n", "<p>36%</p>\\n"],
                    solution_en: "<p>13.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Ratio&nbsp; &nbsp; &nbsp;&rArr;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; C.P.&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;S.P.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Profit&nbsp; &nbsp; &rArr;&nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;27</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Weight&nbsp; &rArr;&nbsp; &nbsp; &nbsp;750&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;1000</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">----------------------------------------</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Overall&nbsp; &rArr;&nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 36</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required profit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>25</mn></mrow><mn>25</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 44%</span></p>\\n",
                    solution_hi: "<p>13.(a)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\">&nbsp; &rArr;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; :&nbsp; &nbsp; </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp;&rArr;&nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 27</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &rArr;&nbsp; &nbsp; &nbsp; &nbsp;750&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;1000</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">-------------------</span><span style=\"font-family: Cambria Math;\">---------------------------</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2327;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp;&rArr;&nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 36</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>25</mn></mrow><mn>25</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 44%</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">The simple interest (in &#8377;, rounded off to the tens place) on &#8377;12,300 from 22 May 1993 to 2 August 1993 (both days are included) at the rate of 12% per annum is:</span></p>\\n",
                    question_hi: "<p>14. <span style=\"font-family: Cambria Math;\">22 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2312;</span><span style=\"font-family: Cambria Math;\"> 1993 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2327;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> 1993 </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">) &#8377;12,300 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> (&#8377; </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2361;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2344;&#2381;&#2344;&#2367;&#2325;&#2335;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">? </span></p>\\n",
                    options_en: ["<p>300</p>\\n", "<p>200</p>\\n", 
                                "<p>250</p>\\n", "<p>350</p>\\n"],
                    options_hi: ["<p>300</p>\\n", "<p>200</p>\\n",
                                "<p>250</p>\\n", "<p>350</p>\\n"],
                    solution_en: "<p>14.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Time = 73 days</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Rate = 12% </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">S.I. =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>,</mo><mn>300</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>73</mn></mrow><mrow><mn>365</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></mrow></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>123</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>12</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 295.2 Rs.&nbsp; &asymp;</span><span style=\"font-family: Cambria Math;\"> 300 Rs.</span></p>\\n",
                    solution_hi: "<p>14.(a)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 73 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 12% </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>,</mo><mn>300</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>73</mn></mrow><mrow><mn>365</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></mrow></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>123</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>12</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 295.2 Rs. &asymp;</span><span style=\"font-family: Cambria Math;\"> 300 Rs.</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">Two points P and Q are 3 cm apart. These two points lie on the circumference of a circle having radius 1.7 cm. What is the distance (in cm) of the line segment PQ from the centre of the circle?</span></p>\\n",
                    question_hi: "<p>15. <span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 3 cm </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> 1.7 cm </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2326;&#2366;&#2326;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> PQ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> (cm </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> ) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\\n",
                    options_en: ["<p>0.8</p>\\n", "<p>1.0</p>\\n", 
                                "<p>0.4</p>\\n", "<p>0.6</p>\\n"],
                    options_hi: ["<p>0.8</p>\\n", "<p>1.0</p>\\n",
                                "<p>0.4</p>\\n", "<p>0.6</p>\\n"],
                    solution_en: "<p>15.(a)</p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image7.png\" width=\"154\" height=\"135\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Height of isosceles triangle =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><msqrt><mn>4</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup></msqrt></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><msqrt><mn>4</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>.</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><msqrt><mn>4</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&times;</mo><mn>2</mn><mo>.</mo><mn>89</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>9</mn></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><msqrt><mn>11</mn><mo>.</mo><mn>56</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>9</mn></msqrt></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>6</mn></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 0.8 cm</span></p>\\n",
                    solution_hi: "<p>15.(a)</p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image7.png\" width=\"152\" height=\"133\"></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2342;&#2381;&#2357;&#2367;&#2348;&#2366;&#2361;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><msqrt><mn>4</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup></msqrt></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><msqrt><mn>4</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>.</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">=&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><msqrt><mn>4</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&times;</mo><mn>2</mn><mo>.</mo><mn>89</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>9</mn></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><msqrt><mn>11</mn><mo>.</mo><mn>56</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>9</mn></msqrt></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>6</mn></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 0.8 cm</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\"> Two people A and B started running from the same point on a circular track of length 400 m in opposite directions with initial speeds of 10 m/s and 40 m/s, respectively. Whenever they meet, A\'s speed doubles and B\'s speed halves. After what time from the </span><span style=\"font-family: Cambria Math;\">start will they meet for the third time?</span></p>\\n",
                    question_hi: "<p>16.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 400 m </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2376;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2346;&#2352;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2358;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 10 m/s </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 40m / s </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2380;&#2337;&#2364;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2352;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2327;&#2369;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>30 sec</p>\\n", "<p>24 sec</p>\\n", 
                                "<p>26 sec</p>\\n", "<p>28 sec</p>\\n"],
                    options_hi: ["<p>30 <span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2325;&#2306;&#2337;</span></p>\\n", "<p>24 <span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2325;&#2306;&#2337;</span></p>\\n",
                                "<p>26 <span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2325;&#2306;&#2337;</span></p>\\n", "<p>28 <span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2325;&#2306;&#2337;</span></p>\\n"],
                    solution_en: "<p>16.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Time taken when they meet for 1st time = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mrow><mn>10</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>40</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 8 sec.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Time taken when they meet for 2nd time = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mrow><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>20</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 10 sec.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Time taken when they meet for 3rd time = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mrow><mn>40</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>10</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 8 sec.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Total time = 8 + 10 +8 = 26 sec.</span></p>\\n",
                    solution_hi: "<p>16.(c)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mrow><mn>10</mn><mo>&nbsp;</mo><mo>+</mo><mn>40</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 8 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2325;&#2306;&#2337;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mrow><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>20</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 10 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2325;&#2306;&#2337;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mrow><mn>40</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>10</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 8 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2325;&#2306;&#2337;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 8 + 10 +8 = 26 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2325;&#2306;&#2337;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">17. </span><span style=\"font-family: Cambria Math;\">Two years ago, the population of the city was 5,00,000. If the annual birth rate and the an</span><span style=\"font-family: Cambria Math;\">nual death rate are 6% and 3%, respectively, what is the present population of the city?</span></p>\\n",
                    question_hi: "<p>17. <span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2361;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 5,00,000 </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2371;&#2340;&#2381;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 6% </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3% </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>536440</p>\\n", "<p>580440</p>\\n", 
                                "<p>530450</p>\\n", "<p>540550</p>\\n"],
                    options_hi: ["<p>536440</p>\\n", "<p>580440</p>\\n",
                                "<p>530450</p>\\n", "<p>540550</p>\\n"],
                    solution_en: "<p>17.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Annual growth rate =(6 - 3)&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 3%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Effective growth rate for 2 years = 3 + 3 +</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math> 6.09%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Present population = 5,00,000 &times; 106.09% </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 530450 </span></p>\\n",
                    solution_hi: "<p>17.(c)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = (6 - 3)</span><span style=\"font-family: Cambria Math;\"> = 3%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 3 + 3 +</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math>= 6.09%</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 5,00,000 &times;106.09% </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 530450</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">18. </span><span style=\"font-family: Cambria Math;\">Find the value of the given expression</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><mfrac><mn>3</mn><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mn>11</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math>)</span></p>\\n",
                    question_hi: "<p>18. <span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac></math>) <span style=\"font-family: Cambria Math;\">&divide;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><mfrac><mn>3</mn><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mn>11</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>8</mn></mfrac></math>)</span></span></p>\\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>135</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>135</mn></mfrac></math></p>\\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>135</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>57</mn><mn>135</mn></mfrac></math></p>\\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>135</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>135</mn></mfrac></math></p>\\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>135</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>57</mn><mn>135</mn></mfrac></math></p>\\n"],
                    solution_en: "<p>18.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&divide;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mo>(</mo><mn>9</mn><mfrac><mn>3</mn><mn>8</mn></mfrac><mo>&divide;</mo><mn>11</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>15</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mo>(</mo><mn>9</mn><mfrac><mn>3</mn><mn>8</mn></mfrac><mo>&divide;</mo><mfrac><mn>35</mn><mn>24</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>75</mn><mn>8</mn></mfrac><mo>&times;</mo><mfrac><mn>24</mn><mn>35</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>45</mn><mn>7</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>28</mn><mn>135</mn></mfrac></math></span></p>\\n",
                    solution_hi: "<p>18.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&divide;</mo><mn>1</mn><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mo>(</mo><mn>9</mn><mfrac><mn>3</mn><mn>8</mn></mfrac><mo>&divide;</mo><mn>11</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>8</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>15</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mo>(</mo><mn>9</mn><mfrac><mn>3</mn><mn>8</mn></mfrac><mo>&divide;</mo><mfrac><mn>35</mn><mn>24</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>75</mn><mn>8</mn></mfrac><mo>&times;</mo><mfrac><mn>24</mn><mn>35</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>45</mn><mn>7</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>28</mn><mn>135</mn></mfrac></math></span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">19. </span><span style=\"font-family: Cambria Math;\">Find the average of prime numbers lying between 69 and 92.</span></p>\\n",
                    question_hi: "<p>19. <span style=\"font-family: Cambria Math;\">69 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 92 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p>76</p>\\n", "<p>77</p>\\n", 
                                "<p>78</p>\\n", "<p>79</p>\\n"],
                    options_hi: ["<p>76</p>\\n", "<p>77</p>\\n",
                                "<p>78</p>\\n", "<p>79</p>\\n"],
                    solution_en: "<p>19.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Prime number between 69 and 92 :- </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">71 , 73 , 79 , 83 , 89 </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required average = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>71</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>73</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>79</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>83</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>89</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 79</span></p>\\n",
                    solution_hi: "<p>19.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">69 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 92 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> :- </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">71 , 73 , 79 , 83 , 89 </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>71</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>73</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>79</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>83</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>89</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 79</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">20. </span><span style=\"font-family: Cambria Math;\">In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">ABC, P a</span><span style=\"font-family: Cambria Math;\">nd Q are the middle points of the sides AB and AC, respectively. R is point on the segment PQ such that PR: RQ=1:3. If PR = 6 cm, then BC:</span></p>\\n",
                    question_hi: "<p>20.<span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">ABC </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, P </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> AB </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> AC </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> R, </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> PQ </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> PR : RQ = 1 : 3 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> PR = 6 cm, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> BC </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>46 cm</p>\\n", "<p>48 cm</p>\\n", 
                                "<p>44 cm</p>\\n", "<p>50 cm</p>\\n"],
                    options_hi: ["<p>46cm</p>\\n", "<p>48cm</p>\\n",
                                "<p>44cm</p>\\n", "<p>50cm</p>\\n"],
                    solution_en: "<p>20.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">PQ = (1 + 3) &times; 6 = 24 cm</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Using mid-point </span><span style=\"font-family: Cambria Math;\">theorem :- </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">BC = 2PQ = 48 cm</span></p>\\n",
                    solution_hi: "<p>20.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">PQ = (1 + 3) &times; 6 = 24 cm</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2350;&#2375;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> :-</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">BC = 2PQ = 48 cm</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">21.</span><span style=\"font-family: Cambria Math;\"> The diameter of a sphere is 14 cm, then the volume of this sphere is (use </span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;):</span></p>\\n",
                    question_hi: "<p>21.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> 14 cm </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">? ( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\">):</span></p>\\n",
                    options_en: ["<p>1437<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">cm&sup3;</span></p>\\n", "<p>1683<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">cm&sup3;</span></p>\\n", 
                                "<p>1521<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">cm</span><span style=\"font-family: Cambria Math;\">&sup3;</span></p>\\n", "<p>2125<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">cm</span><span style=\"font-family: Cambria Math;\">&sup3;</span></p>\\n"],
                    options_hi: ["<p>1437<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">cm</span><span style=\"font-family: Cambria Math;\">&sup3;</span></p>\\n", "<p>1683<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">cm</span><span style=\"font-family: Cambria Math;\">&sup3;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                                "<p>1521<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">cm</span><span style=\"font-family: Cambria Math;\">&sup3;</span></p>\\n", "<p>2125<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">cm</span><span style=\"font-family: Cambria Math;\">&sup3;</span></p>\\n"],
                    solution_en: "<p>21.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Volume of sphere =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&pi;</mi><msup><mi>r</mi><mn>3</mn></msup></math> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn></math></span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>88</mn><mo>&times;</mo><mn>49</mn></mrow><mn>3</mn></mfrac><mo>=</mo><mfrac><mn>4312</mn><mn>3</mn></mfrac><mo>=</mo><mn>1437</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>21.(a)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo></math>&pi;r&sup3;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>88</mn><mo>&times;</mo><mn>49</mn></mrow><mn>3</mn></mfrac><mo>=</mo><mfrac><mn>4312</mn><mn>3</mn></mfrac><mo>=</mo><mn>1437</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">22. </span><span style=\"font-family: Cambria Math;\">If x = (7 + 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\">) then find the value of </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    question_hi: "<p>22.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> x = (7 + 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\">), </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>580</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>315</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>799</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>328</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>799</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>315</mn><msqrt><mn>5</mn></msqrt></mrow><mn>12</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>799</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>315</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac></math></p>\\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>580</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>315</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>799</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>328</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>799</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>315</mn><msqrt><mn>5</mn></msqrt></mrow><mn>12</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>799</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>315</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac></math></p>\\n"],
                    solution_en: "<p>22.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>7</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><msqrt><mn>5</mn><mo>)</mo></msqrt></math> &rArr; x&sup2; </span><span style=\"font-family: Cambria Math;\">= 94 + 42<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mn>7</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow></mfrac><mo>&times;</mo><mfrac><mrow><mo>(</mo><mn>7</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><mn>7</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>(</mo><mn>7</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mfrac><mrow><mn>94</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>42</mn><msqrt><mn>5</mn></msqrt></mrow><mn>16</mn></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Now ,&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mo>(</mo><mn>94</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>42</mn><msqrt><mn>5</mn></msqrt><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>94</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>42</mn><msqrt><mn>5</mn></msqrt></mrow><mn>16</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>(</mo><mn>94</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>42</mn><msqrt><mn>5</mn></msqrt><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>47</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>21</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>94</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>42</mn><msqrt><mn>5</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>47</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>21</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>799</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>315</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac></math></span></p>\\n",
                    solution_hi: "<p>22.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>7</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><msqrt><mn>5</mn><mo>)</mo></msqrt></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = x&sup2; = 94 + 42<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mn>7</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow></mfrac><mo>&times;</mo><mfrac><mrow><mo>(</mo><mn>7</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><mn>7</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>(</mo><mn>7</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow><mn>4</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mfrac><mrow><mn>94</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>42</mn><msqrt><mn>5</mn></msqrt></mrow><mn>16</mn></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\"> ,&nbsp;&nbsp;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mo>(</mo><mn>94</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>42</mn><msqrt><mn>5</mn></msqrt><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>94</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>42</mn><msqrt><mn>5</mn></msqrt></mrow><mn>16</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>(</mo><mn>94</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>42</mn><msqrt><mn>5</mn></msqrt><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>47</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>21</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>94</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>42</mn><msqrt><mn>5</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>47</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>21</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>799</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>315</mn><msqrt><mn>5</mn></msqrt></mrow><mn>8</mn></mfrac></math></span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">23. </span><span style=\"font-family: Cambria Math;\">A person spent 12% of his salary on groceries, 8% on medicines, 20% on children\'s school fees and he had a balance of &#8377;36,000. Find his salary.</span></p>\\n",
                    question_hi: "<p>23. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2352;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, 8% </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2357;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, 20% </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2381;&#2330;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2368;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> &#8377;36,000 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\\n",
                    options_en: ["<p>&#8377;56,000</p>\\n", "<p>&#8377;54,000</p>\\n", 
                                "<p>&#8377;60,000</p>\\n", "<p>&#8377;66,000</p>\\n"],
                    options_hi: ["<p>&#8377;56,000</p>\\n", "<p>&#8377;54,000</p>\\n",
                                "<p>&#8377;60,000</p>\\n", "<p>&#8377;66,000</p>\\n"],
                    solution_en: "<p>23.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">His savings = 100% - (<span style=\"font-weight: 400;\">12% + 8% + 20%</span>) </span><span style=\"font-family: Cambria Math;\">= 60% of income</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Total salary = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>,</mo><mn>000</mn></mrow><mn>60</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 60,000 Rs.</span></p>\\n",
                    solution_hi: "<p>23.(c)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 100% - (<span style=\"font-weight: 400;\">12% + 8% + 20%</span>) &nbsp;</span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 60% </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>,</mo><mn>000</mn></mrow><mn>60</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 60,000 Rs.</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">24. </span><span style=\"font-family: Cambria Math;\">Study the given bar graph and answer the question that follows. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The production of cement (in lakh tonnes) by a company during the last six years is presented by the bar graph.</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image8.png\" width=\"301\" height=\"237\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">What is the average cement production (in lakh tonnes) during the given six years?</span></p>\\n",
                    question_hi: "<p>24. <span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2352;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2331;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;&#2350;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image9.png\" width=\"335\" height=\"268\"></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2342;&#2352;&#2381;&#2349;</span><span style=\"font-family: Cambria Math;\">: Cement Production (in lakh tonnes) - </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;&#2350;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;</span><span style=\"font-family: Nirmala UI;\">&#2306;</span><span style=\"font-family: Cambria Math;\">), Years - </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;&#2350;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>40.0</p>\\n", "<p>39.0</p>\\n", 
                                "<p>39.5</p>\\n", "<p>40.5</p>\\n"],
                    options_hi: ["<p>40.0</p>\\n", "<p>39.0</p>\\n",
                                "<p>39.5</p>\\n", "<p>40.5</p>\\n"],
                    solution_en: "<p>24.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required average = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>40</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>45</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>35</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>35</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>47</mn></mrow><mn>6</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 39.5</span></p>\\n",
                    solution_hi: "<p>24.(c)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>40</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>45</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>35</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>35</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>47</mn></mrow><mn>6</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 39.5</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">25. </span><span style=\"font-family: Cambria Math;\">Three people A, B and C worked together and finished a work in 9 days. If A alone can finish the same work in 18 days, B alone in 30 days, then the number of days C alone will take is:</span></p>\\n",
                    question_hi: "<p>25. <span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> A, B </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 18 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> 30 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>36 days</p>\\n", "<p>45 days</p>\\n", 
                                "<p>40 days</p>\\n", "<p>54 days</p>\\n"],
                    options_hi: ["<p>36 <span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span></p>\\n", "<p>45 <span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span></p>\\n",
                                "<p>40 <span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p>54<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span></p>\\n"],
                    solution_en: "<p>25.(b)</p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image10.png\" width=\"255\" height=\"146\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Efficiency of C = <span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">10 - (</span><span style=\"font-weight: 400;\">5 + 3)</span></span><span style=\"font-family: Cambria Math;\"> = 2 units</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required time = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 45 days</span></p>\\n",
                    solution_hi: "<p>25.(b)</p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701667640/word/media/image11.png\" width=\"255\" height=\"143\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> = <span style=\"font-weight: 400;\">10 - (</span><span style=\"font-weight: 400;\">5 + 3)</span></span><span style=\"font-family: Cambria Math;\"> = 2 units</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 45 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>