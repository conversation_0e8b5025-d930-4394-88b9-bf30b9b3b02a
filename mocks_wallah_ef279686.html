<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The following table shows the production of washing machines (in thousands) in different factories in different years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690530588.png\" alt=\"rId4\" width=\"300\" height=\"114\"> <br>In which year was the production of washing machines in Factory Q more than that in Factory R ?</p>",
                    question_hi: "<p>1. निम्नलिखित तालिका विभिन्न वर्षों में विभिन्न कारखानों में वाशिंग मशीनों के उत्पादन (हजारों में) को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690530742.png\" alt=\"rId5\" width=\"300\"> <br>किस वर्ष कारखाने Q में वाशिंग मशीनों का उत्पादन कारखाने R से अधिक था ?</p>",
                    options_en: ["<p>2004</p>", "<p>2005</p>", 
                                "<p>2002</p>", "<p>2003</p>"],
                    options_hi: ["<p>2004</p>", "<p>2005</p>",
                                "<p>2002</p>", "<p>2003</p>"],
                    solution_en: "<p>1.(b)<br>According to table given<br>In the year 2005,<br>Washing machines in Factory Q = 30<br>Washing machines in Factory R = 23<br>Clearly, in 2005, factory Q had more washing machines than factory R.</p>",
                    solution_hi: "<p>1.(b)<br>दी गई तालिका के अनुसार<br>वर्ष 2005 में,<br>फ़ैक्टरी Q में वाशिंग मशीन = 30<br>फ़ैक्टरी R में वाशिंग मशीनें = 23<br>स्पष्टतः, 2005 में कारखाने Q में वाशिंग मशीनों का उत्पादन अधिक हैं I</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The following table shows the expenditure of a school (in ₹&nbsp;lakh) per annum on different heads over the given years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690530909.png\" alt=\"rId6\" width=\"500\"> <br>What was the fuel &amp; transport expenditure (in ₹ lakh) of the school in the year 2003?</p>",
                    question_hi: "<p>2. निम्नलिखित तालिका दिए गए वर्षों में विभिन्न मदों पर प्रति वर्ष एक स्कूल के खर्च (₹ लाख में) को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690531011.png\" alt=\"rId7\" width=\"325\" height=\"134\"> <br>वर्ष 2003 में स्कूल का ईंधन और परिवहन व्यय (₹ लाख में) कितना था?</p>",
                    options_en: ["<p>95</p>", "<p>45</p>", 
                                "<p>75</p>", "<p>112</p>"],
                    options_hi: ["<p>95</p>", "<p>45</p>",
                                "<p>75</p>", "<p>112</p>"],
                    solution_en: "<p>2.(a)<br>It is clearly shown in the table that expenditure in fuel &amp; transport in the year 2003 was 95 lakh.</p>",
                    solution_hi: "<p>2.(a)<br>तालिका में ईंधन एवं परिवहन में होने वाले व्यय को स्पष्ट रूप से दर्शाया गया है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The pie chart shows the expenditure of a family in percentage. Read the chart and answer the question that follows. The total monthly income is ₹32,000.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690531412.png\" alt=\"rId8\" width=\"169\" height=\"190\"> <br>After the deduction of House Rent and Electricity, find the total income that remains.</p>",
                    question_hi: "<p>3. पाई चार्ट एक परिवार के व्यय को प्रतिशत में दर्शाता है। चार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। कुल मासिक आय ₹32,000 है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690531637.png\" alt=\"rId9\" width=\"165\" height=\"183\"> <br>घर के किराए और बिजली पर व्यय को काटने के बाद, शेष कुल आय ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹19,220</p>", "<p>₹19,230</p>", 
                                "<p>₹19,200</p>", "<p>₹19,210</p>"],
                    options_hi: ["<p>₹19,220</p>", "<p>₹19,230</p>",
                                "<p>₹19,200</p>", "<p>₹19,210</p>"],
                    solution_en: "<p>3.(c)<br>Income spent on house rent and electricity = 25% + 15% = 40%<br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi></math>Income spent on house rent and electricity = 32000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>100</mn></mfrac></math> = ₹ 12800<br><math display=\"inline\"><mo>&#8658;</mo></math> remaining income = 32000 - 12800 = ₹ 19200</p>",
                    solution_hi: "<p>3.(c)<br>घर के किराये और बिजली पर खर्च की गई आय = 25% + 15% = 40%<br>घर का किराया और बिजली पर खर्च आय = 32000 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>100</mn></mfrac></math> = ₹ 12800<br>शेष आय = 32000 - 12800 = ₹ 19200</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The following table shows the % of marks obtained by three students in three different subjects in a school exam. (The maximum marks in each subject are shown in the table at the top)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690531835.png\" alt=\"rId10\" width=\"283\" height=\"99\"> <br>Find the total marks obtained by Varun and Seema together in Science.</p>",
                    question_hi: "<p>4. निम्नलिखित तालिका एक स्कूल परीक्षा में तीन अलग-अलग विषयों में तीन विद्यार्थियों द्वारा प्राप्त अंकों का % दर्शाती है। (प्रत्येक विषय में पूर्णांक तालिका में शीर्ष पर दिए गए हैं)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690531969.png\" alt=\"rId11\" width=\"225\" height=\"118\"> <br>विज्ञान में वरुण और सीमा द्वारा प्राप्त कुल अंक ज्ञात कीजिए।</p>",
                    options_en: ["<p>178</p>", "<p>189</p>", 
                                "<p>145</p>", "<p>126</p>"],
                    options_hi: ["<p>178</p>", "<p>189</p>",
                                "<p>145</p>", "<p>126</p>"],
                    solution_en: "<p>4.(b)<br>Marks obtained by Varun in science = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 105<br>Marks obtained by Seema in science = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>56</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 84<br><math display=\"inline\"><mo>&#8658;</mo></math> Total marks = 105 + 84 = 189</p>",
                    solution_hi: "<p>4.(b)<br>वरुण को विज्ञान में प्राप्त अंक = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 105<br>सीमा को विज्ञान में प्राप्त अंक = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>56</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 84<br><math display=\"inline\"><mo>&#8658;</mo></math> कुल अंक = 105 + 84 = 189</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The given pie-chart shows the number of students admitted into various schools, A, B, C, D, E, in a town. The total number of students is 6500. The number of boys in each school is given by the table. Study the pie-chart and the table carefully and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690532160.png\" alt=\"rId12\" width=\"245\" height=\"206\"> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690532265.png\" alt=\"rId13\" width=\"256\" height=\"41\"> <br>The total number of boys in schools A, C, D is what percentage of the total number of girls in schools B, C, E (rounded to 2 decimal places)?</p>",
                    question_hi: "<p>5. दिया गया पाई-चार्ट एक कस्बे के विभिन्न विद्यालयों, A, B, C, D, E में प्रवेश लेने वाले विद्यार्थियों की संख्या को दर्शाता है। विद्यार्थियों की कुल संख्या 6500 है। प्रत्येक विद्यालय में लड़कों की संख्या तालिका में दी गई है। इस पाई-चार्ट और तालिका का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690532411.png\" alt=\"rId14\" width=\"249\" height=\"212\"> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690532504.png\" alt=\"rId15\" width=\"295\" height=\"57\"> <br>विद्यालय A, C, D में लड़कों की कुल संख्या, विद्यालय B, C, E में लड़कियों की कुल संख्या का कितना प्रतिशत है (दशमलव के 2 स्थानों तक पूर्णांकित)?</p>",
                    options_en: ["<p>1.2662</p>", "<p>1.2963</p>", 
                                "<p>1.2863</p>", "<p>1.2062</p>"],
                    options_hi: ["<p>1.2662</p>", "<p>1.2963</p>",
                                "<p>1.2863</p>", "<p>1.2062</p>"],
                    solution_en: "<p>5.(b)<br>Total number of boys in A,C and D = 800 + 600 + 700 = 2100<br>Number of students in B = 6500 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1430<br>Number of students in C = 6500 &times; <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1040<br>Number of students in E = 6500 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1365<br><math display=\"inline\"><mo>&#8658;</mo></math> Number of girls in B = 1430 - 850 = 580<br>Number of girls in C = 1040 - 600 = 440<br>Number of girls in E = 1365 - 765 = 600<br>Total number of girls in B, C and E = 580 + 440 + 600 = 1620 <br>Required % = <math display=\"inline\"><mfrac><mrow><mn>2100</mn></mrow><mrow><mn>1620</mn></mrow></mfrac></math> &times; 100 = 1.2963</p>",
                    solution_hi: "<p>5.(b)<br>A,C और D में लड़कों की कुल संख्या = 800 + 600 + 700 = 2100<br>B में छात्रों की संख्या = 6500 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1430<br>C में छात्रों की संख्या = 6500 &times; <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1040<br>E में छात्रों की संख्या = 6500 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1365<br><math display=\"inline\"><mo>&#8658;</mo></math> B में लड़कियों की संख्या = 1430 - 850 = 580<br>C में लड़कियों की संख्या = 1040 - 600 = 440<br>E में लड़कियों की संख्या = 1365 - 765 = 600<br>B, C और E में लड़कियों की कुल संख्या = 580 + 440 + 600 = 1620 <br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>2100</mn></mrow><mrow><mn>1620</mn></mrow></mfrac></math> &times; 100 = 1.2963</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The following table shows the number of eggs produced by five farms A, B, C, D and E over the given years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690532608.png\" alt=\"rId16\" width=\"208\" height=\"114\"> <br>What was the average number of eggs produced by Farm B over the given years?</p>",
                    question_hi: "<p>6. निम्नलिखित तालिका दिए गए वर्षों में पाँच फार्म A, B, C, D और E द्वारा उत्पादित अंडों की संख्या दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690532752.png\" alt=\"rId17\" width=\"210\" height=\"124\"> <br>दिए गए वर्षों में फार्म B द्वारा उत्पादित अंडों की औसत संख्या क्या है?</p>",
                    options_en: ["<p>540</p>", "<p>575</p>", 
                                "<p>552</p>", "<p>532</p>"],
                    options_hi: ["<p>540</p>", "<p>575</p>",
                                "<p>552</p>", "<p>532</p>"],
                    solution_en: "<p>6.(d)<br>Total number of eggs produced by Farm B = 418 + 390 + 765 + 555 =2128<br>Average number of eggs produced by Farm B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>number</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>eggs</mi></mrow><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>years</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>2128</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>= 532</p>",
                    solution_hi: "<p>6.(d)<br>फार्म B द्वारा उत्पादित अंडों की कुल संख्या = 418 + 390 + 765 + 555 = 2128<br>फार्म B द्वारा उत्पादित अंडों की औसत संख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2337;&#2379;&#2306;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2357;&#2352;&#2381;&#2359;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> =&nbsp; <math display=\"inline\"><mfrac><mrow><mn>2128</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 532</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The following table shows the percentage of marks obtained by five students in four subjects in an examination.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690532856.png\" alt=\"rId18\" width=\"386\" height=\"141\"> <br>How many students scored more than 60% of marks in all the four subjects?</p>",
                    question_hi: "<p>7. निम्नलिखित तालिका एक परीक्षा में चार विषयों में पाँच विद्यार्थियों द्वारा प्राप्त अंकों का प्रतिशत दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690532974.png\" alt=\"rId19\" width=\"369\" height=\"153\"> <br>कुल कितने विद्यार्थियों ने सभी चार विषयों में 60% से अधिक अंक प्राप्त किये?</p>",
                    options_en: ["<p>4</p>", "<p>2</p>", 
                                "<p>5</p>", "<p>3</p>"],
                    options_hi: ["<p>4</p>", "<p>2</p>",
                                "<p>5</p>", "<p>3</p>"],
                    solution_en: "<p>7.(b)<br>In the table we can clearly see that only A and C scored more than 60% of marks in all the four subjects.</p>",
                    solution_hi: "<p>7.(b)<br>तालिका में हम स्पष्ट रूप से देख सकते हैं कि केवल A और C ने सभी चार विषयों में 60% से अधिक अंक प्राप्त किए हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The table below shows the production of different types of caps (in thousands) in different years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690533084.png\" alt=\"rId20\" width=\"268\" height=\"108\"> <br>In which year was the production (in thousands) of Cap Q equal to that of Cap R?</p>",
                    question_hi: "<p>8. नीचे दी गई तालिका विभिन्न वर्षों में विभिन्न प्रकार की टोपियों का उत्पादन (हजारों में) दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690533187.png\" alt=\"rId21\" width=\"309\" height=\"123\"> <br>किस वर्ष में टोपी Q का उत्पादन (हजारों में), टोपी R के बराबर था?</p>",
                    options_en: ["<p>2010</p>", "<p>2011</p>", 
                                "<p>2009</p>", "<p>2012</p>"],
                    options_hi: ["<p>2010</p>", "<p>2011</p>",
                                "<p>2009</p>", "<p>2012</p>"],
                    solution_en: "<p>8.(d)<br>We can clearly see in the table that in 2012, the production of Cap Q equal to that of Cap R i.e. 678.</p>",
                    solution_hi: "<p>8.(d)<br>हम तालिका में स्पष्ट रूप से देख सकते हैं कि 2012 में टोपी Q का उत्पादन टोपी R के बराबर यानी 678 था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The following table shows the number of voters at 5 different centers P, Q, R, S and T and the percentage of people who voted in an election.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690533283.png\" alt=\"rId22\" width=\"354\" height=\"132\"> <br>If the number of invalid votes cast at Centre S was 20% of the total number of votes cast, then what percentage of the total number of registered voters cast invalid votes at Centre S?</p>",
                    question_hi: "<p>9. निम्नलिखित तालिका 5 अलग-अलग केंद्रों P, Q, R, S और T पर मतदाताओं की संख्या और चुनाव में मतदान करने वाले लोगों का प्रतिशत दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690533407.png\" alt=\"rId23\" width=\"404\" height=\"126\"> <br>यदि केंद्र S पर डाले गए अवैध मतों की संख्या, डाले गए मतों की कुल संख्या की 20% थी, तो केंद्र S पर कुल पंजीकृत मतदाताओं में से कितने प्रतिशत मतदाताओं ने अवैध मत डाले?</p>",
                    options_en: ["<p>20%</p>", "<p>18%</p>", 
                                "<p>25%</p>", "<p>22%</p>"],
                    options_hi: ["<p>20%</p>", "<p>18%</p>",
                                "<p>25%</p>", "<p>22%</p>"],
                    solution_en: "<p>9.(b)<br>Total number of registered voters of S = 1850<br>People who voted = 1850 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1665<br>Invalid votes = 1665 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 333<br>Hence, <br>Required % = <math display=\"inline\"><mfrac><mrow><mn>333</mn></mrow><mrow><mn>1850</mn></mrow></mfrac></math> &times; 100 = 18%</p>",
                    solution_hi: "<p>9.(b)<br>S के पंजीकृत मतदाताओं की कुल संख्या = 1850<br>वोट देने वाले लोग = 1850 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1665<br>अवैध वोट = 1665 &times;<math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 333<br>इस तरह, <br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>333</mn></mrow><mrow><mn>1850</mn></mrow></mfrac></math> &times; 100 = 18%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Study the given pie-chart and answer the question that follows.<br>The pie-chart shows the spendings of a sports academy on various sports during a particular year.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690533521.png\" alt=\"rId24\" width=\"306\" height=\"180\"> <br>How much percentage more is spent on Hockey than that on Golf?</p>",
                    question_hi: "<p>10. निम्नांकित पाई-चार्ट का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br>यह पाई-चार्ट एक विशेष वर्ष के दौरान एक खेल अकादमी द्वारा विभिन्न खेलों पर किए गए खर्च को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690533645.png\" alt=\"rId25\" width=\"315\" height=\"191\"> <br>गोल्फ की तुलना में हॉकी पर कितना प्रतिशत अधिक खर्च किया गया है?<br>&bull; Cricket - क्रिकेट&nbsp; &nbsp; &nbsp; &nbsp;&bull; Hockey - हॉकी&nbsp; &nbsp; &nbsp; &nbsp;&bull; Football - फुटबॉल<br>&bull; Others - अन्य&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &bull; Golf - गोल्फ&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &bull; Tennis - टेनिस<br>&bull; Basketball - बास्केटबॉल</p>",
                    options_en: ["<p>65%</p>", "<p>25%</p>", 
                                "<p>75%</p>", "<p>70%</p>"],
                    options_hi: ["<p>65%</p>", "<p>25%</p>",
                                "<p>75%</p>", "<p>70%</p>"],
                    solution_en: "<p>10.(c)<br>Required percentage = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>63</mn><mo>-</mo><mn>36</mn></mrow><mn>36</mn></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math> &times; 100<br>= 75 %</p>",
                    solution_hi: "<p>10.(c)<br>आवश्यक प्रतिशत =&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>63</mn><mo>-</mo><mn>36</mn></mrow><mn>36</mn></mfrac></math>&nbsp;&times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math> &times; 100<br>= 75 %</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The given pie chart displays the number of patients who come to a clinic per week, for treatment under various heads. The total number of patients who come to the clinic is 4060 per week. Study the pie chart and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690533758.png\" alt=\"rId26\" width=\"400\"> <br>The combined number of patients for the treatment of trauma, dexa and blood works is what percentage (rounded off to 1 decimal place) more or less than the combined number of patients for OPD, X-ray and physiotherapy?</p>",
                    question_hi: "<p>11. दिया गया पाई चार्ट विभिन्न मदों के तहत इलाज के लिए प्रति सप्ताह क्लिनिक में आने वाले मरीजों की संख्या को प्रदर्शित करता है। क्लिनिक में आने वाले मरीजों की कुल संख्या प्रति सप्ताह 4060 है। पाई चार्ट का अध्ययन कीजिये और नीचे दिए गए प्रश्न का उत्तर दीजिये।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690533859.png\" alt=\"rId27\" width=\"413\" height=\"249\"> <br>Number of patients who show up per week to a clinic for treatment under various heads. The total number of patients who come to the clinic is 4060 per week. = विभिन्न मदों के तहत इलाज के लिए क्लिनिक में प्रति सप्ताह आने वाले मरीजों की संख्या। क्लिनिक में आने वाले मरीजों की कुल संख्या प्रति सप्ताह 4060 है।<br>OPD Patients = ओपीडी के मरीज<br>X-ray = एक्स-रे<br>Trauma = ट्रॉमा<br>Physiotherapy = फिजियोथेरेपी<br>Dexa (Bone density) = डेक्सा (अस्थि घनत्व)<br>Blood works = रक्त कार्य<br>ट्रॉमा, डेक्सा और रक्त कार्य के उपचार के लिए मरीजों की संयुक्त संख्या ओपीडी, एक्स-रे और फिजियोथेरेपी के लिए मरीजों की संयुक्त संख्या से कितने प्रतिशत (1 दशमलव स्थान तक पूर्णांकित) अधिक या कम है?</p>",
                    options_en: ["<p>More, 12.7</p>", "<p>More, 14.1%</p>", 
                                "<p>Less, 14.1%</p>", "<p>Less, 12.7%</p>"],
                    options_hi: ["<p>अधिक, 12.7%</p>", "<p>अधिक, 14.1%</p>",
                                "<p>कम, 14.1%</p>", "<p>कम, 12.7%</p>"],
                    solution_en: "<p>11.(c)<br>Number of patients for treatment of trauma, dexa and blood works = (23.80 + 10.80 + 11.60)%<br>= 46.2 %<br>Number of patients for treatment of OPD, X-ray and physiotherapy = (25.90 + 15.60 + 12.30)<br>= 53.8 %<br>Percentage change = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>.</mo><mn>8</mn><mo>-</mo><mn>46</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi></mrow><mrow><mn>53</mn><mo>.</mo><mn>8</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>53</mn><mo>.</mo><mn>8</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100<br>= 14.1 % (less)</p>",
                    solution_hi: "<p>11.(c)<br>ट्रॉमा, डेक्सा और रक्त कार्यों के उपचार के लिए रोगियों की संख्या = (23.80 + 10.80 + 11.60)%<br>= 46.2 %<br>ओपीडी, एक्स-रे और फिजियोथेरेपी के इलाज के लिए मरीजों की संख्या = (25.90 + 15.60 + 12.30)<br>= 53.8 %<br>प्रतिशत परिवर्तन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>.</mo><mn>8</mn><mo>-</mo><mn>46</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi></mrow><mrow><mn>53</mn><mo>.</mo><mn>8</mn><mi>&#160;</mi></mrow></mfrac></math>&nbsp;&times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>53</mn><mo>.</mo><mn>8</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100<br>= 14.1 % (कम)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The following table shows the number of medals won by four boys in different activities.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690533955.png\" alt=\"rId28\" width=\"297\" height=\"108\"> <br>Who won more medals in swimming than in singing?</p>",
                    question_hi: "<p>12. निम्नलिखित तालिका विभिन्न गतिविधियों में चार लड़कों द्वारा जीते गए पदकों की संख्या दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690534052.png\" alt=\"rId29\" width=\"205\" height=\"125\"> <br>किसने गायन की तुलना में तैराकी में अधिक पदक जीते?</p>",
                    options_en: ["<p>Ramagya</p>", "<p>Bitthal</p>", 
                                "<p>Rameshwar</p>", "<p>Krishna</p>"],
                    options_hi: ["<p>रामाज्ञा</p>", "<p>बिट्ठल</p>",
                                "<p>रामेश्&zwj;वर</p>", "<p>कृष्ण</p>"],
                    solution_en: "<p>12.(b)<br>From the table it is clearly shown that Bitthal won more medals in swimming (30) than in singing (28).</p>",
                    solution_hi: "<p>12.(b)<br>तालिका से यह स्पष्ट रूप से पता चलता है कि बिट्ठल ने गायन (28) की तुलना में तैराकी (30) में अधिक पदक जीते।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The following bar graph shows the failure rates (in thousands) for different electric components:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690534141.png\" alt=\"rId30\" width=\"400\"> <br>How many times is the failure rate of hybrid micro circuits compared to that of signal devices?</p>",
                    question_hi: "<p>13. निम्नलिखित बार ग्राफ विभिन्न इलेक्ट्रिक घटकों की विफलता दर (हजारों में) दर्शाता है:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690534257.png\" alt=\"rId31\" width=\"400\"> <br>सिग्नल डिवाइसों की तुलना में हाइब्रिड माइक्रो सर्किट की विफलता दर कितनी गुना है?</p>",
                    options_en: ["<p>5.2</p>", "<p>0.4</p>", 
                                "<p>4</p>", "<p>2.5</p>"],
                    options_hi: ["<p>5.2</p>", "<p>0.4</p>",
                                "<p>4</p>", "<p>2.5</p>"],
                    solution_en: "<p>13.(d)<br><strong>Given</strong> <br>failure rate of hybrid micro circuits = 40<br>failure rate of signal devices = 16<br>Hence the failure rate = <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = 2.5<br>Hence, the failure rate of hybrid micro circuits compared to signal devices is 2.5 times.</p>",
                    solution_hi: "<p>13.(d)<br><strong>दिया गया</strong> <br>हाइब्रिड माइक्रो सर्किट की विफलता दर = 40<br>सिग्नल उपकरणों की विफलता दर = 16<br>इसलिए विफलता दर = <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = 2.5<br>इसलिए, सिग्नल उपकरणों की तुलना में हाइब्रिड माइक्रो सर्किट की विफलता दर 2.5 गुना है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. The given pie-chart shows the expenditure (in ₹ lakh) of a country on various sports during a particular year. Study the chart carefully and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690534367.png\" alt=\"rId32\" width=\"203\" height=\"200\"> <br>The average expenditure on golf and swimming is what percent (rounded to 1 decimal place) more or less than the average expenditure on football and cricket?</p>",
                    question_hi: "<p>14. दिया गया पाई-चार्ट एक विशेष वर्ष के दौरान किसी देश के विभिन्न खेलों पर हुए खर्च (₹ लाख में) को दर्शाता है। इस चार्ट का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690534467.png\" alt=\"rId33\" width=\"200\"> <br>Tennis = टेनिस, Football = फुटबॉल, Golf = गोल्फ, Swimming = तैराकी, Cricket = क्रिकेट, Basketball = बॉस्केटबॉल<br>गोल्फ और तैराकी पर औसत खर्च, फुटबॉल और क्रिकेट पर हुए औसत खर्च से कितना प्रतिशत (दशमलव के 1 स्थान तक पूर्णांकित) अधिक या कम है?</p>",
                    options_en: ["<p>More, 28.5%</p>", "<p>More, 25.5%</p>", 
                                "<p>Less, 28.5%</p>", "<p>Less, 25.5%</p>"],
                    options_hi: ["<p>28.5% अधिक</p>", "<p>25.5% अधिक</p>",
                                "<p>28.5% कम</p>", "<p>25.5% कम</p>"],
                    solution_en: "<p>14.(c)<br>Expenditure on golf and swimming = (48 + 45)&deg;<br>Average Expenditure on golf and swimming = <math display=\"inline\"><mfrac><mrow><mn>93</mn><mo>&#176;</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math> <br>And expenditure on football and cricket = (70 + 60)&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> average expenditure on football and cricket =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>130</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math><br>Hence,<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>93</mn><mn>2</mn></mfrac><mo>-</mo><mfrac><mn>130</mn><mn>2</mn></mfrac></mrow><mfrac><mn>130</mn><mn>2</mn></mfrac></mfrac></math> &times; 100<br>= - 28.46%<br>= 28.5% less</p>",
                    solution_hi: "<p>14.(c)<br>गोल्फ और तैराकी पर व्यय = (48 + 45)&deg;<br>गोल्फ और तैराकी पर औसत व्यय = <math display=\"inline\"><mfrac><mrow><mn>93</mn><mo>&#176;</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>तथा फुटबॉल और क्रिकेट पर व्यय = (70 + 60)&deg;<br>फुटबॉल और क्रिकेट पर औसत व्यय = <math display=\"inline\"><mfrac><mrow><mn>130</mn><mo>&#176;</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>इस तरह,<br>आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>93</mn><mn>2</mn></mfrac><mo>-</mo><mfrac><mn>130</mn><mn>2</mn></mfrac></mrow><mfrac><mn>130</mn><mn>2</mn></mfrac></mfrac></math>&nbsp;&times; 100<br>= - 28.46%<br>= 28.5% (कम)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Study the given bar-graph and answer the question that follows.<br>The bar-graph shows the percentage revenue allocation by two states in various sectors.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690534619.png\" alt=\"rId34\" width=\"344\" height=\"227\"> <br>If the amount of money allocated to the two states is ₹20 billion each, then how much more (in rupees) does State A spend on Exports than on Manufacturing ?</p>",
                    question_hi: "<p>15. निम्नांकित बार-ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें। <br>यह बार-ग्राफ दो राज्यों द्वारा विभिन्न क्षेत्रों में किए गए राजस्व आवंटन का प्रतिशत दर्शाता है<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732690534846.png\" alt=\"rId35\" width=\"300\"> <br>यदि दोनों राज्यों में से प्रत्येक के लिए आवंटित धनराशि ₹20 बिलियन है, तो राज्य A विनिर्माण की तुलना में निर्यात पर कितना अधिक (₹ में) खर्च करता है?<br>&bull; Miscellaneous - विविध&nbsp; &nbsp; &nbsp; &nbsp;&bull; Exports - निर्यात&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&bull; Manufacturing - विनिर्माण<br>&bull; Services - सेवाएँ&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&bull; Agriculture - कृषि&nbsp; &nbsp; &nbsp; &nbsp; &bull; State B - राज्य B<br>&bull; State A - राज्य A</p>",
                    options_en: ["<p>2.5 billion</p>", "<p>1.5 billion</p>", 
                                "<p>2 billion</p>", "<p>3 billion</p>"],
                    options_hi: ["<p>2.5 बिलियन</p>", "<p>1.5 बिलियन</p>",
                                "<p>2 बिलियन</p>", "<p>3 बिलियन</p>"],
                    solution_en: "<p>15.(c)<br>State A spend on Exports = 20% of 20 billion = 4 billion<br>State A spend on Manufacturing = 10% of 20 billion = 2 billion<br><math display=\"inline\"><mo>&#8658;</mo></math> required difference = (4 - 2) billion = 2 billion</p>",
                    solution_hi: "<p>15.(c)<br>राज्य A निर्यात पर खर्च = 20 बिलियन का 20% = 4 बिलियन <br>राज्य A का विनिर्माण पर खर्च = 20 बिलियन का 10% = 2 बिलियन <br><math display=\"inline\"><mo>&#8658;</mo></math> आवश्यक अंतर = (4 - 2) बिलियन = 2 बिलियन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>