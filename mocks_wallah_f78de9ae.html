<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\"> If sin A =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> , calculate the value of cos A + tan A - 1.</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> sin A =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> cos A + tan A - 1 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>20</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>20</mn></mfrac></math></p>\\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>20</mn></mfrac></math></p>\\n", "<p>2</p>\\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>20</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>20</mn></mfrac></math></p>\\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>20</mn></mfrac></math></p>\\n", "<p>2</p>\\n"],
                    solution_en: "<p>1.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sinA</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mi>perpendicular</mi><mi>Hypotenuse</mi></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mi>Base</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mspace linebreak=\"newline\"></mspace><mi>cosA</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tanA</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>16</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>20</mn></mrow><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>11</mn><mn>20</mn></mfrac></math></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>1.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sinA</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>&nbsp;</mo></mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mi>&#2310;&#2343;&#2366;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mspace linebreak=\"newline\"></mspace><mi>cosA</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>tanA</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>16</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>20</mn></mrow><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>11</mn><mn>20</mn></mfrac></math></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> If 4 sin</span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\">A - 3 = 0 and 0 &le; A &le; 90&deg;, then 3 sin A - 4 sin&sup3; A is:</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 4 sin</span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\">A - 3 = 0 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 0 &le; A &le; 90&deg; </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 3 sin A - 4 sin</span><span style=\"font-family: Cambria Math;\">&sup3;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>3</mn><mn>2</mn></mfrac></msqrt></math></p>\\n", 
                                "<p>0</p>\\n", "<p>1</p>\\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>3</mn><mn>2</mn></mfrac></msqrt></math></p>\\n",
                                "<p>0</p>\\n", "<p>1</p>\\n"],
                    solution_en: "<p>2.(c)</p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>Multiply</mi><mo>&nbsp;</mo><mi>by</mi><mo>&nbsp;</mo><mi>sinA</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>both</mi><mo>&nbsp;</mo><mi>sides</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>4</mn><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>sinA</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>or</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>3</mn><mi>sinA</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn></math></p>\\n",
                    solution_hi: "<p>2.(c)</p>\\r\\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mi>&#2342;&#2379;&#2344;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2379;</mi><mo>&nbsp;</mo><mi>sin</mi><mi>A</mi><mo>&nbsp;</mo><mi>&#2360;&#2375;</mi><mo>&nbsp;</mo><mi>&#2327;&#2369;&#2339;&#2366;</mi><mo>&nbsp;</mo><mi>&#2325;&#2352;&#2344;&#2375;</mi><mo>&nbsp;</mo><mi>&#2346;&#2352;</mi><mo>&nbsp;</mo><mo>,</mo><mspace linebreak=\"newline\"></mspace><mn>4</mn><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>sinA</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>or</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>3</mn><mi>sinA</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn></math></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">Table shows the an</span><span style=\"font-family: Cambria Math;\">nual income of 5 schools (in lakhs rupees).</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_49714244311701671472181.png\" width=\"449\" height=\"175\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Which school has the highest percentage of income from donations out of its total income?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2370;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_29160618711701671907118.png\" width=\"397\" height=\"240\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Q</p>\\n", "<p>P</p>\\n", 
                                "<p>S</p>\\n", "<p>R</p>\\n"],
                    options_hi: ["<p>Q</p>\\n", "<p>P</p>\\n",
                                "<p>S</p>\\n", "<p>R</p>\\n"],
                    solution_en: "<p>3.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Percentage of income from donations of school P = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mrow><mn>140</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>280</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>110</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>102</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>160</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math>= 17.67%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Percentage of income from donations of school Q = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>250</mn><mrow><mn>250</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>310</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>110</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>104</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>180</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math> = 26.20% </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Percentage of income from donations of school R = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mrow><mn>200</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>340</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>120</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>110</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>190</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math> = 20.83%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Percentage of income from donations of school S = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mrow><mn>300</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>200</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>125</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>120</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>200</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math>= 31.74%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Clearly , School &lsquo;S&rsquo; has the highest % of income from donation out of its total income .</span></p>\\n",
                    solution_hi: "<p>3.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mrow><mn>140</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>280</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>110</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>102</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>160</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math> = 17.67%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>250</mn><mrow><mn>250</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>310</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>110</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>104</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>180</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math> = 26.20% </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> R </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mrow><mn>200</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>340</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>120</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>110</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>190</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math> = 20.83%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> S </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mrow><mn>300</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>200</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>125</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>120</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>200</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math> = 31.74%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> \'S\' </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Cambria Math;\">&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> A motorboat, whose speed is 20 km/h in still water takes 3 hours to cover a distance of 22.5 km upstream and then comes back to its starting point. The speed of the stream </span><span style=\"font-family: Cambria Math;\">(in km/h) is:</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2379;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> 20 km/h </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2343;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> 22.5 km </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> (km/h </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>10</p>\\n", "<p>9</p>\\n", 
                                "<p>8</p>\\n", "<p>11</p>\\n"],
                    options_hi: ["<p>10</p>\\n", "<p>9</p>\\n",
                                "<p>8</p>\\n", "<p>11</p>\\n"],
                    solution_en: "<p>4.(a)</p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>=</mo><mfrac><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>3</mn><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mfenced><mrow><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced><mfenced><mrow><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mn>40</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn><mo>.</mo><mn>5</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfenced><mrow><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced><mo>&nbsp;</mo><mfenced><mrow><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced><mspace linebreak=\"newline\"></mspace><mn>300</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfenced><mrow><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced><mo>&nbsp;</mo><mfenced><mrow><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced><mspace linebreak=\"newline\"></mspace><mn>300</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>20</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>-</mo><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>300</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>400</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>-</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mi>km</mi><mo>/</mo><mi mathvariant=\"normal\">h</mi><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>km</mi><mo>/</mo><mi mathvariant=\"normal\">h</mi></math></p>\\n",
                    solution_hi: "<p>4.(a)</p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>=</mo><mfrac><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>3</mn><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mfenced><mrow><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced><mfenced><mrow><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mn>40</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn><mo>.</mo><mn>5</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfenced><mrow><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced><mo>&nbsp;</mo><mfenced><mrow><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced><mspace linebreak=\"newline\"></mspace><mn>300</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfenced><mrow><mn>20</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced><mo>&nbsp;</mo><mfenced><mrow><mo>&nbsp;</mo><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi></mrow></mfenced><mspace linebreak=\"newline\"></mspace><mn>300</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>20</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>-</mo><mo>(</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>300</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>400</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>-</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mi>km</mi><mo>/</mo><mi mathvariant=\"normal\">h</mi><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>km</mi><mo>/</mo><mi mathvariant=\"normal\">h</mi></math></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5</span><span style=\"font-family: Cambria Math;\">. A person sold two items selling each for &#8377;42,600. On the first item, he made a loss of 25%, while on the second, he gained 20%. Find his overall percentage gain or loss, giving your answer correct to one place of decimal.</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">42,600 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2368;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2348;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2310;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;</span><span style=\"font-family: Cambria Math;\">&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p>7.7% gain</p>\\n", "<p>7.7% loss</p>\\n", 
                                "<p>8.3% loss</p>\\n", "<p>8.2% loss</p>\\n"],
                    options_hi: ["<p>7.7% <span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span></p>\\n", "<p>7.7% <span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span></p>\\n",
                                "<p>8.3% <span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span></p>\\n", "<p>8.2% <span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span></p>\\n"],
                    solution_en: "<p>5.(b)</p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Overall</mi><mo>&nbsp;</mo><mi>Profit</mi><mo>/</mo><mi>Loss</mi><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>xy</mi></mrow><mrow><mn>200</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>(</mo><mo>-</mo><mn>25</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>20</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>(</mo><mo>-</mo><mn>25</mn><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mn>20</mn><mo>)</mo></mrow><mrow><mn>200</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>25</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>20</mn></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>-</mo><mn>500</mn><mo>-</mo><mn>1000</mn></mrow><mn>195</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>-</mo><mn>1500</mn></mrow><mn>195</mn></mfrac><mo>=</mo><mo>-</mo><mn>7</mn><mo>.</mo><mn>7</mn><mo>%</mo><mspace linebreak=\"newline\"></mspace><mo>-</mo><mi>ve</mi><mo>&nbsp;</mo><mi>indicates</mi><mo>&nbsp;</mo><mi>loss</mi><mo>.</mo></math></p>\\n",
                    solution_hi: "<p>5.(b)</p>\\r\\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2325;&#2369;&#2354;</mi><mo>&nbsp;</mo><mi>&#2354;&#2366;&#2349;</mi><mo>/</mo><mi>&#2361;&#2366;&#2344;&#2367;</mi><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>xy</mi></mrow><mrow><mn>200</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>(</mo><mo>-</mo><mn>25</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>20</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>(</mo><mo>-</mo><mn>25</mn><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mn>20</mn><mo>)</mo></mrow><mrow><mn>200</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>25</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>20</mn></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>-</mo><mn>500</mn><mo>-</mo><mn>1000</mn></mrow><mn>195</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>-</mo><mn>1500</mn></mrow><mn>195</mn></mfrac><mo>=</mo><mo>-</mo><mn>7</mn><mo>.</mo><mn>7</mn><mo>%</mo><mspace linebreak=\"newline\"></mspace><mo>-</mo><mi>ve</mi><mo>&nbsp;</mo><mi>&#2361;&#2366;&#2344;&#2367;</mi><mo>&nbsp;</mo><mi>&#2325;&#2379;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;&#2404;</mi></math></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> A man invested &#8377;4,500 at 5% per annum and &#8377;5,500 at 8% per annum simple interest. Both investments were for 4 years each. The am</span><span style=\"font-family: Cambria Math;\">ount received from the lower rate of interest is what percent (rounded off to the nearest integer) of the amount received from the higher rate of interest?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> 5% </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">4,500 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;</span><span style=\"font-family: Cambria Math;\">&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\">5,500 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> 4-4 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2330;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2325;&#2335;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2344;&#2381;&#2344;&#2367;&#2325;&#2335;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    options_en: ["<p>63%</p>\\n", "<p>74%</p>\\n", 
                                "<p>51%</p>\\n", "<p>48%</p>\\n"],
                    options_hi: ["<p>63%</p>\\n", "<p>74%</p>\\n",
                                "<p>51%</p>\\n", "<p>48%</p>\\n"],
                    solution_en: "<p>6.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">S.I. from 1st principal = 4500&nbsp; &times; 5% &times; 4 = 900 &#8377; </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Amount</mi><mn>1</mn></msub><mo>&nbsp;</mo></math>= 4500 + 900 = 5400 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">S.I. from 2nd principal = 5500&nbsp; &times; 8% &times; 4 = 1760 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Amount</mi><mn>2</mn></msub></math>= 5500 + 1760 = 7260 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required % = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5400</mn><mn>7260</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&asymp;</mo><mn>74</mn><mo>%</mo></math></span></p>\\n",
                    solution_hi: "<p>6.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> = 4500 &times; 5% &times; 4 = 900 </span><span style=\"font-family: Cambria Math;\">&#8377;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>&#2352;&#2366;&#2358;&#2367;</mi><mn>1</mn></msub></math>= 4500 + 900 = 5400 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> = 5500&nbsp; &times; 8% &times; 4 = 1760 </span><span style=\"font-family: Cambria Math;\">&#8377;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>&#2352;&#2366;&#2358;&#2367;</mi><mn>2</mn></msub></math>= 5500 +1760 = 7260 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> % = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5400</mn><mn>7260</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&asymp;</mo><mn>74</mn><mo>%</mo></math></span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">In a circle with radius 20 cm, X is a point located at a </span><span style=\"font-family: Cambria Math;\">distance of y Cm from the center of the circle. If the length of a tangent drawn from point X to the circle is 21 cm, find the value of y.</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">20 cm </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, X </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> y cm </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> X </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2368;&#2306;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2352;&#2381;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 21 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p>31</p>\\n", "<p>28</p>\\n", 
                                "<p>29</p>\\n", "<p>25</p>\\n"],
                    options_hi: ["<p>31</p>\\n", "<p>28</p>\\n",
                                "<p>29</p>\\n", "<p>25</p>\\n"],
                    solution_en: "<p>7.(c) <span style=\"font-family: Cambria Math;\">Pythagorean triplets :</span><span style=\"font-family: Cambria Math;\">- (20,21,29)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701670517/word/media/image1.png\" width=\"173\" height=\"95\"></p>\\n",
                    solution_hi: "<p>7.(c) <span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2311;&#2341;&#2366;&#2327;&#2379;&#2352;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> :- (20,21,29)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701670517/word/media/image1.png\" width=\"173\" height=\"95\"></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> If </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mrow><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>29</mn></math><span style=\"font-family: Cambria Math;\">, then find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mrow><mo>&nbsp;</mo><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>29</mn></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    options_en: ["<p>&plusmn;4</p>\\n", "<p>&plusmn;3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\\n", 
                                "<p>&plusmn;3</p>\\n", "<p>&plusmn;4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\\n"],
                    options_hi: ["<p>&plusmn;4</p>\\n", "<p>&plusmn;3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\\n",
                                "<p>&plusmn;3</p>\\n", "<p>&plusmn;4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\\n"],
                    solution_en: "<p>8.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mn>29</mn><mspace linebreak=\"newline\"></mspace><mi>On</mi><mo>&nbsp;</mo><mi>Subtracting</mi><mo>&nbsp;</mo><mi>both</mi><mo>&nbsp;</mo><mi>side</mi><mo>&nbsp;</mo><mi>by</mi><mo>&nbsp;</mo><mn>2</mn><mspace linebreak=\"newline\"></mspace><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>29</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mspace linebreak=\"newline\"></mspace><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>27</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mfenced><mrow><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></mrow></mfenced><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&plusmn;</mo><mn>3</mn><msqrt><mn>3</mn></msqrt></math></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>8.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mn>29</mn><mspace linebreak=\"newline\"></mspace><mi>&#2342;&#2379;&#2344;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2360;&#2375;</mi><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mi>&#2328;&#2335;&#2366;&#2344;&#2375;</mi><mo>&nbsp;</mo><mi>&#2346;&#2352;</mi><mspace linebreak=\"newline\"></mspace><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>29</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mspace linebreak=\"newline\"></mspace><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>27</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mfenced><mrow><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></mrow></mfenced><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&plusmn;</mo><mn>3</mn><msqrt><mn>3</mn></msqrt></math></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> The production of three products by three companies is given below in the table.</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701670517/word/media/image2.png\" width=\"385\" height=\"143\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Which of the following gives maximum production of a product by any company?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_50916581511701673948904.png\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Product C, Company 3</p>\\n", "<p>Product B, Company 1</p>\\n", 
                                "<p>Product A, Company 3</p>\\n", "<p>Product A, Company 2</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> C, </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> 3</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> B, </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> 1</span></p>\\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> A, </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> 3 </span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> A, </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> 2</span></p>\\n"],
                    solution_en: "<p>9.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Clearly , Company &lsquo;3&rsquo; products &lsquo;C&rsquo; gives maximum production of a product .</span></p>\\n",
                    solution_hi: "<p>9.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> \'3\' </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> \'C\' </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> Find the volume of a solid sphere whose diameter is 42 cm. (Use &pi; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">)</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2336;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> 42 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> (&pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">)</span></p>\\n",
                    options_en: ["<p>38807 cm&sup3;</p>\\n", "<p>38808 <span style=\"font-family: Cambria Math;\">cm&sup3;</span></p>\\n", 
                                "<p>38806 cm&sup3;</p>\\n", "<p>38805 cm&sup3;</p>\\n"],
                    options_hi: ["<p>38807 cm&sup3;</p>\\n", "<p>38808 cm&sup3;</p>\\n",
                                "<p>38806 cm&sup3;</p>\\n", "<p>38805 cm&sup3;</p>\\n"],
                    solution_en: "<p>60.(b)</p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Volume</mi><mo>&nbsp;</mo><mi>of</mi><mo>&nbsp;</mo><mi>solid</mi><mo>&nbsp;</mo><mi>sphere</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>21</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>21</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>21</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>88</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>21</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>21</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>38808</mn><mo>&nbsp;</mo><msup><mi>cm</mi><mn>3</mn></msup></math></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>10.(b)</p>\\r\\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2336;&#2379;&#2360;</mi><mo>&nbsp;</mo><mi>&#2327;&#2379;&#2354;&#2375;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2310;&#2351;&#2340;&#2344;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>21</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>21</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>21</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>88</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>21</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>21</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>38808</mn><mo>&nbsp;</mo><msup><mi>cm</mi><mn>3</mn></msup></math></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">If the difference between the simple interest and the compound interest for 3 years at the rate of 10% per annum on a certain sum is equal to &#8377;310, then that sum of money is:</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;</span><span style=\"font-family: Cambria Math;\">&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;310 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    options_en: ["<p>&#8377;8,000</p>\\n", "<p>&#8377;9,500</p>\\n", 
                                "<p>&#8377;12,000</p>\\n", "<p>&#8377;10,000</p>\\n"],
                    options_hi: ["<p>&#8377;8,000</p>\\n", "<p>&#8377;9,500</p>\\n",
                                "<p>&#8377;12,000</p>\\n", "<p>&#8377;10,000</p>\\n"],
                    solution_en: "<p>11.(d) <span style=\"font-family: Cambria Math;\">Effective rate at 10% for 3 years in case of C.I. = 33.1%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Effective rate at 10% for 3 years in case of S.I. = 30%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required difference = 3.1% = 310 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Principal (100%) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>310</mn><mrow><mn>3</mn><mo>.</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 10,000 &#8377;</span></p>\\n",
                    solution_hi: "<p>11.(d) <span style=\"font-family: Cambria Math;\">C.I. </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2350;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\">= 33.1%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">S.I. </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2350;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 30%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 3.1% = 310 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> (100%) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>310</mn><mrow><mn>3</mn><mo>.</mo><mn>1</mn></mrow></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 10,000 &#8377;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. 8 </span><span style=\"font-family: Cambria Math;\">cm and 5 cm are the radii of two circles. If the distance between the centres of the two circles is 11 cm, then the length </span><span style=\"font-family: Cambria Math;\">(in cm) of the common tangent of two circles is:</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">8 cm </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 5 cm </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2340;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2340;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> 11 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2340;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2349;&#2351;&#2344;&#2367;&#2359;&#2381;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2352;&#2381;&#2358;&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> (cm </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>\\n", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>\\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>\\n", "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>\\n"],
                    options_hi: ["<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>\\n", "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>\\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>\\n", "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>\\n"],
                    solution_en: "<p>12.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Length of direct common tangents = </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mfenced><mn>11</mn></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>8</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>121</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>9</mn></msqrt><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>112</mn></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><msqrt><mn>7</mn></msqrt><mo>&nbsp;</mo><mi>cm</mi></math></p>\\r\\n<p>&nbsp;</p>\\n",
                    solution_hi: "<p>12.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2368;&#2343;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2349;&#2351;&#2344;&#2367;&#2359;&#2381;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2352;&#2381;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2375;&#2326;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mfenced><mn>11</mn></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>8</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>5</mn></mrow></mfenced><mn>2</mn></msup></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>121</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>9</mn></msqrt><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>112</mn></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><msqrt><mn>7</mn></msqrt><mo>&nbsp;</mo><mi>cm</mi></math></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">Simplify the given expression.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>80</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>70</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>70</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>50</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>50</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>50</mn><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>840000</mn></mrow><mrow><mn>6400</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4900</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2500</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>5600</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3500</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4000</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>80</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>70</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>70</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>50</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>50</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>50</mn><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>840000</mn></mrow><mrow><mn>6400</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4900</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2500</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>5600</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3500</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4000</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    options_en: ["<p>100</p>\\n", "<p>200</p>\\n", 
                                "<p>400</p>\\n", "<p>300</p>\\n"],
                    options_hi: ["<p>100</p>\\n", "<p>200</p>\\n",
                                "<p>400</p>\\n", "<p>300</p>\\n"],
                    solution_en: "<p>13.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>c</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>a</mi><mi>b</mi><mi>c</mi></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>c</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>c</mi><mi>a</mi></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>o</mi><mo>&nbsp;</mo><mo>,</mo><mfrac><mrow><msup><mn>80</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>70</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>50</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>50</mn></mrow><mrow><msup><mn>80</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>70</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>50</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>50</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>50</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>80</mn></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>80</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>50</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>200</mn></math></p>\\n",
                    solution_hi: "<p>13.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>c</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>a</mi><mi>b</mi><mi>c</mi></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>c</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>c</mi><mi>a</mi></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>+</mo><mi>c</mi><mo>)</mo><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2340;&#2307;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>,</mo><mfrac><mrow><msup><mn>80</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>70</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>50</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>50</mn></mrow><mrow><msup><mn>80</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>70</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>50</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>50</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>50</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>80</mn></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mn>80</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>70</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>50</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>200</mn></math></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">In a circle with centre</span><span style=\"font-family: Cambria Math;\"> O and diameter EF, if the two chords AE = AF, then m&ang;AEF is:</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> o </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> EF </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2368;&#2357;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> AE = AF, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> m&ang;A</span><span style=\"font-family: Cambria Math;\">EF </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\\n",
                    options_en: ["<p>80&deg;</p>\\n", "<p>90&deg;</p>\\n", 
                                "<p>45&deg;</p>\\n", "<p>60&deg;</p>\\n"],
                    options_hi: ["<p>80&deg;</p>\\n", "<p>90&deg;</p>\\n",
                                "<p>45&deg;</p>\\n", "<p>60&deg;</p>\\n"],
                    solution_en: "<p>14.(c)</p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701670517/word/media/image3.png\" width=\"215\" height=\"176\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&ang;EAF = 90&deg; &hellip;(angle in semicircle)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Now , AE = AF , so &ang;AEF = &ang;AFE</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math><span style=\"font-weight: 400;\">EAF</span></span><span style=\"font-family: Cambria Math;\"> , </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&ang;EAF +&ang;AEF + &ang;AFE = 180&deg;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">2&ang;AEF = 90&deg; &rArr;</span><span style=\"font-family: Cambria Math;\"> &ang;AEF = 45&deg;</span></p>\\n",
                    solution_hi: "<p>14.(c)</p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701670517/word/media/image3.png\" width=\"213\" height=\"174\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&ang;EAF = 90&deg; &hellip;(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\">)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\"> , AE = AF , </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> &ang;AEF = &ang;AFE</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math><span style=\"font-weight: 400;\">EAF</span><span style=\"font-weight: 400;\"> </span></span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> , </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&ang;EAF +&ang;AEF + &ang;AFE = 180&deg;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">2&ang;AEF = 90&deg;&rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> &ang;AEF = 45&deg;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15.</span><span style=\"font-family: Cambria Math;\"> The population of a village in 2019 was 30,000. The increase in population rate was 15% and 25%, respectively, </span><span style=\"font-family: Cambria Math;\">from the year 2019 to 2020 and 2020 to 2021. What was the population in the year 2021?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">65. </span><span style=\"font-family: Cambria Math;\">2019 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2366;&#2305;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 30,000 </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2019 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2020 </span><span style=\"font-family: Cambria Math;\">&#2340;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> 2020 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2021 </span><span style=\"font-family: Cambria Math;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Cambria Math;\">&#2319;&#2357;&#2306;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Cambria Math;\">&#2352;&#2361;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2021 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    options_en: ["<p>42,000</p>\\n", "<p>42,325</p>\\n", 
                                "<p>42,523</p>\\n", "<p>43,125</p>\\n"],
                    options_hi: ["<p>42,000</p>\\n", "<p>42,325</p>\\n",
                                "<p>42,523</p>\\n", "<p>43,125</p>\\n"],
                    solution_en: "<p>15.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Population in 2021 = 30,000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>4</mn></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 43,125</span></p>\\n",
                    solution_hi: "<p>65.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">2021 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: \'Cambria Math\';\">30,000 &times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>4</mn></mfrac></math></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 43,125</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\"> The Pie chart shows the percentage distributions of total number of laptops (both Dell and HP) sold by six stores in March.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Refer to the pie chart and table carefully and answe</span><span style=\"font-family: Cambria Math;\">r the given questions. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Total number =11200</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701670517/word/media/image4.png\" width=\"197\" height=\"196\"></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_38851935411701675798838.png\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The number of Dell laptops sold by store B is what percent more than that of HP laptops sold by stores E and F? (correct to two decimal places)</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2379;&#2352;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\"> ( </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2330;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2340;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 11200</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701670517/word/media/image4.png\"></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_79726701911701681440649.png\" width=\"336\" height=\"164\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2379;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2376;&#2346;&#2335;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2379;&#2352;</span><span style=\"font-family: Cambria Math;\"> E </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> F </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2330;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? (</span><span style=\"font-family: Cambria Math;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span></p>\\n",
                    options_en: ["<p>70.43%</p>\\n", "<p>27.64%</p>\\n", 
                                "<p>57.64%</p>\\n", "<p>64.70%</p>\\n"],
                    options_hi: ["<p>70.43%</p>\\n", "<p>27.64%</p>\\n",
                                "<p>57.64%</p>\\n", "<p>64.70%</p>\\n"],
                    solution_en: "<p>16.(d)</p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><mi>Dell</mi><mo>&nbsp;</mo><mi>laptops</mi><mo>&nbsp;</mo><mi>sold</mi><mo>&nbsp;</mo><mi>by</mi><mo>&nbsp;</mo><mi>store</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">B</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>11200</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>26</mn><mo>%</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>7</mn><mn>13</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>1568</mn><mspace linebreak=\"newline\"></mspace><mi>HP</mi><mo>&nbsp;</mo><mi>laptops</mi><mo>&nbsp;</mo><mi>sold</mi><mo>&nbsp;</mo><mi>by</mi><mo>&nbsp;</mo><mi>store</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">E</mi><mo>&nbsp;</mo><mi>and</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">F</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>11200</mn><mo>&nbsp;</mo><mo>{</mo><mo>(</mo><mn>14</mn><mo>%</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mo>&nbsp;</mo><mn>10</mn><mo>%</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>)</mo><mo>}</mo><mo>=</mo><mn>952</mn><mspace linebreak=\"newline\"></mspace><mi>Required</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>1568</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>952</mn></mrow><mn>952</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>64</mn><mo>.</mo><mn>7</mn><mo>%</mo></math></p>\\n",
                    solution_hi: "<p>16.(d)</p>\\r\\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2360;&#2381;&#2335;&#2379;&#2352;</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">B</mi><mo>&nbsp;</mo><mi>&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</mi><mo>&nbsp;</mo><mi>&#2348;&#2375;&#2330;&#2375;</mi><mo>&nbsp;</mo><mi>&#2327;&#2319;</mi><mo>&nbsp;</mo><mi>Dell</mi><mo>&nbsp;</mo><mi>&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>11200</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>26</mn><mo>%</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>7</mn><mn>13</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>1568</mn><mspace linebreak=\"newline\"></mspace><mi>&#2360;&#2381;&#2335;&#2379;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">E</mi><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">F</mi><mo>&nbsp;</mo><mi>&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</mi><mo>&nbsp;</mo><mi>&#2348;&#2375;&#2330;&#2375;</mi><mo>&nbsp;</mo><mi>&#2327;&#2319;</mi><mo>&nbsp;</mo><mi>HP</mi><mo>&nbsp;</mo><mi>&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>11200</mn><mo>&nbsp;</mo><mo>{</mo><mo>(</mo><mn>14</mn><mo>%</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mo>&nbsp;</mo><mn>10</mn><mo>%</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>)</mo><mo>}</mo><mo>=</mo><mn>952</mn><mspace linebreak=\"newline\"></mspace><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>1568</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>952</mn></mrow><mn>952</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>64</mn><mo>.</mo><mn>7</mn><mo>%</mo></math></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">17. </span><span style=\"font-family: Cambria Math;\">The mean proportion of number 0.049 and 0.9 is:</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">17. </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 0.049 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 0.9 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p>0.021</p>\\n", "<p>0.21</p>\\n", 
                                "<p>0.63</p>\\n", "<p>21</p>\\n"],
                    options_hi: ["<p>0.021</p>\\n", "<p>0.21</p>\\n",
                                "<p>0.63</p>\\n", "<p>21</p>\\n"],
                    solution_en: "<p>17.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Mean proportional =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>.</mo><mn>049</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><mn>9</mn></msqrt></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 0.21</span></p>\\n",
                    solution_hi: "<p>17.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>.</mo><mn>049</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><mn>9</mn></msqrt></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 0.21</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">18. </span><span style=\"font-family: Cambria Math;\">A 45% profit is made when a discount of 45% is given on the marked price of an item. If the discount on the marked price of the same item is 50%, then the profit will be:</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">18. </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 45% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 45% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> 50% </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    options_en: ["<p>31<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>11</mn></mfrac></math>%</p>\\n", "<p>33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>11</mn></mfrac></math>%</p>\\n", 
                                "<p>30<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>11</mn></mfrac></math>%</p>\\n", "<p>31<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>11</mn></mfrac></math>%</p>\\n"],
                    options_hi: ["<p>31<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>11</mn></mfrac><mo>%</mo></math></p>\\n", "<p>33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>11</mn></mfrac><mo>%</mo></math></p>\\n",
                                "<p>30<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>11</mn></mfrac><mo>%</mo></math></p>\\n", "<p>31<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>11</mn></mfrac><mo>%</mo></math></p>\\n"],
                    solution_en: "<p>68.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">C</mi><mo>.</mo><mi mathvariant=\"normal\">P</mi><mo>.</mo></mrow><mrow><mi mathvariant=\"normal\">M</mi><mo>.</mo><mi mathvariant=\"normal\">P</mi><mo>.</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>45</mn></mrow><mrow><mn>100</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>45</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>55</mn><mn>145</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>11</mn><mn>29</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>New</mi><mo>&nbsp;</mo><mi>selling</mi><mo>&nbsp;</mo><mi>price</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>29</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>50</mn><mo>%</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>14</mn><mo>.</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mi>Required</mi><mo>&nbsp;</mo><mi>profit</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>14</mn><mo>.</mo><mn>5</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>11</mn></mrow><mn>11</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>31</mn><mfrac><mn>9</mn><mn>11</mn></mfrac><mo>%</mo></math></p>\\n",
                    solution_hi: "<p>18.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>45</mn></mrow><mrow><mn>100</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>45</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>55</mn><mn>145</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>11</mn><mn>29</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2344;&#2351;&#2366;</mi><mo>&nbsp;</mo><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>=</mo><mo>&nbsp;</mo><mn>29</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>50</mn><mo>%</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>14</mn><mo>.</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&nbsp;</mo><mi>&#2354;&#2366;&#2349;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mn>14</mn><mo>.</mo><mn>5</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>11</mn></mrow><mn>11</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>31</mn><mfrac><mn>9</mn><mn>11</mn></mfrac><mo>%</mo></math></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">19. </span><span style=\"font-family: Cambria Math;\">Simplify the given expression.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">40 &times; 7 - 2 &times; (11</span><span style=\"font-family: Cambria Math;\">&sup3;</span><span style=\"font-family: Cambria Math;\"> &divide; 11) &divide; 22 + 30</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">19. </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">40 &times; 7 - 2 &times; (11</span><span style=\"font-family: Cambria Math;\">&sup3;</span><span style=\"font-family: Cambria Math;\"> &divide; 11) &divide; 22 + 30</span></p>\\n",
                    options_en: ["<p>297</p>\\n", "<p>298</p>\\n", 
                                "<p>300</p>\\n", "<p>299</p>\\n"],
                    options_hi: ["<p>297</p>\\n", "<p>298</p>\\n",
                                "<p>300</p>\\n", "<p>299</p>\\n"],
                    solution_en: "<p>19.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">40 &times; 7 - 2 &times; (11</span><span style=\"font-family: Cambria Math;\">&sup3;</span><span style=\"font-family: Cambria Math;\"> &divide; 11) &divide; 22 + 30</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 280 - </span><span style=\"font-family: Cambria Math;\">2 &times; 121 &divide; 22 + 30</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 280 - </span><span style=\"font-family: Cambria Math;\">11 + 30 = 299</span></p>\\n",
                    solution_hi: "<p>19.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">40 &times; 7 - 2 &times; (11</span><span style=\"font-family: Cambria Math;\">&sup3;</span><span style=\"font-family: Cambria Math;\"> &divide; 11) &divide; 22 + 30</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 280 - </span><span style=\"font-family: Cambria Math;\">2 &times; 121 &divide; 22 + 30</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 280 - </span><span style=\"font-family: Cambria Math;\">11 + 30 = 299</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">20. </span><span style=\"font-family: Cambria Math;\">M is the mid-point of side QR of a parallelogram PQRS (P being on the top left hand, followed by other points going clockwise). The line SM is drawn intersecting PQ produced at T. What is the length (in terms of </span><span style=\"font-family: Cambria Math;\">the length of SR) of PT?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">20. </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> PQRS </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> QR </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> M </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, (</span><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2368;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> SM </span><span style=\"font-family: Cambria Math;\">&#2326;&#2368;&#2306;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2338;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> PQ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> T </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2330;&#2381;&#2331;&#2375;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> PT </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> (SR </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\">&#2381;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>SR</mi></mrow><mn>2</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>SR</mi><mn>2</mn></mfrac></math></p>\\n", 
                                "<p>2SR</p>\\n", "<p>SR</p>\\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>SR</mi></mrow><mn>2</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>SR</mi><mn>2</mn></mfrac></math></p>\\n",
                                "<p>2SR</p>\\n", "<p>SR</p>\\n"],
                    solution_en: "<p>70.(c)</p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701670517/word/media/image5.png\" width=\"249\" height=\"104\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">SR = PQ &hellip;.(PQRS is a parallelogram)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo><mi>SMR</mi><mo>&nbsp;</mo><mo>&cong;</mo><mo>&#8710;</mo><mi>QMT</mi><mo>&nbsp;</mo></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&ang;RMS = &ang;QMT &hellip;.(vertically opposite)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&ang;MRS = &ang;MQT &hellip;(alternate angle)</span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>So</mi><mo>&nbsp;</mo><mo>,</mo><mfrac><mi>RM</mi><mi>MQ</mi></mfrac><mo>=</mo><mfrac><mi>SR</mi><mi>QT</mi></mfrac><mo>&rArr;</mo><mfrac><mn>1</mn><mn>1</mn></mfrac><mo>=</mo><mfrac><mi>SR</mi><mi>QT</mi></mfrac><mo>&rArr;</mo><mi>SR</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>PQ</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>QT</mi><mspace linebreak=\"newline\"></mspace><mi>PT</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>PQ</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>SR</mi></math></p>\\n",
                    solution_hi: "<p>70.(c)</p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701670517/word/media/image5.png\" width=\"249\" height=\"104\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">SR = PQ &hellip;.(PQRS <span style=\"font-weight: 400;\">&#2319;&#2325; &#2360;&#2350;&#2366;&#2306;&#2340;&#2352; &#2330;&#2340;&#2369;&#2352;&#2381;&#2349;&#2369;&#2332; &#2361;&#2376;&#2404;</span>)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo><mi>SMR</mi><mo>&nbsp;</mo><mo>&cong;</mo><mo>&#8710;</mo><mi>QMT</mi><mo>&nbsp;</mo></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&ang;RMS = &ang;QMT &hellip;.(<span style=\"font-weight: 400;\">&#2314;&#2352;&#2381;&#2343;&#2381;&#2357;&#2366;&#2343;&#2352; &#2357;&#2367;&#2346;&#2352;&#2368;&#2340;</span>)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&ang;MRS = &ang;MQT &hellip;(<span style=\"font-weight: 400;\">&#2357;&#2376;&#2325;&#2354;&#2381;&#2346;&#2367;&#2325; &#2325;&#2379;&#2339;</span>)</span></p>\\r\\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>&nbsp;</mo><mo>,</mo><mfrac><mi>RM</mi><mi>MQ</mi></mfrac><mo>=</mo><mfrac><mi>SR</mi><mi>QT</mi></mfrac><mo>&rArr;</mo><mfrac><mn>1</mn><mn>1</mn></mfrac><mo>=</mo><mfrac><mi>SR</mi><mi>QT</mi></mfrac><mo>&rArr;</mo><mi>SR</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>PQ</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>QT</mi><mspace linebreak=\"newline\"></mspace><mi>PT</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>PQ</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mi>SR</mi></math></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">21. </span><span style=\"font-family: Cambria Math;\">In a mixture of 178 litres, the ratio of water and milk is 5:7. How much water should be added to make the ratio of water and milk 3:4 ?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">21. </span><span style=\"font-family: Cambria Math;\">178 </span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2358;&#2381;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 5:7 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2358;&#2381;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 3:4 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2354;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>24</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> liters</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>13</mn><mn>24</mn></mfrac></math>liters</span></p>\\n", 
                                "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>14</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> liters</span></p>\\n", "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>24</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> liters</span></p>\\n"],
                    options_hi: ["<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>24</mn></mfrac></math><span style=\"font-family: Cambria Math;\">&#2354;&#2368;&#2335;&#2352;</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>13</mn><mn>24</mn></mfrac></math>&#2354;&#2368;&#2335;&#2352;</span></p>\\n",
                                "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>14</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;&#2335;&#2352;</span></p>\\n", "<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>24</mn></mfrac></math><span style=\"font-family: Cambria Math;\">&#2354;&#2368;&#2335;&#2352;</span></p>\\n"],
                    solution_en: "<p>71.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Ratio =&nbsp; &nbsp; &nbsp; Water : Milk</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Initial =&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 7 </span><span style=\"font-family: Cambria Math;\">) &times; 4</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Final =&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 4 </span><span style=\"font-family: Cambria Math;\">) &times; 7</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">-------------------------</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Initial quantity (48 units) = 178 litres</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Water added (1 unit) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>178</mn><mn>48</mn></mfrac><mo>=</mo><mn>3</mn><mfrac><mn>17</mn><mn>24</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;litres</span></p>\\n",
                    solution_hi: "<p>21.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> =&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;:&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2343;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> =&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;7 </span><span style=\"font-family: Cambria Math;\">) &times; 4)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> =&nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 4 </span><span style=\"font-family: Cambria Math;\">) &times; 7)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">--------------------</span><span style=\"font-family: Cambria Math;\">-------------------</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> (48 units) = 178 </span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;&#2335;&#2352;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2354;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> (1 unit) = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>178</mn><mn>48</mn></mfrac><mo>=</mo><mn>3</mn><mfrac><mn>17</mn><mn>24</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;&#2335;&#2352;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">22. </span><span style=\"font-family: Cambria Math;\">Dileep purchases 90 articles for &#8377;13,230 and sells them at a loss equal to the selling price of 8 articles. What will be the selling price of one article ?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">22. </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2354;&#2368;&#2346;</span><span style=\"font-family: Cambria Math;\"> 90 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> &#8377;13,230 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    options_en: ["<p>&#8377;120</p>\\n", "<p>&#8377;135</p>\\n", 
                                "<p>&#8377;130</p>\\n", "<p>&#8377;125</p>\\n"],
                    options_hi: ["<p>&#8377;120</p>\\n", "<p>&#8377;135</p>\\n",
                                "<p>&#8377;130</p>\\n", "<p>&#8377;125</p>\\n"],
                    solution_en: "<p>22.(b) <span style=\"font-family: Cambria Math;\">90 &times; C.P. = 90 &times; S.P.&nbsp; +&nbsp; 8 &times; S.P.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">90 &times; C.P.&nbsp; =&nbsp; 98 &times; S.P.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required selling price of each articles :- </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>,</mo><mn>230</mn></mrow><mn>98</mn></mfrac></math>= 135 &#8377;</span></p>\\n",
                    solution_hi: "<p>22.(b) <span style=\"font-family: Cambria Math;\">90 &times; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 90 &times; </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; + 8 &times; </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">90 &times; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351; </span><span style=\"font-family: Cambria Math;\">= 98 &times; </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> :- </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>,</mo><mn>230</mn></mrow><mn>98</mn></mfrac></math>= 135 &#8377;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">23. </span><span style=\"font-family: Cambria Math;\">If sin A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">, where A is an acute angle, what is the value of</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mi>A</mi><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mi>A</mi></mrow><mrow><mo>&radic;</mo><mn>21</mn><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mi>A</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">23.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> sin A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2361;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&nbsp;</mo><mi>sin</mi><mo>&nbsp;</mo><mi>A</mi><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mi>A</mi></mrow><mrow><mo>&radic;</mo><mn>21</mn><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mi>A</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math></p>\\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>\\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math></p>\\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>\\n"],
                    solution_en: "<p>23.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sinA</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mi>Perpendicular</mi><mi>Hypotenuse</mi></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mi>Base</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>25</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>21</mn></msqrt><mspace linebreak=\"newline\"></mspace><mi>Now</mi><mo>&nbsp;</mo><mo>,</mo><mfrac><mrow><mn>5</mn><mo>&nbsp;</mo><mi>sinA</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mi>cosecA</mi></mrow><mrow><msqrt><mn>21</mn></msqrt><mo>&nbsp;</mo><mi>secA</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>5</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>5</mn></mfrac></mstyle><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>2</mn></mfrac></mstyle></mrow><mrow><msqrt><mn>21</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><msqrt><mn>21</mn></msqrt></mfrac></mstyle></mrow></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>5</mn></mfrac></math></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>23.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sinA</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mi>&#2310;&#2343;&#2366;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>25</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn></msqrt><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>21</mn></msqrt><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2348;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>,</mo><mfrac><mrow><mn>5</mn><mo>&nbsp;</mo><mi>sinA</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mi>cosecA</mi></mrow><mrow><msqrt><mn>21</mn></msqrt><mo>&nbsp;</mo><mi>secA</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>5</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>5</mn></mfrac></mstyle><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>2</mn></mfrac></mstyle></mrow><mrow><msqrt><mn>21</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><msqrt><mn>21</mn></msqrt></mfrac></mstyle></mrow></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>5</mn></mfrac></math></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">24.</span><span style=\"font-family: Cambria Math;\"> Simplify </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>.</mo></math></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">24.</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>.</mo></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    options_en: ["<p>12xy</p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></math></p>\\n", 
                                "<p>24 xy</p>\\n", "<p>9x&sup2; - 4y&sup2;</p>\\n"],
                    options_hi: ["<p>12xy</p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>8</mn><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></math></p>\\n",
                                "<p>24 xy</p>\\n", "<p>9x&sup2; - 4y&sup2;</p>\\n"],
                    solution_en: "<p>74.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mi>ab</mi><mspace linebreak=\"newline\"></mspace><mi>Here</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><msup><mfenced><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>24</mn><mi>xy</mi></math></p>\\n",
                    solution_hi: "<p>24.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mi mathvariant=\"normal\">a</mi><mo>-</mo><mi mathvariant=\"normal\">b</mi></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mi>ab</mi><mspace linebreak=\"newline\"></mspace><mi>&#2351;&#2361;&#2366;&#2305;</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><msup><mfenced><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mfenced><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi></mrow></mfenced><mn>2</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>24</mn><mi>xy</mi></math></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">25.</span><span style=\"font-family: Cambria Math;\"> If a man completed a work in 10 days and the same work was completed by a woman in 8 days. How many days will it take to complete if they work together?</span></p>\\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">25.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2354;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>38</mn><mn>8</mn></mfrac></math></p>\\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>7</mn></mfrac></math> </span></p>\\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>9</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>9</mn></mfrac></math></p>\\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>38</mn><mn>8</mn></mfrac></math></p>\\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>7</mn></mfrac></math> </span></p>\\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>9</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>9</mn></mfrac></math></p>\\n"],
                    solution_en: "<p>25.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required</span><span style=\"font-family: Cambria Math;\"> time = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mrow><mn>10</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>8</mn></mrow></mfrac><mo>=</mo><mfrac><mn>40</mn><mn>9</mn></mfrac></math> </span></p>\\n",
                    solution_hi: "<p>25.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mrow><mn>10</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>8</mn></mrow></mfrac><mo>=</mo><mfrac><mn>40</mn><mn>9</mn></mfrac></math> </span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>