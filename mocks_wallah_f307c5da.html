<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the set in which the numbers are related in the same way as are the numbers of the given sets.<br>(3, 9, 27)<br>(4, 16, 64)<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>1. उस समुच्चय का चयन कीजिए जिसमें संख्याएं उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुच्चय की संख्याएं संबंधित हैं।<br>(3, 9, 27)<br>(4, 16, 64)<br>(नोट: संख्याओं को इसके घटक अंकों में तोड़े बिना, संपूर्ण संख्&zwj;याओं पर गणितीय संक्रियाएँ की जानी चाहिए। उदाहरण के लिए, 13 &ndash; 13 पर गणितीय संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(6, 36, 84)</p>", "<p>(9, 81, 731)</p>", 
                                "<p>(5, 25, 120)</p>", "<p>(7, 49, 343)</p>"],
                    options_hi: ["<p>(6, 36, 84)</p>", "<p>(9, 81, 731)</p>",
                                "<p>(5, 25, 120)</p>", "<p>(7, 49, 343)</p>"],
                    solution_en: "<p>1.(d)<br><strong>Logic:-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mi>st</mi></msup><mi>no</mi><mo>.</mo></math> &times; 2<sup>nd</sup>no. = 3<sup>rd</sup> no.<br>(3, 9, 27) :- 3 <math display=\"inline\"><mo>&#215;</mo></math> 9 = 27<br>(4, 16, 64) :- 4 <math display=\"inline\"><mo>&#215;</mo></math> 16 = 64<br>Similarly, <br>(7, 49, 343) :- 7 <math display=\"inline\"><mo>&#215;</mo></math> 49 = 343</p>",
                    solution_hi: "<p>1.(d)<br><strong>तर्क:-</strong> पहली संख्या <math display=\"inline\"><mo>&#215;</mo></math> दूसरी संख्या = तीसरी संख्या <br>(3, 9, 27) :- 3 <math display=\"inline\"><mo>&#215;</mo></math> 9 = 27<br>(4, 16, 64) :- 4 <math display=\"inline\"><mo>&#215;</mo></math> 16 = 64<br>इसी प्रकार,, <br>(7, 49, 343) :- 7 <math display=\"inline\"><mo>&#215;</mo></math> 49 = 343</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>6408 &times; 8 + 27 &ndash; 15 &divide; 10 = ?</p>",
                    question_hi: "<p>2. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>6408 &times; 8 + 27 &ndash; 15 &divide; 10 = ?</p>",
                    options_en: ["<p>923</p>", "<p>924</p>", 
                                "<p>921</p>", "<p>922</p>"],
                    options_hi: ["<p>923</p>", "<p>924</p>",
                                "<p>921</p>", "<p>922</p>"],
                    solution_en: "<p>2.(b)<br><strong>Given</strong> :- 6408 &times;&nbsp;8 + 27 - 15 &divide; 10 = ?<br>As per given instruction after interchanging &lsquo;+ and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get<br>6408 &divide;&nbsp;8 - 27 + 15 &times; 10<br>801 -&nbsp;27 + 150 = 924</p>",
                    solution_hi: "<p>2.(b)<br><strong>Given</strong> :- 6408 &times;&nbsp;8 + 27 - 15 &divide; 10 = ?<br>दिए गए निर्देश के अनुसार \'+ और \'-\' और \'&times;\' और \'&divide;\' को आपस में बदलने के बाद हमें प्राप्त होता है<br>6408 &divide;&nbsp;8 - 27 + 15 &times; 10<br>801 -&nbsp;27 + 150 = 924</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Six letters V, C, R, E, X and Z are written on different faces of a dice. Two positions of this dice are shown in the figures below. Find the letter on the face opposite to C.<br><strong id=\"docs-internal-guid-1932dcd9-7fff-cb0b-acc2-db04116ced8e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXez1VqA9IC7qy93GFOhE75LDmALBdoUOsvKDro0ZBigus8rAGi9lpASEpm_KaFWQaj6aJvX3fGgJDGDf2v7yJeUFSuDslfG9JYFYxR9EiUFOSivq-47gManJNG08V1zWru7hQ4lWv7hdrKMqvqh9JGeweY?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"168\" height=\"82\"></strong></p>",
                    question_hi: "<p>3. एक पासे के विभिन्न फलकों पर छह अक्षर V, C, R, E, X और Z लिखे गए हैं। इस पासे की दो स्थितियाँ नीचे चित्र में दर्शाई गई हैं। C के विपरीत वाले फलक पर आने वाला अक्षर ज्ञात कीजिए।<br><strong id=\"docs-internal-guid-1932dcd9-7fff-cb0b-acc2-db04116ced8e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXez1VqA9IC7qy93GFOhE75LDmALBdoUOsvKDro0ZBigus8rAGi9lpASEpm_KaFWQaj6aJvX3fGgJDGDf2v7yJeUFSuDslfG9JYFYxR9EiUFOSivq-47gManJNG08V1zWru7hQ4lWv7hdrKMqvqh9JGeweY?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"168\" height=\"82\"></strong></p>",
                    options_en: ["<p>X</p>", "<p>R</p>", 
                                "<p>V</p>", "<p>Z</p>"],
                    options_hi: ["<p>X</p>", "<p>R</p>",
                                "<p>V</p>", "<p>Z</p>"],
                    solution_en: "<p>3.(a) From both the dice the opposite face are <br>R &harr; Z , C &harr; X, V &harr; E</p>",
                    solution_hi: "<p>3.(a) दोनों पासों के विपरीत फलक हैं<br>R &harr; Z , C &harr; X, V &harr; E</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Three Statements are given followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>All peas are vegetables.<br>All carrots are vegetables.<br>All vegetables are greens.<br><strong>Conclusions:</strong><br>I. All peas are greens.<br>II. All greens are carrots.<br>III. Some greens are peas.</p>",
                    question_hi: "<p>4. तीन कथन और उसके बाद तीन निष्कर्ष क्रमांकित I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, हों निर्णय लें कि कौन सा/से निष्कर्ष तार्किक रूप से कथनों का अनुसरण करता है/करते हैं।<br><strong>कथन:</strong><br>सभी मटर, सब्जियां हैं।<br>सभी गाजर, सब्जियां हैं।<br>सभी सब्जियां, हरी/हरे हैं।<br><strong>निष्कर्ष:</strong><br>I. सभी मटर, हरी/हरे हैं।<br>II. सभी हरी/हरे, गाजर हैं।<br>III. कुछ हरी/हरे, मटर हैं।</p>",
                    options_en: ["<p>Both conclusions I and III follow.</p>", "<p>All conclusions follow.</p>", 
                                "<p>Both conclusions II and III follow.</p>", "<p>Both conclusions I and II follow.</p>"],
                    options_hi: ["<p>दोनों निष्कर्ष I और III अनुसरण करते हैं।</p>", "<p>सभी निष्कर्ष अनुसरण करते हैं।</p>",
                                "<p>दोनों निष्कर्ष II और III अनुसरण करते हैं।</p>", "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं।</p>"],
                    solution_en: "<p>4.(a)<br><strong id=\"docs-internal-guid-0c8f49cc-7fff-0cc1-45de-8c304624a87e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeagqUkM4txw6dnpzDENfSDISkkmosVpdXE-O_V8OwIRKMuaAn8_7OiOc1pWaynuU0C6PmfTbdaz8Zu73EKesc6hiINlmfl5YsmdsC-q7bmyfSivglKHIIiTaLV9CjCIxL_vOSory9rDsKyNN2_4zpKr9zq?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"142\" height=\"126\"></strong><br>Hence, both conclusion I and III follows.</p>",
                    solution_hi: "<p>4.(a)<br><strong id=\"docs-internal-guid-de33362b-7fff-20b7-e6c6-70da3db8e291\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfEZ14PQoFLAURz1sg3Pqq6xoPReP7B7y7IltOMqwrQudcH0r_YVTHMdG2MAR9ZPLNWu4JjNP7BST2HsWqlATkzyqQvN1YPUCKREP1AINKXQO9fOW78jUapHw1OW0aqmrJ2V7aPXs1VBOGt6xUP7Nym4kox?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"146\" height=\"130\"></strong><br>अतः, निष्कर्ष I और III दोनों अनुसरण करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In a certain code language, &lsquo;BAKE&rsquo; is coded as &lsquo;FDMF&rsquo; and &lsquo;TURN&rsquo; is coded as &lsquo;XXTO&rsquo;. How will &lsquo;MOCK&rsquo; be coded in the given language?</p>",
                    question_hi: "<p>5. एक निश्चित कूट भाषा में, &lsquo;BAKE&rsquo; को &lsquo;FDMF&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;TURN&rsquo; को &lsquo;XXTO&rsquo; के रूप में कूटबद्ध किया जाता है। दी गई भाषा में &lsquo;MOCK&rsquo; को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>RRFL</p>", "<p>QREL</p>", 
                                "<p>RQGL</p>", "<p>QSEL</p>"],
                    options_hi: ["<p>RRFL</p>", "<p>QREL</p>",
                                "<p>RQGL</p>", "<p>QSEL</p>"],
                    solution_en: "<p>5.(b)<br><strong id=\"docs-internal-guid-0330592f-7fff-bb50-5a23-2cd61da41d5c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXekOx5qndDtFtT66CJTZ3jdyd3XBj2fL8y4jDdFDo7rYXctR-SNV9L5AAP9BDblLABrWBXRVYZQvqTBHjmCUuJAeRkSckwxjDjML011wZdWfhsB3IoCf4S1W3jnuLPG69CLmqezrMteT-Lya5N4lyh3rgoy?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"120\" height=\"89\"></strong><br>and<br><strong id=\"docs-internal-guid-054e2bc8-7fff-d9f5-0f8b-efab1e98ce49\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc_011Wcwlu4UKY2t3lpHlhqqA_64HV7qlI82bXGfJk1w00gICCZtTMFSTPIZDTC1HFo4efSEStWzCwKji9_7nYlxg5xZa6yvs77CZRhMMDfWEyYWDZasgCqq_37hmAdQJH6kPatyG3T-EAfEWThT9mvms?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"119\" height=\"88\"></strong></p>\n<p>Similarly<br><strong id=\"docs-internal-guid-2f015877-7fff-0e7e-1ba1-73d65d375b67\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd8gVzIfTp4HAER2HdreuCVXFuweyBofQbyfENmY0vmfRWeaIAeosZxP6hthKMR-myTDikB9PQeFi09kBR6eMvxCGAE6GawEV7t4zWZPGsUTwFcmCGxozKRtAs1gCgiGW-_D3XKIk6gSJJcUWR35lS64z3e?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"119\" height=\"88\"></strong></p>",
                    solution_hi: "<p>5.(b)<br><strong id=\"docs-internal-guid-0330592f-7fff-bb50-5a23-2cd61da41d5c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXekOx5qndDtFtT66CJTZ3jdyd3XBj2fL8y4jDdFDo7rYXctR-SNV9L5AAP9BDblLABrWBXRVYZQvqTBHjmCUuJAeRkSckwxjDjML011wZdWfhsB3IoCf4S1W3jnuLPG69CLmqezrMteT-Lya5N4lyh3rgoy?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"120\" height=\"89\"></strong><br>और<br><strong id=\"docs-internal-guid-054e2bc8-7fff-d9f5-0f8b-efab1e98ce49\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc_011Wcwlu4UKY2t3lpHlhqqA_64HV7qlI82bXGfJk1w00gICCZtTMFSTPIZDTC1HFo4efSEStWzCwKji9_7nYlxg5xZa6yvs77CZRhMMDfWEyYWDZasgCqq_37hmAdQJH6kPatyG3T-EAfEWThT9mvms?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"119\" height=\"88\"></strong></p>\n<p>इसी प्रकार<br><strong id=\"docs-internal-guid-2f015877-7fff-0e7e-1ba1-73d65d375b67\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd8gVzIfTp4HAER2HdreuCVXFuweyBofQbyfENmY0vmfRWeaIAeosZxP6hthKMR-myTDikB9PQeFi09kBR6eMvxCGAE6GawEV7t4zWZPGsUTwFcmCGxozKRtAs1gCgiGW-_D3XKIk6gSJJcUWR35lS64z3e?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"119\" height=\"88\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the set in which the numbers are related in the same way as are the numbers of the following set.<br>(84, 63, 42)<br>(79, 47, 64)<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>6. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित समुच्चयों की संख्याएँ संबंधित हैं।<br>(84, 63, 42)<br>(79, 47, 64)<br>(नोट: पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(75, 33, 48)</p>", "<p>(64, 51, 52)</p>", 
                                "<p>(67, 48, 57)</p>", "<p>(71, 53, 36)</p>"],
                    options_hi: ["<p>(75, 33, 48)</p>", "<p>(64, 51, 52)</p>",
                                "<p>(67, 48, 57)</p>", "<p>(71, 53, 36)</p>"],
                    solution_en: "<p>6.(d)<br><strong>Logic:-</strong> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mi>st</mi></msup><mi>no</mi><mo>.</mo><mo>-</mo></math> 2<sup>nd</sup>no.) &times; 2 = 3<sup>rd</sup> no.<br>(84, 63, 42) :-&nbsp;(84 - 63) &times; 2 &rArr; 21 &times; 2 = 42<br>(79, 47, 64) :-&nbsp;(79 - 47) &times; 2 &rArr; 32 &times; 2 = 64<br>Similarly, <br>(71, 53, 36) :-&nbsp;(71 -53) &times; 2 &rArr; 18 &times; 2 = 36</p>",
                    solution_hi: "<p>6.(d)<br><strong>तर्क</strong> :<math display=\"inline\"><mo>-</mo></math> (पहली संख्या - दूसरी संख्या) &times; 2 = तीसरी संख्या&nbsp;<br>(84, 63, 42) :-&nbsp;(84 -63) &times; 2 &rArr; 21 &times; 2 = 42<br>(79, 47, 64) :-&nbsp;(79 - 47) &times; 2 &rArr; 32 &times; 2 = 64<br>इसी प्रकार , <br>(71, 53, 36) :-&nbsp;(71 -53) &times; 2 &rArr; 18 &times; 2 = 36</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the term from among the given options that can replace the question mark (?) in the following series.<br>MY 84, KW 80, IU 76, GS 72, ?</p>",
                    question_hi: "<p>7. दिए गए विकल्पों में से उस पद का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकता है।<br>MY 84, KW 80, IU 76, GS 72, ?</p>",
                    options_en: ["<p>ER 70</p>", "<p>EQ 68</p>", 
                                "<p>FQ 71</p>", "<p>FP 69</p>"],
                    options_hi: ["<p>ER 70</p>", "<p>EQ 68</p>",
                                "<p>FQ 71</p>", "<p>FP 69</p>"],
                    solution_en: "<p>7.(b)<br><strong id=\"docs-internal-guid-e1e30f46-7fff-1ffe-772f-66455421b578\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeD9VYRzMiAIYz7M0qhrKyW0MCZmFiXlcMwXvVRGcKChPFKHng3xLE6W6JVI0OsKHT7GJr9ASzpb-DDRkAq6GVw-hUmZe5DU3bR7HR5ZfTQxvQgVggR1mRc8cLI-ygQqK-J3ft5X7qh-AVFxl3g7O4vmL8W?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"351\" height=\"90\"></strong></p>",
                    solution_hi: "<p>7.(b)<br><strong id=\"docs-internal-guid-e1e30f46-7fff-1ffe-772f-66455421b578\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeD9VYRzMiAIYz7M0qhrKyW0MCZmFiXlcMwXvVRGcKChPFKHng3xLE6W6JVI0OsKHT7GJr9ASzpb-DDRkAq6GVw-hUmZe5DU3bR7HR5ZfTQxvQgVggR1mRc8cLI-ygQqK-J3ft5X7qh-AVFxl3g7O4vmL8W?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"351\" height=\"90\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. How many circles are there in the given figure?<br><strong id=\"docs-internal-guid-db83a99b-7fff-73a1-bec2-e17cbd14b1b2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdHwxwVxgFK6HBusd86AXwTBLYYW10y_V5GRSwRQx-kVLG_JkbaBciNlpqLHf3sTRSB79LVBPIHplKHGBjQ422UxP1TMXuKb9mvCkoyURVDoWMToxrGClC6k5JJv1ZjKAiiQJuuwEJfk0M_4QrZeD5FmzQ-?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"110\" height=\"106\"></strong></p>",
                    question_hi: "<p>8. दी गई आकृति में कितने वृत्त हैं?<br><strong id=\"docs-internal-guid-db83a99b-7fff-73a1-bec2-e17cbd14b1b2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdHwxwVxgFK6HBusd86AXwTBLYYW10y_V5GRSwRQx-kVLG_JkbaBciNlpqLHf3sTRSB79LVBPIHplKHGBjQ422UxP1TMXuKb9mvCkoyURVDoWMToxrGClC6k5JJv1ZjKAiiQJuuwEJfk0M_4QrZeD5FmzQ-?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"110\" height=\"106\"></strong></p>",
                    options_en: ["<p>11</p>", "<p>10</p>", 
                                "<p>13</p>", "<p>12</p>"],
                    options_hi: ["<p>11</p>", "<p>10</p>",
                                "<p>13</p>", "<p>12</p>"],
                    solution_en: "<p>8.(a)<br>There are 11 circles.</p>",
                    solution_hi: "<p>8.(a)<br>11 वृत्त हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Identify the option figure that when put in place of the question mark (?) will logically complete the series.<br><strong id=\"docs-internal-guid-c7986529-7fff-f5bc-340a-a6516bc7df9f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfAunOJ0jKlXDZZZYw62MGKNtRE7NURae8xqYsamZflsc0vFnachhBddSD_wDuvcaA1Og36OYjAmO6udkDOJZVr6LNX5Bheuw05Fv4haSQH2F1LgkY8pn4BEYaBwbrwW4yErGRtdD4hDoY7fF1BJX1OwOk4?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"375\" height=\"74\"></strong></p>",
                    question_hi: "<p>9. उस विकल्प आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br><strong id=\"docs-internal-guid-ab076c63-7fff-6881-bc55-70823ff94202\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfAunOJ0jKlXDZZZYw62MGKNtRE7NURae8xqYsamZflsc0vFnachhBddSD_wDuvcaA1Og36OYjAmO6udkDOJZVr6LNX5Bheuw05Fv4haSQH2F1LgkY8pn4BEYaBwbrwW4yErGRtdD4hDoY7fF1BJX1OwOk4?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"375\" height=\"74\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-8bb8e0ed-7fff-0fd6-791d-41e40f977e0f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQkrY7knrjm9ToA2e4QKyM6hXW62ywkWWV9EZp7zRcbBoeQ8XqNlIVmbOdpXjr-N8qDAxBJFqV4qUr0LHaRk8JSPYGk5C4EbtnIpVBeIy0zrLNQVq7BI3rJJvd48y8tH16gFWd7NKkwZ6dXkuNpz4JA10?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-ec7508d1-7fff-24bd-d82b-7159874f0b3a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcen2M4fN19PKdO3t7J6o-eLNZ_KPnqoiasQOwvg9sBmON5St08c63umrdVBn4ZTNGv5OEYrhCf-aI7PjxtbTpcTpEjgIwDlT3HtWJ0QLLs3PwNVxvqBZUhovU5CW5tKfmwk7v77wJrLPSrjB98aT2KrbLM?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-fa3ed9b5-7fff-397c-7b66-6771ee595139\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfQqcEdjZaNcXZzgjo_RFuQznLhG1OfmBsDnmnPKgXkQ6uUXUeAngTZnaSWRPZOdj_DfuWnUoy1YwBe9lptAJsRl_20HP8A4qkV97EMJkBiCCj6mviXqbOZ_N2_z_edAB3oVf5cjBhLOkS0UZ2PqDp-gk0O?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-0a24738d-7fff-4dc0-e118-6b43eb461458\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfuSTZ8-xzIBu9Girj1ovaOYyxsTqxg7kHq2vSoFytdxqvTGM2dICLUEjm-GfarPXwlNMXH2YcBIr-z6XBGDjvUz7LyHRDSLNrqq4mraI411NS2l_AUMrChNheZRuq9FhCR-hZobfO4bwq0pY3yGbfNPlrj?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-1a04bef3-7fff-7dfd-c6f0-5b77716911ae\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQkrY7knrjm9ToA2e4QKyM6hXW62ywkWWV9EZp7zRcbBoeQ8XqNlIVmbOdpXjr-N8qDAxBJFqV4qUr0LHaRk8JSPYGk5C4EbtnIpVBeIy0zrLNQVq7BI3rJJvd48y8tH16gFWd7NKkwZ6dXkuNpz4JA10?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-7789ba47-7fff-ae74-e043-5b3cb45a49d0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcen2M4fN19PKdO3t7J6o-eLNZ_KPnqoiasQOwvg9sBmON5St08c63umrdVBn4ZTNGv5OEYrhCf-aI7PjxtbTpcTpEjgIwDlT3HtWJ0QLLs3PwNVxvqBZUhovU5CW5tKfmwk7v77wJrLPSrjB98aT2KrbLM?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-05d329fe-7fff-e103-5bff-cb1b3f5e6362\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfQqcEdjZaNcXZzgjo_RFuQznLhG1OfmBsDnmnPKgXkQ6uUXUeAngTZnaSWRPZOdj_DfuWnUoy1YwBe9lptAJsRl_20HP8A4qkV97EMJkBiCCj6mviXqbOZ_N2_z_edAB3oVf5cjBhLOkS0UZ2PqDp-gk0O?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-7bd07837-7fff-0581-8938-d04f6026a6f1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfuSTZ8-xzIBu9Girj1ovaOYyxsTqxg7kHq2vSoFytdxqvTGM2dICLUEjm-GfarPXwlNMXH2YcBIr-z6XBGDjvUz7LyHRDSLNrqq4mraI411NS2l_AUMrChNheZRuq9FhCR-hZobfO4bwq0pY3yGbfNPlrj?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>"],
                    solution_en: "<p>9.(c)<br><strong id=\"docs-internal-guid-4890ff52-7fff-4c20-d1d8-2d509f888465\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfQqcEdjZaNcXZzgjo_RFuQznLhG1OfmBsDnmnPKgXkQ6uUXUeAngTZnaSWRPZOdj_DfuWnUoy1YwBe9lptAJsRl_20HP8A4qkV97EMJkBiCCj6mviXqbOZ_N2_z_edAB3oVf5cjBhLOkS0UZ2PqDp-gk0O?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>",
                    solution_hi: "<p>9.(c)<br><strong id=\"docs-internal-guid-4890ff52-7fff-4c20-d1d8-2d509f888465\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfQqcEdjZaNcXZzgjo_RFuQznLhG1OfmBsDnmnPKgXkQ6uUXUeAngTZnaSWRPZOdj_DfuWnUoy1YwBe9lptAJsRl_20HP8A4qkV97EMJkBiCCj6mviXqbOZ_N2_z_edAB3oVf5cjBhLOkS0UZ2PqDp-gk0O?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br>(The words must be considered as meaningful English words and must NOT be related to each other based on the number of letters/number of consonants/vowels in the word)<br>Frequency : Hertz</p>",
                    question_hi: "<p>10. उस शब्द-युग्म का चयन करें, जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए संबंध के समान संबंधों का सबसे बेहतर ढंग से प्रतिनिधित्व करता है।<br>(शब्दों को अर्थपूर्ण हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होना चाहिए)<br>Frequency : Hertz (आवृत्ति : हर्ट्ज)</p>",
                    options_en: ["<p>Power : Newton</p>", "<p>Resistance : Candela</p>", 
                                "<p>Force : Volt</p>", "<p>Illuminance : Lux</p>"],
                    options_hi: ["<p>Power : Newton (शक्ति : न्यूटन)</p>", "<p>Resistance : Candela (प्रतिरोध : कैंडेला)</p>",
                                "<p>Force : Volt (बल : वोल्ट)</p>", "<p>Illuminance : Lux (प्रदीप्ति घनत्व : लक्स)</p>"],
                    solution_en: "<p>10.(d)<br>The SI unit of &lsquo;Frequency&rsquo; is &lsquo;Hertz&rsquo; similarly the SI unit of &lsquo;Illuminance&rsquo; is &lsquo;Lux.&rsquo;</p>",
                    solution_hi: "<p>10.(d)<br>\'आवृत्ति\' की SI इकाई \'हर्ट्ज़\' है, उसी प्रकार \'प्रदीप्ति घनत्व\' की SI इकाई \'लक्स\' है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different. The odd one out is not based on the number of consonants/vowels or their position in the letter cluster</p>",
                    question_hi: "<p>11. चार अक्षर समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक समान हैं। उस असमान विकल्प को चुनिए। अक्षर समूह में, असमान विकल्प व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: ["<p>RTTUWW</p>", "<p>NOPPSS</p>", 
                                "<p>ACCDFF</p>", "<p>IKKLNN</p>"],
                    options_hi: ["<p>RTTUWW</p>", "<p>NOPPSS</p>",
                                "<p>ACCDFF</p>", "<p>IKKLNN</p>"],
                    solution_en: "<p>11.(b)<br>By observing four options we can see that in each option except option (b) the 2nd letter is repeated two times.<br>Hence, option (b) is an odd one out.</p>",
                    solution_hi: "<p>11.(b)<br>चार विकल्पों को देखकर हम देख सकते हैं कि विकल्प (b) को छोड़कर प्रत्येक विकल्प में दूसरा अक्षर दो बार दोहराया गया है।<br>इसलिए, विकल्प (b) असमान है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the option figure that will replace the question mark (?) in the figure given below to complete the pattern.<br><strong id=\"docs-internal-guid-c64689ef-7fff-b6e9-1784-f381523641a2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcRUpNHC5OiJZY2aS4jFrexJn1QbNcwl3IY9M4Wj3Zw8ot4bd0LQ6K_znKFY8zmW1wiW4OP_4US4EfUIW74iw7fPCoMWKSH5ifsQPHHE_kXu3fj2UVy6K1xUdsBeOX3LgZts3U8SjMohLHy5uI1En20gu5K?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"101\" height=\"130\"></strong></p>",
                    question_hi: "<p>12. उस विकल्प आकृति का चयन कीजिए, जो पैटर्न को पूरा करने के लिए नीचे दी गई आकृति में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी।<br><strong id=\"docs-internal-guid-40dd9d67-7fff-4214-be1c-c0dd8cfff0a7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcRUpNHC5OiJZY2aS4jFrexJn1QbNcwl3IY9M4Wj3Zw8ot4bd0LQ6K_znKFY8zmW1wiW4OP_4US4EfUIW74iw7fPCoMWKSH5ifsQPHHE_kXu3fj2UVy6K1xUdsBeOX3LgZts3U8SjMohLHy5uI1En20gu5K?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"101\" height=\"130\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-3538ffc7-7fff-d533-c41c-d11f0a70d75f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeaqYlDcz6w7IzN6pQSVdkgLpAE1cM1w6qzhRxXe2vlSopnYZdOqjcdwTirYtLVn8J0rBbsXxiNNlzUHv3Gp37OJHTv8qM9e0W7kErUtHgVGAbLWR0KrhYpc7QnGfwuGpIo3n7DNrY5TLZfzpUTsU0HHIg?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"54\" height=\"133\"></strong></p>", "<p><strong id=\"docs-internal-guid-da7021c8-7fff-ef43-fcd1-13527c369b76\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcFRAt0D24L3DrsBPy85SPF6faqo7Cu_QtH2OVnHeXrIjoftMqAjcj1Htfpmkr7_Ag-ibv7QMjA3S0f9i0ulMw-r7x74kFUWH_mrcABu-esKMFa2VPQ76WSMdCVSw5UNxHWF-padk9V0dzZwDzh5o-8MJ0?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"54\" height=\"132\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-64aa6f78-7fff-0742-57bc-602abf17f516\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcmrsXTqw96FnJ7UD2d1wFbEduCGDAFcNgQUooMhTGwZeaaqzpN1loMOTzC5I1nDTT73VdySnmXhz2DdNvsFJDmC1c6E0OfCqQ_KVRAoEk_UfNou1KYtFeYJBkWIx4-dvqDmZgQQSxJRdg3F0RzMOcbomI?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"54\" height=\"132\"></strong></p>", "<p><strong id=\"docs-internal-guid-6bab835d-7fff-2603-db60-8a71820d42b3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0ybFj_2qArv9xfEesiWdrzriEH82jFw-hYOhzMssRGSad6XVq69bbWS7sDXe5AM7PEmxRJWzkVNLhO7o_bCewn3UD1kOmGNA7OPRNd3ifz8SaHMOfCfG5BYv98ApzNk9H2OagMuKSFnPxAMdFLbULkMTP?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"54\" height=\"132\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-9f83d8e8-7fff-5d1a-27ba-21ca2973f2cd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeaqYlDcz6w7IzN6pQSVdkgLpAE1cM1w6qzhRxXe2vlSopnYZdOqjcdwTirYtLVn8J0rBbsXxiNNlzUHv3Gp37OJHTv8qM9e0W7kErUtHgVGAbLWR0KrhYpc7QnGfwuGpIo3n7DNrY5TLZfzpUTsU0HHIg?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"54\" height=\"133\"></strong></p>", "<p><strong id=\"docs-internal-guid-c4d9f2c0-7fff-aef6-1fd8-3e2ab2c0aada\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcFRAt0D24L3DrsBPy85SPF6faqo7Cu_QtH2OVnHeXrIjoftMqAjcj1Htfpmkr7_Ag-ibv7QMjA3S0f9i0ulMw-r7x74kFUWH_mrcABu-esKMFa2VPQ76WSMdCVSw5UNxHWF-padk9V0dzZwDzh5o-8MJ0?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"54\" height=\"132\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-ae7009ed-7fff-c5a5-7587-f847347dd0fc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcmrsXTqw96FnJ7UD2d1wFbEduCGDAFcNgQUooMhTGwZeaaqzpN1loMOTzC5I1nDTT73VdySnmXhz2DdNvsFJDmC1c6E0OfCqQ_KVRAoEk_UfNou1KYtFeYJBkWIx4-dvqDmZgQQSxJRdg3F0RzMOcbomI?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"54\" height=\"132\"></strong></p>", "<p><strong id=\"docs-internal-guid-55d9cbc8-7fff-e62e-57de-91c555ebe69c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0ybFj_2qArv9xfEesiWdrzriEH82jFw-hYOhzMssRGSad6XVq69bbWS7sDXe5AM7PEmxRJWzkVNLhO7o_bCewn3UD1kOmGNA7OPRNd3ifz8SaHMOfCfG5BYv98ApzNk9H2OagMuKSFnPxAMdFLbULkMTP?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"54\" height=\"132\"></strong></p>"],
                    solution_en: "<p>12.(b)<br><strong id=\"docs-internal-guid-0311f272-7fff-08dd-a1ee-d98af39e020c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcFRAt0D24L3DrsBPy85SPF6faqo7Cu_QtH2OVnHeXrIjoftMqAjcj1Htfpmkr7_Ag-ibv7QMjA3S0f9i0ulMw-r7x74kFUWH_mrcABu-esKMFa2VPQ76WSMdCVSw5UNxHWF-padk9V0dzZwDzh5o-8MJ0?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"54\" height=\"132\"></strong></p>",
                    solution_hi: "<p>12.(b)<br><strong id=\"docs-internal-guid-0311f272-7fff-08dd-a1ee-d98af39e020c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcFRAt0D24L3DrsBPy85SPF6faqo7Cu_QtH2OVnHeXrIjoftMqAjcj1Htfpmkr7_Ag-ibv7QMjA3S0f9i0ulMw-r7x74kFUWH_mrcABu-esKMFa2VPQ76WSMdCVSw5UNxHWF-padk9V0dzZwDzh5o-8MJ0?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"54\" height=\"132\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><strong id=\"docs-internal-guid-05e7bf14-7fff-a619-9935-c3a903067d61\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXftWNuyXrECTX3MLhVZxnZbn_d8zj0iQt1d8IUSzTSAIkEYoQZaf9mxPuuWNLzlXzoGlW2TeQZtziyArPreY-v2mdx2a0qb19hzaZd8hpo60VEwh57RTX68aSAMsJ5qU6AaifyIVs9ygtbLzRtjz8Idpw4q?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"114\" height=\"106\"></strong></p>",
                    question_hi: "<p>13. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><strong id=\"docs-internal-guid-18c96901-7fff-349f-d9c3-e27964a83bf3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXftWNuyXrECTX3MLhVZxnZbn_d8zj0iQt1d8IUSzTSAIkEYoQZaf9mxPuuWNLzlXzoGlW2TeQZtziyArPreY-v2mdx2a0qb19hzaZd8hpo60VEwh57RTX68aSAMsJ5qU6AaifyIVs9ygtbLzRtjz8Idpw4q?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"114\" height=\"106\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-a06f2a95-7fff-c25d-03f2-de8e5592540b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeDyPYTp9yH2TCg2A2o43up7KLYdz2veP2h_98JGe7hDyOFjslmQoaytkPAbkkzREAdNzL1CsZakXoZT0JrcgybR4dS5h1Nn89KFI_nqqNg1yvyVinNi89SPFEEsLwF29xnG9kFZNRWCzqXmBAlLoGCxdMx?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"108\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-955c0281-7fff-db2a-9641-24780a6a8610\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe_szjHxDdtiV3EylNsx9xieDJIk3zUuZXRqXlXJ5YdA7mYMHHgz8qc7HJ4UW3aSaciVnnFRSrgr8pj2yViJ8ONCotUVW6mWOik-xexF6CbYF2WmtJjHNHMz_1gqdYJweW_QlmpzOLlvSG5UJA4IuaS_Vku?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"108\" height=\"20\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-5bbe5b0d-7fff-f219-8557-ec889286c096\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcshAFpkFRZZJTeilwn5J0CbRYemzcGcRm_dkgVa79UhtZ3mRrk14v9vxZ5P9EK1b98MhqSwgoGm28ZajZ5jxDaHGbKes-EK0mAEc50M45Pxg54_LNO2XLsX6MrGVcpNy95QvKCJ_5e7yaEK6OjFD54syt4?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"108\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-9dec19a6-7fff-0c3b-c492-031377e34e46\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeOexTHA0sFRdnIbxjFF0vrEC5h_d232PYuIBmhaN8qpvEIhUKFHzURz7Nh4UNWrLOjgCpqRg2YHIkIRGCZmJ3a8EO3G-gbh1ml35FmgeS1W_WhShYSN253kTwzRp7YQRf33LujmpEJjUq-xfwuwswOcPQZ?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"109\" height=\"20\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-8ef63e4a-7fff-4b18-22de-5ac7d0897684\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeDyPYTp9yH2TCg2A2o43up7KLYdz2veP2h_98JGe7hDyOFjslmQoaytkPAbkkzREAdNzL1CsZakXoZT0JrcgybR4dS5h1Nn89KFI_nqqNg1yvyVinNi89SPFEEsLwF29xnG9kFZNRWCzqXmBAlLoGCxdMx?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"108\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-7c74591c-7fff-63d0-44c6-fb8526422968\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe_szjHxDdtiV3EylNsx9xieDJIk3zUuZXRqXlXJ5YdA7mYMHHgz8qc7HJ4UW3aSaciVnnFRSrgr8pj2yViJ8ONCotUVW6mWOik-xexF6CbYF2WmtJjHNHMz_1gqdYJweW_QlmpzOLlvSG5UJA4IuaS_Vku?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"108\" height=\"20\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-1c063524-7fff-79bf-9a12-72b5680281f8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcshAFpkFRZZJTeilwn5J0CbRYemzcGcRm_dkgVa79UhtZ3mRrk14v9vxZ5P9EK1b98MhqSwgoGm28ZajZ5jxDaHGbKes-EK0mAEc50M45Pxg54_LNO2XLsX6MrGVcpNy95QvKCJ_5e7yaEK6OjFD54syt4?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"108\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-e314f789-7fff-8f53-3ee4-4f700dfadc7d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeOexTHA0sFRdnIbxjFF0vrEC5h_d232PYuIBmhaN8qpvEIhUKFHzURz7Nh4UNWrLOjgCpqRg2YHIkIRGCZmJ3a8EO3G-gbh1ml35FmgeS1W_WhShYSN253kTwzRp7YQRf33LujmpEJjUq-xfwuwswOcPQZ?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"109\" height=\"20\"></strong></p>"],
                    solution_en: "<p>13.(a)<br><strong id=\"docs-internal-guid-c3b7c271-7fff-ac6c-8d87-87b4a458ded1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeDyPYTp9yH2TCg2A2o43up7KLYdz2veP2h_98JGe7hDyOFjslmQoaytkPAbkkzREAdNzL1CsZakXoZT0JrcgybR4dS5h1Nn89KFI_nqqNg1yvyVinNi89SPFEEsLwF29xnG9kFZNRWCzqXmBAlLoGCxdMx?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"108\" height=\"20\"></strong></p>",
                    solution_hi: "<p>13.(a)<br><strong id=\"docs-internal-guid-c3b7c271-7fff-ac6c-8d87-87b4a458ded1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeDyPYTp9yH2TCg2A2o43up7KLYdz2veP2h_98JGe7hDyOFjslmQoaytkPAbkkzREAdNzL1CsZakXoZT0JrcgybR4dS5h1Nn89KFI_nqqNg1yvyVinNi89SPFEEsLwF29xnG9kFZNRWCzqXmBAlLoGCxdMx?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"108\" height=\"20\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><strong id=\"docs-internal-guid-6667615c-7fff-ff9f-f5bd-3a650515e418\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeESxj0ZXttJl9NPrmYhdbuhvUhSM7aHOYNSGZmoF28M9vLbSSpk-61vAMdDD7v-4wG6lf-kJHgl5_w-D_kwSPWCF5tv4pTU44MgNzYU0vDrMpnQ7n0nEoPI02dloZjMAGA5ADu9sFIC8yOKYVMjoX-RRPo?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"327\" height=\"67\"></strong></p>",
                    question_hi: "<p>14. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><strong id=\"docs-internal-guid-b51a48a6-7fff-a4d7-5fbe-e630620cec41\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeESxj0ZXttJl9NPrmYhdbuhvUhSM7aHOYNSGZmoF28M9vLbSSpk-61vAMdDD7v-4wG6lf-kJHgl5_w-D_kwSPWCF5tv4pTU44MgNzYU0vDrMpnQ7n0nEoPI02dloZjMAGA5ADu9sFIC8yOKYVMjoX-RRPo?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"327\" height=\"67\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-f46b7632-7fff-16f3-c9a6-2d20a2349698\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfQ15fOTbVr5z2eqfqUOWrMNDrH4yiYQged-uNOEMPoFUfJa9AaRnvlHV8cCBbi6gqGyewF8VbFO9lX3aLm6YBdN2ULYaXkCpGm0uxkrovfaCZPhvMFEgtVIQeA14x69hrWurzFkDQATcYuUJ2WJwX5gcEW?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-009b49d9-7fff-3470-5af2-268257adf522\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeww4DOFe56rwiUVU295SD-__UgOLZVEKv9WiMU78YNUghEmgJq5mdGLT5Mvh1M0yl8Okn7JqAbTepl9-TwHD9mjPGheVnQGhr1nscOS_0GiB9nHGlasHHpXdvHg7SYzrTo5FXtuh9DEXxEraaBt2i-PYbh?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-62e0c001-7fff-4a99-2116-9aced6d41801\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfIYYEGoWncGPe8UrYJjZa32E0MDvO7b2rtDbrHI6pKYQk2Cp9m2G2vQeOUvbbn2YdPL1hfHwr0dgObLqVblFL1zxgMT8K44acF-oCpvEn_8oyxX-AH1joPTNUYmTCyx-5hBVrGf38dKfMiO6DaV81OYslA?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-5c30f9b8-7fff-6bc7-882d-bc7efb9926a1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfBhopAOfSTWEADa4zxKHjy6wDQjttQjUpXR5gcZyq52ioQhevtHUZ2EBWmbWkysuAZoNkgSlwryEEfNQMY7wjQp8-SNCpghC26jigPVUmAhrMGKjP3Rq4f2Uc-7W0T1z1NptijsdQlEMcBK4-OlU8TJkHr?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-9f270633-7fff-c062-2254-b00b94a89bd2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfQ15fOTbVr5z2eqfqUOWrMNDrH4yiYQged-uNOEMPoFUfJa9AaRnvlHV8cCBbi6gqGyewF8VbFO9lX3aLm6YBdN2ULYaXkCpGm0uxkrovfaCZPhvMFEgtVIQeA14x69hrWurzFkDQATcYuUJ2WJwX5gcEW?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-b1fe1a30-7fff-d835-cf6d-2d6d37d0e1e0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeww4DOFe56rwiUVU295SD-__UgOLZVEKv9WiMU78YNUghEmgJq5mdGLT5Mvh1M0yl8Okn7JqAbTepl9-TwHD9mjPGheVnQGhr1nscOS_0GiB9nHGlasHHpXdvHg7SYzrTo5FXtuh9DEXxEraaBt2i-PYbh?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-6905e426-7fff-f0b9-482d-c7c250598854\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfIYYEGoWncGPe8UrYJjZa32E0MDvO7b2rtDbrHI6pKYQk2Cp9m2G2vQeOUvbbn2YdPL1hfHwr0dgObLqVblFL1zxgMT8K44acF-oCpvEn_8oyxX-AH1joPTNUYmTCyx-5hBVrGf38dKfMiO6DaV81OYslA?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-1ef3e697-7fff-0721-5690-e6ebef1118ab\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfBhopAOfSTWEADa4zxKHjy6wDQjttQjUpXR5gcZyq52ioQhevtHUZ2EBWmbWkysuAZoNkgSlwryEEfNQMY7wjQp8-SNCpghC26jigPVUmAhrMGKjP3Rq4f2Uc-7W0T1z1NptijsdQlEMcBK4-OlU8TJkHr?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>"],
                    solution_en: "<p>14.(c)<br><strong id=\"docs-internal-guid-93a17678-7fff-228d-7270-78e7c3308cce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfIYYEGoWncGPe8UrYJjZa32E0MDvO7b2rtDbrHI6pKYQk2Cp9m2G2vQeOUvbbn2YdPL1hfHwr0dgObLqVblFL1zxgMT8K44acF-oCpvEn_8oyxX-AH1joPTNUYmTCyx-5hBVrGf38dKfMiO6DaV81OYslA?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>",
                    solution_hi: "<p>14.(c)<br><strong id=\"docs-internal-guid-93a17678-7fff-228d-7270-78e7c3308cce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfIYYEGoWncGPe8UrYJjZa32E0MDvO7b2rtDbrHI6pKYQk2Cp9m2G2vQeOUvbbn2YdPL1hfHwr0dgObLqVblFL1zxgMT8K44acF-oCpvEn_8oyxX-AH1joPTNUYmTCyx-5hBVrGf38dKfMiO6DaV81OYslA?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. The same operation(s) are followed in all the given number pairs except one. Find that odd number pair.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>15. दिए गए संख्या युग्मों में से एक को छोड़कर अन्य सभी में समान संक्रिया/संक्रियाओं का अनुसरण किया गया है। वह असंगत संख्या युग्म ज्ञात कीजिए।<br>(नोट : संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: ["<p>24 : 4</p>", "<p>36 :24</p>", 
                                "<p>12 : 8</p>", "<p>72 : 48</p>"],
                    options_hi: ["<p>24 : 4</p>", "<p>36 :24</p>",
                                "<p>12 : 8</p>", "<p>72 : 48</p>"],
                    solution_en: "<p>15.(a)<br><strong>Logic:-</strong> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mi>nd</mi></msup><mi>no</mi><mo>.</mo></math>) &times; 1.5 = 1<sup>st</sup>no.<br>(36 : 24):- (24) &times;&nbsp;1.5 = 36<br>(12 : 8):- (8) &times; 1.5 = 12<br>(72 : 48):- (48) &times; 1.5 = 72<br>but<br>(24 : 4):- (4) &times; 1.5 = 6 &ne; 24</p>",
                    solution_hi: "<p>15.(a)<br><strong>तर्क</strong> :- (दूसरी संख्या) &times; 1.5 = पहली संख्या <br>(36 : 24):- (24) &times; 1.5 = 36<br>(12 : 8):- (8) &times; 1.5 = 12<br>(72 : 48):- (48) &times; 1.5 = 72<br>लेकिन,<br>(24 : 4):- (4) &times; 1.5 = 6 &ne; 24</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last as shown. How would the paper look when unfolded?<br><strong id=\"docs-internal-guid-4e1d957a-7fff-3e79-6d71-2b4c6c3bdac7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4bB0m9gUm8KqZ_gCm46wD5QGIqQ7AZaP8vQBhwQnR-A4NNBoQXkzrv-pI7eFT3Oxm8l4xmVbHxOHpPkXjGFfdbALIc-bfoYUAZuQYq6gIDTcF2l-DEna_Ic6wGVTec-ONCysIrzX4Xbx3uYZObnC-yHDX?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"260\" height=\"80\"></strong></p>",
                    question_hi: "<p>16. कागज की एक वर्गाकार शीट को बिंदीदार रेखा के साथ दिखाई गई दिशाओं के अनुसार क्रमिक रूप से मोड़ा जाता है और फिर दिखाए गए चित्र के अनुसार अंत में पंच किया जाता है। खुलने पर कागज कैसा दिखेगा?<br><strong id=\"docs-internal-guid-46da6f8b-7fff-d0a1-cfae-1e9e437987ed\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4bB0m9gUm8KqZ_gCm46wD5QGIqQ7AZaP8vQBhwQnR-A4NNBoQXkzrv-pI7eFT3Oxm8l4xmVbHxOHpPkXjGFfdbALIc-bfoYUAZuQYq6gIDTcF2l-DEna_Ic6wGVTec-ONCysIrzX4Xbx3uYZObnC-yHDX?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"260\" height=\"80\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-55696add-7fff-6c8c-6e22-fcff0417687b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeX6hs0kLhFOifuLaNzKOljBTKPPIPRWFlc3OF5YiN4SD-itMSl0vsW380pDG3e4RstvQNmFvCy9XdT-4YzLtQgxAaiN4ucD3ceDQxd90UjeQJw5POMYaaX2tD61kQ4AY2lbo4XOXaIh9Sc4BdUiierl1WM?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-033fe073-7fff-8483-a4dc-5043be16adc2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdreYevp6bKlEaMLmkdWWRnWPZnOXo20k2j9I2UXUz5qA0GrI3_lm0-CYuHQBnD-MdKPfDSmg6keybxVLtAbhGKwUH-v9onrK9gcmMxkysFnxAs4n4hmIke6ZxpqdhJoFQcD9i6gX7h4NPdit1xkjNmC6Sj?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-2105ec86-7fff-9df3-ed09-6ec48f7a1462\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcz3pQ9BZSuBiDTgptM3p9o7k4oBWimaHuhqjkFO6B0--IMWW43Gup2R14MRzgroFNdfjBZLPTV2ddMWjmKlLXH7TsZ_MOBLmEpNGEJQfdaTlG6cHncAO-bFpBV0f-q9i3WwmL4QP3q5vRWR_5ugiolVz0?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-0ed3272a-7fff-b4b7-76dd-5d35e01988b2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0PXKbThsvtIAwApTAEgtvrFPvkwjXxDjtryz0E8tRQtkJx_7mu2wCnsYu3_jZmO5VVQ8T33Oz4V6jCTcYZLhV3Afk16OqW2o9ZcRuJ8fdOw1eQvNpHu6CaDwdWEGMb9bP2opc7flDRHSC0NZhA4EnKUo?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-e521cbd4-7fff-e6e0-98ba-864b940f5bfa\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeX6hs0kLhFOifuLaNzKOljBTKPPIPRWFlc3OF5YiN4SD-itMSl0vsW380pDG3e4RstvQNmFvCy9XdT-4YzLtQgxAaiN4ucD3ceDQxd90UjeQJw5POMYaaX2tD61kQ4AY2lbo4XOXaIh9Sc4BdUiierl1WM?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-37c028c3-7fff-fc8f-5b31-2eb105fad557\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdreYevp6bKlEaMLmkdWWRnWPZnOXo20k2j9I2UXUz5qA0GrI3_lm0-CYuHQBnD-MdKPfDSmg6keybxVLtAbhGKwUH-v9onrK9gcmMxkysFnxAs4n4hmIke6ZxpqdhJoFQcD9i6gX7h4NPdit1xkjNmC6Sj?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-2bdd8666-7fff-140a-64a0-13925991c432\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcz3pQ9BZSuBiDTgptM3p9o7k4oBWimaHuhqjkFO6B0--IMWW43Gup2R14MRzgroFNdfjBZLPTV2ddMWjmKlLXH7TsZ_MOBLmEpNGEJQfdaTlG6cHncAO-bFpBV0f-q9i3WwmL4QP3q5vRWR_5ugiolVz0?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-080a378f-7fff-2c48-4bf5-22b713227d7f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0PXKbThsvtIAwApTAEgtvrFPvkwjXxDjtryz0E8tRQtkJx_7mu2wCnsYu3_jZmO5VVQ8T33Oz4V6jCTcYZLhV3Afk16OqW2o9ZcRuJ8fdOw1eQvNpHu6CaDwdWEGMb9bP2opc7flDRHSC0NZhA4EnKUo?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>"],
                    solution_en: "<p>16.(d)<br><strong id=\"docs-internal-guid-080a378f-7fff-2c48-4bf5-22b713227d7f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0PXKbThsvtIAwApTAEgtvrFPvkwjXxDjtryz0E8tRQtkJx_7mu2wCnsYu3_jZmO5VVQ8T33Oz4V6jCTcYZLhV3Afk16OqW2o9ZcRuJ8fdOw1eQvNpHu6CaDwdWEGMb9bP2opc7flDRHSC0NZhA4EnKUo?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>",
                    solution_hi: "<p>16.(d)<br><strong id=\"docs-internal-guid-080a378f-7fff-2c48-4bf5-22b713227d7f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0PXKbThsvtIAwApTAEgtvrFPvkwjXxDjtryz0E8tRQtkJx_7mu2wCnsYu3_jZmO5VVQ8T33Oz4V6jCTcYZLhV3Afk16OqW2o9ZcRuJ8fdOw1eQvNpHu6CaDwdWEGMb9bP2opc7flDRHSC0NZhA4EnKUo?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"72\" height=\"72\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. The position of how many letters will remain unchanged if each of the letter in the word &lsquo;IMPROVE&rsquo; is arranged in English alphabetical order?</p>",
                    question_hi: "<p>17. यदि शब्द &lsquo;IMPROVE&rsquo; के प्रत्येक अक्षर को अंग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित रहेगी?</p>",
                    options_en: ["<p>Zero</p>", "<p>One</p>", 
                                "<p>Three</p>", "<p>Two</p>"],
                    options_hi: ["<p>शून्य</p>", "<p>एक</p>",
                                "<p>तीन</p>", "<p>दो</p>"],
                    solution_en: "<p>17.(a)<br><strong id=\"docs-internal-guid-200e25e4-7fff-4409-695d-a2d9041f5974\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdrysXhRAbEacpssUX9G93qSzPoUq-qF5B4exDhoeRBo6QwBPrfmPbaYEn3aUh3Cv_k__DBONBtV9EFpjhosqwVmEpQ1BoTFz8e6D22OBWEBZ9s5AaWvTbFu8hClZV4T0u8bZSpk93BacpAQBD25VQwjsZb?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"180\" height=\"64\"></strong><br>From the above arrangement we can see that the position of all letters will change.</p>",
                    solution_hi: "<p>17.(a)<br><strong id=\"docs-internal-guid-200e25e4-7fff-4409-695d-a2d9041f5974\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdrysXhRAbEacpssUX9G93qSzPoUq-qF5B4exDhoeRBo6QwBPrfmPbaYEn3aUh3Cv_k__DBONBtV9EFpjhosqwVmEpQ1BoTFz8e6D22OBWEBZ9s5AaWvTbFu8hClZV4T0u8bZSpk93BacpAQBD25VQwjsZb?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"180\" height=\"64\"></strong><br>उपरोक्त व्यवस्था से हम देख सकते हैं कि सभी अक्षरों की स्थिति बदल जाएगी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Three statements are given followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>All crows are parrots.<br>All eagles are parrots.<br>All parrots are birds.<br><strong>Conclusions:</strong><br>I. No crow is a bird.<br>II. Some birds are eagles.<br>III. All birds are crows.</p>",
                    question_hi: "<p>18. तीन कथन और उसके बाद तीन निष्कर्ष क्रमांकित I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों,&nbsp; निर्णय लें कि कौन सा/से निष्कर्ष तार्किक रूप से कथनों का अनुसरण करता है/करते हैं।<br><strong>कथन:</strong><br>सभी कौए, तोते हैं।<br>सभी गरुड़, तोते हैं।<br>सभी तोते, पक्षी हैं।<br><strong>निष्कर्ष:</strong><br>I. कोई भी कौआ, पक्षी नहीं है।<br>II. कुछ पक्षी, गरुड़ हैं।<br>III. सभी पक्षी, कौए हैं।</p>",
                    options_en: ["<p>Only conclusion II follows.</p>", "<p>Either conclusion II or III follows.</p>", 
                                "<p>All conclusions follow.</p>", "<p>Both conclusions I and III follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष II अनुसरण करता है।</p>", "<p>या तो निष्कर्ष II या III अनुसरण करता है।</p>",
                                "<p>सभी निष्कर्ष अनुसरण करते हैं।</p>", "<p>दोनों निष्कर्ष I और III अनुसरण करते हैं।</p>"],
                    solution_en: "<p>18.(a)<br><strong id=\"docs-internal-guid-dfbeeb0b-7fff-4e0c-1bd6-2c8f2aa1a76b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcskjUd2XvYCJuioXPrs3dVMlbQM07FcijiRZkNMvhMctBZvkiaFwKRiMUkBWmnbJ0oGgGkpAvxbW0l_DeUU-m22uRoyQUMnfA9FXmr_gwbV-9MMgiUrQox-Op0Cwkz_CIsXu8Jj2uw0fKkOTejsOon3So?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"136\" height=\"122\"></strong><br>Hence, only conclusion II follows.</p>",
                    solution_hi: "<p>18.(a)<br><strong id=\"docs-internal-guid-9f04e1a2-7fff-6091-f9df-11bd6d299a26\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXckK40AgWcAtxfWEd9X4rWiyiIkbzoMTerYX8lM0FIPixcMLib808PXtCWyOwIGi7stdFsHZm7mTu6vBTZltqD6o4eyTe4YdA-Fbw-crjifyS6fb_nWCywokE7JVdZfaZOhQoObkb2TFgH-ujIAR1np3ABJ?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"144\" height=\"128\"></strong><br>अतः, केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Which of the following numbers will replace the question mark (?) in the given series?<br>81, 65, 50, ?, 23, 11</p>",
                    question_hi: "<p>19. निम्नलिखित में से कौन-सी संख्या दी गई शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित करेगी?<br>81, 65, 50, ?, 23, 11</p>",
                    options_en: ["<p>37</p>", "<p>36</p>", 
                                "<p>34</p>", "<p>35</p>"],
                    options_hi: ["<p>37</p>", "<p>36</p>",
                                "<p>34</p>", "<p>35</p>"],
                    solution_en: "<p>19.(b)<br><strong id=\"docs-internal-guid-f41a5432-7fff-e270-18a7-d9e7f05616bd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfbN_pIezSXsuLOJOY572dQ4ZqkYOJn2TU5DqDCd2QqY5F40jrDdCznFj0tMFvlNiN3ZNUbeW_MRuH9RzEURfYM3baN6QwGyY33Hcz6OmFOFoQMNQ01hV_sWkXpeJz0pMdynzwKUZS5c_A6jPyCKDU6-Ppw?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"296\" height=\"61\"></strong></p>",
                    solution_hi: "<p>19.(b)<br><strong id=\"docs-internal-guid-f41a5432-7fff-e270-18a7-d9e7f05616bd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfbN_pIezSXsuLOJOY572dQ4ZqkYOJn2TU5DqDCd2QqY5F40jrDdCznFj0tMFvlNiN3ZNUbeW_MRuH9RzEURfYM3baN6QwGyY33Hcz6OmFOFoQMNQ01hV_sWkXpeJz0pMdynzwKUZS5c_A6jPyCKDU6-Ppw?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"296\" height=\"61\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series.<br>X _ _ Y_X X _ _ X _ _</p>",
                    question_hi: "<p>20. अक्षरों के उस संयोजन का चयन कीजिए, जिसके अक्षरों को दी गई शृंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर शृंखला पूर्ण हो जाएगी।<br>X _ _ Y_X X _ _ X _ _</p>",
                    options_en: ["<p>X X X Y X X Y</p>", "<p>X X Y Y A A X</p>", 
                                "<p>Y X X Y Y X X</p>", "<p>Y Y X Y A X X</p>"],
                    options_hi: ["<p>X X X Y X X Y</p>", "<p>X X Y Y A A X</p>",
                                "<p>Y X X Y Y X X</p>", "<p>Y Y X Y A X X</p>"],
                    solution_en: "<p>20.(a)<br>Series <math display=\"inline\"><mo>&#8658;</mo></math> X <strong><span style=\"text-decoration: underline;\">X X</span></strong> Y / <strong><span style=\"text-decoration: underline;\">X</span></strong> X X <strong><span style=\"text-decoration: underline;\">Y</span></strong> / <strong><span style=\"text-decoration: underline;\">X</span></strong> X <strong><span style=\"text-decoration: underline;\">X </span><span style=\"text-decoration: underline;\">Y</span></strong></p>",
                    solution_hi: "<p>20.(a)<br>श्रंखला <math display=\"inline\"><mo>&#8658;</mo></math>X <strong><span style=\"text-decoration: underline;\">X X</span></strong> Y / <strong><span style=\"text-decoration: underline;\">X</span></strong> X X <strong><span style=\"text-decoration: underline;\">Y</span></strong> / <strong><span style=\"text-decoration: underline;\">X</span></strong> X <strong><span style=\"text-decoration: underline;\">X </span><span style=\"text-decoration: underline;\">Y</span></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. In a certain code language,<br>X + Y means &lsquo;X is the sister of Y&rsquo;,<br>X &minus; Y means &lsquo;X is the son of Y&rsquo;,<br>X &times; Y means &lsquo;X is the husband of Y&rsquo;,<br>X &divide; Y means &lsquo;X is the mother of Y&rsquo;.<br>Based on the above, how is T related to X if &lsquo;T + U &minus; V &times; W &divide; X&rsquo;?</p>",
                    question_hi: "<p>21. एक निश्चित कूट भाषा में,<br>X + Y का अर्थ है \'X, Y की बहन है\',<br>X &minus; Y का अर्थ है \'X, Y का पुत्र है\',<br>X &times; Y का अर्थ है \'X, Y का पति है\',<br>X &divide; Y का अर्थ है \'X, Y की माता है\'।<br>उपर्युक्त के आधार पर, यदि \'T + U &minus; V &times; W &divide; X\' है, तो T, X से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Father&rsquo;s mother</p>", "<p>Sister</p>", 
                                "<p>Father&rsquo;s sister</p>", "<p>Mother&rsquo;s sister</p>"],
                    options_hi: ["<p>पिता की माता</p>", "<p>बहन</p>",
                                "<p>पिता की बहन</p>", "<p>माता की बहन</p>"],
                    solution_en: "<p>21.(b)<br><strong id=\"docs-internal-guid-32bbcf1d-7fff-e442-cd89-7488670912b7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcFI-OJwBZFRVuoJXX6gKTa-xsACtabZsth4tF9bS2ywVF9VoflaUflUD9tASxYgZXwsUjaB2Y4lo1MjXShOOENgz6vrgq69KI2SKH_q7-245yxRcmXFF3VZx-YmaWElbMfXroq88gxBbfIG0LpLBmFFVhk?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"200\" height=\"112\"></strong><br>Hence, &lsquo;T&rsquo; is the sister of &lsquo;X&rsquo;</p>",
                    solution_hi: "<p>21.(b)<br><strong id=\"docs-internal-guid-32bbcf1d-7fff-e442-cd89-7488670912b7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcFI-OJwBZFRVuoJXX6gKTa-xsACtabZsth4tF9bS2ywVF9VoflaUflUD9tASxYgZXwsUjaB2Y4lo1MjXShOOENgz6vrgq69KI2SKH_q7-245yxRcmXFF3VZx-YmaWElbMfXroq88gxBbfIG0LpLBmFFVhk?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"200\" height=\"112\"></strong><br>अतः, \'T\', \'X\' की बहन है ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. What will come in place of the question mark (?) in the following equation if &lsquo;+&rsquo; and &lsquo;&minus;&lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>15 &minus; 7 &divide; 4 + 36 &times; 9 = ?</p>",
                    question_hi: "<p>22. निम्नलिखित समीकरण में यदि \'+\' और \'-\' को आपस में बदल दिया जाए और \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए तो प्रश्न चिह्न (?) के स्थान पर कौन सी संख्&zwj;या आएगी?<br>15 &minus; 7 &divide; 4 + 36 &times; 9 = ?</p>",
                    options_en: ["<p>42</p>", "<p>34</p>", 
                                "<p>40</p>", "<p>39</p>"],
                    options_hi: ["<p>42</p>", "<p>34</p>",
                                "<p>40</p>", "<p>39</p>"],
                    solution_en: "<p>22.(d)<br><strong>Given</strong> :<math display=\"inline\"><mo>-</mo></math> 15 - 7 &divide; 4 + 36 &times; 9 = ?<br>As per given instruction after interchanging &lsquo;+ and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get<br>15 + 7 &times;&nbsp;4 - 36 &divide; 9 <br>15 + 28 -&nbsp;4 = 39</p>",
                    solution_hi: "<p>22.(d)<br><strong>दिया गया है </strong>:-&nbsp;15 - 7 &divide; 4 + 36 &times; 9 = ?<br>दिए गए निर्देश के अनुसार \'+ और \'-\' और \'&times;\' और \'&divide;\' को आपस में बदलने के बाद हमें प्राप्त होता है<br>15 + 7 &times;&nbsp;4 - 36 &divide; 9 <br>15 + 28 -&nbsp;4 = 39</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Which of the following terms will replace the question mark (?) in the given series?<br>XJUM, VLSO, TNQQ, RPOS, ?</p>",
                    question_hi: "<p>23. निम्नलिखित में से कौन-सा पद दी गई शृंखला में प्रश्न-चिह्न (?) का स्थान लेगा?<br>XJUM, VLSO, TNQQ, RPOS, ?</p>",
                    options_en: ["<p>PRMU</p>", "<p>QRMT</p>", 
                                "<p>PRMT</p>", "<p>PRNU</p>"],
                    options_hi: ["<p>PRMU</p>", "<p>QRMT</p>",
                                "<p>PRMT</p>", "<p>PRNU</p>"],
                    solution_en: "<p>23.(a)<br><strong id=\"docs-internal-guid-9e1327b4-7fff-2c20-3f05-aac588d7684d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdfW_xAoQybHklcZBdEnZnbj5rnWtY1ve54Nl7e7yiGLoBWUA9y3YXOlkLZ-CxqvJa-6h59yPF9sOgD6vApQraAJb3xOX7gRNXuOM5W0CFqQ-N2kh6G7jVIFOST2lXFoDCpEiELcBr-P_mS6Z-s7ikzHLxl?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"417\" height=\"94\"></strong></p>",
                    solution_hi: "<p>23.(a)<br><strong id=\"docs-internal-guid-9e1327b4-7fff-2c20-3f05-aac588d7684d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdfW_xAoQybHklcZBdEnZnbj5rnWtY1ve54Nl7e7yiGLoBWUA9y3YXOlkLZ-CxqvJa-6h59yPF9sOgD6vApQraAJb3xOX7gRNXuOM5W0CFqQ-N2kh6G7jVIFOST2lXFoDCpEiELcBr-P_mS6Z-s7ikzHLxl?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"417\" height=\"94\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. In a certain code language, &lsquo;FLAKE&rsquo; is written as &lsquo;&amp;2348&rsquo; and &lsquo;LAKES&rsquo; is written as &lsquo;23@84&rsquo;. How will &lsquo;S&rsquo; be written in that language?</p>",
                    question_hi: "<p>24. एक निश्चित कूट भाषा में, &lsquo;FLAKE&rsquo; को &lsquo;&amp;2348&rsquo; के रूप में लिखा जाता है और &lsquo;LAKES&rsquo; को &lsquo;23@84&rsquo; के रूप में लिखा जाता है। उसी भाषा में &lsquo;S&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>4</p>", "<p>&amp;</p>", 
                                "<p>3</p>", "<p>@</p>"],
                    options_hi: ["<p>4</p>", "<p>&amp;</p>",
                                "<p>3</p>", "<p>@</p>"],
                    solution_en: "<p>24.(d)<br>F L A K E &rarr;&nbsp;&amp; 2 3 4 8 <br>L A K E S &rarr;&nbsp;2 3 @ 8 4 <br>From the above codes the code for &lsquo;S&rsquo; is &lsquo;@&rsquo;.</p>",
                    solution_hi: "<p>24.(d)<br>F L A K E &rarr;&nbsp;&amp; 2 3 4 8 <br>L A K E S &rarr;&nbsp;2 3 @ 8 4 <br>उपरोक्त कोड से &lsquo;S&rsquo; का कोड &lsquo;@&rsquo; है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers.<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1<br>and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>4 : 32<br>7 : 98</p>",
                    question_hi: "<p>25. उस विकल्&zwj;प का चयन कीजिए जिसमें संख्&zwj;याओं के मध्&zwj;य वही संबंध है जो नीचे दिए गए युग्&zwj;म की संख्&zwj;याओं के मध्&zwj;य है।<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/ घटाना/ गुणा करना आदि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>4 : 32<br>7 : 98</p>",
                    options_en: ["<p>8 : 192</p>", "<p>3 : 18</p>", 
                                "<p>10 : 1000</p>", "<p>12 : 144</p>"],
                    options_hi: ["<p>8 : 192</p>", "<p>3 : 18</p>",
                                "<p>10 : 1000</p>", "<p>12 : 144</p>"],
                    solution_en: "<p>25.(b)<br><strong>Logic:-</strong> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mi>st</mi></msup><mi>no</mi><mo>.</mo></math>) &times; (1<sup>st</sup>no. &times; 2) = 2<sup>nd</sup>no.<br>(4 : 32):- (4) &times; (4 &times; 2) = 32<br>(7 : 98):- (7) &times; (7 &times; 2) = 98<br>Similarly <br>(3 : 18):- (3) &times; (3 &times; 2) = 18</p>",
                    solution_hi: "<p>25.(b)<br><strong>तर्क</strong> :- (पहली संख्या) &times; (पहली संख्या &times; 2) = दूसरी संख्या <br>(4 : 32):- (4) &times; (4 &times; 2) = 32<br>(7 : 98):- (7) &times; (7 &times; 2) = 98<br>इसी प्रकार <br>(3 : 18):- (3) &times; (3 &times; 2) = 18</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Which of the following statements about the second five-year plan is INCORRECT?",
                    question_hi: "26. निम्नलिखित में से कौन-सा कथन द्वितीय पंचवर्षीय योजना के संदर्भ में गलत है?",
                    options_en: [" The plan stressed the rapid industrialisation of heavy and basic industries. ", " Electricity, Railway, Steel etc. industries were established during the plan.", 
                                " The plan was based on the Keynesian Model. ", " The socialist pattern of the society was reflected in the plan."],
                    options_hi: [" योजना में भारी और मूल उद्योगों के तीव्र औद्योगिकीकरण पर जोर दिया गया। ", " योजना के दौरान विद्युत, रेलवे, इस्पात आदि उद्योग स्थापित किये गये।",
                                " यह योजना कीनेसियन मॉडल (Keynesian Model) पर आधारित थी।", " योजना में समाज का समाजवादी स्वरूप प्रतिबिंबित था।"],
                    solution_en: "26.(c) The Second Five-Year Plan (1956-1961) was based on the Mahalanobis model, named after Indian statistician Prasanta Chandra Mahalanobis. The target growth rate was set at 4.5%, while the actual growth achieved was 4.3%. ",
                    solution_hi: "26.(c) द्वितीय पंचवर्षीय योजना (1956-1961) महालनोबिस मॉडल पर आधारित थी, जिसका नाम भारतीय सांख्यिकीविद् प्रशांत चंद्र महालनोबिस के नाम पर रखा गया था। लक्ष्य वृद्धि दर 4.5% निर्धारित की गई थी, जबकि वास्तविक वृद्धि 4.3% रही थी।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. Who among the following was appointed as the Chief Economic Advisor (CEA) to the Indian government in 2022?",
                    question_hi: "27. 2022 में, निम्नलिखित में से किसको भारत सरकार का मुख्य आर्थिक सलाहकार (CEA) नियुक्त किया गया था?",
                    options_en: [" Venkatramanan Anantha Nageswaran ", " Kaushik Basu ", 
                                " TV Somanathan ", " Raghuram Rajan"],
                    options_hi: [" वेंकटरमन अनंत नागेश्वरन ", " कौशिक बसु ",
                                " टी.वी. सोमनाथन ", " रघुराम राजन"],
                    solution_en: "27.(a) Venkatramanan Anantha Nageswaran. He is an Indian economist and the 18th Chief Economic Advisor to the Government of India. Role of the Chief Economic Adviser (CEA) - Advising the Government: The CEA advises the government on economic policies and strategies to promote economic growth and stability. Economic Analysis: Conducts in-depth analyses of economic data and trends to inform policy decisions. Budget Preparation: Assists in preparing the annual budget and provides insights on fiscal policy.",
                    solution_hi: "27.(a) वेंकटरमणन अनंत नागेश्वरन। वे एक भारतीय अर्थशास्त्री और भारत सरकार के 18वें मुख्य आर्थिक सलाहकार हैं। मुख्य आर्थिक सलाहकार (CEA) की भूमिका - सरकार को सलाह देना: CEA आर्थिक विकास और स्थिरता को बढ़ावा देने के लिए आर्थिक नीतियों और रणनीतियों पर सरकार को सलाह देता है। आर्थिक विश्लेषण: नीतिगत निर्णय लेने के लिए आर्थिक आंकड़ों और प्रवृत्तियों का गहन विश्लेषण करता है।  बजट तैयार करना: वार्षिक बजट तैयार करने में सहायता करता है और राजकोषीय नीति पर अंतर्दृष्टि प्रदान करता है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Name the pension scheme that seeks to ensure old age protection for unorganised workers.</p>",
                    question_hi: "<p>28. असंगठित श्रमिकों के लिए वृद्धावस्था सुरक्षा सुनिश्चित करने वाली पेंशन योजना का नाम बताइए।</p>",
                    options_en: ["<p>Atal Pension Yojana</p>", "<p>Pradhan Mantri Mudra Yojna</p>", 
                                "<p>Pradhan Mantri Suraksha Bima Yojna</p>", "<p>Pradhan Mantri Shram Yogi Maandhan</p>"],
                    options_hi: ["<p>अटल पेंशन योजना</p>", "<p>प्रधानमंत्री मुद्रा योजना</p>",
                                "<p>प्रधानमंत्री सुरक्षाबीमा योजना</p>", "<p>प्रधानमंत्री श्रम योगी मानधन</p>"],
                    solution_en: "<p>28.(d) <strong>Pradhan Mantri Shram Yogi Maandhan.</strong> This scheme was launched by Prime Minister Narendra Modi in 2019 to support unorganized sector workers. It offers a monthly pension of ₹3,000 after the age of 60. Workers aged 18 to 40 years, with a monthly income of ₹15,000 or less, and who are not members of EPFO, ESIC, or NPS (government-funded), can enroll in the PM-SYM scheme.</p>",
                    solution_hi: "<p>28.(d) <strong>प्रधानमंत्री श्रम योगी मानधन</strong>। यह योजना प्रधानमंत्री नरेंद्र मोदी द्वारा असंगठित क्षेत्र के श्रमिकों को सहायता देने के लिए 2019 में शुरू की गई थी। यह 60 वर्ष की आयु के बाद ₹3,000 की मासिक पेंशन प्रदान करती है। 18 से 40 वर्ष की आयु के वे श्रमिक जिनकी मासिक आय ₹15,000 या उससे कम है और जो EPFO, ESIC या NPS (सरकार द्वारा वित्तपोषित) के सदस्य नहीं हैं, वे PM-SYM योजना में नामांकन कर सकते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. What is the gas evolved when zinc reacts with sulfuric acid?</p>",
                    question_hi: "<p>29. जिंक के सल्फ्यूरिक अम्&zwj;ल के साथ अभिक्रिया करने पर कौन सी गैस निकलती है?</p>",
                    options_en: ["<p>Hydrogen</p>", "<p>Oxygen</p>", 
                                "<p>Carbon dioxide</p>", "<p>Hydrogen sulfide</p>"],
                    options_hi: ["<p>हाइड्रोजन</p>", "<p>ऑक्सीजन</p>",
                                "<p>कार्बन डाइऑक्साइड</p>", "<p>हाइड्रोजन सल्फाइड</p>"],
                    solution_en: "<p>29.(a) <strong>Hydrogen.</strong> Zinc (Zn) is a metal, and sulfuric acid (H<sub>2</sub>SO<sub>4</sub>) is a strong acid. When they react, the metal (Zinc) displaces the hydrogen from the acid. The reaction can be represented by the following chemical equation:<br>Zn + H<sub>2</sub>SO<sub>4</sub> &rarr; ZnSO<sub>4</sub> + H<sub>2</sub>.</p>",
                    solution_hi: "<p>29.(a) <strong>हाइड्रोजन।</strong> जिंक (Zn) एक धातु है, और सल्फ्यूरिक अम्ल (H<sub>2</sub>SO<sub>4</sub>) एक प्रबल अम्ल है। जब वे अभिक्रिया करते हैं, तो धातु (जिंक) अम्ल से हाइड्रोजन को विस्थापित कर देती है। अभिक्रिया को निम्नलिखित रासायनिक समीकरण द्वारा दर्शाया जा सकता है:<br>Zn + H<sub>2</sub>SO<sub>4</sub> &rarr; ZnSO<sub>4</sub> + H<sub>2</sub>.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. In 2022, who was appointed as the new Attorney General of India?</p>",
                    question_hi: "<p>30. 2022 में, किसे भारत का नया अटॉर्नी जनरल नियुक्त किया गया?</p>",
                    options_en: ["<p>KK Venugopal</p>", "<p>Niren De</p>", 
                                "<p>Mukul Rohatgi</p>", "<p>R Venkatramani</p>"],
                    options_hi: ["<p>के.के. वेणुगोपाल</p>", "<p>नीरेन डे</p>",
                                "<p>मुकुल रोहतगी</p>", "<p>आर. वेंकटरमणि</p>"],
                    solution_en: "<p>30.(d)<strong> R Venkatramani. </strong>He succeeded to the office as the 16th Attorney-General. His predecessor was K. K. Venugopal. The Attorney General (Article 76) of India is a part of the Union Executive. He is the highest law officer in the country who can be part of any court in the Indian Territory.</p>",
                    solution_hi: "<p>30.(d) <strong>आर. वेंकटरमणि।</strong> वे 16वें अटॉर्नी जनरल के रूप में पद पर आसीन हुए। इनसे पहले के. के. वेणुगोपाल थे। भारत का अटॉर्नी जनरल (अनुच्छेद 76) संघीय कार्यकारिणी का हिस्सा होता है। वह देश का सर्वोच्च विधि अधिकारी होता है जो भारतीय क्षेत्र में किसी भी न्यायालय का हिस्सा हो सकता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following is NOT a Kharif crop?</p>",
                    question_hi: "<p>31. निम्नलिखित में से कौन सी खरीफ की फसल नहीं है?</p>",
                    options_en: ["<p>Paddy</p>", "<p>Soybean</p>", 
                                "<p>Wheat</p>", "<p>Cotton</p>"],
                    options_hi: ["<p>चावल</p>", "<p>सोयाबीन</p>",
                                "<p>गेहूँ</p>", "<p>कपास</p>"],
                    solution_en: "<p>31.(c) <strong>Wheat.</strong> Rabi crops are sown in October or mid-November. Examples: Gram, pea, mustard and linseed. Kharif Crops: The crops which are sown in the rainy season. Examples: Paddy, maize, soyabean, groundnut and cotton.</p>",
                    solution_hi: "<p>31.(c) <strong>गेहूँ।</strong> रबी की फसलें अक्टूबर या मध्य नवंबर में बोई जाती हैं। उदाहरण: चना, मटर, सरसों और अलसी। खरीफ फसलें: वे फसलें जो वर्षा के मौसम में बोई जाती हैं। उदाहरण: धान, मक्का, सोयाबीन, मूंगफली और कपास।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following is another name for vitamin C?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन-सा विटामिन C का दूसरा नाम है?</p>",
                    options_en: ["<p>Pyridoxine</p>", "<p>Pyridoxal</p>", 
                                "<p>Pyridoxamine</p>", "<p>Ascorbic acid</p>"],
                    options_hi: ["<p>पाइरिडोक्सिन (Pyridoxine)</p>", "<p>पाइरिडोक्सल (Pyridoxal)</p>",
                                "<p>पाइरिडोक्सामाइन (Pyridoxamine)</p>", "<p>एस्कॉर्बिक अम्ल (Ascorbic acid)</p>"],
                    solution_en: "<p>32.(d) <strong>Ascorbic acid.</strong> It is water-soluble. Function: It contributes to collagen production, wound healing, and bone formation. Deficiency: This may result in scurvy, which causes bleeding gums, a loss of teeth, and poor tissue growth and wound healing. Good source: Citrus fruits like lemon and oranges and non-citrus fruits like strawberries, raspberries, amla. Different types of Vitamins: Vitamin A (Retinol), Vitamin B1 (Thiamine), Vitamin B6 (Pyridoxine), Vitamin E (Tocopherol), Vitamin K (Phytonadione).</p>",
                    solution_hi: "<p>32.(d) <strong>एस्कॉर्बिक अम्ल</strong> (Ascorbic acid)। यह जल में घुलनशील है। कार्य: यह कोलेजन उत्पादन, जख्म भरने और हड्डियों के निर्माण में योगदान देता है। कमी से रोग : इसके परिणामस्वरूप स्कर्वी हो सकता है, जिससे मसूड़ों से खून आना, दांतों का गिरना और ऊतकों की वृद्धि और जख्म भरने में कमी हो सकती है। अच्छा स्रोत: खट्टे फल जैसे नींबू और संतरे और गैर-खट्टे फल जैसे स्ट्रॉबेरी, रास्पबेरी, आंवला। विभिन्न प्रकार के विटामिन: विटामिन A (रेटिनॉल), विटामिन B1 (थायमिन), विटामिन B6 (पाइरिडोक्सिन), विटामिन E (टोकोफेरॉल), विटामिन K (फाइटोनाडियोन)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Who among the following Indian classical dancers is associated with Kuchipudi?</p>",
                    question_hi: "<p>33. निम्नलिखित भारतीय शास्त्रीय नर्तकियों में से कौन कुचिपुड़ी से संबंधित है?</p>",
                    options_en: ["<p>Meenakshi Medhi</p>", "<p>Vempati Chinna Satyam</p>", 
                                "<p>Sanjukta Panigrahi</p>", "<p>Malavika Sarkar</p>"],
                    options_hi: ["<p>मीनाक्षी मेधी</p>", "<p>वेम्पति चिन्ना सत्यम</p>",
                                "<p>संजुक्ता पाणिग्रही</p>", "<p>मालविका सरकार</p>"],
                    solution_en: "<p>33.(b) <strong>Vempati Chinna Satyam</strong>. He was an Indian dancer and started the Kuchipudi Art Academy at Madras (now Chennai) in 1963. His Award: Padma Bhushan (1998). Kuchipudi is a classical Indian dance form that originated in Andhra Pradesh. Famous Kuchipudi Dancers: Dr Sobha Naidu, Raja Radha Reddy and Indrani Bajpai.</p>",
                    solution_hi: "<p>33.(b) <strong>वेम्पति चिन्ना सत्यम।</strong> वे एक भारतीय नर्तक थे और उन्होंने 1963 में मद्रास (अब चेन्नई) में कुचिपुड़ी कला अकादमी की शुरुआत की थी। उनका पुरस्कार: पद्म भूषण (1998)। कुचिपुड़ी एक शास्त्रीय भारतीय नृत्य शैली है जिसकी उत्पत्ति आंध्र प्रदेश में हुई थी। प्रसिद्ध कुचिपुड़ी नर्तक: डॉ. सोभा नायडू, राजा राधा रेड्डी और इंद्राणी बाजपेयी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following Sultans of Delhi primarily adopted a policy of consolidation rather than expansion?</p>",
                    question_hi: "<p>34. दिल्ली के निम्नलिखित में से किस सुल्तान ने मुख्य रूप से विस्तार के बजाय सुदृढ़ीकरण की नीति अपनाई?</p>",
                    options_en: ["<p>Ibrahim Lodi</p>", "<p>Balban</p>", 
                                "<p>Alauddin Khalji</p>", "<p>Bahlol Lodi</p>"],
                    options_hi: ["<p>इब्राहीम लोदी</p>", "<p>बलबन</p>",
                                "<p>अलाउद्दीन खिलजी</p>", "<p>बहलोल लोदी</p>"],
                    solution_en: "<p>34.(b) <strong>Balban.</strong> Ghiyasuddin Balban (Reigned : 1266&ndash;1287) was the ninth sultan of the Slave dynasty of Delhi. He belonged to the famous group of 40 Turkic slaves of Iltutmish. He was the first Muslim ruler to formulate the \'theory of kingship\' similar to the \'theory of the divine right of the kings\'. He established the military department &ldquo;Diwan-i-Arz&rdquo;.</p>",
                    solution_hi: "<p>34.(b) <strong>बलबन।</strong> ग़यासुद्दीन बलबन (शासनकाल: 1266-1287) दिल्ली के गुलाम वंश का नौवां सुल्तान था। वह इल्तुतमिश के 40 तुर्क गुलामों के प्रसिद्ध समूह से संबंधित था। वह \'राजाओं के दैवीय अधिकार के सिद्धांत\' के समान \'राजत्व के सिद्धांत\' को प्रतिपादित करने वाला पहला मुस्लिम शासक था। उसने सैन्य विभाग \"दीवान-ए-अर्ज़\" की स्थापना की।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which of the following team events is included in the Target Olympic Podium Scheme ?</p>",
                    question_hi: "<p>35. निम्नलिखित में से कौन-सा टीम इवेंट &lsquo;लक्ष्य ओलंपिक पोडियम योजना&rsquo; में शामिल है?</p>",
                    options_en: ["<p>Tennis</p>", "<p>Football</p>", 
                                "<p>Cricket</p>", "<p>Hockey</p>"],
                    options_hi: ["<p>टेनिस</p>", "<p>फुटबॉल</p>",
                                "<p>क्रिकेट</p>", "<p>हॉकी</p>"],
                    solution_en: "<p>35.(d) <strong>Hockey.</strong> Target Olympic Podium Scheme (TOPS): To improve India&rsquo;s performance at Olympics and Paralympics, the Ministry of Youth Affairs and Sports (MYAS) started the Target Olympic Podium Scheme (TOPS) in September 2014. The Ministry of Youth Affairs &amp; Sports is responsible for appointment of TOPS members with emphasis on ensuring representation from &lsquo;High-Priority&rsquo; sports (Archery, Badminton, Boxing, Hockey, Shooting and Wrestling).</p>",
                    solution_hi: "<p>35.(d) <strong>हॉकी।</strong> टारगेट ओलंपिक पोडियम योजना (TOPS): ओलंपिक और पैरालिंपिक में भारत के प्रदर्शन को बेहतर बनाने के लिए, युवा मामले और खेल मंत्रालय (MYAS) ने सितंबर 2014 में टारगेट ओलंपिक पोडियम योजना (TOPS) की शुरुआत की। युवा मामले और खेल मंत्रालय \'उच्च प्राथमिकता\' वाले खेलों (तीरंदाजी, बैडमिंटन, मुक्केबाजी, हॉकी, निशानेबाजी और कुश्ती) से प्रतिनिधित्व सुनिश्चित करने पर जोर देते हुए टॉप्स सदस्यों की नियुक्ति के लिए जिम्मेदार है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Articles 214 to 231 in Part VI of the Indian Constitution deal with the __________.</p>",
                    question_hi: "<p>36. भारतीय संविधान के भाग VI में अनुच्छेद 214 से 231 __________ से संबंधित हैं।</p>",
                    options_en: ["<p>High Courts</p>", "<p>District Courts</p>", 
                                "<p>Subordinate Courts</p>", "<p>Supreme Court</p>"],
                    options_hi: ["<p>उच्च न्यायालय</p>", "<p>जिला न्यायालय</p>",
                                "<p>अधीनस्थ न्यायालय</p>", "<p>उच्चतम न्यायालय</p>"],
                    solution_en: "<p>36.(a) <strong>High Courts</strong>. Articles 214 to 231 in Part VI of the Constitution deal with the organization, independence, jurisdiction, powers, and procedures of the high courts. Article 214 : Establishes that there shall be a High Court for each State. Articles 226 : Power of High Courts to issue certain writs. Articles 227 : Power of superintendence over all courts by the High Court. Articles 229 : Officers and servants and the expenses of High Courts. Articles 230 : Extension of jurisdiction of High Courts to Union territories.</p>",
                    solution_hi: "<p>36.(a) <strong>उच्च न्यायालय।</strong> संविधान के भाग VI में अनुच्छेद 214 से 231 उच्च न्यायालयों के संगठन, स्वतंत्रता, अधिकार क्षेत्र, शक्तियों और प्रक्रियाओं से संबंधित हैं। अनुच्छेद 214 : यह स्थापित करता है कि प्रत्येक राज्य के लिए एक उच्च न्यायालय होगा। अनुच्छेद 226 : कुछ रिट जारी करने की उच्च न्यायालयों की शक्ति। अनुच्छेद 227 : उच्च न्यायालय द्वारा सभी न्यायालयों पर अधीक्षण की शक्ति। अनुच्छेद 229 : उच्च न्यायालयों के अधिकारी और सेवक तथा व्यय। अनुच्छेद 230 : उच्च न्यायालयों के अधिकार क्षेत्र का केंद्र शासित प्रदेशों तक विस्तार।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which of the following ministries launched the PM Daksh scheme?</p>",
                    question_hi: "<p>37. पीएम दक्ष योजना का शुभारंभ निम्नलिखित में से किस मंत्रालय ने किया?</p>",
                    options_en: ["<p>Ministry of Education</p>", "<p>Ministry of Electronics and Information Technology</p>", 
                                "<p>Ministry of Social Justice and Empowerment</p>", "<p>Ministry of Food Processing Industries</p>"],
                    options_hi: ["<p>शिक्षा मंत्रालय</p>", "<p>इलेक्ट्रॉनिक्स और सूचना प्रौद्योगिकी मंत्रालय</p>",
                                "<p>सामाजिक न्याय और अधिकारिता मंत्रालय</p>", "<p>खाद्य प्रसंस्करण उद्योग मंत्रालय</p>"],
                    solution_en: "<p>37.(c) <strong>Ministry of Social Justice and Empowerment.</strong> The \"Pradhan Mantri Dakshta Aur Kushalta Sampann Hitgrahi\" (PM-DAKSH) scheme was launched in 2020-21, and is designed to uplift the marginalized sections of society by offering skill development programs. It targets SC (Scheduled Caste), OBC (Other Backward Classes), Economically Backward Classes, Denotified tribes, and Safai Mitras, providing upskilling, reskilling, short-term training, and entrepreneurial development courses.</p>",
                    solution_hi: "<p>37.(c) <strong>सामाजिक न्याय एवं अधिकारिता मंत्रालय</strong>। \"प्रधानमंत्री दक्षता और कुशलता संपन्न हितग्राही\" (PM-DAKSH) योजना 2020-21 में शुरू की गई थी, और इसे कौशल विकास कार्यक्रमों की पेशकश करके समाज के हाशिए पर पड़े वर्गों के उत्थान के लिए बनाया गया है। यह SC (अनुसूचित जाति), OBC (अन्य पिछड़ा वर्ग), आर्थिक रूप से पिछड़े वर्ग, अस्वीकृत जनजातियों और सफाई मित्रों को लक्षित करता है, जो अपस्किलिंग, रीस्किलिंग, अल्पकालिक प्रशिक्षण और उद्यमशीलता विकास पाठ्यक्रम प्रदान करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. ____________ is often touted as the \"Tansen of the 20th century,\" century, this musical genius blended the best of classical music and created a unique style of his own.</p>",
                    question_hi: "<p>38. ___________ को अक्सर 20वीं शताब्दी के तानसेन के रूप में जाना जाता है, संगीत के इस प्रतिभाशाली व्यक्ति ने सर्वश्रेष्ठ शास्त्रीय संगीत का मिश्रण किया और अपनी खुद की एक अनूठी शैली बनाई।</p>",
                    options_en: ["<p>Ustad Ali Baksh Khan</p>", "<p>Bade Ghulam Ali Khan</p>", 
                                "<p>Mubarak Ali Khan</p>", "<p>Barkat Ali Khan</p>"],
                    options_hi: ["<p>उस्ताद अली बख्श खान</p>", "<p>बड़े गुलाम अली खान</p>",
                                "<p>मुबारक अली खान</p>", "<p>बरकत अली खान</p>"],
                    solution_en: "<p>38.(b) <strong>Bade Ghulam Ali Khan. </strong>He was an Indian vocalist from the Kasur Patiala Gharana. His Award: Padma Bhushan (1962), and Sangeet Natak Akademi Award (1962).</p>",
                    solution_hi: "<p>38.(b) <strong>बड़े गुलाम अली खान</strong>। वह कसूर पटियाला घराने के एक भारतीय गायक थे। उनके पुरस्कार: पद्म भूषण (1962), और संगीत नाटक अकादमी पुरस्कार (1962)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. How many ICC World Cups has India won in ODI cricket?</p>",
                    question_hi: "<p>39. भारत ने ओ.डी.आई. (ODI) में कितने आई.सी.सी. (ICC) विश्व कप जीते हैं?</p>",
                    options_en: ["<p>1</p>", "<p>3</p>", 
                                "<p>2</p>", "<p>4</p>"],
                    options_hi: ["<p>1</p>", "<p>3</p>",
                                "<p>2</p>", "<p>4</p>"],
                    solution_en: "<p>39.(c) <strong>2</strong>. 1983 ICC Cricket World Cup: India won its first World Cup under the captaincy of Kapil Dev. The final was held at Lord\'s Cricket Ground in England, where India defeated the West Indies to claim the title. 2011 ICC Cricket World Cup: India won its second World Cup under the captaincy of Mahendra Singh Dhoni. The tournament was co-hosted by India, Sri Lanka, and Bangladesh, and India defeated Sri Lanka in the final, which took place at Wankhede Stadium in Mumbai.</p>",
                    solution_hi: "<p>39.(c) <strong>2</strong>. 1983 ICC क्रिकेट विश्व कप: कपिल देव की कप्तानी में भारत ने अपना पहला विश्व कप जीता। फाइनल इंग्लैंड के लॉर्ड्स क्रिकेट ग्राउंड पर हुआ था, जहाँ भारत ने वेस्टइंडीज को हराकर खिताब अपने नाम किया था। 2011 ICC क्रिकेट विश्व कप: महेंद्र सिंह धोनी की कप्तानी में भारत ने अपना दूसरा विश्व कप जीता। टूर्नामेंट की मेजबानी भारत, श्रीलंका और बांग्लादेश ने की थी और भारत ने फाइनल में श्रीलंका को हराया था, जो मुंबई के वानखेड़े स्टेडियम में हुआ था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Who first stated the principle of refraction that postulates every substance has a specific bending ratio, \"refractive index\"?</p>",
                    question_hi: "<p>40. किसने सर्वप्रथम अपवर्तन के उस सिद्धांत को प्रतिपादित किया था जो यह स्वतःसिद्ध मानता है कि प्रत्येक पदार्थ का एक विशिष्ट बंकन अनुपात - \'अपवर्तक सूचकांक\' होता है?</p>",
                    options_en: ["<p>Dennis Gabor</p>", "<p>Thomas Young</p>", 
                                "<p>Willebrord Snellius</p>", "<p>David Brewster</p>"],
                    options_hi: ["<p>डेनिस गैबोर (Dennis Gabor)</p>", "<p>थॉमस यंग (Thomas Young)</p>",
                                "<p>विलेबरोर्ड स्नेलियस (Willebrord Snellius)</p>", "<p>डेविड ब्रूस्टर (David Brewster)</p>"],
                    solution_en: "<p>40.(c) <strong>Willebrord Snellius.</strong> He was a Dutch astronomer and mathematician, commonly known as Snell. The Snell&rsquo;s law of refraction states that: The incident ray, the refracted ray and the normal at the point of incidence, all lie in the same plane. The ratio of the sine of the angle of incidence to the sine of the angle of refraction is constant for the pair of the given media. Dennis Gabor: known for inventing holography in 1947. Thomas Young : Demonstrated the wave nature of light.</p>",
                    solution_hi: "<p>40.(c) <strong>विलेबरोर्ड स्नेलियस</strong> (Willebrord Snellius)। वह एक डच खगोलशास्त्री और गणितज्ञ थे, जिन्हें सामान्यतः स्नेल के नाम से जाना जाता था। स्नेल के अपवर्तन के नियम के अनुसार: आपतित किरण, अपवर्तित किरण और आपतन बिंदु पर अभिलंब, सभी एक ही तल में होते हैं। आपतन कोण की ज्या (sine) और अपवर्तन कोण की ज्या (sine) का अनुपात दिए गए माध्यमों के युग्म के लिए स्थिरांक है। डेनिस गैबोर: 1947 में होलोग्राफी का आविष्कार करने के लिए जाने जाते हैं। थॉमस यंग: प्रकाश की तरंग प्रकृति का प्रदर्शन किया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Coir conductor Coomi Nariman Wadia was awarded which of the following awards by the Government of India in 2023?</p>",
                    question_hi: "<p>41. कॉयर कंडक्टर कूमी नरीमन वाडिया को 2023 में भारत सरकार द्वारा निम्नलिखित में से किस पुरस्कार से सम्मानित किया गया?</p>",
                    options_en: ["<p>Padma Bhushan</p>", "<p>Padma Vibhushan</p>", 
                                "<p>Bharat Ratna</p>", "<p>Padma Shri</p>"],
                    options_hi: ["<p>पद्म भूषण</p>", "<p>पद्म विभूषण</p>",
                                "<p>भारत रत्न</p>", "<p>पद्म श्री</p>"],
                    solution_en: "<p>41.(d) <strong>Padma Shri</strong>. Coomi Nariman Wadia is a music conductor of Mumbai&rsquo;s Paranjoti Academy Chorus.</p>",
                    solution_hi: "<p>41.(d) <strong>पद्मश्री।</strong> कूमी नरीमन वाडिया मुंबई के परंजोती अकादमी कोरस की संगीत मार्गदर्शक हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Who published Systema Naturae in 1735 classifying the three kingdoms of nature and outlining the sexual system for the classification of plants?</p>",
                    question_hi: "<p>42. 1735 में प्रकृति के तीन जगतों को वर्गीकृत करते हुए और पादपों के वर्गीकरण के लिए लैंगिक प्रणाली को रेखांकित करते हुए सिस्टेमा नेचुरे (Systema Naturae) का प्रकाशन किसने किया था?</p>",
                    options_en: ["<p>Carl Woese</p>", "<p>Robert Whittaker</p>", 
                                "<p>Ernst Haeckel</p>", "<p>Carolus Linnaeus</p>"],
                    options_hi: ["<p>कार्ल वोइज़ (Carl Woese)</p>", "<p>रॉबर्ट व्हिटेकर (Robert Whittaker)</p>",
                                "<p>अर्न्स्ट हेकेल (Ernst Haeckel)</p>", "<p>कैरोलस लिनिअस (Carolus Linnaeus)</p>"],
                    solution_en: "<p>42.(d) <strong>Carolus Linnaeus.</strong> He was a Swedish biologist and physician who formalised binomial nomenclature, the modern system of naming organisms. He is known as the \"father of modern taxonomy&rdquo;. Carl Woese : Developed the Three-Domain System of classification (Archaea, Bacteria, Eukarya). Robert Whittaker : Proposed the Five Kingdom System of classification (Monera, Protista, Fungi, Plantae, Animalia). Ernst Haeckel : Introduced the term \"ecology\" and contributed to evolutionary theory.</p>",
                    solution_hi: "<p>42.(d) <strong>कैरोलस लिनिअस </strong>(Carolus Linnaeus)। वे एक स्वीडिश जीवविज्ञानी और चिकित्सक थे जिन्होंने जीवों के नामकरण की आधुनिक प्रणाली द्विपद नामकरण को औपचारिक रूप दिया। उन्हें \"आधुनिक वर्गीकरण के जनक\" के रूप में जाना जाता है। कार्ल वोइज़: वर्गीकरण की तीन-डोमेन प्रणाली विकसित की (आर्किया, बैक्टीरिया, यूकेरिया)। रॉबर्ट व्हिटेकर: वर्गीकरण की पाँच जगत प्रणाली का प्रस्ताव रखा (मोनेरा, प्रोटिस्टा, कवक, प्लांटे, एनिमेलिया)। अर्न्स्ट हेकेल: \"पारिस्थितिकी\" शब्द की शुरुआत की और विकासवादी सिद्धांत में योगदान दिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. When a person is NOT paying anything for public goods, this is known as ________.</p>",
                    question_hi: "<p>43. जब कोई व्यक्ति सार्वजनिक वस्तुओं के लिए कुछ भी भुगतान नहीं कर रहा है, तो इसे _______ के रूप में जाना जाता है।</p>",
                    options_en: ["<p>Non-excludable</p>", "<p>Rivalrous</p>", 
                                "<p>Free rider</p>", "<p>Private good</p>"],
                    options_hi: ["<p>गैर-बहिष्कृत</p>", "<p>प्रतिद्वंद्विता</p>",
                                "<p>फ्री राइडर</p>", "<p>निजी वस्तुएं</p>"],
                    solution_en: "<p>43.(c)<strong> Free rider.</strong> In the context of public goods, a free rider is able to use or enjoy the benefits of the public good without contributing to its cost. This is possible because of the nature of public goods, which are typically non-excludable. Example: Using public parks without paying for them.</p>",
                    solution_hi: "<p>43.(c) <strong>फ्री राइडर।</strong> सार्वजनिक वस्तुओं के संदर्भ में, एक फ्री राइडर सार्वजनिक वस्तु की लागत में योगदान किए बिना उसके लाभों का उपयोग या आनंद लेने में सक्षम होता है। यह सार्वजनिक वस्तुओं की प्रकृति के कारण संभव है, जो सामान्यतः गैर-बहिष्कृत होती हैं। उदाहरण: बिना भुगतान किए सार्वजनिक पार्कों का उपयोग करना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. In 1936, The Independent Labour Party was founded by _______.</p>",
                    question_hi: "<p>44. 1936 में स्वतंत्र लेबर पार्टी की स्थापना _________ के द्वारा की गई थी।</p>",
                    options_en: ["<p>Mohan Singh Bhakhna</p>", "<p>BR Ambedkar</p>", 
                                "<p>Bal Gangadhar Tilak</p>", "<p>Bhagat Singh</p>"],
                    options_hi: ["<p>मोहन सिंह भखना</p>", "<p>बी.आर. अंबेडकर</p>",
                                "<p>बाल गंगाधर तिलक</p>", "<p>भगत सिंह</p>"],
                    solution_en: "<p>44.(b) <strong>BR Ambedkar</strong> was an Indian jurist and social reformer who served as the first Minister of Law and Justice and was the chief architect of the Constitution of India. He was awarded the Bharat Ratna posthumously in 1990. Organizations and Founders: Swatantra Shramik Party (B.R. Ambedkar), Swarajya Party (Motilal Nehru and Chittaranjan Das), Muslim League (Agha Khan &amp; Salimullah), Indian National Congress (A.O.Hume).</p>",
                    solution_hi: "<p>44.(b) <strong>बी.आर. अंबेडकर</strong> एक भारतीय कानूनविद और समाज सुधारक थे, जिन्होंने कानून और न्याय के प्रथम मंत्री के रूप में कार्य किया और वे भारत के संविधान के मुख्य निर्माता थे। उन्हें 1990 में मरणोपरांत भारत रत्न से सम्मानित किया गया था। संगठन एवं संस्थापक: स्वतंत्र श्रमिक पार्टी (बी.आर. अंबेडकर), स्वराज्य पार्टी (मोतीलाल नेहरू और चित्तरंजन दास), मुस्लिम लीग (आगा खान और सलीमुल्लाह), भारतीय राष्ट्रीय कांग्रेस (ए.ओ. ह्यूम)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following is NOT a tributary of the Ganga?</p>",
                    question_hi: "<p>45. निम्नलिखित में से कौन-सी गंगा की सहायक नदी नहीं है?</p>",
                    options_en: ["<p>Yamuna</p>", "<p>Gandak</p>", 
                                "<p>Kosi</p>", "<p>Lohit</p>"],
                    options_hi: ["<p>यमुना</p>", "<p>गंडक</p>",
                                "<p>कोसी</p>", "<p>लोहित</p>"],
                    solution_en: "<p>45.(d) <strong>Lohit</strong> is a tributary to the Brahmaputra River. Ganga river: It originates in the Gangotri Glacier in the Himalayas in the Uttarkashi district of Uttarakhand. Tributaries include: Yamuna, Son, Gomti, Ghaghra, Gandak, and Kosi rivers.</p>",
                    solution_hi: "<p>45.(d) <strong>लोहित</strong> ब्रह्मपुत्र नदी की एक सहायक नदी है। गंगा नदी: इसका उद्गम उत्तराखंड के उत्तरकाशी जिले में हिमालय के गंगोत्री ग्लेशियर से होता है। सहायक नदियों में शामिल हैं: यमुना, सोन, गोमती, घाघरा, गंडक और कोसी नदियाँ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. The most common rainfall measurement is the total rainfall depth during a given period, which is expressed in ________.</p>",
                    question_hi: "<p>46. वर्षा का सबसे आम मापन एक निश्चित अवधि के दौरान कुल वर्षा गहराई है, जिसे ________ में व्यक्त किया जाता है।</p>",
                    options_en: ["<p>Kilopascal (kPa)</p>", "<p>Millimetres (mm)</p>", 
                                "<p>Hectopascal (hPa)</p>", "<p>Millibar (mbar)</p>"],
                    options_hi: ["<p>किलोपास्कल (kPa)</p>", "<p>मिलीमीटर (mm)</p>",
                                "<p>हेक्टोपास्कल (hPa)</p>", "<p>मिलीबार (mbar)</p>"],
                    solution_en: "<p>46.(b) <strong>Millimetres (mm).</strong> Rainfall is measured using a rain gauge, also known as a pluviometer or hyetometer. Kilopascal (kPa): A unit of pressure equal to 1,000 pascals. Millibar (mbar): A unit of atmospheric pressure equal to 100 pascals, commonly used in meteorology.</p>",
                    solution_hi: "<p>46.(b) <strong>मिलीमीटर (mm)।</strong> वर्षा को रेन गेज का उपयोग करके मापा जाता है, जिसे प्लूवियोमीटर या हाइटोमीटर भी कहा जाता है। किलोपास्कल (kPa): दाब का मात्रक है जो 1,000 पास्कल के बराबर होती है। मिलीबार (mbar): वायुमंडलीय दाब का मात्रक है जो 100 पास्कल के बराबर होती है, जिसका उपयोग सामान्यतः मौसम विज्ञान में किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Kati Bihu is celebrated in which of the following months in Assam?</p>",
                    question_hi: "<p>47. निम्नलिखित में से किस महीने में असम में कटि बिहू मनाया जाता है?</p>",
                    options_en: ["<p>March</p>", "<p>October</p>", 
                                "<p>January</p>", "<p>November</p>"],
                    options_hi: ["<p>मार्च</p>", "<p>अक्टूबर</p>",
                                "<p>जनवरी</p>", "<p>नवंबर</p>"],
                    solution_en: "<p>47.(b) <strong>October.</strong> Kati Bihu is one of the three Bihu festivals celebrated in Assam. Kati means to cut and this day marks the time of relocation of rice saplings. It is also known as Kongali Bihu. There are another two Bihu festivals celebrated in Assam - Bhogali (Magh Bihu) is observed in January and Rongali (Bohag Bihu) is observed in April. Other festivals of Assam: Baishagu, Ali-Ai-Ligang, Baikho, Rongker, Bohaggiyo Bishu, Ambubashi Mela and Jonbill Mela.</p>",
                    solution_hi: "<p>47.(b) <strong>अक्टूबर।</strong> कटि बिहू असम में मनाए जाने वाले तीन बिहू त्योहारों में से एक है। कटि का अर्थ है काटना और यह दिन धान (rice) के पौधों को दूसरे स्थान पर लगाने का दिन है। इसे कोंगाली बिहू के नाम से भी जाना जाता है। असम में दो और बिहू त्योहार मनाए जाते हैं - भोगाली (माघ बिहू) जनवरी में मनाया जाता है और रोंगाली (बोहाग बिहू) अप्रैल में मनाया जाता है। असम के अन्य त्योहार: बैशागु, अली-ऐ-लिगांग, बैखो, रोंगकर, बोहागियो बिशु, अम्बुबाची मेला और जॉनबिल मेला।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. With reference to Ajivikas, consider the following statements:<br>a) Makkhali Gosala was the prominent teacher of this sect.<br>b) Ajivikas believed in fatalism. <br>Which of the above statements is/are correct?</p>",
                    question_hi: "<p>48. आजीवकों के संदर्भ में, निम्नलिखित कथनों पर विचार कीजिए।<br>a) मक्खलि गोशाल इस संप्रदाय के प्रमुख शिक्षक थे।<br>b) आजीवक भाग्यवाद में विश्वास करते थे।<br>उपर्युक्त कथनों में से कौन-सा/कौन-से कथन सही है/हैं?</p>",
                    options_en: ["<p>Both a and b</p>", "<p>Neither a nor b</p>", 
                                "<p>Only b</p>", "<p>Only a</p>"],
                    options_hi: ["<p>a और b दोनों</p>", "<p>न तो a और न ही b</p>",
                                "<p>केवल b</p>", "<p>केवल a</p>"],
                    solution_en: "<p>48.(a) <strong>Both a and b. Ajivika</strong> - It was a school of thought that developed in India during the same time as Jainism and Buddhism. It is one of the schools under Nastika philosophy (it does not believe in the authority of Vedas). It was founded by Goshala Maskariputra. The sect is seen as professing total determinism in the transmigration of souls, or series of rebirths.</p>",
                    solution_hi: "<p>48.(a) <strong>a और b दोनों</strong>। आजीविक - यह एक विचारधारा थी जो भारत में जैन धर्म और बौद्ध धर्म के साथ ही विकसित हुई थी। यह नास्तिक दर्शन के अंतर्गत आने वाले विद्यालयों में से एक है (यह वेदों की प्रामाणिकता में विश्वास नहीं करता)। इसकी स्थापना गोशाल मस्करीपुत्र ने की थी। इस संप्रदाय को आत्माओं के स्थानांतरण या पुनर्जन्म की श्रृंखला में पूर्ण नियतिवाद का समर्थक माना जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Being the President of the Indian National Congress, who among the following had called for complete independence from the British Raj in 1929?</p>",
                    question_hi: "<p>49. भारतीय राष्ट्रीय कांग्रेस के अध्यक्ष होने के नाते, निम्नलिखित में से किसने 1929 में ब्रिटिश राज से पूर्ण स्वतंत्रता का आह्वान किया था?</p>",
                    options_en: ["<p>Jawaharlal Nehru</p>", "<p>Subhas Chandra Bose</p>", 
                                "<p>Sardar Vallabhbhai Patel</p>", "<p>Mahatma Gandhi</p>"],
                    options_hi: ["<p>जवाहरलाल नेहरू</p>", "<p>सुभाष चंद्र बोस</p>",
                                "<p>सरदार वल्लभभाई पटेल</p>", "<p>महात्मा गांधी</p>"],
                    solution_en: "<p>49.(a) <strong>Jawaharlal Nehru.</strong> He was an Indian freedom fighter who later became the first Prime Minister of India. In December 1929, he was chosen as the President of the Indian National Congress during its annual session in Lahore. At this event, he raised the tricolour flag on the banks of the Ravi River.</p>",
                    solution_hi: "<p>49.(a) <strong>जवाहरलाल नेहरू।</strong> वे एक भारतीय स्वतंत्रता सेनानी थे जो बाद में भारत के प्रथम प्रधानमंत्री बने। दिसंबर 1929 में लाहौर में अपने वार्षिक सत्र के दौरान उन्हें भारतीय राष्ट्रीय कांग्रेस का अध्यक्ष चुना गया। इस अवसर पर उन्होंने रावी नदी के तट पर तिरंगा झंडा फहराया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which Article in the fundamental duty is invoked when you see in a private party that the Indian flag has been used as a covering for the speaker&rsquo;s desk?</p>",
                    question_hi: "<p>50. जब आप किसी निजी पार्टी में देखते हैं कि भारतीय ध्वज का उपयोग वक्ता की मेज को ढकने के लिए किया गया है, तो मौलिक कर्तव्यों का कौन-सा अनुच्छेद लागू होता है?</p>",
                    options_en: ["<p>Article 51A (c)</p>", "<p>Article 51A (d)</p>", 
                                "<p>Article 51A (a)</p>", "<p>Article 51A (b)</p>"],
                    options_hi: ["<p>अनुच्छेद 51A (c)</p>", "<p>अनुच्छेद 51A (d)</p>",
                                "<p>अनुच्छेद 51A (a)</p>", "<p>अनुच्छेद 51A (b)</p>"],
                    solution_en: "<p>50.(c) <strong>Article 51A (a)</strong>. Article 51A in Part IV-A of the Indian Constitution lists eleven Fundamental Duties. Here are a few of them: Article 51A (b): To respect and follow the noble ideals that inspired India\'s freedom struggle. Article 51A (c): To uphold and protect India\'s sovereignty, unity, and integrity. Article 51A (d): To defend the country and serve the nation when needed.</p>",
                    solution_hi: "<p>50.(c) <strong>अनुच्छेद 51A (a)</strong>। भारतीय संविधान के भाग IV-A में अनुच्छेद 51A में ग्यारह मौलिक कर्तव्य सूचीबद्ध हैं। उनमें से कुछ इस प्रकार हैं: अनुच्छेद 51A (b): भारत के स्वतंत्रता संग्राम को प्रेरित करने वाले महान आदर्शों का सम्मान करना और उनका पालन करना। अनुच्छेद 51A (c): भारत की संप्रभुता, एकता और अखंडता को बनाए रखना और उसकी रक्षा करना। अनुच्छेद 51A (d ): देश की रक्षा करना और आवश्यकता पड़ने पर राष्ट्र की सेवा करना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If sin A - cos A = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>, then the value of sin A. cos A is:</p>",
                    question_hi: "<p>51. यदि sin A - cos A <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math> हो, तो sin A. cos A का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>51.(c) sin A - cos A = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>Taking square both side,<br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>A + cos<sup>2</sup>A - 2sinA.cosA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>2</mn><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mn>4</mn></mfrac></math><br>1 - 2sinA.cosA = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>2sinA.cosA = 1 -&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>2sinA.cosA = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>sinA.cosA = <math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn><mo>-</mo><mn>1</mn></msqrt></mrow><mn>8</mn></mfrac></math></p>",
                    solution_hi: "<p>51.(c) sin A - cos A = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>दोनों तरफ वर्ग करने पर ,<br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi><mi>&#160;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>A + cos<sup>2</sup>A - 2sinA.cosA = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>2</mn><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mn>4</mn></mfrac></math><br>1 - 2sinA.cosA = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>2sinA.cosA = 1 -&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>2sinA.cosA = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>sinA.cosA = <math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>6</mn><mo>-</mo><mn>1</mn></msqrt></mrow><mn>8</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The following bar chart shows the year-wise percentage hike in the fuel price with respect to the previous year<br><strong id=\"docs-internal-guid-92059e76-7fff-c0db-2369-055515e18e66\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdzfDCxPbi3v2egElLoPU8GsdzBngXswgnvGwLM1BECnCWgcYG4H75EYvfcmAb6CMfuN6VoM5QdcJkFzNJyR0RovCvIxNgAVGqtf9oPTiiMYT-NrUEz68eM2dBslLIo57f-11tnBA28TEvwngkaaGWnj7cG?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"344\" height=\"296\"></strong><br>In how many years did the fuel prices NOT rise?</p>",
                    question_hi: "<p>52. निम्न बार-चार्ट पिछले वर्ष के संदर्भ में ईंधन की कीमत में वर्ष-वार प्रतिशत वृद्धि दर्शाता है। <br><strong id=\"docs-internal-guid-ccdf2ea9-7fff-fc45-e508-c50cee69f2e1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcqNgWxyohFSWYY4QJ0JjqPQrwZ7hJNfZVa-UwMkZtxehe0xezEPPOvJh83teI6vmh9DARY2gZBtDHAgevXb605bz5sffOUndWBXUcbIioKsBdKpuOcq5xHieZxMIgCrl7dLqX2EL-vrRoBgQwNLboyYLds?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"317\" height=\"274\"></strong><br>कितने वर्षों में ईंधन की कीमतों में कोई वृद्धि नहीं हुई?</p>",
                    options_en: ["<p>0</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>5</p>"],
                    options_hi: ["<p>0</p>", "<p>1</p>",
                                "<p>2</p>", "<p>5</p>"],
                    solution_en: "<p>52.(a)<br>From the bar chart we can see that every year there is a rise in fuel prices.</p>",
                    solution_hi: "<p>52.(a)<br>बार चार्ट से हम देख सकते हैं कि हर साल ईंधन की कीमतों में बढ़ोतरी हो रही है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The least number which must be subtracted from 7278745 so as to obtain a sum divisible by 11 is:</p>",
                    question_hi: "<p>53. 11 से विभाज्य संख्या प्राप्त करने के लिए 7278745 में से घटाई जाने वाली छोटी से छोटी संख्या कौन-सी है?</p>",
                    options_en: ["<p>3</p>", "<p>1</p>", 
                                "<p>5</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>1</p>",
                                "<p>5</p>", "<p>2</p>"],
                    solution_en: "<p>53.(b) Divisible of 11 : if the difference between the sum of the digits at odd places and the sum of the digits at even places is 0 or multiple of 11<br>7278745<br><math display=\"inline\"><mo>&#8658;</mo></math> (7 + 7 + 7 + 5) - (2 + 8 + 4)&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> 26 - 14 = 12 <br>It is clear that if 1 is subtracted from this number (786452), then this number will become divisible by 11.</p>",
                    solution_hi: "<p>53.(b) 11 की विभाज्यता: यदि विषम स्थानों पर अंकों के योग और सम स्थानों पर अंकों के योग के बीच का अंतर 0 या 11 का गुणक है<br>7278745<br><math display=\"inline\"><mo>&#8658;</mo></math> (7 + 7 + 7 + 5) - (2 + 8 + 4)&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> 26 - 14 = 12<br>स्पष्ट है कि यदि इस संख्या (786452) में से 1 घटा दिया जाये तो यह संख्या 11 से विभाज्य हो जायेगी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. A motorboat, whose speed is 15 km/h in still water, goes 50 km downstream and comes back in a total of 7 hours 30 minutes. The speed of the stream (in km/h) is:</p>",
                    question_hi: "<p>54. एक मोटरबोट, जिसकी स्थिर जल में चाल 15 km/h है, को धारा के अनुकूल 50 km जाने और वापस आने में 7 घंटे 30 मिनट का समय लगता है। धारा की चाल (km/h में) की गणना करें।</p>",
                    options_en: ["<p>9</p>", "<p>5</p>", 
                                "<p>11</p>", "<p>7</p>"],
                    options_hi: ["<p>9</p>", "<p>5</p>",
                                "<p>11</p>", "<p>7</p>"],
                    solution_en: "<p>54.(b) Let the speed of a stream be <math display=\"inline\"><mi>x</mi></math> km/h<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>&#160;</mo><mo>-</mo><mi>x</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>x</mi></mrow></mfrac></math> = 7.5<br>In this type of question by using hit and trial method,<br>Put the value of x&nbsp;= 5, <br>LHS = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>15</mn><mi>&#160;</mi><mo>-</mo><mi>x</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>x</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>15</mn><mi>&#160;</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>10</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>20</mn></mfrac></math> = 5 + 2.5 = 7.5 = RHS<br>Value of <math display=\"inline\"><mi>x</mi></math> satisfies the equation,<br>So, the speed of a stream is 5 km/h</p>",
                    solution_hi: "<p>54.(b) माना धारा की गति x किमी/घंटा है<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>&#160;</mo><mo>-</mo><mi>x</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>x</mi></mrow></mfrac></math> = 7.5<br>इस प्रकार के प्रश्न में हिट एण्ड ट्रायल विधि का प्रयोग करे,<br><math display=\"inline\"><mi>x</mi></math> = 5 रखने पर ,<br>LHS = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>15</mn><mi>&#160;</mi><mo>-</mo><mi>x</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>x</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>15</mn><mi>&#160;</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>15</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>10</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>20</mn></mfrac></math> = 5 + 2.5 = 7.5 = RHS<br>x का मान समीकरण को संतुष्ट करता है,<br>तो, धारा की गति 5 किमी/घंटा है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The length, breadth and height of a room is 15 m, 9 m, and 5 m, respectively. From each can of paint 40 square metre of area is painted. How many cans of paint will be needed to paint only the walls of the room?</p>",
                    question_hi: "<p>55. एक कमरे की लंबाई, चौड़ाई और ऊँचाई क्रमशः 15 m, 9 m और 5 m है। पेंट के एक डिब्बे से 40 वर्ग-मीटर क्षेत्रफल को पेंट किया जा सकता है। केवल कमरे की दीवारों को पेंट करने के लिए पेंट के कितने डिब्बों की आवश्यकता होगी?</p>",
                    options_en: ["<p>12</p>", "<p>6</p>", 
                                "<p>4</p>", "<p>8</p>"],
                    options_hi: ["<p>12</p>", "<p>6</p>",
                                "<p>4</p>", "<p>8</p>"],
                    solution_en: "<p dir=\"ltr\">55.(b) surface area of four walls = 2h(l + b)<br>= 2 &times; 5(15 + 9)<br>= 10(24) =&nbsp; 240 m<sup>2</sup><br>So,<br>The number of cans = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>40</mn></mfrac></math> = 6&nbsp;</p>",
                    solution_hi: "<p dir=\"ltr\">55.(b) चारों दीवारों का पृष्ठीय क्षेत्रफल= 2h(l + b)<br>= 2 &times; 5(15 + 9)<br>= 10(24) =&nbsp; 240 m<sup>2</sup><br>इसलिए ,<br>डिब्बों की संख्या = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>40</mn></mfrac></math> = 6&nbsp;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "56. The distance between the longest chord and the centre of a circle is:",
                    question_hi: "56. सबसे लंबी जीवा और वृत्त के केंद्र के बीच की दूरी _______ होती है।",
                    options_en: [" Equal to zero unit", " Equal to the diameter", 
                                " Equal to half of the radius", " Equal to the radius"],
                    options_hi: [" शून्य इकाई के बराबर", " व्यास के बराबर",
                                " त्रिज्या के आधे के बराबर", " त्रिज्या के बराबर"],
                    solution_en: "56.(a) We know the longest chord of the circle is also called the diameter of the circle.<br />So, the distance between the longest chord and centre of the circle = 0 unit ",
                    solution_hi: "56.(a) हम जानते हैं कि वृत्त की सबसे लंबी जीवा को वृत्त का व्यास भी कहा जाता है।<br />तो, सबसे लंबी जीवा और वृत्त के केंद्र के बीच की दूरी = 0 इकाई होगी<br /> ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. The marked price of 55 items was equal to the cost price of 99 items. The selling price of 56 items was equal to the marked price of 35 items. Calculate the profit or loss percentage from the sale of each item.</p>",
                    question_hi: "<p>57. 55 वस्तुओं का अंकित मूल्य 99 वस्तुओं के क्रय मूल्य के बराबर था। 56 वस्तुओं का विक्रय मूल्य 35 वस्तुओं के अंकित मूल्य के बराबर था। प्रत्येक वस्तु की बिक्री से होने वाले लाभ या हानि प्रतिशत की गणना करें।</p>",
                    options_en: ["<p>15% profit</p>", "<p>12.25% profit</p>", 
                                "<p>12.5% profit</p>", "<p>12.5% loss</p>"],
                    options_hi: ["<p>15% लाभ</p>", "<p>12.25% लाभ</p>",
                                "<p>12.5% लाभ</p>", "<p>12.5% हानि</p>"],
                    solution_en: "<p>57.(c) MP of 55 items = CP of 99 items<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>MP</mi><mi>CP</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>99</mn><mn>55</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math><br>SP of 56 items = MP of 35 items<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>SP</mi><mi>MP</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>56</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math><br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> CP&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;MP&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 9<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 8&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 8&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 5 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ______________________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;40&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;72&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;45<br>Required profit % = <math display=\"inline\"><mfrac><mrow><mn>45</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>40</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> &times; 100 = 12.5%</p>",
                    solution_hi: "<p>57.(c) 55 वस्तुओं का अंकित मूल्य = 99 वस्तुओं का CP<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>MP</mi><mi>CP</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>99</mn><mn>55</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math><br>56 वस्तुओं का विक्रय मूल्य = 35 वस्तुओं का अंकित मूल्य <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>SP</mi><mi>MP</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>56</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math><br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> क्रय मूल्य : अंकित मूल्य : विक्रय मूल्य <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 9<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;8&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;5 <br>&nbsp; &nbsp; &nbsp; &nbsp;-----------------------------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 40&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 72&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;45<br>आवश्यक लाभ % = <math display=\"inline\"><mfrac><mrow><mn>45</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>40</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> &times; 100 = 12.5%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. In an election between two candidates, P received 42% of the valid votes and Q won by 45,360 votes. 20% people did not cast their vote. If 10% of the votes cast were found invalid, what is the total number of votes registered in the poll booth?</p>",
                    question_hi: "<p>58. दो उम्मीदवारों के बीच हुए एक चुनाव में, P को कुल पड़े वैध मतों के 42% मत मिले और Q ने 45,360 मतों से जीत हासिल की। 20% लोगों ने मत नहीं डाला। यदि डाले गए 10% मत अवैध पाए गए, तो मतदान केंद्र में पंजीकृत मतों की कुल संख्या ज्ञात करें।</p>",
                    options_en: ["<p>3,93,750</p>", "<p>3,59,000</p>", 
                                "<p>3,93,600</p>", "<p>3,53,790</p>"],
                    options_hi: ["<p>3,93,750</p>", "<p>3,59,000</p>",
                                "<p>3,93,600</p>", "<p>3,53,790</p>"],
                    solution_en: "<p>58.(a) Let the total votes be 100%<br><strong id=\"docs-internal-guid-84bd94a8-7fff-67dc-f855-41f55ad17b55\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf5LEP4JyJsjPYb5qHhUDcb3K4h4GR1e9b0diyUc2dsLVYUz_RzIDk3jqOIsMQgRgP3ML4BsLQ3xlDDl4VTKFjK-TSqzQw1uvHPq9a_AlqPuOwvOIWdApUvAYz0RpMMl4OPxFjPmaxMlxaRgff_8JCeTF4?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"174\" height=\"203\"></strong><br><math display=\"inline\"><mo>&#8658;</mo></math> (41.76% - 30.24%) = 11.52% = 45360<br><math display=\"inline\"><mo>&#8658;</mo></math> 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45360</mn><mrow><mn>11</mn><mo>.</mo><mn>52</mn></mrow></mfrac></math> &times; 100 = 393750</p>",
                    solution_hi: "<p>58.(a) माना कुल वोट = 100% <br><strong id=\"docs-internal-guid-61849a15-7fff-ea0b-fff0-434cacb0b69e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcntjg9IRxXJPt3hS1MJ5wG6WysXzE2gdyd50Bs_ixfNL7Hd_UR9WCNGyjhPr5k-a_c30gcAf4IaPWiLjRngTkrUtOeSbaNylICKwPRaz0Rje1b3dfq6eDvzB995jNoMqgvtB3dm4v-Xz5Mhp8MFMc_-dqY?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"158\" height=\"196\"></strong><br><math display=\"inline\"><mo>&#8658;</mo></math> (41.76% - 30.24%) = 11.52% = 45360<br><math display=\"inline\"><mo>&#8658;</mo></math> 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45360</mn><mrow><mn>11</mn><mo>.</mo><mn>52</mn></mrow></mfrac></math> &times; 100 = 393750</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Hema is 20 years old and her sister Neha is 30 years old. How many years ago were their ages in the ratio of 3 : 5 ?</p>",
                    question_hi: "<p>59. हेमा 20 वर्ष की है और उनकी बहन नेहा 30 वर्ष की है। कितने वर्ष पहले उनकी आयु 3 : 5 के अनुपात में थी?</p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>7</p>", "<p>5</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>7</p>", "<p>5</p>"],
                    solution_en: "<p>59.(d) According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>30</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 100 - 5x = 90 - 3x<br><math display=\"inline\"><mo>&#8658;</mo></math> 10 = 2x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5 years</p>",
                    solution_hi: "<p>59.(d) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>30</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 100 - 5x = 90 - 3x<br><math display=\"inline\"><mo>&#8658;</mo></math> 10 = 2x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5 वर्ष</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. AB is the chord of the circle with centre O. A line segment DOC originating from a point D on the circumference of the circle in major segment meets AB produced at C such that BC = OD. If angle BCO = 30&deg;, then angle AOD is:</p>",
                    question_hi: "<p>60. AB केंद्र O वाले वृत्त की जीवा है। प्रमुख खंड में वृत्त की परिधि पर बिंदु D से निकलने वाला एक रेखा खंड DOC, C पर निर्मित AB से इस प्रकार मिलता है कि BC = OD है। यदि कोण BCO = 30&deg; है, तो कोण AOD ज्ञात करें।</p>",
                    options_en: ["<p>30&deg;</p>", "<p>90&deg;</p>", 
                                "<p>60&deg;</p>", "<p>80&deg;</p>"],
                    options_hi: ["<p>30&deg;</p>", "<p>90&deg;</p>",
                                "<p>60&deg;</p>", "<p>80&deg;</p>"],
                    solution_en: "<p>60.(b)<br><strong id=\"docs-internal-guid-bcd7711f-7fff-350b-f411-d8bc9fea1544\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdkyvt1KhduXdUqnTE_6qk5NFTb1g83QwOw3LihJ52PTOniED-W_NLVV9LGynrO3gsCiZTqAZCurmJ0Rku74iKgdrAxC3ZEyy7byjtYFQkZSU5PTlIH2AdYbrogeKX0a_eSw1w9upXFb7OX8IX85XidWbsQ?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"218\" height=\"120\"></strong><br>Given: BC = OD<br>then, OD = OB = OA = radii of the circle <br>In <math display=\"inline\"><mi>&#916;</mi></math>OBC,<br>&ang;OCB = &ang;COB = 30&deg;<br>&ang;OBC = 180&deg; - (30&deg; + 30&deg;) = 120&deg;<br>In <math display=\"inline\"><mi>&#916;</mi></math>OAB,<br>&ang;OBA = 180&deg; - 120&deg; = 60&deg; = &ang;OAB<br>&ang;AOB = 180&deg; - (60&deg; + 60&deg;) = 60&deg;<br>Now,<br>&ang;AOD = 180&deg; - 60&deg; - 30&deg; = 90&deg;</p>",
                    solution_hi: "<p>60.(b)<br><strong id=\"docs-internal-guid-bcd7711f-7fff-350b-f411-d8bc9fea1544\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdkyvt1KhduXdUqnTE_6qk5NFTb1g83QwOw3LihJ52PTOniED-W_NLVV9LGynrO3gsCiZTqAZCurmJ0Rku74iKgdrAxC3ZEyy7byjtYFQkZSU5PTlIH2AdYbrogeKX0a_eSw1w9upXFb7OX8IX85XidWbsQ?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"218\" height=\"120\"></strong><br>दिया गया है : BC = OD<br>तब, OD = OB = OA = वृत्त की त्रिज्या <br><math display=\"inline\"><mi>&#916;</mi></math>OBC में,<br>&ang;OCB = &ang;COB = 30&deg;<br>&ang;OBC = 180&deg; - (30&deg; + 30&deg;) = 120&deg;<br><math display=\"inline\"><mi>&#916;</mi></math>OABमें,<br>&ang;OBA = 180&deg; - 120&deg; = 60&deg; = &ang;OAB<br>&ang;AOB = 180&deg; - (60&deg; + 60&deg;) = 60&deg;<br>अब,<br>&ang;AOD = 180&deg; - 60&deg; - 30&deg; = 90&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The following pie chart shows the percentage distribution of the expenditure incurred in publishing a book. Study the pie chart and answer the question that follows.<br><strong id=\"docs-internal-guid-d7015254-7fff-ad63-0bc0-12a2485911d9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcTGu0PA-H7qqADLPKbxUFNl4JKuaxQoGD3rRfgR2Bw1nUXx5yj0c8fHRhnQ3Svdo53crNN5sEvLmt-asQxliUjdFkDpXLIz-t1aGIA0rRYe9VkaQ4ZsbcSofi5Y0G_c7pl5lCBGcaRpesmSk-1rolPKenf?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"276\" height=\"271\"></strong><br>If the expenditure on royalty in publishing certain number of books be <math display=\"inline\"><mi>&#8377;</mi></math>43,500, then what is the combined expenditure (in ₹) on transportation, binding and printing?</p>",
                    question_hi: "<p>61. निम्नलिखित पाई चार्ट किसी पुस्तक के प्रकाशन में किए गए व्यय का प्रतिशत वितरण दर्शाता है। पाई चार्ट का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><strong id=\"docs-internal-guid-47811ad9-7fff-1543-523d-7485edf64288\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_Hnsq2rfdq3c97L1XTii38F9ct50vtqASVdfjhsQVlKos5VtiKoagd7cEQunGItJw82inEq9pZ0mbk_V3wfiWYFBFRK_iu31urY7QHGyhMHdUpg_vu3B5UbXeK1YZljZ1fUbjpOkqkA1ooUO9FdpBpMo?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"261\" height=\"256\"></strong><br>यदि निश्चित संख्या में पुस्तकों के प्रकाशन में रॉयल्टी पर व्यय रु. 43,500 है, तो परिवहन, बाइंडिंग और प्रिंटिंग पर संयुक्त व्यय (रुपये में) कितना है?</p>",
                    options_en: ["<p>1,97,900</p>", "<p>1,79,400</p>", 
                                "<p>1,79,900</p>", "<p>1,47,900</p>"],
                    options_hi: ["<p>1,97,900</p>", "<p>1,79,400</p>",
                                "<p>1,79,900</p>", "<p>1,47,900</p>"],
                    solution_en: "<p>61.(d) According to the question,<br>15 % = 43500<br>(10 + 16 + 25) % = 51% = <math display=\"inline\"><mfrac><mrow><mn>43500</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 51 = ₹147900</p>",
                    solution_hi: "<p>61.(d) प्रश्न के अनुसार,<br>15 % = 43500<br>(10 + 16 + 25) % = 51% = <math display=\"inline\"><mfrac><mrow><mn>43500</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 51 = ₹147900</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The given pie-chart shows the percentage distribution of the expenditure incurred in publishing a book.<br><strong id=\"docs-internal-guid-3842b441-7fff-b613-dace-9d6d5f891ee2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdeYF06JFvkB-Fe6AW2FKmKSWRwBZEa72AuS1Do29-SHgXMMh_OUvgs0WS2Q3SYqC2IC7_IT7kQCwSDx4Tl37Dz2ViVfrwGcnPbQVGhrX0xRJ79qwr_4H0OMR1ot00_1Ar3lsW_lvcYdS7N1e-vb-S0i0zB?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"269\" height=\"267\"></strong><br>What is the central angle of the sector corresponding to the expenditure incurred on binding?</p>",
                    question_hi: "<p>62. दिया गया पाई-चार्ट किसी पुस्तक के प्रकाशन पर हुए व्यय का प्रतिशत वितरण दर्शाता है।<br><strong id=\"docs-internal-guid-3a5a5d29-7fff-ea29-1b9c-d9be6a1ff6aa\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdm7PzTONVxf9-SQiWAUJWOqR8mXnJ6eqDHR5xOtxN8Z93EhV4pRB97fctyn0_Im7EXjDRD3c6qrh2iyNxoVWE2uvfQGUf75FULDPtUK8Lz5JTzXLXVG3X_f3j4JAYy5VLDRhVKUK65TvOb4H1e4nYdrWfy?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"283\" height=\"282\"></strong><br>बाइंडिंग पर किए गए व्यय के संगत वृतखंड का केंद्रीय कोण क्या है?</p>",
                    options_en: ["<p>78.8&deg;</p>", "<p>61.8&deg;</p>", 
                                "<p>82.8&deg;</p>", "<p>48.8&deg;</p>"],
                    options_hi: ["<p>78.8&deg;</p>", "<p>61.8&deg;</p>",
                                "<p>82.8&deg;</p>", "<p>48.8&deg;</p>"],
                    solution_en: "<p>62.(c) According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 % = 360&deg; <br><math display=\"inline\"><mo>&#8658;</mo></math> 23% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>100</mn></mfrac></math> &times; 23 = 82.8&deg;</p>",
                    solution_hi: "<p>62.(c) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 % = 360&deg; <br><math display=\"inline\"><mo>&#8658;</mo></math> 23% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>100</mn></mfrac></math> &times; 23 = 82.8&deg;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Simplify:<br>25 - (18 - 4 &times;&nbsp;9 &divide; 3)</p>",
                    question_hi: "<p>63. निम्नलिखित को सरल कीजिए। <br>25 - (18 - 4 &times;&nbsp;9 &divide; 3)</p>",
                    options_en: ["<p>-31</p>", "<p>33</p>", 
                                "<p>-17</p>", "<p>19</p>"],
                    options_hi: ["<p>-31</p>", "<p>33</p>",
                                "<p>-17</p>", "<p>19</p>"],
                    solution_en: "<p>63.(d) 25 - (18 - 4 &times; 9 &divide;&nbsp;3)<br>= 25 - (18 - 12)<br>= 25 - 6 = 19</p>",
                    solution_hi: "<p>63.(d) 25 - (18 - 4 &times; 9 &divide;&nbsp;3)<br>= 25 - (18 - 12)<br>= 25 - 6 = 19</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. XAB and XCD are two secants to a circle. If XA = 18 cm, AB = 22 cm and XC = 24 cm, then find the value of XD (in cm).</p>",
                    question_hi: "<p>64. XAB और XCD, एक वृत्त की दो छेदक रेखाएं हैं। यदि XA = 18 cm है, AB = 22 cm है और XC = 24 cm है, तो XD का मान (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>28</p>", "<p>21</p>", 
                                "<p>30</p>", "<p>34</p>"],
                    options_hi: ["<p>28</p>", "<p>21</p>",
                                "<p>30</p>", "<p>34</p>"],
                    solution_en: "<p>64.(c)<br><strong id=\"docs-internal-guid-63f863c8-7fff-d478-b8cd-51d989a2a2cb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc4N8bYgtiZ3C1HfWknZMO50suZC8n5bo-5dExwG2m7ve8HX2tiYMaKirRNvFp7EQ8jmdC6kpo1k4ftbTh7GX8GKumZbPsRlEuUsrFkLHoNlSiXGzBcoUS72mHX48UpGOv0xGJx8JbW92_rHDyhKcCXLjIx?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"221\" height=\"102\"></strong><br>We know that,<br><math display=\"inline\"><mo>&#8658;</mo></math> XC &times; XD = XA &times; XB<br><math display=\"inline\"><mo>&#8658;</mo></math> 24 &times; XD = 18 &times; 40<br><math display=\"inline\"><mo>&#8658;</mo></math> XD = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>40</mn></mrow><mn>24</mn></mfrac></math> = 30 cm</p>",
                    solution_hi: "<p>64.(c) <br><strong id=\"docs-internal-guid-63f863c8-7fff-d478-b8cd-51d989a2a2cb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc4N8bYgtiZ3C1HfWknZMO50suZC8n5bo-5dExwG2m7ve8HX2tiYMaKirRNvFp7EQ8jmdC6kpo1k4ftbTh7GX8GKumZbPsRlEuUsrFkLHoNlSiXGzBcoUS72mHX48UpGOv0xGJx8JbW92_rHDyhKcCXLjIx?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"221\" height=\"102\"></strong><br>हम जानते हैं की ,<br><math display=\"inline\"><mo>&#8658;</mo></math> XC &times; XD = XA &times; XB<br><math display=\"inline\"><mo>&#8658;</mo></math> 24 &times; XD = 18 &times; 40<br><math display=\"inline\"><mo>&#8658;</mo></math> XD = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>40</mn></mrow><mn>24</mn></mfrac></math> = 30 cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Study the below table and answer the question that follows.<br><strong id=\"docs-internal-guid-84d3ea6c-7fff-ca78-9841-e5fd60833a94\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcW_piZxAHeJBF8geQYMr3HIUFyBADP8dQEDrxxpmqMzAcgQicgjuWAOGe5AzCZAabf-xSguVZT0XMDwHj38wM2-QDH5-QAuYOUsJheF843xIASCY1wsMq77Gpmjc7kx1PUEedhSmM8PkVgSA41TQhDfjnU?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"283\" height=\"255\"></strong><br>If 300 calories are burned by jogging 6 km, then how many calories were burnt in the given week?</p>",
                    question_hi: "<p>65. निम्न तालिका का अध्ययन कीजिए और प्रश्न का उत्तर दीजिए।<br><strong id=\"docs-internal-guid-13e23350-7fff-3029-d2b9-3e8f8667eb4d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdbXrWOA9m9-6Kj65atutzYYSoQPbNqNBDLJFOwO7YADsiBN7BPIX2oYvxP0F4oio04QgTokPPLa7_EaRhWM8JuJ_-qnlLZTLIkC5AGtNJ6gu8RGgzl4Jo-1xakZS6tvpXnUOFVns8OMrbqSxoZke12iCU?key=gQ8WpOdIhb2ouHWJyUBOtG9h\" width=\"241\" height=\"224\"></strong><br>यदि 6 km जॉगिंग करने से 300 कैलोरी बर्न होती है, तो दिए गए सप्ताह में कितनी कैलोरी बर्न हुई?</p>",
                    options_en: ["<p>1600</p>", "<p>2200</p>", 
                                "<p>1800</p>", "<p>2000</p>"],
                    options_hi: ["<p>1600</p>", "<p>2200</p>",
                                "<p>1800</p>", "<p>2000</p>"],
                    solution_en: "<p>65.(d) Total distance of the week = 6 + 5.5 + 5.5 + 7 + 5 + 3.5 + 7.5 = 40<br>According to the question,<br>6 km burn calories = 300 <br>40 km burn calories = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 40 = 2000</p>",
                    solution_hi: "<p>65.(d) पुरे सप्ताह में तय की गयी कुल दूरी = 6 + 5.5 + 5.5 + 7 + 5 + 3.5 + 7.5 = 40<br>प्रश्न के अनुसार,<br>6 किमी कैलोरी बर्न = 300 <br>40 किमी कैलोरी बर्न = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 40 = 2000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If cos A + cos<sup>2</sup>A = 1 then sin<sup>2</sup>A + sin<sup>4</sup>A is equal to:</p>",
                    question_hi: "<p>66. यदि cos A + cos<sup>2</sup>A = 1 है, तो sin<sup>2</sup>A + sin<sup>4</sup>A किसके बराबर है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math></p>", "<p>0</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>", "<p>1</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow></mfrac></math></p>", "<p>0</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>A</mi></mrow></mfrac></math></p>", "<p>1</p>"],
                    solution_en: "<p>66.(d) cos A + cos<sup>2</sup>A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> cos A = 1 - cos<sup>2</sup>A<br><math display=\"inline\"><mo>&#8658;</mo></math> cos A = sin<sup>2</sup>A<br>Then,<br>sin<sup>2</sup>A + sin<sup>4</sup>A = sin<sup>2</sup>A + cos<sup>2</sup>A = 1</p>",
                    solution_hi: "<p>66.(d) cos A + cos<sup>2</sup>A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> cos A = 1 - cos<sup>2</sup>A<br><math display=\"inline\"><mo>&#8658;</mo></math> cos A = sin<sup>2</sup>A<br>अब ,<br>sin<sup>2</sup>A + sin<sup>4</sup>A = sin<sup>2</sup>A + cos<sup>2</sup>A = 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. If the medians of a triangle are equal, then the triangle will be:</p>",
                    question_hi: "<p>67. यदि किसी त्रिभुज की माध्यिकाएं बराबर हों, हों तो त्रिभुज _________ होगा।</p>",
                    options_en: ["<p>Equilateral triangle</p>", "<p>Obtuse-angle triangle</p>", 
                                "<p>Scalene triangle</p>", "<p>Right-angled triangle</p>"],
                    options_hi: ["<p>समबाहु त्रिभुज</p>", "<p>अधिक कोण त्रिभुज</p>",
                                "<p>विषमबाहु त्रिभुज</p>", "<p>समकोण त्रिभुज</p>"],
                    solution_en: "<p>67.(a) <br>If the medians of a triangle are equal, the triangle will be <strong>equilateral.</strong><br>This is because in any triangle, the medians are proportional to the sides. If all three medians are of equal length, it implies that all three sides of the triangle must also be equal. Hence, the triangle must be equilateral.</p>",
                    solution_hi: "<p>67.(a) <br>यदि किसी त्रिभुज की माध्यिकाएँ बराबर हों तो त्रिभुज <strong>समबाहु</strong> होगा।<br>ऐसा इसलिए है क्योंकि किसी भी त्रिभुज में माध्यिकाएँ भुजाओं के समानुपाती होती हैं। यदि तीनों माध्यिकाएँ समान लंबाई की हैं, तो त्रिभुज की तीनों भुजाएँ भी समान होनी चाहिए। अत: त्रिभुज समबाहु त्रिभुज होना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Find the fourth proportional to 4, 9 and 16.</p>",
                    question_hi: "<p>68. 4, 9 और 16 का चतुर्थानुपाती ज्ञात करें।</p>",
                    options_en: ["<p>18</p>", "<p>27</p>", 
                                "<p>36</p>", "<p>16</p>"],
                    options_hi: ["<p>18</p>", "<p>27</p>",
                                "<p>36</p>", "<p>16</p>"],
                    solution_en: "<p>68.(c) Let the fourth proportional be x<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mi mathvariant=\"normal\">x</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = 36</p>",
                    solution_hi: "<p>68.(c) माना चतुर्थानुपाती x&nbsp;है<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mi mathvariant=\"normal\">x</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x = 36</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cosec&#952;</mi><mo>-</mo><mi>cot&#952;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mi>sin&#952;</mi></mfrac></math>is equal to:</p>",
                    question_hi: "<p>69. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cosec&#952;</mi><mo>-</mo><mi>cot&#952;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mi>sin&#952;</mi></mfrac></math>&nbsp;का मान किसके बराबर है?</p>",
                    options_en: ["<p>tan<math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>cosec<math display=\"inline\"><mi>&#952;</mi></math></p>", 
                                "<p>cot<math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>sec<math display=\"inline\"><mi>&#952;</mi></math></p>"],
                    options_hi: ["<p>tan<math display=\"inline\"><mi>&#160;</mi><mi>&#952;</mi></math></p>", "<p>cosec <math display=\"inline\"><mi>&#952;</mi></math></p>",
                                "<p>cot <math display=\"inline\"><mi>&#952;</mi></math></p>", "<p>sec <math display=\"inline\"><mi>&#952;</mi></math></p>"],
                    solution_en: "<p>69.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cosec&#952;</mi><mo>-</mo><mi>cot&#952;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mi>sin&#952;</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mi>sin&#952;</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mi>sin&#952;</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mo>-</mo><mo>(</mo><msup><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> = cot&theta;</p>",
                    solution_hi: "<p>69.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cosec&#952;</mi><mo>-</mo><mi>cot&#952;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mi>sin&#952;</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mi>sin&#952;</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mi>sin&#952;</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mo>-</mo><mo>(</mo><msup><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>)</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></math> = cot&theta; </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Ten chairs and six tables together cost ₹5,140; three chairs and two tables together cost ₹1,635. The cost of 1 chair and 1 table is:</p>",
                    question_hi: "<p>70. दस कुर्सियों और छह मेजों का कुल मूल्य ₹5,140 है; तीन कुर्सियों और दो मेजों का कुल मूल्य ₹1,635 है। 1 कुर्सी और 1 मेज का मूल्य कितना है?</p>",
                    options_en: ["<p>₹900</p>", "<p>₹600</p>", 
                                "<p>₹800</p>", "<p>₹700</p>"],
                    options_hi: ["<p>₹900</p>", "<p>₹600</p>",
                                "<p>₹800</p>", "<p>₹700</p>"],
                    solution_en: "<p>70.(d) Let the price of one chairs and one tables be <math display=\"inline\"><mi>x</mi></math> and y respectively,<br><math display=\"inline\"><mo>&#8658;</mo></math> 10x + 6y = ₹5140 &hellip;&hellip;&hellip;.(i)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3x + 2y = ₹1635 &hellip;&hellip;&hellip;.(ii)<br>On multiply by 3 in equation (ii) then subtract equation (i) from (ii)<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5140 - 4905 = ₹235<br>Put the value of <math display=\"inline\"><mi>x</mi></math> in equation (ii)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 &times; 235 + 2y = ₹1635<br><math display=\"inline\"><mo>&#8658;</mo></math> 2y = 1635 - 705<br><math display=\"inline\"><mo>&#8658;</mo></math> y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>930</mn><mn>2</mn></mfrac></math> = ₹465<br>Now,<br>The value of 1 chair and 1 table = 235 + 465 = ₹700</p>",
                    solution_hi: "<p>70.(d) माना कि एक कुर्सी और एक मेज की कीमत क्रमशः <math display=\"inline\"><mi>x</mi></math>और y है,<br><math display=\"inline\"><mo>&#8658;</mo></math> 10x + 6y = ₹5140 &hellip;&hellip;&hellip;.(i)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3x + 2y = ₹1635 &hellip;&hellip;&hellip;.(ii)<br>समीकरण (ii) में 3 से गुणा कर समीकरण (i) को (ii) से घटाने पर <br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5140 - 4905 = ₹235<br>x का मान समीकरण (ii) में रखें<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 &times; 235 + 2y = ₹1635<br><math display=\"inline\"><mo>&#8658;</mo></math> 2y = 1635 - 705<br><math display=\"inline\"><mo>&#8658;</mo></math> y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>930</mn><mn>2</mn></mfrac></math> = ₹465<br>अब,<br>1 कुर्सी और 1 टेबल का मूल्य = 235 + 465 = ₹700 </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. If a + b + c = 0, then the value of (a<sup>2</sup> + b<sup>2</sup> + 2ab) is equal to:</p>",
                    question_hi: "<p>71. यदि a + b + c = 0 है, तो (a<sup>2</sup> + b<sup>2</sup> + 2ab) का मान क्या है ?</p>",
                    options_en: ["<p>c<sup>2</sup></p>", "<p>-c<sup>2</sup></p>", 
                                "<p>c</p>", "<p>-c</p>"],
                    options_hi: ["<p>c<sup>2</sup></p>", "<p>-c<sup>2</sup></p>",
                                "<p>c</p>", "<p>-c</p>"],
                    solution_en: "<p>71.(a) Given: a + b + c = 0<br><math display=\"inline\"><mi>a</mi></math> + b = -c<br>On squaring both side we get,<br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = (-c)<sup>2</sup><br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = c<sup>2</sup></p>",
                    solution_hi: "<p>71.(a) दिया गया है : a + b + c = 0<br><math display=\"inline\"><mi>a</mi></math> + b = -c<br>दोनों पक्षों का वर्ग करने पर हमें प्राप्त होता है,<br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = (-c)<sup>2</sup><br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = c<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The simple interest on a sum for 8 years is three-fifth of the sum. The rate of interest per annum is:</p>",
                    question_hi: "<p>72. किसी राशि पर 8 वर्षों का साधारण ब्याज उस राशि का तीन-पाँचवाँ भाग है। इसके लिए प्रति वर्ष ब्याज दर कितनी होगी?</p>",
                    options_en: ["<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", 
                                "<p>6%</p>", "<p>9%</p>"],
                    options_hi: ["<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                                "<p>6%</p>", "<p>9%</p>"],
                    solution_en: "<p>72.(b) According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> SI = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; Principal<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>Principal</mi><mo>&#215;</mo><mi>rate</mi><mo>&#215;</mo><mn>8</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; Principal<br><math display=\"inline\"><mo>&#8658;</mo></math> rate = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mn>5</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>8</mn></mrow></mfrac></math> &times; 100 = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    solution_hi: "<p>72.(b) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> साधारण ब्याज = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; मूलधन<br><math display=\"inline\"><mo>&#8658;</mo></math><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; मूलधन<br><math display=\"inline\"><mo>&#8658;</mo></math> दर =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mn>5</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>8</mn></mrow></mfrac></math> &times; 100 = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The marked price of a bed sheet is ₹500. At the time of its sale, two successive discounts of 10% and 5% are available. Find the selling price (in ₹) of the bed sheet.</p>",
                    question_hi: "<p>73. एक बेड शीट का अंकित मूल्य ₹ 500 है। इसकी बिक्री के समय, इस पर 10% और 5% की दो क्रमिक छूट उपलब्ध हैं। बेड शीट का विक्रय मूल्य (₹ में) ज्ञात करें।</p>",
                    options_en: ["<p>432.50</p>", "<p>427.50</p>", 
                                "<p>445.00</p>", "<p>430.00</p>"],
                    options_hi: ["<p>432.50</p>", "<p>427.50</p>",
                                "<p>445.00</p>", "<p>430.00</p>"],
                    solution_en: "<p>73.(b) Selling price of the bed sheet = 500 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> = ₹ 427.50</p>",
                    solution_hi: "<p>73.(b) बेड शीट का विक्रय मूल्य = 500 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math> = ₹ 427.50</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A mango juice is made by mixing water and mango concentrate in the ratio 9 : 7. If x litres of water and 3x litres of mango concentrate is mixed in 160 litres of mango juice, then the new ratio becomes 13 : 14. What is the quantity of the new mango juice (in litres)?</p>",
                    question_hi: "<p>74. पानी और आम के सांद्रण को 9 : 7 के अनुपात में मिलाकर आम का जूस बनाया जाता है। यदि 160 लीटर आम के जूस में x लीटर पानी और 3x लीटर आम के सांद्रण को मिलाया जाता है, तो नया अनुपात 13 : 14 हो जाता है। आम के नए जूस की मात्रा (लीटर में) क्या है?</p>",
                    options_en: ["<p>197</p>", "<p>212</p>", 
                                "<p>206</p>", "<p>216</p>"],
                    options_hi: ["<p>197</p>", "<p>212</p>",
                                "<p>206</p>", "<p>216</p>"],
                    solution_en: "<p>74.(d) Let the quantity of water and mango a in juice be 9a and 7a respectively,<br>According to the question,<br>16a = 160<br>a = 10<br>9a = 90 and 7a = 70<br>Then,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>70</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>14</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> 1260 + 14x = 910 + 39x<br><math display=\"inline\"><mo>&#8658;</mo></math> 350 = 25x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>25</mn></mfrac></math> = 14<br>Quantity of the new mango juice = (90 +14) + (70 + 3 &times; 14) = 216 litre</p>",
                    solution_hi: "<p>74.(d) माना जूस में पानी और आम की मात्रा क्रमशः 9a और 7a है,<br>प्रश्न के अनुसार,<br>16a = 160<br>a = 10<br>9a = 90 और 7a = 70<br>तब,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>70</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>14</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> 1260 + 14x = 910 + 39x<br><math display=\"inline\"><mo>&#8658;</mo></math> 350 = 25x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>25</mn></mfrac></math> = 14<br>नये आम के जूस की मात्रा = (90 +14) + (70 + 3 &times; 14) = 216 लीटर</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Three pipes, P, Q and R, together take four hours to fill a tank. All the three pipes were opened at the same time. After three hours, P was closed, and Q and R filled the remaining tank in two hours. How many hours will P alone take to fill the tank?</p>",
                    question_hi: "<p>75. तीन पाइप, P, Q और R मिलकर एक टैंक को भरने में चार घंटे का समय लेते हैं। तीनों पाइपों को एक साथ खोला जाता है। तीन घंटे के बाद, P को बंद कर दिया जाता है, और Q तथा R शेष टैंक को दो घंटों में भर देते हैं। P अकेले टैंक को भरने में कितने घंटे का समय लेगा?</p>",
                    options_en: ["<p>8</p>", "<p>10</p>", 
                                "<p>12</p>", "<p>9</p>"],
                    options_hi: ["<p>8</p>", "<p>10</p>",
                                "<p>12</p>", "<p>9</p>"],
                    solution_en: "<p>75.(a) Part filled in 3 hours = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>Remaining part = 1 - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>(Q + R)&rsquo;s 1 hours work = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> <br>P&rsquo;s 1 hours work = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>8</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>So, P alone can fill the tank = 8 hours </p>",
                    solution_hi: "<p>75.(a) 3 घंटे में भरा भाग = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>शेष भाग = 1 - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>(Q + R) का 1 घंटे का काम = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>P का 1 घंटे का काम = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>8</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>तो, P अकेले टंकी को भर सकता है = 8 hours</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate synonym of the given word: <br>Obstinate</p>",
                    question_hi: "<p>76. Select the most appropriate synonym of the given word: <br>Obstinate</p>",
                    options_en: ["<p>Obedient</p>", "<p>Adaptive</p>", 
                                "<p>Stubborn</p>", "<p>Submissive</p>"],
                    options_hi: ["<p>Obedient</p>", "<p>Adaptive</p>",
                                "<p>Stubborn</p>", "<p>Submissive</p>"],
                    solution_en: "<p>76.(c) <strong>Stubborn-</strong> having or showing determination not to change one\'s attitude.<br><strong>Obstinate-</strong> stubbornly refusing to change one\'s opinion or chosen course of action.<br><strong>Obedient-</strong> willing to comply with authority or instructions.<br><strong>Adaptive-</strong> able to adjust to new conditions or environments.<br><strong>Submissive-</strong> ready to conform to the authority or will of others.</p>",
                    solution_hi: "<p>76.(c) <strong>Stubborn</strong> (जिद्दी/ अड़ियल)- having or showing determination not to change one\'s attitude.<br><strong>Obstinate</strong> (हठी)- stubbornly refusing to change one\'s opinion or chosen course of action.<br><strong>Obedient</strong> (आज्ञाकारी )- willing to comply with authority or instructions.<br><strong>Adaptive</strong> (अनुकूली)- able to adjust to new conditions or environments.<br><strong>Submissive</strong> (दब्बू)- ready to conform to the authority or will of others.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate meaning of the given idiom: <br>Left out in the cold</p>",
                    question_hi: "<p>77. Select the most appropriate meaning of the given idiom: <br>Left out in the cold</p>",
                    options_en: ["<p>To go underground</p>", "<p>To be in hiding</p>", 
                                "<p>To be ignored</p>", "<p>To be extremely negative</p>"],
                    options_hi: ["<p>To go underground</p>", "<p>To be in hiding</p>",
                                "<p>To be ignored</p>", "<p>To be extremely negative</p>"],
                    solution_en: "<p>77.(c) <strong>Left out in the cold</strong>- to be ignored.<br>E.g.- She felt left out in the cold when her friends didn\'t invite her to the party.</p>",
                    solution_hi: "<p>77.(c) <strong>Left out in the cold-</strong> to be ignored./अनदेखा करना। <br>E.g.- She felt left out in the cold when her friends didn\'t invite her to the party.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "78. Parts of a sentence are given in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />P. and has also created a wide gap between them<br />Q . laid too much stress on<br />R. teachers and students<br />S. online mode of education has",
                    question_hi: "78. Parts of a sentence are given in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />P. and has also created a wide gap between them<br />Q . laid too much stress on<br />R. teachers and students<br />S. online mode of education has",
                    options_en: [" SRPQ", " RQPS", 
                                " SQRP", " PSQR"],
                    options_hi: [" SRPQ", " RQPS",
                                " SQRP", " PSQR"],
                    solution_en: "78.(c) SQRP<br />The given sentence starts with Part S as it introduces the subject of the sentence, i.e. ‘online mode of education’. Part S will be followed by Part Q as it contains the main verb ‘has’ and its object ‘too much stress’. Further, Part R states that students and teachers are under too much stress & Part P states that this has also created a gap between them. So, P will follow R. Going through the options, option ‘c’ has the correct sequence.",
                    solution_hi: "78.(c) SQRP<br />दिया गया sentence, Part S से प्रारंभ होगा क्योंकि इसमे sentence के subject, ‘online mode of education शामिल है। Part S के बाद Part Q आएगा क्योंकि इसमें main verb ‘has’ और उसका object ‘too much stress’ है। इसके अलावा, Part R बताता है कि students और teachers बहुत अधिक stress में हैं और Part P बताता है कि इससे उनके बीच दूरी भी पैदा हो गई है। इसलिए, R के बाद P आएगा। अतः options के माध्यम से जाने पर option ‘c’ में सही sequence है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the option that can be used as a one-word substitute for the given group of words:<br>A thing that can be easily carried by hands</p>",
                    question_hi: "<p>79. Select the option that can be used as a one-word substitute for the given group of words:<br>A thing that can be easily carried by hands</p>",
                    options_en: ["<p>Export</p>", "<p>Import</p>", 
                                "<p>Potable</p>", "<p>Portable</p>"],
                    options_hi: ["<p>Export</p>", "<p>Import</p>",
                                "<p>Potable</p>", "<p>Portable</p>"],
                    solution_en: "<p>79.(d) <strong>Portable-</strong> a thing that can be easily carried by hands.<br><strong>Export-</strong> send (goods or services) to another country for sale.<br><strong>Import-</strong> bring (goods or services) into a country from abroad for sale.<br><strong>Potable-</strong> safe to drink.</p>",
                    solution_hi: "<p>79.(d) <strong>Portable</strong> (वहनीय)- a thing that can be easily carried by hands.<br><strong>Export</strong> (निर्यात करना)- send (goods or services) to another country for sale.<br><strong>Import</strong> (आयात करना)- bring (goods or services) into a country from abroad for sale.<br><strong>Potable</strong> (पीने योग्य)- safe to drink.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Parts of the following sentence have been given as options. Select the option that contains an error:<br>He always wants to be in the company of those who are intellectually superior than him.</p>",
                    question_hi: "<p>80. Parts of the following sentence have been given as options. Select the option that contains an error:<br>He always wants to be in the company of those who are intellectually superior than him.</p>",
                    options_en: ["<p>superior than him</p>", "<p>be in the company of</p>", 
                                "<p>He always wants to</p>", "<p>those who are intellectually</p>"],
                    options_hi: ["<p>superior than him</p>", "<p>be in the company of</p>",
                                "<p>He always wants to</p>", "<p>those who are intellectually</p>"],
                    solution_en: "<p>80.(a) superior than him.<br>There are some words that imply comparison but do not take &lsquo;than&rsquo;. Rather, &lsquo;to&rsquo; is used with them. For example:- superior to, inferior to, prior to, senior to, junior to, elder to, etc. Hence, &lsquo;superior to him&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>80.(a) superior than him.<br>कुछ words ऐसे हैं जो comparison को denote करते हैं लेकिन उनके साथ &lsquo;than&rsquo; का प्रयोग नहीं होता है। बल्कि, उनके साथ &lsquo;to&rsquo; का प्रयोग किया जाता है। उदाहरण के लिए:- superior to, inferior to, prior to, senior to, junior to, elder to, आदि। अतः, &lsquo;superior to him&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Parts of a sentence are given in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>O. want<br>P. movie tonight<br>Q . do you<br>R. to watch<br>S. the</p>",
                    question_hi: "<p>81. Parts of a sentence are given in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>O. want<br>P. movie tonight<br>Q . do you<br>R. to watch<br>S. the</p>",
                    options_en: ["<p>RSPQO</p>", "<p>QORSP</p>", 
                                "<p>OSQPR</p>", "<p>OSPQR</p>"],
                    options_hi: ["<p>RSPQO</p>", "<p>QORSP</p>",
                                "<p>OSQPR</p>", "<p>OSPQR</p>"],
                    solution_en: "<p>81.(b) QORSP<br>The correct sentence is : &ldquo;Do you want to watch the movie tonight?&rdquo;</p>",
                    solution_hi: "<p>81.(b) QORSP<br>&ldquo;Do you want to watch the movie tonight?&rdquo; सही sentence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Parts of a sentence are given in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>P) always favours<br>Q) only the<br>R) fortune<br>S) bold</p>",
                    question_hi: "<p>82. Parts of a sentence are given in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>P) always favours<br>Q) only the<br>R) fortune<br>S) bold</p>",
                    options_en: ["<p>SQPR</p>", "<p>RPQS</p>", 
                                "<p>SRQP</p>", "<p>PQRS</p>"],
                    options_hi: ["<p>SQPR</p>", "<p>RPQS</p>",
                                "<p>SRQP</p>", "<p>PQRS</p>"],
                    solution_en: "<p>82.(b) RPQS<br>The correct sentence is : &ldquo;Fortune always favours only the bold.&rdquo;</p>",
                    solution_hi: "<p>82.(b) RPQS<br>&ldquo;Fortune always favours only the bold.&rdquo; सही sentence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "83. Parts of a sentence are given in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />A. Only for themselves<br />B. Is full of<br />C. People who live<br />D. The world",
                    question_hi: "83. Parts of a sentence are given in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />A. Only for themselves<br />B. Is full of<br />C. People who live<br />D. The world",
                    options_en: [" BCAD", " ABCD", 
                                " DBAC", " DBCA"],
                    options_hi: [" BCAD", " ABCD",
                                " DBAC", " DBCA"],
                    solution_en: "83.(d) DBCA<br />The correct sentence is “The world is full of people who live only for themselves.”",
                    solution_hi: "83.(d) DBCA<br />“The world is full of people who live only for themselves.” सही sentence है। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the option that correctly expresses the given sentence in passive voice:<br>Mr. Amar refused him admittance.</p>",
                    question_hi: "<p>84. Select the option that correctly expresses the given sentence in passive voice:<br>Mr. Amar refused him admittance.</p>",
                    options_en: ["<p>Admittance will be refused to him by Mr. Amar.</p>", "<p>Admittance shall be refused to him by Mr. Amar.</p>", 
                                "<p>Admittance would be refused to him by Mr. Amar.</p>", "<p>Admittance was refused to him by Mr. Amar.</p>"],
                    options_hi: ["<p>Admittance will be refused to him by Mr. Amar.</p>", "<p>Admittance shall be refused to him by Mr. Amar.</p>",
                                "<p>Admittance would be refused to him by Mr. Amar.</p>", "<p>Admittance was refused to him by Mr. Amar.</p>"],
                    solution_en: "<p>84.(d) Admittance was refused to him by Mr. Amar. (Correct)<br>(a) Admittance <span style=\"text-decoration: underline;\">will be refused</span> to him by Mr. Amar. (Incorrect Tense)<br>(b) Admittance <span style=\"text-decoration: underline;\">shall be refused</span> to him by Mr. Amar. (Incorrect Tense)<br>(c) Admittance <span style=\"text-decoration: underline;\">would be refused</span> to him by Mr. Amar. (Incorrect Tense)</p>",
                    solution_hi: "<p>84.(d) Admittance was refused to him by Mr. Amar. (Correct)<br>(a) Admittance <span style=\"text-decoration: underline;\">will be refused</span> to him by Mr. Amar. (गलत Tense)<br>(b) Admittance <span style=\"text-decoration: underline;\">shall be refused</span> to him by Mr. Amar. (गलत Tense)<br>(c) Admittance <span style=\"text-decoration: underline;\">would be refused</span> to him by Mr. Amar. (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate meaning of the idiom: <br>Break the ice</p>",
                    question_hi: "<p>85. Select the most appropriate meaning of the idiom: <br>Break the ice</p>",
                    options_en: ["<p>Nervousness before a performance</p>", "<p>Ill-feeling carried by a person over a period of time</p>", 
                                "<p>Respect for someone&rsquo;s achievements</p>", "<p>To say or do something that makes people feel more relaxed, especially at the beginning of a meeting, party</p>"],
                    options_hi: ["<p>Nervousness before a performance</p>", "<p>Ill-feeling carried by a person over a period of time</p>",
                                "<p>Respect for someone&rsquo;s achievements</p>", "<p>To say or do something that makes people feel more relaxed, especially at the beginning&nbsp;of a meeting, party</p>"],
                    solution_en: "<p>85.(d) <strong>Break the ice-</strong> to say or do something that makes people feel more relaxed, <br>especially at the beginning of a meeting, party.<br><strong>E.g.-</strong> He told a joke to break the ice at the start of the meeting.</p>",
                    solution_hi: "<p>85.(d) <strong>Break the ice</strong>- to say or do something that makes people feel more relaxed, <br>especially at the beginning of a meeting, party./कुछ ऐसा कहना या करना जिससे लोग अधिक सहज महसूस करें, विशेषकर किसी मीटिंग, पार्टी की शुरुआत में।<br><strong>E.g.-</strong> He told a joke to break the ice at the start of the meeting.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "86. Select the most appropriate homophone to fill in the blank:<br />The summer was ______ hot this year.",
                    question_hi: "86. Select the most appropriate homophone to fill in the blank:<br />The summer was ______ hot this year.",
                    options_en: [" tow", " two", 
                                " to", " too<br /> "],
                    options_hi: [" tow", " two",
                                " to", " too"],
                    solution_en: "86.(d) too<br />‘Too’ means very much. The given sentence states that the summer was too hot this year. Hence, \'too\' is the most appropriate answer.",
                    solution_hi: "86.(d) too<br />‘Too’ का अर्थ है बहुत ज़्यादा। दिए गए sentence में कहा गया है कि इस वर्ष गर्मी बहुत ज़्यादा थी। अतः, \'too\' सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate synonym of the bracketed word in the following sentence to fill in the blank:<br>The mothers, who suffer from postpartum depression, feel ______ by their children. (vexed)</p>",
                    question_hi: "<p>87. Select the most appropriate synonym of the bracketed word in the following sentence to fill in the blank:<br>The mothers, who suffer from postpartum depression, feel ______ by their children. (vexed)</p>",
                    options_en: ["<p>delighted</p>", "<p>recovered</p>", 
                                "<p>pacific</p>", "<p>annoyed</p>"],
                    options_hi: ["<p>delighted</p>", "<p>recovered</p>",
                                "<p>pacific</p>", "<p>annoyed</p>"],
                    solution_en: "<p>87.(d) <strong>Annoyed-</strong> feeling slight anger or irritation.<br><strong>Vexed-</strong> annoyed or frustrated.<br><strong>Delighted-</strong> feeling or showing great pleasure.<br><strong>Recovered-</strong> having returned to a normal state after a setback or illness.<br><strong>Pacific-</strong> peaceful in character.</p>",
                    solution_hi: "<p>87.(d) <strong>Annoyed</strong> (नाराज़ )- feeling slight anger or irritation.<br><strong>Vexed</strong> (परेशान)- annoyed or frustrated.<br><strong>Delighted</strong> (आनंदित)- feeling or showing great pleasure.<br><strong>Recovered</strong> (पुनर्प्राप्ति)- having returned to a normal state after a setback or illness.<br><strong>Pacific</strong> (शांतिप्रिय)- peaceful in character.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "88. Parts of the following sentence have been given as options. Select the option that contains an error:<br />I have sing ‘Let It Go’ before a crowd of thousands of people.",
                    question_hi: "88. Parts of the following sentence have been given as options. Select the option that contains an error:<br />I have sing ‘Let It Go’ before a crowd of thousands of people.",
                    options_en: [" a crowd of", " thousands of people", 
                                " I have sing", " ‘Let It Go’ before<br /> "],
                    options_hi: [" a crowd of", " thousands of people",
                                " I have sing", " ‘Let It Go’ before"],
                    solution_en: "88.(c) I have sing<br />With ‘has/have/had’, the third form of the verb is used. However, ‘sung’ is the third form of ‘sing’. Hence, \'I have sung\' is the most appropriate answer. ",
                    solution_hi: "88.(c) I have sing<br />‘Has/have/had’ के साथ verb की third form का प्रयोग किया जाता है। हालाँकि, ‘sing’ की third form ‘sung’ है। अतः, ‘I have sung’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate option to substitute the underlined segment in the given sentence:<br>Aesthetic value is specifically a way in which we <span style=\"text-decoration: underline;\">can get positive conclude value</span> from the world at large.</p>",
                    question_hi: "<p>89. Select the most appropriate option to substitute the underlined segment in the given sentence:<br>Aesthetic value is specifically a way in which we <span style=\"text-decoration: underline;\">can get positive conclude value</span> from the world at large.</p>",
                    options_en: ["<p>can get positive convalescent value</p>", "<p>can get positive convulsive value</p>", 
                                "<p>can get positive conclusive value</p>", "<p>can get positive contrastive value</p>"],
                    options_hi: ["<p>can get positive convalescent value</p>", "<p>can get positive convulsive value</p>",
                                "<p>can get positive conclusive value</p>", "<p>can get positive contrastive value</p>"],
                    solution_en: "<p>89.(c) can get positive conclusive value<br>The given sentence needs an adjective (conclusive) to describe the noun &lsquo;value&rsquo;, and not the verb &lsquo;conclude&rsquo;. Hence, &lsquo;can get positive conclusive value&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(c) can get positive conclusive value<br>दिए गए sentence में noun &lsquo;value&rsquo; को describe करने के लिए verb &lsquo;conclude&rsquo; की नहीं बल्कि, adjective (conclusive) की आवश्यकता है। अतः, &lsquo;can get positive conclusive value&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "90. Select the correctly spelt word to fill in the blank:<br />My grandmother ____________ the preparation of every festival celebration in my house.",
                    question_hi: "90. Select the correctly spelt word to fill in the blank:<br />My grandmother ____________ the preparation of every festival celebration in my house.",
                    options_en: [" initiates", " inetiates", 
                                " enitiates", " enetiates"],
                    options_hi: [" initiates", " inetiates",
                                " enitiates", " enetiates"],
                    solution_en: "90.(a) initiates<br />\'Initiates\' is the correct spelling.",
                    solution_hi: "90.(a) initiates<br />\'Initiates\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. In the question, four alternatives are given for the underlined word in the sentence. Choose the alternative which best expresses the opposite meaning of the word:<br>It was as if the newer hybrids had an <span style=\"text-decoration: underline;\">insatiable</span> appetite for chemicals.</p>",
                    question_hi: "<p>91. In the question, four alternatives are given for the underlined word in the sentence. Choose the alternative which best expresses the opposite meaning of the word:<br>It was as if the newer hybrids had an <span style=\"text-decoration: underline;\">insatiable</span> appetite for chemicals.</p>",
                    options_en: ["<p>Satisfiable</p>", "<p>Urgent</p>", 
                                "<p>Curbed</p>", "<p>Pressing</p>"],
                    options_hi: ["<p>Satisfiable</p>", "<p>Urgent</p>",
                                "<p>Curbed</p>", "<p>Pressing</p>"],
                    solution_en: "<p>91.(a) <strong>Satisfiable-</strong> able to be satisfied or fulfilled.<br><strong>Insatiable-</strong> impossible to satisfy.<br><strong>Urgent-</strong> requiring immediate action or attention.<br><strong>Curbed-</strong> restrained or controlled.<br><strong>Pressing-</strong> requiring quick or immediate attention.</p>",
                    solution_hi: "<p>91.(a) <strong>Satisfiable</strong> (संतोषजनक)- able to be satisfied or fulfilled.<br><strong>Insatiable</strong> (अतृप्य)- impossible to satisfy.<br><strong>Urgent</strong> (अत्यावश्यक)- requiring immediate action or attention.<br><strong>Curbed</strong> (अंकुश लगाना)- restrained or controlled.<br><strong>Pressing</strong> (जरूरी/अत्यावश्यक)- requiring quick or immediate attention.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Identify the most appropriate ANTONYM of the underlined word in the given sentence:<br>Lady Stranleigh continued to smile at him <span style=\"text-decoration: underline;\">brilliantly.</span></p>",
                    question_hi: "<p>92. Identify the most appropriate ANTONYM of the underlined word in the given sentence:<br>Lady Stranleigh continued to smile at him <span style=\"text-decoration: underline;\">brilliantly.</span></p>",
                    options_en: ["<p>Brightly</p>", "<p>Dimly</p>", 
                                "<p>Cunningly</p>", "<p>Strangely</p>"],
                    options_hi: ["<p>Brightly</p>", "<p>Dimly</p>",
                                "<p>Cunningly</p>", "<p>Strangely</p>"],
                    solution_en: "<p>92.(b) <strong>Dimly-</strong> in a faint or weak light.<br><strong>Brilliantly-</strong> in a very bright or radiant manner.<br><strong>Brightly-</strong> in a vivid or cheerful manner.<br><strong>Cunningly-</strong> in a clever and deceitful manner.<br><strong>Strangely-</strong> in a way that is unusual or surprising.</p>",
                    solution_hi: "<p>92.(b) <strong>Dimly</strong> (अस्पष्ट/मंद रूप से)- in a faint or weak light.<br><strong>Brilliantly</strong> (प्रकाश बिखेरते हुए)- in a very bright or radiant manner.<br><strong>Brightly</strong> (उल्लसित होकर)- in a vivid or cheerful manner.<br><strong>Cunningly</strong> (धूर्ततापूर्वक)- in a clever and deceitful manner.<br><strong>Strangely</strong> (असाधारण रूप से)- in a way that is unusual or surprising.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the correct spelling of the underlined word in the sentence:<br>Can you please come back to reality from the <span style=\"text-decoration: underline;\">fectitious</span> world you live in?</p>",
                    question_hi: "<p>93. Select the correct spelling of the underlined word in the sentence:<br>Can you please come back to reality from the <span style=\"text-decoration: underline;\">fectitious</span> world you live in?</p>",
                    options_en: ["<p>factitious</p>", "<p>fractional</p>", 
                                "<p>factional</p>", "<p>fictitious</p>"],
                    options_hi: ["<p>factitious</p>", "<p>fractional</p>",
                                "<p>factional</p>", "<p>fictitious</p>"],
                    solution_en: "<p>93.(d) fictitious<br>\'Fictitious\' is the correct spelling to use here. &lsquo;Fictitious&rsquo; means not real.</p>",
                    solution_hi: "<p>93.(d) fictitious<br>यहाँ use करने के लिए सही spelling \'fictitious\' है। &lsquo;Fictitious&rsquo; का अर्थ है काल्पनिक।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate ANTONYM of the given word: <br>Purify</p>",
                    question_hi: "<p>94. Select the most appropriate ANTONYM of the given word: <br>Purify</p>",
                    options_en: ["<p>Chant</p>", "<p>Invite</p>", 
                                "<p>Pollute</p>", "<p>Wash</p>"],
                    options_hi: ["<p>Chant</p>", "<p>Invite</p>",
                                "<p>Pollute</p>", "<p>Wash</p>"],
                    solution_en: "<p>94.(c) <strong>Pollute-</strong> to contaminate or make unclean, especially with harmful substances.<br><strong>Purify-</strong> to remove contaminants or impurities.<br><strong>Chant-</strong> to sing or say repeatedly in a rhythmic way.<br><strong>Invite-</strong> to ask someone to come or participate.<br><strong>Wash-</strong> to clean something with water and typically soap.</p>",
                    solution_hi: "<p>94.(c) <strong>Pollute</strong> (प्रदूषित)- to contaminate or make unclean, especially with harmful substances.<br><strong>Purify</strong> (शुद्ध करना)- to remove contaminants or impurities.<br><strong>Chant</strong> (राग अलापना)- to sing or say repeatedly in a rhythmic way.<br><strong>Invite</strong> (आमंत्रित करना)- to ask someone to come or participate.<br><strong>Wash</strong> (धोना)- to clean something with water and typically soap.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the option that expresses the given sentence in active voice:<br>Trigonometry has been taught by him for donkey&rsquo;s years.</p>",
                    question_hi: "<p>95. Select the option that expresses the given sentence in active voice:<br>Trigonometry has been taught by him for donkey&rsquo;s years.</p>",
                    options_en: ["<p>He had been teaching trigonometry for donkey&rsquo;s years.</p>", "<p>He has been trigonometry teaching for donkey&rsquo;s years.</p>", 
                                "<p>He is teaching trigonometry for donkey&rsquo;s years.</p>", "<p>He has been teaching trigonometry for donkey&rsquo;s years.</p>"],
                    options_hi: ["<p>He had been teaching trigonometry for donkey&rsquo;s years.</p>", "<p>He has been trigonometry teaching for donkey&rsquo;s years.</p>",
                                "<p>He is teaching trigonometry for donkey&rsquo;s years.</p>", "<p>He has been teaching trigonometry for donkey&rsquo;s years.</p>"],
                    solution_en: "<p>95.(d) He has been teaching trigonometry for donkey&rsquo;s years. (Correct)<br>(a) He <span style=\"text-decoration: underline;\">had been teaching</span> trigonometry for donkey&rsquo;s years. (Incorrect Tense)<br>(b) He has been trigonometry teaching for donkey&rsquo;s years. (Incorrect Sentence Structure) <br>(c) He <span style=\"text-decoration: underline;\">is</span> teaching trigonometry for donkey&rsquo;s years. (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>95.(d) He has been teaching trigonometry for donkey&rsquo;s years. (Correct)<br>(a) He <span style=\"text-decoration: underline;\">had been teaching</span> trigonometry for donkey&rsquo;s years. (गलत Tense)<br>(b) He has been trigonometry teaching for donkey&rsquo;s years. (गलत Sentence Structure) <br>(c) He <span style=\"text-decoration: underline;\">is</span> teaching trigonometry for donkey&rsquo;s years. (गलत Helping Verb)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96.<strong> Cloze Test:</strong><br>Music is perhaps the most popular and widely practised (96)______ of fine arts, transcending all kinds of cultural and linguistic barriers. Any form of fine art is (97)________ to master and almost impossible to perfect, and music is no exception.&nbsp;Nature, it is learnt, has blessed almost two-thirds of the human race with musical ability of some sort. Music has the power to (98)______ out the deepest emotions. In fact, it is a magic medicine, and many seek refuge in it when they are depressed or stressed. It is this intimacy that makes us listen to music (99)______ even hum or sing sometimes. This singing, or realistically speaking, expressing one&rsquo;s emotion musically sometimes takes a serious turn. The desire to sing before an audience is innocent and beautiful, and indeed, it is perfectly alright to have such a genuine desire. But it is also important to understand that singing is an (100)________ art&mdash;a highly refined one at that, which requires systematic, prolonged and strict training to be acceptable.<br>Choose the most appropriate option to fill in blank no. 96:</p>",
                    question_hi: "<p>96. <strong>Cloze Test:</strong><br>Music is perhaps the most popular and widely practised (96)______ of fine arts, transcending all kinds of cultural and linguistic barriers. Any form of fine art is (97)________ to master and almost impossible to perfect, and music is no exception.&nbsp;Nature, it is learnt, has blessed almost two-thirds of the human race with musical ability of some sort. Music has the power to (98)______ out the deepest emotions. In fact, it is a magic medicine, and many seek refuge in it when they are depressed or stressed. It is this intimacy that makes us listen to music (99)______ even hum or sing sometimes. This singing, or realistically speaking, expressing one&rsquo;s emotion musically sometimes takes a serious turn. The desire to sing before an audience is innocent and beautiful, and indeed, it is perfectly alright to have such a genuine desire. But it is also important to understand that singing is an (100)________ art&mdash;a highly refined one at that, which requires systematic, prolonged and strict training to be acceptable.<br>Choose the most appropriate option to fill in blank no. 96:</p>",
                    options_en: ["<p>format</p>", "<p>substance</p>", 
                                "<p>form</p>", "<p>design</p>"],
                    options_hi: ["<p>format</p>", "<p>substance</p>",
                                "<p>form</p>", "<p>design</p>"],
                    solution_en: "<p>96.(c) form <br>&lsquo;Form&rsquo; means a kind or type of something. The given passage states that music is perhaps the most popular and widely practised form of fine arts. Hence, &lsquo;form&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(c) form <br>&lsquo;Form&rsquo; का अर्थ है किसी चीज़ का एक प्रकार। दिए गए passage में कहा गया है कि music संभवतः fine arts का सबसे लोकप्रिय और व्यापक रूप से प्रचलित रूप है। अतः, &lsquo;form&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong><br>Music is perhaps the most popular and widely practised (96)______ of fine arts, transcending all kinds of cultural and linguistic barriers. Any form of fine art is (97)________ to master and almost impossible to perfect, and music is no exception.&nbsp;Nature, it is learnt, has blessed almost two-thirds of the human race with musical ability of some sort. Music has the power to (98)______ out the deepest emotions. In fact, it is a magic medicine, and many seek refuge in it when they are depressed or stressed. It is this intimacy that makes us listen to music (99)______ even hum or sing sometimes. This singing, or realistically speaking, expressing one&rsquo;s emotion musically sometimes takes a serious turn. The desire to sing before an audience is innocent and beautiful, and indeed, it is perfectly alright to have such a genuine desire. But it is also important to understand that singing is an (100)________ art&mdash;a highly refined one at that, which requires systematic, prolonged and strict training to be acceptable.<br>Choose the most appropriate option to fill in blank no. 97:</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong><br>Music is perhaps the most popular and widely practised (96)______ of fine arts, transcending all kinds of cultural and linguistic barriers. Any form of fine art is (97)________ to master and almost impossible to perfect, and music is no exception.&nbsp;Nature, it is learnt, has blessed almost two-thirds of the human race with musical ability of some sort. Music has the power to (98)______ out the deepest emotions. In fact, it is a magic medicine, and many seek refuge in it when they are depressed or stressed. It is this intimacy that makes us listen to music (99)______ even hum or sing sometimes. This singing, or realistically speaking, expressing one&rsquo;s emotion musically sometimes takes a serious turn. The desire to sing before an audience is innocent and beautiful, and indeed, it is perfectly alright to have such a genuine desire. But it is also important to understand that singing is an (100)________ art&mdash;a highly refined one at that, which requires systematic, prolonged and strict training to be acceptable.<br>Choose the most appropriate option to fill in blank no. 97:</p>",
                    options_en: ["<p>difficult</p>", "<p>irksome</p>", 
                                "<p>serious</p>", "<p>easy</p>"],
                    options_hi: ["<p>difficult</p>", "<p>irksome</p>",
                                "<p>serious</p>", "<p>easy</p>"],
                    solution_en: "<p>97.(a) difficult<br>&lsquo;Difficult&rsquo; means needing much effort or skill to accomplish. The given passage states that any form of fine art is difficult to master and almost impossible to perfect, and music is no exception. Hence, \'difficult\' is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(a) difficult <br>&lsquo;Difficult&rsquo; का अर्थ है जिसे पूरा करने के लिए बहुत प्रयास या कौशल की आवश्यकता हो। दिए गए passage में कहा गया है कि fine arts के किसी भी form में महारत हासिल करना कठिन है और परिपक्व होना लगभग impossible है, और music कोई exception नहीं है। अतः, \'difficult\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong><br>Music is perhaps the most popular and widely practised (96)______ of fine arts, transcending all kinds of cultural and linguistic barriers. Any form of fine art is (97)________ to master and almost impossible to perfect, and music is no exception.&nbsp;Nature, it is learnt, has blessed almost two-thirds of the human race with musical ability of some sort. Music has the power to (98)______ out the deepest emotions. In fact, it is a magic medicine, and many seek refuge in it when they are depressed or stressed. It is this intimacy that makes us listen to music (99)______ even hum or sing sometimes. This singing, or realistically speaking, expressing one&rsquo;s emotion musically sometimes takes a serious turn. The desire to sing before an audience is innocent and beautiful, and indeed, it is perfectly alright to have such a genuine desire. But it is also important to understand that singing is an (100)________ art&mdash;a highly refined one at that, which requires systematic, prolonged and strict training to be acceptable.<br>Choose the most appropriate option to fill in blank no. 98:</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong><br>Music is perhaps the most popular and widely practised (96)______ of fine arts, transcending all kinds of cultural and linguistic barriers. Any form of fine art is (97)________ to master and almost impossible to perfect, and music is no exception. Nature, it is learnt, has blessed almost two-thirds of the human race with musical ability of some sort. Music has the power to (98)______ out the deepest emotions. In fact, it is a magic medicine, and many seek refuge in it when they are depressed or stressed. It is this intimacy that makes us listen to music (99)______ even hum or sing sometimes. This singing, or realistically speaking, expressing one&rsquo;s emotion musically sometimes takes a serious turn. The desire to sing before an audience is innocent and beautiful, and indeed, it is perfectly alright to have such a genuine desire. But it is also important to understand that singing is an (100)________ art&mdash;a highly refined one at that, which requires systematic, prolonged and strict training to be acceptable.<br>Choose the most appropriate option to fill in blank no. 98:</p>",
                    options_en: ["<p>bring</p>", "<p>catalyse</p>", 
                                "<p>yield</p>", "<p>effect</p>"],
                    options_hi: ["<p>bring</p>", "<p>catalyse</p>",
                                "<p>yield</p>", "<p>effect</p>"],
                    solution_en: "<p>98.(a) bring <br>&lsquo;Bring out&rsquo; means to reveal or cause to be seen. The given passage states that music has the power to reveal the deepest emotions. Hence, &lsquo;bring&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(a) bring <br>&lsquo;Bring out&rsquo; का अर्थ है प्रकट करना या दिखाई देना। दिए गए passage में कहा गया है कि music में deepest emotions को प्रकट करने की शक्ति है। अतः, &lsquo;bring&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:</strong><br>Music is perhaps the most popular and widely practised (96)______ of fine arts, transcending all kinds of cultural and linguistic barriers. Any form of fine art is (97)________ to master and almost impossible to perfect, and music is no exception.&nbsp;Nature, it is learnt, has blessed almost two-thirds of the human race with musical ability of some sort. Music has the power to (98)______ out the deepest emotions. In fact, it is a magic medicine, and many seek refuge in it when they are depressed or stressed. It is this intimacy that makes us listen to music (99)______ even hum or sing sometimes. This singing, or realistically speaking, expressing one&rsquo;s emotion musically sometimes takes a serious turn. The desire to sing before an audience is innocent and beautiful, and indeed, it is perfectly alright to have such a genuine desire. But it is also important to understand that singing is an (100)________ art&mdash;a highly refined one at that, which requires systematic, prolonged and strict training to be acceptable.<br>Choose the most appropriate option to fill in blank no. 99:</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong><br>Music is perhaps the most popular and widely practised (96)______ of fine arts, transcending all kinds of cultural and linguistic barriers. Any form of fine art is (97)________ to master and almost impossible to perfect, and music is no exception. Nature, it is learnt, has blessed almost two-thirds of the human race with musical ability of some sort. Music has the power to (98)______ out the deepest emotions. In fact, it is a magic medicine, and many seek refuge in it when they are depressed or stressed. It is this intimacy that makes us listen to music (99)______ even hum or sing sometimes. This singing, or realistically speaking, expressing one&rsquo;s emotion musically sometimes takes a serious turn. The desire to sing before an audience is innocent and beautiful, and indeed, it is perfectly alright to have such a genuine desire. But it is also important to understand that singing is an (100)________ art&mdash;a highly refined one at that, which requires systematic, prolonged and strict training to be acceptable.<br>Choose the most appropriate option to fill in blank no. 99:</p>",
                    options_en: ["<p>or</p>", "<p>neither</p>", 
                                "<p>nor</p>", "<p>either</p>"],
                    options_hi: ["<p>or</p>", "<p>neither</p>",
                                "<p>nor</p>", "<p>either</p>"],
                    solution_en: "<p>99.(a) or <br>&lsquo;Or&rsquo; is used to connect two or more possibilities. The given passage states two possible effects of intimacy. First is to listen to music. Second is to hum or sing sometimes. Hence, &lsquo;or&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a) or <br>&lsquo;Or&rsquo; का प्रयोग दो या अधिक possibilities को जोड़ने के लिए किया जाता है। दिए गए passage में intimacy के दो possible effects बताए गए हैं। पहला है music सुनना। दूसरा है कभी-कभी गुनगुनाना या गाना। अतःं, &lsquo;or&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:</strong><br>Music is perhaps the most popular and widely practised (96)______ of fine arts, transcending all kinds of cultural and linguistic barriers. Any form of fine art is (97)________ to master and almost impossible to perfect, and music is no exception.&nbsp;Nature, it is learnt, has blessed almost two-thirds of the human race with musical ability of some sort. Music has the power to (98)______ out the deepest emotions. In fact, it is a magic medicine, and many seek refuge in it when they are depressed or stressed. It is this intimacy that makes us listen to music (99)______ even hum or sing sometimes. This singing, or realistically speaking, expressing one&rsquo;s emotion musically sometimes takes a serious turn. The desire to sing before an audience is innocent and beautiful, and indeed, it is perfectly alright to have such a genuine desire. But it is also important to understand that singing is an (100)________ art&mdash;a highly refined one at that, which requires systematic, prolonged and strict training to be acceptable.<br>Choose the most appropriate option to fill in blank no. 100:</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong><br>Music is perhaps the most popular and widely practised (96)______ of fine arts, transcending all kinds of cultural and linguistic barriers. Any form of fine art is (97)________ to master and almost impossible to perfect, and music is no exception.&nbsp;Nature, it is learnt, has blessed almost two-thirds of the human race with musical ability of some sort. Music has the power to (98)______ out the deepest emotions. In fact, it is a magic medicine, and many seek refuge in it when they are depressed or stressed. It is this intimacy that makes us listen to music (99)______ even hum or sing sometimes. This singing, or realistically speaking, expressing one&rsquo;s emotion musically sometimes takes a serious turn. The desire to sing before an audience is innocent and beautiful, and indeed, it is perfectly alright to have such a genuine desire. But it is also important to understand that singing is an (100)________ art&mdash;a highly refined one at that, which requires systematic, prolonged and strict training to be acceptable.<br>Choose the most appropriate option to fill in blank no. 100:</p>",
                    options_en: ["<p>uncomplicated</p>", "<p>uniform</p>", 
                                "<p>mixed</p>", "<p>intricate</p>"],
                    options_hi: ["<p>uncomplicated</p>", "<p>uniform</p>",
                                "<p>mixed</p>", "<p>intricate</p>"],
                    solution_en: "<p>100.(d) intricate <br>&lsquo;Intricate&rsquo; means very complicated. The given passage states that singing is an intricate art&mdash;a highly refined one at that, which requires systematic, prolonged and strict training to be acceptable. Hence, \'intimate\' is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) intricate <br>&lsquo;Intricate&rsquo; का अर्थ है बहुत जटिल। दिए गए passage में कहा गया है कि singing एक जटिल कला है - एक अत्यधिक परिष्कृत कला(refined art), जिसे स्वीकार्य होने के लिए systematic, लंबे समय तक और सख्त प्रशिक्षण की आवश्यकता होती है। अतः, \'intimate\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>