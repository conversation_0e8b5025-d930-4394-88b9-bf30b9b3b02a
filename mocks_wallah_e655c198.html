<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. X and Y together can complete a work in 60 days. They started the work together but after 30 days X left the work. If the remaining work is completed by Y in 75 more days, then X alone can complete the entire work in how many days ?</p>",
                    question_hi: "<p>1. X और Y मिलकर एक काम को 60 दिनों में पूरा कर सकते हैं। दोनों ने एक साथ मिलकर काम शुरू किया लेकिन 30 दिनों के बाद X ने काम छोड़ दिया। यदि शेष काम को पूरा करने में Y को 75 दिन और लगते हैं, तो X अकेले संपूर्ण काम को कितने दिनों में पूरा कर सकता है?</p>",
                    options_en: ["<p>90 days</p>", "<p>70 days</p>", 
                                "<p>100 days</p>", "<p>80 days</p>"],
                    options_hi: ["<p>90 दिन</p>", "<p>70 दिन</p>",
                                "<p>100 दिन</p>", "<p>80 दिन</p>"],
                    solution_en: "<p>1.(c)<br>Let the total work be 1 unit.<br>Work done by X&nbsp;and Y together in 60 days = 1 unit<br>According to question,<br>Work done by X and Y together in 30 days = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>&nbsp;unit.<br>Remaining work (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> unit) done by Y in 75 days.<br>Then, time taken by Y to complete the whole work = 75 &times; 2 = 150 days<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489445846.png\" alt=\"rId4\" width=\"236\" height=\"175\"><br>Efficiency of X&nbsp;= 5 - 2 = 3 units<br>Therefore, required time = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 100 days.</p>\n<p><strong>Short trick : -</strong><br>On comparison to the remaining work.<br>x &times; 30 = y &times; (75 - 30)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math><br>Total work will be = 60 &times; (3 + 2) = 300 units<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 100 days</p>",
                    solution_hi: "<p>1.(c)<br>माना कि कुल कार्य 1 इकाई है।<br>प्रश्न के अनुसार,<br>X और Y द्वारा 60 दिनों में एक साथ किया गया कार्य = 1 इकाई।<br>X और Y द्वारा 30 दिनों में एक साथ किया गया कार्य = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> इकाई।<br>शेष कार्य (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> इकाई) Y द्वारा 75 दिनों में पूरा किया गया।<br>तब, Y द्वारा पूरा कार्य पूरा करने में लिया गया समय = 75 &times; 2 = 150 दिन<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489445986.png\" alt=\"rId5\" width=\"190\" height=\"164\"><br>X की दक्षता = 5 - 2 = 3 इकाई<br>अत: आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 100 दिन।</p>\n<p><strong>शार्ट ट्रिक:-</strong><br>शेष काम की तुलना करने पर<br>x &times; 30 = y &times; (75 - 30)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math><br>कुल कार्य = 60 &times; (3 + 2) = 300 इकाई होगा<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 100 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. M, N and O working alone can complete a work in 10, 12 and 15 days respectively. In how many days can M, N and O together complete&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> parts of the same work?</p>",
                    question_hi: "<p>2. M, N और O अकेले-अकेले तौर पर एक काम को क्रमशः 10, 12 और 15 दिनों में पूरा कर सकते हैं। M, N और O मिलकर उसी काम का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> भाग कितने दिनों में पूरा कर सकते हैं?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>"],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489446088.png\" alt=\"rId6\" width=\"298\" height=\"150\"><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>th of the total work = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 10 unit<br>efficiency (M + N + O) = 6 + 5 + 4 = 15 unit/day<br>Hence, required time = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> day</p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489446198.png\" alt=\"rId7\" width=\"279\" height=\"161\"><br>कुल कार्य का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>वाँ भाग = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 10 यूनिट<br>दक्षता (M + N + O) = 6 + 5 + 4 = 15 यूनिट/दिन<br>अत: आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. John can complete a work in 12 days and Anju can complete the same work in 20 days. If they work together on it for 6 days, the amount of work remaining is ________?</p>",
                    question_hi: "<p>3. जॉन एक काम को 12 दिनों में और अंजू उसी काम को 20 दिनों में पूरा कर सकती है। यदि वे इस पर 6 दिनों तक एक साथ मिलकर काम करते हैं, तो शेष काम का अंश ________ है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489446301.png\" alt=\"rId8\" width=\"236\" height=\"155\"><br>Work done by both in 6 days = 6 &times; (5 + 3) = 48 unit<br>Remaining work = 60 - 48 = 12 unit<br>Hence, Amount of work remaining = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489446391.png\" alt=\"rId9\" width=\"220\" height=\"146\"><br>दोनों द्वारा 6 दिनों में किया गया कार्य = 6 &times; (5 + 3) = 48 इकाई<br>शेष कार्य = 60 - 48 = 12 इकाई<br>अत: शेष काम का अंश = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. E alone can complete a work in 15 days. F and G together can complete the same work in 10 days. E, F and G together will complete the same work in how many days ?</p>",
                    question_hi: "<p>4. E अकेले एक काम को 15 दिनों में पूरा कर सकता है। F और G मिलकर उसी काम को 10 दिनों में पूरा कर सकते हैं। E, F और G मिलकर उसी काम को कितने दिनों में पूरा करेंगे?</p>",
                    options_en: ["<p>12 days</p>", "<p>8 days</p>", 
                                "<p>10 days</p>", "<p>6 days</p>"],
                    options_hi: ["<p>12 दिन</p>", "<p>8 दिन</p>",
                                "<p>10 दिन</p>", "<p>6 दिन</p>"],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489446520.png\" alt=\"rId10\" width=\"213\" height=\"167\"><br>Efficiency (E + F + G) = 2 + 3 = 5 Unit<br>Required days = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 6 days</p>",
                    solution_hi: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489446647.png\" alt=\"rId11\" width=\"209\" height=\"174\"><br>क्षमता (E + F + G) = 2 + 3 = 5 इकाई <br>आवश्यक दिन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math>&nbsp;= 6 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Pankaj is 40 percent less efficient than Rahul. If Rahul can make a computer in 16 days, then Pankaj can make the same computer in how many days?</p>",
                    question_hi: "<p>5. पंकज, राहुल से 40 प्रतिशत कम कुशल है। यदि राहुल एक कंप्यूटर को 16 दिनों में बना सकता है, तो पंकज उसी कंप्यूटर को कितने दिनों में बना सकता है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> days</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> दिन</p>"],
                    solution_en: "<p>5.(a) <br>Ratio -&nbsp; Pankaj : Rahul<br>Efficiency -&nbsp; 3&nbsp; &nbsp;:&nbsp; &nbsp;5 <br>Total work = 16 &times; 5 = 80 unit<br>Time taken by Pankaj to done whole work = <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>5.(a) <br>अनुपात - पंकज&nbsp; :&nbsp; राहुल<br>दक्षता&nbsp; -&nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; :&nbsp; &nbsp;5<br>कुल कार्य = 16 &times; 5 = 80 इकाई <br>पंकज द्वारा पूरा कार्य करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Deepak alone can do a work in 64 hours. Deepak and Rahul together can do the same work in 32 hours. Rahul and Karan together can do the same work in 4 hours. In how many hours can Karan alone do the same work ?</p>",
                    question_hi: "<p>6. दीपक अकेले एक काम को 64 घंटे में पूरा कर सकता है। दीपक और राहुल मिलकर उसी काम को 32 घंटे में पूरा कर सकते हैं। राहुल और करण मिलकर उसी काम को 4 घंटे में पूरा कर सकते हैं। करण अकेले उसी काम को कितने घंटों में पूरा कर सकता है ?</p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>17</mn></mfrac></math>&nbsp;hours</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>8</mn></mfrac></math>&nbsp;hours</p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>15</mn></mfrac></math>&nbsp;hours</p>", "<p>3.5 hours</p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>17</mn></mfrac></math> घंटे</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>8</mn></mfrac></math> घंटे</p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>15</mn></mfrac></math> घंटे</p>", "<p>3.5 घंटे</p>"],
                    solution_en: "<p>6.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489446741.png\" alt=\"rId12\" width=\"297\" height=\"164\"><br>Efficiency of Rahul = 2 - 1 = 1 units<br>Efficiency of Karan = 16 - 1 = 15 units<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> hours</p>",
                    solution_hi: "<p>6.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489446900.png\" alt=\"rId13\" width=\"338\" height=\"175\"><br>राहुल की दक्षता = 2 - 1 = 1 इकाई<br>करण की दक्षता = 16 - 1 = 15 इकाई<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Y can complete a work in 20 days and Z can complete the same work in 10 days. If both of them work together, then in 2 days what percent work of the total work will be completed ?</p>",
                    question_hi: "<p>7. Y एक काम को 20 दिनों में पूरा कर सकता है और Z उसी काम को 10 दिनों में पूरा कर सकता है। यदि वे दोनों एक साथ मिलकर काम करें, तो 2 दिनों में कुल काम का कितना प्रतिशत काम पूरा हो जाएगा ?</p>",
                    options_en: ["<p>45 percent</p>", "<p>35 percent</p>", 
                                "<p>30 percent</p>", "<p>33.3 percent</p>"],
                    options_hi: ["<p>45 प्रतिशत</p>", "<p>35 प्रतिशत</p>",
                                "<p>30 प्रतिशत</p>", "<p>33.3 प्रतिशत</p>"],
                    solution_en: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489447010.png\" alt=\"rId14\" width=\"223\" height=\"140\"><br>Work done by Y and Z in 2 days = (1 + 2) &times; 2 = 6 units<br>required% = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 30%</p>",
                    solution_hi: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489447111.png\" alt=\"rId15\" width=\"227\" height=\"151\"><br>Y और Z द्वारा 2 दिनों में किया गया कार्य = (1 + 2) &times; 2 = 6 इकाई<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 30%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Minto alone can make a table in 75 days while Pinto alone can make it in 150 days. Pinto started the work and worked for 30 days. In how many days Minto can finish the remaining work ?</p>",
                    question_hi: "<p>8. मिंटो अकेले एक मेज 75 दिनों में बना सकता है, जबकि पिंटो अकेले इसे 150 दिनों में बना सकता है। पिंटो ने काम शुरू किया और 30 दिनों तक काम किया। मिंटो शेष काम कितने दिनों में पूरा कर सकता है ?</p>",
                    options_en: ["<p>75 days</p>", "<p>60 days</p>", 
                                "<p>70 days</p>", "<p>85 days</p>"],
                    options_hi: ["<p>75 दिन</p>", "<p>60 दिन</p>",
                                "<p>70 दिन</p>", "<p>85 दिन</p>"],
                    solution_en: "<p>8.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489447246.png\" alt=\"rId16\" width=\"203\" height=\"151\"><br>Worked done by pinto in 30 days = 30 &times;&nbsp;1 = 30 unit<br>Remaining worked done by Minto = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 60 days</p>",
                    solution_hi: "<p>8.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489447447.png\" alt=\"rId17\" width=\"212\" height=\"162\"><br>पिंटो द्वारा 30 दिनों में किया गया कार्य = 30 &times;&nbsp;1 = 30 इकाई<br>मिंटो द्वारा किया गया शेष कार्य = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 60 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. A, B, and C can complete a piece of work in 16 days, 24 days, and 32 days respectively. How many days will all three take together to complete the work ?</p>",
                    question_hi: "<p>9. A, B और C एक काम को क्रमशः 16 दिन, 24 दिन और 32 दिन में पूरा कर सकते हैं। यदि वे तीनों एक साथ मिलकर काम करते है, तो उन्हें उस काम को पूरा करने में कितने दिन लगेंगे ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>99</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> days</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>99</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> दिन</p>"],
                    solution_en: "<p>9.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489447670.png\" alt=\"rId18\" width=\"212\" height=\"119\"><br>Time taken by all three to complete the work =&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mrow><mn>6</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>13</mn></mfrac><mo>&#160;</mo></math>days</p>",
                    solution_hi: "<p>9.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489447797.png\" alt=\"rId19\" width=\"176\" height=\"129\"><br>कार्य को पूरा करने में तीनों द्वारा लिया गया समय =&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mrow><mn>6</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>13</mn></mfrac><mo>&#160;</mo></math> दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If Y can complete <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> parts of a work in 60 days, then how many days will it take for Y to complete <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math>parts of the same work ?</p>",
                    question_hi: "<p>10. यदि Y किसी काम का <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> भाग 60 दिनों में पूरा कर सकता है, तो उसी काम का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math> भाग पूरा करने में Y को कितने दिनों का समय लगेगा?</p>",
                    options_en: ["<p>15 days</p>", "<p>10 days</p>", 
                                "<p>11 days</p>", "<p>12 days</p>"],
                    options_hi: ["<p>15 दिन</p>", "<p>10 दिन</p>",
                                "<p>11 दिन</p>", "<p>12 दिन</p>"],
                    solution_en: "<p>10.(b)<br>Time taken by Y to complete the whole work = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 100 days<br>So, time taken by Y to complete <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math>th of the same work = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 10 days.</p>",
                    solution_hi: "<p>10.(b)<br>पूरा कार्य पूरा करने में Y द्वारा लिया गया समय = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 100 दिन<br>अतः, Y द्वारा उसी कार्य का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math>&nbsp;वां भाग पूरा करने में लिया गया समय = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 10 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If 27 men take 15 days to mow 225 hectares, how much time will 33 men take to mow 165 hectares ?</p>",
                    question_hi: "<p>11. यदि 27 आदमी 225 हेक्टेयर घास काटने में 15 दिन लगाते हैं, तो 33 आदमी 165 हेक्टेयर घास काटने में कितना समय लेंगे?</p>",
                    options_en: ["<p>12 days</p>", "<p>14 days</p>", 
                                "<p>9 days</p>", "<p>11 days</p>"],
                    options_hi: ["<p>12 दिन</p>", "<p>14 दिन</p>",
                                "<p>9 दिन</p>", "<p>11 दिन</p>"],
                    solution_en: "<p>11.(c)<br>Formula used :- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>1</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>1</mn></msub></mfrac></math> =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>2</mn></msub></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>225</mn><mo>&#160;</mo></mrow></mfrac></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>33</mn><mo>&#215;</mo><mi mathvariant=\"normal\">x</mi></mrow><mn>165</mn></mfrac></math> &rArr; x = 9 days</p>",
                    solution_hi: "<p>11.(c)<br>प्रयुक्त सूत्र:- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>1</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>1</mn></msub></mfrac></math> =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>2</mn></msub></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>225</mn><mo>&#160;</mo></mrow></mfrac></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>33</mn><mo>&#215;</mo><mi mathvariant=\"normal\">x</mi></mrow><mn>165</mn></mfrac></math> &rArr; x = 9 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. X and Y together can complete a work in 10 days. Y and Z together can complete the same work in 12.5 days. Z and X together can complete the same work in 50 days. In how many days can all the three complete the same work while working together?</p>",
                    question_hi: "<p>12. X और Y मिलकर एक काम को 10 दिनों में पूरा कर सकते हैं। Y और Z मिलकर उसी काम को 12.5 दिनों में पूरा कर सकते हैं। Z और x मिलकर उसी काम को 50 दिनों में पूरा कर सकते हैं ? तीनों एक साथ काम करते हुए उसी काम को कितने दिनों में पूरा कर सकते हैं।</p>",
                    options_en: ["<p>12.5 days</p>", "<p>15 days</p>", 
                                "<p>10 days</p>", "<p>20 days</p>"],
                    options_hi: ["<p>12.5 दिन</p>", "<p>15 दिन</p>",
                                "<p>10 दिन</p>", "<p>20 दिन</p>"],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489447934.png\" alt=\"rId20\" width=\"236\" height=\"133\"><br>Efficiency of 2(X + Y + Z) = 5 + 4 + 1<br>Efficiency of (X + Y + Z) = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5 units<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 10 days</p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489448157.png\" alt=\"rId21\" width=\"213\" height=\"136\"><br>2(X + Y + Z) की दक्षता = 5 + 4 + 1<br>(X + Y + Z) की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5 इकाई<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 10 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. P and Q together can complete a work in 30 days. They started the work together but after 20 days P left the work. If the remaining work is completed by Q in 50 more days, then P alone can complete the entire work in how many days ?</p>",
                    question_hi: "<p>13. P और Q मिलकर एक काम को 30 दिनों में पूरा कर सकते हैं। उन्होंने एक साथ मिलकर काम शुरू किया लेकिन 20 दिनों के बाद P ने काम छोड़ दिया। यदि शेष काम को पूरा करने में Q को 50 दिन और लगते हैं, तो P अकेले संपूर्ण काम को कितने दिनों में पूरा कर सकता है ?</p>",
                    options_en: ["<p>37.5 days</p>", "<p>170 days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>", "<p>150 days</p>"],
                    options_hi: ["<p>37.5 दिन</p>", "<p>170 दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>", "<p>150 दिन</p>"],
                    solution_en: "<p>13.(a)<br>On comparison to the remaining work.<br>P &times; 10 = Q &times; 40<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>1</mn></mfrac></math><br>Total work = 30(4 + 1) = 150 units<br>Time taken by P to complete the whole work = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 37.5 days</p>\n<p><strong>Alternate method : -</strong> <br>(P + Q) &times; 30 = (P + Q) &times; 20 + 50Q<br>30P + 30Q = 20P + 20Q + 50Q<br>10P = 40Q<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>1</mn></mfrac></math><br>Total work = 30(4 + 1) = 150 units<br>Time taken by P to complete the whole work = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 37.5 days</p>",
                    solution_hi: "<p>13.(a)<br>शेष काम की तुलना करने पर,<br>P &times; 10 = Q &times; 40<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>1</mn></mfrac></math><br>कुल कार्य = 30(4 + 1) = 150 इकाई <br>P द्वारा पूरा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 37.5 दिन</p>\n<p><strong>वैकल्पिक विधि :-</strong><br>(P + Q) &times; 30 = (P + Q) &times; 20 + 50Q<br>30P + 30Q = 20P + 20Q + 50Q<br>10P = 40Q<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>1</mn></mfrac></math><br>कुल कार्य = 30(4 + 1) = 150 इकाई <br>P द्वारा पूरा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 37.5 दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Sharman is 20 percent less efficient than Vivek. If Vivek can make a helmet in 20 days, then in how many days can Sharman make the same helmet?</p>",
                    question_hi: "<p>14. शरमन विवेक से 20 प्रतिशत कम कुशल है। यदि विवेक एक हेलमेट 20 दिनों में बना सकता है, तो शरमन उसी हेलमेट को कितने दिनों में बना सकता है?</p>",
                    options_en: ["<p>27 days</p>", "<p>30 days</p>", 
                                "<p>23 days</p>", "<p>25 days</p>"],
                    options_hi: ["<p>27 दिन</p>", "<p>30 दिन</p>",
                                "<p>23 दिन</p>", "<p>25 दिन</p>"],
                    solution_en: "<p>14.(d) According to question, <br>Efficiency of &rarr; Sharman&nbsp; :&nbsp; Vivek<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 5 <br>Total work done by Vivek = 20 &times;&nbsp;5 = 100 unit<br>No. of days taken by sharman to make the same helmet = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 25 days </p>",
                    solution_hi: "<p>14.(d) प्रश्न के अनुसार,<br>दक्षता &rarr; शरमन&nbsp; :&nbsp; विवेक <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;5 <br>विवेक द्वारा किया गया कुल कार्य = 20 &times;&nbsp;5 = 100 इकाई<br>शरमन द्वारा उसी हेलमेट को बनाने में लिए गए दिनों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 25 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. R and S together can complete a work in 60 days. S and T together can complete the same work in 90 days. If R, S and T all work together, then the same work gets completed in 45 days. How many days will R and T take together to complete the same work ?</p>",
                    question_hi: "<p>15. R और S मिलकर एक काम को 60 दिनों में पूरा कर सकते हैं। S और T मिलकर उसी काम को 90 दिनों में पूरा कर सकते हैं। यदि R, S और T सभी एक साथ मिलकर काम करते हैं, तो वही काम 45 दिनों में पूरा हो जाता है। R और T को मिलकर समान काम को पूरा करने में कितने दिन का समय लगेगा ?</p>",
                    options_en: ["<p>75 days</p>", "<p>80 days</p>", 
                                "<p>50 days</p>", "<p>60 days</p>"],
                    options_hi: ["<p>75 दिन</p>", "<p>80 दिन</p>",
                                "<p>50 दिन</p>", "<p>60 दिन</p>"],
                    solution_en: "<p>15.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489448382.png\" alt=\"rId22\" width=\"250\"><br>Efficiency of R = 2, efficiency of T = 1<br>Then, time taken by R and T to complete the work = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 60 days</p>",
                    solution_hi: "<p>15.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731489448660.png\" alt=\"rId23\" width=\"250\"><br>R की क्षमता = 2, T की क्षमता = 1<br>तब, कार्य पूरा करने में R और T द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 60 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>