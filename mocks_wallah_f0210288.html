<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">8:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Mukhyamantri Bagayat Vikas Mission (Horticulture Development Mission) scheme was launched by which of the following state governments in January 2021?</p>\r\n<p>SSC CGL 23/08/21(Morning)</p>",
                    question_hi: " <p> 1. मुख्यमंत्री बगायत विकास मिशन (बागवानी विकास मिशन) योजना जनवरी 2021 में निम्नलिखित में से किस राज्य सरकार द्वारा शुरू की गई थी? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: ["<p>Gujarat</p>", "<p>Maharashtra</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Bihar</p>"],
                    options_hi: [" <p>  गुजरात</span></p>", " <p> महाराष्ट्र</span></p>",
                                " <p>  उत्तर प्रदेश</span></p>", " <p> बिहार</span></p>"],
                    solution_en: "<p>1.(a) Mukhyamantri Bagayat Vikas Mission scheme was launched by Gujarat in January 2021. The main aim of this Abhiyan is to promote agriculture, horticulture and cultivation of herbal plants.</p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>1.(a) Mukhyamantri Bagayat Vikas Mission scheme was launched by Gujarat in January 2021. The main aim of this Abhiyan is to promote agriculture, horticulture and cultivation of herbal plants.</p>\r\n<p>&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Palanquin Dark;\">2. The area served by a canal system through the supply of water for irrigation and other&nbsp; </span><span style=\"font-family: Palanquin Dark;\">purposes is called a ______.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p> 2. नहर प्रणाली द्वारा सिंचाई और अन्य उद्देश्यों के लिए पानी की आपूर्ति के माध्यम से सेवा प्रदान करने वाले क्षेत्र को ________ कहा जाता है। </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: ["<p>command area</p>", "<p>net sown area</p>", 
                                "<p>net irrigated area</p>", "<p>gross irrigated area</p>"],
                    options_hi: [" <p>  कमान क्षेत्र</span></p>", " <p> शुद्ध बोया क्षेत्र</span></p>",
                                " <p>  शुद्ध सिंचित क्षेत्र</span></p>", " <p> सकल सिंचित क्षेत्र</span></p>"],
                    solution_en: "<p>2.(a) The area served by a canal system through the supply of water for irrigation and other purposes is called a command area. It is the total area that can be economically irrigated from an irrigation scheme without considering the limitations of water. It includes cultivable land, roads, wastelands, forests, barren lands.</p>",
                    solution_hi: " <p>2.(a) नहर प्रणाली द्वारा सिंचाई और अन्य उद्देश्यों के लिए पानी की आपूर्ति के माध्यम से सेवा क्षेत्र को कमांड क्षेत्र कहा जाता है। यह कुल क्षेत्रफल है जिसे पानी की सीमाओं पर विचार किए बिना एक सिंचाई योजना से आर्थिक रूप से सिंचित किया जा सकता है। इसमें कृषि योग्य भूमि, सड़कें, बंजर भूमि, जंगल, बंजर भूमि शामिल हैं।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3<span style=\"font-family: Palanquin Dark;\">. When a river originates from a hill and flows in all directions, the drainage pattern is&nbsp; </span><span style=\"font-family: Palanquin Dark;\">known as ______. </span></p>\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: "<p>3. जब कोई नदी पहाड़ी से निकलती है और सभी दिशाओं में बहती है, तो अपवाह पैटर्न को ______ कहा जाता है।</p>\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: ["<p>centripetal</p>", "<p>trellis</p>", 
                                "<p>dendritic</p>", "<p>radial</p>"],
                    options_hi: ["<p>अभिकेंद्री</p>", "<p>जाफ़री</p>",
                                "<p>द्रुमाकृतिक</p>", "<p>अरीय</p>"],
                    solution_en: "<p>3.(d) When a river originates from a hill and flows in all directions, it is known as a radial pattern.</p>\n<p><span style=\"font-family: Palanquin Dark;\">Other types of drainage patterns are&ndash;</span></p>\n<p><span style=\"font-family: Palanquin Dark;\">Centripetal&ndash; when rivers discharge their waters from all directions into a depression. Eg. Loktak lake in Manipur.</span></p>\n<p><span style=\"font-family: Palanquin Dark;\">Trellis&ndash; when the primary tributaries of main rivers flow parallel to each other and secondary tributaries join them at right angles.</span></p>\n<p><span style=\"font-family: Palanquin Dark;\">Dendritic&ndash;looks like the branching pattern of tree roots. </span></p>",
                    solution_hi: "<p>3.(d) जब कोई नदी पहाड़ी से निकलती है और सभी दिशाओं में बहती है, तो इसे अरिय पैटर्न के रूप में जाना जाता है।<br><span style=\"font-family: Palanquin Dark;\">अन्य प्रकार के जल निकासी पैटर्न हैं-<br></span><span style=\"font-family: Palanquin Dark;\">अभिकेंद्री- जब नदियाँ अपना जल सभी दिशाओं से एक गर्त में प्रवाहित करती हैं। उदाहरण मणिपुर में लोकटक झील।<br></span><span style=\"font-family: Palanquin Dark;\">जाफ़री- जब मुख्य नदियों की प्राथमिक सहायक नदियाँ एक दूसरे के समानांतर बहती हैं और द्वितीयक सहायक नदियाँ समकोण पर उनसे मिलती हैं।<br></span><span style=\"font-family: Palanquin Dark;\">द्रुमाकृतिक - पेड़ की जड़ों की शाखाओं के पैटर्न की तरह दिखता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: " <p>4</span><span style=\"font-family:Palanquin Dark\">. At which of the following places was the ‘Street Theatre and Performing Arts  Fellowship’ scheme launched in January 2021 to give artists an opportunity to  showcase their craft? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>4. निम्नलिखित में से किस स्थान पर कलाकारों को अपने शिल्प का प्रदर्शन करने का अवसर देने के लिए जनवरी 2021 में \'स्ट्रीट थिएटर एंड परफॉर्मिंग आर्ट्स फेलोशिप\' योजना शुरू की गई थी? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Haryana </span></p>", " <p> Goa </span></p>", 
                                " <p> Delhi </span></p>", " <p> Chandigarh </span></p>"],
                    options_hi: [" <p>  हरियाणा</span></p>", " <p> गोवा</span></p>",
                                " <p>  दिल्ली</span></p>", " <p> चंडीगढ़</span></p>"],
                    solution_en: " <p>4.(c) The Fellowship scheme was launched in Delhi with the objective of giving artists an opportunity to showcase their craft. This fellowship </span><span style=\"font-family:Palanquin Dark\">has been started by Sahitya Kala Parishad under the Department of Arts, Culture and Languages of the Delhi government and</span><span style=\"font-family:Palanquin Dark\"> will be given to artists </span><span style=\"font-family:Palanquin Dark\">from diverse backgrounds including singers, dancers, and hip-hop artists.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Palanquin Dark\">4.(c) फैलोशिप योजना दिल्ली में कलाकारों को अपने शिल्प का प्रदर्शन करने का अवसर देने के उद्देश्य से शुरू की गई थी। यह फेलोशिप दिल्ली सरकार के कला, संस्कृति और भाषा विभाग के तहत साहित्य कला परिषद द्वारा शुरू की गई है और गायकों, नर्तकियों और हिप-हॉप कलाकारों सहित विविध पृष्ठभूमि के कलाकारों को दी जाएगी।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5<span style=\"font-family: Palanquin Dark;\">. Which of the following liquids has the highest pH?</span></p>\n<p><span style=\"font-family: Palanquin Dark;\"> SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: "<p>5. निम्नलिखित में से किस तरल पदार्थ का pH सबसे अधिक होता है?</p>\n<p><span style=\"font-family: Palanquin Dark;\"> SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: ["<p>Human blood</p>", "<p>Milk of magnesia</p>", 
                                "<p>Lemon juice</p>", "<p>Orange juice</p>"],
                    options_hi: ["<p>मानव रक्त</p>", "<p>मिल्क ऑफ़ मैग्नीशिया</p>",
                                "<p>नींबू का रस</p>", "<p>संतरे का रस</p>"],
                    solution_en: "<p>5.(b) Milk of magnesia has the highest pH with a pH value of 10.5 because it is basic by nature.<br><span style=\"font-family: Palanquin Dark;\">pH value of Human Blood (7.40), Lemon Juice (2-3), Orange juice (3.3-4.2).</span></p>",
                    solution_hi: "<p>5.(b) मिल्क ऑफ मैग्नेशिया का pH मान 10.5 है जो उच्चतम pH है क्योंकि यह प्रकृति से उदासीन है।<br><span style=\"font-family: Palanquin Dark;\">मानव रक्त का pH मान (7.40), नींबू का रस (2-3), संतरे का रस (3.3-4.2)।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6<span style=\"font-family: Palanquin Dark;\">. Which of the following books is authored by Dalai Lama and launched by him in February 2021? </span></p>\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: "<p>6. निम्नलिखित में से कौन सी पुस्तक दलाई लामा द्वारा लिखी गई है और उनके द्वारा फरवरी 2021 में लॉन्च की गई है?</p>\n<p><span style=\"font-family: Palanquin Dark;\"> SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: ["<p>The Little Book of Encouragement</p>", "<p>A Little Book of Courage</p>", 
                                "<p>The Little Book of Hope</p>", "<p>Connecting, Communicating, Changing</p>"],
                    options_hi: ["<p>द लिटल बुक ऑफ़ एन्क्रेज्मेंट</p>", "<p>द लिटल बुक ऑफ़ क्रेज</p>",
                                "<p>द लिटल बुक ऑफ़ हॉप</p>", "<p>कनेक्टिंग,कम्युनिकेटिंग,चेंजिंग</p>"],
                    solution_en: "<p>6.(a) The Little Book of Encouragement is authored by the Dalai Lama.<br><span style=\"font-family: Palanquin Dark;\">A Little Book of Courage (Ruskin Bond)<br></span><span style=\"font-family: Palanquin Dark;\">The Little Book of Hope (Paul Wilson)<br></span><span style=\"font-family: Palanquin Dark;\">Connecting, Communicating, Changing (Vice President Secretariat ).</span></p>",
                    solution_hi: "<p>6.(a) द लिटिल बुक ऑफ एनकॉरमेंट दलाई लामा द्वारा लिखा गया है।<br><span style=\"font-family: Palanquin Dark;\">ए लिटल बुक ऑफ़ करेज (रस्किन बांड)<br></span><span style=\"font-family: Palanquin Dark;\">ए लिटल बुक ऑफ़ हॉप (पॉल विल्सन)<br></span><span style=\"font-family: Palanquin Dark;\">कनेक्टिंग, कम्युनिकेटिंग, चेंजिंग (उपराष्ट्रपति सचिवालय)।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: " <p>7</span><span style=\"font-family:Palanquin Dark\">. In which of the following years was the micro-credit scheme ‘Street Vendor\'s  AtmaNirbhar Nidhi’ (‘PM SVANidhi’) launched? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: "<p>7. निम्नलिखित में से किस वर्ष में माइक्रो-क्रेडिट योजना \'फेरी वालों के लिए आत्म निर्भर निधि\' (&lsquo;PM स्वानिधि\') शुरू की गई थी?</p>\r\n<p>SSC CGL 23/08/21(Morning)</p>",
                    options_en: [" <p> 2017 </span></p>", " <p> 2020 </span></p>", 
                                " <p> 2014 </span></p>", " <p> 2015 </span></p>"],
                    options_hi: ["<p>2017</p>", "<p>2020</p>",
                                "<p>2014</p>", "<p>2015</p>"],
                    solution_en: " <p>7.(b) In 2020, the micro-credit scheme PM SVANidhi was launched. Under this scheme, a loan of Rs 10,000 with a low rate of interest has been provided to the street vendors for one year.</span></p>",
                    solution_hi: "<p>7.(b) 2020 में, माइक्रो-क्रेडिट योजना PM स्वनिधि शुरू की गई थी। इस योजना के तहत रेहड़ी-पटरी वालों को एक वर्ष के लिए कम ब्याज दर पर 10,000 रुपये का ऋण प्रदान किया गया है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: " <p>8</span><span style=\"font-family:Palanquin Dark\">. What do you call the property of an organism of self-regulation and the tendency to  maintain a steady-state within an external environment which is liable to change?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: "<p>8. आप स्व-नियमन के जीव की संपत्ति और बाहरी वातावरण के भीतर एक स्थिर-स्थिति बनाए रखने की प्रवृत्ति को क्या कहते हैं जो परिवर्तन के लिए उत्तरदायी है?</p>\n<p><span style=\"font-family: Palanquin Dark;\"> SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Irritability </span></p>", " <p> Consciousness </span></p>", 
                                "<p>Homeostasis</p>", "<p>Metabolism</p>"],
                    options_hi: ["<p>चिड़चिड़ापन</p>", "<p>चेतना</p>",
                                "<p>समस्थिति</p>", "<p>चयापचय</p>"],
                    solution_en: "<p>8.(c) The property of an organism of self-regulation and the tendency to maintain a steady-state within an external environment that is liable to change is called Homeostasis.<br><span style=\"font-family: Palanquin Dark;\">Irritability is the feeling of agitation or annoyance. Consciousness is awareness of internal and external environments. Metabolism is the chemical reaction in the body which changes food into energy.</span></p>",
                    solution_hi: "<p>8.(c) स्व-नियमन वाले जीव की संपत्ति और बाहरी वातावरण के भीतर एक स्थिर-स्थिति बनाए रखने की प्रवृत्ति जो परिवर्तन के लिए उत्तरदायी है, होमोस्टैसिस कहलाती है।<br><span style=\"font-family: Palanquin Dark;\">चिड़चिड़ापन आंदोलन या झुंझलाहट की भावना है। चेतना आंतरिक और बाहरी वातावरण के बारे में जागरूकता है। चयापचय शरीर में रासायनिक प्रतिक्रिया है जो भोजन को ऊर्जा में बदल देती है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: " <p>9</span><span style=\"font-family:Palanquin Dark\">.  ______ boundaries occur when plates collide and one plate is pushed under the other. </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>9. ________ सीमाएं तब होती हैं जब प्लेट आपस में टकराती हैं और एक प्लेट को दूसरी के नीचे धकेला जाता है। </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Convergent </span></p>", " <p> Transform </span></p>", 
                                " <p> Continental </span></p>", " <p> Divergent </span></p>"],
                    options_hi: [" <p>  अभिसरण</span></p>", " <p> रूपांतर</span></p>",
                                " <p>  महाद्वीपीय</span></p>", " <p> अपसारी</span></p>"],
                    solution_en: " <p>9.(a) Convergent boundaries occur when plates collide and one plate is pushed under the other. A transform plate boundary occurs when two plates slide and move horizontally. A divergent boundary is when two plates move away from each other.</span></p>",
                    solution_hi: " <p>9.(a) जब प्लेट आपस में टकराती हैं और एक प्लेट को दूसरी प्लेट के नीचे धकेला जाता है तो अभिसारी सीमाएँ उत्पन्न होती हैं। एक ट्रांसफॉर्म प्लेट सीमा तब होती है जब दो प्लेटें गति करती हैं और क्षैतिज रूप से चलती हैं। अपसारी सीमा तब होती है जब दो प्लेटें एक दूसरे से दूर जाती हैं।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: " <p>10</span><span style=\"font-family:Palanquin Dark\">. Muhammad Ghori attacked Tabarhinda (Bhatinda) in 1191, a strategic point for______. </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>10</span><span style=\"font-family:Palanquin Dark\">. मुहम्मद गोरी ने 1191 में तबरहिंडा (भटिंडा) पर हमला किया, जो ______ के लिए एक रणनीतिक बिंदु था। </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Maharana Pratap Singh </span></p>", " <p> Hem Chandra Vikramaditya </span></p>", 
                                " <p> Prithviraj Chauhan </span></p>", " <p> Rana Kumbha </span></p>"],
                    options_hi: [" <p>  महाराणा प्रताप सिंह</span></p>", " <p> हेम चंद्र विक्रमादित्य</span></p>",
                                " <p>  पृथ्वीराज चौहान</span></p>", " <p> राणा कुंभा </span></p>"],
                    solution_en: " <p>10.(c) Muhammad Ghori attacked Bhatinda in 1191, a strategic point for Prithviraj Chauhan.</span></p>",
                    solution_hi: " <p>10.(c) मुहम्मद गोरी ने 1191 में भटिंडा पर हमला किया, जो पृथ्वीराज चौहान के लिए एक रणनीतिक बिंदु था।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: " <p>11</span><span style=\"font-family:Palanquin Dark\">. Which Schedule of the Indian Constitution demarcates the powers of the Union and  the States, that is Union List, State List and Concurrent List? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning) </span></p>",
                    question_hi: " <p>11. भारतीय संविधान की कौन सी अनुसूची संघ और राज्यों की शक्तियों का सीमांकन करती है, अर्थात  संघ सूची, राज्य सूची और समवर्ती सूची? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Fourth Schedule </span></p>", " <p> First Schedule </span></p>", 
                                " <p> Seventh Schedule </span></p>", " <p> Third Schedule </span></p>"],
                    options_hi: [" <p>  चौथी अनुसूची</span></p>", " <p> पहली अनुसूची</span></p>",
                                " <p>  सातवीं अनुसूची</span></p>", " <p> तीसरी अनुसूची</span></p>"],
                    solution_en: " <p>11.(c) Seventh Schedule of the Indian Constitution demarcates the powers of the Union and the States i.e. Union List, State List and Concurrent List. Centre and state both have the right to make a law on topics described in the concurrent list.</span></p>",
                    solution_hi: " <p>11.(c) भारतीय संविधान की सातवीं अनुसूची संघ और राज्यों की शक्तियों यानी संघ सूची, राज्य सूची और समवर्ती सूची का सीमांकन करती है। समवर्ती सूची में वर्णित विषयों पर केंद्र और राज्य दोनों को कानून बनाने का अधिकार है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: " <p>12</span><span style=\"font-family:Palanquin Dark\">. G20 Leaders’ Summit 2020 was held virtually on 20th and 21st November 2020 and  was presided by______. </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning) </span></p>",
                    question_hi: " <p>12. G20 लीडर समिट 2020 वस्तुतः 20 और 21 नवंबर 2020 को आयोजित किया गया था और इसकी अध्यक्षता ______ ने की थी। </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> India </span></p>", " <p> Iran </span></p>", 
                                " <p> Saudi Arabia </span></p>", " <p> Pakistan </span></p>"],
                    options_hi: [" <p>  भारत</span></p>", " <p> ईरान</span></p>",
                                " <p>  सऊदी अरब</span></p>", " <p> पाकिस्तान</span></p>"],
                    solution_en: " <p>12.(c) G20 Leaders’ Summit 2020, which held virtually, was presided over by Saudi Arabia in Riyadh. Humanitarian needs, access to basic services and livelihood, security, fight against terrorism, migration and human rights were topics of discussion in the meeting.</span></p>",
                    solution_hi: " <p>12.(c) G20 लीडर्स समिट 2020, जो वस्तुतः आयोजित हुआ, की अध्यक्षता सऊदी अरब ने रियाद में की। बैठक में मानवीय जरूरतों, बुनियादी सेवाओं तक पहुंच और आजीविका, सुरक्षा, आतंकवाद के खिलाफ लड़ाई, प्रवास और मानवाधिकारों पर चर्चा हुई।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: " <p>13</span><span style=\"font-family:Palanquin Dark\">. Which of the following plant hormones regulates growth, particularly by stimulating cell elongation in stems?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>13. निम्नलिखित में से कौन सा पादप हार्मोन विकास को नियंत्रित करता है, विशेष रूप से तनों में कोशिका वृद्धि को उत्तेजित करके? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Auxin </span></p>", " <p> Ethylene </span></p>", 
                                " <p> Cytokinin </span></p>", " <p> Gibberellin </span></p>"],
                    options_hi: [" <p>  ऑक्सिन</span></p>", " <p> एथिलीन</span></p>",
                                " <p>  साइटोकिनिन</span></p>", " <p> गिबरेलिन</span></p>"],
                    solution_en: " <p>13.(a) Auxin plant hormone regulates growth, particularly by stimulating cell elongation in stems. Ethylene is the only plant hormone in gaseous form that regulates fruit ripening in plants. Cytokinin promotes cell division in plant roots and shoots. Gibberellin regulates various developmental processes, including germination, dormancy, flowering, flower development, and leaf and fruit senescence.</span></p>",
                    solution_hi: " <p>13.(a) ऑक्सिन प्लांट हार्मोन विकास को नियंत्रित करता है, विशेष रूप से तनों में कोशिका वृद्धि को उत्तेजित करके। एथिलीन गैसीय रूप में एकमात्र पादप हार्मोन है जो पौधों में फलों के पकने को नियंत्रित करता है। साइटोकिनिन पौधों की जड़ों और टहनियों में कोशिका विभाजन को बढ़ावा देता है। गिब्बेरेलिन विभिन्न विकासात्मक प्रक्रियाओं को नियंत्रित करता है, जिसमें अंकुरण, सुप्तता, फूल, फूल विकास, और पत्ती और फलों का जीर्णता शामिल है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14<span style=\"font-family: Palanquin Dark;\">. Which book written by Douglas Stuart won him the Booker Prize 2020? </span></p>\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: "<p>14. डगलस स्टुअर्ट द्वारा लिखित किस पुस्तक ने उन्हें बुकर पुरस्कार 2020 जीता?</p>\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: ["<p>Shuggie Bain</p>", "<p>The Testaments</p>", 
                                "<p>Hamnet</p>", "<p>The White Tiger</p>"],
                    options_hi: ["<p>शुगी बैन</p>", "<p>टेस्टामेंट</p>",
                                "<p>हैमनेट</p>", "<p>व्हाइट टाइगर</p>"],
                    solution_en: "<p>14.(a) Shuggie Bain written by Douglas Stuart won him the Booker Prize 2020.<br><span style=\"font-family: Palanquin Dark;\">The Testaments (</span><span style=\"font-family: Palanquin Dark;\">Margaret Atwood), </span><span style=\"font-family: Palanquin Dark;\">Hamnet (</span><span style=\"font-family: Palanquin Dark;\">Maggie O\'Farrell), </span><span style=\"font-family: Palanquin Dark;\">The White Tiger (</span><span style=\"font-family: Palanquin Dark;\">Aravind Adiga).</span></p>",
                    solution_hi: "<p>14.(a) डगलस स्टुअर्ट द्वारा लिखित शुगी बैन ने उन्हें बुकर पुरस्कार 2020 जीता।<br><span style=\"font-family: Palanquin Dark;\">द टेस्टामेंट्स (मार्गरेट एटवुड), हैमनेट (मैगी ओ\'फेरेल), द व्हाइट टाइगर (अरविंद अडिगा)।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: " <p>15</span><span style=\"font-family:Palanquin Dark\">. According to Ashokan edicts, how many years after becoming the king did Ashoka  wage war on Kalinga? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>15. अशोक के अभिलेखों के अनुसार अशोक ने राजा बनने के कितने वर्ष बाद कलिंग पर युद्ध किया था? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Five </span></p>", " <p> Eight </span></p>", 
                                " <p> Six </span></p>", " <p> Seven </span></p>"],
                    options_hi: [" <p>  पांच</span></p>", " <p> आठ</span></p>",
                                " <p>  छह</span></p>", " <p> सात</span></p>"],
                    solution_en: " <p>15.(b) According to Ashokan edicts, eight years after becoming the king, Ashoka waged war on Kalinga.</span></p> <p><span style=\"font-family:Palanquin Dark\">The records of the Kalinga war were portrayed in the rock edict XIII.</span></p>",
                    solution_hi: " <p>15.(b) अशोक के अभिलेखों के अनुसार, राजा बनने के आठ साल बाद, अशोक ने कलिंग पर युद्ध छेड़ दिया।</span></p> <p><span style=\"font-family:Palanquin Dark\">कलिंग युद्ध के अभिलेखों को शिलालेख XIII में चित्रित किया गया था।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: " <p>16</span><span style=\"font-family:Palanquin Dark\">. The Indian Forest Act 1927 was enacted after repealing which of the following Indian  forest acts? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>16. भारतीय वन अधिनियम 1927 निम्नलिखित में से किस भारतीय वन अधिनियम को निरस्त करने के बाद अधिनियमित किया गया था? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Indian Forest Act, 1882 </span></p>", " <p> Indian Forest Act, 1922 </span></p>", 
                                " <p> Indian Forest Act, 1865 </span></p>", " <p> Indian Forest Act, 1878 </span></p>"],
                    options_hi: [" <p>  भारतीय वन अधिनियम, 1882</span></p>", " <p> भारतीय वन अधिनियम, 1922</span></p>",
                                " <p>  भारतीय वन अधिनियम, 1865</span></p>", " <p> भारतीय वन अधिनियम, 1878</span></p>"],
                    solution_en: " <p>16.(d) The Indian Forest Act 1927 was enacted after repealing the Indian Forest Act, 1878. </span><span style=\"font-family:Palanquin Dark\">The Indian Forest Act of 1878 divided Indian forests into reserved forests (completely government-controlled), protected forests (partially government-controlled) and village forests (controlled by abutting villages). The act was further amended and succeeded by the Indian Forest Act of 1927.</span></p>",
                    solution_hi: " <p>16.(d) भारतीय वन अधिनियम, 1878 को निरस्त करने के बाद भारतीय वन अधिनियम 1927 अधिनियमित किया गया था। 1878 के भारतीय वन अधिनियम ने भारतीय वनों को आरक्षित वनों (पूरी तरह से सरकार द्वारा नियंत्रित), संरक्षित वनों (आंशिक रूप से सरकार द्वारा नियंत्रित) में विभाजित किया। और गाँव के जंगल (सटे हुए गाँवों द्वारा नियंत्रित)। इस अधिनियम में और संशोधन किया गया और 1927 के भारतीय वन अधिनियम द्वारा सफल हुआ।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: " <p>17</span><span style=\"font-family:Palanquin Dark\">. Where is the headquarters of the Badminton World Federation located? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>17. बैडमिंटन वर्ल्ड फेडरेशन का मुख्यालय कहाँ स्थित है? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Malaysia </span></p>", " <p> Japan </span></p>", 
                                " <p> Singapore </span></p>", " <p> Switzerland </span></p>"],
                    options_hi: [" <p>  मलेशिया</span></p>", " <p> जापान</span></p>",
                                " <p>  सिंगापुर</span></p>", " <p> स्विट्जरलैंड</span></p>"],
                    solution_en: " <p>17.(a) The headquarters of the Badminton World Federation is located in Malaysia. It</span><span style=\"font-family:Palanquin Dark\"> is the international governing body for the sport of badminton recognized by the International Olympic Committee. It was founded in 1934 as the International Badminton Federation with nine member nations (Canada, Denmark, England, France, Ireland, Netherlands, New Zealand, Scotland and Wales).</span></p>",
                    solution_hi: " <p>17.(a) बैडमिंटन वर्ल्ड फेडरेशन का मुख्यालय मलेशिया में स्थित है। यह अंतरराष्ट्रीय ओलंपिक समिति द्वारा मान्यता प्राप्त बैडमिंटन के खेल के लिए अंतरराष्ट्रीय शासी निकाय है। इसकी स्थापना 1934 में नौ सदस्य देशों (कनाडा, डेनमार्क, इंग्लैंड, फ्रांस, आयरलैंड, नीदरलैंड, न्यूजीलैंड, स्कॉटलैंड और वेल्स) के साथ अंतर्राष्ट्रीय बैडमिंटन संघ के रूप में हुई थी।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: " <p>18</span><span style=\"font-family:Palanquin Dark\">. Henry Moniz has been appointed as the first-ever Chief Compliance Officer of which of the following tech giants in January 2021? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>18. हेनरी मोनिज़ को जनवरी 2021 में निम्नलिखित में से किस तकनीकी दिग्गज के पहले मुख्य अनुपालन अधिकारी के रूप में नियुक्त किया गया है? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Twitter Inc. </span></p>", " <p> Facebook Inc. </span></p>", 
                                " <p> Google LLC </span></p>", " <p> Apple Inc. </span></p>"],
                    options_hi: [" <p>  ट्विटर Inc.</span></p>", " <p> फेसबुक Inc.</span></p>",
                                " <p>  गूगल LLC</span></p>", " <p> ऐप्पल Inc.</span></p>"],
                    solution_en: " <p>18.(b) Henry Moniz has been appointed as the first-ever Chief Compliance Officer of Facebook Inc. in January 2021. He is primarily responsible for overseeing compliance within an organization and ensuring compliance with laws, regulatory requirements, policies, and procedures.</span></p>",
                    solution_hi: " <p>18.(b) हेनरी मोनिज़ को जनवरी 2021 में फेसबुक Inc. के पहले मुख्य अनुपालन अधिकारी के रूप में नियुक्त किया गया है। वह मुख्य रूप से एक संगठन के भीतर अनुपालन की निगरानी और कानूनों, नियामक आवश्यकताओं, नीतियों, और प्रक्रियाओं के अनुपालन को सुनिश्चित करने के लिए जिम्मेदार है। </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19<span style=\"font-family: Palanquin Dark;\">. One of the major events of Ashoka&rsquo;s reign was the convening of the ______ Buddhist Sangha (council) in 250 BCE in the capital Pataliputra. </span></p>\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: "<p>19<span style=\"font-family: Palanquin Dark;\">. अशोक के शासनकाल की प्रमुख घटनाओं में से एक 250 ईसा पूर्व में राजधानी पाटलिपुत्र में ______ बौद्ध संघ (परिषद) का आयोजन था। <br>SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: ["<p>First</p>", "<p>Fourth</p>", 
                                "<p>Third</p>", "<p>Second</p>"],
                    options_hi: ["<p>पहली</p>", "<p>चौथी</p>",
                                "<p>तीसरी</p>", "<p>दूसरी</p>"],
                    solution_en: "<p>19.(c) Third Buddhist Sangha (council) was organized in 250 BCE in Pataliputra during the reign of Ashoka. 4 Buddhist councils were held in total under the patronage of different rulers.</p>",
                    solution_hi: "<p>19.(c) अशोक के शासनकाल के दौरान 250 ईसा पूर्व में पाटलिपुत्र में तीसरे बौद्ध संघ (परिषद) का आयोजन किया गया था।<br><span style=\"font-family: Palanquin Dark;\">विभिन्न शासकों के संरक्षण में कुल मिलाकर 4 बौद्ध परिषदें आयोजित की गईं।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: " <p>20</span><span style=\"font-family:Palanquin Dark\">. Who among the following was the first Indian badminton player to qualify for two  events - mixed doubles and women’s doubles - in the Olympics? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>20</span><span style=\"font-family:Palanquin Dark\">. निम्नलिखित में से कौन ओलंपिक में दो स्पर्धाओं - मिश्रित युगल और महिला युगल - के लिए क्वालीफाई करने वाला पहला भारतीय बैडमिंटन खिलाड़ी था? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> PV Sindhu </span></p>", " <p> Saina Nehwal </span></p>", 
                                " <p> Jwala Gutta </span></p>", " <p> Ashwini Ponnappa </span></p>"],
                    options_hi: [" <p>  पीवी सिंधु</span></p>", " <p> साइना नेहवाल</span></p>",
                                " <p>  ज्वाला गुट्टा</span></p>", " <p> अश्विनी पोनप्पा</span></p>"],
                    solution_en: " <p>20.(c) Jwala Gutta was the first Indian badminton player to qualify for two events in the Olympics. Later, Saina Nehwal became the first Indian to win an Olympic medal in badminton in 2012. PV Sindhu stepped up to keep the trend alive in the next two games by winning a silver at Rio 2016 and a bronze at Tokyo 2020 Olympics.</span></p>",
                    solution_hi: " <p>20.(c) ज्वाला गुट्टा ओलंपिक में दो स्पर्धाओं के लिए क्वालीफाई करने वाली पहली भारतीय बैडमिंटन खिलाड़ी थीं। बाद में, साइना नेहवाल 2012 में बैडमिंटन में ओलंपिक पदक जीतने वाली पहली भारतीय बनीं। पीवी सिंधु ने रियो 2016 में रजत और टोक्यो 2020 ओलंपिक में कांस्य जीतकर अगले दो खेलों में इस प्रवृत्ति को जारी रखने के लिए कदम बढ़ाया।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: " <p>21</span><span style=\"font-family:Palanquin Dark\">. Which of the following organizations releases the Global Innovation Index? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>21. निम्नलिखित में से कौन सा संगठन वैश्विक नवाचार सूचकांक जारी करता है?</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> WEF </span></p>", " <p> INSEAD, Cornell University, WIPO </span></p>", 
                                " <p> UNDP </span></p>", " <p> WTO</span></p>"],
                    options_hi: [" <p>  WEF </span></p>", " <p> INSEAD, कॉर्नेल विश्वविद्यालय, WIPO </span></p>",
                                " <p>  UNDP </span></p>", " <p> विश्व व्यापार संगठन</span></p>"],
                    solution_en: " <p>21.(b) </span><span style=\"font-family:Palanquin Dark\">The Global Innovation Index is an annual ranking of countries by their capacity and success in innovation. It was started in 2007 by INSEAD and World Business. It was published by the World Intellectual Property Organization (WIPO).</span></p>",
                    solution_hi: " <p>21.(b) ग्लोबल इनोवेशन इंडेक्स देशों की उनकी क्षमता और नवाचार में सफलता के आधार पर एक वार्षिक रैंकिंग है। इसकी शुरुआत 2007 में INSEAD और वर्ल्ड बिजनेस द्वारा की गई थी। यह विश्व बौद्धिक संपदा संगठन (WIPO) द्वारा प्रकाशित किया गया था।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: " <p>22</span><span style=\"font-family:Palanquin Dark\">. \'Panche\' is a traditional sarong worn by the men in the state of ______.</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>22. \'पंचे\' ______ राज्य में पुरुषों द्वारा पहना जाने वाला एक पारंपरिक सारंग है। </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Kerala </span></p>", " <p> Karnataka </span></p>", 
                                " <p> Assam </span></p>", " <p> Sikkim </span></p>"],
                    options_hi: [" <p>  केरल</span></p>", " <p> कर्नाटक</span></p>",
                                " <p>  असम</span></p>", " <p> सिक्किम</span></p>"],
                    solution_en: " <p>22.(b) Panche is a traditional sarong worn by the men in the state of Karnataka. Another name of Panche is ‘Dhoti’. This unstitched cloth is wrapped around the legs and knotted around the waist.</span></p>",
                    solution_hi: " <p>22.(b) पंचे कर्नाटक राज्य में पुरुषों द्वारा पहना जाने वाला एक पारंपरिक सारंग है। पंचे का दूसरा नाम \'धोती\' है। बिना सिले इस कपड़े को पैरों के चारों ओर लपेटकर कमर के चारों ओर बांधा जाता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: " <p>23</span><span style=\"font-family:Palanquin Dark\">. In medicine, ______ is a device such as a small metal plate or needle that carries  electricity from an instrument to a patient for treatment or surgery.</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>23. चिकित्सा में, ______ धातु की एक छोटी प्लेट या सुई जैसा एक उपकरण है जो उपचार या सर्जरी के लिए एक उपकरण से रोगी तक बिजली पहुंचाता है। </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> a venturimeter </span></p>", " <p> an electrode </span></p>", 
                                " <p> a machmeter </span></p>", " <p> an electrolyte </span></p>"],
                    options_hi: [" <p>  एक वेंचुरीमीटर</span></p>", " <p> एक इलेक्ट्रोड</span></p>",
                                " <p>  एक मैचमीटर</span></p>", " <p> एक इलेक्ट्रोलाइट</span></p>"],
                    solution_en: " <p>23.(b)  An electrode is a device such as a small metal plate or needle that carries electricity from an instrument to a patient for treatment or surgery. </span><span style=\"font-family:Palanquin Dark\">Venturimeter is a type of flowmeter that works on the principle of Bernoulli\'s Equation. It is widely used in the water, chemical, pharmaceutical, and oil & gas industries to measure the flow rates of fluids inside a pipe. Electrolytes are minerals in our blood and other body fluids that carry an electric charge. Machmeter is </span><span style=\"font-family:Palanquin Dark\">an instrument in an aircraft indicating airspeed as a Mach number.</span></p>",
                    solution_hi: " <p>23.(b) इलेक्ट्रोड एक छोटी धातु की प्लेट या सुई जैसा एक उपकरण है जो किसी उपकरण से बिजली को मरीज तक इलाज या सर्जरी के लिए ले जाता है। वेंचुरीमीटर एक प्रकार का प्रवाहमापी है जो बर्नौली समीकरण के सिद्धांत पर कार्य करता है। यह एक पाइप के अंदर तरल पदार्थ की प्रवाह दर को मापने के लिए पानी, रसायन, दवा और तेल और गैस उद्योगों में व्यापक रूप से उपयोग किया जाता है। इलेक्ट्रोलाइट्स हमारे रक्त और शरीर के अन्य तरल पदार्थों में खनिज होते हैं जो विद्युत आवेश को वहन करते हैं। मैकमीटर एक विमान में एक उपकरण है जो एयरस्पीड को मच संख्या के रूप में दर्शाता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: " <p>24</span><span style=\"font-family:Palanquin Dark\">. All the non-zero vectors are called ______. </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    question_hi: " <p>24. सभी शून्येतर सदिश ______ कहलाते हैं।</span></p> <p><span style=\"font-family:Palanquin Dark\"> SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> coplanar vectors </span></p>", " <p> proper vectors </span></p>", 
                                " <p> co-initial vectors </span></p>", " <p> equal vectors </span></p>"],
                    options_hi: [" <p>  समतलीय सदिश </span></p>", " <p> उचित सदिश </span></p>",
                                " <p>  सह-प्रारंभिक सदिश </span></p>", " <p> तुल्य सदिश </span></p>"],
                    solution_en: " <p>24.(b) All the non-zero vectors are called proper vectors.</span></p> <p><span style=\"font-family:Palanquin Dark\">Coplanar vectors are defined as vectors that are lying on the same plane in a three-dimensional plane.</span></p> <p><span style=\"font-family:Palanquin Dark\">Two vectors are said to be equal when their magnitude is equal and also their direction is the same.</span></p> <p><span style=\"font-family:Palanquin Dark\">A vector is said to be a co-initial vector when two or more vectors have the same starting point. </span></p>",
                    solution_hi: " <p>24.(b) सभी शून्येतर सदिश उचित सदिश कहलाते हैं।</span></p> <p><span style=\"font-family:Palanquin Dark\">सामान तलीय सदिश को उस सदिश के रूप में परिभाषित किया जाता है जो त्रि-आयामी तल में एक ही तल पर स्थित होते हैं।</span></p> <p><span style=\"font-family:Palanquin Dark\">दो सदिशों को तब बराबर कहा जाता है जब उनका परिमाण समान हो और साथ ही उनकी दिशा भी समान हो।</span></p> <p><span style=\"font-family:Palanquin Dark\">एक सदिश को सह-प्रारंभिक सदिश कहा जाता है जब दो या दो से अधिक सदिशों का प्रारंभिक बिंदु समान होता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25</span><span style=\"font-family:Palanquin Dark\">. According to which of the following Articles of the Constitution of India shall a Money Bill not be introduced in a Legislative Council? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning) </span></p>",
                    question_hi: " <p>25. भारत के संविधान के निम्नलिखित में से किस अनुच्छेद के अनुसार धन विधेयक को विधान परिषद में पेश नहीं किया जाना चाहिए? </span></p> <p><span style=\"font-family:Palanquin Dark\">SSC CGL 23/08/21(Morning)</span></p>",
                    options_en: [" <p> Article 333 </span></p>", " <p> Article 198 </span></p>", 
                                " <p> Article 451 </span></p>", " <p> Article 189 </span></p>"],
                    options_hi: [" <p> अनुच्छेद 333</span></p>", " <p> अनुच्छेद 198</span></p>",
                                " <p> अनुच्छेद 451</span></p>", " <p> अनुच्छेद 189</span></p>"],
                    solution_en: " <p>25.(b) According to Article 198 of the Constitution of India, a Money Bill shall not be introduced in a Legislative Council.</span></p>",
                    solution_hi: " <p>25.(b) भारत के संविधान के अनुच्छेद 198 के अनुसार, एक विधान परिषद में एक धन विधेयक पेश नहीं किया जाएगा।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>