<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language, &lsquo;BLOW&rsquo; is coded as &lsquo;3517&rsquo; and &lsquo;LIFE&rsquo; is coded as &lsquo;4258&rsquo;. What is the code for &lsquo;L&rsquo; in that language ?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, \'BLOW\' को \'3517\' के रूप में कूटबद्ध किया जाता है और \'LIFE\' को \'4258\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'L\' के लिए कूट क्या है ?</p>",
                    options_en: [
                        "<p>7</p>",
                        "<p>1</p>",
                        "<p>5</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>7</p>",
                        "<p>1</p>",
                        "<p>5</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>1.(c) <strong>Given</strong> :- BLOW :- 3517&hellip;&hellip;.(i)<br>LIFE :- 4258&hellip;&hellip;. (ii)<br>From (i) and (ii) &lsquo;L&rsquo; and &lsquo;5&rsquo; are common.<br>The code of &lsquo;L&rsquo; = 5.</p>",
                    solution_hi: "<p>1.(c) <strong>दिया गया</strong> :- BLOW :- 3517&hellip;&hellip;.(i)<br>LIFE :- 4258&hellip;&hellip;. (ii)<br>(i) और (ii) से \'L\' और \'5\' उभयनिष्ठ हैं।<br>\'L\' का कोड = 5.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. What should come in place of the question mark (?) in the given series ?<br>9 17 33 65 129 ?</p>",
                    question_hi: "<p>2. दी गई शृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए?<br>9 17 33 65 129 ?</p>",
                    options_en: [
                        "<p>259</p>",
                        "<p>254</p>",
                        "<p>257</p>",
                        "<p>258</p>"
                    ],
                    options_hi: [
                        "<p>259</p>",
                        "<p>254</p>",
                        "<p>257</p>",
                        "<p>258</p>"
                    ],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144127.png\" alt=\"rId6\" width=\"256\" height=\"50\"></p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144127.png\" alt=\"rId6\" width=\"256\" height=\"50\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "3. In a certain code language,<br />‘A + B’ means ‘A is the mother of B’;<br />‘A − B’ means ‘A is the brother of B’;<br />‘A × B’ means ‘A is the wife of B’ and<br />‘A ÷ B’ means ‘A is the father of B’.<br />Based on the above, how is A related to D if ‘A × B ÷ C × D’?",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में,<br>&lsquo;A + B&rsquo; का अर्थ है &lsquo;A, B की माता है&rsquo;;<br>&lsquo;A &minus; B&rsquo; का अर्थ है &lsquo;A, B का भाई है&rsquo;;<br>&lsquo;A &times; B&rsquo; का अर्थ है &lsquo;A, B की पत्नी है&rsquo; और<br>&lsquo;A &divide; B&rsquo; का अर्थ है &lsquo;A, B का पिता है&rsquo;<br>उपरोक्त के आधार पर, यदि &lsquo;A &times; B &divide; C &times; D&rsquo; है, तो A का D से क्या सम्बन्ध है?</p>",
                    options_en: [
                        " Father-in-law",
                        " Father’s sister",
                        "<p>Wife\'s mother</p>",
                        "<p>Father&rsquo;s brother</p>"
                    ],
                    options_hi: [
                        "<p>ससुर</p>",
                        "<p>पिता की बहन</p>",
                        "<p>सास</p>",
                        "<p>पिता का भाई</p>"
                    ],
                    solution_en: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144234.png\" alt=\"rId7\" width=\"158\" height=\"96\"><br>&lsquo;A&rsquo; is the Wife\'s mother of &lsquo;D&rsquo;</p>",
                    solution_hi: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144234.png\" alt=\"rId7\" width=\"153\" height=\"93\"><br>\'A\', \'D\' की पत्नी की मां (सास ) है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Six letters A, B, C, E, F and G are written on different faces of a dice. Two positions of this dice are shown in the figure below. Find the letter on the face opposite to A.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144372.png\" alt=\"rId8\" width=\"200\"></p>",
                    question_hi: "<p>4. एक पासे के विभिन्न फलकों पर छ: अक्षर A, B, C, E, F और G लिखे गए हैं। नीचे दिए गए चित्र में इस पासे की दो स्थितियों को दर्शाया गया है। दिए गए विकल्पों में से A के विपरीत फलक पर लिखा अक्षर ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144372.png\" alt=\"rId8\" width=\"200\"></p>",
                    options_en: [
                        "<p>F</p>",
                        "<p>E</p>",
                        "<p>C</p>",
                        "<p>G</p>"
                    ],
                    options_hi: [
                        "<p>F</p>",
                        "<p>E</p>",
                        "<p>C</p>",
                        "<p>G</p>"
                    ],
                    solution_en: "<p>4.(b) From two dice the opposite face are <br>B &harr; F , C &harr; G , A &harr; E</p>",
                    solution_hi: "<p>4.(b) दो पासों से विपरीत फलक हैं<br>B &harr; F , C &harr; G , A &harr; E</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. 18 is related to 90 following a certain logic. Following the same logic, 31 is related to 155. To which of the following is 52 related, following the same logic?<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>5. एक निश्चित तर्क का पालन करते हुए 18, 90 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 31, 155 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 52 निम्नलिखित में से किससे संबंधित है?<br>(नोट : संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: [
                        "<p>256</p>",
                        "<p>248</p>",
                        "<p>265</p>",
                        "<p>260</p>"
                    ],
                    options_hi: [
                        "<p>256</p>",
                        "<p>248</p>",
                        "<p>265</p>",
                        "<p>260</p>"
                    ],
                    solution_en: "<p>5.(d)<br><strong>Logic:-</strong>&nbsp; (1<sup>st </sup>no.) &times; <strong>5</strong> = 2<sup>nd</sup>no.<br>(18, 90) :- 18 &times;&nbsp;<strong>5</strong> = 90<br>(31, 155) :- 31 &times; <strong>5</strong> = 155<br>similarly<br>(52, ?) :- 52&nbsp;&times;&nbsp;<strong>5</strong> = 260</p>",
                    solution_hi: "<p>5.(d)<br><strong>तर्क:-</strong>&nbsp; पहली संख्या &times; <strong>5</strong> = दूसरी संख्या <br>(18, 90) :- 18 &times;&nbsp;<strong>5</strong> = 90<br>(31, 155) :- 31 &times; <strong>5</strong> = 155<br>इसी प्रकार <br>(52, ?) :- 52 &times;&nbsp;<strong>5</strong> = 260</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Based on the alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group? <br><strong>(Note</strong> : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>6. अंग्रेजी वर्णमाला क्रमानुसार, निम्नलिखित चार अक्षर-समूहों में से तीन एक निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा अक्षर-समूह उस समूह से संबंधित नहीं है? <br>(नोट : असंगत अक्षर-समूह, अक्षर-समूह मेें व्यंजन/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>LIK</p>",
                        "<p>EBD</p>",
                        "<p>PMO</p>",
                        "<p>ROS</p>"
                    ],
                    options_hi: [
                        "<p>LIK</p>",
                        "<p>EBD</p>",
                        "<p>PMO</p>",
                        "<p>ROS</p>"
                    ],
                    solution_en: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144537.png\" alt=\"rId9\" width=\"140\" height=\"99\"> ,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144739.png\" alt=\"rId10\" width=\"130\" height=\"94\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144839.png\" alt=\"rId11\" width=\"140\"><br>But,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144946.png\" alt=\"rId12\" width=\"134\" height=\"91\"></p>",
                    solution_hi: "<p>6.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144537.png\" alt=\"rId9\" width=\"140\" height=\"99\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144739.png\" alt=\"rId10\" width=\"130\" height=\"94\">&nbsp; ,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144839.png\" alt=\"rId11\" width=\"140\"><br>लेकिन,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503144946.png\" alt=\"rId12\" width=\"134\" height=\"91\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Three statements are followed by conclusions numbered I, II. You have to consider&nbsp;these statements to be true, even if they seem to be at variance with commonly known&nbsp;facts. Decide which of the given conclusions logically follow/s from the given statement.<br><strong>Statements</strong> :<br>Some leaves are trees.<br>All stones are grain.<br>Some grains are leaves.<br><strong>Conclusion (I) :</strong> All grains are trees.<br><strong>Conclusion (II) :</strong> Some leaves are stones.</p>",
                    question_hi: "<p>7. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे&nbsp;समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष&nbsp;तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong><br>कुछ पत्तियां, पेड़ हैं।<br>सभी पत्थर, अनाज हैं।<br>कुछ अनाज, पत्तियां हैं।<br><strong>निष्कर्ष (I) :</strong> सभी अनाज, पेड़ हैं।<br><strong>निष्कर्ष (II) : </strong>कुछ पत्तियां, पत्थर हैं।</p>",
                    options_en: [
                        "<p>Both conclusions (I) and (II) follow.</p>",
                        "<p>Only conclusion (I) follows.</p>",
                        "<p>Only conclusion (II) follows.</p>",
                        "<p>Neither conclusion (I) nor (II) follows.</p>"
                    ],
                    options_hi: [
                        "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>",
                        "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                        "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>",
                        "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>"
                    ],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503145366.png\" alt=\"rId13\" width=\"258\" height=\"71\"><br>Neither conclusion I nor II follows.</p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503145619.png\" alt=\"rId14\" width=\"241\" height=\"70\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. If &lsquo;Y&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;N&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;M&rsquo; stands for &lsquo;+&rsquo; and &lsquo;G&rsquo; stands for &lsquo;&ndash;&rsquo;, what will come in place of the question mark (?) in the following equation? <br>100 N 80 G 40 Y 20 M 10 = ?</p>",
                    question_hi: "<p>8. यदि \'Y\' का अर्थ \'&divide;\', \'N\' का अर्थ \'&times;\', \'M\' का अर्थ \'+\' और \'G\' का अर्थ \'&ndash;\' है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा? <br>100 N 80 G 40 Y 20 M 10 = ?</p>",
                    options_en: [
                        "<p>8976</p>",
                        "<p>8008</p>",
                        "<p>5776</p>",
                        "<p>987</p>"
                    ],
                    options_hi: [
                        "<p>8976</p>",
                        "<p>8008</p>",
                        "<p>5776</p>",
                        "<p>987</p>"
                    ],
                    solution_en: "<p>8.(b) <strong>Given</strong> :- 100 N 80 G 40 Y 20 M 10<br>As per given instruction after interchanging the letter with sign we get<br>100 &times; 80 - 40 &divide;&nbsp;20 + 10<br>8000 - 2 + 10 = 8008</p>",
                    solution_hi: "<p>8.(b) <strong>दिया गया :-</strong> 100 N 80 G 40 Y 20 M 10<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>100 &times; 80 - 40 &divide;&nbsp;20 + 10<br>8000 - 2 + 10 = 8008</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. The position of how many letters will remain unchanged if all the letters in the word MENTOR are arranged in English alphabetical order?</p>",
                    question_hi: "<p>9. यदि MENTOR शब्द के सभी अक्षरों को अंग्रेजी वर्णानुक्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित बनी रहेगी?</p>",
                    options_en: [
                        "<p>Two</p>",
                        "<p>One</p>",
                        "<p>None</p>",
                        "<p>Three</p>"
                    ],
                    options_hi: [
                        "<p>दो</p>",
                        "<p>एक</p>",
                        "<p>किसी की भी नहीं</p>",
                        "<p>तीन</p>"
                    ],
                    solution_en: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503145778.png\" alt=\"rId15\" width=\"155\" height=\"99\"><br>Position of only one letter will be unchanged.</p>",
                    solution_hi: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503145778.png\" alt=\"rId15\" width=\"155\" height=\"99\"><br>केवल एक अक्षर की स्थिति अपरिवर्तित रहेगी ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the figure from among the given options that can replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503145916.png\" alt=\"rId16\" width=\"378\" height=\"77\"></p>",
                    question_hi: "<p>10. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो निम्नलिखित शृंखला में प्रश्नचिह्न (?) को प्रतिस्थापित कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503145916.png\" alt=\"rId16\" width=\"378\" height=\"77\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146008.png\" alt=\"rId17\" width=\"90\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146107.png\" alt=\"rId18\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146223.png\" alt=\"rId19\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146323.png\" alt=\"rId20\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146008.png\" alt=\"rId17\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146107.png\" alt=\"rId18\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146223.png\" alt=\"rId19\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146323.png\" alt=\"rId20\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146323.png\" alt=\"rId20\" width=\"90\"></p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146323.png\" alt=\"rId20\" width=\"90\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. How many triangles are there in the following figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146423.png\" alt=\"rId21\" width=\"99\" height=\"90\"></p>",
                    question_hi: "<p>11. नीचे दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146423.png\" alt=\"rId21\" width=\"99\" height=\"90\"></p>",
                    options_en: [
                        "<p>20</p>",
                        "<p>18</p>",
                        "<p>21</p>",
                        "<p>19</p>"
                    ],
                    options_hi: [
                        "<p>20</p>",
                        "<p>18</p>",
                        "<p>21</p>",
                        "<p>19</p>"
                    ],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146539.png\" alt=\"rId22\" width=\"179\" height=\"183\"><br><strong>There are total 20 triangle :- </strong><br>ADB, ABH, BDE, BEF, BHI , BFG, BDG, BDF, BDH, BIG, BEG, BGC, BIC, AJC, HIJ, DEH, FGI, AKJ, CJL, DGJ</p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146539.png\" alt=\"rId22\" width=\"179\" height=\"183\"><br><strong>कुल 20 त्रिभुज हैं:-</strong><br>ADB, ABH, BDE, BEF, BHI , BFG, BDG, BDF, BDH, BIG, BEG, BGC, BIC, AJC, HIJ, DEH, FGI, AKJ, CJL, DGJ</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the figure from the given options that will replace the question mark (?) in the figure given below to complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146660.png\" alt=\"rId23\" width=\"112\" height=\"217\"></p>",
                    question_hi: "<p>12. दिए गए विकल्पों में से उस आकृति का चयन कीजिए, जो पैटर्न को पूरा करने के लिए नीचे दी गई आकृति में प्रश्न-चिह्न (?) के स्थान पर आएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146660.png\" alt=\"rId23\" width=\"108\" height=\"209\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146801.png\" alt=\"rId24\" width=\"150\" height=\"72\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146895.png\" alt=\"rId25\" width=\"150\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146998.png\" alt=\"rId26\" width=\"150\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503147132.png\" alt=\"rId27\" width=\"189\" height=\"63\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146801.png\" alt=\"rId24\" width=\"150\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146895.png\" alt=\"rId25\" width=\"162\" height=\"54\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146998.png\" alt=\"rId26\" width=\"150\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503147132.png\" alt=\"rId27\" width=\"189\" height=\"63\"></p>"
                    ],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146998.png\" alt=\"rId26\" width=\"168\" height=\"64\"></p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503146998.png\" alt=\"rId26\" width=\"168\" height=\"64\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option that is related to the fifth number in the same way as the fourth number is related to the third number and the second number is related to the first number. <br>16 : 36 :: 64 : 100 :: 25 : ?</p>",
                    question_hi: "<p>13. उस विकल्प का चयन कीजिए जो पांचवीं संख्या से उसी प्रकार संबंधित है जिस प्रकार चौथी संख्या तीसरी संख्या से और दूसरी संख्या पहली संख्या से संबंधित है। <br>16 : 36 :: 64 : 100 :: 25 : ?</p>",
                    options_en: [
                        "<p>49</p>",
                        "<p>47</p>",
                        "<p>45</p>",
                        "<p>61</p>"
                    ],
                    options_hi: [
                        "<p>49</p>",
                        "<p>47</p>",
                        "<p>45</p>",
                        "<p>61</p>"
                    ],
                    solution_en: "<p>13.(a) <strong>Logic</strong> :- (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mi>st</mi><mo>&#160;</mo><mi>number</mi></msqrt></math> + 2)<sup>2</sup> = (2nd number)<br><math display=\"inline\"><msqrt><mn>16</mn></msqrt></math> : 36 :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> + 2)<sup>2</sup> &rArr; (4 + 2)<sup>2</sup> = 36<br><math display=\"inline\"><msqrt><mn>64</mn></msqrt></math> : 100 :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> + 2)<sup>2</sup> &rArr; (8 + 2)<sup>2</sup> = 100<br>Similarly,<br><math display=\"inline\"><msqrt><mn>25</mn></msqrt></math> : ? :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>25</mn></msqrt></math>+ 2)<sup>2</sup> &rArr; (5 + 2)<sup>2</sup> = 49</p>",
                    solution_hi: "<p>13.(a) <strong>तर्क</strong> :- (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2346;&#2361;&#2354;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></msqrt></math> + 2)<sup>2</sup> = (दूसरी संख्या)<br><math display=\"inline\"><msqrt><mn>16</mn></msqrt></math> : 36 :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> + 2)<sup>2</sup> &rArr; (4 + 2)<sup>2</sup> = 36<br><math display=\"inline\"><msqrt><mn>64</mn></msqrt></math> : 100 :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> + 2)<sup>2</sup> &rArr; (8 + 2)<sup>2</sup> = 100<br>इसी प्रकार,<br><math display=\"inline\"><msqrt><mn>25</mn></msqrt></math> : ? :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>25</mn></msqrt></math> + 2)<sup>2</sup> &rArr; (5 + 2)<sup>2</sup> = 49</p>\n<p>&nbsp;</p>\n<p>&nbsp;</p>\n<p>&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Which two numbers (not individual digits) should be interchanged to make the given equation correct?<br>21 &times; 5 - 35 + (42 &divide; 7) + 28 = 96</p>",
                    question_hi: "<p>14. दिए गए समीकरण को संतुलित करने के लिए किन दो संख्याओं (अकेले अंकों को नहीं) को आपस में बदलना होगा?<br>21 &times; 5 - 35 + (42 &divide; 7) + 28 = 96</p>",
                    options_en: [
                        "<p>28 and 35</p>",
                        "<p>35 and 42</p>",
                        "<p>21 and 42</p>",
                        "<p>28 and 21</p>"
                    ],
                    options_hi: [
                        "<p>28 और 35</p>",
                        "<p>35 और 42</p>",
                        "<p>21 और 42</p>",
                        "<p>28 और 21</p>"
                    ],
                    solution_en: "<p>14.(b) <strong>Given</strong> :- 21 &times; 5 - 35 + (42 &divide;&nbsp;7) + 28 = 96<br>After going through all the options, option (b) satisfies. After interchanging 35 and 42 we get<br>21 &times; 5 - 42 + (35 &divide; 7) + 28 <br>105 - 42 + 5 + 28<br>105 - 9 = 96<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>14.(b) <strong>दिया गया</strong> :- 21 &times; 5 - 35 + (42 &divide;&nbsp;7) + 28 = 96<br>सभी विकल्पों की जांच करने पर विकल्प ( b ) संतुष्ट होता है। 35 और 42 को आपस में बदलने पर हमें प्राप्त होता है<br>21 &times; 5 - 42 + (35 &divide;&nbsp;7) + 28 <br>105 - 42 + 5 + 28<br>105 - 9 = 96<br>L.H.S. = R.H.S.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. In a certain code language, \'ABROAD\' is written as \'SCBFCQ\' and \'ACCEPT\' is written as \'DDBVRG\'. How will \'ACTION\' be written as in that language?</p>",
                    question_hi: "<p>15. एक निश्चित कूट भाषा में, \'ABROAD\' को \'SCBFCQ\' के रूप में लिखा जाता है और \'ACCEPT\' को \'DDBVRG के रूप में लिखा जाता है। उसी कूट भाषा में \'ACTION\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>UDBPQK</p>",
                        "<p>TCANOI</p>",
                        "<p>TCAPQK</p>",
                        "<p>VECPQK</p>"
                    ],
                    options_hi: [
                        "<p>UDBPQK</p>",
                        "<p>TCANOI</p>",
                        "<p>TCAPQK</p>",
                        "<p>VECPQK</p>"
                    ],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503147343.png\" alt=\"rId28\" width=\"200\">&nbsp; &nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503147476.png\" alt=\"rId29\" width=\"200\"><br>Similarly,&nbsp; &nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503147711.png\" alt=\"rId30\" width=\"197\" height=\"142\"></p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503147343.png\" alt=\"rId28\" width=\"200\">&nbsp; &nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503147476.png\" alt=\"rId29\" width=\"200\"><br>इसी प्रकार,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503147711.png\" alt=\"rId30\" width=\"197\" height=\"142\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Which of the following terms will replace the question mark (?) in the given series?<br>EQBY, GPDX, ?, KNHV, MMJU</p>",
                    question_hi: "<p>16. निम्नलिखित में से कौन-सा पद दी गई शृंखला में प्रश्न-चिह्न (?) का स्थान लेगा?<br>EQBY, GPDX, ?, KNHV, MMJU</p>",
                    options_en: [
                        "<p>IOFW</p>",
                        "<p>IPFW</p>",
                        "<p>HOFW</p>",
                        "<p>IOEW</p>"
                    ],
                    options_hi: [
                        "<p>IOFW</p>",
                        "<p>IPFW</p>",
                        "<p>HOFW</p>",
                        "<p>IOEW</p>"
                    ],
                    solution_en: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503147939.png\" alt=\"rId31\" width=\"394\" height=\"133\"></p>",
                    solution_hi: "<p>16.(a).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503147939.png\" alt=\"rId31\" width=\"394\" height=\"133\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the option that represents the letters that, when sequentially placed from left to right in the blanks, will complete the letter series.<br>r _ m _ s _ t _ i _ m s s _ r i m _ _ st</p>",
                    question_hi: "<p>17. उस विकल्प का चयन करें, जो उन अक्षरों का प्रतिनिधित्व करता है, जो रिक्त स्थानों में क्रमिक रूप से&nbsp;बाएँ से दाएँ रखे जाने पर दी गई अक्षर शृंखला को पूरा करेंगे।<br>r _ m _ s _ t _ i _ m s s _ r i m _ _ st</p>",
                    options_en: [
                        "<p>i m s r m t m s</p>",
                        "<p>i m s r s t s s</p>",
                        "<p>i s m r m t m s</p>",
                        "<p>i m s r t t m s</p>"
                    ],
                    options_hi: [
                        "<p>i m s r m t m s</p>",
                        "<p>i m s r s t s s</p>",
                        "<p>i s m r m t m s</p>",
                        "<p>i m s r t t m s</p>"
                    ],
                    solution_en: "<p>17.(a) r <span style=\"text-decoration: underline;\"><strong>i</strong></span> m <strong><span style=\"text-decoration: underline;\">m</span></strong> s <span style=\"text-decoration: underline;\"><strong>s</strong></span> t / <span style=\"text-decoration: underline;\"><strong>r</strong></span> i <span style=\"text-decoration: underline;\"><strong>m</strong></span> m s s <span style=\"text-decoration: underline;\"><strong>t</strong></span> / r i m <span style=\"text-decoration: underline;\"><strong>m s</strong></span> st</p>",
                    solution_hi: "<p>17.(a) r <span style=\"text-decoration: underline;\"><strong>i</strong></span> m <strong><span style=\"text-decoration: underline;\">m</span></strong> s <span style=\"text-decoration: underline;\"><strong>s</strong></span> t / <span style=\"text-decoration: underline;\"><strong>r</strong></span> i <span style=\"text-decoration: underline;\"><strong>m</strong></span> m s s <span style=\"text-decoration: underline;\"><strong>t</strong></span> / r i m <span style=\"text-decoration: underline;\"><strong>m s</strong></span> st</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Three of the following four number triads are alike in a certain way and thus form a group. Which is the number triad that does not belong to that group?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>18. निम्नलिखित चार संख्या त्रिकों में से तीन संख्या त्रिक किसी निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह संख्या त्रिक कौन-सा है, जो उस समूह से संबंधित नहीं है?<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>31 - 46 - 41</p>",
                        "<p>24 - 39 - 33</p>",
                        "<p>29 - 44 - 38</p>",
                        "<p>17 - 32 - 26</p>"
                    ],
                    options_hi: [
                        "<p>31 - 46 - 41</p>",
                        "<p>24 - 39 - 33</p>",
                        "<p>29 - 44 - 38</p>",
                        "<p>17 - 32 - 26</p>"
                    ],
                    solution_en: "<p>18.(a) <strong>Logic:-</strong> (1st number) + 15 = 2nd number , (2nd number) - 6 = 3rd number<br>(24 - 39 - 33) :- (24) + 15 = 39 , (39) - 6 = 33<br>(29 - 44 - 38) :- (29) + 15 = 44 ,(44) - 6 = 38<br>(17 - 32 - 26) :- (17) + 15 = 32 , (32) - 6 = 26<br>But,<br>(31 - 46 - 41):- (31) + 15 = 46 , (46) - 5 = 41</p>",
                    solution_hi: "<p>18.(a) <strong>तर्क:-</strong> (पहली संख्या) + 15 = दूसरी संख्या, (दूसरी संख्या) - 6 = तीसरी संख्या<br>(24 - 39 - 33) :- (24) + 15 = 39 , (39) - 6 = 33<br>(29 - 44 - 38) :- (29) + 15 = 44 ,(44) - 6 = 38<br>(17 - 32 - 26) :- (17) + 15 = 32 , (32) - 6 = 26<br>लेकिन,<br>(31 - 46 - 41):- (31) + 15 = 46 , (46) - 5 = 41</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Radha visits her brother Rohan, who stays with their father Prasant, mother Sarala and&nbsp;grandfather Raghuvir. Rohan has two children, Binny and Champak. Binny\'s husband&nbsp;is Keshab, and Champak&rsquo;s wife is Tina. Tina&rsquo;s daughter helped Radha to find their&nbsp;home. How is Tina&rsquo;s daughter related to Rohan?</p>",
                    question_hi: "<p>19. राधा अपने भाई रोहन से मिलने जाती है, जो अपने पिता प्रशांत, माता सरला और दादा रघुवीर के साथ रहता है। रोहन के दो बच्चे बिन्नी और चंपक हैं। बिन्नी के पति केशव और चंपक की पत्नी टीना हैं। टीना&nbsp;की पुत्री ने राधा को उनका घर ढूंढने में मदद की। टीना की पुत्री का रोहन से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Son</p>",
                        "<p>Daughter</p>",
                        "<p>Son&rsquo;s son</p>",
                        "<p>Son&rsquo;s daughter</p>"
                    ],
                    options_hi: [
                        "<p>पुत्र</p>",
                        "<p>पुत्री</p>",
                        "<p>पुत्र का पुत्र</p>",
                        "<p>पुत्र की पुत्री</p>"
                    ],
                    solution_en: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148115.png\" alt=\"rId32\" width=\"278\" height=\"252\"><br>Tina&rsquo;s daughter is the daughter of Rohan&rsquo;s son.</p>",
                    solution_hi: "<p>19.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148213.png\" alt=\"rId33\" width=\"265\" height=\"247\"><br>टीना की बेटी रोहन के बेटे की बेटी है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.) <br>Convict : Prison : : Eskimo : ?</p>",
                    question_hi: "<p>20. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से&nbsp;संबंधित है। (शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और इनको शब्द में अक्षरों की&nbsp;संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक-दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>अपराधी : जेल :: एस्किमो : ?</p>",
                    options_en: [
                        "<p>Cottage</p>",
                        "<p>Palace</p>",
                        "<p>Hut</p>",
                        "<p>Igloo</p>"
                    ],
                    options_hi: [
                        "<p>कॉटेज</p>",
                        "<p>महल</p>",
                        "<p>झोपड़ी</p>",
                        "<p>इग्लू</p>"
                    ],
                    solution_en: "<p>20.(d) As the convicted person lives in prison similarly Eskimo lives in an igloo.</p>",
                    solution_hi: "<p>20.(d) जिस प्रकार अपराधी जेल में रहते है उसी प्रकार एस्किमो इग्लू में रहते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(12, 36, 6)<br>(18, 54, 9)</p>",
                    question_hi: "<p>21. उस समुच्चय का चयन करें जिसकी संख्याएं उसी तरह से संबंधित हैं जिस तरह निम्नलिखित समुच्चयों की संख्याएं संबंधित हैं।<br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)<br>(12, 36, 6)<br>(18, 54, 9)</p>",
                    options_en: [
                        "<p>(28, 86,14)</p>",
                        "<p>(26, 84, 14)</p>",
                        "<p>(28, 84, 16)</p>",
                        "<p>(28, 84, 14)</p>"
                    ],
                    options_hi: [
                        "<p>(28, 86,14)</p>",
                        "<p>(26, 84, 14)</p>",
                        "<p>(28, 84, 16)</p>",
                        "<p>(28, 84, 14)</p>"
                    ],
                    solution_en: "<p>21.(d) <strong>Logic</strong> :- (1st number + 3rd number) &times; 2 = 2nd number<br>(12, 36, 6) :- (12 + 6) &times; 2 &rArr; (18) &times; 2 = 36<br>(18, 54,9) :- (18 + 9) &times; 2 &rArr; (27) &times; 2 = 54<br>Similarly,<br>(28, 84, 14) :- (28 + 14) &times; 2 &rArr; (42) &times; 2 = 84</p>",
                    solution_hi: "<p>21.(d) <strong>तर्क</strong> :- (पहली संख्या + तीसरी संख्या) &times; 2 = दूसरी संख्या<br>(12, 36, 6) :- (12 + 6) &times; 2 &rArr; (18) &times; 2 = 36<br>(18, 54,9) :- (18 + 9) &times; 2 &rArr; (27) &times; 2 = 54<br>इसी प्रकार,<br>(28, 84, 14) :- (28 + 14) &times; 2 &rArr; (42) &times; 2 = 84</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the Venn diagram that best illustrates the relationship between the following classes.<br>Tigers, Snakes, Elephants</p>",
                    question_hi: "<p>22. उस वेन आरेख का चयन कीजिए जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है।<br>बाघ, साँप, हाथी</p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148307.png\" alt=\"rId34\" width=\"90\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148418.png\" alt=\"rId35\" width=\"176\" height=\"66\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148538.png\" alt=\"rId36\" width=\"136\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148637.png\" alt=\"rId37\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148307.png\" alt=\"rId34\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148418.png\" alt=\"rId35\" width=\"181\" height=\"68\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148538.png\" alt=\"rId36\" width=\"140\" height=\"72\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148637.png\" alt=\"rId37\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148733.png\" alt=\"rId38\" width=\"307\" height=\"71\"></p>",
                    solution_hi: "<p>22.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503148831.png\" alt=\"rId39\" width=\"286\" height=\"68\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option that represents the correct order of the given words as they would appear in an English dictionary.<br>1. Ferocious<br>2. Fervor<br>3. Fiction<br>4. Faith<br>5. Feign<br>6. Ferment</p>",
                    question_hi: "<p>23. उस विकल्प का चयन कीजिए, जो दिए गए शब्दों के सही क्रम को निरूपित करता है, जैसे कि वे अँग्रेजी शब्दकोश में दिखाई देते हैं।<br>1. Ferocious<br>2. Fervor<br>3. Fiction<br>4. Faith<br>5. Feign<br>6. Ferment</p>",
                    options_en: [
                        "<p>4, 6, 5, 2, 1, 3</p>",
                        "<p>4, 5, 6, 1, 2, 3</p>",
                        "<p>6, 5, 2, 1, 4, 3</p>",
                        "<p>6, 4, 5, 3, 2, 1</p>"
                    ],
                    options_hi: [
                        "<p>4, 6, 5, 2, 1, 3</p>",
                        "<p>4, 5, 6, 1, 2, 3</p>",
                        "<p>6, 5, 2, 1, 4, 3</p>",
                        "<p>6, 4, 5, 3, 2, 1</p>"
                    ],
                    solution_en: "<p>23.(b) The correct order is :- <br>Faith(4) &rarr; Feign(5) &rarr; Ferment(6) &rarr; Ferocious(1) &rarr; Fervor(2) &rarr; Fiction(3)</p>",
                    solution_hi: "<p>23.(b) सही क्रम है:-<br>Faith(4) &rarr; Feign(5) &rarr; Ferment(6) &rarr; Ferocious(1) &rarr; Fervor(2) &rarr; Fiction(3)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the correct mirror image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149121.png\" alt=\"rId40\" width=\"130\" height=\"136\"></p>",
                    question_hi: "<p>24. जब दर्पण को MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149121.png\" alt=\"rId40\" width=\"130\" height=\"136\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149227.png\" alt=\"rId41\" width=\"120\" height=\"28\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149389.png\" alt=\"rId42\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149520.png\" alt=\"rId43\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149684.png\" alt=\"rId44\" width=\"120\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149227.png\" alt=\"rId41\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149389.png\" alt=\"rId42\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149520.png\" alt=\"rId43\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149684.png\" alt=\"rId44\" width=\"120\"></p>"
                    ],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149520.png\" alt=\"rId43\" width=\"120\"></p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149520.png\" alt=\"rId43\" width=\"120\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the option in which the given figure (X) is embedded. (Rotation is NOT allowed). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149772.png\" alt=\"rId45\" width=\"124\" height=\"89\"></p>",
                    question_hi: "<p>25. उस विकल्प का चयन कीजिए, जिसमें दी गई आकृति (X) निहित है। (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149772.png\" alt=\"rId45\" width=\"124\" height=\"89\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149883.png\" alt=\"rId46\" width=\"90\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150021.png\" alt=\"rId47\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150161.png\" alt=\"rId48\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150293.png\" alt=\"rId49\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503149883.png\" alt=\"rId46\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150021.png\" alt=\"rId47\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150161.png\" alt=\"rId48\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150293.png\" alt=\"rId49\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150434.png\" alt=\"rId50\" width=\"90\" height=\"87\"></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150434.png\" alt=\"rId50\" width=\"90\" height=\"87\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Match the following neighbouring countries with their official languages correctly. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150537.png\" alt=\"rId51\" width=\"273\" height=\"101\"></p>",
                    question_hi: "<p>26. निम्नलिखित पड़ोसी देशों का उनकी आधिकारिक भाषाओं के साथ सही मिलान कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150621.png\" alt=\"rId52\" width=\"265\" height=\"142\"></p>",
                    options_en: [
                        "<p>1-c, 2-a, 3-b, 4-d</p>",
                        "<p>1-c, 2-a, 3-d, 4-b</p>",
                        "<p>1-c, 2-d, 3-a, 4-b</p>",
                        "<p>1-a, 2-c, 3-d, 4-b</p>"
                    ],
                    options_hi: [
                        "<p>1-c, 2-a, 3-b, 4-d</p>",
                        "<p>1-c, 2-a, 3-d, 4-b</p>",
                        "<p>1-c, 2-d, 3-a, 4-b</p>",
                        "<p>1-a, 2-c, 3-d, 4-b</p>"
                    ],
                    solution_en: "<p>26.(b) <strong>1-c, 2-a, 3-d, 4-b. </strong>Country and Official language: Bangladesh(Bengali), Israel (Hebrew), Maldives (Dhivehi), Netherlands (Dutch), Portugal (Portuguese), Saudi Arabia (Arabic), Sri Lanka (Sinhala and Tamil).</p>",
                    solution_hi: "<p>26.(b) <strong>1-c, 2-a, 3-d, 4-b. </strong>देश और आधिकारिक भाषा: बांग्लादेश (बंगाली), इज़राइल (हिब्रू), मालदीव (धिवेही), नीदरलैंड (डच), पुर्तगाल (पुर्तगाली), सऊदी अरब (अरबी), श्रीलंका (सिंहली और तमिल)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Pellagra is a deficiency disease that usually occurs due to lack of which of the following nutrients?</p>",
                    question_hi: "<p>27. पेलाग्रा (Pellagra), एक कमी से होने वाला रोग है जो सामान्यतः निम्नलिखित में से किस पोषक तत्त्व की कमी के कारण होता है?</p>",
                    options_en: [
                        "<p>Vitamin E</p>",
                        "<p>Niacin</p>",
                        "<p>Iodine</p>",
                        "<p>Riboflavin</p>"
                    ],
                    options_hi: [
                        "<p>विटामिन E (Vitamin E)</p>",
                        "<p>नियासिन (Niacin)</p>",
                        "<p>आयोडीन (lodine)</p>",
                        "<p>राइबोफ्लेविन (Riboflavin)</p>"
                    ],
                    solution_en: "<p>27.(b) <strong>Niacin (Vitamin</strong> B<sub>3</sub>). Vitamin A (Retinol) - Night Blindness. Vitamin B<sub>1</sub> (Thiamine) - Beriberi. Vitamin B<sub>2</sub> (Riboflavin) - Ariboflavinosis. Vitamin B<sub>5</sub> (Pantothenic Acid) - Paresthesia. Vitamin B<sub>6</sub> (Pyridoxine) - Peripheral Neuropathy. Vitamin B<sub>7</sub> (Biotin) - Hair Loss (Alopecia), Vitamin B<sub>9</sub> (Folic Acid) - Megaloblastic Anemia. Vitamin B<sub>12</sub> (Cobalamin) - Pernicious Anemia, Vitamin C (Ascorbic Acid) - Scurvy. Vitamin D (Cholecalciferol) - Rickets (in children), Osteomalacia (in adults). Vitamin E (Tocopherol) - Ataxia. Vitamin K (Phylloquinone or Menaquinone) - Hemorrhagic Disease (due to poor blood clotting).</p>",
                    solution_hi: "<p>27.(b) <strong>नियासिन (विटामिन B<sub>3</sub></strong>)। विटामिन A (रेटिनॉल) - रतौंधी। विटामिन B<sub>1</sub> (थायमीन) - बेरीबेरी। विटामिन B<sub>2</sub> (राइबोफ्लेविन) - एरिबोफ्लेविनोसिस। विटामिन B<sub>5</sub> (पैंटोथेनिक अम्ल) - पेरेस्टेसिया। विटामिन B<sub>6</sub> (पाइरिडोक्सिन) - परिधीय न्यूरोपैथी। विटामिन B<sub>7</sub> (बायोटिन) - बालों का झड़ना (एलोपेसिया), विटामिन B<sub>9</sub> (फोलिक अम्ल) - मेगालोब्लास्टिक एनीमिया। विटामिन B<sub>12</sub> (कोबालामिन) - घातक एनीमिया, विटामिन C (एस्कॉर्बिक अम्ल) - स्कर्वी। विटामिन D (कोलेकैल्सीफेराल) - रिकेट्स (बच्चों में), ऑस्टियोमैलेशिया (वयस्कों में)। विटामिन E (टोकोफेराल) - गतिभ्रम। विटामिन K (फाइलोक्विनोन या मेनाक्विनोन) - रक्तस्रावी रोग (खराब रक्त के थक्के के कारण)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The Mahananda River rising in the ________ is a tributary of Ganga.</p>",
                    question_hi: "<p>28. _______ से निकलने वाली महानंदा नदी, गंगा की एक सहायक नदी है।</p>",
                    options_en: [
                        "<p>Chota Nagpur plateau</p>",
                        "<p>Darjeeling hills</p>",
                        "<p>Kaimur hills</p>",
                        "<p>Nanda Devi peak</p>"
                    ],
                    options_hi: [
                        "<p>छोटा नागपुर पठार</p>",
                        "<p>दार्जिलिंग की पहाड़ियों</p>",
                        "<p>कैमूर की पहाड़ियों</p>",
                        "<p>नंदा देवी शिखर</p>"
                    ],
                    solution_en: "<p>28.(b) <strong>Darjeeling hills.</strong> The Mahananda River originates in the Darjeeling Hills of West Bengal and flows through Bihar, entering Bangladesh. Chota Nagpur Plateau: The Subarnarekha River originates here, flowing through Jharkhand, West Bengal, and Odisha.<br>Kaimur Hills: The Son river flows parallel to the Kaimur hills, flowing east-northeast through Uttar Pradesh, Jharkhand, and Bihar to join the Ganga just above Patna. Nanda Devi Peak: This is the source of the Alaknanda River, which is one of the main tributaries of the Ganga.</p>",
                    solution_hi: "<p>28.(b) <strong>दार्जिलिंग</strong> <strong>की पहाड़ियों।</strong> महानंदा नदी का उद्गम पश्चिम बंगाल के दार्जिलिंग पहाड़ियों से होता है और बिहार से होकर बांग्लादेश में प्रवेश करती है। छोटा नागपुर पठार: सुवर्णरेखा नदी का उद्गम यहीं से होता है, जो झारखंड, पश्चिम बंगाल और ओडिशा से होकर बहती है। कैमूर पहाड़ियाँ: सोन नदी कैमूर पहाड़ियों के समानांतर बहती है, जो पूर्व में -उत्तर-पूर्व में बहती हुई उत्तर प्रदेश, झारखंड और बिहार के पटना में गंगा में मिल जाती है। नंदा देवी शिखर: यह अलकनंदा नदी का स्रोत है, जो गंगा की मुख्य सहायक नदियों में से एक है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Where was the Neem Summit &amp; Global Neem Trade Fair held in 2024?</p>",
                    question_hi: "<p>29. 2024 में नीम शिखर सम्मेलन और वैश्विक नीम व्यापार मेला कहाँ आयोजित किया गया था?</p>",
                    options_en: [
                        "<p>Mumbai</p>",
                        "<p>New Delhi</p>",
                        "<p>Bengaluru</p>",
                        "<p>Chennai</p>"
                    ],
                    options_hi: [
                        "<p>मुंबई</p>",
                        "<p>नई दिल्ली</p>",
                        "<p>बेंगलुरु</p>",
                        "<p>चेन्नई</p>"
                    ],
                    solution_en: "<p>29.(b) <strong>New Delhi.</strong> The Neem Summit &amp; Global Neem Trade Fair was organized by ICAR-Central Agroforestry Research Institute, Jhansi, and other stakeholders. The event aimed to highlight neem\'s diverse applications in various sectors.</p>",
                    solution_hi: "<p>29.(b) <strong>नई दिल्ली।</strong> नीम शिखर सम्मेलन और वैश्विक नीम व्यापार मेले का आयोजन आईसीएआर-केंद्रीय कृषिवानिकी अनुसंधान संस्थान, झाँसी और अन्य हितधारकों द्वारा किया गया था। इस आयोजन का उद्देश्य विभिन्न क्षेत्रों में नीम के विविध अनुप्रयोगों को उजागर करना था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Pusarla Venkata Sindhu won silver medal in badminton in which Olympics Games?</p>",
                    question_hi: "<p>30. पुसरला वेंकट सिंधु (Pusarla Venkata Sindhu) ने किस ओलंपिक खेल में बैडमिंटन में रजत पदक जीता?</p>",
                    options_en: [
                        "<p>Beijing 2008</p>",
                        "<p>Rio 2016</p>",
                        "<p>Tokyo 2020</p>",
                        "<p>London 2012</p>"
                    ],
                    options_hi: [
                        "<p>बीजिंग 2008</p>",
                        "<p>रियो 2016</p>",
                        "<p>टोक्यो 2020</p>",
                        "<p>लंदन 2012</p>"
                    ],
                    solution_en: "<p>30.(b) <strong>Rio</strong> <strong>2016</strong>. The 2016 Summer Olympics were held in Brazil, in the city of Rio de Janeiro. Mascot - Vinicius. She is the only Indian woman to have won multiple medals at the Olympics - a silver medal in women\'s singles at Rio 2016 and a bronze at Tokyo 2020. Her awards: Padma Bhushan (2020), Padma Shri (2015), Arjuna Award (2013), and Major Dhyan Chand Khel Ratna (2016).</p>",
                    solution_hi: "<p>30.(b) <strong>रियो 2016.</strong> 2016 ग्रीष्मकालीन ओलंपिक ब्राजील के रियो डी जेनेरियो शहर में आयोजित किए गए थे। शुभंकर - विनीसियस। वह ओलंपिक में कई पदक जीतने वाली एकमात्र भारतीय महिला हैं जिसमें रियो 2016 में महिला एकल में रजत पदक और टोक्यो 2020 में कांस्य पदक शामिल है। उनके पुरस्कार: पद्म भूषण (2020), पद्म श्री (2015), अर्जुन पुरस्कार (2013), और मेजर ध्यानचंद खेल रत्न (2016)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Some of the talas invented by _______ include Trimukhi, Panchamukhi, Saptamukhi and Navamukhi.</p>",
                    question_hi: "<p>31. ___________ द्वारा आविष्कार की गई कुछ तालों में त्रिमुखी, पंचमुखी, सप्तमुखी और नवमुखी शामिल हैं।</p>",
                    options_en: [
                        "<p>M Balamuralikrishna</p>",
                        "<p>Hariprasad Chaurasia</p>",
                        "<p>Yashraj</p>",
                        "<p>Bhimsen Joshi</p>"
                    ],
                    options_hi: [
                        "<p>एम. बालमुरलीकृष्णा</p>",
                        "<p>हरिप्रसाद चौरसिया</p>",
                        "<p>यशराज</p>",
                        "<p>भीमसेन जोशी</p>"
                    ],
                    solution_en: "<p>31.(a) <strong>M Balamuralikrishna</strong> was a renowned Indian vocalist, composer, and innovator in Carnatic music. His Awards: Padma Shri (1971), Padma Vibhushan (1991), Sangeet Natak Akademi Award (1975), Mahatma Gandhi Silver Medal from UNESCO (1995).</p>",
                    solution_hi: "<p>31.(a) <strong>एम</strong>. <strong>बालमुरलीकृष्णा</strong> कर्नाटक संगीत के एक प्रसिद्ध भारतीय गायक, संगीतकार और प्रवर्तक थे। उन्हें पद्म श्री (1971), पद्म विभूषण (1991), संगीत नाटक अकादमी पुरस्कार (1975), यूनेस्को से महात्मा गांधी रजत पदक (1995) से सम्मानित किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. What is the name of the ambitious green energy project being developed in the North Sea?</p>",
                    question_hi: "<p>32. उत्तर सागर में किस महत्वाकांक्षी हरित ऊर्जा परियोजना का विकास किया जा रहा है?</p>",
                    options_en: [
                        "<p>The Green Horizon Project</p>",
                        "<p>Princess Elisabeth Energy Island</p>",
                        "<p>North Sea Renewable Hub</p>",
                        "<p>EcoPower Island</p>"
                    ],
                    options_hi: [
                        "<p>द ग्रीन होराइजन प्रोजेक्ट</p>",
                        "<p>प्रिंसेस एलिज़ाबेथ एनर्जी आइलैंड</p>",
                        "<p>नॉर्थ सी रिन्यूएबल हब</p>",
                        "<p>इकोपावर आइलैंड</p>"
                    ],
                    solution_en: "<p>32.(b) <strong>Princess Elisabeth Energy Island.</strong> The energy island is named after Belgium&rsquo;s Princess Elisabeth. It is a world-first initiative for renewable energy transformation, marking a significant step toward green energy. The project is being constructed along the North Sea coast.</p>",
                    solution_hi: "<p>32.(b) <strong>प्रिंसेस एलिज़ाबेथ एनर्जी आइलैंड।</strong> यह ऊर्जा द्वीप बेल्जियम की प्रिंसेस एलिज़ाबेथ के नाम पर रखा गया है। यह नवीकरणीय ऊर्जा परिवर्तन के लिए एक विश्व-प्रथम पहल है, जो हरित ऊर्जा की दिशा में एक महत्वपूर्ण कदम है। यह परियोजना उत्तर सागर तट के साथ बनाई जा रही है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. The approximate number of water molecules present in 9 gm of water is _____.</p>",
                    question_hi: "<p>33. 9 gm जल में उपस्थित जल के अणुओं की लगभग संख्या _____ होती है।</p>",
                    options_en: [
                        "<p>9.023 &times; 10<sup>23</sup></p>",
                        "<p>6.022 &times; 10<sup>23</sup></p>",
                        "<p>3.011 &times; 10<sup>23</sup></p>",
                        "<p>6.022 &times; 10<sup>22</sup></p>"
                    ],
                    options_hi: [
                        "<p>9.023 &times; 10<sup>23</sup></p>",
                        "<p>6.022 &times; 10<sup>23</sup></p>",
                        "<p>3.011 &times; 10<sup>23</sup></p>",
                        "<p>6.022 &times; 10<sup>22</sup></p>"
                    ],
                    solution_en: "<p>33.(c) <strong>3.011</strong> <strong>&times; 10<sup>23</sup></strong>. The molar mass of water = 18 grams/mol. Moles of water in 9 grams =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mrow><mi>Mass</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>water</mi></mrow><mrow><mi>Molar</mi><mo>&#160;</mo><mi>mass</mi></mrow></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>18</mn></mfrac></math> = 0.5 moles. One mole contains 6.011 &times; 10<sup>23</sup> molecules. Molecules in 0.5 moles = 0.5 &times; 6.011 &times; 10<sup>23</sup> = 3.011 &times; 10<sup>23</sup>.</p>",
                    solution_hi: "<p>33.(c) <strong>3.011</strong> <strong>&times; 10<sup>23</sup></strong>. जल का मोलर द्रव्यमान = 18 ग्राम/मोल। 9 ग्राम जल में मोल की संख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2332;&#2354;</mi><mo>&#160;&#160;</mo><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344;</mi><mi mathvariant=\"normal\">&#8203;</mi></mrow><mrow><mi>&#2350;&#2379;&#2354;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>18</mn></mfrac></math> = 0.5 मोल। एक मोल में 6.011 &times; 10<sup>23</sup> अणु होते हैं। 0.5 मोल में अणु = 0.5 &times; 6.011 &times; 10<sup>23</sup> = 3.011 &times; 10<sup>23</sup>.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Match the columns.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150724.png\" alt=\"rId53\" width=\"332\" height=\"111\"></p>",
                    question_hi: "<p>34. स्तंभों का मिलान कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150829.png\" alt=\"rId54\" width=\"423\" height=\"145\"></p>",
                    options_en: [
                        "<p>1-(D); 2 -(C); 3 -(B); 4 -(A)</p>",
                        "<p>1-(C); 2 -(D); 3 -(A); 4 -(B)</p>",
                        "<p>1-(A); 2 -(B); 3 -(C); 4 -(D)</p>",
                        "<p>1-(C); 2 -(A); 3 -(D); 4 -(B)</p>"
                    ],
                    options_hi: [
                        "<p>1-(D); 2 -(C); 3 -(B); 4 -(A)</p>",
                        "<p>1-(C); 2 -(D); 3 -(A); 4 -(B)</p>",
                        "<p>1-(A); 2 -(B); 3 -(C); 4 -(D)</p>",
                        "<p>1-(C); 2 -(A); 3 -(D); 4 -(B)</p>"
                    ],
                    solution_en: "<p>34.(b) <strong>1-(C); 2-(D); 3-(A); 4-(B).</strong> Dynasties and Famous Kings : Pandya - Nedunjeliyan II, Kadungon. Chalukyas - Pulakeshin I, Kirtivarman II. Hoysalas - Ereyanga, Veera Ballala I. Tuluvas - Krishnadevaraya, Achyuta Deva Raya.</p>",
                    solution_hi: "<p>34.(b) <strong>1-(C); 2-(D); 3-(A); 4-(B).</strong> राजवंश और प्रसिद्ध राजा: पाण्ड्य- नेदुन्जेलियन द्वितीय, कडुंगोन। चालुक्य - पुलकेशिन प्रथम, कीर्तिवर्मन द्वितीय। होयसल - एरेयंगा, वीर बल्लाल प्रथम। तुलुव - कृष्णदेवराय, अच्युता देव राय।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which state initiated the implementation of the Mid-day Meal scheme in elementary schools?</p>",
                    question_hi: "<p>35. किस राज्य ने प्राथमिक विद्यालयों में मध्याह्न भोजन योजना के कार्यान्वयन की शुरुआत की?</p>",
                    options_en: [
                        "<p>Tamil Nadu</p>",
                        "<p>Madhya Pradesh</p>",
                        "<p>Uttar Pradesh</p>",
                        "<p>Karnataka</p>"
                    ],
                    options_hi: [
                        "<p>तमिलनाडु</p>",
                        "<p>मध्य प्रदेश</p>",
                        "<p>उत्तर प्रदेश</p>",
                        "<p>कर्नाटक</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>Tamil Nadu.</strong> Mid-day Meal Scheme : Introduced in 1956 by Tamil Nadu\'s Chief Minister K. Kamaraj, the scheme aimed to reduce school dropout rates and increase enrollment by providing free meals to students. Government schemes for schools : Sarva Shiksha Abhiyan (2001-2002), Institution of Eminence Abhiyan (2017), Rashtriya Madhyamik Shiksha Abhiyan (2009), Padhe Bharat, Badhe Bharat (2014).</p>",
                    solution_hi: "<p>35.(a) <strong>तमिलनाडु।</strong> मध्याह्न भोजन योजना: 1956 में तमिलनाडु के मुख्यमंत्री के.कामराज द्वारा शुरू की गई इस योजना का उद्देश्य छात्रों को मुफ्त भोजन प्रदान करके स्कूल छोड़ने की दर को कम करना और नामांकन बढ़ाना था। स्कूलों के लिए सरकारी योजनाएँ: सर्व शिक्षा अभियान (2001-2002), इंस्टीट्यूशन ऑफ एमिनेंस अभियान (2017), राष्ट्रीय माध्यमिक शिक्षा अभियान (2009), पढ़े भारत, बढ़े भारत (2014)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following factors is primarily responsible for soil formation?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन-सा कारक मृदा निर्माण के लिए मुख्य रूप से उत्तरदायी है?</p>",
                    options_en: [
                        "<p>Weathering</p>",
                        "<p>Irrigation</p>",
                        "<p>Degradation</p>",
                        "<p>Mining</p>"
                    ],
                    options_hi: [
                        "<p>अपक्षय (Weathering)</p>",
                        "<p>सिंचाई (Irrigation)</p>",
                        "<p>अवक्रमण (Degradation)</p>",
                        "<p>खनन (Mining)</p>"
                    ],
                    solution_en: "<p>36.(a) <strong>Weathering</strong> : It involves the breakdown of rocks and minerals into smaller particles through exposure to environmental factors like wind, water, ice, temperature fluctuations and others. Irrigation : It supports agriculture and affects soil moisture. Degradation : This refers to the deterioration of soil quality. Mining : It can disturb existing soils.</p>",
                    solution_hi: "<p>36.(a) <strong>अपक्षय</strong> : इसमें वायु, जल, बर्फ, तापमान में परिवर्तन और अन्य पर्यावरणीय कारकों के संपर्क में आने से चट्टानों और खनिजों का छोटे-छोटे कणों में टूटना शामिल है। सिंचाई : यह कृषि को सहायता प्रदान करती है तथा मृदा नमी को प्रभावित करती है। अवक्रमण : इसका तात्पर्य मृदा की गुणवत्ता में गिरावट से है। खनन : यह मौजूदा मृदा को नुकसान पहुंचा सकता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Sardhana Christian Fair is held in which of the following states?</p>",
                    question_hi: "<p>37. सरधना ईसाई मेला निम्नलिखित में से किस राज्य में आयोजित किया जाता है?</p>",
                    options_en: [
                        "<p>West Bengal</p>",
                        "<p>Haryana</p>",
                        "<p>Madhya Pradesh</p>",
                        "<p>Uttar Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिम बंगाल</p>",
                        "<p>हरियाणा</p>",
                        "<p>मध्य प्रदेश</p>",
                        "<p>उत्तर प्रदेश</p>"
                    ],
                    solution_en: "<p>37.(d) <strong>Uttar Pradesh.</strong> Sardhana Christian Fair : Every year on the 1st Sunday of the month of November a large feast is held in honor of Mother Mary. Other Fairs of Uttar Pradesh : Kumbh Mela (the festival of the sacred Pitcher), Gau Charan Fair, Nauchandi Fair, Dewa Sharif Fair, Shakumbhari Fair, Magh Fair (Prayagraj), Rath Fair (Vrindavan), Agra Cattle Fair or Bateshwar Fair (Agra).</p>",
                    solution_hi: "<p>37.(d) <strong>उत्तर प्रदेश।</strong> सरधना ईसाई मेला: प्रत्येक वर्ष नवंबर महीने के प्रथम रविवार को माता मरियम के सम्मान में एक बड़ा उत्सव आयोजित किया जाता है। उत्तर प्रदेश के अन्य मेले: कुंभ मेला (पवित्र घड़े का त्योहार), गौ चरण मेला, नौचंदी मेला, देवा शरीफ मेला, शाकुंभरी मेला, माघ मेला (प्रयागराज), रथ मेला (वृंदावन), आगरा पशु मेला या बटेश्वर मेला (आगरा) इत्यादि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Who is the author of the book \'From Plassey to Partition and After : A History of Modern India\'?</p>",
                    question_hi: "<p>38. \'फ्रॉम प्लासी टू पार्टीशन एंड आफ्टर: अ हिस्ट्री ऑफ मॉडर्न इंडिया (\'From Plassey to Partition and After: A History of Modern India\') पुस्तक के लेखक/लेखिका कौन हैं?</p>",
                    options_en: [
                        "<p>Romila Thapar</p>",
                        "<p>Devid Hardiman</p>",
                        "<p>Sekhar Bandyopadhyay</p>",
                        "<p>Sumit Sarkar</p>"
                    ],
                    options_hi: [
                        "<p>रोमिला थापर</p>",
                        "<p>डेविड हार्डीमैन</p>",
                        "<p>शेखर बंद्योपाध्याय</p>",
                        "<p>सुमित सरकार</p>"
                    ],
                    solution_en: "<p>38.(c) <strong>Sekhar Bandyopadhyay.</strong> His other Book: &lsquo;&rsquo;Caste and Partition in Bengal: The Story of Dalit Refugees, 1946-1961&rsquo;&rsquo;. Romila Thapar - &lsquo;&rsquo;Somanatha&rsquo;&rsquo;, &lsquo;&rsquo;Sakuntala: Texts, Readings, Histories&rsquo;&rsquo;. Devid Hardiman - &lsquo;&rsquo;Gandhi in His Time and Ours: The Global Legacy of His Ideas&rsquo;&rsquo;, &lsquo;&rsquo;Feeding the Baniya: Peasants and Usurers in Western India&rsquo;&rsquo;. Sumit Sarkar - &lsquo;&rsquo;Writing Social History&rsquo;&rsquo;, &lsquo;&rsquo;The Swadeshi Movement in Bengal, 1903-1908&rsquo;&rsquo;.</p>",
                    solution_hi: "<p>38.(c) <strong>शेखर बंद्योपाध्याय।</strong> उनकी अन्य पुस्तक: &lsquo;कास्ट एण्ड पार्टीशन इन बंगाल: द स्टोरी ऑफ दलित रेफ्यूजी, 1946-1961&rsquo;। रोमिला थापर - &lsquo;सोमनाथ&rsquo;, &lsquo;शकुंतला: टेक्स्टस, रीडीन्ग, हिस्ट्रीज&rsquo;। डेविड हार्डीमैन- &lsquo;गांधी इन हिज टाइम एंड अवर्स: द ग्लोबल लिगेसी ऑफ हिज आइडियाज&rsquo;, &lsquo;फीडिंग द बनिया: पीजेंट्स एंड यूजरर्स इन वेस्टर्न इंडिया&rsquo;। सुमित सरकार - &lsquo; राइटिंग सोशल हिस्ट्री&rsquo;, &lsquo; द स्वदेशी मूवमेंट इन बंगाल, 1903-1908&rsquo;।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. A molecule with the chemical formula C<sub>2</sub>H<sub>6</sub> is a/an _________ molecule.</p>",
                    question_hi: "<p>39. रासायनिक सूत्र C<sub>2</sub>H<sub>6</sub> वाला अणु एक _______ अणु है।</p>",
                    options_en: [
                        "<p>butane</p>",
                        "<p>propane</p>",
                        "<p>methane</p>",
                        "<p>ethane</p>"
                    ],
                    options_hi: [
                        "<p>ब्यूटेन (butane)</p>",
                        "<p>प्रोपेन (propane)</p>",
                        "<p>मीथेन (methane)</p>",
                        "<p>एथेन (ethane)</p>"
                    ],
                    solution_en: "<p>39.(d) <strong>ethane.</strong> It is the second stable member of the alkane series. It has 7 covalent bonds. Ethane gas has a slightly sweet taste. It is used to make ethylene glycol which is further used in the manufacturing of polyester fibre. Methane (CH<sub>4</sub>), Ethane (C<sub>2</sub>H<sub>6</sub>), Propane (C<sub>3</sub>H<sub>8</sub>) and Butane (C<sub>4</sub>H<sub>10</sub>) are examples of saturated hydrocarbons.</p>",
                    solution_hi: "<p>39.(d) <strong>एथेन।</strong> यह एल्केन श्रृंखला का दूसरा स्थायी सदस्य है। इसमें 7 सहसंयोजक बंध हैं। इथेन गैस का स्वाद हल्का मीठा होता है। इसका उपयोग एथिलीन ग्लाइकॉल बनाने के लिए किया जाता है जिसका उपयोग पॉलिएस्टर फाइबर के निर्माण में किया जाता है। मीथेन (CH<sub>4</sub>), इथेन (C<sub>2</sub>H<sub>6</sub>), प्रोपेन (C<sub>3</sub>H<sub>8</sub>) और ब्यूटेन (C<sub>4</sub>H<sub>10</sub>) संतृप्त हाइड्रोकार्बन के उदाहरण हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Pandit Arvind Thatte belongs to which of the following gharanas?</p>",
                    question_hi: "<p>40. पंडित अरविंद थट्टे (Pandit Arvind Thatte) निम्नलिखित में से किस घराने से संबंधित हैं?</p>",
                    options_en: [
                        "<p>Indore gharana</p>",
                        "<p>Mewati gharana</p>",
                        "<p>Agra gharana</p>",
                        "<p>Benaras gharana</p>"
                    ],
                    options_hi: [
                        "<p>इंदौर घराना</p>",
                        "<p>मेवाती घराना</p>",
                        "<p>आगरा घराना</p>",
                        "<p>बनारस घराना</p>"
                    ],
                    solution_en: "<p>40.(b) <strong>Mewati gharana.</strong> Arvind Thatte is an Indian harmonium player. The Mewati Gharana is a musical family (Gharana) of Hindustani classical music founded in the late 19th century by Utd. Ghagge Nazir Khan. Gharana and founder: Agra gharana - Haji Sujan Khan, Benaras gharana - Pandit Ram Sahai, Indore gharana - Ustad Amir Khan.</p>",
                    solution_hi: "<p>40.(b) <strong>मेवाती</strong> <strong>घराना</strong>। अरविंद थट्टे एक भारतीय हारमोनियम वादक हैं। मेवाती घराना हिंदुस्तानी शास्त्रीय संगीत का एक संगीत परिवार (घराना) है, जिसकी स्थापना 19वीं शताब्दी के अंत में उस्ताद घग्गे नाजिर खान द्वारा की गई थी। घराना एवं संस्थापक: आगरा घराना -हाजी सुजान खान, बनारस घराना - पंडित राम सहाय, इंदौर घराना - उस्ताद अमीर खान।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following articles of the Constitution of India lays down that &lsquo;The allocation of seats in the Council of States to be filled by representatives of the States and of the Union territories shall be in accordance with the provisions in that behalf contained in the fourth Schedule&rsquo;?</p>",
                    question_hi: "<p>41. भारतीय संविधान के निम्नलिखित में से किस अनुच्छेद में यह कहा गया है कि \'राज्यों और परिषद में सीटों का आवंटन चौथी अनुसूची में निहित प्रावधानों के अनुसार होगा\'?</p>",
                    options_en: [
                        "<p>Article 81</p>",
                        "<p>Article 82</p>",
                        "<p>Article 83</p>",
                        "<p>Article 80</p>"
                    ],
                    options_hi: [
                        "<p>अनुच्छेद 81</p>",
                        "<p>अनुच्छेद 82</p>",
                        "<p>अनुच्छेद 83</p>",
                        "<p>अनुच्छेद 80</p>"
                    ],
                    solution_en: "<p>41.(d) <strong>Article 80</strong> - It lays down the maximum strength of Rajya Sabha as 250, out of which 12 members are nominated by the President and 238 are representatives of the States and of the Union Territories. Other Articles : Article 81 - Composition of the House of the People. Article 82 - Readjustment after each census. Article 83 - Duration of Houses of Parliament.</p>",
                    solution_hi: "<p>41.(d) <strong>अनुच्छेद 80</strong> - यह राज्यसभा की अधिकतम संख्या 250 निर्धारित करता है, जिसमें से 12 सदस्य राष्ट्रपति द्वारा नामित होते हैं और 238 सदस्य राज्यों और केंद्र शासित प्रदेशों के प्रतिनिधि होते हैं। अन्य अनुच्छेद : अनुच्छेद 81 - लोक सभा की संरचना। अनुच्छेद 82 - प्रत्येक जनगणना के बाद पुनः समायोजन। अनुच्छेद 83 - संसद के सदनों की अवधि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. In which region of Bangladesh do the Chakma people predominantly live, among the country\'s various indigenous ethnic groups?</p>",
                    question_hi: "<p>42. बांग्लादेश के किस क्षेत्र में देश के विभिन्न स्वदेशी जातीय समूहों के बीच चकमा लोग (Chakma people) मुख्य रूप से रहते हैं?</p>",
                    options_en: [
                        "<p>Rajshahi Division</p>",
                        "<p>Sylhet Division</p>",
                        "<p>Chittagong Hill Tracts</p>",
                        "<p>Sundarbans Mangrove Forest</p>"
                    ],
                    options_hi: [
                        "<p>राजशाही प्रभाग (Rajshahi Division)</p>",
                        "<p>सिलहट प्रभाग (Sylhet Division)</p>",
                        "<p>चिटगांव पहाड़ी क्षेत्र (Chittagong Hill Tracts)</p>",
                        "<p>सुंदरबन मैंग्रोव वन (Sundarbans Mangrove Forest)</p>"
                    ],
                    solution_en: "<p>42.(c) <strong>Chittagong Hill Tracts.</strong> The Chittagong Hill Tracts in Bangladesh are a hilly region home to various tribes. In the 19th century, the British divided the area into three circles: Chakma, Mong, and Bohmong. Post-independence, it was reorganized into Rangamati, Khagrachhari, and Bandarban districts.The Chakma people are an ethnic group of Buddhists who live in the eastern Indian subcontinent and Western Myanmar.</p>",
                    solution_hi: "<p>42.(c) <strong>चिटगांव पहाड़ी क्षेत्र।</strong> बांग्लादेश में चिटगांव पहाड़ी क्षेत्र विभिन्न जनजातियों का घर है। 19वीं शताब्दी में, अंग्रेजों ने इस क्षेत्र को तीन भागों में विभाजित किया: चकमा, मोंग और बोहमोंग। स्वतंत्रता के बाद, इसे रंगमती, खगराछारी और बंदरबन जिलों में पुनर्गठित किया गया। चकमा लोग बौद्धों का एक जातीय समूह है जो पूर्वी भारतीय उपमहाद्वीप और पश्चिमी म्यांमार में रहते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. The model of \'Quasi Federal\' form of government in the Indian constitution is borrowed from which country?</p>",
                    question_hi: "<p>43. भारतीय संविधान में सरकार के अर्द्ध-संघीय (Quasi Federal)\' स्वरूप का मॉडल किस देश से लिया गया है?</p>",
                    options_en: [
                        "<p>Germany</p>",
                        "<p>Canada</p>",
                        "<p>United Kingdom</p>",
                        "<p>Australia</p>"
                    ],
                    options_hi: [
                        "<p>जर्मनी</p>",
                        "<p>कनाडा</p>",
                        "<p>यूनाइटेड किगडम</p>",
                        "<p>ऑस्ट्रेलिया</p>"
                    ],
                    solution_en: "<p>43.(b) <strong>Canada.</strong> Borrowed features of Indian Constitution: Canada - Federation with a strong Centre, vesting of residuary powers in the Centre, appointment of state Governors by the Centre, and advisory jurisdiction of the Supreme Court. Germany - Suspension of Fundamental Rights during Emergency. Australia - Concurrent List, freedom of trade, commerce and inter-course, and joint sitting of the two Houses of Parliament. British - Parliamentary government, Rule of Law, legislative procedure, single citizenship.</p>",
                    solution_hi: "<p>43.(b) <strong>कनाडा।</strong> भारतीय संविधान की ली गई विशेषताएँ: कनाडा - एक मजबूत केंद्र के साथ संघ, केंद्र में अवशिष्ट शक्तियों का निहित होना, केंद्र द्वारा राज्य के राज्यपालों की नियुक्ति और सर्वोच्च न्यायालय का सलाहकार क्षेत्राधिकार। जर्मनी - आपातकाल के दौरान मौलिक अधिकारों का निलंबन। ऑस्ट्रेलिया - समवर्ती सूची, व्यापार, वाणिज्य और अंतर-संचालन की स्वतंत्रता, और संसद के दोनों सदनों की संयुक्त बैठक। ब्रिटिश - संसदीय सरकार, कानून का शासन, विधायी प्रक्रिया, एकल नागरिकता।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. What does M stand for in BHIM-UPI?</p>",
                    question_hi: "<p>44. BHIM-UPI में M का अर्थ क्या है?</p>",
                    options_en: [
                        "<p>Monetary</p>",
                        "<p>Mobile</p>",
                        "<p>Money</p>",
                        "<p>Monthly</p>"
                    ],
                    options_hi: [
                        "<p>मोनीटरी (Monetary)</p>",
                        "<p>मोबाइल (Mobile)</p>",
                        "<p>मनी (Money)</p>",
                        "<p>मंथली (Monthly)</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>Money.</strong> Bharat Interface for Money (BHIM) is a payment app that enables simple, quick, and easy transactions using the Unified Payments Interface (UPI). It was developed by the National Payments Corporation of India (NPCI) and launched on December 30, 2016.</p>",
                    solution_hi: "<p>44.(c) <strong>मनी (Money)।</strong> भारत इंटरफेस फॉर मनी (BHIM) एक पेमेंट ऐप है जो यूनिफाइड पेमेंट्स इंटरफेस (UPI) का उपयोग करके सरल, त्वरित और आसान लेनदेन को सक्षम बनाता है। इसे नेशनल पेमेंट्स कॉर्पोरेशन ऑफ इंडिया (NPCI) द्वारा विकसित किया गया था और 30 दिसंबर, 2016 को शुरू किया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Jon Beel mela is held every year in which of the following states?</p>",
                    question_hi: "<p>45. निम्नलिखित में से किस राज्य में जॉन बील मेला (Jon Beel mela) हर वर्ष आयोजित किया जाता है?</p>",
                    options_en: [
                        "<p>Haryana</p>",
                        "<p>Arunachal Pradesh</p>",
                        "<p>Assam</p>",
                        "<p>Telangana</p>"
                    ],
                    options_hi: [
                        "<p>हरियाणा</p>",
                        "<p>अरुणाचल प्रदेश</p>",
                        "<p>असम</p>",
                        "<p>तेलंगाना</p>"
                    ],
                    solution_en: "<p>45.(c) <strong>Assam.</strong> The Jonbeel Mela is a three-day annual fair in Assam, India that celebrates the Tiwa community\'s culture and traditions. Other festivals : Assam - Ambubachi Mela. Haryana - Surajkund International Crafts Mela (Faridabad), Kapal Mochan Mela (Gopal Mochan), Chetar Chaudas Fair, Masani Fair.</p>",
                    solution_hi: "<p>45.(c) <strong>असम।</strong> जॉनबील मेला असम, भारत में तीन दिवसीय वार्षिक मेला है जो तिवा समुदाय की संस्कृति और परंपराओं का जश्न मनाता है। अन्य त्योहार: असम - अम्बुबाची मेला। हरियाणा - सूरजकुंड अंतर्राष्ट्रीय शिल्प मेला (फरीदाबाद), कपाल मोचन मेला (गोपाल मोचन), चेतर चौदस मेला, मसानी मेला।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Who launched the Bharat Cleantech Manufacturing Platform at the Bharat Climate Forum 2025 in New Delhi?</p>",
                    question_hi: "<p>46. नई दिल्ली में भारत जलवायु मंच 2025 में भारत क्लीनटेक विनिर्माण मंच का शुभारंभ किसने किया?</p>",
                    options_en: [
                        "<p>Narendra Modi</p>",
                        "<p>Piyush Goyal</p>",
                        "<p>Nitin Gadkari</p>",
                        "<p>Amit Shah</p>"
                    ],
                    options_hi: [
                        "<p>नरेंद्र मोदी</p>",
                        "<p>पीयूष गोयल</p>",
                        "<p>नितिन गडकरी</p>",
                        "<p>अमित शाह</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>Piyush Goyal</strong> is the Union Minister of Commerce &amp; Industry. This initiative aims to strengthen India&rsquo;s cleantech value chains in key sectors such as solar, wind, hydrogen, and battery storage. </p>",
                    solution_hi: "<p>46.(b) <strong>पीयूष गोयल</strong> केंद्रीय वाणिज्य एवं उद्योग मंत्री हैं। इस पहल का उद्देश्य सौर, पवन, हाइड्रोजन और बैटरी भंडारण जैसे प्रमुख क्षेत्रों में भारत की क्लीनटेक मूल्य श्रृंखलाओं को मजबूत&nbsp;करना है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. What is the usual treatment on repo rates by the Reserve Bank of India in case of a hawkish monetary policy stance?</p>",
                    question_hi: "<p>47. आक्रामक मौद्रिक नीति के रुख (hawkish monetary policy stance) के मामले में भारतीय रिजर्व बैंक द्वारा रेपो दरों पर सामान्य उपचार क्या है?</p>",
                    options_en: [
                        "<p>Reduced gradually</p>",
                        "<p>Reduced sharply by more than 100 bps</p>",
                        "<p>Increased</p>",
                        "<p>Kept the same</p>"
                    ],
                    options_hi: [
                        "<p>धीरे-धीरे घटाई जाती है</p>",
                        "<p>100 बीपीएस से अधिक की तीव्र कटौती</p>",
                        "<p>बढ़ाई जाती है</p>",
                        "<p>अपरिवर्तित रखी जाती है</p>"
                    ],
                    solution_en: "<p>47.(c) <strong>Increased.</strong> In a hawkish monetary policy stance, the Reserve Bank of India (RBI) aims to control inflation, reduce demand, and stabilize the economy. To achieve this, the RBI typically increases the repo rate (the rate at which banks borrow money from the RBI), reduces money supply in the economy and makes borrowing more expensive for consumers and businesses.</p>",
                    solution_hi: "<p>47.(c) <strong>बढ़ाई जाती है।</strong> भारतीय रिजर्व बैंक (RBI) का लक्ष्य मुद्रास्फीति को नियंत्रित करना, मांग को कम करना और अर्थव्यवस्था को स्थिर करना है। इसे प्राप्त करने के लिए, RBI सामान्यतः रेपो दर (जिस दर पर बैंक RBI से पैसे उधार लेते हैं) को बढ़ाता है, अर्थव्यवस्था में धन की आपूर्ति को कम करता है और उपभोक्ताओं और व्यवसायों के लिए उधार लेना अधिक महंगा बनाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following Sultans of Delhi primarily adopted a policy of consolidation rather than expansion?</p>",
                    question_hi: "<p>48. दिल्ली के निम्नलिखित में से किस सुल्तान ने मुख्य रूप से विस्तार के बजाय सुदृढ़ीकरण की नीति अपनाई?</p>",
                    options_en: [
                        "<p>Ibrahim Lodi</p>",
                        "<p>Balban</p>",
                        "<p>Alauddin Khalji</p>",
                        "<p>Bahlol Lodi</p>"
                    ],
                    options_hi: [
                        "<p>इब्राहीम लोदी</p>",
                        "<p>बलबन</p>",
                        "<p>अलाउद्दीन खिलजी</p>",
                        "<p>बहलोल लोदी</p>"
                    ],
                    solution_en: "<p>48.(b) <strong>Balban.</strong> Ghiyasuddin Balban (Reigned : 1266&ndash;1287) was the ninth sultan of the Slave dynasty of Delhi. He belonged to the famous group of 40 Turkic slaves of Iltutmish. He was the first Muslim ruler to formulate the \'theory of kingship\' similar to the \'theory of the divine right of the kings\'. He established the military department &ldquo;Diwan-i-Arz&rdquo;.</p>",
                    solution_hi: "<p>48.(b) <strong>बलबन।</strong> ग़यासुद्दीन बलबन (शासनकाल: 1266-1287) दिल्ली के गुलाम वंश का नौवां सुल्तान था। वह इल्तुतमिश के 40 तुर्क गुलामों के प्रसिद्ध समूह से संबंधित था। वह \'राजाओं के दैवीय अधिकार के सिद्धांत\' के समान \'राजत्व के सिद्धांत\' को प्रतिपादित करने वाला पहला मुस्लिम शासक था। उसने सैन्य विभाग \"दीवान-ए-अर्ज़\" की स्थापना की।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. In India, who introduced the idea of the poverty line for the first time?</p>",
                    question_hi: "<p>49. भारत में गरीबी रेखा का विचार पहली बार किसने दिया?</p>",
                    options_en: [
                        "<p>Dadabhai Naoroji</p>",
                        "<p>Dr. BR Ambedkar</p>",
                        "<p>Mahatma Gandhi</p>",
                        "<p>Jawaharlal Nehru</p>"
                    ],
                    options_hi: [
                        "<p>दादाभाई नौरोजी</p>",
                        "<p>डॉ. बी. आर. अम्बेडकर</p>",
                        "<p>महात्मा गांधी</p>",
                        "<p>जवाहर लाल नेहरू</p>"
                    ],
                    solution_en: "<p>49.(a) <strong>Dadabhai Naoroji</strong> through his book, &ldquo;Poverty and Unbritish Rule in India&rdquo; made the earliest estimation of the poverty line (₹16 to ₹35 per capita per year). The poverty line proposed by him was based on the cost of a subsistence or minimum basic diet (rice or flour, dal, mutton, vegetables, ghee, vegetable oil, and salt).</p>",
                    solution_hi: "<p>49.(a) <strong>दादाभाई नौरोजी</strong> ने अपनी पुस्तक, \"पावर्टी एंड अनब्रिटिश रूल इन इंडिया\" के माध्यम से गरीबी रेखा (प्रति व्यक्ति प्रति वर्ष ₹16 से ₹35) का सबसे पहला अनुमान लगाया था। उनके द्वारा प्रस्तावित गरीबी रेखा एक न्यूनतम आहार (चावल या आटा, दाल, मटन, सब्जियाँ, घी, वनस्पति तेल, और नमक) की लागत पर आधारित थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. At what temperature do Celsius and Fahrenheit scales have the same value?</p>",
                    question_hi: "<p>50. किस तापमान पर सेल्सियस (Celsius) और फारेनहाइट (Fahrenheit) पैमाने का मान समान होता है?</p>",
                    options_en: [
                        "<p>40&deg;C</p>",
                        "<p>32&deg;C</p>",
                        "<p>-40&deg;C</p>",
                        "<p>-32&deg;C</p>"
                    ],
                    options_hi: [
                        "<p>40&deg;C</p>",
                        "<p>32&deg;C</p>",
                        "<p>-40&deg;C</p>",
                        "<p>-32&deg;C</p>"
                    ],
                    solution_en: "<p>50.(c) <strong>-40 &deg;C.</strong> At this temperature, both temperature scales show the same numerical value, meaning -40&deg;C is equal to -40&deg;F. The formula to convert between Celsius and Fahrenheit is: F = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>C + 32. By setting the temperatures equal (F = C), solving for the temperature, we get -40&deg;C = -40&deg;F.</p>",
                    solution_hi: "<p>50.(c) <strong>-40 &deg;C</strong>. इस तापमान पर, दोनों तापमान पैमाने एक ही संख्यात्मक मान दर्शाते हैं, जिसका अर्थ है -40&deg;C बराबर -40&deg;F है। सेल्सियस और फ़ारेनहाइट के बीच परिवर्तन का सूत्र है: F = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>C + 32. जब तापमानों को बराबर रखते हैं (F = C), तो हमें -40&deg;C = -40&deg;F प्राप्त होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If 56 &divide; 4 + p &times; 3 &ndash; 24 &divide; 12 + 4 = 21 &divide; 3 + 3, then the value of p is:</p>",
                    question_hi: "<p>51. यदि 56 &divide; 4 + p &times; 3 &ndash; 24 &divide; 12 + 4 = 21 &divide; 3 + 3 है, तो p का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>&minus;1</p>",
                        "<p>1</p>",
                        "<p>&minus;2</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>&minus;1</p>",
                        "<p>1</p>",
                        "<p>&minus;2</p>"
                    ],
                    solution_en: "<p>51.(d)<br>56 &divide; 4 + p &times; 3 &ndash; 24 &divide; 12 + 4 = 21 &divide; 3 + 3<br>14 + p &times; 3 &ndash; 2 + 4 = 7 + 3<br>16 + p &times; 3 = 10<br>p &times; 3 = - 6 <math display=\"inline\"><mo>&#8658;</mo></math> p = - 2</p>",
                    solution_hi: "<p>51.(d)<br>56 &divide; 4 + p &times; 3 &ndash; 24 &divide; 12 + 4 = 21 &divide; 3 + 3<br>14 + p &times; 3 &ndash; 2 + 4 = 7 + 3<br>16 + p &times; 3 = 10<br>p &times; 3 = - 6 <math display=\"inline\"><mo>&#8658;</mo></math> p = - 2</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. If A is an acute angle, which of the following is equal to <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> ?</p>",
                    question_hi: "<p>52. यदि A एक न्यून कोण है, तो निम्न में से कौन सा विकल्प <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math>&nbsp;के बराबर है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>52.(a) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mo>&#160;</mo><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mo>&#160;</mo><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow><mrow><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>",
                    solution_hi: "<p>52.(a) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mo>&#160;</mo><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mo>&#160;</mo><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>)</mo></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow><mrow><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The volume of a cube is four times the volume of a cuboid. If the sides of the cuboid are 32 cm, 8 cm and 4 cm, then find the ratio of the total surface area of the cube to that of the cuboid.</p>",
                    question_hi: "<p>53. घन का आयतन, एक घनाभ के आयतन का चार गुना है। यदि घनाभ की भुजाएँ 32 cm, 8 cm और 4 cm हैं, तो घन के कुल पृष्ठीय क्षेत्रफल और घनाभ के कुल पृष्ठीय क्षेत्रफल का अनुपात ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>23 : 13</p>",
                        "<p>24 : 13</p>",
                        "<p>21 : 13</p>",
                        "<p>22 : 13</p>"
                    ],
                    options_hi: [
                        "<p>23 : 13</p>",
                        "<p>24 : 13</p>",
                        "<p>21 : 13</p>",
                        "<p>22 : 13</p>"
                    ],
                    solution_en: "<p>53.(b)<br>Volume of cube = 4 &times; volume of cuboid <br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math> = 4 &times; 32 &times; 8 &times; 4 = 4096<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 16<br>Now, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>TSA</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>cube</mi></mrow><mrow><mi>TSA</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>cuboid</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>16</mn></mrow><mrow><mn>2</mn><mo>(</mo><mn>32</mn><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>8</mn><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>32</mn><mo>&#215;</mo><mn>4</mn><mo>)</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>16</mn></mrow><mn>832</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>13</mn></mfrac></math></p>",
                    solution_hi: "<p>53.(b)<br>घन का आयतन = 4 &times; घनाभ का आयतन <br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math> = 4 &times; 32 &times; 8 &times; 4 = 4096<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 16<br>अब, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2328;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2328;&#2344;&#2366;&#2349;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>16</mn></mrow><mrow><mn>2</mn><mo>(</mo><mn>32</mn><mo>&#215;</mo><mn>8</mn><mo>+</mo><mn>8</mn><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>32</mn><mo>&#215;</mo><mn>4</mn><mo>)</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>16</mn></mrow><mn>832</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>13</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. ∆ABC and ∆PQR are similar. The areas of these two triangles are 289 sq .cm and 576&nbsp;sq .cm, respectively. If PR = 12 cm, then AC = ?</p>",
                    question_hi: "<p>54. ∆ABC और ∆PQR समरूप हैं। इन दो त्रिभुजों का क्षेत्रफल क्रमशः 289 sq .cm और 576 sq .cm है।&nbsp;यदि PR = 12 cm है, तो AC की लंबाई बताइए।</p>",
                    options_en: [
                        "<p>7.25 cm</p>",
                        "<p>8.5 cm</p>",
                        "<p>12 cm</p>",
                        "<p>6 cm</p>"
                    ],
                    options_hi: [
                        "<p>7.25 cm</p>",
                        "<p>8.5 cm</p>",
                        "<p>12 cm</p>",
                        "<p>6 cm</p>"
                    ],
                    solution_en: "<p style=\"text-align: justify;\">54.(b)<br>∆ABC <math display=\"inline\"><mo>&#8764;</mo></math> ∆PQR (given)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>AC</mi><mn>2</mn></msup><msup><mi>PR</mi><mn>2</mn></msup></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>ar</mi><mo>(</mo><mo>&#9651;</mo><mi>ABC</mi><mo>)</mo></mrow><mrow><mi>ar</mi><mo>(</mo><mo>&#9651;</mo><mi>PQR</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>289</mn><mn>576</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mi>PR</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>24</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mn>12</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>24</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> AC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>2</mn></mfrac></math> = 8.5 cm</p>",
                    solution_hi: "<p>54.(b)<br>∆ABC <math display=\"inline\"><mo>&#8764;</mo></math> ∆PQR (दिया गया है)<br><math display=\"inline\"><mfrac><mrow><mi>A</mi><msup><mrow><mi>C</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>P</mi><msup><mrow><mi>R</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mo>&#9651;</mo><mi>ABC</mi><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mo>(</mo><mo>&#9651;</mo><mi>PQR</mi><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>289</mn><mn>576</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mi>PR</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>24</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mn>12</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>24</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> AC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>2</mn></mfrac></math> = 8.5 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Solve the given set of three equations to get the value of the unknowns:<br>x + 2z = 3<br>x + 2y + 3z = 5<br>3x - 5z = -13</p>",
                    question_hi: "<p>55. अज्ञात मान प्राप्त करने के लिए दिए गए तीन समीकरणों के समूह को हल कीजिए:<br>x + 2z = 3<br>x + 2y + 3z = 5<br>3x - 5z = -13</p>",
                    options_en: [
                        "<p>x = 0,y = -1,z = 2</p>",
                        "<p>x = -1,y = 0,z = 2</p>",
                        "<p>x = -1,y = 2,z = 0</p>",
                        "<p>x = 2,y = 0,z = -1</p>"
                    ],
                    options_hi: [
                        "<p>x = 0,y = -1,z = 2</p>",
                        "<p>x = -1,y = 0,z = 2</p>",
                        "<p>x = -1,y = 2,z = 0</p>",
                        "<p>x = 2,y = 0,z = -1</p>"
                    ],
                    solution_en: "<p>55.(b) <br><strong>Given</strong> :- x + 2z = 3<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; x + 2y + 3z = 5<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3x - 5z = -13<br>By checking all options one by one, only option (b) satisfies,<br>hence, x = -1, y = 0, z = 2</p>",
                    solution_hi: "<p>55.(b) <br><strong>दिया गया है :- </strong>x + 2z = 3<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;x + 2y + 3z = 5<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3x - 5z = -13<br>सभी विकल्पों को एक-एक करके जांचने पर, केवल विकल्प (b) संतुष्ट होता है,<br>इसलिए, x = -1, y = 0, z = 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. 55 workers can finish a work in 16 days. For finishing the same work in 10 days, how many more workers are required?</p>",
                    question_hi: "<p>56. 55 श्रमिक एक काम को 16 दिनों में पूरा कर सकते हैं। उसी काम को 10 दिनों में पूरा करने के लिए कितने और श्रमिकों की आवश्यकता होगी?</p>",
                    options_en: [
                        "<p>88</p>",
                        "<p>68</p>",
                        "<p>33</p>",
                        "<p>31</p>"
                    ],
                    options_hi: [
                        "<p>88</p>",
                        "<p>68</p>",
                        "<p>33</p>",
                        "<p>31</p>"
                    ],
                    solution_en: "<p>56.(c)<br><strong>Formula used:</strong> -&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>1</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>1</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>2</mn></msub></mfrac></math><br>55 &times;&nbsp;16 = x &times; 10<br>x = 88<br>More men required = 88 - 55 = 33</p>",
                    solution_hi: "<p>56.(c)<br><strong>प्रयुक्त सूत्र:-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>1</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>1</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi mathvariant=\"normal\">M</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></mrow><msub><mi mathvariant=\"normal\">W</mi><mn>2</mn></msub></mfrac></math><br>55 &times;&nbsp;16 = x &times; 10<br>x = 88<br>अधिक पुरुषों की आवश्यकता = 88 - 55 = 33</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. A drives for 2 hours, 2.5 hours, 3 hours, 2 hours and 2.5 hours in five days of a week, respectively. His speeds on these days are 45 km/h, 50 km/h, 60 km/h, 70 km/h and 80 km/h, respectively. What is his average speed for the week?</p>",
                    question_hi: "<p>57. A सप्ताह के पांच दिनों में क्रमश: 2 घंटे, 2.5 घंटे, 3 घंटे, 2 घंटे और 2.5 घंटे ड्राइव करता है। इन दिनों में उसकी चाल क्रमशः 45 km/h, 50 km/h, 60 km/h, 70 km/h और 80 km/h है। सप्ताह के लिए उसकी औसत चाल क्या है?</p>",
                    options_en: [
                        "<p>68.02 km/h</p>",
                        "<p>60.8 km/h</p>",
                        "<p>61.25 km/h</p>",
                        "<p>64.08 km/h</p>"
                    ],
                    options_hi: [
                        "<p>68.02 km/h</p>",
                        "<p>60.8 km/h</p>",
                        "<p>61.25 km/h</p>",
                        "<p>64.08 km/h</p>"
                    ],
                    solution_en: "<p>57.(c)<br>Average Speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>distance</mi></mrow><mrow><mi>Total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>Time</mi></mrow></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> Total Distance = 45 &times; 2 + 50 &times; 2.5 + 60 &times; 3 + 70 &times; 2 + 80 &times; 2.5<br><math display=\"inline\"><mo>&#8658;</mo></math> Total Distance = 90 + 125 + 180 + 140 + 200 = 735<br>And total time = 2 + 2.5 + 3 + 2 + 2.5 = 12<br>Hence,<br>Average speed = <math display=\"inline\"><mfrac><mrow><mn>735</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 61.25 km/hr</p>",
                    solution_hi: "<p>57.(c)<br>औसत चाल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> कुल दूरी = 45 &times; 2 + 50 &times; 2.5 + 60 &times; 3 + 70 &times; 2 + 80 &times; 2.5<br><math display=\"inline\"><mo>&#8658;</mo></math> कुल दूरी = 90 + 125 + 180 + 140 + 200 = 735<br>और कुल समय = 2 + 2.5 + 3 + 2 + 2.5 = 12<br>अत:,<br>औसत चाल = <math display=\"inline\"><mfrac><mrow><mn>735</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 61.25 km/hr</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. If 7 tan &theta; = 3, then <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sin&#952;</mi><mo>+</mo><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi></mrow><mrow><mn>7</mn><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>58. यदि 7 tan &theta; = 3 है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sin&#952;</mi><mo>+</mo><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi></mrow><mrow><mn>7</mn><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>7</p>",
                        "<p>5</p>",
                        "<p>8</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>7</p>",
                        "<p>5</p>",
                        "<p>8</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>58.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sin&#952;</mi><mo>+</mo><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi></mrow><mrow><mn>7</mn><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>tan&#952;</mi><mo>+</mo><mn>5</mn><mo>&#160;</mo></mrow><mrow><mn>7</mn><mi>tan&#952;</mi><mo>-</mo><mn>2</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>7</mn></mfrac></mstyle><mo>+</mo><mn>5</mn></mrow><mrow><mn>7</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>7</mn></mfrac></mstyle><mo>-</mo><mn>2</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>5</mn></mrow><mrow><mn>3</mn><mo>-</mo><mn>2</mn></mrow></mfrac></math> = 8</p>",
                    solution_hi: "<p>58.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sin&#952;</mi><mo>+</mo><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi></mrow><mrow><mn>7</mn><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>tan&#952;</mi><mo>+</mo><mn>5</mn><mo>&#160;</mo></mrow><mrow><mn>7</mn><mi>tan&#952;</mi><mo>-</mo><mn>2</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>7</mn></mfrac></mstyle><mo>+</mo><mn>5</mn></mrow><mrow><mn>7</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>7</mn></mfrac></mstyle><mo>-</mo><mn>2</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>5</mn></mrow><mrow><mn>3</mn><mo>-</mo><mn>2</mn></mrow></mfrac></math> = 8</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. In an assembly election, a candidate got 60% of the total valid votes. 3% of the total votes were declared invalid. If the total number of voters is 2,40,000, then find the number of valid votes polled in favour of the candidate.</p>",
                    question_hi: "<p>59. एक विधानसभा चुनाव में एक उम्मीदवार को कुल वैध मतों का 60% प्राप्त हुआ। कुल मतों का 3% अवैध घोषित किया गया। यदि मतदाताओं की कुल संख्या 2,40,000 है, तो उम्मीदवार के पक्ष में डाले गए वैध मतों की संख्या ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>193680</p>",
                        "<p>139860</p>",
                        "<p>139680</p>",
                        "<p>139608</p>"
                    ],
                    options_hi: [
                        "<p>193680</p>",
                        "<p>139860</p>",
                        "<p>139680</p>",
                        "<p>139608</p>"
                    ],
                    solution_en: "<p>59.(c)<br>Required votes = 240000 &times;<math display=\"inline\"><mfrac><mrow><mn>97</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 139680</p>",
                    solution_hi: "<p>59.(c)<br>आवश्यक वोट = 240000 &times;<math display=\"inline\"><mfrac><mrow><mn>97</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 139680</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. If a sum of ₹3,260 on simple interest amounts to ₹5,420 in 6 years, then what will this&nbsp;sum amount to in 4 years at the same rate of interest?</p>",
                    question_hi: "<p>60. यदि साधारण ब्याज पर ₹3,260 की धनराशि 6 वर्ष में ₹5,420 हो जाती है, तो यह धनराशि समान ब्याज दर पर 4 वर्ष में कितनी होगी?</p>",
                    options_en: [
                        "<p>₹4,500</p>",
                        "<p>₹3,700</p>",
                        "<p>₹4,700</p>",
                        "<p>₹3,900</p>"
                    ],
                    options_hi: [
                        "<p>₹4,500</p>",
                        "<p>₹3,700</p>",
                        "<p>₹4,700</p>",
                        "<p>₹3,900</p>"
                    ],
                    solution_en: "<p>60.(c)<br>SI in 6 years = 5420 - 3260 = ₹2160<br>SI in 4 years = <math display=\"inline\"><mfrac><mrow><mn>2160</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 4 = ₹1440<br>Sum amount to in 4 years = 3260 + 1440 = ₹4700</p>",
                    solution_hi: "<p>60.(c)<br>6 वर्षों में साधारण ब्याज = 5420 - 3260 = ₹2160<br>4 वर्ष में साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>2160</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 4 = ₹1440<br>4 वर्ष में कुल राशि = 3260 + 1440 = ₹4700</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Ramesh sells an article for ₹34,440, after allowing an 18% discount on the marked price and still manages to gain 40% profit. If Ramesh sold the article at a 12% discount on the marked price, then what will be his profit, correct to two places of decimals?</p>",
                    question_hi: "<p>61. अंकित मूल्य पर 18% की छूट देने के बाद रमेश एक वस्तु को ₹34,440 में बेचता है और फिर भी 40% लाभ कमाता है। यदि रमेश अंकित मूल्य पर 12% की छूट के साथ वस्तु बेचता है, तो दशमलव के दो स्थानों तक उसका लाभ कितना होगा?</p>",
                    options_en: [
                        "<p>50.42%</p>",
                        "<p>48.24%</p>",
                        "<p>50.24%</p>",
                        "<p>48.42%</p>"
                    ],
                    options_hi: [
                        "<p>50.42%</p>",
                        "<p>48.24%</p>",
                        "<p>50.24%</p>",
                        "<p>48.42%</p>"
                    ],
                    solution_en: "<p>61.(c)<br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>MP</mi><mi>CP</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>82</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>41</mn></mfrac></math><br>New SP = 70 &times; <math display=\"inline\"><mfrac><mrow><mn>88</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 61.6 units<br>profit% = <math display=\"inline\"><mfrac><mrow><mn>61</mn><mo>.</mo><mn>6</mn><mo>-</mo><mn>41</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math> &times; 100 = 50.24%</p>",
                    solution_hi: "<p>61.(c)<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>82</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>41</mn></mfrac></math><br>नया विक्रय मूल्य = 70 &times; <math display=\"inline\"><mfrac><mrow><mn>88</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 61.6 इकाई<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>61</mn><mo>.</mo><mn>6</mn><mo>-</mo><mn>41</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math> &times; 100 = 50.24%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The given table shows the number of pages printed by four printers in three days.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503150956.png\" alt=\"rId55\" width=\"371\" height=\"127\"> <br>If the total number of pages printed by all the given printers together on Wednesday was 15% more than the total number of pages printed by all the given printers together on Saturday, what was the total number of pages printed by all the given printers together on Wednesday?</p>",
                    question_hi: "<p>62. दी गई तालिका तीन दिन में चार प्रिंटरों द्वारा मुद्रित पृष्ठों की संख्या को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503151059.png\" alt=\"rId56\" width=\"349\" height=\"146\"> <br>यदि बुधवार को सभी प्रिंटरों द्वारा एक साथ मुद्रित पृष्ठों की कुल संख्या शनिवार को सभी प्रिंटरों द्वारा मुद्रित पृष्ठों की कुल संख्या से 15% अधिक थी, तो बुधवार को सभी प्रिंटरों द्वारा मिलकर मुद्रित पृष्ठों की कुल संख्या क्या थी?</p>",
                    options_en: [
                        "<p>38,450</p>",
                        "<p>42,577</p>",
                        "<p>40,263</p>",
                        "<p>39,376</p>"
                    ],
                    options_hi: [
                        "<p>38,450</p>",
                        "<p>42,577</p>",
                        "<p>40,263</p>",
                        "<p>39,376</p>"
                    ],
                    solution_en: "<p>62.(d)<br>Total pages printed by printers together on Saturday = (8540 + 11230 + 6580 + 7890) = 34240<br>Hence, required pages printed on Wednesday = 34240 &times; <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 39,376</p>",
                    solution_hi: "<p>62.(d)<br>शनिवार को प्रिंटर द्वारा मुद्रित कुल पृष्ठ = (8540 + 11230 + 6580 + 7890) = 34240<br>अतः, बुधवार को मुद्रित आवश्यक पृष्ठ = 34240 &times; <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 39,376</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. The average of the squares of the first 47 natural numbers is</p>",
                    question_hi: "<p>63. प्रथम 47 प्राकृतिक संख्याओं के वर्गों का औसत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>760</p>",
                        "<p>761</p>",
                        "<p>759</p>",
                        "<p>762</p>"
                    ],
                    options_hi: [
                        "<p>760</p>",
                        "<p>761</p>",
                        "<p>759</p>",
                        "<p>762</p>"
                    ],
                    solution_en: "<p>63.(a) Sum of square of first 47 natural number = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>6</mn></mfrac></math>(n + 1)(2n + 1)<br>= <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>(47 + 1)(2 &times; 47 + 1)<br>= <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>(48)(95)<br>= 47 &times; 8 &times; 95 = 35720<br>Average of square of first 47 natural numbers = <math display=\"inline\"><mfrac><mrow><mn>35720</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> = 760</p>",
                    solution_hi: "<p>63.(a) प्रथम 47 प्राकृतिक संख्या के वर्ग का योग = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>6</mn></mfrac></math>(n + 1)(2n + 1)<br>= <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>(47 + 1)(2 &times; 47 + 1)<br>= <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>(48)(95)<br>= 47 &times; 8 &times; 95 = 35720<br>प्रथम 47 प्राकृत संख्याओं के वर्ग का औसत = <math display=\"inline\"><mfrac><mrow><mn>35720</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> = 760</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. In a circular race of 1100 m, P and Q start from the same point, at the same time and at speeds of 36 km/h and 63 km/h, respectively. When will they meet again for the first time on the track if they are running in the opposite direction ?</p>",
                    question_hi: "<p>64. 1100 m की एक वृत्ताकार दौड़ में, P और Q एक ही बिंदु से, एक ही समय पर और क्रमशः 36 km/h और 63 km/h की चाल से दौड़ना शुरू करते हैं। यदि वे विपरीत दिशा में दौड़ रहे हैं तो वे ट्रैक पर पहली बार कितने समय बाद मिलेंगे ?</p>",
                    options_en: [
                        "<p>40 s</p>",
                        "<p>10 s</p>",
                        "<p>20 s</p>",
                        "<p>27 s</p>"
                    ],
                    options_hi: [
                        "<p>40 सेकंड</p>",
                        "<p>10 सेकंड</p>",
                        "<p>20&nbsp;सेकंड</p>",
                        "<p>27 सेकंड</p>"
                    ],
                    solution_en: "<p>64.(a) Time taken to meet again for the first time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1100</mn><mrow><mo>(</mo><mn>36</mn><mo>+</mo><mn>63</mn><mo>)</mo><mo>&#215;</mo><mfrac><mn>5</mn><mn>18</mn></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1100</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>11</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></math> = 40s</p>",
                    solution_hi: "<p>64.(a) पहली बार दोबारा मिलने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1100</mn><mrow><mo>(</mo><mn>36</mn><mo>+</mo><mn>63</mn><mo>)</mo><mo>&#215;</mo><mfrac><mn>5</mn><mn>18</mn></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1100</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>11</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></math> = 40 सेकंड</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Two circles of radii 3 units and r units, respectively, have a 6 units distance between their centers. If the length of the direct common tangent is <math display=\"inline\"><msqrt><mn>35</mn></msqrt></math> units, then the value of r is _____units.</p>",
                    question_hi: "<p>65. क्रमशः 3 इकाई और r इकाई त्रिज्या वाले दो वृत्तों के केंद्रों के बीच की दूरी 6 इकाई है। यदि सीधी उभयनिष्ठ स्पर्शरेखा की लंबाई<math display=\"inline\"><msqrt><mn>35</mn></msqrt></math> इकाई है, तो r का मान&nbsp; ________ इकाई है।</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p>65.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503151164.png\" alt=\"rId57\" width=\"301\" height=\"152\"><br>Length of common tangent <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">l</mi><mo>)</mo></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br><math display=\"inline\"><msqrt><mn>35</mn></msqrt></math> = &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>6</mn><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>-</mo><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>On squaring both side<br>35 = 36 - [(3)<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mtext>r</mtext><mn>2</mn></msub></math><sup>2</sup> - 2 &times; 3 &times; r<sub>2</sub>]<br>1 = 9 + <math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math><sup>2</sup> - 6r<sub>2</sub><br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math><sup>2</sup> - 6r<sub>2</sub> + 8 = 0<br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math><sup>2</sup> - 4r<sub>2 </sub>- 2r<sub>2</sub> + 8 = 0<br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> (r<sub>2</sub> - 4) - 2(r<sub>2</sub> - 4) = 0<br>(<math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> - 4) (r<sub>2 </sub>- 2) = 0<br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> = 2 or 4<br>Hence option (b) satisfies the value.</p>",
                    solution_hi: "<p>65.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503151164.png\" alt=\"rId57\" width=\"301\" height=\"152\"><br>उभयनिष्ठ स्पर्शरेखा की लंबाई (l) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>-</mo><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br><math display=\"inline\"><msqrt><mn>35</mn></msqrt></math> = &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>6</mn><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>-</mo><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>दोनों तरफ वर्ग करने पर<br>35 = 36 - [(3)<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mtext>r</mtext><mn>2</mn></msub></math><sup>2</sup> - 2 &times; 3 &times; r<sub>2</sub>]<br>1 = 9 + <math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math><sup>2</sup> - 6r<sub>2</sub><br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math><sup>2</sup> - 6r<sub>2</sub> + 8 = 0<br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math><sup>2</sup> - 4r<sub>2 </sub>- 2r<sub>2</sub> + 8 = 0<br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> (r<sub>2</sub> - 4) - 2(r<sub>2</sub> - 4) = 0<br>(<math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> - 4) (r<sub>2 </sub>- 2) = 0<br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> = 2 or 4<br>इसलिए विकल्प (b) मान को संतुष्ट करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. The mean proportion of 0.14 and 2.24 is:</p>",
                    question_hi: "<p>66. 0.14 और 2.24 का माध्यानुपाती ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>0.56</p>",
                        "<p>-2.39</p>",
                        "<p>1.12</p>",
                        "<p>1.02</p>"
                    ],
                    options_hi: [
                        "<p>0.56</p>",
                        "<p>-2.39</p>",
                        "<p>1.12</p>",
                        "<p>1.02</p>"
                    ],
                    solution_en: "<p>66.(a)<br>Mean proportion = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>.</mo><mn>14</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>24</mn></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>14</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>224</mn><mn>100</mn></mfrac></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>14</mn><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>14</mn><mo>&#215;</mo><mn>16</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>100</mn></mfrac></math> = 0.56</p>",
                    solution_hi: "<p>66.(a)<br>माध्य अनुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>.</mo><mn>14</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>24</mn></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>14</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>224</mn><mn>100</mn></mfrac></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>14</mn><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>14</mn><mo>&#215;</mo><mn>16</mn><mo>&#160;</mo></mrow><mn>100</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>100</mn></mfrac></math> = 0.56</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The following table shows the sale (in thousands) of different types of helmets by a shop over the given years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503151261.png\" alt=\"rId58\" width=\"440\" height=\"138\"> <br>What was the percentage increase in the sale of Helmets C in 2002 as compared to that in 1999?</p>",
                    question_hi: "<p>67. निम्नलिखित तालिका दिए गए वर्षों में एक दुकान द्वारा विभिन्न प्रकार के हेलमेट की बिक्री (हजारों में) दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503151363.png\" alt=\"rId59\" width=\"384\" height=\"129\"> <br>1999 की तुलना में 2002 में हेलमेट C की बिक्री में कितने प्रतिशत की वृद्धि हुई?</p>",
                    options_en: [
                        "<p>25.93%</p>",
                        "<p>26.39%</p>",
                        "<p>28.26%</p>",
                        "<p>27.62%</p>"
                    ],
                    options_hi: [
                        "<p>25.93%</p>",
                        "<p>26.39%</p>",
                        "<p>28.26%</p>",
                        "<p>27.62%</p>"
                    ],
                    solution_en: "<p>67.(a)<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>68</mn><mo>-</mo><mn>54</mn></mrow><mrow><mn>54</mn></mrow></mfrac></math> &times; 100 = 25.93%</p>",
                    solution_hi: "<p>67.(a)<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>68</mn><mo>-</mo><mn>54</mn></mrow><mrow><mn>54</mn></mrow></mfrac></math> &times; 100 = 25.93%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. How many numbers between 10 and 65 are divisible by 2, 3 and 4 ?</p>",
                    question_hi: "<p>68. 10 और 65 के बीच कितनी संख्याएं 2, 3 और 4 से विभाज्य हैं ?</p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>7</p>",
                        "<p>9</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>7</p>",
                        "<p>9</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>68.(a)<br>LCM of 2, 3, 4 = 12<br>Numbers between 10 to 65 which is divisible by 2, 3, 4 = 12, 24, 36, 48, 60<br>Hence, the required numbers will be 5.</p>",
                    solution_hi: "<p>68.(a)<br>2, 3, 4 का LCM = 12<br>10 से 65 के बीच की संख्याएँ जो 2, 3, 4 से विभाज्य है = 12, 24, 36, 48, 60<br>अतः, अभीष्ट संख्याएँ 5 होगी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Find the largest number by which dividing 68, 102 and 136 leaves the same remainder in each case.</p>",
                    question_hi: "<p>69. वह सबसे बड़ी संख्या ज्ञात कीजिए, जिससे 68, 102 और 136 को भाग देने पर, प्रत्येक स्थिति में समान शेषफल बचता है।</p>",
                    options_en: [
                        "<p>17</p>",
                        "<p>34</p>",
                        "<p>33</p>",
                        "<p>51</p>"
                    ],
                    options_hi: [
                        "<p>17</p>",
                        "<p>34</p>",
                        "<p>33</p>",
                        "<p>51</p>"
                    ],
                    solution_en: "<p>69.(b)<br>Required largest number =<br>HCF of (68, 102, 136) = 34</p>",
                    solution_hi: "<p>69.(b)<br>आवश्यक सबसे बड़ी संख्या =&nbsp;<br>(68, 102, 136) का HCF = 34</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Rahul spends 70% of his income. His income increased by 15% and his expenditure increased by 7.5%. What is the percentage increase in his savings?</p>",
                    question_hi: "<p>70. राहुल अपनी आय का 70% खर्च करता है। उसकी आय में 15% की वृद्धि हुई और उसके व्यय में 7.5% की वृद्धि हुई। उसकी बचत में प्रतिशत वृद्धि कितनी है?</p>",
                    options_en: [
                        "<p>32.5%</p>",
                        "<p>25.5%</p>",
                        "<p>50%</p>",
                        "<p>30.5%</p>"
                    ],
                    options_hi: [
                        "<p>32.5%</p>",
                        "<p>25.5%</p>",
                        "<p>50%</p>",
                        "<p>30.5%</p>"
                    ],
                    solution_en: "<p>70.(a) <br>Ratio -&nbsp; &nbsp; &nbsp;before : after<br>Income -&nbsp; &nbsp; &nbsp; 100 : 115<br>Expenditure - 70 : 75.25 <br>Saving -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;30 : 39.75<br>required% = <math display=\"inline\"><mfrac><mrow><mn>39</mn><mo>.</mo><mn>75</mn><mo>-</mo><mn>30</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = 32.5%</p>",
                    solution_hi: "<p>70.(a) <br>अनुपात - पहले : बाद में<br>आय -&nbsp; &nbsp; &nbsp; 100 : 115<br>व्यय -&nbsp; &nbsp; &nbsp; &nbsp; 70 : 75.25 <br>बचत -&nbsp; &nbsp; &nbsp; &nbsp;30 : 39.75<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>39</mn><mo>.</mo><mn>75</mn><mo>-</mo><mn>30</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = 32.5%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Rs. 17000 is lent at compound interest at an annual rate of 22 percent for 1 year (compounding annually). If the compounding of interest is done half yearly, then how much more interest will be obtained?</p>",
                    question_hi: "<p>71. 17000 रुपए को 22 प्रतिशत की वार्षिक दर पर 1 वर्ष के लिए चक्रवृद्धि ब्याज पर (वार्षिकरूप से संयोजित) ऋण पर दिए जाते हैं। यदि ब्याज अर्धवार्षिक रूप से संयोजित किया जाए तो कितना अधिक ब्याज प्राप्त होगा?</p>",
                    options_en: [
                        "<p>Rs. 210.8</p>",
                        "<p>Rs. 205.7</p>",
                        "<p>Rs. 204.4</p>",
                        "<p>Rs. 208.8</p>"
                    ],
                    options_hi: [
                        "<p>210.8 रुपए</p>",
                        "<p>205.7 रुपए</p>",
                        "<p>204.4 रुपए</p>",
                        "<p>208.8 रुपए</p>"
                    ],
                    solution_en: "<p>71.(b)<br>Rate for half year = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 11%, Time = 1 yr, No of cycle = 2<br>CI for 1 yr (half-yearly) = (11 + 11 + <math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>&#215;</mo><mn>11</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 22 + 1.21 = 23.21%<br>Required Interest = (23.21% - 22%) = 1.21% of 17,000 = ₹205.7</p>",
                    solution_hi: "<p>71.(b)<br>आधे वर्ष के लिए दर = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 11%, समय = 1 वर्ष, चक्र की संख्या = 2<br>1 वर्ष के लिए चक्रवृद्धि ब्याज (अर्धवार्षिक) = (11 + 11 + <math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>&#215;</mo><mn>11</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 22 + 1.21 = 23.21%<br>आवश्यक ब्याज = (23.21% - 22%) = 1.21% of 17,000 = ₹205.7</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Simplify the following. <br>(<math display=\"inline\"><mi>x</mi></math> - 3y)<sup>&sup3;</sup> + 27(y&sup3; - xy&sup2;)</p>",
                    question_hi: "<p>72. (<math display=\"inline\"><mi>x</mi></math> - 3y)<sup>&sup3;</sup> + 27(y&sup3; - xy&sup2;) को हल कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mi>x</mi></math>&sup3; - 3x&sup2;y</p>",
                        "<p>x<sup>&sup3;</sup> - 27y<sup>&sup3;</sup> - 9x&sup2;y</p>",
                        "<p><math display=\"inline\"><mi>x</mi></math>&sup3; - 27y&sup3; - 9x&sup2;y + 27xy&sup2; + 27y&sup2; - 27y&sup2;</p>",
                        "<p><math display=\"inline\"><mi>x</mi></math>&sup3; - 9x&sup2;y</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mi>x</mi></math>&sup3; - 3x&sup2;y</p>",
                        "<p>x<sup>&sup3;</sup> - 27y<sup>&sup3;</sup> - 9x&sup2;y</p>",
                        "<p><math display=\"inline\"><mi>x</mi></math>&sup3; - 27y&sup3; - 9x&sup2;y + 27xy&sup2; + 27y&sup2; - 27y&sup2;</p>",
                        "<p><math display=\"inline\"><mi>x</mi></math>&sup3; - 9x&sup2;y</p>"
                    ],
                    solution_en: "<p>72.(d)<br>(<math display=\"inline\"><mi>x</mi></math> - 3y)&sup3; + 27(y&sup3; - xy&sup2;)<br><math display=\"inline\"><mo>&#8658;</mo></math> x&sup3; - 27y&sup3; - 9xy (x - 3y) + 27y&sup3; - 27xy&sup2;<br><math display=\"inline\"><mo>&#8658;</mo></math> x&sup3; - 27y&sup3; - 9x&sup2;y + 27xy&sup2; + 27y&sup3; - 27xy&sup2;<br><math display=\"inline\"><mo>&#8658;</mo></math> x&sup3; - 9x&sup2;y</p>",
                    solution_hi: "<p>72.(d)<br>(<math display=\"inline\"><mi>x</mi></math> - 3y)&sup3; + 27(y&sup3; - xy&sup2;)<br><math display=\"inline\"><mo>&#8658;</mo></math> x&sup3; - 27y&sup3; - 9xy (x - 3y) + 27y&sup3; - 27xy&sup2;<br><math display=\"inline\"><mo>&#8658;</mo></math> x&sup3; - 27y&sup3; - 9x&sup2;y + 27xy&sup2; + 27y&sup3; - 27xy&sup2;<br><math display=\"inline\"><mo>&#8658;</mo></math> x&sup3; - 9x&sup2;y</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Pomegranate juice contains 10% sugar solution and orange juice contains 30% sugar solution. What is the percentage of sugar solution in a mixture of two litres of pomegranate juice and three litres of orange juice?</p>",
                    question_hi: "<p>73. अनार के रस में 10% शर्करा विलयन होता है और संतरे के रस में 30% शर्करा विलयन होता है। दो लीटर अनार के रस और तीन लीटर संतरे के रस के मिश्रण में शर्करा विलयन का प्रतिशत कितना है?</p>",
                    options_en: [
                        "<p>22%</p>",
                        "<p>20%</p>",
                        "<p>40%</p>",
                        "<p>25%</p>"
                    ],
                    options_hi: [
                        "<p>22%</p>",
                        "<p>20%</p>",
                        "<p>40%</p>",
                        "<p>25%</p>"
                    ],
                    solution_en: "<p>73.(a)&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Pomegranate&nbsp; &nbsp; Orange<br>Quantity&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;3<br>% of solution&nbsp; &nbsp;10%&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;30%<br>Overall % = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>10</mn><mi>%</mi><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>30</mn><mi>%</mi></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac><mi>&#160;</mi></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>%</mo><mo>+</mo><mn>90</mn><mo>%</mo></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>5</mn></mfrac></math>% = 22%</p>",
                    solution_hi: "<p>73.(a) <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; अनार&nbsp; &nbsp; :&nbsp; &nbsp;संतरा<br>मात्रा -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;3<br>मिश्रण का % - 10%&nbsp; &nbsp; &nbsp; 30%<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>10</mn><mi>%</mi><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>30</mn><mi>%</mi></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac><mi>&#160;</mi></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>%</mo><mo>+</mo><mn>90</mn><mo>%</mo></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>5</mn></mfrac></math>% = 22%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Pipe A takes <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of the time required by pipe B to fill an empty tank individually. When an outlet pipe C is also opened simultaneously with pipes A and B, it takes <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> more time to fill the empty tank than it takes when only pipe A and pipe B are opened together. If it takes 40 hours to fill the tank when all the three pipes are opened simultaneously, in what time (in hours) will pipe C empty the full tank, operating alone?</p>",
                    question_hi: "<p>74. पाइप A एक खाली टैंक को अकेले भरने में, पाइप B द्वारा लिए गए आवश्यक समय का <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> समय लेता है। जब एक निर्गम पाइप C को पाइप A और B के साथ एकसाथ खोला जाता है, तो खाली टैंक को भरने में केवल पाइप A और पाइप B को एक साथ खोलने में लगने वाले समय की तुलना में <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> अधिक समय लगता है। यदि तीनों पाइपों को एकसाथ खोलने पर टैंक को भरने में 40 घंटे लगते हैं, तो अकेले कार्य करते हुए पाइप C कितने समय में (घंटों में) पूरा टैंक खाली कर देगा?</p>",
                    options_en: [
                        "<p>50</p>",
                        "<p>45</p>",
                        "<p>65</p>",
                        "<p>75</p>"
                    ],
                    options_hi: [
                        "<p>50</p>",
                        "<p>45</p>",
                        "<p>65</p>",
                        "<p>75</p>"
                    ],
                    solution_en: "<p>74.(a) <br>Let pipe A fill an empty tank in 4hr , and pipe B fill an empty tank in 5hr<br>Let Total capacity of tank (LCM 4 ,5 ) = 20<br>Efficiency of Pipe A and Pipe B = 5 unit and 4 unit <br>Time taken by both pipe to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math>hr<br>Again , Given C is an outlet pipe , and when A , B and C open together , it will take <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> more time to fill <br>the tank than it takes when only pipe A and pipe B are opened together<br>&there4; Time taken by Pipe A , B and C together to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = 4hr&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503151506.png\" alt=\"rId60\" width=\"215\" height=\"175\"><br>Here efficiency of c = (A + B ) - (A + B +C) = 9 - 5 = 4<br>Now, Total capacity of tank = 5 &times; 40 = 200 <br>Time required by pipe C to empty the full tank = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 50 hr</p>",
                    solution_hi: "<p>74.(a) <br>माना पाइप A एक खाली टैंक को 4 घंटे में भरता है, <br>और पाइप B एक खाली टैंक को 5 घंटे में भरता है।<br>माना टैंक की कुल क्षमता (LCM 4, 5) = 20<br>पाइप A और पाइप B की कार्यक्षमता = 5 इकाई और 4 इकाई <br>दोनों पाइप द्वारा टैंक को भरने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> घंटे।<br>दिया गया है कि C एक निकास पाइप है, और जब A, B और C को एक साथ खोला जाता है, तो टैंक को भरने में पाइप A और पाइप B को एक साथ खोलने की तुलना में<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> अधिक समय लगेगा।<br>&there4; पाइप A, B और C द्वारा टैंक को भरने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>&nbsp;= 4 घंटे।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743503151673.png\" alt=\"rId61\" width=\"200\"><br>अतः C की कार्यक्षमता = (A + B ) - (A + B +C) = 9 - 5 = 4<br>अब, टैंक की कुल क्षमता = 5 &times; 40 = 200<br>पाइप C को पूर्ण टैंक को खाली करने में लगने वाला समय = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>= 50 घंटे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The speed of a boat in still water is 18 km/h and the speed of the stream is 3 km/h. How much time (in hours) will it take to cover a distance of 105 km downstream and in coming back ?</p>",
                    question_hi: "<p>75. स्थिर जल में एक नाव की चाल 18 km/h है और धारा की चाल 3 km/h है। वह धारा के अनुकूल 105 km की दूरी तय करने और वापस आने में कितना समय (घंटों में) लेगी ?</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>15</p>",
                        "<p>9</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>15</p>",
                        "<p>9</p>"
                    ],
                    solution_en: "<p>75.(a)<br>Time taken to cover the distance in Downstream = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>distance</mi><mrow><mi>speed</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>boat</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>speed</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>stream</mi></mrow></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mrow><mn>18</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>15</mn></mfrac></math> = 5 hours<br>Time taken to cover the distance in Upstream = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>distance</mi><mrow><mi>speed</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>boat</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>speed</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>stream</mi></mrow></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mrow><mn>18</mn><mo>-</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>15</mn></mfrac></math> = 7 hours<br>Total time taken = 5 + 7 = 12 hours</p>",
                    solution_hi: "<p>75.(a)<br>धारा के अनुकूल दूरी तय करने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2344;&#2366;&#2357;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2330;&#2366;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2343;&#2366;&#2352;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2330;&#2366;&#2354;</mi></mrow></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mrow><mn>18</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>15</mn></mfrac></math> = 5 घंटे&nbsp;<br>धारा के प्रतिकूल दूरी तय करने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2344;&#2366;&#2357;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2330;&#2366;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2343;&#2366;&#2352;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2330;&#2366;&#2354;</mi></mrow></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mrow><mn>18</mn><mo>-</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>15</mn></mfrac></math> = 7 घंटे<br>कुल लिया गया समय = 5 + 7 = 12 घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has been divided into four segments. Identify the segment that contains an adverbial usage error.<br>Mr. Arun / will pay dear / for his / financial mistake.</p>",
                    question_hi: "<p>76. The following sentence has been divided into four segments. Identify the segment that contains an adverbial usage error.<br>Mr. Arun / will pay dear / for his / financial mistake.</p>",
                    options_en: [
                        "<p>will pay dear</p>",
                        "<p>Mr. Arun</p>",
                        "<p>for his</p>",
                        "<p>financial mistake</p>"
                    ],
                    options_hi: [
                        "<p>will pay dear</p>",
                        "<p>Mr. Arun</p>",
                        "<p>for his</p>",
                        "<p>financial mistake</p>"
                    ],
                    solution_en: "<p>76.(a) will pay dear<br>The given sentence needs an adverb &lsquo;dearly&rsquo; to modify the verb &lsquo;pay&rsquo;, not the adjective &lsquo;dear&rsquo;. Hence, &lsquo;will pay dearly&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(a) will pay dear<br>दिए गए Sentence में verb &lsquo;pay&rsquo; को modify करने के लिए adjective &lsquo;dear&rsquo; की नहीं बल्कि adverb &lsquo;dearly&rsquo; की आवश्यकता है। अतः, &lsquo;will pay dearly&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Identify the segment in the sentence, which contains the error.<br>Thanks to an insight and persistence of the local doctor, hundreds of victims have been able to resume normal life.</p>",
                    question_hi: "<p>77. Identify the segment in the sentence, which contains the error.<br>Thanks to an insight and persistence of the local doctor, hundreds of victims have been able to resume normal life.</p>",
                    options_en: [
                        "<p>no error</p>",
                        "<p>thanks to an insight</p>",
                        "<p>hundreds of victims have been able to resume normal life.</p>",
                        "<p>and persistence of the local doctor,</p>"
                    ],
                    options_hi: [
                        "<p>no error</p>",
                        "<p>thanks to an insight</p>",
                        "<p>hundreds of victims have been able to resume normal life.</p>",
                        "<p>and persistence of the local doctor,</p>"
                    ],
                    solution_en: "<p>77.(b) thanks to an insight. <br>Insight and persistence are abstract nouns and we don&rsquo;t use an article before them but an article is used when they become definite. Here it is the insight and persistence of the local doctor. So article &ldquo;the&rdquo; will be used.</p>",
                    solution_hi: "<p>77.(b) thanks to an insight<br>&lsquo;Insight /अंतर्दृष्टि&rsquo; और &lsquo;persistence/ दृढ़ता&rsquo; abstract nouns हैं और हम उनके पहले article &lsquo;an&rsquo; का उपयोग नहीं करते हैं लेकिन article &lsquo;an&rsquo; का उपयोग तब किया जाता है जब वे निश्चित हो जाते हैं। यहाँ यह स्थानीय चिकित्सक की अंतर्दृष्टि और दृढ़ता है। इसलिए article &ldquo;the&rdquo; का इस्तेमाल किया जाएगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate synonym of the given word.<br>Concordant</p>",
                    question_hi: "<p>78. Select the most appropriate synonym of the given word.<br>Concordant</p>",
                    options_en: [
                        "<p>Unanimous</p>",
                        "<p>Abnormal</p>",
                        "<p>Certain</p>",
                        "<p>Harmonious</p>"
                    ],
                    options_hi: [
                        "<p>Unanimous</p>",
                        "<p>Abnormal</p>",
                        "<p>Certain</p>",
                        "<p>Harmonious</p>"
                    ],
                    solution_en: "<p>78.(d) <strong>Harmonious-</strong> in agreement or concord.<br><strong>Concordant</strong>- being in agreement or harmony.<br><strong>Unanimous-</strong> fully in agreement.<br><strong>Abnormal-</strong> deviating from what is normal or usual.<br><strong>Certain-</strong> known for sure.</p>",
                    solution_hi: "<p>78.(d) <strong>Harmonious</strong> (सामंजस्यपूर्ण) - in agreement or concord.<br><strong>Concordant</strong> (सहमत) - being in agreement or harmony.<br><strong>Unanimous</strong> (सर्वसम्मत) - fully in agreement.<br><strong>Abnormal</strong> (असामान्य) - deviating from what is normal or usual.<br><strong>Certain</strong> (निश्चित) - known for sure.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>Brutus said, &ldquo;Friends, Romans and countrymen, lend me your ears\".</p>",
                    question_hi: "<p>79. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>Brutus said, &ldquo;Friends, Romans and countrymen, lend me your ears\".</p>",
                    options_en: [
                        "<p>Brutus addressed friends, Romans and countrymen and appealed to them to lend him ears.</p>",
                        "<p>Brutus addressed the citizens as friends, Romans and countrymen and appealed to them to lend him their ears.</p>",
                        "<p>Brutus appealed to the citizens to lend him their ears and addressed the citizens.</p>",
                        "<p>Addressing the citizens as friends, Romans and countrymen Brutus appealed to him to lend them their ears.</p>"
                    ],
                    options_hi: [
                        "<p>Brutus addressed friends, Romans and countrymen and appealed to them to lend him ears.</p>",
                        "<p>Brutus addressed the citizens as friends, Romans and countrymen and appealed to them to lend him their ears.</p>",
                        "<p>Brutus appealed to the citizens to lend him their ears and addressed the citizens.</p>",
                        "<p>Addressing the citizens as friends, Romans and countrymen Brutus appealed to him to lend them their ears.</p>"
                    ],
                    solution_en: "<p>79.(b) Brutus addressed the citizens as friends, Romans and countrymen and appealed to them to lend him their ears.<br>(a) Brutus <strong>addressed friends</strong>, Romans and countrymen and appealed to them to lend him ears. (as is missing)<br>(c) Brutus <strong>appealed</strong> to the citizens to lend him their ears and <strong>addressed the citizens.</strong> (Incorrect Reporting Verb)<br>(d) Addressing the citizens as friends, Romans and countrymen Brutus <strong>appealed to</strong> <strong>him</strong> to lend them their ears. (Incorrect change of pronoun)</p>",
                    solution_hi: "<p>79.(b) Brutus addressed the citizens as friends, Romans and countrymen and appealed to them to lend him their ears.<br>(a) Brutus <strong>addressed friends,</strong> Romans and countrymen and appealed to them to lend him ears. (as का प्रयोग नहीं किया गया है)<br>(c) Brutus appealed to the citizens to lend him their ears and <strong>addressed the citizens.</strong> (ग़लत Reporting Verb)<br>(d) Addressing the citizens as friends, Romans and countrymen Brutus <strong>appealed to him</strong> to lend them their ears. (Pronoun का गलत परिवर्तन)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that expresses the given sentence in passive voice. <br>Ishika saw the tiger in the forest.</p>",
                    question_hi: "<p>80. Select the option that expresses the given sentence in passive voice. <br>Ishika saw the tiger in the forest.</p>",
                    options_en: [
                        "<p>The tiger saw by Ishika in the forest</p>",
                        "<p>The tiger was seen by the forest in Ishika.</p>",
                        "<p>The tiger was seen by Ishika in the forest.</p>",
                        "<p>The tiger sees Ishika in the forest.</p>"
                    ],
                    options_hi: [
                        "<p>The tiger saw by Ishika in the forest</p>",
                        "<p>The tiger was seen by the forest in Ishika.</p>",
                        "<p>The tiger was seen by Ishika in the forest.</p>",
                        "<p>The tiger sees Ishika in the forest.</p>"
                    ],
                    solution_en: "<p>80.(c) The tiger was seen by Ishika in the forest.(Corect)<br>(a) The tiger <span style=\"text-decoration: underline;\">saw</span> by Ishika in the forest.(Incorrect Verb)<br>(b) The tiger was seen by the forest in Ishika.(Incorrect Sentence Structure)<br>(d) The tiger <span style=\"text-decoration: underline;\">sees</span> Ishika in the forest.(Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>80.(c) The tiger was seen by Ishika in the forest.(Corect)<br>(a) The tiger <span style=\"text-decoration: underline;\">saw</span> by Ishika in the forest.(गलत Verb)<br>(b) The tiger was seen by the forest in Ishika.(गलत Sentence Structure)<br>(d) The tiger <span style=\"text-decoration: underline;\">sees</span> Ishika in the forest.(गलत Sentence Structure)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate meaning of the idiom ( in the context). <br><span style=\"text-decoration: underline;\"><strong>A worldly man</strong></span> means</p>",
                    question_hi: "<p>81. Select the most appropriate meaning of the idiom ( in the context). <br><span style=\"text-decoration: underline;\"><strong>A worldly man</strong></span> means</p>",
                    options_en: [
                        "<p>An person who has been too long in this world</p>",
                        "<p>A man who is aware about the affairs of the world</p>",
                        "<p>A wise sensible man who has experience of handling situations.</p>",
                        "<p>A widely travelled person.</p>"
                    ],
                    options_hi: [
                        "<p>An person who has been too long in this world</p>",
                        "<p>A man who is aware about the affairs of the world</p>",
                        "<p>A wise sensible man who has experience of handling situations.</p>",
                        "<p>A widely travelled person.</p>"
                    ],
                    solution_en: "<p>81.(c) A wise sensible man who has experience of handling situations. <br>Example- A worldly man knows how to handle situations and personal relations.</p>",
                    solution_hi: "<p>81.(c) A wise sensible man who has experience of handling situations./एक बुद्धिमान&nbsp;व्यक्ति जिसे परिस्थितियों को संभालने का अनुभव है। <br>उदाहरण - A worldly man knows how to handle situations and personal relations./एक बुद्धिमान व्यक्ति जो परिस्थितियों और व्यक्तिगत संबंधों को संभालना जानता हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Don\'t subject the animals to cruelty.</p>",
                    question_hi: "<p>82. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Don\'t subject the animals to cruelty.</p>",
                    options_en: [
                        "<p>The animals are not be subjected to cruelty.</p>",
                        "<p>The animals shall not be subjected to cruelty.</p>",
                        "<p>The animals will not be subjected to cruelty.</p>",
                        "<p>The animals should not be subjected to cruelty.</p>"
                    ],
                    options_hi: [
                        "<p>The animals are not be subjected to cruelty.</p>",
                        "<p>The animals shall not be subjected to cruelty.</p>",
                        "<p>The animals will not be subjected to cruelty.</p>",
                        "<p>The animals should not be subjected to cruelty.</p>"
                    ],
                    solution_en: "<p>82.(d) The animals should not be subjected to cruelty.<br>a. The animals <strong>are not to</strong> be subjected to cruelly. (Incorrect word)<br>b. The animals <strong>shall not</strong> be subjected to cruelty. (Incorrect word)<br>c. The animals <strong>will not</strong> be subjected to cruelty.( Incorrect word)</p>",
                    solution_hi: "<p>82.(d) The animals should not be subjected to cruelty. <br>a. The animals <strong>are not to</strong> be subjected to cruelly. ( word गलत है )<br>b. The animals <strong>shall not</strong> be subjected to cruelty. (word गलत है)<br>c. The animals <strong>will not</strong> be subjected to cruelty. (word गलत है)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Find a word that is the synonym of<br>Delusion</p>",
                    question_hi: "<p>83. Find a word that is the synonym of<br>Delusion</p>",
                    options_en: [
                        "<p>Illumination</p>",
                        "<p>Illusion</p>",
                        "<p>Ascension</p>",
                        "<p>Reality</p>"
                    ],
                    options_hi: [
                        "<p>Illumination</p>",
                        "<p>Illusion</p>",
                        "<p>Ascension</p>",
                        "<p>Reality</p>"
                    ],
                    solution_en: "<p>83.(b) Illusion<br>The word <strong>Delusion (Noun)</strong> means : false belief or opinion; <strong>illusion.</strong> <br><strong>Look at the sentence:</strong><br>Don&rsquo;t go getting delusions of grandeur.</p>",
                    solution_hi: "<p>83.(b) Illusion<br><strong>Delusion</strong> शब्द <strong>(Noun)</strong> का अर्थ है: गलत विश्वास (भ्रम) या राय; मोह माया।<br><strong>वाक्य देखिए:</strong><br>I was under the delusion that he intended to marry me./ मैं , इस भ्रम में थी कि वह मुझसे शादी करना चाहता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Grounds can be oval like the Adelaide Oval or nearly circular, like Chepauk in Chennai. <br>(B) But the size or shape of the ground is not.<br>(C) In cricket, the length of the pitch is specified&mdash;22 yards.<br>(D) A six at the Melbourne Cricket Ground needs to clear much more ground than it does at Feroz Shah Kotla in Delhi.</p>",
                    question_hi: "<p>84. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Grounds can be oval like the Adelaide Oval or nearly circular, like Chepauk in Chennai. <br>(B) But the size or shape of the ground is not.<br>(C) In cricket, the length of the pitch is specified&mdash;22 yards.<br>(D) A six at the Melbourne Cricket Ground needs to clear much more ground than it does at Feroz Shah Kotla in Delhi.</p>",
                    options_en: [
                        "<p>CADB</p>",
                        "<p>DACB</p>",
                        "<p>CBAD</p>",
                        "<p>DCBA</p>"
                    ],
                    options_hi: [
                        "<p>CADB</p>",
                        "<p>DACB</p>",
                        "<p>CBAD</p>",
                        "<p>DCBA</p>"
                    ],
                    solution_en: "<p>84.(c) CBAD<br>Sentence C will be the starting line as it contains the main idea of the parajumble i.e. the length of the pitch in cricket is specified. Sentence B states that but the size and shape of the ground is not specified. So, B will follow C . Further, Sentence A mentions the different shapes of the ground and Sentence D states that a six at the Melbourne cricket ground needs to clear much more ground than it does in the stadium of Delhi . So, D will follow A . Going through the options, option ( c) CBAD has the correct sequence.</p>",
                    solution_hi: "<p>84.(c) CBAD<br>Sentence C starting line होगी क्योंकि इसमें parajumble का मुख्य विचार शामिल है- &lsquo;the length of the pitch in cricket is specified&rsquo; । Sentence B बताता है कि लेकिन जमीन का आकार और आकृति निर्दिष्ट (specified) नहीं है। अतः C के बाद B आएगा। इसके अलावा, Sentence A में मैदान के विभिन्न आकारों का उल्लेख है और Sentence D में कहा गया है कि मेलबर्न क्रिकेट मैदान पर एक छक्के को दिल्ली के स्टेडियम की तुलना में बहुत अधिक मैदान को clear करने की आवश्यकता है। तो, A के बाद D आएगा। Options के माध्यम से जाने पर, option ( c) CBAD में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>The principals of government schools <span style=\"text-decoration: underline;\"><strong>are being train</strong></span> in leadership skills at the IIM, Ahmedabad from 15th to 24th July.</p>",
                    question_hi: "<p>85. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>The principals of government schools <span style=\"text-decoration: underline;\"><strong>are being train</strong></span> in leadership skills at the IIM, Ahmedabad from 15th to 24th July.</p>",
                    options_en: [
                        "<p>is being train</p>",
                        "<p>are being trained</p>",
                        "<p>was been trained</p>",
                        "<p>No substitution required</p>"
                    ],
                    options_hi: [
                        "<p>is being train</p>",
                        "<p>are being trained</p>",
                        "<p>was been trained</p>",
                        "<p>No substitution required</p>"
                    ],
                    solution_en: "<p>85.(b) are being trained<br>&ldquo;Being + V<sub>3</sub>&rdquo; is the correct grammatical structure for the given sentence. However, the V<sub>3</sub> form of &lsquo;train&rsquo; is &lsquo;trained&rsquo;. Hence, &lsquo;are being trained&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>85.(b) &ldquo;Being + V<sub>3</sub>&rdquo; दिए गए वाक्य के लिए सही grammatical structure है। हालाँकि, \'train\' का V<sub>3</sub> रूप \'trained\' है। इसलिए, \'are being trained\' सबसे उपयुक्त है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Find the correctly spelt word:</p>",
                    question_hi: "<p>86. Find the correctly spelt word:</p>",
                    options_en: [
                        "<p>consumerism</p>",
                        "<p>communlism</p>",
                        "<p>passimism</p>",
                        "<p>optemism</p>"
                    ],
                    options_hi: [
                        "<p>consumerism</p>",
                        "<p>communlism</p>",
                        "<p>passimism</p>",
                        "<p>optemism</p>"
                    ],
                    solution_en: "<p>86.(a) consumerism<br>Other words- communalism, pessimism, optimism.</p>",
                    solution_hi: "<p>86.(a) consumerism<br>दूसरे शब्दों की सही spelling - communalism, pessimism, optimism.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of the idiom ( in the context). <br>He <span style=\"text-decoration: underline;\"><strong>chickened out</strong></span> when he confronted opposition.</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of the idiom ( in the context). <br>He <span style=\"text-decoration: underline;\"><strong>chickened out</strong></span> when he confronted opposition.</p>",
                    options_en: [
                        "<p>ate chicken</p>",
                        "<p>released chicken</p>",
                        "<p>hatched eggs</p>",
                        "<p>withdrew</p>"
                    ],
                    options_hi: [
                        "<p>ate chicken</p>",
                        "<p>released chicken</p>",
                        "<p>hatched eggs</p>",
                        "<p>withdrew</p>"
                    ],
                    solution_en: "<p>87.(d) withdrew , Example - We were going to go paragliding, but Shelly chickened out at the last minute.</p>",
                    solution_hi: "<p>87.(d) withdrew/वापस लेना <br>उदाहरण - We were going to go paragliding, but Shelly chickened out at the last minute./ हम पैराग्लाइडिंग के लिए जा रहे थे, लेकिन आखिरी समय में शेली बाहर हो गई। </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>They each listened carefully to what <span style=\"text-decoration: underline;\"><strong>each other</strong></span> said.</p>",
                    question_hi: "<p>88. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>They each listened carefully to what <span style=\"text-decoration: underline;\"><strong>each other</strong></span> said.</p>",
                    options_en: [
                        "<p>each another</p>",
                        "<p>No improvement required</p>",
                        "<p>the other</p>",
                        "<p>one another</p>"
                    ],
                    options_hi: [
                        "<p>each another</p>",
                        "<p>No improvement required</p>",
                        "<p>the other</p>",
                        "<p>one another</p>"
                    ],
                    solution_en: "<p>88.(c) the other <br>We will remove &lsquo;each&rsquo; to grammatically correct the given sentence. Hence, &lsquo;the other&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>88.(c) the other <br>दिए गए वाक्य को grammatically सही करने के लिए हम \'each\' को हटा देंगे। अतः \'the other\' सबसे उपयुक्त है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate option to fill in the blank. <br>We are bewildered by the power which science has suddenly placed in our laps, and we are ______ by the realisation of our unpreparedness to deal with a crisis.</p>",
                    question_hi: "<p>89. Select the most appropriate option to fill in the blank. <br>We are bewildered by the power which science has suddenly placed in our laps, and we are ______ by the realisation of our unpreparedness to deal with a crisis.</p>",
                    options_en: [
                        "<p>condemned</p>",
                        "<p>humbled</p>",
                        "<p>proud</p>",
                        "<p>demeaned</p>"
                    ],
                    options_hi: [
                        "<p>condemned</p>",
                        "<p>humbled</p>",
                        "<p>proud</p>",
                        "<p>demeaned</p>"
                    ],
                    solution_en: "<p>89.(b) Humbled.<br>Humble means to not proud or not believing that I am important. The given sentence states that we are humbled by the realisation of our unpreparedness to deal with a crisis. Hence, &lsquo;humbled&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(b) Humbled.<br>Humble का अर्थ है- विनम्र । &lsquo;Humbled&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) They burrow underground during the hot day and come out at night to eat. <br>(B) Some of them eat other animals and get the water they need from the moisture in the meat. <br>(C ) The smaller desert animals do not drink water. <br>(D) Others eat plants and seeds and get the water they need from plant juices.</p>",
                    question_hi: "<p>90. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) They burrow underground during the hot day and come out at night to eat. <br>(B) Some of them eat other animals and get the water they need from the moisture in the meat. <br>(C ) The smaller desert animals do not drink water. <br>(D) Others eat plants and seeds and get the water they need from plant juices.</p>",
                    options_en: [
                        "<p>DCBA</p>",
                        "<p>ACDB</p>",
                        "<p>CDBA</p>",
                        "<p>CABD</p>"
                    ],
                    options_hi: [
                        "<p>DCBA</p>",
                        "<p>ACDB</p>",
                        "<p>CDBA</p>",
                        "<p>CABD</p>"
                    ],
                    solution_en: "<p>90.(d) CABD<br>Sentence C will be the first sentence as it mentions the subject of the parajumble &ldquo;the smaller desert animals&rdquo;. The given parajumble is about the food habits of the smaller desert animals. C is the first line as it states that they do not drink water. A will follow C .In B and D it is explained how desert animals get the water they need. D will follow B as in B &ldquo;others eat plants&hellip;&rdquo; is used. So, option d (CABD) has the correct sequence.</p>",
                    solution_hi: "<p>90.(d) CABD<br>Sentence C first sentence होगा क्योंकि यह parajumble &ldquo;the smaller desert animals&rdquo; के विषय का उल्लेख करता है। दिया गया parajumble छोटे रेगिस्तानी जानवरों के भोजन की आदतों के बारे में है। C first line है क्योंकि इसमें कहा गया है कि वे पानी नहीं पीते हैं। C के बाद Aआएगा । B और D में यह बताया गया है कि रेगिस्तानी जानवरों को उनकी ज़रूरत का पानी कैसे मिलता है। B के बाद D आएगा क्योंकि B में &ldquo;others eat plants&hellip;&rdquo; का प्रयोग किया जाता है। इसलिए, option d (CABD) में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Pick a word opposite in meaning to <br>Clamp down</p>",
                    question_hi: "<p>91. Pick a word opposite in meaning to <br>Clamp down</p>",
                    options_en: [
                        "<p>move up</p>",
                        "<p>let off</p>",
                        "<p>ease off</p>",
                        "<p>ease up</p>"
                    ],
                    options_hi: [
                        "<p>move up</p>",
                        "<p>let off</p>",
                        "<p>ease off</p>",
                        "<p>ease up</p>"
                    ],
                    solution_en: "<p>91.(b) let off. <br>&lsquo;&rsquo;Clamp down&rsquo;&rsquo; is a phrasal verb which means to fasten, grip, or support with or as if with a clamp<br>.</p>",
                    solution_hi: "<p>91.(b) let off. <br>&lsquo;&rsquo;Clamp down&rsquo;&rsquo; phrasal verb है जिसका अर्थ है जकड़ना, पकड़ना या समर्थन करना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Pick a word opposite in meaning to <br>Tacit</p>",
                    question_hi: "<p>92. Pick a word opposite in meaning to <br>Tacit</p>",
                    options_en: [
                        "<p>implied</p>",
                        "<p>wise</p>",
                        "<p>expressed</p>",
                        "<p>tactful</p>"
                    ],
                    options_hi: [
                        "<p>implied</p>",
                        "<p>wise</p>",
                        "<p>expressed</p>",
                        "<p>tactful</p>"
                    ],
                    solution_en: "<p>92.(c) expressed <br><strong>Tacit</strong> - understood or implied without being stated.<br><strong>Tactful</strong> - having or showing skill and sensitivity in dealing with others or with difficult issues.</p>",
                    solution_hi: "<p>92.(c) expressed <br><strong>Tacit</strong> - बिना बताए समझा गया या निहित किया गया।<br><strong>Tactful</strong> - दूसरों के साथ या कठिन मुद्दों से निपटने में कौशल और संवेदनशीलता होना या दिखाना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Sentence is given with blank to be filled in with an appropriate word. Four alternatives are suggested for each question.<br>You must obtain first class in order to be ______ to apply for this post.</p>",
                    question_hi: "<p>93. Sentence is given with blank to be filled in with an appropriate word. Four alternatives are suggested for each question.<br>You must obtain first class in order to be ______ to apply for this post.</p>",
                    options_en: [
                        "<p>illegible</p>",
                        "<p>elected</p>",
                        "<p>forced</p>",
                        "<p>eligible</p>"
                    ],
                    options_hi: [
                        "<p>illegible</p>",
                        "<p>elected</p>",
                        "<p>forced</p>",
                        "<p>eligible</p>"
                    ],
                    solution_en: "<p>93.(d) eligible<br>&lsquo;Eligible&rsquo; means fit or proper to be chosen. The given sentence states that you must obtain first class in order to be eligible(fit or proper) to apply for this post. Hence, &lsquo;eligible&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>93.(d) eligible/ योग्य<br>&lsquo;Eligible&rsquo; का अर्थ है चुने जाने के योग्य या उचित। दिए गए वाक्य में कहा गया है कि इस post के लिए आवेदन करने के लिए इसके लिए पात्र (उपयुक्त या उचित) होना पड़ेगा , और पात्र होने के लिए आपको प्रथम श्रेणी प्राप्त करनी होगी। इसलिए, &lsquo;Eligible&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Choose the one which can be substituted for the given words/ sentence : <br>Too willing to obey other people</p>",
                    question_hi: "<p>94. Choose the one which can be substituted for the given words/ sentence : <br>Too willing to obey other people</p>",
                    options_en: [
                        "<p>subjugate</p>",
                        "<p>subservient</p>",
                        "<p>sublimate</p>",
                        "<p>subaltern</p>"
                    ],
                    options_hi: [
                        "<p>subjugate</p>",
                        "<p>subservient</p>",
                        "<p>sublimate</p>",
                        "<p>subaltern</p>"
                    ],
                    solution_en: "<p>94.(b) <strong>Subservient</strong><br><strong>Subjugate-</strong> bring under domination or control, especially by conquest.<br><strong>Subservient-</strong> means willing to obey others unquestionably.<br><strong>Subaltern-</strong> means a subordinate. <br><strong>Sublimate-</strong> means to refine or purify.</p>",
                    solution_hi: "<p>94.(b) <strong>Subservient</strong><br><strong>Subjugate-</strong> (वशीभूत) विशेष रूप से विजय द्वारा वर्चस्व या नियंत्रण में लाना<br><strong>Subservient-</strong> का अर्थ है निस्संदेह दूसरों की आज्ञा मानने को तैयार<br><strong>Subaltern-</strong> का अर्थ है अधीनस्थ<br><strong>Sublimate-</strong> का अर्थ है परिशुद्ध करना</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate homonym to fill in the blank.<br>The farmers grow ________ in their fields.</p>",
                    question_hi: "<p>95. Select the most appropriate homonym to fill in the blank.<br>The farmers grow ________ in their fields.</p>",
                    options_en: [
                        "<p>maze</p>",
                        "<p>mays</p>",
                        "<p>mase</p>",
                        "<p>maize</p>"
                    ],
                    options_hi: [
                        "<p>maze</p>",
                        "<p>mays</p>",
                        "<p>mase</p>",
                        "<p>maize</p>"
                    ],
                    solution_en: "<p>95.(d) <strong>&lsquo;Maize&rsquo;</strong> is a tall plant that produces yellow grains in a large mass. The given sentence states that the farmers grow maize in their fields. Hence &lsquo;maize&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(d) <strong>&lsquo;Maize&rsquo;</strong> एक लंबा पौधा है जो बड़े पैमाने पर पीले दाने उत्पादित करता है। दिए गए sentence में कहा गया है कि किसान अपने खेतों में मक्का उगाते हैं। अतः &lsquo;maize&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:-</strong><br>The students (96)______ listening to their teacher. He (97)______ a line on the black-board and asked the students, \"How can you make this line smaller?\"(98)______ of the students thought it was a very simple (99)______. They said, \"We can erase a part of the line and (100)______ it smaller.\"<br><br>Select the most appropriate option to fill in the blank no. 96</p>",
                    question_hi: "<p>96. <strong>Cloze Test:-</strong><br>The students (96)______ listening to their teacher. He (97)______ a line on the black-board and asked the students, \"How can you make this line smaller?\"(98)______ of the students thought it was a very simple (99)______. They said, \"We can erase a part of the line and (100)______ it smaller.\"<br><br>Select the most appropriate option to fill in the blank no. 96</p>",
                    options_en: [
                        "<p>was</p>",
                        "<p>were</p>",
                        "<p>is</p>",
                        "<p>are</p>"
                    ],
                    options_hi: [
                        "<p>was</p>",
                        "<p>were</p>",
                        "<p>is</p>",
                        "<p>are</p>"
                    ],
                    solution_en: "<p>96.(b) were - The given passage is in the past tense and the subject of the sentence is a plural noun &lsquo;the students&rsquo;.So &ldquo;were&rdquo; will be used.</p>",
                    solution_hi: "<p>96.(b) were - दिया गया passage past tense में है और वाक्य का subject एक plural noun है \'the students\'। इसलिए \"were\" का प्रयोग किया जाएगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:-</strong><br>The students (96)______ listening to their teacher. He (97)______ a line on the black-board and asked the students, \"How can you make this line smaller?\"(98)______ of the students thought it was a very simple (99)______. They said, \"We can erase a part of the line and (100)______ it smaller.\"<br><br>Select the most appropriate option to fill in the blank no. 97</p>",
                    question_hi: "<p>97. <strong>Cloze Test:-</strong><br>The students (96)______ listening to their teacher. He (97)______ a line on the black-board and asked the students, \"How can you make this line smaller?\"(98)______ of the students thought it was a very simple (99)______. They said, \"We can erase a part of the line and (100)______ it smaller.\"<br><br>Select the most appropriate option to fill in the blank no. 97</p>",
                    options_en: [
                        "<p>drew</p>",
                        "<p>put</p>",
                        "<p>started</p>",
                        "<p>painted</p>"
                    ],
                    options_hi: [
                        "<p>drew</p>",
                        "<p>put</p>",
                        "<p>started</p>",
                        "<p>painted</p>"
                    ],
                    solution_en: "<p>97.(a) drew , The given paragraph is in the past tense so the second form of draw (drew) should be used.</p>",
                    solution_hi: "<p>97.(a) drew<br>दिया गया paragraph past tense में है इसलिए draw के second form (drew)का उपयोग किया जाना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:-</strong><br>The students (96)______ listening to their teacher. He (97)______ a line on the black-board and asked the students, \"How can you make this line smaller?\"(98)______ of the students thought it was a very simple (99)______. They said, \"We can erase a part of the line and (100)______ it smaller.\"<br><br>Select the most appropriate option to fill in the blank no. 98</p>",
                    question_hi: "<p>98. <strong>Cloze Test:-</strong><br>The students (96)______ listening to their teacher. He (97)______ a line on the black-board and asked the students, \"How can you make this line smaller?\"(98)______ of the students thought it was a very simple (99)______. They said, \"We can erase a part of the line and (100)______ it smaller.\"<br><br>Select the most appropriate option to fill in the blank no. 98</p>",
                    options_en: [
                        "<p>Every</p>",
                        "<p>No</p>",
                        "<p>Any</p>",
                        "<p>Most</p>"
                    ],
                    options_hi: [
                        "<p>Every</p>",
                        "<p>No</p>",
                        "<p>Any</p>",
                        "<p>Most</p>"
                    ],
                    solution_en: "<p>98.(d) Most - &ldquo;Most&rdquo; should be used before &ldquo;of the students&rdquo;</p>",
                    solution_hi: "<p>98.(d) Most- &ldquo;Most&rdquo; का प्रयोग &ldquo;of the students&rdquo; के पहले किया जाना चाहिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:-</strong><br>The students (96)______ listening to their teacher. He (97)______ a line on the black-board and asked the students, \"How can you make this line smaller?\"(98)______ of the students thought it was a very simple (99)______. They said, \"We can erase a part of the line and (100)______ it smaller.\"<br>&nbsp;<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    question_hi: "<p>99. <strong>Cloze Test:-</strong><br>The students (96)______ listening to their teacher. He (97)______ a line on the black-board and asked the students, \"How can you make this line smaller?\"(98)______ of the students thought it was a very simple (99)______. They said, \"We can erase a part of the line and (100)______ it smaller.\"<br>&nbsp;<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    options_en: [
                        "<p>line</p>",
                        "<p>thing</p>",
                        "<p>drawing</p>",
                        "<p>object</p>"
                    ],
                    options_hi: [
                        "<p>line</p>",
                        "<p>thing</p>",
                        "<p>drawing</p>",
                        "<p>object</p>"
                    ],
                    solution_en: "<p>99.(b) thing</p>",
                    solution_hi: "<p>99.(b) thing</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:-</strong><br>The students (96)______ listening to their teacher. He (97)______ a line on the black-board and asked the students, \"How can you make this line smaller?\"(98)______ of the students thought it was a very simple (99)______. They said, \"We can erase a part of the line and (100)______ it smaller.\"<br><br>Select the most appropriate option to fill in the blank no. 100</p>",
                    question_hi: "<p>100. <strong>Cloze Test:-</strong><br>The students (96)______ listening to their teacher. He (97)______ a line on the black-board and asked the students, \"How can you make this line smaller?\"(98)______ of the students thought it was a very simple (99)______. They said, \"We can erase a part of the line and (100)______ it smaller.\"<br><br>Select the most appropriate option to fill in the blank no. 100</p>",
                    options_en: [
                        "<p>rub</p>",
                        "<p>reduce</p>",
                        "<p>make</p>",
                        "<p>cut</p>"
                    ],
                    options_hi: [
                        "<p>rub</p>",
                        "<p>reduce</p>",
                        "<p>make</p>",
                        "<p>cut</p>"
                    ],
                    solution_en: "<p>100.(c) make.</p>",
                    solution_hi: "<p>100.(c) make.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>