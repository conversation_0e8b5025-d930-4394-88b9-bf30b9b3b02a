<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the option that correctly expresses the given sentence in passive voice.<br>I will never overlook this creativity.</p>",
                    question_hi: "<p>1. Select the option that correctly expresses the given sentence in passive voice.<br>I will never overlook this creativity.</p>",
                    options_en: ["<p>This creativity will never be overlooked by me.</p>", "<p>This creativity will be overlooked by me.</p>", 
                                "<p>This creativity will being overlooked by me.</p>", "<p>This creativity will not never be overlooked by me.</p>"],
                    options_hi: ["<p>This creativity will never be overlooked by me.</p>", "<p>This creativity will be overlooked by me.</p>",
                                "<p>This creativity will being overlooked by me.</p>", "<p>This creativity will not never be overlooked by me.</p>"],
                    solution_en: "<p>1.(a) This creativity will never be overlooked by me. (Correct)<br>(b) This creativity will be overlooked by me. (&lsquo;Never&rsquo; is missing)<br>(c) This creativity<span style=\"text-decoration: underline;\"> will being</span> overlooked by me. (Incorrect Verb)<br>(d) This creativity will not never be overlooked by me. (Incorrect Use of &lsquo;not&rsquo;)</p>",
                    solution_hi: "<p>1.(a) This creativity will never be overlooked by me. (Correct)<br>(b) This creativity will be overlooked by me. (&lsquo;Never&rsquo; missing है )<br>(c) This creativity <span style=\"text-decoration: underline;\">will being</span> overlooked by me. (गलत Verb)<br>(d) This creativity will not never be overlooked by me. (&lsquo;not&rsquo; का गलत प्रयोग)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the option that can be used as a one-word substitute for the given group of words.<br>A place where birds are kept</p>",
                    question_hi: "<p>2. Select the option that can be used as a one-word substitute for the given group of words.<br>A place where birds are kept</p>",
                    options_en: ["<p>Zoo</p>", "<p>Aviary</p>", 
                                "<p>Sanctuary</p>", "<p>Hive</p>"],
                    options_hi: ["<p>Zoo</p>", "<p>Aviary</p>",
                                "<p>Sanctuary</p>", "<p>Hive</p>"],
                    solution_en: "<p>2.(b) <strong>Aviary</strong>- a place where birds are kept.<br><strong>Zoo</strong>- an area in which animals, especially wild animals, are kept so that people can go and look at them or study them.<br><strong>Sanctuary</strong>- a place where birds or animals can live and be protected, especially from being hunted or dangerous conditions.<br><strong>Hive</strong>- a container for housing honeybees.</p>",
                    solution_hi: "<p>2.(b) <strong>Aviary </strong>(पक्षीशाला/चिड़ियाखाना)- a place where birds are kept.<br><strong>Zoo </strong>(जंतुशाला/चिड़ियाघर)- an area in which animals, especially wild animals, are kept so that people can go and look at them or study them.<br><strong>Sanctuary </strong>(अभ्यारण्य)- a place where birds or animals can live and be protected, especially from being hunted or dangerous conditions.<br><strong>Hive </strong>(मधुमक्खियों का छत्ता)- a container for housing honeybees.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the most appropriate ANTONYM of the given word.<br>Catastrophe</p>",
                    question_hi: "<p>3. Select the most appropriate ANTONYM of the given word.<br>Catastrophe</p>",
                    options_en: ["<p>Success</p>", "<p>Nurture</p>", 
                                "<p>Immaculate</p>", "<p>Disaster</p>"],
                    options_hi: ["<p>Success</p>", "<p>Nurture</p>",
                                "<p>Immaculate</p>", "<p>Disaster</p>"],
                    solution_en: "<p>3.(a) <strong>Success</strong>- the accomplishment of an aim or purpose.<br><strong>Catastrophe</strong>- a sudden and widespread disaster.<br><strong>Nurture</strong>- to care for and encourage growth or development.<br><strong>Immaculate</strong>- perfectly clean or free from flaws.<br><strong>Disaster</strong>- a sudden event causing great damage or suffering.</p>",
                    solution_hi: "<p>3.(a) <strong>Success</strong> (सफलता)- the accomplishment of an aim or purpose.<br><strong>Catastrophe </strong>(प्रलय)- a sudden and widespread disaster.<br><strong>Nurture </strong>(पालन-पोषण)- to care for and encourage growth or development.<br><strong>Immaculate</strong> (निर्मल)- perfectly clean or free from flaws.<br><strong>Disaster </strong>(आपदा/विपत्ति)- a sudden event causing great damage or suffering.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Beyond skills, experience, and training, companies search for applicants who display excitement &mdash; those they believe <span style=\"text-decoration: underline;\">shall carry out allocated responsibilities cheerfully and cooperatively</span>.</p>",
                    question_hi: "<p>4. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Beyond skills, experience, and training, companies search for applicants who display excitement &mdash; those they believe <span style=\"text-decoration: underline;\">shall carry out allocated responsibilities cheerfully and cooperatively.</span></p>",
                    options_en: ["<p>shall take up any designated obligations</p>", "<p>will execute assigned duties</p>", 
                                "<p>will demonstrate non-biased work</p>", "<p>should pick up all skills and works</p>"],
                    options_hi: ["<p>shall take up any designated obligations</p>", "<p>will execute assigned duties</p>",
                                "<p>will demonstrate non-biased work</p>", "<p>should pick up all skills and works</p>"],
                    solution_en: "<p>4.(b) will execute assigned duties<br>&lsquo;Execute&rsquo; means to carry out fully. &lsquo;Duties&rsquo; means responsibility. The given sentence states that beyond skills, experience, and training, companies search for applicants who display excitement &mdash; those they believe will execute assigned duties cheerfully and cooperatively. Hence, &lsquo;will execute assigned duties&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(b) will execute assigned duties<br>&lsquo;Execute&rsquo; का अर्थ है पूरी तरह से कार्यान्वित करना। &lsquo;Duties&rsquo; का अर्थ है जिम्मेदारी। दिए गए sentence में कहा गया है कि skills, experience और training से परे, कंपनियाँ ऐसे आवेदकों (applicants) की तलाश करती हैं जो उत्साह दिखाते हैं - जिनके बारे में उन्हें लगता है कि वे सौंपे गए कर्तव्यों को प्रसन्नतापूर्वक (cheerfully) और एक दूसरे के सहयोग से निष्पादित करेंगे। अतः, &lsquo;will execute assigned duties&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "5. Select the most appropriate option to fill in the blank.<br />After years of fights with his neighbour Ram, Shekhar decided to hold out the _______ and invited Ram for tea.",
                    question_hi: "5. Select the most appropriate option to fill in the blank.<br />After years of fights with his neighbour Ram, Shekhar decided to hold out the _______ and invited Ram for tea.",
                    options_en: ["  poker face", " comparing apples to oranges", 
                                " chip on shoulder", " olive branch<br /> "],
                    options_hi: ["  poker face", " comparing apples to oranges",
                                " chip on shoulder", " olive branch"],
                    solution_en: "5.(d) olive branch<br />‘Olive branch’ is an idiom which means an offer or gesture of conciliation or goodwill. The given sentence states that after years of fights with his neighbour Ram, Shekhar decided to hold out the olive branch and invited Ram for tea. Hence, ‘olive branch’ is the most appropriate answer.",
                    solution_hi: "5.(d) olive branch<br />‘Olive branch’ एक Idiom है जिसका अर्थ है सुलह या सद्भावना का प्रस्ताव या संकेत। दिए गए sentence में कहा गया है कि अपने पड़ोसी राम के साथ कई वर्षों के झगड़े के बाद, शेखर ने शांति की पेशकश करने का फैसला किया और राम को चाय पर आमंत्रित किया। अतः,  ‘olive branch’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "6. Select the INCORRECTLY spelt word.",
                    question_hi: "6. Select the INCORRECTLY spelt word.",
                    options_en: ["  Centenary", " Consumerist", 
                                " Capitalist", " Countemptible"],
                    options_hi: ["  Centenary", " Consumerist",
                                " Capitalist", " Countemptible"],
                    solution_en: "6.(d) Countemptible<br />\'Contemptible\' is the correct spelling.",
                    solution_hi: "6.(d) Countemptible<br />\'Contemptible\' सही spelling है। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>One must be very <span style=\"text-decoration: underline;\">strong </span>to lift this box of glass jars.</p>",
                    question_hi: "<p>7. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>One must be very <span style=\"text-decoration: underline;\">strong </span>to lift this box of glass jars.</p>",
                    options_en: [" Mighty", " Weak", 
                                " Complicated", " Rugged"],
                    options_hi: [" Mighty", " Weak",
                                " Complicated", " Rugged"],
                    solution_en: "<p>7.(b) <strong>Weak</strong><br><strong>Mighty</strong>- possessing great and impressive power or strength.<br><strong>Complicated</strong>- consisting of many interconnecting parts or elements.<br><strong>Rugged</strong>- having a rough, uneven surface.</p>",
                    solution_hi: "<p>7.(b) <strong>Weak</strong><br><strong>Mighty </strong>(पराक्रमी/शक्तिशाली)- possessing great and impressive power or strength.<br><strong>Complicated </strong>(जटिल)- consisting of many interconnecting parts or elements.<br><strong>Rugged </strong>(खुरदरा/ऊबड़-खाबड़)- having a rough, uneven surface.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate meaning of the underlined idiom.<br>Anand personally believes in the motto,<span style=\"text-decoration: underline;\"> &ldquo;When life gives you lemons, make lemonade.&rdquo;</span></p>",
                    question_hi: "<p>8. Select the most appropriate meaning of the underlined idiom.<br>Anand personally believes in the motto, <span style=\"text-decoration: underline;\">&ldquo;When life gives you lemons, make lemonade.&rdquo;</span></p>",
                    options_en: [" Earn the maximum out of business", " Nourish health", 
                                " Enjoy the most when the season is favourable", " Make the best out of difficult situations"],
                    options_hi: [" Earn the maximum out of business", " Nourish health",
                                " Enjoy the most when the season is favourable", " Make the best out of difficult situations"],
                    solution_en: "<p>8.(d) <strong>When life gives you lemons, make lemonade.- </strong>make the best out of difficult situations.</p>",
                    solution_hi: "<p>8.(d)<strong> When life gives you lemons, make lemonade.-</strong> make the best out of difficult situations./कठिन परिस्थितियों में भी सबसे अच्छा करना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate ANTONYM of the given word.<br>Monotonous</p>",
                    question_hi: "<p>9. Select the most appropriate ANTONYM of the given word.<br>Monotonous</p>",
                    options_en: ["<p>Tiresome</p>", "<p>Tedious</p>", 
                                "<p>Engrossing</p>", "<p>Rational</p>"],
                    options_hi: ["<p>Tiresome</p>", "<p>Tedious</p>",
                                "<p>Engrossing</p>", "<p>Rational</p>"],
                    solution_en: "<p>9.(c) <strong>Engrossing</strong>- absorbing all attention or interest<br><strong>Monotonous</strong>- lacking in variety and interest.<br><strong>Tiresome</strong>- causing one to feel bored or annoyed.<br><strong>Tedious</strong>- too long, slow, or dull.<br><strong>Rational</strong>- based on or in accordance with reason or logic.</p>",
                    solution_hi: "<p>9.(c) <strong>Engrossing </strong>(मनोरंजक)- absorbing all attention or interest<br><strong>Monotonous </strong>(नीरस)- lacking in variety and interest.<br><strong>Tiresome </strong>(नीरस)- causing one to feel bored or annoyed.<br><strong>Tedious </strong>(अरोचक/उबाऊ)- too long, slow, or dull.<br><strong>Rational</strong> (तार्किक)- based on or in accordance with reason or logic.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Identify the INCORRECTLY spelt word in the following sentence.<br />You need to be concious enough to listen to the question.",
                    question_hi: "10. Identify the INCORRECTLY spelt word in the following sentence.<br />You need to be concious enough to listen to the question.",
                    options_en: [" concious", " enough", 
                                " question", " listen"],
                    options_hi: [" concious", " enough",
                                " question", " listen"],
                    solution_en: "10.(a) concious<br />\'Conscious\' is the correct spelling.",
                    solution_hi: "10.(a) concious<br />\'Conscious\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate synonym to replace the underlined word in the given sentence.<br>How did we end up in this <span style=\"text-decoration: underline;\">rapturous </span>situation?</p>",
                    question_hi: "<p>11. Select the most appropriate synonym to replace the underlined word in the given sentence.<br>How did we end up in this <span style=\"text-decoration: underline;\">rapturous </span>situation?</p>",
                    options_en: ["<p>ecstatic</p>", "<p>terrifying</p>", 
                                "<p>misleading</p>", "<p>disastrous</p>"],
                    options_hi: ["<p>ecstatic</p>", "<p>terrifying</p>",
                                "<p>misleading</p>", "<p>disastrous</p>"],
                    solution_en: "<p>11.(a) <strong>Ecstatic</strong>- feeling or expressing overwhelming happiness or joyful excitement.<br><strong>Rapturous</strong>- characterized by, feeling, or expressing great pleasure or enthusiasm.<br><strong>Terrifying</strong>- causing extreme fear.<br><strong>Misleading</strong>- giving the wrong idea or impression.<br><strong>Disastrous</strong>- causing great damage or harm.</p>",
                    solution_hi: "<p>11.(a) <strong>Ecstatic </strong>(आनंदमय)- feeling or expressing overwhelming happiness or joyful excitement.<br><strong>Rapturous </strong>(उत्साहपूर्ण)- characterized by, feeling, or expressing great pleasure or enthusiasm.<br><strong>Terrifying </strong>(भयावह/खौफ़नाक)- causing extreme fear.<br><strong>Misleading </strong>(भ्रामक)- giving the wrong idea or impression.<br><strong>Disastrous</strong> (विनाशकारी)- causing great damage or harm.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>The mother asked the child, &ldquo;Will you tell me <span style=\"text-decoration: underline;\">who did accompany you</span> to the airport?&rdquo;</p>",
                    question_hi: "<p>12. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>The mother asked the child, &ldquo;Will you tell me <span style=\"text-decoration: underline;\">who did accompany you</span> to the airport?&rdquo;</p>",
                    options_en: ["<p>who accompanies you</p>", "<p>who does accompany you</p>", 
                                "<p>who accompanied you</p>", "<p>who must be accompanied you</p>"],
                    options_hi: ["<p>who accompanies you</p>", "<p>who does accompany you</p>",
                                "<p>who accompanied you</p>", "<p>who must be accompanied you</p>"],
                    solution_en: "<p>12.(c) who accompanied you<br>When a question is part of a sentence, the helping verb is written after the subject, not before the subject. Past form (V<sub>2</sub>) is used in simple past tense. Hence, &lsquo;who accompanied(V<sub>2</sub>) you&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(c) who accompanied you<br>जब कोई question, sentence का part होता है, तो helping verb को subject से पहले नहीं बल्कि, subject के बाद लिखा जाता है। Simple past tense में past form (V<sub>2</sub>) का प्रयोग किया जाता है। अतः, &lsquo;who accompanied(V<sub>2</sub>) you&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "13. Sentences of a paragraph are given below in jumbled order. Select the option that arranges the sentences in the correct sequence to form a meaningful and coherent paragraph.<br />A)This past summer, my dream finally came true.<br />B)Strange lands, exciting places, and new cultures have always fascinated me.<br />C)Ever since I was a little girl, I dreamed about travelling overseas.<br />D)I got to travel to England, France, Switzerland, and Germany.",
                    question_hi: "13. Sentences of a paragraph are given below in jumbled order. Select the option that arranges the sentences in the correct sequence to form a meaningful and coherent paragraph.<br />A)This past summer, my dream finally came true.<br />B)Strange lands, exciting places, and new cultures have always fascinated me.<br />C)Ever since I was a little girl, I dreamed about travelling overseas.<br />D)I got to travel to England, France, Switzerland, and Germany.",
                    options_en: [" DCBA", " CBAD", 
                                " ABCD", " BADC<br /> "],
                    options_hi: [" DCBA", " CBAD",
                                " ABCD", " BADC"],
                    solution_en: "13.(b) CBAD<br />Sentence C will be the starting line as it introduces the main idea of the parajumble, i.e. ‘the girl dreamed about travelling overseas ever since childhood’. And, Sentence B states that strange lands, exciting places, and new cultures have always fascinated her. So, B will follow C. Further, Sentence A states that her dream finally came true this summer & Sentence D states that she got to travel to England, France, Switzerland, and Germany. So, D will follow A. Going through the options, option ‘b’ has the correct sequence.",
                    solution_hi: "13.(b) CBAD<br />Sentence C प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार ‘the girl dreamed about travelling overseas ever since childhood’ का परिचय देती है। और Sentence B बताता है कि strange lands, exciting places और new cultures हमेशा से उसे आकर्षित (fascinated) करते रहे हैं। इसलिए, C के बाद B आएगा । इसके अलावा, Sentence A बताता है कि उसका सपना आखिरकार इस summer में सच हो गया और Sentence D बताता है कि उसे इंग्लैंड, फ्रांस, स्विट्जरलैंड और जर्मनी की यात्रा करने का मौका मिला। इसलिए, A के बाद D आएगा। अतः options के माध्यम से जाने पर,  option ‘b’ में सही sequence है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate synonym of the given word.<br>Rectify</p>",
                    question_hi: "<p>14. Select the most appropriate synonym of the given word.<br>Rectify</p>",
                    options_en: ["<p>mar</p>", "<p>amend</p>", 
                                "<p>corrupt</p>", "<p>upset</p>"],
                    options_hi: ["<p>mar</p>", "<p>amend</p>",
                                "<p>corrupt</p>", "<p>upset</p>"],
                    solution_en: "<p>14.(b) <strong>Amend</strong>- to make changes for the better.<br><strong>Rectify</strong>- to correct or make right.<br><strong>Mar</strong>- to impair the appearance or quality of.<br><strong>Corrupt</strong>- to cause to become morally or ethically dishonest.<br><strong>Upset</strong>- to disturb the normal functioning of something.</p>",
                    solution_hi: "<p>14.(b) <strong>Amend </strong>(संशोधन)- to make changes for the better.<br><strong>Rectify </strong>(संशोधित करना)- to correct or make right.<br><strong>Mar </strong>(दूषित करना/बिगाड़ना)- to impair the appearance or quality of.<br><strong>Corrupt </strong>(भ्रष्ट/विकृत)- to cause to become morally or ethically dishonest.<br><strong>Upset </strong>(व्याकुल/परेशान)- to disturb the normal functioning of something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate option to fill in the blank.<br>To ______ the ticket, Amith had to visit the travel agency many times.</p>",
                    question_hi: "<p>15. Select the most appropriate option to fill in the blank.<br>To ______ the ticket, Amith had to visit the travel agency many times.</p>",
                    options_en: ["<p>contract</p>", "<p>confirm</p>", 
                                "<p>conform</p>", "<p>convention</p>"],
                    options_hi: ["<p>contract</p>", "<p>confirm</p>",
                                "<p>conform</p>", "<p>convention</p>"],
                    solution_en: "<p>15.(b) confirm<br>&lsquo;Confirm&rsquo; means to be sure of something. The given sentence states that to confirm the ticket, Amith had to visit the travel agency many times. Hence, &lsquo;confirm&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(b) confirm<br>&lsquo;Confirm&rsquo; का मतलब है सुनिश्चित करना। दिए गए sentence में बताया गया है कि ticket confirm करवाने के लिए अमित को कई बार travel agency जाना पड़ा। अतः, &lsquo;confirm&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "16. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If you don’t find any error, mark ‘No error’ as your answer.<br /> My brother received / his MBA degree into / the university last year.",
                    question_hi: "16. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If you don’t find any error, mark ‘No error’ as your answer.<br /> My brother received / his MBA degree into / the university last year.",
                    options_en: ["  the university last year", "  his MBA degree into", 
                                " No error", "  My brother received"],
                    options_hi: ["  the university last year", "  his MBA degree into",
                                " No error", "  My brother received"],
                    solution_en: "16.(b) his MBA degree into<br />‘Into’ must be replaced with \'from\' as we use ‘from’ to indicate the source of something. Hence, ‘his MBA degree from’ is the most appropriate answer.",
                    solution_hi: "16.(b) his MBA degree into<br />‘Into’ के स्थान पर ‘from’ का प्रयोग होगा, क्योंकि किसी चीज़ के source  को indicate करने के लिए ‘from’ का प्रयोग किया जाता हैं। अतः, ‘his MBA degree from’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "17. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br />A. Mobile phones or smartphones are becoming popular all over the world.<br />B. But at the same time, it also harms us in many ways.<br />C. It is the most widely used means of communication today.<br />D. Today, it is very affordable and available to everyone.",
                    question_hi: "17. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br />A. Mobile phones or smartphones are becoming popular all over the world.<br />B. But at the same time, it also harms us in many ways.<br />C. It is the most widely used means of communication today.<br />D. Today, it is very affordable and available to everyone.",
                    options_en: [" ABCD", " DACB", 
                                " CABD", " ACDB"],
                    options_hi: [" ABCD", " DACB",
                                " CABD", " ACDB"],
                    solution_en: "17.(d) ACDB<br />Sentence A will be the starting line as it introduces the main idea of the parajumble, i.e. ‘the world-wide popularity of mobile phones or smartphones’. And, Sentence C states that it is the most widely used means of communication today. So, C will follow A. Further, Sentence D states that it is very affordable and available to everyone & Sentence B states that it also harms us in many ways. So, B will follow D. Going through the options, option ‘d’ has the correct sequence.",
                    solution_hi: "17.(d) ACDB<br />Sentence A प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार ‘the world-wide popularity of mobile phones or smartphones’ का परिचय देती है। और, Sentence C बताता है कि यह आज communication का सबसे व्यापक रूप से उपयोग किया जाने वाला साधन है। इसलिए, A के बाद C आएगा। इसके अलावा, Sentence D बताता है कि यह बहुत किफायती (affordable) है और सभी के लिए उपलब्ध है और Sentence B बताता है कि यह हमें कई तरह से नुकसान भी पहुँचाता है। इसलिए, D के बाद  B आएगा। अतः options के माध्यम से जाने पर,  option ‘d’ में सही sequence है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the option that can be used as a one-word substitute for the given phrase.<br>Incapable of being defeated</p>",
                    question_hi: "<p>18. Select the option that can be used as a one-word substitute for the given phrase.<br>Incapable of being defeated</p>",
                    options_en: ["<p>Invisible</p>", "<p>Unavoidable</p>", 
                                "<p>Invincible</p>", "<p>Ineluctable</p>"],
                    options_hi: ["<p>Invisible</p>", "<p>Unavoidable</p>",
                                "<p>Invincible</p>", "<p>Ineluctable</p>"],
                    solution_en: "<p>18.(c) <strong>Invincible</strong>- incapable of being defeated.<br><strong>Invisible</strong>- that cannot be seen.<br><strong>Unavoidable</strong>- not able to be avoided, prevented, or ignored.<br><strong>Ineluctable</strong>- impossible to avoid.</p>",
                    solution_hi: "<p>18.(c) <strong>Invincible </strong>(अजेय/अपराजेय)- incapable of being defeated.<br><strong>Invisible </strong>(अदृश्य)- that cannot be seen.<br><strong>Unavoidable </strong>(अटल)- not able to be avoided, prevented, or ignored.<br><strong>Ineluctable </strong>(अपरिहार्य/अटल)- impossible to avoid.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the option that correctly expresses the following sentence in passive voice.<br>Who invited you to this party?</p>",
                    question_hi: "<p>19. Select the option that correctly expresses the following sentence in passive voice.<br>Who invited you to this party?</p>",
                    options_en: ["<p>By whom you are invited to this party?</p>", "<p>By whom are you invited to this party?</p>", 
                                "<p>By whom you were invited to this party?</p>", "<p>By whom were you invited to this party?</p>"],
                    options_hi: ["<p>By whom you are invited to this party?</p>", "<p>By whom are you invited to this party?</p>",
                                "<p>By whom you were invited to this party?</p>", "<p>By whom were you invited to this party?</p>"],
                    solution_en: "<p>19.(d) By whom were you invited to this party? (Correct)<br>(a) By whom <span style=\"text-decoration: underline;\">you are invited</span> to this party? (Incorrect Tense)<br>(b) By whom <span style=\"text-decoration: underline;\">are you invited</span> to this party? (Incorrect Tense)<br>(c) By whom you were invited to this party? (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>19.(d) By whom were you invited to this party? (Correct)<br>(a) By whom <span style=\"text-decoration: underline;\">you are invited </span>to this party? (गलत Tense)<br>(b) By whom <span style=\"text-decoration: underline;\">are you invited</span> to this party? (गलत Tense)<br>(c) By whom you were invited to this party? (गलत Sentence Structure)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "20. The following sentence has been divided into four segments. Identify the segment that contains an error in the usage of the interjection.<br />He replied / in a dry tone, / “Ouch! I will not work / for you anymore.”",
                    question_hi: "20. The following sentence has been divided into four segments. Identify the segment that contains an error in the usage of the interjection.<br />He replied / in a dry tone, / “Ouch! I will not work / for you anymore.”",
                    options_en: [" in a dry tone,", " He replied", 
                                " for you anymore.”", " \'\'Ouch! I will not work"],
                    options_hi: [" in a dry tone,", " He replied",
                                " for you anymore.”", " \'\'Ouch! I will not work"],
                    solution_en: "20.(d) \'\'Ouch! I will not work<br />‘Ouch!’ is used to express pain. However, in the given sentence, the speaker replied in a dry tone. So, the correct interjection to use is ‘Ugh!’. Hence, ‘Ugh! I will not work for you anymore’ is the most appropriate answer.",
                    solution_hi: "20.(d) \'\'Ouch! I will not work<br />‘Ouch!’ का प्रयोग दर्द (pain) को व्यक्त करने के लिए किया जाता है। हालाँकि, दिए गए sentence में, speaker ने रूखे स्वर (dry tone) में उत्तर दिया। इसलिए, उपयोग करने के लिए सही interjection ‘Ugh!’ है। अतः, ‘Ugh! I will not work for you anymore’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:</strong><br>Gandhi, (21)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (22)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (23)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (24)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (25)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21. <strong>Cloze Test:</strong><br>Gandhi, (21)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (22)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (23)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (24)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (25)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>an</p>", "<p>for</p>", 
                                "<p>the</p>", "<p>a</p>"],
                    options_hi: ["<p>an</p>", "<p>for</p>",
                                "<p>the</p>", "<p>a</p>"],
                    solution_en: "<p>21.(c) the<br>The definite article &lsquo;the&rsquo; is used before titles given to great personalities. Gandhi has been given the title &lsquo;The father of the nation&rsquo;. Hence, &lsquo;the&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(c) the<br>महान व्यक्तित्वों को दी जाने वाली उपाधियों (titles) से पहले definite article &lsquo;the&rsquo; का use किया जाता है। गांधी को &lsquo;राष्ट्रपिता(father of the nation)&rsquo; की उपाधि दी गई है। अतः, &lsquo;the&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong><br>Gandhi, (21)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (22)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (23)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (24)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (25)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze Test:</strong><br>Gandhi, (21)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (22)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (23)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (24)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (25)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>forest-living</p>", "<p>life</p>", 
                                "<p>forest-lived</p>", "<p>alive</p>"],
                    options_hi: ["<p>forest-living</p>", "<p>life</p>",
                                "<p>forest-lived</p>", "<p>alive</p>"],
                    solution_en: "<p>22.(a) forest-living<br>&lsquo;Forest-living&rsquo; means living in the forest. The given passage states that if violence is the law of the forest-living-beasts, non-violence is the law of the civilised human species. Hence, &lsquo;forest-living&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(a) forest-living<br>&lsquo;Forest-living&rsquo; का अर्थ है जंगल में रहने वाला। दिए गए passage में कहा गया है कि यदि violence वन-जीवों का नियम है, तो non-violence सभ्य मानव प्रजाति का नियम है। अतः, &lsquo;forest-living&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.<strong> Cloze Test:</strong><br>Gandhi, (21)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (22)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (23)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (24)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (25)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23. <strong>Cloze Test:</strong><br>Gandhi, (21)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (22)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (23)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (24)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (25)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>her</p>", "<p>its</p>", 
                                "<p>one&rsquo;s</p>", "<p>one</p>"],
                    options_hi: ["<p>her</p>", "<p>its</p>",
                                "<p>one&rsquo;s</p>", "<p>one</p>"],
                    solution_en: "<p>23.(c) one&rsquo;s<br>The possessive of &lsquo;one&rsquo; is one&rsquo;s. Hence, one&rsquo;s is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(c) one&rsquo;s<br>&lsquo;One&rsquo; का possessive, one&rsquo;s है। अतः, one&rsquo;s सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong><br>Gandhi, (21)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (22)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (23)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (24)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (25)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze Test:</strong><br>Gandhi, (21)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (22)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (23)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (24)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (25)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>prove</p>", "<p>approved</p>", 
                                "<p>proven</p>", "<p>proving</p>"],
                    options_hi: ["<p>prove</p>", "<p>approved</p>",
                                "<p>proven</p>", "<p>proving</p>"],
                    solution_en: "<p>24.(c) proven<br>&lsquo;Proven&rsquo; means tried and tested. The given passage states that it is a practically proven philosophy. Hence, &lsquo;proven&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(c) proven<br>&lsquo;Proven&rsquo; का अर्थ है आजमाया और परीक्षण किया हुआ। दिए गए passage में कहा गया है कि यह व्यावहारिक रूप से सिद्ध दर्शन (philosophy) है। अतः, &lsquo;proven&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong><br>Gandhi, (21)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (22)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (23)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (24)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (25)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25.<strong> Cloze Test:</strong><br>Gandhi, (21)_________ father of the Indian nation, practiced &lsquo;ahimsa&rsquo; in his personal and political life. He firmly believed that non-violence is not meant merely for monks, saints and priests; it is for commoners too. If violence is the law of the (22)_______-beasts, non-violence is the law of the civilised human species. &lsquo;The spirit of the brute is inactive and so he knows no law; he knows only physical might. The dignity of man requires obedience to a higher law, to strength of the spirit,&rsquo; Gandhi said. Ahimsa, to Gandhiji, meant that one should love all; even (23)________ enemies. And the expression of love, ahimsa, should be in such a manner that it impresses itself indelibly upon the so-called enemy, and then the enemy must return that love. It is a practically (24)________ philosophy. Gandhi was realistic; he believed that non-violence provides the fullest protection to one&rsquo;s self-respect and sense of humour. It won&rsquo;t work in the defence of (25)_______ gains and immoral acts. He called his marches off when people indulged in violence during protests for India&rsquo;s freedom. His faith in &lsquo;ahimsa&rsquo; played an influential role in enabling the creation of an independent India in 1947, with minimum bloodshed, breaking the shackles framed by the British.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>relevant</p>", "<p>ill-gotten</p>", 
                                "<p>ill-will</p>", "<p>moral</p>"],
                    options_hi: ["<p>relevant</p>", "<p>ill-gotten</p>",
                                "<p>ill-will</p>", "<p>moral</p>"],
                    solution_en: "<p>25.(b) ill-gotten<br>&lsquo;Ill-gotten&rsquo; means acquired by illegal or unfair means. The given passage states that it won&rsquo;t work in the defence of ill-gotten gains and immoral acts. Hence, &lsquo;ill-gotten&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(b) ill-gotten<br>&lsquo;Ill-gotten&rsquo; का अर्थ है अवैध या अनुचित तरीकों से अर्जित किया गया। दिए गए passage में कहा गया है कि यह गलत तरीके से अर्जित लाभ और अनैतिक कार्यों (immoral acts) के बचाव में काम नहीं करेगा। अतः, &lsquo;ill-gotten&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>