<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language, \'HEIGHT\' is coded as \"FCGDEQ What is the code for METHOD\' in that code language ?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, \'HEIGHT\' को \'FCGDEQ\' लिखा जाता है। उसी कूट भाषा में \'METHOD\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>KCRELB</p>", "<p>KCRFMB</p>", 
                                "<p>KCREMA</p>", "<p>KCRELA</p>"],
                    options_hi: ["<p>KCRELB</p>", "<p>KCRFMB</p>",
                                "<p>KCREMA</p>", "<p>KCRELA</p>"],
                    solution_en: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343415711.png\" alt=\"rId4\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343415821.png\" alt=\"rId5\"></p>",
                    solution_hi: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343415711.png\" alt=\"rId4\"><br>इसी प्रकार<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343415821.png\" alt=\"rId5\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. From the given option figures, select the one in which the question figure is hidden/embedded. (rotation is NOT allowed)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343415971.png\" alt=\"rId6\"></p>",
                    question_hi: "<p>2. दी गईं विकल्प आकृतियों में से, उस आकृति का चयन कीजिए जिसमें प्रश्न में दी गई आकृति छिपी हुई/निहित है। (घूर्णन की अनुमति नहीं है)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343415971.png\" alt=\"rId6\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416119.png\" alt=\"rId7\" width=\"90\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416222.png\" alt=\"rId8\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416333.png\" alt=\"rId9\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416432.png\" alt=\"rId10\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416119.png\" alt=\"rId7\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416222.png\" alt=\"rId8\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416333.png\" alt=\"rId9\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416432.png\" alt=\"rId10\" width=\"90\"></p>"],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416538.png\" alt=\"rId11\" width=\"90\"></p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416538.png\" alt=\"rId11\" width=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "3. Select the number from among the given options that can replace the question mark (?) in the following series.<br />23, ?, 89, 177, 353, 705",
                    question_hi: "3. दिए गए विकल्पों में से उस संख्या का चयन कीजिए जो निम्नलिखित शृंखला में प्रश्न चिह्न (?) के स्थान पर आ सकती है।<br />23, ?, 89, 177, 353, 705",
                    options_en: [" 45 ", " 47", 
                                " 49", " 42"],
                    options_hi: [" 45 ", " 47",
                                " 49", " 42"],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416635.png\" alt=\"rId12\" width=\"400\"></p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416635.png\" alt=\"rId12\" width=\"400\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Arrange the following words in a logical and meaningful order.<br>1. Tree <br>2. Sprout <br>3. Sapling<br>4. Seed <br>5. Seedling</p>",
                    question_hi: "<p>4. निम्नलिखित शब्दों को तार्किक और अर्थपूर्ण क्रम में व्यवस्थित कीजिए ।<br>1. पेड़ <br>2. अंकुर (Sprout) <br>3. छोटा पौधा<br>4. बीज <br>5. नवांकुर</p>",
                    options_en: ["<p>2, 4, 3, 5, 1</p>", "<p>2, 4, 5, 3, 1</p>", 
                                "<p>4, 5, 2, 3, 1</p>", "<p>4, 2, 5, 3, 1</p>"],
                    options_hi: ["<p>2, 4, 3, 5, 1</p>", "<p>2, 4, 5, 3, 1</p>",
                                "<p>4, 5, 2, 3, 1</p>", "<p>4, 2, 5, 3, 1</p>"],
                    solution_en: "<p>4.(d) The correct order is <br>Seed(4) &rarr; Sprout (2) &rarr; Seedling (5) &rarr; Sapling (3) &rarr; Tree(1)</p>",
                    solution_hi: "<p>4.(d) सही क्रम है <br>बीज(4) &rarr; कोंपल(2) &rarr; नवांकुर(5) &rarr; छोटा&nbsp;पौधा (3) &rarr; पेड़(1)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>Some hooks are rags.<br>All rags are bottles.<br><strong>Conclusions:</strong><br>(I) Some bottles are hooks.<br>(II) All bottles are rags.</p>",
                    question_hi: "<p>5. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, चाहे वह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो और निर्णय लीजिए कि दिए गए निष्कर्षों में से कौन-सा/कौन-से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन:</strong><br>कुछ हुक, रैग हैं।<br>सभी रैग, बोतल हैं।<br><strong>निष्कर्ष:</strong><br>(I) कुछ बोतल, हुक हैं।<br>(II) सभी बोतल, रैग हैं।</p>",
                    options_en: ["<p>Only conclusion (II) follows</p>", "<p>Only conclusion (I) follows</p>", 
                                "<p>Both conclusions (I) and (II) follow</p>", "<p>Neither conclusion (I) nor (II) follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष II अनुसरण करता है।</p>", "<p>केवल निष्कर्ष I अनुसरण करता है।</p>",
                                "<p>निष्कर्ष। और II दोनों अनुसरण करते हैं।</p>", "<p>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416794.png\" alt=\"rId13\" width=\"279\" height=\"83\"><br>Only conclusion I follow.</p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343416947.png\" alt=\"rId14\" width=\"270\" height=\"75\"><br>केवल निष्कर्ष I अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In the following question, select the related letters from the given alternatives.<br>DQMZ : FSOB :: AGHL : ?</p>",
                    question_hi: "<p>6. निम्नलिखित प्रश्न में, दिए गए विकल्पों में से संबंधित अक्षरों का चयन कीजिए।<br>DQMZ : FSOB :: AGHL : ?</p>",
                    options_en: ["<p>NSMT</p>", "<p>PUSX</p>", 
                                "<p>CIJN</p>", "<p>ZOPB</p>"],
                    options_hi: ["<p>NSMT</p>", "<p>PUSX</p>",
                                "<p>CIJN</p>", "<p>ZOPB</p>"],
                    solution_en: "<p>6.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417125.png\" alt=\"rId15\" width=\"135\" height=\"97\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417257.png\" alt=\"rId16\" width=\"135\"></p>",
                    solution_hi: "<p>6.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417125.png\" alt=\"rId15\" width=\"135\" height=\"97\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417257.png\" alt=\"rId16\" width=\"135\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group? <br>(Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>7. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है? <br>(ध्यान दें : असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [" RUX ", " KMP ", 
                                "<p>NQT</p>", "<p>BEH</p>"],
                    options_hi: ["<p>RUX</p>", "<p>KMP</p>",
                                "<p>NQT</p>", "<p>BEH</p>"],
                    solution_en: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417359.png\" alt=\"rId17\" width=\"100\" height=\"59\">&nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417472.png\" alt=\"rId18\" width=\"100\">&nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417724.png\" alt=\"rId19\" width=\"100\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417874.png\" alt=\"rId20\" width=\"100\"></p>",
                    solution_hi: "<p>7.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417359.png\" alt=\"rId17\" width=\"100\" height=\"59\">&nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417472.png\" alt=\"rId18\" width=\"100\">&nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417724.png\" alt=\"rId19\" width=\"100\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343417874.png\" alt=\"rId20\" width=\"100\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain language,<br>A &amp; B means A is the wife of B,<br>A + B means A is the mother of B,<br>A &times; B means A is the son of B,<br>A + B means A is the sister of B and<br>A @ B means A is the father of B.<br>If A @ B &amp; C @ D + K + F, how is C related to K?</p>\n<p>&nbsp;</p>",
                    question_hi: "<p>8. एक निश्चित भाषा में,<br>A &amp; B का अर्थ A, B की पत्नी है,<br>A + B का अर्थ A,B की माँ है,<br>A &times; B का अर्थ A, B का बेटा है,<br>A + B का अर्थ A, B की बहन है और<br>A @ B का अर्थ A, B का पिता है।<br>यदि A @ B &amp; C @ D + K + F है, तो C का K से क्या संबंध है?</p>\n<p>&nbsp;</p>",
                    options_en: [" Father  ", " Sister ", 
                                " Mother ", " Brother"],
                    options_hi: [" पिता", " बहन ",
                                " माँ ", " भाई"],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343418062.png\" alt=\"rId21\" width=\"198\" height=\"227\"><br>C is the father of K.</p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343418062.png\" alt=\"rId21\" width=\"198\" height=\"227\"><br>C, K का पिता है.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements. <br><strong>Statements:</strong> <br>A. No caps are hats. <br>B. Some hats are helmets. <br><strong>Conclusions:</strong> <br>I. All helmets are caps<br>II. Some helmets are hats</p>",
                    question_hi: "<p>9. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई सूचना सत्य है, भले ही यह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्णय कीजिए कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं।<br><strong>कथन:</strong><br>A. कोई कैप, हैट नहीं है।<br>B. कुछ हैट, हेलमेट हैं।<br><strong>निष्कर्षः</strong><br>I. सभी हेलमेट, कैप हैं।<br>II. कुछ हेलमेट, हैट हैं।</p>",
                    options_en: [" Only conclusion I follows ", " Only conclusion II follows ", 
                                " Both the conclusions follow ", " None of the conclusions follow. "],
                    options_hi: [" केवल निष्कर्ष। अनुसरण करता है। ", "<p>केवल निष्कर्ष II अनुसरण करता है।</p>",
                                " दोनों निष्कर्ष अनुसरण करते हैं।", " कोई भी निष्कर्ष अनुसरण नहीं करता है।"],
                    solution_en: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343418370.png\" alt=\"rId22\" width=\"405\" height=\"75\"><br>Only conclusion II follow.</p>",
                    solution_hi: "<p>9.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343418820.png\" alt=\"rId23\" width=\"359\" height=\"67\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?&nbsp;<br>39 &divide; 3 &ndash; 24 + 60 &times; 12 = ?</p>",
                    question_hi: "<p>10. यदि निम्नलिखित समीकरण में \'+\' और \'&ndash;\' को आपस में बदल दिया जाए तथा \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए, तो इस समीकरण में\' ?\' के स्थान पर क्या आएगा? <br>39 &divide; 3 &ndash; 24 + 60 &times; 12 = ?</p>",
                    options_en: ["<p>163</p>", "<p>86</p>", 
                                "<p>136</p>", "<p>190</p>"],
                    options_hi: ["<p>163</p>", "<p>86</p>",
                                "<p>136</p>", "<p>190</p>"],
                    solution_en: "<p>10.(c) <strong>Given</strong> :- 39 &divide;&nbsp;3 - 24 + 60 &times; 12<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get<br>39 &times; 3 + 24 - 60 &divide; 12<br>117 + 24 - 5<br>117 + 19 = 136</p>",
                    solution_hi: "<p>10.(c) <strong>दिया गया :</strong>- 39 &divide; 3 - 24 + 60 &times; 12<br>दिए गए निर्देश के अनुसार &lsquo;+&rsquo; और &lsquo;-&rsquo; तथा &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदलने पर हमें प्राप्त होता है<br>39 &times; 3 + 24 - 60 &divide; 12<br>117 + 24 - 5<br>117 + 19 = 136</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language, \'PARTY\' is coded as \'81\', \'PLACE\' is coded as \'38\'. What is the code for \'PRIME\' in that code language?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में, \'PARTY\' को \'81\' लिखा जाता है, \'PLACE\' को \'38\' लिखा जाता है। उसी कूट भाषा में \'PRIME\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>62</p>", "<p>65</p>", 
                                "<p>63</p>", "<p>64</p>"],
                    options_hi: ["<p>62</p>", "<p>65</p>",
                                "<p>63</p>", "<p>64</p>"],
                    solution_en: "<p>11.(a)<br><strong>Logic</strong> :- (Sum of the place value of letters) + 1 <br>PARTY :- (16 + 1 + 18 + 20 + 25) + 1 &rArr; (80) + 1 = 81&nbsp;<br>PLACE :- (16 + 12 + 1 + 3 + 5) + 1 &rArr; (37) + 1 = 38<br>Similarly,<br>PRIME :- (16 + 18 + 9 + 13 + 5) + 1 &rArr; (61) + 1 = 62</p>",
                    solution_hi: "<p>11.(a)<br><strong>तर्क</strong> :- (अक्षरों के स्थानीय मान का योग)+1<br>PARTY :- (16 + 1 + 18 + 20 + 25) + 1 &rArr; (80) + 1 = 81&nbsp;<br>PLACE :- (16 + 12 + 1 + 3 + 5) + 1 &rArr; (37) + 1 = 38<br>इसी प्रकार,<br>PRIME :- (16 + 18 + 9 + 13 + 5) + 1 &rArr; (61) + 1 = 62</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the option that is related to the third word in the same way as the second word is related to the first word.<br>Penguin : Chick : : Duck : ?</p>",
                    question_hi: "<p>12. निम्नलिखित विकल्पों में से उस विकल्प का चयन कीजिए जो तीसरे शब्द से ठीक उसी प्रकार संबंधित है, जिस प्रकार दूसरा शब्द पहले शब्द से संबंधित है।<br>पेंगुइन : चूजा :: बत्तख : ?</p>",
                    options_en: ["<p>Duckling</p>", "<p>Joey</p>", 
                                "<p>Cub</p>", "<p>Kitten</p>"],
                    options_hi: ["<p>बत्तखशाव</p>", "<p>बिलोटा</p>",
                                "<p>शावक</p>", "<p>कंगारू का बच्चा</p>"],
                    solution_en: "<p>12.(a)<br>As a baby penguin is called chick similarly a baby duck is called duckling.</p>",
                    solution_hi: "<p>12.(a)<br>जिस प्रकार पेंगुइन के बच्चे को चूजा कहा जाता है उसी प्रकार बत्तख के बच्चे को बत्तखशाव कहा जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13. In a certain code language,<br />‘P + Q’ means ‘P is the wife of Q’.<br />‘P − Q’ means ‘P is the brother of Q’.<br />‘P / Q’ means ‘P is the son of Q’.<br />‘P × Q’ means ‘P is the father of Q’.<br />If ‘V / K × T + M × O’, then how is K related to O ?",
                    question_hi: "13. एक निश्चित कूट भाषा में,<br />P + Q का अर्थ P, Q की पत्नी है।<br />P − Q का अर्थ P, Q का भाई है।<br />P / Q का अर्थ P, Q का बेटा है।<br />P × Q का अर्थ P, Q का पिता है।<br />यदि V / K × T + M × O है, तो K का O से क्या संबंध है?",
                    options_en: [" Father ", " Mother’s father ", 
                                " Mother", " Father’s mother"],
                    options_hi: [" पिता ", " मां के पिता ",
                                " मां ", " पिता की मां"],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343418994.png\" alt=\"rId24\" width=\"175\" height=\"158\"><br>K is the Mother&rsquo;s father of O.</p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343418994.png\" alt=\"rId24\" width=\"175\" height=\"158\"><br>K, O की माँ का पिता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Three of the following numbers are alike in a certain way and one is different. Pick the odd one out. <br><strong>(NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>14. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए। <br><strong>(नोट:</strong> गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [" 11 – 882", " 3 – 72 ", 
                                " 4 – 128", " 7 – 392"],
                    options_hi: [" 11 – 882", " 3 – 72 ",
                                " 4 – 128", " 7 – 392"],
                    solution_en: "<p>14.(a) <strong>Logic</strong> : (1st number)&sup2; &times; 8 = 2nd number <br>3 &ndash; 72 : - 3&sup2; &times; 8 = 9 &times; 8 = 72 <br>4 &ndash; 128 ;- 4&sup2; &times; 8 = 16 &times; 8 = 128 <br>7 &ndash; 392 :-&nbsp;&sup2; &times; 8 = 49 &times; 8 = 392<br>But<br>11 - 882 :- 11&sup2; &times; 8 = 121 &times; 8 = 968 (<math display=\"inline\"><mo>&#8800;</mo></math>882)</p>",
                    solution_hi: "<p>14.(a) <strong>तर्क</strong> : (पहली संख्या)&sup2; &times; 8 = दूसरी संख्या <br>3 &ndash; 72 : - 3&sup2; &times; 8 = 9 &times; 8 = 72 <br>4 &ndash; 128 ;- 4&sup2; &times; 8 = 16 &times; 8 = 128 <br>7 &ndash; 392 :- 7&sup2; &times; 8 = 49 &times; 8 = 392<br>लेकिन<br>11 - 882 :- 11&sup2; &times; 8 = 121 &times; 8 = 968 (<math display=\"inline\"><mo>&#8800;</mo></math>882)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the figure that will replace the question mark (?) in the following figure series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419169.png\" alt=\"rId25\" width=\"400\" height=\"77\"></p>",
                    question_hi: "<p>15. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जिसे दी गई आकृति श्रृंखला में प्रश्न चिह्न (?) के स्थान पर रखा जा सकता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419169.png\" alt=\"rId25\" width=\"400\" height=\"77\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419334.png\" alt=\"rId26\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419490.png\" alt=\"rId27\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419608.png\" alt=\"rId28\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419798.png\" alt=\"rId29\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419334.png\" alt=\"rId26\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419490.png\" alt=\"rId27\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419608.png\" alt=\"rId28\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419798.png\" alt=\"rId29\" width=\"90\"></p>"],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419334.png\" alt=\"rId26\" width=\"90\"></p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419334.png\" alt=\"rId26\" width=\"90\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In a certain code language, &lsquo;MAN&rsquo; is coded as &lsquo;15316&rsquo; and &lsquo;SHE&rsquo; is coded as &lsquo;21107&rsquo;. How will &lsquo;OUT&rsquo; be coded in that language ?</p>",
                    question_hi: "<p>16. एक निश्चित कूट भाषा में, \'MAN\' को \'15316\' के रूप में कूटबद्ध किया जाता है और \'SHE\' को \'21107\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'OUT\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>152120</p>", "<p>172121</p>", 
                                "<p>172322</p>", "<p>152222</p>"],
                    options_hi: ["<p>152120</p>", "<p>172121</p>",
                                "<p>172322</p>", "<p>152222</p>"],
                    solution_en: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419932.png\" alt=\"rId30\" width=\"96\" height=\"92\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420032.png\" alt=\"rId31\" width=\"95\" height=\"95\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420130.png\" alt=\"rId32\" width=\"98\" height=\"92\"></p>",
                    solution_hi: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343419932.png\" alt=\"rId30\" width=\"96\" height=\"92\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420032.png\" alt=\"rId31\" width=\"93\" height=\"93\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420130.png\" alt=\"rId32\" width=\"93\" height=\"87\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the correct combination of mathematical signs that can sequentially replace the * signs and balance the given equation. <br>12 * 3 * 4 = 6 * 8 * 8</p>",
                    question_hi: "<p>17. गणितीय चिह्नों के उस सही संयोजन का चयन कीजिए जो क्रमिक रूप से * चिह्नों के स्थान पर आ सकते हैं और दिए गए समीकरण को संतुलित कर सकते हैं।<br>12 * 3 * 4 = 6 * 8 * 8</p>",
                    options_en: ["<p>&times;, &minus;, &times;, +</p>", "<p>&times;, +, &minus;, &times;</p>", 
                                "<p>&times;, +, &times;, &minus;</p>", "<p>+, &times;, &minus;, + </p>"],
                    options_hi: ["<p>&times;, &minus;, &times;, +</p>", "<p>&times;, +, &minus;, &times;</p>",
                                "<p>&times;, +, &times;, &minus;</p>", "<p>+, &times;, &minus;, +</p>"],
                    solution_en: "<p>17.(c) <strong>Given</strong> :- 12 * 3 * 4 = 6 * 8 * 8<br>After going through all the options, option c is satisfied.<br>12 &times; 3 + 4 = 6 &times; 8 - 8<br>40 = 40<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>17.(c) <strong>दिया गया :</strong>- 12 * 3 * 4 = 6 * 8 * 8<br>सभी विकल्पों की जांच करने पर , विकल्प c संतुष्ट करता है।<br>12 &times; 3 + 4 = 6 &times; 8 - 8<br>40 = 40<br>L.H.S. = R.H.S.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Arrange the given words in the sequence in which they occur in the dictionary.<br>1. Change<br>2. Chance<br>3. Channel<br>4. Chant<br>5. Chancellor</p>",
                    question_hi: "<p>18. दिए गए शब्दों को उसी क्रम में व्यवस्थित कीजिए जिस क्रम में वे अंग्रेजी शब्दकोश में आते हैं।<br>1. Change<br>2. Chance<br>3. Channel<br>4. Chant<br>5. Chancellor</p>",
                    options_en: ["<p>2, 5, 1, 4, 3</p>", "<p>2, 3, 1, 4, 5</p>", 
                                "<p>2, 3, 1, 5, 4</p>", "<p>2, 5, 1, 3, 4</p>"],
                    options_hi: ["<p>2, 5, 1, 4, 3</p>", "<p>2, 3, 1, 4, 5</p>",
                                "<p>2, 3, 1, 5, 4</p>", "<p>2, 5, 1, 3, 4</p>"],
                    solution_en: "<p>18.(d) The correct order is:<br>Chance(2) <math display=\"inline\"><mo>&#8594;</mo></math> Chancellor(5) &rarr; Change(1) &rarr; Channel(3) &rarr; Chant(4)</p>",
                    solution_hi: "<p>18.(d) सही क्रम है:<br>Chance(2) <math display=\"inline\"><mo>&#8594;</mo></math> Chancellor(5) &rarr; Change(1) &rarr; Channel(3) &rarr; Chant(4)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "19. Select the option that represents the letters that, when placed from left to right in the following blanks, will complete  the letter-series.<br />Q W_ R T Q _ F R _ Q W _ _ T _ _ H R T",
                    question_hi: "19. उस विकल्प का चयन करें जो उन अक्षरों का प्रतिनिधित्व करता है, जिन्हें निम्नलिखित रिक्त स्थानों में बाएं से दाएं रखे जाने पर अक्षर श्रृंखला पूरी हो जाएगी।<br />Q W_ R T Q _ F R _ Q W _ _ T _ _ H R T",
                    options_en: [" F W T G R Q X", " E X T G R Q X", 
                                " E W T G R Q W", " E W T H S Q W"],
                    options_hi: [" F W T G R Q X", " E X T G R Q X",
                                " E W T G R Q W", " E W T H S Q W"],
                    solution_en: "<p>19.(c)<br>Q W <span style=\"text-decoration: underline;\"><strong>E</strong></span> R T / Q <span style=\"text-decoration: underline;\"><strong>W</strong></span> F R <span style=\"text-decoration: underline;\"><strong>T</strong></span> / Q W <span style=\"text-decoration: underline;\"><strong>G R</strong></span> T / <span style=\"text-decoration: underline;\"><strong>Q W</strong></span> H R T</p>",
                    solution_hi: "<p>19.(c)<br>Q W <span style=\"text-decoration: underline;\"><strong>E</strong></span> R T / Q <span style=\"text-decoration: underline;\"><strong>W</strong></span> F R <span style=\"text-decoration: underline;\"><strong>T</strong></span> / Q W <span style=\"text-decoration: underline;\"><strong>G R</strong></span> T / <span style=\"text-decoration: underline;\"><strong>Q W</strong></span> H R T</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. The question contains pairs of words that are related to each other in a certain way. Three of the following four word pairs are alike as these have the same relationship and thus form a group. Which word pair is the one that DOES NOT belong to that group?<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>20. प्रश्न में ऐसे शब्द युग्म हैं जो एक निश्चित तरीके से एक-दूसरे से संबंधित हैं। निम्नलिखित चार शब्द युग्मों में से तीन शब्द युग्म एक समान हैं क्योंकि इनमें संबंध समान है और इस प्रकार वे एक समूह बनाते हैं। निम्नलिखित में से कौन-सा शब्द युग्म उस समूह से संबंधित नहीं है?<br>(शब्दों को अर्थपूर्ण अंग्रेजी/हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों / व्यंजनों / स्वरों की संख्या के आधार पर एक-दूसरे से संबंधित नहीं होने चाहिए।)</p>",
                    options_en: ["<p>Tired - exhausted</p>", "<p>Loud - noisy</p>", 
                                "<p>Close - open</p>", "<p>Honest - truthful</p>"],
                    options_hi: ["<p>थका - थका माँदा</p>", "<p>ऊँचा स्वर - शोरगुल</p>",
                                "<p>बंद - खुला</p>", "<p>ईमानदार - सत्यनिष्ठ</p>"],
                    solution_en: "<p>20.(c) words (Tired - exhausted , Loud - noisy, Honest - truthful) are synonym each other but (Close - open) is an antonym.</p>",
                    solution_hi: "<p>20.(c) <br>शब्द (थका - थका माँदा, ऊँचा स्वर - शोरगुल, ईमानदार - सत्यनिष्ठ) एक दूसरे के पर्यायवाची हैं लेकिन (बंद - खुला) एक दूसरे का विलोम है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. What should come in place of the question mark (?) in the given series based on the English alphabetical order? <br>TKU, UNP, VQK, WTF, ?</p>",
                    question_hi: "<p>21. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई शृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए? <br>TKU, UNP, VQK, WTF, ?</p>",
                    options_en: ["<p>XXA</p>", "<p>WAW</p>", 
                                "<p>XWA</p>", "<p>YXB</p>"],
                    options_hi: ["<p>XXA</p>", "<p>WAW</p>",
                                "<p>XWA</p>", "<p>YXB</p>"],
                    solution_en: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420287.png\" alt=\"rId33\" width=\"416\" height=\"104\"></p>",
                    solution_hi: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420287.png\" alt=\"rId33\" width=\"416\" height=\"104\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. In a certain code language, \'clean the room\' is written as \'rt yp fs\' and \'room is good\' is coded as \'sw rt la\'. How is \'room\' coded in the given language</p>",
                    question_hi: "<p>22. एक निश्चित कूट भाषा में \'clean the room\' को \'rt yp fs\' लिखा जाता है और \'room is good\' को \'sw rt la\' लिखा जाता है। उसी कूट भाषा में \'room\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>yp</p>", "<p>sw</p>", 
                                "<p>rt</p>", "<p>fs</p>"],
                    options_hi: ["<p>yp</p>", "<p>sw</p>",
                                "<p>rt</p>", "<p>fs</p>"],
                    solution_en: "<p>22.(c) clean the room :- rt yp fs&hellip;&hellip;(i)<br>room is good :- sw rt la&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;room&rsquo; and &lsquo;rt&rsquo; are common. The code of &lsquo;room&rsquo; = &lsquo;rt&rsquo;.</p>",
                    solution_hi: "<p>22.(c) clean the room :- rt yp fs&hellip;&hellip;(i)<br>room is good :- sw rt la&hellip;&hellip;.(ii)<br>(i) और (ii) से &lsquo;room&rsquo; और &lsquo;rt&rsquo; उभयनिष्ठ हैं। &lsquo;room&rsquo; का कोड = &lsquo;rt&rsquo; है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option that is related to the fifth letter cluster in the same way as the fourth letter cluster is related to the third letter cluster and the second letter cluster is related to the first letter cluster.<br>NICE : MOHJBDDF :: KIND : JLHJMOCE :: LAZY : ?</p>",
                    question_hi: "<p>23. निम्नलिखित विकल्पों में से उस विकल्प का चयन कीजिए जो पांचवें अक्षर-समूह से ठीक उसी प्रकार संबंधित है जिस प्रकार चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है, और दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है।<br>NICE : MOHJBDDF :: KIND : JLHJMOCE :: LAZY : ?</p>",
                    options_en: ["<p>KMZBZXAY</p>", "<p>&Mu;&Kappa;&Beta;&Zeta;&Alpha;YZX</p>", 
                                "<p>&Alpha;&Upsilon;&Zeta;XMKBZ</p>", "<p>KMZBYAXZ</p>"],
                    options_hi: ["<p>KMZBZXAY</p>", "<p>&Mu;&Kappa;&Beta;&Zeta;&Alpha;YZX</p>",
                                "<p>&Alpha;&Upsilon;&Zeta;XMKBZ</p>", "<p>KMZBYAXZ</p>"],
                    solution_en: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420415.png\" alt=\"rId34\" width=\"350\" height=\"138\"> ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420507.png\" alt=\"rId35\" width=\"356\" height=\"125\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420643.png\" alt=\"rId36\" width=\"350\"></p>",
                    solution_hi: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420415.png\" alt=\"rId34\" width=\"350\" height=\"138\">&nbsp; ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420507.png\" alt=\"rId35\" width=\"356\" height=\"125\"><br>इसी प्रकार, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420643.png\" alt=\"rId36\" width=\"350\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number result(s) in the second number. Similarly, certain mathematical operation(s) on the second number result(s) in the third number and so on. Which of the given options follows the same set of operations as in the given sets?<br><strong>(NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>14 &ndash; 196 &ndash; 186 &ndash; 172; 15 &ndash; 225 &ndash; 215 &ndash; 200</p>",
                    question_hi: "<p>24. नीचे संख्याओं के दो समुच्चय दिए गए हैं। संख्याओं के प्रत्येक समुच्चय में, पहली संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर दूसरी संख्या प्राप्त होती है। इसी प्रकार, दूसरी संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर तीसरी संख्या प्राप्त होती है और इसी तरह आगे भी होती है। दिए गए विकल्पों में से कौन-सा विकल्प दिए गए समुच्चयों की तरह संक्रियाओं के समान समुच्चय का अनुसरण करता है? <br><strong>(नोट</strong> : संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 लीजिए - 13 पर की जाने वाली संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि केवल 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना, तथा 1 और 3 पर गणितीय संक्रिया करने की अनुमति नहीं है।)<br>14 &ndash; 196 &ndash; 186 &ndash; 172; 15 &ndash; 225 &ndash; 215 &ndash; 200</p>",
                    options_en: ["<p>9 &ndash; 81 &ndash; 71 &ndash; 62</p>", "<p>3 &ndash; 9 &ndash; 19 &ndash; 14</p>", 
                                "<p>8 &ndash; 64 &ndash; 54 &ndash; 40</p>", "<p>6 &ndash; 36 &ndash; 26 &ndash; 22</p>"],
                    options_hi: ["<p>9 &ndash; 81 &ndash; 71 &ndash; 62</p>", "<p>3 &ndash; 9 &ndash; 19 &ndash; 14</p>",
                                "<p>8 &ndash; 64 &ndash; 54 &ndash; 40</p>", "<p>6 &ndash; 36 &ndash; 26 &ndash; 22</p>"],
                    solution_en: "<p>24.(a) <strong>Logic:</strong> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mi>st</mi></msup><mi>no</mi><mo>.</mo></math>)<sup>2</sup> = 2<sup>nd </sup>no., 2<sup>nd </sup>no. - 10 = 3<sup>rd </sup>no., 3<sup>rd </sup>no. - 1<sup>st </sup>no. = 4<sup>th</sup> no.<br>14 &ndash; 196 &ndash; 186 &ndash; 172:- (14)<sup>2</sup> = 196, 196 - 10 = 186, 186 - 14 = 172<br>15 &ndash; 225 &ndash; 215 &ndash; 200:- (15)<sup>2</sup> = 225, 225 - 10 = 215, 215 - 15 = 200<br>Similarly<br>9 &ndash; 81 &ndash; 71 &ndash; 62 :- (9)<sup>2</sup> = 81, 81 - 10 = 71, 71 - 9 = 62</p>",
                    solution_hi: "<p>24.(a) <br><strong>तर्क:</strong> (पहली संख्या)<sup>2</sup> = दूसरी संख्या , दूसरी संख्या - 10 = तीसरी संख्या , तीसरी संख्या - पहली संख्या = चौथी संख्या <br>14 &ndash; 196 &ndash; 186 &ndash; 172:- (14)<sup>2</sup> = 196, 196 - 10 = 186, 186 - 14 = 172<br>15 &ndash; 225 &ndash; 215 &ndash; 200:- (15)<sup>2</sup> = 225, 225 - 10 = 215, 215 - 15 = 200<br>उसी प्रकार<br>9 &ndash; 81 &ndash; 71 &ndash; 62 :- (9)<sup>2</sup> = 81, 81 - 10 = 71, 71 - 9 = 62</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the correct mirror image of the given figure when the mirror MN is placed to the right side of the figure<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420846.png\" alt=\"rId37\" width=\"115\" height=\"129\"></p>",
                    question_hi: "<p>25. दी गई आकृति के उस सही दर्पण प्रतिबिंब का चयन कीजिए, जो दर्पण MN को उस आकृति के दाईं ओर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420846.png\" alt=\"rId37\" width=\"115\" height=\"129\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420944.png\" alt=\"rId38\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421035.png\" alt=\"rId39\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421128.png\" alt=\"rId40\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421216.png\" alt=\"rId41\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420944.png\" alt=\"rId38\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421035.png\" alt=\"rId39\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421128.png\" alt=\"rId40\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421216.png\" alt=\"rId41\" width=\"90\"></p>"],
                    solution_en: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420944.png\" alt=\"rId38\" width=\"90\"></p>",
                    solution_hi: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343420944.png\" alt=\"rId38\" width=\"90\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. What is the study of the production and propagation of sound waves called?</p>",
                    question_hi: "<p>26. ध्वनि तरंगों के उत्पादन और प्रसार के अध्ययन को क्या कहा जाता है?</p>",
                    options_en: ["<p>Optics</p>", "<p>Photonics</p>", 
                                "<p>Astrophysics</p>", "<p>Acoustics</p>"],
                    options_hi: ["<p>ऑप्टिक्स</p>", "<p>फोटोनिक्स</p>",
                                "<p>खगोल भौतिकी</p>", "<p>ध्वनिकी</p>"],
                    solution_en: "<p>26.(d) <strong>Acoustics. Optics</strong> is the branch of physical science that deals with the properties and phenomena of both visible and invisible light and with vision. <strong>Photonics</strong> is the physical science of light waves. <strong>Astrophysics</strong> is a branch of space science that applies the laws of physics and chemistry to seek to understand the universe and our place in it.</p>",
                    solution_hi: "<p>26.(d) <strong>श्रवणगम्यता (Acoustics)</strong> । <strong>प्रकाशिकी (Optics)</strong> भौतिक विज्ञान की वह शाखा है जो दृश्य और अदृश्य दोनों प्रकार के प्रकाश और दृष्टि के गुणों और घटनाओं से संबंधित है। <strong>फोटोनिक्स</strong> प्रकाश तरंगों का भौतिक विज्ञान है। खगोल भौतिकी (एस्ट्रोफिजिक्स) <strong>अंतरिक्ष विज्ञान</strong> की एक शाखा है जो ब्रह्मांड और उसमें हमारे स्थान को समझने के लिए भौतिकी और रसायन विज्ञान के नियमों को लागू करती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which Article of the Indian Constitution empowers a High Court to issue writs for the enforcement of the Fundamental Rights of the citizens?</p>",
                    question_hi: "<p>27. भारतीय संविधान का कौन सा अनुच्छेद एक उच्च न्यायालय को नागरिकों के मौलिक अधिकारों के प्रवर्तन के लिए रिट जारी करने का अधिकार देता है?</p>",
                    options_en: ["<p>Article 226</p>", "<p>Article 235</p>", 
                                "<p>Article 230</p>", "<p>Article 242</p>"],
                    options_hi: ["<p>अनुच्छेद 226</p>", "<p>अनुच्छेद 235</p>",
                                "<p>अनुच्छेद 230</p>", "<p>अनुच्छेद 242</p>"],
                    solution_en: "<p>27.(a) <strong>Article 226</strong> &rarr; Power of High Courts to issue certain writs. <strong>Article 235</strong> &rarr; Control over subordinate courts. <strong>Article 230</strong> &rarr; Extension of jurisdiction of High Courts to Union territories.</p>",
                    solution_hi: "<p>27.(a) <strong>अनुच्छेद 226</strong> - कुछ रिट जारी करने के लिए उच्च न्यायालयों की शक्ति। <strong>अनुच्छेद 235-</strong> अधीनस्थ न्यायालयों पर नियंत्रण। <strong>अनुच्छेद 230</strong> - उच्च न्यायालयों के अधिकार क्षेत्र का केंद्र शासित प्रदेशों तक विस्तार।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. With which of the following states is the folk theatre form &lsquo;Ranmale&rsquo; traditionally associated?</p>",
                    question_hi: "<p>28. लोक नाट्य रूप \'रणमाले\' पारंपरिक रूप से निम्नलिखित में से किस राज्य से जुड़ा है?</p>",
                    options_en: ["<p>West Bengal</p>", "<p>Goa</p>", 
                                "<p>Jharkhand</p>", "<p>Chhattisgarh</p>"],
                    options_hi: ["<p>पश्चिम बंगाल</p>", "<p>गोवा</p>",
                                "<p>झारखंड</p>", "<p>छत्तीसगढ़</p>"],
                    solution_en: "<p>28.(b) The folk theatre form &lsquo;Ranmale&rsquo; traditionally associated with <strong>Goa.</strong> Ranmale is a ritualistic and folk theatre form based on mythological stories from the popular Indian epics, the Ramayana, and the Mahabharata. Zagor is a form of a traditional folk drama performed in many villages in Goa.</p>",
                    solution_hi: "<p>28.(b) लोक नाट्य रूप \'रणमाले\' पारंपरिक रूप से <strong>गोवा</strong> से जुड़ा हुआ है। रणमाले लोकप्रिय भारतीय महाकाव्यों, रामायण और महाभारत की पौराणिक कहानियों पर आधारित एक अनुष्ठानिक और लोक नाट्य रूप है। ज़ागोर गोवा के कई गाँवों में किए जाने वाले पारंपरिक लोक नाटक का एक रूप है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following rivers flows into the Arabian Sea?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन सी नदी अरब सागर में गिरती है?</p>",
                    options_en: ["<p>Krishna</p>", "<p>Narmada</p>", 
                                "<p>Kaveri</p>", "<p>Godavari</p>"],
                    options_hi: ["<p>कृष्णा</p>", "<p>नर्मदा</p>",
                                "<p>कावेरी</p>", "<p>गोदावरी</p>"],
                    solution_en: "<p>29.(b) The rivers that flow into the Arabian Sea are Tapi, <strong>Narmada,</strong> Sindhu, Purna, and Sabarmati. The Indian rivers that flow into the Bay of Bengal include Krishna, Kaveri, Ganga, and Godavari. Narmada river (Rawa river) originated from the Maikala range near Amarkantak. It flows through Gujarat, Chhattisgarh, Madhya Pradesh, and Maharashtra. Tributaries of Narmada River are Kolar River, Shakkar River, Dudhi River, Tawa River, and Hiran River.<br>.</p>",
                    solution_hi: "<p>29.(b) अरब सागर में गिरने वाली नदियाँ तापी, <strong>नर्मदा,</strong> सिंधु, पूर्णा और साबरमती हैं। बंगाल की खाड़ी में गिरने वाली भारतीय नदियों में कृष्णा, कावेरी, गंगा और गोदावरी शामिल हैं। नर्मदा नदी (रावा नदी) का उद्गम अमरकंटक के निकट मैकाल श्रेणी से हुआ है। यह गुजरात, छत्तीसगढ़, मध्य प्रदेश और महाराष्ट्र से होकर बहती है। नर्मदा नदी की सहायक नदियाँ कोलार नदी, शक्कर नदी, दुधी नदी, तवा नदी और हिरन नदी हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Deduction of depreciation from Gross National Product is known as:</p>",
                    question_hi: "<p>30. सकल राष्ट्रीय उत्पाद से मूल्यह्रास की कटौती के रूप में जाना जाता है</p>",
                    options_en: ["<p>Net National Product</p>", "<p>Gross Domestic Product</p>", 
                                "<p>National Income</p>", "<p>Corporate Tax</p>"],
                    options_hi: ["<p>सकल राष्ट्रीय उत्पाद</p>", "<p>सकल घरेलु उत्पाद</p>",
                                "<p>राष्ट्रीय आय</p>", "<p>निगमित कर</p>"],
                    solution_en: "<p>30.(a) Deduction of depreciation from Gross National Product is known as <strong>Net National Product. Gross Domestic Product (GDP)</strong> is the standard measure of the value added created through the production of goods and services in a country during a certain period.</p>",
                    solution_hi: "<p>30.(a) <strong>सकल राष्ट्रीय उत्पाद</strong> से मूल्यह्रास की कटौती को शुद्ध राष्ट्रीय उत्पाद के रूप में जाना जाता है। <strong>सकल घरेलू अवधि</strong> (जीडीपी) एक निश्चित अवधि के दौरान किसी देश में वस्तुओं और सेवाओं के उत्पादन के माध्यम से बनाए गए मूल्य वर्धित मूल्य का मानक उपाय है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. The dance form &lsquo;Chharhi&rsquo; has originated from the state of :</p>",
                    question_hi: "<p>31. नृत्य रूप \'छर्ही\' की उत्पत्ति किस राज्य से हुई है?</p>",
                    options_en: ["<p>Bihar</p>", "<p>West Bengal</p>", 
                                "<p>Himachal Pradesh</p>", "<p>Mizoram</p>"],
                    options_hi: ["<p>बिहार</p>", "<p>पश्चिम बंगाल</p>",
                                "<p>हिमाचल प्रदेश</p>", "<p>मिजोरम</p>"],
                    solution_en: "<p>31.(c) The dance form <strong>&lsquo;Chharhi&rsquo;</strong> originated from the state of Himachal Pradesh. More dances of Himachal Pradesh are Jhora, Jhali, Chharhi, Dhaman, Chhapeli, Mahasu, Nati, Dangi. <strong>Bihar</strong> &rarr; Bidesia, Jat-Jatin, Jhijhiya, Kajari, Fagua.</p>",
                    solution_hi: "<p>31.(c) नृत्य रूप <strong>\'छर्ही\'</strong> की उत्पत्ति हिमाचल प्रदेश राज्य से हुई है। हिमाचल प्रदेश के अधिक नृत्य झोरा, झाली, छाढ़ी, धमन, छपेली, महासू, नाटी, डांगी हैं। <strong>बिहार&rarr;</strong> बिदेसिया, जाट-जतिन, झिझिया, कजरी, फगुआ आदि ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. The first Secretary General of the United Nations belonged to which of the following countries?</p>",
                    question_hi: "<p>32. संयुक्त राष्ट्र संघ का प्रथम महासचिव निम्नलिखित में से किस देश के निवासी थे?</p>",
                    options_en: ["<p>Sweden</p>", "<p>Portugal</p>", 
                                "<p>Finland</p>", "<p>Norway</p>"],
                    options_hi: ["<p>स्वीडन</p>", "<p>पुर्तगाल</p>",
                                "<p>फिनलैंड</p>", "<p>नॉर्वे</p>"],
                    solution_en: "<p>32.(d) On 1 February 1946, Trygve Lie became the first Secretary General of the United Nation who belonged to Norway. UN established - <strong>24 October 1945</strong>. The United Nations (UN) has six principal organs. Five of them &mdash; the General Assembly, the Security Council, the Economic and Social Council, the Trusteeship Council and the Secretariat &mdash; are based at UN Headquarters in New York. The sixth, the International Court of Justice, is located at The Hague in the Netherlands.</p>",
                    solution_hi: "<p>32.(d) 1 फरवरी 1946 को, नॉर्वे के ट्रिगवे लाई संयुक्त राष्ट्र के पहले महासचिव बने। संयुक्त राष्ट्र की स्थापना - <strong>24 अक्टूबर 1945।</strong> संयुक्त राष्ट्र (UN) के छह प्रमुख अंग हैं। इनमें से पांच - महासभा, सुरक्षा परिषद, आर्थिक और सामाजिक परिषद, ट्रस्टीशिप परिषद और सचिवालय - न्यूयॉर्क में संयुक्त राष्ट्र मुख्यालय में स्थित हैं। छठा, अंतर्राष्ट्रीय न्यायालय, नीदरलैंड के हेग में स्थित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which state government has announced Mukhyamantri Awasiya Bhu-adhikar Yojana to provide free plots to families who do not own plots?</p>",
                    question_hi: "<p>33. किस राज्य सरकार ने मुफ्त भूखंड प्रदान करने के लिए मुख्यमंत्री आवास भू-अधिकार योजना की घोषणा की है?</p>",
                    options_en: ["<p>Rajasthan</p>", "<p>Uttar Pradesh</p>", 
                                "<p>Bihar</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>उत्तर प्रदेश</p>",
                                "<p>बिहार</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p>33.(d) <strong>Madhya Pradesh</strong> government has announced Mukhyamantri Awasiya Bhu-adhikar Yojana to provide free plots to families who do not own plots.The scheme will also open the way for the construction of houses under the Pradhan Mantri Awas Yojana. For getting a residential plot, applications have to be submitted online through the SAARA portal.</p>",
                    solution_hi: "<p>33.(d) <strong>मध्य प्रदेश</strong> सरकार ने उन परिवारों को मुफ्त भूखंड प्रदान करने के लिए मुख्यमंत्री आवास भू - अधिकार योजना की घोषणा की है जिनके पास भूखंड (plots) नहीं हैं। यह योजना प्रधानमंत्री आवास योजना के तहत घरों के निर्माण का रास्ता भी खोलेगी। आवासीय भूखंड प्राप्त करने के लिए, आवेदन SAARA पोर्टल के माध्यम से ऑनलाइन जमा करना होगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following carries blood from the heart to the kidneys?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन सी रक्त वाहिकाएं दिल से गुर्दे में रक्त का संचार करती है ?</p>",
                    options_en: ["<p>Renal vein</p>", "<p>Coronary artery</p>", 
                                "<p>Vena cava</p>", "<p>Renal artery</p>"],
                    options_hi: ["<p>गुर्दे की नस</p>", "<p>हृदय धमनी</p>",
                                "<p>वेना कावा</p>", "<p>गुर्दे की धमनी</p>"],
                    solution_en: "<p>34.(d) <strong>The renal arteries</strong> are large blood vessels that carry blood from heart to kidneys.The <strong>coronary arteries</strong> are those blood vessels,which transport oxygenated blood to the heart muscle. <strong>Vena cava</strong> are very large veins that bring deoxygenated blood to your heart. <strong>Renal Veins</strong> carry filtered blood from the kidneys to the posterior vena cava.</p>",
                    solution_hi: "<p>34.(d) <strong>गुर्दे</strong> <strong>की</strong> <strong>धमनियां</strong> बड़ी रक्त वाहिकाएं होती हैं जो हृदय से गुर्दे तक रक्त ले जाती हैं। <strong>कोरोनरी धमनियां</strong> वे रक्त वाहिकाएं होती हैं, जो ऑक्सीजन युक्त रक्त को हृदय की मांसपेशियों तक ले जाती हैं। <strong>वेना</strong> <strong>कावा </strong>बहुत बड़ी नसें होती हैं जो आपके हृदय में ऑक्सीजन रहित रक्त लाती हैं। <strong>गुर्दे की नसें</strong> गुर्दे से फ़िल्टर रक्त को पश्च वेना कावा (posterior vena cava) तक ले जाती हैं</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Who wrote the epic &lsquo;Buddhacharitam&rsquo;?</p>",
                    question_hi: "<p>35. \'बुद्धचरितम्\' महाकाव्य किसने लिखा है?</p>",
                    options_en: ["<p>Gautama Buddha</p>", "<p>Ashvaghosha</p>", 
                                "<p>Nagarjuna</p>", "<p>Hemchandra</p>"],
                    options_hi: ["<p>गौतम बुद्ध</p>", "<p>अश्वघोष:</p>",
                                "<p>नागार्जुन</p>", "<p>हेमचंद्र</p>"],
                    solution_en: "<p>35.(b) <strong>Ashvaghosha</strong> wrote the epic &lsquo;Buddhacharitam&rsquo;. Books by <strong>Nagarjuna</strong> &rarr;\' Rasaratnakar\' and \'Arogyamanjari. <strong>\'Siddham Shabd Anushasan\'</strong> is a grammar book written by <strong>Acharya Hemchandra.</strong></p>",
                    solution_hi: "<p>35.(b) <strong>अश्वघोष</strong> ने \'बुद्धचरितम्\' महाकाव्य लिखा। <strong>नागार्जुन</strong> की पुस्तकें - \'रसरत्नकर\' और \'आरोग्यमंजरी\'। <strong>\'सिद्धेम शब्दानुशासन\'</strong> आचार्य हेमचंद्र द्वारा लिखित व्याकरण ग्रंथ है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. The correct sequence of metallic character of the given elements is:</p>",
                    question_hi: "<p>36. दिए गए तत्वों के धात्विक लक्षणों का सही क्रम क्या है?</p>",
                    options_en: ["<p>Li &gt; Na &gt; K &gt; Cs &gt; Rb</p>", "<p>Na &lt; K &lt; Li &lt; Rb &lt; Cs</p>", 
                                "<p>Li &lt; Na &lt; K &lt; Rb <cs< body=\"\"></cs<></p>", "<p>Li &gt; Na &gt; K&gt;Rb &gt; Cs</p>"],
                    options_hi: ["<p>Li &gt; Na &gt; K &gt; Cs &gt; Rb</p>", "<p>Na &lt; K &lt; Li &lt; Rb &lt; Cs</p>",
                                "<p>Li &lt; Na &lt; K &lt; Rb &lt; Cs</p>", "<p>Li &gt; Na &gt; K&gt; Rb &gt; Cs</p>"],
                    solution_en: "<p>36.(c) The correct sequence of metallic characters of the given elements is Li &lt; Na &lt; K &lt; Rb <cs. metallic=\"\" character=\"\" can=\"\" be=\"\" measured=\"\" by=\"\" the=\"\" ease=\"\" of=\"\" an=\"\" element=\"\" to=\"\" give=\"\" away=\"\" its=\"\" valence=\"\" electrons.=\"\" it=\"\" depends=\"\" on=\"\" electron\'s=\"\" i)=\"\" effective=\"\" nuclear=\"\" charge=\"\" (enc)=\"\" and=\"\" ii)=\"\" distance=\"\" from=\"\" nucleus.<=\"\" body=\"\"></cs.></p>",
                    solution_hi: "<p>36.(c) दिए गए तत्वों के धात्विक लक्षणों का सही क्रम Li &lt; Na &lt; K &lt; Rb &lt; Cs है। धात्विक चरित्र को किसी तत्व की आसानी से मापा जा सकता है ताकि उसके संयोजक इलेक्ट्रॉनों को छोड़ दिया जा सके। यह संयोजकता इलेक्ट्रॉन के i) प्रभावी नाभिकीय आवेश (ENC) और ii) नाभिक से दूरी पर निर्भर करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. When did India adopt its Biological Diversity Act?</p>",
                    question_hi: "<p>37. भारत ने अपना जैविक विविधता अधिनियम कब अपनाया?</p>",
                    options_en: ["<p>2002</p>", "<p>2008</p>", 
                                "<p>2001</p>", "<p>2005</p>"],
                    options_hi: ["<p>2002</p>", "<p>2008</p>",
                                "<p>2001</p>", "<p>2005</p>"],
                    solution_en: "<p>37.(a) India, a key mega - biodiversity country, adopted the <strong>Biological Diversity Act 2002</strong> to halt and reverse effects of diversity loss.The Biological Diversity (Amendment) Bill, 2021 was introduced in Parliament to Amend Biological Diversity Act 2002.</p>",
                    solution_hi: "<p>37.(a) भारत, एक प्रमुख मेगा-जैव विविधता देश, ने विविधता हानि के प्रभावों को रोकने और उलटने के लिए जैविक <strong>विविधता अधिनियम 2002</strong> को अपनाया। जैविक विविधता (संशोधन) विधेयक, 2021 को जैविक विविधता अधिनियम 2002 में संशोधन के लिए संसद में पेश किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. What is India\'s overall rank in the World Bank&rsquo;s Logistics Performance Index (LPI) 2023 ?</p>",
                    question_hi: "<p>38. वर्ल्ड बैंक के लॉजिस्टिक्स परफॉर्मेंस इंडेक्स (LPI) 2023 में भारत की कुल रैंक क्या है?</p>",
                    options_en: ["<p>22nd</p>", "<p>38th</p>", 
                                "<p>44th</p>", "<p>50th</p>"],
                    options_hi: ["<p>22वीं</p>", "<p>38वीं</p>",
                                "<p>44वीं</p>", "<p>50वीं</p>"],
                    solution_en: "<p>38.(b) <strong>38th.</strong> India climbed to the 38th position in the overall Logistics Performance Index due to policy reforms, port modernization, and technology adoption.</p>",
                    solution_hi: "<p>38.(b) <strong>38वीं</strong> । भारत नीतिगत सुधारों, बंदरगाह आधुनिकीकरण और तकनीकी अपनाने के कारण लॉजिस्टिक्स परफॉर्मेंस इंडेक्स में 38वें स्थान पर पहुंच गया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which dynasty ruled India between 320 AD and 550 AD?</p>",
                    question_hi: "<p>39. किस राजवंश ने 320 AD और 550 AD के बीच भारत पर शासन किया?</p>",
                    options_en: ["<p>Mauryan Dynasty</p>", "<p>Hoysala Dynasty</p>", 
                                "<p>Magadha Dynasty</p>", "<p>Gupta Dynasty</p>"],
                    options_hi: ["<p>मौर्य राजवंश</p>", "<p>होयसल राजवंश</p>",
                                "<p>मगध राजवंश</p>", "<p>गुप्त राजवंश</p>"],
                    solution_en: "<p>39.(d) <strong>Gupta Dynasty</strong> ruled India between 320 AD and 550 AD. <strong>Mauryan Empire</strong> (322-185 BCE). Hoysala Dynasty (1110 A.D-1326 A.D) . Magadha Dynasty (684 BCE to 320 BCE).</p>",
                    solution_hi: "<p>39.(d) <strong>गुप्त राजवंश</strong> ने 320 ईस्वी और 550 ईस्वी के बीच भारत पर शासन किया। <strong>मौर्य साम्राज्य</strong> (322-185 ईसा पूर्व)। <strong>होयसल राजवंश</strong> (1110 ई.-1326 ई.) <strong>मगध राजवंश</strong> (684 ईसा पूर्व से 320 ईसा पूर्व)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which of the following is a dance form of West Bengal where there is a confluence of dancing, singing, drama and recital?</p>",
                    question_hi: "<p>40. निम्नलिखित में से कौन सा पश्चिम बंगाल का एक नृत्य रूप है जहां नृत्य, गायन, नाटक और वादन का संगम होता है?</p>",
                    options_en: ["<p>Bhavai</p>", "<p>Alkap</p>", 
                                "<p>Tippani</p>", "<p>Hudo</p>"],
                    options_hi: ["<p>भवई</p>", "<p>अलकप</p>",
                                "<p>टिपानी</p>", "<p>हूडो</p>"],
                    solution_en: "<p>40.(b) <strong>Alkap</strong> is a famous dancing, singing and drama event of West Bengal. It is used to portray the mythological stories, injustice in the societies and much more in modern times. Bhavai (Rajasthan), Tippani (Gujarat). Hudo (Gujarat).</p>",
                    solution_hi: "<p>40.(b) <strong>अल्कप</strong> पश्चिम बंगाल का एक प्रसिद्ध नृत्य, गायन और नाटक कार्यक्रम है। इसका उपयोग पौराणिक कहानियों, समाज में अन्याय और आधुनिक समय में बहुत कुछ चित्रित करने के लिए किया जाता है। <strong>भवई</strong> (राजस्थान), <strong>तिप्पानी</strong> (गुजरात)। <strong>हूडो</strong> (गुजरात)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Who among the following was the first Finance Minister of Independent India?</p>",
                    question_hi: "<p>41. निम्नलिखित में से कौन स्वतंत्र भारत के पहले वित्त मंत्री थे?</p>",
                    options_en: ["<p>KC Neogy</p>", "<p>CD Deshmukh</p>", 
                                "<p>RK Shanmukham Chetty</p>", "<p>Liaquat Ali Khan</p>"],
                    options_hi: ["<p>केसी नियोगी</p>", "<p>सीडी देशमुख</p>",
                                "<p>आरके षणमुखम चेट्टी</p>", "<p>लियाकत अली खान</p>"],
                    solution_en: "<p>41.(c) <strong>RK Shanmukham Chetty.</strong> C.D. Deshmukh - First Indian to be appointed the Governor of the Reserve Bank of India. K.C. Neogy - Chairman of the first Finance Commission of India. The 16th Finance Commission delegation is led by Chairman Dr. Arvind Panagariya.</p>",
                    solution_hi: "<p>41.(c) <strong>आरके षणमुखम चेट्टी।</strong> सी.डी. देशमुख- भारतीय रिजर्व बैंक के गवर्नर नियुक्त होने वाले पहले भारतीय। के. सी. नियोगी - भारत के पहले वित्त आयोग के अध्यक्ष। 16वें वित्त आयोग के प्रतिनिधिमंडल का नेतृत्व अध्यक्ष डॉ. अरविंद पनगढ़िया कर रहे हैं ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following statements about plant tissues is INCORRECT?</p>",
                    question_hi: "<p>42. पौधे के ऊतकों के बारे में निम्नलिखित में से कौन सा कथन गलत है?</p>",
                    options_en: ["<p>Xylem and Phloem are complex tissues.</p>", "<p>Phloem transports food from leaves to other parts of the plant.</p>", 
                                "<p>Xylem transports water and minerals.</p>", "<p>Materials can move in both direction in xylem.</p>"],
                    options_hi: ["<p>जाइलम और फ्लोएम जटिल ऊतक हैं।</p>", "<p>फ्लोएम भोजन को पत्तियों से पौधे के अन्य भागों तक पहुंचाता है।</p>",
                                "<p>जाइलम पानी और खनिजों का परिवहन करता है</p>", "<p>जाइलम में पदार्थ दोनों दिशाओं में गति कर सकते हैं</p>"],
                    solution_en: "<p>42.(d) Materials cannot move in both directions in Xylem.The function of <strong>Xylem</strong> is to transport water and nutrients from roots to stems and leaves. <strong>Phloem</strong> is the complex tissue, which acts as a transport system for soluble organic compounds(Foods) within vascular plants.</p>",
                    solution_hi: "<p>42.(d) जाइलम में पदार्थ दोनों दिशाओं में गति नहीं कर सकते। <strong>जाइलम </strong>का कार्य पानी और पोषक तत्वों को जड़ों से तने और पत्तियों तक पहुँचाना है। <strong>Phloem</strong> जटिल ऊतक है, जो संवहनी पौधों के भीतर घुलनशील कार्बनिक यौगिकों के लिए परिवहन प्रणाली के रूप में कार्य करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which Eminent Novelist and Poet of the 20th century is the author of &lsquo;Pinjar&rsquo;?</p>",
                    question_hi: "<p>43. 20वीं सदी के कौन से प्रसिद्ध उपन्यासकार और कवि \'पिंजर\' के लेखक हैं?</p>",
                    options_en: ["<p>Amrita Pritam</p>", "<p>Harivansh Rai Bachchan</p>", 
                                "<p>Bhawani Prasad Mishra</p>", "<p>Prabha Kiran Jain</p>"],
                    options_hi: ["<p>अमृता प्रीतम</p>", "<p>हरिवंश राय बच्चन</p>",
                                "<p>भवानी प्रसाद मिश्र</p>", "<p>प्रभा किरण जैन</p>"],
                    solution_en: "<p>43.(a) <strong>Amrita Pritam</strong> was an Indian maverick writer and poet. Books by Amrita Pritam :- &lsquo;Rassedi Ticket&rsquo;, &lsquo;Jalte Bujhate Log&rsquo;, &lsquo;49 days&rsquo;, &lsquo;Kore kagaz\',&rsquo; &lsquo;Nagmani&rsquo; etc.</p>",
                    solution_hi: "<p>43.(a) <strong>अमृता प्रीतम</strong> एक भारतीय लेखिका और कवयित्री थीं। अमृता प्रीतम की पुस्तकें:- \'रसदी टिकट\', \'जलते बुझाते लोग\', \'49 डेज \', \'कोरे कागज\', \'नागमणि\' आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Guru Makar Dhwaja Darogha, was conferred the Padma Shri award for his contribution to which form of dance in India ?</p>",
                    question_hi: "<p>44. गुरु मकर ध्वज दरोगा को भारत में किस प्रकार के नृत्य में उनके योगदान के लिए पद्म श्री पुरस्कार से सम्मानित किया गया था?</p>",
                    options_en: ["<p>Panthi</p>", "<p>Saila</p>", 
                                "<p>Chhau</p>", "<p>Raut Nacha</p>"],
                    options_hi: ["<p>पंथी</p>", "<p>सैला</p>",
                                "<p>छऊ</p>", "<p>राउत नाचा</p>"],
                    solution_en: "<p>44.(c) <strong>Chhau.</strong> Guru Makar Dhwaja Darogha was awarded the Padma Shri in 2011 for his remarkable commitment to the advancement of the Seraikela style of Chhau dance. He hailed from the state of Jharkhand. Three styles of Chhau Dance are the Purulia Chhau of West Bengal, the Seraikella Chhau of Jharkhand and the Mayurbhanj Chhau of Odisha.</p>",
                    solution_hi: "<p>44.(c) <strong>छऊ</strong> । गुरु मकर ध्वज दरोगा को छऊ नृत्य की सरायकेला शैली को आगे बढ़ाने में उनकी उल्लेखनीय प्रतिबद्धता के लिए 2011 में पद्म श्री से सम्मानित किया गया था। वे झारखंड राज्य से थे। छऊ नृत्य की तीन शैलियाँ पश्चिम बंगाल की पुरुलिया छाऊ, झारखंड की सरायकेला छाऊ और उड़ीसा की मयूरभंज छाऊ हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Agumbe, a biodiversity-rich region that receives 7640 mm of average annual rainfall in South India, is located in which state?</p>",
                    question_hi: "<p>45. दक्षिण भारत में 7640 मिमी औसत वार्षिक वर्षा प्राप्त करने वाला जैव विविधता समृद्ध क्षेत्र &lsquo;अगुम्बे&rsquo; किस राज्य में स्थित है?</p>",
                    options_en: ["<p>Andhra Pradesh</p>", "<p>Karnataka</p>", 
                                "<p>Tamil Nadu</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>आंध्र प्रदेश</p>", "<p>कर्नाटक</p>",
                                "<p>तमिलनाडु</p>", "<p>केरल</p>"],
                    solution_en: "<p>45.(b) Agumbe is a high-altitude village in the southwest Indian state of <strong>Karnataka.</strong> It is registered as a UNESCO World Heritage Site and is famous for its rich biodiversity, waterfalls (Onake Abbi, Bakarna, and Jogi Gundi fall), and red-hazy sun - set over the Arabian Sea. It is also known as the \'Cobra Capital\' of India.</p>",
                    solution_hi: "<p>45.(b) अगुम्बे दक्षिण-पश्चिम भारतीय राज्य <strong>कर्नाटक</strong> में एक उच्च ऊंचाई वाला गाँव है। यह यूनेस्को की विश्व धरोहर स्थल के रूप में पंजीकृत है और अपनी समृद्ध जैव विविधता, झरनों (ओनाके अब्बी, बकरना, और जोगी गुंडी फॉल) और अरब सागर के ऊपर लाल-धुंधली धूप के लिए प्रसिद्ध है। इसे भारत की \'कोबरा राजधानी\' के रूप में भी जाना जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. During the Gupta period, Gold coins were called by which names?</p>",
                    question_hi: "<p>46. गुप्त काल के दौरान सोने के सिक्कों को किस नाम से बुलाया जाता था ?</p>",
                    options_en: ["<p>Rupaka</p>", "<p>Tanka</p>", 
                                "<p>Drama</p>", "<p>Dinaras</p>"],
                    options_hi: ["<p>रूपक</p>", "<p>टंका</p>",
                                "<p>ड्रामा</p>", "<p>दीनार</p>"],
                    solution_en: "<p>46.(d) <strong>Dinaras.</strong> These coins were regular in size and weight, depicting Gupta kings and showcasing their love for war and art. The silver coins issued by the Guptas were called <strong>Rupaka.</strong> The silver coins <strong>Tanka</strong> were introduced by <strong>Illtumish</strong> (slave Dynasty).</p>",
                    solution_hi: "<p>46.(d) <strong>दीनार</strong> । ये सिक्के नियमित आकार और वजन के थे, तथा गुप्त राजाओं को दर्शाते थे तथा युद्ध और कला के प्रति उनके प्रेम को दर्शाते थे। गुप्तों द्वारा जारी किए गए चांदी के सिक्कों को <strong>रूपक</strong> कहा जाता था। चांदी के सिक्के <strong>टंका</strong> को <strong>इल्तुमिश</strong> (गुलाम राजवंश) द्वारा पेश किया गया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which country will host the 17th BRICS Summit in 2025 ?</p>",
                    question_hi: "<p>47. 2025 में 17वें ब्रिक्स शिखर सम्मेलन की मेजबानी कौन सा देश करेगा?</p>",
                    options_en: ["<p>India</p>", "<p>China</p>", 
                                "<p>Brazil</p>", "<p>South Africa</p>"],
                    options_hi: ["<p>भारत</p>", "<p>चीन</p>",
                                "<p>ब्राजील</p>", "<p>दक्षिण अफ्रीका</p>"],
                    solution_en: "<p>47.(c) <strong>Brazil.</strong> The 17th BRICS Summit will be held in Rio de Janeiro on July 6-7, 2025, where leaders will discuss global governance reforms and cooperation amid U.S. tariff threats.</p>",
                    solution_hi: "<p>47.(c) <strong>ब्राजील।</strong> 17वां ब्रिक्स शिखर&nbsp;सम्मेलन 6-7 जुलाई 2025 को रियो डी जनेरियो में आयोजित होगा, जहां नेता वैश्विक शासन सुधारों और अमेरिकी टैरिफ खतरों के बीच सहयोग पर चर्चा करेंगे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following books by Arundhati Roy is set to release in September 2025?</p>",
                    question_hi: "<p>48. अरुंधति रॉय की निम्नलिखित में से कौन सी पुस्तक सितंबर 2025 में रिलीज़ होने वाली है?</p>",
                    options_en: ["<p>\"The God of Small Things\"</p>", "<p>\"Mother Mary Comes to Me\"</p>", 
                                "<p>\"The Ministry of Utmost Happiness\"</p>", "<p>\"Field Notes on Democracy\"</p>"],
                    options_hi: ["<p>\"द गॉड ऑफ़ स्मॉल थिंग्स\"</p>", "<p>\"मदर मैरी कम्स टू मी\"</p>",
                                "<p>\"द मिनिस्ट्री ऑफ़ अटमोस्ट हैप्पीनेस\"</p>", "<p>\"फील्ड नोट्स ऑन डेमोक्रेसी\"</p>"],
                    solution_en: "<p>48.(b) \"<strong>Mother Mary Comes to Me</strong>\". This memoir explores Roy&rsquo;s relationship with her late mother, Mary Roy, and marks a new literary journey for the author. Her book &ldquo;The God of Small Things&rdquo; (1997), which won the Booker Prize for Fiction in 1997 and became the best-selling book by a non-expatriate Indian author.</p>",
                    solution_hi: "<p>48.(b) \"<strong>मदर मैरी कम्स टू मी</strong>\"। यह संस्मरण रॉय के अपनी दिवंगत मां मैरी रॉय के साथ संबंधों की खोज करता है, और लेखिका के लिए एक नई साहित्यिक यात्रा को चिह्नित करता है। उनकी पुस्तक \"द गॉड ऑफ स्मॉल थिंग्स\" (1997), जिसने 1997 में फिक्शन के लिए बुकर पुरस्कार से सम्मानित किया गया और एक गैर-प्रवासी भारतीय लेखक द्वारा सबसे अधिक बिकने वाली पुस्तक बन गई।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Ashoka the Great, belonged to the:</p>",
                    question_hi: "<p>49.अशोक महान________से सम्बन्धित थे</p>",
                    options_en: ["<p>Gupta Dynasty</p>", "<p>Shunga Dynasty</p>", 
                                "<p>Maurya Dynasty</p>", "<p>Chola Dynasty</p>"],
                    options_hi: ["<p>गुप्त वंश</p>", "<p>शुंग राजवंश</p>",
                                "<p>मौर्य राजवंश</p>", "<p>चोल राजवंश</p>"],
                    solution_en: "<p>49.(c) <strong>Maurya Dynasty.</strong> Ashoka, the third king of the Mauryan dynasty succeeded to the throne around 269 B.C. The region under the Ashoka stretched from Afghanistan in the west to Bangladesh in the east. The founder of the Mauryan dynasty was <strong>Chandragupta Maurya. Sri Gupta,</strong> founder of Gupta Dynasty. The Shunga Dynasty was founded by <strong>Pushyamitra Shunga.</strong> Chola Dynasty was founded by <strong>Vijayalaya.</strong></p>",
                    solution_hi: "<p>49.(c) <strong>मौर्य वंश।</strong> मौर्य वंश के तीसरे राजा अशोक ने 269 ईसा पूर्व के आसपास गद्दी संभाली। अशोक के अधीन क्षेत्र पश्चिम में अफगानिस्तान से लेकर पूर्व में बांग्लादेश तक फैला हुआ था। मौर्य वंश के संस्थापक <strong>चंद्रगुप्त मौर्य</strong> थे। गुप्त वंश के संस्थापक <strong>श्री गुप्त</strong>। शुंग राजवंश, पुष्यमित्र शुंग के संस्थापक, चोल राजवंश की स्थापना <strong>विजयालय</strong> ने की थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Who became the youngest chess world champion, breaking Garry Kasparov&rsquo;s record?</p>",
                    question_hi: "<p>50. सबसे युवा शतरंज विश्व चैंपियन बनने का रिकॉर्ड तोड़ते हुए कौन बना ?</p>",
                    options_en: ["<p>Magnus Carlsen</p>", "<p>Ding Liren</p>", 
                                "<p>Dommaraju Gukesh</p>", "<p>Rameshbabu Praggnanandhaa</p>"],
                    options_hi: ["<p>मैग्नस कार्लसन</p>", "<p>डिंग लिरेन</p>",
                                "<p>डोमरराजू गुकेश</p>", "<p>रमेशबाबू प्रज्ञानानंधा</p>"],
                    solution_en: "<p>50.(c) <strong>Dommaraju Gukesh.</strong> At just 18 years old, Gukesh defeated reigning champion Ding Liren in a tough match in Singapore to become the youngest chess world champion, surpassing Garry Kasparov\'s record.</p>",
                    solution_hi: "<p>50.(c) <strong>डोमरराजू गुकेश।</strong> सिर्फ 18 साल की उम्र में, गुकेश ने सिंगापुर में मौजूदा चैंपियन डिंग लिरेन को एक कठिन मुकाबले में हराकर सबसे युवा शतरंज विश्व चैंपियन बनने का गौरव हासिल किया, गैरी कास्परोव का रिकॉर्ड तोड़ते हुए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Which of the following numbers is divisible by 11?</p>",
                    question_hi: "<p>51. निम्नलिखित में से कौन-सी संख्या 11 से विभाज्य है?</p>",
                    options_en: ["<p>6589</p>", "<p>9164</p>", 
                                "<p>8962</p>", "<p>4857</p>"],
                    options_hi: ["<p>6589</p>", "<p>9164</p>",
                                "<p>8962</p>", "<p>4857</p>"],
                    solution_en: "<p>51.(a)<br><strong>Divisibility rule of 11 : </strong><br>The difference of the sum of alternate digits of the given number should be divisible by 11.<br>Checking all the options one by one, we get 6589 i.e.(6 + 8) - (9 + 5) = 0 , which is divisible by 11&nbsp;<br>So, the correct option is (a)</p>",
                    solution_hi: "<p>51.(a)<br><strong>11 का विभाज्यता नियम: </strong><br>दी गई संख्या के एकान्तर अंकों के योग का अंतर 11 से विभाज्य होना चाहिए।.<br>सभी विकल्पों को एक-एक करके जांचने पर हमें 6589 मिलता है यानी (6 + 8) - (9 + 5) = 0, जो 11 से विभाज्य है<br>तो, सही विकल्प (a) है |</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. If M : N = 9 : 6, then what is the value of (M + N) : (M - N) ?</p>",
                    question_hi: "<p>52. यदि M : N = 9 : 6 है, तो (M + N) : (M - N) का मान कितना है?</p>",
                    options_en: ["<p>5 : 1</p>", "<p>1 : 5</p>", 
                                "<p>5 : 4</p>", "<p>5 : 2</p>"],
                    options_hi: ["<p>5 : 1</p>", "<p>1 : 5</p>",
                                "<p>5 : 4</p>", "<p>5 : 2</p>"],
                    solution_en: "<p>52.(a)<br>(M + N) : (M - N) <br>= (9 + 6) : (9 - 6) <br>= 15 : 3 <br>= 5 : 1</p>",
                    solution_hi: "<p>52.(a)<br>(M + N) : (M - N) <br>= (9 + 6) : (9 - 6) <br>= 15 : 3 <br>= 5 : 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. A policeman was asked to chase a thief. Before the policeman started the chase, he realised that the thief was 200 metres ahead of him and was running at a speed of 16 km/h. The policeman started the chase at a speed of 20 km/h. How far will the thief run before he is overtaken by the policeman?</p>",
                    question_hi: "<p>53. एक पुलिसकर्मी को एक चोर का पीछा करने के लिए कहा गया। इससे पहले कि पुलिसकर्मी पीछा करना शुरू करता, उसे ज्ञात होता है कि चोर उससे 200 मीटर आगे था और 16 km/h की चाल से भाग रहा था। पुलिसकर्मी ने 20 km/h की चाल से पीछा करना शुरू किया। पुलिसकर्मी द्वारा पकड़े जाने से पहले चोर कितनी दूर तक भागेगा?</p>",
                    options_en: ["<p>600 m</p>", "<p>700 m</p>", 
                                "<p>800 m</p>", "<p>1000 m</p>"],
                    options_hi: ["<p>600 m</p>", "<p>700 m</p>",
                                "<p>800 m</p>", "<p>1000 m</p>"],
                    solution_en: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421329.png\" alt=\"rId42\" width=\"338\" height=\"143\"><br>Distance between police and thief = 200 meter<br>Relative speed = (20 - 16) &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math> m/sec<br>Time taken by police to catch the thief = <math display=\"inline\"><mfrac><mrow><mn>200</mn><mi>&#160;</mi></mrow><mrow><mfrac><mrow><mn>10</mn></mrow><mrow><mn>9</mn></mrow></mfrac><mi>&#160;</mi></mrow></mfrac></math> = 180 second<br>Time taken by police = time taken by thief<br>Speed of thief = 16 km/h = 16 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> m/sec<br>Distance travel by thief in 180 second = 180 &times; 16 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 800 meter</p>",
                    solution_hi: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421423.png\" alt=\"rId43\" width=\"361\" height=\"149\"><br>पुलिस और चोर के बीच की दूरी = 200 मीटर<br>सापेक्ष गति = (20 - 16) &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math> मीटर/सेकंड<br>पुलिस द्वारा चोर को पकड़ने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>200</mn><mi>&#160;</mi></mrow><mrow><mfrac><mrow><mn>10</mn></mrow><mrow><mn>9</mn></mrow></mfrac><mi>&#160;</mi></mrow></mfrac></math> = 180 सेकंड<br>पुलिस द्वारा लिया गया समय = चोर द्वारा लिया गया समय<br>चोर की गति = 16 किमी/घंटा = 16 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> मीटर/सेकंड<br>चोर द्वारा 180 सेकंड में तय की गयी दूरी = 180 &times; 16 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 800 मीटर</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Find the area of a rhombus whose diagonals are of lengths 10 cm and 8.2 cm.</p>",
                    question_hi: "<p>54. उस समचतुर्भुज का क्षेत्रफल ज्ञात कीजिए जिसके विकर्णों की लंबाई 10 cm और 8.2 cm है।</p>",
                    options_en: ["<p>40 cm<sup>2</sup></p>", "<p>42 cm<sup>2</sup></p>", 
                                "<p>41 cm<sup>2</sup></p>", "<p>43 cm<sup>2</sup></p>"],
                    options_hi: ["<p>40 cm<sup>2</sup></p>", "<p>42 cm<sup>2</sup></p>",
                                "<p>41 cm<sup>2</sup></p>", "<p>43 cm<sup>2</sup></p>"],
                    solution_en: "<p>54.(c)<br>Area of rhombus = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; d<sub>1</sub> &times; d<sub>2</sub><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 10 &times; 8.2<br>= 41 cm<sup>2</sup></p>",
                    solution_hi: "<p>54.(c)<br>समचतुर्भुज का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; d<sub>1</sub> &times; d<sub>2</sub><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 10 &times; 8.2<br>= 41 cm<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The average of 16.5, 9, 10.5, 12.5 and Y is 12.5. What is the value of Y ?</p>",
                    question_hi: "<p>55. 16.5, 9, 10.5, 12.5 और Y का औसत 12.5 है। Y का मान कितना है ?</p>",
                    options_en: ["<p>17</p>", "<p>16</p>", 
                                "<p>14</p>", "<p>11</p>"],
                    options_hi: ["<p>17</p>", "<p>16</p>",
                                "<p>14</p>", "<p>11</p>"],
                    solution_en: "<p>55.(c)<br>Net deviation = +4 +(-3.5) + (-2) + 0 = -1.5<br>Y = 12.5 + 1.5 = 14 <br><strong><br>Alternate</strong> ;<br><math display=\"inline\"><mfrac><mrow><mn>16</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>10</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>+</mo><mi>Y</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 12.5<br><math display=\"inline\"><mfrac><mrow><mn>48</mn><mo>.</mo><mn>5</mn><mo>+</mo><mi>Y</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 12.5<br>48.5 + Y = 62.5<br>Y = 14</p>",
                    solution_hi: "<p>55.(c)<br>शुद्ध विचलन = +4 +(-3.5) + (-2) + 0 = -1.5<br>Y = 12.5 + 1.5 = 14 </p>\n<p><strong>वैकल्पिक</strong> ;<br><math display=\"inline\"><mfrac><mrow><mn>16</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>9</mn><mo>+</mo><mn>10</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>+</mo><mi>Y</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 12.5<br><math display=\"inline\"><mfrac><mrow><mn>48</mn><mo>.</mo><mn>5</mn><mo>+</mo><mi>Y</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 12.5<br>48.5 + Y = 62.5<br>Y = 14</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Three students of class VII participated in a race during a school sports day. In a 1 km race, A wins the race with B by 100 m, and in a 300 m race, B beats C by 50 m. Then in the race of 1 km, with what margin will A beat C?</p>",
                    question_hi: "<p>56. स्कूल के खेल दिवस के दौरान कक्षा VII के तीन विद्यार्थियों ने 1 km की दौड़ में भाग लिया। यदि A, B से 100 m के अंतर से दौड़ जीत जाता है और B, C को 300 m की दौड़ में 50 m से हरा देता है, तो 1 km की दौड़ में, A, C को कितने अंतर से हराएगा ?</p>",
                    options_en: ["<p>700 m</p>", "<p>750 m</p>", 
                                "<p>150 m</p>", "<p>250 m</p>"],
                    options_hi: ["<p>700 m</p>", "<p>750 m</p>",
                                "<p>150 m</p>", "<p>250 m</p>"],
                    solution_en: "<p>56.(d) In 1km race<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; A&nbsp; &nbsp; &nbsp; &nbsp;B <br>Distance<math display=\"inline\"><mo>&#8594;</mo></math> 1000 : 900 <br>In 300m race<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; &nbsp; C <br>Distance<math display=\"inline\"><mo>&#8594;</mo></math> 300 : 250<br>Hence ,&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; A&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;C <br>Distance<math display=\"inline\"><mo>&#8594;</mo></math> 1000 : 900<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;300&times;3 : 250&times;3<br><br>Distance <math display=\"inline\"><mo>&#8594;</mo></math> &nbsp;A&nbsp; &nbsp; &nbsp; &nbsp; B&nbsp; &nbsp; &nbsp; C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;1000 : 900 : 750<br>Margin between A beat C = 1000 - 750 = 250m</p>",
                    solution_hi: "<p>56.(d) 1 किमी दौड़ में<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; A&nbsp; &nbsp; &nbsp; B&nbsp;<br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math> 1000 : 900 <br>300 मीटर दौड़ में<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; &nbsp; C <br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math> 300 : 250<br>अत:,&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;A&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; C <br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math> 1000&nbsp; :&nbsp; 900<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;300&times;3 : 250&times;3<br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;A&nbsp; &nbsp; &nbsp; &nbsp; B&nbsp; &nbsp; &nbsp; C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;1000 : 900 : 750<br>A और C के बीच का अंतर = 1000 - 750 = 250m</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If two numbers are 6.25% and 25% more than a third number, respectively, then the first number is how much per cent of the second number?</p>",
                    question_hi: "<p>57. यदि दो संख्याएं, तीसरी संख्या से क्रमशः 6.25% और 25% अधिक हैं, तो पहली संख्या, दूसरी संख्या की कितने प्रतिशत है?</p>",
                    options_en: ["<p>95%</p>", "<p>85%</p>", 
                                "<p>90%</p>", "<p>80%</p>"],
                    options_hi: ["<p>95%</p>", "<p>85%</p>",
                                "<p>90%</p>", "<p>80%</p>"],
                    solution_en: "<p>57.(b)<br>Let third no. = 100<br>First no. = 106.25 <br>Second no. = 125<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>106</mn><mo>.</mo><mn>25</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> &times; 100 = 85%</p>",
                    solution_hi: "<p>57.(b)<br>माना तीसरी संख्या = 100<br>पहली संख्या = 106.25 <br>दूसरी संख्या = 125<br>अपेक्षित % = <math display=\"inline\"><mfrac><mrow><mn>106</mn><mo>.</mo><mn>25</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> &times; 100 = 85%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. What is the sum of all prime numbers between 30 and 50 ?</p>",
                    question_hi: "<p>58. 30 और 50 के बीच की सभी अभाज्य संख्याओं का योग कितना है ?</p>",
                    options_en: ["<p>202</p>", "<p>187</p>", 
                                "<p>199</p>", "<p>173</p>"],
                    options_hi: ["<p>202</p>", "<p>187</p>",
                                "<p>199</p>", "<p>173</p>"],
                    solution_en: "<p>58.(c) <br>The prime number between 30 and 50 = 31, 37, 41, 43, 47, <br>Therefore, required sum = 31 + 37 + 41 + 43 + 47 = 199</p>",
                    solution_hi: "<p>58.(c) <br>30 और 50 के बीच की अभाज्य संख्या = 31, 37, 41, 43, 47,<br>अत: अभीष्ट योग = 31 + 37 + 41 + 43 + 47 = 199</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The ratio of the incomes of Rama and Ganga is 4 : 3 and the ratio of their expenditures is 3 : 2. If each person saves ₹2,700, then find their expenditures, respectively.</p>",
                    question_hi: "<p>59. राम और गंगा की आय का अनुपात 4 : 3 है और उनके व्यय का अनुपात 3 : 2 है। यदि प्रत्येक व्यक्ति ₹2,700 बचाता है, तो क्रमशः उनका व्यय ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹8,100 and ₹5,000</p>", "<p>₹8,000 and ₹5,000</p>", 
                                "<p>₹8,100 and ₹5,400</p>", "<p>₹8,000 and ₹5,400</p>"],
                    options_hi: ["<p>₹8,100 और ₹5,000</p>", "<p>₹8,000 और ₹5,000</p>",
                                "<p>₹8,100 और ₹5,400</p>", "<p>₹8,000 और ₹5,400</p>"],
                    solution_en: "<p>59.(c)<br><strong>Ratio -&nbsp; &nbsp; &nbsp;Rama&nbsp; : Ganga</strong><br><strong>Income</strong> -&nbsp; &nbsp; &nbsp; 4<math display=\"inline\"><mi>x</mi></math> &nbsp; :&nbsp; &nbsp;3x<br><strong>Expendi.</strong> -&nbsp; &nbsp; 3<math display=\"inline\"><mi>y</mi></math> &nbsp; :&nbsp; &nbsp;2y<br>---------------------<br>Savings - 4<math display=\"inline\"><mi>x</mi></math> - 3y : 3x - 2y <br>According to the question,<br>4<math display=\"inline\"><mi>x</mi></math> - 3y = 3x - 2y <br><math display=\"inline\"><mi>x</mi></math> = y<br>savings of Rama (4<math display=\"inline\"><mi>x</mi></math> - 3y) = 4x - 3x = x = ₹2700 <br>Also we can say that , x = y = ₹2700 <br>So, expenditure of Rama = 3y = ₹8100 <br>And expenditure of Ganga = 2y = ₹5400</p>",
                    solution_hi: "<p>59.(c)<br><strong>अनुपात -&nbsp; राम&nbsp; &nbsp; :&nbsp; &nbsp;गंगा</strong><br><strong>आय</strong> -&nbsp; &nbsp; &nbsp; &nbsp;4<math display=\"inline\"><mi>x</mi></math>&nbsp; &nbsp;&nbsp; :&nbsp; &nbsp; 3x<br><strong>व्यय</strong> -&nbsp; &nbsp; &nbsp; &nbsp; 3<math display=\"inline\"><mi>y</mi></math>&nbsp; &nbsp; :&nbsp; &nbsp;2y<br>--------------------------------------<br>बचत - 4<math display=\"inline\"><mi>x</mi></math> - 3y&nbsp; :&nbsp; 3x - 2y <br>प्रश्न के अनुसार,<br>4<math display=\"inline\"><mi>x</mi></math> - 3y = 3x - 2y <br><math display=\"inline\"><mi>x</mi></math> = y<br>राम की बचत(4<math display=\"inline\"><mi>x</mi></math> - 3y) = 4x - 3x = x = ₹2700 <br>हम यह भी कह सकते हैं कि, x = y = ₹2700 <br>तो, राम का व्यय = 3y = ₹8100 <br>तथा गंगा का व्यय = 2y = ₹5400</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The average weight of a group of 20 women was calculated as 58.5 kg and it was later discovered that the weight of one of the women was read as 35 kg, whereas her actual weight was 65 kg. What is the actual average weight of the group of 20 women?</p>",
                    question_hi: "<p>60. 20 महिलाओं के एक समूह का औसत वजन 58.5 kg परिकलित किया गया और बाद में यह पता चला कि उनमें से एक महिला का वजन 35 kg पढ़ लिया गया था, जबकि उसका वास्तविक वजन 65 kg था। 20 महिलाओं के समूह का वास्तविक औसत वजन कितना है?</p>",
                    options_en: ["<p>70 kg</p>", "<p>40 kg</p>", 
                                "<p>60 kg</p>", "<p>50 kg</p>"],
                    options_hi: ["<p>70 kg</p>", "<p>40 kg</p>",
                                "<p>60 kg</p>", "<p>50 kg</p>"],
                    solution_en: "<p>60.(c) The average weight of 20 women = 58.5 kg<br>It was discovered that the weight of one of the women was read as 35 kg instead of 65 kg<br>Then, net deviation = <math display=\"inline\"><mfrac><mrow><mn>35</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>65</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = -1.5<br>So, Actual weight of the 20 women = 58.5 + 1.5 = 60 kg</p>",
                    solution_hi: "<p>60.(c) 20 महिलाओं का औसत वजन = 58.5 kg<br>पता चला कि एक महिला का वजन 65 किलो की जगह 35 किलो पढ़ा गया था<br>तो, शुद्ध विचलन = <math display=\"inline\"><mfrac><mrow><mn>35</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>65</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = -1.5<br>इसलिए, 20 महिलाओं का वास्तविक औसत वजन = 58.5 + 1.5 = 60 kg</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A book is sold at <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> of its marked price and there is a loss of 50 percent. What will be the ratio of marked price and cost price of the book ?</p>",
                    question_hi: "<p>61. एक पुस्तक को उसके अंकित मूल्य के <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> पर बेचने पर 50 प्रतिशत की हानि होती है। पुस्तक के अंकित मूल्य और क्रय मूल्य का अनुपात कितना होगा ?</p>",
                    options_en: ["<p>2 : 5</p>", "<p>3 : 8</p>", 
                                "<p>5 : 6</p>", "<p>6 : 5</p>"],
                    options_hi: ["<p>2 : 5</p>", "<p>3 : 8</p>",
                                "<p>5 : 6</p>", "<p>6 : 5</p>"],
                    solution_en: "<p>61.(d) Let the marked price be x<br>According to question,<br>S.P. of book = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math><br>CP of book = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>50</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>6</mn></mfrac></math><br>Required Ratio <math display=\"inline\"><mo>&#8658;</mo></math> x : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>6</mn></mfrac></math> &rArr; 6 : 5</p>",
                    solution_hi: "<p>61.(d) माना अंकित मूल्य x&nbsp;है<br>प्रश्न के अनुसार,<br>पुस्तक का विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math><br>पुस्तक का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>50</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>6</mn></mfrac></math><br>आवश्यक अनुपात <math display=\"inline\"><mo>&#8658;</mo></math> x : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>6</mn></mfrac></math> &rArr; 6 : 5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Pomegranate juice contains 10% sugar solution and orange juice contains 30% sugar solution. What is the percentage of sugar solution in a mixture of two litres of pomegranate juice and three litres of orange juice?</p>",
                    question_hi: "<p>62. अनार के रस में 10% शर्करा विलयन होता है और संतरे के रस में 30% शर्करा विलयन होता है। दो लीटर अनार के रस और तीन लीटर संतरे के रस के मिश्रण में शर्करा विलयन का प्रतिशत कितना है?</p>",
                    options_en: ["<p>22%</p>", "<p>20%</p>", 
                                "<p>40%</p>", "<p>25%</p>"],
                    options_hi: ["<p>22%</p>", "<p>20%</p>",
                                "<p>40%</p>", "<p>25%</p>"],
                    solution_en: "<p>62.(a) <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Pomegranate&nbsp; &nbsp; &nbsp;Orange<br>Quantity&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 3<br>% of solution &nbsp;&rarr;&nbsp; &nbsp; &nbsp; &nbsp;10%&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 30%<br>Overall % = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>10</mn><mi>%</mi><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>30</mn><mi>%</mi></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac><mi>&#160;</mi></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>%</mo><mo>+</mo><mn>90</mn><mo>%</mo></mrow><mn>5</mn></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>5</mn></mfrac></math>% <br>= 22%</p>",
                    solution_hi: "<p>62.(a) <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;अनार&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;संतरा<br>मात्रा -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 3<br>मिश्रण का % -&nbsp; 10%&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;30%<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>10</mn><mi>%</mi><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>30</mn><mi>%</mi></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac><mi>&#160;</mi></math><br>= &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>%</mo><mo>+</mo><mn>90</mn><mo>%</mo></mrow><mn>5</mn></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>5</mn></mfrac></math>% <br>= 22%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. X and Y can do a job in 18 days; Y and Z can do the same in 24 days. If all the three can finish the job in 12 days, in how many days can X and Z can complete the job?</p>",
                    question_hi: "<p>63. X और Y एक काम को 18 दिनों में पूरा कर सकते हैं; Y और Z उसी काम को 24 दिनों में पूरा कर सकते हैं। यदि वह तीनों उसी काम को 12 दिनों में पूरा कर सकते हैं, तो X और Z कितने दिनों में काम पूरा कर सकते हैं?</p>",
                    options_en: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days</p>", 
                                "<p>7 days</p>", "<p>5 days</p>"],
                    options_hi: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन</p>",
                                "<p>7 दिन</p>", "<p>5 दिन</p>"],
                    solution_en: "<p>63.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421569.png\" alt=\"rId44\" width=\"267\" height=\"136\"><br>Efficiency of X = 6 - 3 = 3 units<br>Efficiency of Z = 6 - 4 = 2 units<br>Efficiency of (X + Z) = 5 units<br>So, Work done by (X + Z) = <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>63.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421743.png\" alt=\"rId45\" width=\"257\" height=\"144\"><br>X की दक्षता = 6 - 3 = 3 इकाई<br>Z की दक्षता = 6 - 4 = 2 इकाई<br>(X + Z) की दक्षता = 5 इकाई<br>इसलिए, (X + Z) द्वारा किया गया कार्य =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A boat can go 54 km upstream in 6 hours. If the speed of the stream is 4.8 km/h, then how much time (in hours) will the boat take to cover a distance of 279 km downstream?</p>",
                    question_hi: "<p>64. एक नाव धारा के विपरीत दिशा में 54 km की दूरी 6 घंटे में तय कर सकती है। यदि धारा की चाल 4.8 km/h है, तो नाव को धारा की दिशा में 279 km की दूरी तय करने में कितना समय (घंटे में) लगेगा?</p>",
                    options_en: ["<p>20</p>", "<p>15</p>", 
                                "<p>18</p>", "<p>16</p>"],
                    options_hi: ["<p>20</p>", "<p>15</p>",
                                "<p>18</p>", "<p>16</p>"],
                    solution_en: "<p>64.(b)<br>Let the speed of boat be B<br>Upstream speed = <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 9 km/hr<br><math display=\"inline\"><mo>&#8658;</mo></math> B - 4.8 = 9<br>B = 9 + 4.8 = 13.8 km/hr<br>Then, Downstream speed = 13.8 + 4.8 = 18.6 km/hr<br>Required time taken by boat = <math display=\"inline\"><mfrac><mrow><mn>279</mn></mrow><mrow><mn>18</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> = 15 hrs</p>",
                    solution_hi: "<p>64.(b)<br>माना नाव की चाल B है <br>धारा के विपरीत दिशा मे नाव की चाल = <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 9 km/hr<br><math display=\"inline\"><mo>&#8658;</mo></math> B - 4.8 = 9<br>B = 9 + 4.8 = 13.8 km/hr<br>तो नाव को धारा की दिशा में = 13.8 + 4.8 = 18.6 km/hr<br>नाव द्वारा लिया गया आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>279</mn></mrow><mrow><mn>18</mn><mo>.</mo><mn>6</mn></mrow></mfrac></math> = 15 घंटे</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. If a car travels 150 km at a speed of 50 km/h, how long does it take to complete the journey?</p>",
                    question_hi: "<p>65. यदि एक कार 50 km/h की चाल से 150 km की यात्रा करती है, तो उसे यात्रा पूरी करने में कितना समय लगेगा?</p>",
                    options_en: ["<p>3 hours</p>", "<p>3.5 hours</p>", 
                                "<p>4.5 hours</p>", "<p>4 hours</p>"],
                    options_hi: ["<p>3 घंटे</p>", "<p>3.5 घंटे</p>",
                                "<p>4.5 घंटे</p>", "<p>4 घंटे</p>"],
                    solution_en: "<p>65.(a)<br>Time taken by car to complete the journey = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> = 3 hrs</p>",
                    solution_hi: "<p>65.(a)<br>यात्रा पूरी करने में कार द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> = 3 घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Kalyan spends 80% of his income. His income increases by 15% and his expenditure also increases by 5%. The percentage of increase in his savings is:</p>",
                    question_hi: "<p>66. कल्याण अपनी आय का 80% खर्च करता है। उसकी आय 15% बढ़ जाती है और उसका व्यय भी 5% बढ़ जाता है। उसकी बचत में वृद्धि का प्रतिशत क्या है?</p>",
                    options_en: ["<p>50%</p>", "<p>20%</p>", 
                                "<p>55%</p>", "<p>40%</p>"],
                    options_hi: ["<p>50%</p>", "<p>20%</p>",
                                "<p>55%</p>", "<p>40%</p>"],
                    solution_en: "<p>66.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421856.png\" alt=\"rId46\" width=\"274\" height=\"153\"><br>percentage of increase in his savings = <math display=\"inline\"><mfrac><mrow><mn>31</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn><mi>&#160;</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 55%</p>",
                    solution_hi: "<p>66.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744343421974.png\" alt=\"rId47\" width=\"272\" height=\"165\"><br>उसकी बचत में वृद्धि का प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>31</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn><mi>&#160;</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 55%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A shopkeeper sells a notebook that has a marked price of ₹80 at a discount of 8% and gives a pen costing ₹8.60 free with each notebook. Even then he makes a profit of 17%. Find the cost price of each note book correct to two places of decimals.</p>",
                    question_hi: "<p>67. एक दुकानदार ₹80 अंकित मूल्य वाली एक नोटबुक 8% की छूट पर बेचता है और प्रत्येक नोटबुक के साथ ₹8.60 मूल्य वाला एक पेन मुफ़्त में देता है। फिर भी उसे 17% का लाभ प्राप्त होता है। प्रत्येक नोटबुक का दो दशमलव स्थानों तक पूर्णांकित क्रय मूल्य ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹49.75</p>", "<p>₹45.35</p>", 
                                "<p>₹55.56</p>", "<p>₹51.32</p>"],
                    options_hi: ["<p>₹49.75</p>", "<p>₹45.35</p>",
                                "<p>₹55.56</p>", "<p>₹51.32</p>"],
                    solution_en: "<p>67.(c) Marked price of notebook = ₹80<br>SP of notebook = ₹ 80 &times; <math display=\"inline\"><mfrac><mrow><mn>92</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 73.6<br>A shopkeeper gives a pen free then SP = 73.6 - 8.6 = ₹ 65<br>Cost price of notebook = <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>117</mn></mrow></mfrac></math> &times; 100 = ₹ 55.56</p>",
                    solution_hi: "<p>67.(c) नोटबुक का अंकित मूल्य = ₹80<br>नोटबुक का विक्रय मूल्य = ₹ 80 &times; <math display=\"inline\"><mfrac><mrow><mn>92</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 73.6<br>एक दुकानदार एक पेन मुफ़्त देता है तो विक्रय मूल्य = 73.6 - 8.6 = ₹ 65<br>नोटबुक का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>117</mn></mrow></mfrac></math> &times; 100 = ₹ 55.56</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. A man invested a sum of ₹5,000 on simple interest for 5 years such that the rate of interest for the first 2 years is 10% per annum, for the next 3 years it is 12% per annum. How much interest (in ₹), will he earn at the end of 5 years?</p>",
                    question_hi: "<p>68. एक व्यक्ति ने ₹5,000 की धनराशि को 5 वर्ष के लिए साधारणा ब्याज पर इस प्रकार निवेश करता है कि पहले 2 वर्ष के लिए ब्याज दर 10% वार्षिक है, अगले 3 वर्ष के लिए यह 12% वार्षिक है। 5 वर्ष के अंत में वह कितना ब्याज (₹ में) अर्जित करेगा?</p>",
                    options_en: ["<p>3180</p>", "<p>3000</p>", 
                                "<p>2450</p>", "<p>2800</p>"],
                    options_hi: ["<p>3180</p>", "<p>3000</p>",
                                "<p>2450</p>", "<p>2800</p>"],
                    solution_en: "<p>68.(d)<br>Total interest gain in 5 year = 2 &times; 10% + 3 &times; 12% = 56%<br>So, interest earn at the end of 5 year = 5000 &times; 56% = 2800</p>",
                    solution_hi: "<p>68.(d)<br>5 वर्ष में कुल ब्याज लाभ = 2 &times; 10% + 3 &times; 12% = 56%<br>तो, 5 वर्ष के अंत में अर्जित ब्याज = 5000 &times; 56% = 2800</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. 4 bells ring at intervals of 6, 8, 9 and 10 seconds. All the bells ring at the same time. After how many minutes will they ring together again?</p>",
                    question_hi: "<p>69. 4 घंटियां 6, 8, 9 और 10 सेकंड के अंतराल पर बजती हैं। सभी घंटियां एक ही समय पर बजती हैं। कितने मिनट के बाद वे पुनः एक साथ बजेंगी?</p>",
                    options_en: ["<p>15 min</p>", "<p>10 min</p>", 
                                "<p>8 min</p>", "<p>6 min</p>"],
                    options_hi: ["<p>15 मिनट</p>", "<p>10 मिनट</p>",
                                "<p>8 मिनट</p>", "<p>6 मिनट</p>"],
                    solution_en: "<p>69.(d)<br>LCM of (6, 8, 9,10) = 360 sec. = 6 min.<br>So, after 6 min. bells will ring together again.</p>",
                    solution_hi: "<p>69.(d)<br>(6,8,9,10) का LCM = 360 सेकंड = 6 मिनट<br>तो, 6 मिनट के बाद घंटियाँ फिर से एक साथ बजेगी</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Anandi bought a smart watch from an electronic store for Rs.4,400. Since she had less money, she paid Rs.2,000 for down payment and the shopkeeper offered her an alternative to pay Rs.2,440 after 1 month. Find the rate of interest being charged at simple interest by the shopkeeper in this installment option?</p>",
                    question_hi: "<p>70. आनंदी ने एक इलेक्ट्रॉनिक स्टोर से Rs.4,400 में एक स्मार्ट घड़ी खरीदी। चूंकि उसके पास कम पैसे थे, उसने डाउन पेमेंट के लिए Rs.2,000 का भुगतान किया, और दुकानदार ने उसे 1 महीने के बाद Rs.2,440 का भुगतान करने का विकल्प दिया। इस किश्त विकल्प में दुकानदार किस दर से साधारण ब्याज वसूल करेगा ?</p>",
                    options_en: ["<p>15%</p>", "<p>20%</p>", 
                                "<p>10%</p>", "<p>22%</p>"],
                    options_hi: ["<p>15%</p>", "<p>20%</p>",
                                "<p>10%</p>", "<p>22%</p>"],
                    solution_en: "<p>70.(b) <br>Original price of the watch = Rs. 4400<br>Rest amount to be paid later <br>= Rs. 4400 - 2000 = rs. 2400<br>Shopkeeper offered installment on Rs. 2440<br>S.I = 2440 - 2400 = Rs.40<br>Now, rate percent = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>100</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mo>&#160;</mo><mn>12</mn></mrow><mrow><mn>2400</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow></mfrac></math> = 20%</p>",
                    solution_hi: "<p>70.(b) घड़ी की मूल कीमत = रु. 4400<br>शेष राशि बाद में भुगतान की जाएगी = रु. 4400 - 2000 = रु. 2400<br>दुकानदार ने 2440 रुपये पर किश्त देने की पेशकश की<br>ब्याज = 2440 - 2400 = Rs.40<br>अब, दर प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>40</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>100</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>12</mn></mrow><mrow><mn>2400</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow></mfrac></math> = 20%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A father is presently 3 times his daughter&rsquo;s age. After 10 years he will be twice as old as her. Find the daughter&rsquo;s present age.</p>",
                    question_hi: "<p>71. एक पिता की वर्तमान आयु, उसकी पुत्री की आयु की 3 गुनी है। 10 वर्ष बाद उसकी आयु उसकी पुत्री की आयु की दोगुनी होगी। पुत्री की वर्तमान आयु ज्ञात कीजिए।</p>",
                    options_en: ["<p>10 years</p>", "<p>5 years</p>", 
                                "<p>15 years</p>", "<p>20 years</p>"],
                    options_hi: ["<p>10 वर्ष</p>", "<p>5 वर्ष</p>",
                                "<p>15 वर्ष</p>", "<p>20 वर्ष</p>"],
                    solution_en: "<p>71.(a)<br>Let the present age of the father and his daughter is 3<math display=\"inline\"><mi>x</mi></math> and x years respectively.<br>According to the question,<br>3<math display=\"inline\"><mi>x</mi></math> + 10 = 2(x + 10)<br>3<math display=\"inline\"><mi>x</mi></math> + 10 = 2x + 20<br><math display=\"inline\"><mi>x</mi></math> = 10<br>Hence, the age of the daughter (<math display=\"inline\"><mi>x</mi></math>) = 10 years</p>",
                    solution_hi: "<p>71.(a)<br>माना पिता और उसकी पुत्री की वर्तमान आयु क्रमशः 3<math display=\"inline\"><mi>x</mi></math> और x वर्ष है।<br>प्रश्न के अनुसार,<br>3<math display=\"inline\"><mi>x</mi></math> + 10 = 2(x + 10)<br>3<math display=\"inline\"><mi>x</mi></math> + 10 = 2x + 20<br><math display=\"inline\"><mi>x</mi></math> = 10<br>अत: पुत्री की आयु (<math display=\"inline\"><mi>x</mi></math>) = 10 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. On buying 24 bangles Meena gets 6 bangles free of cost. By what percentage does Meena get a discount?</p>",
                    question_hi: "<p>72. 24 चूड़ियाँ खरीदने पर मीना को 6 चूड़ियाँ मुफ्त मिलती हैं। मीना को कितने प्रतिशत की छूट मिलती है?</p>",
                    options_en: ["<p>15%</p>", "<p>10%</p>", 
                                "<p>5%</p>", "<p>20%</p>"],
                    options_hi: ["<p>15%</p>", "<p>10%</p>",
                                "<p>5%</p>", "<p>20%</p>"],
                    solution_en: "<p>72.(d)<br>Required Discount % = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>24</mn><mo>+</mo><mn>6</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    solution_hi: "<p>72.(d)<br>आवश्यक छूट % = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>24</mn><mo>+</mo><mn>6</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The surface area of a cube is 486 cm<sup>2</sup> Find the volume.</p>",
                    question_hi: "<p>73. एक घन का पृष्ठीय क्षेत्रफल 486 cm<sup>2</sup> है, उसका आयतन ज्ञात करें।</p>",
                    options_en: ["<p>625 cm<sup>3</sup></p>", "<p>512 cm<sup>3</sup></p>", 
                                "<p>729 cm<sup>3</sup></p>", "<p>486 cm<sup>3</sup></p>"],
                    options_hi: ["<p>625 cm<sup>3</sup></p>", "<p>512 cm<sup>3</sup></p>",
                                "<p>729 cm<sup>3</sup></p>", "<p>486 cm<sup>3</sup></p>"],
                    solution_en: "<p>73.(c)<br>Surface area of the cube = 6<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>486 = 6<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>a = 9 cm<br>Volume of the cube = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math><br>= <math display=\"inline\"><msup><mrow><mo>(</mo><mn>9</mn><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math><br>= 729 cm<sup>3</sup></p>",
                    solution_hi: "<p>73.(c)<br>घन का पृष्ठीय क्षेत्रफल = 6<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>486 = 6<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>a = 9 cm<br>घन का आयतन = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math><br>= <math display=\"inline\"><msup><mrow><mo>(</mo><mn>9</mn><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math><br>= 729 cm<sup>3</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. 8 apples and 10 oranges weigh 5 kg. 12 apples and 20 oranges weigh 9 kg. What is the weight (in kg, rounded off to the nearest integer) of 15 apples and 24 oranges ?</p>",
                    question_hi: "<p>74. 8 सेब और 10 संतरे का वजन 5 kg है। 12 सेब और 20 संतरे का वजन 9 kg है। 15 सेब और 24 संतरे का वजन (kg में, निकटतम पूर्णांक तक पूर्णांकित) कितना होगा?</p>",
                    options_en: ["<p>16</p>", "<p>11</p>", 
                                "<p>13</p>", "<p>10</p>"],
                    options_hi: ["<p>16</p>", "<p>11</p>",
                                "<p>13</p>", "<p>10</p>"],
                    solution_en: "<p>74.(b)<br>Let the weight of apples and oranges is &lsquo;a&rsquo; and &lsquo;b&rsquo; respectively.<br>According to the question,<br>8a + 10b = 5&hellip;&hellip;.(i)<br>12a + 20b = 9&hellip;&hellip;.(ii)<br>Multiply by 2 in e.q .(i) then subtract e.q .(ii) from (i) we get,<br>4a = 1<br>a = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> (put the value in e.q .(i) we get,<br>8 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 10b = 5<br>10b = 3, b = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>Weight of 15 apples and 24 oranges = 15 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 24 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math><br>= 3.75 + 7.2<br>= 10.95 kg (approx 11 kg)</p>",
                    solution_hi: "<p>74.(b)<br>माना सेब और संतरे का वजन क्रमशः \'a\' और \'b\' है।<br>प्रश्न के अनुसार,<br>8a + 10b = 5&hellip;&hellip;.(i)<br>12a + 20b = 9&hellip;&hellip;.(ii)<br>समीकरण (i) में 2 से गुणा करने पर व फिर समीकरण (i) में से (ii) घटाने पर, हमें प्राप्त होता है,<br>4a = 1<br>a = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> (मान का समीकरण (i) में रखने पर हमे मिलता है)<br>8 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 10b = 5<br>10b = 3, b = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>15 सेब और 24 संतरो का वजन = 15 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 24 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math><br>= 3.75 + 7.2<br>= 10.95 kg (लगभग 11 kg)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The income of a person is ₹16,500, and his expenditure is ₹12,000. Next year, his income increases by 24% but his expenditure decreases by 10%. The percentage increase (rounded off to the nearest integer) in his savings is:</p>",
                    question_hi: "<p>75. एक व्यक्ति की आय ₹16,500 है और उसका व्यय ₹12,000 है। अगले वर्ष उसकी आय 24% बढ़ती है लेकिन उसका व्यय 10% कम हो जाता है, तो उसकी बचत में प्रतिशत वृद्धि (निकटतम पूर्णांक तक पूर्णांकित) कितनी होगी?</p>",
                    options_en: ["<p>90%</p>", "<p>120%</p>", 
                                "<p>115%</p>", "<p>95%</p>"],
                    options_hi: ["<p>90%</p>", "<p>120%</p>",
                                "<p>115%</p>", "<p>95%</p>"],
                    solution_en: "<p>75.(c) <br>Income - expenditure = saving<br>Initial <math display=\"inline\"><mo>&#8594;</mo></math> 16500 - 12000 = 4500<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; +24% &darr;- 10%&nbsp;<br>Final <math display=\"inline\"><mo>&#8594;</mo></math> 20460 - 10800 = 9660<br>% increase = <math display=\"inline\"><mfrac><mrow><mn>9660</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4500</mn></mrow><mrow><mn>4500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5160</mn><mn>45</mn></mfrac></math>% = 114.6% &asymp; 115 %</p>",
                    solution_hi: "<p>75.(c) <br>आय - व्यय = बचत<br>प्रारंभिक<math display=\"inline\"><mo>&#8594;</mo></math> 16500 - 12000 = 4500<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<math display=\"inline\"><mo>&#8595;</mo></math>+24% &darr;-10% <br>अंतिम <math display=\"inline\"><mo>&#8594;</mo></math> 20460 - 10800 = 9660<br>% वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>9660</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4500</mn></mrow><mrow><mn>4500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5160</mn><mn>45</mn></mfrac></math>% = 114.6% &asymp; 115 %</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the option that can be used as a one-word substitute for the underlined group of words.<br>Because the politician\'s speech was <span style=\"text-decoration: underline;\">high - flown and pretentious</span>, it gained popularity.</p>",
                    question_hi: "<p>76. Select the option that can be used as a one-word substitute for the underlined group of words.<br>Because the politician\'s speech was <span style=\"text-decoration: underline;\">high - flown and pretentious</span>, it gained popularity.</p>",
                    options_en: ["<p>bombastic</p>", "<p>austere</p>", 
                                "<p>schematising</p>", "<p>mnemonic</p>"],
                    options_hi: ["<p>bombastic</p>", "<p>austere</p>",
                                "<p>schematising</p>", "<p>mnemonic</p>"],
                    solution_en: "<p>76.(a) <strong>Bombastic-</strong> high-flown and pretentious.<br><strong>Austere-</strong> severe or strict in manner or attitude.<br><strong>Schematising-</strong> organizing or arranging something according to a particular scheme or systematic plan.<br><strong>Mnemonic-</strong> a memory aid for something, often taking the form of a rhyme or an acronym.</p>",
                    solution_hi: "<p>76.(a) <strong>Bombastic</strong> (आडंबरपूर्ण) - high-flown and pretentious.<br><strong>Austere</strong> (कठोर) - severe or strict in manner or attitude.<br><strong>Schematising</strong> (योजना बनाना) - organizing or arranging something according to a particular scheme or systematic plan.<br><strong>Mnemonic</strong> (स्मरणीय संकेत) - a memory aid for something, often taking the form of a rhyme or an acronym.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>He <span style=\"text-decoration: underline;\">wanted to visiting</span> the Golden Temple in Amritsar.</p>",
                    question_hi: "<p>77. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>He <span style=\"text-decoration: underline;\">wanted to visiting</span> the Golden Temple in Amritsar.</p>",
                    options_en: ["<p>wanted to visited</p>", "<p>wanted to visit</p>", 
                                "<p>wanted to be visit</p>", "<p>wanted to visitor</p>"],
                    options_hi: ["<p>wanted to visited</p>", "<p>wanted to visit</p>",
                                "<p>wanted to be visit</p>", "<p>wanted to visitor</p>"],
                    solution_en: "<p>77.(b) wanted to visit<br>&lsquo;To + V<sub>1</sub>&rsquo; , also known as an infinitive, is the correct grammatical structure. Hence, &lsquo;wanted to visit (V<sub>1</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(b) wanted to visit<br>&lsquo;To + V<sub>1</sub>&rsquo;, जिसे infinitive कहा जाता है, सही grammatical structure है। इसलिए, &lsquo;wanted to visit (V<sub>1</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate meaning of the given idiom.<br>Lose heart</p>",
                    question_hi: "<p>78. Select the most appropriate meaning of the given idiom.<br>Lose heart</p>",
                    options_en: ["<p>To believe in your success</p>", "<p>To be suspicious of your success</p>", 
                                "<p>To stop believing that you can succeed</p>", "<p>To get success by believing in yourself</p>"],
                    options_hi: ["<p>To believe in your success</p>", "<p>To be suspicious of your success</p>",
                                "<p>To stop believing that you can succeed</p>", "<p>To get success by believing in yourself</p>"],
                    solution_en: "<p>78.(c) <strong>Lose heart-</strong> to stop believing that you can succeed.<br>E.g.- After a few setbacks, he began to lose heart in his project.</p>",
                    solution_hi: "<p>78.(c) <strong>Lose heart-</strong> to stop believing that you can succeed./ यह मानना बंद करें कि आप सफल हो सकते हैं।<br>E.g.- After a few setbacks, he began to lose heart in his project.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the most appropriate option to change the voice (active/passive) form of the given sentence:<br>Ram asked Rajesh to switch off the fan.</p>",
                    question_hi: "<p>79. Choose the most appropriate option to change the voice (active/passive) form of the given sentence:<br>Ram asked Rajesh to switch off the fan.</p>",
                    options_en: ["<p>Rajesh is asked to switch off the fan by Ram.</p>", "<p>Rajesh was asked to switch off the fan by Ram.</p>", 
                                "<p>Ram asked to be switched off the fan by Ram.</p>", "<p>Ram was asked to switch off the fan by Ram.</p>"],
                    options_hi: ["<p>Rajesh is asked to switch off the fan by Ram.</p>", "<p>Rajesh was asked to switch off the fan by Ram.</p>",
                                "<p>Ram asked to be switched off the fan by Ram.</p>", "<p>Ram was asked to switch off the fan by Ram.</p>"],
                    solution_en: "<p>79.(b) Rajesh was asked to switch off the fan by Ram.<br>(a) Rajesh <strong>is asked</strong> to switch off the fan by Ram. (Tense has changed)<br>(c) Ram <strong>asked to be switched off the fan</strong> by Ram. (Incorrect structure)<br>(d) <strong>Ram</strong> was asked to switch off the fan by <strong>Ram.</strong> (Rajesh is mentioned nowhere)</p>",
                    solution_hi: "<p>79.(b) Rajesh was asked to switch off the fan by Ram.<br>(a) Rajesh <strong>is asked</strong> to switch off the fan by Ram. ( Tense बदल गया है)<br>(c) Ram <strong>asked to be switched off the fan</strong> by Ram. ( गलत structure )<br>(d) <strong>Ram</strong> was asked to switch off the fan by <strong>Ram.</strong> (Rajesh का कहीं उल्लेख नहीं है)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>80. Select the INCORRECTLY spelt word.</p>",
                    options_en: ["<p>Aquire</p>", "<p>Congratulate</p>", 
                                "<p>Hygiene</p>", "<p>Category</p>"],
                    options_hi: ["<p>Aquire</p>", "<p>Congratulate</p>",
                                "<p>Hygiene</p>", "<p>Category</p>"],
                    solution_en: "<p>80.(a) Aquire <br>&lsquo;Acquire&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>80.(a) Aquire <br>&lsquo;Acquire&rsquo; सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate meaning of the given idiom.<br>Slip your mind</p>",
                    question_hi: "<p>81. Select the most appropriate meaning of the given idiom.<br>Slip your mind</p>",
                    options_en: ["<p>Immediately think of something</p>", "<p>Forget about something</p>", 
                                "<p>Can&rsquo;t think of anything to say</p>", "<p>Think about something for a short time</p>"],
                    options_hi: ["<p>Immediately think of something</p>", "<p>Forget about something</p>",
                                "<p>Can&rsquo;t think of anything to say</p>", "<p>Think about something for a short time</p>"],
                    solution_en: "<p>81.(b) <strong>Slip your mind</strong> - forget about something. <br><strong>E.g.-</strong> I was supposed to pick up groceries after work, but it totally slipped my mind.</p>",
                    solution_hi: "<p>81.(b) <strong>Slip your mind </strong>- forget about something./ भूल जाना। <br><strong>E.g.-</strong> I was supposed to pick up groceries after work, but it totally slipped my mind.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Read the sentence carefully and select the synonym of the underlined word from the&nbsp;given alternatives.<br>The Television show was hosted by a young and <span style=\"text-decoration: underline;\">vivacious</span> woman called Laura.</p>",
                    question_hi: "<p>82. Read the sentence carefully and select the synonym of the underlined word from the&nbsp;given alternatives.<br>The Television show was hosted by a young and <span style=\"text-decoration: underline;\">vivacious</span> woman called Laura.</p>",
                    options_en: ["<p>Liberal</p>", "<p>Energetic</p>", 
                                "<p>Legendary</p>", "<p>Atrocious</p>"],
                    options_hi: ["<p>Liberal</p>", "<p>Energetic</p>",
                                "<p>Legendary</p>", "<p>Atrocious</p>"],
                    solution_en: "<p>82.(b) <strong>Vivacious-</strong> full of energy and enthusiasm.<br><strong>Liberal-</strong> open to new ideas.<br><strong>Energetic-</strong> displaying great vitality.<br><strong>Legendary-</strong> famous and admired for achievements.<br><strong>Atrocious-</strong> extremely bad or unpleasant.</p>",
                    solution_hi: "<p>82.(b) <strong>Vivacious</strong> (जोशपूर्ण) - full of energy and enthusiasm.<br><strong>Liberal</strong> (उदार) - open to new ideas.<br><strong>Energetic</strong> (ऊर्जावान) - displaying great vitality.<br><strong>Legendary</strong> (प्रसिद्ध) - famous and admired for achievements.<br><strong>Atrocious</strong> (नृशंस/घटिया) - extremely bad or unpleasant.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;After the lecture I had to rush home&rdquo;.\'</p>",
                    question_hi: "<p>83. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;After the lecture I had to rush home&rdquo;.\'</p>",
                    options_en: ["<p>He told that after the lecture he had to rush home.</p>", "<p>He said if after the lecture he had to rush home.</p>", 
                                "<p>He asked if he wanted to rush home after the lecture.</p>", "<p>He said that after the lecture he had had to rush home.</p>"],
                    options_hi: ["<p>He told that after the lecture he had to rush home.</p>", "<p>He said if after the lecture he had to rush home.</p>",
                                "<p>He asked if he wanted to rush home after the lecture.</p>", "<p>He said that after the lecture he had had to rush home.</p>"],
                    solution_en: "<p>83.(d) He said that after the lecture he had had to rush home.<br>(a) He <strong>told</strong> that after the lecture he had to rush home. (Incorrect Reporting Verb)<br>(b) He said <strong>if</strong> after the lecture he had to rush home. (Meaning of sentence changed by using if)<br>(c) He <strong>asked</strong> if he wanted to rush home after the lecture. (Incorrect Reporting Verb)</p>",
                    solution_hi: "<p>83.(d) He said that after the lecture he had had to rush home.<br>(a) He <strong>told</strong> that after the lecture he had to rush home. ( गलत Reporting Verb )<br>(b) He said <strong>if</strong> after the lecture he had to rush home. (if के प्रयोग से वाक्य का अर्थ बदल गया है)<br>(c) He <strong>asked</strong> if he wanted to rush home after the lecture. ( गलत Reporting Verb )</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate ANTONYM of the underlined word in the given sentence. <br>The truth was <span style=\"text-decoration: underline;\">revealed</span> in the interrogation.</p>",
                    question_hi: "<p>84. Select the most appropriate ANTONYM of the underlined word in the given sentence. <br>The truth was <span style=\"text-decoration: underline;\">revealed</span> in the interrogation.</p>",
                    options_en: ["<p>Found</p>", "<p>Declared</p>", 
                                "<p>Evident</p>", "<p>Concealed</p>"],
                    options_hi: ["<p>Found</p>", "<p>Declared</p>",
                                "<p>Evident</p>", "<p>Concealed</p>"],
                    solution_en: "<p>84.(d) <strong>Concealed-</strong> kept hidden or secret.<br><strong>Revealed-</strong> to make something publicly known.<br><strong>Found-</strong> to bring something to existence.<br><strong>Declared-</strong> openly announced.<br><strong>Evident-</strong> clearly visible or understood.</p>",
                    solution_hi: "<p>84.(d) <strong>Concealed</strong> (छिपा हुआ) - kept hidden or secret.<br><strong>Revealed</strong> (उजागर) - to make something publicly known.<br><strong>Found</strong> (पाना/मिलना) - to bring something to existence.<br><strong>Declared</strong> (घोषित करना) - openly announced.<br><strong>Evident</strong> (सुस्पष्ट) - clearly visible or understood.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options.<br>Mr. Thomas / is going / to buy / horse.</p>",
                    question_hi: "<p>85. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options.<br>Mr. Thomas / is going / to buy / horse.</p>",
                    options_en: ["<p>to buy</p>", "<p>Mr. Thomas</p>", 
                                "<p>is going</p>", "<p>horse</p>"],
                    options_hi: ["<p>to buy</p>", "<p>Mr. Thomas</p>",
                                "<p>is going</p>", "<p>horse</p>"],
                    solution_en: "<p>85.(d) horse<br>We generally use an indefinite article before a singular noun. Article &lsquo;a&rsquo; will be used before &lsquo;horse&rsquo; as it begins with a consonant sound. Hence, &lsquo;a horse&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>85.(d) horse<br>हम आमतौर पर singular noun से पहले indefinite article का प्रयोग करते हैं। Article &lsquo;a&rsquo; का प्रयोग &lsquo;horse&rsquo; से पहले किया जाएगा क्योंकि यह consonant sound से शुरू होता है। इसलिए, &lsquo;a horse&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate connotation to fill in the blank.<br>I always love the _______ of my mother&rsquo;s cooking.</p>",
                    question_hi: "<p>86. Select the most appropriate connotation to fill in the blank.<br>I always love the _______ of my mother&rsquo;s cooking.</p>",
                    options_en: ["<p>perfume</p>", "<p>stench</p>", 
                                "<p>aroma</p>", "<p>scent</p>"],
                    options_hi: ["<p>perfume</p>", "<p>stench</p>",
                                "<p>aroma</p>", "<p>scent</p>"],
                    solution_en: "<p>86.(c) <strong>aroma</strong><br>&lsquo;Aroma&rsquo; means a pleasant smell often associated with food, drinks or natural scents. The given sentence states that I always love the aroma of my mother&rsquo;s cooking. Hence &lsquo;aroma&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>86.(c) <strong>aroma</strong><br>&lsquo;Aroma&rsquo; का अर्थ है एक सुखद गंध (pleasant smell) जो अक्सर भोजन, पेय या प्राकृतिक सुगंधों से जुड़ी होती है। दिए गए sentence में कहा गया है कि मुझे हमेशा अपनी माँ के हाथ के खाने की सुगंध पसंद आती है। अतः &lsquo;aroma&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who rules with absolute power and authority</p>",
                    question_hi: "<p>87. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who rules with absolute power and authority</p>",
                    options_en: ["<p>Senator</p>", "<p>Ruler</p>", 
                                "<p>Dictator</p>", "<p>Sovereign</p>"],
                    options_hi: ["<p>Senator</p>", "<p>Ruler</p>",
                                "<p>Dictator</p>", "<p>Sovereign</p>"],
                    solution_en: "<p>87.(c) <strong>Dictator</strong> - a person who rules with absolute power and authority.<br><strong>Senator</strong> - a member of a senate.<br><strong>Ruler</strong> - a person exercising government or dominion.<br><strong>Sovereign</strong> - a supreme ruler, especially a monarch.</p>",
                    solution_hi: "<p>87.(c) <strong>Dictator</strong> (तानाशाह) - a person who rules with absolute power and authority.<br><strong>Senator</strong> (सिनेटर) - a member of a senate.<br><strong>Ruler</strong> (शासक) - a person exercising government or dominion.<br><strong>Sovereign</strong> (प्रधान शासक) - a supreme ruler, especially a monarch.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the incorrect form of the verb.</p>",
                    question_hi: "<p>88. Select the incorrect form of the verb.</p>",
                    options_en: ["<p>stricken</p>", "<p>strucken</p>", 
                                "<p>strike</p>", "<p>struck</p>"],
                    options_hi: ["<p>stricken</p>", "<p>strucken</p>",
                                "<p>strike</p>", "<p>struck</p>"],
                    solution_en: "<p>88.(b) strucken <br>&lsquo;Stricken&rsquo; is the correct verb form.</p>",
                    solution_hi: "<p>88.(b) strucken <br>&lsquo;Stricken&rsquo; सही verb form है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate synonym of the given word. <br>Confidential</p>",
                    question_hi: "<p>89. Select the most appropriate synonym of the given word. <br>Confidential</p>",
                    options_en: ["<p>Public</p>", "<p>Secret</p>", 
                                "<p>Limited</p>", "<p>Open source</p>"],
                    options_hi: ["<p>Public</p>", "<p>Secret</p>",
                                "<p>Limited</p>", "<p>Open source</p>"],
                    solution_en: "<p>89.(b) <strong>Secret-</strong> something that is kept hidden.<br><strong>Confidential-</strong> not known by others.<br><strong>Public-</strong> that is open and accessible to others.<br><strong>Limited-</strong> restricted in amount.<br><strong>Open source-</strong> that is freely available to others.</p>",
                    solution_hi: "<p>89.(b) <strong>Secret</strong> (रहस्य) - something that is kept hidden.<br><strong>Confidential</strong> (गोपनीय) - not known by others.<br><strong>Public</strong> (सार्वजनिक) - that is open and accessible to others.<br><strong>Limited</strong> (सीमित) - restricted in amount.<br><strong>Open source</strong> (खुला स्रोत) - that is freely available to others.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate option to fill in the blank.<br>Both the criminals were cruel and ________.</p>",
                    question_hi: "<p>90. Select the most appropriate option to fill in the blank.<br>Both the criminals were cruel and ________.</p>",
                    options_en: ["<p>brutal</p>", "<p>diligent</p>", 
                                "<p>warm</p>", "<p>sympathetic</p>"],
                    options_hi: ["<p>brutal</p>", "<p>diligent</p>",
                                "<p>warm</p>", "<p>sympathetic</p>"],
                    solution_en: "<p>90.(a) <strong>brutal</strong><br>&lsquo;Brutal&rsquo; means savagely violent. The given sentence states that both the criminals were cruel and brutal. Hence, &lsquo;brutal&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>90.(a) <strong>brutal</strong><br>&lsquo;Brutal&rsquo; का अर्थ है क्रूरतापूर्वक हिंसक (savagely violent)। दिए गए sentence में बताया गया है कि दोनों अपराधी (criminals) निर्दयी (cruel) एवं क्रूर (brutal) थे। अतः, &lsquo;brutal&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. <strong>Cloze Test :</strong><br>The ___(91)___age is the age of machines. From the ___(92)___ the industrial revolution began in Europe. Man&rsquo;s life has been changing ___(93)___ many ways. At first the change was ___(94)___. Now machines have become ___(95)___of our daily lives.<br>Select the most appropriate option to fill in blank number 91.</p>",
                    question_hi: "<p>91. <strong>Cloze Test :</strong><br>The ___(91)___age is the age of machines. From the ___(92)___ the industrial revolution began in Europe. Man&rsquo;s life has been changing ___(93)___ many ways. At first the change was ___(94)___. Now machines have become ___(95)___of our daily lives.<br>Select the most appropriate option to fill in blank number 91.</p>",
                    options_en: ["<p>Modern</p>", "<p>New</p>", 
                                "<p>Civilized</p>", "<p>Present</p>"],
                    options_hi: ["<p>Modern</p>", "<p>New</p>",
                                "<p>Civilized</p>", "<p>Present</p>"],
                    solution_en: "<p>91.(a) <strong>Modern</strong><br>As the phrase - &ldquo; age of machines&rdquo; is mentioned so the word modern will be used.</p>",
                    solution_hi: "<p>91.(a) <strong>Modern</strong><br>Phrase- &ldquo; age of machines&rdquo; का उल्लेख किया गया है, इसलिए modern शब्द का प्रयोग किया जाएगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. <strong>Cloze Test</strong> :<br>The ___(91)___age is the age of machines. From the ___(92)___ the industrial revolution began in Europe. Man&rsquo;s life has been changing ___(93)___ many ways. At first the change was ___(94)___. Now machines have become ___(95)___of our daily lives.<br>Select the most appropriate option to fill in blank number 92.</p>",
                    question_hi: "<p>92. <strong>Cloze Test</strong> :<br>The ___(91)___age is the age of machines. From the ___(92)___ the industrial revolution began in Europe. Man&rsquo;s life has been changing ___(93)___ many ways. At first the change was ___(94)___. Now machines have become ___(95)___of our daily lives.<br>Select the most appropriate option to fill in blank number 92.</p>",
                    options_en: ["<p>Birth</p>", "<p>Time</p>", 
                                "<p>Beginning</p>", "<p>Start</p>"],
                    options_hi: ["<p>Birth</p>", "<p>Time</p>",
                                "<p>Beginning</p>", "<p>Start</p>"],
                    solution_en: "<p>92.(b) Time</p>",
                    solution_hi: "<p>92.(b) Time</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93.<strong> Cloze Test :</strong><br>The ___(91)___age is the age of machines. From the ___(92)___ the industrial revolution began in Europe. Man&rsquo;s life has been changing ___(93)___ many ways. At first the change was ___(94)___. Now machines have become ___(95)___of our daily lives.<br>Select the most appropriate option to fill in blank number 93.</p>",
                    question_hi: "<p>93. <strong>Cloze Test</strong> :<br>The ___(91)___age is the age of machines. From the ___(92)___ the industrial revolution began in Europe. Man&rsquo;s life has been changing ___(93)___ many ways. At first the change was ___(94)___. Now machines have become ___(95)___of our daily lives.<br>Select the most appropriate option to fill in blank number 93.</p>",
                    options_en: ["<p>Into</p>", "<p>To</p>", 
                                "<p>In</p>", "<p>With</p>"],
                    options_hi: ["<p>Into</p>", "<p>To</p>",
                                "<p>In</p>", "<p>With</p>"],
                    solution_en: "<p>93.(c) In<br>&ldquo;In&rdquo; is the most appropriate preposition. &ldquo;Change in&rdquo; means to alter or modify.</p>",
                    solution_hi: "<p>93.(c) In<br>&ldquo;In&rdquo; सबसे उपयुक्त preposition है। &ldquo;Change in&rdquo; का अर्थ है बदलना या संशोधित करना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. <strong>Cloze Test</strong> :<br>The ___(91)___age is the age of machines. From the ___(92)___ the industrial revolution began in Europe. Man&rsquo;s life has been changing ___(93)___ many ways. At first the change was ___(94)___. Now machines have become ___(95)___of our daily lives.<br>Select the most appropriate option to fill in blank number 94.</p>",
                    question_hi: "<p>94. <strong>Cloze Test</strong> :<br>The ___(91)___age is the age of machines. From the ___(92)___ the industrial revolution began in Europe. Man&rsquo;s life has been changing ___(93)___ many ways. At first the change was ___(94)___. Now machines have become ___(95)___of our daily lives.<br>Select the most appropriate option to fill in blank number 94.</p>",
                    options_en: ["<p>Slow</p>", "<p>Steady</p>", 
                                "<p>Fast</p>", "<p>Stagnant</p>"],
                    options_hi: ["<p>Slow</p>", "<p>Steady</p>",
                                "<p>Fast</p>", "<p>Stagnant</p>"],
                    solution_en: "<p>94.(a) <strong>Slow</strong><br>Here &ldquo;slow&rdquo; will be the appropriate answer as the next sentence states that now machines have become a part of our daily lives.</p>",
                    solution_hi: "<p>94.(a) <strong>Slow</strong><br>यहाँ \"slow\" उपयुक्त उत्तर होगा क्योंकि अगला वाक्य बताता है कि अब मशीनें हमारे दैनिक जीवन का हिस्सा बन गई हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. <strong>Cloze Test</strong> :<br>The ___(91)___age is the age of machines. From the ___(92)___ the industrial revolution began in Europe. Man&rsquo;s life has been changing ___(93)___ many ways. At first the change was ___(94)___. Now machines have become ___(95)___of our daily lives.<br>Select the most appropriate option to fill in blank number 95.</p>",
                    question_hi: "<p>95. <strong>Cloze Test</strong> :<br>The ___(91)___age is the age of machines. From the ___(92)___ the industrial revolution began in Europe. Man&rsquo;s life has been changing ___(93)___ many ways. At first the change was ___(94)___. Now machines have become ___(95)___of our daily lives.<br>Select the most appropriate option to fill in blank number 95.</p>",
                    options_en: ["<p>Component</p>", "<p>Part</p>", 
                                "<p>Necessity</p>", "<p>Support</p>"],
                    options_hi: ["<p>Component</p>", "<p>Part</p>",
                                "<p>Necessity</p>", "<p>Support</p>"],
                    solution_en: "<p>95.(b) Part</p>",
                    solution_hi: "<p>95.(b) Part</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Comprehension:-</strong><br>A potential flashpoint in the long-running boundary dispute between Maharashtra and Karnataka was averted when the scheduled visit of two ministers in the Maharashtra government to Belgavi was called off. Unfortunately, this will not end the disruption endured by people in the area. Prohibitory orders have been imposed, public transport disrupted, and the atmosphere remains charged. The immediate trigger for this round of flare-ups is the conduct of senior functionaries in both governments and organisations with a stake in the issue.<br>The dispute goes back to reorganisation of states on linguistic lines in the 1950s. Multilingual regions were demarcated and Maharasthtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka. A joint effort by governments in the two states in the 1960s to find a solution did not fructify. The dispute currently is in the Supreme Court. In other words, no amount of grandstanding by politicians on both sides is going to result in redrawing borders. However, that hasn\'t prevented them from periodically raking it up with the consequences borne by people in the region. As is the case with border areas, many inhabitants speak both languages even as politicians are ostensibly fighting the cause of speakers of one language.<br>India does need a more active national interstate council to manage tensions that arise out of disagreements over maps. However, an inactive council cannot condone the conduct of senior politicians as heated rhetoric causes collateral damage. India\'s painstaking transition to dismantling interstate fiscal barriers through the roll-out of GST is undermined when physical movement of goods and people get disrupted by escalating tension. The political class that set aside differences to arrive at a grand bargain and created a common market in India should not undo their achievement. One of India\'s strengths is the extent of multilingualism even when internal borders have been determined largely on linguistic basis. Politicians need to learn from common Indians.<br>Select the most appropriate ANTONYM of the word &lsquo;impose&rsquo;.</p>",
                    question_hi: "<p>96. <strong>Comprehension:-</strong><br>A potential flashpoint in the long-running boundary dispute between Maharashtra and Karnataka was averted when the scheduled visit of two ministers in the Maharashtra government to Belgavi was called off. Unfortunately, this will not end the disruption endured by people in the area. Prohibitory orders have been imposed, public transport disrupted, and the atmosphere remains charged. The immediate trigger for this round of flare-ups is the conduct of senior functionaries in both governments and organisations with a stake in the issue.<br>The dispute goes back to reorganisation of states on linguistic lines in the 1950s. Multilingual regions were demarcated and Maharasthtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka. A joint effort by governments in the two states in the 1960s to find a solution did not fructify. The dispute currently is in the Supreme Court. In other words, no amount of grandstanding by politicians on both sides is going to result in redrawing borders. However, that hasn\'t prevented them from periodically raking it up with the consequences borne by people in the region. As is the case with border areas, many inhabitants speak both languages even as politicians are ostensibly fighting the cause of speakers of one language.<br>India does need a more active national interstate council to manage tensions that arise out of disagreements over maps. However, an inactive council cannot condone the conduct of senior politicians as heated rhetoric causes collateral damage. India\'s painstaking transition to dismantling interstate fiscal barriers through the roll-out of GST is undermined when physical movement of goods and people get disrupted by escalating tension. The political class that set aside differences to arrive at a grand bargain and created a common market in India should not undo their achievement. One of India\'s strengths is the extent of multilingualism even when internal borders have been determined largely on linguistic basis. Politicians need to learn from common Indians.<br>Select the most appropriate ANTONYM of the word &lsquo;impose&rsquo;.</p>",
                    options_en: ["<p>Compel</p>", "<p>Ennoble</p>", 
                                "<p>Release</p>", "<p>Cure</p>"],
                    options_hi: ["<p>Compel</p>", "<p>Ennoble</p>",
                                "<p>Release</p>", "<p>Cure</p>"],
                    solution_en: "<p>96.(c) <strong>Release-</strong> to set free, as from confinement, duty, work, etc.<br><strong>Impose</strong>- to officially force a rule, tax, punishment, etc. to be obeyed or received.<br><strong>Compel-</strong> to force someone to do something.<br><strong>Ennoble-</strong> to make someone a member of the highest social rank.<br><strong>Cure-</strong> to make someone with an illness healthy again.</p>",
                    solution_hi: "<p>96.(c) <strong>Release</strong> (मुक्त करना/रिहाई देना )- to set free, as from confinement, duty, work, etc.<br><strong>Impose</strong> (लागू करना)- to officially force a rule, tax, punishment, etc. to be obeyed or received.<br><strong>Compel</strong> (बाध्य करना)- to force someone to do something.<br><strong>Ennoble</strong> (उन्नति करना/प्रतिष्ठा बढ़ाना)- to make someone a member of the highest social rank.<br><strong>Cure</strong> (इलाज)- to make someone with an illness healthy again.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Comprehension</strong><br>A potential flashpoint in the long-running boundary dispute between Maharashtra and Karnataka was averted when the scheduled visit of two ministers in the Maharashtra government to Belgavi was called off. Unfortunately, this will not end the disruption endured by people in the area. Prohibitory orders have been imposed, public transport disrupted, and the atmosphere remains charged. The immediate trigger for this round of flare-ups is the conduct of senior functionaries in both governments and organisations with a stake in the issue.<br>The dispute goes back to reorganisation of states on linguistic lines in the 1950s. Multilingual regions were demarcated and Maharasthtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka. A joint effort by governments in the two states in the 1960s to find a solution did not fructify. The dispute currently is in the Supreme Court. In other words, no amount of grandstanding by politicians on both sides is going to result in redrawing borders. However, that hasn\'t prevented them from periodically raking it up with the consequences borne by people in the region. As is the case with border areas, many inhabitants speak both languages even as politicians are ostensibly fighting the cause of speakers of one language.<br>India does need a more active national interstate council to manage tensions that arise out of disagreements over maps. However, an inactive council cannot condone the conduct of senior politicians as heated rhetoric causes collateral damage. India\'s painstaking transition to dismantling interstate fiscal barriers through the roll-out of GST is undermined when physical movement of goods and people get disrupted by escalating tension. The political class that set aside differences to arrive at a grand bargain and created a common market in India should not undo their achievement. One of India\'s strengths is the extent of multilingualism even when internal borders have been determined largely on linguistic basis. Politicians need to learn from common Indians.<br>Reorganisation of states in the 1950s was done based on __________.</p>",
                    question_hi: "<p>97. <strong>Comprehension</strong><br>A potential flashpoint in the long-running boundary dispute between Maharashtra and Karnataka was averted when the scheduled visit of two ministers in the Maharashtra government to Belgavi was called off. Unfortunately, this will not end the disruption endured by people in the area. Prohibitory orders have been imposed, public transport disrupted, and the atmosphere remains charged. The immediate trigger for this round of flare-ups is the conduct of senior functionaries in both governments and organisations with a stake in the issue.<br>The dispute goes back to reorganisation of states on linguistic lines in the 1950s. Multilingual regions were demarcated and Maharasthtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka. A joint effort by governments in the two states in the 1960s to find a solution did not fructify. The dispute currently is in the Supreme Court. In other words, no amount of grandstanding by politicians on both sides is going to result in redrawing borders. However, that hasn\'t prevented them from periodically raking it up with the consequences borne by people in the region. As is the case with border areas, many inhabitants speak both languages even as politicians are ostensibly fighting the cause of speakers of one language.<br>India does need a more active national interstate council to manage tensions that arise out of disagreements over maps. However, an inactive council cannot condone the conduct of senior politicians as heated rhetoric causes collateral damage. India\'s painstaking transition to dismantling interstate fiscal barriers through the roll-out of GST is undermined when physical movement of goods and people get disrupted by escalating tension. The political class that set aside differences to arrive at a grand bargain and created a common market in India should not undo their achievement. One of India\'s strengths is the extent of multilingualism even when internal borders have been determined largely on linguistic basis. Politicians need to learn from common Indians.<br>Reorganisation of states in the 1950s was done based on __________.</p>",
                    options_en: [" religious lines ", " castes ", 
                                " linguistic lines ", " economic lines"],
                    options_hi: [" religious lines ", " castes ",
                                " linguistic lines ", " economic lines"],
                    solution_en: "<p>97.(c) <strong>Linguistic lines</strong><br>(Line/s from the passage- The dispute goes back to reorganisation of states on linguistic lines in the 1950s).</p>",
                    solution_hi: "<p>97.(c) <strong>Linguistic lines</strong><br>(Passage से ली गई line/s- The dispute goes back to reorganisation of states on linguistic lines in the 1950s./यह विवाद 1950 के दशक में भाषाई आधार पर राज्यों के पुनर्गठन से संबंधित है।)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Comprehension</strong><br>A potential flashpoint in the long-running boundary dispute between Maharashtra and Karnataka was averted when the scheduled visit of two ministers in the Maharashtra government to Belgavi was called off. Unfortunately, this will not end the disruption endured by people in the area. Prohibitory orders have been imposed, public transport disrupted, and the atmosphere remains charged. The immediate trigger for this round of flare-ups is the conduct of senior functionaries in both governments and organisations with a stake in the issue.<br>The dispute goes back to reorganisation of states on linguistic lines in the 1950s. Multilingual regions were demarcated and Maharasthtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka. A joint effort by governments in the two states in the 1960s to find a solution did not fructify. The dispute currently is in the Supreme Court. In other words, no amount of grandstanding by politicians on both sides is going to result in redrawing borders. However, that hasn\'t prevented them from periodically raking it up with the consequences borne by people in the region. As is the case with border areas, many inhabitants speak both languages even as politicians are ostensibly fighting the cause of speakers of one language.<br>India does need a more active national interstate council to manage tensions that arise out of disagreements over maps. However, an inactive council cannot condone the conduct of senior politicians as heated rhetoric causes collateral damage. India\'s painstaking transition to dismantling interstate fiscal barriers through the roll-out of GST is undermined when physical movement of goods and people get disrupted by escalating tension. The political class that set aside differences to arrive at a grand bargain and created a common market in India should not undo their achievement. One of India\'s strengths is the extent of multilingualism even when internal borders have been determined largely on linguistic basis. Politicians need to learn from common Indians.<br>Identify the most suitable title for the passage.</p>",
                    question_hi: "<p>98. <strong>Comprehension</strong><br>A potential flashpoint in the long-running boundary dispute between Maharashtra and Karnataka was averted when the scheduled visit of two ministers in the Maharashtra government to Belgavi was called off. Unfortunately, this will not end the disruption endured by people in the area. Prohibitory orders have been imposed, public transport disrupted, and the atmosphere remains charged. The immediate trigger for this round of flare-ups is the conduct of senior functionaries in both governments and organisations with a stake in the issue.<br>The dispute goes back to reorganisation of states on linguistic lines in the 1950s. Multilingual regions were demarcated and Maharasthtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka. A joint effort by governments in the two states in the 1960s to find a solution did not fructify. The dispute currently is in the Supreme Court. In other words, no amount of grandstanding by politicians on both sides is going to result in redrawing borders. However, that hasn\'t prevented them from periodically raking it up with the consequences borne by people in the region. As is the case with border areas, many inhabitants speak both languages even as politicians are ostensibly fighting the cause of speakers of one language.<br>India does need a more active national interstate council to manage tensions that arise out of disagreements over maps. However, an inactive council cannot condone the conduct of senior politicians as heated rhetoric causes collateral damage. India\'s painstaking transition to dismantling interstate fiscal barriers through the roll-out of GST is undermined when physical movement of goods and people get disrupted by escalating tension. The political class that set aside differences to arrive at a grand bargain and created a common market in India should not undo their achievement. One of India\'s strengths is the extent of multilingualism even when internal borders have been determined largely on linguistic basis. Politicians need to learn from common Indians.<br>Identify the most suitable title for the passage.</p>",
                    options_en: ["<p>political Leaders and their Corruption</p>", "<p>Border Disputes and Political Fuel</p>", 
                                "<p>Linguistic Love of the People</p>", "<p>Unending Love of Religion</p>"],
                    options_hi: ["<p>political Leaders and their Corruption</p>", "<p>Border Disputes and Political Fuel</p>",
                                "<p>Linguistic Love of the People</p>", "<p>Unending Love of Religion</p>"],
                    solution_en: "<p>98.(b) Border Disputes and Political Fuel<br>It can be inferred from the passage that the most suitable title for the passage is &lsquo;Border disputes and Political Fuel&rsquo;.</p>",
                    solution_hi: "<p>98.(b) Border Disputes and Political Fuel<br>दिए गए Passage से यह अनुमान लगाया जा सकता है कि passage के लिए सबसे उपयुक्त title &lsquo;Border disputes and Political Fuel&rsquo; है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Comprehension</strong><br>A potential flashpoint in the long-running boundary dispute between Maharashtra and Karnataka was averted when the scheduled visit of two ministers in the Maharashtra government to Belgavi was called off. Unfortunately, this will not end the disruption endured by people in the area. Prohibitory orders have been imposed, public transport disrupted, and the atmosphere remains charged. The immediate trigger for this round of flare-ups is the conduct of senior functionaries in both governments and organisations with a stake in the issue.<br>The dispute goes back to reorganisation of states on linguistic lines in the 1950s. Multilingual regions were demarcated and Maharasthtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka. A joint effort by governments in the two states in the 1960s to find a solution did not fructify. The dispute currently is in the Supreme Court. In other words, no amount of grandstanding by politicians on both sides is going to result in redrawing borders. However, that hasn\'t prevented them from periodically raking it up with the consequences borne by people in the region. As is the case with border areas, many inhabitants speak both languages even as politicians are ostensibly fighting the cause of speakers of one language.<br>India does need a more active national interstate council to manage tensions that arise out of disagreements over maps. However, an inactive council cannot condone the conduct of senior politicians as heated rhetoric causes collateral damage. India\'s painstaking transition to dismantling interstate fiscal barriers through the roll-out of GST is undermined when physical movement of goods and people get disrupted by escalating tension. The political class that set aside differences to arrive at a grand bargain and created a common market in India should not undo their achievement. One of India\'s strengths is the extent of multilingualism even when internal borders have been determined largely on linguistic basis. Politicians need to learn from common Indians.<br>What is the apple of discord mentioned in the passage ?</p>",
                    question_hi: "<p>99. <strong>Comprehension</strong><br>A potential flashpoint in the long-running boundary dispute between Maharashtra and Karnataka was averted when the scheduled visit of two ministers in the Maharashtra government to Belgavi was called off. Unfortunately, this will not end the disruption endured by people in the area. Prohibitory orders have been imposed, public transport disrupted, and the atmosphere remains charged. The immediate trigger for this round of flare-ups is the conduct of senior functionaries in both governments and organisations with a stake in the issue.<br>The dispute goes back to reorganisation of states on linguistic lines in the 1950s. Multilingual regions were demarcated and Maharasthtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka. A joint effort by governments in the two states in the 1960s to find a solution did not fructify. The dispute currently is in the Supreme Court. In other words, no amount of grandstanding by politicians on both sides is going to result in redrawing borders. However, that hasn\'t prevented them from periodically raking it up with the consequences borne by people in the region. As is the case with border areas, many inhabitants speak both languages even as politicians are ostensibly fighting the cause of speakers of one language.<br>India does need a more active national interstate council to manage tensions that arise out of disagreements over maps. However, an inactive council cannot condone the conduct of senior politicians as heated rhetoric causes collateral damage. India\'s painstaking transition to dismantling interstate fiscal barriers through the roll-out of GST is undermined when physical movement of goods and people get disrupted by escalating tension. The political class that set aside differences to arrive at a grand bargain and created a common market in India should not undo their achievement. One of India\'s strengths is the extent of multilingualism even when internal borders have been determined largely on linguistic basis. Politicians need to learn from common Indians.<br>What is the apple of discord mentioned in the passage ?</p>",
                    options_en: ["<p>Mumbai</p>", "<p>Bellari</p>", 
                                "<p>Belgavi</p>", "<p>Bengaluru</p>"],
                    options_hi: ["<p>Mumbai</p>", "<p>Bellari</p>",
                                "<p>Belgavi</p>", "<p>Bengaluru</p>"],
                    solution_en: "<p>99.(c) Belgavi<br>(Line/s from the passage- Multilingual regions were demarcated and Maharashtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka.)</p>",
                    solution_hi: "<p>99.(c) Belgavi<br>(Passage से ली गई line/s- Multilingual regions were demarcated and Maharasthtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka./बहुभाषी क्षेत्रों का सीमांकन किया गया और महाराष्ट्र सरकार कर्नाटक को बेलगावी के आवंटन से असंतुष्ट हैं।)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Comprehension</strong><br>A potential flashpoint in the long-running boundary dispute between Maharashtra and Karnataka was averted when the scheduled visit of two ministers in the Maharashtra government to Belgavi was called off. Unfortunately, this will not end the disruption endured by people in the area. Prohibitory orders have been imposed, public transport disrupted, and the atmosphere remains charged. The immediate trigger for this round of flare-ups is the conduct of senior functionaries in both governments and organisations with a stake in the issue.<br>The dispute goes back to reorganisation of states on linguistic lines in the 1950s. Multilingual regions were demarcated and Maharasthtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka. A joint effort by governments in the two states in the 1960s to find a solution did not fructify. The dispute currently is in the Supreme Court. In other words, no amount of grandstanding by politicians on both sides is going to result in redrawing borders. However, that hasn\'t prevented them from periodically raking it up with the consequences borne by people in the region. As is the case with border areas, many inhabitants speak both languages even as politicians are ostensibly fighting the cause of speakers of one language.<br>India does need a more active national interstate council to manage tensions that arise out of disagreements over maps. However, an inactive council cannot condone the conduct of senior politicians as heated rhetoric causes collateral damage. India\'s painstaking transition to dismantling interstate fiscal barriers through the roll-out of GST is undermined when physical movement of goods and people get disrupted by escalating tension. The political class that set aside differences to arrive at a grand bargain and created a common market in India should not undo their achievement. One of India\'s strengths is the extent of multilingualism even when internal borders have been determined largely on linguistic basis. Politicians need to learn from common Indians.<br>What is the passage based upon?</p>",
                    question_hi: "<p>100. <strong>Comprehension</strong><br>A potential flashpoint in the long-running boundary dispute between Maharashtra and Karnataka was averted when the scheduled visit of two ministers in the Maharashtra government to Belgavi was called off. Unfortunately, this will not end the disruption endured by people in the area. Prohibitory orders have been imposed, public transport disrupted, and the atmosphere remains charged. The immediate trigger for this round of flare-ups is the conduct of senior functionaries in both governments and organisations with a stake in the issue.<br>The dispute goes back to reorganisation of states on linguistic lines in the 1950s. Multilingual regions were demarcated and Maharasthtra\'s governments have been unsatisfied with the allocation of Belgavi to Karnataka. A joint effort by governments in the two states in the 1960s to find a solution did not fructify. The dispute currently is in the Supreme Court. In other words, no amount of grandstanding by politicians on both sides is going to result in redrawing borders. However, that hasn\'t prevented them from periodically raking it up with the consequences borne by people in the region. As is the case with border areas, many inhabitants speak both languages even as politicians are ostensibly fighting the cause of speakers of one language.<br>India does need a more active national interstate council to manage tensions that arise out of disagreements over maps. However, an inactive council cannot condone the conduct of senior politicians as heated rhetoric causes collateral damage. India\'s painstaking transition to dismantling interstate fiscal barriers through the roll-out of GST is undermined when physical movement of goods and people get disrupted by escalating tension. The political class that set aside differences to arrive at a grand bargain and created a common market in India should not undo their achievement. One of India\'s strengths is the extent of multilingualism even when internal borders have been determined largely on linguistic basis. Politicians need to learn from common Indians.<br>What is the passage based upon?</p>",
                    options_en: ["<p>Political leaders and their opportunism in using court decisions for their own pastime</p>", "<p>The bureaucracy in India and the egoism of the leaders in the administration</p>", 
                                "<p>Existing border dispute between karnataka and Maharashtra fuelled often by politicians, not common men</p>", "<p>Linguistic narrowmindedness of the common men of the two states karnataka and maharashtra</p>"],
                    options_hi: ["<p>Political leaders and their opportunism in using court decisions for their own pastime</p>", "<p>The bureaucracy in India and the egoism of the leaders in the administration</p>",
                                "<p>Existing border dispute between karnataka and Maharashtra fuelled often by politicians, not common men</p>", "<p>Linguistic narrowmindedness of the common men of the two states karnataka and maharashtra</p>"],
                    solution_en: "<p>100.(c) Existing border dispute between Karnataka and Maharashtra fuelled often by politicians, not common men.<br>The passage discusses the Karnataka-Maharashtra border dispute, often worsened by politicians rather than common people, causing disruptions and tension in the border area. Hence, option (c) is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) Existing border dispute between karnataka and Maharashtra fuelled often by politicians, not common men.<br>दिए गए passage में कर्नाटक-महाराष्ट्र सीमा विवाद पर चर्चा की गई है, जिसे अक्सर आम लोगों के बजाय राजनेताओं(politician) द्वारा और भी बदतर बना दिया जाता है, जिससे सीमा क्षेत्र(border area) में व्यवधान(disruptions) और तनाव पैदा होता है। अतः, option (c) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>