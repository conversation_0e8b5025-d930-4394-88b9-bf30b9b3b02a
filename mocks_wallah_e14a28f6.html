<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Which of the following letter-clusters should replace # and % so that the pattern and relationship followed between the letter-cluster pair on the left side of :: is the same as that on the right side of :: ? <br># : LNI :: QSN : %</p>",
                    question_hi: "<p>1. निम्नलिखित में से किन अक्षर-समूहों द्वारा # और % को प्रतिस्थापित करने पर :: के बाईं ओर के अक्षर-समूह युग्म के बीच का पैटर्न और संबंध :: के दाईं ओर के अक्षर-समूह युग्म के बीच के पैटर्न और संबंध के समान होगा?<br># : LNI :: QSN : %</p>",
                    options_en: ["<p># = JDD, % = VNH</p>", "<p># = JHD, % = MJP</p>", 
                                "<p># = GID, % = VXS</p>", "<p># = LKD, % = VML</p>"],
                    options_hi: ["<p># = JDD, % = VNH</p>", "<p># = JHD, % = MJP</p>",
                                "<p># = GID, % = VXS</p>", "<p># = LKD, % = VML</p>"],
                    solution_en: "<p>1.(c) # = GID, % = VXS.<br>The transformation follows a consistent pattern:<br>For # : LNI, each letter of GID is increased by 5 to get LNI.<br>For QSN : %, adding 5 to each letter of QSN gives VXS.</p>",
                    solution_hi: "<p>1.(c) # = GID, % = VXS.<br>परिवर्तन एक निरंतर पैटर्न का पालन करता है:<br># : LNI के लिए, GID के प्रत्येक अक्षर को 5 से बढ़ाकर LNI प्राप्त किया जाता है।<br>QSN : % के लिए, QSN के प्रत्येक अक्षर में 5 जोड़ने से VXS मिलता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the correct mirror image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317034.png\" alt=\"rId4\" width=\"119\" height=\"128\"></p>",
                    question_hi: "<p>2. दर्पण को MN पर रखे जाने पर दी गई आकृति का दर्पण में निर्मित सही प्रतिबिंब का चयन करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317034.png\" alt=\"rId4\" width=\"105\" height=\"112\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317182.png\" alt=\"rId5\" width=\"92\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317300.png\" alt=\"rId6\" width=\"90\" height=\"88\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317399.png\" alt=\"rId7\" width=\"91\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317490.png\" alt=\"rId8\" width=\"91\" height=\"89\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317182.png\" alt=\"rId5\" width=\"91\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317300.png\" alt=\"rId6\" width=\"90\" height=\"88\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317399.png\" alt=\"rId7\" width=\"90\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317490.png\" alt=\"rId8\" width=\"92\" height=\"90\"></p>"],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317182.png\" alt=\"rId5\" width=\"101\" height=\"99\"></p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317182.png\" alt=\"rId5\" width=\"100\" height=\"98\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. The same operation(s) are followed in all the given number pairs except one. Find that odd number pair. <br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>3. दिए गए संख्या युग्मों में से एक को छोड़कर अन्य सभी में समान संक्रिया/संक्रियाओं का अनुसरण किया गया है। वह असंगत संख्या युग्म ज्ञात कीजिए। <br>(<strong>नोट :</strong> संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: ["<p>56 : 9</p>", "<p>44 : 7</p>", 
                                "<p>68 : 11</p>", "<p>72 : 12</p>"],
                    options_hi: ["<p>56 : 9</p>", "<p>44 : 7</p>",
                                "<p>68 : 11</p>", "<p>72 : 12</p>"],
                    solution_en: "<p>3.(d) <strong>Logic:</strong> 2nd &times; 6 + 2 = 1st number <br>56 : 9 :- 9 &times; 6 + 2 = 56<br>44 : 7 :- 7 &times; 6 + 2 = 44<br>68 : 11 :- 11 &times; 6 + 2 = 68<br>But<br>72 : 12 :- 12 &times; 6 + 2 = 74 (&ne; 72)</p>",
                    solution_hi: "<p>3.(d) <strong>तर्क : </strong>दूसरी संख्या &times; 6 + 2 = पहली संख्या <br>56 : 9 :- 9 &times; 6 + 2 = 56<br>44 : 7 :- 7 &times; 6 + 2 = 44<br>68 : 11 :- 11 &times; 6 + 2 = 68<br>लेकिन <br>72 : 12 :- 12 &times; 6 + 2 = 74 (&ne; 72)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last. How would the paper look when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317593.png\" alt=\"rId9\" width=\"248\" height=\"84\"></p>",
                    question_hi: "<p>4. कागज की एक वर्गाकार शीट को दर्शाई गई दिशाओ में बिंदीदार रेखा पर अनुक्रमशः मोड़ा जाता है और आखिर में उसमें छेद किया जाता है। खोलने पर यह कागज कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317593.png\" alt=\"rId9\" width=\"248\" height=\"84\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317723.png\" alt=\"rId10\" width=\"90\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317847.png\" alt=\"rId11\" width=\"91\" height=\"87\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317948.png\" alt=\"rId12\" width=\"99\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318061.png\" alt=\"rId13\" width=\"90\" height=\"86\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317723.png\" alt=\"rId10\" width=\"90\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317847.png\" alt=\"rId11\" width=\"90\" height=\"86\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639317948.png\" alt=\"rId12\" width=\"92\" height=\"84\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318061.png\" alt=\"rId13\" width=\"93\" height=\"89\"></p>"],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318061.png\" alt=\"rId13\" width=\"91\" height=\"87\"></p>",
                    solution_hi: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318061.png\" alt=\"rId13\" width=\"93\" height=\"89\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In a certain code language, \'GOLIATH\' is coded as \'216\' and \'DYNAMIC\' is coded as \'207\'. How will \'CURRANT\' be coded in that language ?</p>",
                    question_hi: "<p>5. एक निश्चित कूट भाषा में \'GOLIATH\' को \'216\' और \'DYNAMIC\' को \'207\' के रूप में कूटबद्ध किया जाता है। उसी कूट भाषा में \'CURRANT\' को किस प्रकार कूटबद्ध किया जाएगा ?</p>",
                    options_en: ["<p>285</p>", "<p>235</p>", 
                                "<p>168</p>", "<p>148</p>"],
                    options_hi: ["<p>285</p>", "<p>235</p>",
                                "<p>168</p>", "<p>148</p>"],
                    solution_en: "<p>5.(a) <strong>Logic :-</strong> (Sum of the place value of letter) &times; 3<br>GOLIATH :- (7 + 15 + 12 + 9 + 1 + 20 + 8) &times; 3 &rArr; (72) &times; 3 = 216<br>DYNAMIC :- (4 + 25 + 14 + 1 + 13 + 9 + 3) &times; 3 &rArr; (69) &times; 3 = 207<br>Similarly,<br>CURRANT :- (3 + 21 + 18 + 18 + 1 + 14 + 20) &times; 3 &rArr; (95) &times; 3 = 285</p>",
                    solution_hi: "<p>5.(a) <strong>तर्क :-</strong> (अक्षर के स्थानीय मान का योग) &times; 3<br>GOLIATH :- (7 + 15 + 12 + 9 + 1 + 20 + 8) &times; 3 &rArr; (72) &times; 3 = 216<br>DYNAMIC :- (4 + 25 + 14 + 1 + 13 + 9 + 3) &times; 3 &rArr; (69) &times; 3 = 207<br>इसी प्रकार,<br>CURRANT :- (3 + 21 + 18 + 18 + 1 + 14 + 20) &times; 3 &rArr; (95) &times; 3 = 285</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Which of the following letter-clusters will replace the question mark (?) in the given series to make it logically complete? <br>OEK 16 , PJN 9, ? , RTT &minus;11, SYW &minus;24</p>",
                    question_hi: "<p>6. निम्नलिखित अक्षर-समूह श्रृंखला में, प्रश्न चिह्न (?) को किस अक्षर-समूह द्वारा प्रतिस्थापित किए जाने पर श्रृंखला तार्किक रूप से पूर्ण हो जाएगी?<br>OEK 16 , PJN 9 , ? , RTT &minus;11 , SYW &minus;24</p>",
                    options_en: ["<p>OUI 9</p>", "<p>QOQ 0</p>", 
                                "<p>ROP 3</p>", "<p>QIO 7</p>"],
                    options_hi: ["<p>OUI 9</p>", "<p>QOQ 0</p>",
                                "<p>ROP 3</p>", "<p>QIO 7</p>"],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318242.png\" alt=\"rId14\" width=\"320\" height=\"78\"></p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318242.png\" alt=\"rId14\" width=\"320\" height=\"78\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Which two numbers should be interchanged to make the given equation correct?<br>(99 &minus; 42 + 11) + (36 &divide; 6) &times; 7 &minus; 50 = 73<br>(NOTE: Numbers must be interchanged and not the constituent digits e.g. if 2 and 3&nbsp;are to be interchanged in the equation 43 &times; 3 + 4 &divide; 2, then interchanged equation is 43&nbsp;&times; 2 + 4 &divide; 3)</p>",
                    question_hi: "<p>7. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए?<br>(99 &minus; 42 + 11) + (36 &divide; 6) &times; 7 &minus; 50 = 73<br>(ध्यान दें: संख्याओं को आपस में बदला जाना चाहिए और घटक अंकों को नहीं बदला जाना चाहिए।<br>उदाहरण यदि समीकरण 43 &times; 3 + 4 &divide; 2 में 2 और 3 को आपस में बदलना है, तो बदला हुआ समीकरण 43&times; 2 + 4 &divide; 3 होगा)</p>",
                    options_en: ["<p>7 and 11</p>", "<p>50 and 99</p>", 
                                "<p>50 and 11</p>", "<p>42 and 36</p>"],
                    options_hi: ["<p>7 और 11</p>", "<p>50 और 11</p>",
                                "<p>50 और 99</p>", "<p>42 और 36</p>"],
                    solution_en: "<p>7.(d)<strong> Given :-</strong> (99 - 42 + 11) + (36 &divide; 6) &times; 7 - 50 = 73<br>After going through all the options, option d satisfies. After interchanging 42 and 36 we get<br>(99 - 36 + 11) + (42 &divide; 6) &times; 7 - 50<br>74 + 49 - 50 = 73<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>7.(d) <strong>दिया गया :- </strong>(99 - 42 + 11) + (36 &divide; 6)&times; 7 - 50 = 73<br>सभी विकल्पों की जांच करने पर विकल्प d संतुष्ट करता है। 42 और 36 को आपस में बदलने पर हमें प्राप्त होता है<br>(99 - 36 + 11) + (42 &divide; 6) &times; 7 - 50<br>74 + 49 - 50 = 73<br>L.H.S. = R.H.S.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent&nbsp;digits. E.g. 13- Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(21, 3, 15)<br>(49, 7, 35)</p>",
                    question_hi: "<p>8. उस समुच्चय का चयन करें जिसकी संख्याएं उसी तरह से संबंधित हैं जिस तरह निम्नलिखित समुच्चयों की संख्याएं संबंधित हैं।<br><strong>नोट:</strong> संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें -13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)<br>(21, 3, 15)<br>(49, 7, 35)</p>",
                    options_en: ["<p>(84, 12, 62)</p>", "<p>(85, 12, 60)</p>", 
                                "<p>(84,12,60)</p>", "<p>(85, 13,60)</p>"],
                    options_hi: ["<p>(84, 12, 62)</p>", "<p>(85, 12, 60)</p>",
                                "<p>(84,12,60)</p>", "<p>(85, 13,60)</p>"],
                    solution_en: "<p>8.(c) <strong>Logic :-</strong> (1st number - 3rd number) &divide; 2 = 2nd number<br>(21, 3 ,15) :- (21 - 15) &divide; 2 = 3<br>(49, 7, 35) :- (49 - 35) &divide; 2 = 7<br>Similarly,<br>(84, 12, 60) :- (84 - 60) &divide; 2 = 12</p>",
                    solution_hi: "<p>8.(c) <strong>तर्क :- </strong>(पहली संख्या - तीसरी संख्या) &divide; 2 = दूसरी संख्या<br>(21, 3 ,15) :- (21 - 15) &divide; 2 = 3<br>(49, 7, 35) :- (49 - 35) &divide; 2 = 7<br>इसी प्रकार,<br>(84, 12, 60) :- (84 - 60) &divide; 2 = 12</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the option that represents the letters that, when sequentially placed from left to right in the blanks below, will complete the letter series.<br>JJ_K_LM_N_ _O_ _Q _ R _</p>",
                    question_hi: "<p>9. उस विकल्प का चयन कीजिए जो उन अक्षरों का निरूपण करते हैं, जिन्हें नीचे दिए गए रिक्त स्थान में क्रमिक रूप से बाएं से दाएं रखे जाने पर अक्षर श्रृंखला पूर्ण हो जाए।<br>JJ_K_LM_N_ _O_ _Q_R_</p>",
                    options_en: ["<p>KLMNOPPQR</p>", "<p>IJKLMNOPQ</p>", 
                                "<p>KLNOPPQRS</p>", "<p>JKNNOOPRR</p>"],
                    options_hi: ["<p>KLMNOPPQR</p>", "<p>IJKLMNOPQ</p>",
                                "<p>KLNOPPQRS</p>", "<p>JKNNOOPRR</p>"],
                    solution_en: "<p>9.(a) <br>JJ<span style=\"text-decoration: underline;\"><strong>K</strong></span>K<span style=\"text-decoration: underline;\"><strong>L</strong></span>L / M<span style=\"text-decoration: underline;\"><strong>M</strong></span>N<span style=\"text-decoration: underline;\"><strong>N O</strong></span>O / <span style=\"text-decoration: underline;\"><strong>P P</strong></span>Q<span style=\"text-decoration: underline;\"><strong>Q</strong></span> R <span style=\"text-decoration: underline;\"><strong>R</strong></span><br>The pairs of letters are increasing by 3.</p>",
                    solution_hi: "<p>9.(a) <br>JJ<span style=\"text-decoration: underline;\"><strong>K</strong></span>K<span style=\"text-decoration: underline;\"><strong>L</strong></span>L / M<span style=\"text-decoration: underline;\"><strong>M</strong></span>N<span style=\"text-decoration: underline;\"><strong>N O</strong></span>O / <span style=\"text-decoration: underline;\"><strong>P P</strong></span>Q<span style=\"text-decoration: underline;\"><strong>Q</strong></span> R <span style=\"text-decoration: underline;\"><strong>R</strong></span><br>अक्षरों के जोड़े 3 से बढ़ रहे हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusion(s) logically follow(s) from the statements. <br><strong>Statements :</strong> <br>All stoves are cylinders. <br>All cylinders are mixers. <br>Some cylinders are grinders. <br><strong>Conclusions :</strong> <br>(I) Some grinders are cylinders. <br>(II) Some mixers are grinders.</p>",
                    question_hi: "<p>10. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। कथनों में दी गई जानकारी को सत्य मानते हुए, भले ही यह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय कीजिए कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है। करते हैं।<br><strong>कथन :</strong><br>सभी स्टोव, सिलेंडर हैं।<br>सभी सिलेंडर, मिक्सर हैं।<br>कुछ सिलेंडर, ग्राइंडर हैं।<br><strong>निष्कर्ष :</strong><br>(I) कुछ ग्राइंडर, सिलेंडर हैं।<br>(II) कुछ मिक्सर, ग्राइंडर हैं।</p>",
                    options_en: ["<p>Both conclusions (I) and (II) follow.</p>", "<p>Only conclusion (II) follows.</p>", 
                                "<p>Neither conclusion (I) nor (II) follows.</p>", "<p>Only conclusion (I) follows.</p>"],
                    options_hi: ["<p>निष्कर्ष (I) और (II), दोनों अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>",
                                "<p>न तो निष्कर्ष (I) और न ही (II) अनुसरण करता है।</p>", "<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>"],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318471.png\" alt=\"rId15\" width=\"208\" height=\"105\"><br>Hence, both conclusions I and II follow.</p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318706.png\" alt=\"rId16\" width=\"212\" height=\"128\"><br>अतः, निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. What should come in place of the question mark (?) in the given series?<br>13, 17, 26, ?, 67, 103, 152</p>",
                    question_hi: "<p>11. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>13, 17, 26, ?, 67, 103, 152</p>",
                    options_en: ["<p>59</p>", "<p>42</p>", 
                                "<p>33</p>", "<p>47</p>"],
                    options_hi: ["<p>59</p>", "<p>42</p>",
                                "<p>33</p>", "<p>47</p>"],
                    solution_en: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318895.png\" alt=\"rId17\" width=\"233\" height=\"93\"></p>",
                    solution_hi: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318895.png\" alt=\"rId17\" width=\"233\" height=\"93\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. RN 13 is related to TM -6 in a certain way. In the same way, LO 9 is related to NN -10. To which of the following is OS 3 related, following the same logic? <br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>12. RN 13, किसी प्रकार TM-6 से संबंधित है। LO 9, ठीक उसी प्रकार NN-10 से संबंधित है। समान तर्क का अनुसरण करते हुए, OS 3, निम्नलिखित में से किससे संबंधित है?<br>(<strong>नोटः </strong>संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 लीजिए - 13 पर संक्रिया जैसे कि 13 में जोड़ना/घटाना/गुणा करना 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रिया करना अनुमत नहीं है।)</p>",
                    options_en: ["<p>QT -16</p>", "<p>PR -14</p>", 
                                "<p>QR -16</p>", "<p>PS -19</p>"],
                    options_hi: ["<p>QT -16</p>", "<p>PR -14</p>",
                                "<p>QR -16</p>", "<p>PS -19</p>"],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318997.png\" alt=\"rId18\" width=\"110\" height=\"105\">&nbsp; and&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319085.png\" alt=\"rId19\" width=\"114\" height=\"105\"><br>Similarly&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319214.png\" alt=\"rId20\" width=\"109\" height=\"101\"></p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639318997.png\" alt=\"rId18\" width=\"110\" height=\"105\"> &nbsp;और <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319085.png\" alt=\"rId19\" width=\"114\" height=\"105\"><br>इसी प्रकार,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319214.png\" alt=\"rId20\" width=\"109\" height=\"101\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain code language, <br>A + B means &lsquo;A is the sister of B&rsquo;, <br>A &ndash; B means &lsquo;A is the father of B&rsquo;, <br>A &times; B means &lsquo;A is the brother of B&rsquo; and <br>A &divide; B means &lsquo;A is the wife of B&rsquo;. <br>How is P related to T if &lsquo;P &times; Q &divide; R &ndash; S + T&rsquo;?</p>",
                    question_hi: "<p>13. किसी निश्चित कूट भाषा में,<br>A + B का अर्थ है कि \'A, B की बहन है\',<br>A - B का अर्थ है कि \'A, B का पिता है\',<br>A &times; B का अर्थ है कि \'A, B का भाई है\' और<br>A &divide; B का अर्थ है कि \'A, B की पत्नी है\'।<br>यदि &lsquo;P &times; Q &divide; R &ndash; S + T&rsquo; है, तो P का T से क्या संबंध है?</p>",
                    options_en: ["<p>Daughter&rsquo;s son</p>", "<p>Sister&rsquo;s husband</p>", 
                                "<p>Mother&rsquo;s brother</p>", "<p>Father&rsquo;s brother</p>"],
                    options_hi: ["<p>पुत्री का पुत्र</p>", "<p>बहन का पति</p>",
                                "<p>माँ का भाई</p>", "<p>पिता का भाई</p>"],
                    solution_en: "<p>13.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319345.png\" alt=\"rId21\" width=\"233\" height=\"85\"><br>Hence, we can see that &lsquo;P&rsquo; is the mother\'s brother of &lsquo;T&rsquo;.</p>",
                    solution_hi: "<p>13.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319345.png\" alt=\"rId21\" width=\"233\" height=\"85\"><br>इसलिए, हम देख सकते हैं कि \'P\', \'T\' की माँ का भाई है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, &lsquo;DARK&rsquo; is coded as &lsquo;3284&rsquo; and &lsquo;RAKE&rsquo; is coded as&nbsp;&lsquo;6438&rsquo;.What is the code for &lsquo;E&rsquo; in that language?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में, \'DARK\' को \'3284\' के रूप में कूटबद्ध किया जाता है और \'RAKE\' को \'6438\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'E\' के लिए कूट क्या है?</p>",
                    options_en: ["<p>4</p>", "<p>3</p>", 
                                "<p>6</p>", "<p>8</p>"],
                    options_hi: ["<p>4</p>", "<p>3</p>",
                                "<p>6</p>", "<p>8</p>"],
                    solution_en: "<p>14.(c) <br>DARK :- 3284&hellip;..(i)<br>RAKE :- 6438&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;ARK&rsquo; and &lsquo;348&rsquo; are common. The code of &lsquo;E&rsquo; = &lsquo;6&rsquo;.</p>",
                    solution_hi: "<p>14.(c) <br>DARK :- 3284&hellip;..(i)<br>RAKE :- 6438&hellip;&hellip;.(ii)<br>(i) और (ii) से &lsquo;ARK&rsquo; और &lsquo;348&rsquo; उभयनिष्ठ हैं। &lsquo;E&rsquo; का कोड = &lsquo;6&rsquo; है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. If &lsquo;A&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&minus;&rsquo;, what will be come in place of question mark &lsquo;?&rsquo; in the following equation? <br>3 B 41 A 3 D 34 C 17 = ?</p>",
                    question_hi: "<p>15. यदि \'A\' का अर्थ &lsquo;&divide;&rsquo; है, \'B\' का अर्थ \'&times;\' है, \'C\' का अर्थ \'+\' है और \'D\' का अर्थ &lsquo;&minus;&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न- चिह्न \'?\' के स्थान पर क्या आएगा?<br>3 B 41 A 3 D 34 C 17 = ?</p>",
                    options_en: ["<p>25</p>", "<p>24</p>", 
                                "<p>26</p>", "<p>22</p>"],
                    options_hi: ["<p>25</p>", "<p>24</p>",
                                "<p>26</p>", "<p>22</p>"],
                    solution_en: "<p>15.(b)<br>3 B 41 A 3 D 34 C 17<br>After interchanging signs as per instructions, we get<br>3 &times;&nbsp;41 &divide; 3 - 34 + 17<br>= 41 -&nbsp;17 = 24</p>",
                    solution_hi: "<p>15.(b)<br>3 B 41 A 3 D 34 C 17<br>निर्देशों के अनुसार संकेतों को बदलने के बाद हमें प्राप्त होता है <br>3 &times;&nbsp;41 &divide; 3 - 34 + 17<br>= 41 - 17 = 24</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Identify the figure given in the options which when put in place of \'?\' will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319495.png\" alt=\"rId22\" width=\"374\" height=\"76\"></p>",
                    question_hi: "<p>16. विकल्पों में दी गई उस आकृति को पहचानिए, जिसे \'?\' के स्थान पर रखने पर तार्किक रूप से श्रृंखला पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319495.png\" alt=\"rId22\" width=\"374\" height=\"76\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319660.png\" alt=\"rId23\" width=\"91\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319750.png\" alt=\"rId24\" width=\"90\" height=\"89\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319882.png\" alt=\"rId25\" width=\"92\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319997.png\" alt=\"rId26\" width=\"91\" height=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319660.png\" alt=\"rId23\" width=\"90\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319750.png\" alt=\"rId24\" width=\"91\" height=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319882.png\" alt=\"rId25\" width=\"90\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319997.png\" alt=\"rId26\" width=\"91\" height=\"90\"></p>"],
                    solution_en: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319882.png\" alt=\"rId25\" width=\"90\" height=\"89\"></p>",
                    solution_hi: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639319882.png\" alt=\"rId25\" width=\"90\" height=\"89\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Each letter in the word CLAUSE is changed to the letter immediately succeeding it in the English alphabetical order and then all the letters thus formed are arranged in alphabetical order. Which of the following letters will be third from the right in the new group of letters thus formed?</p>",
                    question_hi: "<p>17. शब्द CLAUSE में प्रत्येक अक्षर को अंग्रेजी वर्णमाला क्रम में उसके ठीक बाद वाले अक्षर से बदल दिया जाता है और फिर इस प्रकार बने सभी अक्षरों को वर्णमाला क्रम में व्यवस्थित किया जाता है। इस प्रकार बने अक्षरों के नए समूह में निम्नलिखित में से कौन सा अक्षर दाएँ से तीसरा होगा?</p>",
                    options_en: ["<p>V</p>", "<p>F</p>", 
                                "<p>T</p>", "<p>M</p>"],
                    options_hi: ["<p>V</p>", "<p>F</p>",
                                "<p>T</p>", "<p>M</p>"],
                    solution_en: "<p>17.(d)<br>CLAUSE<br>After changing each letter to the immediately succeeding it in English alphabetical order, We get<br>DMBVTF<br>Now, on arranging in alphabetical order, we get<br>BDFMTV<br>Letter third from right = M</p>",
                    solution_hi: "<p>17.(d)<br>CLAUSE<br>प्रत्येक अक्षर को अंग्रेजी वर्णमाला के क्रम में उसके ठीक बाद वाले अक्षर में बदलने पर, हमें प्राप्त होता है<br>DMBVTF<br>अब, वर्णमाला क्रम में व्यवस्थित करने पर, हमें प्राप्त होता है<br>BDFMTV<br>दाएँ से तीसरा अक्षर = M</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. The ratio of a father&rsquo;s age to his son&rsquo;s age is 7 : 4. The product of the numbers representing their ages is 1372. The ratio of their ages after 8 years will be:</p>",
                    question_hi: "<p>18. एक पिता की आयु और उसके पुत्र की आयु का अनुपात 7 : 4 है। उनकी आयु को दर्शाने वाली संख्याओं का गुणनफल 1372 है। 8 वर्ष बाद उनकी आयु का अनुपात क्या होगा?</p>",
                    options_en: ["<p>19 : 11</p>", "<p>19 : 14</p>", 
                                "<p>19 : 13</p>", "<p>19 : 12</p>"],
                    options_hi: ["<p>19 : 11</p>", "<p>19 : 14</p>",
                                "<p>19 : 13</p>", "<p>19 : 12</p>"],
                    solution_en: "<p>18.(d)<br>Let present age of father be 7x&nbsp;and his son be 4x years<br>According to the question,<br>7x&nbsp;&times; 4x = 1372<br>28x<sup>2</sup>&nbsp;= 1372 <br>x<sup>2</sup> = 49 &rArr; x = 7<br>Then, present age of father and son be 49 and 28 years respectively<br>Hence, required ratio = (49 + 8) : (28 + 8) = 19 : 12</p>",
                    solution_hi: "<p>18.(d)<br>माना पिता की वर्तमान आयु 7x&nbsp;और पुत्र की वर्तमान आयु 4x वर्ष है<br>प्रश्न के अनुसार,<br>7x&nbsp;&times; 4x = 1372<br>28x<sup>2</sup>&nbsp;= 1372 <br>x<sup>2</sup> = 49 &rArr; x = 7<br>तो, पिता और पुत्र की वर्तमान आयु क्रमशः 49 और 28 वर्ष होगी<br>अतः, आवश्यक अनुपात = (49 + 8) : (28 + 8) = 19 : 12</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Following a certain logic, if 36 is related to 9 and 100 is related to 49, to which of the following is 225 related ?</p>",
                    question_hi: "<p>19. एक निश्चित तर्क का अनुसरण करते हुए, यदि 36 का संबंध 9 से है और 100 का संबंध 49 से है, तो 225 का संबंध निम्नलिखित में से किससे है ?</p>",
                    options_en: ["<p>133</p>", "<p>135</p>", 
                                "<p>144</p>", "<p>148</p>"],
                    options_hi: ["<p>133</p>", "<p>135</p>",
                                "<p>144</p>", "<p>148</p>"],
                    solution_en: "<p>19.(c) Logic :- (<math display=\"inline\"><msqrt><mn>1</mn><mi>s</mi><mi>t</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi></msqrt></math> - 3)<sup>2</sup> = (2nd number)<br>(36 ,9) :- (<math display=\"inline\"><msqrt><mn>3</mn><mn>6</mn></msqrt></math> - 3)<sup>2</sup> &rArr; (6 - 3)<sup>2</sup> = 9<br>(100 , 49) :- (<math display=\"inline\"><msqrt><mn>100</mn></msqrt></math> - 3)<sup>2</sup> &rArr; (10 - 3)<sup>2</sup> = 49<br>Similarly,<br>(225 ,?) :- (<math display=\"inline\"><msqrt><mn>225</mn></msqrt></math> - 3)<sup>2</sup> &rArr; (15 - 3)<sup>2</sup> = 144</p>",
                    solution_hi: "<p>19.(c) <strong>तर्क :</strong>- (<math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2346;&#2361;&#2354;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></msqrt></math> - 3)<sup>2</sup> = (दूसरी संख्या)<br>(36 ,9) :- (<math display=\"inline\"><msqrt><mn>3</mn><mn>6</mn></msqrt></math> - 3)<sup>2</sup> &rArr; (6 - 3)<sup>2</sup> = 9<br>(100 , 49) :- (<math display=\"inline\"><msqrt><mn>100</mn></msqrt></math> - 3)<sup>2</sup> &rArr; (10 - 3)<sup>2</sup> = 49<br>इसी प्रकार,<br>(225 ,?) :- (<math display=\"inline\"><msqrt><mn>225</mn></msqrt></math> - 3)<sup>2</sup> &rArr; (15 - 3)<sup>2</sup> = 144</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. If the seventh day of a month is three days earlier than Friday, then what day will it be on the 20th day of the month?</p>",
                    question_hi: "<p>20. यदि किसी महीने का सातवाँ दिन, शुक्रवार से तीन दिन पहले पड़ता हो, तो महीने के 20वें दिन पर कौन- सा दिन होगा?</p>",
                    options_en: ["<p>Wednesday</p>", "<p>Monday</p>", 
                                "<p>Tuesday</p>", "<p>Thursday</p>"],
                    options_hi: ["<p>बुधवार</p>", "<p>सोमवार</p>",
                                "<p>मंगलवार</p>", "<p>गुरुवार</p>"],
                    solution_en: "<p>20.(b) Three days earlier than Friday = Tuesday = 7th day of the month.We know that after every 7 days, the same day repeats. So, 7 &times; 3 = 21st day = Tuesday. But we want to know the day on the 20th day of the month. Tuesday - 1 = Monday.</p>",
                    solution_hi: "<p>20.(b) शुक्रवार से तीन दिन पहले = मंगलवार = महीने का 7वाँ दिन। हम जानते हैं कि हर 7 दिन के बाद वही दिन दोहराया जाता है। तो, 7 &times; 3 = 21वाँ दिन = मंगलवार। लेकिन हम महीने की 20 तारीख को दिन जानना चाहते हैं। मंगलवार - 1 = सोमवार.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. How many rectangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320127.png\" alt=\"rId27\" width=\"120\" height=\"114\"></p>",
                    question_hi: "<p>21. दी गई आकृति में कितने आयत हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320127.png\" alt=\"rId27\" width=\"120\" height=\"114\"></p>",
                    options_en: ["<p>8</p>", "<p>7</p>", 
                                "<p>6</p>", "<p>5</p>"],
                    options_hi: ["<p>8</p>", "<p>7</p>",
                                "<p>6</p>", "<p>5</p>"],
                    solution_en: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320290.png\" alt=\"rId28\" width=\"160\" height=\"154\"><br>Total number of rectangle = 5 + ABCD = 6</p>",
                    solution_hi: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320290.png\" alt=\"rId28\" width=\"160\" height=\"154\"><br>आयतो की कुल संख्या = 5 + ABCD = 6</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. 29 is related to 118 following a certain logic. Following the same logic, 10 is related to 42. To which of the following is 13 related, following the same logic?<br>(<strong>NOTE:</strong> Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding / subtracting /multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>22. एक निश्चित तर्क का अनुसरण करते हुए, 29, 118 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 10, 42 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 13 निम्नलिखित में से किससे संबंधित है?<br>(<strong>नोट</strong>: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए, 13 को लीजिए - 13 पर संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 व 3 में तोड़ना और फिर 1 व 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं हैं।)</p>",
                    options_en: ["<p>56</p>", "<p>54</p>", 
                                "<p>52</p>", "<p>51</p>"],
                    options_hi: ["<p>56</p>", "<p>54</p>",
                                "<p>52</p>", "<p>51</p>"],
                    solution_en: "<p>22.(b)<br><strong>Logic:-</strong> 1<sup>st</sup>no. &times; 4 + 2 = 2<sup>nd</sup>no.<br>(29, 118):- 29 &times; 4 + 2 &rarr; 116 + 2 = 118<br>(10, 42):- 10 &times; 4 + 2 &rarr; 40 + 2 = 42<br>Similarly<br>(13, ?):- 13 &times; 4 + 2 &rarr; 52 + 2 = 54</p>",
                    solution_hi: "<p>22.(b)<br><strong>तर्क :- </strong>पहली संख्या &times; 4 + 2 = दूसरी संख्या <br>(29, 118):- 29 &times; 4 + 2 &rarr; 116 + 2 = 118<br>(10, 42):- 10 &times; 4 + 2 &rarr; 40 + 2 = 42<br>इसी प्रकार <br>(13, ?):- 13 &times; 4 + 2 &rarr; 52 + 2 = 54</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320408.png\" alt=\"rId29\" width=\"104\" height=\"126\"></p>",
                    question_hi: "<p>23. उस विकल्प आकृति का चयन कीजिए, जिसमें दी गई आकृति (X) उसके एक भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320408.png\" alt=\"rId29\" width=\"106\" height=\"128\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320549.png\" alt=\"rId30\" width=\"119\" height=\"122\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320710.png\" alt=\"rId31\" width=\"119\" height=\"111\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320892.png\" alt=\"rId32\" width=\"119\" height=\"114\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639321007.png\" alt=\"rId33\" width=\"119\" height=\"111\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320549.png\" alt=\"rId30\" width=\"119\" height=\"122\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320710.png\" alt=\"rId31\" width=\"120\" height=\"112\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639320892.png\" alt=\"rId32\" width=\"121\" height=\"115\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639321007.png\" alt=\"rId33\" width=\"120\" height=\"112\"></p>"],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639321214.png\" alt=\"rId34\" width=\"121\" height=\"118\"></p>",
                    solution_hi: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639321214.png\" alt=\"rId34\" width=\"121\" height=\"118\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Three of the following four number triads are alike in a certain way and thus form a group. Which is the number triad that does not belong to that group?<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>24. निम्नलिखित चार संख्या त्रिकों में से तीन संख्या त्रिक किसी निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह संख्या त्रिक कौन-सा है, जो उस समूह से संबंधित नहीं है?<br>(<strong>ध्यान दें:</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>31 - 46 - 41</p>", "<p>24 - 39 - 33</p>", 
                                "<p>29 - 44 - 38</p>", "<p>17 - 32 - 26</p>"],
                    options_hi: ["<p>31 - 46 - 41</p>", "<p>24 - 39 - 33</p>",
                                "<p>29 - 44 - 38</p>", "<p>17 - 32 - 26</p>"],
                    solution_en: "<p>24.(a) <strong>Logic:- </strong>(1st number) + 15 = 2nd number , (2nd number) - 6 = 3rd number<br>(24 - 39 - 33) :- (24) + 15 = 39 , (39) - 6 = 33<br>(29 - 44 - 38) :- (29) + 15 = 44 ,(44) - 6 = 38<br>(17 - 32 - 26) :- (17) + 15 = 32 , (32) - 6 = 26<br>But,<br>(31 - 46 - 41):- (31) + 15 = 46 , (46) - 5 = 41</p>",
                    solution_hi: "<p>24.(a) <strong>तर्क:- </strong>(पहली संख्या) + 15 = दूसरी संख्या, (दूसरी संख्या) - 6 = तीसरी संख्या<br>(24 - 39 - 33) :- (24) + 15 = 39 , (39) - 6 = 33<br>(29 - 44 - 38) :- (29) + 15 = 44 ,(44) - 6 = 38<br>(17 - 32 - 26) :- (17) + 15 = 32 , (32) - 6 = 26<br>लेकिन,<br>(31 - 46 - 41):- (31) + 15 = 46 , (46) - 5 = 41</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. All 91 people are standing in a row facing north. Fi is 12th from the right end while Yu is 86th from the left end. How many people are there between Fi and Yu?</p>",
                    question_hi: "<p>25. 91 लोग उत्तर की ओर अभिमुख होकर एक पंक्ति में खड़े हैं। Fi दाएं छोर से 12वें स्थान पर है जबकि Yu बाएं छोर से 86 वें स्थान पर है। Fi और Yu के बीच कितने लोग हैं?</p>",
                    options_en: ["<p>3</p>", "<p>6</p>", 
                                "<p>4</p>", "<p>5</p>"],
                    options_hi: ["<p>3</p>", "<p>6</p>",
                                "<p>4</p>", "<p>5</p>"],
                    solution_en: "<p>25.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639321366.png\" alt=\"rId35\" width=\"272\" height=\"117\"><br>People between Fi and Yu = 86 + 12 -&nbsp;91 - (Fi + Yu)<br>People between Fi and Yu = 98 - 91 - 2 = 5</p>",
                    solution_hi: "<p>25.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639321517.png\" alt=\"rId36\" width=\"246\" height=\"119\"><br>Fi और Yu के बीच लोगों की संख्या = 86 + 12 -&nbsp;91 - (Fi + Yu)<br>Fi और Yu के बीच लोगों की संख्या = 98 - 91 - 2 = 5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Where and when was India&rsquo;s first Senior National Archery Championship held ?</p>",
                    question_hi: "<p>26. भारत की पहली सीनियर राष्ट्रीय तीरंदाज़ी चैम्पियनशिप (Senior National Archery Championship) कहां और कब आयोजित की गई थी ?</p>",
                    options_en: ["<p>Chennai, 1975</p>", "<p>Delhi, 1973</p>", 
                                "<p>Delhi, 1975</p>", "<p>Chennai, 1973</p>"],
                    options_hi: ["<p>चेन्नई, 1975</p>", "<p>दिल्ली , 1973</p>",
                                "<p>दिल्ली , 1975</p>", "<p>चेन्नई, 1973</p>"],
                    solution_en: "<p>26.(b) <strong>Delhi, 1973</strong>. The Senior National Archery Championship was introduced as part of organized archery in India, largely due to the efforts of Prof. Vijay Kumar Malhotra, who played a key role in introducing and promoting the sport in the country. 1972: Archery is included as an Olympic discipline in the Munich Games. 1975: Modern archery takes root in India, with the establishment of the Archery Association of India.</p>",
                    solution_hi: "<p>26.(b) <strong>दिल्ली, 1973. </strong>भारत में संगठित तीरंदाजी के एक भाग के रूप में सीनियर राष्ट्रीय तीरंदाजी चैम्पियनशिप की शुरुआत की गई,&nbsp;जिसका श्रेय मुख्य रूप से प्रोफेसर विजय कुमार मल्होत्रा ​​को जाता है, जिन्होंने देश में इस खेल को शुरू करने और बढ़ावा देने में महत्वपूर्ण भूमिका निभाई। 1972 : तीरंदाजी को म्यूनिख खेलों में एक ओलंपिक अनुशासन के रूप में शामिल किया गया। 1975: भारतीय तीरंदाजी संघ की स्थापना के साथ भारत में आधुनिक तीरंदाजी की स्थापना हुई।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. What was the name of the wind measuring instrument invented for the first time in 1450?</p>",
                    question_hi: "<p>27. 1450 में पहली बार आविष्कृत पवन मापक यंत्र का क्या नाम था?</p>",
                    options_en: ["<p>Transmissometer</p>", "<p>Dropsonde</p>", 
                                "<p>Anemometer</p>", "<p>Ceiling Projector</p>"],
                    options_hi: ["<p>संचरणलेखित्र (Transmissometer)</p>", "<p>अधःपाती सोंड (Dropsonde)</p>",
                                "<p>पवनवेगमापी (Anemometer)</p>", "<p>सीलिंग प्रोजेक्टर (Ceiling Projector)</p>"],
                    solution_en: "<p>27.(c) <strong>Anemometer.</strong> It is an instrument that measures wind speed and wind pressure. A transmissometer measures the attenuation of light as it passes through a fluid, such as water or the atmosphere. A dropsonde is a weather device dropped from an aircraft at specified altitudes, falling to Earth due to gravity. A ceiling projector is a portable device used to project images onto a ceiling.</p>",
                    solution_hi: "<p>27.(c) <strong>पवनवेगमापी (Anemometer) </strong>। यह एक ऐसा उपकरण है जो वायु की गति और वायु के दाब को मापता है। ट्रांसमिसोमीटर प्रकाश के क्षीणन को मापता है जब यह किसी द्रव पदार्थ, जैसे जल या वायुमंडल से होकर गुजरता है। ड्रॉपसॉन्ड एक मौसम संबंधी उपकरण है जिसे विमान से निर्दिष्ट ऊंचाई पर गिराया जाता है, जो गुरुत्वाकर्षण के कारण पृथ्वी पर गिरता है। सीलिंग प्रोजेक्टर एक पोर्टेबल डिवाइस है जिसका उपयोग छत पर छवियों को प्रोजेक्ट करने के लिए किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following is a festival primarily celebrated in the state of Odisha?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन-सा त्योहार मुख्य रूप से ओडिशा राज्य में मनाया जाता है?</p>",
                    options_en: ["<p>Baishagu</p>", "<p>Chhath</p>", 
                                "<p>Raja Parba</p>", "<p>Me-Dam-Me-Phi</p>"],
                    options_hi: ["<p>बैशागु</p>", "<p>छठ</p>",
                                "<p>रज पर्व</p>", "<p>मी-डैम-मी-फी</p>"],
                    solution_en: "<p>28.(c) <strong>Raja Parba. </strong>It is a unique festival celebrating the onset of the monsoon and the earth&rsquo;s womanhood. Odisha\'s famous festivals include Konark Dance Festival, Kalinga Mahotsava, Durga Puja, Chhau Festival, Puri Beach Festival, Nuakhai, Pakhala Dibasa, and Laxmi Puja. Baishagu, celebrated by the Boro Kacharis of Assam, marks the new year. Chhath is a major Hindu festival in Bihar and eastern Uttar Pradesh, while Me-Dam-Me-Phi is an ancestor worship festival of the Ahom people in Assam.</p>",
                    solution_hi: "<p>28.(c) <strong>रज पर्व। </strong>यह मानसून की शुरुआत और पृथ्वी की नारीत्व का जश्न मनाने वाला एक अनूठा त्योहार है। ओडिशा के प्रसिद्ध त्योहारों में कोणार्क नृत्य महोत्सव, कलिंग महोत्सव, दुर्गा पूजा, छऊ महोत्सव, पुरी बीच महोत्सव, नुआखाई, पखला दिवस और लक्ष्मी पूजा शामिल हैं। असम के बोरो कचारियों द्वारा मनाया जाने वाला बैशागु, नए वर्ष का प्रतीक है। छठ, बिहार और पूर्वी उत्तर प्रदेश में एक प्रमुख हिंदू त्योहार है, जबकि मे-डैम-मे-फी असम में अहोम लोगों का एक पूर्वज पूजा त्योहार है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which group of oilfields among the following is located in Assam?</p>",
                    question_hi: "<p>29. निम्नलिखित में से तेल क्षेत्रों का कौन सा समूह असम में स्थित है?</p>",
                    options_en: ["<p>Digboi and Naharkatiya</p>", "<p>Ankaleshwar and Kalol</p>", 
                                "<p>Mehsana and Nawagam</p>", "<p>Kosamba and Lunej</p>"],
                    options_hi: ["<p>डिगबोई और नहरकटिया</p>", "<p>अंकलेश्वर और कलोल</p>",
                                "<p>मेहसाणा और नवगाम</p>", "<p>कोसम्बा और लुनेज</p>"],
                    solution_en: "<p>29.(a) <strong>Digboi and Naharkatiya.</strong> Digboi Oilfield : One of the oldest oil fields in Asia, discovered in 1867, and home to Asia\'s oldest oil refinery (established in 1901). It is located in Tinsukia district, Assam. Naharkatiya Oilfield: Discovered in 1953, with oil production starting in 1954. It is located in Dibrugarh district, Assam. Kosamba, Lunej, Mehsana, Nawagam, Ankleshwar and Kalol are oil-producing regions located in Gujarat.</p>",
                    solution_hi: "<p>29.(a) <strong>डिगबोई और नहरकटिया</strong>। डिगबोई तेल क्षेत्र: यह एशिया के सबसे पुराने तेल क्षेत्रों में से एक है, जो 1867 में खोजा गया, तथा यह एशिया की सबसे पुरानी तेल रिफाइनरी (1901 में स्थापित) है। यह असम के तिनसुकिया जिले में स्थित है। नहरकटिया तेल क्षेत्र: 1953 में खोजा गया था तथा 1954 में तेल उत्पादन शुरू हुआ था। यह असम के डिब्रूगढ़ जिले में स्थित है। कोसांबा, लुनेज, मेहसाणा, नवागाम, अंकलेश्वर और कलोल गुजरात में स्थित तेल उत्पादक क्षेत्र हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Fighting in the upright position in judo is called ________.</p>",
                    question_hi: "<p>30. जूडो में सीधी स्थिति में खड़े होकर लड़ना _________कहलाता है।</p>",
                    options_en: ["<p>ne-waza</p>", "<p>tachi-waza</p>", 
                                "<p>koshi-waza</p>", "<p>ashi-waza</p>"],
                    options_hi: ["<p>ने-वाजा (ne-waza)</p>", "<p>ताची-वाजा (tachi-waza)</p>",
                                "<p>कोशी-वाजा (koshi-waza)</p>", "<p>अशी-वाजा (ashi-waza)</p>"],
                    solution_en: "<p>30.(b) <strong>tachi-waza. </strong>Judo has its origin in the ancient Japanese art of Ju-jitsu. Kano Jigoro invented Judo in 1882. It was used in war and practiced in full body armor. Other Terminology: Kumi Kata - a gripping pattern. Shintai - forwards, sideways and backward movement of the body during Judo. Ukemi - Breakfall techniques.</p>",
                    solution_hi: "<p>30.(b) <strong>ताची-वाजा</strong> (tachi-waza)। जूडो की उत्पत्ति प्राचीन जापानी कला जू-जित्सु से हुई है। कानो जिगोरो ने 1882 में जूडो का आविष्कार किया था। इसका प्रयोग युद्ध में किया जाता था और पूरे शरीर पर कवच पहनकर अभ्यास किया जाता था। अन्य शब्दावली: कुमी काटा - प्रतिद्वंदी को पकड़कर नियंत्रित करने का तरीका। शिंताई - जूडो के दौरान शरीर की आगे, बगल और पीछे की ओर गति। उकेमी - ब्रेकफॉल तकनीक।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who was announced as the recipient of the Satyajit Ray Lifetime Achievement Award on November 11, 2024?</p>",
                    question_hi: "<p>31. 11 नवम्बर 2024 को सत्यजीत राय लाइफटाइम अचीवमेंट अवार्ड का प्राप्तकर्ता किसे घोषित किया गया?</p>",
                    options_en: ["<p>Steven Spielberg</p>", "<p>Martin Scorsese</p>", 
                                "<p>Phillip Noyce</p>", "<p>Quentin Tarantino</p>"],
                    options_hi: ["<p>स्टीवन स्पीलबर्ग</p>", "<p>मार्टिन स्कॉर्सेसी</p>",
                                "<p>फिलिप नॉयस</p>", "<p>क्वेंटिन टारनटिनो</p>"],
                    solution_en: "<p>31.(c)<strong> Phillip Noyce.</strong> The International Film Festival of India (IFFI), held annually in Goa.</p>",
                    solution_hi: "<p>31.(c) <strong>फिलिप नॉयस। </strong>अंतरराष्ट्रीय फिल्म महोत्सव (IFFI), जो गोवा में प्रत्येक वर्ष आयोजित होता है, उन्हें यह प्रतिष्ठित पुरस्कार प्रदान किया गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Who was the first person to conduct a systematic study of chromosomes during&nbsp;division and called this process mitosis?</p>",
                    question_hi: "<p>32. विभाजन के दौरान गुणसूत्रों का व्यवस्थित अध्ययन करने वाला पहला व्यक्ति कौन था, जिसने इस&nbsp;प्रक्रिया को समसूत्री विभाजन (mitosis) कहा था?</p>",
                    options_en: ["<p>Thomas Hunt Morgan</p>", "<p>Antonie van Leeuwenhoek</p>", 
                                "<p>Walther Flemming</p>", "<p>Friedrich Miescher</p>"],
                    options_hi: ["<p>थॉमस हंट मॉर्गन (Thomas Hunt Morgan)</p>", "<p>एंटोनी वैन ल्यूवेनहॉक (Antonie van Leeuwenhoek)</p>",
                                "<p>वाल्थर फ्लेमिंग (Walther Flemming)</p>", "<p>फ्रेडरिक मिशेर (Friedrich Miescher)</p>"],
                    solution_en: "<p>32.(c) <strong>Walther Flemming.</strong> He was a German biologist and a founder of cytogenetics. His key discoveries - Chromatin, Chromosomes. Thomas Hunt Morgan received the 1933 Nobel Prize in Physiology or Medicine for his discoveries concerning the role of chromosomes in heredity. Antoni van Leeuwenhoek (Father of Microbiology): He was the first to discover and describe microorganisms, such as protists and bacteria, which he referred to as \"animalcules\" (little animals).</p>",
                    solution_hi: "<p>32.(c) <strong>वाल्थर फ्लेमिंग</strong> (Walther Flemming)। वे एक जर्मन जीवविज्ञानी और साइटोजेनेटिक्स के संस्थापक थे। उनकी प्रमुख खोजें - क्रोमेटिन, क्रोमोसोम। थॉमस हंट मॉर्गन को आनुवंशिकता में गुणसूत्रों की भूमिका से संबंधित उनकी खोजों के लिए 1933 में फिजियोलॉजी या मेडिसिन में नोबेल पुरस्कार से सम्मानित किया गया था। एंटोनी वॉन ल्यूवेनहॉक (सूक्ष्म जीव विज्ञान के जनक): वे सूक्ष्मजीवों, जैसे प्रोटिस्ट और बैक्टीरिया की खोज और वर्णन करने वाले प्रथम व्यक्ति थे, जिन्हें उन्होंने \"एनिमलक्यूल्स\" (छोटे जानवर) कहा था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Raziya Sultan was the first and only female ruler of the Sultanate, who ascended the throne in:</p>",
                    question_hi: "<p>33. रज़िया सुल्ताना सल्तनत की पहली और एकमात्र महिला शासक थी, जो ______में सिंहासन पर बैठीं ।</p>",
                    options_en: ["<p>1238 AD</p>", "<p>1236 AD</p>", 
                                "<p>1241 AD</p>", "<p>1242 AD</p>"],
                    options_hi: ["<p>1238 ईस्वी</p>", "<p>1236 ईस्वी</p>",
                                "<p>1241 ईस्वी</p>", "<p>1242 ईस्वी</p>"],
                    solution_en: "<p>33.(b) <strong>1236 AD</strong>. Razia Sultana was the daughter of Iltutmish and served as the Sultan of Delhi from 1236 to 1240. She was married to Malik Altunia, the governor of Bathinda. She was buried in Kaithal, Haryana. She was imprisoned by Malik Ikhtiyar-ud-din Altunia in the Qila Mubarak Fort (Bathinda). The book \"Razia Queen of India\" was written by Rafiq Zakaria about her life.</p>",
                    solution_hi: "<p>33.(b) <strong>1236 ईस्वी।</strong> रज़िया सुल्तान इल्तुतमिश की पुत्री थीं और 1236 से 1240 तक दिल्ली की सुल्तान के रूप में शासन किया। वह मलिक अल्तुनिया, बठिंडा के गवर्नर, से विवाहित थीं। उनकी कब्र कैथल, हरियाणा में है । उन्हें मलिक इख्तियार-उद-दीन अल्तुनिया द्वारा किला मुबारक (बठिंडा) में कैद कर लिया गया था। उनके जीवन पर \"रज़िया क्वीन ऑफ इंडिया\" नामक पुस्तक रफीक जकारिया द्वारा लिखी गई थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. In which year did India make its Olympic debut in hockey?</p>",
                    question_hi: "<p>34. भारत ने हॉकी में ओलंपिक में अपनी शुरूआत किस वर्ष की थी?</p>",
                    options_en: ["<p>1936</p>", "<p>1932</p>", 
                                "<p>1924</p>", "<p>1928</p>"],
                    options_hi: ["<p>1936</p>", "<p>1932</p>",
                                "<p>1924</p>", "<p>1928</p>"],
                    solution_en: "<p>34.(d) <strong>1928.</strong> India made its Olympic debut in hockey in 1928 at the Amsterdam Olympics. The Indian men\'s hockey team won their first Olympic gold medal in this tournament, beating the Netherlands. The team was led by captain Jaipal Singh and Dhyan Chand, who scored the most goals with 14. India\'s hockey team is the most successful in Olympic history, winning eight gold medals in 1928, 1932, 1936, 1948, 1952, 1956, 1964, and 1980.</p>",
                    solution_hi: "<p>34.(d) <strong>1928</strong>. भारत ने हॉकी में अपना ओलंपिक पदार्पण 1928 में एम्स्टर्डम ओलंपिक में किया था। भारतीय पुरुष हॉकी टीम ने इस टूर्नामेंट में नीदरलैंड को हराकर अपना पहला ओलंपिक स्वर्ण पदक जीता था। टीम का नेतृत्व कप्तान जयपाल सिंह और ध्यानचंद ने किया था, जिन्होंने सबसे अधिक 14 गोल किए थे। भारत की हॉकी टीम ओलंपिक इतिहास में सबसे सफल है, जिसने 1928, 1932, 1936, 1948, 1952, 1956, 1964 और 1980 में आठ स्वर्ण पदक जीते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. The famous Hindi novel \'Maila Aanchal\' is written by:</p>",
                    question_hi: "<p>35. प्रसिद्ध हिंदी उपन्यास &lsquo;मैला आंचल (Maila Aanchal)\' __________द्वारा लिखा गया है l</p>",
                    options_en: ["<p>Mridula Garg</p>", "<p>Shrilal Shukla</p>", 
                                "<p>Phanishwar Nath Renu</p>", "<p>Premchand</p>"],
                    options_hi: ["<p>मृदुला गर्ग (Mridula Garg)</p>", "<p>श्रीलाल शुक्ला (Shrilal Shukla)</p>",
                                "<p>फणीश्वर नाथ रेणु (Phanishwar Nath Renu)</p>", "<p>प्रेमचंद (Premchand)</p>"],
                    solution_en: "<p>35.(c) <strong>Phanishwar Nath Renu</strong> received the Padma Shri award in 1970. His other Novels include &ldquo;Parti Parikatha&rdquo;, &ldquo;Juloos&rdquo;, &ldquo;Kitne Chaurahe&rdquo;, &ldquo;Rinjal Dhanjal&rdquo;. Other Authors : Premchand - &ldquo;Godaan&rdquo;, &ldquo;Idgah&rdquo;, &ldquo;Kaphan&rdquo;, &ldquo;Gaban&rdquo;. Shrilal Shukla - &ldquo;Raag Darbari&rdquo;, &ldquo;Suni Ghati Ka Suraj&rdquo;. Mridula Garg - &ldquo;Kathgulab&rdquo;, &ldquo;Cittakobara&rdquo;, &ldquo;Anitya&rdquo;.</p>",
                    solution_hi: "<p>35.(c) <strong>फणीश्वर नाथ रेणु </strong>को 1970 में पद्म श्री पुरस्कार से सम्मानित किया गया था। उनके अन्य उपन्यासों में \"परती परिकथा\", \"जुलूस\", \"कितने चौराहे\", &rdquo;ऋणजल-धनजल\" शामिल हैं। अन्य लेखक : प्रेमचंद - \"गोदान\", \"ईदगाह\", \"कफन\", \"गबन\"। श्रीलाल शुक्ल - \"राग दरबारी\", \"सूनी घाटी का सूरज\"। मृदुला गर्ग - \"कठगुलाब\", \"चित्तकोबारा\", \"अनित्य\"।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. _______ is the most commonly used measure of money supply.</p>",
                    question_hi: "<p>36. ______ मुद्रा आपूर्ति की माप का सबसे साधारण रूप है।</p>",
                    options_en: ["<p>M1</p>", "<p>M2</p>", 
                                "<p>M4</p>", "<p>M3</p>"],
                    options_hi: ["<p>M1</p>", "<p>M2</p>",
                                "<p>M4</p>", "<p>M3</p>"],
                    solution_en: "<p>36.(d) <strong>M3</strong>. M3 is the most commonly used measure of money supply, also referred to as aggregate monetary resources. M1 and M2 are known as narrow money, while M3 and M4 are classified as broad money. M1 is the most liquid and easiest for transactions, whereas M4 is the least liquid. M0, also known as Reserve money or high-powered money, represents the base money in the economy.</p>",
                    solution_hi: "<p>36.(d) M3. M3 मुद्रा आपूर्ति का सबसे अधिक उपयोग किया जाने वाला माप है, जिसे समग्र मौद्रिक संसाधन भी कहा जाता है। M1 और M2 को संकीर्ण मुद्रा के रूप में जाना जाता है, जबकि M3 और M4 को व्यापक मुद्रा के रूप में वर्गीकृत किया जाता है। M1 सबसे अधिक तरल और लेन-देन के लिए सबसे आसान है, जबकि M4 सबसे कम तरल है। M0, जिसे आरक्षित मुद्रा या उच्च-शक्तिशाली मुद्रा के रूप में भी जाना जाता है, जो अर्थव्यवस्था में आधार मुद्रा का प्रतिनिधित्व करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Who were the recipients of the Sahitya Akademi Yuva Puraskar 2024?</p>",
                    question_hi: "<p>37. 2024 के साहित्य अकादमी युवा पुरस्कार के प्राप्तकर्ता कौन थे?</p>",
                    options_en: ["<p>K Vaishali and Gaurav Pandey</p>", "<p>Gaurav Pandey and Kiran Desai</p>", 
                                "<p>K Vaishali and Arundhati Roy</p>", "<p>Chetan Bhagat and Gaurav Pandey</p>"],
                    options_hi: ["<p>के वैशाली और गौरव पांडे</p>", "<p>गौरव पांडे और किरन देसाई</p>",
                                "<p>के वैशाली और अरुंधति रॉय</p>", "<p>चेतन भगत और गौरव पांडे</p>"],
                    solution_en: "<p>37.(a) <strong>K Vaishali and Gaurav Pandey.</strong> The Sahitya Akademi on June 15, announced the names of 23 writers, including English writer K Vaishali and Hindi author Gaurav Pandey, who will receive the prestigious Yuva Puraskar across as many languages.</p>",
                    solution_hi: "<p>37.(a)<strong> के वैशाली और गौरव पांडे।</strong> साहित्य अकादमी ने 15 जून को 23 लेखकों के नामों की घोषणा की, जिनमें अंग्रेजी लेखिका के वैशाली और हिंदी लेखक गौरव पांडे शामिल हैं, जिन्हें विभिन्न भाषाओं में प्रतिष्ठित युवा पुरस्कार मिलेगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following raw materials is NOT used to prepare the baking soda?</p>",
                    question_hi: "<p>38. बेकिंग सोडा तैयार करने के लिए निम्नलिखित में से किस कच्चे पदार्थ (raw materials) का उपयोग नहीं किया जाता है?</p>",
                    options_en: ["<p>H<sub>2</sub>O</p>", "<p>Na<sub>2</sub>CO<sub>3</sub></p>", 
                                "<p>NaCI</p>", "<p>NH<sub>3</sub></p>"],
                    options_hi: ["<p>H<sub>2</sub>O</p>", "<p>Na<sub>2</sub>CO<sub>3</sub></p>",
                                "<p>NaCI</p>", "<p>NH<sub>3</sub></p>"],
                    solution_en: "<p>38.(b) <strong>Na<sub>2</sub>CO<sub>3</sub>.</strong> The raw materials used to prepare baking soda are: Sodium chloride (NaCl): Also known as common salt, this is the source of sodium ions. Ammonia (NH<sub>3</sub>) : Reacts with carbon dioxide and water to form ammonium bicarbonate. Carbon dioxide (CO<sub>2</sub>): Reacts with ammonium bicarbonate to precipitate out sodium bicarbonate. Water (H<sub>2</sub>O): Used in the reaction. Reaction: NaCl + H<sub>2</sub>O + CO<sub>2</sub> + NH<sub>3</sub> &rarr; NH<sub>4</sub>CI + NaHCO<sub>3</sub>.</p>",
                    solution_hi: "<p>38.(b) <strong>Na<sub>2</sub>CO<sub>3</sub>. </strong>बेकिंग सोडा बनाने के लिए उपयोग किए जाने वाला कच्चा पदार्थ हैं: सोडियम क्लोराइड (NaCl): इसे साधारण नमक के रूप में भी जाना जाता है, यह सोडियम आयनों का स्रोत है। अमोनिया (NH<sub>3</sub>): कार्बन डाइऑक्साइड और जल के साथ अभिक्रिया करके अमोनियम बाइकार्बोनेट बनाता है। कार्बन डाइऑक्साइड (CO<sub>2</sub>): अमोनियम बाइकार्बोनेट के साथ अभिक्रिया करके सोडियम बाइकार्बोनेट को अवक्षेपित करता है। जल (H<sub>2</sub>O): अभिक्रिया में उपयोग किया जाता है। अभिक्रिया: NaCl + H<sub>2</sub>O + CO<sub>2</sub> + NH<sub>3</sub> &rarr; NH<sub>4</sub>CI + NaHCO<sub>3</sub>.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which chemical compound is commonly utilised as a preservative in tomato sauce?</p>",
                    question_hi: "<p>39. टमाटर के सॉस में परिरक्षक के रूप में आमतौर पर किस रासायनिक यौगिक का उपयोग किया जाता है?</p>",
                    options_en: ["<p>Green Tea Extract</p>", "<p>Carnosic Acid</p>", 
                                "<p>Vinegar</p>", "<p>Sodium Benzoate</p>"],
                    options_hi: ["<p>ग्रीन टी एक्सट्रैक्ट (Green Tea Extract)</p>", "<p>कार्नोसिक अम्ल (Carnosic Acid)</p>",
                                "<p>सिरका (Vinegar)</p>", "<p>सोडियम बेंजोएट (Sodium Benzoate)</p>"],
                    solution_en: "<p>39.(d) <strong>Sodium Benzoate</strong> is a widely used preservative in tomato sauce and other acidic food products due to its antimicrobial properties. It prevents the growth of bacteria, yeast, and mold, extending the shelf life of the product. Its effectiveness increases in acidic conditions, making it suitable for foods like tomato sauce with a low pH. It is recognized as safe for consumption in regulated quantities by food safety authorities worldwide. Sodium benzoate is also used in beverages, pickles, and condiments for its preservative qualities.</p>",
                    solution_hi: "<p>39.(d) <strong>सोडियम बेंजोएट </strong>एक व्यापक रूप से उपयोग किया जाने वाला संरक्षक है, जिसका उपयोग टमाटर सॉस और अन्य अम्लीय खाद्य उत्पादों में किया जाता है। इसकी प्रतिजैविक गुणधर्म बैक्टीरिया, यीस्ट और फफूंदी के विकास को रोकते हैं, जिससे उत्पाद का शेल्फ जीवन बढ़ जाता है। अम्लीय परिस्थितियों में इसकी प्रभावशीलता बढ़ जाती है, जिससे यह कम pH वाले टमाटर सॉस जैसे खाद्य पदार्थों के लिए उपयुक्त हो जाता है। इसे विश्व भर के खाद्य सुरक्षा अधिकारियों द्वारा विनियमित मात्रा में उपभोग के लिए सुरक्षित माना जाता है। सोडियम बेंजोएट का उपयोग इसके संरक्षक गुणों के कारण पेय पदार्थों, अचार और मसालों में भी किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. The Mughal emperor, Babur was a devotee of the ______Sufi Silsila.</p>",
                    question_hi: "<p>40. मुगल बादशाह बाबर ________ सूफी सिलसिला (Sufi Silsila) का अनुयायी था।</p>",
                    options_en: ["<p>Naqshbandi</p>", "<p>Suhrawardi</p>", 
                                "<p>Qadri</p>", "<p>Chisti</p>"],
                    options_hi: ["<p>नक्शबंदी (Naqshbandi)</p>", "<p>सुहरावर्दी (Suhrawardi)</p>",
                                "<p>कादरी (Qadri)</p>", "<p>चिश्ती (Chisti)</p>"],
                    solution_en: "<p>40.(a) <strong>Naqshbandi.</strong> Babur (1526-1530 AD) founded Mughal rule in India. He was invited to invade by Daulat Khan Lodhi, the Punjab subedar, Alam Khan Lodhi (Ibrahim Lodhi\'s uncle), and Rana Sanga. Babur was the first to assume the title &lsquo;Badshah&rsquo; and introduced gunpowder and artillery in India. He also authored his autobiography, Tuzuk-i-Baburi, in the Turki language.</p>",
                    solution_hi: "<p>40.(a) <strong>नक्शबंदी। </strong>बाबर (1526-1530 ई.) ने भारत में मुगल शासन की स्थापना की। उसे पंजाब के सूबेदार दौलत खान लोधी, आलम खान लोधी (इब्राहिम लोधी के चाचा) और राणा सांगा ने आक्रमण करने के लिए आमंत्रित किया था। बाबर ने सबसे पहले \'बादशाह\' की उपाधि धारण की और भारत में बारूद और तोपखाने की शुरुआत की। उन्होंने तुर्की भाषा में अपनी आत्मकथा, तुजुक-ए-बाबरी भी लिखी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41.Where is the 60th Munich Security Conference taking place?</p>",
                    question_hi: "<p>41. 60वाँ म्यूनिख सुरक्षा सम्मेलन कहाँ हो रहा है?</p>",
                    options_en: ["<p>Berlin, Germany</p>", "<p>Munich, Germany</p>", 
                                "<p>Frankfurt, Germany</p>", "<p>Hamburg, Germany</p>"],
                    options_hi: ["<p>बर्लिन, जर्मनी</p>", "<p>म्यूनिख, जर्मनी</p>",
                                "<p>फ्रैंकफर्ट, जर्मनी</p>", "<p>हैम्बर्ग, जर्मनी</p>"],
                    solution_en: "<p>41.(b) <strong>Munich, Germany. </strong>Dr. S Jaishankar, the External Affairs Minister, represented India at the conference. It focused on strengthening bilateral cooperation and addressing global and regional issues.</p>",
                    solution_hi: "<p>41.(b)&nbsp;<strong>म्यूनिख, जर्मनी। </strong>विदेश मंत्री डॉ. एस जयशंकर ने सम्मेलन में भारत का प्रतिनिधित्व किया। इसमें द्विपक्षीय सहयोग को मजबूत करने और वैश्विक और क्षेत्रीय मुद्दों को संबोधित करने पर ध्यान केंद्रित किया गया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. The peninsular plateau of India was a part of which continent earlier ?</p>",
                    question_hi: "<p>42. भारत का प्रायद्वीपीय पठार पहले किस महाद्वीप का भाग था ?</p>",
                    options_en: ["<p>North America</p>", "<p>Europe</p>", 
                                "<p>South America</p>", "<p>Africa</p>"],
                    options_hi: ["<p>उत्तर अमेरिका</p>", "<p>यूरोप</p>",
                                "<p>दक्षिण अमेरिका</p>", "<p>अफ्रीका</p>"],
                    solution_en: "<p>42.(d) <strong>Africa.</strong> The Peninsular Plateau is roughly triangular in shape. The peninsular plateau of India was a part of the supercontinent Gondwana, which included Africa, South America, Australia, and Antarctica. After Breaking up from Gondwana, the Indian subcontinent drifted northwards and collided with Eurasia which formed the Himalayas.</p>",
                    solution_hi: "<p>42.(d) <strong>अफ्रीका।</strong> प्रायद्वीपीय पठार आकार में लगभग त्रिभुजाकार है। भारत का प्रायद्वीपीय पठार गोंडवाना महाद्वीप का हिस्सा था, जिसमें अफ्रीका, दक्षिण अमेरिका, ऑस्ट्रेलिया और अंटार्कटिका शामिल थे। गोंडवाना से अलग होने के बाद, भारतीय उपमहाद्वीप उत्तर की ओर खिसक गया और यूरेशिया से टकराया जिसके परिणामस्वरूप हिमालय का निर्माण हुआ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. The state religion of Bangladesh is:</p>",
                    question_hi: "<p>43. बांग्लादेश का राजकीय धर्म (state religion) क्या है?</p>",
                    options_en: ["<p>Christianity</p>", "<p>Hinduism</p>", 
                                "<p>Buddhism</p>", "<p>Islam</p>"],
                    options_hi: ["<p>ईसाई धर्म</p>", "<p>हिन्दू धर्म</p>",
                                "<p>बौद्ध धर्म</p>", "<p>इस्लाम</p>"],
                    solution_en: "<p>43.(d) <strong>Islam.</strong> Muslims of Bangladesh are predominant native Bengali Muslims. The majority of Bangladeshis are Sunni, and follow the Hanafi school of fiqh (Islamic jurisprudence). Other Countries and Their State Religions: India: Secular (No state religion; all religions treated equally). Pakistan: Islam (State religion). Nepal: Secular (formerly Hinduism). Sri Lanka: Buddhism (Promoted under the constitution, but no strict state religion).</p>",
                    solution_hi: "<p>43.(d) <strong>इस्लाम। </strong>बांग्लादेश के मुसलमानों में प्रमुख रूप से स्वदेशी बंगाली मुसलमान हैं। बांग्लादेश के अधिकांश लोग सुन्नी हैं और हनफ़ी स्कूल ऑफ़ फ़िक़्ह (इस्लामिक न्यायशास्त्र) का पालन करते हैं। अन्य देश एवं उनके राज्य धर्म: भारत: धर्मनिरपेक्ष (कोई राज्य धर्म नहीं; सभी धर्मों को समान माना जाता है)। पाकिस्तान: इस्लाम (राज्य धर्म)। नेपाल: धर्मनिरपेक्ष (पूर्व में हिंदू धर्म)। श्रीलंका: बौद्ध धर्म (संविधान के तहत प्रचारित, लेकिन कोई सख्त राज्य धर्म नहीं)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which writ is issued by a high court or supreme court when a lower court has considered a case going beyond its jurisdiction?</p>",
                    question_hi: "<p>44. जब निचली अदालत ने अपने अधिकार क्षेत्र से बाहर जाकर किसी मामले पर विचार किया हो तो किसी उच्च न्यायालय या सर्वोच्च न्यायालय द्वारा कौन सी रिट जारी की जाती है?</p>",
                    options_en: ["<p>Quo Warrant</p>", "<p>Habeas Corpus</p>", 
                                "<p>Certiorari</p>", "<p>Prohibition</p>"],
                    options_hi: ["<p>अधिकार पृच्छा (Quo Warrant)</p>", "<p>बन्दी प्रत्यक्षीकरण (Habeas Corpus)</p>",
                                "<p>उत्प्रेषण लेख (Certiorari)</p>", "<p>निषेधाज्ञा (Prohibition)</p>"],
                    solution_en: "<p>44.(d) <strong>Prohibition. </strong>Writs are issued by the Supreme Court under Article 32 and by the High Court under Article 226 of the Indian Constitution. The five types of writs in India are: Habeas Corpus: Ensures that a detained person is brought before the court. Mandamus: Directs a public official to perform a duty. Certiorari: Transfers a case for review by a higher court. Prohibition: Prevents a lower court from exceeding its jurisdiction. Quo-Warranto: Questions the legal authority of someone holding a public office.</p>",
                    solution_hi: "<p>44.(d) <strong>निषेधाज्ञा</strong> <strong>(Prohibition)</strong>। भारतीय संविधान के अनुच्छेद 32 के तहत सर्वोच्च न्यायालय और अनुच्छेद 226 के तहत उच्च न्यायालय द्वारा रिट जारी की जाती हैं। भारत में पाँच प्रकार की रिट हैं: बंदी प्रत्यक्षीकरण: यह सुनिश्चित करता है कि हिरासत में लिए गए व्यक्ति को न्यायालय के समक्ष लाया जाए। परमादेश: किसी सार्वजनिक अधिकारी को कोई कर्तव्य निभाने का निर्देश देता है। उत्प्रेषण लेख : किसी मामले को उच्च न्यायालय द्वारा समीक्षा के लिए स्थानांतरित करता है। निषेधाज्ञा: किसी निचली अदालत को अपने अधिकार क्षेत्र से बाहर जाने से रोकता है। अधिकार-पृच्छा: किसी सार्वजनिक पद पर आसीन व्यक्ति के कानूनी अधिकार पर सवाल उठाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following pairs is INCORRECT regarding the grade of organisation and its example?</p>",
                    question_hi: "<p>45. संगठन के ग्रेड और उसके उदाहरण के संबंध में निम्नलिखित में से कौन सी जोड़ी गलत है?</p>",
                    options_en: ["<p>Cellular grade organisation - Sycon</p>", "<p>Protoplasmic grade organisation - Paramecium</p>", 
                                "<p>Cell-tissue grade organisation - Jellyfish</p>", "<p>Tissue-organ grade organisation - Euplectella</p>"],
                    options_hi: ["<p>कोशिकीय ग्रेड संगठन - साइकोन</p>", "<p>प्रोटोप्लाज्मिक ग्रेड संगठन - पैरामीशियम</p>",
                                "<p>कोशिका-ऊतक ग्रेड संगठन - जेलीफ़िश</p>", "<p>ऊतक-अंग ग्रेड संगठन - यूप्लेक्टेला</p>"],
                    solution_en: "<p>45.(d) Euplectella, is a genus of glass sponges which includes the well-known Venus\' Flower Basket, exhibits a cellular grade of organization rather than a tissue-organ grade. A cellular grade of organisation is an aggregation of cells that are functionally differentiated.</p>",
                    solution_hi: "<p>45.(d) यूप्लेक्टेला, ग्लास स्पंज की एक प्रजाति है जिसमें वीनस फ्लावर बास्केट शामिल है, जो ऊतक-अंग ग्रेड के बजाय सेलुलर ग्रेड का संगठन प्रदर्शित करता है। सेलुलर ग्रेड का संगठन कोशिकाओं का एक समूह है जो कार्यात्मक रूप से विभेदित होते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. What is the standard length of an Olympic-size swimming pool ?</p>",
                    question_hi: "<p>46. ओलंपिक-आकार (Olympic-size) के स्विमिंग पूल की मानक लंबाई कितनी होती है ?</p>",
                    options_en: ["<p>70 metres</p>", "<p>50 metres</p>", 
                                "<p>60 metres</p>", "<p>40 metres</p>"],
                    options_hi: ["<p>70 मीटर</p>", "<p>50 मीटर</p>",
                                "<p>60 मीटर</p>", "<p>40 मीटर</p>"],
                    solution_en: "<p>46.(b)<strong> 50 metres.</strong> An Olympic swimming pool is divided into eight lanes. Swimming is one of the oldest Olympic sports, having featured at every modern Olympic Games since Athens 1896. Women began competing at the Stockholm 1912 edition. The four strokes of Olympic swimming events&mdash;for both individual and relay races&mdash;are breaststroke, butterfly, backstroke and front crawl.</p>",
                    solution_hi: "<p>46.(b) <strong>50 मीटर। </strong>एक ओलंपिक स्विमिंग पूल को आठ लेन में विभाजित किया जाता है। तैराकी सबसे प्राचीन ओलंपिक खेलों में से एक है, जो एथेंस 1896 के बाद से प्रत्येक आधुनिक ओलंपिक खेलों में शामिल रहा है। महिलाओं ने स्टॉकहोम 1912 संस्करण में प्रतिस्पर्धा करना शुरू किया। व्यक्तिगत और रिले दौड़ दोनों के लिए ओलंपिक तैराकी स्पर्धाओं के चार स्ट्रोक हैं - ब्रेस्टस्ट्रोक, बटरफ्लाई, बैकस्ट्रोक और फ्रंट क्रॉल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which part of the Indian Constitution deals with the Election Commission?</p>",
                    question_hi: "<p>47. भारतीय संविधान का कौन-सा भाग चुनाव आयोग से संबंधित है?</p>",
                    options_en: ["<p>Part XV</p>", "<p>Part XII</p>", 
                                "<p>Part IV</p>", "<p>Part XI</p>"],
                    options_hi: ["<p>भाग XV</p>", "<p>भाग XII</p>",
                                "<p>भाग IV</p>", "<p>भाग XI</p>"],
                    solution_en: "<p>47.(a) <strong>Part XV </strong>(Article 324-329). The Election Commission of India (ECI, Established - 25th January 1950) is an autonomous constitutional authority responsible for administering Union and State election processes in India. Originally the commission had only one election commissioner but after the Election Commissioner Amendment Act 1989, it was made a multi-member body. Other Parts: Part IV (Article 36 - 51) - Directive Principle of States Policy. Part XI (Article 245 - 263) - Center- State Relations. Part XII (Article 264 - 300A) - Finance, Property, Contracts and Suits.</p>",
                    solution_hi: "<p>47.(a) <strong>भाग XV </strong>(अनुच्छेद 324-329)। भारत का चुनाव आयोग (ECI, स्थापना - 25 जनवरी 1950) एक स्वायत्त संवैधानिक प्राधिकरण है जो भारत में संघ एवं राज्य चुनाव प्रक्रियाओं को संचालित करने के लिए उत्तरदायी है। मूल रूप से आयोग में केवल एक चुनाव आयुक्त था, लेकिन चुनाव आयुक्त संशोधन अधिनियम 1989 के बाद इसे बहु-सदस्यीय निकाय बना दिया गया। अन्य भाग: भाग IV (अनुच्छेद 36 - 51) - राज्यों के नीति निदेशक सिद्धांत। भाग XI (अनुच्छेद 245 - 263) - केंद्र-राज्य संबंध। भाग XII (अनुच्छेद 264 - 300A) - वित्त, संपत्ति, अनुबंध और मुकदमे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. What is net investment?</p>",
                    question_hi: "<p>48. शुद्ध निवेश क्या है?</p>",
                    options_en: ["<p>Sum of all the investments in a country</p>", "<p>Gross investment + depreciation</p>", 
                                "<p>Gross capital investment - indirect taxes</p>", "<p>Gross investment - depreciation</p>"],
                    options_hi: ["<p>किसी देश में सभी निवेशों का योग</p>", "<p>सकल निवेश + मूल्यह्रास</p>",
                                "<p>सकल पूंजी निवेश - अप्रत्यक्ष कर</p>", "<p>सकल निवेश - मूल्यह्रास</p>"],
                    solution_en: "<p>48.(d) <strong>Gross investment - depreciation.</strong> Net Investment: It refers to the actual addition to the capital stock of a country or business, accounting for the depreciation of existing assets. Gross Investment: This is the total expenditure on new assets, including new buildings, machinery, equipment, and inventory. Depreciation: This refers to the reduction in value of existing assets due to wear and tear, obsolescence, and damage.</p>",
                    solution_hi: "<p>48.(d) <strong>सकल निवेश - मूल्यह्रास।</strong> शुद्ध निवेश: यह किसी देश या व्यवसाय के पूंजी स्टॉक में वास्तविक वृद्धि को संदर्भित करता है, जिसमें मौजूदा परिसंपत्तियों के मूल्यह्रास को शामिल किया जाता है। सकल निवेश: यह नई इमारतों, मशीनरी, उपकरण और इन्वेंट्री सहित नई परिसंपत्तियों पर कुल व्यय है। मूल्यह्रास: यह टूट-फूट, अप्रचलन और क्षति के कारण मौजूदा परिसंपत्तियों के मूल्य में कमी को संदर्भित करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following is NOT a unit of energy?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन-सी ऊर्जा की इकाई नहीं है?</p>",
                    options_en: ["<p>Joule</p>", "<p>Calorie</p>", 
                                "<p>Newton</p>", "<p>Kilowatt hour</p>"],
                    options_hi: ["<p>जूल</p>", "<p>कैलोरी</p>",
                                "<p>न्यूटन</p>", "<p>किलोवाट घंटा</p>"],
                    solution_en: "<p>49.(c) <strong>Newton. </strong>The SI unit of force is the Newton. Horsepower (hp) is a unit of measurement of power, with 1 horsepower equaling 746 watts, where Watt is the SI unit of power. Joule is the SI unit of energy, and kilowatt-hour is a commercial unit representing the energy used by a 1-kilowatt device in one hour. A calorie, also a unit of energy, is the amount of heat needed to raise the temperature of 1 gram of water by 1&deg;C or 1 Kelvin.</p>",
                    solution_hi: "<p>49.(c) <strong>न्यूटन।</strong> बल का SI मात्रक न्यूटन है। अश्वशक्ति (hp) शक्ति मापने की एक इकाई है, जिसमें 1 अश्वशक्ति 746 वाट के बराबर होता है, जहाँ वाट शक्ति का SI मात्रक है। जूल ऊर्जा का SI मात्रक है, और किलोवाट-घंटा एक वाणिज्यिक इकाई है जो एक घंटे में 1-किलोवाट डिवाइस द्वारा उपयोग की जाने वाली ऊर्जा का प्रतिनिधित्व करती है। एक कैलोरी, जो ऊर्जा की एक इकाई भी है, 1 ग्राम जल के तापमान को 1&deg;C या 1 केल्विन बढ़ाने के लिए आवश्यक ऊष्मा की मात्रा है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Where is the Kala Ghoda Arts Festival organised every year?</p>",
                    question_hi: "<p>50. काला घोड़ा कला महोत्सव (Kala Ghoda Arts Festival) प्रतिवर्ष कहाँ आयोजित किया जाता है?</p>",
                    options_en: ["<p>Mumbai</p>", "<p>New Delhi</p>", 
                                "<p>Chennai</p>", "<p>Bengaluru</p>"],
                    options_hi: ["<p>मुंबई</p>", "<p>नई दिल्ली</p>",
                                "<p>चेन्नई</p>", "<p>बेंगलुरु</p>"],
                    solution_en: "<p>50.(a) <strong>Mumbai.</strong> The Kala Ghoda Arts Festival, initiated in 1999, is a nine-day annual celebration of arts organized by the Kala Ghoda Association. Other festivals in Mumbai : Dahi Handi Festival, Ganesh Chaturthi / Ganeshotsav, Banganga Festival, Elephanta Festival. New Delhi - Qutub Festival, International Arts Festival. Tamil Nadu - Pongal, Thaipusam. Karnataka - Kambala, Hampi, Pattadakal Dance Festival.</p>",
                    solution_hi: "<p>50.(a) <strong>मुंबई।</strong> काला घोड़ा कला महोत्सव की शुरुआत 1999 ईस्वी में हुई थी। काला घोड़ा एसोसिएशन द्वारा आयोजित कला का नौ दिवसीय वार्षिक उत्सव है। मुंबई में मनायें जाने वाले अन्य त्योहार: दही हांडी महोत्सव, गणेश चतुर्थी/गणेशोत्सव, बाणगंगा महोत्सव, एलीफेंटा महोत्सव। नई दिल्ली - कुतुब महोत्सव, अंतर्राष्ट्रीय कला महोत्सव। तमिलनाडु - पोंगल, थाईपुसम। कर्नाटक - कंबाला, हम्पी, पत्तदकल नृत्य महोत्सव।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The circumference of the base of the cylindrical vessel is 154 cm and its height is 49 mm. How many litres of water can it hold? (correct to three places of decimals, use &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)</p>",
                    question_hi: "<p>51. किसी बेलनाकार बर्तन के आधार की परिधि 154 cm है और इसकी ऊँचाई 49 mm है। इसमें कितने लीटर पानी आ सकता है? (दशमलव के तीन स्थानों तक शुद्ध, &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> का उपयोग कीजिए)</p>",
                    options_en: ["<p>9.244</p>", "<p>2.439</p>", 
                                "<p>3.924</p>", "<p>4.329</p>"],
                    options_hi: ["<p>9.244</p>", "<p>2.439</p>",
                                "<p>3.924</p>", "<p>4.329</p>"],
                    solution_en: "<p>51.(a) <br>Circumference of base of cylinder = 154 cm<br>2&pi;r = 154<br>2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; r = 154<br>r = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>2</mn></mfrac></math><br>Volume of cylindrical vessel = &pi;r<sup>2</sup>h = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>2</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>2</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>10</mn></mfrac></math> <br>= 11 &times; 7 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>2</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>10</mn></mfrac></math> = 9243.5 cm<sup>3</sup><br>In litres = 9243.5 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>1000</mn></mfrac></math> = 9.244 litres</p>",
                    solution_hi: "<p>51.(a) <br>बेलन के आधार की परिधि = 154 cm<br>2&pi;r = 154<br>2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; r = 154<br>r = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>2</mn></mfrac></math><br>बेलनाकार बर्तन का आयतन = &pi;r<sup>2</sup>h = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>2</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>2</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>10</mn></mfrac></math> <br>= 11 &times; 7 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>2</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>10</mn></mfrac></math> = 9243.5 cm<sup>3</sup><br>लीटर में = 9243.5 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>1000</mn></mfrac></math> = 9.244 लीटर</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. A. B and C jointly started a business in which A invested ₹7,000 for 12 months, B invested ₹8,000 for 10 months and C invested ₹12,000 for 6 months. A was the working member for which he received 20% of the profits as his remuneration. The total profit earned at the end of one year was ₹14,750. What is C\'s share (in₹) in the profit?</p>",
                    question_hi: "<p>52. A, B और C ने साथ मिलकर एक व्यवसाय शुरू किया जिसमें A ने 12 महीने के लिए ₹7,000 का निवेश किया, B ने 10 महीने के लिए ₹8,000 का निवेश किया और C ने 6 महीने के लिए ₹12,000 का निवेश किया। A कार्यकारी सदस्य था जिसके लिए उसे अपने पारिश्रमिक के रूप में लाभ का 20% प्राप्त हुआ। एक वर्ष के अंत में अर्जित कुल लाभ ₹14,750 था। इस लाभ में C का हिस्सा (₹ में) कितना है?</p>",
                    options_en: ["<p>4,000</p>", "<p>2,950</p>", 
                                "<p>3,600</p>", "<p>4,200</p>"],
                    options_hi: ["<p>4,000</p>", "<p>2,950</p>",
                                "<p>3,600</p>", "<p>4,200</p>"],
                    solution_en: "<p>52.(c) <br>Share&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; A&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;C<br>Investment &rarr;&nbsp; 7000 :&nbsp; 8000&nbsp; &nbsp;:&nbsp; 12000<br>Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; 12&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 6<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; 84&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;80&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 72<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;21&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;18<br>A received 20% of the profits as his remuneration <br>Remaining profit = 14,750 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 11800<br>C\'s share = <math display=\"inline\"><mfrac><mrow><mn>11800</mn></mrow><mrow><mn>59</mn><mi>&#160;</mi></mrow></mfrac></math>&times; 18 = ₹3600</p>",
                    solution_hi: "<p>52.(c) <br>हिस्सा&nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; A&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;B&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;C<br>निवेश&nbsp;<strong id=\"docs-internal-guid-a4f8f01b-7fff-2724-4106-c9264ad88573\"> &nbsp; &nbsp; &nbsp;</strong>&rarr;&nbsp; 7000 :&nbsp; 8000&nbsp; &nbsp;:&nbsp; 12000<br>समय &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; 12&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 6<br>अनुपात<strong id=\"docs-internal-guid-13e96e8a-7fff-6032-acab-aaea8c212f66\"> </strong>&nbsp; &nbsp; &rarr;&nbsp; 84&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;80&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 72<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;21&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;18<br>A को पारिश्रमिक के रूप में लाभ का 20% प्राप्त हुआ<br>शेष लाभ = 14,750 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 11800<br>C का हिस्सा = <math display=\"inline\"><mfrac><mrow><mn>11800</mn></mrow><mrow><mn>59</mn><mi>&#160;</mi></mrow></mfrac><mi>&#160;</mi></math>&times; 18 = ₹3600</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The sum of the digits of a two&minus;digit number is 9. The number obtained by interchanging its digits exceeds the given number by 45, then the original number is:</p>",
                    question_hi: "<p>53. दो अंकों की एक संख्या के अंकों का योग 9 है। इसके अंकों को आपस में बदलने पर प्राप्त संख्या दी गई संख्या से 45 अधिक है, तो मूल संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>18</p>", "<p>36</p>", 
                                "<p>27</p>", "<p>54</p>"],
                    options_hi: ["<p>18</p>", "<p>36</p>",
                                "<p>27</p>", "<p>54</p>"],
                    solution_en: "<p>53.(c) <br>Let the no of unit and tens digits be y and x&nbsp;respectively<br>Then, number will be (10x&nbsp;+ y)<br>On interchanging its digits, we get (10y + <math display=\"inline\"><mi>x</mi></math>)<br>According to the question,<br>x + y = 9 ------------- (i)<br>(10y + x) - (10x + y) = 45<br>9y - 9x&nbsp;= 45<br>&rArr; y - x = 5 ------------- (ii)<br>Solving equation (i) &amp; (ii) we have ;<br>x = 2 , y = 7<br>So, the original number = 10 &times; 2 + 7 = 27</p>",
                    solution_hi: "<p>53.(c) <br>माना इकाई और दहाई के अंको की संख्या क्रमशः y और <math display=\"inline\"><mi>x</mi></math> है<br>तो संख्या होगी (10x&nbsp;+ y)<br>इसके अंको को आपस मे बदलने पर हमें (10y + x) मिलता है<br>प्रश्न के अनुसार,<br>x + y = 9 ------------- (i)<br>(10y + x) - (10x + y) = 45<br>9y - 9x&nbsp;= 45<br>&rArr; y - x = 5 ------------- (ii)<br>समीकरण (i) और (ii) को हल करने पर हमे मिलता है;<br>x = 2 , y = 7<br>तो वास्तविक संख्या होगी = 10 &times; 2 + 7 = 27</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. 300 m from the foot of a cliff on a level ground, the angle of elevation of the top of a cliff is 60&deg;. Find the height of the cliff.</p>",
                    question_hi: "<p>54. समतल भूमि पर एक चट्टान के पाद से 300 मी, एक चट्टान की चोटी का उन्नयन कोण 60&deg; है। चट्टान की ऊँचाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>200<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>", "<p>150<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>", 
                                "<p>300<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>", "<p>250<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>"],
                    options_hi: ["<p>200<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>", "<p>150<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>",
                                "<p>300<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>", "<p>250<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>"],
                    solution_en: "<p>54.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639321740.png\" alt=\"rId37\" width=\"154\" height=\"103\"><br>In triangle ABC <br>BC = 300 m <br>tan&theta;&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>BC</mi></mfrac></math> <br>tan60&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mn>300</mn></mfrac></math> <br><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mn>300</mn></mfrac></math><br>AB = 300<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                    solution_hi: "<p>54.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639321740.png\" alt=\"rId37\" width=\"176\" height=\"118\"><br>त्रिभुज ABC में <br>BC = 300 m <br>tan&theta;&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>BC</mi></mfrac></math> <br>tan60&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mn>300</mn></mfrac></math> <br><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mn>300</mn></mfrac></math><br>AB = 300<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. M varies inversely as (N<sup>2</sup> + 3). If M = 3 when N = 3, then what will be the value of M when N<sup>2</sup> = 69 ?</p>",
                    question_hi: "<p>55. M, (N<sup>2</sup> + 3) का व्युत्क्रमानुपाती है। यदि N = 3 होने पर M = 3 होता है, तो N<sup>2</sup> = 69 होने पर M का मान क्या होगा ?</p>",
                    options_en: ["<p>1</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><msqrt><mn>69</mn></msqrt></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>1</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><msqrt><mn>69</mn></msqrt></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>55.(d) <br>Accordion to the question,<br>M &prop; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">N</mi><mn>2</mn></msup><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math> <br>M = k &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">N</mi><mn>2</mn></msup><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math>&nbsp;-------(i) {where k is constant} <br>Puting M = 3 and N = 3 in equation (i)<br>3 = k &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math> &rArr; k = 36<br>Now, value of M when given that N<sup>2</sup> = 69 <br>M = 36 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><mn>69</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math> <br>M = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>72</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>55.(d) <br>प्रश्न के अनुसार,<br>M &prop; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">N</mi><mn>2</mn></msup><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math> <br>M = k &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><msup><mi mathvariant=\"normal\">N</mi><mn>2</mn></msup><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math> -------(i){जहां k स्थिरांक है}&nbsp;<br>समीकरण (i) में M = 3 और N = 3 रखने पर<br>3 = k &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math> &rArr; k = 36<br>अब, जब N<sup>2</sup> = 69 दिया गया है तो M का मान<br>M = 36 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><mn>69</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math> <br>M = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>72</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. WXYZ is a quadrilateral in which XQ and ZP are perpendicular to WY such that XQ = ZP. Diagonals ZX and WY intersect each other at point O. If OX = 12 cm, then find the value of ZX (in cm).</p>",
                    question_hi: "<p>56. WXYZ एक चतुर्भुज है जिसमें XQ और ZP, रेखा WY के इस प्रकार लंबवत हैं कि XQ = ZP है। विकर्ण ZX और WY एक-दूसरे को बिंदु O पर प्रतिच्छेद करते हैं। यदि OX = 12 cm है, तो ZX का मान (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>12</p>", "<p>24</p>", 
                                "<p>36</p>", "<p>18</p>"],
                    options_hi: ["<p>12</p>", "<p>24</p>",
                                "<p>36</p>", "<p>18</p>"],
                    solution_en: "<p>56.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639321842.png\" alt=\"rId38\" width=\"154\" height=\"134\"><br>In &Delta;ZOP and &Delta;XOQ<br>XQ = ZP &hellip;..(Given )<br>&ang; P = &ang;Q &hellip; (each 90&deg;)<br>&ang; POZ = &ang;XOQ &hellip;(vertically opposite angle )<br>&there4; &Delta;ZOP &cong; &Delta;XOQ ( AAS Congruence )<br>So , OX = ZO = 12 cm (CPCT )<br>Hence , ZX =XO + ZO = 12 + 12 = 24cm</p>",
                    solution_hi: "<p>56.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639321842.png\" alt=\"rId38\" width=\"148\" height=\"129\"><br>&Delta;ZOP और &Delta;XOQ में&nbsp;<br>XQ = ZP &hellip;..(दिया गया )<br>&ang; P = &ang;Q &hellip; ( प्रत्येक 90&deg;)<br>&ang; POZ = &ang;XOQ &hellip;(शीर्षाभिमुख कोण )<br>&there4; &Delta;ZOP &cong; &Delta;XOQ ( AAS सर्वांगसमता)<br>तो , OX = ZO = 12 cm (समरूप त्रिभुजों के संगत भाग )<br>अतः , ZX =XO + ZO = 12 + 12 = 24cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Find the values of x, y and z, so as to satisfy the equations given below:<br>x + y + z = 12; x + y -z = 6; x - y + z = 4</p>",
                    question_hi: "<p>57. x,y और z के वे मान ज्ञात करें, जिससे नीचे दिए गए समीकरणों को संतुष्ट किया जा सके:<br>x + y + z = 12; x + y -z = 6; x - y + z = 4</p>",
                    options_en: ["<p>x =5, y = 4, z =- 3</p>", "<p>x =5, y =- 4, z = 3</p>", 
                                "<p>x=5, y = 4, z = 3</p>", "<p>x =5, y =- 4, z =- 3</p>"],
                    options_hi: ["<p>x =5, y = 4, z =- 3</p>", "<p>x =5, y =- 4, z = 3</p>",
                                "<p>x=5, y = 4, z = 3</p>", "<p>x =5, y =- 4, z =- 3</p>"],
                    solution_en: "<p>57.(c) After checking all the options one by one, only option (c) satisfied.<br>x = 5,y = 4,z = 3<br>&rArr; x + y + z = 12; <br>LHS = 5 + 4 + 3 = 12 = RHS<br>&rArr; x + y -z = 6;<br>LHS = 5 + 4 - 3 = 6 = RHS <br>&rArr; x - y + z = 4<br>LHS = 5 - 4 + 3 = 4 = RHS</p>",
                    solution_hi: "<p>57.(c) सभी विकल्पों को एक-एक करके जांचने के बाद, केवल विकल्प (c) संतुष्ट है।<br>x = 5 , y = 4 , z = 3 <br>&rArr; x + y + z = 12; <br>बायाँ पक्ष = 5 + 4 + 3 = 12 = दायाँ पक्ष<br>&rArr; x + y -z = 6;<br>बायाँ पक्ष = 5 + 4 - 3 = 6 = दायाँ पक्ष<br>&rArr; x - y + z = 4<br>बायाँ पक्ष = 5 - 4 + 3 = 4 = दायाँ पक्ष</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Amita can build a room in the same amount of time that Bina and Sita working together can build. If Amita and Bina together could do it in 25 days and Sita alone in 35 days, then how many days are required for Bina alone to do the same work ?</p>",
                    question_hi: "<p>58. अमिता उतने ही समय में एक कमरा बना सकती है, जितने समय में बीना और सीता एकसाथ मिलकर उसे बना सकती हैं। यदि अमिता और बीना एकसाथ मिलकर इसे 25 दिन में कर सकती हैं और सीता अकेले 35 दिन में कर सकती हैं, तो बीना को अकेले उसी कार्य को करने में कितने दिन लगेंगे ?</p>",
                    options_en: ["<p>152 Days</p>", "<p>175 Days</p>", 
                                "<p>165 Days</p>", "<p>180 Days</p>"],
                    options_hi: ["<p>152 दिन</p>", "<p>175 दिन</p>",
                                "<p>165 दिन</p>", "<p>180 दिन</p>"],
                    solution_en: "<p>58.(b)<br>Ratio &rarr;&nbsp;Amita : Bina + Sita<br>Time&nbsp; &rarr;&nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;1<br>Effi.&nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;1<br>Total Effi. = 2 units &hellip; (i)<br>Now, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639322012.png\" alt=\"rId39\" width=\"169\" height=\"119\"><br>Effi. of Amita + Bina + Sita = 12 unit &hellip; (ii)<br>From (i) and (ii)<br>Effi. of Bina and Sita = 6 unit<br>Effi. of Bina = 6 - 5 = 1 unit<br>Time taken by Bina = <math display=\"inline\"><mfrac><mrow><mn>175</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 175 days</p>",
                    solution_hi: "<p>58.(b)<br>अनुपात &rarr; अमिता : बीना + सीता<br>समय&nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;1<br>दक्षता&nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;1<br>कुल दक्षता = 2 इकाई &hellip; (i)<br>अब, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639322156.png\" alt=\"rId40\" width=\"171\" height=\"126\"><br>अमिता + बीना + सीता की दक्षता = 12 इकाई&hellip; (ii)<br>(i) और (ii) से<br>बीना और सीता की दक्षता = 6 इकाई<br>बीना की दक्षता = 6 - 5 = 1 इकाई<br>बीना द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>175</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 175 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A bucket contains a mixture of milk and water in the proportion of 7 : 5. If 9 litres of&nbsp;milk is replaced by water, then the ratio becomes 7 : 9. How much milk was there in the&nbsp;bucket initially ?</p>",
                    question_hi: "<p>59. एक बाल्टी में दूध और पानी का मिश्रण 7 : 5 के अनुपात में है। यदि 9 लीटर दूध को पानी से बदल दिया&nbsp;जाए, तो अनुपात 7 : 9 हो जाता है। प्रारभ में बाल्टी में कितना दूध था?</p>",
                    options_en: ["<p>36 litres</p>", "<p>30 litres</p>", 
                                "<p>25 litres</p>", "<p>35 litres</p>"],
                    options_hi: ["<p>36 लीटर</p>", "<p>30 लीटर</p>",
                                "<p>25 लीटर</p>", "<p>35 लीटर</p>"],
                    solution_en: "<p>59.(a)<br>Let the fraction reduced by &lsquo;f&rsquo;<br><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; f = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>16</mn></mfrac></math><br>f = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math><br>Quantity of milk replaced by water = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> <br>According to question,<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 9 litres<br>So, the initial quantity of milk = 4 &times; 9 = 36 litres</p>",
                    solution_hi: "<p>59.(a)<br>माना कि भिन्न \'f\' से कम हो गई है<br><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; f = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>16</mn></mfrac></math><br>f = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math><br>पानी द्वारा प्रतिस्थापित दूध की मात्रा = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> ,<br>प्रश्न के अनुसार, <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 9 लीटर <br>अतः, दूध की प्रारंभिक मात्रा = 4 &times; 9 = 36 लीटर[</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A man wishes to cover 1 km distance in river water. In still water he takes 12 minutes to cover it, but in the flowing river he takes 13 minutes. The speed of the flowing water of the river is:</p>",
                    question_hi: "<p>60. एक पुरुष नदी के पानी में 1 km की दूरी तय करना चाहता है। शांत जल में इस दूरी को तय करने में उसे 12 मिनट लेकिन बहती नदी में 13 मिनट लगते हैं। नदी के बहते पानी की चाल ज्ञात करें।</p>",
                    options_en: ["<p>25 km/h</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math> km/h</p>", 
                                "<p>22 km/h</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> km/h</p>"],
                    options_hi: ["<p>25 km/h</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math> km/h</p>",
                                "<p>22 km/h</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> km/h</p>"],
                    solution_en: "<p>60.(b)<br>Speed of man in still water(B) = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>60</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 5 km/hr<br>Speed of man in flowing river(B - S) = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>60</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> km/hr<br>So, speed of flowing water of the river(S) = 5 - <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math> km/hr</p>",
                    solution_hi: "<p>60.(b)<br>शांत जल में पुरुष की गति(B) = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>60</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 5 किमी/घंटा<br>बहती नदी में पुरुष की गति (B-S) = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>60</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> किमी/घंटा<br>अतः, नदी के बहते पानी की गति (S) = 5 - <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math>किमी/घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. One pipe can fill a tank four times as fast as another pipe. If together the two pipes can fill the tank in 48 minutes, the slower pipe alone will be able to fill the tank in:</p>",
                    question_hi: "<p>61. एक पाइप, दूसरे पाइप की तुलना में चार गुना तेजी से एक टैंक भर सकता है। यदि दोनों पाइप मिलकर टैंक को 48 मिनट में भर सकते हैं, तो धीमा पाइप अकेले टैंक को कितने समय में भरने में सक्षम होगा?</p>",
                    options_en: ["<p>192 minutes</p>", "<p>288 minutes</p>", 
                                "<p>240 minutes</p>", "<p>144 minutes</p>"],
                    options_hi: ["<p>192 मिनट</p>", "<p>288 मिनट</p>",
                                "<p>240 मिनट</p>", "<p>144 मिनट</p>"],
                    solution_en: "<p>61.(c)<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &rarr; pipe<sub>1</sub> : pipe<sub>2</sub><br>Efficiency &rarr;&nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 1<br>Total volume = 5 &times;&nbsp;48 = 240 unit<br>Time taken by pipe<sub>2</sub>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>1</mn></mfrac></math> = 240 minutes</p>",
                    solution_hi: "<p>61.(c)<br>अनुपात &rarr; पाइप<sub>1</sub> : पाइप<sub>2</sub><br>दक्षता&nbsp; &nbsp; &rarr;&nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;1<br>कुल आयतन = 5 &times; 48 = 240 इकाई<br>पाइप<sub>2</sub> द्वारा लिया गया समय = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>1</mn></mfrac></math> = 240 मिनट</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The volume of a cuboid is twice the volume of a cube. If the dimensions of the cuboid are 12 cm, 16 cm and 18 cm, then the total surface area of the cube is:</p>",
                    question_hi: "<p>62. एक घनाभ का आयतन एक घन के आयतन का दोगुना है। यदि घनाभ की विमाएँ 12 cm, 16 cm और&nbsp;18 cm हैं, तो घन का संपूर्ण पृष्ठीय क्षेत्रफल कितना है?</p>",
                    options_en: ["<p>625 cm<sup>2</sup></p>", "<p>864 cm<sup>2</sup></p>", 
                                "<p>484 cm<sup>2</sup></p>", "<p>764 cm<sup>2</sup></p>"],
                    options_hi: ["<p>625 cm<sup>2</sup></p>", "<p>864 cm<sup>2</sup></p>",
                                "<p>484 cm<sup>2</sup></p>", "<p>764 cm<sup>2</sup></p>"],
                    solution_en: "<p>62.(b)<br>Volume of a cuboid = 2 &times; Volume of a cube<br>12 &times; 16 &times; 18 = 2 &times; Volume of a cube<br>3,456 = 2 &times; Volume of a cube<br>Volume of a cube = 1728<br>a<sup>3</sup> = 1728<br>&rArr; a = 12<br>TSA of cube = 6a<sup>2</sup> = 6 &times; 12<sup>2</sup> = 864 cm<sup>2</sup></p>",
                    solution_hi: "<p>62.(b)<br>घनाभ का आयतन = 2 &times; घन का आयतन<br>12 &times; 16 &times; 18 = 2 &times; घन का आयतन<br>3,456 = 2 &times; घन का आयतन<br>घन का आयतन (a<sup>3</sup>) = 1728<br>a<sup>3</sup> = 1728<br>&rArr; a = 12<br>घन का संपूर्ण पृष्ठीय क्षेत्रफल = 6a<sup>2</sup> = 6 &times; 12<sup>2</sup> = 864 cm<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Ram and Shyam start Walking from the same place in the opposite direction. If Shyam walks at a speed of 5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> km/hr and Ram at a speed of 3 km/hr, then after how much time will they be 44 km apart?</p>",
                    question_hi: "<p>63. राम और श्याम एक ही स्थान से विपरीत दिशा में चलना शुरू करते हैं। यदि श्याम 5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> km/hr की चाल से चलता है और राम 3 km/hr की चाल से चलता है, तो कितने समय बाद वे एक दूसरे से 44 km दूर होंगे?</p>",
                    options_en: ["<p>6 hours 20 minutes</p>", "<p>4 hours 20 minutes</p>", 
                                "<p>3 hours 20 minutes</p>", "<p>5 hours 20 minutes</p>"],
                    options_hi: ["<p>6 घंटे 20 मिनट</p>", "<p>4 घंटे 20 मिनट</p>",
                                "<p>3 घंटे 20 मिनट</p>", "<p>5 घंटे 20 मिनट</p>"],
                    solution_en: "<p>63.(d)<br>They walking in the opposite direction<br>Relative speed = (5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 3) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>4</mn></mfrac></math> km/h<br>Time taken when they are 44 km apart from each other = <math display=\"inline\"><mfrac><mrow><mn>44</mn><mi>&#160;</mi></mrow><mrow><mfrac><mrow><mn>33</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mi>&#160;</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math> hr.<br>= 5 hr. + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 60 min. = 5 hours 20 minutes</p>",
                    solution_hi: "<p>63.(d)<br>वे विपरीत दिशा में चल रहे हैं<br>सापेक्ष गति = (5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + 3) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>4</mn></mfrac></math> km/h<br>एक दूसरे से 44 किमी दूर होने में लगा समय =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>44</mn><mi>&#160;</mi></mrow><mrow><mfrac><mrow><mn>33</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mi>&#160;</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math> घंटे <br>= 5 घंटे + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 60 मिनट = 5 घंटे 20 मिनट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Which of the following is the largest 5-digit number divisible by 47 ?</p>",
                    question_hi: "<p>64. निम्नलिखित में से कौन-सी, 47 से विभाज्य 5 अंकों की सबसे बड़ी संख्या है?</p>",
                    options_en: ["<p>99999</p>", "<p>98888</p>", 
                                "<p>99969</p>", "<p>10000</p>"],
                    options_hi: ["<p>99999</p>", "<p>98888</p>",
                                "<p>99969</p>", "<p>10000</p>"],
                    solution_en: "<p>64.(c)<br>Largest 5-digit number = 99999<br>On dividing 99999 by 47, we get 30 as a remainder.<br>So, the required no which is divisible by 47 = 99999 - 30 = 99969</p>",
                    solution_hi: "<p>64.(c)<br>5 अंकों की सबसे बड़ी संख्या = 99999<br>99999 को 47 से विभाजित करने पर प्राप्त शेषफल = 30<br>तो, आवश्यक संख्या जो 47 से विभाज्य हो = 99999 - 30 = 99969</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. The price of fuel decreases by 60%, 30% and 20% in three successive months, but increases by 60% in the fourth month. What is the percentage increase/decrease in the price of fuel in the fourth month as compared to its original price?</p>",
                    question_hi: "<p>65. ईंधन की कीमत क्रमागत तीन महीनों में 60%, 30% और 20% कम की गई है, लेकिन चौथे महीने में 60% वृद्धि की गई है। चौथे महीने में ईंधन की कीमत में उसकी मूल कीमत की तुलना में कितने प्रतिशत की वृद्धि/कमी हुई है?</p>",
                    options_en: ["<p>Decreases by 67.12%</p>", "<p>Increases by 61.74%</p>", 
                                "<p>Decreases by 64.16%</p>", "<p>Increases by 67.33%</p>"],
                    options_hi: ["<p>67.12% की कमी</p>", "<p>61.74% की वृद्धि</p>",
                                "<p>64.16% की कमी</p>", "<p>67.33% की वृद्धि</p>"],
                    solution_en: "<p>65.(c)<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr; Initial : Final<br>1<sup>st </sup>month&nbsp; &rarr;&nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;2<br>2<sup>nd</sup> month &rarr;&nbsp; &nbsp;10&nbsp; &nbsp; :&nbsp; &nbsp;7<br>3<sup>rd</sup> month &rarr;&nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;4<br>4<sup>th</sup> month &rarr;&nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;8<br>--------------------------------------<br>Net Price &rarr;&nbsp; &nbsp;625&nbsp; &nbsp;:&nbsp; &nbsp;224<br>% decrease = <math display=\"inline\"><mfrac><mrow><mn>625</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>224</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>401</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math> &times; 100 = 64.16%</p>",
                    solution_hi: "<p>65.(c)<br>अनुपात&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr; आरंभिक : अंतिम<br>पहला महीना&nbsp; &rarr;&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 2<br>दूसरा महीना&nbsp; &rarr;&nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; :&nbsp; &nbsp; 7<br>तीसरा महीना &rarr;&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 4<br>चौथा महीना&nbsp; &rarr;&nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 8<br>------------------------------------------<br>कुल कीमत&nbsp; &rarr;&nbsp; &nbsp; &nbsp;625&nbsp; &nbsp; :&nbsp; &nbsp; 224<br>% कमी = <math display=\"inline\"><mfrac><mrow><mn>625</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>224</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>401</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math> &times; 100 = 64.16%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A shopkeeper sells an item for ₹940.8 after giving two successive discounts of 84% and 44% on its marked price. Had he not given any discount, he would have earned a profit of 25%. What is the cost price (in ₹) of the item?</p>",
                    question_hi: "<p>66. एक दुकानदार किसी वस्तु को उसके अंकित मूल्य पर 84% और 44% की दो क्रमिक छूट देकर ₹940.8 में बेचता है। यदि उसके द्वारा कोई छूट नहीं दी गई होती, तो वह 25% का लाभ अर्जित करता। वस्तु का क्रय मूल्य (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>8363</p>", "<p>8404</p>", 
                                "<p>8441</p>", "<p>8400</p>"],
                    options_hi: ["<p>8363</p>", "<p>8404</p>",
                                "<p>8441</p>", "<p>8400</p>"],
                    solution_en: "<p>66.(d)<br>SP = MP &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>100</mn></mfrac></math><br>940.8 = MP &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>100</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>9408</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = MP &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>100</mn></mfrac></math><br>MP = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9408</mn><mo>&#215;</mo><mn>10000</mn></mrow><mrow><mn>16</mn><mo>&#215;</mo><mn>56</mn><mo>&#215;</mo><mn>10</mn></mrow></mfrac></math> = ₹10500<br>CP = MP &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math><br>CP = 10500 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = ₹8400</p>",
                    solution_hi: "<p>66.(d)<br>विक्रय मूल्य = अंकित मूल्य &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>100</mn></mfrac></math><br>940.8 = अंकित मूल्य &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>100</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>9408</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = अंकित मूल्य &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>100</mn></mfrac></math><br>अंकित मूल्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9408</mn><mo>&#215;</mo><mn>10000</mn></mrow><mrow><mn>16</mn><mo>&#215;</mo><mn>56</mn><mo>&#215;</mo><mn>10</mn></mrow></mfrac></math>&nbsp;= ₹10500<br>क्रय मूल्य = अंकित मूल्य &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math><br>क्रय मूल्य = 10500 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>&nbsp;= ₹8400</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Find the altitude (in cm) from the vertex Q to side PR of triangle PQR with side lengths&nbsp;PQ = 40 cm, PR = 40 cm and QR = 60 cm.</p>",
                    question_hi: "<p>67. भुजाओं की लंबाई PQ = 40 सेमी, PR = 40 सेमी और QR = 60 सेमी वाले त्रिभुज PQR के शीर्ष Q से&nbsp;भुजा PR तक का शीर्षलंब (सेमी में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>25<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>20<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", 
                                "<p>15<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>30<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>"],
                    options_hi: ["<p>25<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>20<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>",
                                "<p>15<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>30<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>"],
                    solution_en: "<p>67.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639322274.png\" alt=\"rId41\" width=\"149\" height=\"130\"><br>Semiperimeter(s) = <math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>60</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>2</mn></mfrac></math> = 70 cm<br>Using Heron&rsquo;s formula, Area of triangle = <math display=\"inline\"><msqrt><mi>s</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>a</mi><mo>)</mo><mo>(</mo><mi>s</mi><mo>-</mo><mi>b</mi><mo>)</mo><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>70</mn><mo>(</mo><mn>70</mn><mo>-</mo><mn>40</mn><mo>)</mo><mo>(</mo><mn>70</mn><mo>-</mo><mn>40</mn><mo>)</mo><mo>(</mo><mn>70</mn><mo>-</mo><mn>60</mn><mo>)</mo></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>70</mn><mo>&#215;</mo><mn>30</mn><mo>&#215;</mo><mn>30</mn><mo>&#215;</mo><mn>10</mn></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>90000</mn><mo>&#215;</mo><mn>7</mn></msqrt></math> <br>= 300<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math><br>Also, area of triangle = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>&times; x &times; 40 = 20x<br>Now, 20x = 300<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math><br>x = 15<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>67.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639322274.png\" alt=\"rId41\" width=\"191\" height=\"167\"><br>अर्धपरिमाप(s) = <math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>60</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>2</mn></mfrac></math> = 70 सेमी<br>हीरोन के सूत्र का उपयोग करते हुए, त्रिभुज का क्षेत्रफल = <math display=\"inline\"><msqrt><mi>s</mi><mo>(</mo><mi>s</mi><mo>-</mo><mi>a</mi><mo>)</mo><mo>(</mo><mi>s</mi><mo>-</mo><mi>b</mi><mo>)</mo><mo>(</mo><mi>s</mi><mo>-</mo><mi>c</mi><mo>)</mo></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>70</mn><mo>(</mo><mn>70</mn><mo>-</mo><mn>40</mn><mo>)</mo><mo>(</mo><mn>70</mn><mo>-</mo><mn>40</mn><mo>)</mo><mo>(</mo><mn>70</mn><mo>-</mo><mn>60</mn><mo>)</mo></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>70</mn><mo>&#215;</mo><mn>30</mn><mo>&#215;</mo><mn>30</mn><mo>&#215;</mo><mn>10</mn></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>90000</mn><mo>&#215;</mo><mn>7</mn></msqrt></math> <br>= 300<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math><br>त्रिभुज का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; x &times; 40 = 20x<br>अब, 20x = 300<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math><br>x = 15<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math> सेमी</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Kiran lent ₹4,000 to two persons such that one part of it at the rate of 8% per annum and the remaining part at the rate of 10% per annum. He got an annual simple interest of ₹352. Then the sum lent at 10% is:</p>",
                    question_hi: "<p>68. किरण ने दो व्यक्तियों को ₹ 4,000 का एक भाग 8% प्रति वर्ष की दर से और शेष भाग 10% प्रति वर्ष की दर से उधार दिए । उसे ₹ 352 का वार्षिक साधारण ब्याज मिला। तो 10% पर उधार दी गई राशि ज्ञात करें।</p>",
                    options_en: ["<p>₹1800</p>", "<p>₹2200</p>", 
                                "<p>₹2400</p>", "<p>₹1600</p>"],
                    options_hi: ["<p>₹1800</p>", "<p>₹2200</p>",
                                "<p>₹2400</p>", "<p>₹1600</p>"],
                    solution_en: "<p>68.(d) <br>Overall rate% = <math display=\"inline\"><mfrac><mrow><mn>352</mn></mrow><mrow><mn>4000</mn></mrow></mfrac></math> &times; 100 = 8.8%<br>Using Alligation method, we have ;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639322404.png\" alt=\"rId42\" width=\"177\" height=\"176\"><br>So, the sum lent at 10% = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> &times; 4000 = ₹1600</p>",
                    solution_hi: "<p>68.(d) <br>कुल दर % = <math display=\"inline\"><mfrac><mrow><mn>352</mn></mrow><mrow><mn>4000</mn></mrow></mfrac></math>&times;100 = 8.8%<br>एलीगेशन विधि का उपयोग करने पर - <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639322404.png\" alt=\"rId42\" width=\"190\" height=\"189\"><br>तो, 10% पर उधार दी गई राशि = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> &times; 4000 = ₹1600</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Simplify the following:<br>[{(- 0.4) + (4.6)<sup>2</sup>&nbsp;+ (2.3)<sup>2</sup>} &divide; 5 ] - 3.24</p>",
                    question_hi: "<p>69. निम्नलिखित को सरल कीजिए:<br>[{(- 0.4) + (4.6)<sup>2</sup>&nbsp;+ (2.3)<sup>2</sup>} &divide; 5 ] - 3.24</p>",
                    options_en: ["<p>1.93</p>", "<p>1.79</p>", 
                                "<p>1.39</p>", "<p>1.97</p>"],
                    options_hi: ["<p>1.93</p>", "<p>1.79</p>",
                                "<p>1.39</p>", "<p>1.97</p>"],
                    solution_en: "<p>69.(d)<br>[{(- 0.4) + (4.6)<sup>2</sup>&nbsp;+ (2.3)<sup>2</sup>} &divide; 5 ] - 3.24<br>= [{(- 0.4) + 21.16 + 5.29 } &divide; 5 ] - 3.24<br>= [{- 0.4 + 26.45} &divide; 5 ] - 3.24<br>= 26.05 &divide; 5 - 3.24<br>= 5.21 - 3.24<br>= 1.97</p>",
                    solution_hi: "<p>69.(d)<br>[{(- 0.4) + (4.6)<sup>2</sup>&nbsp;+ (2.3)<sup>2</sup>} &divide; 5 ] - 3.24<br>= [{(- 0.4) + 21.16 + 5.29 } &divide; 5 ] - 3.24<br>= [{- 0.4 + 26.45} &divide; 5 ] - 3.24<br>= 26.05 &divide; 5 - 3.24<br>= 5.21 - 3.24<br>= 1.97</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. If A is an acute angle and tanA + cotA = 2, find the value of 7tan<sup>8</sup>A - 6cot<sup>8</sup>A + 8sec<sup>2</sup>A.</p>",
                    question_hi: "<p>70. यदि A एक न्यून कोण है और tanA + cotA = 2 है, तो 7tan<sup>8</sup>A - 6cot<sup>8</sup>A + 8sec<sup>2</sup>A का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>6</p>", "<p>17</p>", 
                                "<p>16</p>", "<p>7</p>"],
                    options_hi: ["<p>6</p>", "<p>17</p>",
                                "<p>16</p>", "<p>7</p>"],
                    solution_en: "<p>70.(b)<br>tanA + cotA = 2<br>Put A = 45&deg;<br>LHS = tan45&deg; + cot45&deg;<br>= 1 + 1 = RHS<br>Now,<br>7tan<sup>8</sup>45&deg; - 6cot<sup>8</sup>45&deg; + 8sec<sup>2</sup>45&deg;<br>7 &times; (1)<sup>8</sup>&nbsp;- 6 &times; (1)<sup>8</sup> + 8 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>)<sup>2</sup><br>7 - 6 + 16 = 17</p>",
                    solution_hi: "<p>70.(b)<br>tanA + cotA = 2<br>Put A = 45&deg;<br>LHS = tan45&deg; + cot45&deg;<br>= 1 + 1 = RHS<br>अब,<br>7tan<sup>8</sup>45&deg; - 6cot<sup>8</sup>45&deg; + 8sec<sup>2</sup>45&deg;<br>7 &times; (1)<sup>8</sup>&nbsp;- 6 &times; (1)<sup>8</sup> + 8 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>)<sup>2</sup><br>7 - 6 + 16 = 17</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Lalit\'s average earning per month in the first three months of a year was ₹4080. In April, his earning was 75% more than the average earning in the first three months. If his average earning per month for the whole year is ₹66405, then what will be Lalit\'s average earning (in ₹) per month from May to December?</p>",
                    question_hi: "<p>71. वर्ष के प्रथम तीन महीनों में ललित की प्रति माह औसत आय ₹4080 थी। अप्रैल में उसकी आय, प्रथम तीन महीनों की औसत आय से 75% अधिक थी। यदि पूरे वर्ष केलिए उसकी प्रति माह औसत आय ₹66405 है, तो मई से दिसंबर तक ललित की प्रति माह औसत आय (₹ में) कितनी होगी?</p>",
                    options_en: ["<p>97185</p>", "<p>97180</p>", 
                                "<p>97184</p>", "<p>97183</p>"],
                    options_hi: ["<p>97185</p>", "<p>97180</p>",
                                "<p>97184</p>", "<p>97183</p>"],
                    solution_en: "<p>71.(a)<br>Sum of first three months = 4080 &times; 3 = ₹12,240<br>His April month earning = 4080 &times; <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹7140<br>Sum of the money of May to December = 66405 &times; 12 - (12,240 + 7140) <br>= 796860 - 19380 = ₹777480<br>Required Average = <math display=\"inline\"><mfrac><mrow><mn>777480</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = ₹97185</p>",
                    solution_hi: "<p>71.(a)<br>पहले तीन महीनों का योग = 4080 &times; 3 = ₹12,240<br>अप्रैल महीने की कमाई = 4080 &times; <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹7140<br>मई से दिसंबर का योग = 66405 &times; 12 - (12,240 + 7140) <br>= 796860 - 19380 = ₹777480<br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>777480</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = ₹97185</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If the greatest common factor of two numbers is 47 and their sum is 188 then find those two numbers.</p>",
                    question_hi: "<p>72. यदि दो संख्याओं का महत्तम समापवर्तक 47 है और उनका योग 188 है तो वे दोनों संख्याएं ज्ञात कीजिए।</p>",
                    options_en: ["<p>47 and 51</p>", "<p>215 and 211</p>", 
                                "<p>47 and 141</p>", "<p>141 and 138</p>"],
                    options_hi: ["<p>47 और 51</p>", "<p>215 और 211</p>",
                                "<p>47 और 141</p>", "<p>141 और 138</p>"],
                    solution_en: "<p>72.(c)<br>Let the numbers be 47x and47y respectively where (x,y are co-primes)<br>ATQ,<br>47x + 47y = 188<br>47(x + y) = 188<br>x + y = 4<br>The only possible pairs = (1,3)<br>so, two no&rsquo;s are ; 47 &times; 1 = 47 and 47 &times; 3 = 141</p>",
                    solution_hi: "<p>72.(c)<br>माना संख्याएँ क्रमशः 47x और 47y हैं (जहाँ x,y सह-अभाज्य हैं)<br>प्रश्नानुसार,<br>47x + 47y = 188<br>47(x + y) = 188<br>x + y = 4<br>एकमात्र संभावित जोड़े = (1,3)<br>तो, दो संख्याएँ हैं; 47 &times; 1 = 47 और 47 &times; 3 = 141</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Study the given table and answer the question that follows. The table shows the monthly expenditure (in ₹) of five people on education, rent, food, conveyance and electricity. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639322531.png\" alt=\"rId43\" width=\"347\" height=\"106\"> <br>Find the difference between the average amount spent on education and the average amount spent on conveyance.</p>",
                    question_hi: "<p>73. दी गई तालिका का अध्ययन कीजिए और प्रश्न का उत्तर दीजिए।<br>तालिका शिक्षा, किराया, भोजन, परिवहन और विद्युत पर पांच व्यक्तियों का मासिक व्यय (₹ में) दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740639322649.png\" alt=\"rId44\" width=\"303\" height=\"133\"> <br>शिक्षा पर खर्च हुई औसत राशि और परिवहन पर खर्च हुई औसत राशि के बीच अंतर ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹1,845</p>", "<p>₹1,698</p>", 
                                "<p>₹2,460</p>", "<p>₹2,080</p>"],
                    options_hi: ["<p>₹1,845</p>", "<p>₹1,698</p>",
                                "<p>₹2,460</p>", "<p>₹2,080</p>"],
                    solution_en: "<p>73.(d)<br>Average amount spent on Education = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5000</mn><mo>+</mo><mn>6400</mn><mo>+</mo><mn>5500</mn><mo>+</mo><mn>6000</mn><mo>+</mo><mn>7000</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29900</mn><mn>5</mn></mfrac></math> = 5980<br>Average amount spent on Conveyance = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3500</mn><mo>+</mo><mn>4000</mn><mo>+</mo><mn>3600</mn><mo>+</mo><mn>4400</mn><mo>+</mo><mn>4000</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19500</mn><mn>5</mn></mfrac></math> = 3900<br>Required difference = 5980 - 3900 = 2080</p>",
                    solution_hi: "<p>73.(d)<br>शिक्षा पर खर्च की गई औसत राशि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5000</mn><mo>+</mo><mn>6400</mn><mo>+</mo><mn>5500</mn><mo>+</mo><mn>6000</mn><mo>+</mo><mn>7000</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29900</mn><mn>5</mn></mfrac></math> = 5980<br>परिवहन पर खर्च की गई औसत राशि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3500</mn><mo>+</mo><mn>4000</mn><mo>+</mo><mn>3600</mn><mo>+</mo><mn>4400</mn><mo>+</mo><mn>4000</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19500</mn><mn>5</mn></mfrac></math> = 3900<br>आवश्यक अंतर = 5980 - 3900 = 2080</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. What is the value of cos(x + y) - sin(x - y) + tan(2z)?</p>",
                    question_hi: "<p>74. cos(x + y) - sin(x - y) + tan(2z) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>cos x. cos y - sin x. sin y - sin x. cos y - cos x. sin y + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>", "<p>sin x. sin y + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>", 
                                "<p>cos x.cos y - sin x. sin y - sin x.cos y + cos x. sin y + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>", "<p>cos x.cos y - sin x. sin y - cos x. sin y + <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>"],
                    options_hi: ["<p>cos x. cos y - sin x. sin y - sin x. cos y - cos x. sin y + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>", "<p>sin x. sin y + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>",
                                "<p>cos x.cos y - sin x. sin y - sin x.cos y + cos x. sin y + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>", "<p>cos x.cos y - sin x. sin y - cos x. sin y + <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>"],
                    solution_en: "<p>74.(c)<br>As we know <br>{cos(x + y) = cos x.cos y - sin x. sin y }<br>{ sin(x - y) = sin x.cos y - cos x. sin y}<br>{tan(2z) = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math> }<br>So, cos(x + y) - sin(x - y) + tan(2z)<br>= cos x.cos y - sin x. sin y - (sin x.cos y - cos x. sin y) + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math><br>= cos x.cos y - sin x. sin y - sin x.cos y + cos x. sin y + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>",
                    solution_hi: "<p>74.(c)<br>जैसा कि हम जानते हैं <br>{cos(x + y) = cos x.cos y - sin x. sin y }<br>{ sin(x - y) = sin x.cos y - cos x. sin y}<br>{tan(2z) = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math> }<br>तो , cos(x + y) - sin(x - y) + tan(2z)<br>= cos x.cos y - sin x. sin y - (sin x.cos y - cos x. sin y) + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math><br>= cos x.cos y - sin x. sin y - sin x.cos y + cos x. sin y + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The shopkeeper offers two successive discounts on a machine that is marked at ₹18,000 so that its selling price gets reduced to ₹12,960. If 10% and x % are the respective discounts, the value of x is:</p>",
                    question_hi: "<p>75. एक दुकानदार, ₹18,000 अंकित मूल्य वाली एक मशीन पर दो क्रमिक छूटें देता है, जिससे इसका विक्रय मूल्य घटकर ₹12,960 हो जाता है। यदि 10% और x% क्रमिक छूटें है, तो x का मान कितना होगा ?</p>",
                    options_en: ["<p>20</p>", "<p>10</p>", 
                                "<p>15</p>", "<p>12</p>"],
                    options_hi: ["<p>20</p>", "<p>10</p>",
                                "<p>15</p>", "<p>12</p>"],
                    solution_en: "<p>75.(a)<br>18000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mi>x</mi></mrow><mn>100</mn></mfrac></math> = 12960<br>&rArr; 100 - x = 80<br>&rArr; x = 100 - 80 = 20%</p>",
                    solution_hi: "<p>75.(a)<br>18000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mi>x</mi></mrow><mn>100</mn></mfrac></math> = 12960<br>&rArr; 100 - x = 80<br>&rArr; x = 100 - 80 = 20%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate synonym of the word in brackets to fill in the blank. <br>Queen Gulnaar of Arabia mourned her ________ beauty. (waning)</p>",
                    question_hi: "<p>76. Select the most appropriate synonym of the word in brackets to fill in the blank. <br>Queen Gulnaar of Arabia mourned her ________ beauty. (waning)</p>",
                    options_en: ["<p>false</p>", "<p>fading</p>", 
                                "<p>futile</p>", "<p>frightening</p>"],
                    options_hi: ["<p>false</p>", "<p>fading</p>",
                                "<p>futile</p>", "<p>frightening</p>"],
                    solution_en: "<p>76.(b) <strong>Fading-</strong> gradually grow faint and disappear.<br><strong>Waning- </strong>decreasing in size, strength, or power.<br><strong>False-</strong> not true or correct.<br><strong>Futile- </strong>incapable of producing any result; useless.<br><strong>Frightening- </strong>causing fear or alarm.</p>",
                    solution_hi: "<p>76.(b) <strong>Fading </strong>(लुप्तप्राय) - gradually grow faint and disappear.<br><strong>Waning </strong>(क्षीण होना) - decreasing in size, strength, or power.<br><strong>False </strong>(असत्य) - not true or correct.<br><strong>Futile </strong>(निरर्थक) - incapable of producing any result; useless.<br><strong>Frightening </strong>(भयावह) - causing fear or alarm.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate option that rectifies the error(s) in the following sentence. <br>One must do his/her duty proparly.</p>",
                    question_hi: "<p>77. Select the most appropriate option that rectifies the error(s) in the following sentence. <br>One must do his/her duty proparly.</p>",
                    options_en: ["<p>One must do once duty proparly.</p>", "<p>One must do ones duty properly.</p>", 
                                "<p>One must do one&rsquo;s duti proprly.</p>", "<p>One must do one\'s duty properly</p>"],
                    options_hi: ["<p>One must do once duty proparly.</p>", "<p>One must do ones duty properly.</p>",
                                "<p>One must do one&rsquo;s duti proprly.</p>", "<p>One must do one\'s duty properly</p>"],
                    solution_en: "<p>77.(d) One must do one\'s duty properly<br><span style=\"text-decoration: underline;\">One&rsquo;s</span> is the correct possessive used for the pronoun &lsquo;one&rsquo;. Hence, the sentence given in option (d) is correct.</p>",
                    solution_hi: "<p>77.(d) One must do one\'s duty properly<br>Pronoun &lsquo;one&rsquo; के लिए प्रयुक्त सही possessive <span style=\"text-decoration: underline;\">One&rsquo;s</span> है। अतः option (d) में दिया गया sentence सही है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>The patient waited in dread for the trolly <span style=\"text-decoration: underline;\"><strong>it would taken him</strong></span> to the operation theatre.</p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>The patient waited in dread for the trolly <span style=\"text-decoration: underline;\"><strong>it would taken him</strong></span>&nbsp;to the operation theatre.</p>",
                    options_en: ["<p>that would taken</p>", "<p>it would take</p>", 
                                "<p>that would take</p>", "<p>no improvement.</p>"],
                    options_hi: ["<p>that would taken</p>", "<p>it would take</p>",
                                "<p>that would take</p>", "<p>no improvement.</p>"],
                    solution_en: "<p>78.(c) that would take<br>We always use the root form of a verb(V<sub>1</sub>) after a modal verb. Similarly, in the given sentence, the auxiliary verb(would) must be followed by &lsquo;V<sub>1</sub>&rsquo;. Hence, &lsquo;would take(V<sub>1</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(c) that would take<br>हम हमेशा verb(V<sub>1</sub>) के मूल रूप का उपयोग एक modal verb के बाद करते हैं। इसी प्रकार, दिए गए वाक्य में, auxiliary verb(would) के बाद verb(V<sub>1</sub>)\' होना चाहिए। इसलिए, &lsquo;would take(V<sub>1</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate option that can substitute the underlined segment in the&nbsp;given sentence.<br>Rahul had <span style=\"text-decoration: underline;\">strong</span> pain in his stomach.</p>",
                    question_hi: "<p>79. Select the most appropriate option that can substitute the underlined segment in the&nbsp;given sentence.<br>Rahul had <span style=\"text-decoration: underline;\">strong</span> pain in his stomach.</p>",
                    options_en: ["<p>strict</p>", "<p>severe</p>", 
                                "<p>high</p>", "<p>deep</p>"],
                    options_hi: ["<p>strict</p>", "<p>severe</p>",
                                "<p>high</p>", "<p>deep</p>"],
                    solution_en: "<p>79.(b) <strong>Severe -</strong> very serious, harsh or extreme.<br><strong>Strict-</strong> someone who sticks to a particular set of rules.</p>",
                    solution_hi: "<p>79.(b) <strong>Severe</strong> (गंभीर) - very serious, harsh or extreme.<br><strong>Strict</strong> (सख्त) - someone who sticks to a particular set of rules.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that expresses the given sentence in passive voice.<br>He ordered the police to pursue the robber and his gang.</p>",
                    question_hi: "<p>80. Select the option that expresses the given sentence in passive voice.<br>He ordered the police to pursue the robber and his gang.</p>",
                    options_en: ["<p>The police were ordered to pursue the robber and his gang.</p>", "<p>Order the police to pursue the robber gang.</p>", 
                                "<p>Pursue the robber and gang were the orders by police.</p>", "<p>The robber and gang were pursued.</p>"],
                    options_hi: ["<p>The police were ordered to pursue the robber and his gang.</p>", "<p>Order the police to pursue the robber gang.</p>",
                                "<p>Pursue the robber and gang were the orders by police.</p>", "<p>The robber and gang were pursued.</p>"],
                    solution_en: "<p>80.(a) The police were ordered to pursue the robber and his gang. (Correct)<br>(b) <span style=\"text-decoration: underline;\">Order the police to pursue</span> the robber gang. (Incorrect sentence structure)<br>(c) <span style=\"text-decoration: underline;\">Pursue the robber and gang were</span> the orders by police. (Incorrect sentence structure)<br>(d) <span style=\"text-decoration: underline;\">The robber and gang were pursued.</span> (Incorrect sentence structure)</p>",
                    solution_hi: "<p>80.(a) The police were ordered to pursue the robber and his gang. (Correct)<br>(b) <span style=\"text-decoration: underline;\">Order the police to pursue</span> the robber gang. (गलत sentence structure)<br>(c) <span style=\"text-decoration: underline;\">Pursue the robber and gang were</span> the orders by police. (गलत sentence structure)<br>(d) <span style=\"text-decoration: underline;\">The robber and gang were</span> pursued. (गलत sentence structure)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate meaning of the given phrase.<br>Hit the sack</p>",
                    question_hi: "<p>81. Select the most appropriate meaning of the given phrase.<br>Hit the sack</p>",
                    options_en: ["<p>Cracking the exams</p>", "<p>Making money quickly</p>", 
                                "<p>Going to sleep</p>", "<p>Running on the beach</p>"],
                    options_hi: ["<p>Cracking the exams</p>", "<p>Making money quickly</p>",
                                "<p>Going to sleep</p>", "<p>Running on the beach</p>"],
                    solution_en: "<p>81.(c) <strong>Hit the sack </strong>- going to sleep.<br>E.g.- I am really tired, so I am going to hit the sack early tonight.</p>",
                    solution_hi: "<p>81.(c) <strong>Hit the sack</strong> - going to sleep./सोने जाना<br>E.g.- I am really tired, so I am going to hit the sack early tonight.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82.Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: There are more men on the same land.<br>Q: We will have to make more schools, more hospitals and more essential things for the growing population.<br>R: There is no increase in our resources.<br>S: But there is a regular increase in our population.</p>",
                    question_hi: "<p>82.Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: There are more men on the same land.<br>Q: We will have to make more schools, more hospitals and more essential things for the growing population.<br>R: There is no increase in our resources.<br>S: But there is a regular increase in our population.</p>",
                    options_en: ["<p>RSPQ</p>", "<p>RPSQ</p>", 
                                "<p>PRSQ</p>", "<p>QPRS</p>"],
                    options_hi: ["<p>RSPQ</p>", "<p>RPSQ</p>",
                                "<p>PRSQ</p>", "<p>QPRS</p>"],
                    solution_en: "<p>82.(a) <strong>RSPQ</strong> <br>Sentence R will be the starting line as it contains the main idea of the parajumble i.e. There is no increase in our resources. However, Sentence S states that there is a regular increase in our population. So, S will follow . R. Further, Sentence P states that there are more men on the same land &amp; Sentence Q states that we will have to make more schools, more hospitals and more essential things for the growing population. So, Q will follow P. Going through the options, only option a has the correct sequence.</p>",
                    solution_hi: "<p>82.(a) <strong>RSPQ</strong> <br>वाक्य R शुरूआती पंक्ति (line) होगी क्योंकि इसमें parajumble का मुख्य विचार है - हमारे संसाधनों में कोई वृद्धि नहीं हुई है। वाक्य S बताता है कि हमारी जनसंख्या में नियमित वृद्धि हो रही है। तो, R के बाद S का प्रयोग होगा । आगे, वाक्य P बताता है कि एक ही भूमि पर अधिक पुरुष हैं और वाक्य Q में कहा गया है कि हमें बढ़ती आबादी के लिए और अधिक स्कूल, अधिक अस्पताल और अधिक आवश्यक चीजें बनानी होंगी। तो, P के बाद Q आएगा। विकल्पों के माध्यम से जाने पर, केवल विकल्प a में सही क्रम है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate synonym of the given word.<br>Wanton</p>",
                    question_hi: "<p>83. Select the most appropriate synonym of the given word.<br>Wanton</p>",
                    options_en: ["<p>Abrogation</p>", "<p>Obscene</p>", 
                                "<p>Prevent</p>", "<p>Habit</p>"],
                    options_hi: ["<p>Abrogation</p>", "<p>Obscene</p>",
                                "<p>Prevent</p>", "<p>Habit</p>"],
                    solution_en: "<p>83.(b) <strong>Obscene-</strong> offensive or indecent, often in a shocking or inappropriate way.<br><strong>Wanton-</strong> reckless or deliberate without regard for others\' feelings or consequences.<br><strong>Abrogation-</strong> the act of officially ending or canceling something, like a law or agreement.<br><strong>Prevent-</strong> to stop something from happening or keep it from occurring.<br><strong>Habit-</strong> a regular practice or behavior done often, usually without thinking.</p>",
                    solution_hi: "<p>83.(b) <strong>Obscene</strong>(अश्लील)- offensive or indecent, often in a shocking or inappropriate way.<br><strong>Wanton</strong>(ऊधमी)- reckless or deliberate without regard for others\' feelings or consequences.<br><strong>Abrogation</strong>(निष्प्रभावी)- the act of officially ending or canceling something, like a law or agreement.<br><strong>Prevent</strong>(रोकना)- to stop something from happening or keep it from occurring.<br><strong>Habit</strong>(आदत)- a regular practice or behavior done often, usually without thinking.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Identify the correctly spelt word.</p>",
                    question_hi: "<p>84. Identify the correctly spelt word.</p>",
                    options_en: ["<p>Humanitarian</p>", "<p>Humungus</p>", 
                                "<p>Hoardeng</p>", "<p>Hallabaloo</p>"],
                    options_hi: ["<p>Humanitarian</p>", "<p>Humungus</p>",
                                "<p>Hoardeng</p>", "<p>Hallabaloo</p>"],
                    solution_en: "<p>84.(a) Humanitarian</p>",
                    solution_hi: "<p>84.(a) Humanitarian</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the option that expresses the given sentence in direct speech.<br>The teacher said that plants have life in them.</p>",
                    question_hi: "<p>85. Select the option that expresses the given sentence in direct speech.<br>The teacher said that plants have life in them.</p>",
                    options_en: ["<p>The teacher said, &ldquo;Plants have life in them.&rdquo;</p>", "<p>The teacher says, &ldquo;A plant has life in them.&rdquo;</p>", 
                                "<p>The teacher said, &ldquo;Plants too have life in them.&rdquo;</p>", "<p>The teacher said, &ldquo;The plants had life in them.&rdquo;</p>"],
                    options_hi: ["<p>The teacher said, &ldquo;Plants have life in them.&rdquo;</p>", "<p>The teacher says, &ldquo;A plant has life in them.&rdquo;</p>",
                                "<p>The teacher said, &ldquo;Plants too have life in them.&rdquo;</p>", "<p>The teacher said, &ldquo;The plants had life in them.&rdquo;</p>"],
                    solution_en: "<p>85.(a) The teacher said, &ldquo;Plants have life in them.&rdquo; (Correct)<br>(b) The teacher <span style=\"text-decoration: underline;\">says,</span> &ldquo;A plant has life in them.&rdquo; (Incorrect form of Reporting Verb)<br>(c) The teacher said, &ldquo;Plants <span style=\"text-decoration: underline;\">too</span> have life in them.&rdquo; (&lsquo;Too&rsquo; is not required)<br>(d) The teacher said, &ldquo;The plants <span style=\"text-decoration: underline;\">had</span> life in them.&rdquo; (Incorrect Tense)</p>",
                    solution_hi: "<p>85.(a) The teacher said, &ldquo;Plants have life in them.&rdquo; (Correct)<br>(b) The teacher <span style=\"text-decoration: underline;\">says</span>, &ldquo;A plant has life in them.&rdquo; (Reporting Verb की गलत form)<br>(c) The teacher said, &ldquo;Plants <span style=\"text-decoration: underline;\">too</span> have life in them.&rdquo; (&lsquo;Too&rsquo; required नहीं है)<br>(d) The teacher said, &ldquo;The plants <span style=\"text-decoration: underline;\">had</span> life in them.&rdquo; (गलत Tense)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate meaning of the idiom given in the following Question<br>By fits and starts</p>",
                    question_hi: "<p>86. Select the most appropriate meaning of the idiom given in the following Question<br>By fits and starts</p>",
                    options_en: ["<p>consistently</p>", "<p>irregularly</p>", 
                                "<p>in high spirits</p>", "<p>enthusiastically</p>"],
                    options_hi: ["<p>consistently</p>", "<p>irregularly</p>",
                                "<p>in high spirits</p>", "<p>enthusiastically</p>"],
                    solution_en: "<p>86.(b) irregularly. <br>Example - My friend Harish works by fits and starts.</p>",
                    solution_hi: "<p>86.(b) irregularly./अनियमित <br>उदाहरण - My friend Harish works by fits and starts./मेरा मित्र हरीश अनियमित रूप से कार्य करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Identify the segment in the sentence, which contains the grammatical error.<br>It is high time Raghav renovate his old house</p>",
                    question_hi: "<p>87. Identify the segment in the sentence, which contains the grammatical error.<br>It is high time Raghav renovate his old house</p>",
                    options_en: ["<p>It is high time</p>", "<p>Raghav renovate</p>", 
                                "<p>his old house</p>", "<p>No error</p>"],
                    options_hi: ["<p>It is high time</p>", "<p>Raghav renovate</p>",
                                "<p>his old house</p>", "<p>No error</p>"],
                    solution_en: "<p>87.(b) Raghav renovate<br><span style=\"text-decoration: underline;\">&ldquo;It is time/It is high time + </span>V<sub>2</sub><span style=\"text-decoration: underline;\">(second form of the verb</span>)&rdquo; is the correct grammatical structure for the given sentence. Hence, &lsquo;renovate&rsquo; will be replaced with &lsquo;renovated(V<sub>2</sub>)&rsquo; to make the given sentence grammatically correct.</p>",
                    solution_hi: "<p>87.(b) Raghav renovate<br><span style=\"text-decoration: underline;\">&ldquo;It is time/It is high time + </span>V<sub>2</sub><span style=\"text-decoration: underline;\">(second form of the verb</span>)&rdquo; दिए गए वाक्य के लिए सही grammatical structure है। इसलिए, दिए गए वाक्य को व्याकरणिक रूप से सही बनाने के लिए \'renovate\' को \'renovated (V<sub>2</sub>)\' से बदल दिया जाएगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the word which means the same as the group of words given. <br>One who is indifferent to pleasure or pain.</p>",
                    question_hi: "<p>88. Select the word which means the same as the group of words given. <br>One who is indifferent to pleasure or pain.</p>",
                    options_en: ["<p>stoic</p>", "<p>stylist</p>", 
                                "<p>cynic</p>", "<p>psychic</p>"],
                    options_hi: ["<p>stoic</p>", "<p>stylist</p>",
                                "<p>cynic</p>", "<p>psychic</p>"],
                    solution_en: "<p>88.(a) <strong>stoic-</strong> indifferent to pleasure or pain <br><strong>Stylist</strong>- a designer or consultant in a field subject to changes in style<br><strong>Cynic-</strong> a person who believes that only selfishness motivates human actions<br><strong>Psychic-</strong> relating to mind</p>",
                    solution_hi: "<p>88.(a) <strong>Stoic (</strong><span class=\"Y2IQFc\" lang=\"hi\"><strong>उदासीन)</strong> - </span>indifferent to pleasure or pain<br><strong>Stylist(शैलीकार )- </strong>a designer or consultant in a field subject to changes in style<br><strong>Cynic(मानवद्वेषी)-</strong> a person who believes that only selfishness motivates human actions<br><strong>Psychic(मानसिक)-</strong> relating to mind</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the option that can be used as a one-word substitute for the given group of words.<br>The study of Man</p>",
                    question_hi: "<p>89. Select the option that can be used as a one-word substitute for the given group of words.<br>The study of Man</p>",
                    options_en: ["<p>Anthropology</p>", "<p>Biography</p>", 
                                "<p>Autobiography</p>", "<p>Biology</p>"],
                    options_hi: ["<p>Anthropology</p>", "<p>Biography</p>",
                                "<p>Autobiography</p>", "<p>Biology</p>"],
                    solution_en: "<p>89.(a) <strong>Anthropology-</strong> the study of man.<br><strong>Biography-</strong> an account of a person&rsquo;s life written by someone else.<br><strong>Autobiography- </strong>an account of a person&rsquo;s life written by himself.<br><strong>Biology-</strong> a branch of science that deals with living organisms and their vital processes.</p>",
                    solution_hi: "<p>89.(a) <strong>Anthropology </strong>(मनुष्य जाति का विज्ञान) - the study of man.<br><strong>Biography</strong> (जीवनी) - an account of a person&rsquo;s life written by someone else.<br><strong>Autobiography </strong>(आत्मकथा) - an account of a person&rsquo;s life written by himself.<br><strong>Biology </strong>(जीव विज्ञान) - a branch of science that deals with living organisms and their vital processes.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate synonym of the given word.<br>Mourn</p>",
                    question_hi: "<p>90. Select the most appropriate synonym of the given word.<br>Mourn</p>",
                    options_en: ["<p>Approve</p>", "<p>Dread</p>", 
                                "<p>Amuse</p>", "<p>Grieve</p>"],
                    options_hi: ["<p>Approve</p>", "<p>Dread</p>",
                                "<p>Amuse</p>", "<p>Grieve</p>"],
                    solution_en: "<p>90.(d)&nbsp;<strong>Grieve- </strong>to feel great sadness.<br><strong>Mourn-</strong> to feel and show great sadness.<br><strong>Approve- </strong>to be pleased about something.<br><strong>Amuse- </strong>to make somebody laugh or smile.<br><strong>Dread - </strong>to be very afraid of or worried about something.</p>",
                    solution_hi: "<p>90.(d)<strong> Grieve (शोक करना) - to feel great sadness.</strong><br><strong>Mourn</strong> (शोक मनाना) - to feel and show great sadness.<br><strong>Approve</strong> (अनुमोदन करना) - to be pleased about something.<br><strong>Amuse</strong> (मनोरंजन करना) - to make somebody laugh or smile.<br><strong>Dread</strong> (भय) - to be very afraid of or worried about something.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the option that expresses the given sentence in passive voice.<br>The seller shall dispatch the parcel soon to Chennai.</p>",
                    question_hi: "<p>91.Select the option that expresses the given sentence in passive voice.<br>The seller shall dispatch the parcel soon to Chennai.</p>",
                    options_en: ["<p>The parcel will be dispatched soon to Chennai by the seller</p>", "<p>The parcel may be dispatched by the seller to Chennai soon</p>", 
                                "<p>The parcel might be dispatched by the seller to Chennai soon.</p>", "<p>The parcel can be dispatched by the seller to Chennai soon.</p>"],
                    options_hi: ["<p>The parcel will be dispatched soon to Chennai by the seller</p>", "<p>The parcel may be dispatched by the seller to Chennai soon</p>",
                                "<p>The parcel might be dispatched by the seller to Chennai soon.</p>", "<p>The parcel can be dispatched by the seller to Chennai soon.</p>"],
                    solution_en: "<p>91.(a) The parcel will be dispatched soon to Chennai by the seller.(Correct)<br>(b) The parcel <span style=\"text-decoration: underline;\">may</span> be dispatched by the seller to Chennai soon.(Incorrect Verb)<br>(c) The parcel <span style=\"text-decoration: underline;\">might</span> be dispatched by the seller to Chennai soon.(Incorrect Verb)<br>(d) The parcel <span style=\"text-decoration: underline;\">can</span> be dispatched by the seller to Chennai soon.(Incorrect Verb)</p>",
                    solution_hi: "<p>91.(a) The parcel will be dispatched soon to Chennai by the seller. (Correct)<br>(b) The parcel <span style=\"text-decoration: underline;\">may</span> be dispatched by the seller to Chennai soon. (गलत Verb)<br>(c) The parcel <span style=\"text-decoration: underline;\">might</span> be dispatched by the seller to Chennai soon. (गलत Verb)<br>(d) The parcel <span style=\"text-decoration: underline;\">can</span> be dispatched by the seller to Chennai soon. (गलत Verb)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate option to fill in the blank.<br>Lata Mangeshkar was _______with a natural talent for music.</p>",
                    question_hi: "<p>92. Select the most appropriate option to fill in the blank.<br>Lata Mangeshkar was _______with a natural talent for music.</p>",
                    options_en: ["<p>given</p>", "<p>found</p>", 
                                "<p>endowed</p>", "<p>entrusted</p>"],
                    options_hi: ["<p>given</p>", "<p>found</p>",
                                "<p>endowed</p>", "<p>entrusted</p>"],
                    solution_en: "<p>92.(c) endowed , <strong>Be endowed with something </strong>- It means to naturally have a particular feature, quality etc.</p>",
                    solution_hi: "<p>92.(c) endowed <br><strong>Be endowed with something</strong> (किसी वस्तु से संपन्न होना) - It means to naturally have a particular feature, quality etc./इसका अर्थ है स्वाभाविक रूप से किसी विशेष गुण, गुण आदि का होना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. Strikes are not bad but their method is bad.<br>Q. Strikes are a weapon of the weak against the strong.<br>R. The workers often resort to strikes to highlight their de&shy;mands. <br>S. Workers should not go on strikes for long periods as the production suffers.</p>",
                    question_hi: "<p>93. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. Strikes are not bad but their method is bad.<br>Q. Strikes are a weapon of the weak against the strong.<br>R. The workers often resort to strikes to highlight their de&shy;mands. <br>S. Workers should not go on strikes for long periods as the production suffers.</p>",
                    options_en: ["<p>QPRS</p>", "<p>QSPR</p>", 
                                "<p>QRPS</p>", "<p>RQPS</p>"],
                    options_hi: ["<p>QPRS</p>", "<p>QSPR</p>",
                                "<p>QRPS</p>", "<p>RQPS</p>"],
                    solution_en: "<p>93.(c) <strong>QRPS.</strong> <br>Sentence Q will be the starting line as it contains the main idea of the parajumble i.e. Strikes are a weapon of the weak against the strong. However, Sentence R states that the workers often resort to strikes to highlight their de&shy;mands. So, R will follow Q . Further, Sentence P states that strikes are not bad but their method is bad &amp; Sentence S states that workers should not go on strikes for long periods as the production suffers. So, S will follow P. Going through the options, only option c has the correct sequence.</p>",
                    solution_hi: "<p>93.(c) <strong>QRPS.</strong> <br>वाक्य Q शुरुआती line होगी क्योंकि इसमें parajumble का मुख्य विचार है अर्थात हड़ताल कमजोर का ताकतवर के खिलाफ हथियार है । R वाक्य कहता है कि कर्मचारी अक्सर अपनी मांगों को उजागर करने के लिए हड़ताल का सहारा लेते हैं। तो Q के बाद R आयेगा । आगे, वाक्य P कहता है कि हड़ताल खराब नहीं हैं लेकिन उनका तरीका खराब है और वाक्य S बताता है कि श्रमिकों को लंबे समय तक हड़ताल पर नहीं जाना चाहिए क्योंकि उत्पादन का नुकसान होता है। तो, P के बाद S आयेगा । विकल्पों के माध्यम से जाने पर, केवल विकल्प c में सही क्रम है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate ANTONYM of the underlined word.<br>The detective\'s <span style=\"text-decoration: underline;\">astute</span> observations led to the discovery of crucial evidence, unlike his oblivious partner.</p>",
                    question_hi: "<p>94. Select the most appropriate ANTONYM of the underlined word.<br>The detective\'s <span style=\"text-decoration: underline;\">astute</span> observations led to the discovery of crucial evidence, unlike his oblivious partner.</p>",
                    options_en: ["<p>idiotic</p>", "<p>keen</p>", 
                                "<p>attentive</p>", "<p>sharp</p>"],
                    options_hi: ["<p>idiotic</p>", "<p>keen</p>",
                                "<p>attentive</p>", "<p>sharp</p>"],
                    solution_en: "<p>94.(a) <strong>Idiotic</strong>- very stupid and lacking intelligence.<br><strong>Astute</strong>- having sharp judgment and intelligence.<br><strong>Keen-</strong> having a strong sense of perception.<br><strong>Attentive</strong>- paying close attention to details.<br><strong>Sharp- </strong>quick to notice or understand things.</p>",
                    solution_hi: "<p>94.(a) <strong>Idiotic </strong>(मूर्खतापूर्ण) - very stupid and lacking intelligence.<br><strong>Astute </strong>(चतुर) - having sharp judgment and intelligence.<br><strong>Keen </strong>(तीव्र) - having a strong sense of perception.<br><strong>Attentive </strong>(सतर्क) - paying close attention to details.<br><strong>Sharp</strong> (तेज) - quick to notice or understand things.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank given in the sentence-<br>Hindus believe that ______ from the cycle of birth and rebirth can be attained only by good deeds.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank given in the sentence-<br>Hindus believe that ______ from the cycle of birth and rebirth can be attained only by good deeds.</p>",
                    options_en: ["<p>bondage</p>", "<p>deliverance</p>", 
                                "<p>delivery</p>", "<p>retirement</p>"],
                    options_hi: ["<p>bondage</p>", "<p>deliverance</p>",
                                "<p>delivery</p>", "<p>retirement</p>"],
                    solution_en: "<p>95.(b) <strong>deliverance</strong><br>&lsquo;Deliverance&rsquo; means the action of being rescued or set free. The given sentence talks about the deliverance from the cycle of birth and rebirth. Hence, &lsquo;deliverance&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(b) <strong>deliverance</strong> <br>&lsquo;Deliverance&rsquo; का अर्थ है छुड़ाए जाने या मुक्त किए जाने का प्रयास। दिया गया वाक्य जन्म और पुनर्जन्म के चक्र से मुक्ति के बारे में बात करता है। इसलिए, \'deliverance\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :</strong><br>Superheroes are fictional characters with 96) _______ powers who use their powers to fight crime and protect the public. Some popular superheroes include Superman, Batman, Spider-Man and Wonder Woman. Superheroes often have backstories that 97) _______ how they have gained their powers,98) ______ being born with special powers, being bitten by a radioactive spider or being exposed to cosmic radiation. They usually have a specific outfit or clothing that they wear to hide their identity and protect 99) _________ while fighting crime. Superheroes have been a staple of popular culture 100) ______ decades, appearing in comics, movies, television shows, and video games.<br>Select the most appropriate option to fill in blank no. 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test :</strong><br>Superheroes are fictional characters with (96) _______ powers who use their powers to fight crime and protect the public. Some popular superheroes include Superman, Batman, Spider-Man and Wonder Woman. Superheroes often have backstories that (97) _______ how they have gained their powers,(98) ______ being born with special powers, being bitten by a radioactive spider or being exposed to cosmic radiation. They usually have a specific outfit or clothing that they wear to hide their identity and protect (99) _________ while fighting crime. Superheroes have been a staple of popular culture (100) ______ decades, appearing in comics, movies, television shows, and video games.<br>Select the most appropriate option to fill in blank no. 96.</p>",
                    options_en: ["<p>pragmatic</p>", "<p>extraordinary</p>", 
                                "<p>familiar</p>", "<p>hereditary</p>"],
                    options_hi: ["<p>pragmatic</p>", "<p>extraordinary</p>",
                                "<p>familiar</p>", "<p>hereditary</p>"],
                    solution_en: "<p>96.(b) <strong>extraordinary</strong> <br>&lsquo;Extraordinary&rsquo; means very unusual and special. The given passage states that superheroes are fictional characters with extraordinary powers who use their powers to fight crime and protect the public. Hence, &lsquo;extraordinary&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(b) <strong>extraordinary</strong> <br>&lsquo;Extraordinary&rsquo; का अर्थ है बहुत ही unusual और special। दिए गए passage में कहा गया है कि superheroes असाधारण powers वाले fictional characters हैं जो अपनी शक्तियों (powers) का प्रयोग अपराध (crime) से लड़ने और जनता की रक्षा करने के लिए करते हैं। इसलिए, &lsquo;extraordinary&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :</strong><br>Superheroes are fictional characters with (96) _______ powers who use their powers to fight crime and protect the public. Some popular superheroes include Superman, Batman, Spider-Man and Wonder Woman. Superheroes often have backstories that (97) _______ how they have gained their powers,(98) ______ being born with special powers, being bitten by a radioactive spider or being exposed to cosmic radiation. They usually have a specific outfit or clothing that they wear to hide their identity and protect (99) _________ while fighting crime. Superheroes have been a staple of popular culture (100) ______ decades, appearing in comics, movies, television shows, and video games.<br>Select the most appropriate option to fill in blank no. 97.</p>",
                    question_hi: "<p>97.<strong>Cloze Test :</strong><br>Superheroes are fictional characters with (96) _______ powers who use their powers to fight crime and protect the public. Some popular superheroes include Superman, Batman, Spider-Man and Wonder Woman. Superheroes often have backstories that (97) _______ how they have gained their powers,(98) ______ being born with special powers, being bitten by a radioactive spider or being exposed to cosmic radiation. They usually have a specific outfit or clothing that they wear to hide their identity and protect (99) _________ while fighting crime. Superheroes have been a staple of popular culture (100) ______ decades, appearing in comics, movies, television shows, and video games.<br>Select the most appropriate option to fill in blank no.97.</p>",
                    options_en: ["<p>had explain</p>", "<p>explain</p>", 
                                "<p>explaining</p>", "<p>was explain</p>"],
                    options_hi: ["<p>had explain</p>", "<p>explain</p>",
                                "<p>explaining</p>", "<p>was explain</p>"],
                    solution_en: "<p>97.(b) <strong>explain</strong> <br>Simple present tense is used to express opinions or beliefs held by people in the present. The given passage expresses an opinion about superheroes. &lsquo;Plural Sub(backstories) + V<sub>1</sub>&rsquo; is the grammatically correct structure for this sentence. Hence, &lsquo;explain(V<sub>1</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b)<strong> explain</strong> <br>Simple present tense का प्रयोग present में लोगों द्वारा रखे गए opinions या beliefs को व्यक्त करने के लिए किया जाता है। दिया गया passage सुपरहीरो (superheroes) के बारे में एक राय (opinion) व्यक्त करता है। &lsquo;Plural Sub(backstories) + V<sub>1</sub>&rsquo; इस sentence के लिए grammatically सही structure है। इसलिए, &lsquo;explain(V<sub>1</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98.<strong> Cloze Test :</strong><br>Superheroes are fictional characters with (96) _______ powers who use their powers to fight crime and protect the public. Some popular superheroes include Superman, Batman, Spider-Man and Wonder Woman. Superheroes often have backstories that (97) _______ how they have gained their powers,(98) ______ being born with special powers, being bitten by a radioactive spider or being exposed to cosmic radiation. They usually have a specific outfit or clothing that they wear to hide their identity and protect (99) _________ while fighting crime. Superheroes have been a staple of popular culture (100) ______ decades, appearing in comics, movies, television shows, and video games.<br>Select the most appropriate option to fill in blank no. 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test :</strong><br>Superheroes are fictional characters with (96) _______ powers who use their powers to fight crime and protect the public. Some popular superheroes include Superman, Batman, Spider-Man and Wonder Woman. Superheroes often have backstories that (97) _______ how they have gained their powers,(98) ______ being born with special powers, being bitten by a radioactive spider or being exposed to cosmic radiation. They usually have a specific outfit or clothing that they wear to hide their identity and protect (99) _________ while fighting crime. Superheroes have been a staple of popular culture (100) ______ decades, appearing in comics, movies, television shows, and video games.<br>Select the most appropriate option to fill in blank no. 98.</p>",
                    options_en: ["<p>instead</p>", "<p>subsequently</p>", 
                                "<p>such as</p>", "<p>therefore</p>"],
                    options_hi: ["<p>instead</p>", "<p>subsequently</p>",
                                "<p>such as</p>", "<p>therefore</p>"],
                    solution_en: "<p>98.(c)<strong> such as</strong><br>&lsquo;Such as&rsquo; is used to introduce examples to clarify a concept. Similarly, the given passage introduces some examples to clarify the way superheroes have gained their powers. Hence, &lsquo;such as&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(c) <strong>such as</strong><br>&lsquo;Such as&rsquo; का प्रयोग किसी concept को स्पष्ट करने के लिए examples प्रस्तुत करने के लिए किया जाता है। इसी तरह, दिए गए passage में superheroes द्वारा अपनी powers प्राप्त करने के तरीके को स्पष्ट करने के लिए कुछ examples प्रस्तुत किए गए हैं। इसलिए, &lsquo;such as&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :</strong><br>Superheroes are fictional characters with (96) _______ powers who use their powers to fight crime and protect the public. Some popular superheroes include Superman, Batman, Spider-Man and Wonder Woman. Superheroes often have backstories that (97) _______ how they have gained their powers,(98) ______ being born with special powers, being bitten by a radioactive spider or being exposed to cosmic radiation. They usually have a specific outfit or clothing that they wear to hide their identity and protect (99) _________ while fighting crime. Superheroes have been a staple of popular culture (100) ______ decades, appearing in comics, movies, television shows, and video games.<br>Select the most appropriate option to fill in blank no. 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test :</strong><br>Superheroes are fictional characters with (96) _______ powers who use their powers to fight crime and protect the public. Some popular superheroes include Superman, Batman, Spider-Man and Wonder Woman. Superheroes often have backstories that (97) _______ how they have gained their powers,(98) ______ being born with special powers, being bitten by a radioactive spider or being exposed to cosmic radiation. They usually have a specific outfit or clothing that they wear to hide their identity and protect (99) _________ while fighting crime. Superheroes have been a staple of popular culture (100) ______ decades, appearing in comics, movies, television shows, and video games.<br>Select the most appropriate option to fill in blank no.99.</p>",
                    options_en: ["<p>himself</p>", "<p>herself</p>", 
                                "<p>ourselves</p>", "<p>themselves</p>"],
                    options_hi: ["<p>himself</p>", "<p>herself</p>",
                                "<p>ourselves</p>", "<p>themselves</p>"],
                    solution_en: "<p>99.(d) <strong>themselves</strong><br>&lsquo;Themselves&rsquo; is a reflexive pronoun used for a plural noun and &lsquo;they&rsquo;. Hence, &lsquo;themselves&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) <strong>themselves</strong><br>&lsquo;Themselves&rsquo; एक reflexive pronoun है जिसका प्रयोग plural noun और &lsquo;they&rsquo; के लिए किया जाता है। इसलिए, &lsquo;themselves&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :</strong><br>Superheroes are fictional characters with (96) _______ powers who use their powers to fight crime and protect the public. Some popular superheroes include Superman, Batman, Spider-Man and Wonder Woman. Superheroes often have backstories that (97) _______ how they have gained their powers,(98) ______ being born with special powers, being bitten by a radioactive spider or being exposed to cosmic radiation. They usually have a specific outfit or clothing that they wear to hide their identity and protect (99) _________ while fighting crime. Superheroes have been a staple of popular culture (100) ______ decades, appearing in comics, movies, television shows, and video games.<br>Select the most appropriate option to fill in blank no. 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test :</strong><br>Superheroes are fictional characters with (96) _______ powers who use their powers to fight crime and protect the public. Some popular superheroes include Superman, Batman, Spider-Man and Wonder Woman. Superheroes often have backstories that (97) _______ how they have gained their powers,(98) ______ being born with special powers, being bitten by a radioactive spider or being exposed to cosmic radiation. They usually have a specific outfit or clothing that they wear to hide their identity and protect (99) _________ while fighting crime. Superheroes have been a staple of popular culture (100) ______ decades, appearing in comics, movies, television shows, and video games.<br>Select the most appropriate option to fill in blank no. 100.</p>",
                    options_en: ["<p>on</p>", "<p>under</p>", 
                                "<p>for</p>", "<p>above</p>"],
                    options_hi: ["<p>on</p>", "<p>under</p>",
                                "<p>for</p>", "<p>above</p>"],
                    solution_en: "<p>100.(c) <strong>for</strong><br>&lsquo;For&rsquo; is used for an indefinite period of time. For example- for years, for decades, for days, for months, for centuries, for a long time, for a while, for some time, etc. Hence, &lsquo;for&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) <strong>for</strong><br>&lsquo;For&rsquo; का प्रयोग अनिश्चित काल (indefinite period of time) के लिए किया जाता है। Example के लिए - for years, for decades, for days, for months, for centuries, for a long time, for a while, for some time,आदि। इसलिए, &lsquo;for&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>