<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["6"] = {
                name: "Reasoning",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Break a leg</span></p>",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Break a leg</span></p>",
                    options_en: ["<p>Speak directly</p>", "<p>Work long hours</p>", 
                                "<p>Wish someone good luck</p>", "<p>Wish someone bad luck</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    options_hi: ["<p>Speak directly</p>", "<p>Work long hours</p>",
                                "<p>Wish someone good luck</p>", "<p>Wish someone bad luck</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Break a leg </strong>- wish someone good luck.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>E.g</strong>.- </span><span style=\"font-family: Cambria Math;\">I told my friend to break a leg right before he went up on stage</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Break a leg</strong>&nbsp; - wish someone good luck.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>E.g.</strong>- </span><span style=\"font-family: Cambria Math;\">I told my friend to break a leg right before he went up on stage.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Miss the boat</span></p>",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Miss the boat</span></p>",
                    options_en: ["<p>Miss the goal of life</p>", "<p>Miss the person</p>", 
                                "<p>Miss an opportunity</p>", "<p>Miss the journey</p>"],
                    options_hi: ["<p>Miss the goal of life</p>", "<p>Miss the person</p>",
                                "<p>Miss an opportunity</p>", "<p>Miss the journey</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Miss the boat</strong>&nbsp; - miss an opportunity.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>E.g. </strong>-&nbsp; Gaurav missed the boat as the time for submitting the application ended.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Miss the boat&nbsp;</strong> -&nbsp; miss an opportunity.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>E.g</strong>.- Gaurav missed the boat as the time for submitting the application ended.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Comely</span></p>",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Comely</span></p>",
                    options_en: ["<p>Grandiloquent</p>", "<p>Gorgeous</p>", 
                                "<p>Gregarious</p>", "<p>Grotesque</p>"],
                    options_hi: ["<p>Grandiloquent</p>", "<p>Gorgeous</p>",
                                "<p>Gregarious</p>", "<p>Grotesque</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><strong>Comely&nbsp; </strong>- pleasant to look at, attractive</p>\r\n<p><strong>Grotesque&nbsp; </strong>-&nbsp; strange or ugly in a way that is not natural</p>\r\n<p><strong>Grandiloquent&nbsp; </strong>- using long, complicated, or formal words in speech or writing in order to impress people</p>\r\n<p><strong>Gorgeous </strong>- extremely pleasant or attractive</p>\r\n<p><strong>Gregarious </strong>- liking to be with other people</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Comely&nbsp; </strong>- </span><span style=\"font-family: Cambria Math;\">pleasant to look at, attractive </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Grotesque&nbsp; </strong>- strange or ugly in a way that is not natural</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Grandiloquent&nbsp; </strong>- </span><span style=\"font-family: Cambria Math;\">using long, complicated, or formal words in speech or writing in order to impress people</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Gorgeous&nbsp; </strong>- extremely pleasant or attractive</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Gregarious&nbsp; -</strong> liking to be with other people</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Arraign</span></p>",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Arraign</span></p>",
                    options_en: ["<p>Arrive</p>", "<p>Prosecute</p>", 
                                "<p>Persecute</p>", "<p>Arrange</p>"],
                    options_hi: ["<p>Arrive</p>", "<p>Prosecute</p>",
                                "<p>Persecute</p>", "<p>Arrange</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><strong>Arraign&nbsp; </strong>-&nbsp; to bring a person to a court of law in order to formally accuse him/her of a crime</p>\r\n<p><strong>Prosecute </strong>-&nbsp; to officially charge somebody with a crime and try to show that he/she is guilty, in a court of law</p>\r\n<p><strong>Arrive </strong>- to reach the place to which you were traveling</p>\r\n<p><strong>Persecute </strong>- to treat somebody in a cruel and unfair way, especially because of race, religion, or political beliefs</p>\r\n<p><strong>Arrange</strong> - to put something in order or in a particular pattern</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Arraign </strong>- to bring a person to a court of law in order to formally accuse him/her of a crime</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Prosecute&nbsp; </strong>- to officially charge somebody with a crime and try to show that he/she is guilty, in a court of law</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Arrive&nbsp; </strong>- </span><span style=\"font-family: Cambria Math;\">to reach the place to which you were traveling</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Persecute&nbsp; </strong>- to treat somebody in a cruel and unfair way, especially because of race, religion, or political beliefs</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Arrange&nbsp; </strong>- </span><span style=\"font-family: Cambria Math;\">to put something in order or in a particular pattern</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.</p>\r\n<p>Many times ________ starts with the creative _________ and the enduring passion of a single individual.</p>",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> In the following passage, some words have been deleted. Read the passage carefully</span><span style=\"font-family: Cambria Math;\">and select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Many </span><span style=\"font-family: Cambria Math;\">times</span><span style=\"font-family: Cambria Math;\"> ________ starts with the creative _________ and the enduring passion of a </span><span style=\"font-family: Cambria Math;\">single individual.</span></p>",
                    options_en: ["<p>tradition, lethargy</p>", "<p>stagnation, determination</p>", 
                                "<p>renovation, dullness</p>", "<p>innovation, spark</p>"],
                    options_hi: ["<p>tradition, lethargy</p>", "<p>stagnation, determination</p>",
                                "<p>renovation, dullness</p>", "<p>innovation, spark</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;<strong>Innovation</strong>&rsquo; means a new idea, method, or device &amp; &lsquo;spark&rsquo; means an exciting quality that somebody/something has. The given sentence states that many times innovation starts with the creative spark and the enduring passion of a single individual. Hence, <strong>&lsquo;innovation, spark&rsquo;</strong> is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&lsquo;Innovation&rsquo;</strong> means a new idea, method, or device &amp; &lsquo;spark&rsquo; means an exciting quality that somebody/something has. The given sentence states that many times innovation starts with the creative spark and the enduring passion of a single individual. Hence, <strong>&lsquo;innovation, spark&rsquo;</strong> is the most appropriate answer.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in passive voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Alfred&rsquo;s doctor treated Rohan for his fever.</span></p>",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in passive voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Alfred&rsquo;s doctor treated Rohan for his fever.</span></p>",
                    options_en: ["<p>Rohan will be treated by Alfred&rsquo;s doctor for his fever.</p>", "<p>Rohan has been treated by Alfred&rsquo;s doctor for his fever.</p>", 
                                "<p>Rohan was treats by Alfred&rsquo;s doctor for his fever.</p>", "<p>Rohan was treated by Alfred&rsquo;s doctor for his fever.</p>"],
                    options_hi: ["<p>Rohan will be treated by Alfred&rsquo;s doctor for his fever.</p>", "<p>Rohan has been treated by Alfred&rsquo;s doctor for his fever.</p>",
                                "<p>Rohan was treats by Alfred&rsquo;s doctor for his fever.</p>", "<p>Rohan was treated by Alfred&rsquo;s doctor for his fever.</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) Rohan </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">will be treated</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>by Alfred&rsquo;s doctor for his fever. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) Rohan<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">has been treated</span></span><span style=\"font-weight: 400;\"> by Alfred&rsquo;s doctor for his fever. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) Rohan was </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">treats</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>by Alfred&rsquo;s doctor for his fever. (Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) Rohan was treated by Alfred&rsquo;s doctor for his fever. <strong>(Correct)</strong></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) Rohan </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">will be treated</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>by Alfred&rsquo;s doctor for his fever. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) Rohan </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">has been treated</span></span><span style=\"font-weight: 400;\"> by Alfred&rsquo;s doctor for his fever. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) Rohan was </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">treats</span></span><span style=\"font-weight: 400;\"> by Alfred&rsquo;s doctor for his fever. (Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) Rohan was treated by Alfred&rsquo;s doctor for his fever. (<strong>Correct</strong>)</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in&nbsp; the correct order to form a meaningful and coherent paragraph.</p>\r\n<p>A. In 1895, a man named Birsa was seen roaming the forests and villages of Chottanagpur in Jharkhand.</p>\r\n<p>B. Soon thousands began following Birsa, believing that he was God and had come to solve all their problems.</p>\r\n<p>C. Birsa himself declared that God had appointed him to save his people from trouble, free them from the slavery of &lsquo;dikus&rsquo;.</p>\r\n<p>D. People said he had miraculous powers &ndash; he could cure all diseases and multiply grain.</p>",
                    question_hi: "<p>7. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.</p>\r\n<p>A. In 1895, a man named Birsa was seen roaming the forests and villages of Chottanagpur in Jharkhand.</p>\r\n<p>B. Soon thousands began following Birsa, believing that he was God and had come to solve all their problems.</p>\r\n<p>C. Birsa himself declared that God had appointed him to save his people from trouble, free them from the slavery of &lsquo;dikus&rsquo;.</p>\r\n<p>D. People said he had miraculous powers &ndash; he could cure all diseases and multiply grain.</p>",
                    options_en: ["<p>CABD</p>", "<p>BADC</p>", 
                                "<p>ADCB</p>", "<p>DCBA</p>"],
                    options_hi: ["<p>CABD</p>", "<p>BADC</p>",
                                "<p>ADCB</p>", "<p>DCBA</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\">Sentence A will be the starting line as it contains the main idea of the </span><span style=\"font-family: Cambria Math;\">parajumble</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">i.e.</span><span style=\"font-family: Cambria Math;\"> a man named Birsa. However, Sentence D states that people said he had miraculous powers he could cure all diseases and multiply grain. So, D will follow A. Further, Sentence C states that Birsa himself declared that God had appointed him to save his people from trouble, free them from the slavery of </span><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">dikus</span><span style=\"font-family: Cambria Math;\">&rsquo; and sentence B states that soon thousands began following Birsa, believing that he was God and had come to solve all their problems. So, B will follow C. Going through the options, <strong>option c has the correct sequence.</strong></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">Sentence A will be the starting line as it contains the main idea of the parajumble i.e. a man named Birsa. However, Sentence D states that people said he had miraculous powers he could cure all diseases and multiply grain. So, D will follow A. Further, Sentence C states that Birsa himself declared that God had appointed him to save his people from trouble, free them from the slavery of &lsquo;dikus&rsquo; and sentence B states that soon thousands began following Birsa, believing that he was God and had come to solve all their problems. So, B will follow C. Going through the options,<strong> option c has the correct sequence.</strong></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the option that expresses the given sentence in active voice.</p>\r\n<p>The flyer for the international symposium is being sent by students to institutes all over the country.</p>",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in active voice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The flyer for the international symposium is being sent by students to institutes all </span><span style=\"font-family: Cambria Math;\">over the country.</span></p>",
                    options_en: ["<p>Students are being sent the flyer for the international symposium to institutes all over the country.</p>", "<p>The flyer for the international symposium is sent by students to institutes all over the country.</p>", 
                                "<p>Students are sending the flyer for the international symposium to institutes all over the country.</p>", "<p>The flyer is sending the students to institutes all over the country for the international <span style=\"font-family: Cambria Math;\">symposium.</span></p>"],
                    options_hi: ["<p>Students are being sent the flyer for the international symposium to institutes all over <span style=\"font-family: Cambria Math;\">the country.</span></p>", "<p>The flyer for the international symposium is sent by students to institutes all over the <span style=\"font-family: Cambria Math;\">country.</span></p>",
                                "<p>Students are sending the flyer for the international symposium to institutes all over <span style=\"font-family: Cambria Math;\">the country.</span></p>", "<p>The flyer is sending the students to institutes all over the country for the international <span style=\"font-family: Cambria Math;\">symposium.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p>(a) Students <span style=\"text-decoration: underline;\">are being sent</span> the flyer for the international symposium to institutes all over the country. (Incorrect Tense)</p>\r\n<p>(b) The flyer for the international symposium is sent by students to institutes all over the Country. (Incorrect Sentence Structure)</p>\r\n<p>(c) Students are sending the flyer for the international symposium to institutes all over the country. <strong>(Correct)</strong></p>\r\n<p>(d) The flyer is sending the students to institutes all over the country for the international Symposium. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p>Students <span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">are being sent</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"> </span>the flyer for the international symposium to institutes all over </span><span style=\"font-family: Cambria Math;\">the country. (Incorrect Tense)</span></p>\r\n<p>The flyer for the international symposium is sent by students to institutes all over the <span style=\"font-family: Cambria Math;\">Country. (Incorrect Sentence Structure)</span></p>\r\n<p>Students are sending the flyer for the international symposium to institutes all over <span style=\"font-family: Cambria Math;\">the country. <strong>(Correct)</strong></span></p>\r\n<p>The flyer is sending the students to institutes all over the country for the international <span style=\"font-family: Cambria Math;\">Symposium. (Incorrect Sentence Structure)</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. The following sentence contains a grammatical error. Select the option that correctly rectifies the error.</p>\r\n<p>In its August 1992 issue, the highly respected British Journal of Addiction describe three unusual cases of carrot dependence.</p>",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">The following sentence contains a grammatical error. Select the option that correctly </span><span style=\"font-family: Cambria Math;\">rectifies the error.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In its August 1992 issue, the highly respected British Journal of Addiction describe </span><span style=\"font-family: Cambria Math;\">three unusual cases of carrot dependence.</span></p>",
                    options_en: ["<p>In its August 1992 issue, the highly respected British Journal of Addiction was describing three unusual cases of carrot dependence<span style=\"font-family: Cambria Math;\">.</span></p>", "<p>In its August 1992 issue, the highly respected British Journal of Addiction describing three unusual cases of carrot dependence.</p>", 
                                "<p>In its August 1992 issue, the highly respected British Journal of Addiction described <span style=\"font-family: Cambria Math;\">three unusual cases of carrot dependence.</span></p>", "<p>In its August 1992 issue, the highly respected British Journal of Addiction has been described three British Journal of Addiction has been described three</p>"],
                    options_hi: ["<p>In its August 1992 issue, the highly respected British Journal of Addiction was <span style=\"font-family: Cambria Math;\">describing three unusual cases of carrot dependence.</span></p>", "<p>In its August 1992 issue, the highly respected British Journal of Addiction describing <span style=\"font-family: Cambria Math;\">three unusual cases of carrot dependence.</span></p>",
                                "<p>In its August 1992 issue, the highly respected British Journal of Addiction described <span style=\"font-family: Cambria Math;\">three unusual cases of carrot dependence.</span></p>", "<p>In its August 1992 issue, the highly respected British Journal of Addiction has been <span style=\"font-family: Cambria Math;\">described three British Journal of Addiction has been described three</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) In its August 1992 issue, the highly respected British Journal of Addiction </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was </span><span style=\"font-weight: 400;\">describing</span></span><span style=\"font-weight: 400;\"> three unusual cases of carrot dependence. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) In its August 1992 issue, the highly respected British Journal of Addiction<span style=\"text-decoration: underline;\"> </span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\">describing</span> </span><span style=\"font-weight: 400;\">three unusual cases of carrot dependence. (Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) In its August 1992 issue, the highly respected British Journal of Addiction described&nbsp; </span><span style=\"font-weight: 400;\">three unusual cases of carrot dependence. <strong>(Correct)</strong></span></p>\r\n<p><span style=\"font-weight: 400;\">(d) In its August 1992 issue, the highly respeced British Journal of Addiction </span><span style=\"font-weight: 400;\">has been </span><span style=\"font-weight: 400;\">described</span><span style=\"font-weight: 400;\"> three British Journal of Addiction<span style=\"text-decoration: underline;\"> has been described</span> three. (Incorrect Tense)</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">(a) In its August 1992 issue, the highly respected British Journal of Addiction </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">was </span><span style=\"font-weight: 400;\">describing</span></span><span style=\"font-weight: 400;\"> three unusual cases of carrot dependence. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) In its August 1992 issue, the highly respected British Journal of Addiction<span style=\"text-decoration: underline;\"> </span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\">describing</span> </span><span style=\"font-weight: 400;\">three unusual cases of carrot dependence. (Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) In its August 1992 issue, the highly respected British Journal of Addiction described </span><span style=\"font-weight: 400;\">three unusual cases of carrot dependence. <strong>(Correct)</strong></span></p>\r\n<p><span style=\"font-weight: 400;\">(d) In its August 1992 issue, the highly respeced British Journal of Addiction </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">has been&nbsp; </span><span style=\"font-weight: 400;\">described</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>three British Journal of Addiction has been described three. (Incorrect Tense)</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">On cloud nine</span></p>",
                    question_hi: "<p>10. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the given idiom.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">On cloud nine</span></p>",
                    options_en: ["<p>Good number</p>", "<p>Travel by airplane</p>", 
                                "<p>Extremely happy</p>", "<p>Heavy rain</p>"],
                    options_hi: ["<p>Good number</p>", "<p>Travel by airplane</p>",
                                "<p>Extremely happy</p>", "<p>Heavy rain</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>On cloud nine</strong>&nbsp; -&nbsp; extremely happy.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>E.g.</strong>- Neeraj is on cloud nine as he got selected in the medical college.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>On cloud nine</strong>&nbsp; -&nbsp; extremely happy.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>E.g.</strong>- Neeraj is on cloud nine as he got selected in the medical college.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Reinforce</span></p>",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Reinforce</span></p>",
                    options_en: ["<p>Validate</p>", "<p>Weaken</p>", 
                                "<p>Diverge</p>", "<p>Determine</p>"],
                    options_hi: ["<p>Validate</p>", "<p>Weaken</p>",
                                "<p>Diverge</p>", "<p>Determine</p>"],
                    solution_en: "<p>11.(b)</p>\r\n<p><strong>Reinforce </strong>- to make something stronger</p>\r\n<p><strong>Weaker&nbsp; </strong>- having little strength or energy</p>\r\n<p><strong>Validate&nbsp; </strong>- to show that something is true</p>\r\n<p><strong>Diverge&nbsp; </strong>- to separate and go in different directions</p>\r\n<p><strong>Determine</strong>- to discover the facts about something</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Reinforce</strong> - to make something stronger</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Weaker</strong> - </span><span style=\"font-family: Cambria Math;\">having little strength or energy</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Validate</strong> - </span><span style=\"font-family: Cambria Math;\">to show that something is true</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Diverge</strong> - </span><span style=\"font-family: Cambria Math;\">to separate and go in different directions</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Determine</strong> - to discover the facts about something</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">Select the INCORRECTLY spelt word.</span></p>",
                    question_hi: "<p>12. <span style=\"font-family: Cambria Math;\">Select the INCORRECTLY spelt word.</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Quear</span></p>", "<p>Antibodies</p>", 
                                "<p>Brag</p>", "<p>Rascal</p>"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">Quear</span></p>", "<p>Antibodies</p>",
                                "<p>Brag</p>", "<p>Rascal</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>\'Queer\' </strong>is the correct spelling.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Queer</strong> is the correct spelling.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the option that can be used as a one-word substitute for the given group of&nbsp; words.</p>\r\n<p>To dispute angrily</p>",
                    question_hi: "<p>13. Select the option that can be used as a one-word substitute for the given group of words.</p>\r\n<p>To dispute angrily</p>",
                    options_en: ["<p>Wrench</p>", "<p>Wreck</p>", 
                                "<p>Wrangle</p>", "<p>Wrack</p>"],
                    options_hi: ["<p>Wrench</p>", "<p>Wreck</p>",
                                "<p>Wrangle</p>", "<p>Wrack</p>"],
                    solution_en: "<p>13.(c)</p>\r\n<p><strong>Wrangle&nbsp; </strong>-&nbsp; to dispute angrily</p>\r\n<p><strong>Wrench&nbsp; </strong>- to pull or turn somebody/something strongly and suddenly</p>\r\n<p><strong>Wreck&nbsp; &nbsp; </strong>-&nbsp; a ship that has sunk or been badly damaged at sea</p>\r\n<p><strong>Wrack&nbsp; &nbsp; </strong>-&nbsp; the violent destruction of a structure, machine, or vehicle</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Wrangle </strong>- to dispute angrily</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Wrench&nbsp;</strong> - to pull or turn somebody/something strongly and suddenly</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Wreck</strong>&nbsp; - </span><span style=\"font-family: Cambria Math;\">a ship that has sunk or been badly damaged at sea</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Wrack</strong> - </span><span style=\"font-family: Cambria Math;\">the violent destruction of a structure, machine, or vehicle</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate ANTONYM of the underlined word in the following sentence.</p>\r\n<p>there are some technologies available today that make a notebook <span style=\"text-decoration: underline;\">dispensable</span>.</p>",
                    question_hi: "<p>14. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the underlined word in the following</span><span style=\"font-family: Cambria Math;\">sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">there are some technologies available today that make a notebook </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">dispensable</span></span><span style=\"font-family: Cambria Math;\">.</span></p>",
                    options_en: ["<p>acceptable</p>", "<p>urgent</p>", 
                                "<p>essential</p>", "<p>popular</p>"],
                    options_hi: ["<p>acceptable</p>", "<p>urgent</p>",
                                "<p>essential</p>", "<p>popular</p>"],
                    solution_en: "<p>14.(c)</p>\r\n<p><strong>Dispensable&nbsp; </strong>- not necessary</p>\r\n<p><strong>Essential&nbsp; &nbsp; &nbsp; </strong>-&nbsp; completely necessary</p>\r\n<p><strong>Acceptable&nbsp; </strong>-&nbsp; that can be allowed</p>\r\n<p><strong>Urgent&nbsp; &nbsp; &nbsp; &nbsp; </strong>-&nbsp; needing immediate attention</p>\r\n<p><strong>Popular&nbsp; &nbsp; &nbsp; &nbsp;</strong>-&nbsp; liked by many people or by most people in a group</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Dispensable&nbsp;</strong> -.not necessary</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Essential&nbsp; </strong>- </span><span style=\"font-family: Cambria Math;\">completely necessary</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Acceptable </strong>- </span><span style=\"font-family: Cambria Math;\">that can be allowed</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Urgent</strong> - needing immediate attention</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Popular</strong> - </span><span style=\"font-family: Cambria Math;\">liked by many people or by most people in a group</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Select the INCORRECTLY spelt word.</span></p>",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">Select the INCORRECTLY spelt word.</span></p>",
                    options_en: ["<p>Tactile</p>", "<p><span style=\"font-family: Cambria Math;\">Concentratte</span></p>", 
                                "<p>Domicile</p>", "<p>Customary</p>"],
                    options_hi: ["<p>Tactile</p>", "<p><span style=\"font-family: Cambria Math;\">Concentratte</span></p>",
                                "<p>Domicile</p>", "<p>Customary</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>\'Concentrate\'</strong> is the correct spelling.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Concentrate</strong> is the correct spelling.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. <span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">She was __________ right nor wrong.</span></p>",
                    question_hi: "<p>16. <span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">She was __________ right nor wrong.</span></p>",
                    options_en: ["<p>not</p>", "<p>neither</p>", 
                                "<p>whether</p>", "<p>either</p>"],
                    options_hi: ["<p>not</p>", "<p>neither</p>",
                                "<p>whether</p>", "<p>either</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&ldquo;Neither&hellip;&hellip;nor&rdquo;</strong> is a fixed conjunction pair. Hence,<strong> &lsquo;neither&rsquo;</strong> is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&ldquo;Neither&hellip;&hellip;nor&rdquo;</strong> is a fixed conjunction pair. Hence,<strong> &lsquo;neither&rsquo; </strong>is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">Select the option that will improve the underlined part of the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The children<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">opens</span><span style=\"font-family: Cambria Math;\"> the door</span></span><span style=\"font-family: Cambria Math;\"> silently, yesterday.</span></p>\n",
                    question_hi: "<p>17. <span style=\"font-family: Cambria Math;\">Select the option that will improve the underlined part of the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The children </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">opens</span><span style=\"font-family: Cambria Math;\"> the door</span></span><span style=\"font-family: Cambria Math;\"> silently, yesterday.</span></p>",
                    options_en: ["<p>will open the door</p>\n", "<p>open the door</p>\n", 
                                "<p>opened the door</p>\n", "<p>Will have opened the door</p>\n"],
                    options_hi: ["<p>will open the door</p>", "<p>open the door</p>",
                                "<p>opened the door</p>", "<p>Will have opened the door</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The given sentence is in the simple past tense so the verb must be used in the simple past form(V</span><span style=\"font-family: Cambria Math;\">2</span><span style=\"font-family: Cambria Math;\">). Hence, <strong>&lsquo;opened(V</strong></span><strong><span style=\"font-family: Cambria Math;\">2</span></strong><span style=\"font-family: Cambria Math;\"><strong>) the door&rsquo;</strong> is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The given sentence is in the simple past tense so the verb must be used in the simple past form(V</span><span style=\"font-family: Cambria Math;\">2</span><span style=\"font-family: Cambria Math;\">). Hence,<strong> &lsquo;opened(V</strong></span><strong><span style=\"font-family: Cambria Math;\">2</span></strong><span style=\"font-family: Cambria Math;\"><strong>) the door&rsquo;</strong> is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Admire</span></p>",
                    question_hi: "<p>18. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Admire</span></p>",
                    options_en: ["<p>Neglect</p>", "<p>Admonish</p>", 
                                "<p>Forget</p>", "<p>Appreciate</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    options_hi: ["<p>Neglect</p>", "<p>Admonish</p>",
                                "<p>Forget</p>", "<p>Appreciate</p>"],
                    solution_en: "<p>18.(d)</p>\r\n<p><strong>Admire </strong>- to respect or like somebody/something very much</p>\r\n<p><strong>Appreciate&nbsp; </strong>-&nbsp; to enjoy something or to understand the value of somebody/something</p>\r\n<p><strong>Neglect&nbsp; </strong>-&nbsp; to give too little or no attention or care to somebody/something</p>\r\n<p><strong>Admonish&nbsp; </strong>-&nbsp; to tell somebody firmly that you do not approve of something that he/she has done</p>\r\n<p><strong>Forget&nbsp; </strong>-&nbsp; &nbsp;to not be able to remember something<span style=\"font-family: Cambria Math;\"> </span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Admire</strong>&nbsp; - </span><span style=\"font-family: Cambria Math;\">to respect or like somebody/something very much</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Appreciate</strong> - </span><span style=\"font-family: Cambria Math;\">to enjoy something or to understand the value of somebody/something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Neglect&nbsp;</strong> - to give too little or no attention or care to somebody/something</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Admonish&nbsp;</strong> - </span><span style=\"font-family: Cambria Math;\">to tell somebody firmly that you do not approve of something that he/she has done</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Forget </strong>- </span><span style=\"font-family: Cambria Math;\">to not be able to remember something</span><span style=\"font-family: Cambria Math;\"> </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the option that can be used as a one-word substitute for the underlined group of words.</p>\r\n<p>Nisha participates in almost every activity and in all functions held in the school as she <span style=\"text-decoration: underline;\">has many skills.</span></p>\n",
                    question_hi: "<p>19.<span style=\"font-family: Cambria Math;\"> Select the option that can be used as a one-word substitute for the underlined group </span><span style=\"font-family: Cambria Math;\">of words.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Nisha participates in almost every activity and in all functions held in the school as </span><span style=\"font-family: Cambria Math;\">she </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">has many skills</span></span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    options_en: ["<p>Vulnerable</p>\n", "<p>Innovative</p>\n", 
                                "<p>Versatile</p>\n", "<p>Fragile</p>\n"],
                    options_hi: ["<p>Vulnerable</p>\n", "<p>Innovative</p>\n",
                                "<p>Versatile</p>\n", "<p>Fragile</p>\n"],
                    solution_en: "<p>19.(c) <strong>Versatile&nbsp; </strong>- &nbsp;able to adapt or be adapted to many different functions or activities.</p>\r\n<p><strong>Vulnerable&nbsp; </strong>-&nbsp; weak and easy to hurt physically or emotionally</p>\r\n<p><strong>Innovative&nbsp; </strong>- using new methods or ideas</p>\r\n<p><strong>Fragile&nbsp; </strong>- easily damaged or broken</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\"><strong>Versatile</strong>&nbsp; - able to adapt or be adapted to many different functions or activities.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Vulnerable&nbsp;</strong> - </span><span style=\"font-family: Cambria Math;\">weak and easy to hurt physically or emotionally</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Innovative</strong>&nbsp; - </span><span style=\"font-family: Cambria Math;\">using new methods or ideas</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Fragile</strong>&nbsp; - easily damaged or broken</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. <span style=\"font-family: Cambria Math;\">Which word in the given sentence is the ANTONYM of &ndash; exonerate?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">It was difficult to convict him of the falsity of his beliefs.</span></p>",
                    question_hi: "<p>20. <span style=\"font-family: Cambria Math;\">Which word in the given sentence is the ANTONYM of&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\">exonerate?</span></p>\r\n<p>&nbsp;</p>",
                    options_en: ["<p>falsity</p>", "<p>convict</p>", 
                                "<p>beliefs</p>", "<p>difficult</p>"],
                    options_hi: ["<p>falsity</p>", "<p>convict</p>",
                                "<p>beliefs</p>", "<p>difficult</p>"],
                    solution_en: "<p>20.(b)</p>\r\n<p><strong>Exonerate&nbsp; </strong>- to say officially that somebody was not responsible for something bad that happened</p>\r\n<p><strong>Convict </strong>- to say officially in a court of law that somebody is guilty of a crime</p>\r\n<p><strong>Falsity&nbsp; </strong>-&nbsp; the fact of being untrue, incorrect, or insincere.</p>\r\n<p><strong>Beliefs </strong>-&nbsp; a feeling that somebody/something is true, morally good or right, or that somebody/something really exists</p>\r\n<p><strong>Difficult </strong>-&nbsp; not easy to do or understand</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">20.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>Exonerate</strong> - to say officially that somebody was not responsible for something bad that happened</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>Convict</strong> - </span><span style=\"font-weight: 400;\">to say officially in a court of law that somebody is guilty of a crime</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>Falsity</strong> - </span><span style=\"font-weight: 400;\">the fact of being untrue, incorrect, or insincere.</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>Beliefs </strong>- </span><span style=\"font-weight: 400;\">a feeling that somebody/something is true, morally good or right, or that somebody/something really exists</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>Difficult-</strong> </span><span style=\"font-weight: 400;\">not easy to do or understand</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Comprehension:- </strong></p>\r\n<p>In the following passage, some words have been deleted. Read the passage carefully andselect the most appropriate option to fill in each blank.</p>\r\n<p>The Hall of Dharma was in a circular building, built of stone and mortar, with a (21)________ dome. The delicate (22)________of the dome was believed to represent the feminine while the typical temple spire represented the masculine. The hall was also (23)________ All rishis sat as (24)________ without a moderating &lsquo;head&rsquo;, debating issues openly and without fear, freedom of (25)________at its zenith.</p>\r\n<p>Select the most appropriate option to fill in blank number (21).</p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">21<strong>.</strong></span><strong>Comprehension:</strong></p>\r\n<p>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.</p>\r\n<p>The Hall of Dharma was in a circular building, built of stone and mortar, with a (21)________ dome. The delicate (22)________of the dome was believed to represent the feminine while the typical temple spire represented the masculine. The hall was also (23)________ All rishis sat as (24)________ without a moderating &lsquo;head&rsquo;, debating issues openly and without fear, freedom of (25)________at its zenith.</p>\r\n<p>&nbsp;</p>\r\n<p>Select the most appropriate option to fill in blank number (21).</p>",
                    options_en: ["<p>massive</p>", "<p>passive</p>", 
                                "<p>intrusive</p>", "<p>conclusive</p>"],
                    options_hi: ["<p>massive</p>", "<p>passive</p>",
                                "<p>intrusive</p>", "<p>conclusive</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&lsquo;Massive&rsquo;</strong> means very big. The given passage states that the Hall of Dharma was in a circular building, built of stone and mortar, with a massive dome. Hence, <strong>&lsquo;massive&rsquo;</strong> is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&lsquo;Massive</strong>&rsquo; means very big. The given passage states that the Hall of Dharma was in a circular building, built of stone and mortar, with a massive dome. Hence,<strong> &lsquo;massive&rsquo; </strong>is the most appropriate answer.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Comprehension:-</strong></p>\r\n<p>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.</p>\r\n<p>The Hall of Dharma was in a circular building, built of stone and mortar, with a (21)________ dome. The delicate (22)________of the dome was believed to represent the feminine while the typical temple spire represented the masculine. The hall was also (23)________ All rishis sat as (24)________ without a moderating &lsquo;head&rsquo;, debating issues openly and without fear, freedom of (25)________at its zenith.</p>\r\n<p>Select the most appropriate option to fill in blank number(22).</p>",
                    question_hi: "<p>22. <strong>Comprehension:</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The Hall of Dharma was in a circular building, built of stone and mortar, with a (2</span><span style=\"font-family: Cambria Math;\">1)_</span><span style=\"font-family: Cambria Math;\">_______ </span><span style=\"font-family: Cambria Math;\">dome. The delicate (2</span><span style=\"font-family: Cambria Math;\">2)_</span><span style=\"font-family: Cambria Math;\">_______of the dome was believed to represent the feminine while the </span><span style=\"font-family: Cambria Math;\">typical temple spire represented the masculine. The hall was also (2</span><span style=\"font-family: Cambria Math;\">3)_</span><span style=\"font-family: Cambria Math;\">_______ All rishis sat</span><span style=\"font-family: Cambria Math;\">as (2</span><span style=\"font-family: Cambria Math;\">4)_</span><span style=\"font-family: Cambria Math;\">_______ without a moderating &lsquo;head&rsquo;, debating issues openly and without fear, </span><span style=\"font-family: Cambria Math;\">freedom of (2</span><span style=\"font-family: Cambria Math;\">5)_</span><span style=\"font-family: Cambria Math;\">_______at its zenith.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (22).</span></p>",
                    options_en: ["<p>harshness</p>", "<p>masculinity</p>", 
                                "<p>vengeance</p>", "<p>elegance</p>"],
                    options_hi: ["<p>harshness</p>", "<p>masculinity</p>",
                                "<p>vengeance</p>", "<p>elegance</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&lsquo;Elegance&rsquo; </strong>means </span><span style=\"font-family: Cambria Math;\">the quality of being graceful and attractive in appearance or behaviour.</span><span style=\"font-family: Cambria Math;\"> The given passage states that the delicate elegance of the dome was believed to represent the feminine. Hence, <strong>&lsquo;elegance&rsquo; </strong>is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&lsquo;Elegance&rsquo;</strong> means </span><span style=\"font-family: Cambria Math;\">the quality of being graceful and attractive in appearance or behaviour.</span><span style=\"font-family: Cambria Math;\"> The given passage states that the delicate elegance of the dome was believed to represent the feminine. Hence, <strong>&lsquo;elegance&rsquo; </strong>is the most appropriate answer.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.<strong> Comprehension:-</strong></p>\r\n<p>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.</p>\r\n<p>The Hall of Dharma was in a circular building, built of stone and mortar, with a (21)________ dome. The delicate (22)________of the dome was believed to represent the feminine while the typical temple spire represented the masculine. The hall was also (23)________ All rishis sat as (24)________ without a moderating &lsquo;head&rsquo;, debating issues openly and without fear,freedom of (25)________at its zenith.</p>\r\n<p>Select the most appropriate option to fill in blank number (23).</p>",
                    question_hi: "<p>23.<strong> Comprehension:</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The Hall of Dharma was in a circular building, built of stone and mortar, with a (2</span><span style=\"font-family: Cambria Math;\">1)_</span><span style=\"font-family: Cambria Math;\">_______ </span><span style=\"font-family: Cambria Math;\">dome. The delicate (2</span><span style=\"font-family: Cambria Math;\">2)_</span><span style=\"font-family: Cambria Math;\">_______of the dome was believed to represent the feminine while the </span><span style=\"font-family: Cambria Math;\">typical temple spire represented the masculine. The hall was also (2</span><span style=\"font-family: Cambria Math;\">3)_</span><span style=\"font-family: Cambria Math;\">_______ All rishis sat </span><span style=\"font-family: Cambria Math;\">as (2</span><span style=\"font-family: Cambria Math;\">4)_</span><span style=\"font-family: Cambria Math;\">_______ without a moderating &lsquo;head&rsquo;, debating issues openly and without fear, </span><span style=\"font-family: Cambria Math;\">freedom of (2</span><span style=\"font-family: Cambria Math;\">5)_</span><span style=\"font-family: Cambria Math;\">_______at its zenith.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (23).</span></p>",
                    options_en: ["<p>perpendicular</p>", "<p>peculiar</p>", 
                                "<p>vertical</p>", "<p>circular</p>"],
                    options_hi: ["<p>perpendicular</p>", "<p>peculiar</p>",
                                "<p>vertical</p>", "<p>circular</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&lsquo;Circular&rsquo;</strong> means </span><span style=\"font-family: Cambria Math;\">round and flat. </span><span style=\"font-family: Cambria Math;\">The given passage states that the hall was also circular. Hence, <strong>&lsquo;circular&rsquo; </strong>is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&lsquo;Circular&rsquo; </strong>means </span><span style=\"font-family: Cambria Math;\">round and flat. </span><span style=\"font-family: Cambria Math;\">The given passage states that the hall was also circular. Hence,<strong> &lsquo;circular&rsquo; </strong>is the most appropriate answer.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24.<strong> Comprehension:-</strong></p>\r\n<p>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.</p>\r\n<p>The Hall of Dharma was in a circular building, built of stone and mortar, with a (21)________ dome. The delicate (22)________of the dome was believed to represent the feminine while thetypical temple spire represented the masculine. The hall was also (23)________ All rishis satas (24)________ without a moderating &lsquo;head&rsquo;, debating issues openly and without fear,freedom of (25)________at its zenith.</p>\r\n<p>Select the most appropriate option to fill in blank number (24)<span style=\"font-family: Cambria Math;\">.</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">24. <strong>Comprehension</strong></span><strong><span style=\"font-family: Cambria Math;\">:</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The Hall of Dharma was in a circular building, built of stone and mortar, with a (2</span><span style=\"font-family: Cambria Math;\">1)_</span><span style=\"font-family: Cambria Math;\">_______ </span><span style=\"font-family: Cambria Math;\">dome. The delicate (2</span><span style=\"font-family: Cambria Math;\">2)_</span><span style=\"font-family: Cambria Math;\">_______of the dome was believed to represent the feminine while the </span><span style=\"font-family: Cambria Math;\">typical temple spire represented the masculine. The hall was also (2</span><span style=\"font-family: Cambria Math;\">3)_</span><span style=\"font-family: Cambria Math;\">_______ All rishis sat </span><span style=\"font-family: Cambria Math;\">as (2</span><span style=\"font-family: Cambria Math;\">4)_</span><span style=\"font-family: Cambria Math;\">_______ without a moderating &lsquo;head&rsquo;, debating issues openly and without fear, </span><span style=\"font-family: Cambria Math;\">freedom of (2</span><span style=\"font-family: Cambria Math;\">5)_</span><span style=\"font-family: Cambria Math;\">_______at its zenith.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number (24).</span></p>",
                    options_en: ["<p>crossed</p>", "<p>unequal</p>", 
                                "<p>equals</p>", "<p>conical</p>"],
                    options_hi: ["<p>crossed</p>", "<p>unequal</p>",
                                "<p>equals</p>", "<p>conical</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&lsquo;Equals&rsquo; </strong>means to be the same as something. The given passage states that all rishis sat as equals without a moderating &lsquo;head&rsquo;. Hence, <strong>&lsquo;equals&rsquo;</strong> is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&lsquo;Equals&rsquo; </strong>means to be the same as something. The given passage states that all rishis sat as equals without a moderating &lsquo;head&rsquo;. Hence,<strong> &lsquo;equals&rsquo;</strong> is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p>25.<strong>Comprehension:-</strong></p>\r\n<p>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.</p>\r\n<p>The Hall of Dharma was in a circular building, built of stone and mortar, with a (21)________ dome. The delicate (22)________of the dome was believed to represent the feminine while the typical temple spire represented the masculine. The hall was also (23)________ All rishis satas (24)________ without a moderating &lsquo;head&rsquo;, debating issues openly and without fear,freedom of (25)________at its zenith.</p>\r\n<p>Select the most appropriate option to fill in blank number (25).</p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">25.<strong>Comprehension</strong></span><strong><span style=\"font-family: Cambria Math;\">:</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully and </span><span style=\"font-family: Cambria Math;\">select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The Hall of Dharma was in a circular building, built of stone and mortar, with a (2</span><span style=\"font-family: Cambria Math;\">1)_</span><span style=\"font-family: Cambria Math;\">_______ </span><span style=\"font-family: Cambria Math;\">dome. The delicate (2</span><span style=\"font-family: Cambria Math;\">2)_</span><span style=\"font-family: Cambria Math;\">_______of the dome was believed to represent the feminine while the </span><span style=\"font-family: Cambria Math;\">typical temple spire represented the masculine. The hall was also (2</span><span style=\"font-family: Cambria Math;\">3)_</span><span style=\"font-family: Cambria Math;\">_______ All rishis sat </span><span style=\"font-family: Cambria Math;\">as (2</span><span style=\"font-family: Cambria Math;\">4)_</span><span style=\"font-family: Cambria Math;\">_______ without a moderating &lsquo;head&rsquo;, debating issues openly and without fear, </span><span style=\"font-family: Cambria Math;\">freedom of (2</span><span style=\"font-family: Cambria Math;\">5)_</span><span style=\"font-family: Cambria Math;\">_______at its zenith.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 5.</span></p>",
                    options_en: ["<p>depression</p>", "<p>expression</p>", 
                                "<p>running</p>", "<p>inspiration</p>"],
                    options_hi: ["<p>depression</p>", "<p>expression</p>",
                                "<p>running</p>", "<p>inspiration</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">25.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&lsquo;Expression&rsquo;</strong> means </span><span style=\"font-family: Cambria Math;\">something that you say that shows your opinions or feelings. </span><span style=\"font-family: Cambria Math;\">The given passage talks about freedom of expression at its zenith. Hence, <strong>&lsquo;expression&rsquo;</strong> is the most appropriate answer.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">25.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&lsquo;Expression&rsquo;</strong> means </span><span style=\"font-family: Cambria Math;\">something that you say that shows your opinions or feelings. </span><span style=\"font-family: Cambria Math;\">The given passage talks about freedom of expression at its zenith. Hence, <strong>&lsquo;expression&rsquo;</strong> is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "6",
                    question_en: "<p>26. <span style=\"font-family: Cambria Math;\">Two different positions of the same dice are shown, the six faces of which are numbered from 1 to 6. Select the number that will be on the face opposite to the one showing &lsquo;1&rsquo;.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image1.png\" /></p>",
                    question_hi: "<p>26.&nbsp; <span style=\"font-weight: 400;\">एक ही पासे के दो अलग-अलग स्थितियाँ दिखाई गई हैं, जिसके छह फलक 1 से 6 तक संख्याकिंत हैं। उस संख्या का चयन कीजिए जो \'1\' दर्शाने वाले के विपरीत फलक पर होगी</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_26108381711665138638976.png\" /></p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>6</p>", "<p>4</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>6</p>", "<p>4</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">26.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">From both the dice given in question, we can see that the number that will be on the face opposite to the one showing <strong>&lsquo;1&rsquo; is &lsquo;6&rsquo;</strong>.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">26.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">प्रश्न में दिए गए दोनों पासों से हम देख सकते हैं कि जो संख्या <strong>\'1\'</strong> दर्शाने वाले फलक के विपरीत फलक पर होगी वह <strong>\'6\'</strong> है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "6",
                    question_en: "<p>27. <span style=\"font-family: Cambria Math;\">Which letter-cluster will replace the question mark (?) to complete the given series?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">TVEQ, PBWA</span><span style=\"font-family: Cambria Math;\">, ?</span><span style=\"font-family: Cambria Math;\">, HNGU, DTYE</span></p>",
                    question_hi: "<p>27.&nbsp; <span style=\"font-weight: 400;\">दी गई श्रृंखला को पूरा करने के लिए कौन सा अक्षर-समूह प्रश्न चिह्न (?) को प्रतिस्थापित करेगा?</span></p>\r\n<p><span style=\"font-weight: 400;\">TVEQ, PBWA, ?, HNGU, DTYE</span></p>",
                    options_en: ["<p>IEFN</p>", "<p>LHOK</p>", 
                                "<p>MWJK</p>", "<p>RUDA</p>"],
                    options_hi: ["<p>IEFN</p>", "<p>LHOK</p>",
                                "<p>MWJK</p>", "<p>RUDA</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">27.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: arial, helvetica, sans-serif;\"><span style=\"font-weight: 400;\">For first letter of each word : T - 4 = P, P - 4 = </span><strong>L</strong><span style=\"font-weight: 400;\">, L - 4 = H, H - 4 = D</span></span></p>\r\n<p><span style=\"font-family: arial, helvetica, sans-serif;\"><span style=\"font-weight: 400;\">For second letter of each word : V + 6 = B, B + 6 = </span><strong>H</strong><span style=\"font-weight: 400;\">, H + 6 = N, N + 6 = T</span></span></p>\r\n<p><span style=\"font-family: arial, helvetica, sans-serif;\"><span style=\"font-weight: 400;\">For third letter of each word : E - 8 = W, W - 8 = </span><strong>O</strong><span style=\"font-weight: 400;\">, O - 8 = G, G - 8 = Y</span></span></p>\r\n<p><span style=\"font-family: arial, helvetica, sans-serif;\"><span style=\"font-weight: 400;\">For fourth letter of each word : Q + 10 = A, A + 10 = </span><strong>K</strong><span style=\"font-weight: 400;\">, K + 10 = U, U + 10 = E</span></span></p>\r\n<p><span style=\"font-weight: 400; font-family: arial, helvetica, sans-serif;\">Hence, we get<strong>&nbsp; LHOK</strong>.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">27.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-weight: 400;\">प्रत्येक शब्द के पहले अक्षर के लिए : T - 4 = P, P - 4 = </span><strong>L</strong><span style=\"font-weight: 400;\">, L - 4 = H, H - 4 = D</span></p>\r\n<p><span style=\"font-weight: 400;\">प्रत्येक शब्द के दूसरे अक्षर के लिए : V + 6 = B, B + 6 = </span><strong>H</strong><span style=\"font-weight: 400;\">, H + 6 = N, N + 6 = T</span></p>\r\n<p><span style=\"font-weight: 400;\">प्रत्येक शब्द के तीसरे अक्षर के लिए : E - 8 = W, W - 8 = </span><strong>O</strong><span style=\"font-weight: 400;\">, O - 8 = G, G - 8 = Y</span></p>\r\n<p><span style=\"font-weight: 400;\">प्रत्येक शब्द के चौथे अक्षर के लिए : Q + 10 = A, A + 10 = </span><strong>K</strong><span style=\"font-weight: 400;\">, K + 10 = U, U + 10 = E</span></p>\r\n<p><span style=\"font-weight: 400;\">अतः, हमें <strong>LHOK</strong> प्राप्त होता है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "6",
                    question_en: "<p>28. <span style=\"font-family: Cambria Math;\">Select the option that is related to the fifth term in the same way as the second term is related to the first term and the fourth term is related to the third term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tis :</span><span style=\"font-family: Cambria Math;\"> Sit :: Tip : Pit :: Ten : ?</span></p>",
                    question_hi: "<p>28.&nbsp; <span style=\"font-weight: 400;\">उस विकल्प का चयन करें जो पांचवें पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से और चौथा पद तीसरे पद से संबंधित है।</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tis :</span><span style=\"font-family: Cambria Math;\"> Sit :: Tip : Pit :: Ten : ?</span></p>",
                    options_en: ["<p>Ken</p>", "<p>Net</p>", 
                                "<p>Set</p>", "<p>Pen</p>"],
                    options_hi: ["<p>Ken</p>", "<p>Net</p>",
                                "<p>Set</p>", "<p>Pen</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">28.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Logic :</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>All letters of a particular word are written in reverse manner and the first letter of the order so obtained is written in upper case.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tis </span><span style=\"font-family: Cambria Math;\"> Sit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tip </span><span style=\"font-family: Cambria Math;\"> Pit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, <strong>Ten &rArr;&nbsp;</strong></span><strong><span style=\"font-family: Cambria Math;\"> Net </span></strong></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">28.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>तर्क :-</strong> किसी शब्द विशेष के सभी अक्षरों को उल्टे क्रम में लिखा जाता है और इस प्रकार प्राप्त क्रम का पहला अक्षर अपर केस में लिखा जाता है।</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tis &rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Sit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tip &rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Pit</span></p>\r\n<p><span style=\"font-family: Kokila;\">इसी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">तरह</span><span style=\"font-family: Cambria Math;\">, <strong>Ten &rArr;&nbsp;</strong></span><strong><span style=\"font-family: Cambria Math;\"> Net </span></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "6",
                    question_en: "<p>29. <span style=\"font-family: Cambria Math;\">A paper is folded and cut as shown below. How will it appear when unfolded?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image2.png\" /></p>",
                    question_hi: "<p>29. <span style=\"font-weight: 400;\">एक कागज को निम्नांकित आकृतिओं के अनुसार मोड़ा और काटा जाता है । खोले जाने पर यह कैसे दिखाई देगा?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_48641455611665138886789.png\" /></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image3.png\" width=\"76\" height=\"114\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image4.png\" width=\"75\" height=\"113\" /></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image5.png\" width=\"78\" height=\"117\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image6.png\" width=\"82\" height=\"123\" /></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/images/mceu_43388398431665139026947.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/images/mceu_41297734741665139076652.png\" /></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_69355742051665139108100.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/images/mceu_83611504261665139185774.png\" /></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">29.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image4.png\" /></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">29.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_32501611871665139204830.png\" /></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "6",
                    question_en: "<p>30. <span style=\"font-family: Cambria Math;\">Select the option that is related to the fourth term in the same way as the first term is related to the second term and the fifth term is related to the sixth term</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">9 :</span><span style=\"font-family: Cambria Math;\"> 126 :: ? : 238 :: 21 : 294</span></p>",
                    question_hi: "<p>30. उस विकल्प का चयन करें जो चौथे पद से उसी प्रकार संबंधित है जैसे पहला पद दूसरे पद से संबंधित है और पांचवां पद छठे पद से संबंधित है।</p>\r\n<p><span style=\"font-family: Cambria Math;\">9 :</span><span style=\"font-family: Cambria Math;\"> 126 :: ? : 238 :: 21 : 294</span></p>",
                    options_en: ["<p>18</p>", "<p>28</p>", 
                                "<p>22</p>", "<p>17</p>"],
                    options_hi: ["<p>18</p>", "<p>28</p>",
                                "<p>22</p>", "<p>17</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">30.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Logic :</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> [n : n <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Cambria Math;\">14]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">9 :</span><span style=\"font-family: Cambria Math;\"> 126, 9 : 9 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Cambria Math;\"> 14 = 9 : 126</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">21 :</span><span style=\"font-family: Cambria Math;\"> 294, 21 : 21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Cambria Math;\"> 14 = 21 : 294</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, in </span><span style=\"font-family: Cambria Math;\">(?) :</span><span style=\"font-family: Cambria Math;\"> 238, 17 : 17<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> </span><span style=\"font-family: Cambria Math;\"> 14 = </span><strong><span style=\"font-family: Cambria Math;\">17</span><span style=\"font-family: Cambria Math;\"> : 238</span></strong></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">30.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><strong><span style=\"font-family: Kokila;\">तर्क</span><span style=\"font-family: Cambria Math;\"> :</span></strong><span style=\"font-family: Cambria Math;\"><strong>- </strong>[n : n &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 14]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">9 :</span><span style=\"font-family: Cambria Math;\"> 126 </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\">, 9 : 9 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 14 = 9 : 126</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">21 :</span><span style=\"font-family: Cambria Math;\"> 294 </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\">, 21 : 21 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 14 = 21 : 294</span></p>\r\n<p><span style=\"font-family: Kokila;\">इसी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रकार</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">(?) :</span><span style=\"font-family: Cambria Math;\"> 238 </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\">, 17 : 17 &times;</span><span style=\"font-family: Cambria Math;\">14 = </span><strong><span style=\"font-family: Cambria Math;\">17</span><span style=\"font-family: Cambria Math;\"> : 238</span></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "6",
                    question_en: "<p>31. <span style=\"font-family: Cambria Math;\">Select the option that represents the correct order of the given words as they would appear in an English dictionary.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.Absorption</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2.Absolutely</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3.Abduct</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4.Abbreviation</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5.Accurate</span></p>",
                    question_hi: "<p>31.&nbsp; <span style=\"font-weight: 400;\">उस विकल्प का चयन कीजिए जो दिए गए शब्दों के सही क्रम को दर्शाता है, जैसा कि वे एक अंग्रेजी शब्दकोश में दिखाई देंगे।</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.Absorption</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2.Absolutely</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3.Abduct</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4.Abbreviation</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5.Accurate</span></p>",
                    options_en: ["<p>3,4,2,1,5</p>", "<p>4,3,2,1,5</p>", 
                                "<p>1,2,3,4,5</p>", "<p>3,2,4,1,5</p>"],
                    options_hi: ["<p>3,4,2,1,5</p>", "<p>4,3,2,1,5</p>",
                                "<p>1,2,3,4,5</p>", "<p>3,2,4,1,5</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">31.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Correct order will </span><span style=\"font-family: Cambria Math;\">be :</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">Abbreviation &nbsp;&rarr; </span><span style=\"font-family: Cambria Math;\">Abduct &rarr; &nbsp;</span><span style=\"font-family: Cambria Math;\"> Absolutely &rarr; &nbsp;</span><span style=\"font-family: Cambria Math;\"> Absorption &rarr; &nbsp;</span><span style=\"font-family: Cambria Math;\"> Accurate</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">31.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><strong><span style=\"font-family: Kokila;\">सही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">क्रम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">होगा</span><span style=\"font-family: Cambria Math;\">:-</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">Abbreviation &rarr;</span><span style=\"font-family: Cambria Math;\"> Abduct&rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Absolutely &rarr;</span><span style=\"font-family: Cambria Math;\"> Absorption&rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Accurate</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "6",
                    question_en: "<p>32. <span style=\"font-family: Cambria Math;\">Which of the following numbers will replace the question mark (?) in the given series?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">229,233,239,241,251,?</span></p>",
                    question_hi: "<p>32. <span style=\"font-weight: 400;\">निम्नलिखित में से कौन सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">229,233,239,241,251,?</span></p>",
                    options_en: ["<p>257</p>", "<p>263</p>", 
                                "<p>261</p>", "<p>259</p>"],
                    options_hi: ["<p>257</p>", "<p>263</p>",
                                "<p>261</p>", "<p>259</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">32.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Logic :</span></strong><span style=\"font-family: Cambria Math;\"> Given number series is a series of consecutive prime numbers.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, the next consecutive prime number will be <strong>257</strong>.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">32.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>तर्क :-</strong> दी गई संख्या श्रंखला क्रमागत अभाज्य संख्याओं की श्रंखला है।</span></p>\r\n<p><span style=\"font-weight: 400;\">अत: अगली क्रमागत अभाज्य संख्या 257 होगी।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "6",
                    question_en: "<p>33. <span style=\"font-family: Cambria Math;\">Select the option that is related to the fifth term in the same way as the second term is related to the first term and the fourth term is related to the third term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SIMPLE :</span><span style=\"font-family: Cambria Math;\"> ISNKEL :: PUBLIC : UPYOCI :: MINUTE : ?</span></p>\n",
                    question_hi: "<p>33. <span style=\"font-family: Kokila;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2380;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2368;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Kokila;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SIMPLE :</span><span style=\"font-family: Cambria Math;\"> ISNKEL :: PUBLIC : UPYOCI :: MINUTE : ?</span></p>\n",
                    options_en: ["<p>NIMTUE</p>\n", "<p>NIMETU</p>\n", 
                                "<p>IMUNET</p>\n", "<p>IMMFET</p>\n"],
                    options_hi: ["<p>NIMTUE</p>\n", "<p>NIMETU</p>\n",
                                "<p>IMUNET</p>\n", "<p>IMMFET</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">33.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_65114399321665132391811.png\" width=\"251\" height=\"186\"></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_95330546031665132407901.png\" width=\"253\" height=\"194\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_77824227911671017335304.png\" width=\"238\" height=\"170\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">33.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_95222545711665139447033.png\" width=\"239\" height=\"178\"></p>\r\n<p><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_73978142921665139484572.png\" width=\"235\" height=\"171\"></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_24469702211671017297161.png\" width=\"219\" height=\"156\"></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "6",
                    question_en: "<p>34. Study the given pattern carefully and select the number that can replace the question mark (?) in it.</p>\r\n<p>First row- 6, 8, 34</p>\r\n<p>Second row- 3, 2, 10</p>\r\n<p>Third row- 11, 18, ?</p>\r\n<p>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is NOT allowed).</p>",
                    question_hi: "<p>34.&nbsp; &nbsp; <span style=\"font-weight: 400;\">दिए गए पैटर्न का ध्यानपूर्वक अध्ययन&nbsp; कीजिए और उस संख्या का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।</span></p>\r\n<p><span style=\"font-weight: 400;\">पहली पंक्ति- 6, 8, 34</span></p>\r\n<p><span style=\"font-weight: 400;\">दूसरी पंक्ति- 3, 2, 10</span></p>\r\n<p><span style=\"font-weight: 400;\">तीसरी पंक्ति- 11, 18, ?</span></p>\r\n<p><span style=\"font-weight: 400;\">(नोट: संख्या को उसके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर गणितीय संक्रियाए किया जाना चाहिए। उदाहरण के लिए, 13 - 13 पर गणितीय संक्रियाए जैसे कि जोड़ने/घटाने/गुणा करने आदि पर 13 में किया जा सकता है। 13 को 1 और 3 में तोड़कर और फिर 1 और 3 पर गणितीय संक्रियाओं को करने की अनुमति नहीं है।)</span></p>",
                    options_en: ["<p>47</p>", "<p>84</p>", 
                                "<p>74</p>", "<p>48</p>"],
                    options_hi: ["<p>47</p>", "<p>84</p>",
                                "<p>74</p>", "<p>48</p>"],
                    solution_en: "<p><span style=\"font-family: arial, helvetica, sans-serif;\">34.(c)</span></p>\r\n<p><span style=\"font-family: arial, helvetica, sans-serif;\"><strong>Logic :</strong> In each row, (First number &times; 2 + Second number &times; 3) - 2 = Third number</span></p>\r\n<p><span style=\"font-family: arial, helvetica, sans-serif;\">In the first row, (6 &times; 2) + (8 &times; 3) = 12 + 24 = 36 - 2 = 34</span></p>\r\n<p><span style=\"font-family: arial, helvetica, sans-serif;\">In the second row, (3 &times; 2) + (2 &times; 3) = 6 + 6 = 12 - 2 = 10</span></p>\r\n<p><span style=\"font-family: arial, helvetica, sans-serif;\">Similarly, in the third row, (11 &times; 2) + (18 &times; 3) = 22 + 54 = 76 - 2 = 74</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">34.(</span><span style=\"font-family: Cambria Math;\">C)</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>तर्क :- </strong>प्रत्येक पंक्ति में, (पहली संख्या &times; 2 + दूसरी संख्या &times; 3) - 2 = तीसरी संख्या</span></p>\r\n<p><span style=\"font-weight: 400;\">पहली पंक्ति में, (6 &times; 2) + (8 &times; 3) = 12 + 24 = 36 - 2 = 34</span></p>\r\n<p><span style=\"font-weight: 400;\">दूसरी पंक्ति में, (3 &times; 2) + (2 &times; 3) = 6 + 6 = 12 - 2 = 10</span></p>\r\n<p><span style=\"font-weight: 400;\">इसी तरह, तीसरी पंक्ति में, (11 &times; 2) + (18 &times; 3) = 22+ 54 = 76 - 2 = <strong>74</strong></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "6",
                    question_en: "<p>35. Select the option that represents the correct order of the given words as they would appear in an English dictionary.</p>\r\n<p>1.Draft</p>\r\n<p>2.Drake</p>\r\n<p>3.Drain</p>\r\n<p>4.Drape</p>\r\n<p>5.Dragon</p>",
                    question_hi: "<p>35.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विकल्प</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">चयन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कीजिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">गए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">शब्दों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">क्रम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दर्शाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">जैसा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">अंग्रेजी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">शब्दकोश</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दिखाई</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">देंगे</span><span style=\"font-family: Kokila;\">।</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.Draft</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2.Drake</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3.Drain</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4.Drape</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5.Dragon</span></p>",
                    options_en: ["<p>1, 5, 3, 2, 4</p>", "<p>3, 1, 2, 5, 4</p>", 
                                "<p>3, 1, 5, 2, 4</p>", "<p>1, 3, 5, 2, 4</p>"],
                    options_hi: ["<p>1, 5, 3, 2, 4</p>", "<p>3, 1, 2, 5, 4</p>",
                                "<p>3, 1, 5, 2, 4</p>", "<p>1, 3, 5, 2, 4</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">35.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Correct order will </span><span style=\"font-family: Cambria Math;\">be :</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">Draft &nbsp;&rarr;</span><span style=\"font-family: Cambria Math;\">Dragon &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Drain &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Drake &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Drape</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">35.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><strong><span style=\"font-family: Kokila;\">सही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">क्रम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">होगा</span><span style=\"font-family: Cambria Math;\">:</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">Draft&rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Dragon&rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Drain&rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Drake&rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Drape</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "6",
                    question_en: "<p>36. Select the correct combination of mathematical signs to replace the * signs and to balance the given equation.</p>\r\n<p>14*5*55*74*12*4*3</p>",
                    question_hi: "<p>36.&nbsp; <span style=\"font-weight: 400;\">दिए गए समीकरण में * चिह्नों को क्रमिक रूप में बदलने और समीकरण को संतुलित करने के लिए गणितीय चिह्नों के सही संयोजन का चयन कीजिए।</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14*5*55*74*12*4*3</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">=,+</span><span style=\"font-family: Cambria Math;\">,&minus;,&times;,&minus;,&divide;</span></p>", "<p><span style=\"font-family: Cambria Math;\">+,&minus;</span><span style=\"font-family: Cambria Math;\">,=,&minus;,&times;,+</span></p>", 
                                "<p><span style=\"font-family: Cambria Math;\">&times;,+</span><span style=\"font-family: Cambria Math;\">,&minus;,=,&times;,+</span></p>", "<p><span style=\"font-family: Cambria Math;\">&times;,&minus;</span><span style=\"font-family: Cambria Math;\">,=,+,&minus;,&times;</span></p>"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">=,+</span><span style=\"font-family: Cambria Math;\">,&minus;,&times;,&minus;,&divide;</span></p>", "<p><span style=\"font-family: Cambria Math;\">+,&minus;</span><span style=\"font-family: Cambria Math;\">,=,&minus;,&times;,+</span></p>",
                                "<p><span style=\"font-family: Cambria Math;\">&times;,+</span><span style=\"font-family: Cambria Math;\">,&minus;,=,&times;,+</span></p>", "<p><span style=\"font-family: Cambria Math;\">&times;,&minus;</span><span style=\"font-family: Cambria Math;\">,=,+,&minus;,&times;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">36.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In this type of question, we will check by putting options one by one and doing so option (c) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14*5*55*74*12*4*3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putting the values of option (c) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5 + 55 - 74 = 12&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> 4 + 3</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">LHS </span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 14&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5 + 55 - 74</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 70 + 55 - 74</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 125 - 74</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 51</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>RHS&nbsp; </strong>= 12 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Cambria Math;\"> 4 + 3 = 48 + 3 = 51</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">So, LHS = RHS</span></strong></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">36.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">इस प्रकार के प्रश्न में हम एक-एक करके विकल्पों को समीकरण में रखकर जांच करेंगे और ऐसा करने से विकल्प (c) संतुष्ट हो जाता है।</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14*5*55*74*12*4*3</span></p>\r\n<p><span style=\"font-family: Kokila;\">उपरोक्त</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">व्यंजक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विकल्प</span><span style=\"font-family: Cambria Math;\"> (c) </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">रखने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हमें</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्राप्त</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">होता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">:-</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14&nbsp; &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5 + 55 - 74 = 12 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 4 + 3</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">LHS </span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 14 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5 + 55 - 74</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 70 + 55 - 74</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 125 - 74</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 51</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>RHS </strong>= 12 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 4 + 3 = 48 + 3 = 51</span></p>\r\n<p><span style=\"font-family: Kokila;\">तो</span><span style=\"font-family: Cambria Math;\">, <strong>LHS = RHS</strong></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "6",
                    question_en: "<p><span style=\"font-family: arial, helvetica, sans-serif;\">37. In a certain code language, &ldquo;CASTLE&rdquo; is written as &ldquo;BYPUNH&rdquo;, and &ldquo;DEMAND&rdquo; is written as &ldquo;CCJBPG&rdquo;. How will &ldquo;EITHER&rdquo; be written in that language?</span></p>",
                    question_hi: "<p>37. <span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निश्चित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कूट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">भाषा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\">, &ldquo;CASTLE&rdquo; </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> &ldquo;BYPUNH&rdquo; </span><span style=\"font-family: Kokila;\">लिखा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">और</span><span style=\"font-family: Cambria Math;\"> &ldquo;DEMAND&rdquo; </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> &ldquo;CCJBPG&rdquo; </span><span style=\"font-family: Kokila;\">लिखा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Kokila;\">।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">भाषा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> &ldquo;EITHER&rdquo; </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कैसे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लिखा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाएगा</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>DGQIHU</p>", "<p>DGQIGU</p>", 
                                "<p>DGQJGU</p>", "<p>DGPIGU</p>"],
                    options_hi: ["<p>DGQIHU</p>", "<p>DGQIGU</p>",
                                "<p>DGQJGU</p>", "<p>DGPIGU</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">37.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_28825519711665132687363.png\" /></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_45511903121665132701194.png\" /></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Similarly,</span></strong></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_66244874331665132721596.png\" /></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">37.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_94113539211665139850029.png\" /></p>\r\n<p><span style=\"font-family: Kokila;\">उसी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रकार</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_28243291121665139904361.png\" /></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_27512173031665139951950.png\" /></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "6",
                    question_en: "<p>38. <span style=\"font-family: Cambria Math;\">Select the correct mirror image of the given combination when the mirror is placed at &lsquo;MN&rsquo; as shown.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image14.png\"></p>\n",
                    question_hi: "<p>38. <span style=\"font-weight: 400;\">&#2332;&#2348; &#2342;&#2352;&#2381;&#2346;&#2339; &#2325;&#2379; &#2344;&#2367;&#2350;&#2381;&#2344; &#2310;&#2325;&#2371;&#2340;&#2367; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &lsquo;MN&rsquo; &#2346;&#2352; &#2352;&#2326;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2340;&#2379; &#2342;&#2367;&#2319; &#2327;&#2319; &#2360;&#2351;&#2379;&#2306;&#2332;&#2344; &#2325;&#2366; &#2342;&#2352;&#2381;&#2346;&#2339; &#2350;&#2375;&#2306; &#2344;&#2367;&#2352;&#2381;&#2350;&#2367;&#2340; &#2360;&#2361;&#2368; &#2346;&#2381;&#2352;&#2340;&#2367;&#2348;&#2367;&#2306;&#2348; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404; </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_16338035811665140051073.png\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image15.png\" width=\"136\" height=\"20\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image16.png\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image17.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image18.png\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/images/mceu_65576420221665140212940.png\" width=\"156\" height=\"25\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/images/mceu_14333321631665140254402.png\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_70679960741665140302083.png\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/images/mceu_55818551851665140332576.png\" width=\"160\" height=\"41\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">38.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image15.png\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">38.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_72179947361665140346376.png\"></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "6",
                    question_en: "<p>39. <span style=\"font-family: Cambria Math;\">Select the correct mirror image of the given combination when the mirror is placed at &lsquo;PQ&rsquo; as shown.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image19.png\" /></p>",
                    question_hi: "<p>39. <span style=\"font-family: Kokila;\">जब</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दर्पण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निम्न</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">आकृति</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">अनुसार</span><span style=\"font-family: Cambria Math;\"> &lsquo;PQ&rsquo; </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">रखा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">तो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">गए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सयोंजन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दर्पण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निर्मित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रतिबिंब</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">चयन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कीजिए</span><span style=\"font-family: Kokila;\">।</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_83211653711665140433697.png\" /></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image20.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image21.png\" /></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image22.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image23.png\" /></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/images/mceu_81193542621665140462165.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/images/mceu_63147979031665140537813.png\" /></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_94951950541665140569581.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/images/mceu_8873582551665140626178.png\" /></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">39.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image20.png\" /></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">39.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_83950398061665140638531.png\" /></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "6",
                    question_en: "<p>40.<span style=\"font-family: Cambria Math;\"> Which of the following numbers will replace the question mark (?) in the given number series?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5, 6</span><span style=\"font-family: Cambria Math;\">, ?</span><span style=\"font-family: Cambria Math;\">, 45, 184, 925</span></p>",
                    question_hi: "<p>40.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कौन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संख्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">गई</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संख्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">श्रृंखला</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रश्नवाचक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">चिह्न</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">स्थान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लेगी</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5, 6</span><span style=\"font-family: Cambria Math;\">, ?</span><span style=\"font-family: Cambria Math;\">, 45, 184, 925</span></p>",
                    options_en: ["<p>12</p>", "<p>15</p>", 
                                "<p>14</p>", "<p>18</p>"],
                    options_hi: ["<p>12</p>", "<p>15</p>",
                                "<p>14</p>", "<p>18</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">40.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_45362642311665132845033.png\" /></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">40.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_34113252311665140710041.png\" width=\"338\" height=\"117\" /></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "6",
                    question_en: "<p>41.<span style=\"font-family: Cambria Math;\"> Select the set in which the numbers are related in the same way as are the numbers of the following set.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(360, 12, 60)</span></span></p>",
                    question_hi: "<p>41.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">समुच्चय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">चयन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कीिजए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जिसमे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संख्याएँ</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">दूसरे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उसी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">तरह</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संबंधित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जिस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">तरह</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निम्न</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">समुच्चय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संख्याएँ</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">दूसरे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संबंधित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Kokila;\">।</span></p>\r\n<p><span style=\"font-family: Kokila;\"><span style=\"font-weight: 400;\">(360, 12, 60)</span></span></p>",
                    options_en: ["<p><span style=\"font-weight: 400;\">(362, 13, 54)</span></p>", "<p><span style=\"font-weight: 400;\">(342, 19, 36)</span></p>", 
                                "<p><span style=\"font-weight: 400;\">369, 15, 78)</span></p>", "<p><span style=\"font-weight: 400;\">(398, 16, 34)</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">(362, 13, 54)</span></p>", "<p><span style=\"font-weight: 400;\">(342, 19, 36)</span></p>",
                                "<p><span style=\"font-weight: 400;\">(369, 15, 78)</span></p>", "<p><span style=\"font-weight: 400;\">(398, 16, 34)</span><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">41.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Logic :</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> [(a&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> b)&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> 2, a, b]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In (360, 12, 60), 12&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> 60 = 720 &rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 720 &divide;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 2 = 360</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, in (342, 19, 36), 19 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Cambria Math;\"> 36 = 684 &rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 684 &divide;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 2 = 342</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">41.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><strong><span style=\"font-family: Kokila;\">तर्क</span><span style=\"font-family: Cambria Math;\"> :</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> [(a &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> b) &divide;</span><span style=\"font-family: Cambria Math;\">2, a, b]</span></p>\r\n<p><span style=\"font-family: Kokila;\"><span style=\"font-weight: 400;\">(360, 12, 60) </span>में</span><span style=\"font-family: Cambria Math;\">, 12 &times;</span><span style=\"font-family: Cambria Math;\">60 = 720 &rArr; 720 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#247;</mo></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">2 = 360</span></p>\r\n<p><span style=\"font-family: Kokila;\">उसी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रकार</span><span style=\"font-family: Cambria Math;\">, (</span><span style=\"font-family: Cambria Math;\">342, 19, 36) </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\">, 19 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 36 = 684 &rArr; &nbsp; </span><span style=\"font-family: Cambria Math;\">684 &divide;</span><span style=\"font-family: Cambria Math;\">2 = 342</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "6",
                    question_en: "<p>42. <span style=\"font-family: Cambria Math;\">Select the correct mirror image of the given combination when the mirror is placed at &lsquo;MN&rsquo; as shown.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image25.png\" /></p>",
                    question_hi: "<p>42. <span style=\"font-family: Kokila;\">दिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">गए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संयोजन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सही</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दर्पण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रतिबिंब</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">चयन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करें</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जब</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दर्पण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> \'MN\' </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">दर्शाए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">गए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">अनुसार</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">रखा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Kokila;\">।</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_51752097711665140883280.png\" /></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image26.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image27.png\" /></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image28.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image29.png\" /></p>"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/images/mceu_51786868421665141004171.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/images/mceu_32037956031665141044326.png\" /></p>",
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_46306491141665141082100.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/images/mceu_1888652351665141155396.png\" /></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">42.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image29.png\" /></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">42.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_87454841461665141168612.png\" /></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "6",
                    question_en: "<p>43. <span style=\"font-family: Cambria Math;\">Two statements are given followed by two conclusions numbered I and II. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Statements:</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">Some apartments are bungalows.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Some bungalows are flats.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Conclusions:</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. Some apartments are flats.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. Some flats are bungalows.</span></p>",
                    question_hi: "<p>43. <span style=\"font-weight: 400;\">दो कथनों के बाद दो निष्कर्ष I और II दिए गए हैं। उन कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, यह तय कीजिए कि कौन सा निष्कर्ष इन कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं।</span></p>\r\n<p><strong>कथन:</strong></p>\r\n<p><span style=\"font-weight: 400;\">कुछ अपार्टमेंट बंगले हैं।</span></p>\r\n<p><span style=\"font-weight: 400;\">कुछ बंगले फ्लैट हैं।</span></p>\r\n<p><strong>निष्कर्ष:</strong></p>\r\n<p><span style=\"font-weight: 400;\">I.&nbsp; </span>कुछ अपार्टमेंट फ्लैट हैं।</p>\r\n<p><span style=\"font-weight: 400;\">II. </span>कुछ फ्लैट बंगले हैं।</p>",
                    options_en: ["<p>Only conclusion I follows</p>", "<p>Both conclusions I and II follow</p>", 
                                "<p>Neither conclusion I nor II follows</p>", "<p>Only conclusion II follows</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">केवल निष्कर्ष I अनुसरण करता है</span></p>", "<p><span style=\"font-weight: 400;\">निष्कर्ष I और II दोनों अनुसरण करते हैं</span></p>",
                                "<p><span style=\"font-weight: 400;\">न तो निष्कर्ष I और न ही II अनुसरण करता है</span></p>", "<p><span style=\"font-family: Kokila;\">केवल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निष्कर्ष</span><span style=\"font-family: Cambria Math;\"> II </span><span style=\"font-family: Kokila;\">अनुसरण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">43.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_68853451411665125387813.png\" width=\"276\" height=\"141\" /></p>\r\n<p><span style=\"font-family: Cambria Math;\">Clearly, we can see that only conclusion 2 follows.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">43.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_47146851911664943828985.png\" /></p>\r\n<p><span style=\"font-family: Kokila;\">स्पष्ट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">रूप</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">हम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">देख</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सकते</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हैं</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">केवल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निष्कर्ष</span><span style=\"font-family: Cambria Math;\"> II</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">अनुसरण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Kokila;\">।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "6",
                    question_en: "<p>44.<span style=\"font-family: Cambria Math;\"> Which of the following numbers will replace the question mark (?) in the given series?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">6, 36, 41</span><span style=\"font-family: Cambria Math;\">, ?</span><span style=\"font-family: Cambria Math;\">, 167, 334</span></p>",
                    question_hi: "<p>44.<span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">निम्नलिखित में से कौन सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी?</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">6, 36, 41</span><span style=\"font-family: Cambria Math;\">, ?</span><span style=\"font-family: Cambria Math;\">, 167, 334</span></p>",
                    options_en: ["<p>125</p>", "<p>153</p>", 
                                "<p>160</p>", "<p>164</p>"],
                    options_hi: ["<p>125</p>", "<p>153</p>",
                                "<p>160</p>", "<p>164</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">44.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_4979877011665125524426.png\" /></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">44.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_94822109611665141299307.png\" /></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "6",
                    question_en: "<p>45.<span style=\"font-family: Cambria Math;\"> Select the correct combination of mathematical signs to sequentially replace the * signs and to balance the given equation.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16*4*8*10*2*52</span></p>",
                    question_hi: "<p>45.<span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">दिए गए समीकरण में * चिह्नों को क्रमिक रूप में बदलने और समीकरण को संतुलित करने के लिए गणितीय चिह्नों के सही संयोजन का चयन कीजिए।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16*4*8*10*2*52</span></p>",
                    options_en: ["<p>&times;<span style=\"font-family: Cambria Math;\">, &divide;,&ndash;,+, =</span></p>", "<p><span style=\"font-family: Cambria Math;\">&ndash;,+</span><span style=\"font-family: Cambria Math;\">,&times;,&divide;,=</span></p>", 
                                "<p><span style=\"font-family: Cambria Math;\">&divide;,&times;</span><span style=\"font-family: Cambria Math;\">,&ndash;,+, =</span></p>", "<p><span style=\"font-family: Cambria Math;\">&divide;,&times;</span><span style=\"font-family: Cambria Math;\">,+,&ndash;, =</span></p>"],
                    options_hi: ["<p>&times;<span style=\"font-family: Cambria Math;\">, &divide;,&ndash;,+, =</span></p>", "<p><span style=\"font-family: Cambria Math;\">&ndash;,+</span><span style=\"font-family: Cambria Math;\">,&times;,&divide;,=</span></p>",
                                "<p><span style=\"font-family: Cambria Math;\">&divide;,&times;</span><span style=\"font-family: Cambria Math;\">,&ndash;,+, =</span></p>", "<p><span style=\"font-family: Cambria Math;\">&divide;,&times;</span><span style=\"font-family: Cambria Math;\">,+,&ndash;, =</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">45.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In this type of question, we will check by putting options one by one and doing so option (b) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16*4*8*10*2*52</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putting the values of option (b) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16 - 4 + 8 &times;</span><span style=\"font-family: Cambria Math;\"> 10 &nbsp;&divide; </span><span style=\"font-family: Cambria Math;\">2 = 52</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">LHS</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 16 - 4 + 8 &times;</span><span style=\"font-family: Cambria Math;\"> 10 &divide; </span><span style=\"font-family: Cambria Math;\">2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 16 - 4 + 8 &times;</span><span style=\"font-family: Cambria Math;\"> 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 16 - 4 + 40</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 56 - 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 52 =<strong> RHS</strong></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">45.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Kokila;\">इस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रकार</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रश्न</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करके</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विकल्पों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">समीकरण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">रखकर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जांच</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करेंगे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">ऐसा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विकल्प</span><span style=\"font-family: Cambria Math;\"> (b) </span><span style=\"font-family: Kokila;\">संतुष्ट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Kokila;\">।</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16*4*8*10*2*52</span></p>\r\n<p><span style=\"font-family: Kokila;\">उपरोक्त</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">समीकरण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विकल्प</span><span style=\"font-family: Cambria Math;\"> (b) </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">रखने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हमें</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्राप्त</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">होता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">:-</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16 - 4 + 8 &times;</span><span style=\"font-family: Cambria Math;\">10 &divide;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 2 = 52</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">LHS</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 16 - 4 + 8 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> <strong>1</strong>0 &divide;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 16 - 4 + 8 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 16 - 4 + 40</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 56 - 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 52 =<strong> RHS</strong></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "6",
                    question_en: "<p>46. <span style=\"font-family: Cambria Math;\">Which of the following numbers will replace the question mark (?) in the given series?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13, 17, 24, 36, 55, 85, 128, 188</span><span style=\"font-family: Cambria Math;\">, ?</span></p>",
                    question_hi: "<p>46. <span style=\"font-weight: 400;\">निम्नलिखित में से कौन सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) का स्थान लेगी?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13, 17, 24, 36, 55, 85, 128, 188</span><span style=\"font-family: Cambria Math;\">, ?</span></p>",
                    options_en: ["<p>267</p>", "<p>274</p>", 
                                "<p>276</p>", "<p>247</p>"],
                    options_hi: ["<p>267</p>", "<p>274</p>",
                                "<p>276</p>", "<p>247</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">46.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_70309921911665125815230.png\" /></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">46.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_14407412411665141382592.png\" /></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "6",
                    question_en: "<p>47. <span style=\"font-family: Cambria Math;\">Which letter cluster will replace the question mark (?) to complete the given series?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">KGCJ, JEZF</span><span style=\"font-family: Cambria Math;\">, ?</span><span style=\"font-family: Cambria Math;\"> , HATX, GYQT</span></p>",
                    question_hi: "<p>47. <span style=\"font-weight: 400;\">दी गई श्रृंखला को पूरा करने के लिए कौन सा अक्षर समूह प्रश्न चिह्न (?) को प्रतिस्थापित करेगा?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">KGCJ, JEZF</span><span style=\"font-family: Cambria Math;\">, ?</span><span style=\"font-family: Cambria Math;\"> , HATX, GYQT</span></p>",
                    options_en: ["<p>WBCI</p>", "<p>WBCJ</p>", 
                                "<p>ICWB</p>", "<p>ICWA</p>"],
                    options_hi: ["<p>WBCI</p>", "<p>WBCJ</p>",
                                "<p>ICWB</p>", "<p>ICWA</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">47.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">For first letter of each word : K - 1 = J, J - 1 = </span><strong>I</strong><span style=\"font-weight: 400;\">, I - 1 = H, H - 1 = G</span></p>\r\n<p><span style=\"font-weight: 400;\">For second letter of each word : G - 2 = E, E - 2 = </span><strong>C</strong><span style=\"font-weight: 400;\">, C - 2 = A, A - 2&nbsp; =Y</span></p>\r\n<p><span style=\"font-weight: 400;\">For third letter of each word : C - 3 = Z, Z - 3 = </span><strong>W</strong><span style=\"font-weight: 400;\">, W - 3 = T, T - 3 = Q</span></p>\r\n<p><span style=\"font-weight: 400;\">For fourth letter of each word : J - 4 = F, F - 4 =</span><strong> B</strong><span style=\"font-weight: 400;\">, B - 4 = X, X - 4 = T</span></p>\r\n<p><span style=\"font-weight: 400;\">So, we get<strong>&nbsp; ICWB.</strong></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">47.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp; </span><span style=\"font-weight: 400;\">प्रत्येक शब्द के पहले अक्षर के लिए : K - 1 = J, J - 1 = </span><strong>I</strong><span style=\"font-weight: 400;\">, I - 1 = H, H - 1 = G</span></p>\r\n<p><span style=\"font-weight: 400;\">प्रत्येक शब्द के दूसरे अक्षर के लिए : G - 2 = E, E - 2 = </span><strong>C</strong><span style=\"font-weight: 400;\">, C - 2 = A, A - 2&nbsp; =Y</span></p>\r\n<p><span style=\"font-weight: 400;\">प्रत्येक शब्द के तीसरे अक्षर के लिए : C - 3 = Z, Z - 3 = </span><strong>W</strong><span style=\"font-weight: 400;\">, W - 3 = T, T - 3 = Q</span></p>\r\n<p><span style=\"font-weight: 400;\">प्रत्येक शब्द के चौथे अक्षर के लिए : J - 4 = F, F - 4 =</span><strong> B</strong><span style=\"font-weight: 400;\">, B - 4 = X, X - 4 = T</span></p>\r\n<p><span style=\"font-weight: 400;\">तो, हमें <strong>ICWB </strong>प्राप्त होता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "6",
                    question_en: "<p>48.<span style=\"font-family: Cambria Math;\"> In a certain code language, \'DIVIDE\' is coded as &lsquo;7 17 43 17 7 9&rsquo; and &lsquo;SUBTRACT&rsquo; is coded as &rsquo;37 41 3 39 35 1 5 39&rsquo;. How will \'ADDITION\' be coded in that language?</span></p>\n",
                    question_hi: "<p>48.&nbsp; <span style=\"font-weight: 400;\">&#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2325;&#2370;&#2335; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306;, \'DIVIDE\' &#2325;&#2379; \'7 17 43 17 7 9\' &#2324;&#2352; \'SUBTRACT\' &#2325;&#2379; \'37 41 3 39 35 1 5 39\' &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2370;&#2335;&#2348;&#2342;&#2381;&#2343; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2360;&#2368; &#2349;&#2366;&#2359;&#2366; &#2350;&#2375;&#2306; \'ADDITION\' &#2325;&#2379; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2370;&#2335;&#2348;&#2342;&#2381;&#2343; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2319;&#2327;&#2366;?</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>1 6 7 17 29 17 29 27</p>\n", "<p>1 7 2 17 22 17 29 27</p>\n", 
                                "<p>1 7 7 15 39 17 29 29</p>\n", "<p>1 7 7 17 39 17 29 27</p>\n"],
                    options_hi: ["<p>1 6 7 17 29 17 29 27</p>\n", "<p>1 7 2 17 22 17 29 27</p>\n",
                                "<p>1 7 7 15 39 17 29 29</p>\n", "<p>1 7 7 17 39 17 29 27</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-weight: 400;\"><strong>&nbsp;</strong>48.(b)</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>Logic :&nbsp; </strong>Each letter is coded by a corresponding number.</span></p>\r\n<p><span style=\"font-weight: 400;\">DIVIDE </span><span style=\"font-weight: 400;\"> 7-17-43-17-7-9. So D = 7, I = 17 and so on.</span></p>\r\n<p><span style=\"font-weight: 400;\">SUBTRACT </span><span style=\"font-weight: 400;\"> 37-41-3-39-35-1-5-39. So, S = 37, U = 41 and so on.</span></p>\r\n<p><span style=\"font-weight: 400;\">From above, we get <strong>ADDITION </strong></span><strong> 1-7-7-17-39-17-29-27.</strong></p>\r\n<p><strong>additional logic :</strong> each number&nbsp; is coded as place value &times;2 - 1</p>\n",
                    solution_hi: "<p><span style=\"font-weight: 400;\">48.(d)</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>&#2340;&#2352;&#2381;&#2325; :</strong> &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2309;&#2325;&#2381;&#2359;&#2352; &#2325;&#2379; &#2319;&#2325; &#2360;&#2306;&#2327;&#2340; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2360;&#2375; &#2325;&#2379;&#2337;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-weight: 400;\">DIVIDE &rArr; 7-17-43-17-7-9&#2404; &#2340;&#2379; D = 7, I = 17 &#2324;&#2352; &#2311;&#2360;&#2368; &#2340;&#2352;&#2361;&#2404;</span></p>\r\n<p><span style=\"font-weight: 400;\">SUBTRACT &rArr; 37-41-3-39-35-1-5-39&#2404; &#2340;&#2379;, S = 37, U= 41 &#2324;&#2352; &#2311;&#2360;&#2368; &#2340;&#2352;&#2361;&#2404;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2314;&#2346;&#2352; &#2360;&#2375;, &#2361;&#2350;&#2375;&#2306; <strong>ADDITION &rArr; 1-7-7-17-39-17-29-27</strong> &#2350;&#2367;&#2354;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>&#2309;&#2340;&#2367;&#2352;&#2367;&#2325;&#2381;&#2340; &#2340;&#2352;&#2381;&#2325; ;</strong> &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2379; &#2360;&#2381;&#2341;&#2366;&#2344;&#2368;&#2351; &#2350;&#2366;&#2344; &times; 2 - 1 &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2379;&#2337;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "6",
                    question_en: "<p>49. <span style=\"font-family: Cambria Math;\">How many triangles are there in the given figure?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_77609180911671021502002.png\" width=\"178\" height=\"160\"></p>\n",
                    question_hi: "<p>49. <span style=\"font-family: Kokila;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_56059105111671021520637.png\" width=\"187\" height=\"168\"></p>\n",
                    options_en: ["<p>24</p>\n", "<p>36</p>\n", 
                                "<p>32</p>\n", "<p>30</p>\n"],
                    options_hi: ["<p>24</p>\n", "<p>36</p>\n",
                                "<p>32</p>\n", "<p>30</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">49.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">On counting the number of triangles in the figure given in question, we can see that there </span><span style=\"font-family: Cambria Math;\">are</span><span style=\"font-family: Cambria Math;\"> total <strong>32 triangles</strong>.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">49.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2350;&#2375;&#2306; &#2342;&#2368; &#2327;&#2312; &#2310;&#2325;&#2371;&#2340;&#2367; &#2350;&#2375;&#2306; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2327;&#2367;&#2344;&#2344;&#2375; &#2346;&#2352; &#2361;&#2350; &#2342;&#2375;&#2326; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2367; &#2325;&#2369;&#2354; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; <strong>32</strong> &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. The second number in the given number-pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) are followed in all the number-pairs EXCEPT one. Find that odd number-pair.</p>",
                    question_hi: "<p>50.&nbsp; <span style=\"font-weight: 400;\">दी गई संख्या युग्मों में दूसरी संख्या पहली संख्या पर कुछ गणितीय संक्रियाएँ करके प्राप्त की जाती है। एक संख्या युग्म को छोड़कर शेष सभी संख्या युग्मों में समान संक्रियाओ का अनुसरण किया जाता है। वह असंगत संख्या युग्म ज्ञात कीजिए।</span><span style=\"font-family: Kokila;\">ए</span><span style=\"font-family: Kokila;\">।</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">64 :</span><span style=\"font-family: Cambria Math;\"> 6</span></p>", "<p><span style=\"font-family: Cambria Math;\">36 :</span><span style=\"font-family: Cambria Math;\"> 5</span></p>", 
                                "<p><span style=\"font-family: Cambria Math;\">169 :</span><span style=\"font-family: Cambria Math;\"> 11</span></p>", "<p><span style=\"font-family: Cambria Math;\">196 :</span><span style=\"font-family: Cambria Math;\"> 12</span></p>"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">64 :</span><span style=\"font-family: Cambria Math;\"> 6</span></p>", "<p><span style=\"font-family: Cambria Math;\">36 :</span><span style=\"font-family: Cambria Math;\"> 5</span></p>",
                                "<p><span style=\"font-family: Cambria Math;\">169 :</span><span style=\"font-family: Cambria Math;\"> 11</span></p>", "<p><span style=\"font-family: Cambria Math;\">196 :</span><span style=\"font-family: Cambria Math;\"> 12</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">50.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :</span><span style=\"font-family: Cambria Math;\">- [ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mi>n</mi><mo>+</mo><mn>2</mn></mrow></mfenced><mn>2</mn></msup><mo>:</mo><mi>n</mi></math></span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\">]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">64 :</span><span style=\"font-family: Cambria Math;\"> 6, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>6</mn><mo>+</mo><mn>2</mn></mrow></mfenced><mn>2</mn></msup><mo>:</mo><mn>6</mn><mo>=</mo><msup><mn>8</mn><mn>2</mn></msup><mo>:</mo><mn>6</mn><mo>=</mo><mn>64</mn><mo>:</mo><mn>6</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">36 :</span><span style=\"font-family: Cambria Math;\"> 5,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>5</mn><mo>+</mo><mn>2</mn></mrow></mfenced><mn>2</mn></msup><mo>:</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo><mo>=</mo><msup><mn>7</mn><mn>2</mn></msup><mo>:</mo><mo>&#160;</mo><mn>5</mn><mo>&#160;</mo><mo>=</mo><mn>49</mn><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>5</mn></math>&ne; 36 : 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">169 :</span><span style=\"font-family: Cambria Math;\"> 11,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>11</mn><mo>+</mo><mn>2</mn></mrow></mfenced><mn>2</mn></msup><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>11</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mn>13</mn><mn>2</mn></msup><mo>&#160;</mo><mo>:</mo><mn>11</mn><mo>=</mo><mo>&#160;</mo><mn>169</mn><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>11</mn><mspace linebreak=\"newline\"/></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">196 :</span><span style=\"font-family: Cambria Math;\"> 12,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mn>12</mn><mo>+</mo><mn>2</mn></mrow></mfenced><mn>2</mn></msup><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mo>=</mo><msup><mn>14</mn><mn>2</mn></msup><mo>:</mo><mn>12</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>196</mn><mo>:</mo><mn>12</mn></math></span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Clearly, we can see that (</span><span style=\"font-family: Cambria Math;\">36 :</span><span style=\"font-family: Cambria Math;\"> 5) is an odd one.</span></strong></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">50.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Kokila;\">तर्क</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">- [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>n</mi><mo>+</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> :&nbsp; n </span><span style=\"font-family: Cambria Math;\">]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">64 :</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\"> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>6</mn><mo>+</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></math> </span><span style=\"font-family: Cambria Math;\">: 6=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>2</mn></msup></math>: 6= 64 : 6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">36 :</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\">,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>5</mn><mo>+</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">&nbsp;: 5 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>7</mn><mn>2</mn></msup></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> :&nbsp; 5 = 49 : 5 &nbsp;&ne;</span><span style=\"font-family: Cambria Math;\">36 : 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">169 :</span><span style=\"font-family: Cambria Math;\"> 11 </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\">,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>11</mn><mo>+</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> : 11 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>13</mn><mn>2</mn></msup></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> : 11 = 169 : 11</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">196 :</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\">,&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>12</mn><mo>+</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">: 12 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>14</mn><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> : 12 = 196 : 12 </span></p>\r\n<p><span style=\"font-weight: 400;\">स्पष्ट रूप से, हम देख सकते हैं कि <strong>(36 : 5) एक विषम संख्या युग्म है।</strong></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. <span style=\"font-family: Cambria Math;\">∆ABC is similar to ∆PQR. The area of ∆ABC is 64 </span><span style=\"font-family: Cambria Math;\">, and the area of ∆PQR is 121 </span><span style=\"font-family: Cambria Math;\">. If QR=15.4 cm, what is the length of BC?</span></p>",
                    question_hi: "<p>51. <span style=\"font-family: Cambria Math;\">∆ABC, ∆PQR </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">समरूप</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Kokila;\">।</span><span style=\"font-family: Cambria Math;\"> ∆ABC </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">क्षेत्रफल</span><span style=\"font-family: Cambria Math;\"> 64 </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">और</span><span style=\"font-family: Cambria Math;\"> ∆PQR </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">क्षेत्रफल</span><span style=\"font-family: Cambria Math;\"> 121 </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Kokila;\">।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">यदि</span><span style=\"font-family: Cambria Math;\"> QR=15.4 cm, </span><span style=\"font-family: Kokila;\">तो</span><span style=\"font-family: Cambria Math;\"> BC </span><span style=\"font-family: Kokila;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लंबाई</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>13.2 cm</p>", "<p>15.4 cm</p>", 
                                "<p>11.2 cm</p>", "<p>12.3 cm</p>"],
                    options_hi: ["<p>13.2 cm</p>", "<p>15.4 cm</p>",
                                "<p>11.2 cm</p>", "<p>12.3 cm</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">51.(</span><span style=\"font-family: Cambria Math;\">c)Since, ∆ABC &sim; ∆PQR</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SO,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>r</mi><mo>&#8710;</mo><mo>(</mo><mi>a</mi><mi>b</mi><mi>c</mi><mo>)</mo></mrow><mrow><mi>a</mi><mi>r</mi><mo>&#8710;</mo><mo>(</mo><mi>p</mi><mi>q</mi><mi>r</mi><mo>)</mo></mrow></mfrac><mo>=</mo><msup><mfenced><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></mfenced><mn>2</mn></msup><mo>=</mo><msup><mfenced><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></mfenced><mn>2</mn></msup><mo>=</mo><msup><mfenced><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>P</mi><mi>R</mi></mrow></mfrac></mfenced><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mfrac><mn>64</mn><mn>121</mn></mfrac><mo>=</mo><msup><mfenced><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></mfenced><mn>2</mn></msup><mo>=</mo><msup><mfenced><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>P</mi><mi>R</mi></mrow></mfrac></mfenced><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><msqrt><mfrac><mn>64</mn><mn>121</mn></mfrac></msqrt><mo>=</mo><msup><mfenced><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>15</mn><mo>.</mo><mn>4</mn></mrow></mfrac></mfenced><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace></math></span></p>\r\n<p><span style=\"font-family: \'Cambria Math\';\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>=</mo><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>15</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: \'Cambria Math\';\">&rArr;15.4 &times; 8 = 11BC</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-family: \'Cambria Math\';\">&rArr;</span>123.2 = 11BC</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-family: \'Cambria Math\';\">&rArr;</span>BC =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>123</mn><mo>.</mo><mn>2</mn></mrow><mn>11</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 11.2 cm</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">51.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Kokila;\">चूँकि</span><span style=\"font-family: Cambria Math;\">, ∆ABC &sim;&nbsp;</span><span style=\"font-family: Cambria Math;\"> ∆PQR </span><span style=\"font-family: Kokila;\">इसलिए</span><span style=\"font-family: Cambria Math;\">,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>r</mi><mo>(</mo><mo>&#8710;</mo><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></mrow><mrow><mi>a</mi><mi>r</mi><mo>(</mo><mo>&#8710;</mo><mi>P</mi><mi>Q</mi><mi>R</mi><mo>)</mo></mrow></mfrac><mo>=</mo><msup><mfenced><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></mfenced><mn>2</mn></msup><mo>=</mo><msup><mfenced><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></mfenced><mn>2</mn></msup><mo>=</mo><msup><mfenced><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>P</mi><mi>R</mi></mrow></mfrac></mfenced><mn>2</mn></msup></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p>&rArr;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>121</mn></mfrac><mo>=</mo><msup><mfenced><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></mfenced><mn>2</mn></msup></math></p>\r\n<p>&rArr;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>64</mn><mn>121</mn></mfrac></msqrt></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>15</mn><mo>.</mo><mn>4</mn></mrow></mfrac></mfenced><mn>2</mn></msup></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>=</mo><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>15</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr;15.4 &times; 8 = 11BC</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr;123.2 = 11BC</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> BC =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>123</mn><mo>.</mo><mn>2</mn></mrow><mn>11</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 11.2 cm</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. <span style=\"font-family: Cambria Math;\">The following table represents the weightage of different decision features of an automobile. With the help of this information, calculate the weighted average.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Features Weightage</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Safety &ndash; 8/10 40%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Comfort &ndash; 6/10 20%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Fuel Mileage &ndash; 5/10 30%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Exterior looks &ndash; 8/10 10%</span></p>",
                    question_hi: "<p>52.&nbsp; <span style=\"font-weight: 400;\">निम्नलिखित तालिका एक ऑटोमोबाइल की विभिन्न निर्णय सुविधाओं के वेटेज (weightage) निरूपित करती है। इस सूचना की सहायता से भारित औसत की गणना कीजिए।</span></p>\r\n<p><span style=\"font-family: Kokila;\">सुविधाए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वेटेज</span></p>\r\n<p><span style=\"font-family: Kokila;\">सुरक्षा</span><span style=\"font-family: Cambria Math;\"> - 8/10 40%</span></p>\r\n<p><span style=\"font-family: Kokila;\">आराम</span><span style=\"font-family: Cambria Math;\"> - 6/10 20%</span></p>\r\n<p><span style=\"font-family: Kokila;\">ईंधन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लाभ</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Cambria Math;\"> 5/10 30%</span></p>\r\n<p><span style=\"font-family: Kokila;\">बाहरी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">रूप</span><span style=\"font-family: Cambria Math;\"> - 8/10 10%</span></p>",
                    options_en: ["<p>0.57</p>", "<p>0.76</p>", 
                                "<p>0.67</p>", "<p>0.5</p>"],
                    options_hi: ["<p>0.57</p>", "<p>0.76</p>",
                                "<p>0.67</p>", "<p>0.5</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">52.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-family: arial, helvetica, sans-serif;\">Let the total weightage of diff decision feature of an automobile </span>= 10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Weighted average</strong> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>3</mn><mo>+</mo><mn>8</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>1</mn></mrow><mn>10</mn></mfrac><mo>=</mo><mfrac><mrow><mn>3</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>8</mn></mrow><mn>10</mn></mfrac><mo>=</mo><mfrac><mrow><mn>6</mn><mo>.</mo><mn>7</mn></mrow><mn>10</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>67</mn></math></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">52.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">मान लीजिए, एक ऑटोमोबाइल की विभिन्न निर्णय सुविधाओं के वेटेज (weightage) = 10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>वेटेज औसत</strong>=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>3</mn><mo>+</mo><mn>8</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>1</mn></mrow><mn>10</mn></mfrac><mo>=</mo><mfrac><mrow><mn>3</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>8</mn></mrow><mn>10</mn></mfrac><mo>=</mo><mfrac><mrow><mn>6</mn><mo>.</mo><mn>7</mn></mrow><mn>10</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>67</mn></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. <span style=\"font-family: Cambria Math;\">The ratio of time taken by Anamika and Bani to complete a work is </span><span style=\"font-family: Cambria Math;\">1 :</span><span style=\"font-family: Cambria Math;\"> 3, respectively. Therefore, Anamika is able to finish a job in 40 days less than Bani. If they work together, they can complete the work in ______ days.</span></p>",
                    question_hi: "<p>53.&nbsp; <span style=\"font-weight: 400;\">अनामिका और बानी द्वारा एक कार्य को पूरा करने में लिए गए समय का अनुपात क्रमशः 1:3 है। अत: अनामिका उस काम को बानी से 40 दिन कम समय में पूरा कर पाती है। यदि वे एक साथ काम करती हैं, तो कितने&nbsp; दिनों में काम पूरा होगा।</span><span style=\"font-family: Kokila;\">।</span></p>",
                    options_en: ["<p>20</p>", "<p>10</p>", 
                                "<p>15</p>", "<p>25</p>"],
                    options_hi: ["<p>20</p>", "<p>10</p>",
                                "<p>15</p>", "<p>25</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">53.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<strong>A&nbsp; &nbsp; &nbsp;</strong></span><span style=\"font-family: Cambria Math;\">:&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\"> <strong>B</strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Time&nbsp; &nbsp; </strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;1&nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; 3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Efficiency&nbsp; &nbsp; &nbsp; </strong>3&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp;1</span></p>\r\n<p>(3-1) = 2 unit correspond to 40 days</p>\r\n<p><span style=\"font-family: Cambria Math;\">1 unit correspond to 20 days </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, time taken by Anamika to complete a work = 20 days</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Time taken</span><span style=\"font-family: Cambria Math;\"> by Bani to complete a work = 20 &times; 3 = 60 days</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total work = 60 &times; 1 = 60 unit</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Time taken by </span><span style=\"font-family: Cambria Math;\">them to</span></strong><span style=\"font-family: Cambria Math;\"><strong> complete whole work </strong>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mrow><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac><mo>=</mo><mfrac><mn>60</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">15days</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">53.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<strong> &nbsp; A&nbsp; &nbsp;</strong></span><strong><span style=\"font-family: Cambria Math;\">&nbsp;:&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> B</span></strong></p>\r\n<p><strong><span style=\"font-family: Kokila;\">समय</span></strong><span style=\"font-family: Cambria Math;\"><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</strong>1&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;3</span></p>\r\n<p><strong><span style=\"font-family: Kokila;\">दक्षता</span></strong><span style=\"font-family: Cambria Math;\"><strong>&nbsp; &nbsp; &nbsp; &nbsp; </strong>3&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">:&nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\"> 1</span></p>\r\n<p>(3-1) = <span style=\"font-weight: 400;\">&nbsp;2 इकाई 40 दिनों के अनुरूप है।</span></p>\r\n<p><span style=\"font-weight: 400;\">1 इकाई 20 दिनों के अनुरूप है।</span></p>\r\n<p><span style=\"font-weight: 400;\">अत: अनामिका द्वारा एक कार्य को पूरा करने में लिया गया समय</span><span style=\"font-family: \'Cambria Math\';\">= </span><span style=\"font-weight: 400;\">20 दिन</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">बानी द्वारा एक कार्य को पूरा करने में लिया गया समय</span> = 20 &times; 3 = <span style=\"font-weight: 400;\">60 दिन</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">कुल कार्य </span>&nbsp;= 60 &times; 1 =<span style=\"font-weight: 400;\">&nbsp;60 इकाई</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>उनके द्वारा पूरा कार्य पूरा करने में लिया गया समय </strong>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mrow><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac><mo>=</mo><mfrac><mn>60</mn><mn>4</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&nbsp;15 </span><span style=\"font-family: Kokila;\">दिन</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. <span style=\"font-family: Cambria Math;\">Simplify : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>a</mi><mi>b</mi><mo>&#160;</mo><mo>&#215;</mo><msup><mfenced><mrow><mi>a</mi><mo>&#215;</mo><mi>b</mi></mrow></mfenced><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>&#160;</mo><mo>&#215;</mo><mfenced><mrow><msup><mi>a</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>&#215;</mo><msup><mi>b</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup></mrow></mfenced></math></span><span style=\"font-family: Cambria Math;\"> </span></p>",
                    question_hi: "<p>54. <span style=\"font-weight: 400;\">सरलीकरण करें:&nbsp;&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>a</mi><mi>b</mi><mo>&#215;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mrow><mo>-</mo><mn>1</mn><mo>&#160;</mo></mrow></msup><mo>&#215;</mo><mo>(</mo><msup><mi>a</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>+</mo><msup><mi>b</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>)</mo></math></p>",
                    options_en: ["<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfrac></math></span></p>", "<p>1</p>", 
                                "<p>(a+b)</p>", "<p>3</p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow></mfrac></math></p>", "<p>1</p>",
                                "<p>(a+b)</p>", "<p>3</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">54.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>3</mn><mi>a</mi><mi>b</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>&#160;</mo><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>&#215;</mo><mo>(</mo><msup><mi>a</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>+</mo><mo>&#160;</mo><msup><mi>b</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>)</mo><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mn>3</mn><mi>a</mi><mi>b</mi><mo>&#160;</mo><mo>&#215;</mo><mfrac><mn>1</mn><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfrac><mo>&#215;</mo><mfenced><mrow><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>+</mo><mfrac><mn>1</mn><mi>b</mi></mfrac></mrow></mfenced><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mn>3</mn><mi>a</mi><mi>b</mi><mo>&#215;</mo><mfrac><mn>1</mn><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfrac><mo>&#215;</mo><mfenced><mfrac><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></mfenced><mo>=</mo><mn>3</mn></math></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">54.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>a</mi><mi>b</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi><mo>)</mo></mrow><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>(</mo><msup><mi>a</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>+</mo><msup><mi>b</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>)</mo></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>a</mi><mi>b</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mi>a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi></mrow></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfenced><mrow><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>+</mo><mfrac><mn>1</mn><mi>b</mi></mfrac></mrow></mfenced></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 3ab &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mi>a</mi><mo>+</mo><mi>b</mi></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac><mo>=</mo><mn>3</mn></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. <span style=\"font-family: Cambria Math;\">If<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mn>5</mn></math> </span><span style=\"font-family: Cambria Math;\">, then the value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>2</mn><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">will be ________.</span></p>",
                    question_hi: "<p>55. <span style=\"font-family: Kokila;\">यदि<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mn>5</mn></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है&nbsp; </span><span style=\"font-family: Cambria Math;\">,&nbsp; </span><span style=\"font-family: Kokila;\">तो<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>2</mn><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">होगा ?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">55.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac></mrow></mfenced></math>= 5</p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Multiplying both side by 2</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mi>x</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Now,</strong> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>2</mn><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mi>x</mi><mfenced><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mi>x</mi></mfrac></mstyle><mo>-</mo><mn>5</mn></mrow></mfenced></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mrow><mn>10</mn><mo>-</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">55.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>)</mo></math> = 5</p>\r\n<p><span style=\"font-family: Kokila;\">दोनों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पक्षों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">गुणा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 10</span></p>\r\n<p><span style=\"font-family: Kokila;\">अब</span><span style=\"font-family: Cambria Math;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>2</mn><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mi>x</mi><mo>(</mo><mn>2</mn><mi>x</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mi>x</mi></mfrac></mstyle><mo>-</mo><mn>5</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mrow><mn>10</mn><mo>-</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. <span style=\"font-family: Cambria Math;\">If a sum on compound interest (compounded yearly) becomes three times in 4 years, then with the same interest rate, the sum will become 81 times in:</span></p>",
                    question_hi: "<p>56.&nbsp; &nbsp;<span style=\"font-weight: 400;\">यदि कोई राशि चक्रवृद्धि ब्याज पर (वार्षिक रूप से चक्रवृद्धि होने वाले ) 4 वर्षों में तीन गुनी हो जाती है, तो तो समान ब्याज दर पर वह राशि कितने समय में 81 गुना हो जाएगी:</span></p>",
                    options_en: ["<p>12 years</p>", "<p>18 years</p>", 
                                "<p>15 years</p>", "<p>16 years</p>"],
                    options_hi: ["<p>12 <span style=\"font-family: Kokila;\">वर्ष</span><span style=\"font-family: Cambria Math;\"> </span></p>", "<p>18 <span style=\"font-family: Kokila;\">वर्ष</span><span style=\"font-family: Cambria Math;\"> </span></p>",
                                "<p>15 <span style=\"font-family: Kokila;\">वर्ष</span><span style=\"font-family: Cambria Math;\"> </span></p>", "<p>16 <span style=\"font-family: Kokila;\">वर्ष</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">56.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">As </span><span style=\"font-family: Cambria Math;\">for a fixed time period, the sum gets multiplied by the same factor.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Since sum becomes <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>4</mn></msup></math>times in 4years. So, the sum will </span><span style=\"font-family: Cambria Math;\">becomes</span><span style=\"font-family: Cambria Math;\"> 81</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">i.e&nbsp;</span></strong><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>4</mn></msup></math></span><strong><span style=\"font-family: Cambria Math;\">times in 4 + 4 + 4 + 4 = 16 yrs </span></strong></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">56.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निश्चित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">समय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">अवधि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लिए</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">योग</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">उसी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कारक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">गुणा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Kokila;\">।</span></p>\r\n<p><span style=\"font-family: Kokila;\">चूँकि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">योग</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Kokila;\">वर्ष</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>4</mn></msup></math> </span><span style=\"font-family: Kokila;\">गुना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Kokila;\">।</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">तो</span><span style=\"font-family: Cambria Math;\">, 4 + 4 + 4 + 4 = 16 </span><span style=\"font-family: Kokila;\">साल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">योग</span><span style=\"font-family: Cambria Math;\"> 81 </span><span style=\"font-family: Kokila;\">गुना</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हो</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाएगी</span><span style=\"font-family: Kokila;\">।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. <span style=\"font-family: Cambria Math;\">Simplify&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>+</mo><mn>1</mn><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>1</mn><mfrac><mn>6</mn><mn>7</mn></mfrac><mo>&#247;</mo><mn>5</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p>57. <span style=\"font-family: Kokila;\">सरलीकरण</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करें</span><span style=\"font-family: Cambria Math;\">:&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>+</mo><mn>1</mn><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>1</mn><mfrac><mn>6</mn><mn>7</mn></mfrac><mo>&#247;</mo><mn>5</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>3</mn><mn>11</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>5</mn><mn>11</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>4</mn><mn>11</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>3</mn><mn>11</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>5</mn><mn>11</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>4</mn><mn>11</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">57.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>2</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>+</mo><mn>1</mn><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>1</mn><mfrac><mn>6</mn><mn>7</mn></mfrac><mo>&#247;</mo><mn>5</mn><mfrac><mn>1</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>7</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>13</mn><mn>17</mn></mfrac><mo>&#247;</mo><mfrac><mn>11</mn><mn>2</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>7</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>26</mn><mn>77</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&#8658;</mo><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>26</mn><mn>23</mn></mfrac><mo>=</mo><mfrac><mrow><mn>88</mn><mo>+</mo><mn>26</mn></mrow><mn>33</mn></mfrac><mo>=</mo><mfrac><mn>114</mn><mn>33</mn></mfrac><mo>=</mo><mn>3</mn><mfrac><mn>5</mn><mn>11</mn></mfrac><mspace linebreak=\"newline\"></mspace></math></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">57.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>+</mo><mn>1</mn><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#215;</mo><mn>1</mn><mfrac><mn>6</mn><mn>7</mn></mfrac><mo>&#247;</mo><mn>5</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>7</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>13</mn><mn>7</mn></mfrac><mo>&#247;</mo><mfrac><mn>11</mn><mn>2</mn></mfrac></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>7</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>26</mn><mn>77</mn></mfrac></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>26</mn><mn>33</mn></mfrac><mo>=</mo><mfrac><mrow><mn>88</mn><mo>+</mo><mn>26</mn></mrow><mn>33</mn></mfrac><mo>=</mo><mfrac><mn>114</mn><mn>33</mn></mfrac><mo>=</mo><mfrac><mn>38</mn><mn>11</mn></mfrac><mo>=</mo><mn>3</mn><mfrac><mn>5</mn><mn>11</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. <span style=\"font-family: Cambria Math;\">There is a 20% discount on a dozen pairs of shoes marked at ₹7,200. How many pair of shoes can be bought with ₹1,440?</span></p>",
                    question_hi: "<p>58.&nbsp; <span style=\"font-weight: 400;\">एक दर्जन जोड़ी जूतों का अंकित मूल्य ₹7,200 है। अंकित मूल्य पर 20% की छूट है। ₹1,440 में कितने जोड़ी जूते खरीदे जा सकते हैं?</span></p>",
                    options_en: ["<p>3</p>", "<p>5</p>", 
                                "<p>2</p>", "<p>4</p>"],
                    options_hi: ["<p>3</p>", "<p>5</p>",
                                "<p>2</p>", "<p>4</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">58.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MP of 1 pair of shoes = 7200 &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= ₹600</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of 1 pair of shoes </span><span style=\"font-family: Cambria Math;\">= 600</span><span style=\"font-family: Cambria Math;\"> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = ₹480</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, <strong>the no of pairs of shoes can be bought in ₹1440</strong> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1440</mn><mn>480</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 3</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">58.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Kokila;\">जोड़ी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जूते</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">अंकित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">मूल्य</span><span style=\"font-family: Cambria Math;\"> = 7200 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>12</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = ₹600</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Kokila;\">जोड़ी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जूते</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विक्रय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">मूल्य</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 600</span><span style=\"font-family: Cambria Math;\"> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = ₹480</span></p>\r\n<p><span style=\"font-family: Kokila;\">तो</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">जूतों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जोड़े</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संख्या</span><span style=\"font-family: Cambria Math;\"> ₹1440 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1440</mn><mn>480</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 3 </span><span style=\"font-family: Kokila;\">जोड़े</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">खरीदी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सकती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Kokila;\">।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. <span style=\"font-family: Cambria Math;\">In a right circular cylinder, the ratio of the curved surface to the total surface area is 3:7. Find the ratio of the height of the cylinder to the radius of its base.</span></p>",
                    question_hi: "<p>59&nbsp; <span style=\"font-weight: 400;\">एक लम्ब वृत्तीय बेलन में, वक्र पृष्ठ का संपूर्ण पृष्ठीय क्षेत्रफल से अनुपात 3:7 है। बेलन की ऊँचाई का उसके आधार की त्रिज्या से अनुपात ज्ञात कीजिए।</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">3 :</span><span style=\"font-family: Cambria Math;\"> 4</span></p>", "<p><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 3</span></p>", 
                                "<p><span style=\"font-family: Cambria Math;\">3 :</span><span style=\"font-family: Cambria Math;\"> 2</span></p>", "<p><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 3</span></p>"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">3 :</span><span style=\"font-family: Cambria Math;\"> 4</span></p>", "<p><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 3</span></p>",
                                "<p><span style=\"font-family: Cambria Math;\">3 :</span><span style=\"font-family: Cambria Math;\"> 2</span></p>", "<p><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">59.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><mi>c</mi><mi>s</mi><mi>a</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>c</mi><mi>y</mi><mi>l</mi><mi>i</mi><mi>n</mi><mi>d</mi><mi>e</mi><mi>r</mi></mrow><mrow><mi>t</mi><mi>s</mi><mi>a</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>c</mi><mi>y</mi><mi>l</mi><mi>i</mi><mi>n</mi><mi>d</mi><mi>e</mi><mi>r</mi></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mrow><mn>2</mn><mi>&#960;rh</mi></mrow><mrow><mn>2</mn><mi>&#960;r</mi><mfenced><mrow><mi mathvariant=\"normal\">r</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi></mrow></mfenced></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mi>h</mi><mrow><mi>r</mi><mo>+</mo><mi>h</mi></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mn>7</mn><mi>h</mi><mo>=</mo><mn>3</mn><mi>r</mi><mo>&#160;</mo><mo>+</mo><mn>3</mn><mi>h</mi><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mn>4</mn><mi>h</mi><mo>=</mo><mn>3</mn><mi>r</mi><mspace linebreak=\"newline\"/><mo>&#8658;</mo><mfrac><mi>h</mi><mi>r</mi></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">So, the ratio of height and radius of the cylinder = </span><span style=\"font-family: Cambria Math;\">3 :</span><span style=\"font-family: Cambria Math;\"> 4</span></strong></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">59.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mi>S</mi><mi>A</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>c</mi><mi>y</mi><mi>l</mi><mi>i</mi><mi>n</mi><mi>d</mi><mi>e</mi><mi>r</mi></mrow><mrow><mi>T</mi><mi>S</mi><mi>A</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>c</mi><mi>y</mi><mi>l</mi><mi>i</mi><mi>n</mi><mi>d</mi><mi>e</mi><mi>r</mi></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mspace linebreak=\"newline\"/><mfrac><mrow><mn>2</mn><mi>r</mi><mi>h</mi></mrow><mrow><mn>2</mn><mi>r</mi><mo>(</mo><mi>r</mi><mo>+</mo><mi>h</mi><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>7</mn></mfrac></math></span></span></p>\r\n<p><span style=\"font-family: \'Cambria Math\';\">7h = 3r + 3h</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 4h = 3r</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>h</mi><mi>r</mi></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>\r\n<p><span style=\"font-weight: 400;\">अत: बेलन की ऊँचाई और त्रिज्या का अनुपात = 3 : 4</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. <span style=\"font-family: Cambria Math;\">The salaries of P and Q together amount to ₹1,20,000. P spends 95% of his salary and Q 85% of his. If their savings are the same, then what is P&rsquo;s salary?</span></p>",
                    question_hi: "<p>60.&nbsp; <span style=\"font-weight: 400;\">P और Q का मिलाकर वेतन ₹1,20,000 है। P अपने वेतन का 95% और Q अपने वेतन का 85% खर्च करता है। यदि उनकी बचत समान है, तो P का वेतन क्या है?</span></p>",
                    options_en: ["<p>₹80,000</p>", "<p>₹72,000</p>", 
                                "<p>₹90,000</p>", "<p>₹60,000</p>"],
                    options_hi: ["<p>₹80,000</p>", "<p>₹72,000</p>",
                                "<p>₹90,000</p>", "<p>₹60,000</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">60.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">ATQ,</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">P &times; 5% = Q &times; 15%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>Q</mi></mfrac><mo>=</mo><mfrac><mn>15</mn><mn>3</mn></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>1</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Salaries of P and Q = 4 unit which correspond to ₹120,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 unit corresponds to <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120000</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 30,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>So, the salary of P</strong> = 3 unit = 30,000 &times; 3 = ₹90,000</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">60.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><strong>प्रश्न के अनुसार,</strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">P &times; 5% = Q &times; 15%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>q</mi></mfrac><mo>=</mo><mfrac><mn>15</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>1</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">P और Q का वेतन</span> = <span style=\"font-weight: 400;\">4 इकाई, जो ₹120,000 के अनुरूप है।</span></span></p>\r\n<p><span style=\"font-family: Kokila;\"><span style=\"font-weight: 400;\">1 इकाई</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12000</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 30,000 <span style=\"font-weight: 400;\">&nbsp;के समान है।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">तो, P का वेतन </span>= 3<span style=\"font-weight: 400;\">&nbsp;इकाई</span></span><span style=\"font-family: Cambria Math;\">&nbsp;= 30,000 &times; 3 = ₹90,000</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. <span style=\"font-family: Cambria Math;\">The monthly income of Manisha was ₹1,20,000 and her monthly expenditure was ₹55,000. Next year, her income increased by 22% and her expenditure increase by 10%. Find the percentage increase in her savings (correct to 2 decimal places).</span></p>",
                    question_hi: "<p>61. <span style=\"font-weight: 400;\">मनीषा की मासिक आय ₹1,20,000 थी और उसका मासिक खर्च ₹55,000 था। अगले वर्ष, उसकी आय में 22% की वृद्धि हुई और उसके व्यय में 10% की वृद्धि हुई। उसकी बचत में प्रतिशत वृद्धि ज्ञात कीजिए (2 दशमलव स्थानों तक पूर्णाकिंत )।</span></p>",
                    options_en: ["<p>28.16%</p>", "<p>26.25%</p>", 
                                "<p>32.15%</p>", "<p>30.08%</p>"],
                    options_hi: ["<p>28.16%</p>", "<p>26.25%</p>",
                                "<p>32.15%</p>", "<p>30.08%</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">61.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Income&nbsp; &nbsp; =&nbsp; Expenditure + Saving</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Original&nbsp; </strong>&nbsp;1</span><span style=\"font-family: Cambria Math;\">,20,000&nbsp; =&nbsp; 55,000 + 65,000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>New&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</strong>1,46,</span><span style=\"font-family: Cambria Math;\">400&nbsp; &nbsp;=&nbsp;</span><span style=\"font-family: Cambria Math;\"> 60,500 + 85,900</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Percentage increase in her savings</strong> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>85</mn><mo>,</mo><mn>900</mn><mo>-</mo><mn>65000</mn></mrow><mn>65000</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> &times; 100 =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20900</mn><mn>65000</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>418</mn><mn>13</mn></mfrac></math>&times; 100&nbsp; </span><span style=\"font-family: Cambria Math;\">= 32.15%</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">61.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">आय&nbsp; &nbsp; &nbsp; = &nbsp; &nbsp; &nbsp; व्यय &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; +&nbsp; &nbsp; बचत</span></p>\r\n<p><span style=\"font-weight: 400;\">मूल&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1,20,000&nbsp; &nbsp; = &nbsp; &nbsp; &nbsp; 55,000 &nbsp; &nbsp; &nbsp; +&nbsp; &nbsp; 65,000</span></p>\r\n<p><span style=\"font-weight: 400;\">नया&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1,46,400&nbsp; &nbsp; =&nbsp; &nbsp; &nbsp; 60,500 &nbsp; &nbsp; &nbsp; +&nbsp; &nbsp; 85,900</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">उसकी बचत में प्रतिशत वृद्धि </span>&nbsp;=</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>85900</mn><mo>-</mo><mn>65000</mn></mrow><mn>65000</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mn>20900</mn><mn>65000</mn></mfrac><mo>&#215;</mo><mn>100</mn><mo>=</mo><mfrac><mn>418</mn><mn>13</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = </span><span style=\"font-family: Cambria Math;\">&nbsp;32.15%</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. <span style=\"font-family: Cambria Math;\">A fruit seller purchased 300 bananas at the rate of &#8377;18 per dozen and sold 200 bananas at the rate of &#8377;24 per dozen and the remaining bananas at the rate of &#8377;21 per dozen. What is his net profit percentage?</span></p>\n",
                    question_hi: "<p>62.&nbsp; <span style=\"font-weight: 400;\">&#2319;&#2325; &#2347;&#2354; &#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366; &#2344;&#2375; 300 &#2325;&#2375;&#2354;&#2375; &#8377;18 &#2346;&#2381;&#2352;&#2340;&#2367; &#2342;&#2352;&#2381;&#2332;&#2344; &#2325;&#2368; &#2342;&#2352; &#2360;&#2375; &#2326;&#2352;&#2368;&#2342;&#2375; &#2324;&#2352; 200 &#2325;&#2375;&#2354;&#2375; &#8377;24 &#2346;&#2381;&#2352;&#2340;&#2367; &#2342;&#2352;&#2381;&#2332;&#2344; &#2325;&#2368; &#2342;&#2352; &#2360;&#2375; &#2340;&#2341;&#2366; &#2358;&#2375;&#2359; &#2325;&#2375;&#2354;&#2375; &#8377;21 &#2346;&#2381;&#2352;&#2340;&#2367; &#2342;&#2352;&#2381;&#2332;&#2344; &#2325;&#2368; &#2342;&#2352; &#2360;&#2375; &#2348;&#2375;&#2330;&#2375;&#2404; &#2313;&#2360;&#2325;&#2366; &#2358;&#2369;&#2342;&#2381;&#2343; &#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>28%</p>\n", "<p>26%</p>\n", 
                                "<p>27%</p>\n", "<p><span style=\"font-family: Cambria Math;\">27<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>9</mn></mfrac></math>%</span></p>\n"],
                    options_hi: ["<p>28%</p>\n", "<p>26%</p>\n",
                                "<p>27%</p>\n", "<p><span style=\"font-family: Cambria Math;\">27<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>9</mn></mfrac></math>%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">62.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CP of&nbsp; bananas = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 300 = &#8377;450</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of&nbsp; bananas = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 200 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 400 + 175 = &#8377;575</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Profit % </strong>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>575</mn><mo>-</mo><mn>450</mn></mrow><mn>450</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>450</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 27<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">62.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&nbsp;&#2325;&#2375;&#2354;&#2375; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; </span>&nbsp;=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>12</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> &times; 300 = &#8377;450</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&nbsp;&#2325;&#2375;&#2354;&#2375; &#2325;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;</span> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 200 + </span><span style=\"font-family: Cambria Math;\"> &times; 100 = 400 + 175 = &#8377;575</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>575</mn><mo>-</mo><mn>450</mn></mrow><mn>450</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> &times; 100 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>450</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> &times; 100 = 27<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. <span style=\"font-family: Cambria Math;\">The table below gives the&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>i</mi><mi>n</mi><mi>p</mi><mi>u</mi><mi>t</mi></mrow><mrow><mi>o</mi><mi>u</mi><mi>t</mi><mi>p</mi><mi>u</mi><mi>t</mi></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> ratio of a particular firm over five consecutive years.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image34.png\" /></p>\r\n<p><span style=\"font-family: Cambria Math;\">If the inputs in the year 1998 was ₹1,200 crores and total output in the years 1998 and 2000 taken together was ₹2,500 crores, then what was the input of the firm in the year 2000?</span></p>",
                    question_hi: "<p>63. <span style=\"font-family: Kokila;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2327;&#2366;&#2340;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2347;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2311;&#2344;&#2346;&#2369;&#2335;</span><span style=\"font-family: Cambria Math;\">)- </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2352;&#2381;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2310;&#2313;&#2335;&#2346;&#2369;&#2335;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Kokila;\">&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_18658146611671023121154.png\" width=\"241\" height=\"149\"></p>\r\n<p><span style=\"font-weight: 400;\">&#2351;&#2342;&#2367; &#2357;&#2352;&#2381;&#2359; 1998 &#2350;&#2375;&#2306; &#2344;&#2367;&#2357;&#2375;&#2358; &#8377;1,200 &#2325;&#2352;&#2379;&#2337;&#2364; &#2341;&#2375; &#2324;&#2352; &#2357;&#2352;&#2381;&#2359; 1998 &#2324;&#2352; 2000 &#2350;&#2375;&#2306; &#2350;&#2367;&#2354;&#2366;&#2325;&#2352; &#2344;&#2367;&#2352;&#2381;&#2327;&#2340; &#8377;2,500 &#2325;&#2352;&#2379;&#2337;&#2364; &#2341;&#2366;, &#2340;&#2379; &#2357;&#2352;&#2381;&#2359; 2000 &#2350;&#2375;&#2306; &#2344;&#2367;&#2357;&#2375;&#2358; &#2325;&#2367;&#2340;&#2344;&#2366; &#2341;&#2366;?</span></p>\n",
                    options_en: ["<p>₹1,200 crores</p>", "<p>₹2,100 crores</p>", 
                                "<p>₹3,200 crores</p>", "<p>₹1,700 crores</p>"],
                    options_hi: ["<p>&#8377;1,200 <span style=\"font-family: Kokila;\">&#2325;&#2352;&#2379;&#2337;&#2364;</span></p>\n", "<p>&#8377;2,100 <span style=\"font-family: Kokila;\">&#2325;&#2352;&#2379;&#2337;&#2364;</span></p>\n",
                                "<p>&#8377;3,200 <span style=\"font-family: Kokila;\">&#2325;&#2352;&#2379;&#2337;&#2364;</span></p>\n", "<p>&#8377;1,700 <span style=\"font-family: Kokila;\">&#2325;&#2352;&#2379;&#2337;&#2364;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">63.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total output in the years 1998 </span><span style=\"font-family: Cambria Math;\">and 2000</span><span style=\"font-family: Cambria Math;\"> = ₹2500 crores</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total output in the year 1998 = 1200 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>3</mn></mfrac><mo>=</mo><mfrac><mn>4000</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">crores</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the output in the year 2000 = 2500 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4000</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3500</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">crores</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence the input of the firm in the year 2000 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3500</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>9</mn><mn>5</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 2100crores</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">63.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 1998 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2000 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> &#8377;2500 </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2379;&#2337;&#2364;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 1998 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 1200 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4000</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2379;&#2337;&#2364;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2000 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 2500 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4000</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3500</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2379;&#2337;&#2364;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2000 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2347;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3500</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 2100 </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2379;&#2337;&#2364;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. <span style=\"font-family: Cambria Math;\">The given pie chart shows the share of labour, raw material, energy, transportation cost, and plant &amp; machinery in the total manufacturing cost of the company during a particular tear. Study the pie chart and answer the question that follows.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image35.png\" /></p>\r\n<p><span style=\"font-family: Cambria Math;\">The total central angle showing the share of energy, labour and transportation cost in the total manufacturing cost during the year was:</span></p>",
                    question_hi: "<p>64.&nbsp; <span style=\"font-weight: 400;\">&#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2346;&#2366;&#2312; &#2330;&#2366;&#2352;&#2381;&#2335; &#2319;&#2325; &#2357;&#2367;&#2358;&#2375;&#2359; &#2357;&#2352;&#2381;&#2359; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2368; &#2325;&#2369;&#2354; &#2357;&#2367;&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2354;&#2366;&#2327;&#2340; &#2350;&#2375;&#2306; &#2358;&#2381;&#2352;&#2350;, &#2325;&#2330;&#2381;&#2330;&#2375; &#2350;&#2366;&#2354;, &#2314;&#2352;&#2381;&#2332;&#2366;, &#2346;&#2352;&#2367;&#2357;&#2361;&#2344; &#2354;&#2366;&#2327;&#2340; &#2324;&#2352; &#2360;&#2306;&#2351;&#2306;&#2340;&#2381;&#2352; &#2324;&#2352; &#2350;&#2358;&#2368;&#2344;&#2352;&#2368; &#2325;&#2375; &#2361;&#2367;&#2360;&#2381;&#2360;&#2375; &#2325;&#2379; &#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2346;&#2366;&#2312; &#2330;&#2366;&#2352;&#2381;&#2335; &#2325;&#2366; &#2309;&#2343;&#2381;&#2351;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2324;&#2352; &#2344;&#2368;&#2330;&#2375; &#2342;&#2367;&#2319; &#2327;&#2319; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2366; &#2313;&#2340;&#2381;&#2340;&#2352; &#2342;&#2375;&#2306;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_88184394111671023476231.png\" width=\"260\" height=\"225\"></p>\r\n<p><span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2359; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2325;&#2369;&#2354; &#2357;&#2367;&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2354;&#2366;&#2327;&#2340; &#2350;&#2375;&#2306; &#2314;&#2352;&#2381;&#2332;&#2366;, &#2358;&#2381;&#2352;&#2350; &#2324;&#2352; &#2346;&#2352;&#2367;&#2357;&#2361;&#2344; &#2354;&#2366;&#2327;&#2340; &#2325;&#2375; &#2361;&#2367;&#2360;&#2381;&#2360;&#2375; &#2325;&#2379; &#2342;&#2352;&#2381;&#2358;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2346;&#2370;&#2352;&#2381;&#2339; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351; &#2325;&#2379;&#2339; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>162&deg;</p>", "<p>120&deg;</p>", 
                                "<p>144&deg;</p>", "<p>135&deg;</p>"],
                    options_hi: ["<p>162&deg;</p>\n", "<p>120&deg;</p>\n",
                                "<p>144&deg;</p>\n", "<p>135&deg;</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">64.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total percentage of sharing of </span><span style=\"font-family: Cambria Math;\">energy ,</span><span style=\"font-family: Cambria Math;\"> labour and transporting cost in manufacturing cost in the company = 20 + 10 + 10 = 40%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Total central angle associated with it</strong> = 360&deg; &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 144&deg;</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">64.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Kokila;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2314;&#2352;&#2381;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2358;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2357;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2375;&#2342;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 20 + 10 + 10 = 40%</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2369;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> = 360&deg; &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 144&deg;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. <span style=\"font-family: Cambria Math;\">When a certain number is multiplied by 11, the product is a six-digit number containing only 6s. Find the number that is multiplied by 11.</span></p>",
                    question_hi: "<p>65. <span style=\"font-weight: 400;\">जब एक निश्चित संख्या को 11 से गुणा किया जाता है, तो गुणनफल में सभी छह अंकों की संख्या होती है। 11 से गुणा की जाने वाली संख्या ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>79365</p>", "<p>78365</p>", 
                                "<p>60606</p>", "<p>61661</p>"],
                    options_hi: ["<p>79365</p>", "<p>78365</p>",
                                "<p>60606</p>", "<p>61661</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">65.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Let the number be x</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>ATQ</strong>,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x &times; 11 = 666666</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>666666</mn><mn>11</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 60606</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the <strong>required no</strong> is 60606</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">65.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">माना, संख्या x है।</span></p>\r\n<p><span style=\"font-weight: 400;\">प्रश्न के अनुसार,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x &times; 11 = 666666</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>666666</mn><mn>11</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 60606</span></p>\r\n<p><span style=\"font-weight: 400;\">तो, अभीष्ट संख्या 60606 है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. <span style=\"font-family: Cambria Math;\">The table below gives the numbers of students from five different colleges who participated in the </span><span style=\"font-family: Cambria Math;\">Olympiades</span><span style=\"font-family: Cambria Math;\"> of five different subjects during a given year.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1664780942/word/media/image36.png\" /></p>\r\n<p><span style=\"font-family: Cambria Math;\">Which college had the maximum aggregate number of participants in all the five different </span><span style=\"font-family: Cambria Math;\">subject</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Olympiades</span><span style=\"font-family: Cambria Math;\"> taken together during that year?</span></p>",
                    question_hi: "<p>66.&nbsp; <span style=\"font-weight: 400;\">&#2344;&#2368;&#2330;&#2375; &#2342;&#2368; &#2327;&#2312; &#2340;&#2366;&#2354;&#2367;&#2325;&#2366; &#2350;&#2375;&#2306; &#2346;&#2366;&#2306;&#2330; &#2309;&#2354;&#2327;-&#2309;&#2354;&#2327; &#2325;&#2377;&#2354;&#2375;&#2332;&#2379;&#2306; &#2325;&#2375; &#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2342;&#2368; &#2327;&#2312; &#2361;&#2376;, &#2332;&#2367;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2319;&#2325; &#2342;&#2367;&#2319; &#2327;&#2319; &#2357;&#2352;&#2381;&#2359; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2346;&#2366;&#2306;&#2330; &#2309;&#2354;&#2327;-&#2309;&#2354;&#2327; &#2357;&#2367;&#2359;&#2351;&#2379;&#2306; &#2325;&#2375; &#2323;&#2354;&#2306;&#2346;&#2367;&#2351;&#2366;&#2337; &#2350;&#2375;&#2306; &#2349;&#2366;&#2327; &#2354;&#2367;&#2351;&#2366;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_20005878011671023653661.png\" width=\"444\" height=\"102\"></p>\r\n<p><span style=\"font-weight: 400;\">&#2313;&#2360; &#2357;&#2352;&#2381;&#2359; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2360;&#2349;&#2368; &#2346;&#2366;&#2306;&#2330; &#2309;&#2354;&#2327;-&#2309;&#2354;&#2327; &#2357;&#2367;&#2359;&#2351; &#2323;&#2354;&#2306;&#2346;&#2367;&#2351;&#2366;&#2337;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2366;&#2327;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2309;&#2343;&#2367;&#2325;&#2340;&#2350; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2367;&#2360; &#2325;&#2377;&#2354;&#2375;&#2332; &#2350;&#2375;&#2306; &#2341;&#2368;?</span></p>\n",
                    options_en: ["<p>College D</p>", "<p>College B</p>", 
                                "<p>College E</p>", "<p>College C</p>"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2354;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> D</span></p>\n", "<p><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2354;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> B</span></p>\n",
                                "<p><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2354;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> E</span></p>\n", "<p><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2354;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> C</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">66.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total no of participants in different subjects </span><span style=\"font-family: Cambria Math;\">olympiad</span><span style=\"font-family: Cambria Math;\"> in college A = 110+98+130+100+182 = 620</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total no of participants in different subjects </span><span style=\"font-family: Cambria Math;\">olympiad</span><span style=\"font-family: Cambria Math;\"> in college </span><span style=\"font-family: Cambria Math;\">B =</span><span style=\"font-family: Cambria Math;\"> 100+120+110+100+200 = 630</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total no of participants in different subjects </span><span style=\"font-family: Cambria Math;\">olympiad</span><span style=\"font-family: Cambria Math;\"> in college </span><span style=\"font-family: Cambria Math;\">C =</span><span style=\"font-family: Cambria Math;\"> 125+80+250+150+120 = 720</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total no of participants in different subjects </span><span style=\"font-family: Cambria Math;\">olympiad</span><span style=\"font-family: Cambria Math;\"> in college </span><span style=\"font-family: Cambria Math;\">D =</span><span style=\"font-family: Cambria Math;\"> 103+122+160+200+130 =710</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total no of participants in different subjects </span><span style=\"font-family: Cambria Math;\">olympiad</span><span style=\"font-family: Cambria Math;\"> in college </span><span style=\"font-family: Cambria Math;\">E =</span><span style=\"font-family: Cambria Math;\"> 112+105+180+80+183 = 658</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, we can clearly see that the <strong>maximum aggregate no of students in college </strong></span><strong><span style=\"font-family: Cambria Math;\">C </span><span style=\"font-family: Cambria Math;\">.</span></strong></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">66.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2325;&#2377;&#2354;&#2375;&#2332; A &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2357;&#2367;&#2359;&#2351;&#2379;&#2306; &#2323;&#2354;&#2306;&#2346;&#2367;&#2351;&#2366;&#2337; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2366;&#2327;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = 110+98+130+100+182 = 620</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2325;&#2377;&#2354;&#2375;&#2332; B &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2357;&#2367;&#2359;&#2351;&#2379;&#2306; &#2323;&#2354;&#2306;&#2346;&#2367;&#2351;&#2366;&#2337; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2366;&#2327;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = 100+120+110+100+200 = 630</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2325;&#2377;&#2354;&#2375;&#2332; C &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2357;&#2367;&#2359;&#2351;&#2379;&#2306; &#2323;&#2354;&#2306;&#2346;&#2367;&#2351;&#2366;&#2337; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2366;&#2327;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = 125+80+250+150+120 = 720</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2325;&#2377;&#2354;&#2375;&#2332; D &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2357;&#2367;&#2359;&#2351;&#2379;&#2306; &#2323;&#2354;&#2306;&#2346;&#2367;&#2351;&#2366;&#2337; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2366;&#2327;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = 103+122+160+200+130 =710</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2325;&#2377;&#2354;&#2375;&#2332; E &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2357;&#2367;&#2359;&#2351;&#2379;&#2306; &#2323;&#2354;&#2306;&#2346;&#2367;&#2351;&#2366;&#2337; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2366;&#2327;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = 112+105+180+80+183 = 658</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2311;&#2360;&#2354;&#2367;&#2319;, &#2361;&#2350; &#2360;&#2381;&#2346;&#2359;&#2381;&#2335; &#2352;&#2370;&#2346; &#2360;&#2375; &#2342;&#2375;&#2326; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2367; &#2325;&#2377;&#2354;&#2375;&#2332; C &#2350;&#2375;&#2306; &#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306; &#2325;&#2368; &#2309;&#2343;&#2367;&#2325;&#2340;&#2350; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2341;&#2368;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. <span style=\"font-family: Cambria Math;\">Under a sale offer, Tanvir was offered a 32% discount on the part of the marked price that was paid in cash, but had to add 1.2% on the part of the marked price paid through a credit card. If Tanvir paid 75% of the marked price in cash and the rest through a credit card, what percentage of the marked price was his total final payment?</span></p>\n",
                    question_hi: "<p>67.&nbsp; <span style=\"font-weight: 400;\">बिक्री प्रस्ताव के तहत, नकद में भुगतान किए अंकित मूल्य के हिस्से पर तनवीर को 32% छूट का प्रस्ताव दिया गया था, लेकिन क्रेडिट कार्ड के माध्यम से भुगतान किए गए अंकित मूल्य के हिस्से पर 1.2% जोड़ना पड़ा।&nbsp; यदि तनवीर ने अंकित मूल्य का 75% नकद और शेष क्रेडिट कार्ड के माध्यम से भुगतान किया, तो उसका कुल अंतिम भुगतान अंकित मूल्य का कितना प्रतिशत था?</span></p>",
                    options_en: ["<p>76.6%</p>\n", "<p>75.9%</p>\n", 
                                "<p>76.1%</p>\n", "<p>76.3%</p>\n"],
                    options_hi: ["<p>76.6%</p>", "<p>75.9%</p>",
                                "<p>76.1%</p>", "<p>76.3%</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">67.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Cambria Math;\">Total Percentage of MP, when </span><span style=\"font-family: Cambria Math;\">Tanvir paid</span><span style=\"font-family: Cambria Math;\"> the payment through cash = 75% &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>68</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 51%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total Percentage of MP, when </span><span style=\"font-family: Cambria Math;\">Tanvir paid</span><span style=\"font-family: Cambria Math;\"> the payment through credit card = 25% &times; 1.012 = 25.3%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, <strong>the required percentage</strong> = 51 + 25.3 = 76.3%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">67.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">अंकित मूल्य का कुल प्रतिशत, जब तनवीर ने नकद के माध्यम से भुगतान किया </span>&nbsp;= 75% &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>68</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 51%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">अंकित मूल्य का कुल प्रतिशत, जब तनवीर ने क्रेडिट कार्ड से भुगतान किया </span>&nbsp;= 25% &times; 1.012 = 25.3%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">अतः अभीष्ट प्रतिशत</span>= 51 + 25.3 = 76.3%</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. <span style=\"font-family: Cambria Math;\">Find the length of the longest bamboo pole that can be placed in a room 16 m long, 12 m broad and 10 m high.</span></p>\n",
                    question_hi: "<p>68.&nbsp; <span style=\"font-weight: 400;\">16 m लंबे, 12 m चौड़े और 10 m ऊंचे कमरे में रखे जा सकने वाले सबसे लंबे बांस के खंभे की लंबाई ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt><mi>m</mi></math></p>\n", "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt><mi>m</mi></math></p>\n", 
                                "<p>10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt><mi>m</mi></math></p>\n", "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt><mi>m</mi></math></p>\n"],
                    options_hi: ["<p>10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math></p>", "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>",
                                "<p>10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>", "<p>4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">68.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Length of the longest bamboo pole which can be placed in the given dimensions of room = Diagonal of that room= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msqrt><msup><mi>l</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup></msqrt><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msqrt><msup><mrow><msup><mn>16</mn><mn>2</mn></msup><mo>+</mo><msup><mn>12</mn><mn>2</mn></msup><mo>+</mo><mn>10</mn></mrow><mn>2</mn></msup></msqrt><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>256</mn><mo>+</mo><mn>144</mn><mo>+</mo><mn>100</mn></msqrt><mo>=</mo><mo>&nbsp;</mo><msqrt><mn>500</mn></msqrt><mo>=</mo><mn>10</mn><msqrt><mn>5</mn></msqrt><mi>m</mi></math></span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">68.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">सबसे लंबे बांस के खंभे की लंबाई जिसे कमरे के दिए गए आयामों में रखा जा सकता है = उस कमरे का विकर्ण = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>l</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup></msqrt></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>16</mn><mn>2</mn></msup><mo>+</mo><msup><mn>12</mn><mn>2</mn></msup><mo>+</mo><msup><mn>10</mn><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mn>256</mn><mo>+</mo><mn>144</mn><mo>+</mo><mn>100</mn></msqrt><mo>=</mo><mn>10</mn><msqrt><mn>5</mn><mi>m</mi></msqrt></math></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. <span style=\"font-family: Cambria Math;\">The average weight of 8 persons increased by 2.5 kg when a new person comes in place of one of them weighting 45 kg. What is the weight of the new person?</span></p>",
                    question_hi: "<p>69.&nbsp; <span style=\"font-weight: 400;\">8 व्यक्तियों के औसत भार में 2.5 kg की वृद्धि हो जाती है, जब उनमें से 45 kg भार वाले व्यक्ति के स्थान पर एक नया व्यक्ति आता है। नए व्यक्ति का वजन कितना है?</span></p>",
                    options_en: ["<p>63 kg</p>", "<p>60 kg</p>", 
                                "<p>65 kg</p>", "<p>62 kg</p>"],
                    options_hi: ["<p>63 kg</p>", "<p>60 kg</p>",
                                "<p>65 kg</p>", "<p>62 kg</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">69.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Since the average weight of 8 persons is increased by 2.5kg due to replacement of one person in that group. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>So, weight of new person</strong> </span><span style=\"font-family: Cambria Math;\">= 45</span><span style=\"font-family: Cambria Math;\"> + (2.5 &times; 8) = 45 + 20 = 65 kg</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">69.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\">चूँकि, 8 व्यक्तियों का औसत भार उस समूह में एक व्यक्ति के प्रतिस्थापन के कारण 2.5 kg बढ़ जाता है।</span></p>\r\n<p><span style=\"font-weight: 400;\">अत: नए व्यक्ति का भार =&nbsp; 45 + (2.5 &times; 8) = 45 + 20 = 65 kg</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. <span style=\"font-family: Cambria Math;\">The mean proportional between 6 and another number is 30. What is that number?</span></p>",
                    question_hi: "<p>70. <span style=\"font-weight: 400;\">6 &#2324;&#2352; &#2309;&#2344;&#2381;&#2351; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2375; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2366; &#2350;&#2343;&#2381;&#2351;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;&nbsp; 30 &#2361;&#2376;&#2404; &#2357;&#2361; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;?</span></p>\n",
                    options_en: ["<p>150</p>", "<p>5&radic;6</p>", 
                                "<p>180</p>", "<p>6&radic;5</p>"],
                    options_hi: ["<p>150</p>\n", "<p>5&radic;6</p>\n",
                                "<p>180</p>\n", "<p>6&radic;5</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">70.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Let, </span><span style=\"font-family: Cambria Math;\">the another</span><span style=\"font-family: Cambria Math;\"> no be x</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mean proportional of 6 and x = 30</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn><mi>x</mi></msqrt></math></span><span style=\"font-family: Cambria Math;\">= 30</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">6x =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>30</mn><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">= 900</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>900</mn><mn>6</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 150 </span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">70.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-weight: 400;\">&#2350;&#2366;&#2344;&#2366;, &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; x &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-weight: 400;\">6 &#2324;&#2352; x&#2325;&#2366; &#2350;&#2343;&#2381;&#2351;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368; = 30</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn><mi>x</mi></msqrt></math></span><span style=\"font-weight: 400;\">&nbsp; =&nbsp; 30</span></p>\r\n<p><span style=\"font-weight: 400;\">6x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>30</mn><mn>2</mn></msup></math></span><span style=\"font-weight: 400;\">= 900</span></p>\r\n<p><span style=\"font-weight: 400;\">x = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>900</mn><mn>6</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= 150 </span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. <span style=\"font-family: Cambria Math;\">A thief is spotted by a policeman from a distance of 480 m. When the policeman starts the chase, the thief also starts running. If the speed of the thief is 19 km/h and that of the policeman is 23 km/h, then how far would the thief have to run before he is overtaken?</span></p>",
                    question_hi: "<p>71.&nbsp; <span style=\"font-weight: 400;\">&#2319;&#2325; &#2330;&#2379;&#2352; &#2325;&#2379; &#2319;&#2325; &#2346;&#2369;&#2354;&#2367;&#2360;&#2325;&#2352;&#2381;&#2350;&#2368; 480 m &#2325;&#2368; &#2342;&#2370;&#2352;&#2368; &#2360;&#2375; &#2342;&#2375;&#2326;&#2340;&#2366; &#2361;&#2376;&#2404; &#2346;&#2369;&#2354;&#2367;&#2360;&#2325;&#2352;&#2381;&#2350;&#2368; &#2332;&#2376;&#2360;&#2375; &#2361;&#2368; &#2330;&#2379;&#2352; &#2325;&#2366; &#2346;&#2368;&#2331;&#2366; &#2325;&#2352;&#2344;&#2366; &#2358;&#2369;&#2352;&#2370; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;, &#2330;&#2379;&#2352; &#2349;&#2368; &#2349;&#2366;&#2327;&#2344;&#2375; &#2354;&#2327;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2342;&#2368; &#2330;&#2379;&#2352; &#2325;&#2368; &#2330;&#2366;&#2354; 19 km/h &#2324;&#2352; &#2346;&#2369;&#2354;&#2367;&#2360;&#2325;&#2352;&#2381;&#2350;&#2368; &#2325;&#2368; &#2330;&#2366;&#2354; 23 km/h &#2361;&#2376;, &#2340;&#2379; &#2346;&#2325;&#2337;&#2364;&#2375; &#2332;&#2366;&#2344;&#2375; &#2340;&#2325; &#2330;&#2379;&#2352; &#2325;&#2367;&#2340;&#2344;&#2368; &#2342;&#2370;&#2352;&#2368; &#2340;&#2325; &#2349;&#2366;&#2327; &#2330;&#2369;&#2325;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>2080 m</p>", "<p>2280 m</p>", 
                                "<p>2290 m</p>", "<p>2180 m</p>"],
                    options_hi: ["<p>2080 m</p>\n", "<p>2280 m</p>\n",
                                "<p>2290 m</p>\n", "<p>2180 m</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">71.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Relative speed when policeman and thief are in same direction = 23 - 19 = 4 km/hr = 4 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> m/sec</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Time taken to chase the thief = 480 &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\">432 sec</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>So, the distance covered by thief before he is overtaken </strong>= 19 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> &times; 432 = 2280 m</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">71.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2332;&#2348; &#2346;&#2369;&#2354;&#2367;&#2360;&#2325;&#2352;&#2381;&#2350;&#2368; &#2324;&#2352; &#2330;&#2379;&#2352; &#2319;&#2325; &#2361;&#2368; &#2342;&#2367;&#2358;&#2366; &#2350;&#2375;&#2306; &#2361;&#2379;&#2306; &#2340;&#2379; &#2360;&#2366;&#2346;&#2375;&#2325;&#2381;&#2359; &#2330;&#2366;&#2354;&nbsp; = 23 - 19 = 4 km/hr = 4 &times;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math><span style=\"font-weight: 400;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math><span style=\"font-weight: 400;\">m/sec</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2330;&#2379;&#2352; &#2325;&#2366; &#2346;&#2368;&#2331;&#2366; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2354;&#2327;&#2366; &#2360;&#2350;&#2351; = 480 &times; </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;= 432&nbsp; sec</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2309;&#2340;: &#2330;&#2379;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2313;&#2360;&#2325;&#2375; &#2310;&#2327;&#2375; &#2344;&#2367;&#2325;&#2354;&#2344;&#2375; &#2360;&#2375; &#2346;&#2361;&#2354;&#2375; &#2340;&#2351; &#2325;&#2368; &#2327;&#2312; &#2342;&#2370;&#2352;&#2368; = 19 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;&times; 432 = 2280 m</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. <span style=\"font-family: Cambria Math;\">In what proportion must wheat at &#8377;20.40 per kg be mixed with wheat at &#8377;25.50 per kg, so that the mixture is worth &#8377;23.80 per kg?</span></p>\n",
                    question_hi: "<p>72. <span style=\"font-weight: 400;\">₹20.40 प्रति kg की दर से मिलने वाले&nbsp; गेहूँ और ₹25.50 प्रति kg की दर सेमिलने वाले&nbsp; गेहूँ को किस अनुपात में मिलाया जाना चािहए, तािक&nbsp; मिश्रण का मूल्य ₹23.80 प्रति kg हो?</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">1 :</span><span style=\"font-family: Cambria Math;\"> 3</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 1</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 3</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">1 :</span><span style=\"font-family: Cambria Math;\"> 2</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">1 :</span><span style=\"font-family: Cambria Math;\"> 3</span></p>", "<p><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 1</span></p>",
                                "<p><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 3</span></p>", "<p><span style=\"font-family: Cambria Math;\">1 :</span><span style=\"font-family: Cambria Math;\"> 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">72.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_20073607111665131252532.png\" width=\"210\" height=\"200\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>So, the required quantity of the two varieties of wheat </strong>= </span><span style=\"font-family: Cambria Math;\">1 :</span><span style=\"font-family: Cambria Math;\"> 2</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">72.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_2406996711665141921765.png\" /></p>\r\n<p><span style=\"font-weight: 400;\">अतः गेहूँ की दो किस्मों की अभीष्ट मात्रा = 1: 2</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. <span style=\"font-family: Cambria Math;\">What is the simplified value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mo>(</mo><mn>90</mn><mo>-</mo><mi>&theta;</mi><mo>)</mo><mo>-</mo><mfenced open=\"[\" close=\"]\"><mfrac><mrow><mfenced open=\"{\" close=\"}\"><mrow><mi>cos</mi><mo>(</mo><msup><mn>90</mn><mo>&deg;</mo></msup><mo>-</mo><mi>&theta;</mi></mrow></mfenced><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi></mrow></mfrac></mfenced></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    question_hi: "<p>73.&nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mo>(</mo><mn>90</mn><mo>-</mo><mi>&theta;</mi><mo>)</mo><mo>-</mo><mfenced open=\"[\" close=\"]\"><mfrac><mrow><mfenced open=\"{\" close=\"}\"><mrow><mi>cos</mi><mo>(</mo><msup><mn>90</mn><mo>&deg;</mo></msup><mo>-</mo><mi>&theta;</mi></mrow></mfenced><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi></mrow></mfrac></mfenced></math><span style=\"font-weight: 400;\">&#2325;&#2366; &#2360;&#2352;&#2354;&#2368;&#2325;&#2371;&#2340; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>4</p>\n", "<p>2</p>\n", 
                                "<p>0</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>4</p>\n", "<p>2</p>\n",
                                "<p>0</p>\n", "<p>1</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">73.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mfenced><mrow><msup><mn>90</mn><mo>&deg;</mo></msup><mo>-</mo><mi>&theta;</mi></mrow></mfenced><mo>-</mo><mfenced open=\"[\" close=\"]\"><mfrac><mrow><mi>cos</mi><mfenced><mrow><msup><mn>90</mn><mo>&deg;</mo></msup><mo>-</mo><mi>&theta;</mi></mrow></mfenced></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi></mrow></mfrac></mfenced><mspace linebreak=\"newline\"></mspace><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mfenced open=\"[\" close=\"]\"><mfrac><mrow><mi>sin</mi><mi>&theta;</mi><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi></mrow></mfrac></mfenced><mspace linebreak=\"newline\"></mspace><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mfenced open=\"[\" close=\"]\"><mrow><mi>sin</mi><mi>&theta;</mi><mi>cos</mi><mi>&theta;</mi><mo>&times;</mo><mfrac><mrow><mi>sin</mi><mi>&theta;</mi></mrow><mrow><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></mrow></mfenced><mspace linebreak=\"newline\"></mspace><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">73.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mo>(</mo><mn>90</mn><mo>-</mo><mi>&theta;</mi><mo>)</mo><mo>-</mo><mfenced open=\"[\" close=\"]\"><mfrac><mrow><mi>cos</mi><mo>(</mo><msup><mn>90</mn><mo>&deg;</mo></msup><mo>-</mo><mi>&theta;</mi><mo>)</mo><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi></mrow></mfrac></mfenced><mspace linebreak=\"newline\"></mspace><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mfenced open=\"[\" close=\"]\"><mfrac><mrow><mi>sin</mi><mi>&theta;</mi><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi></mrow></mfrac></mfenced><mspace linebreak=\"newline\"></mspace><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mfenced open=\"[\" close=\"]\"><mrow><mi>sin</mi><mi>&theta;</mi><mi>cos</mi><mi>&theta;</mi><mo>&times;</mo><mfrac><mrow><mi>sin</mi><mi>&theta;</mi></mrow><mrow><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></mrow></mfenced><mspace linebreak=\"newline\"></mspace><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mn>0</mn></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. <span style=\"font-family: Cambria Math;\">Find the length of diagonal (in cm) of a cube if the volume of the cube is 1331<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math> </span></p>\n",
                    question_hi: "<p>74. <span style=\"font-weight: 400;\">&#2351;&#2342;&#2367; &#2328;&#2344; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; 1331 </span><span style=\"font-weight: 400;\">cm</span><span style=\"font-weight: 400;\">3</span><span style=\"font-weight: 400;\"> &#2361;&#2376;, &#2340;&#2379; &#2328;&#2344; &#2325;&#2375; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; (cm &#2350;&#2375;&#2306;) &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>331<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", "<p>21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", 
                                "<p>11<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", "<p>111<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    options_hi: ["<p>331<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", "<p>21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n",
                                "<p>11<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n", "<p>111<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">74.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\">Volume of cube =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1331 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a = 11 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>The diagonal of cube&nbsp;</strong> =&nbsp; </span><span style=\"font-family: Cambria Math;\">a<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>&nbsp; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> &times; 11 = 11<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> cm</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">74.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Kokila;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1331 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a = 11 cm</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> a = </span><span style=\"font-family: Cambria Math;\"> &times; 11 = 11<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> cm</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "18",
                    question_en: "<p>75. <span style=\"font-family: Cambria Math;\">If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>=</mo><mn>14159</mn></math> </span><span style=\"font-family: Cambria Math;\">, then a possible value of&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> is:</span></p>",
                    question_hi: "<p>75.&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>=</mo><mn>14159</mn></math> <span style=\"font-family: Kokila;\">यदि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">तो&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संभावित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">मान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">:</span></p>",
                    options_en: ["<p>69</p>", "<p>121</p>", 
                                "<p>81</p>", "<p>11</p>"],
                    options_hi: ["<p>69</p>", "<p>121</p>",
                                "<p>81</p>", "<p>11</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">75.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac></math>= 14159</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>14159</mn><mo>+</mo><mn>2</mn></msqrt></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 119</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>119</mn><mo>+</mo><mn>2</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">=&nbsp; 11</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">75.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>=</mo><mn>14159</mn><mspace linebreak=\"newline\"/><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><msqrt><mn>14159</mn><mo>+</mo><mn>2</mn><mo>&#160;</mo></msqrt><mo>=</mo><mn>119</mn><mspace linebreak=\"newline\"/><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><msqrt><mn>119</mn><mo>+</mo><mn>2</mn></msqrt><mo>=</mo><mn>11</mn></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "18",
                    question_en: "<p>76. <span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: Cambria Math;\">Wadali</span><span style=\"font-family: Cambria Math;\"> Brothers are famous for which of the following?</span></p>",
                    question_hi: "<p>76. <span style=\"font-family: Kokila;\">वडाली</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">ब्रदर्स</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">Wadali</span><span style=\"font-family: Cambria Math;\"> Brothers) </span><span style=\"font-family: Kokila;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किसके</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रसिद्ध</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हैं</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>Carnatic Music</p>", "<p>Chhattisgarhi Folk</p>", 
                                "<p>Sufi Music</p>", "<p>Hindustani Classical Music</p>"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">कर्नाटक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संगीत</span></p>", "<p><span style=\"font-family: Kokila;\">छत्तीसगढ़ी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लोक</span></p>",
                                "<p><span style=\"font-family: Kokila;\">सूफी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संगीत</span></p>", "<p><span style=\"font-family: Kokila;\">हिंदुस्तानी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">शास्त्रीय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संगीत</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">76.(</span><span style=\"font-family: Cambria Math;\">c) <strong>&nbsp;The Wadali Brothers</strong><span style=\"font-weight: 400;\"> &ndash; Puranchand Wadali and Pyarelal Wadali &ndash; are Sufi singers from Punjab. </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">The </span><strong>Gundecha Brothers</strong><span style=\"font-weight: 400;\"> are Indian classical singers of the dhrupad genre of the Dagar Vani.</span></span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">Famous </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Famous Sufi singers:</strong><span style=\"font-weight: 400;\"> Sabri Brothers, Qutbi Brothers, Nizami brothers, Nusrat Fateh Ali Khan, Rahat Fateh Ali Khan, etc. </span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">76.(</span><span style=\"font-family: Cambria Math;\">c)<strong> &nbsp;वडाली ब्रदर्स<span style=\"font-weight: 400;\"> - पूरनचंद वडाली और प्यारेलाल वडाली - पंजाब के </span>सूफी गायक<span style=\"font-weight: 400;\"> हैं।</span></strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong><span style=\"font-weight: 400;\"> </span>गुंडेचा ब्रदर्स<span style=\"font-weight: 400;\"> डागर वाणी की ध्रुपद शैली के भारतीय शास्त्रीय गायक हैं।</span></strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong><span style=\"font-weight: 400;\"><strong> प्रसिद्ध सूफी गायक: </strong>साबरी ब्रदर्स, कुतुबी ब्रदर्स, निजामी ब्रदर्स, नुसरत फतेह अली खान, राहत फतेह अली खान, आदि है।</span></strong></span><span style=\"font-family: Kokila;\">।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "18",
                    question_en: "<p>77. <span style=\"font-family: Cambria Math;\">The important singers of ___________Gharana are </span><span style=\"font-family: Cambria Math;\">Faiyyaz</span><span style=\"font-family: Cambria Math;\"> Khan, </span><span style=\"font-family: Cambria Math;\">Latafat</span><span style=\"font-family: Cambria Math;\"> Hussein Khan and </span><span style=\"font-family: Cambria Math;\">Dinkar</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Kakini</span><span style=\"font-family: Cambria Math;\">.</span></p>",
                    question_hi: "<p>77.&nbsp; <span style=\"font-weight: 400;\">___________घराने के महत्वपूर्ण गायक फैय्याज खान, लताफत हुसैन खान और दिनकर काकिनी हैं।</span></p>",
                    options_en: ["<p>Patiala</p>", "<p><span style=\"font-family: Cambria Math;\">Mewati</span></p>", 
                                "<p>Agra</p>", "<p><span style=\"font-family: Cambria Math;\">Benaras</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">पटियाला</span></p>", "<p><span style=\"font-weight: 400;\">मेवाती</span></p>",
                                "<p><span style=\"font-weight: 400;\">आगरा</span></p>", "<p><span style=\"font-weight: 400;\">बनारस</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">77.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">The</span><strong> Agra Gharana</strong><span style=\"font-weight: 400;\"> is a tradition of Hindustani classical vocal music descended from the</span><strong> Nauhar Bani (</strong><span style=\"font-weight: 400;\">during the reign of Emperor Allauddin Khilji of Delhi).</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong> Some prominent exponents are</strong> Faiyaz Khan&nbsp; \"Prempiya\", Vilayat Hussain Khan \"Pran Piya\", Khadim Hussain Khan \"Sajan Piya\", Sharafat Hussain Khan \"Prem Rang\", Shrikrishna Narayan Ratanjankar \"Sujan\", Babanrao Haldankar \"Raspiya\", Zohrabai, etc.&nbsp;</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">77.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> <strong>आगरा घराना</strong><span style=\"font-weight: 400;\"> नौहर बानी (दिल्ली के सम्राट अलाउद्दीन खिलजी के शासनकाल के दौरान) से निकले हिंदुस्तानी शास्त्रीय गायन संगीत की एक परंपरा है। </span></span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">कुछ प्रमुख प्रतिपादक:</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> फैयाज खान \"प्रेमपिया\", </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">विलायत हुसैन खान \"प्राण पिया\", </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">खादिम हुसैन खान \"साजन पिया\",</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> शराफत हुसैन खान \"प्रेम रंग\", </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">श्रीकृष्ण नारायण रतनजंकर \"सुजान\", </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">बबनराव हल्दांकर \"रास्पिया\", जोहराबाई, आदि हैं। .</span></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "18",
                    question_en: "<p>78. <span style=\"font-family: Cambria Math;\">Who among the following has written the autobiography &lsquo;In the afternoon of time: An autobiography&rsquo;?</span></p>",
                    question_hi: "<p>78. <span style=\"font-family: Kokila;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किसने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">आत्मकथा</span><span style=\"font-family: Cambria Math;\"> &lsquo;In the afternoon of time: An autobiography&rsquo; </span><span style=\"font-family: Kokila;\">लिखी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>Harivansh Rai Bachchan</p>", "<p>Ruskin Bond</p>", 
                                "<p>R K Lakshman</p>", "<p>R K Narayanan</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">हरिवंश राय बच्चन</span></p>", "<p><span style=\"font-weight: 400;\">रस्किन बॉन्ड</span></p>",
                                "<p><span style=\"font-weight: 400;\">आर. के. लक्ष्मण</span></p>", "<p><span style=\"font-weight: 400;\">आर. के. लक्ष्मण</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">78.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span></p>\r\n<p><strong>Harivansh Rai Bachchan</strong><span style=\"font-weight: 400;\">- &ldquo;In the afternoon of time: An autobiography&rdquo;. </span></p>\r\n<p><strong>Ruskin Bond-</strong><span style=\"font-weight: 400;\"> \"Lone Fox Dancing: My Autobiography\". </span></p>\r\n<p><strong>R K Lakshman-</strong><span style=\"font-weight: 400;\"> &ldquo;The Tunnel of Time&rdquo;.</span><strong> </strong></p>\r\n<p><strong>R K Narayanan-</strong><span style=\"font-weight: 400;\"> &ldquo;Grandmother\'s Tale&rdquo;.&nbsp;</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">78.(</span><span style=\"font-family: Cambria Math;\">a) <strong>हरिवंश राय बच्चन</strong><span style=\"font-weight: 400;\">- &ldquo;In the afternoon of time: An autobiography&rdquo;। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>रस्किन बॉन्ड</strong><span style=\"font-weight: 400;\">- \"Lone Fox Dancing: My Autobiography\"। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>आर. के. लक्ष्मण</strong><span style=\"font-weight: 400;\">- &ldquo;The Tunnel of Time&rdquo;। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>आर. के. नारायणन</strong><span style=\"font-weight: 400;\">- &ldquo;Grandmother\'s Tale&rdquo;।</span></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "18",
                    question_en: "<p>79. <span style=\"font-family: Cambria Math;\">&lsquo;Faster than Lightning: My Autobiography&rsquo; is the story of which of the following international sprinters?</span></p>",
                    question_hi: "<p>79. <span style=\"font-family: Cambria Math;\">&lsquo;Faster than Lightning: My Autobiography&rsquo;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">अंतरराष्ट्रीय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">धावक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">की</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कहानी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: ["<p>Usain Bolt</p>", "<p>Justin Gatlin</p>", 
                                "<p>Christian Coleman</p>", "<p>Michael Norman</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">उसैन बोल्ट</span></p>", "<p><span style=\"font-weight: 400;\">जस्टिन गैटलिन</span></p>",
                                "<p><span style=\"font-weight: 400;\">क्रिश्चियन कोलमैन</span></p>", "<p><span style=\"font-weight: 400;\">माइकल नॉर्मन</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">79.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><strong>Usain Bolt&nbsp; </strong><span style=\"font-weight: 400;\">- <strong>&ldquo;Faster than Lightning: My Autobiography&rdquo;</strong>.</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong> Another famous book by him and Matt Allen&nbsp; </strong></span><span style=\"font-weight: 400;\">- is &ldquo;The Fastest Man Alive: The True Story of Usain Bolt&rdquo;. His World Record- 100m; 9.58 seconds; IAAF World Championships (2009).&nbsp;</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">79.(</span><span style=\"font-family: Cambria Math;\">a) <strong>उसैन बोल्ट </strong><span style=\"font-weight: 400;\">- &ldquo;Faster than Lightning: My Autobiography&rdquo;। उनकी और </span></span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">मैट एलन की एक अन्य प्रसिद्ध पुस्तक- &ldquo;The Fastest Man Alive: The True Story of Usain Bolt&rdquo; है। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>उनका विश्व रिकॉर्ड </strong>- 100 मीटर; 9.58 सेकंड; IAAF विश्व चैंपियनशिप (2009)।</span></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "18",
                    question_en: "<p>80. <span style=\"font-family: Cambria Math;\">The proportion of a small increase in income which will lead to increased consumption</span><span style=\"font-family: Cambria Math;\">expenditure is known as ________.</span></p>",
                    question_hi: "<p>80. <span style=\"font-weight: 400;\">आय में एक छोटी सी वृद्धि का अनुपात जिससे खपत व्यय में वृद्धि होगी ________ के रूप में जाना जाता है।</span></p>",
                    options_en: ["<p>marginal consumption efficiency</p>", "<p>marginal propensity to consume</p>", 
                                "<p>marginal efficiency of income</p>", "<p>marginal propensity to save</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">सीमांत खपत दक्षता</span></p>", "<p><span style=\"font-weight: 400;\">सीमांत उपभोग प्रवृत्ति</span></p>",
                                "<p><span style=\"font-weight: 400;\">&nbsp;आय की सीमांत दक्षता</span></p>", "<p><span style=\"font-weight: 400;\">सीमांत बचत प्रवृत्ति</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">80.(</span><span style=\"font-family: Cambria Math;\">b) <span style=\"font-weight: 400;\">The&nbsp;</span><strong> marginal propensity to consume</strong><span style=\"font-weight: 400;\"> measures the degree to which a consumer will spend or save in relation to an aggregate raise in pay. </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>The</strong> </span></span><span style=\"font-family: Cambria Math;\"><strong>marginal efficiency of capital </strong><span style=\"font-weight: 400;\">is the rate of discount which would equate the price of a fixed capital asset with its present discounted value of expected income.</span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">80.(</span><span style=\"font-family: Cambria Math;\">b)<strong>&nbsp; सीमांत उपभोग प्रवृत्ति</strong><span style=\"font-weight: 400;\"> उस डिग्री को मापती है जिस पर उपभोक्ता खर्च करेगा या वेतन में कुल वृद्धि के संबंध में बचत करेगा। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>आय की सीमांत दक्षता</strong><span style=\"font-weight: 400;\"> छूट की दर है जो एक निश्चित पूंजी परिसंपत्ति की कीमत को अपेक्षित आय के वर्तमान छूट वाले मूल्य के साथ समान करेगी।</span></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "18",
                    question_en: "<p>81. <span style=\"font-family: Cambria Math;\">Who among the following is NOT a Padma Shri awardee 2022?</span></p>",
                    question_hi: "<p>81.&nbsp; <strong>&nbsp;</strong><span style=\"font-weight: 400;\">निम्नलिखित में से कौन पद्मश्री से सम्मानित 2022 नहीं है?</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Mithali</span><span style=\"font-family: Cambria Math;\"> Raj</span></p>", "<p><span style=\"font-family: Cambria Math;\">Avani</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Lekhara</span></p>", 
                                "<p><span style=\"font-family: Cambria Math;\">Sumit</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Antil</span></p>", "<p>Neeraj Chopra</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">मिताली राज</span></p>", "<p><span style=\"font-weight: 400;\">अवनि लेखरा</span></p>",
                                "<p><span style=\"font-weight: 400;\">सुमित एंटील</span></p>", "<p><span style=\"font-weight: 400;\">नीरज चोपड़ा</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">81.(</span><span style=\"font-family: Cambria Math;\">a) </span></p>\r\n<p><strong>&nbsp;Mithali Dorai Ra</strong><span style=\"font-weight: 400;\">j is an Indian cricketer who was awarded Padma Shri in 2015. </span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>Padma Shri awardee</strong> </span><strong>2022 in sports:&nbsp; </strong><span style=\"font-weight: 400;\">Neeraj Chopra (Javelin thrower, Haryana); Shri Sumit Antil (Paralympian and javelin thrower, Haryana); Ms. Avani Lekhara (Indian Paralympian and rifle shooter, Rajasthan). </span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">81.(</span><span style=\"font-family: Cambria Math;\">a) <strong>मिताली दोराई राज</strong><span style=\"font-weight: 400;\"> एक भारतीय क्रिकेटर हैं जिन्हें 2015 में पद्म श्री से सम्मानित किया गया था।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong> खेलों में पद्म श्री पुरस्कार से</strong> </span><strong>सम्मानित 2022</strong><span style=\"font-weight: 400;\">: </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">नीरज चोपड़ा (भाला फेंकने वाला, हरियाणा); </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">श्री सुमित अंतिल (पैरालिंपियन और भाला फेंकने वाला, हरियाणा);</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> सुश्री अवनि लेखारा (भारतीय पैरालिंपियन और राइफल शूटर, राजस्थान)।</span></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "18",
                    question_en: "<p>82. <span style=\"font-family: Cambria Math;\">The &lsquo;Karbi </span><span style=\"font-family: Cambria Math;\">Anglong</span><span style=\"font-family: Cambria Math;\"> Agreement&rsquo; signed in September 2021 is related to the ethnic community of the state of ________.</span></p>",
                    question_hi: "<p>82.&nbsp; <span style=\"font-weight: 400;\">सितंबर 2021 में हस्ताक्षरित \'कार्बी आंगलोंग समझौता\' ________ राज्य के जातीय समुदाय से संबंधित है।</span></p>",
                    options_en: ["<p>Sikkim</p>", "<p>Assam</p>", 
                                "<p>Bihar</p>", "<p>Uttar Pradesh</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">सिक्किम</span></p>", "<p><span style=\"font-weight: 400;\">असम</span></p>",
                                "<p><span style=\"font-weight: 400;\">बिहार</span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;उत्तर प्रदेश</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">82.(</span><span style=\"font-family: Cambria Math;\">b) <strong>&nbsp;Karbi Anglong</strong><span style=\"font-weight: 400;\"> Agreement is a tripartite agreement among five insurgent groups of Assam, the Centre, and the state government. </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Karbi Anglong Autonomous Council (KAAC)</strong><span style=\"font-weight: 400;\"> is protected under the Sixth Schedule of the Indian Constitution. Other peace agreements in North East are </span><strong>Bru Accord, 2020</strong><span style=\"font-weight: 400;\"> (Tripura, Mizoram, and Assam), </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Bodo Peace Accord</strong><span style=\"font-weight: 400;\"> (Assam),</span><strong> </strong>and</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong> NLFT Tripura Agreement, 2019. </strong></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">82.(</span><span style=\"font-family: Cambria Math;\">b) <strong>&nbsp;कार्बी आंगलोंग </strong><span style=\"font-weight: 400;\">समझौता असम, केंद्र और राज्य सरकार के पांच विद्रोही समूहों के बीच एक त्रिपक्षीय समझौता है। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">कार्बी आंगलोंग स्वायत्त परिषद (</span><strong>KAAC</strong><span style=\"font-weight: 400;\">) भारतीय संविधान की छठी अनुसूची के तहत संरक्षित है। </span></span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">उत्तर पूर्व में अन्य शांति समझौते:-</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> </span><strong>ब्रू एकॉर्ड, 2020</strong><span style=\"font-weight: 400;\"> (त्रिपुरा, मिजोरम और असम), </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>बोडो शांति समझौता</strong><span style=\"font-weight: 400;\"> (असम), और </span><strong>NLFT</strong><span style=\"font-weight: 400;\"> </span><strong>त्रिपुरा समझौता, 2019</strong><span style=\"font-weight: 400;\"> हैं।</span></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "18",
                    question_en: "<p>83. <span style=\"font-family: Cambria Math;\">What is the number of players in a cricket team on the ground?</span></p>",
                    question_hi: "<p>83. <span style=\"font-weight: 400;\">एक क्रिकेट टीम में मैदान पर खिलाड़ियों की संख्या कितनी होती है?</span></p>",
                    options_en: ["<p>14</p>", "<p>10</p>", 
                                "<p>11</p>", "<p>12</p>"],
                    options_hi: ["<p>14</p>", "<p>10</p>",
                                "<p>11</p>", "<p>12</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">83.(</span><span style=\"font-family: Cambria Math;\">c) <span style=\"font-weight: 400;\">The number of</span><strong> players </strong><span style=\"font-weight: 400;\">in a cricket team on a field is 11. The cricket pitch is 20.12 meters (22 yards) in length and 3.05 meters (3.33 yards) in width. The cricket ball weighs 163 grams.</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong> Number of players&nbsp;</strong><span style=\"font-weight: 400;\"><strong> in</strong> Rugby Football - 15,&nbsp; Kabaddi- 7, Polo- 4, Volleyball- 6, Lacrosse- 12, Handball-7, Water polo-7, etc.</span></span><span style=\"font-family: Cambria Math;\">.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">83.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp; <span style=\"font-weight: 400;\">एक क्रिकेट टीम में एक मैदान पर खिलाड़ियों की संख्या 11 होती है। क्रिकेट पिच की लंबाई 20.12 मीटर (22 गज) और चौड़ाई 3.05 मीटर (3.33 गज) होती है। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">क्रिकेट की गेंद का वजन 163 ग्राम है। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>रग्बी फुटबॉल&nbsp; </strong>में खिलाड़ियों की संख्या-15, </span></span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>कबड्डी</strong> -7<strong>, पोलो</strong> -4, <strong>वॉलीबॉल</strong>-6, <strong>लैक्रोस</strong>-12,<strong> हैंडबाल</strong>-7,&nbsp; <strong>वाटर पोलो</strong>-7, आदि होती है।</span></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "18",
                    question_en: "<p>84. <span style=\"font-family: Cambria Math;\">Who among the following is the author of the book &lsquo;Sultry Days&rsquo;?</span></p>",
                    question_hi: "<p>84. <span style=\"font-weight: 400;\">निम्नलिखित में से कौन &lsquo;Sultry Days&rsquo; पुस्तक के लेखक हैं?</span></p>",
                    options_en: ["<p>Anita Nair</p>", "<p>Nikita Singh</p>", 
                                "<p>Shobhaa De</p>", "<p>Judy Balan</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;अनीता नायर</span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;निकिता सिंह</span></p>",
                                "<p><span style=\"font-weight: 400;\">&nbsp;शोभा दे</span></p>", "<p><span style=\"font-weight: 400;\">जूडी बालन</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">84.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp; </span><strong>Shobhaa De</strong><span style=\"font-weight: 400;\">- &lsquo;Sultry Days&rsquo;, &lsquo;Sethji&rsquo;, &lsquo;Bollywood Nights&rsquo;, &lsquo;Snapshots&rsquo;,&nbsp;</span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&lsquo;Speed Post&rsquo;, &lsquo;Socialite Evenings&rsquo;, &lsquo;Second Thoughts&rsquo;, &lsquo;Sisters&rsquo;.</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> </span><strong>Anita Nair&nbsp; &nbsp;-</strong><span style=\"font-weight: 400;\">&nbsp; &lsquo;A Better Man&rsquo;, &lsquo;Mistress&rsquo;, and &lsquo;Lessons in Forgetting. </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Nikita Singh&nbsp; </strong><span style=\"font-weight: 400;\">-&nbsp; &lsquo;Someone Like You&rsquo;, &lsquo;The Promise&rsquo;, &lsquo;Letters to My Ex&rsquo;.</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> </span><strong>Judy Balan&nbsp; </strong><span style=\"font-weight: 400;\">- &lsquo;Two Fates&rsquo;, &lsquo;Half Boyfriend&rsquo;, &lsquo;I Have a Theory about That&rsquo;</span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">84.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp; <strong>शोभा डे</strong><span style=\"font-weight: 400;\">- &lsquo;Sultry Days&rsquo;, &lsquo;Sethji&rsquo;, &lsquo;Bollywood Nights&rsquo;, &lsquo;Snapshots&rsquo;, &lsquo;Speed Post&rsquo;, &lsquo;Socialite Evenings&rsquo;, &lsquo;Second Thoughts&rsquo;, &lsquo;Sisters&rsquo;, </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>निकिता सिंह</strong><span style=\"font-weight: 400;\">- &lsquo;Someone Like You&rsquo;, &lsquo;The Promise&rsquo;, &lsquo;Letters to My Ex&rsquo;। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>जूडी बालन</strong><span style=\"font-weight: 400;\">-&nbsp; &lsquo;Two Fates&rsquo;, &lsquo;Half Boyfriend&rsquo;, &lsquo;I Have a Theory about That&rsquo;</span></span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> पुस्तक के लेखक हैं।</span></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "18",
                    question_en: "<p>85. <span style=\"font-family: Cambria Math;\">Which state does </span><span style=\"font-family: Cambria Math;\">Brahmaputra river</span><span style=\"font-family: Cambria Math;\"> enters when it takes U turn at </span><span style=\"font-family: Cambria Math;\">Namcha</span><span style=\"font-family: Cambria Math;\"> Barwa?</span></p>",
                    question_hi: "<p>85. <span style=\"font-weight: 400;\">नामचा बरवा में यू - टर्न लेने पर ब्रह्मपुत्र नदी किस राज्य में प्रवेश करती है?</span></p>",
                    options_en: ["<p>Mizoram</p>", "<p>Assam</p>", 
                                "<p>Nagaland</p>", "<p>Arunachal Pradesh</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">मिजोरम</span></p>", "<p><span style=\"font-weight: 400;\">असम</span></p>",
                                "<p><span style=\"font-weight: 400;\">नागालैंड</span></p>", "<p><span style=\"font-weight: 400;\">अरुणाचल प्रदेश</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">85.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp; &nbsp;</span><strong>The Tsangpo</strong><span style=\"font-weight: 400;\"> (Brahmaputra River)&nbsp; takes a U-turn on reaching the Namcha Barwa and enters India in Arunachal Pradesh through a gorge. The catchment area of the river falls in four countries (Tibet, Bhutan, India, and Bangladesh). </span><strong>Majuli Island </strong><span style=\"font-weight: 400;\">(the oldest and largest inhabited riverine island in the world) is made by this river.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">85.(</span><span style=\"font-family: Cambria Math;\">d) <strong>&nbsp;त्संगपो</strong><span style=\"font-weight: 400;\"> (ब्रह्मपुत्र नदी) नामचा बरवा पहुंचने पर यू-टर्न लेती है और एक कण्ठ के माध्यम से अरुणाचल प्रदेश में भारत में प्रवेश करती है। नदी का जलग्रहण क्षेत्र चार देशों (तिब्बत, भूटान, भारत और बांग्लादेश) में पड़ता है।</span><strong> </strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>माजुली द्वीप</strong><span style=\"font-weight: 400;\"> (दुनिया का सबसे पुराना और सबसे बड़ा बसा हुआ नदी द्वीप) इसी नदी द्वारा बनाया गया है।</span></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "18",
                    question_en: "<p>86. <span style=\"font-family: Cambria Math;\">Who shared the Nobel Prize in Physiology or Medicine 2005 with Barry J Marshall for the discovery of the Helicobacter pylori bacterium and its role in gastritis and peptic ulcer disease?</span></p>",
                    question_hi: "<p>86. <span style=\"font-weight: 400;\">हेलिकोबैक्टर पाइलोरी जीवाणु की खोज और गैस्ट्राइटिस और पेप्टिक अल्सर रोग में इसकी भूमिका के अध्ध्यन के लिए फिजियोलॉजी या मेडिसिन 2005 में नोबेल पुरस्कार बैरी जे मार्शल के साथ किसे दिया गया था?</span></p>",
                    options_en: ["<p>Paul Lauterbur</p>", "<p>J Robin Warren</p>", 
                                "<p>Richard Axel</p>", "<p>Oliver Smithies</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">पॉल लॉटरबुर</span></p>", "<p><span style=\"font-weight: 400;\">जे रॉबिन वारेन</span></p>",
                                "<p><span style=\"font-weight: 400;\">रिचर्ड एक्सेल</span></p>", "<p><span style=\"font-weight: 400;\">ओलिवर स्मिथीज</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">86.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&nbsp;The Nobel Prize in <strong>Physiology or Medicine 2005</strong> was awarded jointly to </span><strong>Barry J. Marshall and J. Robin Warren. Paul Lauterbur</strong><span style=\"font-weight: 400;\">, </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Peter Mansfield </strong><span style=\"font-weight: 400;\">(2013; Magnetic resonance imaging). </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Richard Axel</strong><span style=\"font-weight: 400;\"> and Linda B. Buck (2004; olfactory system).</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong> Mario R. Capecchi, Sir Martin J. Evans and</strong> </span><strong>Oliver Smithies </strong><span style=\"font-weight: 400;\">(2007; gene modifications in mice).</span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">86.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">2005 में फिजियोलॉजी या मेडिसिन में नोबेल पुरस्कार </span><strong>बैरी जे मार्शल और जे रॉबिन वॉरेन</strong><span style=\"font-weight: 400;\"> को संयुक्त रूप से प्रदान किया गया था। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>पॉल लॉटरबर, पीटर मैन्सफील्ड</strong> (2013; चुंबकीय अनुनाद इमेजिंग)।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> </span><strong>रिचर्ड एक्सल</strong><span style=\"font-weight: 400;\"> <strong>और लिंडा बी. बक</strong> (2004; घ्राण प्रणाली)। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>मारियो आर. कैपेची, सर मार्टिन जे. इवांस और ओलिवर स्मिथीज़</strong><span style=\"font-weight: 400;\"> (2007; चूहों में जीन संशोधन)।</span></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "18",
                    question_en: "<p>87. <span style=\"font-family: Cambria Math;\">Kalamandalam</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Kalyanikutty</span><span style=\"font-family: Cambria Math;\"> Amma was an Indian Classical Dancer of _________ dance form.</span></p>",
                    question_hi: "<p>87.&nbsp; <span style=\"font-weight: 400;\">कलामंडलम कल्याणीकुट्टी अम्मा _________ नृत्य रूप की एक भारतीय शास्त्रीय नृत्यांगना थीं।</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Sattriya</span></p>", "<p><span style=\"font-family: Cambria Math;\">Mohiniyattam</span></p>", 
                                "<p>Odissi</p>", "<p>Kathak</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">सत्त्रिया</span></p>", "<p><span style=\"font-weight: 400;\">मोहिनीअट्टम</span></p>",
                                "<p><span style=\"font-weight: 400;\">ओडिसी</span></p>", "<p><span style=\"font-weight: 400;\">कथक</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">87.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">Kalamandalam Kallyanikutty Amma was a </span><strong>Mohiniyattam</strong><span style=\"font-weight: 400;\"> danseuse from Kerala. Her famous book- <strong>\"Mohiniyattam - History and Dance Structure\"</strong>.&nbsp;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong><span style=\"font-weight: 400;\"><strong>Some other famous</strong> </span>Mohiniyattam dancers :</strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&nbsp;Deepti Omchery Bhalla, Gopika Varma, Kalamandalam Kshemavathy, Kalamandalam Leelamma, Kalamandalam Satyabhama, Kalamandalam Sugandhi, Vimala Menon and V. K. Hymavathy. </span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">87.(</span><span style=\"font-family: Cambria Math;\">b) <span style=\"font-weight: 400;\">कलामंडलम कल्याणीकुट्टी अम्मा केरल की </span><strong>मोहिनीअट्टम</strong><span style=\"font-weight: 400;\"> नृत्यांगना थीं। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>उनकी प्रसिद्ध पुस्तक</strong>- \"Mohiniyattam - History and Dance Structure\"। </span></span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">कुछ अन्य प्रसिद्ध मोहिनीअट्टम नर्तकियों में:-</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> दीप्ति ओमचेरी भल्ला, गोपिका वर्मा, कलामंडलम क्षेमवती, कलामंडलम लीलम्मा, कलामंडलम सत्यभामा, कलामंडलम सुगंधी, विमला मेनन और वी के ह्यमवती शामिल है।</span></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "18",
                    question_en: "<p>88. <span style=\"font-family: Cambria Math;\">Which Indian dancer was awarded the French Palme D\'or by the French Government in 1977?</span></p>",
                    question_hi: "<p>88.&nbsp; <span style=\"font-weight: 400;\">किस भारतीय नर्तक को 1977 में फ्रांसीसी सरकार द्वारा फ्रेंच पाल्मे डी\'ओर से सम्मानित किया गया था?</span></p>",
                    options_en: ["<p>Chitra <span style=\"font-family: Cambria Math;\">Visweswaran</span></p>", "<p><span style=\"font-family: Cambria Math;\">Oopali</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Operajita</span><span style=\"font-family: Cambria Math;\">.</span></p>", 
                                "<p>Mallika Sarabhai</p>", "<p>Kavya <span style=\"font-family: Cambria Math;\">Madhavan</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">चित्रा विस्वेस्वरन </span></p>", "<p><span style=\"font-weight: 400;\">ऊपाली ओपेराजिता</span></p>",
                                "<p><span style=\"font-weight: 400;\">मल्लिका साराभाई</span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;काव्या माधवान</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">88.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Mallika Sarabhai</strong><span style=\"font-weight: 400;\"> is a Kuchipudi and Bharatanatyam from Gujarat.&nbsp; Her writings include Shakti: The Power of Women, Sita&rsquo;s Daughters, Itan Kahani, Aspiration, Ganga, and Surya. </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>Chitra Visweswaran (</strong>Bharatanatyam dancer), </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>Oopali Operajita</strong> (classical Odissi and Bharatanatyam dancer), and </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>Kavya Madhavan </strong>(Bharatanatyam). </span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">88.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp; &nbsp;<strong>मल्लिका साराभाई</strong><span style=\"font-weight: 400;\"> गुजरात की कुचिपुड़ी और भरतनाट्यम नर्तक हैं। उनके लेखन में The Power of Women, Sita&rsquo;s Daughters, Itan Kahani, Aspiration, Ganga, and Surya शामिल हैं।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong> चित्रा विश्वेश्वरन</strong> (भरतनाट्यम नर्तकी),</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong> ऊपाली ओपेराजिता </strong>(शास्त्रीय ओडिसी और भरतनाट्यम नर्तकी), और</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong> काव्या माधवन </strong>(भरतनाट्यम) नर्तक हैं।</span></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "18",
                    question_en: "<p>89. <span style=\"font-family: Cambria Math;\">Who received the Noble Prize in </span><span style=\"font-family: Cambria Math;\">1906,for</span><span style=\"font-family: Cambria Math;\"> recognition of the great merits of his theoretical and experimental investigations on the conduction of electricity by gases?</span></p>",
                    question_hi: "<p>89. <span style=\"font-weight: 400;\">1906 में गैसों द्वारा बिजली के संचालन पर उनके सैद्धांतिक और प्रायोगिक जांच के महान गुणों की पहचान के लिए नोबेल पुरस्कार किसे मिला?</span></p>",
                    options_en: ["<p>Andre-Marie Ampere</p>", "<p>Sir JJ Thomson</p>", 
                                "<p>Albert Einstein</p>", "<p>Alessandro Volta</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">आंद्रे-मैरी एम्पीयर</span></p>", "<p><span style=\"font-weight: 400;\">सर जे जे थॉमसन</span></p>",
                                "<p><span style=\"font-weight: 400;\">अल्बर्ट आइंस्टीन</span></p>", "<p><span style=\"font-weight: 400;\">एलेसेंड्रो वोल्टा </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">89.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&nbsp;The Nobel Prize in Physics 1906 was awarded to </span><strong>Joseph John Thomson. Andre-Marie Ampere</strong><span style=\"font-weight: 400;\"> (named the science of electrodynamics, now known as electromagnetism), </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Albert Einstein</strong><span style=\"font-weight: 400;\"> (1921; Photoelectric effect), and</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> </span><strong>Alessandro Volta</strong><span style=\"font-weight: 400;\"> (invent the electric battery). </span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">89.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">1906 में भौतिकी का नोबेल पुरस्कार </span><strong>जोसेफ जॉन थॉमसन</strong><span style=\"font-weight: 400;\"> को दिया गया था। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>आंद्रे-मैरी एम्पीयर </strong><span style=\"font-weight: 400;\">(इलेक्ट्रोडायनामिक्स के विज्ञान का नाम, जिसे अब विद्युत चुंबकत्व के रूप में जाना जाता है), </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>अल्बर्ट आइंस्टीन</strong><span style=\"font-weight: 400;\"> (1921; फोटोइलेक्ट्रिक प्रभाव), और</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> </span><strong>एलेसेंड्रो वोल्टा</strong><span style=\"font-weight: 400;\"> (इलेक्ट्रिक बैटरी का आविष्कार)।</span></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "18",
                    question_en: "<p>90. <span style=\"font-family: Cambria Math;\">Who among the following has written &lsquo;Sangeet Kala Prakash&rsquo;?</span></p>",
                    question_hi: "<p>90. <span style=\"font-weight: 400;\">निम्नलिखित में से किसने &lsquo;Sangeet Kala Prakash&rsquo; लिखा है?</span></p>",
                    options_en: ["<p>Prabhu <span style=\"font-family: Cambria Math;\">Atre</span></p>", "<p>Pandit <span style=\"font-family: Cambria Math;\">Jasraj</span></p>", 
                                "<p><span style=\"font-family: Cambria Math;\">Ramakrishnabuva</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Vaze</span></p>", "<p>Pandit Kumar <span style=\"font-family: Cambria Math;\">Gandharva</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">प्रभु अत्रे</span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;पंडित जसराज</span></p>",
                                "<p><span style=\"font-weight: 400;\">&nbsp;रामकृष्णबुवा वाज़े</span></p>", "<p><span style=\"font-weight: 400;\">पंडित कुमार गंधर्व</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">90.(</span><span style=\"font-family: Cambria Math;\">c) <strong>&nbsp;</strong><span style=\"font-weight: 400;\"><strong>&lsquo;Sangeet Kala Prakash&rsquo;</strong> was written by </span><strong>Ramakrishnabuva Vaze</strong><span style=\"font-weight: 400;\">. He was a Hindustani classical musician of the Gwalior tradition. </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Pandit Jasraj </strong><span style=\"font-weight: 400;\">(Meewati Gharana)</span><strong>, </strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Pandit Kumar Gandharva</strong><span style=\"font-weight: 400;\"> (Singing Emptiness: Kumar Gandharva Performs the Poetry of Kabir), and </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Prabha Atre</strong><span style=\"font-weight: 400;\"> (Kirana Gharana).</span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">90.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp; &nbsp;<span style=\"font-weight: 400;\">&lsquo;Sangeet Kala Prakash&rsquo; <strong>रामकृष्णबुवा वेज़ </strong>द्वारा लिखा गया था। वे ग्वालियर परंपरा के हिंदुस्तानी शास्त्रीय संगीतकार थे। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>पंडित जसराज</strong> (मीवती घराना),</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong> पंडित कुमार गंधर्व </strong>(गायन खालीपन: कुमार गंधर्व कबीर की कविता का प्रदर्शन करते हैं), और</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong> प्रभा अत्रे</strong> (किराना घराना)।</span></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "18",
                    question_en: "<p>91. <span style=\"font-family: Cambria Math;\">How many seats were reserved for the Scheduled Tribes in Lok Sabha for the 2019 general election?</span></p>",
                    question_hi: "<p>91. <span style=\"font-weight: 400;\">2019 के आम चुनाव के लिए लोकसभा में अनुसूचित जनजातियों के लिए कितनी सीटें आरक्षित की गईं है?</span></p>",
                    options_en: ["<p>46</p>", "<p>43</p>", 
                                "<p>47</p>", "<p>45</p>"],
                    options_hi: ["<p>46</p>", "<p>43</p>",
                                "<p>47</p>", "<p>45</p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">91.(</span><span style=\"font-family: Cambria Math;\">c)</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">As per the order issued by the <strong>Delimitation Commission</strong> in 2008, 412 are general, 84 seats are reserved for Scheduled Castes, and 47 seats for the Scheduled Tribes. Max ST seats (Madhya Pradesh). After every census, the Parliament will enact a Delimitation Act, as per </span><strong>Article 82. </strong><span style=\"font-weight: 400;\">&nbsp;Four Delimitation Commissions till date 1952, 1963, 1972, and 2002.</span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">91.(</span><span style=\"font-family: Cambria Math;\">c)</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\"><strong>2008 में परिसीमन आयोग द्वारा जारी आदेश के अनुसार</strong>, </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">412 सीटें सामान्य हैं, 84 सीटें अनुसूचित जाति के लिए और <strong>47 सीटें अनुसूचित जनजाति </strong>के लिए आरक्षित हैं।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> अधिकतम अनुसूचित जाति सीटें (मध्य प्रदेश)।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> प्रत्येक जनगणना के बाद, संसद <strong>अनुच्छेद 82 के अनुसार एक परिसीमन अधिनियम</strong> बनाएगी। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">1952, 1963, 1972 और 2002 तक चार परिसीमन आयोग। </span></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "18",
                    question_en: "<p>92. <span style=\"font-family: Cambria Math;\">What is the number of players playing in a team in a hockey match?</span></p>",
                    question_hi: "<p>92. <span style=\"font-weight: 400;\">हॉकी मैच में एक टीम में खेलने वाले खिलाड़ियों की संख्या कितनी होती है?</span></p>",
                    options_en: ["<p>12</p>", "<p>9</p>", 
                                "<p>10</p>", "<p>11</p>"],
                    options_hi: ["<p>12</p>", "<p>9</p>",
                                "<p>10</p>", "<p>11</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">92.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp; <span style=\"font-weight: 400;\">There are<strong> 11 players</strong> on the field from each team at any one time. However, a team can have up to 16 players kitted up and playing in the 75-minute long game. It is the 2nd most played sport in the world (after football/soccer). </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">India has won the most Olympic Gold medals with a total of 8, winning 6 consecutive Gold between 1928-1956. </span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">92.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp; <span style=\"font-weight: 400;\">प्रत्येक टीम से एक समय में 11 खिलाड़ी मैदान पर होते हैं। हालांकि, एक टीम में अधिकतम 16 खिलाड़ी शामिल हो सकते हैं और 75 मिनट के लंबे खेल में खेल सकते हैं। यह दुनिया में दूसरा सबसे अधिक खेला जाने वाला खेल है (फुटबॉल/सॉकर के बाद)। <strong>भारत ने 1928-1956 के बीच लगातार 6 स्वर्ण जीतकर, कुल 8 के साथ सबसे अधिक ओलंपिक स्वर्ण पदक जीते हैं।</strong></span></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "18",
                    question_en: "<p>93. <span style=\"font-family: Cambria Math;\">The name of which element is derived from an Anglo-Saxon word and its symbol comes from the Latin word &lsquo;</span><span style=\"font-family: Cambria Math;\">Aurum</span><span style=\"font-family: Cambria Math;\">&rsquo;?</span></p>",
                    question_hi: "<p>93. <span style=\"font-weight: 400;\">किस तत्व का नाम एंग्लो-सैक्सन (Anglo-Saxon) शब्द से लिया गया है, और इसका प्रतीक लैटिन शब्द \'ऑरम\' (&lsquo;Aurum&rsquo;) से आया है?</span></p>",
                    options_en: ["<p>Argon</p>", "<p>Gold</p>", 
                                "<p>Aluminium</p>", "<p>Silver</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">आर्गन</span></p>", "<p><span style=\"font-weight: 400;\">सोना</span></p>",
                                "<p><span style=\"font-weight: 400;\">एल्यूमिनियम</span></p>", "<p><span style=\"font-weight: 400;\">चांदी</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">93.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <strong>Gold</strong><span style=\"font-weight: 400;\"> is element <strong>79</strong> and its symbol is<strong> Au</strong> derived from&nbsp; Anglo Saxon word. The top producers of Gold in the </span><strong>world</strong><span style=\"font-weight: 400;\"> are China, Australia, Russia, and the United States. The largest producer in India (Karnataka). The largest resources in terms of </span><strong>gold ore </strong><span style=\"font-weight: 400;\">(primary) are located in Bihar, Rajasthan, Karnataka, West Bengal, Andhra Pradesh, and Jharkhand.</span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">93.(</span><span style=\"font-family: Cambria Math;\">b) <strong>सोना तत्व</strong><span style=\"font-weight: 400;\"> 79 है और इसका प्रतीक Au है जो एंग्लो सैक्सन (Anglo Saxon) शब्द से लिया गया है।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> दुनिया में सोने के शीर्ष उत्पादक चीन, ऑस्ट्रेलिया, रूस और संयुक्त राज्य अमेरिका हैं। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>भारत में सबसे बड़ा उत्पादक (कर्नाटक)</strong>। स्वर्ण अयस्क (प्राथमिक) के मामले में सबसे बड़े संसाधन बिहार, राजस्थान, कर्नाटक, पश्चिम बंगाल, आंध्र प्रदेश और झारखंड में स्थित हैं।</span></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "18",
                    question_en: "<p>94. <span style=\"font-family: Cambria Math;\">Sartaj Khan, Sarwar Khan, Swaroop Khan and </span><span style=\"font-family: Cambria Math;\">Mame</span><span style=\"font-family: Cambria Math;\"> Khan are famous for which of the following?</span></p>",
                    question_hi: "<p>94. <span style=\"font-weight: 400;\">सरताज खान, सरवर खान, स्वरूप खान और मामे खान निम्नलिखित में से किसके लिए प्रसिद्ध हैं?</span></p>",
                    options_en: ["<p>Rajasthani folk music</p>", "<p>Hindustani classical vocal</p>", 
                                "<p>Playing percussion instruments</p>", "<p>Playing string instruments</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">राजस्थानी लोक संगीत</span></p>", "<p><span style=\"font-weight: 400;\">हिंदुस्तानी शास्त्रीय गायन</span></p>",
                                "<p><span style=\"font-weight: 400;\">ताल वाद्य वादन </span></p>", "<p><span style=\"font-weight: 400;\">तत वाद्य वादन</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">94.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Rajasthan</strong><span style=\"font-weight: 400;\"> has a diverse collection of musician castes, including langas, sapera, bhopa, and Manganiar. </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>The most famous Rajasthani Maand singer</strong> is Allah Jilai Bai (Bikaner Gharana). Other Rajasthani folk singers are Ila Arun, Rapperiya Baalam, and Rajnigandha Shekhawat. </span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">94.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp; &nbsp;<strong>राजस्थान</strong><span style=\"font-weight: 400;\"> में लंगा, सपेरा, भोपा और मंगनियार सहित संगीतकार जातियों का विविध संग्रह है।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong> सबसे प्रसिद्ध राजस्थानी मांड गायिका </strong>अल्लाह जिलाई बाई (बीकानेर घराना) हैं।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> अन्य <strong>राजस्थानी लोक गायक</strong> इला अरुण, रैपेरिया बालम और रजनीगंधा शेखावत हैं।</span></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "18",
                    question_en: "<p>95. <span style=\"font-family: Cambria Math;\">Famous Folk dancer </span><span style=\"font-family: Cambria Math;\">Gulabo</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Sapera</span><span style=\"font-family: Cambria Math;\"> was conferred Padma Shri award for her contribution to which of the following dance forms?</span></p>",
                    question_hi: "<p>95. <span style=\"font-weight: 400;\">प्रसिद्ध लोक नृत्यांगना गुलाबो सपेरा को निम्नलिखित में से किस नृत्य शैली में उनके योगदान के लिए पद्म श्री पुरस्कार से सम्मानित किया गया?</span></p>",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Terah</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Tali</span></p>", "<p><span style=\"font-family: Cambria Math;\">Kalbeliya</span></p>", 
                                "<p><span style=\"font-family: Cambria Math;\">Ghoomar</span></p>", "<p><span style=\"font-family: Cambria Math;\">Bhavai</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">तेरह तालि</span></p>", "<p><span style=\"font-weight: 400;\">कालबेलिया</span></p>",
                                "<p><span style=\"font-weight: 400;\">घूमर</span></p>", "<p><span style=\"font-weight: 400;\">भवई</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">95.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>Gulabo Sapera </strong>(Rajasthan) was conferred the Padma Shri award in 2016 for her </span><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">contribution to the </span><strong>Kalbeliya dance</strong><span style=\"font-weight: 400;\"> form (folk dance of the Rajasthan well known by other names like \'Sapera Dance\' or \'Snake Charmer Dance\').<strong> </strong></span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>Cultural</strong> </span><strong>Folk Dances of Rajasthan </strong><span style=\"font-weight: 400;\">include Chakri, Chari, Drum, Fire, Gair, Gawari, Ghoomar, etc. </span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">95.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> &nbsp;<span style=\"font-weight: 400;\">गुलाबो सपेरा (राजस्थान) को 2016 में </span><strong>कालबेलिया नृत्य</strong><span style=\"font-weight: 400;\"> रूप (राजस्थान के लोक नृत्य को \'सपेरा नृत्य\' या \'सांप चार्मर नृत्य\' जैसे अन्य नामों से जाना जाता है) में उनके योगदान के लिए पद्म श्री पुरस्कार से सम्मानित किया गया था।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"> </span><strong>राजस्थान के सांस्कृतिक लोक नृत्यों</strong><span style=\"font-weight: 400;\"> में चकरी, चारी, ढोल, अग्नि, गैर, गवरी, घूमर आदि शामिल हैं।</span></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "18",
                    question_en: "<p>96.<span style=\"font-family: Cambria Math;\"> Which type of biome is located in Eastern North America, Western Europe, and Northeast Asia?</span></p>",
                    question_hi: "<p>96.<span style=\"font-family: Cambria Math;\"> &nbsp;<span style=\"font-weight: 400;\">पूर्वी उत्तरी अमेरिका, पश्चिमी यूरोप और पूर्वोत्तर एशिया में किस प्रकार का बायोम स्थित है?</span></span></p>",
                    options_en: ["<p>Tropical Rainforest Biomes</p>", "<p>Coniferous Forest Biomes</p>", 
                                "<p>Aquatic Biomes</p>", "<p>Deciduous Forest Biomes</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">उष्णकटिबंधीय वर्षावन बायोमे </span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;शंकुधारी वन बायोमे </span></p>",
                                "<p><span style=\"font-weight: 400;\">जलीय बायोम</span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;पर्णपाती वन बायोमे</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">96.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Temperate deciduous forest:</strong><span style=\"font-weight: 400;\"> This biome experiences all four seasons, these forests are divided into five zones based on the heights of the trees, The soil is particularly fertile and nutrient-rich and is</span><strong> located in</strong><span style=\"font-weight: 400;\"> North America (Canada, the United States, and Mexico), Europe, and western regions of Asia (Japan, China, North Korea, South Korea, and parts of Russia).</span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">96.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><strong>&nbsp;पर्णपाती वन बायोमे </strong><span style=\"font-weight: 400;\"><strong>:</strong>&nbsp;यह बायोम सभी चार मौसमों का अनुभव करता है, इन वनों को पेड़ों की ऊंचाई के आधार पर पांच क्षेत्रों में विभाजित किया गया है, मिट्टी विशेष रूप से उपजाऊ और पोषक तत्वों से भरपूर है और उत्तरी अमेरिका (कनाडा, संयुक्त राज्य अमेरिका और मैक्सिको) में स्थित है। ), यूरोप और एशिया के पश्चिमी क्षेत्र (जापान, चीन, उत्तर कोरिया, दक्षिण कोरिया और रूस के कुछ हिस्से)।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "18",
                    question_en: "<p>97. <span style=\"font-family: Cambria Math;\">Which of the following mountains was formed when molten rock from the depths of the earth rose from the crust and piled up on its own?</span></p>",
                    question_hi: "<p>97.&nbsp; <span style=\"font-weight: 400;\">निम्नलिखित में से किस पर्वत का निर्माण तब हुआ जब पृथ्वी की गहराई से पिघली हुई चट्टान क्रस्ट (भूपर्पटी) से ऊपर उठी और जमकर ढेर के रूप में हो गई?</span></p>",
                    options_en: ["<p>Mount Kilimanjaro in Africa</p>", "<p>Rockies in North America</p>", 
                                "<p><span style=\"font-family: Cambria Math;\">Ural mountain</span><span style=\"font-family: Cambria Math;\"> in Russia</span></p>", "<p>Alps in Europe</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">अफ्रीका में माउंट किलिमंजारो</span></p>", "<p><span style=\"font-weight: 400;\">उत्तरी अमेरिका में रॉकीज</span></p>",
                                "<p><span style=\"font-weight: 400;\">रूस में यूराल पर्वत</span></p>", "<p><span style=\"font-weight: 400;\">यूरोप में आल्प्स</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">97.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;<strong> Mount Kilimanjaro</strong><span style=\"font-weight: 400;\"> in Africa. It is the Dormant Volcano in Tanzania.</span><strong> Types of Mountains </strong><span style=\"font-weight: 400;\">-</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>Volcanic Mountains</strong> (Vesuvius and Mount Fuji),</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong> Fold Mountains </strong>(Himalayas, Andes, and Alps),&nbsp; </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>Block Mountains</strong> (The mountains of Satpura and Vindhya Range), </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>Residual Mountains</strong> (Nilgiris, the Parasnath, the Girnar), </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>Dome Mountains </strong>(Kedar Dome peak, Uttarakhand).</span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">97.(</span><span style=\"font-family: Cambria Math;\">a)</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">अफ्रीका में </span><strong>माउंट किलिमंजारो</strong><span style=\"font-weight: 400;\">- यह तंजानिया में सुप्त ज्वालामुखी है।</span></span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\"> पहाड़ों के प्रकार:-</span></strong></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>ज्वालामुखी पर्वत</strong> (वेसुवियस और माउंट फ़ूजी), <strong>वलित पर्वत</strong> (हिमालय, एंडीज और आल्प्स), <strong>अवरोधी पर्वत</strong> (सतपुड़ा और विंध्य रेंज के पहाड़), <strong>अवशिष्ट पर्वत</strong> (नीलगिरी, पारसनाथ, गिरनार), <strong>गुंबद पर्वत</strong> (केदार डोम पीक, उत्तराखंड)।</span></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "18",
                    question_en: "<p>98. <span style=\"font-family: Cambria Math;\">The Guru of Kuchipudi dance form &lsquo;Guru </span><span style=\"font-family: Cambria Math;\">Vempati</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Chinna</span><span style=\"font-family: Cambria Math;\"> Satyam&rsquo; who was instrumental in getting classical status to Kuchipudi was conferred by which of the following awards in 1998?</span></p>",
                    question_hi: "<p>98.&nbsp; <span style=\"font-weight: 400;\">कुचिपुड़ी नृत्य के गुरु \'गुरु वेम्पति चिन्ना सत्यम\', जिन्होंने कुचिपुड़ी को शास्त्रीय दर्जा दिलाने में महत्वपूर्ण भूमिका निभाई थी, उन्हें 1998 में निम्नलिखित में से किस पुरस्कार से सम्मानित किया गया था?</span></p>",
                    options_en: ["<p>Padma Shri</p>", "<p>Padma Vibhushan</p>", 
                                "<p>Sangeet Natak <span style=\"font-family: Cambria Math;\">Akademi</span><span style=\"font-family: Cambria Math;\"> Award</span></p>", "<p>Padma Bhushan</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">पद्म श्री</span></p>", "<p><span style=\"font-weight: 400;\">&nbsp;पद्म विभूषण</span></p>",
                                "<p><span style=\"font-weight: 400;\">संगीत नाटक अकादमी पुरस्कार</span></p>", "<p><span style=\"font-weight: 400;\">पद्म भूषण</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">98.(</span><span style=\"font-family: Cambria Math;\">d) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Vempati Chinna Satyam</strong><span style=\"font-weight: 400;\"> (<strong>guru of the Kuchipudi dance form)</strong> was conferred with Padma Bhushan (1998), Sangeet Natak Akademi Fellowship (1967), Raja Lakshmi award, Central Sangeet Naatak Akademi award, Kalaimamani and many more.</span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">98.(</span><span style=\"font-family: Cambria Math;\">d) </span></p>\r\n<p><strong>वेम्पति चिन्ना सत्यम</strong><span style=\"font-weight: 400;\"> (कुचिपुड़ी नृत्य रूप के गुरु) को पद्म भूषण (1998), संगीत नाटक अकादमी फैलोशिप (1967), राजा लक्ष्मी पुरस्कार, केंद्रीय संगीत नाटक अकादमी पुरस्कार, कलाईममणि और कई अन्य से सम्मानित किया गया था। </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "18",
                    question_en: "<p>99. <span style=\"font-family: Cambria Math;\">Who among the following won Arjuna Award 2021 in Para Table Tennis discipline?</span></p>",
                    question_hi: "<p>99. <span style=\"font-weight: 400;\">निम्नलिखित में से किसने पैरा टेबल टेनिस में अर्जुन पुरस्कार 2021 जीता?</span></p>",
                    options_en: ["<p>Achanta Sharath Kamal</p>", "<p><span style=\"font-family: Cambria Math;\">Manika</span><span style=\"font-family: Cambria Math;\"> Batra</span></p>", 
                                "<p><span style=\"font-family: Cambria Math;\">Bhavina</span><span style=\"font-family: Cambria Math;\"> Patel</span></p>", "<p>Anusha <span style=\"font-family: Cambria Math;\">Kutumbale</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">अचंता शरथ कमल</span></p>", "<p><span style=\"font-weight: 400;\">मनिका बत्रा</span></p>",
                                "<p><span style=\"font-weight: 400;\">भाविना पटेल</span></p>", "<p><span style=\"font-weight: 400;\">अनुषा कुटुम्बले</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">99.(</span><span style=\"font-family: Cambria Math;\">c) <strong>&nbsp; &nbsp;</strong></span><span style=\"font-family: Cambria Math;\"><strong>Bhavina</strong><span style=\"font-weight: 400;\"> Hasmukhbhai Patel is an Indian parathlete and table tennis player from Gujarat who was conferred with Arjuna Award for Para-Table Tennis in 2021.</span><strong> </strong></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Achanta Sharath Kamal</strong><span style=\"font-weight: 400;\"> (first Indian table tennis player ever to become nine times Senior National Champion). </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong>Manika Batra</strong> (Major Dhyan Chand Khel Ratna (2020). </span></span></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">99.(</span><span style=\"font-family: Cambria Math;\">c) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>भाविना हसमुखभाई पटेल</strong><span style=\"font-weight: 400;\"> गुजरात की एक भारतीय पैराथलीट और टेबल टेनिस खिलाड़ी हैं, जिन्हें 2021 में पैरा-टेबल टेनिस के लिए अर्जुन पुरस्कार से सम्मानित किया गया था। </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>अचंता शरथ कमल </strong><span style=\"font-weight: 400;\">(नौ बार सीनियर नेशनल चैंपियन बनने वाली पहली भारतीय टेबल टेनिस खिलाड़ी)।</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><strong> मनिका बत्रा</strong> (मेजर ध्यानचंद खेल रत्न (2020) से सम्मानित।</span></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <span style=\"font-family: Cambria Math;\">Which metal sulphate, composed of potassium, aluminium, and sulphate ions in the ratio </span><span style=\"font-family: Cambria Math;\">1 :</span><span style=\"font-family: Cambria Math;\"> 1 : 2, plays a role as a flame retardant, a mordant and an astringent?</span></p>",
                    question_hi: "<p>100.&nbsp; <span style=\"font-weight: 400;\">1: 1: 2 के अनुपात में पोटेशियम, एल्यूमीनियम और सल्फेट आयनों से बना कौन सा धातु सल्फेट, एक मंदक रिट्राड्रेटतीक्ष्ण (मोरडेंट), और स्तभंक (एस्ट्रिंजेंट) की भूमिका निभाता है?</span></p>",
                    options_en: ["<p>Gypsum</p>", "<p>Potash alum</p>", 
                                "<p>Epsom salts</p>", "<p>Celestite</p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">जिप्सम</span></p>", "<p><span style=\"font-weight: 400;\">पोटाश एलम </span></p>",
                                "<p><span style=\"font-weight: 400;\">एप्सम लवण</span></p>", "<p><span style=\"font-weight: 400;\">सेलेस्टाइट</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">100.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp; &nbsp;<strong>Potash alum</strong><span style=\"font-weight: 400;\"> (Potassium aluminum sulfate)&nbsp; is a metal sulfate composed of potassium, aluminum, and sulphate ions in the ratio of 1:1:2. The chemical formula of potash alum is </span><strong>K</strong><strong>2</strong><strong>SO</strong><strong>4</strong><strong>.Al</strong><strong>2</strong><strong>(SO</strong><strong>4</strong><strong>)</strong><strong>3</strong><strong>.24H</strong><strong>2</strong><strong>O</strong><span style=\"font-weight: 400;\">. It is also commonly referred to as</span><strong> &lsquo;fitkari.</strong><span style=\"font-weight: 400;\">&rsquo; Extracted from a mineral called </span><strong>alunite. </strong><span style=\"font-weight: 400;\">It is used for the purification of impure water, stops bleeding, and is mordant for the dyeing industry, leather tanning, fireproof textiles, and baking powder.</span></span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">100.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span></p>\r\n<p><strong>पोटाश फिटकरी </strong><span style=\"font-weight: 400;\">(पोटेशियम एल्युमिनियम सल्फेट) एक धातु सल्फेट है जो 1: 1: 2 के अनुपात में पोटेशियम, एल्यूमीनियम और सल्फेट आयनों से बना होता है। पोटाश फिटकरी का रासायनिक सूत्र </span><strong>K</strong><strong>2</strong><strong>SO</strong><strong>4</strong><strong>.Al</strong><strong>2</strong><strong>(SO</strong><strong>4</strong><strong>)</strong><strong>3</strong><strong>.24H</strong><strong>2</strong><strong>O</strong><span style=\"font-weight: 400;\"> है। इसे आमतौर पर \'</span><strong>फिटकारी\'</strong><span style=\"font-weight: 400;\"> भी कहा जाता है। यह </span><strong>एलुनाइट</strong><span style=\"font-weight: 400;\"> नामक खनिज से निकाला जाता है। </span><span style=\"font-weight: 400;\">इसका उपयोग अशुद्ध पानी की शुद्धि के लिए किया जाता है, यह खून बहना बंद कर देता है, और रंगाई उद्योग, चमड़ा पकान, अग्निरोधक वस्त्र और बेकिंग पाउडर के लिए उपयोगी है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>