<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 26</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">26</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 25,
                end: 25
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the number that can replace the question mark (?) in the following series.<br>2,5,11,20,32,47,65,?</p>",
                    question_hi: "<p>1. उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकती है।<br>2,5,11,20,32,47,65,?</p>",
                    options_en: ["<p>77</p>", "<p>73</p>", 
                                "<p>86</p>", "<p>72</p>"],
                    options_hi: ["<p>77</p>", "<p>73</p>",
                                "<p>86</p>", "<p>72</p>"],
                    solution_en: "<p>1.(c)<br>2 + 3 = 5<br>5 + 6 = 11<br>11 + 9 = 20<br>20 + 12 = 32<br>32 + 15 = 47<br>47 + 18 = 65<br>65 + 21 = 86</p>",
                    solution_hi: "<p>1.(c)<br>2 + 3 = 5<br>5 + 6 = 11<br>11 + 9 = 20<br>20 + 12 = 32<br>32 + 15 = 47<br>47 + 18 = 65<br>65 + 21 = 86</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "2. Four words have been given , out of which three are alike in some manner and one is different, Select the odd one.",
                    question_hi: "2. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है, विषम का चयन करें।",
                    options_en: [" Asian Games", " ICC World Cup", 
                                " Commonwealth Games", " Olympic Games<br /> "],
                    options_hi: [" एशियाई खेल", " आईसीसी विश्व कप",
                                " राष्ट्रमंडल खेल", " ओलंपिक खेल"],
                    solution_en: "2.(b) Four words have been given , out of which three(Asian Games, Commonwealth Games, Olympic Games) are alike in some manner and  ICC World Cup is different.",
                    solution_hi: "2.(b) चार शब्द दिए गए हैं, जिनमें से तीन (एशियाई खेल, राष्ट्रमंडल खेल, ओलंपिक खेल) किसी तरह से एक जैसे हैं और ICC विश्व कप अलग है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. The following diagram is a combination of four figures. A,B,C and D. Figure A represents the number of people who drink tea. Figure B represents the number of people who drink coffee. Figure C represents the number of people who drink milk. Figure D represents the number of people who drink cold drinks.<br>How many people do not drink coffee or milk?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917215826.png\" alt=\"rId5\" width=\"179\" height=\"101\"></p>",
                    question_hi: "<p>3. निम्नलिखित आरेख चार आकृतियों A,B,C और D का एक संयोजन है। चित्र A चाय पीने वाले लोगों की संख्या को दर्शाता है। चित्रा B कॉफी पीने वाले लोगों की संख्या का प्रतिनिधित्व करता है। चित्र C दूध पीने वाले लोगों की संख्या को दर्शाता है। चित्र D शीतल पेय पीने वाले लोगों की संख्या को दर्शाता है।<br>कितने लोग कॉफी या दूध नहीं पीते हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917215826.png\" alt=\"rId5\" width=\"189\" height=\"107\"></p>",
                    options_en: ["<p>15</p>", "<p>20</p>", 
                                "<p>10</p>", "<p>12</p>"],
                    options_hi: ["<p>15</p>", "<p>20</p>",
                                "<p>10</p>", "<p>12</p>"],
                    solution_en: "<p>3.(c)<br>People do not drink coffee or milk = Number of people who drink tea or cold drinks.<br>And people who drink tea or cold drinks is represented by the figure A and figure D<br>3 + 7 = 10</p>",
                    solution_hi: "<p>3.(c)<br>लोग कॉफी या दूध नहीं पीते = चाय या कोल्ड ड्रिंक पीने वालों की संख्या।<br>और जो लोग चाय या कोल्ड ड्रिंक पीते हैं उन्हें आकृति A और आकृति D द्वारा दर्शाया जाता है<br>3 + 7 = 10</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language, &lsquo;FLOW&rsquo; is written as DJMU&rsquo;. How will &lsquo;WING&rsquo; be written in that language?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में, \'FLOW\' को DJMU\' लिखा जाता है। उसी भाषा में \'WING\' को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>UGLE</p>", "<p>UHMF</p>", 
                                "<p>UFKD</p>", "<p>NQHG</p>"],
                    options_hi: ["<p>UGLE</p>", "<p>UHMF</p>",
                                "<p>UFKD</p>", "<p>NQHG</p>"],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917215990.png\" alt=\"rId6\" width=\"369\" height=\"82\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917215990.png\" alt=\"rId6\" width=\"369\" height=\"82\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "5. If you arrange the following words in the order in which they appear in an English dictionary, then which word would come last?",
                    question_hi: "5. यदि आप निम्नलिखित शब्दों को उसी क्रम में व्यवस्थित करें जिस क्रम में वे अंग्रेजी शब्दकोश में आते हैं, तो कौन सा शब्द सबसे अंत में आएगा?",
                    options_en: [" Accent", " About", 
                                " Acacia", " Abuse"],
                    options_hi: [" Accent", " About",
                                " Acacia", " Abuse"],
                    solution_en: "5.(a)<br />About, Abuse, Acacia, Accent",
                    solution_hi: "5.(a)<br />About, Abuse, Acacia, Accent",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the Venn diagram that best represents the relationship between the given set of classes.<br>Tea, Beverage, Cup</p>",
                    question_hi: "<p>6. वेन आरेख का चयन करें जो दिए गए वर्गों के बीच संबंध का सबसे अच्छा प्रतिनिधित्व करता है।<br>चाय, पेय, कप</p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216107.png\" alt=\"rId7\" width=\"89\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216297.png\" alt=\"rId8\" width=\"115\" height=\"54\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216490.png\" alt=\"rId9\" width=\"69\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216614.png\" alt=\"rId10\" width=\"78\" height=\"70\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216107.png\" alt=\"rId7\" width=\"89\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216297.png\" alt=\"rId8\" width=\"115\" height=\"54\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216490.png\" alt=\"rId9\" width=\"69\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216614.png\" alt=\"rId10\" width=\"78\" height=\"70\"></p>"],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216297.png\" alt=\"rId8\" width=\"137\" height=\"64\"></p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216297.png\" alt=\"rId8\" width=\"137\" height=\"64\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Study the given pattern carefully and select the number from among the given options that can replace the question mark(?).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216837.png\" alt=\"rId11\" width=\"258\" height=\"80\"></p>",
                    question_hi: "<p>7. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन करें और दिए गए विकल्पों में से उस संख्या का चयन करें जो प्रश्न चिह्न (?) को प्रतिस्थापित कर सके।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917216837.png\" alt=\"rId11\" width=\"258\" height=\"80\"></p>",
                    options_en: ["<p>7</p>", "<p>5</p>", 
                                "<p>6</p>", "<p>4</p>"],
                    options_hi: ["<p>7</p>", "<p>5</p>",
                                "<p>6</p>", "<p>4</p>"],
                    solution_en: "<p>7.(d)<br>Sum of all the numbers in 3 figures are prime numbers.<br>So by putting option (d) we get the answer.</p>",
                    solution_hi: "<p>7.(d)<br>तीनों छवियों में सभी संख्याओं का योग अभाज्य संख्याएँ हैं।<br>अतः विकल्प (d) डालने पर हमें उत्तर प्राप्त होता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option in which the words share the same relationship as that shared by the given pair of words.<br>Ankle : Feet</p>",
                    question_hi: "<p>8. उस विकल्प का चयन करें जिसमें शब्द वही संबंध साझा करते हैं जो दिए गए शब्दों के जोड़े द्वारा साझा किए गए हैं।<br>एंकल : पैर</p>",
                    options_en: ["<p>Wrist : Palm</p>", "<p>Knee : Calf</p>", 
                                "<p>Elbow : Hand</p>", "<p>Finger : Thumb</p>"],
                    options_hi: ["<p>कलाई: हथेली</p>", "<p>घुटने: पिंडली</p>",
                                "<p>कोहनी: हाथ</p>", "<p>उंगली: अंगूठा</p>"],
                    solution_en: "<p>8.(a)<br>Ankle is related to feet similarly Wrist is related to Palm.</p>",
                    solution_hi: "<p>8.(a)<br>टखनों का संबंध पैरों से होता है उसी प्रकार कलाई का संबंध हथेली से होता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "9. Select a term from the following option that is related to term 3 in the same way as term 2 is related to term 1.<br />Kite : Fly : : Dice : ?",
                    question_hi: "9. निम्नलिखित विकल्पों में से एक पद का चयन कीजिए जो पद 3 से उसी प्रकार संबंधित है जैसे पद 2 पद 1 से संबंधित है।<br />पतंग : उड़ना : : पासा : ?",
                    options_en: [" Pair", " Roll", 
                                " Ludo", " Face"],
                    options_hi: [" जोड़ा", " रोल",
                                " लूडो", " चेहरा"],
                    solution_en: "9.(b)<br />Kites fly in the same way Dice is rolled.",
                    solution_hi: "9.(b)<br />जैसे पासा लुढ़काया जाता है, वैसे ही पतंगें उड़ती हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Read the given statements and decide which of the conclusions given in the options logically follow from the statements.<br><strong>Statements:</strong><br>1. All bats are bangles.<br>2. Some baskets are bangles but not bats.<br>3. Not all baskets are bangles.</p>",
                    question_hi: "<p>10. दिए गए कथनों को पढ़िए और तय कीजिए कि दिए गए विकल्पों में से कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>सभी चमगादड़ चूड़ियाँ हैं।<br>कुछ टोकरियाँ चूड़ियाँ हैं लेकिन चमगादड़ नहीं हैं।<br>सभी टोकरियाँ चूड़ियाँ नहीं हैं।</p>",
                    options_en: ["<p>Some bangles are neither bats nor baskets.</p>", "<p>Some bats are neither baskets nor bangles.</p>", 
                                "<p>Some baskets are both bats and bangles.</p>", "<p>Some baskets are neither bangles nor bats.</p>"],
                    options_hi: ["<p>कुछ चूड़ियाँ न तो चमगादड़ हैं और न ही टोकरियाँ हैं।</p>", "<p>कुछ चमगादड़ न तो टोकरियाँ हैं और न ही चूड़ियाँ हैं।</p>",
                                "<p>कुछ टोकरियाँ चमगादड़ और चूड़ियाँ दोनों हैं।</p>", "<p>कुछ टोकरियाँ न तो चूड़ियाँ हैं और न ही चमगादड़ हैं।</p>"],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217010.png\" alt=\"rId12\" width=\"214\" height=\"81\"><br>From the above diagram it is clear that some baskets are neither bangles nor bats.</p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217010.png\" alt=\"rId12\" width=\"214\" height=\"81\"><br>उपरोक्त आरेख से यह स्पष्ट है कि कुछ टोकरियाँ न तो चूड़ियाँ हैं और न ही चमगादड़ हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. X is the mother-in-law of Y and Z. P is the daughter of N who is the husband of Z. How is N related to X?</p>",
                    question_hi: "<p>11. X, Y और Z की सास है। P, N की पुत्री है जो Z का पति है। N, X से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Son</p>", "<p>Brother</p>", 
                                "<p>Father</p>", "<p>Son in Law</p>"],
                    options_hi: ["<p>बेटा</p>", "<p>भाई</p>",
                                "<p>पिता</p>", "<p>दामाद</p>"],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217129.png\" alt=\"rId13\" width=\"196\" height=\"130\"></p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217129.png\" alt=\"rId13\" width=\"196\" height=\"130\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "12. Four options have been given out of which three are alike in some manner and one is different. Select the odd one.",
                    question_hi: "12. चार विकल्प दिए गए हैं जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक अलग है। विषम का चयन करें।",
                    options_en: [" HISR", " OPML", 
                                " CDXW", " EFVU"],
                    options_hi: [" HISR", " OPML",
                                " CDXW", " EFVU"],
                    solution_en: "12.(b)<br />The gap between the 2nd and 3rd letter is different from others.",
                    solution_hi: "12.(b)<br />दूसरे और तीसरे अक्षर के बीच का अंतर दूसरों से अलग है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13. Four words have been given,out of which three are alike in some manner and one is different. Select the odd one.",
                    question_hi: "13. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। विषम का चयन करें।",
                    options_en: [" Lose", " Toss", 
                                " Draw", " Win<br /> "],
                    options_hi: [" हार", " टॉस",
                                " ड्रा", " जीतना"],
                    solution_en: "13.(b)<br />Except Toss, all are results of any game.",
                    solution_hi: "13.(b)<br />टॉस को छोड़कर, सभी किसी भी खेल के परिणाम हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Study the given grid carefully and select the number from among the given options that can replace the question mark(?).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217226.png\" alt=\"rId14\" width=\"105\" height=\"108\"></p>",
                    question_hi: "<p>14. दिए गए ग्रिड का ध्यानपूर्वक अध्ययन करें और दिए गए विकल्पों में से उस संख्या का चयन करें जो प्रश्न चिह्न (?) को प्रतिस्थापित कर सके।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217226.png\" alt=\"rId14\" width=\"105\" height=\"108\"></p>",
                    options_en: ["<p>20</p>", "<p>4</p>", 
                                "<p>12</p>", "<p>16</p>"],
                    options_hi: ["<p>20</p>", "<p>4</p>",
                                "<p>12</p>", "<p>16</p>"],
                    solution_en: "<p>14.(d)<br>(7 - 1 ) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn><mo>=</mo><mn>12</mn></math><br>(9 - 5) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn><mo>=</mo><mn>8</mn></math><br>(14 - 6) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn><mo>=</mo><mn>16</mn></math></p>",
                    solution_hi: "<p>14.(d)<br>(7 - 1 ) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn><mo>=</mo><mn>12</mn></math><br>(9 - 5) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn><mo>=</mo><mn>8</mn></math><br>(14 - 6) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn><mo>=</mo><mn>16</mn></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Roopa walked from her office 20 meters towards the north, 50 meters towards the east, 25 meters towards the north, and then 50 meters towards the west. She stopped at this point to visit a friend at her house. She again walked 30 meters towards the east. How far and in which direction is Roopa&rsquo;s office from her friend&rsquo;s house?</p>",
                    question_hi: "<p>15. रूपा अपने कार्यालय से उत्तर की ओर 20 मीटर, पूर्व की ओर 50 मीटर, उत्तर की ओर 25 मीटर और फिर पश्चिम की ओर 50 मीटर चली। वह अपने घर पर एक दोस्त से मिलने के लिए इस बिंदु पर रुक गई। वह फिर पूर्व की ओर 30 मीटर चली। रूपा का कार्यालय उसकी सहेली के घर से कितनी दूर और किस दिशा में है?</p>",
                    options_en: ["<p>45 meters, North</p>", "<p>25 meters, West</p>", 
                                "<p>30 meters, East</p>", "<p>45 meters, South</p>"],
                    options_hi: ["<p>45 मीटर, उत्तर</p>", "<p>25 मीटर, पश्चिम</p>",
                                "<p>30 मीटर, पूर्व</p>", "<p>45 मीटर, दक्षिण</p>"],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217329.png\" alt=\"rId15\" width=\"158\" height=\"146\"></p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217329.png\" alt=\"rId15\" width=\"158\" height=\"146\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Study the given pattern carefully and select the number that can replace the question mark(?) in it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217533.png\" alt=\"rId16\" width=\"254\" height=\"100\"></p>",
                    question_hi: "<p>16. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन करें और उस संख्या का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217533.png\" alt=\"rId16\" width=\"254\" height=\"100\"></p>",
                    options_en: ["<p>9</p>", "<p>1</p>", 
                                "<p>6</p>", "<p>12</p>"],
                    options_hi: ["<p>9</p>", "<p>1</p>",
                                "<p>6</p>", "<p>12</p>"],
                    solution_en: "<p>16.(b)<br>(5 + 4 + 9) - (3 + 4) = 11<br>(3 + 4 + 6) - (2 + 1) = 10<br>(2 + 3 + 1) - (4 + 1) = 1</p>",
                    solution_hi: "<p>16.(b)<br>(5 + 4 + 9) - (3 + 4) = 11<br>(3 + 4 + 6) - (2 + 1) = 10<br>(2 + 3 + 1) - (4 + 1) = 1</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. In a certain code language , &lsquo;DEN&rsquo; is written as &lsquo;GHQ&rsquo; and &lsquo;MEN&rsquo; is written as &lsquo;PHQ&rsquo;. How will &lsquo;BOX&rsquo; be written as in that code language?</p>",
                    question_hi: "<p>17. एक निश्चित कूट भाषा में, \'DEN\' को \'GHQ\' लिखा जाता है और \'MEN\' को \'PHQ\' लिखा जाता है। उसी कोड भाषा में \'BOX\' को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>DQZ</p>", "<p>ERA</p>", 
                                "<p>ERZ</p>", "<p>FSB</p>"],
                    options_hi: ["<p>DQZ</p>", "<p>ERA</p>",
                                "<p>ERZ</p>", "<p>FSB</p>"],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217702.png\" alt=\"rId17\" width=\"364\" height=\"61\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217702.png\" alt=\"rId17\" width=\"364\" height=\"61\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. You were traveling in a bus when a lady you met told you, &ldquo;You are the father of my eldest daughter\'s maternal aunt&rsquo;s youngest sister&rdquo;.How are you related to that lady?</p>",
                    question_hi: "<p>18. आप एक बस में यात्रा कर रहे थे जब आपकी मुलाकात एक महिला ने आपसे की, \"आप मेरी सबसे बड़ी बेटी की मौसी की सबसे छोटी बहन के पिता हैं\"। आप उस महिला से कैसे संबंधित हैं?</p>",
                    options_en: ["<p>Cousin</p>", "<p>Uncle</p>", 
                                "<p>Brother</p>", "<p>Father</p>"],
                    options_hi: ["<p>कज़न</p>", "<p>चाचा</p>",
                                "<p>भाई</p>", "<p>पिता</p>"],
                    solution_en: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217934.png\" alt=\"rId18\" width=\"185\" height=\"94\"></p>",
                    solution_hi: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917217934.png\" alt=\"rId18\" width=\"185\" height=\"94\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Read the given statements and conclusions carefully and decide which of the conclusions logically follows from the statements.<br><strong>Statements:</strong><br>I. Some of the beverages sold at this cafeteria are coffee.<br>II. None of the beverages sold are tea.<br>III. All the beverages sold at this cafeteria are very aromatic and tasty.<br><strong>Conclusions:</strong><br>i) Every coffee sold at this cafeteria is very aromatic.<br>ii) Some espressos sold at this cafeteria are cappuccinos.<br>iii) Some of the tea sold at this cafeteria is not aromatic.</p>",
                    question_hi: "<p>19. दिए गए कथनों और निष्कर्षों को ध्यान से पढ़ें और तय करें कि कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>I. इस कैफेटेरिया में बेचे जाने वाले कुछ पेय पदार्थ कॉफी हैं।<br>II. बेचे जाने वाले पेय पदार्थों में से कोई भी चाय नहीं है।<br>III. इस कैफेटेरिया में बिकने वाले सभी पेय पदार्थ बहुत सुगंधित और स्वादिष्ट होते हैं।<br><strong>निष्कर्ष:</strong><br>i) इस कैफेटेरिया में बिकने वाली हर कॉफी बहुत सुगंधित होती है।<br>ii) इस कैफेटेरिया में बिकने वाले कुछ एस्प्रेसो कैप्पुकिनो हैं।<br>iii) इस कैफेटेरिया में बेची जाने वाली कुछ चाय सुगंधित नहीं होती है।</p>",
                    options_en: ["<p>Only conclusions (i) and (ii) follow.</p>", "<p>Only conclusions (i) and (iii) follow.</p>", 
                                "<p>Only conclusion (iii) follows.</p>", "<p>Only conclusion (i) follows.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (i) और (ii) अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष (i) और (iii) अनुसरण करते हैं।</p>",
                                "<p>केवल निष्कर्ष (iii) अनुसरण करता है।</p>", "<p>केवल निष्कर्ष (i) अनुसरण करता है।</p>"],
                    solution_en: "<p>19.(d)<br>From the given statements it is clear that Some of the tea sold at this cafeteria is not aromatic.</p>",
                    solution_hi: "<p>19.(d)<br>दिए गए कथनों से यह स्पष्ट है कि इस कैफेटेरिया में बेची जाने वाली कुछ चाय सुगंधित नहीं होती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. In the equations below, the function of the rectangle remains unchanged. Select the option that can replace the question mark(?) in the fourth equation.<br>4 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218083.png\" alt=\"rId19\"> 5 = 21 / 4<br>3 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218083.png\" alt=\"rId19\"> 6 = 19 / 3<br>6 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218083.png\" alt=\"rId19\"> 5 = 31 / 6<br>a <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218083.png\" alt=\"rId19\"> b = ?<br><br></p>",
                    question_hi: "<p>20. नीचे दिए गए समीकरणों में, आयत का कार्य अपरिवर्तित रहता है। उस विकल्प का चयन करें जो चौथे समीकरण में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है।<br>4 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218083.png\" alt=\"rId19\"> 5 = 21 / 4<br>3 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218083.png\" alt=\"rId19\"> 6 = 19 / 3<br>6 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218083.png\" alt=\"rId19\"> 5 = 31 / 6<br>a <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218083.png\" alt=\"rId19\"> b = ?</p>",
                    options_en: ["<p>(a - b) / ab</p>", "<p>(ab + 1) / a</p>", 
                                "<p>(ab + 1) / b</p>", "<p>ab / (a + b)</p>"],
                    options_hi: ["<p>(a - b) / ab</p>", "<p>(ab + 1) / a</p>",
                                "<p>(ab + 1) / b</p>", "<p>ab / (a + b)</p>"],
                    solution_en: "<p>20.(b)<br>a <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218083.png\" alt=\"rId19\"> b = ?<br>(ab + 1) / a</p>",
                    solution_hi: "<p>20.(b)<br>a <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218083.png\" alt=\"rId19\"> b = ?<br>(ab + 1) / a</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. The <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218207.png\" alt=\"rId20\"> represents a set of calculations that are common across all the given equations. Identify the calculations involved and solve the fourth equation on the same basis:<br>4 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218207.png\" alt=\"rId20\"> 5 = 11&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218207.png\" alt=\"rId20\"> 8 = 17<br>&nbsp; &nbsp; &nbsp; &nbsp;I.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;II.<br>2 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218207.png\" alt=\"rId20\"> 9 = 10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;6 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218207.png\" alt=\"rId20\"> 3 = ?<br>&nbsp; &nbsp; &nbsp; &nbsp;III.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IV.</p>",
                    question_hi: "<p>21. <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218207.png\" alt=\"rId20\"> गणना के एक सेट का प्रतिनिधित्व करता है जो सभी दिए गए समीकरणों में सामान्य हैं। शामिल गणनाओं को पहचानें और उसी आधार पर चौथे समीकरण को हल करें:<br>4 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218207.png\" alt=\"rId20\"> 5 = 11&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218207.png\" alt=\"rId20\"> 8 = 17<br>&nbsp; &nbsp; &nbsp; &nbsp;I.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;II.<br>2 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218207.png\" alt=\"rId20\"> 9 = 10&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;6 <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218207.png\" alt=\"rId20\"> 3 = ?<br>&nbsp; &nbsp; &nbsp; &nbsp;III.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IV.</p>",
                    options_en: ["<p>11</p>", "<p>10</p>", 
                                "<p>3</p>", "<p>14</p>"],
                    options_hi: ["<p>11</p>", "<p>10</p>",
                                "<p>3</p>", "<p>14</p>"],
                    solution_en: "<p>21.(b)<br>(4 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>5</mn><mo>)</mo><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>11</mn></math><br>(4 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>8</mn><mo>)</mo><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>17</mn></math><br>(2 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>9</mn><mo>)</mo><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>10</mn></math><br>(6 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn><mo>)</mo><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>10</mn></math></p>",
                    solution_hi: "<p>21.(b)<br>(4 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>5</mn><mo>)</mo><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>11</mn></math><br>(4 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>8</mn><mo>)</mo><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>17</mn></math><br>(2 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>9</mn><mo>)</mo><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>10</mn></math><br>(6 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn><mo>)</mo><mo>&#247;</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>10</mn></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Read the given statement and courses of action carefully and decide which of the courses of action logically follows from the statement.<br><strong>Statement:</strong><br>An epidemic disease has spread in an XYZ area due to contaminated water in a nearby pond.<br><strong>Courses of Action:</strong><br>(i) Awareness of preventive measures regarding the spread of that disease must be communicated in that area and in the areas nearby.<br>(ii) The people of XYZ area must be shifted to other areas populated with healthy people.<br>(iii) The contaminated water body must be treated.</p>",
                    question_hi: "<p>22. दिए गए कथन और कार्रवाई के पाठ्यक्रम को ध्यान से पढ़ें और तय करें कि कौन सी कार्रवाई तार्किक रूप से कथन का अनुसरण करती है।<br><strong>कथन:</strong><br>एक XYZ क्षेत्र में पास के तालाब में दूषित पानी के कारण महामारी की बीमारी फैल गई है।<br><strong>कार्यवाई के दौरान:</strong><br>(i) उस क्षेत्र में और आस-पास के क्षेत्रों में उस बीमारी के प्रसार के संबंध में निवारक उपायों के बारे में जागरूकता का संचार किया जाना चाहिए।<br>(ii) XYZ क्षेत्र के लोगों को स्वस्थ लोगों की आबादी वाले अन्य क्षेत्रों में स्थानांतरित किया जाना चाहिए।<br>(iii) दूषित जल निकाय का उपचार किया जाना चाहिए।</p>",
                    options_en: ["<p>Only actions (i) and (ii) follow.</p>", "<p>Only actions (i) and (iii) follow.</p>", 
                                "<p>Only action (iii) follows.</p>", "<p>Action (i),(ii) and (iii) follow.</p>"],
                    options_hi: ["<p>केवल कार्रवाई (i) और (ii) अनुसरण करती हैं।</p>", "<p>केवल कार्रवाई (i) और (iii) का पालन करें।</p>",
                                "<p>केवल कार्रवाई (iii) अनुसरण करती है।</p>", "<p>कार्रवाई (i), (ii) और (iii) का पालन करें।</p>"],
                    solution_en: "<p>22.(b)<br>From the given statements it is clear that only action 1 and 3 follow.</p>",
                    solution_hi: "<p>22.(b)<br>दिए गए कथनों से यह स्पष्ट है कि केवल क्रिया 1 और 3 अनुसरण करते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Study the given pattern carefully and select the number that can replace the question mark(?) in it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218345.png\" alt=\"rId21\" width=\"317\" height=\"102\"></p>",
                    question_hi: "<p>23. दिए गए पैटर्न का&nbsp;ध्यानपूर्वक अध्ययन करें और उस संख्या का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218345.png\" alt=\"rId21\" width=\"317\" height=\"102\"></p>",
                    options_en: ["<p>40</p>", "<p>25</p>", 
                                "<p>20</p>", "<p>1</p>"],
                    options_hi: ["<p>40</p>", "<p>25</p>",
                                "<p>20</p>", "<p>1</p>"],
                    solution_en: "<p>23.(c)<br>In first figure:<br>4 + 3 + 5 + 6 = 18 <br>Now 18 <math display=\"inline\"><mo>&#247;</mo></math> 2 = 9<br>In Second figure:<br>11 + 8 + 4 + 3 = 26<br>Now 26 <math display=\"inline\"><mo>&#247;</mo></math> 2 = 13<br>And same goes with figure 3<br>13 + 10 + 12 + 5 = 40<br>So the answer will be 40 <math display=\"inline\"><mo>&#247;</mo></math> 2 = 20</p>",
                    solution_hi: "<p>23.(c)<br>पहले चित्र में:<br>4 + 3 + 5 + 6 = 18<br>अब 18 <math display=\"inline\"><mo>&#247;</mo></math> 2 = 9<br>दूसरे चित्र में:<br>11 + 8 + 4 + 3 = 26<br>अब 26 <math display=\"inline\"><mo>&#247;</mo></math> 2 = 13<br>और चित्र 3 के साथ भी ऐसा ही है:<br>13 + 10 + 12 + 5 = 40<br>तो उत्तर 40 <math display=\"inline\"><mo>&#247;</mo></math> 2 = 20</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Study the given pattern carefully and select the number that can replace the question mark (?) in it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218484.png\" alt=\"rId22\" width=\"294\" height=\"82\"></p>",
                    question_hi: "<p>24. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन करें और उस संख्या का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729917218484.png\" alt=\"rId22\" width=\"294\" height=\"82\"></p>",
                    options_en: ["<p>26</p>", "<p>17</p>", 
                                "<p>20</p>", "<p>12</p>"],
                    options_hi: ["<p>26</p>", "<p>17</p>",
                                "<p>20</p>", "<p>12</p>"],
                    solution_en: "<p>24.(b)<br>18 + 7 = 8 + 17<br>22 + 7 = 15 + 14<br>18 + 13 = 14 + 17</p>",
                    solution_hi: "<p>24.(b)<br>18 + 7 = 8 + 17<br>22 + 7 = 15 + 14<br>18 + 13 = 14 + 17</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p>25. If &lsquo;&times;&rsquo; means &lsquo;+&rsquo;, &lsquo;-&rsquo; means &lsquo;&times;&rsquo; , &lsquo;+&rsquo; means &lsquo;-&rsquo;, then find the value of the given expression.<br>(4 - 12 <math display=\"inline\"><mo>&#247;</mo><mn>16</mn></math>) &times; 8 + 5</p>",
                    question_hi: "<p>25. यदि \'&times;\' का अर्थ \'+\', \'-\' का अर्थ \'&times;\', \'+\' का अर्थ \'-\' है, तो दिए गए व्यंजक का मान ज्ञात कीजिए।<br>(4 - 12 <math display=\"inline\"><mo>&#247;</mo><mn>16</mn></math>) &times; 8 + 5</p>",
                    options_en: ["<p>3</p>", "<p>23</p>", 
                                "<p>9</p>", "<p>15</p>"],
                    options_hi: ["<p>3</p>", "<p>23</p>",
                                "<p>9</p>", "<p>15</p>"],
                    solution_en: "<p>25.(a)<br>(4 - 12&nbsp;<math display=\"inline\"><mo>&#247;</mo><mn>16</mn></math>) x 8 + 5<br>= (4 <math display=\"inline\"><mo>&#215;</mo><mn>12</mn><mi>&#160;</mi><mo>+</mo><mn>16</mn><mo>)</mo><mo>&#247;</mo><mn>8</mn><mo>-</mo><mn>5</mn></math><br>= 64<math display=\"inline\"><mo>&#247;</mo><mn>8</mn><mo>-</mo><mn>5</mn></math><br>= 8 - 5 <br>= 3</p>",
                    solution_hi: "<p>25.(a)<br>(4 - 12&nbsp;<math display=\"inline\"><mo>&#247;</mo><mn>16</mn></math>) x 8 + 5<br>= (4 <math display=\"inline\"><mo>&#215;</mo><mn>12</mn><mi>&#160;</mi><mo>+</mo><mn>16</mn><mo>)</mo><mo>&#247;</mo><mn>8</mn><mo>-</mo><mn>5</mn></math><br>= 64<math display=\"inline\"><mo>&#247;</mo><mn>8</mn><mo>-</mo><mn>5</mn></math><br>= 8 - 5 <br>= 3</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "misc",
                    question_en: "<p>26. In a group of boys, Rohan is not as strong as Zeeshan. Mathew is stronger than Paul. Subhash is weaker than Rohan but stronger than Paul . Akhil is as stronger as Zeeshan but weaker than Sujeet. Mathew is not as strong as Rohan. Who is the strongest among the given group of boys ?</p>",
                    question_hi: "<p>26. लड़कों के समूह में रोहन, जीशान जितना मजबूत नहीं है। मैथ्यू ,पॉल से ज्यादा मजबूत है। सुभाष, रोहन से कमजोर है लेकिन पॉल से मजबूत है । अखिल , जीशान जितना मजबूत है लेकिन सुजीत से कमजोर है। मैथ्यू ,रोहन जितना मजबूत नहीं है। दिए गए लड़कों के समूह में सबसे मजबूत कौन है ?</p>",
                    options_en: ["<p>Sujeet</p>", "<p>Mathew</p>", 
                                "<p>Akhil</p>", "<p>Zeeshan</p>"],
                    options_hi: ["<p>सुजीत</p>", "<p>मैथ्यू</p>",
                                "<p>अखिल</p>", "<p>जीशान</p>"],
                    solution_en: "<p>26.(a)<br>Sujeet &gt; akhil = zeeshan &gt; Rohan &gt; subhash &gt; mathew &gt; paul <br>So, Sujeet is the strongest among the given group.</p>",
                    solution_hi: "<p>26.(a)<br>सुजीत &gt; अखिल = जीशान &gt; रोहन &gt; सुभाष &gt; मैथ्यू &gt; पॉल <br>तो, सुजीत दिए गए समूह में सबसे मजबूत है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>