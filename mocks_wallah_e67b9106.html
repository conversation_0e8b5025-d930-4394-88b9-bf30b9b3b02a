<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Study the given table and answer the question that follows. <br>The table shows the number of students studying in 6 different classes of 6 different schools.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171767868.png\" alt=\"rId4\" width=\"304\" height=\"160\"> <br>Which class has the minimum number of students from all schools together ?</p>",
                    question_hi: "<p>1. दी गई तालिका का अध्ययन करें, और नीचे दिए गए प्रश्न का उत्तर दें। <br>निम्न तालिका 6 अलग-अलग स्कूलों की 6 अलग-अलग कक्षाओं में पढ़ने वाले विद्यार्थियों की संख्या को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171767996.png\" alt=\"rId5\" width=\"319\" height=\"160\"> <br>सभी स्कूलों में मिलाकर किस कक्षा में विद्यार्थियों की संख्या न्यूनतम है ?</p>",
                    options_en: ["<p>VII</p>", "<p>IX</p>", 
                                "<p>X</p>", "<p>VIII</p>"],
                    options_hi: ["<p>VII</p>", "<p>IX</p>",
                                "<p>X</p>", "<p>VIII</p>"],
                    solution_en: "<p>1.(b)<br>From the above table, we can clearly see that class IX has the minimum number of students.(903)</p>",
                    solution_hi: "<p>1.(b)<br>उपरोक्त तालिका से, हम स्पष्ट रूप से देख सकते हैं कि कक्षा IX में छात्रों की संख्या सबसे कम है। (903)</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Study the given graph and answer the question that follows. <br>The graph shows data related to number of students enrolled for a vocational course in two institutes (X and Y) during three years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171768139.png\" alt=\"rId6\" width=\"248\" height=\"180\"> <br>What is the difference between the average number of students enrolled in institute X in 2010 and 2011 and that in institute Y in 2011 and 2012 ?</p>",
                    question_hi: "<p>2. दिए गए आलेख का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए । आलेख तीन वर्षों के दौरान दो संस्थानों (X और Y) में व्यावसायिक पाठ्यक्रम के लिए नामांकित छात्रों की संख्या से संबंधित डेटा दिखाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171768273.png\" alt=\"rId7\" width=\"230\" height=\"182\"> <br>Number of students = छात्रों की संख्या, Institute X = संस्थान X, Institute Y = संस्थान Y<br>2010 और 2011 में संस्थान X में नामांकित छात्रों की औसत संख्या तथा 2011 और 2012 में संस्थान Y में नामांकित छात्रों की औसत संख्या के बीच कितना अंतर है ?</p>",
                    options_en: ["<p>15</p>", "<p>20</p>", 
                                "<p>18</p>", "<p>22</p>"],
                    options_hi: ["<p>15</p>", "<p>20</p>",
                                "<p>18</p>", "<p>22</p>"],
                    solution_en: "<p>2.(b)<br>Average number of students enrolled in institute X in 2010 and 2011 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>320</mn><mo>+</mo><mn>240</mn></mrow><mn>2</mn></mfrac></math> = 280<br>Average number of students enrolled in institute Y in 2011 and 2012 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>320</mn><mo>+</mo><mn>200</mn></mrow><mn>2</mn></mfrac></math> = 260<br>Required difference = 280 - 260 = 20</p>",
                    solution_hi: "<p>2.(b)<br>2010 और 2011 में संस्थान X में नामांकित छात्रों की औसत संख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>320</mn><mo>+</mo><mn>240</mn></mrow><mn>2</mn></mfrac></math> = 280<br>2011 और 2012 में संस्थान Y में नामांकित छात्रों की औसत संख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>320</mn><mo>+</mo><mn>200</mn></mrow><mn>2</mn></mfrac></math> = 260<br>आवश्यक अंतर = 280 - 260 = 20</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The given pie-chart represents the survey report on favourite games of a group of young people.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171768535.png\" alt=\"rId8\" width=\"173\" height=\"157\"> <br>If a total of 4980 people were surveyed, then what is the central angle produced by the sector indicating football ?</p>",
                    question_hi: "<p>3. दिया गया पाई चार्ट युवा लोगों के एक समूह के पसंदीदा खेलों पर सर्वेक्षण रिपोर्ट को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171768680.png\" alt=\"rId9\" width=\"176\" height=\"161\"> <br>यदि कुल 4980 लोगों का सर्वेक्षण किया गया, तो फुटबाल को दर्शाने वाले क्षेत्र द्वारा निर्मित केंद्रीय कोण क्या है ?</p>",
                    options_en: ["<p>100&deg;</p>", "<p>108&deg;</p>", 
                                "<p>105&deg;</p>", "<p>90&deg;</p>"],
                    options_hi: ["<p>100&deg;</p>", "<p>108&deg;</p>",
                                "<p>105&deg;</p>", "<p>90&deg;</p>"],
                    solution_en: "<p>3.(b)<br>Number of people who favor football = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>100</mn></mfrac></math>&times; 4980 = 1494<br>Central angle produced by football = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1494</mn><mn>4980</mn></mfrac></math> &times; 360 = 108&deg;</p>",
                    solution_hi: "<p>3.(b)<br>फुटबॉल को पसंद करने वाले व्यक्तियों की संख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>100</mn></mfrac></math>&times; 4980 = 1494<br>फुटबॉल द्वारा निर्मित केंद्रीय कोण = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1494</mn><mn>4980</mn></mfrac></math> &times; 360 = 108&deg;</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. In a survey, 200 respondents were asked whether they owned a vehicle or not. Their responses are tabulated below. What percentage of respondents do NOT own a car ? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171768815.png\" alt=\"rId10\" width=\"236\" height=\"105\"></p>",
                    question_hi: "<p>4. एक सर्वेक्षण में 200 उत्तरदाताओं से पूछा गया कि क्या उनके पास एक वाहन है या नहीं। उनके उत्तर नीचे सारणीबद्ध हैं। कितने प्रतिशत उत्तरदाताओं के पास कार नहीं है ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171768988.png\" alt=\"rId11\" width=\"212\" height=\"108\"></p>",
                    options_en: ["<p>72.5%</p>", "<p>65%</p>", 
                                "<p>68.5%</p>", "<p>75%</p>"],
                    options_hi: ["<p>72.5%</p>", "<p>65%</p>",
                                "<p>68.5%</p>", "<p>75%</p>"],
                    solution_en: "<p>4.(a)<br>According to the question,<br>Total number of respondents = 120 + 80 = 200<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>-</mo><mo>[</mo><mo>(</mo><mn>25</mn><mo>+</mo><mn>10</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>15</mn><mo>+</mo><mn>5</mn><mo>)</mo><mo>]</mo></mrow><mn>200</mn></mfrac></math> &times; 100 = 72.5%</p>",
                    solution_hi: "<p>4.(a)<br>प्रश्न के अनुसार,<br>उत्तरदाताओं की कुल संख्या = 120 + 80 = 200<br>आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>-</mo><mo>[</mo><mo>(</mo><mn>25</mn><mo>+</mo><mn>10</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>15</mn><mo>+</mo><mn>5</mn><mo>)</mo><mo>]</mo></mrow><mn>200</mn></mfrac></math> &times; 100 = 72.5%</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Study the given table chart carefully and answer the question that follows.<br>The given table represents the number of employees in five different organisations, i.e., A, B, C, D and E in 2019, 2020, 2021 and 2022.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171769460.png\" alt=\"rId12\" width=\"277\" height=\"145\"> <br>The ratio of the number of employees in 2019 in organisations D and E taken together to that in 2021 in the same organisations is:</p>",
                    question_hi: "<p>5. दिए गए तालिका चार्ट का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br>दी गई तालिका 2019 2020 2021 और 2022 में पांच अलग-अलग संगठनों, यानी A, B, C, D और E में कर्मचारियों की संख्या दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171769601.png\" alt=\"rId13\" width=\"246\" height=\"152\"> <br>2019 में संगठन D और E में कर्मचारियों की संख्या और 2021 में समान संगठनों मैं कर्मचारियों की संख्या का अनुपात क्या है ?</p>",
                    options_en: [" 34 : 39 ", " 39 : 34. ", 
                                " 14 : 9 ", " 9 : 14"],
                    options_hi: [" 34 : 39 ", " 39 : 34. ",
                                " 14 : 9 ", " 9 : 14"],
                    solution_en: "5.(a)<br />According to the question,<br />Number of employees in 2019 in organizations D and E = 7000 + 6600 = 13600<br />Number of employees in 2021 in organizations D and E = 8000 + 7600 = 15600<br />Required ratio = 34 : 39",
                    solution_hi: "5.(a)<br />2019 में संगठन D और E में कर्मचारियों की संख्या = 7000 + 6600 = 13600<br />2021 में संगठन D और E में कर्मचारियों की संख्या = 8000 + 7600 = 15600<br />अभीष्ट अनुपात = 34 : 39",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Study the given pie-chart and answer the question that follows. <br>The pie-chart shows the sale of different fruits in a shop in a day.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171769724.png\" alt=\"rId14\" width=\"172\" height=\"160\"> <br>If a total of 1200 kg of fruits were sold in a day, find the difference between sales of grapes and oranges (in kg).</p>",
                    question_hi: "<p>6. दिए गए पाई चार्ट का अध्ययन करें, और निम्नलिखित प्रश्न का उत्तर दें। <br>निम्न पाई चार्ट एक दुकान में एक दिन में विभिन्न फलों की बिक्री को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171769877.png\" alt=\"rId15\" width=\"186\" height=\"172\"> <br>Banana = केला, Grapes अंगूर, Apple = सेब, Orange = संतरा, Others = अन्य यदि <br>एक दिन में कुल 1200 kg फल बेचे गए, तो अंगूर और संतरे की बिक्री (kg में) के बीच का अंतर ज्ञात कीजिए।</p>",
                    options_en: ["<p>60</p>", "<p>45</p>", 
                                "<p>65</p>", "<p>55</p>"],
                    options_hi: ["<p>60</p>", "<p>45</p>",
                                "<p>65</p>", "<p>55</p>"],
                    solution_en: "<p>6.(a)<br>According to the question,<br>Required difference = 1200 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>15</mn><mo>-</mo><mn>10</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> = 60</p>",
                    solution_hi: "<p>6.(a)<br>प्रश्न के अनुसार,<br>आवश्यक अंतर = 1200 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>15</mn><mo>-</mo><mn>10</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> = 60</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The following table shows the marks obtained out of 100, by 5 students in 5 different subjects.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171770012.png\" alt=\"rId16\" width=\"338\" height=\"118\"> <br>What are the total marks scored by Ayush in all the subjects ?</p>",
                    question_hi: "<p>7. निम्न तालिका 5 विद्यार्थियों द्वारा 5 विभिन्न विषयों में 100 में से प्राप्त अंकों को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171770164.png\" alt=\"rId17\" width=\"339\" height=\"142\"> <br>आयुष ने सभी विषयों में कुल कितने अंक प्राप्त किए ?</p>",
                    options_en: ["<p>445</p>", "<p>435</p>", 
                                "<p>465</p>", "<p>425</p>"],
                    options_hi: ["<p>445</p>", "<p>435</p>",
                                "<p>465</p>", "<p>425</p>"],
                    solution_en: "<p>7.(d)<br>Total marks scored by Ayush in all subjects = 88 + 80 + 90 + 82 + 85 = 425</p>",
                    solution_hi: "<p>7.(d)<br>सभी विषयों में आयुष द्वारा प्राप्त कुल अंक= 88 + 80 + 90 + 82 + 85 = 425</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.Study the given table and answer the question that follows.<br>The table shows the number of students studying in six different classes of six different schools.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171770322.png\" alt=\"rId18\" width=\"453\" height=\"158\"> <br>The number of students studying in class VII from school U is what percentage of the total number of students from all the classes together from that school (rounded off to 2 digits after decimal) ?</p>",
                    question_hi: "<p>8. दी गई तालिका का अध्ययन कीजिए और उसके बाद दिए गए प्रश्न का उत्तर दीजिए। <br>तालिका छह अलग-अलग स्कूलों की छह अलग-अलग कक्षाओं में पढ़ने वाले विद्यार्थियों की संख्या को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171770446.png\" alt=\"rId19\" width=\"384\" height=\"164\"> <br>स्कूल U से कक्षा VII में पढ़ने वाले विद्यार्थियों की संख्या, उस स्कूल की सभी कक्षाओं के विद्यार्थियों की कुल संख्या का कितना प्रतिशत है ( दशमलव के 2 स्थानों तक पूर्णांकित) ?</p>",
                    options_en: ["<p>16.48%</p>", "<p>15.63%</p>", 
                                "<p>17.36%</p>", "<p>18.25%</p>"],
                    options_hi: ["<p>16.48%</p>", "<p>15.63%</p>",
                                "<p>17.36%</p>", "<p>18.25%</p>"],
                    solution_en: "<p>8.(c)<br>Number of students in school U in all classes = 150 + 160 + 162 + 160 + 161 + 140 = 933<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>162</mn></mrow><mrow><mn>933</mn></mrow></mfrac></math> &times; 100 = 17.36%</p>",
                    solution_hi: "<p>8.(c)<br>स्कूल U में सभी कक्षाओं में छात्रों की संख्या = 150 + 160 + 162 + 160 + 161 + 140 = 933<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>162</mn></mrow><mrow><mn>933</mn></mrow></mfrac></math> &times; 100 = 17.36%</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The following table shows the data of 10th grade students of Portland High school.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171770572.png\" alt=\"rId20\" width=\"263\" height=\"67\"> <br>What is the approximate percentage of the 10th grade boys of Portland High school who are NOT enrolled in Science?</p>",
                    question_hi: "<p>9. निम्न तालिका पोर्टलैंड हाई स्कूल के 10वीं कक्षा के छात्रों का डेटा दर्शाती है I<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171770679.png\" alt=\"rId21\" width=\"263\" height=\"75\"> <br>पोर्टलैंड हाई स्कूल के 10वीं कक्षा के उन लड़कों का अनुमानित प्रतिशत क्या है, जो विज्ञान में नामांकित नहीं हैं ?</p>",
                    options_en: ["<p>33.33%</p>", "<p>55.55%</p>", 
                                "<p>22.22%</p>", "<p>44.44%</p>"],
                    options_hi: ["<p>33.33%</p>", "<p>55.55%</p>",
                                "<p>22.22%</p>", "<p>44.44%</p>"],
                    solution_en: "<p>9.(d)<br>According to the question,<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mrow><mn>25</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math> &times; 100 = 44.44%</p>",
                    solution_hi: "<p>9.(d)<br>प्रश्न के अनुसार,<br>आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mrow><mn>25</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math> &times; 100 = 44.44%</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Study the following table carefully and answer the questions based on it.<br>The following table shows the domestic sales of vehicles of four manufacturers from 2010 to 2015.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171770792.png\" alt=\"rId22\" width=\"458\" height=\"100\"> <br>With respect to which of the following combinations, is the sales of vehicles lowest over the given period ?</p>",
                    question_hi: "<p>10. निम्नांकित तालिका का ध्यानपूर्वक अध्ययन कीजिए और उसके आधार पर पूछे गए प्रश्नों के उत्तर दीजिए । <br>तालिका 2010 से 2015 के बीच चार विनिर्माताओं द्वारा निर्मित वाहनों की घरेलू बिक्री को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171770934.png\" alt=\"rId23\" width=\"467\" height=\"109\"> <br>निम्नलिखित में से किस संयोजन के संदर्भ में दी गई अवधि में वाहनों की बिक्री न्यूनतम \'है ?</p>",
                    options_en: ["<p>B, 2010</p>", "<p>B, 2015</p>", 
                                "<p>A, 2011</p>", "<p>A, 2010</p>"],
                    options_hi: ["<p>B, 2010</p>", "<p>B, 2015</p>",
                                "<p>A, 2011</p>", "<p>A, 2010</p>"],
                    solution_en: "<p>10.(a)<br>Sale in B, 2010 = 540000<br>Sale in B, 2015 = 550000<br>Sale in A, 2011 = 580000<br>Sale in A, 2010 = 560000<br>We can clearly see that the sale in B, 2010 is the lowest sale.</p>",
                    solution_hi: "<p>10.(a)<br>B, 2010 में बिक्री = 540000<br>B, 2015 में बिक्री = 550000<br>A, 2011 में बिक्री = 580000<br>A, 2010 में बिक्री = 560000<br>हम स्पष्ट रूप से देख सकते हैं कि B, 2010 में बिक्री सबसे कम बिक्री है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Study the given table and answer the question that follows.<br>The table shows the number of employees in five different organisation A, B, C, D and E in different departments. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171771067.png\" alt=\"rId24\" width=\"330\" height=\"184\"> <br>Which organisation has maximum number of employees</p>",
                    question_hi: "<p>11. दी गई तालिका का अध्ययन करें, और नीचे दिए गए प्रश्न का उत्तर दें। <br>निम्न तालिका पाँच अलग-अलग संस्थाओं A, B, C, D और E में विभिन्न विभागों में कर्मचारियों की संख्या को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171771356.png\" alt=\"rId25\" width=\"291\" height=\"191\"> <br>किस संस्था में कर्मचारियों की संख्या सर्वाधिक है ?</p>",
                    options_en: ["<p>A</p>", "<p>E</p>", 
                                "<p>B</p>", "<p>D</p>"],
                    options_hi: ["<p>A</p>", "<p>E</p>",
                                "<p>B</p>", "<p>D</p>"],
                    solution_en: "<p>11.(a)<br>Number of employees in organization A = 5825<br>Number of employees in organization E = 5625<br>Number of employees in organization B = 5703<br>Number of employees in organization D = 5613<br>So we can clearly see that organization A has the maximum number of employees.</p>",
                    solution_hi: "<p>11.(a)<br>संगठन A में कर्मचारियों की संख्या = 5825<br>संगठन E में कर्मचारियों की संख्या = 5625<br>संगठन B में कर्मचारियों की संख्या = 5703<br>संगठन D में कर्मचारियों की संख्या = 5613<br>अतः हम स्पष्ट रूप से देख सकते हैं कि संगठन A में कर्मचारियों की संख्या सबसे अधिक है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Study the following pie chart and answer the below question:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171771481.png\" alt=\"rId26\" width=\"227\" height=\"193\"> <br>How much per cent is more expense on rent than the expense on others ?</p>",
                    question_hi: "<p>2. निम्नलिखित पाई चार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171771654.png\" alt=\"rId27\" width=\"228\" height=\"194\"> <br>Total estimated yearly expenses = कुल अनुमानित वार्षिक व्यय, Food items = खाद्य वस्तुएं, Travel = यात्रा, Education = शिक्षा, Rent = किराया Saving = बचत Others = अन्य मदें<br>अन्य मदों पर किए गए व्यय की तुलना में किराये पर कितने प्रतिशत अधिक व्यय किया गया है ?</p>",
                    options_en: ["<p>150%</p>", "<p>10%</p>", 
                                "<p>50%</p>", "<p>100%</p>"],
                    options_hi: ["<p>150%</p>", "<p>10%</p>",
                                "<p>50%</p>", "<p>100%</p>"],
                    solution_en: "<p>12.(d)<br>According to the question,<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>20</mn><mo>-</mo><mn>10</mn><mo>)</mo></mrow><mn>10</mn></mfrac></math> &times; 100 = 100%</p>",
                    solution_hi: "<p>12.(d)<br>प्रश्न के अनुसार,<br>आवश्यक% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>20</mn><mo>-</mo><mn>10</mn><mo>)</mo></mrow><mn>10</mn></mfrac></math> &times; 100 = 100%</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The following table gives the number of different types of batteries sold by a company over the years (numbers in hundreds). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171771769.png\" alt=\"rId28\" width=\"290\" height=\"135\"> <br>What is the difference in the number of 7AH batteries sold in 2021 and 2022 ?</p>",
                    question_hi: "<p>13. निम्न तालिका किसी कंपनी द्वारा पिछले कुछ वर्षों में बेची गई विभिन्न प्रकार की बैटरियों की संख्या प्रदान करती है (संख्या सैकड़ों में) । <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171771893.png\" alt=\"rId29\" width=\"313\" height=\"142\"> <br>2021 और 2022 में बेची गई 7AH बैटरियों की संख्या में कितना अंतर है ?</p>",
                    options_en: ["<p>300</p>", "<p>200</p>", 
                                "<p>500</p>", "<p>700</p>"],
                    options_hi: ["<p>300</p>", "<p>200</p>",
                                "<p>500</p>", "<p>700</p>"],
                    solution_en: "<p>13.(c)<br>Required difference = 4500 - 4000 = 500 batteries</p>",
                    solution_hi: "<p>13.(c)<br>आवश्यक अंतर = 4500 - 4000 = 500 बैटरी</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Study the given pie-chart and answer the question that follows.<br>The pie-chart shows the expenditure of a family on different items and their savings throughout the year 2021.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171772023.png\" alt=\"rId30\" width=\"221\" height=\"221\"> <br>If the total income for the year was ₹1,00,000, then the difference in the expenses (in ₹) between housing and transport was :</p>",
                    question_hi: "<p>14. दिए गए पाई-चार्ट का अध्ययन करें और निम्नलिखित प्रश्न का उत्तर दें।<br>पाई-चार्ट वर्ष 2021 के दौरान विभिन्न मदों पर एक परिवार के द्वारा किए गए खर्च और उनकी बचत को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171772159.png\" alt=\"rId31\" width=\"222\" height=\"222\"> <br>Food - भोजन <br>Clothing - वस्त्र <br>Housing - आवास <br>Education of children - बच्चों की शिक्षा <br>Saving - बचत <br>Other - अन्य <br>Transport- परिवहन<br>यदि वर्ष की कुल आय ₹1,00,000 थी, तो आवास और परिवहन के बीच खर्चो में अंतर (₹ में) कितना था ?</p>",
                    options_en: ["<p>9,500</p>", "<p>10,500</p>", 
                                "<p>10,000</p>", "<p>9,000</p>"],
                    options_hi: ["<p>9,500</p>", "<p>10,500</p>",
                                "<p>10,000</p>", "<p>9,000</p>"],
                    solution_en: "<p>14.(c) Given,<br>Total income = ₹ 1,00,000<br>Required difference = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>15</mn><mo>-</mo><mn>5</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> &times; 1,00,000 = ₹ 10,000</p>",
                    solution_hi: "<p>14.(c) दिया गया,<br>कुल आय = ₹ 1,00,000<br>आवश्यक अंतर = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>15</mn><mo>-</mo><mn>5</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> &times; 1,00,000 = ₹ 10,000</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Study the given bar graph carefully and answer the question that follows. Increment of four employees (in ₹) in the months of May, June and July. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171772309.png\" alt=\"rId32\" width=\"309\" height=\"219\"> <br>What is Kamal&rsquo;s overall average increment in three months ?</p>",
                    question_hi: "<p>15. दिए गए बार ग्राफ का ध्यानपूर्वक अध्ययन कीजिए, और निम्नलिखित प्रश्न का उत्तर दीजिए। चार कर्मचारियों की मई, जून और जुलाई महीनों में हुई वेतन-वृद्धि (₹में)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737171772420.png\" alt=\"rId33\" width=\"267\" height=\"195\"> <br>Increment = वेतन वृद्धि <br>Employee = कर्मचारी<br>May = मई <br>June = जून <br>July = जुलाई <br>तीन महीनों में कमल की कुल औसत वेतन-वृद्धि कितनी है ?</p>",
                    options_en: ["<p>₹60</p>", "<p>₹75</p>", 
                                "<p>₹70</p>", "<p>₹65</p>"],
                    options_hi: ["<p>₹60</p>", "<p>₹75</p>",
                                "<p>₹70</p>", "<p>₹65</p>"],
                    solution_en: "<p>15.(d)<br>Kamal&rsquo;s average increment in 3 months = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>65</mn><mo>+</mo><mn>55</mn><mo>+</mo><mn>75</mn></mrow><mn>3</mn></mfrac></math> = 65</p>",
                    solution_hi: "<p>15.(d)<br>3 महीने में कमल की औसत वेतन वृद्धि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>65</mn><mo>+</mo><mn>55</mn><mo>+</mo><mn>75</mn></mrow><mn>3</mn></mfrac></math> = 65</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>