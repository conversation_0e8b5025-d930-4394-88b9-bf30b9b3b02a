<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">20:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Times New Roman;\">A sports-goods shop has tennis balls of 3 colours - red, green and white. The number of white balls is 60% more than the number of red balls and the number of green balls is 12.5% less than the number of while balls. </span><span style=\"font-family: Times New Roman;\">If the total</span><span style=\"font-family: Times New Roman;\"> number of balls is 120, then how many green balls are there?.</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">1. </span><span style=\"font-family: Baloo;\">एक खेल के सामान की दुकान में 3 रंगों की टेनिस गेंदें हैं - लाल, हरा और सफेद। सफेद गेंदों की संख्या लाल गेंदों की संख्या से 60% अधिक है और हरी गेंदों की संख्या, सफेद गेंदों की संख्या से 12.5% ​​कम है। यदि गेंदों की कुल संख्या 120 है, तो कितनी हरी गेंदें हैं?</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>48</mn></math></p>", "<p>40</p>", 
                                "<p>30</p>", "<p>42</p>"],
                    options_hi: ["<p>48</p>", "<p>40</p>",
                                "<p>30</p>", "<p>42</p>"],
                    solution_en: "<p>1.(d)<br><span style=\"font-family: Times New Roman;\">Let the Red balls = 100 unit</span><br><span style=\"font-family: Times New Roman;\">So, white balls = 160 unit</span><br><span style=\"font-family: Times New Roman;\">Green balls = 140 unit</span><br><span style=\"font-family: Times New Roman;\">Total = 100 + 160 + 140 = 400 units</span><br><span style=\"font-family: Times New Roman;\">400 unit = 120</span><br><span style=\"font-family: Times New Roman;\">140 unit = 42</span><br><span style=\"font-family: Times New Roman;\">Number of green balls = 42</span></p>",
                    solution_hi: "<p>1.(d)<br><span style=\"font-family: Baloo;\">माना लाल गेंदों की संख्या </span><span style=\"font-family: Times New Roman;\">= 100 </span><span style=\"font-family: Baloo;\">इकाई</span><br><span style=\"font-family: Baloo;\">तो, सफेद गेंदें =</span><span style=\"font-family: Times New Roman;\"> 160</span><span style=\"font-family: Baloo;\"> इकाई</span><br><span style=\"font-family: Baloo;\">हरी गेंद </span><span style=\"font-family: Times New Roman;\">= 140</span><span style=\"font-family: Baloo;\"> इकाई</span><br><span style=\"font-family: Baloo;\">कुल =</span><span style=\"font-family: Times New Roman;\"> 100 + 160 + 140 = 400 </span><span style=\"font-family: Baloo;\">इकाई</span><br><span style=\"font-family: Times New Roman;\">400 </span><span style=\"font-family: Baloo;\">इकाई </span><span style=\"font-family: Times New Roman;\">= 120</span><br><span style=\"font-family: Times New Roman;\">140 </span><span style=\"font-family: Baloo;\">इकाई </span><span style=\"font-family: Times New Roman;\">&nbsp;= 42</span><br><span style=\"font-family: Baloo;\">हरी गेंदों की संख्या</span><span style=\"font-family: Times New Roman;\"> = 42</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Times New Roman;\">The following bar graph shows the demand and production (in Lakhs) of motorcycles of five different companies A, B, C, D and E in 2020.</span><br><strong id=\"docs-internal-guid-9cf95e52-7fff-580f-c793-105d7bf44b1e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfJIqwyW2PKX1KBc3wI3bKSZR456DWk87PIB3CMK6CnbBB0WUsJBsZQgsOOxP2LFhcA8I9e7aN9PEbc1Hc2_ezuGKZUKzQuBcLKdhMK--dpgXvO-6IzzoUTQ7_dUNbMVspdZUKObPHycAJPjxydUG44JMM?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"350\" height=\"250\"></strong><br><span style=\"font-family: Times New Roman;\">What is the ratio of the total production of motorcycles of companies A, B, C, D and E to that of the total demand of motorcycles of all the companies during the five years?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">2. </span><span style=\"font-family: Baloo;\">निम्नलिखित बार ग्राफ 2020 में पांच अलग-अलग कंपनियों A, B, C, D और E की मोटरसाइकिलों की मांग और उत्पादन (लाखों में) दिखाता है।</span><br><strong id=\"docs-internal-guid-9cf95e52-7fff-580f-c793-105d7bf44b1e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfJIqwyW2PKX1KBc3wI3bKSZR456DWk87PIB3CMK6CnbBB0WUsJBsZQgsOOxP2LFhcA8I9e7aN9PEbc1Hc2_ezuGKZUKzQuBcLKdhMK--dpgXvO-6IzzoUTQ7_dUNbMVspdZUKObPHycAJPjxydUG44JMM?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"350\" height=\"250\"></strong><br><span style=\"font-family: Baloo;\">कंपनी A, B, C, D और E की मोटरसाइकिलों के कुल उत्पादन का पांच वर्षों के दौरान सभी कंपनियों की मोटरसाइकिलों की कुल मांग से अनुपात कितना है?</span></p>",
                    options_en: ["<p>53 : 50</p>", "<p>50 : 53</p>", 
                                "<p>100 : 54</p>", "<p>50 : 57</p>"],
                    options_hi: ["<p>53 : 50</p>", "<p>50 : 53</p>",
                                "<p>100 : 54</p>", "<p>50 : 57</p>"],
                    solution_en: "<p>2.(a)<br><span style=\"font-family: Times New Roman;\">Total production of motorcycles of companies A, B, C, D and E = 265</span><br><span style=\"font-family: Times New Roman;\">Total demand of motorcycles of companies A, B, C, D and E = 250</span><br><span style=\"font-family: Times New Roman;\">then ,&nbsp;</span><br><span style=\"font-family: Times New Roman;\">production : demand = 265 : 250 = 53 : 50</span></p>",
                    solution_hi: "<p>2.(a)<br><span style=\"font-family: Times New Roman;\">कंपनियों A , B , C , D और E की मोटरसाइकिलों का कुल उत्पादन = 265</span><br><span style=\"font-family: Times New Roman;\">कंपनियों A , B , C , D और E की मोटरसाइकिलों का कुल &nbsp;मांग = 250</span><br><span style=\"font-family: Times New Roman;\">फिर,&nbsp;</span><br><span style=\"font-family: Times New Roman;\">उत्पादन : मांग&nbsp; = 265 : 250 = 53 : 50</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Times New Roman;\">Each one of five men independently can complete a work in 20 days. The work is started by one person. The next day one more person joins and every next day one more person joins. From the fifth day, five persons continued working as a team. In how many days, will the work be completed?</span></p>",
                    question_hi: "<p>3. <span style=\"font-family: Baloo;\">पांच में से प्रत्येक व्यक्ति स्वतंत्र रूप से एक कार्य को 20 दिनों में पूरा कर सकता है। काम एक व्यक्ति द्वारा शुरू किया जाता है। अगले दिन एक और व्यक्ति जुड़ता है और हर अगले दिन एक और व्यक्ति जुड़ता है। पांचवें दिन से, पांच व्यक्ति एक टीम के रूप में कार्य करना जारी रखते हैं। कार्य कितने दिनों में पूरा होगा?</span></p>",
                    options_en: ["<p>2</p>", "<p>6</p>", 
                                "<p>3</p>", "<p>5</p>"],
                    options_hi: ["<p>2</p>", "<p>6</p>",
                                "<p>3</p>", "<p>5</p>"],
                    solution_en: "<p>3.(b)<br><span style=\"font-family: Times New Roman;\">Let the total work = 20 and</span><br><span style=\"font-family: Times New Roman;\">efficiency of Each man = 1</span><br><span style=\"font-family: Times New Roman;\">Day 1, work done by an employee = 1 unit, remaining = 19</span><br><span style=\"font-family: Times New Roman;\">Day 2, work done by 2 employee = 2 unit, remaining = 17</span><br><span style=\"font-family: Times New Roman;\">Day 3, work done by 3 employee = 3 unit, remaining = 14</span><br><span style=\"font-family: Times New Roman;\">Day 4, work done by 4 employee = 4 unit, remaining = 10</span><br><span style=\"font-family: Times New Roman;\">Day 5, work done by 5 employee = 5 unit, remaining = 5</span><br><span style=\"font-family: Times New Roman;\">Day 6, work done by 5 employee = 5 unit, remaining = 0</span><br><span style=\"font-family: Times New Roman;\">So, Total work to be completed in 6 days.</span></p>",
                    solution_hi: "<p>3.(b)<br><span style=\"font-family: Baloo;\">माना , कुल कार्य = 20 इकाई और</span><br><span style=\"font-family: Baloo;\">प्रत्येक व्यक्ति की क्षमता = 1 इकाई</span><br><span style=\"font-family: Baloo;\">पहला दिन एक कर्मचारी द्वारा किया गया कार्य = 1,शेष कार्य = 19 इकाई</span><br><span style=\"font-family: Baloo;\">दूसरा दिन, 2 कर्मचारी द्वारा किया गया कार्य = 2 इकाई , शेष = 17 इकाई</span><br><span style=\"font-family: Baloo;\">तीसरे दिन, 3 कर्मचारी द्वारा किया गया कार्य = 3 इकाई , शेष = 14 इकाई</span><br><span style=\"font-family: Baloo;\">चौथा दिन , 4 कर्मचारी द्वारा किया गया कार्य = 4 इकाई , शेष = 10 इकाई</span><br><span style=\"font-family: Baloo;\">पाँचवा दिन , 5 कर्मचारी द्वारा किया गया कार्य = 5 इकाई , शेष = 5 इकाई&nbsp;</span><br><span style=\"font-family: Baloo;\">छठा दिन, 5 कर्मचारी द्वारा किया गया कार्य = 5 इकाई , शेष = 0</span><br><span style=\"font-family: Baloo;\">अत: कुल कार्य को 6 दिनों में पूरा होगा </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Times New Roman;\"> In triangle ABC, D is a point on BC such that BD: DC = 3: 4. E is a point on AD such that AE:ED = 2 : 3. Find the ratio&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>area</mi><mo>(</mo><mo>&#8710;</mo><mi>ECD</mi><mo>)</mo><mo>:</mo><mi>area</mi><mo>(</mo><mo>&#8710;</mo><mi>AEB</mi><mo>)</mo><mo>.</mo></math></span></p>",
                    question_hi: "<p>4.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">त्रिभुज ABC में, D, BC पर एक ऐसा बिंदु है कि BD: DC = 3:4। E AD पर एक ऐसा बिंदु है कि AE:ED = 2:3। क्षेत्र (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>ECD): क्षेत्र (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>AEB) अनुपात ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>9 : 8</p>", "<p>1 : 2</p>", 
                                "<p>2 : 1</p>", "<p>8 : 9</p>"],
                    options_hi: ["<p>9 : 8</p>", "<p>1 : 2</p>",
                                "<p>2 : 1</p>", "<p>8 : 9</p>"],
                    solution_en: "<p>4.(c)<br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Area</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>ABC</mi></mrow><mrow><mi>Area</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>ADC</mi></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">(triangles with same height)</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Area</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>ABC</mi></mrow><mrow><mi>Area</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>BEC</mi></mrow></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">(Triangles with the same base)</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Area</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>ECD</mi></mrow><mrow><mi>Area</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>ABE</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>5</mn></mfrac></mstyle><mo>&#215;</mo><mn>4</mn></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>5</mn></mfrac></mstyle><mo>&#215;</mo><mn>3</mn></mrow></mfrac><mo>&#160;</mo></math>&nbsp;</span><span style=\"font-family: Times New Roman;\">= 2 : 1</span></p>",
                    solution_hi: "<p>4.(c)<br><span style=\"font-family: Times New Roman;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>ABC</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mo>&#160;</mo><mi>ADC</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Times New Roman;\">(</span><span style=\"font-family: Baloo;\">समान ऊँचाई वाले त्रिभुज)</span><br><span style=\"font-family: Times New Roman;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>ABC</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mo>&#160;</mo><mi>BEC</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">(</span><span style=\"font-family: Baloo;\">समान आधार वाले त्रिभुज</span><span style=\"font-family: Times New Roman;\">)</span><br><span style=\"font-family: Times New Roman;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>ECD</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mo>&#160;</mo><mi>ABE</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac><mo>=</mo><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&#215;</mo><mn>4</mn></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>5</mn></mfrac><mo>&#215;</mo><mn>3</mn></mstyle></mfrac><mo>&#160;</mo></math>= 2 : 1</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Times New Roman;\">What is the area of the square (in cm</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\">) whose vertices lie on a circle of radius 5 cm?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">5. </span><span style=\"font-family: Baloo;\">उस वर्ग का क्षेत्रफल (सेमी&sup2; में) क्या है जिसके शीर्ष 5 सेमी त्रिज्या वाले वृत्त पर स्थित हैं?</span></p>",
                    options_en: ["<p>100</p>", "<p>80</p>", 
                                "<p>50</p>", "<p>75</p>"],
                    options_hi: ["<p>100</p>", "<p>80</p>",
                                "<p>50</p>", "<p>75</p>"],
                    solution_en: "<p>5.(c)<br><span style=\"font-family: Times New Roman;\">Radius = 5 cm</span><br><span style=\"font-family: Times New Roman;\">Diameter = diagonal of the square = 10 cm</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>a = 10 cm</span><br><span style=\"font-family: Times New Roman;\">a = 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></span><br><span style=\"font-family: Times New Roman;\">Area = (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\">)&sup2; = 50 cm&sup2;</span></p>",
                    solution_hi: "<p>5.(c)<br><span style=\"font-family: Baloo;\">त्रिज्या </span><span style=\"font-family: Times New Roman;\">= 5 cm</span><br><span style=\"font-family: Baloo;\">व्यास = वर्ग का विकर्ण</span><span style=\"font-family: Times New Roman;\"> = 10 cm</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>a = 10 cm</span><br><span style=\"font-family: Times New Roman;\">a = 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></span><br><span style=\"font-family: Baloo;\">क्षेत्रफल</span><span style=\"font-family: Times New Roman;\"> = (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></span><span style=\"font-family: Times New Roman;\">)&sup2; = 50 cm&sup2;</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Times New Roman;\">Simplify the following expression&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>of</mi><mo>&#160;</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#160;</mo></math>.</span></p>",
                    question_hi: "<p>6. <span style=\"font-family: Baloo;\"> निम्नलिखित व्यंजक को सरल कीजिए।</span></p>\n<p><span style=\"font-family: Baloo;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#2325;&#2366;</mi><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#160;</mo></math></span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>317</mn><mn>96</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>71</mn><mn>150</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>6</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>317</mn><mn>96</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>71</mn><mn>150</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>6</mn></mfrac></math></p>"],
                    solution_en: "<p>6.(d)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>of</mi><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>(</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>10</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>(</mo><mfrac><mn>35</mn><mn>8</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>-</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><mn>42</mn><mo>-</mo><mn>5</mn><mo>+</mo><mn>9</mn></mrow><mn>12</mn></mfrac><mo>=</mo><mfrac><mn>23</mn><mn>6</mn></mfrac></math></p>",
                    solution_hi: "<p>6.(d)<br><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#2325;&#2366;</mi><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#160;</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>(</mo><mfrac><mn>7</mn><mn>16</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>10</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mo>(</mo><mfrac><mn>35</mn><mn>8</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mn>7</mn><mn>2</mn></mfrac><mo>-</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><mn>42</mn><mo>-</mo><mn>5</mn><mo>+</mo><mn>9</mn></mrow><mn>12</mn></mfrac><mo>=</mo><mfrac><mn>23</mn><mn>6</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Times New Roman;\">If 4sin&sup2;</span><span style=\"font-family: Gungsuh;\">(2x-10)&deg; = 3, 0 &le; (2x-10) &le; 90, then find the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>Sin</mi><mn>4</mn></msup><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn><mo>)</mo><mo>&#176;</mo><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn><mo>)</mo><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>15</mn><mo>)</mo><mo>&#176;</mo><mi>cos</mi><mo>&#178;</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>15</mn><mo>)</mo><mo>&#176;</mo></mrow></mfrac></math>.</span></p>",
                    question_hi: "<p>7. <span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">4sin&sup2;</span><span style=\"font-family: Gungsuh;\">(2x-10)&deg; = 3, 0 &le; (2x-10) &le; 90</span><span style=\"font-family: Baloo;\"> है, तो</span><span style=\"font-family: Times New Roman;\"> <span style=\"font-family: Gungsuh;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>Sin</mi><mn>4</mn></msup><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn><mo>)</mo><mo>&#176;</mo><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn><mo>)</mo><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>15</mn><mo>)</mo><mo>&#176;</mo><mi>cos</mi><mo>&#178;</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>15</mn><mo>)</mo><mo>&#176;</mo></mrow></mfrac></math></span></span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Baloo;\">का मान ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>1<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>5/8<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>-5/8<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>-1</p>"],
                    options_hi: ["<p>1</p>", "<p>5/8<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>-5/8</p>", "<p>-1</p>"],
                    solution_en: "<p>7.(b)<br><span style=\"font-family: Times New Roman;\">4sin&sup2;(2x - 10)&deg; = 3</span><br><span style=\"font-family: Times New Roman;\">sin&sup2;(2x - 10)&deg; = 3/4</span><br><span style=\"font-family: Times New Roman;\">sin(2x - 10)&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></span><br><span style=\"font-weight: 400;\">(2x-10)</span>&deg; = 60&shy;&deg;<br><span style=\"font-family: Times New Roman;\">x = 35&deg;</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>Sin</mi><mn>4</mn></msup><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn><mo>)</mo><mo>&#176;</mo><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn><mo>)</mo><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>15</mn><mo>)</mo><mo>&#176;</mo><mi>cos</mi><mo>&#178;</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>15</mn><mo>)</mo><mo>&#176;</mo></mrow></mfrac></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><msup><mi>Sin</mi><mn>4</mn></msup><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>4</mn></msup><mn>30</mn><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>sin</mi><mo>&#178;</mo><mn>90</mn><mo>&#176;</mo><mo>&#160;</mo><mi>cos</mi><mo>&#178;</mo><mn>90</mn><mo>&#176;</mo></mrow></mfrac></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>4</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>4</mn></msup></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mo>.</mo><mn>1</mn><mo>.</mo><mn>0</mn></mrow></mfrac></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mn>5</mn><mn>8</mn></mfrac></math></span></p>",
                    solution_hi: "<p>7.(b)<br><span style=\"font-family: Times New Roman;\">4sin&sup2;(2x - 10)&deg; = 3</span><br><span style=\"font-family: Times New Roman;\">sin&sup2;(2x - 10)&deg; = 3/4</span><br><span style=\"font-family: Times New Roman;\">sin(2x - 10)&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></span><br><span style=\"font-weight: 400;\">(2x-10)</span>&deg; = 60&shy;&deg;<br><span style=\"font-family: Times New Roman;\">x = 35&deg;</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>Sin</mi><mn>4</mn></msup><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn><mo>)</mo><mo>&#176;</mo><mo>+</mo><msup><mi>cos</mi><mn>4</mn></msup><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn><mo>)</mo><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>15</mn><mo>)</mo><mo>&#176;</mo><mi>cos</mi><mo>&#178;</mo><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>15</mn><mo>)</mo><mo>&#176;</mo></mrow></mfrac></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><msup><mi>Sin</mi><mn>4</mn></msup><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>4</mn></msup><mn>30</mn><mo>&#176;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>sin</mi><mo>&#178;</mo><mn>90</mn><mo>&#176;</mo><mo>&#160;</mo><mi>cos</mi><mo>&#178;</mo><mn>90</mn><mo>&#176;</mo></mrow></mfrac></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>4</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>4</mn></msup></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mo>.</mo><mn>1</mn><mo>.</mo><mn>0</mn></mrow></mfrac></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mn>5</mn><mn>8</mn></mfrac></math></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Times New Roman;\">The value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mi>tan</mi><mo>(</mo><mn>60</mn><mo>&#176;</mo><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>&#160;</mo><mi>tan</mi><mo>(</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">is:</span></p>",
                    question_hi: "<p>8. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mi>tan</mi><mo>(</mo><mn>60</mn><mo>&#176;</mo><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mi>tan</mi><mo>(</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>+</mo><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow></mfrac></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">का मान ज्ञात कीजिए।</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>&#8730;</mo><mn>2</mn></mrow></mfrac></math><span style=\"font-family: Gungsuh;\"> </span></p>", "<p>1</p>", 
                                "<p>2</p>", "<p>&radic;2</p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math><span style=\"font-family: Gungsuh;\"> </span></p>", "<p>1<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>2<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>&radic;2</p>"],
                    solution_en: "<p>8.(d)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mi>tan</mi><mo>(</mo><mn>60</mn><mo>&#176;</mo><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>&#160;</mo><mi>tan</mi><mo>(</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mi>sin</mi><mo>&#178;</mo><mo>&#160;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>&#160;</mo><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>&#178;</mo><mo>&#160;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow></mfrac></math><br><span style=\"font-family: Times New Roman;\">Put <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">= 0&deg;</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mi>tan</mi><mo>(</mo><mn>60</mn><mo>&#176;</mo><mo>)</mo><mi>tan</mi><mo>(</mo><mn>30</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo><mo>+</mo><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><msqrt><mn>3</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></mrow></mfrac><mo>=</mo><msqrt><mn>2</mn></msqrt></math></span></p>",
                    solution_hi: "<p>8.(d)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mi>tan</mi><mo>(</mo><mn>60</mn><mo>&#176;</mo><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>&#160;</mo><mi>tan</mi><mo>(</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>+</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>+</mo><mi>sin</mi><mo>&#178;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>-</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow></mfrac></math><br><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">= 0&deg;,रखने पर&nbsp;</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mi>tan</mi><mo>(</mo><mn>60</mn><mo>&#176;</mo><mo>)</mo><mo>&#160;</mo><mi>tan</mi><mo>(</mo><mn>30</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>sin</mi><mo>&#178;</mo><mo>&#160;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>&#178;</mo><mo>&#160;</mo><mo>(</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><msqrt><mn>3</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></mfrac><mo>=</mo><msqrt><mn>2</mn></msqrt></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Times New Roman;\"> If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>=</mo><mn>5</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn><mo>,</mo><mi mathvariant=\"normal\">x</mi><mo>&#62;</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\"> then what is the value of (2x</span><span style=\"font-family: Times New Roman;\"> - x - 1)?</span></p>",
                    question_hi: "<p>9.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>=</mo><mn>5</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn><mo>,</mo><mi mathvariant=\"normal\">x</mi><mo>&#62;</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>,</mo></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">तो</span><span style=\"font-family: Times New Roman;\"> (2x&sup2;</span><span style=\"font-family: Times New Roman;\"> - x - 1) </span><span style=\"font-family: Baloo;\">का मान क्या है?</span></p>",
                    options_en: ["<p>0<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>1<span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>-2</p>", "<p>2</p>"],
                    options_hi: ["<p>0</p>", "<p>1</p>",
                                "<p>-2<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>2</p>"],
                    solution_en: "<p>9.(a)<br><span style=\"font-family: Times New Roman;\">Short-Trick:</span><br><span style=\"font-family: Times New Roman;\">Put x = 1</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>=</mo><mn>5</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></math></span><br><span style=\"font-family: Times New Roman;\">4(1) = 5(1) - 1</span><br><span style=\"font-family: Times New Roman;\">4 = 4 (satisfy)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>1</mn><mo>)</mo></math> = 2(1) - 1 - 1 = 0</p>",
                    solution_hi: "<p>9.(a)<br><span style=\"font-family: Times New Roman;\">x = 1 </span><span style=\"font-family: Baloo;\">रखें।</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mi mathvariant=\"normal\">x</mi><mn>4</mn></msup><mo>=</mo><mn>5</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></math></span><br><span style=\"font-family: Times New Roman;\">4(1) = 5(1) - 1</span><br><span style=\"font-family: Times New Roman;\">4 = 4 </span><span style=\"font-family: Baloo;\">(सभी शर्तों को पूरा करता है)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mi>x</mi><mo>-</mo><mn>1</mn><mo>)</mo><mo>&#160;</mo></math>= 2(1) - 1 - 1 = 0</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Times New Roman;\"> A man can row a distance of 8 km downstream in a certain time and can row 6 km upstream in the same time. If he rows 24 km upstream and the same distance downstream in 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"12px\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math></span><span style=\"font-family: Times New Roman;\">hours, then the speed (in km/h) of the current is:</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">10.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक आदमी एक निश्चित समय में धारा के अनुकूल 8 किमी की दूरी तय कर सकता है और उसी समय में धारा के प्रतिकूल 6 किमी की दूरी तय कर सकता है। यदि वह धारा के प्रतिकूल 24 किमी और धारा के अनुकूल समान दूरी <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"12px\"><mn>1</mn><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math></span><span style=\"font-family: Baloo;\"> घंटे में पार करता है, तो धारा की गति (किमी/घंटा में) है:</span></p>",
                    options_en: ["<p>4</p>", "<p>4.5</p>", 
                                "<p>3</p>", "<p>2.5</p>"],
                    options_hi: ["<p>4</p>", "<p>4.5</p>",
                                "<p>3</p>", "<p>2.5</p>"],
                    solution_en: "<p>10.(a)<br><span style=\"font-family: Times New Roman;\">Let the downstream = x + y</span><br><span style=\"font-family: Times New Roman;\">And the upstream = x - y</span><br><span style=\"font-family: Times New Roman;\">If the time is same then the ratio of the speed and the distance is same</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac><mo>=</mo><mfrac><mn>8</mn><mn>6</mn></mfrac></math><br><span style=\"font-family: Times New Roman;\">X = 7 unit and y = 1 unit</span><br><span style=\"font-family: Times New Roman;\">According to the question</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac><mo>+</mo><mo>&#160;</mo><mfrac><mn>24</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mn>8</mn><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi></mrow></mfrac><mo>+</mo><mo>&#160;</mo><mfrac><mn>24</mn><mrow><mn>6</mn><mi>u</mi><mi>n</mi><mi>i</mi><mi>t</mi></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math><br><span style=\"font-family: Times New Roman;\">1 = 4km/h so the speed of the current is 4km/hr</span></p>",
                    solution_hi: "<p>10.(a)<br><span style=\"font-family: Baloo;\">माना धारा के अनुकूल गति</span><span style=\"font-family: Times New Roman;\"> = x + y</span><br><span style=\"font-family: Baloo;\">और धारा के प्रतिकूल गति</span><span style=\"font-family: Times New Roman;\"> = x - y</span><br><span style=\"font-family: Baloo;\">यदि समय समान है तो गति और दूरी का अनुपात समान होता है</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac><mo>=</mo><mfrac><mn>8</mn><mn>6</mn></mfrac></math><br><span style=\"font-family: Baloo;\">X = 7 इकाई </span><span style=\"font-family: Baloo;\">और </span><span style=\"font-family: Times New Roman;\">y = 1</span><span style=\"font-family: Baloo;\"> इकाई</span><br><span style=\"font-family: Baloo;\">प्रश्न के अनुसार,</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac><mo>+</mo><mo>&#160;</mo><mfrac><mn>24</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math><br><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mn>8</mn><mi>&#2311;&#2325;&#2366;&#2312;</mi></mrow></mfrac><mo>+</mo><mo>&#160;</mo><mfrac><mn>24</mn><mrow><mn>6</mn><mi>&#2311;&#2325;&#2366;&#2312;</mi></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math><br><span style=\"font-family: Baloo;\">1 इकाई = 4 km/h</span><br><span style=\"font-family: Baloo;\">तो धारा की गति</span><span style=\"font-family: Times New Roman;\"> = 4 km/hr </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Times New Roman;\">The pie chart shows the percentage distribution of a total of 800 employees in different departments of a company</span><br><strong id=\"docs-internal-guid-a6bebed0-7fff-dcf0-2d38-4ed13e1d6d6f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJXESbzPIfTf_xubMYsQbJ44N7Fay3aFU7hNwpDSFrJkGWT4KHc8Lut_CTu1KKyHginAM60H2gEfHF_XBWaY4E9itmffG6YXtRcybcqZaZP8QDzxdQIrsqNAAFyl_GWG4puBzH0CbmwjcNjpyAxM6bZYfX?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"225\" height=\"186\"></strong><br><span style=\"font-family: Times New Roman;\">How many employees are working in the field of marketing?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">11. </span><span style=\"font-family: Baloo;\">पाई चार्ट एक कंपनी के विभिन्न विभागों में कुल 800 कर्मचारियों का प्रतिशत वितरण दर्शाता है।</span><br><strong id=\"docs-internal-guid-a6bebed0-7fff-dcf0-2d38-4ed13e1d6d6f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJXESbzPIfTf_xubMYsQbJ44N7Fay3aFU7hNwpDSFrJkGWT4KHc8Lut_CTu1KKyHginAM60H2gEfHF_XBWaY4E9itmffG6YXtRcybcqZaZP8QDzxdQIrsqNAAFyl_GWG4puBzH0CbmwjcNjpyAxM6bZYfX?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"214\" height=\"177\"></strong><br><span style=\"font-family: Baloo;\">मार्केटिंग के क्षेत्र में कितने कर्मचारी कार्यरत हैं?</span></p>",
                    options_en: ["<p>240</p>", "<p>120</p>", 
                                "<p>200</p>", "<p>176</p>"],
                    options_hi: ["<p>240</p>", "<p>120</p>",
                                "<p>200</p>", "<p>176</p>"],
                    solution_en: "<p>11.(d)<br><span style=\"font-family: Times New Roman;\">Number of employees in marketing = 22%</span><br><span style=\"font-family: Times New Roman;\">Required answer = 22% of 800 = 176</span></p>",
                    solution_hi: "<p>11.(d)<br><span style=\"font-family: Baloo;\">विपणन में कर्मचारियों की संख्या</span><span style=\"font-family: Times New Roman;\"> = 22%</span><br><span style=\"font-family: Baloo;\">आवश्यक उत्तर</span><span style=\"font-family: Times New Roman;\"> = 22% of 800 = 176</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Times New Roman;\">Bar graph shows the number of students enrolled for a vocational course in institutes A and B during 5 years from 2014 to 2018.</span><br><strong id=\"docs-internal-guid-c2747dd6-7fff-8b2b-1306-2a31617dd801\"><strong id=\"docs-internal-guid-2a5b9fd7-7fff-269b-2690-5d3abb8c0790\">&nbsp;<img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd5TXQqCe-Xy4dK98Cs_-WCRwoKuXm3NxhCv_UYUI0S4xOv9fDTMfqpD0iRHlS72ZnZ8hGgpbhN47HOXWW9tu25rTAZgKfhAe7HY6Knkhkiv5fuAveZc7jDZ5vNhQs8MG5nfMWZ9A?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"366\" height=\"223\"></strong></strong><br><span style=\"font-family: Times New Roman;\">The total number of students enrolled in institute B during 2014, 2016 and 2018 is what percent of the total number of students enrolled in institute A during the five years?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">12. </span><span style=\"font-family: Baloo;\">बार ग्राफ 2014 से 2018 तक 5 वर्षों के दौरान संस्थानों A और B में व्यावसायिक पाठ्यक्रम के लिए नामांकित छात्रों की संख्या को दर्शाता है।</span><br><strong id=\"docs-internal-guid-24102432-7fff-b8a3-8686-4d5f6cfb3e2c\"><strong id=\"docs-internal-guid-be315e2d-7fff-1bb3-5c14-7930f55caf15\">&nbsp;<img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXff-KsOts7mRQcrol69E95diFSlA4pT9rAZWVXwivhX1Zq_V1tuThtZtFWrpKcVwvyiNqrOwxylQR-B3ieRJt_kM5Yc7HKoQenY74k4_lczy8aG7U98FnZ9ZlZm8aVQ11yA7jXQPg?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"398\" height=\"243\"></strong></strong><br><span style=\"font-family: Baloo;\">2014, 2016 और 2018 के दौरान संस्थान B में नामांकित छात्रों की कुल संख्या, पाँच वर्षों के दौरान संस्थान A में नामांकित छात्रों की कुल संख्या का कितना प्रतिशत है?</span></p>",
                    options_en: ["<p>49%</p>", "<p>66%</p>", 
                                "<p>75%</p>", "<p>57%</p>"],
                    options_hi: ["<p>49%</p>", "<p>66%</p>",
                                "<p>75%</p>", "<p>57%</p>"],
                    solution_en: "<p>12.(b)<br><span style=\"font-family: Times New Roman;\">Number of students in B during 2014, 2016 and 2018 = 225 + 375 + 225 = 825</span><br><span style=\"font-family: Times New Roman;\">Total number of students in A = 160 + 280 + 300 + 250 + 260 = 1250</span><br><span style=\"font-family: Times New Roman;\">x% of 1250 = 825</span><br><span style=\"font-family: Times New Roman;\">x =</span><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>825</mn><mn>1250</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\">= 66%</span></p>",
                    solution_hi: "<p>12.(b)<br><span style=\"font-family: Times New Roman;\">2014, 2016 </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> 2018 </span><span style=\"font-family: Baloo;\">के दौरान</span><span style=\"font-family: Times New Roman;\"> B </span><span style=\"font-family: Baloo;\">में छात्रों की संख्या</span><span style=\"font-family: Times New Roman;\"> = 225 + 375 + 225 = 825</span><br><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">में छात्रों की कुल संख्या</span><span style=\"font-family: Times New Roman;\">= 160 + 280 + 300 + 250 + 260 = 1250</span><br><span style=\"font-family: Times New Roman;\">x% of 1250 = 825</span><br><span style=\"font-family: Times New Roman;\">x =</span><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>825</mn><mn>1250</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\">= 66%</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. <span style=\"font-family: Times New Roman;\">The cost price and the marked price of an item are Rs.720 and Rs.900 respectively. When it is sold at a discount of x%, the profit&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">%. What is the value of x ?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">13. </span><span style=\"font-family: Baloo;\">एक वस्तु का क्रय मूल्य और अंकित मूल्य क्रमशः</span><span style=\"font-family: Times New Roman;\"> 720 </span><span style=\"font-family: Baloo;\">रुपये और</span><span style=\"font-family: Times New Roman;\"> 900 </span><span style=\"font-family: Baloo;\">रुपये है। जब इसे x% की छूट पर बेचा जाता है, तो लाभ</span><span style=\"font-family: Times New Roman;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>3</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">% </span><span style=\"font-family: Baloo;\">होता है।</span><span style=\"font-family: Times New Roman;\"> x </span><span style=\"font-family: Baloo;\">का मूल्य क्या है ?</span></p>",
                    options_en: ["<p>7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo> </mo></mrow><mn>7</mn></mfrac></math></p>", "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac><mo> </mo></math></p>", 
                                "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac><mo> </mo></math></p>", "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math></p>"],
                    options_hi: ["<p>7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math></p>", "<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math></p>",
                                "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>", "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math></p>"],
                    solution_en: "<p>13.(b)<br>720 + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>3</mn></mfrac></math>% of 720)= 900 - (x% of 900)<br>&rArr; 5x% of 240 + (x% of 900)= 180<br>&rArr; 12x + 9x = 180<br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>21</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>7</mn></mfrac></math><br>&rArr; x = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math></p>",
                    solution_hi: "<p>13.(b)<br>720 + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>3</mn></mfrac></math>% का 720)= 900 - (x% का 900)<br>&rArr; 5x% का 240 + (x% का 900)= 180<br>&rArr; 12x + 9x = 180<br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>21</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>7</mn></mfrac></math><br>&rArr; x = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Times New Roman;\">Points A and B are on a circle with center O. PAM and </span><span style=\"font-family: Times New Roman;\">PBN</span><span style=\"font-family: Gungsuh;\"> are tangents to the circle at A and B respectively from a point P outside the circle. Point Q is on the major arc AB such that &ang;QAM = 58&deg; and &ang;QBN = 50&deg;, then find the measure of &ang;APB.</span></p>",
                    question_hi: "<p>14. <span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Arial Unicode MS;\">बिंदु A और B केंद्र O वाले एक वृत्त पर हैं। PAM और PBN वृत्त के बाहर एक बिंदु P से क्रमशः A और B पर वृत्त की स्पर्श रेखाएँ हैं। बिंदु Q दीर्घ चाप AB पर इस प्रकार है कि QAM = 58&deg; और &ang;QBN = 50&deg; है, तो APB की माप ज्ञात कीजिए।</span></p>",
                    options_en: ["<p>30&deg;</p>", "<p>32&deg;</p>", 
                                "<p>36&deg;</p>", "<p>40&deg;</p>"],
                    options_hi: ["<p>30&deg;</p>", "<p>32&deg;</p>",
                                "<p>36&deg;</p>", "<p>40&deg;</p>"],
                    solution_en: "<p>14.(c)<br><strong id=\"docs-internal-guid-2b32ff41-7fff-95d5-5d90-02bf0904b103\">&nbsp;<img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXelfYjK3svW97gpzWcMHuZ3S2fXsHaruLE2xCnKxz8xnmR5ha5Xjf5-CVEcMhwCb0Viy1RDRJF-Q1xlmZK_7bSNiZnI1MCYgjKaTscVMwauElPHSdnEBSlHGw9jpnZhTdoCZbu800Zt0HuGEXMhlKtlFH4?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"210\" height=\"119\"></strong><br><span style=\"font-family: Gungsuh;\">&ang;QAM = &ang;QBA = 58&deg;</span><br><span style=\"font-family: Gungsuh;\">&ang;QAN = &ang;QAB = 50&deg;</span><br><span style=\"font-family: Gungsuh;\">&ang;AQB = 180&deg; - 58&deg; - 50&deg; = 72&deg;</span><br><span style=\"font-family: Gungsuh;\">&ang;AOB = 2 &times; 72&deg; = 144&deg;</span><br><span style=\"font-family: Gungsuh;\">&ang;APB = 180&deg; - 144&deg; = 36&deg;</span></p>",
                    solution_hi: "<p>14.(c)<br><strong id=\"docs-internal-guid-2b32ff41-7fff-95d5-5d90-02bf0904b103\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXelfYjK3svW97gpzWcMHuZ3S2fXsHaruLE2xCnKxz8xnmR5ha5Xjf5-CVEcMhwCb0Viy1RDRJF-Q1xlmZK_7bSNiZnI1MCYgjKaTscVMwauElPHSdnEBSlHGw9jpnZhTdoCZbu800Zt0HuGEXMhlKtlFH4?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"210\" height=\"119\"></strong><br><span style=\"font-family: Gungsuh;\">&ang;QAM = &ang;QBA = 58&deg;</span><br><span style=\"font-family: Gungsuh;\">&ang;QAN = &ang;QAB = 50&deg;</span><br><span style=\"font-family: Gungsuh;\">&ang;AQB = 180&deg; - 58&deg; - 50&deg; = 72&deg;</span><br><span style=\"font-family: Gungsuh;\">&ang;AOB = 2 &times; 72&deg; = 144&deg;</span><br><span style=\"font-family: Gungsuh;\">&ang;APB = 180&deg; - 144&deg; = 36&deg;</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. <span style=\"font-family: Times New Roman;\">The marked price of an article is Rs.240. A shopkeeper sells it by allowing 18% discount on its marked price and still gains 23%, what is the cost price (in Rs.) of the article?</span></p>",
                    question_hi: "<p>15. <span style=\"font-family: Baloo;\">एक वस्तु का अंकित मूल्य 240 रुपये है। एक दुकानदार इसे इसके अंकित मूल्य पर 18% की छूट देकर बेचता है और फिर भी 23% का लाभ प्राप्त करता है, वस्तु का लागत मूल्य (रु में) क्या है?</span></p>",
                    options_en: ["<p>200</p>", "<p>180</p>", 
                                "<p>160</p>", "<p>150</p>"],
                    options_hi: ["<p>200</p>", "<p>180</p>",
                                "<p>160</p>", "<p>150</p>"],
                    solution_en: "<p>15.(c)<br><span style=\"font-family: Times New Roman;\">Selling price =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"14px\"><mn>240</mn><mo>&#215;</mo><mfrac><mn>82</mn><mn>100</mn></mfrac><mo>&#160;</mo></mstyle></math></span><span style=\"font-family: Times New Roman;\">= 196.8</span><br><span style=\"font-family: Times New Roman;\">Cost price =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"14px\"><mn>196</mn><mo>.</mo><mn>8</mn><mo>&#215;</mo><mfrac><mn>100</mn><mn>123</mn></mfrac></mstyle></math> </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 160</span></p>",
                    solution_hi: "<p>15.(c)<br><span style=\"font-family: Baloo;\">विक्रय मूल्य</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn mathsize=\"14px\">240</mn><mo mathsize=\"14px\">&#215;</mo><mfrac><mn mathsize=\"14px\">82</mn><mn mathsize=\"14px\">100</mn></mfrac><mo mathsize=\"14px\">&#160;</mo></math></span><span style=\"font-family: Times New Roman;\">= 196.8</span><br><span style=\"font-family: Baloo;\">लागत मूल्य</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn mathsize=\"14px\">196</mn><mo mathsize=\"14px\">.</mo><mn mathsize=\"14px\">8</mn><mo mathsize=\"14px\">&#215;</mo><mfrac><mn mathsize=\"14px\">100</mn><mn mathsize=\"14px\">123</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 160</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. <span style=\"font-family: Times New Roman;\">If x&sup2;</span><span style=\"font-family: Times New Roman;\"> + 9y&sup2;</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">+ 4z&sup2;</span><span style=\"font-family: Times New Roman;\"> = 12(x - 2y + 2z) - 88, then the value of (x - 3y + z) is:</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">16. </span><span style=\"font-family: Baloo;\">यदि</span><span style=\"font-family: Times New Roman;\"> x&sup2;</span><span style=\"font-family: Times New Roman;\"> + 9y&sup2;</span><span style=\"font-family: Times New Roman;\"> + 4z&sup2;</span><span style=\"font-family: Times New Roman;\"> = 12(x - 2y + 2z) - 88, </span><span style=\"font-family: Baloo;\">तो</span><span style=\"font-family: Times New Roman;\"> (x - 3y+ z) </span><span style=\"font-family: Baloo;\">का मान है:</span></p>",
                    options_en: ["<p>10<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>13</p>", 
                                "<p>11</p>", "<p>5</p>"],
                    options_hi: ["<p>10<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>13<span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>11</p>", "<p>5</p>\n<p><span style=\"font-family: Times New Roman;\"> </span></p>"],
                    solution_en: "<p>16.(b)<br><span style=\"font-family: Times New Roman;\">x&sup2; + 9y&sup2; + 4z&sup2; = 12(x - 2y + 2z) - 88</span><br><span style=\"font-family: Times New Roman;\">x&sup2; - 12x + 9y&sup2; + 24y + 4z&sup2; - 24z + 88 = 0</span><br><span style=\"font-family: Times New Roman;\">x&sup2; - 12x + 36 + 9y&sup2; + 24y + 16 + 4z&sup2; - 24z + 36 = 0</span><br>(x - 6)&sup2; + (3y + 4)&sup2; + (2z - 6)&sup2; = 0<br><span style=\"font-family: Times New Roman;\">x = 6, y = -4/3 , z = 3</span><br><span style=\"font-weight: 400;\">(x - 3y + z) </span>= (6 + 4 +3) = 13</p>",
                    solution_hi: "<p>16.(b)<br><span style=\"font-family: Times New Roman;\">x&sup2; + 9y&sup2; + 4z&sup2; = 12(x - 2y + 2z) - 88</span><br><span style=\"font-family: Times New Roman;\">x&sup2; - 12x + 9y&sup2; + 24y + 4z&sup2; - 24z + 88 = 0</span><br><span style=\"font-family: Times New Roman;\">x&sup2; - 12x + 36 + 9y&sup2; + 24y + 16 + 4z&sup2; - 24z + 36 = 0</span><br>(x - 6)&sup2; + (3y + 4)&sup2; + (2z - 6)&sup2; = 0<br><span style=\"font-family: Times New Roman;\">x = 6, y = -4/3 , z = 3</span><br><span style=\"font-weight: 400;\">(x - 3y + z) </span>= (6 + 4 +3) = 13</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">17.</span><span style=\"font-family: Times New Roman;\"> The average score of 40 students in a class test is 45. Later on, it was found that at two places 25 was read as 35 and at one place 38 was read as 32. What is the actual average score of the class?</span></p>",
                    question_hi: "<p>17.<span style=\"font-family: Baloo;\"> एक कक्षा परीक्षा में 40 छात्रों का औसत अंक 45 है। बाद में, यह पाया गया कि दो स्थानों पर 25 को 35 और एक स्थान पर 38 को 32 के रूप में पढ़ा गया था। कक्षा का वास्तविक औसत अंक क्या है?</span></p>",
                    options_en: ["<p>45.35</p>", "<p>39.69</p>", 
                                "<p>44.65</p>", "<p>43.80</p>"],
                    options_hi: ["<p>45.35</p>", "<p>39.69</p>",
                                "<p>44.65</p>", "<p>43.80</p>"],
                    solution_en: "<p><span style=\"font-family: Times New Roman;\">17.(c)</span><br><span style=\"font-family: Times New Roman;\">Sum of score = 45 &times; 40 = 1800</span><br><span style=\"font-family: Times New Roman;\">Actual score = 1800 - 35 - 38 + 25 + 32 = 1784</span><br><span style=\"font-family: Times New Roman;\">Required Average =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1784</mn><mn>40</mn></mfrac></math>&nbsp;= </span><span style=\"font-family: Times New Roman;\">44.6</span></p>",
                    solution_hi: "<p>17.(c)<br><span style=\"font-family: Baloo;\">स्कोर का योग = 45 &times; 40 = 1800</span><br><span style=\"font-family: Baloo;\">वास्तविक स्कोर = 1800 - 35 - 38 + 25 + 32 = 1784</span><br><span style=\"font-family: Baloo;\">आवश्यक औसत =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1784</mn><mrow><mn>40</mn><mo>&#160;</mo></mrow></mfrac></math></span><span style=\"font-family: Times New Roman;\">= 44.6</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">18. </span><span style=\"font-family: Times New Roman;\">A man borrowed a certain sum and </span><span style=\"font-family: Times New Roman;\">agrees</span><span style=\"font-family: Times New Roman;\"> to repay it by paying Rs.4000 at the end of first year and Rs.7700 at the end of second year. If the rate of compound interest compounded annually is 10% per annum, then find the sum (in Rs.) borrowed?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">18.</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">एक व्यक्ति ने एक निश्चित राशि उधार ली और पहले वर्ष के अंत में 4000 रुपये और दूसरे वर्ष के अंत में 7700 रुपये का भुगतान करके इसे चुकाने के लिए सहमत हो गया। यदि वार्षिक रूप से संयोजित चक्रवृद्धि ब्याज की दर 10% प्रति वर्ष है, तो उधार ली गई राशि (रुपये में) ज्ञात कीजिए?</span></p>",
                    options_en: ["<p>11500</p>", "<p>11000</p>", 
                                "<p>9000</p>", "<p>10,000</p>"],
                    options_hi: ["<p>11500</p>", "<p>11000</p>",
                                "<p>9000</p>", "<p>10,000</p>"],
                    solution_en: "<p>18.(d)<br><span style=\"font-family: Times New Roman;\">Let the principal borrowed = 100x</span><br><span style=\"font-family: Times New Roman;\">Amount after 1 year = 110x</span><br><span style=\"font-family: Times New Roman;\">After paying amount = 110x - 4000</span><br><span style=\"font-family: Times New Roman;\">Amount after 2</span><span style=\"font-family: Times New Roman;\">nd</span><span style=\"font-family: Times New Roman;\"> year = (110x -4000) + 11x -400</span><br><span style=\"font-family: Times New Roman;\">110x - 4000 + 11x -400 = 7700</span><br><span style=\"font-family: Times New Roman;\">121x - 4400 = 7700</span><br><span style=\"font-family: Times New Roman;\">121x = 12100</span><br><span style=\"font-family: Times New Roman;\">x = 100 so, Amount = 10,000</span></p>",
                    solution_hi: "<p>18.(d)<br><span style=\"font-family: Baloo;\">माना की मूलधन</span><span style=\"font-family: Times New Roman;\"> = 100x</span><br><span style=\"font-family: Times New Roman;\">1 </span><span style=\"font-family: Baloo;\">वर्ष बाद की राशि</span><span style=\"font-family: Times New Roman;\"> = 110x</span><br><span style=\"font-family: Baloo;\">राशि चुकाने के बाद</span><span style=\"font-family: Times New Roman;\"> = 110x - 4000</span><br><span style=\"font-family: Baloo;\">दूसरे वर्ष के बाद की राशि </span><span style=\"font-family: Times New Roman;\">= (110x -4000) + 11x -400</span><br><span style=\"font-family: Times New Roman;\">110x - 4000 + 11x -400 = 7700</span><br><span style=\"font-family: Times New Roman;\">121x - 4400 = 7700</span><br><span style=\"font-family: Times New Roman;\">121x = 12100</span><br><span style=\"font-family: Times New Roman;\">x = 100 , </span><span style=\"font-family: Baloo;\">मूलधन</span><span style=\"font-family: Times New Roman;\">= 10,000</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. <span style=\"font-family: Times New Roman;\">The following Pie charts represent the distribution of candidates who were enrolled for competitive examination and the candidates (out of those enrolled) who passed the exam from five different institutes P, Q, R, S and T.</span><br><span style=\"font-family: Times New Roman;\">Fig(i) Total number of candidates enrolled in five different institutes = 7500</span><br><strong id=\"docs-internal-guid-f3f5a9b6-7fff-cafa-5377-28ddf27af15e\">&nbsp;<img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeoF010b0wZwGKJCorOT1qIgUzEe0NpLwaZNqmLAArqWUUXoh4c3aWHz0xSAx6c09xTxqS8pmuP48wtg6sxQSpNhba42-8wPl-Sr9lyw-V2FbOTefh5BjPgR9DAGlmvtUopC1GCnxqII4AT11oEGiBJ2xw?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"139\" height=\"138\"></strong><br><span style=\"font-family: Times New Roman;\">Fig (ii) Total number of candidates passed the examination from five institutes = 4000<br></span><img src=\"data:image/png;base64,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\" width=\"161\" height=\"149\"><br><span style=\"font-family: Times New Roman;\">What is the pass percentage for Institutes Q? (correct upto one decimal place)</span></p>",
                    question_hi: "<p>19. <span style=\"font-family: Baloo;\">निम्नलिखित पाई चार्ट प्रतियोगी परीक्षा के लिए नामांकित उम्मीदवारों और पांच अलग-अलग संस्थानों P, Q, R, S और T से परीक्षा उत्तीर्ण करने वाले उम्मीदवारों (नामांकित लोगों में से) के वितरण का प्रतिनिधित्व करते हैं।</span><br><span style=\"font-family: Baloo;\">Fig(i) पांच अलग-अलग संस्थानों में नामांकित उम्मीदवारों की कुल संख्या = 7500</span><br><strong id=\"docs-internal-guid-f3f5a9b6-7fff-cafa-5377-28ddf27af15e\">&nbsp;<img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeoF010b0wZwGKJCorOT1qIgUzEe0NpLwaZNqmLAArqWUUXoh4c3aWHz0xSAx6c09xTxqS8pmuP48wtg6sxQSpNhba42-8wPl-Sr9lyw-V2FbOTefh5BjPgR9DAGlmvtUopC1GCnxqII4AT11oEGiBJ2xw?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"139\" height=\"138\"></strong><br><span style=\"font-family: Baloo;\">Fig (ii)पांच संस्थानों से परीक्षा उत्तीर्ण करने वाले उम्मीदवारों की कुल संख्या = 4000</span><br><img src=\"data:image/png;base64,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\" width=\"161\" height=\"149\"><br><span style=\"font-family: Baloo;\">संस्थान Q के लिए उत्तीर्ण प्रतिशत क्या है? (एक दशमलव स्थान तक सही)</span></p>",
                    options_en: ["<p>42.7%</p>", "<p>80%</p>", 
                                "<p>48%</p>", "<p>71.1%</p>"],
                    options_hi: ["<p>42.7%</p>", "<p>80%</p>",
                                "<p>48%</p>", "<p>71.1%</p>"],
                    solution_en: "<p>19.(a)<br><span style=\"font-family: Times New Roman;\">Enrolled students in Q =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7500</mn><mo>&#215;</mo><mfrac><mn>30</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 2250</span><br><span style=\"font-family: Times New Roman;\">Pass students =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4000</mn><mo>&#215;</mo><mfrac><mn>24</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">= 960</span><br><span style=\"font-family: Times New Roman;\">Percentage =</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mn>2250</mn></mfrac><mo>&#215;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\">= 42.7%</span></p>",
                    solution_hi: "<p>19.(a)<br><span style=\"font-family: Times New Roman;\">Q </span><span style=\"font-family: Baloo;\">में नामांकित छात्र </span><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7500</mn><mo>&#215;</mo><mfrac><mn>30</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 2250</span><br><span style=\"font-family: Baloo;\">उत्तीर्ण छात्र </span><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4000</mn><mo>&#215;</mo><mfrac><mn>24</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 960</span><br><span style=\"font-family: Baloo;\">प्रतिशत</span><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mn>2250</mn></mfrac><mo>&#215;</mo><mn>100</mn></math> </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">= 42.7%</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">20. </span><span style=\"font-family: Times New Roman;\">In a circle, a 10 cm long chord is at a distance of 12 cm from the centre of the circle. Length of the diameter of the circle (in cm) is:</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">20. </span><span style=\"font-family: Baloo;\">एक वृत्त में, एक 10 सेमी लंबी जीवा वृत्त के केंद्र से 12 सेमी की दूरी पर है। वृत्त के व्यास की लंबाई (सेमी में) है:</span></p>",
                    options_en: ["<p>20</p>", "<p>26</p>", 
                                "<p>13</p>", "<p>22</p>"],
                    options_hi: ["<p>20</p>", "<p>26</p>",
                                "<p>13</p>", "<p>22</p>"],
                    solution_en: "<p>20.(b)<br><strong id=\"docs-internal-guid-84431494-7fff-5797-c8dc-5f02c26df5a6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdagIVR_YxN6fIbp-i5Gk3W10qQ2YwrMYJxSaIT-LMAmK-Ki54K9_RhZSwn_AA8g2nTlViINtHJ6mWHfojXUhuAZWQ54nvR-HlOyhbEA2TST5Yq6h9Ni5N1aeF0b6GEvu64Gm762v4tErhx9oIFKUZqKXE?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"140\" height=\"134\"></strong><br><span style=\"font-family: Times New Roman;\">AO&sup2; = 5</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\"> + 12</span><span style=\"font-family: Times New Roman;\">&sup2;</span><br><span style=\"font-family: Times New Roman;\">AO = 13</span><br><span style=\"font-family: Times New Roman;\">Diameter = 2 &times; 13 = 26</span></p>",
                    solution_hi: "<p>20.(b)<br><strong id=\"docs-internal-guid-84431494-7fff-5797-c8dc-5f02c26df5a6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdagIVR_YxN6fIbp-i5Gk3W10qQ2YwrMYJxSaIT-LMAmK-Ki54K9_RhZSwn_AA8g2nTlViINtHJ6mWHfojXUhuAZWQ54nvR-HlOyhbEA2TST5Yq6h9Ni5N1aeF0b6GEvu64Gm762v4tErhx9oIFKUZqKXE?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"105\" height=\"101\"></strong><br><span style=\"font-family: Times New Roman;\">AO&sup2; = 5</span><span style=\"font-family: Times New Roman;\">&sup2;</span><span style=\"font-family: Times New Roman;\"> + 12</span><span style=\"font-family: Times New Roman;\">&sup2;</span><br><span style=\"font-family: Times New Roman;\">AO = 13</span><br><span style=\"font-family: Baloo;\">व्यास </span><span style=\"font-family: Times New Roman;\">= 2 &times; 13 = 26</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">21. </span><span style=\"font-family: Times New Roman;\">In triangle</span><span style=\"font-family: Times New Roman;\"> ABC, P and Q are the midpoints of AB and AC. respectively. R is a point on PQ such that PR : RQ = 3 : 5 and QR = 20 cm, then what is the length (in cm) of BC?</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">21. </span><span style=\"font-family: Baloo;\">त्रिभुज ABC में, P और Q AB और AC के मध्य बिंदु हैं। क्रमश: R, PQ पर एक बिंदु इस प्रकार है कि PR: RQ = 3: 5 और QR = 20 सेमी, तो BC की लंबाई (सेमी में) क्या है</span><span style=\"font-family: Times New Roman;\">?</span></p>",
                    options_en: ["<p>24</p>", "<p>40</p>", 
                                "<p>64</p>", "<p>66.66</p>"],
                    options_hi: ["<p>24</p>", "<p>40</p>",
                                "<p>64</p>", "<p>66.66</p>"],
                    solution_en: "<p>21.(c)<br><strong id=\"docs-internal-guid-644c58d7-7fff-13e2-01fd-8be8fa036bc3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXejt8ZughN7DP8blHfQYPR6uQ7lalYGnD-bvdC19dXllS6-r7VjsjevpGF6RmxaqQwYJ50Oc9e1Pv5Nk1kc0g1aKIthZHUccbuyivWbP5ObI6LjQS9Bm4lsow7b2BkdI2W1FGqbPBu5de0pRPldjz46Z7Ws?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"155\" height=\"147\"></strong><br><span style=\"font-family: Times New Roman;\">PR : RQ = 3 : 5</span><br><span style=\"font-family: Times New Roman;\">PQ = 3 + 5 = 8 unit</span><br><span style=\"font-family: Times New Roman;\">5 unit = 20</span><br><span style=\"font-family: Times New Roman;\">3 unit = 12</span><br><span style=\"font-family: Times New Roman;\">PR = 32 cm</span><br><span style=\"font-family: Times New Roman;\">BC = 2 &times; 32 = 64 cm</span></p>",
                    solution_hi: "<p>21.(c)<br><strong id=\"docs-internal-guid-644c58d7-7fff-13e2-01fd-8be8fa036bc3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXejt8ZughN7DP8blHfQYPR6uQ7lalYGnD-bvdC19dXllS6-r7VjsjevpGF6RmxaqQwYJ50Oc9e1Pv5Nk1kc0g1aKIthZHUccbuyivWbP5ObI6LjQS9Bm4lsow7b2BkdI2W1FGqbPBu5de0pRPldjz46Z7Ws?key=QuK1uhYJLaNP_ixqgebG1CAS\" width=\"155\" height=\"147\"></strong><br><span style=\"font-family: Times New Roman;\">PR : RQ = 3 : 5</span><br><span style=\"font-family: Times New Roman;\">PQ = 3 + 5 = 8 </span><span style=\"font-family: Baloo;\">इकाई</span><br><span style=\"font-family: Times New Roman;\">5 </span><span style=\"font-family: Baloo;\">इकाई </span><span style=\"font-family: Times New Roman;\">= 20</span><br><span style=\"font-family: Times New Roman;\">3 </span><span style=\"font-family: Baloo;\">इकाई </span><span style=\"font-family: Times New Roman;\">= 12</span><br><span style=\"font-family: Times New Roman;\">PR = 32 cm</span><br><span style=\"font-family: Times New Roman;\">BC = 2 &times; 32 = 64 cm</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">22. </span><span style=\"font-family: Times New Roman;\">If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"14px\"><mn>7</mn><mi>cos</mi><mo>&#178;</mo><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mn>5</mn><mi>sin</mi><mo>&#178;</mo><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>6</mn><mo>=</mo><mn>0</mn><mo>,</mo><mo>(</mo><mn>0</mn><mo>&#176;</mo><mo>&#60;</mo><mi mathvariant=\"normal\">&#952;</mi><mo>&#60;</mo><mn>90</mn><mo>&#176;</mo><mo>)</mo></mstyle></math></span><span style=\"font-family: Times New Roman;\">, then what is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mo>&#160;</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><mi>tan</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></msqrt></math>.</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">22. </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn mathsize=\"14px\">7</mn><mi mathsize=\"14px\">cos</mi><mo mathsize=\"14px\">&#178;</mo><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi><mo mathsize=\"14px\">+</mo><mn mathsize=\"14px\">5</mn><mi mathsize=\"14px\">sin</mi><mo mathsize=\"14px\">&#178;</mo><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi><mo mathsize=\"14px\">-</mo><mn mathsize=\"14px\">6</mn><mo mathsize=\"14px\">=</mo><mn mathsize=\"14px\">0</mn><mo mathsize=\"14px\">,</mo><mo mathsize=\"14px\">(</mo><mn mathsize=\"14px\">0</mn><mo mathsize=\"14px\">&#176;</mo><mo mathsize=\"14px\">&#60;</mo><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi><mo mathsize=\"14px\">&#60;</mo><mn mathsize=\"14px\">90</mn><mo mathsize=\"14px\">&#176;</mo><mo mathsize=\"14px\">)</mo></math></span><span style=\"font-family: Times New Roman;\">, </span><span style=\"font-family: Baloo;\">तो</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mo>&#160;</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>+</mo><mo>&#160;</mo><mi>tan</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></msqrt></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">का मान क्या होगा?</span></p>",
                    options_en: ["<p>&radic;2 - 1</p>", "<p>&radic;3 + 1</p>", 
                                "<p>&radic;2 + 1</p>", "<p>&radic;3 - 1</p>"],
                    options_hi: ["<p>&radic;2 - 1</p>", "<p>&radic;3 + 1</p>",
                                "<p>&radic;2 + 1</p>", "<p>&radic;3 - 1</p>"],
                    solution_en: "<p>22.(c)<br><span style=\"font-family: Times New Roman;\">7cos&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"14px\"><mi mathvariant=\"normal\">&#952;</mi></mstyle></math></span><span style=\"font-family: Times New Roman;\">+ 5sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">- 6 = 0</span><br><span style=\"font-family: Times New Roman;\">7(1-sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">) + 5sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">- 6 = 0</span><br><span style=\"font-family: Times New Roman;\">7 - 7sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math> </span><span style=\"font-family: Times New Roman;\">+ 5sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math> </span><span style=\"font-family: Times New Roman;\">- 6 = 0</span><br><span style=\"font-family: Times New Roman;\">2sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math> </span><span style=\"font-family: Times New Roman;\">= 1</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math> = 45&deg;</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>+</mo><mi>tan</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></msqrt></math></span><br><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msqrt><mfrac><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mn>45</mn><mo>&#176;</mo></mrow></mfrac></msqrt></math></span><br><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>2</mn><mo>&#160;</mo></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></msqrt></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> + 1</span></p>",
                    solution_hi: "<p>22.(c)<br><span style=\"font-family: Times New Roman;\">7cos&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"14px\"><mi mathvariant=\"normal\">&#952;</mi></mstyle></math></span><span style=\"font-family: Times New Roman;\">+ 5sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">- 6 = 0</span><br><span style=\"font-family: Times New Roman;\">7(1-sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">) + 5sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math></span><span style=\"font-family: Times New Roman;\">- 6 = 0</span><br><span style=\"font-family: Times New Roman;\">7 - 7sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math> </span><span style=\"font-family: Times New Roman;\">+ 5sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math> </span><span style=\"font-family: Times New Roman;\">- 6 = 0</span><br><span style=\"font-family: Times New Roman;\">2sin&sup2;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math> </span><span style=\"font-family: Times New Roman;\">= 1</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\" mathsize=\"14px\">&#952;</mi></math> = 45&deg;</span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>+</mo><mi>tan</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#952;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#952;</mi></mrow></mfrac></msqrt></math></span><br><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msqrt><mfrac><mrow><mi>cos</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mn>45</mn><mo>&#176;</mo></mrow></mfrac></msqrt></math></span><br><span style=\"font-family: Times New Roman;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>2</mn><mo>&#160;</mo></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></msqrt></math></span><br><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> + 1</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. <span style=\"font-family: Times New Roman;\">Find the sum of squares of the greatest value and smallest value of K in the number so that the number 45082k is divisible by 3.</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">23. </span><span style=\"font-family: Baloo;\">संख्या</span><span style=\"font-family: Times New Roman;\"> 45082k </span><span style=\"font-family: Baloo;\">में</span><span style=\"font-family: Times New Roman;\"> K </span><span style=\"font-family: Baloo;\">के सबसे बड़े मान और सबसे छोटे मान वाले वर्गों का योग ज्ञात कीजिए ताकि संख्या</span><span style=\"font-family: Times New Roman;\"> 3 </span><span style=\"font-family: Baloo;\">से विभाज्य हो।</span></p>",
                    options_en: ["<p>68</p>", "<p>64</p>", 
                                "<p>100</p>", "<p>50</p>"],
                    options_hi: ["<p>68</p>", "<p>64</p>",
                                "<p>100<span style=\"font-family: Times New Roman;\"> </span></p>", "<p>50</p>"],
                    solution_en: "<p>23.(a)<br><span style=\"font-family: Times New Roman;\">For divisibility of 3, check sum of digits</span><br><span style=\"font-family: Times New Roman;\">4 + 5 + 0 + 8 + 2 + k = multiple of 3</span><br><span style=\"font-family: Times New Roman;\">19 + k = multiple of 3</span><br><span style=\"font-family: Times New Roman;\">Possible value of k = 2, 5, 8</span><br><span style=\"font-family: Times New Roman;\">Required Answer = 8&sup2; + 2&sup2; = 68 </span></p>",
                    solution_hi: "<p>23.(a)<br><span style=\"font-family: Times New Roman;\">3 </span><span style=\"font-family: Baloo;\">की विभाज्यता के लिए, अंकों का योग जांचें</span><br><span style=\"font-family: Times New Roman;\">4 + 5 + 0 + 8 + 2 + k = 3 </span><span style=\"font-family: Baloo;\">का गुणज</span><br><span style=\"font-family: Times New Roman;\">19 + k = 3 </span><span style=\"font-family: Baloo;\">का गुणज</span><br><span style=\"font-family: Times New Roman;\">k = 2, 5, 8 </span><span style=\"font-family: Baloo;\">का संभावित मान</span><br><span style=\"font-family: Baloo;\">आवश्यक उत्तर =</span><span style=\"font-family: Times New Roman;\"> 8&sup2; + 2&sup2; = 68</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Times New Roman;\">24. </span><span style=\"font-family: Times New Roman;\">The ratio of present ages of A and B is 7 : 8. After 6 years from now, the ratio of their ages will be 8 : 9. If C&rsquo;s present age is 10 years more than the present age of A, then the present age (in years) of C is:</span></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">24. </span><span style=\"font-family: Baloo;\">A और B की वर्तमान आयु का अनुपात 7:8 है। अब से 6 वर्ष बाद, उनकी आयु का अनुपात 8:9 होगा। यदि C की वर्तमान आयु A की वर्तमान आयु से 10 वर्ष अधिक है, तो वर्तमान आयु (वर्षों में) C है:</span></p>",
                    options_en: ["<p>56</p>", "<p>52</p>", 
                                "<p>59</p>", "<p>45</p>"],
                    options_hi: ["<p>56</p>", "<p>52</p>",
                                "<p>59</p>", "<p>45</p>"],
                    solution_en: "<p>24.(b)</p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>x</mi><mo>+</mo><mn>6</mn></mrow><mrow><mn>8</mn><mi>x</mi><mo>+</mo><mn>6</mn></mrow></mfrac><mo>=</mo><mfrac><mn>8</mn><mn>9</mn></mfrac></math></p>\n<p><span style=\"font-family: Times New Roman;\">63x + 54 = 64x + 48 </span></p>\n<p><span style=\"font-family: Times New Roman;\">x = 6</span></p>\n<p><span style=\"font-family: Times New Roman;\">Present Age of A = 7x = 42</span></p>\n<p><span style=\"font-family: Times New Roman;\">Present Age of C = 42 + 10 = 52</span></p>",
                    solution_hi: "<p>24.(b)</p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn mathsize=\"14px\">7</mn><mi mathsize=\"14px\">x</mi><mo mathsize=\"14px\">+</mo><mn mathsize=\"14px\">6</mn></mrow><mrow><mn mathsize=\"14px\">8</mn><mi mathsize=\"14px\">x</mi><mo mathsize=\"14px\">+</mo><mn mathsize=\"14px\">6</mn></mrow></mfrac><mo mathsize=\"14px\">=</mo><mfrac><mn mathsize=\"14px\">8</mn><mn mathsize=\"14px\">9</mn></mfrac></math></p>\n<p><span style=\"font-family: Times New Roman;\">63x + 54 = 64x + 48 </span></p>\n<p><span style=\"font-family: Times New Roman;\">x = 6</span></p>\n<p><span style=\"font-family: Times New Roman;\">A </span><span style=\"font-family: Baloo;\">की वर्तमान आयु</span><span style=\"font-family: Times New Roman;\"> = 7x = 42</span></p>\n<p><span style=\"font-family: Times New Roman;\">C </span><span style=\"font-family: Baloo;\">की वर्तमान आयु </span><span style=\"font-family: Times New Roman;\">= 42 + 10 = 52</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Times New Roman;\"> Simplify the following expression</span></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn></mrow><mrow><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn></mrow></mfrac></math></p>",
                    question_hi: "<p><span style=\"font-family: Times New Roman;\">25. </span><span style=\"font-family: Baloo;\">निम्नलिखित व्यंजक को सरल कीजिए</span><span style=\"font-family: Baloo;\">।</span></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn></mrow><mrow><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn></mrow></mfrac></math></p>",
                    options_en: ["<p>200</p>", "<p>1</p>", 
                                "<p>16</p>", "<p>-1</p>"],
                    options_hi: ["<p>200</p>", "<p>1</p>",
                                "<p>16</p>", "<p>-1</p>"],
                    solution_en: "<p>25.(c)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mrow><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn></mrow><mrow><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>108</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>92</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>108</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>92</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>108</mn><mo>-</mo><mn>92</mn><mo>)</mo><mo>{</mo><mrow><msup><mrow><mo>(</mo><mn>108</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>92</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn><mo>}</mo></mrow></mrow><mrow><mo>{</mo><msup><mrow><mo>(</mo><mn>108</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>92</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn><mo>}</mo></mrow></mfrac></math> = 16</p>",
                    solution_hi: "<p>25.(c)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mrow><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn></mrow><mrow><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mo>&#160;</mo><mn>92</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>108</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>92</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mn>108</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>92</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn></mrow></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>108</mn><mo>-</mo><mn>92</mn><mo>)</mo><mo>{</mo><mrow><msup><mrow><mo>(</mo><mn>108</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>92</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn><mo>}</mo></mrow></mrow><mrow><mo>{</mo><msup><mrow><mo>(</mo><mn>108</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>92</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>108</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>92</mn><mo>}</mo></mrow></mfrac></math> = 16</p>\n<p><span style=\"font-family: Times New Roman;\"> </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>