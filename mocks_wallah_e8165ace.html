<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> The control unit controls other units by generating___________ .</span></p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; &#2351;&#2370;&#2344;&#2367;&#2335; </span><span style=\"font-family: Cambria Math;\">_____ </span><span style=\"font-family: Cambria Math;\">&#2332;&#2375;&#2344;&#2352;&#2375;&#2335; &#2325;&#2352;&#2325;&#2375; &#2309;&#2344;&#2381;&#2351; &#2351;&#2370;&#2344;&#2367;&#2335;&#2379;&#2306; &#2325;&#2379; &#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404; </span></p>\n",
                    options_en: ["<p>control signal</p>\n", "<p>timing signal</p>\n", 
                                "<p>transfer signal</p>\n", "<p>command signal</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; &#2360;&#2367;&#2327;&#2381;&#2344;&#2354; </span><span style=\"font-family: Cambria Math;\">(control signal) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2335;&#2366;&#2311;&#2350;&#2367;&#2306;&#2327; &#2360;&#2367;&#2327;&#2381;&#2344;&#2354; (timing signal)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2352; &#2360;&#2367;&#2327;&#2381;&#2344;&#2354; (transfer signal)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2350;&#2366;&#2306;&#2337; &#2360;&#2367;&#2327;&#2381;&#2344;&#2354; (command signal)</span></p>\n"],
                    solution_en: "<p>1.(a) <span style=\"font-family: Cambria Math;\">The basic function of the control unit is to fetch the instruction stored in the main memory, identify the operations and the devices involved in it and <strong>accordingly generate control signals.</strong></span></p>\n",
                    solution_hi: "<p>1.(a) <span style=\"font-family: Cambria Math;\"><strong>&#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; &#2351;&#2370;&#2344;&#2367;&#2335; &#2325;&#2366; &#2350;&#2370;&#2354; &#2325;&#2366;&#2352;&#2381;&#2351; &#2350;&#2369;&#2326;&#2381;&#2351; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368; &#2350;&#2375;&#2306; &#2360;&#2381;&#2335;&#2379;&#2352;&#2381;&#2337; &#2311;&#2306;&#2360;&#2381;&#2335;&#2381;&#2352;&#2325;&#2381;&#2358;&#2344;</strong> &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2330;&#2366;&#2354;&#2344; &#2324;&#2352; &#2313;&#2360;&#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2325;&#2368; &#2346;&#2361;&#2330;&#2366;&#2344; &#2325;&#2352;&#2344;&#2366; &#2324;&#2352; &#2340;&#2342;&#2344;&#2369;&#2360;&#2366;&#2352; &#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; &#2360;&#2367;&#2327;&#2381;&#2344;&#2354; &#2332;&#2375;&#2344;&#2352;&#2375;&#2335; &#2325;&#2352;&#2344;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Who developed an integrated chip?</span></p>\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2335;&#2368;&#2327;&#2381;&#2352;&#2375;&#2335;&#2375;&#2337; &#2330;&#2367;&#2346; </span><span style=\"font-family: Cambria Math;\">(integrated chip) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2325;&#2367;&#2360;&#2344;&#2375; &#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>JS Kilby</p>\n", "<p>Robert Nayak</p>\n", 
                                "<p>C Babbage</p>\n", "<p>CV Raman</p>\n"],
                    options_hi: ["<p>JS <span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2354;&#2381;&#2348;&#2368; </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2352;&#2377;&#2348;&#2352;&#2381;&#2335; &#2344;&#2366;&#2351;&#2325; </span></p>\n",
                                "<p>C <span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2348;&#2375;&#2332; </span></p>\n", "<p>CV <span style=\"font-family: Cambria Math;\">&#2352;&#2350;&#2344; </span></p>\n"],
                    solution_en: "<p>2.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><strong>Jack Kilby</strong> </span><span style=\"font-family: Cambria Math;\">developed the principle of integration, created the prototype ICs and commercialized them.</span></p>\n",
                    solution_hi: "<p>2.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><strong>&#2332;&#2376;&#2325; &#2325;&#2367;&#2354;&#2381;&#2348;&#2368;</strong> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375; &#2311;&#2306;&#2335;&#2368;&#2327;&#2381;&#2352;&#2375;&#2358;&#2344; &#2325;&#2375; &#2346;&#2381;&#2352;&#2367;&#2306;&#2360;&#2367;&#2346;&#2354; </span><span style=\"font-family: Cambria Math;\">(principle) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2357;&#2367;&#2325;&#2360;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2335;&#2366;&#2311;&#2346; </span><span style=\"font-family: Cambria Math;\">(prototype) IC </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2325;&#2367;&#2351;&#2366; &#2324;&#2352; &#2313;&#2344;&#2325;&#2366; &#2357;&#2381;&#2351;&#2366;&#2357;&#2360;&#2366;&#2351;&#2368;&#2325;&#2352;&#2339; &#2325;&#2367;&#2351;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> A microprocessor is the brain of the computer and is also called a_______ .</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2366; &#2350;&#2360;&#2381;&#2340;&#2367;&#2359;&#2381;&#2325; </span><span style=\"font-family: Cambria Math;\">(brain) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2324;&#2352; &#2311;&#2360;&#2375; </span><span style=\"font-family: Cambria Math;\">_____ </span><span style=\"font-family: Cambria Math;\">&#2349;&#2368; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>microchip</p>\n", "<p>macrochip</p>\n", 
                                "<p>macro processor</p>\n", "<p>calculator</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2330;&#2367;&#2346; (microchip)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2376;&#2325;&#2381;&#2352;&#2379;&#2330;&#2367;&#2346; (macrochip)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2350;&#2376;&#2325;&#2381;&#2352;&#2379; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352; (macro processor)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2376;&#2354;&#2325;&#2369;&#2354;&#2375;&#2335;&#2352; (calculator)</span></p>\n"],
                    solution_en: "<p>3.(a)<span style=\"font-family: Cambria Math;\"> It is the <strong>controlling element in a computer system and is sometimes referred to as the chip</strong>. Microprocessor is the main hardware that drives the computer. It is a large Printed Circuit Board (PCB), which is used in all electronic systems such as computer, calculator, </span><span style=\"font-family: Cambria Math;\">digital system, etc.</span></p>\n",
                    solution_hi: "<p>3.(a) <span style=\"font-family: Cambria Math;\">&#2351;&#2361; &#2319;&#2325; <strong>&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2350;&#2375;&#2306; &#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354;&#2367;&#2306;&#2327; &#2319;&#2354;&#2367;&#2350;&#2375;&#2306;&#2335; </strong></span><span style=\"font-family: Cambria Math;\"><strong>(controlling element)</strong> </span><strong><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2324;&#2352; &#2311;&#2360;&#2375; &#2325;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\">-</span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2325;&#2349;&#2368; &#2330;&#2367;&#2346; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</strong>&#2404; &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352; &#2350;&#2369;&#2326;&#2381;&#2351; &#2361;&#2366;&#2352;&#2381;&#2337;&#2357;&#2375;&#2351;&#2352; &#2361;&#2376; &#2332;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2379; &#2330;&#2354;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2319;&#2325; &#2348;&#2337;&#2364;&#2366; &#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2375;&#2337; &#2360;&#2352;&#2381;&#2325;&#2367;&#2335; &#2348;&#2379;&#2352;&#2381;&#2337; </span><span style=\"font-family: Cambria Math;\">(PCB) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2360;&#2349;&#2368; &#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2332;&#2376;&#2360;&#2375; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; </span><span style=\"font-family: Cambria Math;\">(computer), </span><span style=\"font-family: Cambria Math;\">&#2325;&#2376;&#2354;&#2325;&#2369;&#2354;&#2375;&#2335;&#2352; </span><span style=\"font-family: Cambria Math;\">(calculator), </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; </span><span style=\"font-family: Cambria Math;\">(digital system) </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367; &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> Which of the following could be digital input devices for computers?</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2344;&#2346;&#2369;&#2335; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2361;&#2379; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Digital camcorder</p>\n", "<p>Microphone</p>\n", 
                                "<p>Scanner</p>\n", "<p>All of these</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2325;&#2376;&#2350;&#2325;&#2377;&#2352;&#2381;&#2337;&#2352; (Digital camcorder)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2347;&#2379;&#2344; (Microphone)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2325;&#2376;&#2344;&#2352; (Scanner)</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340; &#2360;&#2349;&#2368; </span></p>\n"],
                    solution_en: "<p>4.(d) Scanner, digital camcorder and microphone<span style=\"font-family: Cambria Math;\"> <strong>are the digital input devices for computers.</strong></span></p>\n",
                    solution_hi: "<p>4.(d) <span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2376;&#2344;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2325;&#2376;&#2350;&#2325;&#2377;&#2352;&#2381;&#2337;&#2352; &#2324;&#2352; &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2347;&#2379;&#2344; <strong>&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2311;&#2344;&#2346;&#2369;&#2335; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2361;&#2376;&#2306;</strong>&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> Where is data saved permanently?</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366; &#2360;&#2381;&#2341;&#2366;&#2351;&#2368; &#2352;&#2370;&#2346; &#2360;&#2375; &#2325;&#2361;&#2366;&#2305; &#2360;&#2375;&#2357; </span><span style=\"font-family: Cambria Math;\">(save) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Memory</p>\n", "<p>Storage</p>\n", 
                                "<p>CPU</p>\n", "<p>Printer</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2350;&#2379;&#2352;&#2368; </span><span style=\"font-family: Cambria Math;\">(Memory) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; (Storage)</span></p>\n",
                                "<p>CPU</p>\n", "<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2352; (Printer)</span></p>\n"],
                    solution_en: "<p>5.(b)<span style=\"font-family: Cambria Math;\"> Data is <strong>stored permanently in </strong></span><span style=\"font-family: Cambria Math;\"><strong>secondary storage</strong>.</span><span style=\"font-family: Cambria Math;\"> It is a storage medium that holds data or information until it is deleted or overwritten</span></p>\n",
                    solution_hi: "<p>5.(b)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366; &#2360;&#2381;&#2341;&#2366;&#2351;&#2368; &#2352;&#2370;&#2346; &#2360;&#2375; </span><strong><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2325;&#2375;&#2306;&#2337;&#2352;&#2368; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2350;&#2375;&#2306; &#2360;&#2375;&#2357; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</strong>&#2404; &#2351;&#2361; &#2319;&#2325; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2325;&#2366; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2361;&#2376; &#2332;&#2379; &#2337;&#2375;&#2335;&#2366; &#2351;&#2366; &#2311;&#2344;&#2347;&#2366;&#2352;&#2381;&#2350;&#2375;&#2358;&#2344; &#2325;&#2379; &#2340;&#2348; &#2340;&#2325; &#2352;&#2326;&#2340;&#2366; &#2361;&#2376; &#2332;&#2348; &#2340;&#2325; &#2325;&#2367; &#2311;&#2360;&#2375; &#2350;&#2367;&#2335;&#2366;&#2351;&#2366; &#2351;&#2366; &#2309;&#2343;&#2367;&#2354;&#2375;&#2326;&#2367;&#2340; </span><span style=\"font-family: Cambria Math;\">(delete or overwritten) </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> Today&rsquo;s mostly used coding system is/are________ .</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2332; &#2325;&#2366; &#2309;&#2343;&#2367;&#2325;&#2340;&#2352; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2325;&#2379;&#2337;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; </span><span style=\"font-family: Cambria Math;\">_____ </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306; &#2404;</span></p>\n",
                    options_en: ["<p>EBCDIC</p>\n", "<p>ASCII</p>\n", 
                                "<p>Both &lsquo;a&rsquo; and &lsquo;b&rsquo;</p>\n", "<p>BCD</p>\n"],
                    options_hi: ["<p>EBCDIC</p>\n", "<p>ASCII</p>\n",
                                "<p>\'a\' <span style=\"font-family: Cambria Math;\">&#2324;&#2352; </span><span style=\"font-family: Cambria Math;\">\'b\' </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\n", "<p>BCD</p>\n"],
                    solution_en: "<p>6.(c)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">ASCII and EBCDIC</span></strong><span style=\"font-family: Cambria Math;\"> are two most used coding system .</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">ASCII</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&rarr;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">American Standard Code for Information Interchange</span><span style=\"font-family: Cambria Math;\">, a standard data-encoding format for electronic communication between computers. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>EBCDIC</strong> </span><span style=\"font-family: Cambria Math;\">&rarr;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Extended binary-coded decimal interchange code</span><span style=\"font-family: Cambria Math;\">, data-encoding system, developed by IBM and used mostly on its computers,</span></p>\n",
                    solution_hi: "<p>6.(c) <span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">ASCII </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; </span><span style=\"font-family: Cambria Math;\">EBCDIC</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379; &#2360;&#2348;&#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2368; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2325;&#2379;&#2337;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>ASCII</strong> </span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2347;&#2366;&#2352;&#2381;&#2350;&#2375;&#2358;&#2344; &#2311;&#2306;&#2335;&#2352;&#2330;&#2375;&#2306;&#2332; &#2325;&#2375; &#2354;&#2367;&#2319; &#2309;&#2350;&#2375;&#2352;&#2367;&#2325;&#2344; &#2360;&#2381;&#2335;&#2376;&#2306;&#2337;&#2352;&#2381;&#2337; &#2325;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\">,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2348;&#2368;&#2330; &#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325; &#2360;&#2306;&#2330;&#2366;&#2352; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2360;&#2381;&#2335;&#2376;&#2306;&#2337;&#2352;&#2381;&#2337; &#2337;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2319;&#2344;&#2381;&#2325;&#2379;&#2337;&#2367;&#2306;&#2327; &#2347;&#2377;&#2352;&#2381;&#2350;&#2375;&#2335; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>EBCDIC</strong> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2335;&#2375;&#2306;&#2337;&#2375;&#2337; &#2348;&#2366;&#2311;&#2344;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2337;&#2375;&#2337; &#2337;&#2375;&#2360;&#2368;&#2350;&#2354; &#2311;&#2306;&#2335;&#2352;&#2330;&#2375;&#2306;&#2332; &#2325;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2319;&#2344;&#2381;&#2325;&#2379;&#2337;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\">, IBM </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2357;&#2367;&#2325;&#2360;&#2367;&#2340; &#2324;&#2352; &#2332;&#2381;&#2351;&#2366;&#2342;&#2366;&#2340;&#2352; &#2311;&#2360;&#2325;&#2375; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;&#2379;&#2306; &#2346;&#2352; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Software designed for a specific purpose/ application such as pay calculations, processing of examination result, etc are known as</span></p>\n",
                    question_hi: "<p>7.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2360;&#2381;&#2346;&#2375;&#2360;&#2367;&#2347;&#2367;&#2325; &#2346;&#2352;&#2381;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2319;&#2346;&#2381;&#2354;&#2368;&#2325;&#2375;&#2358;&#2344; </span><span style=\"font-family: Cambria Math;\">(specific purpose/ application) </span><span style=\"font-family: Cambria Math;\">&#2332;&#2376;&#2360;&#2375; &#2357;&#2375;&#2340;&#2344; &#2327;&#2339;&#2344;&#2366; </span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2366; &#2346;&#2352;&#2367;&#2339;&#2366;&#2350; </span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">&#2325;&#2368; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327; </span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2367;&#2332;&#2364;&#2366;&#2311;&#2344; &#2325;&#2367;&#2319; &#2327;&#2319; &#2360;&#2377;&#2347;&#2364;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">______ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>utility software</p>\n", "<p>system software</p>\n", 
                                "<p>customized software</p>\n", "<p>application software</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2351;&#2370;&#2335;&#2367;&#2354;&#2367;&#2335;&#2368; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (utility software)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (system software)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2325;&#2360;&#2381;&#2335;&#2350;&#2366;&#2311;&#2332;&#2381;&#2337; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (customized software)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2346;&#2381;&#2354;&#2368;&#2325;&#2375;&#2358;&#2344; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (application software)</span></p>\n"],
                    solution_en: "<p>7.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">An</span><span style=\"font-family: Cambria Math;\"> application software </span><span style=\"font-family: Cambria Math;\">is a computer program <strong>designed to carry out a specific task</strong> like processing of examination results, pay calculations for e.g.MS Office ,MS Powerpoint etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Utility software </span><span style=\"font-family: Cambria Math;\">performs tasks related to the <strong>maintenance of the computer system </strong>for e.g.backup utilities, antivirus, text editor etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">System software </span><span style=\"font-family: Cambria Math;\">consists of several programs, which are directly <strong>responsible for controlling, integrating and managing the individual hardware components </strong>of a computer system for e.g. mac os,linux.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Customized software</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">has been developed specifically <strong>for a single person, company, or organization for e.g bug tracking software.</strong></span></p>\n",
                    solution_hi: "<p>7.(d) <span style=\"font-family: Cambria Math;\">&#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344; &#2360;&#2377;&#2347;&#2364;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2361;&#2376; <strong>&#2332;&#2367;&#2360;&#2375; &#2319;&#2325; &#2360;&#2381;&#2346;&#2375;&#2360;&#2367;&#2347;&#2367;&#2325; &#2335;&#2366;&#2360;&#2381;&#2325; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2367;&#2332;&#2364;&#2366;&#2311;&#2344; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;</strong>&#2404; &#2332;&#2376;&#2360;&#2375; &#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2366; &#2346;&#2352;&#2367;&#2339;&#2366;&#2350; </span><span style=\"font-family: Cambria Math;\">(examination result), </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2340;&#2344; &#2327;&#2339;&#2344;&#2366; </span><span style=\"font-family: Cambria Math;\">(pay calculations) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368; &#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2404; </span><span style=\"font-family: Cambria Math;\">MS </span><span style=\"font-family: Cambria Math;\">&#2321;&#2347;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\">, MS </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2357;&#2352;&#2346;&#2377;&#2311;&#2306;&#2335; &#2311;&#2340;&#2381;&#2351;&#2366;&#2342;&#2367;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2335;&#2367;&#2354;&#2367;&#2335;&#2368; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; </span><strong><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2375; &#2350;&#2375;&#2306;&#2335;&#2375;&#2344;&#2375;&#2306;&#2360; </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">maintenance) </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</strong>&#2404; &#2348;&#2376;&#2325;&#2309;&#2346; &#2351;&#2370;&#2335;&#2367;&#2354;&#2367;&#2335;&#2368;&#2332; </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">backup utilities)</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2319;&#2306;&#2335;&#2368;&#2357;&#2366;&#2351;&#2352;&#2360; </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">antivirus)</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2319;&#2337;&#2367;&#2335;&#2352; </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">text editor) </span><span style=\"font-family: Cambria Math;\">&#2311;&#2340;&#2381;&#2351;&#2366;&#2342;&#2367;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2360;&#2377;&#2347;&#2364;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2325;&#2312; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379; <strong>&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2375; &#2311;&#2306;&#2337;&#2367;&#2357;&#2367;&#2332;&#2369;&#2309;&#2354; &#2361;&#2366;&#2352;&#2381;&#2337;&#2357;&#2375;&#2351;&#2352; &#2325;&#2306;&#2346;&#2379;&#2344;&#2375;&#2306;&#2335;&#2381;&#2360; </strong></span><strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">individual hardware components) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; </span><span style=\"font-family: Cambria Math;\">(control) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2335;&#2375;&#2327;&#2381;&#2352;&#2335; </span></strong><span style=\"font-family: Cambria Math;\"><strong>(integrate)</strong> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2350;&#2376;&#2344;&#2375;&#2332; </span><span style=\"font-family: Cambria Math;\">(manage) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2360;&#2368;&#2343;&#2375; &#2332;&#2367;&#2350;&#2381;&#2350;&#2375;&#2342;&#2366;&#2352; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; &#2350;&#2376;&#2325; </span><span style=\"font-family: Cambria Math;\">OS, </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2344;&#2325;&#2381;&#2360;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2325;&#2360;&#2381;&#2335;&#2350;&#2366;&#2311;&#2332;&#2381;&#2337; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2375;&#2359; &#2352;&#2370;&#2346; &#2360;&#2375; <strong>&#2319;&#2325; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; </strong></span><strong><span style=\"font-family: Cambria Math;\">(person), </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2344;&#2368; </span><span style=\"font-family: Cambria Math;\">(company) </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366; &#2360;&#2306;&#2327;&#2336;&#2344; </span><span style=\"font-family: Cambria Math;\">(organization) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2354;&#2367;&#2319; &#2357;&#2367;&#2325;&#2360;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;</span></strong><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2327; &#2335;&#2381;&#2352;&#2376;&#2325;&#2367;&#2306;&#2327; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; </span><span style=\"font-family: Cambria Math;\">(bug tracking software)</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">WINDOWS, UNIX and LINUX are called ________</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2344;&#2381;&#2337;&#2379;&#2332;&#2364;</span><span style=\"font-family: Cambria Math;\">(WINDOWS), </span><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2344;&#2367;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">(UNIX) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2354;&#2366;&#2311;&#2344;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">(LINUX) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">________ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</span></p>\n",
                    options_en: ["<p>Application</p>\n", "<p>Hardware</p>\n", 
                                "<p>System</p>\n", "<p>Operating system</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2319;&#2346;&#2381;&#2354;&#2368;&#2325;&#2375;&#2358;&#2344; (application)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2361;&#2366;&#2352;&#2381;&#2337;&#2357;&#2375;&#2351;&#2352; (Hardware)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; (System)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; (Operating system)</span></p>\n"],
                    solution_en: "<p>8.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Operating system</span><span style=\"font-family: Cambria Math;\"> is an <strong>organized collection or integrated set of specialized programs that controls</strong> the overall operations of a computer </span><span style=\"font-family: Cambria Math;\">for e.g. MS DOS,linux ,unix etc.</span></p>\n",
                    solution_hi: "<p>8.(d) <span style=\"font-family: Cambria Math;\">&#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\">(operating system)</span><strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2360;&#2306;&#2327;&#2336;&#2367;&#2340; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\">(organized collection) </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366; &#2357;&#2367;&#2358;&#2375;&#2359; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">(specialized programs) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2319;&#2325;&#2368;&#2325;&#2371;&#2340; &#2360;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\">(integrated set) </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2361;&#2376; </strong>&#2332;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2360;&#2350;&#2327;&#2381;&#2352; &#2360;&#2306;&#2330;&#2366;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\">(overall operation) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">(control) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; </span><span style=\"font-family: Cambria Math;\">&#2319;&#2350;&#2319;&#2360; &#2337;&#2377;&#2360;</span><span style=\"font-family: Cambria Math;\">(MS DOS), </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2344;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">(LInux) </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> C, BASIC, COBOL and Java are examples of ......... languages.</span></p>\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> C, BASIC, COBOL </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; </span><span style=\"font-family: Cambria Math;\">Java ........... </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2359;&#2366;&#2323;&#2306; &#2325;&#2375; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2361;&#2376;&#2306;&#2404; </span></p>\n",
                    options_en: ["<p>Low level</p>\n", "<p>Computer</p>\n", 
                                "<p>System programming</p>\n", "<p>High level</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2354;&#2379; &#2354;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\">(Low level) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;(computer)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2367;&#2306;&#2327;(system programming)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2361;&#2366;&#2312; &#2354;&#2375;&#2357;&#2354;(High level)</span></p>\n"],
                    solution_en: "<p>9.(d)<span style=\"font-family: Cambria Math;\"> High level language is a machine </span><strong><span style=\"font-family: Cambria Math;\">independent language</span></strong><span style=\"font-family: Cambria Math;\"><strong> that works on different computer systems regardless of their components</strong>. Thus a program written in one machine would run on any other machine.e.g. BASIC, C, FORTRAN, Java, Pascal, etc.</span></p>\n",
                    solution_hi: "<p>9.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2312; &#2354;&#2375;&#2357;&#2354; &#2354;&#2376;&#2306;&#2327;&#2381;&#2357;&#2375;&#2332;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\">(high level language) </span><strong><span style=\"font-family: Cambria Math;\">&#2319;&#2325; </span><span style=\"font-family: Cambria Math;\">&#2350;&#2358;&#2368;&#2344; </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2337;&#2367;&#2346;&#2375;&#2306;&#2337;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> (machine-independent) </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2359;&#2366; &#2361;&#2376; &#2332;&#2379; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2346;&#2352; &#2313;&#2344;&#2325;&#2375; &#2325;&#2377;&#2350;&#2381;&#2346;&#2379;&#2344;&#2375;&#2344;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">(component) </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2325;&#2368; &#2346;&#2352;&#2357;&#2366;&#2361; &#2325;&#2367;&#2319; &#2348;&#2367;&#2344;&#2366;</strong> &#2325;&#2366;&#2350; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404; &#2311;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2319;&#2325; &#2350;&#2358;&#2368;&#2344; &#2350;&#2375;&#2306; &#2354;&#2367;&#2326;&#2366; &#2327;&#2351;&#2366; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2325;&#2367;&#2360;&#2368; &#2309;&#2344;&#2381;&#2351; &#2350;&#2358;&#2368;&#2344; &#2346;&#2352; &#2330;&#2354;&#2375;&#2327;&#2366;&#2404;&#2313;&#2342;&#2361;&#2366;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2375;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">(BASIC), </span><span style=\"font-family: Cambria Math;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\">(C), </span><span style=\"font-family: Cambria Math;\">&#2347;&#2379;&#2352;&#2335;&#2381;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">(FORTRAN), </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">(Java), </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2360;&#2381;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\">(Pascal), </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "31",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> For creating a new document, you use which command at File menu?</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2344;&#2351;&#2366; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335; &#2348;&#2344;&#2366;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2310;&#2346; &#2347;&#2366;&#2311;&#2354; &#2350;&#2375;&#2344;&#2381;&#2351;&#2370; &#2350;&#2375;&#2306; &#2325;&#2367;&#2360; &#2325;&#2350;&#2366;&#2306;&#2337; &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Open</p>\n", "<p>Close</p>\n", 
                                "<p>Save</p>\n", "<p>New</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2323;&#2346;&#2344; (open)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2381;&#2354;&#2379;&#2332; (close)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2375;&#2357; (save)</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2370; </span><span style=\"font-family: Cambria Math;\">(new) </span></p>\n"],
                    solution_en: "<p>10.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">For creating a document we use a </span><strong><span style=\"font-family: Cambria Math;\">new </span></strong><span style=\"font-family: Cambria Math;\"><strong>command at file menu to open a new document</strong>. (</span><span style=\"font-family: Cambria Math;\">Ctrl+O</span><span style=\"font-family: Cambria Math;\">) Displays the Open dialog box where you can select an existing file to open.</span></p>\n",
                    solution_hi: "<p>10.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350; &#2319;&#2325; &#2344;&#2351;&#2366; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\">(Document) </span><span style=\"font-family: Cambria Math;\">&#2326;&#2379;&#2354;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; <strong>&#2347;&#2364;&#2366;&#2311;&#2354; &#2350;&#2375;&#2344;&#2370;</strong></span><strong><span style=\"font-family: Cambria Math;\">(menu) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2319;&#2325; </span><span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2350;&#2366;&#2306;&#2337; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2319;&#2325; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335;</span></strong><span style=\"font-family: Cambria Math;\"><strong>(Document)</strong> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2366;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><span style=\"font-family: Cambria Math;\">(Ctrl+O) </span><span style=\"font-family: Cambria Math;\">&#2323;&#2346;&#2344; &#2337;&#2366;&#2351;&#2354;&#2377;&#2327; &#2348;&#2377;&#2325;&#2381;&#2360; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2361;&#2366;&#2306; <strong>&#2310;&#2346; &#2325;&#2367;&#2360;&#2368; &#2350;&#2380;&#2332;&#2370;&#2342;&#2366; &#2347;&#2364;&#2366;&#2311;&#2354; &#2325;&#2379; &#2326;&#2379;&#2354;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2330;&#2369;&#2344; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;</strong>&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "31",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> Which among the following is not such an operation which can be carried out on objects in a graphic program?</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2320;&#2360;&#2366; &#2321;&#2346;&#2352;&#2375;&#2358;&#2344; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376; &#2332;&#2367;&#2360;&#2375; &#2327;&#2381;&#2352;&#2366;&#2347;&#2367;&#2325; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\">(graphic program) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2321;&#2348;&#2381;&#2332;&#2375;&#2325;&#2381;&#2335; &#2346;&#2352; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Change size</p>\n", "<p>Move</p>\n", 
                                "<p>Spell check</p>\n", "<p>None of the above</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2330;&#2375;&#2306;&#2332; &#2360;&#2366;&#2311;&#2332; (change size)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2370;&#2357; (move)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2346;&#2375;&#2354; &#2330;&#2375;&#2325;(spell check)</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2379;&#2312; &#2344;&#2361;&#2368;&#2306;</span></p>\n"],
                    solution_en: "<p>11.(d)<span style=\"font-family: Cambria Math;\"> Graphic </span><span style=\"font-family: Cambria Math;\">involves </span><span style=\"font-family: Cambria Math;\">computations, creation, and manipulation of data</span><span style=\"font-family: Cambria Math;\">. In other words, we can say that <strong>computer graphics is a rendering tool for the generation and manipulation of images.</strong></span></p>\n",
                    solution_hi: "<p>11.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2381;&#2352;&#2366;&#2347;&#2367;&#2325; &#2350;&#2375;&#2306; &#2337;&#2375;&#2335;&#2366; &#2325;&#2368; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2375;&#2358;&#2344;&#2381;&#2360; </span><span style=\"font-family: Cambria Math;\">(computations), </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2367;&#2319;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\">(creation) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2350;&#2376;&#2344;&#2368;&#2346;&#2369;&#2354;&#2375;&#2358;&#2344; </span><span style=\"font-family: Cambria Math;\">(manipulation) </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2404; &#2342;&#2370;&#2360;&#2352;&#2375; &#2358;&#2348;&#2381;&#2342;&#2379;&#2306; &#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350; &#2325;&#2361; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2367; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2327;&#2381;&#2352;&#2366;&#2347;&#2367;&#2325;&#2381;&#2360; <strong>&#2311;&#2350;&#2375;&#2332;&#2375;&#2332;</strong></span><strong><span style=\"font-family: Cambria Math;\">(images) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2324;&#2352; &#2350;&#2376;&#2344;&#2368;&#2346;&#2369;&#2354;&#2375;&#2358;&#2344; </span><span style=\"font-family: Cambria Math;\">(manipulation) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2352;&#2375;&#2306;&#2337;&#2352;&#2367;&#2306;&#2327; &#2335;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">rendering tool</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2361;&#2376;</strong>&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "31",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> Which area in an Excel window allows entering values and formulas?</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2357;&#2367;&#2306;&#2337;&#2379; &#2350;&#2375;&#2306; &#2325;&#2380;&#2344; &#2360;&#2366; &#2319;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">(area) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2370;&#2354;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">(values) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2360;&#2370;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">(formulas) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2342;&#2352;&#2381;&#2332; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Standard Toolbar</p>\n", "<p>Menu Bar</p>\n", 
                                "<p>Title Bar</p>\n", "<p>Formula Bar</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2335;&#2376;&#2339;&#2381;&#2337;&#2352;&#2381;&#2337; &#2335;&#2370;&#2354;&#2348;&#2366;&#2352; (Standard Toolbar)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2375;&#2344;&#2381;&#2351;&#2370; &#2348;&#2366;&#2352; (Menu Bar)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2335;&#2366;&#2311;&#2335;&#2354; &#2348;&#2366;&#2352; (Title Bar)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2347;&#2377;&#2352;&#2381;&#2350;&#2370;&#2354;&#2366; &#2348;&#2366;&#2352; (Formula Bar)</span></p>\n"],
                    solution_en: "<p>12.(d)<span style=\"font-family: Cambria Math;\"> We use</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">formula bars </span><span style=\"font-family: Cambria Math;\">to enter a <strong>new formula or copy an existing formula; its uses also include displaying and editing formulas</strong>.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: Cambria Math;\">Menu Bar</span><span style=\"font-family: Cambria Math;\"> at the top of the screen gives you access to <strong>different commands that are used for such tasks as opening and closing files, and other operations</strong>.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The title bar at the top of a window <strong>displays an application-defined icon and line of text</strong>. The text specifies the name of the application and indicates the purpose of the window.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Standard toolbar</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Contains buttons<strong> that are shortcuts to some commonly used menu items</strong>.</span></p>\n",
                    solution_hi: "<p>12.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350; &#2325;&#2379;&#2312; &#2344;&#2351;&#2366; &#2347;&#2364;&#2377;&#2352;&#2381;&#2350;&#2370;&#2354;&#2366; &#2342;&#2352;&#2381;&#2332; &#2325;&#2352;&#2344;&#2375; &#2351;&#2366; &#2350;&#2380;&#2332;&#2370;&#2342;&#2366; <strong>&#2347;&#2364;&#2377;&#2352;&#2381;&#2350;&#2370;&#2354;&#2366; &#2325;&#2377;&#2346;&#2368; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2347;&#2364;&#2377;&#2352;&#2381;&#2350;&#2370;&#2354;&#2366; &#2348;&#2366;&#2352;</strong></span><strong><span style=\"font-family: Cambria Math;\">(formula bar) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</span></strong><span style=\"font-family: Cambria Math;\">; </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2375; &#2313;&#2346;&#2351;&#2379;&#2327; &#2350;&#2375;&#2306; &#2347;&#2364;&#2366;&#2352;&#2381;&#2350;&#2369;&#2354;&#2379;&#2306; &#2325;&#2379; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2367;&#2340; &#2325;&#2352;&#2344;&#2366; &#2324;&#2352; &#2319;&#2337;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\">(edit) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366; &#2349;&#2368; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2381;&#2352;&#2368;&#2344; &#2325;&#2375; &#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\">(top) </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352; &#2360;&#2381;&#2341;&#2367;&#2340; </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2344;&#2370; &#2348;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2346;&#2325;&#2379; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; <strong>&#2325;&#2350;&#2366;&#2306;&#2337;&#2379;&#2306; &#2340;&#2325; &#2346;&#2361;&#2369;&#2306;&#2330; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2367;&#2344;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2347;&#2366;&#2311;&#2354;&#2379;&#2306; &#2325;&#2379; &#2326;&#2379;&#2354;&#2344;&#2375; &#2324;&#2352; &#2348;&#2306;&#2342; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2309;&#2344;&#2381;&#2351; &#2325;&#2366;&#2352;&#2381;&#2351;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</strong>&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2306;&#2337;&#2379; &#2325;&#2375; &#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\">(top) </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352; &#2335;&#2366;&#2311;&#2335;&#2354; &#2348;&#2366;&#2352; <strong>&#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344;</strong></span><strong><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2347;&#2364;&#2366;&#2311;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">application-defined</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2310;&#2311;&#2325;&#2344; &#2324;&#2352; &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2325;&#2368; &#2354;&#2366;&#2311;&#2344; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</strong>&#2404; &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">application</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2344;&#2366;&#2350; &#2344;&#2367;&#2352;&#2381;&#2342;&#2367;&#2359;&#2381;&#2335; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2357;&#2367;&#2306;&#2337;&#2379; &#2325;&#2375; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2325;&#2379; &#2311;&#2306;&#2327;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">(indicate) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2376;&#2339;&#2381;&#2337;&#2352;&#2381;&#2337; &#2335;&#2370;&#2354;&#2348;&#2366;&#2352; </span><span style=\"font-family: Cambria Math;\">(Standard Toolbar) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2320;&#2360;&#2375; &#2348;&#2335;&#2344; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306; &#2332;&#2379; &#2325;&#2369;&#2331; &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351; &#2352;&#2370;&#2346; &#2360;&#2375; <strong>&#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2319; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2350;&#2375;&#2344;&#2370; &#2310;&#2311;&#2335;&#2350; &#2325;&#2375; &#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335;</strong></span><strong><span style=\"font-family: Cambria Math;\">(shortcut) </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;</strong>&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "31",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> A collection of conceptual tools for describing data, relationships, semantics and </span><span style=\"font-family: Cambria Math;\">constraints is referred to as_________.</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2358;&#2348;&#2381;&#2342;&#2366;&#2352;&#2381;&#2341; &#2324;&#2352; &#2357;&#2367;&#2357;&#2358;&#2340;&#2366;&#2323;&#2306; &#2325;&#2366; &#2357;&#2352;&#2381;&#2339;&#2344; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2357;&#2376;&#2330;&#2366;&#2352;&#2367;&#2325; &#2313;&#2346;&#2325;&#2352;&#2339;&#2379;&#2306; &#2325;&#2366; &#2319;&#2325; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2354;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306;____________</span></p>\n",
                    options_en: ["<p>E-R model</p>\n", "<p>database</p>\n", 
                                "<p>data model</p>\n", "<p>DBMS</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2312;-&#2310;&#2352; &#2350;&#2377;&#2337;&#2354;(E-R model)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360;(database)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2337;&#2375;&#2335;&#2366; &#2350;&#2377;&#2337;&#2354;(data model)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2337;&#2368;&#2348;&#2368;&#2319;&#2350;&#2319;&#2360;(DBMS)</span></p>\n"],
                    solution_en: "<p>13.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Data models</span><span style=\"font-family: Cambria Math;\"> are used to <strong>describe how the data is stored, accessed, and updated in a DBMS</strong>. An </span><span style=\"font-family: Cambria Math;\">Entity&ndash;relationship model</span><span style=\"font-family: Cambria Math;\"> (ER model) describes the structure of <strong>a database with the help of a diagram</strong>. A </span><span style=\"font-family: Cambria Math;\">database</span><span style=\"font-family: Cambria Math;\"> is an organized <strong>collection of structured information, or data, typically stored electronically in a computer system</strong>. A</span><span style=\"font-family: Cambria Math;\"> database management system</span><span style=\"font-family: Cambria Math;\"> is a software tool used <strong>to create and manage one or more databases, offering an easy way to create a database</strong>, update tables, retrieve information, and enhance data.</span></p>\n",
                    solution_hi: "<p>13.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366; &#2350;&#2377;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2351;&#2361; &#2348;&#2340;&#2366;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; &#2325;&#2367; </span><strong><span style=\"font-family: Cambria Math;\">DBMS </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2337;&#2375;&#2335;&#2366; &#2325;&#2376;&#2360;&#2375; &#2360;&#2381;&#2335;&#2379;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\">(stored) ,</span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\">(access) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2309;&#2346;&#2337;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\">(update) </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</strong>&#2404; &#2319;&#2325; </span><span style=\"font-family: Cambria Math;\">&#2319;&#2306;&#2335;&#2367;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2352;&#2367;&#2354;&#2375;&#2358;&#2344;&#2358;&#2367;&#2346; &#2350;&#2377;&#2337;&#2354; </span><strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2312;&#2310;&#2352; &#2350;&#2377;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2337;&#2366;&#2351;&#2327;&#2381;&#2352;&#2366;&#2350; </span><span style=\"font-family: Cambria Math;\">(diagram) </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2325;&#2368; &#2350;&#2342;&#2342; &#2360;&#2375; &#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2325;&#2368; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2325;&#2366; &#2357;&#2352;&#2381;&#2339;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</strong>&#2404; &#2319;&#2325; &#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2360;&#2306;&#2352;&#2330;&#2367;&#2340; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">(structure information), </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366; &#2337;&#2375;&#2335;&#2366; &#2325;&#2366; &#2319;&#2325; &#2360;&#2306;&#2327;&#2336;&#2367;&#2340; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\">(organized collection) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\"><strong>&#2332;&#2367;&#2360;&#2375; &#2310;&#2350;&#2340;&#2380;&#2352; &#2346;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2350;&#2375;&#2306; &#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325; &#2352;&#2370;&#2346; &#2360;&#2375; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361;&#2368;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</strong>&#2404; &#2319;&#2325; &#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2350;&#2376;&#2344;&#2375;&#2332;&#2350;&#2375;&#2306;&#2335; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2319;&#2325; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2313;&#2346;&#2325;&#2352;&#2339; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; <strong>&#2319;&#2325; &#2351;&#2366; &#2309;&#2343;&#2367;&#2325; &#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2348;&#2344;&#2366;&#2344;&#2375; &#2324;&#2352; &#2346;&#2381;&#2352;&#2348;&#2306;&#2343;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</strong></span><span style=\"font-family: Cambria Math;\"> &#2404; </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361; &#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2348;&#2344;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2335;&#2375;&#2348;&#2354; &#2309;&#2346;&#2337;&#2375;&#2335; &#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2346;&#2369;&#2344;&#2352;&#2381;&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2337;&#2375;&#2335;&#2366; &#2348;&#2338;&#2364;&#2366;&#2344;&#2375; &#2325;&#2366; &#2319;&#2325; &#2310;&#2360;&#2366;&#2344; &#2340;&#2352;&#2368;&#2325;&#2366; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "31",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> ......... is the most important/powerful computer in a typical network.</span></p>\n",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> ......... </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2350;&#2375;&#2306; &#2360;&#2348;&#2360;&#2375; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2358;&#2325;&#2381;&#2340;&#2367;&#2358;&#2366;&#2354;&#2368; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Desktop</p>\n", "<p>Network client</p>\n", 
                                "<p>Network server</p>\n", "<p>Network station</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2337;&#2375;&#2360;&#2381;&#2325;&#2335;&#2377;&#2346;(desktop)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2325;&#2381;&#2354;&#2366;&#2311;&#2306;&#2335;(network client)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2360;&#2352;&#2381;&#2357;&#2352;(network server)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2360;&#2381;&#2335;&#2375;&#2358;&#2344;(network station)</span></p>\n"],
                    solution_en: "<p>14.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">A network server</span><span style=\"font-family: Cambria Math;\"> is a powerful computer that <strong>provides various shared resources to workstations and other servers on a network</strong>.</span></p>\n",
                    solution_hi: "<p>14.(c) <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2360;&#2352;&#2381;&#2357;&#2352;</span><span style=\"font-family: Cambria Math;\">(network server) </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2358;&#2325;&#2381;&#2340;&#2367;&#2358;&#2366;&#2354;&#2368; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2361;&#2376; &#2332;&#2379; <strong>&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2346;&#2352; &#2357;&#2352;&#2381;&#2325;&#2360;&#2381;&#2335;&#2375;&#2358;&#2344;</strong></span><strong><span style=\"font-family: Cambria Math;\">(workstation) </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2324;&#2352; &#2309;&#2344;&#2381;&#2351; &#2360;&#2352;&#2381;&#2357;&#2352;&#2379;&#2306; &#2325;&#2379; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2360;&#2366;&#2333;&#2366; &#2360;&#2306;&#2360;&#2366;&#2343;&#2344; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</strong>&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "31",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> A Website address is a unique name that identifies a specific ......... on the Web.</span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2348;&#2360;&#2366;&#2311;&#2335; &#2319;&#2337;&#2381;&#2352;&#2375;&#2360; &#2319;&#2325; &#2309;&#2344;&#2370;&#2336;&#2366; &#2344;&#2366;&#2350; &#2361;&#2376; &#2332;&#2379; &#2357;&#2375;&#2348; &#2346;&#2352; &#2319;&#2325; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; </span><span style=\"font-family: Cambria Math;\">........ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368; &#2346;&#2361;&#2330;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Web browser</p>\n", "<p>Website</p>\n", 
                                "<p>PDA</p>\n", "<p>link</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2357;&#2375;&#2348; &#2348;&#2381;&#2352;&#2366;&#2313;&#2332;&#2364;&#2352;(Web browser)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2357;&#2375;&#2348;&#2360;&#2366;&#2311;&#2335;(Website)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2346;&#2368;&#2337;&#2368;&#2319;(PDA)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2354;&#2367;&#2306;&#2325;(link)</span></p>\n"],
                    solution_en: "<p>15.(b)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Cambria Math;\">website</span><span style=\"font-family: Cambria Math;\"> is a<strong> collection of publicly accessible, interlinked Web pages that share a single domain name</strong>.</span><strong><span style=\"font-family: Cambria Math;\">Personal Digital Assistants (PDAs)</span></strong><span style=\"font-family: Cambria Math;\"> are small networked computers which can fit in the palm of your hand.</span><span style=\"font-family: Cambria Math;\">Link</span><span style=\"font-family: Cambria Math;\"> is an item like a word or button <strong>that points to another location</strong>. When you click on a link, the link will <strong>take you to the target of the link</strong>, which may be a webpage, document or other online content. A </span><strong><span style=\"font-family: Cambria Math;\">web browser</span></strong><span style=\"font-family: Cambria Math;\"><strong> takes you anywhere on the internet</strong>, letting you see text, images and video from anywhere in the world.</span></p>\n",
                    solution_hi: "<p>15.(b)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2348;&#2360;&#2366;&#2311;&#2335;</span><span style=\"font-family: Cambria Math;\">(website) </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325; <strong>&#2352;&#2370;&#2346; &#2360;&#2375; &#2360;&#2369;&#2354;&#2349;</strong></span><strong><span style=\"font-family: Cambria Math;\">, </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2310;&#2346;&#2360; &#2350;&#2375;&#2306; &#2332;&#2369;&#2337;&#2364;&#2375; &#2357;&#2375;&#2348; &#2346;&#2375;&#2332;&#2379;&#2306; &#2325;&#2366; &#2319;&#2325; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361; &#2361;&#2376; &#2332;&#2379; &#2319;&#2325; &#2337;&#2379;&#2350;&#2375;&#2344; &#2344;&#2366;&#2350; &#2360;&#2366;&#2333;&#2366; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</strong>&#2404; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2327;&#2340; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2360;&#2361;&#2366;&#2351;&#2325; </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2368;&#2337;&#2368;&#2319;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2331;&#2379;&#2335;&#2375; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2357;&#2366;&#2354;&#2375; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2361;&#2376;&#2306; <strong>&#2332;&#2379; &#2310;&#2346;&#2325;&#2375; &#2361;&#2366;&#2341; &#2325;&#2368; &#2361;&#2341;&#2375;&#2354;&#2368; &#2350;&#2375;&#2306; &#2347;&#2367;&#2335; &#2361;&#2379; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;</strong>&#2404; &#2354;&#2367;&#2306;&#2325; &#2319;&#2325; &#2358;&#2348;&#2381;&#2342; &#2325;&#2368; &#2340;&#2352;&#2361; &#2319;&#2325; &#2310;&#2311;&#2335;&#2350; &#2351;&#2366; &#2348;&#2335;&#2344; &#2361;&#2376; <strong>&#2332;&#2379; &#2325;&#2367;&#2360;&#2368; &#2309;&#2344;&#2381;&#2351; &#2360;&#2381;&#2341;&#2366;&#2344; &#2325;&#2368; &#2323;&#2352; &#2311;&#2358;&#2366;&#2352;&#2366; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</strong>&#2404; &#2332;&#2348; &#2310;&#2346; &#2325;&#2367;&#2360;&#2368; &#2354;&#2367;&#2306;&#2325; &#2346;&#2352; &#2325;&#2381;&#2354;&#2367;&#2325; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379; &#2354;&#2367;&#2306;&#2325; &#2310;&#2346;&#2325;&#2379; &#2354;&#2367;&#2306;&#2325; &#2325;&#2375; &#2354;&#2325;&#2381;&#2359;&#2381;&#2351; &#2340;&#2325; &#2354;&#2375; &#2332;&#2366;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><strong><span style=\"font-family: Cambria Math;\">&#2332;&#2379; &#2319;&#2325; &#2357;&#2375;&#2348;&#2346;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\">, </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364; &#2351;&#2366; &#2309;&#2344;&#2381;&#2351; &#2321;&#2344;&#2354;&#2366;&#2311;&#2344; &#2325;&#2306;&#2335;&#2375;&#2306;&#2335; &#2361;&#2379; &#2360;&#2325;&#2340;&#2368; &#2361;&#2376;</strong>&#2404; &#2319;&#2325; &#2357;&#2375;&#2348; &#2348;&#2381;&#2352;&#2366;&#2313;&#2332;&#2364;&#2352; &#2310;&#2346;&#2325;&#2379; &#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2335; &#2346;&#2352; &#2325;&#2361;&#2368;&#2306; &#2349;&#2368; &#2354;&#2375; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2360;&#2375; &#2310;&#2346; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2350;&#2375;&#2306; &#2325;&#2361;&#2368;&#2306; &#2360;&#2375; &#2349;&#2368; &#2346;&#2366;&#2336;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2330;&#2367;&#2340;&#2381;&#2352; &#2324;&#2352; &#2357;&#2368;&#2337;&#2367;&#2351;&#2379; &#2342;&#2375;&#2326; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "16",
                    section: "31",
                    question_en: "<p>16.<span style=\"font-family: Cambria Math;\"> A ......... is a small program embedded inside of a GIF image.</span></p>\n",
                    question_hi: "<p>16.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; </span><span style=\"font-family: Cambria Math;\">......... GIF </span><span style=\"font-family: Cambria Math;\">&#2331;&#2357;&#2367; &#2325;&#2375; &#2309;&#2306;&#2342;&#2352; &#2360;&#2344;&#2381;&#2344;&#2367;&#2361;&#2367;&#2340; &#2319;&#2325; &#2331;&#2379;&#2335;&#2366; &#2360;&#2366; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>spyware application</p>\n", "<p>Web bug</p>\n", 
                                "<p>cookie</p>\n", "<p>spam</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2366;&#2311;&#2357;&#2375;&#2351;&#2352; &#2309;&#2344;&#2369;&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2357;&#2375;&#2348; &#2348;&#2327; (Web bug)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2325;&#2369;&#2325;&#2368; (cookie)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2346;&#2376;&#2350; (spam)</span></p>\n"],
                    solution_en: "<p>16.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">The new attack is called GIFShell and it <strong>installs malware on your computer to steal data</strong>.It can be done by a </span><span style=\"font-family: Cambria Math;\">spyware application.<strong>Web Bug</strong></span><span style=\"font-family: Cambria Math;\">&rarr;</span><span style=\"font-family: Cambria Math;\">Malicious code, invisible to a user, placed on web sites in such a way that it allows third parties to track use of web servers and collect information about the user. </span><strong><span style=\"font-family: Cambria Math;\">Cookie</span></strong><span style=\"font-family: Cambria Math;\">&rarr;</span><span style=\"font-family: Cambria Math;\">A cookie is information that a website puts on a user\'s computer. Cookies store limited information from a web browser session on a given website that can then be retrieved in the future. </span><strong><span style=\"font-family: Cambria Math;\">Spam</span></strong><span style=\"font-family: Cambria Math;\">&rarr;</span><span style=\"font-family: Cambria Math;\">Spam is any kind of unwanted, unsolicited digital communication that gets sent out in bulk. Often spam is sent via email, but it can also be distributed via text messages, phone calls, or social media.</span></p>\n",
                    solution_hi: "<p>16.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2319; &#2361;&#2350;&#2354;&#2375; &#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">GIFShell </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; <strong>&#2351;&#2361; &#2337;&#2375;&#2335;&#2366; &#2330;&#2379;&#2352;&#2368; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2310;&#2346;&#2325;&#2375; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2346;&#2352; &#2350;&#2376;&#2354;&#2357;&#2375;&#2351;&#2352; &#2311;&#2306;&#2360;&#2381;&#2335;&#2377;&#2354; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</strong>&#2404; &#2351;&#2361; &#2319;&#2325; &#2360;&#2381;&#2346;&#2366;&#2311;&#2357;&#2375;&#2351;&#2352; &#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; <strong>&#2357;&#2375;&#2348; &#2348;&#2327;</strong> </span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2352;&#2381;&#2349;&#2366;&#2357;&#2344;&#2366;&#2346;&#2370;&#2352;&#2381;&#2339; &#2325;&#2379;&#2337;(malicious code)</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2325;&#2375; &#2354;&#2367;&#2319; &#2309;&#2342;&#2371;&#2358;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2348; &#2360;&#2366;&#2311;&#2335;&#2379;&#2306; &#2346;&#2352; &#2311;&#2360; &#2340;&#2352;&#2361; &#2352;&#2326;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; &#2325;&#2367; &#2351;&#2361; &#2340;&#2368;&#2360;&#2352;&#2375; &#2346;&#2325;&#2381;&#2359; &#2325;&#2379; &#2357;&#2375;&#2348; &#2360;&#2352;&#2381;&#2357;&#2352; &#2325;&#2375; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2379; &#2335;&#2381;&#2352;&#2376;&#2325; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2325;&#2375; &#2348;&#2366;&#2352;&#2375; &#2350;&#2375;&#2306; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2319;&#2325;&#2340;&#2381;&#2352; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; <strong>&#2325;&#2369;&#2325;&#2368; </strong></span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2325;&#2368; &#2357;&#2361; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2361;&#2376; &#2332;&#2379; &#2319;&#2325; &#2357;&#2375;&#2348;&#2360;&#2366;&#2311;&#2335; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2325;&#2375; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2346;&#2352; &#2337;&#2366;&#2354;&#2340;&#2368; &#2361;&#2376;&#2404; &#2325;&#2369;&#2325;&#2368;&#2332;&#2364; &#2325;&#2367;&#2360;&#2368; &#2357;&#2375;&#2348; &#2348;&#2381;&#2352;&#2366;&#2313;&#2332;&#2364;&#2352; &#2360;&#2340;&#2381;&#2352; &#2360;&#2375; &#2360;&#2368;&#2350;&#2367;&#2340; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2325;&#2379; &#2325;&#2367;&#2360;&#2368; &#2357;&#2375;&#2348;&#2360;&#2366;&#2311;&#2335; &#2346;&#2352; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361;&#2368;&#2340; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2306; &#2332;&#2367;&#2360;&#2375; &#2349;&#2357;&#2367;&#2359;&#2381;&#2351; &#2350;&#2375;&#2306; &#2346;&#2369;&#2344;&#2352;&#2381;&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; <strong>&#2360;&#2381;&#2346;&#2376;&#2350; </strong></span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2376;&#2350; &#2325;&#2367;&#2360;&#2368; &#2349;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2309;&#2357;&#2366;&#2306;&#2331;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2366;&#2306;&#2331;&#2367;&#2340; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2360;&#2306;&#2330;&#2366;&#2352; &#2361;&#2376; &#2332;&#2379; &#2341;&#2379;&#2325; &#2350;&#2375;&#2306; &#2349;&#2375;&#2332;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2309;&#2325;&#2381;&#2360;&#2352; &#2360;&#2381;&#2346;&#2376;&#2350; &#2312;&#2350;&#2375;&#2354; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2349;&#2375;&#2332;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2325;&#2367;&#2344; &#2311;&#2360;&#2375; &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2350;&#2376;&#2360;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2347;&#2379;&#2344; &#2325;&#2377;&#2354; &#2351;&#2366; &#2360;&#2379;&#2358;&#2354; &#2350;&#2368;&#2337;&#2367;&#2351;&#2366; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2349;&#2368; &#2357;&#2367;&#2340;&#2352;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "17",
                    section: "31",
                    question_en: "<p>17.<span style=\"font-family: Cambria Math;\"> What type of device is a computer monitor?</span></p>\n",
                    question_hi: "<p>17.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2350;&#2377;&#2344;&#2368;&#2335;&#2352; &#2325;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Software</p>\n", "<p>Processing</p>\n", 
                                "<p>Input</p>\n", "<p>Output</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (Software)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327; (Processing)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2311;&#2344;&#2346;&#2369;&#2335; (Input)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2310;&#2313;&#2335;&#2346;&#2369;&#2335; (Output)</span></p>\n"],
                    solution_en: "<p>17.(d) <span style=\"font-family: Cambria Math;\">A computer monitor is an </span><span style=\"font-family: Cambria Math;\">output device</span><span style=\"font-family: Cambria Math;\"> that <strong>displays information in pictorial or textual form</strong>.</span></p>\n",
                    solution_hi: "<p>17.(d) <span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2350;&#2377;&#2344;&#2367;&#2335;&#2352; &#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2313;&#2335;&#2346;&#2369;&#2335; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2332;&#2379; <strong>&#2330;&#2367;&#2340;&#2381;&#2352;&#2366;&#2340;&#2381;&#2350;&#2325; &#2351;&#2366; &#2346;&#2366;&#2336;&#2381;&#2351; </strong></span><strong><span style=\"font-family: Cambria Math;\">(pictorial or textual) </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</strong>&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "18",
                    section: "31",
                    question_en: "<p>18.<span style=\"font-family: Cambria Math;\"> The term ......... refers to data storage systems that make it possible for a computer or electronic device to store and retrieve data. </span></p>\n",
                    question_hi: "<p>18.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2348;&#2381;&#2342; </span><span style=\"font-family: Cambria Math;\">........ </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2379; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2351;&#2366; &#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2360;&#2381;&#2335;&#2379;&#2352; &#2324;&#2352; &#2346;&#2369;&#2344;&#2352;&#2381;&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2366; &#2360;&#2306;&#2349;&#2357; &#2348;&#2344;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>storage technology</p>\n", "<p>output technology</p>\n", 
                                "<p>input technology</p>\n", "<p>retrieval technology</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2335;&#2375;&#2325;&#2381;&#2344;&#2379;&#2354;&#2377;&#2332;&#2368; (storage technology)</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2310;&#2313;&#2335;&#2346;&#2369;&#2335; &#2335;&#2375;&#2325;&#2381;&#2344;&#2379;&#2354;&#2377;&#2332;&#2368; </span><span style=\"font-family: Cambria Math;\">(output technology) </span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2311;&#2344;&#2346;&#2369;&#2335; &#2335;&#2375;&#2325;&#2381;&#2344;&#2379;&#2354;&#2377;&#2332;&#2368; (input technology)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2352;&#2367;&#2335;&#2381;&#2352;&#2368;&#2357;&#2354; &#2335;&#2375;&#2325;&#2381;&#2344;&#2379;&#2354;&#2377;&#2332;&#2368; (retrieval technology)</span></p>\n"],
                    solution_en: "<p>18.(a)<span style=\"font-family: Cambria Math;\"> <strong>Storage Technology</strong> </span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">It </span><span style=\"font-family: Cambria Math;\"> is the collective methods and technologies that capture and retain digital information on electromagnetic, optical or silicon-based storage media. </span></p>\n",
                    solution_hi: "<p>18.(a)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2335;&#2375;&#2325;&#2381;&#2344;&#2379;&#2354;&#2377;&#2332;&#2368;</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361; &#2325;&#2354;&#2375;&#2325;&#2381;&#2335;&#2367;&#2357; &#2350;&#2375;&#2341;&#2337; &#2324;&#2352; &#2335;&#2375;&#2325;&#2381;&#2344;&#2379;&#2354;&#2377;&#2332;&#2368; &#2361;&#2376;&#2306; &#2332;&#2379; &#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2379;&#2350;&#2376;&#2327;&#2381;&#2344;&#2375;&#2335;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2321;&#2346;&#2381;&#2335;&#2367;&#2325;&#2354; &#2351;&#2366; &#2360;&#2367;&#2354;&#2367;&#2325;&#2377;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2310;&#2343;&#2366;&#2352;&#2367;&#2340; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2350;&#2368;&#2337;&#2367;&#2351;&#2366; &#2346;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2325;&#2379; &#2325;&#2376;&#2346;&#2381;&#2330;&#2352; &#2324;&#2352; &#2348;&#2344;&#2366;&#2319; &#2352;&#2326;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "19",
                    section: "31",
                    question_en: "<p>19. <span style=\"font-family: Cambria Math;\">Code &lsquo;EBCDIC&rsquo; that is used in computing stands for</span></p>\n",
                    question_hi: "<p>19.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2337; </span><span style=\"font-family: Cambria Math;\">\'EBCDIC\' </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2367;&#2306;&#2327; &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2325;&#2381;&#2351;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Extension BCD Information Code</p>\n", "<p>Extension BCD Interchange Conduct</p>\n", 
                                "<p>Extended BCD Interchange Conduct</p>\n", "<p>Extended BCD Information Code</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2335;&#2375;&#2306;&#2358;&#2344; </span><span style=\"font-family: Cambria Math;\">BCD </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2347;&#2366;&#2352;&#2381;&#2350;&#2375;&#2358;&#2344; &#2325;&#2379;&#2337; </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2335;&#2375;&#2306;&#2358;&#2344; </span><span style=\"font-family: Cambria Math;\">BCD </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2335;&#2352;&#2330;&#2375;&#2306;&#2332; &#2325;&#2306;&#2337;&#2325;&#2381;&#2335; </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2335;&#2375;&#2306;&#2337;&#2375;&#2337; </span><span style=\"font-family: Cambria Math;\">BCD </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2335;&#2352;&#2330;&#2375;&#2306;&#2332; &#2325;&#2306;&#2337;&#2325;&#2381;&#2335; </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2335;&#2375;&#2306;&#2337;&#2375;&#2337; </span><span style=\"font-family: Cambria Math;\">BCD </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2347;&#2366;&#2352;&#2381;&#2350;&#2375;&#2358;&#2344; &#2325;&#2379;&#2337; </span></p>\n"],
                    solution_en: "<p>19.(d)<span style=\"font-family: Cambria Math;\"> The correct answer is </span><span style=\"font-family: Cambria Math;\"><strong>Extended BCD Information Code</strong>.</span></p>\n",
                    solution_hi: "<p>19.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368; &#2313;&#2340;&#2381;&#2340;&#2352; </span><span style=\"font-family: Cambria Math;\"><strong>&#2319;&#2325;&#2381;&#2360;&#2335;&#2375;&#2306;&#2337;&#2375;&#2337;</strong> </span><strong><span style=\"font-family: Cambria Math;\">BCD </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2347;&#2366;&#2352;&#2381;&#2350;&#2375;&#2358;&#2344; &#2325;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\"><strong>&#2361;&#2376;</strong>&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20.<span style=\"font-family: Cambria Math;\"> Corel Ventura, Illustrator are the examples of</span></p>\n",
                    question_hi: "<p>20.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2352;&#2354; &#2357;&#2375;&#2306;&#2330;&#2369;&#2352;&#2366; </span><span style=\"font-family: Cambria Math;\">(Corel Ventura), </span><span style=\"font-family: Cambria Math;\">&#2311;&#2354;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2335;&#2352; </span><span style=\"font-family: Cambria Math;\">(Illustrator)____ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>DTP</p>\n", "<p>Multimedia</p>\n", 
                                "<p>Graphic</p>\n", "<p>Word Processing</p>\n"],
                    options_hi: ["<p>DTP</p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2354;&#2381;&#2335;&#2368;&#2350;&#2368;&#2337;&#2367;&#2351;&#2366; (Multimedia)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2327;&#2381;&#2352;&#2366;&#2347;&#2367;&#2325; (Graphic)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2337; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327; (Word Processing)</span></p>\n"],
                    solution_en: "<p>20.(a)<span style=\"font-family: Cambria Math;\"> <strong>DTP</strong></span><span style=\"font-family: Cambria Math;\"><strong> (Desktop publishing software)</strong> </span><span style=\"font-family: Cambria Math;\">is a tool for graphic designers and non-designers to create visual communications for professional or desktop printing as well as for online or on screen electronic publishing.e.g. Adobe PageMaker, CorelDraw, Corel Ventura,etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Word processors</strong> &rarr; </span><span style=\"font-family: Cambria Math;\">A word processor is a software program capable of creating, storing and printing of documents.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Graphic software</strong> &rarr;</span><span style=\"font-family: Cambria Math;\"> It is an application program or collection of programs that enable a person to manipulate visual images on a computer system for e.g. photoshop,coreldraw etc.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Multimedia software</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">Multimedia Includes a combination of text, audio, still images, animation, video of interactivity content forms. e.g.,Xilisoft Video Converter, VLC Media Player,etc</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    solution_hi: "<p>20.(a)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">DTP (</span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2360;&#2381;&#2325;&#2335;&#2377;&#2346; &#2346;&#2348;&#2381;&#2354;&#2367;&#2358;&#2367;&#2306;&#2327; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">&#2327;&#2381;&#2352;&#2366;&#2347;&#2367;&#2325; &#2337;&#2367;&#2332;&#2366;&#2311;&#2344;&#2352; &#2324;&#2352; &#2344;&#2377;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2332;&#2366;&#2311;&#2344;&#2352; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2375;&#2358;&#2375;&#2357;&#2352; &#2351;&#2366; &#2337;&#2375;&#2360;&#2381;&#2325;&#2335;&#2377;&#2346; &#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2367;&#2306;&#2327; &#2325;&#2375; &#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2341; &#2321;&#2344;&#2354;&#2366;&#2311;&#2344; </span><span style=\"font-family: Cambria Math;\">(online) </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366; &#2360;&#2381;&#2325;&#2381;&#2352;&#2368;&#2344; &#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325; &#2346;&#2348;&#2381;&#2354;&#2367;&#2358;&#2367;&#2306;&#2327; </span><span style=\"font-family: Cambria Math;\">(screen electronic publishing) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2354;&#2367;&#2319; &#2342;&#2371;&#2358;&#2381;&#2351; &#2360;&#2306;&#2330;&#2366;&#2352; </span><span style=\"font-family: Cambria Math;\">(visual communications) </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2366;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2313;&#2346;&#2325;&#2352;&#2339; </span><span style=\"font-family: Cambria Math;\">(tool) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; </span><span style=\"font-family: Cambria Math;\">Adobe PageMaker, CorelDraw, Corel Ventura </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2337; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352;</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2337; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352; &#2319;&#2325; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2361;&#2376; &#2332;&#2379; &#2337;&#2366;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335;&#2381;&#2360; &#2325;&#2379; &#2348;&#2344;&#2366;&#2344;&#2375; </span><span style=\"font-family: Cambria Math;\">(create), </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2379;&#2352; </span><span style=\"font-family: Cambria Math;\">(store) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2346;&#2381;&#2352;&#2367;&#2306;&#2335; </span><span style=\"font-family: Cambria Math;\">(print) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2360;&#2325;&#2381;&#2359;&#2350; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&#2327;&#2381;&#2352;&#2366;&#2347;&#2367;&#2325; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;</strong>&#2352; </span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361; &#2319;&#2325; &#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2351;&#2366; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2379;&#2306; &#2325;&#2366; &#2325;&#2354;&#2375;&#2325;&#2381;&#2358;&#2344; </span><span style=\"font-family: Cambria Math;\">(application program or collection of programs) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2332;&#2379; &#2325;&#2367;&#2360;&#2368; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2325;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2346;&#2352; &#2357;&#2367;&#2395;&#2369;&#2309;&#2354; &#2311;&#2350;&#2375;&#2332; </span><span style=\"font-family: Cambria Math;\">(visual image) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2350;&#2376;&#2344;&#2367;&#2346;&#2369;&#2354;&#2375;&#2335; </span><span style=\"font-family: Cambria Math;\">(manipulate) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2360;&#2325;&#2381;&#2359;&#2350; &#2348;&#2344;&#2366;&#2340;&#2366; &#2361;&#2376; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; &#2347;&#2379;&#2335;&#2379;&#2358;&#2377;&#2346; </span><span style=\"font-family: Cambria Math;\">(photoshop) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2325;&#2379;&#2352;&#2354; &#2337;&#2381;&#2352;&#2377; </span><span style=\"font-family: Cambria Math;\">(CorelDraw) </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&#2350;&#2354;&#2381;&#2335;&#2368;&#2350;&#2368;&#2337;&#2367;&#2351;&#2366; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2350;&#2354;&#2381;&#2335;&#2368;&#2350;&#2368;&#2337;&#2367;&#2351;&#2366; &#2350;&#2375;&#2306; &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; </span><span style=\"font-family: Cambria Math;\">(text), </span><span style=\"font-family: Cambria Math;\">&#2321;&#2337;&#2367;&#2351;&#2379; </span><span style=\"font-family: Cambria Math;\">(audio), </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2367;&#2354; &#2311;&#2350;&#2375;&#2332; </span><span style=\"font-family: Cambria Math;\">(still images), </span><span style=\"font-family: Cambria Math;\">&#2319;&#2344;&#2367;&#2350;&#2375;&#2358;&#2344; </span><span style=\"font-family: Cambria Math;\">(animation), </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2335;&#2352;&#2319;&#2325;&#2381;&#2335;&#2367;&#2357;&#2367;&#2335;&#2368; &#2325;&#2306;&#2335;&#2375;&#2306;&#2335; &#2347;&#2377;&#2352;&#2381;&#2350; &#2325;&#2375; &#2357;&#2368;&#2337;&#2367;&#2351;&#2379; </span><span style=\"font-family: Cambria Math;\">(video of interactivity content forms) </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; </span><span style=\"font-family: Cambria Math;\">Xilisoft </span><span style=\"font-family: Cambria Math;\">&#2357;&#2368;&#2337;&#2367;&#2351;&#2379; &#2325;&#2344;&#2381;&#2357;&#2352;&#2381;&#2335;&#2352; </span><span style=\"font-family: Cambria Math;\">(video converter), VLC </span><span style=\"font-family: Cambria Math;\">&#2350;&#2368;&#2337;&#2367;&#2351;&#2366; &#2346;&#2381;&#2354;&#2375;&#2351;&#2352; </span><span style=\"font-family: Cambria Math;\">(Media Player) </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>