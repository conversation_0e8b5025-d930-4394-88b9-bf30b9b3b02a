<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 18</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">18</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 16
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 17,
                end: 17
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. In the following table, the production of various crops (in tonnes) is given from 2015 to 2019. Study the table and answer the question that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415728610.png\" alt=\"rId4\" width=\"526\" height=\"131\"> <br>The average production of wheat (in tonnes) in the period given in the table is:</p>",
                    question_hi: "<p>1. दी गयी तालिका में, 2015 से 2019 तक विभिन्न फसलों का उत्पादन (टन में) दिया गया है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्न का उत्तर दीजिए |<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415728769.png\" alt=\"rId5\" width=\"381\" height=\"130\"> <br>तालिका में दी गयी अवधि में गेहूँ का औसत उत्पादन (टन में) कितना रहा है ?</p>",
                    options_en: ["<p>3600</p>", "<p>3482</p>", 
                                "<p>3300</p>", "<p>4218</p>"],
                    options_hi: ["<p>3600</p>", "<p>3482</p>",
                                "<p>3300</p>", "<p>4218</p>"],
                    solution_en: "<p>1.(a) <br>Total production of wheat = 2500 + 4218 + 3482 + 4500 + 3300 = 18000<br>Average production = <math display=\"inline\"><mfrac><mrow><mn>18000</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 3600</p>",
                    solution_hi: "<p>1.(a) गेहूँ का कुल उत्पादन&nbsp;= 2500 + 4218 + 3482 + 4500 + 3300 = 18000<br>औसत उत्पादन =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>18000</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 3600</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. In the following table, the production of various crops (in tonnes) is given from 2015 to 2019. Study the table and answer the question that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415728610.png\" alt=\"rId4\" width=\"501\" height=\"125\"> <br>The percentage growth of maize in the year 2019 over the year 2015 is: (correct to one decimal place)</p>",
                    question_hi: "<p>2. दी गयी तालिका में, 2015 से 2019 तक विभिन्न फसलों का उत्पादन (टन में) दिया गया है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्न का उत्तर दीजिए |<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415728769.png\" alt=\"rId5\" width=\"413\" height=\"141\"> <br>वर्ष 2019 में वर्ष 2015 की तुलना में मक्का के उत्पादन में कितने प्रतिशत की वृद्धि हुई है ? (दशमलव के एक स्थान तक)</p>",
                    options_en: ["<p>65.12%</p>", "<p>60.28%</p>", 
                                "<p>71.43%</p>", "<p>77.77%</p>"],
                    options_hi: ["<p>65.12%</p>", "<p>60.28%</p>",
                                "<p>71.43%</p>", "<p>77.77%</p>"],
                    solution_en: "<p>2.(c)&nbsp;Required % <br>=<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>840</mn><mo>-</mo><mn>490</mn></mrow><mn>490</mn></mfrac></math> &times; 100 = 71.43%</p>",
                    solution_hi: "<p>2.(c) आवश्यक % <br>=<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>840</mn><mo>-</mo><mn>490</mn></mrow><mn>490</mn></mfrac></math> &times; 100 = 71.43%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. In the following table, the production of various crops (in tonnes) is given from 2015 to 2019. Study the table and answer the question that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415728610.png\" alt=\"rId4\" width=\"490\" height=\"122\"> <br>The highest growth of sugarcane production over its previous year is recorded in the year:</p>",
                    question_hi: "<p>3. दी गयी तालिका में, 2015 से 2019 तक विभिन्न फसलों का उत्पादन (टन में) दिया गया है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्न का उत्तर दीजिए |<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415728769.png\" alt=\"rId5\" width=\"407\" height=\"139\"> <br>किस वर्ष गन्ने का उत्पादन पिछले वर्ष की तुलना में अधिकतम रहा है ?</p>",
                    options_en: ["<p>2019</p>", "<p>2016</p>", 
                                "<p>2018</p>", "<p>2017</p>"],
                    options_hi: ["<p>2019</p>", "<p>2016</p>",
                                "<p>2018</p>", "<p>2017</p>"],
                    solution_en: "<p>3.(b) <br>% increase in sugarcane in 2016 = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 100 = 12%&nbsp;<br>% increase in sugarcane in 2017 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>130</mn><mn>1120</mn></mfrac></math> &times; 100 = 11.6%<br>% increase in sugarcane in 2018 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>1250</mn></mfrac></math> &times; 100 = 6%<br>% increase in sugarcane in 2019 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>1325</mn></mfrac></math> &times; 100 = 9.4%<br>Highest growth in sugarcane production is in 2016.</p>",
                    solution_hi: "<p>3.(b) 2016 में गन्ने में % वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 100 = 12%&nbsp;<br>2017 में गन्ने में % वृद्धि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>130</mn><mn>1120</mn></mfrac></math> &times; 100 = 11.6%<br>2018 में गन्ने में % वृद्धि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>1250</mn></mfrac></math> &times; 100 = 6%<br>2019 में गन्ने में % वृद्धि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>1325</mn></mfrac></math> &times; 100 = 9.4%<br>गन्ना उत्पादन में सर्वाधिक वृद्धि 2016 में हुई है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. In the following table, the production of various crops (in tonnes) is given from 2015 to 2019. Study the table and answer the question that follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415728610.png\" alt=\"rId4\" width=\"509\" height=\"127\"> <br>The difference (in tonnes) between the average production of barley and average production of rice is:</p>",
                    question_hi: "<p>4. दी गयी तालिका में, 2015 से 2019 तक विभिन्न फसलों का उत्पादन (टन में) दिया गया है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्न का उत्तर दीजिए |<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415728769.png\" alt=\"rId5\" width=\"398\" height=\"136\"> <br>जौ के औसत उत्पादन तथा चावल के औसत उत्पादन के बीच क्या अंतर (टन में) है ?</p>",
                    options_en: ["<p>549</p>", "<p>231</p>", 
                                "<p>471</p>", "<p>780</p>"],
                    options_hi: ["<p>549</p>", "<p>231</p>",
                                "<p>471</p>", "<p>780</p>"],
                    solution_en: "<p>4.(b) <br>Average production of barley = <math display=\"inline\"><mfrac><mrow><mn>975</mn><mo>+</mo><mn>825</mn><mo>+</mo><mn>700</mn><mo>+</mo><mn>625</mn><mo>+</mo><mn>775</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 780<br>Average production of rice = <math display=\"inline\"><mfrac><mrow><mn>415</mn><mo>+</mo><mn>520</mn><mo>+</mo><mn>585</mn><mo>+</mo><mn>625</mn><mo>+</mo><mn>600</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 549<br>Required difference = 231 tonnes</p>",
                    solution_hi: "<p>4.(b) जौ का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>975</mn><mo>+</mo><mn>825</mn><mo>+</mo><mn>700</mn><mo>+</mo><mn>625</mn><mo>+</mo><mn>775</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 780<br>चावल का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>415</mn><mo>+</mo><mn>520</mn><mo>+</mo><mn>585</mn><mo>+</mo><mn>625</mn><mo>+</mo><mn>600</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>= 549<br>आवश्यक अंतर = 231 tonnes</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The following table shows the income (in ₹) for a particular month, together with their source, in respect of 5 employees(A, B, C, D and E).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415728867.png\" alt=\"rId6\" width=\"518\" height=\"136\"> <br>How many employees got more than a total of ₹ 10,000 as income other than salary ?</p>",
                    question_hi: "<p>5. निम्नलिखित तालिका 5 कर्मचारियों (A, B, C, D तथा E) के संबंध में एक विशेष माह की आय (रुपये में) तथा उसके स्रोत को दर्शाती है |&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415728979.png\" alt=\"rId7\" width=\"481\" height=\"161\"> <br>कितने कर्मचारियों को वेतन के अतिरिक्त कुल 10,000 रुपये से अधिक की आय हुई है ?</p>",
                    options_en: ["<p>4</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>3</p>"],
                    options_hi: ["<p>4</p>", "<p>1</p>",
                                "<p>2</p>", "<p>3</p>"],
                    solution_en: "<p>5.(c) <br>Income other than salary = total - salary<br>For A = ₹(66000 - 52000) = ₹ 14000<br>For B = ₹(59000 - 48500) = ₹ 10500<br>For C = ₹(52000 - 42000) = ₹ 10000<br>For D = ₹(40000 - 31000) = ₹ 9000<br>For E = ₹(31700 - 25000) = ₹ 6700<br>A and B got more than ₹ 10000 income other than their salary.</p>",
                    solution_hi: "<p>5.(c) वेतन के अलावा अन्य आय = कुल - वेतन<br>A के लिये = ₹(66000 - 52000) = ₹ 14000<br>B के लिये = ₹(59000 - 48500) = ₹ 10500<br>C के लिये = ₹(52000 - 42000) = ₹ 10000<br>D के लिये = ₹(40000 - 31000) = ₹ 9000<br>E के लिये = ₹(31700 - 25000) = ₹ 6700<br>A और B को अपने वेतन के अलावा ₹10000 से अधिक की आय प्राप्त हुई।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The following table shows the daily earnings of 45 skilled workers:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415729100.png\" alt=\"rId8\" width=\"707\" height=\"44\"> <br>How many workers earn less than ₹1,100 in a day ?</p>",
                    question_hi: "<p>6. निम्नलिखित तालिका 45 कुशल श्रमिकों की दैनिक आय को दर्शाती है | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415729308.png\" alt=\"rId9\" width=\"719\" height=\"58\"> <br>कितने श्रमिक एक दिन में 1,100 रुपये से कम कमाते हैं ?</p>",
                    options_en: ["<p>43</p>", "<p>39</p>", 
                                "<p>29</p>", "<p>10</p>"],
                    options_hi: ["<p>43</p>", "<p>39</p>",
                                "<p>29</p>", "<p>10</p>"],
                    solution_en: "<p>6.(b) <br>Workers earning less than ₹ 1,100 = 4 + 15 + 10+ 10 = 39</p>",
                    solution_hi: "<p>6.(b) ₹ 1,100 से कम आय वाले श्रमिक = 4 + 15 + 10+ 10 = 39</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The number of students enrolled in different streams in a college is shown in the following table: <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415729457.png\" alt=\"rId10\" width=\"539\" height=\"59\"> <br>The ratio of total number of boys to that of girls in the college is:</p>",
                    question_hi: "<p>7. निम्नलिखित तालिका में एक कॉलेज के विभिन्न विषयों में नामांकित छात्रों की संख्या दी गयी है : <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415729596.png\" alt=\"rId11\" width=\"594\" height=\"75\"> <br>इस कॉलेज में लड़कों की कुल संख्या तथा लड़कियों की कुल संख्या के बीच क्या अनुपात है ?</p>",
                    options_en: ["<p>13 : 12</p>", "<p>23 : 27</p>", 
                                "<p>1 : 1</p>", "<p>27 : 23</p>"],
                    options_hi: ["<p>13 : 12</p>", "<p>23 : 27</p>",
                                "<p>1 : 1</p>", "<p>27 : 23</p>"],
                    solution_en: "<p>7.(d) <br>Total number of boys in college = 18 + 47 + 40 + 30 = 135<br>Total number of girls in college = 35 + 25 + 45 + 10 = 115<br>Required ratio = 135 : 115 = 27 : 23</p>",
                    solution_hi: "<p>7.(d) कॉलेज में लड़कों की कुल संख्या = 18 + 47 + 40 + 30 = 135<br>कॉलेज में लड़कियों की कुल संख्या = 35 + 25 + 45 + 10 = 115<br>आवश्यक अनुपात = 135 : 115 = 27 : 23</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. During a medical checkup, the height of 40 students in a class were recorded as shown in the following table.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415729739.png\" alt=\"rId12\" width=\"286\" height=\"161\"> <br>How many students have a height of 165 cm or more ?</p>",
                    question_hi: "<p>8. एक चिकित्सा जाँच के दौरान, एक कक्षा के 40 छात्रों की लंबाई दर्ज की गयी जिसे निम्नलिखित तालिका में प्रस्तुत किया गया है |<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415729870.png\" alt=\"rId13\" width=\"236\" height=\"186\"> <br>कितने छात्रों की लंबाई 165 सेमी या अधिक है ?</p>",
                    options_en: ["<p>10</p>", "<p>15</p>", 
                                "<p>16</p>", "<p>25</p>"],
                    options_hi: ["<p>10</p>", "<p>15</p>",
                                "<p>16</p>", "<p>25</p>"],
                    solution_en: "<p>8.(b) <br>Students with height 165 cm or more = 40 - 25 = 15 cm</p>",
                    solution_hi: "<p>8.(b) 165 cm या अधिक ऊंचाई वाले छात्र = 40 - 25 = 15 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The following table gives the details of five commodities A, B, C, D and E with quantity required and their cost for a family in a month. Study the table and answer the questions that follow.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730056.png\" alt=\"rId14\" width=\"457\" height=\"145\"> <br>The percentage of increase (per kg) in the rate of commodity D from 2016 to 2019 is:</p>",
                    question_hi: "<p>9. निम्नलिखित तालिका पाँच वस्तुओं A, B, C, D तथा E के बारे में बताती है कि एक महीने में एक परिवार को उनकी कितनी मात्रा की आवश्यकता है तथा उनकी लागत कितनी है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्नों के उत्तर दें | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730217.png\" alt=\"rId15\" width=\"379\" height=\"136\"> <br>The 2016 से 2019 तक वस्तु D की दर में कितने प्रतिशत की वृद्धि हुई है ?</p>",
                    options_en: ["<p>38.24%</p>", "<p>22.17%</p>", 
                                "<p>13.33%</p>", "<p>5%</p>"],
                    options_hi: ["<p>38.24%</p>", "<p>22.17%</p>",
                                "<p>13.33%</p>", "<p>5%</p>"],
                    solution_en: "<p>9.(c)&nbsp;Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>85</mn><mo>-</mo><mn>75</mn></mrow><mn>75</mn></mfrac></math> &times; 100 = 13.33%</p>",
                    solution_hi: "<p>9.(c) आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>85</mn><mo>-</mo><mn>75</mn></mrow><mn>75</mn></mfrac></math> &times; 100 = 13.33%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. The following table gives the details of five commodities A,B,C,D and E with quantity required and their cost for a family in a month. Study the table and answer the questions that follow.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730056.png\" alt=\"rId14\" width=\"454\" height=\"144\"> <br>The total amount spent on the five commodities by the family in the year 2019 is:</p>",
                    question_hi: "<p>10. निम्नलिखित तालिका पाँच वस्तुओं A, B, C, D तथा E के बारे में बताती है कि एक महीने में एक परिवार को उनकी कितनी मात्रा की आवश्यकता है तथा उनकी लागत कितनी है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्नों के उत्तर दें | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730217.png\" alt=\"rId15\" width=\"382\" height=\"137\"> <br>इस परिवार के द्वारा वर्ष 2019 में पाँचों वस्तुओं पर कुल कितनी राशि खर्च की गयी थी ?</p>",
                    options_en: ["<p><math display=\"inline\"><mi>&#8377;</mi></math>6,856</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>8,122</p>", 
                                "<p><math display=\"inline\"><mi>&#8377;</mi></math>5,400</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>7,248</p>"],
                    options_hi: ["<p><math display=\"inline\"><mi>&#8377;</mi></math>6,856</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>8,122</p>",
                                "<p><math display=\"inline\"><mi>&#8377;</mi></math>5,400</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>7,248</p>"],
                    solution_en: "<p>10.(a) <br>Total amount spent on five commodities in 2019 = 15 &times; 80 + 20 &times; 60 + 12 &times; 40 + 40 &times; 85 + 8 &times; 72 = ₹ 6,856</p>",
                    solution_hi: "<p>10.(a) 2019 में पांच वस्तुओं पर खर्च की गई कुल राशि = 15 &times; 80 + 20 &times; 60 + 12 &times; 40 + 40 &times; 85 + 8 &times; 72 = ₹ 6,856</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The following table gives the details of five commodities A, B, C, D and E with quantity required and their cost for a family in a month. Study the table and answer the questions that follow.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730056.png\" alt=\"rId14\" width=\"442\" height=\"140\"> <br>The ratio of the total amount spent on A and D commodities in the year 2019 is:</p>",
                    question_hi: "<p>11. निम्नलिखित तालिका पाँच वस्तुओं A, B, C, D तथा E के बारे में बताती है कि एक महीने में एक परिवार को उनकी कितनी मात्रा की आवश्यकता है तथा उनकी लागत कितनी है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्नों के उत्तर दें | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730217.png\" alt=\"rId15\" width=\"354\" height=\"127\"> <br>वर्ष 2019 में A और D वस्तुओं पर खर्च की गयी कुल राशि का अनुपात कितना है ?</p>",
                    options_en: ["<p>15 : 17</p>", "<p>6 : 17</p>", 
                                "<p>1 : 1</p>", "<p>3 : 8</p>"],
                    options_hi: ["<p>15 : 17</p>", "<p>6 : 17</p>",
                                "<p>1 : 1</p>", "<p>3 : 8</p>"],
                    solution_en: "<p>11.(b)<br>Required ratio = 15 &times; 80 : 40 &times; 85 = 6 : 17</p>",
                    solution_hi: "<p>11.(b) आवश्यक अनुपात = 15 &times; 80 : 40 &times; 85 = 6 : 17</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The following table gives the details of five commodities A, B, C, D and E with quantity required and their cost for a family in a month. Study the table and answer the questions that follow.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730056.png\" alt=\"rId14\" width=\"442\" height=\"140\"> <br>The amount spent extra on commodities B and C in the year 2019 as compared to that in the year 2016 is:</p>",
                    question_hi: "<p>12. निम्नलिखित तालिका पाँच वस्तुओं A, B, C, D तथा E के बारे में बताती है कि एक महीने में एक परिवार को उनकी कितनी मात्रा की आवश्यकता है तथा उनकी लागत कितनी है | इस तालिका का अध्ययन करें तथा फिर पूछे गए प्रश्नों के उत्तर दें | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730217.png\" alt=\"rId15\" width=\"379\" height=\"136\"> <br>वर्ष 2016 की तुलना में वर्ष 2019 में वस्तु B तथा C पर कितनी अतिरिक्त राशि खर्च की गयी है ?</p>",
                    options_en: ["<p><math display=\"inline\"><mi>&#8377;</mi></math>260</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>248</p>", 
                                "<p><math display=\"inline\"><mi>&#8377;</mi></math>110</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>192</p>"],
                    options_hi: ["<p><math display=\"inline\"><mi>&#8377;</mi></math>260</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>248</p>",
                                "<p><math display=\"inline\"><mi>&#8377;</mi></math>110</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>192</p>"],
                    solution_en: "<p>12.(a) <br>Extra amount spent on B = 20 &times; (60 - 50) = ₹ 200<br>Extra amount spent on C = 12 &times; (40 - 35) = ₹ 60<br>Total extra amount spent by B and C in 2019 over 2016 = ₹ 260&nbsp;</p>",
                    solution_hi: "<p>12.(a) <br>B पर खर्च की गई अतिरिक्त राशि = 20 &times; (60 - 50) = ₹ 200<br>C पर खर्च की गई अतिरिक्त राशि = 12 &times; (40 - 35) = ₹ 60<br>2016 की तुलना में 2019 में B और C द्वारा खर्च की गई कुल अतिरिक्त राशि = ₹ 260</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. In a school, the distribution of teachers is as follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730393.png\" alt=\"rId16\" width=\"261\" height=\"184\"> <br>The total number of teachers of age less than 40 years is</p>",
                    question_hi: "<p>13. एक विद्यालय में, शिक्षकों का वितरण इस प्रकार है : <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730509.png\" alt=\"rId17\" width=\"223\" height=\"184\"> <br>40 वर्ष से कम उम्र के शिक्षकों की कुल संख्या कितनी है ?</p>",
                    options_en: ["<p>39</p>", "<p>10</p>", 
                                "<p>12</p>", "<p>18</p>"],
                    options_hi: ["<p>39</p>", "<p>10</p>",
                                "<p>12</p>", "<p>18</p>"],
                    solution_en: "<p>13.(c) <br>Teachers of age less than 40 years = 2 + 3 + 5 + 2 = 12</p>",
                    solution_hi: "<p>13.(c) 40 वर्ष से कम आयु के शिक्षक = 2 + 3 + 5 + 2 = 12</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Study the given table carefully and answer the questions that follows: <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730609.png\" alt=\"rId18\" width=\"587\" height=\"127\"> <br>The percentage of students who have passed with distinction in the year 2012 is:</p>",
                    question_hi: "<p>14. निम्नलिखित तालिका का ध्यानपूर्वक अध्ययन कीजिए तथा फिर पूछे गए प्रश्नों के उत्तर दीजिए | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730706.png\" alt=\"rId19\" width=\"587\" height=\"134\"> <br>ऐसे छात्रों का प्रतिशत बताएँ जो वर्ष 2012 में डिस्टिंक्शन के साथ सफल हुए हैं ?</p>",
                    options_en: ["<p>27%</p>", "<p>20%</p>", 
                                "<p>25%</p>", "<p>22%</p>"],
                    options_hi: ["<p>27%</p>", "<p>20%</p>",
                                "<p>25%</p>", "<p>22%</p>"],
                    solution_en: "<p>14.(c)&nbsp;% students who have passed with distinction in 2012<br>= <math display=\"inline\"><mfrac><mrow><mn>210</mn></mrow><mrow><mn>840</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>14.(c) 2012 में डिस्टिंक्शन के साथ पास हुए छात्रों का % <br>= <math display=\"inline\"><mfrac><mrow><mn>210</mn></mrow><mrow><mn>840</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. In a particular year, the number of students enrolled in different streams in a college is as follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730786.png\" alt=\"rId20\" width=\"558\" height=\"62\"> <br>The percentage of girl students is:</p>",
                    question_hi: "<p>15. एक विशेष वर्ष में, किसी कॉलेज के विभिन्न विषयों में नामांकित छात्रों की संख्या इस प्रकार है : <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415730950.png\" alt=\"rId21\" width=\"533\" height=\"68\"> <br>छात्राओं का प्रतिशत है :</p>",
                    options_en: ["<p>54%</p>", "<p>46%</p>", 
                                "<p>135%</p>", "<p>50%</p>"],
                    options_hi: ["<p>54%</p>", "<p>46%</p>",
                                "<p>135%</p>", "<p>50%</p>"],
                    solution_en: "<p>15.(a) <br>Total number of boys = 32 + 28 + 42 + 13 = 115<br>Total number of girls = 18 + 45 + 42 + 30 = 135<br>Total number of students = 250<br>% of girls = <math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>250</mn></mrow></mfrac></math> &times; 100 = 54%</p>",
                    solution_hi: "<p>15.(a) लड़कों की कुल संख्या = 32 + 28 + 42 + 13 = 115<br>लड़कियों की कुल संख्या = 18 + 45 + 42 + 30 = 135<br>छात्रों की कुल संख्या = 250<br>लड़कियों का % = <math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>250</mn></mrow></mfrac></math> &times; 100 = 54%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. From the given table, what is the percentage of students scoring 40 or more, but less than 70.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415731126.png\" alt=\"rId22\" width=\"279\" height=\"181\"></p>",
                    question_hi: "<p>16. दी गयी तालिका से, ऐसे छात्रों का प्रतिशत ज्ञात करें जिन्होंने 40 अथवा अधिक लेकिन 70 से कम अंक प्राप्त किया है | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415731255.png\" alt=\"rId23\" width=\"209\" height=\"185\"></p>",
                    options_en: ["<p>8%</p>", "<p>96%</p>", 
                                "<p>56%</p>", "<p>48%</p>"],
                    options_hi: ["<p>8%</p>", "<p>96</p>",
                                "<p>56%</p>", "<p>48%</p>"],
                    solution_en: "<p>16.(d) Total number of students = 50 <br>Students scoring more than 40 and less than 70 = 46 - 22 = 24<br>Required % =<math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 100 = 48%</p>",
                    solution_hi: "<p>16.(d) छात्रों की कुल संख्या = 50 <br>40 से अधिक और 70 से कम स्कोर करने वाले छात्र = 46 - 22 = 24<br>आवश्यक % =<math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 100 = 48%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. The following table shows the age-wise brand ownership of mobile phone handsets. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415731362.png\" alt=\"rId24\" width=\"596\" height=\"118\"> <br>If a total of 5000 &lsquo;C&rsquo; mobile phone sets are sold till date, then how many are more than one year old ?</p>",
                    question_hi: "<p>17. निम्नलिखित तालिका मोबाइल फ़ोन हैंडसेट की निर्माण अवधि के अनुसार स्वामित्व को दर्शाती है | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415731491.png\" alt=\"rId25\" width=\"552\" height=\"129\"> <br>यदि आज की तारीख तक कुल 5000 &lsquo;C&rsquo; मोबाइल फ़ोन सेट बेचे गए हैं, तो उनमें से कितने सेट एक वर्ष से अधिक पुराने हैं ?</p>",
                    options_en: ["<p>4200</p>", "<p>4500</p>", 
                                "<p>4350</p>", "<p>4000</p>"],
                    options_hi: ["<p>4200</p>", "<p>4500</p>",
                                "<p>4350</p>", "<p>4000</p>"],
                    solution_en: "<p>17.(b) <br>Total C mobile phones till date = 5000<br>Mobile phone sold to more than 1 year old = 90% of 5000 = 4500</p>",
                    solution_hi: "<p>17.(b) <br>अब तक के कुल C मोबाइल फोन = 5000<br>1 वर्ष से अधिक पुराने मोबाइल फ़ोन को बेचा गया = 90% of 5000 = 4500</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "misc",
                    question_en: "<p>18. The following table shows the number of employees working in various departments of an organization from 2016 to 2019. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415731601.png\" alt=\"rId26\" width=\"442\" height=\"110\"> <br>In which year were the maximum number of employees working in the organization ?</p>",
                    question_hi: "<p>18. निम्नलिखित तालिका 2016 से 2019 तक एक संगठन के विभिन्न विभागों में कार्य करने वाले कर्मचारियों की संख्या को दर्शाती है | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743415731733.png\" alt=\"rId27\" width=\"416\" height=\"108\"> <br>किस वर्ष संगठन में कार्य करने वाले कर्मचारियों की संख्या अधिकतम रही है ?</p>",
                    options_en: ["<p>2019</p>", "<p>2017</p>", 
                                "<p>2016</p>", "<p>2018</p>"],
                    options_hi: ["<p>2019</p>", "<p>2017</p>",
                                "<p>2016</p>", "<p>2018</p>"],
                    solution_en: "<p>18.(a) <br>Employees working in 2016 = 500 + 130 + 50 + 145 = 1325<br>Employees working in 2017 = 940 + 146 + 60 + 140 = 1286<br>Employees working in 2018 = 1000 + 160 + 70 + 146 = 1376<br>Employees working in 2019 = 1010 + 150 + 75 + 150 = 1385<br>Clearly, in 2019 maximum employees are working in the organization.</p>",
                    solution_hi: "<p>18.(a) <br>2016 में काम कर रहे कर्मचारी = 500 + 130 + 50 + 145 = 1325<br>2017 में काम कर रहे कर्मचारी = 940 + 146 + 60 + 140 = 1286<br>2018 में काम कर रहे कर्मचारी = 1000 + 160 + 70 + 146 = 1376<br>2019 में काम कर रहे कर्मचारी = 1010 + 150 + 75 + 150 = 1385<br>अतः 2019 में सबसे ज्यादा कर्मचारी संगठन में काम कर रहे हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>