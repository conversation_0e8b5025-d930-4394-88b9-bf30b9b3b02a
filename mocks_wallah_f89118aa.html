<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Pradeep sells two fans at ₹480 each and by doing so he gains 20% on one fan and loses 20% on the other. His loss on the whole (in ₹) is:</p>",
                    question_hi: "<p>1. प्रदीप प्रत्येक ₹480 के मूल्य से दो पंखे बेचता है और ऐसा करके वह एक पंखे पर 20% का लाभ कमाता है और दूसरे पर उसे 20% की हानि होती है। कुल मिलाकर उसे कितनी हानि (₹ में) हुई ?</p>",
                    options_en: ["<p>10</p>", "<p>40</p>", 
                                "<p>20</p>", "<p>30</p>"],
                    options_hi: ["<p>10</p>", "<p>40</p>",
                                "<p>20</p>", "<p>30</p>"],
                    solution_en: "<p>1.(b)<br>Ratio&nbsp; &nbsp;- CP : SP <br>1st fan&nbsp; - 5&nbsp; :&nbsp; 6 <strong>) &times; 2</strong><br>2nd fan - 5&nbsp; :&nbsp; 4<strong> ) &times; 3</strong><br>------------------------------<br>Final - (10 + 15) : (12 + 12) = 25 : 24<br>Loss = 25 - 24 = 1 units<br>SP of each fan = 12 units<br>12 units = ₹ 480<br>(loss) 1 units = ₹ 40</p>",
                    solution_hi: "<p>1.(b)<br>अनुपात&nbsp; &nbsp; &nbsp;- क्रय मूल्य : विक्रय मूल्य <br>पहला पंखा -&nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 6<strong> ) &times; 2</strong><br>दूसरा पंखा -&nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 4<strong> ) &times; 3</strong><br>---------------------------------------<br>अंतिम - (10 + 15) : (12 + 12) = 25 : 24<br>हानि = 25 - 24 = 1 इकाई<br>प्रत्येक पंखे का विक्रय मूल्य = 12 इकाई<br>12 इकाई = ₹ 480<br>(हानि) 1 इकाई = ₹ 40</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A sells an object to B at 10% profit, B sells it to C at 60% profit and C sells it to D at 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% profit. If D paid ₹59.40, then at what price did A buy it ?</p>",
                    question_hi: "<p>2. A, B को एक वस्तु 10% लाभ पर बेचता है, B उसी वस्तु को C को 60% लाभ पर बेचता है और C उसी वस्तु को D को 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% लाभ पर बेचता है। यदि D ने उस वस्तु के लिए ₹59.40 का भुगतान किया, तो A ने उसे किस मूल्य पर खरीदा ?</p>",
                    options_en: [" ₹30 ", " ₹32", 
                                " ₹34 ", " ₹28"],
                    options_hi: [" ₹30 ", " ₹32",
                                " ₹34 ", " ₹28"],
                    solution_en: "<p>2.(a)<br>Let &lsquo;A&rsquo; buy the object at Rs. x<br>According to the question,<br><math display=\"inline\"><mi>x</mi></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac><mo>&#215;</mo><mfrac><mn>8</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>9</mn><mn>8</mn></mfrac></math> = 59.40<br><math display=\"inline\"><mi>x</mi></math> = 59.40 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>11</mn></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>&#215;</mo><mfrac><mn>8</mn><mn>9</mn></mfrac></math> = रु. 30</p>",
                    solution_hi: "<p>2.(a)<br>मान लीजिए \'A\' वस्तु को <math display=\"inline\"><mi>x</mi></math> रुपये में खरीदता है। <br>प्रश्न के अनुसार,<br><math display=\"inline\"><mi>x</mi></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac><mo>&#215;</mo><mfrac><mn>8</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>9</mn><mn>8</mn></mfrac></math> = 59.40<br><math display=\"inline\"><mi>x</mi></math> = 59.40 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>11</mn></mfrac><mo>&#215;</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>&#215;</mo><mfrac><mn>8</mn><mn>9</mn></mfrac></math> = रु. 30</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A trader buys 500 kg cotton for ₹8,000. If 10% of this cotton is spoiled due to rain, at what rate (₹/kg) should he sell the rest to earn a 20% profit ?</p>",
                    question_hi: "<p>3. एक व्यापारी ₹8,000 में 500 kg कपास खरीदता है। यदि बारिश के कारण इस कपास का 10% खराब हो जाता है, तो 20% लाभ अर्जित करने के लिए उसे शेष कपास को किस दर (₹/kg) पर बेचना चाहिए ?</p>",
                    options_en: ["<p>21<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>23<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p>22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>25<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>21<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>23<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>25<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>3.(a)<br>According to the question,<br>10% of cotton spoiled due to rain so,<br>Remaining cotton to sell = 500 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 450 kg<br>For profit of 20%<br>Selling price of cotton = 8000 &times; 120% = Rs. 9600<br>Selling price per kg = <math display=\"inline\"><mfrac><mrow><mn>9600</mn></mrow><mrow><mn>450</mn></mrow></mfrac></math> = Rs. 21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> /kg</p>",
                    solution_hi: "<p>3.(a)<br>प्रश्न के अनुसार,<br>बारिश के कारण 10 फीसदी कपास खराब हो गई तो,<br>बेचने के लिए शेष कपास = 500 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 450 किग्रा<br>20% के लाभ के लिए<br>कपास का विक्रय मूल्य = 8000 &times; 120% = Rs. 9600<br>प्रति किलो विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>9600</mn></mrow><mrow><mn>450</mn></mrow></mfrac></math> = Rs. 21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>/किग्रा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A shopkeeper allows a discount of 22% to his customers and still gains 36%. Find the marked price (in₹) of an article which costs ₹1,092 to the shopkeeper.</p>",
                    question_hi: "<p>4. एक दुकानदार अपने ग्राहकों को 22% की छूट देता है और फिर भी 36% का लाभ अर्जित करता है। यदि दुकानदार के लिए वस्तु की लागत ₹1,092 हे, तो वस्तु का अंकित मूल्य (₹ में) ज्ञात कीजिए |</p>",
                    options_en: ["<p>1,792</p>", "<p>2,024</p>", 
                                "<p>1,904</p>", "<p>1,872</p>"],
                    options_hi: ["<p>1,792</p>", "<p>2,024</p>",
                                "<p>1,904</p>", "<p>1,872</p>"],
                    solution_en: "<p>4.(c) <br>Marked price = 1092 &times; <math display=\"inline\"><mfrac><mrow><mn>136</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math> = ₹1904</p>",
                    solution_hi: "<p>4.(c) <br>अंकित मूल्य = 1092 &times; <math display=\"inline\"><mfrac><mrow><mn>136</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math> = ₹1904</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A dishonest dealer professes to sell his goods at the cost price, but he uses a false weight of 850 grams per 1 kg weight. Find the percentage of his gain. (Correct up to two decimal places)</p>",
                    question_hi: "<p>5. एक बेईमान व्यापारी अपने माल को क्रय मूल्य पर बेचने का दावा करता है, परंतु वह 1 kg के स्थान पर 850 ग्राम के खोटे बाट का उपयोग करता है। उसके लाभ का प्रतिशत ज्ञात कीजिए। (दशमलव के दो स्थानों तक सही)</p>",
                    options_en: ["<p>17.56%</p>", "<p>17.65%</p>", 
                                "<p>17.45%</p>", "<p>17.54%</p>"],
                    options_hi: ["<p>17.56%</p>", "<p>17.65%</p>",
                                "<p>17.45%</p>", "<p>17.54%</p>"],
                    solution_en: "<p>5.(b)<br>Gain % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1000</mn><mo>-</mo><mn>850</mn></mrow><mn>850</mn></mfrac></math> &times; 100<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>17</mn></mfrac></math> = 17.65%</p>",
                    solution_hi: "<p>5.(b)<br>लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1000</mn><mo>-</mo><mn>850</mn></mrow><mn>850</mn></mfrac></math> &times; 100<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>17</mn></mfrac></math> = 17.65%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Ramesh sells an article for ₹34,440, after allowing an 18% discount on the marked price and still manages to gain 40% profit. If Ramesh sold the article at a 12% discount on the marked price, then what will be his profit, correct to two places of decimals ?</p>",
                    question_hi: "<p>6. अंकित मूल्य पर 18% की छूट देने के बाद रमेश एक वस्तु को ₹34,440 में बेचता है और फिर भी 40% लाभ कमाता है। यदि रमेश अंकित मूल्य पर 12% की छूट के साथ वस्तु बेचता है, तो दशमलव के दो स्थानों तक उसका लाभ कितना होगा ?</p>",
                    options_en: ["<p>50.42%</p>", "<p>48.24%</p>", 
                                "<p>50.24%</p>", "<p>48.42%</p>"],
                    options_hi: ["<p>50.42%</p>", "<p>48.24%</p>",
                                "<p>50.24%</p>", "<p>48.42%</p>"],
                    solution_en: "<p>6.(c)<br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>82</mn></mfrac><mo>=</mo><mfrac><mn>70</mn><mn>41</mn></mfrac></math><br>New SP = 70 &times; <math display=\"inline\"><mfrac><mrow><mn>88</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 61.6 units<br>profit% = <math display=\"inline\"><mfrac><mrow><mn>61</mn><mo>.</mo><mn>6</mn><mo>-</mo><mn>41</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math> &times; 100 = 50.24%</p>",
                    solution_hi: "<p>6.(c)<br>प्रश्न के अनुसार,<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>82</mn></mfrac><mo>=</mo><mfrac><mn>70</mn><mn>41</mn></mfrac></math><br>नया विक्रय मूल्य = 70 &times; <math display=\"inline\"><mfrac><mrow><mn>88</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 61.6 इकाई<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>61</mn><mo>.</mo><mn>6</mn><mo>-</mo><mn>41</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math> &times; 100 = 50.24%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. By selling 60 pens, a man gains an amount equal to the selling price of 12 pens. What is his gain percentage ?</p>",
                    question_hi: "<p>7. 60 कलमों को बेचकर, एक आदमी को 12 कलमों के विक्रय मूल्य के बराबर लाभ प्राप्त होता है। उसका लाभ प्रतिशत क्या है ?</p>",
                    options_en: ["<p>25%</p>", "<p>22%</p>", 
                                "<p>20%</p>", "<p>27%</p>"],
                    options_hi: ["<p>25%</p>", "<p>22%</p>",
                                "<p>20%</p>", "<p>27%</p>"],
                    solution_en: "<p>7.(a) <br>Profit = S.P. - C.P.<br>12 S.P. = 60 S.P. - 60 C.P.<br>48 S.P. = 60 C.P.<br><math display=\"inline\"><mfrac><mrow><mi>S</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow><mrow><mi>C</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>profit% = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>7.(a) <br>लाभ = विक्रय मूल्य(SP) - क्रय मूल्य(CP)<br>12SP = 60SP - 60CP<br>48SP = 60CP<br><math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. By selling 22 items, Rohit gains the selling price of 6 items. His gain percent is:</p>",
                    question_hi: "<p>8. 22 वस्तुओं को बेचकर, रोहित को 6 वस्तुओं का विक्रय मूल्य प्राप्त होता है। उसका लाभ प्रतिशत कितना है ?</p>",
                    options_en: ["<p>37 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>37 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", 
                                "<p>33 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>33 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>37 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>37 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                                "<p>33 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>", "<p>33 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>8.(a)<br>According to the question,<br>6 &times; S.P. = 22 &times; S.P. - 22 &times; C.P.<br>22 &times; C.P. = 16 &times; S.P.<br>C.P. : S.P. = 8 : 11<br>profit% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = 37<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    solution_hi: "<p>8.(a)<br>प्रश्न के अनुसार,<br>6 &times; विक्रय मूल्य = 22 &times; विक्रय मूल्य - 22 &times; क्रय मूल्य<br>22 &times; क्रय मूल्य = 16 &times; विक्रय मूल्य <br>क्रय मूल्य : विक्रय मूल्य = 8 : 11<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = 37<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. A shopkeeper sells an item at 20% discount on the marked price and earns a profit of 90%. If he sells the same item at 40% discount, then his new profit percentage will be:</p>",
                    question_hi: "<p>9. एक दुकानदार अंकित मूल्य पर 20% छूट पर एक वस्तु बेचता है और 90% का लाभ अर्जित करता है। यदि वह उसी वस्तु को 40% छूट पर बेचता है, तो उसका नया लाभ प्रतिशत क्या होगा ?</p>",
                    options_en: ["<p>48.2%</p>", "<p>45.8%</p>", 
                                "<p>42.5%</p>", "<p>41.8%</p>"],
                    options_hi: ["<p>48.2%</p>", "<p>45.8%</p>",
                                "<p>42.5%</p>", "<p>41.8%</p>"],
                    solution_en: "<p>9.(c) <br><math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>-</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>c</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi><mo>&#160;</mo><mo>%</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>190</mn><mn>80</mn></mfrac></math><br>Now, after selling the same item at 40% discount<br>New selling price = 190 &times; 60% = 114<br>So, new profit % = <math display=\"inline\"><mfrac><mrow><mn>114</mn><mi>&#160;</mi><mo>-</mo><mn>80</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>4</mn></mfrac></math> &times; 5 = 42.5%</p>",
                    solution_hi: "<p>9.(c) <br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mi>&#2354;&#2366;&#2349;</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>-</mo><mi>&#2331;&#2370;&#2335;</mi><mo>%</mo></mrow></mfrac></math><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>190</mn><mn>80</mn></mfrac></math><br>अब, उसी वस्तु को 40% छूट पर बेचने के बाद<br>नया विक्रय मूल्य = 190 &times; 60% = 114<br>तो, नया लाभ % = <math display=\"inline\"><mfrac><mrow><mn>114</mn><mi>&#160;</mi><mo>-</mo><mn>80</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>4</mn></mfrac></math> &times; 5 = 42.5%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A vendor sells his goods using weights 19% less than true weights and makes a profit of 35%. His net gain percentage (rounded off to 2 decimal places) is:</p>",
                    question_hi: "<p>10. एक विक्रेता वास्तविक भार वाले बाट से 19% कम भार के बाट का उपयोग करके अपना माल बेचता है और 35% का लाभ अर्जित करता है। उसका निवल लाभ प्रतिशत (दशमलव के 2 स्थानों तक सन्निकटित) क्या है ?</p>",
                    options_en: ["<p>81.33%</p>", "<p>66.33%</p>", 
                                "<p>81.67%</p>", "<p>66.67%</p>"],
                    options_hi: ["<p>81.33%</p>", "<p>66.33%</p>",
                                "<p>81.67%</p>", "<p>66.67%</p>"],
                    solution_en: "<p>10.(d) 35 % = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math> Initial&nbsp; :&nbsp; Final<br>By weight <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 81&nbsp; &nbsp;:&nbsp; 100<br>By price&nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;20&nbsp; &nbsp;:&nbsp; &nbsp;27<br>----------------------------------------<br>Final&nbsp; &nbsp; &nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 1620&nbsp; :&nbsp; 2700<br>Profit% = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>2700</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1620</mn><mo>)</mo></mrow><mrow><mn>1620</mn></mrow></mfrac></math> &times; 100<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1080</mn><mn>1620</mn></mfrac></math> &times; 100 = 66.67%</p>",
                    solution_hi: "<p>10.(d) 35 % = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br>अनुपात&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math> आरंभिक : अंतिम<br>वज़न के हिसाब से&nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 81&nbsp; &nbsp; :&nbsp; &nbsp;100<br>कीमत के हिसाब से <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;27<br>----------------------------------------------------<br>अंतिम&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 1620&nbsp; :&nbsp; 2700<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>2700</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1620</mn><mo>)</mo></mrow><mrow><mn>1620</mn></mrow></mfrac></math> &times; 100<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1080</mn><mn>1620</mn></mfrac></math> &times; 100 = 66.67%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Shopkeeper A marks up his price for an item at 25% and offers a discount of 15%. The same item is marked at 20% by another shopkeeper B and sold at a discount of 12%. Who gets a better deal in terms of % profit and by what profit percentage he sells that item ?</p>",
                    question_hi: "<p>11. दुकानदार A एक वस्तु का मूल्य 25% अधिक अंकित करता है और 15% की छूट देता है। उसी वस्तु पर एक दूसरे दुकानदार B द्वारा 20% अधिक मूल्य अंकित किया जाता है और 12% की छूट पर बेचा जाता है। % लाभ के मामले में किसे एक बेहतर डील मिलती है और वह उस वस्तु को कितने प्रतिशत लाभ में बेचता है ?</p>",
                    options_en: ["<p>A by 0.55%</p>", "<p>B by 0.65%</p>", 
                                "<p>B by 5.6%</p>", "<p>A by 6.25%</p>"],
                    options_hi: ["<p>A द्वारा 0.55%</p>", "<p>B द्वारा 0.65%</p>",
                                "<p>B द्वारा 5.6%</p>", "<p>A द्वारा 6.25%</p>"],
                    solution_en: "<p>11.(d)<br><strong>For shopkeeper A :-</strong> <br>C.P. : M.P. = 4 : 5<br>After giving 15% discount<br>S.P. = 5 - 5 &times; 15%<br>= 5 - 0.75 = 4.25<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>25</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>25</mn></mrow><mn>4</mn></mfrac></math> <strong id=\"docs-internal-guid-ac3c418d-7fff-a8d6-3ec1-39ca1b8e78af\">&nbsp;</strong>&times; 100<strong id=\"docs-internal-guid-ac3c418d-7fff-a8d6-3ec1-39ca1b8e78af\"> </strong>= 6.25%<br><strong>For shopkeeper B :-</strong><br>C.P. : M.P. = 5 : 6<br>After giving 12% discount<br>S.P. = 6 - 6 &times; 12%<br>= 6 - 0.72 = 5.28<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>.</mo><mn>28</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>28</mn></mrow><mn>5</mn></mfrac></math> &times; 100 = 5.60%<br>Clearly, A gets the better deal with 6.25% profit.</p>",
                    solution_hi: "<p>11.(d)<br><strong>दुकानदार A के लिए :-</strong> <br>क्रय मूल्य : अंकित मूल्य = 4 : 5<br>15% छूट देने के बाद<br>विक्रय मूल्य = 5 - 5 &times; 15%<br>= 5 - 0.75 = 4.25<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>25</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>25</mn></mrow><mn>4</mn></mfrac></math> <strong id=\"docs-internal-guid-ac3c418d-7fff-a8d6-3ec1-39ca1b8e78af\">&nbsp;</strong>&times; 100<strong id=\"docs-internal-guid-ac3c418d-7fff-a8d6-3ec1-39ca1b8e78af\"> </strong>= 6.25%<br><strong>दुकानदार B के लिए:-</strong><br>क्रय मूल्य : अंकित मूल्य = 5 : 6<br>12% छूट देने के बाद<br>विक्रय मूल्य = 6 - 6 &times; 12%<br>= 6 - 0.72 = 5.28<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>.</mo><mn>28</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>28</mn></mrow><mn>5</mn></mfrac></math> &times; 100 = 5.60%<br>स्पष्टतः, A को 6.25% लाभ के साथ बेहतर सौदा मिलता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. A vendor buys 20 dozen bananas for ₹1,200 and sells 5 dozen for ₹350, then his gain percentage is:</p>",
                    question_hi: "<p>12. एक विक्रेता 20 दर्जन केले ₹1,200 में खरीदता है और 5 दर्जन केले ₹350 में बेचता है, तो उसका लाभ प्रतिशत कितना है ?</p>",
                    options_en: ["<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>", "<p>16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>", 
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>21<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>", "<p>16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>",
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>21<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>12.(b) <br>Cost price of 5 dozen bananas = <math display=\"inline\"><mfrac><mrow><mn>1200</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 5 = ₹300<br>Selling price of 5 dozen bananas = ₹350<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>300</mn></mrow></mfrac></math> &times; 100 = 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> %</p>",
                    solution_hi: "<p>12.(b) <br>5 दर्जन केले का लागत मूल्य = <math display=\"inline\"><mfrac><mrow><mn>1200</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 5 = ₹300<br>5 दर्जन केलों का विक्रय मूल्य = ₹350<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>300</mn></mrow></mfrac></math> &times; 100 = 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A merchant sells 195 kg of wheat for ₹10,260 at a profit of ₹4.50 per kg of wheat. What is the cost price (in ₹, to the nearest integer) of 15 kg of wheat ?</p>",
                    question_hi: "<p>13. एक व्यापारी 195 kg गेहूं ₹4.50 प्रति किलोग्राम गेहूं के लाभ पर ₹10,260 में बेचता है। 15 kg गेहूं का क्रय मूल्य (₹ में, निकटतम पूर्णांक तक) क्या है ?</p>",
                    options_en: ["<p>682</p>", "<p>750</p>", 
                                "<p>722</p>", "<p>595</p>"],
                    options_hi: ["<p>682</p>", "<p>750</p>",
                                "<p>722</p>", "<p>595</p>"],
                    solution_en: "<p>13.(c)<br>SP of 1 kg wheat = <math display=\"inline\"><mfrac><mrow><mn>10260</mn></mrow><mrow><mn>195</mn></mrow></mfrac></math> = ₹52.6<br>Then, CP of 1 kg wheat = 52.6 - 4.5 = ₹48.1<br>So, CP of 15 kg wheat = 15 &times; 48.1 = 721.5 <math display=\"inline\"><mo>&#8776;</mo></math> ₹722</p>",
                    solution_hi: "<p>13.(c)<br>1 किलो गेहूं का विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>10260</mn></mrow><mrow><mn>195</mn></mrow></mfrac></math> = ₹52.6<br>फिर, 1 किलो गेहूं का क्रय मूल्य = 52.6 - 4.5 = ₹48.1<br>तो, 15 किलो गेहूं का क्रय मूल्य= 15 &times; 48.1 = 721.5 <math display=\"inline\"><mo>&#8776;</mo></math> ₹722</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A costs thrice as much as B. A is sold at a loss of 10%, and B is sold at <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> of its own price. If the selling price of A is ₹5,800 more than the selling price of B, then the cost price of A is:</p>",
                    question_hi: "<p>14. A का मूल्य B से तीन गुना है। A को 10% की हानि पर बेचा जाता है और B को इसके मूल्य के <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> भाग पर बेचा जाता है। यदि A का विक्रय मूल्य B के विक्रय मूल्य से ₹5,800 अधिक है, तो A का क्रय मूल्य कितना है ?</p>",
                    options_en: ["<p>₹12,000</p>", "<p>₹9,600</p>", 
                                "<p>₹15,000</p>", "<p>₹4,000</p>"],
                    options_hi: ["<p>₹12,000</p>", "<p>₹9,600</p>",
                                "<p>₹15,000</p>", "<p>₹4,000</p>"],
                    solution_en: "<p>14.(a)<br>Let the CP of A and B be 300 and 100 respectively<br>SP of A = 300 &times; 90% = 270<br>SP of B = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 125<br>According to the question,<br>(270-125) = 145 unit = ₹5800<br>Then, 300 unit = <math display=\"inline\"><mfrac><mrow><mn>5800</mn></mrow><mrow><mn>145</mn></mrow></mfrac></math> &times; 300 = ₹12,000<br>Then, CP of A = ₹12000</p>",
                    solution_hi: "<p>14.(a)<br>माना A और B का क्रय मूल्य क्रमशः 300 और 100 है<br>A का विक्रय मूल्य = 300 &times; 90% = 270<br>B का विक्रय मूल्य = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 125<br>प्रश्न के अनुसार,<br>(270-125) = 145 इकाई = ₹5800<br>फिर, 300 इकाई = <math display=\"inline\"><mfrac><mrow><mn>5800</mn></mrow><mrow><mn>145</mn></mrow></mfrac></math> &times; 300 = ₹12,000<br>फिर, A क्रय मूल्य = ₹12000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. By selling an article for ₹2,160, Prashant allows a 20% discount and earns 28% profit. If the article is sold without any discount, the profit will be:</p>",
                    question_hi: "<p>15. एक वस्तु पर 20% की छूट देने के बाद प्रशांत उसे ₹2,160 में बेचता है और 28% का लाभ कमाता है। यदि वस्तु को बिना किसी छूट के बेचा जाए, तो लाभ कितना होगा ?</p>",
                    options_en: ["<p>50%</p>", "<p>55%</p>", 
                                "<p>65%</p>", "<p>60%</p>"],
                    options_hi: ["<p>50%</p>", "<p>55%</p>",
                                "<p>65%</p>", "<p>60%</p>"],
                    solution_en: "<p>15.(d)<br>ATQ,<br><math display=\"inline\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mi>D</mi><mo>)</mo><mo>%</mo></mrow><mrow><mn>100</mn><mo>+</mo><mi>P</mi><mo>)</mo><mo>%</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>128</mn></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>8</mn></mfrac></math><br>Since, there is no discount given , then MP = SP <br>Hence, profit% = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>-</mo><mn>5</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; 100 = 60%</p>",
                    solution_hi: "<p>15.(d)<br>प्रश्न के अनुसार,<br><math style=\"font-family:\'Times New Roman\'\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mi>&#2331;&#2370;&#2335;</mi><mo>)</mo><mo>%</mo></mrow><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mo>&#160;</mo><mi>&#2354;&#2366;&#2349;</mi><mo>)</mo><mo>%</mo></mrow></mfrac></math><br><math style=\"font-family:\'Times New Roman\'\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>128</mn></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>8</mn></mfrac></math><br>चूँकि कोई छूट नहीं दी गई है, तो अंकित मूल्य = विक्रय मूल्य <br>अतः लाभ% = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>-</mo><mn>5</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; 100 = 60%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>