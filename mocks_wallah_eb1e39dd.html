<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: " <p>1.  </span><span style=\"font-family:Cambria Math\">Choose the word that means the same as the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Magnanimity</span></p>",
                    question_hi: " <p>1.  </span><span style=\"font-family:Cambria Math\">Choose the word that means the same as the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Magnanimity</span></p>",
                    options_en: [" <p>  Virulence</span></p>", " <p>  Invidiousness</span></p>", 
                                " <p>  Volatile</span></p>", " <p> Altruism</span></p>"],
                    options_hi: [" <p>  Virulence</span></p>", " <p>  Invidiousness</span></p>",
                                " <p>  Volatile</span></p>", " <p> Altruism</span></p>"],
                    solution_en: " <p>1.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Magnanimity-</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">the quality of being magnanimous altruism, to disdain meanness and pettines. </span></p> <p><span style=\"font-family:Cambria Math\">Virulence- the severity or harmfulness of a disease or </span><span style=\"font-family:Cambria Math\">poison</span><span style=\"font-family:Cambria Math\">.</span></p> <p><span style=\"font-family:Cambria Math\">Invidiousness- the quality of being likely to cause unhappiness or being unpleasant</span></p> <p><span style=\"font-family:Cambria Math\">Volatile- that can change s</span><span style=\"font-family:Cambria Math\">uddenly and unexpectedly  </span></p>",
                    solution_hi: " <p>1.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Magnanimity (</span><span style=\"font-family:Cambria Math\">उदारता</span><span style=\"font-family:Cambria Math\">)-</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">the quality of being magnanimous altruism,to disdain meanness and pettiness.</span></p> <p><span style=\"font-family:Cambria Math\">Virulence (</span><span style=\"font-family:Cambria Math\">द्वेष</span><span style=\"font-family:Cambria Math\">)</span><span style=\"font-family:Cambria Math\"> - the severity or harmfulness of a disease or </span><span style=\"font-family:Cambria Math\">poison</span><span style=\"font-family:Cambria Math\">.</span></p> <p><span style=\"font-family:Cambria Math\">Invidiousness (</span><span style=\"font-family:Cambria Math\">द्वेषपूर्णता</span><span style=\"font-family:Cambria Math\">)- the quality of being </span><span style=\"font-family:Cambria Math\">likely to cause unhappiness or being unpleasant</span></p> <p><span style=\"font-family:Cambria Math\">Volatile (</span><span style=\"font-family:Cambria Math\">परिवर्तनशील</span><span style=\"font-family:Cambria Math\">)- that can change suddenly and unexpectedly</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: " <p>2.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">In the following question, out of the given four alternatives, select the alternative which best expresses the meaning of the Idiom/Phrase. </span></p> <p><span style=\"font-family:Cambria Math\">Body and soul</span></p>",
                    question_hi: " <p>2.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">In the following question, out of the given four alternatives, select the alternative which best expresses the meaning of the Idiom/Phrase. </span></p> <p><span style=\"font-family:Cambria Math\">Body and soul</span></p>",
                    options_en: [" <p>  </span><span style=\"font-family:Cambria Math\">Life and death</span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">To solve a difficulty</span></p>", 
                                " <p>  </span><span style=\"font-family:Cambria Math\">Happiness and sorrow</span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">Wholly and entirely</span></p>"],
                    options_hi: [" <p>  </span><span style=\"font-family:Cambria Math\">Life and death</span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">To solve a difficulty</span></p>",
                                " <p>  </span><span style=\"font-family:Cambria Math\">Happiness and sorrow</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">Wholly and e</span><span style=\"font-family:Cambria Math\">ntirely</span></p>"],
                    solution_en: " <p>2.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Body and soul - Wholly and entirely</span></p> <p><span style=\"font-family:Cambria Math\">E.g.- </span><span style=\"font-family:Cambria Math\">Virat kohli worked body and soul to make this day a success.</span></p>",
                    solution_hi: " <p>2.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Body and soul - Wholly and entirely /</span><span style=\"font-family:Cambria Math\">पूरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तरह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">उदहारण</span><span style=\"font-family:Cambria Math\"> - </span><span style=\"font-family:Cambria Math\">Virat kohli worked body and soul to make this day a success.</span></p> <p><span style=\"font-family:Cambria Math\">विराट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कोहली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">इस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दिन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सफल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बनाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तन</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Cambria Math\">मन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मेहनत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Choose the option that is the correct passive form of the sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Ravi gave you the information?</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">Choose the option that is the correct passive form of the sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ravi g</span><span style=\"font-family: Cambria Math;\">ave you the information?</span></p>\n",
                    options_en: ["<p>Was the information given to you by Ravi?</p>\n", "<p>Have the information been given to you?</p>\n", 
                                "<p>Has the information given to you?</p>\n", "<p>You have been given the information?</p>\n"],
                    options_hi: ["<p>Was the information given to you by Ravi?</p>\n", "<p>Have the information been given to you?</p>\n",
                                "<p>Has the information given to you?</p>\n", "<p>You have been given the information?</p>\n"],
                    solution_en: "<p>3.(a)<span style=\"font-family: Cambria Math;\"> Was the information given to you by R</span><span style=\"font-family: Cambria Math;\">avi?(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">Have</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>the information been given to you?(Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) <span style=\"text-decoration: underline;\">Has</span> the information given to you?(Incorrect Verb)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) You have been given the information?(Incorrect Sentence Structure)</span></p>\n",
                    solution_hi: "<p>3.(a)<span style=\"font-family: Cambria Math;\"> Was the information given to you by Ravi?(Co</span><span style=\"font-family: Cambria Math;\">rrect)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(b) </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">Have</span></span><span style=\"font-weight: 400;\"> the information been given to you?(&#2327;&#2354;&#2340;&nbsp; Verb)</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(c) <span style=\"text-decoration: underline;\">Has</span> the information given to you?(&#2327;&#2354;&#2340; Verb)</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) You have been given the information?(&#2327;&#2354;&#2340;&nbsp; Sentence Structure)</span></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Choose the word that means the same as the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Quandary</span></p>\n",
                    question_hi: " <p>4. </span><span style=\"font-family:Cambria Math\">Choose the word that means the same as the given word.</span></p> <p><span style=\"font-family:Cambria Math\"> Quandary</span></p>",
                    options_en: ["<p>Impasse</p>\n", "<p>Cinch</p>\n", 
                                "<p>Clique</p>\n", "<p>Breeze</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: [" <p> Impasse  </span></p>", " <p> Cinch</span></p>",
                                " <p> Clique</span></p>", " <p> Breeze</span></p>"],
                    solution_en: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> Quandary </span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">a state of perplexity or uncertainty over what to d</span><span style=\"font-family: Cambria Math;\">o in a difficult situation.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Cinch -an extremely easy task.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Clique -a small group of people with the same interests who do not want others to join their group</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Breeze</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Cambria Math;\">a light wind</span></p>\n",
                    solution_hi: " <p>4.(a)</span></p> <p><span style=\"font-family:Cambria Math\"> Quandary -</span><span style=\"font-family:Cambria Math\">(</span><span style=\"font-family:Cambria Math\">व्याकुलता</span><span style=\"font-family:Cambria Math\">) - </span><span style=\"font-family:Cambria Math\">a state of perplexity or uncertainty over what to do in a difficult situation.</span></p> <p><span style=\"font-family:Cambria Math\"> Cinch -(</span><span style=\"font-family:Cambria Math\">आसान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">काम</span><span style=\"font-family:Cambria Math\">) - an extremely easy task.</span></p> <p><span style=\"font-family:Cambria Math\"> Cliqu</span><span style=\"font-family:Cambria Math\">e -(</span><span style=\"font-family:Cambria Math\">गुट</span><span style=\"font-family:Cambria Math\">) - a small group of people with the same interests who do not want others to join their group</span></p> <p><span style=\"font-family:Cambria Math\"> Breeze -(</span><span style=\"font-family:Cambria Math\">समीर</span><span style=\"font-family:Cambria Math\">) -</span><span style=\"font-family:Cambria Math\">  a lig</span><span style=\"font-family:Cambria Math\">ht wind</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">5</span><span style=\"font-family:Cambria Math\">. Choose the sentence with no spelling error.</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">5</span><span style=\"font-family:Cambria Math\">. Choose the sentence with no spelling error.</span></p>",
                    options_en: [" <p> You remiend me of somone I knew once.   </span></p>", " <p> You reminde me of someone I knew once.  </span></p>", 
                                " <p> You remind me of someone I knew onse.</span></p>", " <p> You remind me of someone I knew once.</span></p> <p><span style=\"font-family:Cambria Math\">  </span></p>"],
                    options_hi: [" <p> You remiend me of somone I knew once.   </span></p>", " <p> You reminde me of someone I knew once.  </span></p>",
                                " <p> You remind me of someone I knew onse.</span></p>", " <p> You remind me of someone I knew once.</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">5.(d) </span></p> <p><span style=\"font-family:Cambria Math\">‘Remind’ </span><span style=\"font-family:Cambria Math\">& ‘someone’ are the correct spellings which are only given in option d.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">5.(d) </span></p> <p><span style=\"font-family:Cambria Math\">‘Remind’ & ‘someone’ </span><span style=\"font-family:Cambria Math\">सही</span><span style=\"font-family:Cambria Math\"> spellings  </span><span style=\"font-family:Cambria Math\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">केवल</span><span style=\"font-family:Cambria Math\"> option d  </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हैं।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6. </span><span style=\"font-family:Cambria Math\">Select the most appropriate \'one word\' for the expression given below.</span></p> <p><span style=\"font-family:Cambria Math\"> One who has persistent neurotic impulse to steal</span></p>",
                    question_hi: " <p>6. </span><span style=\"font-family:Cambria Math\">Select the most appropriate \'one word\' for the expression given below.</span></p> <p><span style=\"font-family:Cambria Math\"> One who has persistent neurotic impulse to steal</span></p>",
                    options_en: [" <p><span style=\"font-family:Cambria Math\"> Connoisseur</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">Kleptomaniac</span></p>", 
                                " <p> </span><span style=\"font-family:Cambria Math\">Hypocrite</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> </span><span style=\"font-family:Cambria Math\">Iconoclast</span></p>"],
                    options_hi: [" <p><span style=\"font-family:Cambria Math\"> Connoisseur</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">Kleptomaniac</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">Hypocrite</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> </span><span style=\"font-family:Cambria Math\">Iconoclast</span></p>"],
                    solution_en: " <p>6.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Kleptomaniac-</span><span style=\"font-family:Cambria Math\"> one who has persistent neurotic impulse to steal</span></p> <p><span style=\"font-family:Cambria Math\">Connoisseur - a person who knows a lot about art, good food, music, etc.</span></p> <p><span style=\"font-family:Cambria Math\">Hypocrite -a person who pretends to have moral standards or opinions which he/she does not really have. </span></p> <p><span style=\"font-family:Cambria Math\">Iconoclast - a person who does not believe in and is opposed to accepted ideas, traditions, etc.</span></p>",
                    solution_hi: " <p>6.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Kleptomaniac -</span><span style=\"font-family:Cambria Math\">(</span><span style=\"font-family:Cambria Math\">चोरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बीमारी</span><span style=\"font-family:Cambria Math\">)- Comp</span><span style=\"font-family:Cambria Math\">ulsion to steal </span></p> <p><span style=\"font-family:Cambria Math\">Connoisseur (</span><span style=\"font-family:Cambria Math\">विशेषज्ञ</span><span style=\"font-family:Cambria Math\">) - a person who knows a lot about art, good food, music, etc.</span></p> <p><span style=\"font-family:Cambria Math\">Hypocrite (</span><span style=\"font-family:Cambria Math\">पाखंडी</span><span style=\"font-family:Cambria Math\">) -a person who pretends to have moral standards or opinions which he/she does not really have.</span></p> <p><span style=\"font-family:Cambria Math\">Iconoclast (</span><span style=\"font-family:Cambria Math\">मूर्ति</span><span style=\"font-family:Cambria Math\">)- a person who does not</span><span style=\"font-family:Cambria Math\"> believe in and is opposed to accepted ideas, traditions, etc.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: " <p>7. </span><span style=\"font-family:Cambria Math\">Choose the word that is opposite in meaning to the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Frustrate</span></p>",
                    question_hi: " <p>7. </span><span style=\"font-family:Cambria Math\">Choose the word that is opposite in meaning to the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Frustrate</span></p>",
                    options_en: [" <p>  Circumvent </span></p>", " <p>  Foster</span></p>", 
                                " <p>  Irk</span></p>", " <p>  Fetter</span></p>"],
                    options_hi: [" <p>  Circumvent </span></p>", " <p>  Foster</span></p>",
                                " <p>  Irk</span></p>", " <p>  Fetter</span></p>"],
                    solution_en: " <p>7.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Frustrate-</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">a feeling of anger because you cannot get what you want</span></p> <p><span style=\"font-family:Cambria Math\">Circumvent - to find a clever way of avoiding a difficulty or rule</span></p> <p><span style=\"font-family:Cambria Math\">Irk- to irritate or annoy somebody</span></p> <p><span style=\"font-family:Cambria Math\">Fetter - a chain or manacle used to restrain a prisoner, typically placed around the ankles.</span></p>",
                    solution_hi: " <p>7.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Frustrate (</span><span style=\"font-family:Cambria Math\">हताश</span><span style=\"font-family:Cambria Math\">) -</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">a feeling of anger because yo</span><span style=\"font-family:Cambria Math\">u cannot get what you want</span></p> <p><span style=\"font-family:Cambria Math\">Circumvent (</span><span style=\"font-family:Cambria Math\">दरकिनार</span><span style=\"font-family:Cambria Math\">)- to find a clever way of avoiding a difficulty or rule</span></p> <p><span style=\"font-family:Cambria Math\">Irk (</span><span style=\"font-family:Cambria Math\">सताना</span><span style=\"font-family:Cambria Math\"> )-to irritate or annoy somebody</span></p> <p><span style=\"font-family:Cambria Math\">Fetter (</span><span style=\"font-family:Cambria Math\">बेड़ी</span><span style=\"font-family:Cambria Math\">)- a chain or manacle used to restrain a prisoner, typically placed around the ankles</span></p> <p><span style=\"font-family:Cambria Math\">.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: " <p>8</span><span style=\"font-family:Cambria Math\">. Choose the word that means the same as the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Apologetic</span></p>",
                    question_hi: " <p>8</span><span style=\"font-family:Cambria Math\">. Choose the word that means the same as the given word. </span></p> <p><span style=\"font-family:Cambria Math\">Apologetic</span></p>",
                    options_en: [" <p> Stagnant</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">Derisive</span></p>", 
                                " <p> </span><span style=\"font-family:Cambria Math\">Solid</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">Contrite</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> Stagnant</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">Derisive</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">Solid</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">Contrite</span></p>"],
                    solution_en: " <p>8.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Apologetic - </span><span style=\"font-family:Cambria Math\">expressing or showing regretful acknowledgement of an offence or failure.</span></p> <p><span style=\"font-family:Cambria Math\">Stagnant -  </span><span style=\"font-family:Cambria Math\">not active</span></p> <p><span style=\"font-family:Cambria Math\">Derisive - </span><span style=\"font-family:Cambria Math\">expressing or causing contemptuous ridicule or scorn</span></p> <p><span style=\"font-family:Cambria Math\">Solid - </span><span style=\"font-family:Cambria Math\">a substance or object that is hard</span></p>",
                    solution_hi: " <p>8.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Apolog</span><span style=\"font-family:Cambria Math\">etic (</span><span style=\"font-family:Cambria Math\">क्षमाशील</span><span style=\"font-family:Cambria Math\">)- </span><span style=\"font-family:Cambria Math\">expressing or showing regretful acknowledgement of an offence or failure.</span></p> <p><span style=\"font-family:Cambria Math\">Stagnant (</span><span style=\"font-family:Cambria Math\">आलसी</span><span style=\"font-family:Cambria Math\">)-  </span><span style=\"font-family:Cambria Math\">not active</span></p> <p><span style=\"font-family:Cambria Math\">Derisive (</span><span style=\"font-family:Cambria Math\">हास्यजनक</span><span style=\"font-family:Cambria Math\">)</span><span style=\"font-family:Cambria Math\"> - </span><span style=\"font-family:Cambria Math\">expressing or causing contemptuous ridicule or scorn</span></p> <p><span style=\"font-family:Cambria Math\">Solid (</span><span style=\"font-family:Cambria Math\">ठोस</span><span style=\"font-family:Cambria Math\">)- </span><span style=\"font-family:Cambria Math\">a substance or object that is hard</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Find the part of the given sentence that has an error in it. If there is no error, choose </span><span style=\"font-family: Cambria Math;\">\'No error\'.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The recent discoveries have risen hopes about possible applications.</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">Find the part of the given sentence that has an error in it. If there is no error, choose </span><span style=\"font-family: Cambria Math;\">\'No error\'.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The recent discoveries have risen hopes about possible applications.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">have risen hopes</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">No error</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">The recent discoveries</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">about possible </span><span style=\"font-family: Cambria Math;\">applications.</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">have risen hopes</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">No error</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">The recent discoveries</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">about possible applications.</span></p>\n"],
                    solution_en: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Have + </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">&rsquo; is the correct grammatical structure for the given sentence which is in the present perfect tense. Hence, &lsquo;risen&rsquo; will be replaced with &lsquo;raised&rsquo; &amp; &lsquo;have raised(</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">) hopes&rsquo; becomes the most </span><span style=\"font-family: Cambria Math;\">appropriate answer.</span></p>\n",
                    solution_hi: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> \'Have + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math>\' </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> grammatical structure </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> present perfect tense </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;risen&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'&lsquo;raised </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;have raised(</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math><span style=\"font-family: Cambria Math;\">) hopes&rsquo;\' </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2319;&#2327;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> Fill in the blank with an appropriate option. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">My feet don\'t__________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">the ground for the rest of the day.</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> Fill in the blank with an appropriate option. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">My feet don\'t__________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">the ground</span><span style=\"font-family: Cambria Math;\"> for the rest of the day.</span></p>\n",
                    options_en: ["<p>slab</p>\n", "<p>touch</p>\n", 
                                "<p>deal</p>\n", "<p>purify</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>slab</p>\n", "<p>touch</p>\n",
                                "<p>deal</p>\n", "<p>purify</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>10.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Touch&rsquo; means </span><span style=\"font-family: Cambria Math;\">to put your hand or fingers onto somebody/something</span><span style=\"font-family: Cambria Math;\">. The given sentence states that the narrator&rsquo;s feet don\'t touch the ground for the rest of the </span><span style=\"font-family: Cambria Math;\">day. Hence, &lsquo;touch&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>10.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Touch&rsquo;</span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2305;&#2327;&#2354;&#2367;&#2351;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2326;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">narrator </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2376;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2350;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2370;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Cambria Math;\"> &lsquo;touch&rsquo;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;</span><span style=\"font-family: Cambria Math;\">&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">However, even after Independence in 1947, British expatriate firms did not suddenly____________ </span><span style=\"font-family: \'Cambria Math\';\">from India.</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">However, even after Independence in 1947, British expatriate firms did not suddenly____________ </span><span style=\"font-family: \'Cambria Math\';\">from India.</span></p>\n",
                    options_en: ["<p>close</p>\n", "<p>disallow</p>\n", 
                                "<p>divest</p>\n", "<p>consecrate</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>close</p>\n", "<p>disallow</p>\n",
                                "<p>divest</p>\n", "<p>consecrate</p>\n"],
                    solution_en: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Di</span><span style=\"font-family: Cambria Math;\">vest&rsquo; means to </span><span style=\"font-family: Cambria Math;\">take away possessions of something from someone</span><span style=\"font-family: Cambria Math;\">. The given sentence states that even after Independence in 1947, British expatriate firms did not suddenly divest from India. Hence, &lsquo;divest&rsquo; is the most appropriate answer.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Div</span><span style=\"font-family: Cambria Math;\">est&rsquo; </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> sentence </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> 1947 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">British </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2357;&#2366;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2352;&#2381;&#2350;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2330;&#2366;&#2344;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">divest&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: " <p>12</span><span style=\"font-family:Cambria Math\">. Rearrange the part</span><span style=\"font-family:Cambria Math\">s of the sentence in correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. versions of the European poor laws, charitable and</span></p> <p><span style=\"font-family:Cambria Math\">Q. in former colonies, such as Ghana, Jamaica, India and the</span></p> <p><span style=\"font-family:Cambria Math\">R. missionary activities, and the introduction of Western juvenile justice procedures</span></p> <p><span style=\"font-family:Cambria Math\">S. Philippines the basic welfare services grew out of modified</span></p>",
                    question_hi: " <p>12</span><span style=\"font-family:Cambria Math\">. Rearrange the parts of the sentence in correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. versions of the European poor laws, charitable and</span></p> <p><span style=\"font-family:Cambria Math\">Q. in former colonies, such as Gha</span><span style=\"font-family:Cambria Math\">na, Jamaica, India and the</span></p> <p><span style=\"font-family:Cambria Math\">R. missionary activities, and the introduction of Western juvenile justice procedures</span></p> <p><span style=\"font-family:Cambria Math\">S. Philippines the basic welfare services grew out of modified</span></p>",
                    options_en: [" <p> </span><span style=\"font-family:Cambria Math\">PRSQ</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> </span><span style=\"font-family:Cambria Math\">QSPR</span><span style=\"font-family:Cambria Math\"> </span></p>", 
                                " <p> </span><span style=\"font-family:Cambria Math\"> QPRS</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">PSQR</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\">PRSQ</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> </span><span style=\"font-family:Cambria Math\">QSPR</span><span style=\"font-family:Cambria Math\"> </span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\"> QPRS</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> </span><span style=\"font-family:Cambria Math\">PSQR</span></p>"],
                    solution_en: " <p>12.(b)</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Cambria Math\">QSPR</span></p> <p><span style=\"font-family:Cambria Math\">Sentence Q</span><span style=\"font-family:Cambria Math\"> will be the starting line as it introduces the main idea of the parajumble i.e. former colonies. However, Sentence S talks about the basic welfare services. So, S will follow Q. Further, Sentence P talks about the modified versions of the European poor la</span><span style=\"font-family:Cambria Math\">ws and Sentence R talks about the introduction of Western juvenile justice procedures. So, R will follow P. Going through the options, option (b) has the correct sequence.</span></p>",
                    solution_hi: " <p>12.(b)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">QSPR</span></p> <p><span style=\"font-family:Cambria Math\">Sentence Q </span><span style=\"font-family:Cambria Math\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> line  </span><span style=\"font-family:Cambria Math\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विचार</span><span style=\"font-family:Cambria Math\"> ‘former colonies’ </span><span style=\"font-family:Cambria Math\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हालाँकि</span><span style=\"font-family:Cambria Math\">, sentence S </span><span style=\"font-family:Cambria Math\">मूलभूत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कल्याण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सेवाओं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तो</span><span style=\"font-family:Cambria Math\">, Q </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बाद</span><span style=\"font-family:Cambria Math\"> S </span><span style=\"font-family:Cambria Math\">आएगा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आगे</span><span style=\"font-family:Cambria Math\">, Sentence  P </span><span style=\"font-family:Cambria Math\">यूरोपीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गरीब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कानूनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संशोधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संस्करणों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> Sentence R </span><span style=\"font-family:Cambria Math\">पश्चिमी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किशोर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">न्याय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रक्रियाओं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">श</span><span style=\"font-family:Cambria Math\">ुरुआत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तो</span><span style=\"font-family:Cambria Math\">, P </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बाद</span><span style=\"font-family:Cambria Math\"> R </span><span style=\"font-family:Cambria Math\">आएगा।</span><span style=\"font-family:Cambria Math\"> Options  </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पर</span><span style=\"font-family:Cambria Math\"> , option ( b) </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सही</span><span style=\"font-family:Cambria Math\"> Sequence  </span><span style=\"font-family:Cambria Math\">है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: " <p>13. </span><span style=\"font-family:Cambria Math\">Choose the word that can substitute the given group of words. </span></p> <p><span style=\"font-family:Cambria Math\">Study of heavenly bodies</span></p>",
                    question_hi: " <p>13. </span><span style=\"font-family:Cambria Math\">Choose the word that can substitute the given group of words. </span></p> <p><span style=\"font-family:Cambria Math\">Study of heavenly bodies</span></p>",
                    options_en: [" <p> Numerology </span></p>", " <p> Astrology </span></p>", 
                                " <p> Stargazing</span></p>", " <p> Astronomy</span></p>"],
                    options_hi: [" <p> Numerology </span></p>", " <p> Astrology </span></p>",
                                " <p> Stargazing</span></p>", " <p> Astronomy</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">13.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Astronomy</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">- Study of heavenly bodies </span></p> <p><span style=\"font-family:Cambria Math\">Starg</span><span style=\"font-family:Cambria Math\">azing - observe the stars.</span></p> <p><span style=\"font-family:Cambria Math\">Astrology  - the study of the positions and movements of the stars and planets and the way that some people believe they affect people and events</span></p> <p><span style=\"font-family:Cambria Math\">Numerology -the use of numbers to tell somebody what will happen in the future</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">13.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Astronomy (</span><span style=\"font-family:Cambria Math\">खगोल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विज्ञान</span><span style=\"font-family:Cambria Math\">)- Study of heavenly bodies</span></p> <p><span style=\"font-family:Cambria Math\">Stargazing (</span><span style=\"font-family:Cambria Math\">व्यर्थ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बातें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सोचना</span><span style=\"font-family:Cambria Math\">)</span><span style=\"font-family:Cambria Math\"> - observe the stars.</span></p> <p><span style=\"font-family:Cambria Math\">Astrology (</span><span style=\"font-family:Cambria Math\">ज्योतिष</span><span style=\"font-family:Cambria Math\">)  - the study of the positions and movements of the stars and planets and the way that some people believe they affect people and events</span></p> <p><span style=\"font-family:Cambria Math\">Numerology (</span><span style=\"font-family:Cambria Math\">अंक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">शास्त्र</span><span style=\"font-family:Cambria Math\"> )-the use of numbers to tell somebody what will happen in the future</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">14</span><span style=\"font-family:Cambria Math\">. Rearrange the parts of the sentence in correct order.</span></p> <p><span style=\"font-family:Cambria Math\">Climate change is</span></p> <p><span style=\"font-family:Cambria Math\">P. climate caused by natural forces in combination</span></p> <p><span style=\"font-family:Cambria Math\">Q. a broad topic that includes periodic alterations in Earth\'s</span></p> <p><span style=\"font-family:Cambria Math\">R. with the effects of various human activities like burning of </span></p> <p><span style=\"font-family:Cambria Math\">S</span><span style=\"font-family:Cambria Math\">. fossil fuels and changes in land cover and biodiversity</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">14</span><span style=\"font-family:Cambria Math\">. Rearrange the parts of the sentence in correct order.</span></p> <p><span style=\"font-family:Cambria Math\">Climate change is</span></p> <p><span style=\"font-family:Cambria Math\">P. climate caused by natural forces in combination</span></p> <p><span style=\"font-family:Cambria Math\">Q. a broad topic that </span><span style=\"font-family:Cambria Math\">includes periodic alterations in Earth\'s</span></p> <p><span style=\"font-family:Cambria Math\">R. with the effects of various human activities like burning of </span></p> <p><span style=\"font-family:Cambria Math\">S. fossil fuels and changes in land cover and biodiversity</span></p>",
                    options_en: [" <p> </span><span style=\"font-family:Cambria Math\"> SRPQ</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">QPRS</span></p>", 
                                " <p> </span><span style=\"font-family:Cambria Math\"> PSQR</span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">RSQP</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\"> SRPQ</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">QPRS</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\"> PSQR</span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">RSQP</span></p>"],
                    solution_en: " <p>14.(b) </span><span style=\"font-family:Cambria Math\">QPRS</span></p> <p><span style=\"font-family:Cambria Math\">Sentence Q will be the starting line as it introduces the main idea of the parajumble i.e. climate change is a broad topic. However, Sentence P states that it is alterations in Earth\'s climate. So, P will follow Q. Further, Sentence R talks about the effec</span><span style=\"font-family:Cambria Math\">ts of various human activities and Sentence S talks about human activities like burning of fossil fuels and changes in land cover. So, S will follow R. Going through the options, option b has the correct sequence.</span></p>",
                    solution_hi: " <p>14.(b) </span><span style=\"font-family:Cambria Math\">QPRS</span></p> <p><span style=\"font-family:Cambria Math\">Sentence Q </span><span style=\"font-family:Cambria Math\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> li</span><span style=\"font-family:Cambria Math\">ne  </span><span style=\"font-family:Cambria Math\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विचार</span><span style=\"font-family:Cambria Math\"> ‘</span><span style=\"font-family:Cambria Math\">climate change is a broad topic’ </span><span style=\"font-family:Cambria Math\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हालाँकि</span><span style=\"font-family:Cambria Math\"> Sentence P </span><span style=\"font-family:Cambria Math\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पृथ्वी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जलवायु</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">परिवर्तन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तो</span><span style=\"font-family:Cambria Math\">, Q </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बाद</span><span style=\"font-family:Cambria Math\"> P </span><span style=\"font-family:Cambria Math\">आएगा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आगे</span><span style=\"font-family:Cambria Math\">, Sentence  R </span><span style=\"font-family:Cambria Math\">विभिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मानवीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गतिविधियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रभावों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करत</span><span style=\"font-family:Cambria Math\">ा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> Sentence S </span><span style=\"font-family:Cambria Math\">जीवाश्म</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ईंधन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जलाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">भूमि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आवरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">परिवर्तन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जैसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मानवीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गतिविधियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तो</span><span style=\"font-family:Cambria Math\">,  R </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बाद</span><span style=\"font-family:Cambria Math\"> S </span><span style=\"font-family:Cambria Math\">आएगा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">।</span><span style=\"font-family:Cambria Math\"> Options  </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पर</span><span style=\"font-family:Cambria Math\">, option (b) </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सही</span><span style=\"font-family:Cambria Math\"> sequence  </span><span style=\"font-family:Cambria Math\">है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Select the correct indirect form of the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> The sage said, \"Man proposes and God disposes.\"</span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">Select the correct indirect form of the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> The sage said, \"Man proposes and God disposes.\"</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">The sage said that man proposes and God disposes.</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">The sage said that man propose and God dispose.</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">The sage said that man proposed and</span><span style=\"font-family: Cambria Math;\"> God disposed.</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">The sage said that man had proposed and God had disposed.</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">The sage said that man proposes and God disposes.</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">The</span><span style=\"font-family: Cambria Math;\"> sage said that man propose and God dispose.</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">The sage said that man proposed and God disposed.</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">The sage said that man had proposed and God had disposed.</span></p>\n"],
                    solution_en: "<p>15.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">The sage said that man proposes and God disposes (Correct)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(b) </span><span style=\"font-weight: 400;\">The sage said that man </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">propose</span></span><span style=\"font-weight: 400;\"> and God </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">dispose</span></span><span style=\"font-weight: 400;\">.(Incorrect Verbs)</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(c) The sage said that man <span style=\"text-decoration: underline;\">proposed</span> and God <span style=\"text-decoration: underline;\">disposed</span>.(Incorrect Verbs)</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(d) The sage said that man <span style=\"text-decoration: underline;\">had proposed</span> and God <span style=\"text-decoration: underline;\">had</span> <span style=\"text-decoration: underline;\">disposed</span>.(Incorrect Tense)</span></span></p>\n",
                    solution_hi: "<p>15.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">The sage said that man prop</span><span style=\"font-family: Cambria Math;\">oses and God disposes.(Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) </span><span style=\"font-weight: 400;\">The sage said that man </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">propose</span></span><span style=\"font-weight: 400;\"> and God </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">dispose</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\">.</span>(&#2327;&#2354;&#2340;&nbsp; Verbs)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) </span><span style=\"font-weight: 400;\">The sage said that man </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">proposed</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>and God </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">disposed</span></span><span style=\"font-weight: 400;\">. (&#2327;&#2354;&#2340; Verbs)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) </span><span style=\"font-weight: 400;\">The sage said that man </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">had proposed</span></span><span style=\"font-weight: 400;\"> and God </span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\">had disposed</span></span><span style=\"font-weight: 400;\">.(&#2327;&#2354;&#2340;&nbsp; Tense)</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16<span style=\"font-family: Cambria Math;\">. Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">One who hates institution of marriage</span></p>\n",
                    question_hi: "<p>16<span style=\"font-family: Cambria Math;\">. Choose the word that can substitute the given group of words. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">One who hates institution of marriage</span></p>\n",
                    options_en: ["<p>Coquette</p>\n", "<p>Pedantic</p>\n", 
                                "<p>Polyglot</p>\n", "<p>Misogamist</p>\n"],
                    options_hi: ["<p>Coquette</p>\n", "<p>Pedantic</p>\n",
                                "<p>Polyglot</p>\n", "<p>Misogamist</p>\n"],
                    solution_en: "<p>16.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Misogamist - One who hates the institution of marriage.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Coquette - a flirtatious woman.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pedantic - too worried about rules or details</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pol</span><span style=\"font-family: Cambria Math;\">yglot - knowing or written in more than one language</span></p>\n",
                    solution_hi: "<p>16.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Misogamist (</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2357;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2371;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">) - One who hates the institution of marriage.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Coquette (</span><span style=\"font-family: Cambria Math;\">&#2344;&#2326;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2326;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> )- a flirtatious woman.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Pedantic (</span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2397;&#2367;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> - too worried about rules or details</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Polyglot (</span><span style=\"font-family: Cambria Math;\">&#2348;&#2361;&#2369;&#2349;&#2366;&#2359;&#2368;</span><span style=\"font-family: Cambria Math;\">) - knowing or written in more than one language</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">17. </span><span style=\"font-family: Cambria Math;\">Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A man may die, nations may rise and fall, but an idea </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"><strong>lived on</strong></span>.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">17. </span><span style=\"font-family: Cambria Math;\">Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A man may die, nations may rise and fall, but an ide</span><span style=\"font-family: Cambria Math;\">a </span><span style=\"font-family: Cambria Math;\"><strong>lived on</strong>.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"> lives in</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">live on</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">No improvement</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">lives on</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"> lives in</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">live on</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">No improvement</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">lives on</span></p>\n"],
                    solution_en: "<p>17.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">The given sentence is in the simple present tense so the verb must be used in the simple present form(</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>1</mn></msub></math><span style=\"font-family: Cambria Math;\">). Hence, &lsquo;lives(</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>1</mn></msub></math><span style=\"font-family: Cambria Math;\">) on&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>17.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> sentence simple present tense </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> Verb </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> simple present form (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>1</mn></msub></math>) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">,&lsquo;lives(</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>1</mn></msub></math><span style=\"font-family: Cambria Math;\">) on&rsquo; \' </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">18</span><span style=\"font-family: Cambria Math;\">. Select the most appropriate meaning of the given idiom. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Slap on the wrist</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">18</span><span style=\"font-family: Cambria Math;\">. Select the most appropriate meaning of the given idiom. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Slap on the wrist</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Wild with excitement</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Misunderstand each other</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Criticize someone strongly and angrily</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">A mild punishment</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">Wild with excitement</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Misunderstand e</span><span style=\"font-family: Cambria Math;\">ach other</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">Criticize someone strongly and angrily</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">A mild punishment</span></p>\n"],
                    solution_en: "<p>18.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Slap on the wrist - A mild punishment .</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">E.g.- Teacher gives his student a slap on the wrist for not doing the homework.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>18.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Slap on the wrist - A mild punishment .(</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2354;&#2381;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> - Teacher gives his student a slap on the wrist for not doing the homework./</span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2350;&#2357;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352; </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2381;&#2351;&#2366;&#2346;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2366;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2354;&#2381;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> | </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\">. Identify the segment in the sentence which contains a grammatical error. If there is no error, then select the option \"No error\".</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">And it just recently awarded/ a million-dollar contract/ that reeks of corruption.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\">. Identify the segment in the sentence which contains a grammatical error. If there is no error, then select the option \"No error\".</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">And it just recently awarded/ a million-dollar contract/ that reeks of corruption.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">a million-dollar contract</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">And it</span><span style=\"font-family: Cambria Math;\"> just recently awarded</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">that reeks of corruption.</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> No error</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">a million-dollar contract</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">And it just recently awarded</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">that reeks of corruption.</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> No error</span></p>\n"],
                    solution_en: "<p>19.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">No error. The given sentence is grammatic</span><span style=\"font-family: Cambria Math;\">ally correct.</span></p>\n",
                    solution_hi: "<p>19.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">No error. </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> sentence grammar </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2371;&#2359;&#2381;&#2335;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> correct </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">20. </span><span style=\"font-family: Cambria Math;\">Choose the incorrectly spelt word.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">20. </span><span style=\"font-family: Cambria Math;\">Choose the incorrectly spelt word.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Bloom</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Gloomy</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Shuot</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Broom</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">Bloom</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Gloomy</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">Shuot</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Broom</span></p>\n"],
                    solution_en: "<p>20.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Shout&rsquo; is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Bloom - produce flowers</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Gloomy - sad</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Broom - a brush with a long handle and bristles</span></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>20.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Shout&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> spelling </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">21</span><span style=\"font-family: Cambria Math;\">. </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong><br><span style=\"font-family: Cambria Math;\">Many fruits and vegetables change (21)____________ as they ripen. For example, tomatoes, strawberries, and red bell peppers all</span><span style=\"font-family: Cambria Math;\"> change from green to red. Colour is just one way along with size, texture, and smell that you can tell if a fruit or vegetable is (22)____________ to be harvested or eaten. Imagine a factory that (23) ___________strawberries into plastic containers to </span><span style=\"font-family: Cambria Math;\">be</span><span style=\"font-family: Cambria Math;\"> (24)___________</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">to grocery stores across the country. It might be useful to have a machine that automatically (25)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> any green strawberries that were picked before they were ripe. Or, you might want a machine on the farm that</span><span style=\"font-family: Cambria Math;\"> automatically recognizes strawberries by colour and only picks the red ones to begin with.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 21.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">21</span><span style=\"font-family: Cambria Math;\">. </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong><br><span style=\"font-family: Cambria Math;\">Many fruits and vegetables change (21)____________ as they ripen. For example, tomatoes, strawberries, and red bell peppers all change from green to red. Colour is just one way along with size, texture, and smell that you can tell if a fruit or vegetable i</span><span style=\"font-family: Cambria Math;\">s (22)____________ to be harvested or eaten. Imagine a factory that (23) __________strawberries into plastic containers to </span><span style=\"font-family: Cambria Math;\">be (24)__________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to grocery stores across the country. It might be useful to have a machine that automatically (25)__________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> any green strawberries that were picked before they were ripe. Or, you might want a machine on the farm that automatically recognizes strawberries by colour and only picks the red ones to begin with.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill </span><span style=\"font-family: Cambria Math;\">in blank number 21.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">shape</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">clothes</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">colour</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">size</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">shape</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">clothes</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">colour</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">size</span></p>\n"],
                    solution_en: "<p>21.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Colour&rsquo; means </span><span style=\"font-family: Cambria Math;\">the fact that something is red, green, yellow, blue, etc</span><span style=\"font-family: Cambria Math;\">. The given passage states that </span><span style=\"font-family: Cambria Math;\">many fruits and vegetables change colour as they ripen</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;col</span><span style=\"font-family: Cambria Math;\">our&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>21.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Colour&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2341;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2361;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2368;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2381;&#2332;&#2367;&#2351;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2325;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2342;&#2354;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &lsquo;colour&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">22. </span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong><br><span style=\"font-family: Cambria Math;\">Many fruits and vegetables change (21)____________ as they ripen. For example, tomatoes, strawberries, and red bell peppers all change from green to red. Colour is just one way along with size, texture, and smell that you can tell if a fruit or</span><span style=\"font-family: Cambria Math;\"> vegetable is (22)____________ to be harvested or eaten. Imagine a factory that (23) ____________strawberries into plastic containers to </span><span style=\"font-family: Cambria Math;\">be (24)______________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to grocery stores across the country. It might be useful to have a machine that automatically (</span><span style=\"font-family: Cambria Math;\">25)____________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> any green strawberries that were picked before they were ripe. Or, you might want a machine on the farm that automatically recognizes strawberries by colour and only picks the red ones to begin with.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate</span><span style=\"font-family: Cambria Math;\"> option to fill in blank number 22.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">22. </span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong><br><span style=\"font-family: Cambria Math;\">Many fruits and vegetables change (21)____________ as they ripen. For example, tomatoes, strawberries, and red bell peppers all change from green to red. Colour is just one way along with size, texture, and smell that you can tell if a fruit or vegetable i</span><span style=\"font-family: Cambria Math;\">s (22)____________ to be harvested or eaten. Imagine a factory that (23) ____________strawberries into plastic containers to </span><span style=\"font-family: Cambria Math;\">be (24)____________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to grocery stores across the country. It might be useful to have a machine that automatically (25)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> any green strawberries that were picked before they were ripe. Or, you might want a machine on the farm that automatically recognizes strawberries by colour and only picks the red ones to begin with.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fil</span><span style=\"font-family: Cambria Math;\">l in blank number 22.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">steady</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> trepid</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">sober</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">ready</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">steady</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> trepid</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">sober</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">ready</span></p>\n"],
                    solution_en: "<p>22.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Ready</span><span style=\"font-family: Cambria Math;\">&rsquo; means </span><span style=\"font-family: Cambria Math;\">prepared and able to do something</span><span style=\"font-family: Cambria Math;\">. The given passage states that </span><span style=\"font-family: Cambria Math;\">a fruit or vegetable is ready to be harvested or eaten</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">ready</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>22.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Ready</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2376;&#2351;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2381;&#2359;&#2350;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2381;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2335;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2376;&#2351;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">ready</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\">. </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong><br><span style=\"font-family: Cambria Math;\">Many fruits and vegetables ch</span><span style=\"font-family: Cambria Math;\">ange (21)____________ as they ripen. For example, tomatoes, strawberries, and red bell peppers all change from green to red. Colour is just one way along with size, texture, and smell that you can tell if a fruit or vegetable is (22)____________ to be harv</span><span style=\"font-family: Cambria Math;\">ested or eaten. Imagine a factory that (23) ____________strawberries into plastic containers to&nbsp; </span><span style=\"font-family: Cambria Math;\">be (24)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to grocery stores across the country. It might be useful to have a machine that automatically (25)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> any green strawb</span><span style=\"font-family: Cambria Math;\">erries that were picked before they were ripe. Or, you might want a machine on the farm that automatically recognizes strawberries by colour and only picks the red ones to begin with.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 23.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\">. </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong><br><span style=\"font-family: Cambria Math;\">Many fruits and vegetables change (21)____________ as they ripen. For example, tomatoes, strawberries, and red bell peppers all change from green to red. Colour is just one way along with size, texture, and smell that you can tell if a fruit or vegetable i</span><span style=\"font-family: Cambria Math;\">s (22)____________ to be harvested or eaten. Imagine a factory that (23) _____________strawberries into plastic containers to </span><span style=\"font-family: Cambria Math;\">be (24)_____________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to grocery stores across the country. It might be useful to have a machine that automatically (25)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> any green strawberries that were picked before they were ripe. Or, you might want a machine on the farm that automatically recognizes strawberries by colour and only picks the red ones to begin with.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill </span><span style=\"font-family: Cambria Math;\">in blank number 23.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">packs</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">breaks</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">tapes</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">halts</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">packs</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">breaks</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">tapes</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">halts</span></p>\n"],
                    solution_en: "<p>23.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Pack</span><span style=\"font-family: Cambria Math;\">&rsquo; means </span><span style=\"font-family: Cambria Math;\">a set of things that are supplied together for a particular purpose</span><span style=\"font-family: Cambria Math;\">. The given passage talks about</span><span style=\"font-family: Cambria Math;\"> a factory that packs strawberries into plastic containers</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">packs</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>23.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Pack</span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2346;&#2370;&#2352;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2368;&#2332;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2326;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> strawberries</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2354;&#2366;&#2360;&#2381;&#2335;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2335;&#2375;&#2344;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2376;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">packs</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">24. </span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong><br><span style=\"font-family: Cambria Math;\">Many fruits and vegetables change (21)____________ as they ripen. For example, tomatoes, strawberries, and red bell peppers all change from green to red. C</span><span style=\"font-family: Cambria Math;\">olour is just one way along with size, texture, and smell that you can tell if a fruit or vegetable is (22)____________ to be harvested or eaten. Imagine a factory that (23) _______strawberries into plastic containers to </span><span style=\"font-family: Cambria Math;\">be (24)__________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to g</span><span style=\"font-family: Cambria Math;\">rocery stores across the country. It might be useful to have a machine that automatically (25)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> any green strawberries that were picked before they were ripe. Or, you might want a machine on the farm that automatically recognizes strawb</span><span style=\"font-family: Cambria Math;\">erries by colour and only picks the red ones to begin with.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 24.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">24. </span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong><br><span style=\"font-family: Cambria Math;\">Many fruits and vegetables change (21)____________ as they ripen. For example, tomatoes, strawberries, and red bell peppers all change from green to red. Colour is just one way along with size, texture, and smell that you can tell if a fruit or vegetable i</span><span style=\"font-family: Cambria Math;\">s (22)____________ to be harvested or eaten. Imagine a factory that (23) ___________strawberries into plastic containers to&nbsp; </span><span style=\"font-family: Cambria Math;\">be (24)__________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to grocery stores across the country. It might be useful to have a machine that automatically (25)_________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> any green strawberries that were picked before they were ripe. Or, you might want a machine on the farm that automatically recognizes strawberries by colour and only picks the red ones to begin with.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fi</span><span style=\"font-family: Cambria Math;\">ll in blank number 24.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">managed of</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">shipped off</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">suited to</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">accounted to</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">managed of</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">shipped off</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">suited to</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">accounted to</span></p>\n"],
                    solution_en: "<p>24.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Shipped off</span><span style=\"font-family: Cambria Math;\">&rsquo; means to send something away from somewhere. The given passage talks about</span><span style=\"font-family: Cambria Math;\"> a factory that packs strawberries into plastic containers to be shipped off to grocery stores across the country</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">shipped off</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>24.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Shipped off</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2368;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2375;&#2332;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2319;</span><span style=\"font-family: Cambria Math;\">&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2326;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2381;&#2352;&#2377;&#2348;&#2375;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2354;&#2366;&#2360;&#2381;&#2335;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2335;&#2375;&#2344;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2376;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2352;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, \'</span><span style=\"font-family: Cambria Math;\">shipped off</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">25.</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong><br><span style=\"font-family: Cambria Math;\">Many fruits and vegetables change (21)____________ as they ripen. For example, tomatoes, strawberries, and red bell peppers all change from green to red. Colour is just one way along with size, texture, and smell that you can tell if a fruit or vegetable i</span><span style=\"font-family: Cambria Math;\">s (22)____________ to be harvested or eaten. Imagine a factory that (23) ____________strawberries into plastic containers to </span><span style=\"font-family: Cambria Math;\">be (24)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to grocery stores across the country. It might be useful to have a machine that automatically (25)__________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> any green strawberries that were picked before they were ripe. Or, you might want a machine on the farm that automatically recognizes strawberries by colour and only picks the red ones to begin with.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">25. </span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong><br><span style=\"font-family: Cambria Math;\">Many fruits and vegetables change (21)____________ as they ripen. For example, tomatoes, strawberries, and red bel</span><span style=\"font-family: Cambria Math;\">l peppers all change from green to red. Colour is just one way along with size, texture, and smell that you can tell if a fruit or vegetable is (22)____________ to be harvested or eaten. Imagine a factory that (23) ____________strawberries into plastic containers</span><span style=\"font-family: Cambria Math;\"> to </span><span style=\"font-family: Cambria Math;\">be (24)____________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> to grocery stores across the country. It might be useful to have a machine that automatically (25)___________</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> any green strawberries that were picked before they were ripe. Or, you might want a machine on the fa</span><span style=\"font-family: Cambria Math;\">rm that automatically recognizes strawberries by colour and only picks the red ones to begin with.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">sustains</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> detains</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"> proceeds</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">rejects</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">sustains</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> detains</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"> proceeds</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">rejects</span></p>\n"],
                    solution_en: "<p>25.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Reject</span><span style=\"font-family: Cambria Math;\">&rsquo; me</span><span style=\"font-family: Cambria Math;\">ans </span><span style=\"font-family: Cambria Math;\">to refuse to accept somebody/something</span><span style=\"font-family: Cambria Math;\">. The given passage states that </span><span style=\"font-family: Cambria Math;\">it might be useful to have a machine that automatically rejects any green strawberries that were picked before they were ripe</span><span style=\"font-family: Cambria Math;\">. Hence, &lsquo;</span><span style=\"font-family: Cambria Math;\">rejects</span><span style=\"font-family: Cambria Math;\">&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>25.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&lsquo;</span><span style=\"font-family: Cambria Math;\">Reject</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2368;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2368;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2320;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">strawberries</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2330;&#2366;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2381;&#2357;&#2368;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &lsquo;</span><span style=\"font-family: Cambria Math;\">rejects</span><span style=\"font-family: Cambria Math;\">&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>