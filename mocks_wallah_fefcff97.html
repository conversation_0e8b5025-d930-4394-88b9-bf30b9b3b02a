<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">90:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the combination of letters that when sequentially placed in the blanks of the given letter series will complete the series.</p>\r\n<p><span style=\"font-family: Times New Roman;\">M T _ R M _ Q R _ V Q _ M W _ R M _ Q R </span></p>",
                    question_hi: "<p>1. अक्षरों के उस संयोजन का चयन करें जिसे दी गई अक्षर श्रंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर श्रंखला पूरी हो जाएगी।</p>\r\n<p><span style=\"font-family: Times New Roman;\">M T _ R M _ Q R _ V Q _ M W _ R M _ Q R </span></p>",
                    options_en: ["<p>R U M R Q Y</p>", "<p>Q U M Q R X</p>", 
                                "<p>Q U M R Q X</p>", "<p>Q U S R Q R</p>"],
                    options_hi: ["<p>R U M R Q Y</p>", "<p>Q U M Q R X</p>",
                                "<p>Q U M R Q X</p>", "<p>Q U S R Q R</p>"],
                    solution_en: "<p>1.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Repetitive unit is &lsquo;MT(T increasing by 1 each time)QR&rsquo;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">MT</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">Q</span></span><span style=\"font-family: Times New Roman;\">R-M</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">U</span></span><span style=\"font-family: Times New Roman;\">QR-</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">M</span></span><span style=\"font-family: Times New Roman;\">VQ</span><span style=\"font-family: Times New Roman;\">R</span><span style=\"font-family: Times New Roman;\">-MW</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">Q</span></span><span style=\"font-family: Times New Roman;\">R-M</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">X</span></span><span style=\"font-family: Times New Roman;\">WR</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">QUMRQX</span><span style=\"font-family: Times New Roman;\"> is the correct answer.</span></p>",
                    solution_hi: "<p>1.(c)</p>\r\n<p><span style=\"font-family: Baloo;\">दोहराई जाने वाली इकाई \'&lsquo;MT(T हर बार 1 से बढ़ रही है)QR\' है। </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">MT</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">Q</span></span><span style=\"font-family: Times New Roman;\">R-M</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">U</span></span><span style=\"font-family: Times New Roman;\">QR-</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">M</span></span><span style=\"font-family: Times New Roman;\">VQ</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">R</span></span><span style=\"font-family: Times New Roman;\">-MW</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">Q</span></span><span style=\"font-family: Times New Roman;\">R-M</span><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">X</span></span><span style=\"font-family: Times New Roman;\">WR</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">QUMRQX</span><span style=\"font-family: Baloo;\"> सही उत्तर है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: " <p>2. Three of the following four letter-clusters are alike in a certain way and one is different. </span></p> <p><span style=\"font-family:Times New Roman\">Select the odd one. </span></p>",
                    question_hi: " <p>2. निम्नलिखित चार अक्षर-समूहों में से तीन एक निश्चित तरीके से समान हैं और एक अलग है। विषम का चयन करें।</span></p>",
                    options_en: [" <p> PLSK          </span></p>", " <p> DZHW </span></p>", 
                                " <p> GCKT </span></p>", " <p> HDLS</span></p>"],
                    options_hi: [" <p> PLSK </span></p>", " <p> DZHW </span></p>",
                                " <p> GCKT </span></p>", " <p> HDLS</span></p>"],
                    solution_en: " <p>2.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Logic : First letter : first letter - 4 : First letter + 4 : Reverse of First letter.</span></p>",
                    solution_hi: " <p>2.(a)</span></p> <p><span style=\"font-family:Baloo\">Logic : पहला अक्षर : पहला अक्षर - 4 : पहला अक्षर +4 : पहले अक्षर का उल्टा।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: " <p>3. In a code language, ORANGE is written as LKZGTX and MIRACLE is written as NBITXEV. How will LOCKDOWN be written in that language? </span></p>",
                    question_hi: " <p>3. एक कूट भाषा में ORANGE को LKZGTX और MIRACLE को NBITXEV लिखा जाता है। उस भाषा में LOCKDOWN कैसे लिखा जाएगा?</span></p>",
                    options_en: [" <p> OHXHWDGD </span></p>", " <p> </span><span style=\"font-family:Times New Roman\">OHDXHWDG</span><span style=\"font-family:Times New Roman\"> </span></p>", 
                                " <p> OHXHWDDG </span></p>", " <p> OHXDWHDG </span></p>"],
                    options_hi: [" <p> OHXHWDGD </span></p>", " <p> </span><span style=\"font-family:Times New Roman\">OHDXHWDG</span><span style=\"font-family:Times New Roman\"> </span></p>",
                                " <p> OHXHWDDG </span></p>", " <p> OHXDWHDG </span></p>"],
                    solution_en: " <p>3.(d)</span></p> <p><span style=\"font-family:Times New Roman\">Logic : alternatively reverse alphabetical positioning and -7 pattern is followed.</span></p> <p><span style=\"font-family:Times New Roman\">LOCKDOWN is 12-15-3-11-4-15-23-14 on applying the above pattern is 15-8-24-4-23-8-4-7</span></p> <p><span style=\"font-family:Times New Roman\">Which is the alphabetical position of OHXDWHDG.</span></p>",
                    solution_hi: " <p>3.(d)</span></p> <p><span style=\"font-family:Baloo\">Logic : वैकल्पिक रूप से विपरीत वर्णमाला स्थिति और -7 पैटर्न का पालन किया गया है।</span></p> <p><span style=\"font-family:Baloo\">LOCKDOWN = 12-15-3-11-4-15-23-14 उपरोक्त पैटर्न को लागू करने पर, 15-8-24-4-23-8-4-7</span></p> <p><span style=\"font-family:Baloo\">जो </span><span style=\"font-family:Times New Roman\">OHXDWHDG</span><span style=\"font-family:Baloo\"> की वर्णानुक्रमिक स्थिति है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: " <p>4. Select the number that can replace the question mark (?) in the following series.  </span></p> <p><span style=\"font-family:Times New Roman\">4, 11, 31, 65, 193, ? </span></p>",
                    question_hi: " <p>4. उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकती है। </span></p> <p><span style=\"font-family:Times New Roman\">4, 11, 31, 65, 193, ? </span></p>",
                    options_en: [" <p> 396 </span></p>", " <p> 369 </span></p>", 
                                " <p> 389 </span></p>", " <p> 398 </span></p>"],
                    options_hi: [" <p> 396 </span></p>", " <p> 369 </span></p>",
                                " <p> 389 </span></p>", " <p> 398 </span></p>"],
                    solution_en: " <p>4.(c)</span></p> <p><span style=\"font-family:Times New Roman\">Logic : ×2+3, ×3-2 is repeated </span></p> <p><span style=\"font-family:Times New Roman\">So, 193×2 +3 = 386+3=389.</span></p>",
                    solution_hi: " <p>4.(c)</span></p> <p><span style=\"font-family:Times New Roman\">Logic : ×2+3, ×3-2 is repeated </span></p> <p><span style=\"font-family:Baloo\">इसलिए, 193×2 +3 = 386+3=389.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: " <p>5. Pointing towards Kapil, Aditya said, \"His father is the husband of my father\'s father\'s daughter\". How is Aditya’s father related to Kapil\'s mother?  </span></p>",
                    question_hi: " <p>5. कपिल की ओर इशारा करते हुए, आदित्य ने कहा, \"उसके पिता मेरे पिता के पिता की बेटी के पति हैं\"। आदित्य के पिता का कपिल की मां से क्या संबंध है?  </span></p>",
                    options_en: [" <p> Father </span></p>", " <p> Husband </span></p>", 
                                " <p> Son </span></p>", " <p> Brother </span></p>"],
                    options_hi: [" <p> पिता </span></p>", " <p> पति</span></p>",
                                " <p> सोन</span></p>", " <p> भाई</span></p>"],
                    solution_en: " <p>5.(d) According to the following family diagram, Aditya’s father is the brother of Kapil\'s mother.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image8.png\"/></p>",
                    solution_hi: " <p>5.(d) निम्नलिखित परिवार आरेख के अनुसार, आदित्य के पिता कपिल की माता के भाई हैं।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image8.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: " <p>6. Select the correct combination of mathematical signs to sequentially replace the * signs to balance the given equation. </span></p> <p><span style=\"font-family:Times New Roman\">1496 * 8 * 13 * 40 * 5 = 0 </span></p>",
                    question_hi: " <p>6. दिए गए समीकरण को संतुलित करने के लिए * चिह्नों को क्रमिक रूप से बदलने के लिए गणितीय चिह्नों के सही संयोजन का चयन करें। </span></p> <p><span style=\"font-family:Times New Roman\">1496 * 8 * 13 * 40 * 5 = 0 </span></p>",
                    options_en: [" <p> ÷, +, −, ×  </span></p>", " <p> +, ÷, −, × </span></p>", 
                                " <p> −, +, ÷, × </span></p>", " <p> +, ÷, ×, − </span></p>"],
                    options_hi: [" <p> ÷, +, −, ×  </span></p>", " <p> +, ÷, −, × </span></p>",
                                " <p> −, +, ÷, × </span></p>", " <p> +, ÷, ×, − </span></p>"],
                    solution_en: " <p>6.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Putting the first options in the question</span></p> <p><span style=\"font-family:Times New Roman\">1496÷8+13-40×5=0</span></p> <p><span style=\"font-family:Times New Roman\">187+13-200=0</span></p> <p><span style=\"font-family:Times New Roman\">200-200= 0</span></p> <p><span style=\"font-family:Times New Roman\">Hence Verified.</span></p>",
                    solution_hi: " <p>6.(a)</span></p> <p><span style=\"font-family:Baloo\">प्रश्न में पहला विकल्प रखने पर,</span></p> <p><span style=\"font-family:Times New Roman\">1496÷8+13-40×5=0</span></p> <p><span style=\"font-family:Times New Roman\">187+13-200=0</span></p> <p><span style=\"font-family:Times New Roman\">200-200= 0</span></p> <p><span style=\"font-family:Times New Roman\">Hence Verified.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: " <p>7. A paper is folded and cut as shown below. How will it appear when unfolded?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image20.png\"/></p>",
                    question_hi: " <p>7. एक कागज को मोड़ा और काटा जाता है जैसा कि नीचे दिखाया गया है, खोलने पर यह कैसे दिखाई देगा?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image20.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image7.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image31.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image5.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image17.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image7.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image31.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image5.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image17.png\"/></p>"],
                    solution_en: " <p>7.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image21.png\"/></p>",
                    solution_hi: " <p>7.(b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image21.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: " <p>8. Three statements are given, followed by four conclusions numbered I, II, III and IV. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.  </span></p> <p><span style=\"font-family:Times New Roman\">Statements:  </span></p> <p><span style=\"font-family:Times New Roman\">1. All carpets are beds.  </span></p> <p><span style=\"font-family:Times New Roman\">2. Some tables are carpets.  </span></p> <p><span style=\"font-family:Times New Roman\">3. All pillows are tables.  </span></p> <p><span style=\"font-family:Times New Roman\">Conclusions:  </span></p> <p><span style=\"font-family:Times New Roman\">I. All pillows are beds. </span></p> <p><span style=\"font-family:Times New Roman\">II. Some pillows are beds.  </span></p> <p><span style=\"font-family:Times New Roman\">III. Some beds are tables.  </span></p> <p><span style=\"font-family:Times New Roman\">IV. All tables are beds.  </span></p>",
                    question_hi: " <p>8. तीन कथन दिए गए हैं, जिसके बाद चार निष्कर्ष I, II, III और IV दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्णय लें कि कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।</span></p> <p><span style=\"font-family:Baloo\">कथन:</span></p> <p><span style=\"font-family:Baloo\">1. सभी कार्पेट बेड हैं।</span></p> <p><span style=\"font-family:Baloo\">2. कुछ टेबल कालीन हैं।</span></p> <p><span style=\"font-family:Baloo\">3. सभी पिलो टेबल हैं।</span></p> <p><span style=\"font-family:Baloo\">निष्कर्ष:</span></p> <p><span style=\"font-family:Baloo\">I. सभी पिलो बेड हैं।</span></p> <p><span style=\"font-family:Baloo\">II. कुछ तकिए बिस्तर हैं।</span></p> <p><span style=\"font-family:Baloo\">III. कुछ बेड टेबल हैं।</span></p> <p><span style=\"font-family:Baloo\">IV. सभी टेबल बेड हैं।</span></p>",
                    options_en: [" <p> Both conclusions II and III follow . </span></p>", " <p> Only conclusion I follows . </span></p>", 
                                " <p> Both conclusions I and II follow . </span></p>", " <p> Only conclusion III follows .</span></p>"],
                    options_hi: [" <p> निष्कर्ष II और III दोनों अनुसरण करते हैं।</span></p>", " <p> केवल निष्कर्ष I अनुसरण करता है।</span></p>",
                                " <p> निष्कर्ष I और II दोनों अनुसरण करते हैं।</span></p>", " <p> केवल निष्कर्ष III अनुसरण करता है।</span></p>"],
                    solution_en: " <p>8.(d) According to the following diagram, only conclusion III follows.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image9.png\"/></p>",
                    solution_hi: " <p>8.(d) निम्नलिखित आरेख के अनुसार, केवल निष्कर्ष III अनुसरण करता है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image9.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: " <p>9. Which option represents the correct order of the given words as they would appear in </span></p> <p><span style=\"font-family:Times New Roman\">English dictionary?  </span></p> <p><span style=\"font-family:Times New Roman\">1. Rumoured </span></p> <p><span style=\"font-family:Times New Roman\">2. Rudiment </span></p> <p><span style=\"font-family:Times New Roman\">3. Rumanian </span></p> <p><span style=\"font-family:Times New Roman\">4. Royalties </span></p> <p><span style=\"font-family:Times New Roman\">5. Rigorous </span></p>",
                    question_hi: " <p>9. कौन सा विकल्प दिए गए शब्दों के सही क्रम का प्रतिनिधित्व करता है जैसा कि वे अंग्रेजी शब्दकोश में दिखाई देंगे?</span></p> <p><span style=\"font-family:Times New Roman\">1. Rumoured </span></p> <p><span style=\"font-family:Times New Roman\">2. Rudiment </span></p> <p><span style=\"font-family:Times New Roman\">3. Rumanian </span></p> <p><span style=\"font-family:Times New Roman\">4. Royalties </span></p> <p><span style=\"font-family:Times New Roman\">5. Rigorous </span></p>",
                    options_en: [" <p> 4, 5, 2, 3, 1          </span></p>", " <p> 4, 5, 2, 1, 3 </span></p>", 
                                " <p> 5, 4, 2, 1, 3           </span></p>", " <p> 5, 4, 2, 3, 1 </span></p>"],
                    options_hi: [" <p> 4, 5, 2, 3, 1           </span></p>", " <p> 4, 5, 2, 1, 3 </span></p>",
                                " <p> 5, 4, 2, 1, 3           </span></p>", " <p> 5, 4, 2, 3, 1 </span></p>"],
                    solution_en: " <p>9.(d)</span></p> <p><span style=\"font-family:Times New Roman\">Correct order is :5, 4, 2, 3, 1</span></p> <p><span style=\"font-family:Cardo\">Rigorous→Royalties→Rudiment→Rumanian→Rumoured.</span></p>",
                    solution_hi: " <p>9.(d)</span></p> <p><span style=\"font-family:Baloo\">सही क्रम है: 5, 4, 2, 3, 1</span></p> <p><span style=\"font-family:Cardo\">Rigorous→Royalties→Rudiment→Rumanian→Rumoured.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: " <p>10. Select the venn diagram that best illustrates the relationship between the following classes.</span></p> <p><span style=\"font-family:Times New Roman\">Rats , Cats , Mammals</span></p>",
                    question_hi: " <p>10. वेन आरेख का चयन करें जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है।</span></p> <p><span style=\"font-family:Baloo\">चूहे, बिल्लियाँ, स्तनधारी</span></p>",
                    options_en: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image25.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image19.png\"/></p>", 
                                " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image10.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image4.png\"/></p>"],
                    options_hi: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image25.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image19.png\"/></p>",
                                " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image10.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image4.png\"/></p>"],
                    solution_en: " <p>10.(c)</span></p> <p><span style=\"font-family:Times New Roman\">Both </span><span style=\"font-family:Times New Roman\">rat</span><span style=\"font-family:Times New Roman\"> and </span><span style=\"font-family:Times New Roman\">cat</span><span style=\"font-family:Times New Roman\"> are </span><span style=\"font-family:Times New Roman\">mammal</span><span style=\"font-family:Times New Roman\">. But there is no direct relationship between rats and cats.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image2.png\"/></p>",
                    solution_hi: " <p>10.(c)</span></p> <p><span style=\"font-family:Baloo\">चूहा और बिल्ली दोनों स्तनधारी हैं। लेकिन चूहों और बिल्लियों के बीच कोई सीधा संबंध नहीं है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image2.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: " <p>11. Select the option in which the given figure is embedded(rotation is not allowed).</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image32.png\"/></p>",
                    question_hi: " <p>11. उस विकल्प का चयन करें जिसमें दी गई आकृति अंतःस्थापित है (रोटेशन की अनुमति नहीं है)।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image32.png\"/></p>",
                    options_en: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image23.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image13.png\"/></p>", 
                                " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image33.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image11.png\"/></p>"],
                    options_hi: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image23.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image13.png\"/></p>",
                                " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image33.png\"/></p>", " <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image11.png\"/></p>"],
                    solution_en: " <p>11.(b) </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image1.png\"/></p>",
                    solution_hi: " <p>11.(b) </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image1.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Two numbers, A and B, are such that the sum of 3% of A and 6% of B is four-fifth of the sum of 4% of A and 6% of B. Find the ratio of A + B and A &ndash; B?</p>",
                    question_hi: "<p>12. दो संख्याएँ, A और B, ऐसी हैं कि A के 3% और B के 6% का योग A के 4% और B के 6% के योग का चार-पांचवां है। A + B और A - B का अनुपात ज्ञात कीजिए?</p>",
                    options_en: ["<p>4 : 5</p>", "<p>6 : 5</p>", 
                                "<p>7 : 5</p>", "<p>5 : 6</p>"],
                    options_hi: ["<p>4 : 5</p>", "<p>6 : 5</p>",
                                "<p>7 : 5</p>", "<p>5 : 6</p>"],
                    solution_en: "<p>12.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">3% of A + 6% of B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">(4% of A + 6% of B)15% of A + 30% of B = 16% of A + 24% of&nbsp; B6% of B = 1% of A</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A : B = 6 : 1 A +B : A - B = 6+1 : 6-1 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 7 : 5</span></p>",
                    solution_hi: "<p>12.(c)</p>\r\n<p><span style=\"font-family: Baloo;\">A का 3% B का 6% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> (A का 6% B का 6%) A का 15% B का 30% = A का 16% B का 24% B का 6% = A का 1%</span></p>\r\n<p><span style=\"font-family: Baloo;\">A : B = 6: 1, A+B : A - B= 6+1 : 6-1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 7 : 5</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the set in which the numbers are related in the same way as are the numbers of the following set.</p>\r\n<p>(13, 37, 77)</p>",
                    question_hi: " <p>13. उस समुच्चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित समुच्चय की संख्याएँ संबंधित हैं।</span></p> <p> </span></p>",
                    options_en: ["<p>(15, 43, 83) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(12, 38, 79)</p>", 
                                "<p>(14, 40, 83) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(17, 53, 109)</p>"],
                    options_hi: [" <p> (15, 43, 83) </span><span style=\"font-family:Times New Roman\">       </span></p>", " <p> (12, 38, 79) </span></p>",
                                " <p> (14, 40, 83) </span><span style=\"font-family:Times New Roman\">       </span></p>", " <p> (17, 53, 109) </span></p>"],
                    solution_en: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic is : n : 3n -2 : 6n-1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Similarly 14 : 42-2 : 84-1 = 14 : 40 : 83.</span></p>",
                    solution_hi: " <p>13.(c)</span></p> <p><span style=\"font-family:Times New Roman\">Logic is : n : 3n -2 : 6n-1</span></p> <p><span style=\"font-family:Baloo\">उसी प्रकार, 14 : 42-2 : 84-1</span></p> <p><span style=\"font-family:Times New Roman\"> = 14 : 40 : 83.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: " <p>14. Three of the following four words are alike in a certain way and one is different. Select the odd one. </span></p>",
                    question_hi: " <p>14. निम्नलिखित चार शब्दों में से तीन एक निश्चित तरीके से एक जैसे हैं और एक अलग है। विषम का चयन करें।</span></p>",
                    options_en: [" <p> Contention </span><span style=\"font-family:Times New Roman\">      </span></p>", " <p> Turmoil </span></p>", 
                                " <p> Conflict </span><span style=\"font-family:Times New Roman\">      </span></p>", " <p> Tranquility </span></p>"],
                    options_hi: [" <p> विवाद</span><span style=\"font-family:Baloo\">  </span></p>", " <p> अशांति</span></p>",
                                " <p> संघर्ष</span><span style=\"font-family:Baloo\">  </span></p>", " <p> शांति</span></p>"],
                    solution_en: " <p>14.(d)</span></p> <p><span style=\"font-family:Times New Roman\">All other options except tranquil are synonyms of each other but tranquilizer is an antonym.</span></p>",
                    solution_hi: " <p>14.(d)</span></p> <p><span style=\"font-family:Baloo\">tranquil को छोड़कर अन्य सभी विकल्प एक दूसरे के पर्यायवाची हैं लेकिन tranquilizer एक विलोम है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the set in which the numbers are related in the same way as are the numbers of the following set.</p>\r\n<p>(4, 140, 6)</p>",
                    question_hi: "<p><span style=\"font-family: Baloo;\">15. उस समुच्चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित समुच्चय की संख्याएँ संबंधित हैं। </span></p>\r\n<p><span style=\"font-weight: 400;\">(4, 140, 6)</span></p>",
                    options_en: ["<p>(9, 396, 4) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(2, 121, 6)</p>", 
                                "<p>(5, 234, 7) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(3, 269, 8)</p>"],
                    options_hi: ["<p>(9, 396, 4) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(2, 121, 6)</p>",
                                "<p>(5, 234, 7) <span style=\"font-family: Times New Roman;\"> </span></p>", "<p>(3, 269, 8)</p>"],
                    solution_en: "<p>15.(c) (4, 140, 6)</p>\r\n<p>(4 &times; 6 )= 24, (4 + 6 &divide; 2), 24 &times; 5 = 120, 6 + 4 &times; 2 = 20</p>\r\n<p>120 + 20 = 140</p>\r\n<p>(5, 234, 7)</p>\r\n<p>(5 &times; 7 = 35, 35 &times; 6 = 210</p>\r\n<p>5 + 7 &times; 2 = 24</p>\r\n<p>210 + 24 = 234&nbsp;</p>",
                    solution_hi: "<p>15.(c)</p>\r\n<p><span style=\"font-weight: 400;\">(4,140,6)</span></p>\r\n<p><span style=\"font-weight: 400;\">(4&times;</span><span style=\"font-weight: 400;\">6=24, </span><span style=\"font-weight: 400;\">(4+6&divide;</span><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\"> ),24&times;</span><span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\">)=120, 6+4&times;</span><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">=20</span></p>\r\n<p><span style=\"font-weight: 400;\">120+20=140</span></p>\r\n<p><span style=\"font-weight: 400;\">(5,234,7)</span></p>\r\n<p><span style=\"font-weight: 400;\">(5&times;</span><span style=\"font-weight: 400;\">7=35, 35&times;</span><span style=\"font-weight: 400;\">6</span><span style=\"font-weight: 400;\">= 210,&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">5+7&times;</span><span style=\"font-weight: 400;\">2=24</span></p>\r\n<p><span style=\"font-weight: 400;\">210+24=234</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: " <p>16. Select the word-pair that best represents a similar relationship to the one expressed in the given word-pair.  </span></p> <p><span style=\"font-family:Times New Roman\">Spanner : Grip </span></p>",
                    question_hi: " <p>16. उस शब्द-युग्म का चयन करें जो दिए गए शब्द-युग्म में व्यक्त किए गए समान संबंध को सर्वोत्तम रूप से दर्शाता है।</span></p> <p><span style=\"font-family:Baloo\">स्पैनर  : ग्रिप  </span></p>",
                    options_en: [" <p> Axe : Dig </span></p>", " <p> Tongs : Guard </span></p>", 
                                " <p> Auger : Bore </span></p>", " <p> Carve : Chisel </span></p>"],
                    options_hi: [" <p> कुल्हाड़ी: दिग</span></p>", " <p> चिमटे: गार्ड</span></p>",
                                " <p>ऑगर : बोर</span></p>", " <p> नक्काशी: छेनी</span></p>"],
                    solution_en: " <p>16.(c)</span></p> <p><span style=\"font-family:Times New Roman\">As, Spanner is used to provide grip similarly Auger is an instrument used to do boring.</span></p>",
                    solution_hi: " <p>16.(c)</span></p> <p><span style=\"font-family:Baloo\">जिस प्रकार स्पैनर का उपयोग ग्रिप प्रदान करने के लिए किया जाता है उसी प्रकार ऑगर का उपयोग बोरिंग करने के लिए किया जाता है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Three of the following four number-pairs are alike in a certain way and one is different. Select the one that is different.</p>",
                    question_hi: "<p>17. निम्नलिखित चार संख्या-जोड़ों में से तीन एक निश्चित तरीके से समान हैं और एक अलग है। जो अलग है उसे चुनें।</p>",
                    options_en: ["<p>4 : 48</p>", "<p>5 : 105</p>", 
                                "<p>7 : 294</p>", "<p>6 : 180</p>"],
                    options_hi: ["<p>4 : 48</p>", "<p>5 : 105</p>",
                                "<p>7 : 294</p>", "<p>6 : 180</p>"],
                    solution_en: "<p>17.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic : n : n&sup3;</span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">- n&sup2;</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">But 5 : 105 does not follow this logic as 5&sup3;</span><span style=\"font-family: Times New Roman;\">-5&sup2;</span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">= 125-25 = 100 (not 105) </span></p>",
                    solution_hi: "<p>17.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic : n : n&sup3;</span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">- n&sup2;</span></p>\r\n<p><span style=\"font-family: Baloo;\">लेकिन 5 : 105 इस तर्क का पालन नहीं करते क्योंकि 5&sup3;</span><span style=\"font-family: Times New Roman;\">&nbsp;</span><span style=\"font-family: Times New Roman;\">- 5&sup2;</span><span style=\"font-family: Baloo;\"> = 125-25 = 100(105 नहीं)</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: " <p>18. ‘Awl’ is related to ‘Cobbler’ and ‘Chisel’ is related to ‘Sculptor’ in the same way as ‘Anvil’ is related to \'______\'. </span></p>",
                    question_hi: " <p>18. \'अवल\' का संबंध \'मोची\' से है और \'छेनी\' का संबंध \'मूर्तिकार\' से है, ठीक उसी प्रकार जैसे \'एनविल\' का संबंध \'________\' से है।</span></p>",
                    options_en: [" <p> Carpenter </span></p>", " <p> Surgeon </span></p>", 
                                " <p> Butcher </span></p>", " <p> Blacksmith </span></p>"],
                    options_hi: [" <p> बढ़ई</span></p>", " <p> सर्जन</span></p>",
                                " <p> कसाई</span></p>", " <p> लोहार</span></p>"],
                    solution_en: " <p>18.(d)</span></p> <p><span style=\"font-family:Times New Roman\">As, Cobbler uses Awl to stitch the footwear, Sculptor uses the chisel to make the idol similarly Anvil is used by the Blacksmith to make different products.</span></p>",
                    solution_hi: " <p>18.(d)</span></p> <p><span style=\"font-family:Baloo\">जिस प्रकार मोची जूते की सिलाई के लिए Awl का उपयोग करता है, मूर्तिकार मूर्ति बनाने के लिए छेनी का उपयोग करता है उसी प्रकार लोहार द्वारा विभिन्न उत्पादों को बनाने के लिए Anvil का उपयोग करता है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. If STROKE is coded as 2317131089 and GLUTEN is coded as 1423871621, then how&nbsp;<span style=\"font-family: Times New Roman;\">will&nbsp;CHARGE&nbsp;be coded as? </span></p>",
                    question_hi: " <p>19. यदि STROKE को 2317131089 और GLUTEN को 1423871621 के रूप में कोडित किया जाता है, तो CHARGE को कैसे कोडित किया जाएगा? </span></p>",
                    options_en: ["<p>231220272052</p>", "<p>232110270225</p>", 
                                "<p>230211722025</p>", "<p>232110272025</p>"],
                    options_hi: [" <p> 231220272052 </span></p>", " <p> 232110270225 </span></p>",
                                " <p> 230211722025 </span></p>", " <p> 232110272025 </span></p>"],
                    solution_en: "<p>19.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic : reverse the word code as per their reverse alphabetical positioning +1</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">CHARGE on reversing EGRAHC reverse alphabetical order is 22-20-9-26-19-24</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">On adding 1 to it 23-21-10-27-20-25 is the correct answer.</span></p>",
                    solution_hi: " <p>19.(d)</span></p> <p><span style=\"font-family:Baloo\">Logic : शब्द कोड को उनकी विपरीत वर्णमाला स्थिति के अनुसार उलट दें  और +1 करे </span></p> <p><span style=\"font-family:Baloo\">CHARGE को उलटने पर EGRAHC = विपरीत वर्णमाला क्रम = 22-20-9-26-19-24</span></p> <p><span style=\"font-family:Baloo\">इसमें 1 जोड़ने पर 23-21-10-27-20-25 सही उत्तर है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Find the number of triangles in the following figure.</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image16.png\" /></p>",
                    question_hi: "<p>20. निम्नलिखित आकृति में त्रिभुजों की संख्या ज्ञात कीजिए |</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image16.png\" /></p>",
                    options_en: ["<p>32</p>", "<p>33</p>", 
                                "<p>30</p>", "<p>31</p>"],
                    options_hi: ["<p>32</p>", "<p>33</p>",
                                "<p>30</p>", "<p>31</p>"],
                    solution_en: "<p>20.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image22.png\" width=\"158\" height=\"143\" /></p>\r\n<p><span style=\"font-family: Times New Roman;\">There are 32 triangles in the given figure that are (ard, are, ade, aqf, aqg, afg, ajb, ajc, abc, abt, aht, hpn, hpm, hmn, hfa, hfb, ahb, aht, hjk, hjb, hbk, sgt, gol, hqf, tbc, tba, som, fmn, hmf, fkb, mtl &amp; mso).</span></p>",
                    solution_hi: "<p>20.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image22.png\" width=\"188\" height=\"170\" /></p>\r\n<p><span style=\"font-family: Baloo;\">दी गई आकृति में 32 त्रिभुज हैं जो हैं = (ard, are, ade, aqf, aqg, afg, ajb, ajc, abc, abt, aht, hpn, hpm, hmn, hfa, hfb, ahb, aht, hjk, hjb, hbk, sgt, gol, hqf, tbc, tba, som, fmn, hmf, fkb, mtl &amp; mso).</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: " <p>21. If the given figure is folded to form a cube, which number will be on the  face opposite to one having “1”?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image29.png\"/></p>",
                    question_hi: " <p>21. यदि दी गई आकृति को मोड़कर एक घन बनाया जाए, तो \"1\" वाले के विपरीत फलक पर कौन-सी संख्या होगी?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image29.png\"/></p>",
                    options_en: [" <p> 4</span></p>", " <p> 2</span></p>", 
                                " <p> 3</span></p>", " <p> 5</span></p>"],
                    options_hi: [" <p> 4</span></p>", " <p> 2</span></p>",
                                " <p> 3</span></p>", " <p> 5</span></p>"],
                    solution_en: " <p>21.(b)</span></p> <p><span style=\"font-family:Cardo\">After folding the paper in cubic shape, the opposite pairs will be 4 ↔ 6, 1 ↔ 2 and 5 ↔ 3. </span></p>",
                    solution_hi: " <p>21.(b)</span></p> <p><span style=\"font-family:Arial Unicode MS\">कागज को घन आकार में मोड़ने के बाद, विपरीत जोड़े होंगे = 4 ↔ 6, 1 ↔ 2 and 5 ↔ 3.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: " <p>22. Select the option that is related to the third letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster.  </span></p> <p><span style=\"font-family:Times New Roman\">PRINT : TWRIP : : CLOSE : ? </span></p>",
                    question_hi: " <p>22. उस विकल्प का चयन करें जो तीसरे अक्षर-समूह से उसी प्रकार संबंधित है जैसे दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है।  </span></p> <p><span style=\"font-family:Times New Roman\">PRINT : TWRIP : : CLOSE : ? </span></p>",
                    options_en: [" <p> EQLNC </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> EOKHC </span></p>", 
                                " <p> EQKNC </span><span style=\"font-family:Times New Roman\">  </span></p>", " <p> EPLMC </span></p>"],
                    options_hi: [" <p> EQLNC </span></p>", " <p> EOKHC </span></p>",
                                " <p> EQKNC </span></p>", " <p> EPLMC </span></p>"],
                    solution_en: " <p>22.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Logic : first and last letter are interchanged third letter is written in reverse alphabetical order and 5 is added and subtracted from the second and fourth letter respectively</span></p> <p><span style=\"font-family:Times New Roman\">C-L-O-S-E is E-(L+5 =Q)-L-(S-5=N)-V</span></p> <p><span style=\"font-family:Times New Roman\">So EQLNV is the correct answer.</span></p>",
                    solution_hi: " <p>22.(a)</span></p> <p><span style=\"font-family:Baloo\">Logic : पहले और अंतिम अक्षर को आपस में बदल दिया जाता है, तीसरे अक्षर को उल्टे वर्णानुक्रम में लिखा जाता है और दूसरे और चौथे अक्षर से क्रमशः 5 जोड़ा और घटाया जाता है। </span></p> <p><span style=\"font-family:Times New Roman\">C-L-O-S-E is E-(L+5 =Q)-L-(S-5=N)-V</span></p> <p><span style=\"font-family:Baloo\">तो EQLNV सही उत्तर है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the correct mirror image of the given combination when the mirror is placed to the right side of it.</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image6.png\" /></p>",
                    question_hi: " <p>23. दिए गए संयोजन की सही दर्पण छवि का चयन करें जब दर्पण को इसके दाईं ओर रखा जाए।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image6.png\"/></p>",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image18.png\" width=\"197\" height=\"21\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image30.png\" /></p>", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image26.png\" /></p>", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image14.png\" /></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image18.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image30.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image26.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image14.png\"/></p>"],
                    solution_en: "<p>23.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image27.png\" /></p>",
                    solution_hi: " <p>23.(a)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image27.png\"/></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: " <p>24. Select the figure that will come in place of the question mark (?) in the following figure series.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image34.png\"/></p>",
                    question_hi: " <p>24. निम्नलिखित आकृति श्रृंखला में प्रश्नवाचक चिन्ह (?) के स्थान पर आने वाली आकृति का चयन कीजिए।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image34.png\"/></p>",
                    options_en: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image28.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image24.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image12.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image15.png\"/></p>"],
                    options_hi: [" <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image28.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image24.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image12.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image15.png\"/></p>"],
                    solution_en: " <p>24.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image3.png\"/></p>",
                    solution_hi: " <p>24.(c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1646912521/word/media/image3.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. In the following question, the second number is related to the first number in some way, choose the alternative which is similarly related to the third number</p>\r\n<p><span style=\"font-family: Times New Roman;\">13 : 196 : : 15 : ? </span></p>",
                    question_hi: "<p>25. निम्नलिखित प्रश्न में, दूसरी संख्या किसी तरह से पहली संख्या से संबंधित है, उस विकल्प का चयन करें जो तीसरी संख्या से समान रूप से संबंधित है।</p>\r\n<p><span style=\"font-family: Times New Roman;\">13 : 196 : : 15 : ? </span></p>",
                    options_en: ["<p>221</p>", "<p>247</p>", 
                                "<p>298</p>", "<p>256</p>"],
                    options_hi: ["<p>221</p>", "<p>247</p>",
                                "<p>298</p>", "<p>256</p>"],
                    solution_en: "<p>25.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic : n : (n+1)&sup2;</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Similarly 15 : 16&sup2; = 256</span></p>",
                    solution_hi: "<p>25.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Logic : n : (n+1)&sup2;</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Baloo;\">उसी प्रकार, 15 : 16&sup2; = 256</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>