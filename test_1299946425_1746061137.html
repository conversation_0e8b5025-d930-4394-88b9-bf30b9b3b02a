<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. 14 is related to 42 following a certain logic. Following the same logic, 26 is related to 78. To which of the following is 47 related, following the same logic ?&nbsp;<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>1. एक निश्चित तर्क का अनुसरण करते हुए 14, 42 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 26, 78 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 47 निम्नलिखित में से किससे संबंधित है ?<br>(<strong>नोट:</strong> संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: [
                        "<p>137</p>",
                        "<p>135</p>",
                        "<p>143</p>",
                        "<p>141</p>"
                    ],
                    options_hi: [
                        "<p>137</p>",
                        "<p>135</p>",
                        "<p>143</p>",
                        "<p>141</p>"
                    ],
                    solution_en: "<p>1.(d)<br><strong>Logic:-</strong> (1<sup>st</sup>no) &times; <strong>3</strong> = 2<sup>nd</sup>no.<br>(14, 42) :- 14 &times;&nbsp;<strong>3</strong> = 42<br>(26, 78) :- 26 &times; <strong>3 </strong>= 78<br>similarly<br>(47, ?) :- 47 &times; <strong>3 </strong>= 141</p>",
                    solution_hi: "<p>1.(d)<br><strong>तर्क:- </strong>(पहली संख्या) &times; <strong>3</strong> = दूसरी संख्या <br>(14, 42) :- 14 &times; <strong>3</strong> = 42<br>(26, 78) :- 26 &times; <strong>3</strong> = 78<br>इसी प्रकार, <br>(47, ?) :- 47 &times; <strong>3 </strong>= 141</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a code language, \'COOL\' is coded as &lsquo;180&rsquo; and \'MAN\' is coded as &lsquo;84&rsquo;. How will &lsquo;QUIET\' be coded in the same language ?</p>",
                    question_hi: "<p>2. एक कूट भाषा में, \'COOL\' को &lsquo;180&rsquo; के रूप में कूटबद्ध किया जाता है और \'MAN\' को &lsquo;84&rsquo; के रूप में कूटबद्ध किया जाता है। उसी भाषा में &lsquo;QUIET\' को कैसे कूटबद्ध किया जाएगा ?</p>",
                    options_en: [
                        "<p>360</p>",
                        "<p>56</p>",
                        "<p>36</p>",
                        "<p>284</p>"
                    ],
                    options_hi: [
                        "<p>360</p>",
                        "<p>56</p>",
                        "<p>36</p>",
                        "<p>284</p>"
                    ],
                    solution_en: "<p>2.(a) <strong>Logic </strong>:- (Sum of the place value of letter) &times; Number of letter<br>COOL :- (3 + 15 + 15 + 12) &times; 4 &rArr; 45 &times; 4 = 180<br>MAN :- (13 + 1 + 14) &times; 3 &rArr; 28 &times; 3 = 84<br>Similarly,<br>QUIET :- (17 + 21 + 9 + 5 + 20) &times; 5 &rArr; 72 &times; 5 = 360</p>",
                    solution_hi: "<p>2.(a)<strong> तर्क :- </strong>(अक्षर के स्थानीय मान का योग) &times; अक्षर की संख्या<br>COOL :- (3 + 15 + 15 + 12) &times; 4 &rArr; 45 &times; 4 = 180<br>MAN :- (13 + 1 + 14) &times; 3 &rArr; 28 &times; 3 = 84<br>इसी प्रकार,<br>QUIET :- (17 + 21 + 9 + 5 + 20) &times; 5 &rArr; 72 &times; 5 = 360</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Which of the following terms will replace the question mark (?) in the given series ?<br>JKT, LJR, NIP, ?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगा ?<br>JKT, LJR, NIP, ?</p>",
                    options_en: [
                        "<p>PHM</p>",
                        "<p>PGO</p>",
                        "<p>OGP</p>",
                        "<p>PHN</p>"
                    ],
                    options_hi: [
                        "<p>PHM</p>",
                        "<p>PGO</p>",
                        "<p>OGP</p>",
                        "<p>PHN</p>"
                    ],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619557618.png\" alt=\"rId4\" width=\"217\" height=\"81\"></p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619557618.png\" alt=\"rId4\" width=\"217\" height=\"81\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement. <br><strong>Statements :</strong> <br>Some tables are gates. <br>All mats are tables. <br>All plates are gates. <br><strong>Conclusion (I) : </strong>All gates are mats. <br><strong>Conclusion (II) :</strong> All tables are plates</p>",
                    question_hi: "<p>4. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं। <br><strong>कथन :</strong> <br>कुछ टेबल, गेट हैं। <br>सभी मैट, टेबल हैं। <br>सभी प्लेट, गेट हैं। <br><strong>निष्कर्ष (I) : </strong>सभी गेट, मैट हैं। <br><strong>निष्कर्ष (II) : </strong>सभी टेबल, प्लेट हैं।</p>",
                    options_en: [
                        "<p>Neither conclusion (I) nor (II) follows</p>",
                        "<p>Both conclusions (I) and (II) follow</p>",
                        "<p>Only conclusion (I) follows.</p>",
                        "<p>Only conclusion (II) follows.</p>"
                    ],
                    options_hi: [
                        "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>",
                        "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>",
                        "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                        "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>"
                    ],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619557855.png\" alt=\"rId5\" width=\"255\" height=\"84\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>4.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558024.png\" alt=\"rId6\" width=\"244\" height=\"80\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558201.png\" alt=\"rId7\" width=\"99\" height=\"88\"></p>",
                    question_hi: "<p>5. दर्पण को MN पर रखे जाने पर दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558201.png\" alt=\"rId7\" width=\"99\" height=\"88\"></p>",
                    options_en: [
                        "<p><strong id=\"docs-internal-guid-fb3ed060-7fff-e979-e088-ab1a590eca13\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeE17kCiq7jMgR5tR1IhE7UA-V_1qpj49-1XfQEce3aFuUH2ZLCng8-VXv413qcFXMrZpe5RXeg9YcPke2-hHhLDr-fxi2M1jTIoLGGmtzQrGWsrVcMEwCTRr0mcX_PynD8SfqX6A?key=zuxCNC4VsmTh8Sqzselljlis\" width=\"118\" height=\"19\"></strong></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558482.png\" alt=\"rId9\" width=\"118\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558599.png\" alt=\"rId10\" width=\"117\" height=\"18\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558718.png\" alt=\"rId11\" width=\"119\" height=\"19\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558365.png\" alt=\"rId8\" width=\"118\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558482.png\" alt=\"rId9\" width=\"118\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558599.png\" alt=\"rId10\" width=\"117\" height=\"18\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558718.png\" alt=\"rId11\" width=\"119\" height=\"19\"></p>"
                    ],
                    solution_en: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558365.png\" alt=\"rId8\" width=\"118\" height=\"19\"></p>",
                    solution_hi: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558365.png\" alt=\"rId8\" width=\"118\" height=\"19\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What should come in place of the question mark (?) in the given series ?<br>3&nbsp; 16&nbsp; 66&nbsp; 201&nbsp; 406&nbsp; ?</p>",
                    question_hi: "<p>6. दी गई श्रृंखला में प्रश्नचिन्ह (?) के स्थान पर क्या आएगा ?<br>3&nbsp; 16&nbsp; 66&nbsp; 201&nbsp; 406&nbsp; ?</p>",
                    options_en: [
                        "<p>409</p>",
                        "<p>410</p>",
                        "<p>411</p>",
                        "<p>412</p>"
                    ],
                    options_hi: [
                        "<p>409</p>",
                        "<p>410</p>",
                        "<p>411</p>",
                        "<p>412</p>"
                    ],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558898.png\" alt=\"rId12\" width=\"290\" height=\"44\"></p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619558898.png\" alt=\"rId12\" width=\"290\" height=\"44\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>456 &times; 4 &minus; 3 &divide; 11 + 70 = ?</p>",
                    question_hi: "<p>7. यदि \'+\' और \'&minus;\' को आपस में बदल दिया जाए तथा \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा ?<br>456 &times; 4 &minus; 3 &divide; 11 + 70 = ?</p>",
                    options_en: [
                        "<p>84</p>",
                        "<p>77</p>",
                        "<p>87</p>",
                        "<p>79</p>"
                    ],
                    options_hi: [
                        "<p>84</p>",
                        "<p>77</p>",
                        "<p>87</p>",
                        "<p>79</p>"
                    ],
                    solution_en: "<p>7.(b)<strong> Logic:- </strong>456 &times; 4 - 3 &divide; 11 + 70 <br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get<br>456 &divide; 4 + 3 &times; 11 - 70<br>114 + 33 - 70 = 77</p>",
                    solution_hi: "<p>7.(b) <strong>तर्क:-</strong> 456 &times; 4 - 3 &divide; 11 + 70 <br>दिए गए निर्देश के अनुसार &lsquo;+&rsquo; और &lsquo;-&rsquo; तथा &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदलने पर हमें प्राप्त होता है<br>456 &divide; 4 + 3 &times; 11 - 70<br>114 + 33 - 70 = 77</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the set in which the numbers are related in the same way as are the numbers of the given sets. (<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g., 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(111, 16, 7)<br>(167, 14, 12)</p>",
                    question_hi: "<p>8. उस समुचय का चयन कीजिए जिसमें संख्याएँ ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएँ संबंधित हैं। (<strong>ध्यान दें:</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(111, 16, 7)<br>(167, 14, 12)</p>",
                    options_en: [
                        "<p>(108, 9, 12)</p>",
                        "<p>(190, 21, 9)</p>",
                        "<p>(131, 12, 11)</p>",
                        "<p>(140, 8, 17)</p>"
                    ],
                    options_hi: [
                        "<p>(108, 9, 12)</p>",
                        "<p>(190, 21, 9)</p>",
                        "<p>(131, 12, 11)</p>",
                        "<p>(140, 8, 17)</p>"
                    ],
                    solution_en: "<p>8.(c)<strong> Logic :- </strong>(2nd number &times; 3rd number) - 1 = 1st number<br>(111 , 16, 7) :- (16&times; 7) - 1 &rArr; 112 -1 = 111<br>(167, 14 ,12) :- (14 &times; 12) - 1 &rArr; 168 - 1 = 167<br>Similarly,<br>(131 , 12, 11) :- (12 &times; 11) - 1 &rArr; 132 - 1 = 131</p>",
                    solution_hi: "<p>8.(c) <strong>तर्क:-</strong> (दूसरी संख्या &times; तीसरी संख्या) - 1 = पहली संख्या<br>(111 , 16, 7) :- (16&times; 7) - 1 &rArr; 112 -1 = 111<br>(167, 14 ,12) :- (14 &times; 12) - 1 &rArr; 168 - 1 = 167<br>इसी प्रकार,<br>(131 , 12, 11) :- (12 &times; 11) - 1 &rArr; 132 - 1 = 131</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain language, \'this is music\' is written as \'Ta Bu Ka\' and \'who touched this\' is written as \'Pi Ka Bi. How is \'this\' written in the given language ?</p>",
                    question_hi: "<p>9. एक निश्चित कूट भाषा में, \'this is music\' को Ta Bu Ka\' लिखा जाता है और who touched this\' को \'Pi Ka Bi\' लिखा जाता है। उस कूट भाषा में \'this\' को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>Bu</p>",
                        "<p>Ta</p>",
                        "<p>Bi</p>",
                        "<p>Ka</p>"
                    ],
                    options_hi: [
                        "<p>Bu</p>",
                        "<p>Ta</p>",
                        "<p>Bi</p>",
                        "<p>Ka</p>"
                    ],
                    solution_en: "<p>9.(d) this is music &rarr; Ta Bu Ka&hellip;&hellip;. (i)<br>who touched this &rarr; Pi Ka Bi&hellip;.. (ii)<br>From (i) and (ii) &lsquo;this &rsquo; and &lsquo;Ka&rsquo; are common. The code of &lsquo;this&rsquo; = &lsquo;Ka&rsquo;.</p>",
                    solution_hi: "<p>9.(d) this is music &rarr; Ta Bu Ka&hellip;&hellip;. (i)<br>who touched this &rarr; Pi Ka Bi&hellip;.. (ii)<br>(i) और (ii) से \'this\' और \'Ka\' उभयनिष्ठ हैं। \'this\' का कूट = \'Ka\'.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. The position of how many letters will remain unchanged if each of the letters in the word SOUNDBAR is arranged in alphabetical order ?</p>",
                    question_hi: "<p>10. यदि शब्द SOUNDBAR के प्रत्येक अक्षर को वर्णमाला के क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों के स्थान अपरिवर्तित रहेगा ?</p>",
                    options_en: [
                        "<p>Two</p>",
                        "<p>Three</p>",
                        "<p>One</p>",
                        "<p>Four</p>"
                    ],
                    options_hi: [
                        "<p>दो</p>",
                        "<p>तीन</p>",
                        "<p>एक</p>",
                        "<p>चार</p>"
                    ],
                    solution_en: "<p>10.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559116.png\" alt=\"rId13\" width=\"171\" height=\"78\"><br>The position of only <strong>one letter </strong>remain unchanged.</p>",
                    solution_hi: "<p>10.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559116.png\" alt=\"rId13\" width=\"171\" height=\"78\"><br>केवल <strong>एक अक्षर</strong> का स्थान अपरिवर्तित रहता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Each of the letters in the word TRAMPOLINE are arranged from left to right in alphabetical order to form a new word. How many letters are there in the English alphabetical series between the alphabet which is fourth from the left and the one which is first from the right in the newly formed word ?</p>",
                    question_hi: "<p>11. शब्द TRAMPOLINE के प्रत्येक अक्षर को एक नया शब्द बनाने के लिए बाएं से दाएं वर्णमाला क्रम में व्यवस्थित किया गया है। नवनिर्मित शब्द में, बाएं से चौथे स्थान वाले अक्षर और दाएं से पहले स्थान वाले अक्षर के मध्य अंग्रेजी वर्णमाला श्रृंखला में कितने अक्षर हैं ?</p>",
                    options_en: [
                        "<p>Six</p>",
                        "<p>Four</p>",
                        "<p>Seven</p>",
                        "<p>Five</p>"
                    ],
                    options_hi: [
                        "<p>छ:</p>",
                        "<p>चार</p>",
                        "<p>सात</p>",
                        "<p>पांच</p>"
                    ],
                    solution_en: "<p>11.(c) <strong>Given :-</strong> TRAMPOLINE. After arranging in the alphabetical order we get<br>AEI<strong>L</strong>MNOPR<strong>T</strong> <br>Fourth from the left is L and first from right is T. Number of letters in between L and T in the alphabetical series are 7.</p>",
                    solution_hi: "<p>11.(c) <strong>दिया गया :- </strong>TRAMPOLINE वर्णमाला क्रम में व्यवस्थित करने पर हमें प्राप्त होता है<br>AEI<strong>L</strong>MNOPR<strong>T</strong> <br>बाएं से चौथा L है और दाएं से पहला T है। <br>वर्णमाला श्रृंखला में L और T के बीच अक्षरों की संख्या 7 है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559294.png\" alt=\"rId14\" width=\"111\" height=\"113\"></p>",
                    question_hi: "<p>12. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559294.png\" alt=\"rId14\" width=\"111\" height=\"113\"></p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>11</p>",
                        "<p>13</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>11</p>",
                        "<p>13</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559459.png\" alt=\"rId15\" width=\"190\" height=\"215\"><br>Total number of triangles = 10 + ABC + ABD + BCD = 13</p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559459.png\" alt=\"rId15\" width=\"190\" height=\"215\"><br>त्रिभुजों की कुल संख्या = 10 + ABC + ABD + BCD = 13</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559578.png\" alt=\"rId16\" width=\"111\" height=\"95\"></p>",
                    question_hi: "<p>13. उस विकल्प आकृति का चयन कीजिए, जिसमें दी गई आकृति (X) उसके एक भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559578.png\" alt=\"rId16\" width=\"111\" height=\"95\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559727.png\" alt=\"rId17\" width=\"103\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559908.png\" alt=\"rId18\" width=\"104\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560055.png\" alt=\"rId19\" width=\"103\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560174.png\" alt=\"rId20\" width=\"106\" height=\"95\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559727.png\" alt=\"rId17\" width=\"103\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619559908.png\" alt=\"rId18\" width=\"104\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560055.png\" alt=\"rId19\" width=\"103\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560174.png\" alt=\"rId20\" width=\"104\" height=\"93\"></p>"
                    ],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560299.png\" alt=\"rId21\" width=\"107\" height=\"103\"></p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560299.png\" alt=\"rId21\" width=\"107\" height=\"103\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Identify the figure in the options that when put in place of the question mark (?) will logically complete the series ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560412.png\" alt=\"rId22\" width=\"356\" height=\"81\"></p>",
                    question_hi: "<p>14. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560412.png\" alt=\"rId22\" width=\"356\" height=\"81\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560524.png\" alt=\"rId23\" width=\"88\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560645.png\" alt=\"rId24\" width=\"89\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560767.png\" alt=\"rId25\" width=\"88\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560881.png\" alt=\"rId26\" width=\"89\" height=\"87\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560524.png\" alt=\"rId23\" width=\"88\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560645.png\" alt=\"rId24\" width=\"89\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560767.png\" alt=\"rId25\" width=\"89\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560881.png\" alt=\"rId26\" width=\"89\" height=\"87\"></p>"
                    ],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560645.png\" alt=\"rId24\" width=\"87\" height=\"75\"></p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619560645.png\" alt=\"rId24\" width=\"87\" height=\"75\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Six numbers 5, 6, 7, 8, 9 and 10 are written on different faces of a dice. Two positions of this dice are shown in the figure below. Find the number on the face opposite to &lsquo;7&rsquo;.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561004.png\" alt=\"rId27\" width=\"137\" height=\"82\"></p>",
                    question_hi: "<p>15. एक पासे के विभिन्न फलकों पर छ: संख्याएँ 5, 6, 7, 8, 9 और 10 लिखी गई हैं। इस पासे की दो स्थितियों को नीचे आकृति में दर्शाया गया है। \'7\' के विपरीत फलक पर संख्या ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561004.png\" alt=\"rId27\" width=\"137\" height=\"82\"></p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>9</p>",
                        "<p>8</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>9</p>",
                        "<p>8</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>15.(d) in both dice two numbers( 8, 9) are common.<br>So, the opposite face of &lsquo;7&rsquo; is &lsquo;10&rsquo;.</p>",
                    solution_hi: "<p>15.(d) दोनों पासों में दो संख्याएँ (8, 9) उभयनिष्ठ हैं।<br>तो, \'7\' का विपरीत फलक \'10\' है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In a certain code language,<br>\'A + B\' means \'A is the mother of B\';<br>\'A - B\' means \'A is the husband of B\';<br>\'A &times; B\' means \'A is the father of B\' and<br>\'A <math display=\"inline\"><mo>&#247;</mo></math> B\' means \'A is the daughter of B\'.<br>Based on the above, how is K related to O if \'K <math display=\"inline\"><mo>&#247;</mo></math> L - M + N &times; O\' ?</p>",
                    question_hi: "<p>16. एक निश्चित कूट भाषा में,<br>\'A + B\' का अर्थ है&rsquo; A, B की माँ है\'; <br>\'A - B\' का अर्थ है&rsquo; A, B का पति है\'; <br>\'A &times; B\' का अर्थ है&rsquo; A, B का पिता है\' और <br>\'A <math display=\"inline\"><mo>&#247;</mo></math> B\' का अर्थ है&rsquo; A, B की पुत्री है\'।<br>उपर्युक्त के आधार पर, यदि \'K <math display=\"inline\"><mo>&#247;</mo></math> L - M + N &times; O\' है, तो K, O से किस प्रकार संबंधित है ?</p>",
                    options_en: [
                        " Sister\'s daughter ",
                        " Father\'s sister  ",
                        " Mother\'s sister ",
                        " Father\'s mother"
                    ],
                    options_hi: [
                        " बहन की पुत्री ",
                        " पिता की बहन ",
                        " माँ की बहन ",
                        " पिता की माँ"
                    ],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561156.png\" alt=\"rId28\" width=\"95\" height=\"150\"><br>K is the sister of O&rsquo;s father.</p>",
                    solution_hi: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561156.png\" alt=\"rId28\" width=\"95\" height=\"150\"><br>K, O के पिता की बहन है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Based on the English alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which is the one that DOES NOT belong to that group ?<br>(<strong>Note:</strong> The odd man out is not based on the number of consonants/vowels or their position in the letter-cluster.)</p>",
                    question_hi: "<p>17. अंग्रेजी वर्णमाला क्रम के आधार पर, निम्नलिखित चार अक्षर-समूहों में से तीन किसी निश्चित तरीके से एकसमान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा अक्षर-समूह उस समूह से संबंधित नहीं है ?<br>(<strong>नोट:</strong> असंगत अक्षर-समूह, व्यंजनों/स्वरों की संख्या या अक्षर-समूह में इनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>QST</p>",
                        "<p>ACD</p>",
                        "<p>TWW</p>",
                        "<p>IKL</p>"
                    ],
                    options_hi: [
                        "<p>QST</p>",
                        "<p>ACD</p>",
                        "<p>TWW</p>",
                        "<p>IKL</p>"
                    ],
                    solution_en: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561275.png\" alt=\"rId29\" width=\"167\" height=\"53\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561398.png\" alt=\"rId30\" width=\"166\" height=\"52\"></p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561516.png\" alt=\"rId31\" width=\"163\" height=\"53\"><br>but </p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561653.png\" alt=\"rId32\" width=\"167\" height=\"50\"></p>",
                    solution_hi: "<p>17.(c)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561275.png\" alt=\"rId29\" width=\"167\" height=\"53\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561398.png\" alt=\"rId30\" width=\"166\" height=\"52\"></p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561516.png\" alt=\"rId31\" width=\"163\" height=\"53\"></p>\n<p>लेकिन&nbsp;</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561653.png\" alt=\"rId32\" width=\"167\" height=\"50\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Which two numbers (not digits) should be interchanged to make the given equation correct ?<br>104 &divide; 8 - 15 + 4 &times; 26 = 21</p>",
                    question_hi: "<p>18. दिए गए समीकरण को सही करने के लिए किन दो संख्याओं (अंक नहीं) को आपस में बदलना चाहिए ?<br>104 &divide; 8 - 15 + 4 &times; 26 = 21</p>",
                    options_en: [
                        "<p>15 and 4</p>",
                        "<p>8 and 26</p>",
                        "<p>8 and 15</p>",
                        "<p>15 and 26</p>"
                    ],
                    options_hi: [
                        "<p>15 और 4</p>",
                        "<p>8 और 26</p>",
                        "<p>8 और 15</p>",
                        "<p>15 और 26</p>"
                    ],
                    solution_en: "<p>18.(b)<br>104 <math display=\"inline\"><mo>&#247;</mo></math> 8 - 15 + 4 &times; 26 = 21<br>By checking all options one by one option (b) satisfies the condition.<br>On interchanging &lsquo;<math display=\"inline\"><mn>8</mn></math>&rsquo; and &lsquo;26&rsquo; as per option (b), we get<br>LHS = 104 <math display=\"inline\"><mo>&#247;</mo></math> 26 - 15 + 4 &times; 8 <br>= 4 <math display=\"inline\"><mo>-</mo></math> 15 + 32 = 21 = RHS</p>",
                    solution_hi: "<p>18.(b)<br>104 <math display=\"inline\"><mo>&#247;</mo></math> 8 - 15 + 4 &times; 26 = 21<br>सभी विकल्पों को एक-एक करके जांचने पर विकल्प (b) शर्त को पूरा करता है।<br>विकल्प (b) के अनुसार \'8\' और \'26\' को आपस में बदलने पर, हमें मिलता है। <br>LHS = 104 <math display=\"inline\"><mo>&#247;</mo></math> 26 - 15 + 4 &times; 8 <br>= 4 <math display=\"inline\"><mo>-</mo></math> 15 + 32 = 21 = RHS</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must NOT be related to each other based on the number of letters / number of consonants / vowels in the word)<br>Immigration : Entrance :: Emigration : ?</p>",
                    question_hi: "<p>19. उस विकल्प का चयन करें, जो तीसरे शब्द से उसी प्रकार संबंधित है, जिस प्रकार दूसरा शब्द, पहले शब्द से&nbsp;संबंधित है। (शब्दों को अर्थपूर्ण हिंदी/ अँग्रेजी शब्दों के रूप में माना जाना चाहिए और शब्द में अक्षरों की संख्या/ व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होना चाहिए)<br>आप्रवासन : प्रवेश :: उत्प्रवासन : ?</p>",
                    options_en: [
                        "<p>Bridge</p>",
                        "<p>Departure</p>",
                        "<p>Passport</p>",
                        "<p>Aeroplane</p>"
                    ],
                    options_hi: [
                        "<p>जोड़ना</p>",
                        "<p>प्रस्थान</p>",
                        "<p>पासपोर्ट</p>",
                        "<p>हवाई-जहाज</p>"
                    ],
                    solution_en: "<p>19.(b)<br>\"Immigration : Entrance\" relates to coming in, while \"Emigration : Departure\" relates to leaving.</p>",
                    solution_hi: "<p>19.(b)<br>\"आप्रवासन : प्रवेश\" का संबंध आने से है, जबकि \"उत्प्रवासन : प्रस्थान\" का संबंध जाने से है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Which figure should replace the question mark (?) if the following series were to be continued ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561790.png\" alt=\"rId33\" width=\"334\" height=\"68\"></p>",
                    question_hi: "<p>20. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561790.png\" alt=\"rId33\" width=\"334\" height=\"68\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561921.png\" alt=\"rId34\" width=\"75\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562060.png\" alt=\"rId35\" width=\"75\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562177.png\" alt=\"rId36\" width=\"75\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562324.png\" alt=\"rId37\" width=\"75\" height=\"74\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619561921.png\" alt=\"rId34\" width=\"75\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562060.png\" alt=\"rId35\" width=\"75\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562177.png\" alt=\"rId36\" width=\"75\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562324.png\" alt=\"rId37\" width=\"75\" height=\"74\"></p>"
                    ],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562324.png\" alt=\"rId37\" width=\"75\" height=\"74\"></p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562324.png\" alt=\"rId37\" width=\"75\" height=\"74\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the option that represents the correct order of the given words as they would appear in an English dictionary.<br>1. Consistence<br>2. Conscience<br>3. Conflagration<br>4. Consequence<br>5. Considerable<br>6. Consignment</p>",
                    question_hi: "<p>21. उस विकल्प का चयन कीजिए, जो दिए गए शब्दों के सही क्रम को निरूपित करता है, जैसे कि वे अँग्रेजी शब्दकोश में दिखाई देते हैं।<br>1. Consistence<br>2. Conscience<br>3. Conflagration<br>4. Consequence<br>5. Considerable<br>6. Consignment</p>",
                    options_en: [
                        "<p>3, 4, 2, 5,6, 1</p>",
                        "<p>4, 3, 2, 5, 6, 1</p>",
                        "<p>3, 2, 4, 5, 6, 1</p>",
                        "<p>4, 2, 3, 5, 1, 6</p>"
                    ],
                    options_hi: [
                        "<p>3, 4, 2, 5,6, 1</p>",
                        "<p>4, 3, 2, 5, 6, 1</p>",
                        "<p>3, 2, 4, 5, 6, 1</p>",
                        "<p>4, 2, 3, 5, 1, 6</p>"
                    ],
                    solution_en: "<p>21.(c) The correct order is :- <br>Conflagration(3) &rarr; Conscience(2) &rarr; Consequence(4) &rarr; Considerable(5) &rarr; Consignment(6) &rarr; Consistence(1)</p>",
                    solution_hi: "<p>21.(c) सही क्रम है:-<br>Conflagration(3) &rarr; Conscience(2) &rarr; Consequence(4) &rarr; Considerable(5) &rarr; Consignment(6) &rarr; Consistence(1)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Which letter-cluster will complete the given series ?<br>FPW, FFPPPWW, FFFFPPPPPWWW, _______</p>",
                    question_hi: "<p>22. कौन सा अक्षर-समूह दी गई श्रृंखला को पूरा करेगा ?<br>FPW, FFPPPWW, FFFFPPPPPWWW, _______</p>",
                    options_en: [
                        "<p>FFFFFFFFPPPPPPPPPWWWWW</p>",
                        "<p>FFFFFFFFPPPPPPPWWWW</p>",
                        "<p>FFFFFFPPPPPPWWWWW</p>",
                        "<p>FFFFFFPPPPPWWWW</p>"
                    ],
                    options_hi: [
                        "<p>FFFFFFFFPPPPPPPPPWWWWW</p>",
                        "<p>FFFFFFFFPPPPPPPWWWW</p>",
                        "<p>FFFFFFPPPPPPWWWWW</p>",
                        "<p>FFFFFFPPPPPWWWW</p>"
                    ],
                    solution_en: "<p>22.(b) FFFFFFFFPPPPPPPWWWW</p>",
                    solution_hi: "<p>22.(b) FFFFFFFFPPPPPPPWWWW</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Manish starts from Point A and drives 12 km towards East. He then takes a left turn, drives 5 km, turns left and drives 30 km. He then takes a left turn and drives 20 km. He takes a final left turn, drives 18 km and stops at Point Q . How far (shortest distance) and towards which direction should he now drive to reach Point A again ? (All turns are 90&deg; turns only.)</p>",
                    question_hi: "<p>23. मनीष बिंदु A से गाड़ी चलाना शुरू करता है और 12 km पूर्व की ओर गाड़ी चलाता है। फिर वह बाएँ मुड़ता है, 5 km गाड़ी चलाता है, बाएँ मुड़ता है और 30 km गाड़ी चलाता है। फिर वह बाएँ मुड़ता है और 20 km गाड़ी चलाता है। वह अंतिम बार बाएँ मुड़ता है, 18 km गाड़ी चलाता है और बिंदु Q पर रुकता है। बिंदु A पर फिर से पहुँचने के लिए उसे कितनी दूर (न्यूनतम दूरी) और किस दिशा में गाड़ी चलानी चाहिए ? (सभी मोड़ केवल 90&deg; मोड़ वाले हैं।)</p>",
                    options_en: [
                        "<p>15 km towards North</p>",
                        "<p>10 km towards North</p>",
                        "<p>15 km towards South</p>",
                        "<p>10 km towards South</p>"
                    ],
                    options_hi: [
                        "<p>उत्तर की ओर 15 km</p>",
                        "<p>उत्तर की ओर 10 km</p>",
                        "<p>दक्षिण की ओर 15 km</p>",
                        "<p>दक्षिण की ओर 10 km</p>"
                    ],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562450.png\" alt=\"rId38\" width=\"231\" height=\"151\"><br>He should drive 15 km towards the north direction to reach point A.</p>",
                    solution_hi: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562450.png\" alt=\"rId38\" width=\"231\" height=\"151\"><br>बिंदु A तक पहुंचने के लिए उसे उत्तर दिशा की ओर 15 km गाड़ी चलानी चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. In a code language, \'DINESH\' is coded as 12-27-42-15-57-24 and \'VIRAT\' is coded as 66-27-54-3-60. How will \'GAUTAM\' be coded in the same language ?</p>",
                    question_hi: "<p>24. एक कूट भाषा में, \'DINESH\' को 12-27-42-15-57-24 के रूप में कूटबद्ध किया जाता है और \'VIRAT\' को 66-27-54-3-60 के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'GAUTAM\' को किस प्रकार कूटबद्ध किया जाएगा ?</p>",
                    options_en: [
                        "<p>29-9-55-47-8-28</p>",
                        "<p>26-7-25-67-4-37</p>",
                        "<p>24-7-61-34-5-31</p>",
                        "<p>21-3-63-60-3-39</p>"
                    ],
                    options_hi: [
                        "<p>29-9-55-47-8-28</p>",
                        "<p>26-7-25-67-4-37</p>",
                        "<p>24-7-61-34-5-31</p>",
                        "<p>21-3-63-60-3-39</p>"
                    ],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562621.png\" alt=\"rId39\" width=\"165\" height=\"94\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562828.png\" alt=\"rId40\" width=\"141\" height=\"96\"></p>\n<p>Similarly,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562972.png\" alt=\"rId41\" width=\"169\" height=\"94\"></p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562621.png\" alt=\"rId39\" width=\"165\" height=\"94\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562828.png\" alt=\"rId40\" width=\"141\" height=\"96\"></p>\n<p>इसी प्रकार,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619562972.png\" alt=\"rId41\" width=\"169\" height=\"94\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. The sequence of folding a paper and the manner in which the folded paper is cut is shown in the following figures. How would this paper look when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563094.png\" alt=\"rId42\" width=\"218\" height=\"113\"></p>",
                    question_hi: "<p>25. निम्नलिखित आकृतियों में एक कागज़ को मोड़ने का क्रम और मुड़े हुए कागज़ को काटने का तरीका दर्शाया गया है। खोले जाने पर यह कागज़ कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563094.png\" alt=\"rId42\" width=\"218\" height=\"113\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563207.png\" alt=\"rId43\" width=\"74\" height=\"103\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563318.png\" alt=\"rId44\" width=\"74\" height=\"109\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563440.png\" alt=\"rId45\" width=\"70\" height=\"110\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563583.png\" alt=\"rId46\" width=\"75\" height=\"117\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563207.png\" alt=\"rId43\" width=\"74\" height=\"103\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563318.png\" alt=\"rId44\" width=\"74\" height=\"110\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563440.png\" alt=\"rId45\" width=\"69\" height=\"109\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563583.png\" alt=\"rId46\" width=\"73\" height=\"114\"></p>"
                    ],
                    solution_en: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563207.png\" alt=\"rId43\" width=\"75\" height=\"104\"></p>",
                    solution_hi: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563207.png\" alt=\"rId43\" width=\"75\" height=\"104\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. With which of the following states is the Bhatiali folk music, which is mostly sung by&nbsp;the fishermen, associated ?</p>",
                    question_hi: "<p>26. भटियाली लोक-संगीत (Bhatiali folk music), जो अधिकतर मछुआरों द्वारा गाया जाता है, निम्नलिखित में से किस राज्य से संबंधित है ?</p>",
                    options_en: [
                        "<p>Kerala</p>",
                        "<p>Andhra Pradesh</p>",
                        "<p>West Bengal</p>",
                        "<p>Odisha</p>"
                    ],
                    options_hi: [
                        "<p>केरल</p>",
                        "<p>आंध्र प्रदेश</p>",
                        "<p>पश्चिम बंगाल</p>",
                        "<p>ओडिशा</p>"
                    ],
                    solution_en: "<p>26.(c) <strong>West Bengal.</strong> Bhatiali music is a river song characterized by its soulful and melancholic tunes, reflecting the lives and struggles of the fishermen community. Other folk music of West Bengal: Baul, Bhawaia, Kirtan, Gambhira, Shyama Sangeet, Lalon Geeti. Other States and their folk music: Kerala - Kathakali Music, Mappila Pattu, Ottamthullal Songs, Sopanam, Kolkali. Andhra Pradesh - Madiga Dappu, Mala Jamidika. Odisha - Avanti, Panchali, Odramagadhi.</p>",
                    solution_hi: "<p>26.(c) <strong>पश्चिम बंगाल। </strong>भटियाली संगीत एक नदी गीत है जिसकी विशेषता इसकी भावपूर्ण और उदासी भरी धुनें हैं, जो मछुआरा समुदाय के जीवन और संघर्ष को दर्शाती हैं। पश्चिम बंगाल के अन्य लोक संगीत: बाउल, भवाईया, कीर्तन, गंभीरा, श्यामा संगीत, ललन गीति। अन्य राज्य और उनके लोक संगीत: केरल - कथकली संगीत, मप्पिला पट्टू, ओट्टमथुलाल गीत, सोपानम, कोलकाली। आंध्र प्रदेश - मडिगा दप्पू, माला जामिदिका। ओडिशा - अवंती, पांचाली, ओड्रमागधी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. National Institute of Mountaineering and Adventure Sports (NIMAS) is located at _______.</p>",
                    question_hi: "<p>27. राष्ट्रीय पर्वतारोहण और साहसिक खेल संस्थान (National Institute of Mountaineering and Adventure Sports - NIMAS) ______में स्थित है।</p>",
                    options_en: [
                        "<p>Nagaland</p>",
                        "<p>Arunachal Pradesh</p>",
                        "<p>Mizoram</p>",
                        "<p>Himachal Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>नागालैंड</p>",
                        "<p>अरुणाचल प्रदेश</p>",
                        "<p>मिजोरम</p>",
                        "<p>हिमाचल प्रदेश</p>"
                    ],
                    solution_en: "<p>27.(b) <strong>Arunachal Pradesh. </strong>NIMAS was established in 2013 and is located in the picturesque Dirang Valley within the West Kameng district of Arunachal Pradesh. Other Mountaineering Institutes in India: Himalayan Mountaineering Institute, Darjeeling (West Bengal). National Institute of Mountaineering and Adventure Sports, Dirang (Arunachal Pradesh). Indian Institute of Skiing and Mountaineering (IISM) Gulmarg (Kashmir). Atal Bihari Vajpayee Institute of Mountaineering and Allied Sports, Manali (Himachal Pradesh).</p>",
                    solution_hi: "<p>27.(b) <strong>अरुणाचल प्रदेश। </strong>NIMAS की स्थापना 2013 में हुई थी और यह अरुणाचल प्रदेश के पश्चिम कामेंग जिले की सुरम्य दिरांग घाटी में स्थित है। भारत में अन्य पर्वतारोहण संस्थान: हिमालयन पर्वतारोहण संस्थान, दार्जिलिंग (पश्चिम बंगाल)। राष्ट्रीय पर्वतारोहण एवं साहसिक खेल संस्थान, दिरांग (अरुणाचल प्रदेश)। भारतीय स्कीइंग एवं पर्वतारोहण संस्थान (IISM) गुलमर्ग (कश्मीर)। अटल बिहारी वाजपेयी पर्वतारोहण एवं संबद्ध खेल संस्थान, मनाली (हिमाचल प्रदेश)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Langvir Nritya is a folk dance from the state of______</p>",
                    question_hi: "<p>28. लंगवीर नृत्य (Langvir Nritya) _____ राज्य का एक लोक नृत्य है।</p>",
                    options_en: [
                        "<p>Gujarat</p>",
                        "<p>Uttar Pradesh</p>",
                        "<p>Uttarakhand</p>",
                        "<p>Haryana</p>"
                    ],
                    options_hi: [
                        "<p>गुजरात</p>",
                        "<p>उत्तर प्रदेश</p>",
                        "<p>उत्तराखंड</p>",
                        "<p>हरियाणा</p>"
                    ],
                    solution_en: "<p>28.(c) <strong>Uttarakhand. </strong>Langvir Nritya is a dance form practiced by only men. The dancers use a long bamboo pole. States and their Folk dances : Gujarat - Garba and Dandiya Raas. Haryana - Phalgun dance, Luur dance, Daf dance, Gugga dance, and Khoria dance. Uttar Pradesh - Charkula, Rasiya, Nautanki.</p>",
                    solution_hi: "<p>28.(c) <strong>उत्तराखंड।</strong> लंगवीर नृत्य एक ऐसी नृत्य शैली है जिसे केवल पुरुषों द्वारा किया जाता है। इसमें नर्तक एक लंबे बांस के खंभे का उपयोग करते हैं। कुछ राज्य और उनके प्रमुख लोक नृत्य : गुजरात - गरबा और डांडिया रास। हरियाणा - फाल्गुन नृत्य, लूर नृत्य, डफ नृत्य, गुग्गा नृत्य और खोरिया नृत्य। उत्तर प्रदेश - चरकुला, रसिया, नौटंकी आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. The peninsular plateau of India was a part of which continent earlier?</p>",
                    question_hi: "<p>29. भारत का प्रायद्वीपीय पठार पहले किस महाद्वीप का भाग था ?</p>",
                    options_en: [
                        "<p>North America</p>",
                        "<p>Europe</p>",
                        "<p>South America</p>",
                        "<p>Africa</p>"
                    ],
                    options_hi: [
                        "<p>उत्तर अमेरिका</p>",
                        "<p>यूरोप</p>",
                        "<p>दक्षिण अमेरिका</p>",
                        "<p>अफ्रीका</p>"
                    ],
                    solution_en: "<p>29.(d) <strong>Africa. </strong>The Peninsular Plateau is roughly triangular in shape. The peninsular plateau of India was a part of the supercontinent Gondwana, which included Africa, South America, Australia, and Antarctica. After breaking up from Gondwana, the Indian subcontinent drifted northwards and collided with Eurasia which formed the Himalayas.</p>",
                    solution_hi: "<p>29.(d) <strong>अफ्रीका।</strong> प्रायद्वीपीय पठार आकार में लगभग त्रिभुजाकार है। भारत का प्रायद्वीपीय पठार गोंडवाना महाद्वीप का हिस्सा था, जिसमें अफ्रीका, दक्षिण अमेरिका, ऑस्ट्रेलिया और अंटार्कटिका शामिल थे। गोंडवाना से अलग होने के बाद, भारतीय उपमहाद्वीप उत्तर की ओर खिसक गया और यूरेशिया से टकराया जिसके परिणामस्वरूप हिमालय का निर्माण हुआ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. How many squares are there on a chessboard ?</p>",
                    question_hi: "<p>30. शतरंज की बिसात पर कितने वर्ग होते हैं ?</p>",
                    options_en: [
                        "<p>67</p>",
                        "<p>68</p>",
                        "<p>64</p>",
                        "<p>69</p>"
                    ],
                    options_hi: [
                        "<p>67</p>",
                        "<p>68</p>",
                        "<p>64</p>",
                        "<p>69</p>"
                    ],
                    solution_en: "<p>30.(c) <strong>64.</strong> A standard chessboard consists of 8 rows and 8 columns, resulting in a total of 64 squares. This is calculated by multiplying the number of rows by the number of columns (8 &times; 8 = 64).</p>",
                    solution_hi: "<p>30.(c) <strong>64.</strong> एक मानक शतरंज बोर्ड में 8 पंक्तियाँ एवं 8 स्तंभ होते हैं, जिसके परिणामस्वरूप कुल 64 वर्ग होते हैं। इसकी गणना पंक्तियों की संख्या को स्तंभों की संख्या से गुणा करके (8 &times; 8 = 64) की जाती है ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. The five-year plans for India with their duration are given below. Which of the following is correctly matched ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563725.png\" alt=\"rId47\" width=\"325\" height=\"64\"> </p>",
                    question_hi: "<p>31. नीचे भारत की पंचवर्षीय योजनाएं उनकी अवधि के साथ दी गई हैं। निम्नलिखित में से कौन-सी सही से सुमेलित हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619563964.png\" alt=\"rId48\" width=\"350\" height=\"88\"></p>",
                    options_en: [
                        "<p>Both b and c</p>",
                        "<p>Both a and b</p>",
                        "<p>Both a and c</p>",
                        "<p>Only a</p>"
                    ],
                    options_hi: [
                        "<p>b और c, दोनों</p>",
                        "<p>a और b, दोनों</p>",
                        "<p>a और c, दोनों</p>",
                        "<p>केवल a</p>"
                    ],
                    solution_en: "<p>31.(b) <strong>Both a and b. </strong>The First Five-Year Plan was launched in 1951. The Second Plan (1956-1961), known as the Mahalanobis Plan, had a target growth rate of 4.5% and achieved 4.3%. The Third Plan (1961-1966) aimed to make the economy self-sufficient. Due to the Indo-China war, Indo-Pakistan war, and severe droughts, a \"Plan Holiday\" was observed from 1966 to 1969. The Fourth Plan (1969-1974), under Indira Gandhi, focused on growth and included Family Planning Programmes.</p>",
                    solution_hi: "<p>31.(b)<strong> a और b, दोनों। </strong>प्रथम पंचवर्षीय योजना 1951 में शुरू की गई थी। दूसरी पंचवर्षीय योजना (1956-1961), जिसे महालनोबिस योजना के नाम से भी जाना जाता है, इस योजना का लक्ष्य 4.5% की वृद्धि दर हासिल करना था, जबकि वास्तविक वृद्धि दर 4.3% रही। तीसरी पंचवर्षीय योजना (1961-1966) का उद्देश्य अर्थव्यवस्था को आत्मनिर्भर बनाना था। भारत-चीन युद्ध, भारत-पाकिस्तान युद्ध और गंभीर सूखे की स्थिति के कारण 1966 से 1969 तक \"योजना अवकाश\" के रूप में जाना जाता है। इंदिरा गांधी के नेतृत्व में चौथी पंचवर्षीय योजना (1969-1974) में आर्थिक विकास पर ध्यान केंद्रित किया गया और साथ ही परिवार नियोजन कार्यक्रमों को भी शामिल किया गया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which country has recently chosen not to join China&rsquo;s Belt and Road Initiative (BRI), becoming the second BRICS member to opt out ?</p>",
                    question_hi: "<p>32. हाल ही में किस देश ने चीन की बेल्ट एंड रोड इनिशिएटिव (BRI) में शामिल होने का निर्णय नहीं लिया, और यह दूसरा BRICS सदस्य बना है जिसने इस परियोजना में भाग लेने से इंकार किया ?</p>",
                    options_en: [
                        "<p>South Africa</p>",
                        "<p>Brazil</p>",
                        "<p>Russia</p>",
                        "<p>India</p>"
                    ],
                    options_hi: [
                        "<p>दक्षिण अफ्रीका</p>",
                        "<p>ब्राजील</p>",
                        "<p>रूस</p>",
                        "<p>भारत</p>"
                    ],
                    solution_en: "<p>32.(b) <strong>Brazil</strong>, led by President Luiz Inacio Lula da Silva and advised by Celso Amorim, opted out of the BRI to explore alternative collaborations with China. This makes Brazil the second BRICS member, after India, to decline participation in China&rsquo;s global infrastructure project.</p>",
                    solution_hi: "<p>32.(b) <strong>ब्राजील। </strong>ब्राजील के राष्ट्रपति लुइज़ इनासियो लुला दा सिल्वा और सलाहकार सेल्सो अमोरिम के नेतृत्व में, ब्राजील ने BRI में शामिल होने से इंकार किया और चीन के साथ वैकल्पिक सहयोगों की खोज करने का निर्णय लिया। इस प्रकार, ब्राजील भारत के बाद दूसरा BRICS सदस्य बना है जिसने इस वैश्विक अवसंरचना परियोजना से बाहर रहने का निर्णय लिया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following is NOT a stringed instrument ?</p>",
                    question_hi: "<p>33. निम्नलिखित में से कौन-सा तार वाला एक वाद्य-यंत्र नहीं है ?</p>",
                    options_en: [
                        "<p>Mandolin</p>",
                        "<p>Guitar</p>",
                        "<p>Flute</p>",
                        "<p>Violin</p>"
                    ],
                    options_hi: [
                        "<p>सारंगी</p>",
                        "<p>गिटार</p>",
                        "<p>बाँसुरी</p>",
                        "<p>वायलिन</p>"
                    ],
                    solution_en: "<p>33.(c) <strong>Flute </strong>is a wind instrument where sound is produced by vibration of the air column inside it. Instruments without strings include the tabla, drums, and harmonium. Instruments with strings include the guitar, cello, banjo, ukulele, and harp.</p>",
                    solution_hi: "<p>33.(c) <strong>बांसुरी</strong> एक वायु वाद्य यंत्र है जिसमें ध्वनि उसके अंदर मौजूद वायु स्तंभ के कंपन से उत्पन्न होती है। बिना तार वाले वाद्य यंत्रों में तबला, ड्रम और हारमोनियम शामिल हैं। तार वाले वाद्य यंत्रों में गिटार, सेलो, बैंजो, यूकुलेले और वीणा शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following dances developed in the monasteries of Assam ?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन सा नृत्य असम के मठों (monasteries) में विकसित हुआ ?</p>",
                    options_en: [
                        "<p>Bharatanatyam</p>",
                        "<p>Kathak</p>",
                        "<p>Sattriya</p>",
                        "<p>Kuchipudi</p>"
                    ],
                    options_hi: [
                        "<p>भरतनाट्यम</p>",
                        "<p>कथक</p>",
                        "<p>सत्रीया</p>",
                        "<p>कुचिपुड़ी</p>"
                    ],
                    solution_en: "<p>34.(c) <strong>Sattriya </strong>originated in the Sattra (monastery) as part of the neo-Vaishnavite movement started by Srimanta Sankardev in the 15th century. He promoted &ldquo;ek sharan naam dharma&rdquo; (devotional chanting of one God&rsquo;s name). In 2000, the Sangeet Natak Akademi recognized Sattriya as a classical dance. Other classical dances of India include Bharatnatyam (Tamil Nadu), Kathakali (Kerala), Kuchipudi (Andhra Pradesh), Kathak (North India), Mohiniyattam (Kerala), Manipuri (Manipur), and Odissi (Odisha).</p>",
                    solution_hi: "<p>34.(c) <strong>सत्रीया </strong>की उत्पत्ति 15वीं शताब्दी में श्रीमंत शंकरदेव द्वारा शुरू किए गए नव-वैष्णव आंदोलन के एक भाग के रूप में सत्रा (मठ) में हुई थी। उन्होंने \"एक शरण नाम धर्म\" (एक भगवान के नाम का भक्तिपूर्वक जप) को बढ़ावा दिया। वर्ष 2000 में, संगीत नाटक अकादमी ने सत्रीया को शास्त्रीय नृत्य के रूप में मान्यता दी। भारत के अन्य शास्त्रीय नृत्यों में भरतनाट्यम (तमिलनाडु), कथकली (केरल), कुचिपुड़ी (आंध्र प्रदेश), कथक (उत्तर भारत), मोहिनीअट्टम (केरल), मणिपुरी (मणिपुर), और ओडिसी (ओडिशा) शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. In the given main characteristics of farming which is NOT a characteristic of the Indian commercial farming ?</p>",
                    question_hi: "<p>35. कृषि संबंधी दी गई मुख्य विशेषताओं में कौन-सी, भारतीय वाणिज्यिक कृषि की विशेषता नहीं है ?</p>",
                    options_en: [
                        "<p>Slash and burn</p>",
                        "<p>Insecticides and pesticides</p>",
                        "<p>Chemical fertilisers</p>",
                        "<p>High yielding variety seeds</p>"
                    ],
                    options_hi: [
                        "<p>झूम खेती</p>",
                        "<p>कीटनाशक और पीड़कनाशी</p>",
                        "<p>रासायनिक उर्वरक</p>",
                        "<p>उच्च पैदावार किस्म की बीजें</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>Slash and burn </strong>farming is a form of shifting agriculture where natural vegetation is cut down and burned to clear the land for cultivation. In commercial farming, higher doses of modern inputs are used to obtain higher productivity. The degree of commercialization in agriculture varies from one region to another. For example, rice is a commercial crop in Haryana and Punjab, but in Odisha, it is a subsistence crop.</p>",
                    solution_hi: "<p>35.(a) <strong>झूम खेती या स्लैश-एंड-बर्न </strong>खेती एक प्रकार की स्थानांतरण कृषि है जिसमें प्राकृतिक वनस्पति को काटकर जला दिया जाता है ताकि कृषि के लिए भूमि को साफ किया जा सके। वाणिज्यिक कृषि में, उच्च उत्पादकता प्राप्त करने के लिए आधुनिक उत्पादक सामग्री की उच्च मात्रा का उपयोग किया जाता है। कृषि में व्यावसायीकरण का स्तर एक क्षेत्र से दूसरे क्षेत्र में भिन्न-भिन्न होती है। उदाहरण के लिए, हरियाणा और पंजाब में चावल एक वाणिज्यिक फसल है, लेकिन ओडिशा में यह एक निर्वाह फसल है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. &lsquo;Hampi Utsava,&rsquo; which is celebrated in Karnataka, is also called ______. The festival that captures the pomp, splendour and glory of the historical period of Karnataka is celebrated over a week.</p>",
                    question_hi: "<p>36. &lsquo;हम्पी उत्सव\', जो कर्नाटक में मनाया जाता है, को ______भी कहा जाता है। कर्नाटक के ऐतिहासिक काल की धूमधाम, वैभव और महिमा को दर्शाने वाला यह त्योहार एक सप्ताह तक मनाया जाता है।</p>",
                    options_en: [
                        "<p>Vijaya Utsava</p>",
                        "<p>Sarhul Mahotsava</p>",
                        "<p>Rajasi Utsava</p>",
                        "<p>Nritya Mahotsav</p>"
                    ],
                    options_hi: [
                        "<p>विजय उत्सव (Vijaya Utsava)</p>",
                        "<p>सरहुल महोत्सव (Sarhul Mahotsava)</p>",
                        "<p>राजसी उत्सव (Rajasi Utsava)</p>",
                        "<p>नृत्य महोत्सव (Nritya Mahotsav)</p>"
                    ],
                    solution_en: "<p>36.(a) <strong>Vijaya Utsava</strong> celebrates the grandeur of the Vijayanagara Empire and is held over a week in Karnataka. Festivals Of Karnataka: Vairamudi Festival, Kambala Festival, Karaga Festival, Ugadi. The Sarhul festival marks the beginning of the New Year and is celebrated by the Oraon, Munda, and Ho tribes of Jharkhand.</p>",
                    solution_hi: "<p>36.(a) <strong>विजय उत्सव </strong>(Vijaya Utsava) विजयनगर साम्राज्य की भव्यता के जश्न का उत्सव है और यह उत्सव कर्नाटक में एक सप्ताह तक चलता है। कर्नाटक के प्रमुख त्यौहार: वैरामुडी महोत्सव, कंबाला महोत्सव, करगा महोत्सव, उगादी। सरहुल त्योहार नए वर्ष की शुरुआत का प्रतीक है और इसे झारखंड के उरांव, मुंडा और हो जनजातियों द्वारा मनाया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. In his &lsquo;Drain of Wealth&rsquo; argument, who among the following stated that Britain was completely draining India ?</p>",
                    question_hi: "<p>37. अपने \'धन निष्कासन (Drain of Wealth)\' तर्क में, निम्नलिखित में से किसने कहा था कि ब्रिटेन भारत को पूरी तरह से खाली कर रहा है ?</p>",
                    options_en: [
                        "<p>Badruddin Tyabji</p>",
                        "<p>Lala Lajpat Rai</p>",
                        "<p>Dadabhai Naoroji</p>",
                        "<p>Bal Gangadhar Tilak</p>"
                    ],
                    options_hi: [
                        "<p>बदरुद्दीन तैयबजी</p>",
                        "<p>लाला लाजपत राय</p>",
                        "<p>दादाभाई नौरोजी</p>",
                        "<p>बाल गंगाधर तिलक</p>"
                    ],
                    solution_en: "<p>37.(c) <strong>Dadabhai Naoroji -</strong> He was known as the \"Grand Old Man of India&rdquo;. He introduced thе \'drain thеory\' in his rеnownеd book \"Povеrty and thе Un-British Rulе in India&rdquo;. Hе argued that India\'s economic resources wеrе systеmatically drainеd to England through tradе, industrialisation, and high salariеs to British officials, all fundеd by Indian monеy. R.C.Dutt, in his book \"Economic History of India\" and G.Subramania Iyer in his book \"Somе Economic Aspеcts of British Rulе in India\" also criticized Colonial economic policies.</p>",
                    solution_hi: "<p>37.(c) <strong>दादाभाई नौरोजी - </strong>इन्हें \"ग्रैंड ओल्ड मैन ऑफ इंडिया \" के रूप में जाना जाता है। उन्होंने अपनी प्रसिद्ध पुस्तक \"पावर्टी एण्ड द अनब्रिटिश रूल इन इंडिया&rdquo; में \'ड्रेन थ्योरी\' का सिद्धांत दिया था। उन्होंने तर्क दिया कि भारत के आर्थिक संसाधनों को व्यापार, औद्योगिकीकरण और ब्रिटिश अधिकारियों को उच्च वेतन के माध्यम से व्यवस्थित रूप से इंग्लैंड में ले जाया गया, जो सभी भारतीय धन से वित्तपोषित थे। आर.सी. दत्त ने अपनी पुस्तक \"इकोनॉमिक हिस्ट्री ऑफ इंडिया\" और जी. सुब्रमण्यम अय्यर ने अपनी पुस्तक \"सम इकोनॉमिक एसपेक्टस आफ ब्रिटिश रूल इन इंडिया\" में भी औपनिवेशिक आर्थिक नीतियों की आलोचना की है ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. How might ICT enhance the openness of e-land records ?</p>",
                    question_hi: "<p>38. ICT ई-भूमि अभिलेखों (e-land records) के खुलेपन को कैसे बढ़ा सकता है ?</p>",
                    options_en: [
                        "<p>Enabling quick search and retrieval of land records</p>",
                        "<p>Facilitating manual record updates and amendments</p>",
                        "<p>Enforcing strict regulations on land transactions</p>",
                        "<p>Reducing the need for land surveys and assessments</p>"
                    ],
                    options_hi: [
                        "<p>भूमि अभिलेखों की त्वरित खोज और पुनर्प्राप्ति को सक्षम बनाना</p>",
                        "<p>मैन्युअल अभिलेखों अद्यतन और संशोधन की सुविधा प्रदान करना</p>",
                        "<p>भूमि लेनदेन पर सख्त नियम लागू करके</p>",
                        "<p>भूमि सर्वेक्षण और कर-निर्धारण की आवश्यकता को कम करना</p>"
                    ],
                    solution_en: "<p>38.(a) Information and Communication Technology (ICT) facilitates the digitization and online availability of land records, making it easier for stakeholders to access information efficiently. ICT systems can provide real-time updates and tracking of land records, reducing delays and increasing the reliability of information. Digital India Land Records Modernization Programme was launched in 2008 by the Ministry of Rural Development to digitize and modernize land records and develop a centralized land record management system.</p>",
                    solution_hi: "<p>38.(a) सूचना और संचार प्रौद्योगिकी (ICT) भूमि अभिलेखों के डिजिटलीकरण और ऑनलाइन उपलब्धता की सुविधा प्रदान करती है, जिससे हितधारकों के लिए सूचना तक कुशलतापूर्वक पहुँचना आसान हो जाता है। ICT सिस्टम भूमि अभिलेखों के वास्तविक समय के अपडेट और ट्रैकिंग प्रदान कर सकते हैं, जिससे देरी कम हो सकती है और सूचना की विश्वसनीयता बढ़ सकती है। ग्रामीण विकास मंत्रालय द्वारा भूमि अभिलेखों को डिजिटल बनाने और आधुनिकीकरण करने तथा एक केंद्रीकृत भूमि अभिलेख प्रबंधन प्रणाली विकसित करने के लिए 2008 में डिजिटल इंडिया भूमि अभिलेख आधुनिकीकरण कार्यक्रम शुरू किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which country do the &lsquo;Veddas&rsquo; traditional forest dwellers who foraged, hunted and lived in close-knit groups in caves in the dense jungles ?</p>",
                    question_hi: "<p>39. \'वेदा (Veddas)\' पारंपरिक वनवासी किस देश के निवासी थे, जो चारा खोजते थे, शिकार करते थे और घने जंगलों में गुफओं में एकजुट समूहों में रहते थे ?</p>",
                    options_en: [
                        "<p>Sri Lanka</p>",
                        "<p>Nepal</p>",
                        "<p>Myanmar</p>",
                        "<p>Bhutan</p>"
                    ],
                    options_hi: [
                        "<p>श्रीलंका</p>",
                        "<p>नेपाल</p>",
                        "<p>म्यांमार</p>",
                        "<p>भूटान</p>"
                    ],
                    solution_en: "<p>39.(a)<strong> Sri Lanka. </strong>Some tribes from different countries : Nepal - Tamang, Newar, Tharu, Rai, Limbu, and Bhutia. Myanmar - Burmese, Rakhine, Kachin, Danu, Lisu. Bhutan - Monpa, Brokpa, Bumthangpa, Tibetans. Pakistan - Pashtuns, Sindhis, Muhajirs.</p>",
                    solution_hi: "<p>39.(a) <strong>श्रीलंका। </strong>विभिन्न देशों की कुछ प्रमुख जनजातियाँ: नेपाल - तमांग, नेवार, थारू, राई , लिम्बु और भूटिया। म्यांमार - बर्मी, राखीन, काचिन, दानू, लिसु। भूटान - मोनपा, ब्रोकपा, बुमथांगपा, तिब्बती। पाकिस्तान - पश्तून, सिंधी, मुहाजिर।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. How is the structure of health infrastructure and health care system in India ?</p>",
                    question_hi: "<p>40. भारत में स्वास्थ्य अवसंरचना और स्वास्थ्य देखभाल प्रणाली की संरचना कैसी है ?</p>",
                    options_en: [
                        "<p>Three-tier system</p>",
                        "<p>Five-tier system</p>",
                        "<p>Two-tier system</p>",
                        "<p>Four-tier system</p>"
                    ],
                    options_hi: [
                        "<p>त्रिस्तरीय प्रणाली</p>",
                        "<p>पाँच-स्तरीय प्रणाली</p>",
                        "<p>दो-स्तरीय प्रणाली</p>",
                        "<p>चार-स्तरीय प्रणाली</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Three-tier system. </strong>The healthcare system in India is a three-tier structure, with Sub Health Centres, Primary Health Centres, and Community Health Centres, both in urban and rural areas, serving as the pillars of the primary health care system. As per established norms, Sub Health Centre: Serves 5,000 people in plains and 3,000 in hilly/tribal areas. Primary Health Centre (PHC): Serves 30,000 people in plains and 20,000 in hilly/tribal areas. Community Health Centre (CHC): Serves 1,20,000 people in plains and 80,000 in hilly/tribal areas.</p>",
                    solution_hi: "<p>40.(a) <strong>त्रिस्तरीय प्रणाली।</strong> भारत में स्वास्थ्य सेवा प्रणाली तीन-स्तरीय संरचना वाली है, जिसमें शहरी और ग्रामीण दोनों क्षेत्रों में उप-स्वास्थ्य केंद्र, प्राथमिक स्वास्थ्य केंद्र और सामुदायिक स्वास्थ्य केंद्र हैं, जो प्राथमिक स्वास्थ्य देखभाल प्रणाली के स्तंभ के रूप में कार्य करते हैं। स्थापित मानदंडों के अनुसार, उप-स्वास्थ्य केंद्र: मैदानी इलाकों में 5,000 और पहाड़ी/आदिवासी क्षेत्रों में 3,000 लोगों को सेवाएं प्रदान कर है। प्राथमिक स्वास्थ्य केंद्र (PHC): मैदानी इलाकों में 30,000 और पहाड़ी/आदिवासी क्षेत्रों में 20,000 लोगों को सेवाएं प्रदान करता है। सामुदायिक स्वास्थ्य केंद्र (CHC): मैदानी इलाकों में 1,20,000 और पहाड़ी/आदिवासी क्षेत्रों में 80,000 लोगों को सेवा प्रदान करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which language is known as &lsquo;Italian of the East&rsquo; ?</p>",
                    question_hi: "<p>41. किस भाषा को \'पूर्व का इतालवी\' कहा जाता है ?</p>",
                    options_en: [
                        "<p>Telugu</p>",
                        "<p>Malayalam</p>",
                        "<p>Marathi</p>",
                        "<p>Kannada</p>"
                    ],
                    options_hi: [
                        "<p>तेलुगु</p>",
                        "<p>मलयालम</p>",
                        "<p>मराठी</p>",
                        "<p>कन्नड़</p>"
                    ],
                    solution_en: "<p>41.(a) <strong>Telugu. </strong>Telugu Language Day is celebrated every year on August 29. In 2008, Telugu was granted the status of a classical language. Nicolo di Conti, who visited the Vijayanagara Empire, found that the words in the Telugu language end with vowels, just like those in Italian, and hence referred to it as \"The Italian of the East&rdquo;.</p>",
                    solution_hi: "<p>41.(a) <strong>तेलुगु। </strong>तेलुगु भाषा दिवस प्रत्येक वर्ष 29 अगस्त को मनाया जाता है। 2008 में तेलुगु को शास्त्रीय भाषा का दर्जा प्रदान किया गया था। विजयनगर साम्राज्य का दौरा करने वाले निकोलो डी कोंटी ने पाया कि तेलुगु भाषा में शब्द इटालियन की तरह ही स्वरों (vowels) के साथ समाप्त होते हैं, इसलिए इसे \"पूर्व का इटालियन\" कहा जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. In which year did Indian archers participate in the Olympics for the first time ?</p>",
                    question_hi: "<p>42. भारतीय तीरंदाज़ों (archers) ने पहली बार ओलंपिक में किस वर्ष भाग लिया था ?</p>",
                    options_en: [
                        "<p>1980</p>",
                        "<p>1973</p>",
                        "<p>1988</p>",
                        "<p>1975</p>"
                    ],
                    options_hi: [
                        "<p>1980</p>",
                        "<p>1973</p>",
                        "<p>1988</p>",
                        "<p>1975</p>"
                    ],
                    solution_en: "<p>42.(c) <strong>1988. </strong>India competed in 7 events at the 1988 Summer Olympic Games held in Seoul, South Korea with a contingent of about 20 athletes participating in sports disciplines including Archery for the first time, Athletics, Boxing, Hockey, Swimming, Table Tennis and Tennis.</p>",
                    solution_hi: "<p>42.(c) <strong>1988. </strong>भारत ने दक्षिण कोरिया के सियोल में आयोजित 1988 ग्रीष्मकालीन ओलंपिक खेलों में 7 स्पर्धाओं में भाग लिया था, जिसमें लगभग 20 एथलीटों के दल ने पहली बार तीरंदाजी, एथलेटिक्स, मुक्केबाजी, हॉकी, तैराकी, टेबल टेनिस और टेनिस सहित खेलों में भाग लिया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Mewar festival is celebrated during the ______ festival at Udaipur.</p>",
                    question_hi: "<p>43. मेवाड़ उत्सव उदयपुर में _____उत्सव के दौरान मनाया जाता है</p>",
                    options_en: [
                        "<p>Gangaur</p>",
                        "<p>Teej</p>",
                        "<p>Dussehra</p>",
                        "<p>Holi</p>"
                    ],
                    options_hi: [
                        "<p>गणगौर</p>",
                        "<p>तीज</p>",
                        "<p>दशहरा</p>",
                        "<p>होली</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>Gangaur</strong> - A festival devoted to Goddess Parvati. It is celebrated in March. Festivals of Rajasthan : Camel Festival (Bikaner), Kumbhalgarh Festival, Desert Festival (Jaisalmer), Kajli Teej (Bundi), Abhaneri (Dausa), Ranakpur (Pali), Matsya Festival (Alwar) and Bundi Festival (Bundi).</p>",
                    solution_hi: "<p>43.(a) <strong>गणगौर</strong> - देवी पार्वती को समर्पित एक त्योहार है। यह मार्च माह में मनाया जाता है। राजस्थान के अन्य त्योहार: ऊँट महोत्सव (बीकानेर), कुम्भलगढ़ महोत्सव, रेगिस्तान महोत्सव (जैसलमेर), कजली तीज (बूंदी), आभानेरी (दौसा), रणकपुर (पाली), मत्स्य महोत्सव (अलवर) और बूंदी महोत्सव (बूंदी)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. If there is a fall in the demand of a good, the equilibrium price is expected to:</p>",
                    question_hi: "<p>44. यदि किसी वस्तु की मांग में गिरावट आती है, तो संतुलन मूल्य में होने की उम्मीद है:</p>",
                    options_en: [
                        "<p>first rise, and then fall sharply</p>",
                        "<p>fall</p>",
                        "<p>Rise</p>",
                        "<p>neither fall nor rise</p>"
                    ],
                    options_hi: [
                        "<p>पहले बढ़ता है, और फिर तेजी से गिरता है</p>",
                        "<p>गिरता है</p>",
                        "<p>बढ़ता है</p>",
                        "<p>न तो गिरता है और न ही बढ़ता है</p>"
                    ],
                    solution_en: "<p>44.(b) <strong>fall. </strong>When demand falls, it means consumers are willing to buy less of the goods at any given price, which naturally pushes the price downwards to reach a new equilibrium point where quantity demanded equals quantity supplied. Demand: The quantity of a good or service that consumers can purchase at various prices. Supply: The quantity of a good or service that producers can offer for sale at various prices. Equilibrium Price: The price at which the quantity demanded by consumers equals the quantity supplied by producers.</p>",
                    solution_hi: "<p>44.(b) <strong>गिरता है। </strong>जब मांग गिरता है, तो इसका अर्थ है कि उपभोक्ता किसी भी कीमत पर कम सामान खरीदने को तैयार हैं, जो स्वाभाविक रूप से कीमत को नीचे की ओर ले जाता है और एक नए संतुलन बिंदु पर पहुँचता है जहाँ माँग की गई मात्रा आपूर्ति की गई मात्रा के बराबर होती है। माँग: किसी वस्तु या सेवा की वह मात्रा जिसे उपभोक्ता विभिन्न कीमतों पर खरीद सकते हैं। आपूर्ति: किसी वस्तु या सेवा की वह मात्रा जिसे उत्पादक विभिन्न कीमतों पर बिक्री के लिए पेश कर सकते हैं। संतुलन मूल्य: वह मूल्य जिस पर उपभोक्ताओं द्वारा माँग की गई मात्रा उत्पादकों द्वारा आपूर्ति की गई मात्रा के बराबर होती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. The zone separating the troposphere from the stratosphere is called the _____.</p>",
                    question_hi: "<p>45. क्षोभमंडल को समताप मंडल से अलग करने वाले क्षेत्र को ______कहा जाता है।</p>",
                    options_en: [
                        "<p>Tropopause</p>",
                        "<p>stratospause</p>",
                        "<p>Stratomerge</p>",
                        "<p>Tropomerge</p>"
                    ],
                    options_hi: [
                        "<p>ट्रोपोपॉज़ (Tropopause)</p>",
                        "<p>स्ट्रैटोस्पॉज़ (Stratospause)</p>",
                        "<p>स्ट्रैटोमर्ज (Stratomerge)</p>",
                        "<p>ट्रोपोमर्ज (Tropomerge)</p>"
                    ],
                    solution_en: "<p>45.(a)<strong> Tropopause. </strong>The air temperature at the tropopause is about - 80o C over the equator and about - 45&deg; C over the poles. The temperature in this region is nearly constant, and hence, it is called the tropopause. The stratosphere is found above the tropopause and extends up to a height of 50 km. One important feature of the stratosphere is that it contains the ozone layer.</p>",
                    solution_hi: "<p>45.(a) <strong>ट्रोपोपॉज़। </strong>ट्रोपोपॉज़ पर वायु का तापमान भूमध्य रेखा पर लगभग - 80 डिग्री सेल्सियस और ध्रुवों पर लगभग - 45 डिग्री सेल्सियस होता है। इस क्षेत्र में तापमान लगभग स्थिर रहता है, और इसलिए इसे ट्रोपोपॉज़ कहा जाता है। समताप मंडल ट्रोपोपॉज़ के ऊपर पाया जाता है और 50 किमी की ऊँचाई तक फैला होता है। समताप मंडल की एक महत्वपूर्ण विशेषता यह है कि इसमें ओजोन परत होती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. After independence, the Census Act was enacted in India in the year ______.</p>",
                    question_hi: "<p>46. आजादी के बाद, भारत में जनगणना अधिनियम (Census Act) वर्ष ______में अधिनियमित किया गया था।</p>",
                    options_en: [
                        "<p>1960</p>",
                        "<p>1962</p>",
                        "<p>1955</p>",
                        "<p>1948</p>"
                    ],
                    options_hi: [
                        "<p>1960</p>",
                        "<p>1962</p>",
                        "<p>1955</p>",
                        "<p>1948</p>"
                    ],
                    solution_en: "<p>46.(d) <strong>1948. </strong>The first census of Independent India was conducted in 1951, which was the seventh census in its continuous series. The enumeration period of this Census was from 9th to 28th, February 1951. The Census Act, 1948 - An Act to provide for certain matters in connection with the taking of Census.</p>",
                    solution_hi: "<p>46.(d) <strong>1948. </strong>स्वतंत्र भारत की प्रथम जनगणना 1951 में हुई थी, जो लगातार होने वाली सातवीं जनगणना थी। इस जनगणना की गणना अवधि 9 से 28 फरवरी 1951 तक थी। जनगणना अधिनियम, 1948 - जनगणना के संबंध में कुछ मामलों का प्रावधान करने के लिए एक अधिनियम।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which technology is associated with providing high-speed wireless data transmission for mobile devices ?</p>",
                    question_hi: "<p>47. कौन-सी तकनीक मोबाइल डिवाइसों के लिए हाई-स्पीड वायरलेस डेटा ट्रांसमिशन (high-speed wireless data transmission) प्रदान करने से संबंधित है ?</p>",
                    options_en: [
                        "<p>GPS</p>",
                        "<p>Wi-Max</p>",
                        "<p>GPRS</p>",
                        "<p>LTE</p>"
                    ],
                    options_hi: [
                        "<p>GPS</p>",
                        "<p>Wi-Max</p>",
                        "<p>GPRS</p>",
                        "<p>LTE</p>"
                    ],
                    solution_en: "<p>47.(d) <strong>LTE </strong>stands for Long Term Evolution. The full form of GPS is the Global Positioning System and it is a satellite navigation system used to identify the ground position of an object. GPRS is General Packet Radio Service. It is a non-voice high-speed packet switching system developed for GSM networks. WiMAX stands for Worldwide Interoperability for Microwave Access.</p>",
                    solution_hi: "<p>47.(d) <strong>LTE </strong>का मतलब है लॉन्ग टर्म इवोल्यूशन। GPS का पूरा नाम ग्लोबल पोजिशनिंग सिस्टम है और यह एक सैटेलाइट नेविगेशन सिस्टम है जिसका इस्तेमाल किसी वस्तु की जमीनी स्थिति की पहचान करने के लिए किया जाता है। GPRS का अर्थ है जनरल पैकेट रेडियो सर्विस है। यह GSM नेटवर्क के लिए विकसित एक नॉन-वॉयस हाई-स्पीड पैकेट स्विचिंग सिस्टम है। WiMAX का अर्थ है वर्ल्डवाइड इंटरऑपरेबिलिटी फॉर माइक्रोवेव एक्सेस ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Of the football clubs listed below, which is the oldest in India ?</p>",
                    question_hi: "<p>48. नीचे सूचीबद्ध फुटबॉल क्लबों में से, भारत में सबसे पुराना क्लब कौन-सा है ?</p>",
                    options_en: [
                        "<p>Bombay FC</p>",
                        "<p>FC Kochin</p>",
                        "<p>Madras FC</p>",
                        "<p>Calcutta FC</p>"
                    ],
                    options_hi: [
                        "<p>बॉम्बे एफ.सी.</p>",
                        "<p>एफ.सी. कोचीन</p>",
                        "<p>मद्रास एफ.सी.</p>",
                        "<p>कलकत्ता एफ.सी.</p>"
                    ],
                    solution_en: "<p>48.(d) <strong>Calcutta FC.</strong> It was established in 1872. The oldest club that is currently active is the Mohun Bagan AC, which was established in 1889. The Indian Football Association (IFA) was established in Calcutta in 1893. The All India Football Federation (AIFF) was formed in 1937. Famous Football Clubs In India : Mumbai City FC, Bengaluru FC, Kerala Blasters FC, Jamshedpur FC, etc.</p>",
                    solution_hi: "<p>48.(d) <strong>कलकत्ता एफ.सी.। </strong>इसकी स्थापना 1872 में हुई थी। वर्तमान में सक्रिय सबसे पुराना क्लब मोहन बागान ए.सी. है, जिसकी स्थापना 1889 में हुई थी। भारतीय फुटबॉल संघ (IFA) की स्थापना 1893 में कलकत्ता में हुई थी। अखिल भारतीय फुटबॉल महासंघ (AIFF) का गठन 1937 में हुआ था। भारत में प्रसिद्ध फुटबॉल क्लब: मुंबई सिटी एफ.सी., बेंगलुरु एफ.सी., केरला ब्लास्टर्स एफ.सी., जमशेदपुर एफ.सी., आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Who among the following was the founder of &lsquo;Vikramshila Vishwavidyalaya&rsquo; ?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन \'विक्रमशिला विश्वविद्यालय\' के संस्थापक थे ?</p>",
                    options_en: [
                        "<p>Mahipala I</p>",
                        "<p>Dharmapala</p>",
                        "<p>Ramapala</p>",
                        "<p>Govindapala</p>"
                    ],
                    options_hi: [
                        "<p>महिपाल प्रथम</p>",
                        "<p>धर्मपाल</p>",
                        "<p>रामपाल</p>",
                        "<p>गोविंदपाल</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>Dharmapala. </strong>He was the son of Gopala (founder of Pala Dynasty). He revived Nalanda University. He constructed the Somapura Mahavihara at Paharpur. Art and Architecture of Palas - Vikramshila Vihar, Odantpuri Vihar, and Jagaddal Vihar. Vikramashila was one of the two most crucial centers of learning in India during the Pala Empire, along with Nalanda.</p>",
                    solution_hi: "<p>49.(b) <strong>धर्मपाल।</strong> वह गोपाल (पाल वंश के संस्थापक) के पुत्र थे। उन्होंने नालन्दा विश्वविद्यालय का पुनरुद्धार किया। उन्होंने पहाड़पुर में सोमपुरा महाविहार का निर्माण किया। पालों की कला और वास्तुकला - विक्रमशिला विहार, ओदंतपुरी विहार और जगद्दल विहार। पाल साम्राज्य के दौरान नालंदा के साथ विक्रमशिला भारत में शिक्षा के दो सबसे महत्वपूर्ण केंद्रों में से एक था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. What is observed on November 16 every year in India to commemorate the establishment of the Press Council of India (PCI) in 1966 ?</p>",
                    question_hi: "<p>50. 1966 में भारतीय प्रेस परिषद (PCI) की स्थापना के उपलक्ष्य में भारत में हर साल 16 नवंबर को क्या मनाया जाता है ?</p>",
                    options_en: [
                        "<p>National Media Day</p>",
                        "<p>National Press Day</p>",
                        "<p>National Journalism Day</p>",
                        "<p>National News Day</p>"
                    ],
                    options_hi: [
                        "<p>राष्ट्रीय मीडिया दिवस</p>",
                        "<p>राष्ट्रीय प्रेस दिवस</p>",
                        "<p>राष्ट्रीय पत्रकारिता दिवस</p>",
                        "<p>राष्ट्रीय समाचार दिवस</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>National Press Day. </strong>This day serves as a reminder of the indispensable role of a free press in a vibrant democracy like India and highlights the importance of ethical journalism.</p>",
                    solution_hi: "<p>50.(b) <strong>राष्ट्रीय प्रेस दिवस।</strong> यह दिन भारत जैसे जीवंत लोकतंत्र में स्वतंत्र प्रेस की अपरिहार्य भूमिका की याद दिलाता है और नैतिक पत्रकारिता के महत्व पर प्रकाश डालता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If LCM of 6 and 7 is half of the LCM of (x, y) and if x is 12, what will be the value of y ?</p>",
                    question_hi: "<p>51. यदि 6 और 7 का लघुत्तम समापवर्त्य (x, y) के लघुत्तम समापवर्त्य का आधा है और यदि x का मान 12 है, तो y का मान क्या होगा?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>24</p>",
                        "<p>18</p>",
                        "<p>7</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>24</p>",
                        "<p>18</p>",
                        "<p>7</p>"
                    ],
                    solution_en: "<p>51.(d)<br>We know that <br>HCF &times; LCM = Product of number<br>LCM (6, 7) = 42<br>Also, HCF (6, 7) = 1<br>According to the question, LCM (6, 7) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mi>C</mi><mi>M</mi><mi>&#160;</mi><mo>(</mo><mi>x</mi><mo>,</mo><mi>&#160;</mi><mi>y</mi><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>LCM (<math display=\"inline\"><mi>x</mi></math>, y) = 42 &times; 2 = 84<br>Also, HCF (<math display=\"inline\"><mi>x</mi></math>, y) = 1<br>Since <math display=\"inline\"><mi>x</mi></math> = 12,<br>y = <math display=\"inline\"><mfrac><mrow><mn>84</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 7</p>",
                    solution_hi: "<p>51.(d)<br>हम जानते हैं<br>महत्तम समापवर्तक &times; लघुत्तम समापवर्त्य = संख्या का गुणनफल<br>लघुत्तम समापवर्त्य (6, 7) = 42<br>साथ ही, महत्तम समापवर्तक (6, 7) = 1<br>प्रश्न के अनुसार, लघुत्तम समापवर्त्य (6, 7) = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2328;&#2369;&#2340;&#2381;&#2340;&#2350;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2366;&#2346;&#2357;&#2352;&#2381;&#2340;&#2381;&#2351;</mi><mo>&#160;</mo><mo>(</mo><mi>x</mi><mo>,</mo><mo>&#160;</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>लघुत्तम समापवर्त्य (<math display=\"inline\"><mi>x</mi></math>, y) = 42 &times; 2 = 84<br>साथ ही,महत्तम समापवर्तक (<math display=\"inline\"><mi>x</mi></math>, y) = 1<br>चूँकि <math display=\"inline\"><mi>x</mi></math> = 12,<br>y = <math display=\"inline\"><mfrac><mrow><mn>84</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 7</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The diameter of a roller is 35 cm and its length is 100 cm. It takes 200 complete revolutions to level a playground. Find the area of the playground in m<sup>2</sup></p>",
                    question_hi: "<p>52. एक रोलर का व्यास 35 cm और इसकी लंबाई 100 cm है। एक खेल के मैदान को समतल बनाने में इसे 200 पूर्ण परिक्रमण लगाने पड़ते हैं। m<sup>2</sup> में खेल के मैदान का क्षेत्रफल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>220</p>",
                        "<p>2,20,00,000</p>",
                        "<p>1,10,000</p>",
                        "<p>110</p>"
                    ],
                    options_hi: [
                        "<p>220</p>",
                        "<p>2,20,00,000</p>",
                        "<p>1,10,000</p>",
                        "<p>110</p>"
                    ],
                    solution_en: "<p>52.(a) <strong>Given:</strong> radius = <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> cm and length = 100 cm<br>Surface area of a roller = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>2</mn></mfrac></math> &times; 100 = 11000 cm<sup>2</sup><br>1 revolution of roller = surface area of a roller <br>200 revolution = 11000 &times; 200 = 2200000 cm<sup>2</sup> = 220 m<sup>2</sup><br>Hence, Area of the playground = 220 m<sup>2</sup></p>",
                    solution_hi: "<p>52.(a) <strong>दिया गया है:</strong> त्रिज्या = <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> cm और लंबाई = 100 cm<br>रोलर का पृष्ठीय क्षेत्रफल = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>2</mn></mfrac></math> &times; 100 = 11000 cm<sup>2</sup><br>रोलर की 1 परिक्रमा = रोलर का पृष्ठीय क्षेत्रफल <br>200 पूर्ण परिक्रमण = 11000 &times; 200 = 2200000 cm<sup>2</sup> = 220 m<sup>2</sup><br>अतः, खेल के मैदान का क्षेत्रफल = 220 m<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. If for a non-zero <math display=\"inline\"><mi>x</mi></math>, 7x<sup>2</sup> + 5x + 7 = 0, then the x<sup>3</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> is _________ .</p>",
                    question_hi: "<p>53. यदि गैर-शून्य <math display=\"inline\"><mi>x</mi></math> के लिए,7x<sup>2</sup> + 5x + 7 = 0 है, x<sup>3</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>343</mn><mn>610</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>420</mn><mn>343</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>610</mn><mn>420</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>610</mn><mn>343</mn></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>343</mn><mn>610</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>420</mn><mn>343</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>610</mn><mn>420</mn></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>610</mn><mn>343</mn></mfrac></math></p>"
                    ],
                    solution_en: "<p>53.(d)<br>Given<br>7x<sup>2</sup> + 5x + 7 = 0<br>Dividing both sides by <math display=\"inline\"><mi>x</mi></math>, we get<br>7x&nbsp;+ 5 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mi>x</mi></mfrac></math> = 0<br>&rArr; x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>5</mn></mrow><mn>7</mn></mfrac></math><br>Taking cube both sides <br>(x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>)<sup>3</sup> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>5</mn></mrow><mn>7</mn></mfrac></math>)<sup>3</sup><br>&rArr; x<sup>3</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> + 3.x.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> (x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>125</mn></mrow><mn>343</mn></mfrac></math><br>&rArr; x<sup>3</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> + 3 (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>5</mn></mrow><mn>7</mn></mfrac></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>125</mn></mrow><mn>343</mn></mfrac></math><br>&rArr; x<sup>3</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>125</mn></mrow><mn>343</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>7</mn></mfrac></math><br>&rArr; x<sup>3</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>610</mn><mn>343</mn></mfrac></math></p>",
                    solution_hi: "<p>53.(d)<br>दिया गया है <br>7x<sup>2</sup> + 5x + 7 = 0<br>दोनों पक्षों को <math display=\"inline\"><mi>x</mi></math> से विभाजित करने पर,<br>7x&nbsp;+ 5 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mi>x</mi></mfrac></math> = 0<br>&rArr; x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>5</mn></mrow><mn>7</mn></mfrac></math><br>दोनों पक्षों को घन करने पर <br>(x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>)<sup>3</sup> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>5</mn></mrow><mn>7</mn></mfrac></math>)<sup>3</sup><br>&rArr; x<sup>3</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> + 3.x.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> (x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>125</mn></mrow><mn>343</mn></mfrac></math><br>&rArr; x<sup>3</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> + 3 (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>5</mn></mrow><mn>7</mn></mfrac></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>125</mn></mrow><mn>343</mn></mfrac></math><br>&rArr; x<sup>3</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>125</mn></mrow><mn>343</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>7</mn></mfrac></math><br>&rArr; x<sup>3</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>610</mn><mn>343</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. 32 kg of sugar costing Rs. 24 per kg is mixed with 24 kg of sugar costing Rs. 31 per kg. What is the cost of the mixture ?</p>",
                    question_hi: "<p>54. 24 रुपए प्रति किलोग्राम मूल्य वाली 32 किलोग्राम चीनी को 31 रुपए प्रति किलोग्राम मूल्य वाली 24 किलोग्राम चीनी के साथ मिलाया जाता है। मिश्रण का मूल्य कितना है ?</p>",
                    options_en: [
                        "<p>Rs. 26/kg</p>",
                        "<p>Rs. 31/kg</p>",
                        "<p>Rs. 29/kg</p>",
                        "<p>Rs. 27/kg</p>"
                    ],
                    options_hi: [
                        "<p>26 रुपए प्रति किलोग्राम</p>",
                        "<p>31 रुपए प्रति किलोग्राम</p>",
                        "<p>29 रुपए प्रति किलोग्राम</p>",
                        "<p>27 रुपए प्रति किलोग्राम</p>"
                    ],
                    solution_en: "<p>54.(d)<br>Cost of mixture = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32</mn><mo>&#215;</mo><mn>24</mn><mo>+</mo><mn>24</mn><mo>&#215;</mo><mn>31</mn></mrow><mrow><mn>32</mn><mo>+</mo><mn>24</mn></mrow></mfrac></math> = 27/kg</p>",
                    solution_hi: "<p>54.(d)<br>मिश्रण की लागत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32</mn><mo>&#215;</mo><mn>24</mn><mo>+</mo><mn>24</mn><mo>&#215;</mo><mn>31</mn></mrow><mrow><mn>32</mn><mo>+</mo><mn>24</mn></mrow></mfrac></math>&nbsp;= 27/किग्रा</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Pipe X can fill a tank in 9 hours and Pipe Y can fill it in 21 hours. If they are opened on alternate hours and Pipe X is opened first, in how many hours shall the tank be full ?</p>",
                    question_hi: "<p>55. पाइप X एक टंकी को 9 घंटे में भर सकता है और पाइप Y इसे 21 घंटे में भर सकता है। यदि उन्हें एकांतर घंटों में (बारी-बारी से एक-एक घंटे पर) खोला जाता है और पाइप X को पहले खोला जाता है, तो टंकी कितने घंटों में पूरी भर जाएगी ?</p>",
                    options_en: [
                        "<p>10<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>10<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>55.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619564106.png\" alt=\"rId49\" width=\"200\" height=\"144\"><br>If Pipes are opened on alternate hr , than <br>In 2hr , tank fill in = (7 + 3) = 10 unit <br>&rArr; 12 hr = 60 unit <br>Remaining 3 unit fill by pipe X in <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hr<br><math display=\"inline\"><mo>&#8756;</mo></math> Total time taken to full the tank = 12 hr + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hr = 12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>hr</p>",
                    solution_hi: "<p>55.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619564249.png\" alt=\"rId50\" width=\"172\" height=\"151\"><br>यदि पाइप वैकल्पिक घंटे पर खोले जाते हैं, तो <br>2 घंटे में, टैंक भरेगा = (7 + 3) = 10 इकाई <br>&rArr; 12 घंटे = 60 इकाई <br>शेष 3 इकाई , पाइप X द्वारा <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे में भरा जाएगा <br><math display=\"inline\"><mo>&#8756;</mo></math> टैंक को पूरा भरने में लगा कुल समय = 12 घंटे + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे = 12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Express sin 74&deg; + tan 74&deg; in terms of trigonometric ratios of angles between 0&deg; and 45&deg;.</p>",
                    question_hi: "<p>56. sin 74&deg; + tan 74&deg; को 0&deg; और 45&deg; के बीच के कोणों के त्रिकोणमितीय अनुपात के रूप में व्यक्त करें।</p>",
                    options_en: [
                        "<p>sec 16&deg; + cot 16&deg;</p>",
                        "<p>cosec 16&deg; + sec 16&deg;</p>",
                        "<p>cosec 16&deg; + cot 16&deg;</p>",
                        "<p>cos 16&deg; + cot 16&deg;</p>"
                    ],
                    options_hi: [
                        "<p>sec 16&deg; + cot 16&deg;</p>",
                        "<p>cosec 16&deg; + sec 16&deg;</p>",
                        "<p>cosec 16&deg; + cot 16&deg;</p>",
                        "<p>cos 16&deg; + cot 16&deg;</p>"
                    ],
                    solution_en: "<p>56.(d)<br>sin(90&deg;- &theta;) = cos&theta;<br>tan(90&deg;- &theta;) = cot&theta;<br>Now, sin 74&deg; + tan 74&deg;<br>sin(90&deg; - 16&deg;) + tan(90&deg; - 16&deg;)<br>cos16&deg; + cot16&deg;</p>",
                    solution_hi: "<p>56.(d)<br>sin(90&deg; - &theta;) = cos&theta;<br>tan(90&deg; - &theta;) = cot&theta;<br>अब, sin 74&deg; + tan 74&deg;<br>sin(90&deg; - 16&deg;) + tan(90&deg; - 16&deg;)<br>cos16&deg; + cot16&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. ∆ABC ~ ∆EDF and area(∆ABC) : area (∆EDF) = 1 : 4. If AB = 7 cm, BC = 8 cm and CA = 9 cm, then DF is equal to:</p>",
                    question_hi: "<p>57. ∆ABC ~ ∆EDF और क्षेत्रफल (ABC) : क्षेत्रफल (∆EDF) = 1 : 4 है। यदि AB = 7 cm, BC = 8 cm और CA = 9 cm है, तो DF बराबर होगाः</p>",
                    options_en: [
                        "<p>14 cm</p>",
                        "<p>12 cm</p>",
                        "<p>18 cm</p>",
                        "<p>16 cm</p>"
                    ],
                    options_hi: [
                        "<p>14 cm</p>",
                        "<p>12 cm</p>",
                        "<p>18 cm</p>",
                        "<p>16 cm</p>"
                    ],
                    solution_en: "<p>57.(d)<br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mi>&#160;</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>E</mi><mi>D</mi><mi>F</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>B</mi><mi>C</mi><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mi>D</mi><mi>F</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>8</mn><mn>2</mn></msup><msup><mrow><mo>(</mo><mi>D</mi><mi>F</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br>(DF)<sup>2</sup> = 64 &times; 4 = 256<br>DF = 16 cm</p>",
                    solution_hi: "<p>57.(d)<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8710;</mo><mi>A</mi><mi>B</mi><mi>C</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mo>&#8710;</mo><mi>E</mi><mi>D</mi><mi>F</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>B</mi><mi>C</mi><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mi>D</mi><mi>F</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>8</mn><mn>2</mn></msup><msup><mrow><mo>(</mo><mi>D</mi><mi>F</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br>(DF)<sup>2</sup> = 64 &times; 4 = 256<br>DF = 16 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A and B, working together, take 15 days to complete a piece of work. If A alone can do this work in 18 days, then how long would B take to complete the same work ?</p>",
                    question_hi: "<p>58. A और B, एक साथ काम करते हुए, एक काम को पूरा करने में 15 दिन लेते हैं। यदि A अकेले इस काम को 18 दिनों में पूरा कर सकता है, तो B को उसी काम को पूरा करने में कितना समय लगेगा ?</p>",
                    options_en: [
                        "<p>33 days</p>",
                        "<p>15 days</p>",
                        "<p>8 days</p>",
                        "<p>90 days</p>"
                    ],
                    options_hi: [
                        "<p>33 दिन</p>",
                        "<p>15 दिन</p>",
                        "<p>8 दिन</p>",
                        "<p>90 दिन</p>"
                    ],
                    solution_en: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619564362.png\" alt=\"rId51\" width=\"267\" height=\"180\"><br>Efficiency of B = 6 - 5 = 1 units<br>Time taken by B to done whole work = <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 90 days</p>",
                    solution_hi: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619564518.png\" alt=\"rId52\" width=\"253\" height=\"191\"><br>B की क्षमता = 6 - 5 = 1 इकाई<br>B द्वारा पूरा कार्य करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 90 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. By what percentage should a racer increase the speed to reduce the time by 25% to cover a fixed distance ?</p>",
                    question_hi: "<p>59. एक निश्चित दूरी को तय करने में लगने वाले समय को 25% कम करने के लिए एक धावक को अपनी चाल में कितने प्रतिशत की वृद्धि करनी चाहिए ?</p>",
                    options_en: [
                        "<p>41 %</p>",
                        "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>31%</p>",
                        "<p>37<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>41 %</p>",
                        "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>31%</p>",
                        "<p>37<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>59.(b) <br>Time &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>S</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi></mrow></mfrac></math> (when distance is constant)<br>Time&nbsp; &nbsp; 4 : 3<br>Speed 3 : 4<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mo>-</mo><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times;&nbsp; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>3</mn></mfrac></math> = 33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>59.(b) <br>समय &prop; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>&#2327;&#2340;&#2367;</mi></mfrac></math> (जब दूरी स्थिर हो)<br>समय - 4 : 3<br>गति&nbsp; -&nbsp; 3 : 4<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mo>-</mo><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times;&nbsp; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>3</mn></mfrac></math> = 33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. If the income of Neha is 5% more than that of Monica, then the income of Monica is what percentage less than that of Neha ?</p>",
                    question_hi: "<p>60. यदि नेहा की आय मोनिका की आय से 5% अधिक है, तो मोनिका की आय नेहा की आय से कितने प्रतिशत कम है ?</p>",
                    options_en: [
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>60.(a)<br>Ratio -&nbsp; &nbsp;Neha : Monica<br>Income - 105&nbsp; : 100<br>required% = <math display=\"inline\"><mfrac><mrow><mn>105</mn><mo>-</mo><mn>100</mn></mrow><mrow><mn>105</mn></mrow></mfrac></math> &times; 100 = 4<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>60.(a)<br>अनुपात -&nbsp; नेहा&nbsp; &nbsp;: मोनिका<br>आय&nbsp; &nbsp; &nbsp;-&nbsp; &nbsp;105&nbsp; :&nbsp; &nbsp;100<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>105</mn><mo>-</mo><mn>100</mn></mrow><mrow><mn>105</mn></mrow></mfrac></math> &times; 100 = 4<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The difference between the cost price and the selling price of an article is ₹1,800. If there is a profit of 20%, then the cost price of the article is:</p>",
                    question_hi: "<p>61. एक वस्तु के क्रय मूल्य और विक्रय मूल्य के बीच का अंतर ₹1,800 है। यदि 20% का लाभ होता है, तो वस्तु का क्रय मूल्य क्या है ?</p>",
                    options_en: [
                        "<p>₹7,000</p>",
                        "<p>₹11,000</p>",
                        "<p>₹8,000</p>",
                        "<p>₹9,000</p>"
                    ],
                    options_hi: [
                        "<p>₹7,000</p>",
                        "<p>₹11,000</p>",
                        "<p>₹8,000</p>",
                        "<p>₹9,000</p>"
                    ],
                    solution_en: "<p>61.(d) <br>Ratio - CP : SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp;:&nbsp; 6<br>Difference = 6 - 5 = 1 unit<br>1 unit = 1800<br>(CP) 5 units = 1800 &times; 5 = ₹ 9000</p>",
                    solution_hi: "<p>61.(d) <br>अनुपात - क्रय मूल्य : विक्रय मूल्य<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;6<br>अंतर = 6 - 5 = 1 इकाई<br>1 इकाई = 1800<br>(क्रय मूल्य) 5 इकाई = 1800 &times; 5 = ₹ 9000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. A coffee maker with marked price ₹4,000 is available on successive discounts of 15%, 10% and 5%. Find the selling price of the coffee maker.</p>",
                    question_hi: "<p>62. ₹4,000 अंकित मूल्य वाला एक कॉफी मेकर 15%, 10% और 5% की क्रमिक छूट पर उपलब्ध है। कॉफी मेकर का विक्रय मूल्य ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>₹2,905</p>",
                        "<p>₹2,907</p>",
                        "<p>₹2,900</p>",
                        "<p>₹2,903</p>"
                    ],
                    options_hi: [
                        "<p>₹2,905</p>",
                        "<p>₹2,907</p>",
                        "<p>₹2,900</p>",
                        "<p>₹2,903</p>"
                    ],
                    solution_en: "<p>62.(b)<br>Selling price of coffee maker = 4000 &times; <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math><br>= ₹ 2907</p>",
                    solution_hi: "<p>62.(b)<br>कॉफ़ी मेकर का विक्रय मूल्य = 4000 &times; <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math><br>= ₹ 2907</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Reshma took a loan of ₹12,00,000 with simple interest for as many years as the rate of interest. If she paid ₹9,72,000 as interest at the end of the loan period, what was the rate of interest per annum ?</p>",
                    question_hi: "<p>63. रेशमा ने साधारण ब्याज पर ₹12,00,000 का ऋण उतने ही वर्षों के लिए लिया जितनी ब्याज की दर है। यदि उसने ऋण अवधि के अंत में ब्याज के रूप में ₹9,72,000 का भुगतान किया, तो वार्षिक ब्याज की दर क्या थी ?</p>",
                    options_en: [
                        "<p>9%</p>",
                        "<p>8.7%</p>",
                        "<p>8%</p>",
                        "<p>7.5%</p>"
                    ],
                    options_hi: [
                        "<p>9%</p>",
                        "<p>8.7%</p>",
                        "<p>8%</p>",
                        "<p>7.5%</p>"
                    ],
                    solution_en: "<p>63.(a)<br>SI = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>R</mi><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>Let Reshma took a loan at <math display=\"inline\"><mi>x</mi></math>% interest rate per annum<br>So, According to the question,<br>972000 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1200000</mn><mo>&#215;</mo><mi>&#160;</mi><mi>x</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>x</mi></mrow><mn>100</mn></mfrac></math><br>972000 &times; 100 = 1200000 &times; x<sup>2</sup><br>x<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>972000</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>1200000</mn></mfrac></math> = 81<br><math display=\"inline\"><mi>x</mi></math> = 9<br>Hence, required interest rate = 9%</p>",
                    solution_hi: "<p>63.(a)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;</mi><mi>&#2352;</mi><mo>&#215;</mo><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>माना रेशमा द्वारा x% वार्षिक ब्याज दर पर ऋण लिया गया <br>तब , प्रश्न के अनुसार,<br>972000 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1200000</mn><mo>&#215;</mo><mi>&#160;</mi><mi>x</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>x</mi></mrow><mn>100</mn></mfrac></math><br>972000 &times; 100 = 1200000 &times; x<sup>2</sup><br>x<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>972000</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>1200000</mn></mfrac></math> = 81<br><math display=\"inline\"><mi>x</mi></math> = 9<br>अतः, आवश्यक ब्याज दर = 9%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A group of 30 students appeared in a test. The average score of 12 students is 62, and that of the rest is 62. What is the average score of the group ?</p>",
                    question_hi: "<p>64. 30 छात्रों के एक समूह ने एक टेस्ट दिया। 12 छात्रों का औसत स्कोर 62 है और बाकी छात्रों का औसत स्कोर भी 62 है। समूह का औसत स्कोर ज्ञात करें।</p>",
                    options_en: [
                        "<p>62</p>",
                        "<p>63</p>",
                        "<p>60</p>",
                        "<p>61</p>"
                    ],
                    options_hi: [
                        "<p>62</p>",
                        "<p>63</p>",
                        "<p>60</p>",
                        "<p>61</p>"
                    ],
                    solution_en: "<p>64.(a) <br>Since both groups of 12 and 18 students have the same average score of 62, the total sum of their scores will also result in an average of 62 for the entire group.</p>",
                    solution_hi: "<p>64.(a) <br>चूँकि, 12 और 18 छात्रों के दोनों समूहों का औसत स्कोर 62 समान है, इसलिए उनके अंकों का कुल योग भी पूरे समूह के लिए औसत 62 होगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. If sinB = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>, what is the value of cosB(sec B - tan B) ? Given that 0 &lt; B &lt; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#960;</mi><mn>2</mn></mfrac></math></p>",
                    question_hi: "<p>65. यदि sinB = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> है, तो cosB(sec B - tan B) का मान कितना होगा ? दिया गया है कि 0 &lt; B &lt;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#960;</mi><mn>2</mn></mfrac></math> है।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>65.(d)<br><math display=\"inline\"><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>B</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math> <br>now,<br><math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>B</mi></math>(sec B - tan B)<br><math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>B</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>B</mi></mrow></mfrac></math> - cos B &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>B</mi></mrow><mrow><mi>cos</mi><mi>B</mi></mrow></mfrac></math><br>= 1 - sin B<br>= 1 - <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>17</mn></mfrac></math></p>",
                    solution_hi: "<p>65.(d)<br><math display=\"inline\"><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>B</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>17</mn></mfrac></math><br>अब,<br><math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>B</mi></math>(sec B - tan B)<br><math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>B</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>B</mi></mrow></mfrac></math> - cos B &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>B</mi></mrow><mrow><mi>cos</mi><mi>B</mi></mrow></mfrac></math><br>= 1 - sin B<br>= 1 - <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>17</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A cylindrical tank of radius 14 cm is full of water. If 616 litres of water are drawn out, then the water level in the tank is dropped by _____ m. (Take &pi; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    question_hi: "<p>66. 14 cm त्रिज्या का एक बेलनाकार टैंक जल से भरा है। यदि 616 लीटर जल निकाला जाता है, तो टैंक में जल का स्तर ______ m कम हो जाता है। ( &pi; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;लें )</p>",
                    options_en: [
                        "<p>1000</p>",
                        "<p>100</p>",
                        "<p>1</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>1000</p>",
                        "<p>100</p>",
                        "<p>1</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p dir=\"ltr\">66.(d) 1 l = 1000 cm<sup>3</sup><br>Volume of water level dropped = &pi;r<sup>2</sup>h<br>616 &times; 1000&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 14 &times; 14 &times; h<br>h = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>616000</mn><mrow><mn>22</mn><mo>&#215;</mo><mn>14</mn><mo>&#215;</mo><mn>2</mn></mrow></mfrac></math> = 1000 cm = 10 m</p>",
                    solution_hi: "<p dir=\"ltr\">66.(d) 1 l = 1000 cm<sup>3</sup><br>गिरे हुए जल स्तर का आयतन = &pi;r<sup>2</sup>h<br>616 &times; 1000&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 14 &times; 14 &times; h<br>h = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>616000</mn><mrow><mn>22</mn><mo>&#215;</mo><mn>14</mn><mo>&#215;</mo><mn>2</mn></mrow></mfrac></math> = 1000 cm = 10 m</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Let C be a circle with center O and PQ be the diameter of C. Let AB be a chord on C. If &ang;QOB = 40&deg;, &ang;AOP = 80&deg;, then find &ang;AOB.</p>",
                    question_hi: "<p>67. माना C एक वृत्त है, जिसका केंद्र O है और PQ, C का व्यास है। माना AB, C पर एक जीवा है। यदि &ang;QOB = 40&deg;, &ang;AOP = 80&deg;, तो &ang;AOB ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>70&deg;</p>",
                        "<p>90&deg;</p>",
                        "<p>100&deg;</p>",
                        "<p>60&deg;</p>"
                    ],
                    options_hi: [
                        "<p>70&deg;</p>",
                        "<p>90&deg;</p>",
                        "<p>100&deg;</p>",
                        "<p>60&deg;</p>"
                    ],
                    solution_en: "<p>67.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619564692.png\" alt=\"rId53\" width=\"190\" height=\"166\"><br>&ang;POA + &ang;AOB + &ang;QOB = 180&deg; <br>80&deg; + &ang;AOB + 40&deg; = 180&deg;<br>&ang;AOB = 60&deg;</p>",
                    solution_hi: "<p>67.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619564692.png\" alt=\"rId53\" width=\"190\" height=\"166\"><br>&ang;POA + &ang;AOB + &ang;QOB = 180&deg; <br>80&deg; + &ang;AOB + 40&deg; = 180&deg;<br>&ang;AOB = 60&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. If x : y = 5 : 6,then 2x + 3y : 3x + 5y is:</p>",
                    question_hi: "<p>68. यदि x : y = 5 : 6 है, तो 2x + 3y : 3x + 5y का मान क्या होगा ?</p>",
                    options_en: [
                        "<p>18 : 35</p>",
                        "<p>28 : 45</p>",
                        "<p>18 : 45</p>",
                        "<p>28 : 48</p>"
                    ],
                    options_hi: [
                        "<p>18 : 35</p>",
                        "<p>28 : 45</p>",
                        "<p>18 : 45</p>",
                        "<p>28 : 48</p>"
                    ],
                    solution_en: "<p>68.(b)<br>Given:- x : y = 5 : 6<br>2x + 3y : 3x + 5y <br>= 2(5) + 3(6) : 3(5) + 5(6)<br>= 10 + 18 : 15 + 30 = 28 : 45</p>",
                    solution_hi: "<p>68.(b)<br>दिया गया:- x : y = 5 : 6<br>2x + 3y : 3x + 5y <br>= 2(5) + 3(6) : 3(5) + 5(6)<br>= 10 + 18 : 15 + 30 = 28 : 45</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The speed of a boat when travelling downstream is 33 km/h, whereas when travelling upstream it is 27 km/h. What is the speed of the boat in still water ?</p>",
                    question_hi: "<p>69. धारा की दिशा में यात्रा करते समय एक नाव की गति 33 km/h है, जबकि धारा के प्रतिकूल दिशा में यात्रा करते समय यह 27 km/h है। शांत जल में नाव की गति क्या है ?</p>",
                    options_en: [
                        "<p>6 km/h</p>",
                        "<p>60 km/h</p>",
                        "<p>12 km/h</p>",
                        "<p>30 km/h</p>"
                    ],
                    options_hi: [
                        "<p>6 km/h</p>",
                        "<p>60 km/h</p>",
                        "<p>12 km/h</p>",
                        "<p>30 km/h</p>"
                    ],
                    solution_en: "<p>69.(d)<br>x + y = 33 &hellip;(i)<br>x - y = 27 &hellip;(ii)<br>On adding (i) and (ii)<br>2x&nbsp;= 60<br>x = 30 <br>Hence, speed of boat in still water = 30 km/h</p>",
                    solution_hi: "<p>69.(d)<br>x + y = 33 &hellip;(i)<br>x - y = 27 &hellip;(ii)<br>(i) और (ii) जोड़ने पर<br>2x&nbsp;= 60<br>x = 30 <br>अत: शांत जल में नाव की गति = 30 km/h</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The following table shows the sale (in thousands) of different types of helmets by a shop over the given years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619564893.png\" alt=\"rId54\" width=\"358\" height=\"122\"> <br>What was the percentage increase in the sale of Helmets C in 2002 as compared to that in 1999 ?</p>",
                    question_hi: "<p>70. निम्नलिखित तालिका दिए गए वर्षों में एक दुकान द्वारा विभिन्न प्रकार के हेलमेट की बिक्री (हजारों में) दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619565059.png\" alt=\"rId55\" width=\"366\" height=\"134\"> <br>1999 की तुलना में 2002 में हेलमेट C की बिक्री में कितने प्रतिशत की वृद्धि हुई ?</p>",
                    options_en: [
                        "<p>25.93%</p>",
                        "<p>26.39%</p>",
                        "<p>28.26%</p>",
                        "<p>27.62%</p>"
                    ],
                    options_hi: [
                        "<p>25.93%</p>",
                        "<p>26.39%</p>",
                        "<p>28.26%</p>",
                        "<p>27.62%</p>"
                    ],
                    solution_en: "<p>70.(a)<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>68</mn><mo>-</mo><mn>54</mn></mrow><mrow><mn>54</mn></mrow></mfrac></math> &times; 100 = 25.93%</p>",
                    solution_hi: "<p>70.(a)<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>68</mn><mo>-</mo><mn>54</mn></mrow><mrow><mn>54</mn></mrow></mfrac></math> &times; 100 = 25.93%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Rahul and Rohit start running on a circular track (of length 360 m) from the same point simultaneously with a speed of 3 m/s and 4 m/s. After how much time from the start, they will meet again at the starting point ?</p>",
                    question_hi: "<p>71. राहुल और रोहित एक ही बिंदु से एक वृत्ताकार ट्रैक (360 m लंबा) पर 3 m/s और 4 m/s की चाल से एक साथ दौड़ना शुरू करते हैं। दौड़ शुरु करने से कितने समय बाद वे फिर से प्रारंभिक बिंदु पर मिलेंगे ?</p>",
                    options_en: [
                        "<p>2 minutes</p>",
                        "<p>12 minutes</p>",
                        "<p>6 minutes</p>",
                        "<p>3 minutes</p>"
                    ],
                    options_hi: [
                        "<p>2 मिनट</p>",
                        "<p>12 मिनट</p>",
                        "<p>6 मिनट</p>",
                        "<p>3 मिनट</p>"
                    ],
                    solution_en: "<p>71.(c)<br>Time taken by Rahul to complete 1 round of circular track = <math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 120 second<br>Time taken by Rohit to complete 1 round of circular track = <math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 90 second<br>Now, LCM(120 , 90) = 360 second = 6 min.<br>Hence, time when they again meet at starting point = 6 min.</p>",
                    solution_hi: "<p>71.(c)<br>राहुल द्वारा वृत्ताकार ट्रैक का 1 चक्कर पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 120 सेकंड <br>रोहित द्वारा वृत्ताकार ट्रैक का 1 चक्कर पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 90 सेकंड <br>अब, LCM(120, 90) = 360 सेकंड = 6 मिनट<br>अतः, जब वे आरंभिक बिंदु पर मिलेंगे तो समय = 6 मिनट</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Replace * by the smallest digit so that 723*56* is divisible by 6</p>",
                    question_hi: "<p>72. * को उस लघुतम अंक से प्रतिस्थापित कीजिए, जिससे 723*56* संख्या 6 से विभाज्य हो जाए।</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>2</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>2</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>72.(c) <br>6 = 2 &times; 3<br>Using the given options, we have ;<br>(a) 723<strong>4</strong>56<strong>4</strong>, which is not divisible by 3.<br>(b) 723<strong>3</strong>56<strong>3</strong>, which is not divisible by 2 <br>(c) 723<strong>2</strong>56<strong>2</strong>, which is divisible by 2 and 3 both<br>(d) 723<strong>1</strong>56<strong>1</strong>, which is not divisible by 2<br>So, the correct option is (c)</p>",
                    solution_hi: "<p>72.(c) <br>6 = 2 &times; 3<br>दिए गए विकल्पों का उपयोग करते हुए,<br>(a) 723<strong>4</strong>56<strong>4</strong>, 3 से विभाज्य नही है.<br>(b) 723<strong>3</strong>56<strong>3</strong>, 2 से विभाज्य नही है.<br>(c) 723<strong>2</strong>56<strong>2</strong>, 2 और 3 दोनो से विभाज्य है.<br>(d) 723<strong>1</strong>56<strong>1</strong>, 2 से विभाज्य नही है.<br>इसलिए , सही विकल्प (c) है ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The value of 15 - 16 &divide; [4 + (5 + 7) &divide; 3] is equal to:</p>",
                    question_hi: "<p>73. 15 - 16 <math display=\"inline\"><mo>&#247;</mo></math> [4 + (5 + 7) &divide; 3] का मान निम्न में से किसके बराबर होगा ?</p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>17</p>",
                        "<p>18</p>",
                        "<p>13</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>17</p>",
                        "<p>18</p>",
                        "<p>13</p>"
                    ],
                    solution_en: "<p>73.(d)<br>15 - 16 &divide; [4 + (5 + 7) &divide; 3] <br>15 - 16 &divide; [4 + 4]<br>15 - 16 &divide; 8<br>15 - 2 = 13</p>",
                    solution_hi: "<p>73.(d)<br>15 - 16 &divide; [4 + (5 + 7) &divide; 3] <br>15 - 16 &divide; [4 + 4]<br>15 - 16 &divide; 8<br>15 - 2 = 13</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Find the average of the first 18 multiples of 9.</p>",
                    question_hi: "<p>74. 9 के प्रथम 18 गुणकों का औसत ज्ञात करें।</p>",
                    options_en: [
                        "<p>88.5</p>",
                        "<p>82.3</p>",
                        "<p>85.5</p>",
                        "<p>84.6</p>"
                    ],
                    options_hi: [
                        "<p>88.5</p>",
                        "<p>82.3</p>",
                        "<p>85.5</p>",
                        "<p>84.6</p>"
                    ],
                    solution_en: "<p>74.(c)<br>First 18 multiples of 9 are<br>9 , 18, 27, &hellip;&hellip;&hellip;&hellip;.168<br>Average of these multiples = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>f</mi><mi>i</mi><mi>r</mi><mi>s</mi><mi>t</mi><mi>&#160;</mi><mi>n</mi><mi>o</mi><mo>.</mo><mo>+</mo><mi>l</mi><mi>a</mi><mi>s</mi><mi>t</mi><mi>&#160;</mi><mi>n</mi><mi>o</mi><mo>.</mo></mrow><mn>2</mn></mfrac></math><br>(<strong>Note:</strong> we use these formula when the given no. are in A.P)<br>&rArr; Average = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>171</mn><mn>2</mn></mfrac></math><br>&rArr; Average = 85.5</p>",
                    solution_hi: "<p>74.(c)<br>9 के प्रथम 18 गुणज हैं<br>9 , 18, 27, &hellip;&hellip;&hellip;&hellip;.168<br>इन गुणजों का औसत = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2381;&#2352;&#2341;&#2350;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>+</mo><mi>&#2309;&#2306;&#2340;&#2367;&#2350;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow><mn>2</mn></mfrac></math><br>(<strong>नोट: </strong>हम इस सूत्र का उपयोग तब करते हैं जब दी गई संख्या A.P में होती है)<br>&rArr; औसत = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>171</mn><mn>2</mn></mfrac></math><br>&rArr; औसत = 85.5</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The following table represents the total number of research scholars and the respective number of female research scholars in various departments of a university. Study the table carefully and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619565194.png\" alt=\"rId56\" width=\"579\" height=\"99\"> <br>What is the ratio of male research scholars in Mathematics to Statistics ?</p>",
                    question_hi: "<p>75. निम्नलिखित तालिका एक विश्वविद्यालय के विभिन्न विभागों में शोधार्थियों की कुल संख्या और महिला शोधार्थियों की संबंधित संख्या को दर्शाती है। तालिका का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744619565352.png\" alt=\"rId57\" width=\"424\" height=\"124\"> <br>गणित और सांख्यिकी विभाग में पुरुष शोधार्थियों का अनुपात कितना है ?</p>",
                    options_en: [
                        "<p>14 : 9</p>",
                        "<p>7 : 6</p>",
                        "<p>6 : 7</p>",
                        "<p>9 : 14</p>"
                    ],
                    options_hi: [
                        "<p>14 : 9</p>",
                        "<p>7 : 6</p>",
                        "<p>6 : 7</p>",
                        "<p>9 : 14</p>"
                    ],
                    solution_en: "<p>75.(a)<br>No of male research scholars in Mathematics = 18 - 4 = 14<br>No of male research scholars in Statistics = 12 - 3 = 9<br>Required ratio = 14 : 9</p>",
                    solution_hi: "<p>75.(a)<br>गणित में पुरुष शोधार्थियों की संख्या = 18 - 4 = 14<br>सांख्यिकी में पुरुष शोधार्थियों की संख्या = 12 - 3 = 9<br>अभीष्ट अनुपात = 14 : 9</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence, which contains the grammatical error.<br>People all over the world need to think what they can do for reduce harmful pollution.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence, which contains the grammatical error.<br>People all over the world need to think what they can do for reduce harmful pollution.</p>",
                    options_en: [
                        "<p>People all over the world</p>",
                        "<p>need to think</p>",
                        "<p>for reduce harmful pollution</p>",
                        "<p>what they can do</p>"
                    ],
                    options_hi: [
                        "<p>People all over the world</p>",
                        "<p>need to think</p>",
                        "<p>for reduce harmful pollution</p>",
                        "<p>what they can do</p>"
                    ],
                    solution_en: "<p>76.(c)&nbsp; for reduce harmful pollution<br>There is a prepositional error in the given sentence. The preposition &lsquo;for&rsquo; must be replaced with &lsquo;to&rsquo;. Hence, &lsquo;to reduce harmful pollution&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(c)&nbsp; for reduce harmful pollution<br>दिए गए sentence में prepositional error है। Preposition \'for\' को \'to\' से बदला जाना चाहिए। इसलिए, \'to reduce harmful pollution\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: " c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Find the correctly spelt word :</p>",
                    question_hi: "<p>77. Find the correctly spelt word :</p>",
                    options_en: [
                        "<p>millionair</p>",
                        "<p>millionare</p>",
                        "<p>millionaire</p>",
                        "<p>milionaire</p>"
                    ],
                    options_hi: [
                        "<p>millionair</p>",
                        "<p>millionare</p>",
                        "<p>millionaire</p>",
                        "<p>milionaire</p>"
                    ],
                    solution_en: "<p>77.(c) millionaire</p>",
                    solution_hi: "<p>77.(c) millionaire/करोड़पति</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the segment in the sentence, which contains the grammatical error.<br>The biggest challenge before India is that how to tackle cancer</p>",
                    question_hi: "<p>78. Identify the segment in the sentence, which contains the grammatical error.<br>The biggest challenge before India is that how to tackle cancer</p>",
                    options_en: [
                        "<p>the biggest challenge</p>",
                        "<p>before India is</p>",
                        "<p>that how to tackle cancer</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>the biggest challenge</p>",
                        "<p>before India is</p>",
                        "<p>that how to tackle cancer</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>78.(c)&nbsp;that how to tackle cancer<br>In non-interrogative sentences, a questioning word (what, where, when, why etc) is never preceded by &lsquo;that&rsquo;.. Hence, the correct sentence is The biggest challenge before India is how to tackle cancer.</p>",
                    solution_hi: "<p>78.(c)&nbsp;that how to tackle cancer<br>Non-interrogative sentences में, एक questioning शब्द (what, where, when, why आदि) के पहले कभी भी \'that\' नहीं लगाया जाता है। इसलिए, सही sentence है - The biggest challenge before India is how to tackle cancer.</p>",
                    correct: " c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate synonym of the given word.<br>Courageous</p>",
                    question_hi: "<p>79. Select the most appropriate synonym of the given word.<br>Courageous</p>",
                    options_en: [
                        "<p>Timid</p>",
                        "<p>Gutsy</p>",
                        "<p>Fearful</p>",
                        "<p>Cowardly</p>"
                    ],
                    options_hi: [
                        "<p>Timid</p>",
                        "<p>Gutsy</p>",
                        "<p>Fearful</p>",
                        "<p>Cowardly</p>"
                    ],
                    solution_en: "<p>79.(b) <strong>Gutsy-</strong> showing courage and determination.<br><strong>Courageous-</strong> having the ability to face fear or challenges bravely.<br><strong>Timid-</strong> lacking confidence.<br><strong>Fearful-</strong> feeling afraid about something.<br><strong>Cowardly-</strong> lacking bravery.</p>",
                    solution_hi: "<p>79.(b) <strong>Gutsy</strong> (साहसी) - showing courage and determination.<br><strong>Courageous</strong> (निडर) - having the ability to face fear or challenges bravely.<br><strong>Timid</strong> (डरपोक) - lacking confidence.<br><strong>Fearful</strong> (भयभीत) - feeling afraid about something.<br><strong>Cowardly</strong> (कायरतापूर्ण) - lacking bravery.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The toddler has a habit of <span style=\"text-decoration: underline;\"><strong>throwing tantrums.</strong></span></p>",
                    question_hi: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The toddler has a habit of <span style=\"text-decoration: underline;\"><strong>throwing tantrums.</strong></span></p>",
                    options_en: [
                        "<p>Expressing emotions</p>",
                        "<p>expressing frustrations</p>",
                        "<p>expressing happiness.</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>Expressing emotions</p>",
                        "<p>expressing frustrations</p>",
                        "<p>expressing happiness.</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>80.(b) expressing frustrations</p>",
                    solution_hi: "<p>80.(b) expressing frustrations/ हताशा जाहिर करना</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Choose the most appropriate option to change the voice (active / passive) form of the given sentence. <br><strong>You will be well looked after.</strong></p>",
                    question_hi: "<p>81. Choose the most appropriate option to change the voice (active / passive) form of the given sentence. <br><strong>You will be well looked after.</strong></p>",
                    options_en: [
                        "<p>They will look after you well.</p>",
                        "<p>They can look after you well.</p>",
                        "<p>They may look after you well.</p>",
                        "<p>They shall look after you well.</p>"
                    ],
                    options_hi: [
                        "<p>They will look after you well.</p>",
                        "<p>They can look after you well.</p>",
                        "<p>They may look after you well.</p>",
                        "<p>They shall look after you well.</p>"
                    ],
                    solution_en: "<p>81.(a) They will look after you well.<br>b. They <strong><span style=\"text-decoration: underline;\">can</span></strong> look after you well.(Incorrect word)<br>c. They <strong><span style=\"text-decoration: underline;\">may</span></strong> look after you well. (Incorrect word)<br>d. They <strong><span style=\"text-decoration: underline;\">shall</span></strong> look after you weft. (Incorrect word)</p>",
                    solution_hi: "<p>81.(a) They will look after you well.<br>b. They <strong><span style=\"text-decoration: underline;\">can</span> </strong>look after you well.(गलत शब्द का प्रयोग)<br>c. They <strong><span style=\"text-decoration: underline;\">may</span></strong> look after you well. (गलत शब्द का प्रयोग)<br>d. They <strong><span style=\"text-decoration: underline;\">shall</span></strong> look after you weft. (गलत शब्द का प्रयोग)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(A) while the employees<br>(B) meeting on time<br>(C) the boss arrived at the<br>(D) despite the heavy traffic<br>(E) were late</p>",
                    question_hi: "<p>82. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(A) while the employees<br>(B) meeting on time<br>(C) the boss arrived at the<br>(D) despite the heavy traffic<br>(E) were late</p>",
                    options_en: [
                        "<p>DCBAE</p>",
                        "<p>DACEB</p>",
                        "<p>BACED</p>",
                        "<p>ACEDB</p>"
                    ],
                    options_hi: [
                        "<p>DCBAE</p>",
                        "<p>DACEB</p>",
                        "<p>BACED</p>",
                        "<p>ACEDB</p>"
                    ],
                    solution_en: "<p>82.(a) <strong>DCBAE</strong><br>The given sentence starts with Part D as it introduces the main idea of the sentence, i.e. &lsquo;Despite the heavy traffic&rsquo;. Part C contains the subject of the first clause, &lsquo;the boss&rsquo; &amp; Part B states that the boss arrived at the meeting on time. So, B will follow C. Further, Part A states a contrasting idea using the conjunction &lsquo;while&rsquo; &amp; Part E states that the employees were late. So, E will follow A. Going through the options, option &lsquo;a&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>82.(a) <strong>DCBAE</strong><br>दिया गया sentence, Part D से प्रारंभ होता है क्योंकि यह sentence के मुख्य विचार &lsquo;Despite the heavy traffic&rsquo; को प्रस्तुत करता है। Part C में पहले clause का subject &lsquo;the boss&rsquo; है और Part B में कहा गया है कि boss समय पर meeting में पहुंचे। इसलिए, C के बाद B आएगा। इसके अलावा, Part A, conjunction &lsquo;while&rsquo; का उपयोग करके एक contrasting idea बताता है और Part E में कहा गया है कि employees देर से आए थे। इसलिए, A के बाद E आएगा। अतः options के माध्यम से जाने पर option &lsquo;a&rsquo; में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate ANTONYM of the given word.<br>Glory</p>",
                    question_hi: "<p>83. Select the most appropriate ANTONYM of the given word.<br>Glory</p>",
                    options_en: [
                        "<p>Delight</p>",
                        "<p>Shame</p>",
                        "<p>Honour</p>",
                        "<p>Gluttony</p>"
                    ],
                    options_hi: [
                        "<p>Delight</p>",
                        "<p>Shame</p>",
                        "<p>Honour</p>",
                        "<p>Gluttony</p>"
                    ],
                    solution_en: "<p>83.(b) <strong>Shame-</strong> a feeling of humiliation or an uncomfortable feeling of guilt.<br><strong>Glory-</strong> high honour won by notable achievements.<br><strong>Delight-</strong> great pleasure.<br><strong>Honour-</strong> regard with great respect.<br><strong>Gluttony-</strong> excess in eating.</p>",
                    solution_hi: "<p>83.(b) <strong>Shame</strong> (शर्म) - a feeling of humiliation or an uncomfortable feeling of guilt.<br><strong>Glory</strong> (गौरव) - high honour won by notable achievements.<br><strong>Delight</strong> (खुश होना) - great pleasure.<br><strong>Honour</strong> (सम्मान) - regard with great respect.<br><strong>Gluttony</strong> (पेटूपन) - excess in eating.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Find the correctly spelt word.</p>",
                    question_hi: "<p>84. Find the correctly spelt word.</p>",
                    options_en: [
                        "<p>privilege</p>",
                        "<p>previlege</p>",
                        "<p>prevelege</p>",
                        "<p>privelage</p>"
                    ],
                    options_hi: [
                        "<p>privilege</p>",
                        "<p>previlege</p>",
                        "<p>prevelege</p>",
                        "<p>privelage</p>"
                    ],
                    solution_en: "<p>84.(a) privilege</p>",
                    solution_hi: "<p>84.(a) privilege/विशेषाधिकार । </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The DDA on Saturday announced that <span style=\"text-decoration: underline;\">a water park will built</span> near the Pragati Maidan</p>",
                    question_hi: "<p>85. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The DDA on Saturday announced that <span style=\"text-decoration: underline;\">a water park will built</span> near the Pragati Maidan</p>",
                    options_en: [
                        "<p>A water park will be built</p>",
                        "<p>A water park are being built</p>",
                        "<p>A water park was being built</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>A water park will be built</p>",
                        "<p>A water park are being built</p>",
                        "<p>A water park was being built</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>85.(a) A water park will be built<br>An announcement which is given in the sentence that something will be done in the future so we will use the Simple future passive form (will/shall + be + V3(third form of the verb). Hence, &lsquo;a water park will be built&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>85.(a) A water park will be built<br>एक घोषणा जो sentence में दी गई है कि भविष्य में कुछ किया जाएगा इसलिए हम Simple future passive form का उपयोग करेंगे (will/shall + be + V3(third form of the verb)। इसलिए, \'a water park will be built\' है सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate idiom to fill in the blank.<br>When I went to Egypt and lost my wallet and passport, I was_______</p>",
                    question_hi: "<p>86. Select the most appropriate idiom to fill in the blank.<br>When I went to Egypt and lost my wallet and passport, I was_______</p>",
                    options_en: [
                        "<p>rolling up my sleeves</p>",
                        "<p>working my fingers to the bone</p>",
                        "<p>putting my nose to the grindstone</p>",
                        "<p>up in a creek without a paddle</p>"
                    ],
                    options_hi: [
                        "<p>rolling up my sleeves</p>",
                        "<p>working my fingers to the bone</p>",
                        "<p>putting my nose to the grindstone</p>",
                        "<p>up in a creek without a paddle</p>"
                    ],
                    solution_en: "<p>86.(d) <strong>up in a creek without a paddle - </strong>in a difficult situation with no clear way out.</p>",
                    solution_hi: "<p>86.(d)<strong> up in a creek without a paddle -</strong> in a difficult situation with no clear way out./ऐसी कठिन परिस्थिति में होना जहाँ से बाहर निकलने का कोई स्पष्ट उपाय न हो।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate idiom to fill in the blank. <br>The two brothers ______when their father died.</p>",
                    question_hi: "<p>87. Select the most appropriate idiom to fill in the blank. <br>The two brothers ______when their father died.</p>",
                    options_en: [
                        "<p>fell down</p>",
                        "<p>look down upon</p>",
                        "<p>got the sack</p>",
                        "<p>fell out</p>"
                    ],
                    options_hi: [
                        "<p>fell down</p>",
                        "<p>look down upon</p>",
                        "<p>got the sack</p>",
                        "<p>fell out</p>"
                    ],
                    solution_en: "<p>87.(d)<strong> fell out - </strong>to have an argument that ends a relationship.</p>",
                    solution_hi: "<p>87.(d) <strong>fell out - </strong>to have an argument that ends a relationship./ऐसा तर्क-वितर्क करना जिससे रिश्ता ख़त्म हो जाए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>Why do you <span style=\"text-decoration: underline;\"><strong>fight shy</strong></span> of me?</p>",
                    question_hi: "<p>88. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>Why do you <span style=\"text-decoration: underline;\"><strong>fight shy</strong></span> of me?</p>",
                    options_en: [
                        "<p>fight with</p>",
                        "<p>avoid</p>",
                        "<p>embarrass</p>",
                        "<p>shout at</p>"
                    ],
                    options_hi: [
                        "<p>fight with</p>",
                        "<p>avoid</p>",
                        "<p>embarrass</p>",
                        "<p>shout at</p>"
                    ],
                    solution_en: "<p>88.(b) avoid<br>Example- Before buying this phone I had always fought shy of technology.</p>",
                    solution_hi: "<p>88.(b) avoid/बचना<br>Example - Before buying this phone I had always fought shy of technology.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>Branch of medicine concerned with children and their illness.</p>",
                    question_hi: "<p>89. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>Branch of medicine concerned with children and their illness.</p>",
                    options_en: [
                        "<p>Cardiology</p>",
                        "<p>Osteopathy</p>",
                        "<p>Pediatrics</p>",
                        "<p>Morphology</p>"
                    ],
                    options_hi: [
                        "<p>Cardiology</p>",
                        "<p>Osteopathy</p>",
                        "<p>Pediatrics</p>",
                        "<p>Morphology</p>"
                    ],
                    solution_en: "<p>89.(c) <strong>Pediatrics</strong> - the branch of medicine dealing with children and their diseases.<br><strong>Cardiology</strong> - the branch of medicine that deals with diseases and abnormalities of the heart.<br><strong>Osteopathy</strong> - a system of complementary medicine involving the treatment of medical disorders through the manipulation and massage of the skeleton and musculature.<br><strong>Morphology</strong> - the study of the forms of things, in particular:</p>",
                    solution_hi: "<p>89.(c) <strong>Pediatrics</strong> - बच्चों और उनके रोगों से निपटने वाली चिकित्सा की शाखा।<br><strong>Cardiology</strong> - चिकित्सा की वह शाखा जो हृदय की बीमारियों और असामान्यताओं से संबंधित है।<br><strong>Osteopathy</strong> - चिकित्सा पद्धति की एक शाखा जो हड्डियों, जोड़ों और मांसपेशियों की मालिश के माध्यम से चिकित्सा विकारों के उपचार पर जोर देती है।<br><strong>Morphology</strong> - विशेष रूप से चीजों के रूपों का अध्ययन |</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(A) he had to take the bus<br>(B) his friend, who used to give<br>(C) to his office because he had<br>(D) him a lift, was on vacation<br>(E) never learned to drive and</p>",
                    question_hi: "<p>90. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(A) he had to take the bus<br>(B) his friend, who used to give<br>(C) to his office because he had<br>(D) him a lift, was on vacation<br>(E) never learned to drive and</p>",
                    options_en: [
                        "<p>DACEB</p>",
                        "<p>ACEBD</p>",
                        "<p>EBADC</p>",
                        "<p>BADEC</p>"
                    ],
                    options_hi: [
                        "<p>DACEB</p>",
                        "<p>ACEBD</p>",
                        "<p>EBADC</p>",
                        "<p>BADEC</p>"
                    ],
                    solution_en: "<p>90.(b) <strong>ACEBD</strong><br>The given sentence starts with Part A as it introduces the main idea of the sentence, i.e. &lsquo;He had to take the bus&rsquo;. Part C states the destination (his office) and has the conjunction &lsquo;because&rsquo; to state the reason for taking the bus. Part E states the reason for taking the bus, which is that he never learned to drive. So, E will follow C. Further, Part B talks about his friend and Part E states that his friend was on vacation. So, D will follow B. Going through the options, option &lsquo;b&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>90.(b) <strong>ACEBD</strong><br>दिया गया sentence, Part A से प्रारंभ होता है क्योंकि यह sentence के मुख्य विचार &lsquo;He had to take the bus&rsquo; को प्रस्तुत करता है। Part C गंतव्य (his office) बताता है और इसमे bus लेने का कारण बताने के लिए conjunction &lsquo;because&rsquo; है। Part E में bus लेने का कारण यह बताया गया है कि उसने कभी कार चलाना नहीं सीखा। इसलिए, C के बाद E आएगा। इसके अलावा, Part B उसके friend के बारे में बात करता है और Part E बताता है कि उसका friend छुट्टी पर था। इसलिए, B के बाद D आएगा। अतः options के माध्यम से जाने पर option &lsquo;b&rsquo; में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Give the synonym of &ldquo;offensive&rdquo;.</p>",
                    question_hi: "<p>91. Give the synonym of &ldquo;offensive&rdquo;.</p>",
                    options_en: [
                        "<p>courteous</p>",
                        "<p>Aggressive</p>",
                        "<p>Sympathetic</p>",
                        "<p>Defending</p>"
                    ],
                    options_hi: [
                        "<p>courteous</p>",
                        "<p>Aggressive</p>",
                        "<p>Sympathetic</p>",
                        "<p>Defending</p>"
                    ],
                    solution_en: "<p>91.(b) Aggressive</p>",
                    solution_hi: "<p>91.(b) Aggressive / आक्रामक।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>Will he have written a letter ?</p>",
                    question_hi: "<p>92. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>Will he have written a letter ?</p>",
                    options_en: [
                        "<p>Will a letter have written by him?</p>",
                        "<p>Will a letter be written by him?</p>",
                        "<p>Will a letter to be written by him?</p>",
                        "<p>Will a letter have been written by him?</p>"
                    ],
                    options_hi: [
                        "<p>Will a letter have written by him?</p>",
                        "<p>Will a letter be written by him?</p>",
                        "<p>Will a letter to be written by him?</p>",
                        "<p>Will a letter have been written by him?</p>"
                    ],
                    solution_en: "<p>92.(d) Will a letter have been written by him ?<br>(a). Will a letter <strong>have written</strong> by him? (Been is missing)<br>(b). Will a letter <strong>be written </strong>by hi? (Tense is changed)<br>(c). Will a letter <strong>to be written</strong> by him? (Incorrect structure)</p>",
                    solution_hi: "<p>92.(d) Will a letter have been written by him?<br>(a) Will a letter <strong>have written</strong> by him? (Been का प्रयोग नहीं हुआ है)<br>(b). Will a letter <strong>be written</strong> by him? (Tense बदल गया है)<br>(c). Will a <strong>letter to be written</strong> by him? (गलत वाक्य संरचना)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate ANTONYM of the given word .<br>Trivial</p>",
                    question_hi: "<p>93. Select the most appropriate ANTONYM of the given word .<br>Trivial</p>",
                    options_en: [
                        "<p>Lavish</p>",
                        "<p>Vain</p>",
                        "<p>Liable</p>",
                        "<p>Essential</p>"
                    ],
                    options_hi: [
                        "<p>Lavish</p>",
                        "<p>Vain</p>",
                        "<p>Liable</p>",
                        "<p>Essential</p>"
                    ],
                    solution_en: "<p>93.(d) <strong>Essential-</strong> a thing that is absolutely necessary.<br><strong>Trivial-</strong> of very little importance and value.<br><strong>Lavish-</strong> something that is rich.<br><strong>Vain-</strong> producing no result or useless.<br><strong>Liable-</strong> having legal responsibility for something or someone.</p>",
                    solution_hi: "<p>93.(d) <strong>Essential</strong> (आवश्यक) - a thing that is absolutely necessary.<br><strong>Trivial</strong> (मामूली) - of very little importance and value.<br><strong>Lavish</strong> (प्रचुर मात्रा में) - something that is rich.<br><strong>Vain</strong> (व्यर्थ) - producing no result or useless.<br><strong>Liable</strong> (उत्तरदायी) - having legal responsibility for something or someone.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank. <br>Which _______ do you usually take to reach your office?</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank. <br>Which _______ do you usually take to reach your office?</p>",
                    options_en: [
                        "<p>rude</p>",
                        "<p>route</p>",
                        "<p>root</p>",
                        "<p>rout</p>"
                    ],
                    options_hi: [
                        "<p>rude</p>",
                        "<p>route</p>",
                        "<p>root</p>",
                        "<p>rout</p>"
                    ],
                    solution_en: "<p>94.(b) route<br>&lsquo;Route&rsquo; means a particular path between places. The given sentence is asking about a particular path to reach the office. Hence, &lsquo;route&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>94.(b) route<br>&lsquo;Route&rsquo; का अर्थ है स्थानों के बीच एक विशेष मार्ग (particular path)। दिए गए sentence में कार्यालय तक पहुँचने के लिए एक विशेष मार्ग के बारे में पूछा जा रहा है। अतः, &lsquo;route&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>A fixed territory in which authority can be exercised</p>",
                    question_hi: "<p>95. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>A fixed territory in which authority can be exercised</p>",
                    options_en: [
                        "<p>Jurisdiction</p>",
                        "<p>Judiciary</p>",
                        "<p>Jurisprudence</p>",
                        "<p>Juristic</p>"
                    ],
                    options_hi: [
                        "<p>Jurisdiction</p>",
                        "<p>Judiciary</p>",
                        "<p>Jurisprudence</p>",
                        "<p>Juristic</p>"
                    ],
                    solution_en: "<p>95.(a) <strong>Jurisdiction</strong><br><strong>Jurisdiction</strong> - the official power to make legal decisions and judgements.<br><strong>Judiciary</strong> - the judicial authorities of a country; judges collectively.<br><strong>Jurisprudence</strong> - the theory or philosophy of law.<br><strong>Juristic</strong> - of or relating to a jurist or jurisprudence</p>",
                    solution_hi: "<p>95.(a) <strong>Jurisdiction</strong><br><strong>Jurisdiction</strong> - कानूनी निर्णय और निर्णय लेने की आधिकारिक शक्ति।<br><strong>Judiciary</strong> - किसी देश के न्यायिक अधिकारी; सामूहिक रूप से न्यायाधीश।<br><strong>Jurisprudence</strong> - कानून का सिद्धांत या दर्शन।<br><strong>Juristic</strong> - एक न्यायविद या न्यायशास्त्र से संबंधित</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:-&nbsp;</strong><br>Scarcity is not created by war; (96)_____ is a permanent characteristic of all humans (97)______and has been faced by the whole human (98)_____. It springs from the fact that the material resources of the world are (99)______and that our ability to make use of those resources is even more limited (100)_____ our ignorance.&nbsp;<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:-&nbsp;</strong><br>Scarcity is not created by war; (96)_____ is a permanent characteristic of all humans (97)______and has been faced by the whole human (98)_____. It springs from the fact that the material resources of the world are (99)______and that our ability to make use of those resources is even more limited (100)_____ our ignorance.&nbsp;<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: [
                        "<p>there</p>",
                        "<p>these</p>",
                        "<p>those</p>",
                        "<p>it</p>"
                    ],
                    options_hi: [
                        "<p>there</p>",
                        "<p>these</p>",
                        "<p>those</p>",
                        "<p>it</p>"
                    ],
                    solution_en: "<p>96.(d) it, &ldquo;It&rdquo; is the pronoun here and it refers to scarcity which is already mentioned in the first line.</p>",
                    solution_hi: "<p>96.(d) it , \"It\" यहाँ सर्वनाम है और यह कमी को बता रहा है जिसका उल्लेख पहली पंक्ति में पहले ही किया जा चुका है।</p>",
                    correct: " d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:-&nbsp;</strong><br>Scarcity is not created by war; (96)_____ is a permanent characteristic of all humans (97)______and has been faced by the whole human (98)_____. It springs from the fact that the material resources of the world are (99)______and that our ability to make use of those resources is even more limited (100)_____ our ignorance.&nbsp;<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:-&nbsp;</strong><br>Scarcity is not created by war; (96)_____ is a permanent characteristic of all humans (97)______and has been faced by the whole human (98)_____. It springs from the fact that the material resources of the world are (99)______and that our ability to make use of those resources is even more limited (100)_____ our ignorance.&nbsp;<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: [
                        "<p>behaviour</p>",
                        "<p>cities</p>",
                        "<p>nations</p>",
                        "<p>societies</p>"
                    ],
                    options_hi: [
                        "<p>behaviour</p>",
                        "<p>cities</p>",
                        "<p>nations</p>",
                        "<p>societies</p>"
                    ],
                    solution_en: "<p>97.(d) societies, Human society is a social group sharing the same geographical territory.<br>So , option (d) is the answer</p>",
                    solution_hi: "<p>97.(d) societies<br>मानव समाज समान भौगोलिक क्षेत्र साझा करने वाला एक सामाजिक समूह है। अतः, विकल्प (d) उत्तर है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:-&nbsp;</strong><br>Scarcity is not created by war; (96)_____ is a permanent characteristic of all humans (97)______and has been faced by the whole human (98)_____. It springs from the fact that the material resources of the world are (99)______and that our ability to make use of those resources is even more limited (100)_____ our ignorance.&nbsp;<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:-&nbsp;</strong><br>Scarcity is not created by war; (96)_____ is a permanent characteristic of all humans (97)______and has been faced by the whole human (98)_____. It springs from the fact that the material resources of the world are (99)______and that our ability to make use of those resources is even more limited (100)_____ our ignorance.&nbsp;<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: [
                        "<p>space</p>",
                        "<p>race</p>",
                        "<p>kingdom</p>",
                        "<p>beings</p>"
                    ],
                    options_hi: [
                        "<p>space</p>",
                        "<p>race</p>",
                        "<p>kingdom</p>",
                        "<p>beings</p>"
                    ],
                    solution_en: "<p>98.(b) Race <br><strong>Race</strong> - caste, species<br><strong>Space</strong> - an unoccupied area of land<br><strong>Kingdom</strong> - a country, state, or a territory ruled by a king or queen.<br><strong>Beings</strong> - existence.<br>Option <strong>(b) </strong>fits to the context of the sentence.</p>",
                    solution_hi: "<p>98.(b) Race<br><strong>Race</strong> - नस्ल <br><strong>Space</strong> - खाली स्थान <br><strong>Kingdom</strong> - साम्राज्य <br><strong>Beings</strong> - अस्तित्व। <br>विकल्प <strong>(b) </strong>वाक्य के संदर्भ में उचित है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:-&nbsp;</strong><br>Scarcity is not created by war; (96)_____ is a permanent characteristic of all humans (97)______and has been faced by the whole human (98)_____. It springs from the fact that the material resources of the world are (99)______and that our ability to make use of those resources is even more limited (100)_____ our ignorance.&nbsp;<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:-&nbsp;</strong><br>Scarcity is not created by war; (96)_____ is a permanent characteristic of all humans (97)______and has been faced by the whole human (98)_____. It springs from the fact that the material resources of the world are (99)______and that our ability to make use of those resources is even more limited (100)_____ our ignorance.&nbsp;<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: [
                        "<p>increased</p>",
                        "<p>limited</p>",
                        "<p>restricted</p>",
                        "<p>declined</p>"
                    ],
                    options_hi: [
                        "<p>increased</p>",
                        "<p>limited</p>",
                        "<p>restricted</p>",
                        "<p>declined</p>"
                    ],
                    solution_en: "<p>99.(b) limited, Read the sentence carefully, more limited is given in the latter part of the sentence.</p>",
                    solution_hi: "<p>99.(b) limited<br>वाक्य को ध्यान से पढ़िए, वाक्य के पिछले भाग में more limited दिया गया है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:-&nbsp;</strong><br>Scarcity is not created by war; (96)_____ is a permanent characteristic of all humans (97)______and has been faced by the whole human (98)_____. It springs from the fact that the material resources of the world are (99)______and that our ability to make use of those resources is even more limited (100)_____ our ignorance.&nbsp;<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:-&nbsp;</strong><br>Scarcity is not created by war; (96)_____ is a permanent characteristic of all humans (97)______and has been faced by the whole human (98)_____. It springs from the fact that the material resources of the world are (99)______and that our ability to make use of those resources is even more limited (100)_____ our ignorance.&nbsp;<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: [
                        "<p>of</p>",
                        "<p>by</p>",
                        "<p>for</p>",
                        "<p>from</p>"
                    ],
                    options_hi: [
                        "<p>of</p>",
                        "<p>by</p>",
                        "<p>for</p>",
                        "<p>from</p>"
                    ],
                    solution_en: "<p>100.(b) by , The preposition &ldquo;by&rdquo; should be used with &ldquo;limited&rdquo;. <br>Limited to (a described quantity or range)<br>Limited by (an external limiting force or factor) So , option (b) is the most logical answer.</p>",
                    solution_hi: "<p>100.(b) by , Preposition \"by\" का उपयोग \"limited\" के साथ किया जाना चाहिए।<br>Limited to (एक वर्णित मात्रा या सीमा)<br>Limited by (एक बाहरी सीमित बल या कारक)<br>इसलिए, विकल्प (b) सबसे तार्किक उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>