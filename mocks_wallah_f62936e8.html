<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. After working for 8 days, Rakhi finds that only 10 percent of the work is completed. She employs Poonam who is 20 percent more efficient than Rakhi. How many more days will they together take to complete the remaining work ?</p>",
                    question_hi: "<p>1. 8 दिनों तक काम करने के बाद, राखी को पता चलता है कि अभी तक केवल 10 प्रतिशत काम पूरा हुआ है। वह पूनम को काम पर रखती है, जो राखी से 20 प्रतिशत अधिक कुशल है। शेष काम को पूरा करने में उन्हें कितने दिन और लगेंगे ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>340</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>340</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>340</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>340</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>"],
                    solution_en: "<p>1.(b)<br>Time taken by Rakhi to complete the whole work = 8 &times; 10 = 80 days<br>Ratio of efficiency of Poonam and Rakhi= 6 : 5<br>Total work = 5 &times; 80 = 400 unit<br>Remaining work = 400 &times; 90% = 360 unit<br>Time taken by both to complete the remaining work = <math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>11</mn></mfrac></math> days</p>",
                    solution_hi: "<p>1.(b)<br>राखी द्वारा पूरा कार्य पूरा करने में लिया गया समय = 8 &times; 10 = 80 दिन<br>पूनम और राखी की दक्षता का अनुपात = 6 : 5<br>कुल कार्य = 5 &times; 80 = 400 इकाई<br>शेष कार्य = 400 &times; 90% = 360 इकाई<br>शेष कार्य को पूरा करने में दोनों द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>11</mn></mfrac></math> दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "2. R can do a work in 90 days. What percent work of the total work can be done by R in 45 days ?",
                    question_hi: "2. R एक काम को 90 दिनों में पूरा कर सकता है। कुल काम का कितना प्रतिशत काम R द्वारा 45 दिनों में पूरा किया जा सकता है ?",
                    options_en: [" 70 percent", " 50 percent", 
                                " 56 percent", " 65 percent"],
                    options_hi: ["  70 प्रतिशत", "  50 प्रतिशत",
                                "  56 प्रतिशत", " 65 प्रतिशत"],
                    solution_en: "2.(b)<br />Work <math display=\"inline\"><mo>∝</mo></math> Time (when efficiency is same)<br />Ratio of work done by R = 90 : 45 = 2 : 1<br />Required % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> × 100 = 50%",
                    solution_hi: "2.(b)<br />कार्य ∝ समय (जब दक्षता समान हो)<br />R द्वारा किये गये कार्य का अनुपात = 90 : 45 = 2 : 1<br />आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> × 100 = 50%<br /> ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. If 72 binders can bind 720 books in 22 days, how many binders will be required to bind 660 books in 12 days ?</p>",
                    question_hi: "<p>3. यदि 72 जिल्दसाज़ 720 पुस्तकों की जिल्दबंदी 22 दिनों में कर सकते हैं, तो 12 दिनों में 660 पुस्तकों की जिल्दबंदी करने के लिए कितने जिल्दसाजों की आवश्यकता होगी ?</p>",
                    options_en: ["<p>121</p>", "<p>120</p>", 
                                "<p>123</p>", "<p>124</p>"],
                    options_hi: ["<p>121</p>", "<p>120</p>",
                                "<p>123</p>", "<p>124</p>"],
                    solution_en: "<p>3.(a)<br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo><mfrac><mrow><mn>72</mn><mo>&#215;</mo><mn>22</mn></mrow><mrow><mn>720</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>12</mn></mrow><mn>660</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>55</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> x = 121</p>",
                    solution_hi: "<p>3.(a)<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo><mfrac><mrow><mn>72</mn><mo>&#215;</mo><mn>22</mn></mrow><mrow><mn>720</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>12</mn></mrow><mn>660</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>55</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> x = 121</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Swapnil can build a sofa in 16 days. Working together with Revathi he can finish the work in&nbsp;<math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> days. Find the number of days taken by Revathi to complete the work if she works alone.</p>",
                    question_hi: "<p>4. स्वप्रिल 16 दिनों में एक सोफा बना सकता है। रेवती के साथ मिलकर काम करते हुए वह काम को <math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> दिनों में पूरा कर सकता है। यदि रेवती अकेले काम करती है, तो काम पूरा करने में रेवती को कितने दिन लगेंगे ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> days</p>", "<p>6 days</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> दिन</p>", "<p>6 दिन</p>"],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483831584.png\" alt=\"rId4\" width=\"251\" height=\"194\"><br>Efficiency of Revathi = 17 - 8 = 9 unit<br>So, time taken by Revathi alone to complete the whole work = <math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483831730.png\" alt=\"rId5\" width=\"217\" height=\"202\"><br>रेवती की क्षमता= 17 - 8 = 9 इकाई<br>अतः रेवती द्वारा अकेले पूरा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A and B can complete a work alone in 20 days and 60 days respectively. They began the work together but A left the work after some days and B completed the remaining work in 12 days. After how many days from the beginning A left the work ?</p>",
                    question_hi: "<p>5. A और B अकेले-अकेले तौर पर एक काम को क्रमशः 20 दिन और 60 दिन में पूरा कर सकते हैं। उन्होंने एक साथ काम शुरू किया लेकिन A ने कुछ दिनों के बाद काम छोड़ दिया और B ने शेष काम 12 दिनों में पूरा किया। काम की शुरुआत से कितने दिनों के बाद A ने काम छोड़ दिया ?</p>",
                    options_en: ["<p>15 days</p>", "<p>10 days</p>", 
                                "<p>9 days</p>", "<p>12 days</p>"],
                    options_hi: ["<p>15 दिन</p>", "<p>10 दिन</p>",
                                "<p>9 दिन</p>", "<p>12 दिन</p>"],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483831898.png\" alt=\"rId6\" width=\"155\" height=\"155\"><br>Work done by B in 12 days = 12 &times; 1 = 12 unit<br>Remaining work = 60 - 12 = 48 unit<br>time taken by (A + B) to complete the work = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 12 days<br>So, 12 days from the beginning A left the work.</p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483832088.png\" alt=\"rId7\" width=\"155\" height=\"160\"><br>B द्वारा 12 दिनों में किया गया कार्य = 12 &times; 1 = 12 इकाई&nbsp;<br>शेष कार्य = 60-12 = 48 इकाई <br>कार्य पूरा करने में (A + B) द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 12 दिन <br>तो, शुरुआत से 12 दिन बाद A ने काम छोड़ दिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If 18 men can finish a work in 13 days. Then in how many days will 54 men finish the same work ?</p>",
                    question_hi: "<p>6. यदि 18 आदमी एक काम को 13 दिनों में पूरा कर सकते हैं, तो 54 आदमी उसी काम को कितने दिनों में पूरा करेंगे ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>", "<p>9 days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> days</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>", "<p>9 दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> दिन</p>"],
                    solution_en: "<p>6.(a)<br>Let the required no of days be x<br>ATQ,<br>18&times;13 = 54&times;<math display=\"inline\"><mi>&#160;</mi><mi>x</mi></math><br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>13</mn></mrow><mn>54</mn></mfrac><mo>=</mo><mfrac><mn>13</mn><mn>3</mn></mfrac></math> days</p>",
                    solution_hi: "<p>6.(a)<br>माना दिनों की आवश्यक संख्या <math display=\"inline\"><mi>x</mi></math> है<br>प्रश्न के अनुसार,<br>18 &times; 13 = 54 &times;<math display=\"inline\"><mi>&#160;</mi><mi>x</mi></math><br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>13</mn></mrow><mn>54</mn></mfrac><mo>=</mo><mfrac><mn>13</mn><mn>3</mn></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Sonu alone can make a table in 125 days while Monu alone can make it in 250 days. Monu started the work and worked for 75 days. In how many days Sonu can finish the remaining work ?</p>",
                    question_hi: "<p>7. सोनू अकेले एक मेज को 125 दिनों में बना सकता है जबकि मोनू अकेले इसे 250 दिनों में बना सकता है। मोनू काम शुरू करता है और 75 दिनों तक काम करता है। सोनू शेष काम कितने दिनों में पर में पूरा कर सकता है ?</p>",
                    options_en: ["<p>135 days</p>", "<p>120 days</p>", 
                                "<p>100 days</p>", "<p>87.5 days</p>"],
                    options_hi: ["<p>135 दिन</p>", "<p>120 दिन</p>",
                                "<p>100 दिन</p>", "<p>87.5 दिन</p>"],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483832261.png\" alt=\"rId8\" width=\"188\" height=\"146\"><br>Work done by Monu for 75 days = 1 &times; 75 = 75 unit<br>Remaining work = 250 - 75 = 175 unit<br>Time taken by Sonu to complete the remaining work = <math display=\"inline\"><mfrac><mrow><mn>175</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 87.5 days</p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483832436.png\" alt=\"rId9\" width=\"187\" height=\"169\"><br>मोनू द्वारा 75 दिनों में किया गया कार्य = 1 &times; 75 = 75 इकाई<br>शेष कार्य = 250 - 75 = 175 इकाई<br>शेष कार्य को पूरा करने में सोनू द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>175</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 87.5 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. X1, X2 and X3 can do a work in 90, 45 and 67.5 days respectively. All three of them began the work together but X2 left 13.5 days before the completion of the work. In how many days was the work completed ?</p>",
                    question_hi: "<p>8. X1, X2 और X3 एक काम को क्रमशः 90, 45 और 67.5 दिनों में पूरा कर सकते हैं। तीनों ने एक साथ मिलकर काम शुरू किया, लेकिन X2 ने काम पूरा होने से 13.5 दिन पहले ही काम छोड़ दिया। काम कितने दिनों में पूरा हुआ ?</p>",
                    options_en: ["<p>25 days</p>", "<p>24 days</p>", 
                                "<p>18 days</p>", "<p>27 days</p>"],
                    options_hi: ["<p>25 दिन</p>", "<p>24 दिन</p>",
                                "<p>18 दिन</p>", "<p>27 दिन</p>"],
                    solution_en: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483832544.png\" alt=\"rId10\" width=\"192\" height=\"151\"><br>If X2 do not leave the work then he did the work in 13.5 days = 13.5 &times; 30 = 405 unit<br>X2 works till the work is completed, then total work = 1350 + 405 = 1755 unit<br>Time taken by all of them to complete the whole work = <math display=\"inline\"><mfrac><mrow><mn>1755</mn></mrow><mrow><mn>15</mn><mo>+</mo><mn>30</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1755</mn><mn>65</mn></mfrac></math> = 27 days<br>So, the whole work was completed in 27 days.</p>",
                    solution_hi: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483832669.png\" alt=\"rId11\"><br>यदि X2 कार्य नहीं छोड़ता है तो वह कार्य को 13.5 दिन में पूरा करेगा = 13.5 &times; 30 = 405 इकाई<br>X2 कार्य पूरा होने तक कार्य करता है, तो कुल कार्य = 1350 + 405 = 1755 इकाई<br>उन सभी द्वारा पूरा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>1755</mn></mrow><mrow><mn>15</mn><mo>+</mo><mn>30</mn><mo>+</mo><mn>20</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1755</mn><mn>65</mn></mfrac></math> = 27 दिन<br>तो, पूरा काम 27 दिनों में पूरा हो गया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. P and Q together can complete a work in 30 days. Q and R together can complete the same work in 37.5 days. R and P together can complete the same work in 150 days. In how many days can all the three complete the same work while working together ?</p>",
                    question_hi: "<p>9. P और Q मिलकर एक काम को 30 दिनों में पूरा कर सकते हैं। Q और R मिलकर उसी काम को 37.5 दिनों में पूरा कर सकते हैं। R और P मिलकर उसी काम को 150 दिनों में पूरा कर सकते हैं। तीनों एक साथ काम करते हुए उसी काम को कितने दिनों में पूरा कर सकते हैं ?</p>",
                    options_en: ["<p>20 days</p>", "<p>25 days</p>", 
                                "<p>35 days</p>", "<p>30 days</p>"],
                    options_hi: ["<p>20 दिन</p>", "<p>25 दिन</p>",
                                "<p>35 दिन</p>", "<p>30 दिन</p>"],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483832773.png\" alt=\"rId12\" width=\"249\" height=\"151\"><br>Efficiency (P + Q + R) = <math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>20</mn><mo>+</mo><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>2</mn></mfrac><mo>&#160;</mo></math>= 25 unit<br>Time taken by all of them to complete the whole work = <math display=\"inline\"><mfrac><mrow><mn>750</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = 30 days</p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483832968.png\" alt=\"rId13\" width=\"246\" height=\"160\"><br>दक्षता (P + Q + R) = <math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>20</mn><mo>+</mo><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>2</mn></mfrac><mo>&#160;</mo></math>= 25 इकाई <br>उन सभी द्वारा पूरा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>750</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = 30 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Arvind can do a work in 20 days and Bakshi in 22 days. If they work on it together for 5 days, then the fraction of the work that is left is:</p>",
                    question_hi: "<p>10. अरविंद एक काम को 20 दिन में और बक्शी 22 कर सकता है | यदि वे दोनो इस पर 5 दिन कार्य करते है तो बचे हुए कार्य का अनुपात क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>44</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>28</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>44</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>28</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483833079.png\" alt=\"rId14\" width=\"186\" height=\"146\"><br>Work done by both in 5 days = (11+10) &times; 5 = 105 unit<br>Remaining work = 220-105 = 115 unit<br>Required fraction = <math display=\"inline\"><mfrac><mrow><mn>115</mn></mrow><mrow><mn>220</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>44</mn></mfrac></math></p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483833192.png\" alt=\"rId15\" width=\"201\" height=\"171\"><br>दोनों द्वारा 5 दिन में किया गया कार्य = (11 + 10) &times; 5 = 105 इकाई<br>शेष कार्य = 220-105 = 115 इकाई<br>अभीष्ट भिन्न = <math display=\"inline\"><mfrac><mrow><mn>115</mn></mrow><mrow><mn>220</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>44</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. A and B can complete a work in <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days while B and C can complete the same work in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math> days and C and A can complete the same work in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math> days. If all three of them work together, what fraction of the same work can they complete in 2 days ?</p>",
                    question_hi: "<p>11. A और B एक काम को <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिनों में पूरा कर सकते हैं जबकि B और C उसी काम को <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math>दिनों में पूरा कर सकते हैं तथा C और A उसी काम को <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math> दिनों में पूरा कर सकते हैं। यदि वे तीनों एक साथ मिलकर काम करते हैं, तो वे समान काम का कितना हिस्सा 2 दिनों में पूरा कर सकते हैं ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>225</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>127</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>277</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>225</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>127</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>277</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>11.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483833344.png\" alt=\"rId16\" width=\"259\" height=\"170\"><br>work done by (A + B + C) in one day = <math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 137.5 units<br>According to question,<br>work done by (A + B + C) in two days = 275 units<br>Now, Required fraction of work = <math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>11.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483833491.png\" alt=\"rId17\" width=\"248\" height=\"171\"><br>(A + B + C) द्वारा एक दिन में किया गया कार्य = <math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 137.5 इकाई<br>प्रश्न के अनुसार,<br>(A + B + C) द्वारा दो दिनों में किया गया कार्य = 275 इकाई<br>अतः, कार्य का आवश्यक हिस्सा =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>275</mn></mrow><mrow><mn>336</mn></mrow></mfrac></math></p>",
                    correct: " d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. A can complete a work in 7 hours, B and C can complete it in <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> hours, and C and A can complete it in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math> hours. How much time will B take to complete the work alone ?</p>",
                    question_hi: "<p>12. A एक काम को 7 घंटे में पूरा कर सकता है, B और C इसे <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटे में पूरा कर सकते हैं, और C और A इसे <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math>घंटे में पूरा कर सकते हैं। B को अकेले इस काम को पूरा करने में कितना समय लगेगा ?</p>",
                    options_en: ["<p>22 hours</p>", "<p>20 hours</p>", 
                                "<p>21 hours</p>", "<p>24 hours</p>"],
                    options_hi: ["<p>22 घंटे</p>", "<p>20 घंटे</p>",
                                "<p>21 घंटे</p>", "<p>24 घंटे</p>"],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483833595.png\" alt=\"rId18\" width=\"216\" height=\"168\"><br>Efficiency of (C + A - B - C) = 6 - 4&nbsp;<br>Efficiency of (A - B) = 2 unit<br>Efficiency of B = A - 2<br>So, efficiency of B = 3 - 2 = 1 unit<br>Work done by B = <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 21 hours</p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483833696.png\" alt=\"rId19\" width=\"216\" height=\"178\"><br>(C + A - B - C) की क्षमता = 6 - 4&nbsp;<br>(A - B) की क्षमता = 2 इकाई<br>B की क्षमता = A - 2<br>तो, B की दक्षता = 3 - 2 = 1 इकाई<br>B द्वारा किया गया कार्य = <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 21 घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Seema can do a work in 120 days, Anjali can do the same work in 144 days and Soniya can do the same work in 180 days. If they do that work together and they are paid Rs. 3300, then what is the share of Anjali ?</p>",
                    question_hi: "<p>13. सीमा एक काम को 120 दिनों में पूरा कर सकती है, अंजलि उसी काम को 144 दिनों में पूरा कर सकती है और सोनिया उसी काम को 180 दिनों में पूरा कर सकती है। यदि वे तीनों उस काम को एक साथ मिलकर करते हैं और उन्हें 3300 रुपए का भुगतान किया जाता है, तो भुगतान में से अंजलि का हिस्सा कितना है ?</p>",
                    options_en: ["<p>Rs. 1100</p>", "<p>Rs. 2000</p>", 
                                "<p>Rs. 2500</p>", "<p>Rs. 1700</p>"],
                    options_hi: ["<p>1100 रुपए</p>", "<p>2000 रुपए</p>",
                                "<p>2500 रुपए</p>", "<p>1700 रुपए</p>"],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483833820.png\" alt=\"rId20\" width=\"271\" height=\"171\"><br>As we know that , Wages <math display=\"inline\"><mo>&#8733;</mo></math> efficiency <br>Share of Anjali = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math> &times; 3300 = ₹1100</p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483833947.png\" alt=\"rId21\" width=\"264\" height=\"177\"><br>जैसा कि हम जानते हैं, मजदूरी &prop; दक्षता<br>अंजलि का हिस्सा = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math> &times; 3300 = ₹1100</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. S1 alone can do a work in 16 days and S2 alone can do the same work in 80 days. With help of S3, they finish the same work in 6 days. In how many days S3 alone can do the same work ?</p>",
                    question_hi: "<p>14. S1 अकेले एक काम को 16 दिनों में कर सकता है और S2 अकेले उसी काम को 80 दिनों में कर सकता है। S3 की सहायता से वे उसी काम को 6 दिनों में पूरा करते हैं। S3 अकेले उसी काम को कितने दिनों में पूरा कर सकता है ?</p>",
                    options_en: ["<p>9 days</p>", "<p>7 days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> days</p>", "<p>12 days</p>"],
                    options_hi: ["<p>9 दिन</p>", "<p>7 दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>दिन</p>", "<p>12 दिन</p>"],
                    solution_en: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483834083.png\" alt=\"rId22\" width=\"257\" height=\"177\"><br>Efficiency of S3 = 40 - (15 + 3) = 22 unit<br>Time taken by S3 alone to complete the whole work = <math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>11</mn></mfrac></math>&nbsp;days</p>",
                    solution_hi: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483834211.png\" alt=\"rId23\" width=\"250\" height=\"169\"><br>S3 की दक्षता = 40 - (15 + 3) = 22 इकाई&nbsp;<br>S3 द्वारा अकेले पूरा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>11</mn></mfrac></math> दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Z1, Z2 and Z3 can do a work in 100, 50 and 75 days respectively. All three of them began the work together but Z2 left 15 days before the completion of the work. In how many days was the work completed ?</p>",
                    question_hi: "<p>15. Z1, Z2 और Z3 एक काम को क्रमशः 100, 50 और 75 दिनों में पूरा कर सकते हैं। उन तीनों ने एक साथ मिलकर काम शुरू किया लेकिन Z2 ने काम पूरा होने से 15 दिन पहले काम छोड़ दिया। काम कितने दिनों में पूरा हुआ ?</p>",
                    options_en: ["<p>30 days</p>", "<p>45 days</p>", 
                                "<p>35 days</p>", "<p>40 days</p>"],
                    options_hi: ["<p>30 दिन</p>", "<p>45 दिन</p>",
                                "<p>35 दिन</p>", "<p>40 दिन</p>"],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483834346.png\" alt=\"rId24\" width=\"253\" height=\"200\"><br>Work done by Z2 in 15 days = 15 &times; 6 = 90 unit<br>If Z2 works till the work is completed, then total work = 300 + 90 = 390 unit<br>Time taken by all of them to complete the whole work = <math display=\"inline\"><mfrac><mrow><mn>390</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>390</mn><mn>13</mn></mfrac></math> = 30 days</p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731483834508.png\" alt=\"rId25\" width=\"247\" height=\"188\"><br>Z2 द्वारा 15 दिनों में किया गया कार्य = 15 &times; 6 = 90 इकाई<br>यदि Z2 कार्य पूरा होने तक कार्य करता है, तो कुल कार्य = 300 + 90 = 390 इकाई<br>उन सभी द्वारा पूरा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>390</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>390</mn><mn>13</mn></mfrac></math> = 30 दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>