<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the option that can be used as a one-word substitute for the given group of words.<br>The study of ancient things such as art, graves, ruins etc.</p>",
                    question_hi: "<p>1. Select the option that can be used as a one-word substitute for the given group of words.<br>The study of ancient things such as art, graves, ruins etc.</p>",
                    options_en: ["<p>Astrology</p>", "<p>Archaeology</p>", 
                                "<p>Geology</p>", "<p>Meteorology</p>"],
                    options_hi: ["<p>Astrology</p>", "<p>Archaeology</p>",
                                "<p>Geology</p>", "<p>Meteorology</p>"],
                    solution_en: "<p>1.(b) <strong>Archaeology</strong>- the study of ancient things such as art, graves, ruins etc.<br><strong>Astrology</strong>- the study of the movements and relative positions of celestial bodies interpreted as having an influence on human affairs and the natural world.<br><strong>Geology</strong>- the science that deals with the earth\'s physical structure and substance, its history, and the processes that act on it.<br><strong>Meteorology</strong>- the branch of science concerned with the processes and phenomena of the atmosphere, especially as a means of forecasting the weather.</p>",
                    solution_hi: "<p>1.(b) <strong>Archaeology </strong>(पुरातत्त्व विज्ञान) - the study of ancient things such as art, graves, ruins etc.<br><strong>Astrology </strong>(ज्योतिष शास्त्र) - the study of the movements and relative positions of celestial bodies interpreted as having an influence on human affairs and the natural world.<br><strong>Geology </strong>(भू-गर्भ शास्त्र) - the science that deals with the earth\'s physical structure and substance, its history, and the processes that act on it.<br><strong>Meteorology </strong>(मौसम विज्ञान) - the branch of science concerned with the processes and phenomena of the atmosphere, especially as a means of forecasting the weather.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "2.  Select the term which means the same as the given group of words.<br />A continuing unpleasant situation, created when one problem causes another problem that then makes the first problem worse",
                    question_hi: "<p>2. Select the term which means the same as the given group of words.<br>A continuing unpleasant situation, created when one problem causes another problem that then makes the first problem worse</p>",
                    options_en: ["<p>Vicious cycle</p>", "<p>Round cycle</p>", 
                                "<p>Repetitive cycle</p>", "<p>Enclosed cycle</p>"],
                    options_hi: ["<p>Vicious cycle</p>", "<p>Round cycle</p>",
                                "<p>Repetitive cycle</p>", "<p>Enclosed cycle</p>"],
                    solution_en: "<p>2.(a) <strong>Vicious cycle-</strong> a continuing unpleasant situation, created when one problem causes another problem that then makes the first problem worse.</p>",
                    solution_hi: "<p>2.(a)<strong> Vicious cycle- </strong>a continuing unpleasant situation, created when one problem causes another problem that then makes the first problem worse./एक निरंतर अप्रिय स्थिति, जो तब उत्पन्न होती है जब एक समस्या दूसरी समस्या को जन्म देती है जो पहली समस्या को और बदतर बना देती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3. Select the option that can be used as a one-word substitute for the given group of words.<br />A person who has supreme power or authority",
                    question_hi: "<p>3. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who has supreme power or authority</p>",
                    options_en: ["<p>Secular</p>", "<p>Sovereign</p>", 
                                "<p>Diversity</p>", "<p>Foreign</p>"],
                    options_hi: ["<p>Secular</p>", "<p>Sovereign</p>",
                                "<p>Diversity</p>", "<p>Foreign</p>"],
                    solution_en: "<p>3.(b) <strong>Sovereign</strong>- a person who has supreme power or authority.<br><strong>Secular</strong>- not having any connection with religion.<br><strong>Diversity</strong>- the fact of there being many different things existing together in a group.<br><strong>Foreign</strong>- strange and unfamiliar.</p>",
                    solution_hi: "<p>3.(b) <strong>Sovereign </strong>(संप्रभु)- a person who has supreme power or authority.<br><strong>Secular </strong>(धर्मनिरपेक्ष)- not having any connection with religion.<br><strong>Diversity </strong>(विविधता)- the fact of there being many different things existing together in a group.<br><strong>Foreign </strong>(विदेशी/बाहरी)- strange and unfamiliar.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "4. Select the option that can be used as a one-word substitute for the given group of words.<br />Speaking disrespectfully about sacred or religious things.",
                    question_hi: "<p>4. Select the option that can be used as a one-word substitute for the given group of words.<br>Speaking disrespectfully about sacred or religious things.</p>",
                    options_en: ["<p>Blasphemy</p>", "<p>Mystic</p>", 
                                "<p>Atheist</p>", "<p>Theist</p>"],
                    options_hi: ["<p>Blasphemy</p>", "<p>Mystic</p>",
                                "<p>Atheist</p>", "<p>Theist</p>"],
                    solution_en: "<p>4.(a) <strong>Blasphemy</strong>- speaking disrespectfully about sacred or religious things.<br><strong>Mystic</strong>- someone who attempts to be united with God through prayer.<br><strong>Atheist</strong>- one who does not believe in the existence of God.<br><strong>Theist</strong>- one who believes in the existence of God.</p>",
                    solution_hi: "<p>4.(a) <strong>Blasphemy </strong>(ईश्वर-निंदा)- speaking disrespectfully about sacred or religious things.<br><strong>Mystic </strong>( आध्यात्मिक/रहस्यवादी)- someone who attempts to be united with God through prayer.<br><strong>Atheist </strong>(नास्तिक)- one who does not believe in the existence of God.<br><strong>Theist </strong>(आस्तिक)- one who believes in the existence of God.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "5. Select the option that can be used as a one-word substitute for the given group of words:<br />A thing that can be easily carried by hands",
                    question_hi: "<p>5. Select the option that can be used as a one-word substitute for the given group of words:<br>A thing that can be easily carried by hands</p>",
                    options_en: ["<p>Export</p>", "<p>Import</p>", 
                                "<p>Potable</p>", "<p>Portable</p>"],
                    options_hi: ["<p>Export</p>", "<p>Import</p>",
                                "<p>Potable</p>", "<p>Portable</p>"],
                    solution_en: "<p>5.(d) <strong>Portable</strong>- a thing that can be easily carried by hands.<br><strong>Export</strong>- send (goods or services) to another country for sale.<br><strong>Import</strong>- bring (goods or services) into a country from abroad for sale.<br><strong>Potable</strong>- safe to drink.</p>",
                    solution_hi: "<p>5.(d) <strong>Portable </strong>(वहनीय)- a thing that can be easily carried by hands.<br><strong>Export </strong>(निर्यात करना)- send (goods or services) to another country for sale.<br><strong>Import </strong>(आयात करना)- bring (goods or services) into a country from abroad for sale.<br><strong>Potable </strong>(पीने योग्य)- safe to drink.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "6.  Select the option that can be used as a one-word substitute for the given group of words.<br />A place to park aeroplanes",
                    question_hi: "<p>6. Select the option that can be used as a one-word substitute for the given group of words.<br>A place to park aeroplanes</p>",
                    options_en: [" Hanger", " Depot", 
                                "<p>Shed</p>", "<p>Hangar</p>"],
                    options_hi: ["<p>Hanger</p>", "<p>Depot</p>",
                                "<p>Shed</p>", "<p>Hangar</p>"],
                    solution_en: "<p>6.(d) <strong>Hangar</strong>- a place to park aeroplanes.<br><strong>Hanger</strong>- a shaped piece of wood, plastic, or metal with a hook at the top, from which clothes may be hung.<br><strong>Depot</strong>- a place for the storage of large quantities of equipment, food, or goods.<br><strong>Shed</strong>- a simple roofed structure used for garden storage, to shelter animals, or as a workshop.</p>",
                    solution_hi: "<p>6.(d) <strong>Hangar </strong>(विमानशाला) - a place to park aeroplanes.<br><strong>Hanger </strong>(खूँटी) - a shaped piece of wood, plastic, or metal with a hook at the top, from which clothes may be hung.<br><strong>Depot </strong>(गोदाम) - a place for the storage of large quantities of equipment, food, or goods.<br><strong>Shed </strong>(झोपड़ी/छप्पर) - a simple roofed structure used for garden storage, to shelter animals, or as a workshop.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Athens was suffering under the rule of <span style=\"text-decoration: underline;\">a powerful small group</span> that had no concern for the people\'s welfare.</p>",
                    question_hi: "<p>7. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Athens was suffering under the rule of <span style=\"text-decoration: underline;\">a powerful small group</span> that had no concern for the people\'s welfare.</p>",
                    options_en: ["<p>a democracy</p>", "<p>an oligarchy</p>", 
                                "<p>a monarchy</p>", "<p>a missionary</p>"],
                    options_hi: ["<p>a democracy</p>", "<p>an oligarchy</p>",
                                "<p>a monarchy</p>", "<p>a missionary</p>"],
                    solution_en: "<p>7.(b<strong>) An oligarchy</strong>- a powerful small group.<br><strong>A democracy</strong>- a system of government in which power is held by elected representatives who are freely voted for by the people.<br><strong>A monarchy</strong>- a form of government with a monarch at the head.<br><strong>A missionary</strong>- a person sent on a religious mission, especially one sent to promote Christianity in a foreign country.</p>",
                    solution_hi: "<p>7.(b)<strong> An oligarchy </strong>(कुलीनतंत्र)- a powerful small group.<br><strong>A democracy </strong>(लोकतंत्र)- a system of government in which power is held by elected representatives who are freely voted for by the people.<br><strong>A monarchy</strong> (राजतंत्र)- a form of government with a monarch at the head.<br><strong>A missionary </strong>(पादरी)- a person sent on a religious mission, especially one sent to promote Christianity in a foreign country.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "8. Select the option that can be used as a one-word substitute for the given group of words.<br />The evening time when the sun has just set and it is almost dark.",
                    question_hi: "<p>8. Select the option that can be used as a one-word substitute for the given group of words.<br>The evening time when the sun has just set and it is almost dark.</p>",
                    options_en: ["<p>Dusk</p>", "<p>Night</p>", 
                                "<p>Dawn</p>", "<p>Day break</p>"],
                    options_hi: ["<p>Dusk</p>", "<p>Night</p>",
                                "<p>Dawn</p>", "<p>Day break</p>"],
                    solution_en: "<p>8.(a) <strong>Dusk</strong>- the evening time when the sun has just set and it is almost dark.<br><strong>Night</strong>- the period from sunset to sunrise.<br><strong>Dawn</strong>- the period in the day when light from the sun begins to appear in the sky.<br><strong>Day break</strong>- the time in the morning when daylight first appears.</p>",
                    solution_hi: "<p>8.(a) <strong>Dusk </strong>(सांझ)- the evening time when the sun has just set and it is almost dark.<br><strong>Night </strong>(रात्रि)- the period from sunset to sunrise.<br><strong>Dawn </strong>(प्रभात)- the period in the day when light from the sun begins to appear in the sky.<br><strong>Day break</strong> (प्रातःकाल)- the time in the morning when daylight first appears.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "9. Select the option that can be used as a one-word substitute for the given group of words. <br />A person who does not believe in God",
                    question_hi: "<p>9. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who does not believe in God</p>",
                    options_en: ["<p>Amateur</p>", "<p>Anarchy</p>", 
                                "<p>Autocrat</p>", "<p>Atheist</p>"],
                    options_hi: ["<p>Amateur</p>", "<p>Anarchy</p>",
                                "<p>Autocrat</p>", "<p>Atheist</p>"],
                    solution_en: "<p>9.(d) <strong>Atheist</strong>- a person who does not believe in God.<br><strong>Amateur</strong>- a person who engages in a pursuit, especially a sport, on an unpaid rather than a professional basis.<br><strong>Anarchy</strong>- a state of disorder due to absence or non-recognition of authority.<br><strong>Autocrat</strong>- a ruler who has absolute power.</p>",
                    solution_hi: "<p>9.(d) <strong>Atheist </strong>(नास्तिक)- a person who does not believe in God.<br><strong>Amateur </strong>(अव्यवसायी)- a person who engages in a pursuit, especially a sport, on an unpaid rather than a professional basis.<br><strong>Anarchy </strong>(अराजकता)- a state of disorder due to absence or non-recognition of authority.<br><strong>Autocrat </strong>(तानाशाह)- a ruler who has absolute power.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10.  Select the option that can be used as a one-word substitute for the given group of words.<br />Dealing with things in a practical and sensible way",
                    question_hi: "<p>10. Select the option that can be used as a one-word substitute for the given group of words.<br>Dealing with things in a practical and sensible way</p>",
                    options_en: ["<p>Pragmatic</p>", "<p>Sceptic</p>", 
                                "<p>Stoic</p>", "<p>Cynic</p>"],
                    options_hi: ["<p>Pragmatic</p>", "<p>Sceptic</p>",
                                "<p>Stoic</p>", "<p>Cynic</p>"],
                    solution_en: "<p>10.(a) <strong>Pragmatic</strong>- dealing with things in a practical and sensible way.<br><strong>Sceptic</strong>- a person inclined to question or doubt accepted opinions.<br><strong>Stoic</strong>- a person who can endure pain or hardship without showing their feelings or complaining.<br><strong>Cynic</strong>- believing that people are only interested in themselves and are not sincere.</p>",
                    solution_hi: "<p>10.(a) <strong>Pragmatic </strong>(व्यावहारिक)- dealing with things in a practical and sensible way.<br><strong>Sceptic </strong>(संदेहवादी)- a person inclined to question or doubt accepted opinions.<br><strong>Stoic </strong>(सहनशील)- a person who can endure pain or hardship without showing their feelings or complaining.<br><strong>Cynic </strong>(निंदक)- believing that people are only interested in themselves and are not sincere.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the option that can be used as a one-word substitute for the given group of words<br>Anything pertaining to cats</p>",
                    question_hi: "<p>11. Select the option that can be used as a one-word substitute for the given group of words<br>Anything pertaining to cats</p>",
                    options_en: ["<p>Bovine</p>", "<p>Canine</p>", 
                                "<p>Taurine</p>", "<p>Feline</p>"],
                    options_hi: ["<p>Bovine</p>", "<p>Canine</p>",
                                "<p>Taurine</p>", "<p>Feline</p>"],
                    solution_en: "<p>11.(d) <strong>Feline</strong>- anything pertaining to cats.<br><strong>Bovine</strong>- relating to cattle.<br><strong>Canine</strong>- relating to dogs.<br><strong>Taurine</strong>- relating to a bull.</p>",
                    solution_hi: "<p>11.(d) <strong>Feline </strong>(बिल्ली से संबंधित)- anything pertaining to cats.<br><strong>Bovine </strong>(घरेलू पशु से संबंधित)- relating to cattle.<br><strong>Canine </strong>(कुत्ते से संबंधित)- relating to dogs.<br><strong>Taurine </strong>(बैल से संबंधित)- relating to a bull.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the option that can be used as a one-word substitute for the given group of words.<br>That which is no longer in existence</p>",
                    question_hi: "<p>12. Select the option that can be used as a one-word substitute for the given group of words.<br>That which is no longer in existence</p>",
                    options_en: ["<p>Extinct</p>", "<p>Removed</p>", 
                                "<p>Vanished</p>", "<p>Destroyed</p>"],
                    options_hi: ["<p>Extinct</p>", "<p>Removed</p>",
                                "<p>Vanished</p>", "<p>Destroyed</p>"],
                    solution_en: "<p>12.(a) <strong>Extinct</strong>- that which is no longer in existence.</p>",
                    solution_hi: "<p>12.(a) <strong>Extinct</strong>- that which is no longer in existence./जो अब अस्तित्व में नहीं है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "13. Select the word which means the same as the group of words given.<br />Essence or main point of any passage, lecture or book",
                    question_hi: "<p>13. Select the word which means the same as the group of words given.<br>Essence or main point of any passage, lecture or book</p>",
                    options_en: ["<p>Gist</p>", "<p>Significance</p>", 
                                "<p>Meaning</p>", "<p>Heart</p>"],
                    options_hi: ["<p>Gist</p>", "<p>Significance</p>",
                                "<p>Meaning</p>", "<p>Heart</p>"],
                    solution_en: "<p>13.(a) <strong>Gist</strong>- essence or main point of any passage, lecture or book.</p>",
                    solution_hi: "<p>13.(a) <strong>Gist </strong>(सारांश/भावार्थ)- essence or main point of any passage, lecture or book.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the option that can be used as a one-word substitute for the underlined group of words.<br>We, as human beings, have a tendency of believing in the <span style=\"text-decoration: underline;\">things that can be seen and touched</span>.</p>",
                    question_hi: "<p>14. Select the option that can be used as a one-word substitute for the underlined group of words.<br>We, as human beings, have a tendency of believing in the <span style=\"text-decoration: underline;\">things that can be seen and touched</span>.</p>",
                    options_en: [" impalpable", " spiritual", 
                                " inexpressible", "<p>tangible</p>"],
                    options_hi: ["<p>impalpable</p>", "<p>spiritual</p>",
                                "<p>inexpressible</p>", "<p>tangible</p>"],
                    solution_en: "<p>14.(d) <strong>Tangible</strong>- things that can be seen and touched.<br><strong>Impalpable</strong>- unable to be felt by touch.<br><strong>Spiritual</strong>- relating to or affecting the human spirit or soul.<br><strong>Inexpressible</strong>- (of a feeling) too strong to be described or conveyed in words.</p>",
                    solution_hi: "<p>14.(d) <strong>Tangible </strong>(मूर्त)- things that can be seen and touched.<br><strong>Impalpable </strong>(अदृश्य)- unable to be felt by touch.<br><strong>Spiritual </strong>(आध्यात्मिक)- relating to or affecting the human spirit or soul.<br><strong>Inexpressible </strong>(अवर्णनीय)- (of a feeling) too strong to be described or conveyed in words.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Select the option that can be used as a one-word substitute for the given group of words.<br>Fear of snakes</p>",
                    question_hi: "<p>15. Select the option that can be used as a one-word substitute for the given group of words.<br>Fear of snakes</p>",
                    options_en: ["<p>Claustrophobia</p>", "<p>Syngenesophobia</p>", 
                                "<p>Xenophobia</p>", "<p>Ophidiophobia</p>"],
                    options_hi: ["<p>Claustrophobia</p>", "<p>Syngenesophobia</p>",
                                "<p>Xenophobia</p>", "<p>Ophidiophobia</p>"],
                    solution_en: "<p>15.(d) <strong>Ophidiophobia</strong>- fear of snakes.<br><strong>Claustrophobia</strong>- fear of closed spaces.<br><strong>Syngenesophobia</strong>- fear of relatives.<br><strong>Xenophobia</strong>- fear of strangers or foreigners.</p>",
                    solution_hi: "<p>15.(d) <strong>Ophidiophobia </strong>(सर्प-भय) - fear of snakes.<br><strong>Claustrophobia </strong>(संकीर्ण स्थानों का भय) - fear of closed spaces.<br><strong>Syngenesophobia </strong>(संबंधियों से भय) - fear of relatives.<br><strong>Xenophobia </strong>(विदेशियों से भय) - fear of strangers or foreigners.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>