<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Study the given pattern carefully and select the number from among the given options that can replace the question mark(?).<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711389293.png\" alt=\"rId4\" width=\"174\" height=\"77\"></p>",
                    question_hi: "<p>1. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और दिए गए विकल्पों में से उस संख्या का चयन करें जो प्रश्न चिह्न (?) को प्रतिस्थापित कर सके।<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711389293.png\" alt=\"rId4\" width=\"162\" height=\"72\"></p>",
                    options_en: ["<p>12</p>", "<p>8</p>", 
                                "<p>6</p>", "<p>7</p>"],
                    options_hi: ["<p>12</p>", "<p>8</p>",
                                "<p>6</p>", "<p>7</p>"],
                    solution_en: "<p>1.(b)<br>6<math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>5</mn><mo>=</mo><mn>29</mn></math><br>8<math display=\"inline\"><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>8</mn><mo>=</mo><mn>48</mn></math></p>",
                    solution_hi: "<p>1.(b)<br>6<math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>5</mn><mo>=</mo><mn>29</mn></math><br>8<math display=\"inline\"><mo>&#215;</mo><mn>5</mn><mo>+</mo><mn>8</mn><mo>=</mo><mn>48</mn></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. From the given diagram, find which number represents the following.<br>Boys are good at sports but not dance <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711389426.png\" alt=\"rId5\" width=\"134\" height=\"104\"></p>",
                    question_hi: "<p>2. दिए गए आरेख से ज्ञात कीजिए कि कौन-सी संख्या निम्नलिखित का प्रतिनिधित्व करती है।<br>लड़के खेलकूद में अच्छे होते हैं लेकिन नृत्य नहीं करते।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711389426.png\" alt=\"rId5\" width=\"146\" height=\"114\"></p>",
                    options_en: ["<p>5</p>", "<p>3</p>", 
                                "<p>4</p>", "<p>2</p>"],
                    options_hi: ["<p>5</p>", "<p>3</p>",
                                "<p>4</p>", "<p>2</p>"],
                    solution_en: "<p>2.(d)<br>Boys are good at sports but not dance is the area common between circle and Boys.<br>And the common area is represented by the number = 2</p>",
                    solution_hi: "<p>2.(d)<br>लड़के खेलकूद में अच्छे होते हैं लेकिन नृत्य नहीं करना मंडली और लड़कों के बीच का क्षेत्र है।<br>तथा उभयनिष्ठ क्षेत्रफल को संख्या = 2 द्वारा निरूपित किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the Venn diagram from among the given options that best represents the relationship between the following classes.<br>Human being, Father, Man<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711389564.png\" alt=\"rId6\" width=\"274\" height=\"99\"> </p>",
                    question_hi: "<p>3. दिए गए विकल्पों में से वेन आरेख का चयन कीजिए जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है।<br>मनुष्य, पिता, आदमी <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711389564.png\" alt=\"rId6\" width=\"267\" height=\"96\"> </p>",
                    options_en: ["<p>Diagram B</p>", "<p>Diagram A</p>", 
                                "<p>Diagram C</p>", "<p>Diagram D</p>"],
                    options_hi: ["<p>आरेख B</p>", "<p>आरेख A</p>",
                                "<p>आरेख C</p>", "<p>आरेख D</p>"],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711389710.png\" alt=\"rId7\" width=\"93\" height=\"84\"></p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711389710.png\" alt=\"rId7\" width=\"89\" height=\"80\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Choose the number pair that is different from the other three.</p>",
                    question_hi: "<p>4. उस संख्या युग्म को चुनिए जो अन्य तीन से भिन्न है।</p>",
                    options_en: ["<p>0 : 6</p>", "<p>5 : 14</p>", 
                                "<p>12 : 28</p>", "<p>21 : 46</p>"],
                    options_hi: ["<p>0 : 6</p>", "<p>5 : 14</p>",
                                "<p>12 : 28</p>", "<p>21 : 46</p>"],
                    solution_en: "<p>4.(a)<br>Except option A, the difference of each number pair is a perfect square.</p>",
                    solution_hi: "<p>4.(a)<br>विकल्प A को छोड़कर, प्रत्येक संख्या युग्म का अंतर एक पूर्ण वर्ग है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. If all the numbers are dropped from the given arrangement, which of the following will be the ninth to the left of S?<br>2 R Q 4 A 5 $ I ^ 9 R # E % 8 S * P</p>",
                    question_hi: "<p>5. यदि दी गई व्यवस्था से सभी संख्याओं को हटा दिया जाता है, तो निम्न में से कौन S के बायें से नौवां होगा?<br>2 R Q 4 A 5 $ I ^ 9 R # E % 8 S * P</p>",
                    options_en: ["<p>A</p>", "<p>$</p>", 
                                "<p>Q</p>", "<p>I</p>"],
                    options_hi: ["<p>A</p>", "<p>$</p>",
                                "<p>Q</p>", "<p>I</p>"],
                    solution_en: "<p>5.(c)<br>If all numbers are dropped the new arrangement will be<br>R Q A $ I ^ R # E % S * P<br>9th to the left of S = Q</p>",
                    solution_hi: "<p>5.(c)<br>यदि सभी संख्याओं को हटा दिया जाए तो नई व्यवस्था होगी<br>R Q A $ I ^ R # E % S * P<br>S के बायें से 9वां = Q</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the option that will fill in the blank and complete the series.<br>PE<sub>11</sub>, QD<sub>2</sub>, RC<sub>9</sub>, SB<sub>4</sub>, _____</p>",
                    question_hi: "<p>6. उस विकल्प का चयन कीजिये जो रिक्त स्थान को भरेगा और श्रृंखला को पूरा करेगा।<br>PE<sub>11</sub>, QD<sub>2</sub>, RC<sub>9</sub>, SB<sub>4</sub>, _____</p>",
                    options_en: ["<p>RA<sub>6</sub></p>", "<p>TA<sub>7</sub></p>", 
                                "<p>BA<sub>7</sub></p>", "<p>TA<sub>6</sub></p>"],
                    options_hi: ["<p>RA<sub>6</sub></p>", "<p>TA<sub>7</sub></p>",
                                "<p>BA<sub>7</sub></p>", "<p>TA<sub>6</sub></p>"],
                    solution_en: "<p>6.(b)<br>Logic : 1st alphabet + 1 = 1st alphabet of next<br>2nd alphabet - 1 = 2nd alphabet of next<br>Their is alternate series in no&rsquo;s <br>11 - 2 = 9 in same way 9 - 2 = 7&nbsp;<br>And another series is 2 + 2 = 4</p>",
                    solution_hi: "<p>6.(b)<br>तर्क : पहला अक्षर + 1 = अगले का पहला अक्षर<br>दूसरा अक्षर - 1 = अगले का दूसरा अक्षर<br>संख्या में उनकी वैकल्पिक श्रंखला है<br>11 - 2 = 9 इसी तरह 9 - 2 = 7<br>और दूसरी श्रंखला 2 + 2 = 4 है।</p>",
                    correct: "---Can\'t read from the Document---",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the option that will fill the blank and complete the series.<br>XII, V, X, ____, VIII, CXXV</p>",
                    question_hi: "<p>7. उस विकल्प का चयन कीजिये जो रिक्त स्थान को भरेगा और श्रृंखला को पूरा करेगा।<br>XII, V, X, ____, VIII, CXXV</p>",
                    options_en: ["<p>XXVI</p>", "<p>XII</p>", 
                                "<p>XXV</p>", "<p>XX</p>"],
                    options_hi: ["<p>XXVI</p>", "<p>XII</p>",
                                "<p>XXV</p>", "<p>XX</p>"],
                    solution_en: "<p>7.(c)<br>Decoding Roman numeral into decimal system<br>12, 5, 10, ___, 8, 125<br>There are two series one is of - 2 and other is of &times; 5<br>So, 5 &times; 5 = 25 and 25 in roman is XXV.</p>",
                    solution_hi: "<p>7.(c)<br>रोमन अंक को दशमलव प्रणाली में डिकोड करना<br>12, 5, 10, ___, 8, 125<br>दो श्रंखलाएँ हैं एक -2 की है और दूसरी &times; 5 की है<br>तो, 5 &times; 5 = 25 और रोमन में 25 XXV है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Harsh is the son of Ajeet. Vinita is the wife of Ajeet. Ajay is the brother of Harsh. Neha is the only daughter of Ajay and Kavita.<br>How is Vinita related to Neha?</p>",
                    question_hi: "<p>8. हर्ष अजीत का पुत्र है। विनीता अजीत की पत्नी है। अजय हर्ष का भाई है। नेहा अजय और कविता की इकलौती बेटी है।<br>विनीता, नेहा से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Father&rsquo;s Sister</p>", "<p>Mother</p>", 
                                "<p>Father&rsquo;s mother</p>", "<p>Sister</p>"],
                    options_hi: ["<p>पिता की बहन</p>", "<p>माता</p>",
                                "<p>पिता की मां</p>", "<p>बहन</p>"],
                    solution_en: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711389897.png\" alt=\"rId8\" width=\"331\" height=\"108\"></p>",
                    solution_hi: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711389897.png\" alt=\"rId8\" width=\"346\" height=\"113\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the option that will fill in the blank and complete the series.<br>6, 44, 9, 42, ___, 40</p>",
                    question_hi: "<p>9. उस विकल्प का चयन कीजिये जो रिक्त स्थान को भरेगा और श्रृंखला को पूरा करेगा<br>6, 44, 9, 42, ___, 40</p>",
                    options_en: ["<p>41</p>", "<p>15</p>", 
                                "<p>12</p>", "<p>46</p>"],
                    options_hi: ["<p>41</p>", "<p>15</p>",
                                "<p>12</p>", "<p>46</p>"],
                    solution_en: "<p>9.(c)<br>6 + 3 = 9<br>44 - 2 = 42<br>9 + 3 = 12<br>42 - 2 = 40</p>",
                    solution_hi: "<p>9.(c)<br>6 + 3 = 9<br>44 - 2 = 42<br>9 + 3 = 12<br>42 - 2 = 40</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. A question is given followed by two arguments. Decide which of the arguments is/are strong with respect to the question.<br><strong>Question :</strong><br>Should there be no uniform for school children like college students?<br><strong>Arguments :</strong><br>I. Yes, students should be given freedom to wear the clothes of their choice <br>II. No, uniforms helps to create discipline and equality among the students.</p>",
                    question_hi: "<p>10. एक प्रश्न के बाद दो तर्क दिए गए हैं। बताइये कि प्रश्न के संबंध में कौन सा तर्क पुष्ट हैं<br><strong>प्रश्न :</strong><br>क्या स्कूली बच्चों के लिए कॉलेज के छात्रों की तरह वर्दी नहीं होनी चाहिए?<br><strong>तर्क :</strong><br>I. हां, छात्रों को उनकी पसंद के कपड़े पहनने की आजादी दी जानी चाहिए<br>II. नहीं, वर्दी छात्रों में अनुशासन और समानता पैदा करने में मदद करती है</p>",
                    options_en: ["<p>Neither I and II is strong</p>", "<p>Only argument I is strong</p>", 
                                "<p>Both the arguments are strong</p>", "<p>Only argument II is strong.</p>"],
                    options_hi: ["<p>न तो I और II पुष्ट हैं</p>", "<p>केवल तर्क I पुष्ट है</p>",
                                "<p>दोनों तर्क पुष्ट है</p>", "<p>केवल तर्क II पुष्ट है।</p>"],
                    solution_en: "<p>10.(c)<br>For the given question both arguments I and II are strong</p>",
                    solution_hi: "<p>10.(c)<br>दिए गए प्रश्न के लिए तर्क I और II दोनों प्रबल हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Consider the given statement and decide which of the given assumptions is/are implicit in the statement.<br><strong>Statement :</strong><br>X tells Y, &ldquo;if you need to grow the sales of your company, you should advertise in the newspaper &lsquo;A&rsquo;<br><strong>Assumptions :</strong><br>I. Newspaper A has a more extensive circulation than other newspapers. <br>II. Sales of &lsquo;Y&rsquo;s company are down.</p>",
                    question_hi: "<p>11. दिए गए कथन पर विचार कीजिये और बताइये कि दी गई अवधारणा में से कौन सा कथन निहित हैं।<br><strong>कथन :</strong><br>X, Y से कहता है, \"यदि आपको अपनी कंपनी की बिक्री बढ़ाने की आवश्यकता है, तो आपको समाचार पत्र \'A\' में विज्ञापन देना चाहिए।<br><strong>अवधारणा :</strong><br>I. समाचार पत्र A का अन्य समाचार पत्रों की तुलना में अधिक व्यापक प्रसार है<br>II. \'Y की कंपनी की बिक्री घटी है</p>",
                    options_en: ["<p>Only assumption II is implicit</p>", "<p>Both assumptions I and II are implicit</p>", 
                                "<p>Neither assumption I nor II is implicit</p>", "<p>Only assumption I is implicit</p>"],
                    options_hi: ["<p>केवल अवधारणा II निहित है</p>", "<p>दोनों धारणाएँ I और II निहित हैं</p>",
                                "<p>न तो धारणा I और न ही II निहित है</p>", "<p>केवल धारणा I निहित है</p>"],
                    solution_en: "<p>11.(c)<br>From the given statements it is not clear that Sales of Y&rsquo;s company are down and Newspaper A has a more extensive circulation than other newspapers.</p>",
                    solution_hi: "<p>11.(c)<br>दिए गए कथनों से यह स्पष्ट नहीं है कि Y की कंपनी की बिक्री कम है और समाचार पत्र A का अन्य समाचार पत्रों की तुलना में अधिक व्यापक प्रसार है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Choose the word that is different from the other three.</p>",
                    question_hi: "<p>12. वह शब्द चुनिए जो अन्य तीनों से भिन्न है</p>",
                    options_en: ["<p>Hand</p>", "<p>Kidney</p>", 
                                "<p>Liver</p>", "<p>Heart</p>"],
                    options_hi: ["<p>हाथ</p>", "<p>गुर्दा</p>",
                                "<p>यकृत</p>", "<p>हृदय</p>"],
                    solution_en: "<p>12.(a)<br>Except the hand, all other organs given in the option are inside our body.</p>",
                    solution_hi: "<p>12.(a)<br>हाथ को छोड़कर विकल्प में दिए गए अन्य सभी अंग हमारे शरीर के अंदर हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option that will fill in the blanks and complete the series.<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mn>1</mn><mo>,</mo></math> _____, 16</p>",
                    question_hi: "<p>13. उस विकल्प का चयन कीजिये जो रिक्त स्थानों को भरेगा और श्रृंखला को पूरा करेगा<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mn>1</mn><mo>,</mo></math> _____, 16</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p>4</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p>8</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p>4</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p>8</p>"],
                    solution_en: "<p>13.(b)<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac><mo>&#215;</mo><mn>4</mn><mo>=</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>, 14 &times; 4 = 1, 1 &times; 4 = 4, 4 &times; 4 = 16</p>",
                    solution_hi: "<p>13.(b)<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac><mo>&#215;</mo><mn>4</mn><mo>=</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>, 14 &times; 4 = 1, 1 &times; 4 = 4, 4 &times; 4 = 16</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Which of the following indicates &lsquo;Some hard workers are smart workers&rsquo;?</p>",
                    question_hi: "<p>14. निम्नलिखित में से कौन इंगित करता है कि \'कुछ मेहनती कार्यकर्ता स्मार्ट कर्मचारी हैं\'?</p>",
                    options_en: ["<p>All hard workers are smart workers.</p>", "<p>All smart workers are hard workers.</p>", 
                                "<p>No smart workers are hard workers.</p>", "<p>Some smart workers are not hard workers.</p>"],
                    options_hi: ["<p>सभी मेहनती कर्मचारी स्मार्ट वर्कर हैं</p>", "<p>सभी स्मार्ट वर्कर मेहनती होते हैं</p>",
                                "<p>कोई स्मार्ट वर्कर परिश्रमी कामगार नहीं है</p>", "<p>कुछ स्मार्ट वर्कर परिश्रमी कामगार नहीं हैं</p>"],
                    solution_en: "<p>14.(d)<br>Some smart workers are not hard workers, it means that some smart workers are also hard workers.</p>",
                    solution_hi: "<p>14.(d)<br>कुछ स्मार्ट वर्कर हार्ड वर्कर नहीं होते हैं, इसका मतलब है कि कुछ स्मार्ट वर्कर हार्ड वर्कर भी होते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. If <strong>1</strong> is the code for <strong>B,</strong> what will be the code for <strong>PEANUT</strong>?</p>",
                    question_hi: "<p>15. यदि <strong>B </strong>का कूट <strong>1 </strong>है, तो <strong>PEANUT </strong>का कूट क्या होगा?</p>",
                    options_en: ["<p>1540132019</p>", "<p>1613204019</p>", 
                                "<p>1640201319</p>", "<p>151602019</p>"],
                    options_hi: ["<p>1540132019</p>", "<p>1613204019</p>",
                                "<p>1640201319</p>", "<p>151602019</p>"],
                    solution_en: "<p>15.(a)<br><strong>Logic :</strong> place value - 1<br>PEANUT = 1540132019</p>",
                    solution_hi: "<p>15.(a)<br><strong>तर्क : </strong>स्थानीय मान - 1<br>PEANUT = 1540132019</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Study the given pattern carefully and select the number from among the given options that can replace the question mark.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711390047.png\" alt=\"rId9\" width=\"103\" height=\"108\"></p>",
                    question_hi: "<p>16. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और दिए गए विकल्पों में से उस संख्या का चयन करें जो प्रश्न चिह्न को प्रतिस्थापित कर सके।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711390047.png\" alt=\"rId9\" width=\"108\" height=\"114\"></p>",
                    options_en: ["<p>13</p>", "<p>7</p>", 
                                "<p>9</p>", "<p>11</p>"],
                    options_hi: ["<p>13</p>", "<p>7</p>",
                                "<p>9</p>", "<p>11</p>"],
                    solution_en: "<p>16.(a)<br>3<math display=\"inline\"><mo>&#215;</mo><mn>6</mn><mo>=</mo><mn>18</mn></math><br>4<math display=\"inline\"><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi></math>5 = 20<br>6 <math display=\"inline\"><mo>&#215;</mo></math> 10 = 60<br>8 <math display=\"inline\"><mo>&#215;</mo></math> 13 = 104<br>18 + 20 + 60 + 104 = 202</p>",
                    solution_hi: "<p>16.(a)<br>3<math display=\"inline\"><mo>&#215;</mo><mn>6</mn><mo>=</mo><mn>18</mn></math><br>4<math display=\"inline\"><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi></math>5 = 20<br>6 <math display=\"inline\"><mo>&#215;</mo></math> 10 = 60<br>8 <math display=\"inline\"><mo>&#215;</mo></math> 13 = 104<br>18 + 20 + 60 + 104 = 202</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the option in which the words do <strong>NOT </strong>share the same relationship as that shared by the given group of words.<br>Patient : Surgeon : Surgery</p>",
                    question_hi: "<p>17. उस विकल्प का चयन कीजिये जिसमें शब्द समान संबंध साझा नहीं करते हैं जो दिए गए शब्दों के समूह द्वारा साझा किया गया है<br>रोगी : शल्य चिकित्सक: शल्य चिकित्सा</p>",
                    options_en: ["<p>Chalk : Teacher : Students</p>", "<p>Cloth : Tailor : Stitch</p>", 
                                "<p>Vegetables : Chef : Cook</p>", "<p>Clay : Potter : Pottery</p>"],
                    options_hi: ["<p>चाक : शिक्षक : विद्यार्थी</p>", "<p>कपड़ा : दर्जी : सिलाई</p>",
                                "<p>सब्जियां : बावर्ची : रसोइया</p>", "<p>मिट्टी : कुम्हार : मिट्टी के बर्तन</p>"],
                    solution_en: "<p>17.(a)<br>As patient Surgeon and Surgery is related to each other, in the same way chalk , teacher, Students are related.</p>",
                    solution_hi: "<p>17.(a)<br>जैसे मरीज सर्जन और सर्जरी एक दूसरे से संबंधित हैं, उसी तरह चाक, शिक्षक, छात्र संबंधित हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the option that will fill in the blanks and complete the series.<br>CDC EFE GHG _____</p>",
                    question_hi: "<p>18. उस विकल्प का चयन कीजिये जो रिक्त स्थानों को भरेगा और श्रृंखला को पूरा करेगा<br>CDC EFE GHG _____</p>",
                    options_en: ["<p>GHI</p>", "<p>GIJ</p>", 
                                "<p>IJI</p>", "<p>IJK</p>"],
                    options_hi: ["<p>GHI</p>", "<p>GIJ</p>",
                                "<p>IJI</p>", "<p>IJK</p>"],
                    solution_en: "<p>18.(c)<br>1st letter + 2 = 1st letter of next<br>2nd letter + 2 = 2nd letter of next<br>3rd letter + 2 = 3rd letter of next<br>Next = G + 2 = I, H + 2 = J, G + 2 = I <math display=\"inline\"><mo>&#8658;</mo></math> IJI</p>",
                    solution_hi: "<p>18.(c)<br>पहला अक्षर + 2 = अगले का पहला अक्षर<br>दूसरा अक्षर + 2 = अगले का दूसरा अक्षर<br>तीसरा अक्षर + 2 = अगले का तीसरा अक्षर<br>अगला = G + 2 = I, H + 2 = J, G + 2 = I IJI</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In a certain code language, <strong>GOLDEN CIRCLE </strong>is written as <strong>715124514 39183125</strong>. How will <strong>ENGLAND </strong>be written in that language?</p>",
                    question_hi: "<p>19. एक निश्चित कूट भाषा में <strong>GOLDEN CIRCLE</strong> को <strong>715124514 39183125</strong> लिखा जाता है। उस भाषा में <strong>ENGLAND </strong>को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>5147112114</p>", "<p>5174121144</p>", 
                                "<p>5174111114</p>", "<p>5147121144</p>"],
                    options_hi: ["<p>5147112114</p>", "<p>5174121144</p>",
                                "<p>5174111114</p>", "<p>5147121144</p>"],
                    solution_en: "<p>19.(d)<br><strong>Logic :</strong> place value of alphabets are written.<br>ENGLAND = <strong>5147121144</strong></p>",
                    solution_hi: "<p>19.(d)<br><strong>तर्क :</strong> अक्षरों का स्थानीय मान लिखा जाता है।<br>ENGLAND = <strong>5147121144</strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the Venn diagram from among the given options that best represents the relationship between the following classes.<br>Girls, Students, Professional performers<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711390173.png\" alt=\"rId10\" width=\"250\" height=\"90\"></p>",
                    question_hi: "<p>20. दिए गए विकल्पों में से वेन आरेख का चयन कीजिये जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है।<br>लड़कियां, छात्र, पेशेवर कलाकार<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711390173.png\" alt=\"rId10\" width=\"264\" height=\"95\"></p>",
                    options_en: [" Diagram D  ", " Diagram B  ", 
                                " Diagram A ", " Diagram C "],
                    options_hi: [" आरेख D", " आरेख B",
                                " आरेख A", " आरेख C"],
                    solution_en: "20.(d)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711390310.png\" alt=\"rId11\" />",
                    solution_hi: "20.(d)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711390310.png\" alt=\"rId11\" />",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "21. In a certain code language, Sun is written as Earth, Earth is written as Mercury, Mercury is written as Venus, Mars is written as Sun and Venus is written as Mars. How will ‘ Venus lies between Mercury and Earth’ be written as in that language?",
                    question_hi: "21. एक निश्चित कोड भाषा में सूर्य को पृथ्वी, पृथ्वी को बुध, बुध को शुक्र, मंगल को सूर्य और शुक्र को मंगल के रूप में लिखा जाता है। उस भाषा में \'शुक्र, बुध और पृथ्वी के बीच स्थित है\' को कैसे लिखा जाएगा?",
                    options_en: [" Mars lies between Earth and Sun ", " Mercury lies between Earth and Sun ", 
                                " Mercury lies between Venus and Mars ", " Mars lies between Venus and Mercury "],
                    options_hi: [" मंगल पृथ्वी और सूर्य के बीच स्थित है", " बुध पृथ्वी और सूर्य के बीच स्थित है",
                                " बुध शुक्र और मंगल के बीच स्थित है", " मंगल शुक्र और बुध के बीच स्थित है"],
                    solution_en: "<p>21.(d)<br>Venus lies between Mercury and Earth&rsquo; = <strong>Mars lies between Venus and Mercury</strong></p>",
                    solution_hi: "<p>21.(d)<br>\'शुक्र बुध और पृथ्वी के बीच स्थित है\' = <strong>मंगल शुक्र और बुध के बीच स्थित है।</strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Read the given statements and conclusion carefully and decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>All kings have grey hair. Some knights become kings<br><strong>Conclusions :</strong><br>I. All knights have grey hair. <br>II. All grey haired knights are kings.</p>",
                    question_hi: "<p>22. दिए गए कथनों और निष्कर्षों को ध्यान से पढिए और बताइये कि कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है<br><strong>कथन :</strong><br>सभी राजाओं के बाल भूरे होते हैं। कुछ शूरवीर राजा बन जाते हैं<br><strong>निष्कर्ष :</strong><br>I. सभी शूरवीरों के भूरे बाल होते हैं<br>II. सभी भूरे बालों वाले शूरवीर राजा होते हैं</p>",
                    options_en: [" Neither conclusion I nor II follows ", " Both conclusion follow ", 
                                " Conclusion I follows ", " Conclusion II follows "],
                    options_hi: [" न तो निष्कर्ष I और न ही II अनुसरण करता है", " दोनों निष्कर्ष अनुसरण करते हैं",
                                " निष्कर्ष I अनुसरण करता है", " निष्कर्ष II अनुसरण करता है"],
                    solution_en: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711390474.png\" alt=\"rId12\" width=\"246\" height=\"65\"></p>",
                    solution_hi: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728711390474.png\" alt=\"rId12\" width=\"258\" height=\"68\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option that is related to the third term in the same way as the second term is related to the first term.<br>KRVI : NUYL : : AHMQ : ?</p>",
                    question_hi: "<p>23. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br>KRVI : NUYL : : AHMQ : ?</p>",
                    options_en: ["<p>DKPT</p>", "<p>FSCY</p>", 
                                "<p>BJLV</p>", "<p>WSMT</p>"],
                    options_hi: ["<p>DKPT</p>", "<p>FSCY</p>",
                                "<p>BJLV</p>", "<p>WSMT</p>"],
                    solution_en: "<p>23.(a)<br>K + 3 = N , R + 3 = U, V + 3 = Y, I + 3 = L<br>A + 3 = D, H + 3 =k, M + 3 = P, Q + 3 = T<math display=\"inline\"><mo>&#8658;</mo></math> DKPT</p>",
                    solution_hi: "<p>23.(a)<br>K + 3 = N , R + 3 = U, V + 3 = Y, I + 3 = L<br>A + 3 = D, H + 3 = k, M + 3= P, Q + 3 = T<math display=\"inline\"><mo>&#8658;</mo></math> DKPT</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. If <strong>T </strong>= <strong>7 </strong>and <strong>Y </strong>= <strong>2</strong>, then <strong>TUESDAY </strong>= ?</p>",
                    question_hi: "<p>24. यदि <strong>T</strong> =<strong> 7</strong> और <strong>Y</strong> = <strong>2</strong>, तो <strong>TUESDAY </strong>= ?</p>",
                    options_en: ["<p>7642823162</p>", "<p>7632824262</p>", 
                                "<p>7622863462</p>", "<p>7622823262</p>"],
                    options_hi: ["<p>7642823162</p>", "<p>7632824262</p>",
                                "<p>7622863462</p>", "<p>7622823262</p>"],
                    solution_en: "<p>24.(d)<br><strong>Logic :</strong> Place value of opposite letter is written.<br>TUESDAY = <strong>7622823262</strong></p>",
                    solution_hi: "<p>24.(d)<br><strong>तर्क :</strong> विपरीत अक्षर का स्थानीय मान लिखा जाता है।<br>TUESDAY = <strong>7622823262</strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Choose the number pair that is different from the other three.</p>",
                    question_hi: "<p>25. वह संख्या युग्म चुनें जो अन्य तीनों से भिन्न हो</p>",
                    options_en: ["<p>65 : 16</p>", "<p>62 : 15</p>", 
                                "<p>49 : 12</p>", "<p>33 : 8</p>"],
                    options_hi: ["<p>65 : 16</p>", "<p>62 : 15</p>",
                                "<p>49 : 12</p>", "<p>33 : 8</p>"],
                    solution_en: "<p>25.(b)<br>16<math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>65</mn></math><br><strong>15</strong><math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>1</mn></math> <strong>= 61</strong><br>12 <math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>1</mn></math> = 49<br>8<math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>1</mn></math> = 33</p>",
                    solution_hi: "<p>25.(b)<br>16<math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>65</mn></math><br><strong>15</strong><math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>1</mn></math> <strong>= 61</strong><br>12 <math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>1</mn></math> = 49<br>8<math display=\"inline\"><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>1</mn></math> = 33</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>