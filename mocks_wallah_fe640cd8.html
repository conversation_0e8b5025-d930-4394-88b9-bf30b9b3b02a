<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. The maximum resistance which can be made using four resistors each of 1/4 &Omega; in series is ____.</p>",
                    question_hi: "<p>1. श्रृंखला में प्रत्येक 1/4 के चार प्रतिरोधों का उपयोग करके अधिकतम प्रतिरोध _________ बनाया जा सकता है।</p>",
                    options_en: ["<p>1 &Omega;</p>", "<p>&frac14; &Omega;</p>", 
                                "<p>⅛ &Omega;</p>", "<p>4 &Omega;</p>"],
                    options_hi: ["<p>1 &Omega;</p>", "<p>&frac14; &Omega;</p>",
                                "<p>⅛ &Omega;</p>", "<p>4 &Omega;</p>"],
                    solution_en: "<p>1.(a)<strong> 1 &Omega;.</strong> Combination of series resistances can gives maximum resistance. So, connecting given resistance in Series to obtain maximum resistance. <br>The maximum resistance which can be made using four resistors each of 1/4 &Omega; in series is = (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> +<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>) &Omega;&nbsp;<br>= (<math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>) &Omega; = 1 &Omega;.<br>Hence, maximum resistance is 1 &Omega;.</p>",
                    solution_hi: "<p>1.(a)<strong> 1 &Omega;।</strong> श्रृंखला प्रतिरोधों का संयोजन अधिकतम प्रतिरोध दे सकता है। तो, अधिकतम प्रतिरोध प्राप्त करने के लिए दिए गए प्रतिरोध को श्रृंखला में जोड़ना।<br>श्रृंखला में प्रत्येक 1/4 &Omega; के चार प्रतिरोधकों का उपयोग करके बनाया जा सकने वाला अधिकतम प्रतिरोध = (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> +<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>) &Omega;&nbsp;&nbsp;है<br>= (<math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>) &Omega; = 1 &Omega; । <br>अत: अधिकतम प्रतिरोध 1 &Omega; है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Among various electrical safety devices, one based on the heating effect of electric current is called a _____.</p>",
                    question_hi: "<p>2. विभिन्न विद्युत सुरक्षा उपकरणों में, विद्युत धारा के ऊष्मीय प्रभाव पर आधारित उपकरण को _____ कहा जाता है।</p>",
                    options_en: ["<p>fuse</p>", "<p>circuit breaker</p>", 
                                "<p>protective relay</p>", "<p>surge protector</p>"],
                    options_hi: ["<p>फ्यूज (fuse)</p>", "<p>सर्किट ब्रेकर (circuit breaker)</p>",
                                "<p>प्रोटेक्टिव रिले (protective relay)</p>", "<p>सर्ज प्रोटेक्टर (surge protector)</p>"],
                    solution_en: "<p>2.(a) <strong>Fuse:-</strong> It is a piece of wire( made up of Tin (Sn) and Lead (Pb) alloy) with a very low melting point, when high current flows through the circuit due to short circuit, the wires get heated and melt. <strong>Capacitor-</strong> Device that stores electrical energy in an electric field. <strong>Surge protector -</strong> electrical device whose purpose is to protect electrical equipment from voltage spikes in alternating current (AC) circuits. <strong>Protective Relay</strong> - Device that detects the fault and initiates the operation of the circuit breaker to isolate the defective element from the rest of the system.</p>",
                    solution_hi: "<p>2.(a) <strong>फ़्यूज़</strong> - यह तार का एक टुकड़ा {टिन (Sn) और लेड (Pb) का मिश्र धातु} होता है जिसका गलनांक बहुत निम्न होता है, शॉर्ट सर्किट के कारण सर्किट में जब उच्च धारा प्रवाहित होती है, तार गर्म होकर पिघल जाता है। <strong>संधारित्र (Capacitor) -</strong> वह उपकरण जो विद्युत ऊर्जा को विद्युत क्षेत्र में संग्रहीत करता है। <strong>सर्ज प्रोटेक्टर -</strong> विद्युत उपकरण जिसका उद्देश्य प्रत्यावर्ती धारा (AC) सर्किट में वोल्टेज स्पाइक्स से विद्युत उपकरणों की रक्षा करना है। <strong>प्रोटेक्टिव रिले </strong>- वह उपकरण जो खराबी का पता लगाता है और दोषपूर्ण तत्व को सिस्टम के अन्य भागों से अलग करने के लिए सर्किट ब्रेकर का संचालन शुरू करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Modern electric generator works on _______ principle.</p>",
                    question_hi: "<p>3. आधुनिक विद्युत जनरेटर _______ सिद्धांत पर कार्य करता है।</p>",
                    options_en: ["<p>Electric Induction</p>", "<p>Electromagnetic Induction</p>", 
                                "<p>Electrical Energy</p>", "<p>Magnetic Induction</p>"],
                    options_hi: ["<p>विद्युत प्रेरण</p>", "<p>विद्युतचुंबकीय प्रेरण</p>",
                                "<p>विद्युत ऊर्जा</p>", "<p>चुंबकीय प्रेरण</p>"],
                    solution_en: "<p>3.(b) <strong>Electromagnetic Induction -</strong> The generation of electric current in a conductor due to a changing magnetic field. <strong>Electrical Energy -</strong> Energy resulting from the movement of electric charges through a conductor. <strong>Magnetic induction -</strong> The process by which a magnetic substance acquires magnetic properties temporarily due to the presence of a magnet close to it.</p>",
                    solution_hi: "<p>3.(b) <strong>विद्युतचुंबकीय प्रेरण</strong> - बदलते चुंबकीय क्षेत्र के कारण किसी चालक में विद्युत धारा का उत्पन्न होना। <strong>विद्युत ऊर्जा - </strong>किसी चालक के माध्यम से विद्युत आवेशों की गति से उत्पन्न ऊर्जा। <strong>चुंबकीय प्रेरण </strong>- वह प्रक्रिया जिसके द्वारा कोई चुंबकीय पदार्थ अपने निकट चुंबक की उपस्थिति के कारण अस्थायी रूप से चुंबकीय गुण प्राप्त कर लेता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. Which of the following devices is based on the phenomenon of electromagnetic induction?",
                    question_hi: "4. निम्नलिखित में से कौन सा उपकरण विद्युत चुम्बकीय प्रेरण की घटना पर आधारित है?",
                    options_en: [" Ammeter ", " Generator ", 
                                " Galvanometer  ", " Bulb  "],
                    options_hi: [" एमीटर", " जनित्र",
                                " गैल्वेनोमीटर", " बल्ब "],
                    solution_en: "4.(b) Generator is a device that converts motive force (mechanical energy) into electrical power for use in an external circuit to produce electricity. Galvanometer is one of the instruments for measuring electrical current par excellence. Ammeter is a device or instrument that can measure either direct or alternating electric current in amperes that flow in an electric circuit.",
                    solution_hi: "4.(b)  जनित्र एक उपकरण है जो बिजली पैदा करने के लिए बाह्य सर्किट में उपयोग के लिए प्रेरक बल (यांत्रिक ऊर्जा) को विद्युत शक्ति में परिवर्तित करता है। गैल्वेनोमीटर विद्युत धारा को उत्कृष्टता से मापने वाले उपकरणों में से एक है। एमीटर एक उपकरण या यंत्र है जो विद्युत परिपथ में प्रवाहित होने वाले एम्पीयर में प्रत्यक्ष या प्रत्यावर्ती विद्युत धारा को माप सकता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. _________is NOT an example of an insulator.</p>",
                    question_hi: "<p>5. _________ एक विद्युतरोधी का उदाहरण नहीं है।</p>",
                    options_en: ["<p>Diamond</p>", "<p>Rubber</p>", 
                                "<p>Human body</p>", "<p>Glass</p>"],
                    options_hi: ["<p>हीरा</p>", "<p>रबर</p>",
                                "<p>मानव शरीर</p>", "<p>कांच</p>"],
                    solution_en: "<p>5.(c) The <strong>human body</strong> is a conductor because the cells of the body contain various ions which help to conduct electricity. More examples of <strong>Conductors</strong> - Silver, Copper, Gold, Aluminium, Iron, Steel, Brass, Bronze. <strong>Insulators</strong> : Materials that do not allow electricity to pass through them. <strong>Examples</strong> - Plastics, Styrofoam, Paper, Rubber, Glass and Dry air.</p>",
                    solution_hi: "<p>5.(c) <strong>मानव शरीर</strong> एक चालक है क्योंकि शरीर की कोशिकाओं में विभिन्न आयन होते हैं जो बिजली के संचालन में मदद करते हैं।<strong> चालक के उदाहरण </strong>- चांदी, तांबा, सोना, एल्युमीनियम, लोहा, स्टील, पीतल, कांस्य। <strong>विद्युतरोधी(Insulators):</strong> वे पदार्थ जो बिजली को अपने अंदर से गुजरने नहीं देतीं। उदाहरण - प्लास्टिक, स्टायरोफोम, कागज, रबर, कांच और शुष्क हवा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6.Which of the following is NOT an example of the heating effect of current?</p>",
                    question_hi: "<p>6. निम्नलिखित में से कौन धारा के ऊष्मीय प्रभाव का उदाहरण नहीं है?</p>",
                    options_en: ["<p>Bulb</p>", "<p>Working of a fan</p>", 
                                "<p>Electric iron</p>", "<p>Electric kettle</p>"],
                    options_hi: ["<p>बल्ब</p>", "<p>पंखे की कार्यप्रणाली</p>",
                                "<p>विद्युत प्रेस</p>", "<p>विद्युत केतली</p>"],
                    solution_en: "<p>6.(b) <strong>Working of a fan </strong>- It works on the principle of electromagnetic induction<strong>. Heating effect of electric current: </strong>States that when an electric current passes through a conductor it becomes hot and produces heat. <strong>Examples</strong> - Bulbs, Electric iron, Electric Kettle, Electric Heater, Electric Fuse.</p>",
                    solution_hi: "<p>6.(b)<strong> पंखे की कार्यप्रणाली </strong>। यह विद्युत चुम्बकीय प्रेरण के सिद्धांत पर कार्य करता है। विद्युत धारा का तापीय प्रभाव: बताता है कि जब विद्युत धारा किसी चालक से होकर गुजरती है तो वह गर्म हो जाती है और ऊष्मा पैदा करती है। उदाहरण - बल्ब, इलेक्ट्रिक आयरन, इलेक्ट्रिक केतली, इलेक्ट्रिक हीटर, इलेक्ट्रिक फ्यूज।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. The process of depositing a layer of any desired metal on another material, by means of electricity, is called _________</p>",
                    question_hi: "<p>7. विद्युत के माध्यम से किसी अन्य पदार्थ पर किसी वांछित धातु की परत जमा करने की प्रक्रिया कहलाती है</p>",
                    options_en: ["<p>Electroplating</p>", "<p>Electromagnet</p>", 
                                "<p>Electro conductor</p>", "<p>Electrode</p>"],
                    options_hi: ["<p>विद्युत लेपन</p>", "<p>विद्युत चुम्बकीय</p>",
                                "<p>विद्युत चालक</p>", "<p>इलेक्ट्रोड</p>"],
                    solution_en: "<p>7.(a) <strong>Electroplating.</strong> Electromagnets are made of coils of wire with electricity passing through them. A <strong>conductor </strong>is an object or type of material that allows the flow of charge (electrical current) in one or more directions. Metals are commonly used as electrical conductors. An <strong>electrode </strong>is an electrical conductor used to make contact with a nonmetallic part of a circuit (e.g. a semiconductor, an electrolyte, a vacuum or air).</p>",
                    solution_hi: "<p>7.(a) <strong>विद्युत लेपन (Electroplating)।</strong> <strong>विद्युत चुम्बक</strong> तार की कुंडलियों से बने होते हैं जिनमें से विद्युत प्रवाहित होती है। चालक एक वस्तु या प्रकार की पदार्थ है जो एक या अधिक दिशाओं में आवेश (विद्युत धारा) के प्रवाह की अनुमति देता है। धातुओं का उपयोग सामान्यतः विद्युत चालक के रूप में किया जाता है। <strong>इलेक्ट्रोड </strong>एक विद्युत चालक है जिसका उपयोग सर्किट के अधातु भाग (जैसे अर्द्ध चालक, इलेक्ट्रोलाइट, वैक्यूम या वायु) के साथ संपर्क बनाने के लिए किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which of the following devices is used to convert alternating current into direct current?</p>",
                    question_hi: "<p>8. प्रत्यावर्ती धारा को दिष्ट धारा में परिवर्तित करने के लिए निम्नलिखित में से किस उपकरण का उपयोग किया जाता है?</p>",
                    options_en: ["<p>Calorimeter</p>", "<p>Rheostat</p>", 
                                "<p>Capacitor</p>", "<p>Rectifier</p>"],
                    options_hi: ["<p>कैलोरीमीटर (Calorimeter)</p>", "<p>धारा नियंत्रक (Rheostat)</p>",
                                "<p>संधारित्र (Capacitor)</p>", "<p>दिष्टकारी (Rectifier)</p>"],
                    solution_en: "<p>8.(d) <strong>Rectifier. Calorimeter </strong>is a measuring device used for calorimetry or the process of measuring the heat of chemical reactions or physical changes as well as heat capacity. <strong>Rheostat </strong>is a variable resistor that is used to control current. <strong>Capacitor</strong> is a device for storing electrical energy consisting of two conductors in close proximity and insulated from each other.</p>",
                    solution_hi: "<p>8.(d) <strong>दिष्टकारी (Rectifier)। कैलोरीमीटर</strong> एक मापक यन्त्र है जिसका उपयोग कैलोरीमेट्री या रासायनिक अभिक्रियाओं या भौतिक परिवर्तनों की ऊष्मा के साथ-साथ ऊष्मा क्षमता को मापने की प्रक्रिया के लिए किया जाता है। <strong>धारा नियंत्रक</strong> एक परिवर्तनशील अवरोधक है जिसका उपयोग धारा को नियंत्रित करने के लिए किया जाता है। <strong>संधारित्र (Capacitor)</strong> विद्युत ऊर्जा को संग्रहीत करने के लिए एक उपकरण है जिसमें दो चालक निकटता में होते हैं और एक दूसरे से अलग होते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following solutions do NOT conduct electricity?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन सा विलयन बिजली का संचालन नहीं करता है?</p>",
                    options_en: ["<p>Acid and base</p>", "<p>Glucose and base</p>", 
                                "<p>Alcohol and glucose</p>", "<p>Alcohol and acid</p>"],
                    options_hi: ["<p>अम्ल और क्षार</p>", "<p>ग्लूकोज और क्षार</p>",
                                "<p>अल्कोहल और ग्लूकोज</p>", "<p>अल्कोहल और अम्ल</p>"],
                    solution_en: "<p>9.(c) <strong>Alcohol and Glucose.</strong> Reason - No free ions to conduct electrical charges across the solution<strong>. Acids and bases</strong> exhibit the property of conducting electricity only when they are taken in aqueous solution in which they can completely ionize and generate free ions.</p>",
                    solution_hi: "<p>9.(c) <strong>अल्कोहल और ग्लूकोज </strong>। कारण - विलयन में विद्युत आवेश का संचालन करने के लिए कोई मुक्त आयन नहीं है। <strong>अम्ल और क्षार</strong> विद्युत संचालन का गुण तभी प्रदर्शित करते हैं जब उन्हें जलीय घोल में मिलाया जाता है जिसमें वे पूरी तरह से आयनित हो सकते हैं और मुक्त आयन उत्पन्न कर सकते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Three wires of resistance 3 ohm, 6 ohm and 9 ohm are connected in parallel. What will be the total resistance of the circuit?</p>",
                    question_hi: "<p>10. प्रतिरोध के तीन तार 3 ओम, 6 ओम और 9 ओम समानांतर में जुड़े हुए हैं। परिपथ का कुल प्रतिरोध कितना होगा?</p>",
                    options_en: ["<p>11 ohms</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>ohms</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math>ohms</p>", "<p>18 ohms</p>"],
                    options_hi: ["<p>11 ओम</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>ओम</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math>ओम</p>", "<p>18 ओम</p>"],
                    solution_en: "<p>10.(b) <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> <strong>Ohms.</strong> Given Three wires of resistance 3 ohm, 6 ohm and 9 ohm are connected in parallel. <br>Total Resistance in parallel Circuit is given by Formula, <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>R</mi><mi>e</mi><mi>q</mi></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>R</mi><mn>1</mn></mrow></mfrac><mi>&#160;</mi></mstyle></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>R</mi><mn>2</mn></mrow></mfrac><mi>&#160;</mi></mstyle></math>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>R</mi><mn>3</mn></mrow></mfrac><mi>&#160;</mi></mstyle></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>9</mn></mfrac></mstyle></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mrow><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">q</mi></mrow></msub></math>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> Ohms</p>",
                    solution_hi: "<p>10.(b) <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> <strong>ओम</strong> । दिए गए 3 ओम, 6 ओम और 9 ओम प्रतिरोध के तीन तार समानांतर में जुड़े हुए हैं।<br>समानांतर परिपथ में कुल प्रतिरोध सूत्र द्वारा दिया गया है,<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>R</mi><mi>e</mi><mi>q</mi></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>R</mi><mn>1</mn></mrow></mfrac><mi>&#160;</mi></mstyle></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>R</mi><mn>2</mn></mrow></mfrac><mi>&#160;</mi></mstyle></math>+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mrow><mi>R</mi><mn>3</mn></mrow></mfrac><mi>&#160;</mi></mstyle></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>9</mn></mfrac></mstyle></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mrow><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">q</mi></mrow></msub></math>= <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> ओम</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. Which equipment is used to transform electrical energy into mechanical energy?",
                    question_hi: "11. विद्युत ऊर्जा को यांत्रिक ऊर्जा में बदलने के लिए किस उपकरण का उपयोग किया जाता है?",
                    options_en: [" Battery", " Electric motor", 
                                " Candle", " Photocell"],
                    options_hi: [" बैटरी", " इलेक्ट्रिक मोटर",
                                " मोमबत्ती", " फोटोसेल"],
                    solution_en: "<p>11.(b)<strong> Electric motor</strong> (works on the principle of &ldquo;Faraday\'s law of electromagnetic induction) Applications : Fans, washing machines, refrigerators, pumps, and vacuum cleaners. The first electric motor - Invented by Michael Faraday in 1821.<strong> A battery</strong> stores chemical energy and converts it to electrical energy.<strong> A photocell</strong> converts light energy into electrical energy.</p>",
                    solution_hi: "<p>11.(b) <strong>इलेक्ट्रिक मोटर </strong>(फैराडे के विद्युत चुम्बकीय प्रेरण के नियम के सिद्धांत पर कार्य करती है) उपयोग: पंखे, वाशिंग मशीन, रेफ्रिजरेटर, पंप और वैक्यूम क्लीनर । पहला इलेक्ट्रिक मोटर - 1821 में माइकल फैराडे द्वारा आविष्कार किया गया। <strong>बैटरी </strong>रासायनिक ऊर्जा को संग्रहीत करती है और इसे विद्युत ऊर्जा में परिवर्तित करती है। <strong>फोटो सेल</strong> (Photocell) प्रकाश ऊर्जा को विद्युत ऊर्जा में परिवर्तित करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. The neutral point in the magnetic field of a horizontally positioned bar magnet is the :</p>",
                    question_hi: "<p>12. क्षैतिज रूप से स्थित छड़ चुंबक के चुंबकीय क्षेत्र में उदासीन बिंदु कौन-सा होता है?</p>",
                    options_en: ["<p>point where the magnetic field changes direction</p>", "<p>point where the magnetic field is the weakest</p>", 
                                "<p>point where the magnetic field is the strongest</p>", "<p>point where the magnetic field is zero</p>"],
                    options_hi: ["<p>वह बिंदु जहां चुंबकीय क्षेत्र दिशा बदलता है।</p>", "<p>वह बिंदु जहां चुंबकीय क्षेत्र सबसे दुर्बल होता है।</p>",
                                "<p>वह बिंदु जहां चुंबकीय क्षेत्र सबसे प्रबल होता है।</p>", "<p>वह बिंदु जहां चुंबकीय क्षेत्र शून्य होता है।</p>"],
                    solution_en: "<p>12.(d) <strong>Neutral point</strong> : The point at which the resultant magnetic field is zero. It is a point where the magnetic field produced due to a bar magnet is completely balanced by the horizontal component of Earth\'s <strong>magnetic field.</strong> Magnetic Field - The region around a magnetic material or a moving electric charge within which the force of magnetism acts.<strong> Types of magnets :</strong> Permanent magnet, temporary magnet and electromagnet.</p>",
                    solution_hi: "<p>12.(d) <strong>उदासीन बिंदु</strong> : वह बिंदु जिस पर परिणामी चुंबकीय क्षेत्र शून्य होता है। यह एक ऐसा बिंदु है जहां एक छड़ चुंबक के कारण उत्पन्न चुंबकीय क्षेत्र, पृथ्वी के चुंबकीय क्षेत्र के क्षैतिज घटक द्वारा पूरी तरह से संतुलित होता है। <strong>चुंबकीय क्षेत्र -</strong> किसी चुंबकीय पदार्थ या गतिशील विद्युत आवेश के आसपास का क्षेत्र जिसमें चुंबकत्व का बल कार्य करता है। <strong>चुम्बक के प्रकार :</strong> स्थायी चुम्बक, अस्थायी चुम्बक और विद्युत चुम्बक।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. In 1834, who proposed a law that states that the current induced by a change in flow opposes the effect producing the change ?</p>",
                    question_hi: "<p>13. 1834 में किसने एक नियम प्रस्तावित किया था जिसके अनुसार प्रवाह में परिवर्तन से प्रेरित विद्युत धारा परिवर्तन उत्पन्न करने वाले प्रभाव का प्रतिरोध करती है ?</p>",
                    options_en: ["<p>David Brewster</p>", "<p>Henry Moseley</p>", 
                                "<p>Christian Johann Doppler</p>", "<p>Heinrich Friedrich Lenz</p>"],
                    options_hi: ["<p>डेविड ब्रूस्टर (David Brewster)</p>", "<p>हेनरी मोजले (Henry Moseley)</p>",
                                "<p>क्रिश्चियन जोहान डॉपलर (Christian Johann Doppler)</p>", "<p>हेनरिक फ्रेडरिच लेंज (Heinrich Friedrich Lenz)</p>"],
                    solution_en: "<p>13.(d) <strong>Heinrich Friedrich Lenz. Lenz&rsquo;s law</strong> - An induced electric current flows in a direction such that the current opposes the change that induced it. Lenz&rsquo;s law is used in electromagnetic brakes and induction cooktops. It is also applied to electric generators, AC generators. <strong>Christian Johann Doppler </strong>- Doppler&rsquo;s effect refers to the change in wave frequency during the relative motion between a wave source and its observer. <strong>Sir David Brewster </strong>- Brewster law is a statement that says that when unpolarized light falls on an interface, the reflected light is completely polarized if the angle of incidence is a specific angle called the Brewster\'s angle.</p>",
                    solution_hi: "<p>13.(d) <strong>हेनरिक फ्रेडरिच लेंज (Heinrich Friedrich Lenz)</strong>।<strong> लेन्ज़ का नियम -</strong> प्रेरित धारा की दिशा हमेशा ऐसी होती है जो परिपथ में परिवर्तन या इसे उत्पन्न करने वाले चुंबकीय क्षेत्र का विरोध करती है। लेन्ज़ के नियम का उपयोग विद्युत चुम्बकीय ब्रेक और इंडक्शन कुकटॉप में किया जाता है। इसे विद्युत जनरेटर, AC जनरेटर पर भी लागू किया जाता है।<strong> क्रिश्चियन जोहान डॉपलर </strong>- डॉपलर का प्रभाव एक तरंग स्रोत और उसके पर्यवेक्षक के बीच सापेक्ष गति के दौरान तरंग आवृत्ति में परिवर्तन को संदर्भित करता है।<strong> सर डेविड ब्रूस्टर -</strong> ब्रूस्टर नियम कहता है कि जब अध्रुवीकृत प्रकाश किसी अंतराफलक पर पड़ता है, तो परावर्तित प्रकाश पूर्णतय ध्रुवीकृत हो जाता है, यदि आपतन कोण एक विशिष्ट कोण होता है, जिसे ब्रूस्टर कोण कहा जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following options is correct for Coulomb\'s law?</p>",
                    question_hi: "<p>14. कूलॉम नियम के लिए निम्नलिखित में से कौन-सा विकल्प सही है?</p>",
                    options_en: ["<p>Valid for both point and distributed charges</p>", "<p>Valid for only distributed charge</p>", 
                                "<p>Valid for only point charge</p>", "<p>Valid for neither point charge nor distributed charge</p>"],
                    options_hi: ["<p>बिंदु और वितरित आवेश दोनों के लिए मान्य</p>", "<p>केवल वितरित आवेश के लिए मान्य</p>",
                                "<p>केवल बिन्दु आवेश के लिए मान्य</p>", "<p>न तो बिन्दु आवेश और न ही वितरित आवेश के लिए मान्य</p>"],
                    solution_en: "<p>14.(c) <strong>Valid for only point charge. Coulomb&rsquo;s Law :</strong> The electrical force between two charged objects is directly proportional to the product of the quantity of charge on the objects and inversely proportional to the square of the separation distance between the two objects. F = <math display=\"inline\"><mfrac><mrow><mi>k</mi><mi>&#160;</mi><msub><mrow><mi>Q</mi></mrow><mrow><mn>1</mn></mrow></msub><mi>&#160;</mi><msub><mrow><mi>Q</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow><mrow><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math>, where {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Q</mi><mn>1</mn></msub><mo>&#160;</mo></math>- quantity of charge on object 1 (in Coulombs), <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Q</mi><mn>2</mn></msub></math>- quantity of charge on object 2 (in Coulombs), and d - distance of separation between the two objects (in meters). k - proportionality constant (the Coulomb\'s law constant)}. Value of k -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mn>8</mn><mo>.</mo><mn>988</mn><mo>&#215;</mo><msup><mn>10</mn><mn>9</mn></msup><mo>&#160;</mo><mi>N</mi><msup><mi>m</mi><mn>2</mn></msup><mo>/</mo><msup><mi>C</mi><mn>2</mn></msup></math></p>",
                    solution_hi: "<p>14.(c) <strong>केवल बिन्दु आवेश के लिए मान्य। कूलॉम का नियम :</strong> दो आवेशित वस्तुओं के बीच विद्युत बल वस्तुओं पर आवेश की मात्रा के गुणनफल के समानुपातिक होता है और दो वस्तुओं के बीच की दूरी के वर्ग के व्युत्क्रमानुपाती होता है। F = <math display=\"inline\"><mfrac><mrow><mi>k</mi><mi>&#160;</mi><msub><mrow><mi>Q</mi></mrow><mrow><mn>1</mn></mrow></msub><mi>&#160;</mi><msub><mrow><mi>Q</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow><mrow><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math>। जहां {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Q</mi><mn>1</mn></msub><mo>&#160;</mo></math>- वस्तु 1 पर आवेश की मात्रा (कूलॉम में), <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Q</mi><mn>2</mn></msub></math>- वस्तु 2 पर आवेश की मात्रा (कूलॉम में), और d - दो वस्तुओं के बीच पृथक्करण की दूरी (मीटर में)। k - एक आनुपातिकता स्थिरांक (कूलॉम का नियम स्थिरांक)}। k का मान -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mn>8</mn><mo>.</mo><mn>988</mn><mo>&#215;</mo><msup><mn>10</mn><mn>9</mn></msup><mo>&#160;</mo><mi>N</mi><msup><mi>m</mi><mn>2</mn></msup><mo>/</mo><msup><mi>C</mi><mn>2</mn></msup></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which of the following laws state that &ldquo;The magnitude of the induced EMF in a circuit is equal to the time rate of change of magnetic flux through the circuit&rdquo;?</p>",
                    question_hi: "<p>15. निम्नलिखित में से किस नियम के अनुसार \"प्रेरित विद्युत वाहक बल का परिणाम चुंबकीय फ्लक्स में समय के साथ होने वाले परिवर्तन की दर के बराबर होता है\"?</p>",
                    options_en: ["<p>Faraday&rsquo;s law</p>", "<p>Coulomb&rsquo;s law</p>", 
                                "<p>Kirchhoff&rsquo;s laws</p>", "<p>Laplace&rsquo;s law</p>"],
                    options_hi: ["<p>फैराडे का नियम</p>", "<p>कूलॉम का नियम</p>",
                                "<p>किरचॉफ के नियम</p>", "<p>लाप्लास का नियम</p>"],
                    solution_en: "<p>15.(a)<strong> Faraday\'s law. Coulomb\'s law </strong>states that the force between two charged particles is directly proportional to the product of their charges and inversely proportional to the square of the distance between them.<strong> Kirchhoff\'s laws</strong> - The algebraic sum of all the currents meeting at a point in a circuit is equal to zero<strong>. Laplace\'s law -</strong> The tension in the walls of a hollow sphere or cylinder is dependent on the pressure of its contents and its radius.</p>",
                    solution_hi: "<p>15.(a) <strong>फैराडे का नियम। कूलॉम के नियम </strong>के अनुसार दो आवेशित कणों के बीच का बल उनके आवेशों के गुणनफल के अनुक्रमानुपाती और उनके बीच की दूरी के वर्ग के व्युत्क्रमानुपाती होता है। <strong>किरचॉफ के नियम - </strong>किसी परिपथ में एक बिंदु पर मिलने वाली सभी धाराओं का बीजगणितीय योग शून्य के बराबर होता है।<strong> लाप्लास के नियम</strong> के अनुसार, किसी खोखले गोले या बेलन की दीवारों में तनाव उसकी सामग्री के दबाव और उसकी त्रिज्या पर निर्भर&nbsp;करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>