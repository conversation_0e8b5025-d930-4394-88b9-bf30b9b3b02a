<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 50</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">50</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 49,
                end: 49
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following players is associated with para-badminton?</p>",
                    question_hi: "<p>1. निम्नलिखित में से किस खिलाड़ी का संबंध पैरा-बैडमिंटन से है?</p>",
                    options_en: ["<p>Sumit Antil</p>", "<p>Manish Narwal</p>", 
                                "<p>Avani Lekhara</p>", "<p>Krishna Nagar</p>"],
                    options_hi: ["<p>सुमित अंतिल</p>", "<p>मनीष नरवाल</p>",
                                "<p>अवनि लेखरा</p>", "<p>कृष्णा नागर</p>"],
                    solution_en: "<p>1.(d) <strong>Krishna Nagar.</strong> He clinched the Gold medal in Tokyo Paralympics 2020 and thus became the first ever Gold medalist in SH6 category in Para Badminton in the Paralympic Games. Other famous Indian para-badminton players - Pramod Bhagat, Suhas L. Y, Thulasimathi M, Nitesh Kumar, Chirag Baretha, Manasi Joshi.</p>",
                    solution_hi: "<p>1.(d) <strong>कृष्णा नागर </strong>I उन्होंने टोक्यो पैरालंपिक 2020 में स्वर्ण पदक जीता और इस तरह पैरालंपिक खेलों में पैरा बैडमिंटन में SH6 श्रेणी में पहले स्वर्ण पदक विजेता बन गए। अन्य प्रसिद्ध भारतीय पैरा-बैडमिंटन खिलाड़ी - प्रमोद भगत, सुहास एल वाई, थुलसिमथी एम, नितेश कुमार, चिराग बरेठा, मानसी जोशी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. According to the Census of India 2011, which is the second most literate state in India?</p>",
                    question_hi: "<p>2. भारत की जनगणना 2011 के अनुसार, भारत का दूसरा सबसे अधिक साक्षर राज्य कौन-सा है?</p>",
                    options_en: ["<p>Mizoram</p>", "<p>Karnataka</p>", 
                                "<p>Odisha</p>", "<p>Tripura</p>"],
                    options_hi: ["<p>मिज़ोरम</p>", "<p>कर्नाटक</p>",
                                "<p>ओडिशा</p>", "<p>त्रिपुरा</p>"],
                    solution_en: "<p>2.(a) <strong>Mizoram.</strong> As per Census 2011: Most literate state - Kerala (94%). Least literate state - Bihar (61.8%). Most literate Union Territory - Lakshadweep (91.85). Least literate Union Territory - Dadra and Nagar Haveli (76.24). The literacy rate in the country is 74.04 per cent, 82.14 for males and 65.46 for females.</p>",
                    solution_hi: "<p>2.(a) <strong>मिज़ोरम।</strong> जनगणना 2011 के अनुसार: सर्वाधिक साक्षर राज्य - केरल (94%)। सबसे कम साक्षर राज्य - बिहार (61.8%)। सर्वाधिक साक्षर केंद्र शासित प्रदेश - लक्षद्वीप (91.85%)। सबसे कम साक्षर केंद्र शासित प्रदेश - दादरा एवं नगर हवेली (76.24%)। देश में साक्षरता दर 74.04 प्रतिशत है, पुरुषों के लिए 82.14 प्रतिशत और महिलाओं के लिए 65.46 प्रतिशत है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Sitara Devi was a _____ dancer of India.</p>",
                    question_hi: "<p>3. सितारा देवी भारत की _____ नृत्यांगना थीं।</p>",
                    options_en: ["<p>Kuchipudi</p>", "<p>Kathak</p>", 
                                "<p>Bharatanatyam</p>", "<p>Kathakali</p>"],
                    options_hi: ["<p>कुचिपूड़ी</p>", "<p>कथक</p>",
                                "<p>भरतनाट्यम</p>", "<p>कथकली</p>"],
                    solution_en: "<p>3.(b) <strong>Kathak.</strong> Sitara Devi was depicted as the Nritya Samragini by Rabindranath Tagore. Her Awards - Sangeet Natak Akademi award (1969), Padma Shri (1973), Kalidas Samman (1995), Lifetime Achievement Award (2011). Other famous Kathak dancers of India - Shambhu Maharaj, Shovna Narayan, Pandit Birju Maharaj. Kathak is a dance form that originated in Uttar Pradesh.</p>",
                    solution_hi: "<p>3.(b) <strong>कथक</strong> I सितारा देवी को रवीन्द्रनाथ टैगोर ने नृत्य साम्राज्ञी के रूप में चित्रित किया था। पुरस्कार - संगीत नाटक अकादमी पुरस्कार (1969), पद्म श्री पुरस्कार (1973), कालिदास सम्मान पुरस्कार (1995), लाइफटाइम अचीवमेंट पुरस्कार (2011)। भारत के अन्य प्रसिद्ध कथक नर्तक - शंभू महाराज, शोवना नारायण, पंडित बिरजू महाराज। कथक एक नृत्य शैली है, जिसकी उत्पत्ति उत्तर प्रदेश में हुई थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Avani Lekhara is associated with which of the following sports?</p>",
                    question_hi: "<p>4. अवनि लेखरा निम्नलिखित में से किस खेल से संबंधित हैं?</p>",
                    options_en: ["<p>Freestyle wrestling</p>", "<p>Para-athletics</p>", 
                                "<p>Para-badminton</p>", "<p>Paralympic shooting</p>"],
                    options_hi: ["<p>फ्रीस्टाइल कुश्ती</p>", "<p>पैरा-एथलेटिक्स</p>",
                                "<p>पैरा-बैडमिंटन</p>", "<p>पैरालंपिक निशानेबाजी</p>"],
                    solution_en: "<p>4.(d) <strong>Paralympic shooting.</strong> Avani Lekhara won a gold medal in R2-Women\'s 10m Air Rifle Standing SH1 and a bronze medal in the 50m rifle 3 positions at the Tokyo Paralympics Games 2020. Other Indian players associated with Paralympic shooting - Manish Narwal, Singhraj, Deepender Singh, Rahul Jakhar, Sidhartha Babu, Rubina Francis. Famous Indian Paralympic players - Devendra Jhajaria (Javelin thrower), Deepa Malik (Shotput), Amit Kumar (Club thrower), Pooja Rani (Archery), Farman Basha (Powerlifter).</p>",
                    solution_hi: "<p>4.(d) <strong>पैरालंपिक शूटिंग।</strong> उन्होंने टोक्यो पैरालिंपिक गेम्स 2020 में R2-महिला 10 मीटर एयर राइफल स्टैंडिंग SH1 में स्वर्ण पदक और 50 मीटर राइफल 3 पोजीशन में कांस्य पदक जीता। पैरालंपिक शूटिंग से जुड़े अन्य भारतीय खिलाड़ी - मनीष नरवाल, सिंहराज, दीपेंद्र सिंह, राहुल जाखड़, सिद्धार्थ बाबू, रूबीना फ्रांसिस। प्रसिद्ध भारतीय पैरालम्पिक खिलाड़ी - देवेन्द्र झाझरिया (भाला फेंक खिलाड़ी), दीपा मलिक (शॉटपुट), अमित कुमार (क्लब थ्रोअर), पूजा रानी (तीरंदाजी), फरमान बाशा (भारोत्तोलक)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following Articles are related to Right Against Exploitation?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन-से अनुच्छेद शोषण के विरुद्ध अधिकार से संबंधित हैं?</p>",
                    options_en: ["<p>Article 28-29</p>", "<p>Article 23-24</p>", 
                                "<p>Article 31-32</p>", "<p>Article 25-26</p>"],
                    options_hi: ["<p>अनुच्छेद 28-29</p>", "<p>अनुच्छेद 23-24</p>",
                                "<p>अनुच्छेद 31-32</p>", "<p>अनुच्छेद 25-26</p>"],
                    solution_en: "<p>5.(b) <strong>Article 23-24.</strong> Right Against Exploitation prohibit all forms of forced labour, child labour and traffic in human beings. Fundamental rights (FR) are the basic human rights enshrined in the Constitution of India which are guaranteed to all citizens. FR rights are enforceable by the courts, subject to certain conditions. Other five Fundamental Rights - Right to equality (Articles 14&ndash;18), Right to freedom (Articles 19&ndash;22), Right to freedom of religion (Articles 25&ndash;28), Cultural and educational rights (Articles 29&ndash;30), Right to constitutional remedies (Article 32&ndash;35).</p>",
                    solution_hi: "<p>5.(b) <strong>अनुच्छेद 23-24 I</strong> शोषण के विरुद्ध अधिकार सभी प्रकार के जबरन श्रम, बाल श्रम और मानव तस्करी पर रोक लगाते हैं। मौलिक अधिकार भारत के संविधान में निहित बुनियादी मानवाधिकार हैं, जिनकी गारंटी सभी नागरिकों को दी गई है। ये अधिकार कुछ शर्तों के अधीन, अदालतों द्वारा लागू किए जा सकते हैं। अन्य पांच मौलिक अधिकार - समानता का अधिकार (अनुच्छेद 14-18), स्वतंत्रता का अधिकार (अनुच्छेद 19-22), धार्मिक स्वतंत्रता का अधिकार (अनुच्छेद 25-28), सांस्कृतिक और शैक्षिक अधिकार (अनुच्छेद 29-30), संवैधानिक उपचारों का अधिकार (अनुच्छेद 32-35)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. In 8<sup>th</sup> century India, Nagabhatta I established which of the following Rajput dynasties in Malwa region?</p>",
                    question_hi: "<p>6. 8वीं शताब्दी में भारत में नागभट्ट प्रथम ने मालवा क्षेत्र में निम्नलिखित में से किस राजपूत राजवंश की स्थापना की थी?</p>",
                    options_en: ["<p>Tomara</p>", "<p>Gurjara-Pratihara</p>", 
                                "<p>Chauhan</p>", "<p>Parmara</p>"],
                    options_hi: ["<p>तोमर</p>", "<p>गुर्जर-प्रतिहार</p>",
                                "<p>चौहान</p>", "<p>परमार</p>"],
                    solution_en: "<p>6.(b) <strong>Gurjara-Pratihara</strong>. The dynasty also took part in the tripartite struggle for the control over the city of Kannauj. The Pratiharas originated from Gujarat or the Southwest Rajasthan. The dynasty ruled mainly from the 8th to 10th century. Initially, the main center of their power was Marwar. Mihir bhoj was the greatest ruler of this dynasty.</p>",
                    solution_hi: "<p>6.(b) <strong>गुर्जर-प्रतिहार। </strong>इस राजवंश ने कन्नौज शहर पर नियंत्रण के लिए त्रिपक्षीय संघर्ष में भी भाग लिया। प्रतिहारों की उत्पत्ति गुजरात या दक्षिण-पश्चिम राजस्थान से हुई थी। इस राजवंश ने मुख्यतः 8वीं से 10वीं शताब्दी तक शासन किया। प्रारंभ में उनकी शक्ति का मुख्य केन्द्र मारवाड़ था। मिहिर भोज इस वंश का सबसे महान शासक था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. In which of the following years was the Election Commission of India established?</p>",
                    question_hi: "<p>7. भारत के चुनाव आयोग की स्थापना निम्नलिखित में से किस वर्ष में की गई थी?</p>",
                    options_en: ["<p>1954</p>", "<p>1957</p>", 
                                "<p>1950</p>", "<p>1960</p>"],
                    options_hi: ["<p>1954</p>", "<p>1957</p>",
                                "<p>1950</p>", "<p>1960</p>"],
                    solution_en: "<p>7.(c) <strong>1950.</strong> Election Commission of India (ECI) administers elections to the Lok Sabha, Rajya Sabha, State Legislative Assemblies in India, and the offices of the President and Vice President in the country. It is a permanent constitutional body. Originally the commission had only a Chief Election Commissioner (CEC). It currently consists of the Chief Election Commissioner and two Election Commissioners. The President appoints Chief Election Commissioner and Election Commissioners. They have tenure of six years, or up to the age of 65 years, whichever is earlier. The first Chief Election Commissioner of India was Sukumar Sen.</p>",
                    solution_hi: "<p>7.(c) <strong>1950।</strong> भारत का चुनाव आयोग (ECI) भारत में लोकसभा, राज्यसभा, राज्य विधानसभाओं और देश में राष्ट्रपति और उपराष्ट्रपति के कार्यालयों के लिए चुनाव का संचालन करता है। मूलतः आयोग में केवल एक मुख्य चुनाव आयुक्त था। इसमें वर्तमान में मुख्य चुनाव आयुक्त और दो चुनाव आयुक्त शामिल हैं। राष्ट्रपति मुख्य चुनाव आयुक्त और चुनाव आयुक्तों की नियुक्ति करता है। उनका कार्यकाल 6 वर्ष या 65 वर्ष की आयु तक, जो भी पहले हो, होता है। भारत के पहले मुख्य चुनाव आयुक्त सुकुमार सेन थे।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which Satavahana king restored the Satavahana realm to its earlier greatness around 125 CE?</p>",
                    question_hi: "<p>8. किस सातवाहन राजा ने 125 CE के आसपास सातवाहन क्षेत्र को उसकी पूर्व की महानता में बहाल किया?</p>",
                    options_en: ["<p>Gautamiputra</p>", "<p>Krishna III</p>", 
                                "<p>Mihira Bhoja</p>", "<p>Rajaraja I</p>"],
                    options_hi: ["<p>गौतमीपुत्र</p>", "<p>कृष्ण तृतीय</p>",
                                "<p>मिहिर भोज</p>", "<p>राजराजा प्रथम</p>"],
                    solution_en: "<p>8.(a) <strong>Gautamiputra.</strong> King Simuka was the founder of the Satavahana dynasty. The dynasty was an ancient Indian dynasty based in the Deccan. They played the most significant role in Indian history in the period between the fall of the Mauryas and the rise of the Gupta Empire. The dynasty reached its zenith under the rule of Gautamiputra Satakarni and his successor Vasisthiputra Pulamavi. Pratishthan (Paithan), was the capital of the kingdom. It was located on the banks of the river Godavari in the Aurangabad district. Satavahanas were also referred to as Andhras in the Puranas.</p>",
                    solution_hi: "<p>8.(a) <strong>गौतमीपुत्र</strong> I राजा सिमुक सातवाहन वंश के संस्थापक थे। यह राजवंश दक्कन में स्थित एक प्राचीन भारतीय राजवंश था। उन्होंने मौर्यों के पतन और गुप्त साम्राज्य के उदय के बीच की अवधि में भारतीय इतिहास में सबसे महत्वपूर्ण भूमिका निभाई। गौतमीपुत्र शातकर्णी और उनके उत्तराधिकारी वासिष्ठिपुत्र पुलमावी के शासन में यह राजवंश अपने चरम पर पहुंच गया। प्रतिष्ठान (पैठान), साम्राज्य की राजधानी थी। यह औरंगाबाद जिले में गोदावरी नदी के तट पर स्थित था । पुराणों में सातवाहनों को आंध्र भी कहा गया है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Harmika is one of the features of _____ in Buddhist architecture.</p>",
                    question_hi: "<p>9. \'हरमिका\', बौद्ध वास्तुकला में _____ की विशेषताओं में से एक है।</p>",
                    options_en: ["<p>chaityas</p>", "<p>pillars</p>", 
                                "<p>viharas</p>", "<p>stupas</p>"],
                    options_hi: ["<p>चैत्यों</p>", "<p>स्तंभों</p>",
                                "<p>विहारों</p>", "<p>स्तूपो</p>"],
                    solution_en: "<p>9.(d) <strong>stupas.</strong> Anda, Chatra, Yashti, Medhi, Pradakshinapath and Torana are some other features of Buddhist Stupas. Different kinds of Stupas : &lsquo;Sharirik&rsquo; (enclosed the mortal remains of various Buddhist figures), &lsquo;Paribhogika&rsquo; ( enclosed the various objects and utensils used by the various Buddhist figures during their lifetime), &lsquo;Uddeshika&rsquo; (mainly used for pooja and aradhana).</p>",
                    solution_hi: "<p>9.(d) <strong>स्तूप</strong> I अंडा, छत्र, यष्टि, मेधी, ​​प्रदक्षिणापथ और तोरण बौद्ध स्तूपों की कुछ अन्य विशेषताएं हैं। विभिन्न प्रकार के स्तूप: \'शारीरिक\' (विभिन्न बौद्ध हस्तियों के नश्वर अवशेष शामिल थे), \'परिभोगिका\' (विभिन्न बौद्ध हस्तियों द्वारा उनके जीवनकाल के दौरान उपयोग की गई विभिन्न वस्तुएं और बर्तन शामिल थे), \'उद्देशिका\' (मुख्य रूप से पूजा और आराधना के लिए उपयोग किया जाता था)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. The federal scheme, Judiciary and Governors system of the Constitution of India are drawn from the _____.</p>",
                    question_hi: "<p>10. भारत के संविधान की संघीय योजना, न्यायपालिका और राज्यपाल प्रणाली ______ से ली गई है।</p>",
                    options_en: ["<p>Government of India Act, 1919</p>", "<p>Government of India Act, 1935</p>", 
                                "<p>Government of India Act of 1942</p>", "<p>Government of India Act of 1945</p>"],
                    options_hi: ["<p>भारत सरकार अधिनियम, 1919</p>", "<p>भारत सरकार अधिनियम, 1935</p>",
                                "<p>1942 का भारत सरकार अधिनियम</p>", "<p>1945 का भारत सरकार अधिनियम</p>"],
                    solution_en: "<p>10.(b) <strong>Government of India Act, 1935. </strong>The Act ended the Diarchy and provided for the establishment of the All India Federation. This was the longest Act passed under the British Act of Parliament. The Act introduced provincial autonomy (the powers between the center and provinces were divided in terms of three lists &ndash; Federal list, Provincial list and Concurrent list).</p>",
                    solution_hi: "<p>10.(b) <strong>भारत सरकार अधिनियम, 1935।</strong> इस अधिनियम ने द्वैध शासन को समाप्त कर दिया और अखिल भारतीय संघ की स्थापना का प्रावधान किया। यह अगस्त 1935 में संसद के ब्रिटिश अधिनियम के तहत पारित सबसे लंबा अधिनियम था। इस अधिनियम ने प्रांतीय स्वायत्तता की शुरुआत की (केंद्र और प्रांतों के बीच शक्तियों को तीन सूचियों - संघीय सूची, प्रांतीय सूची और समवर्ती सूची के अनुसार विभाजित किया गया था)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. In which Olympics did Abhinav Bindra win the gold medal in Men&rsquo;s 10 m air rifle shooting?</p>",
                    question_hi: "<p>11. अभिनव बिंद्रा ने किस ओलंपिक में पुरुषों की 10 m एयर राइफल निशानेबाजी में स्वर्ण पदक जीता था?</p>",
                    options_en: ["<p>London 2012</p>", "<p>Athens 2004</p>", 
                                "<p>Beijing 2008</p>", "<p>Rio 2016</p>"],
                    options_hi: ["<p>लंदन 2012</p>", "<p>एथेंस 2004</p>",
                                "<p>बीजिंग 2008</p>", "<p>रियो 2016</p>"],
                    solution_en: "<p>11.(c) <strong>Beijing 2008.</strong> Abhinav Bindra won gold medal in the 10m air rifle pairs and silver in the singles at the 2002 Commonwealth Games. He won a gold medal in the 2014 Commonwealth Games. He is India\'s first individual gold medallist at the Olympics. His autobiography - &lsquo;A Shot at History : My Obsessive Journey to Olympic Gold&rsquo;.</p>",
                    solution_hi: "<p>11.(c) <strong>बीजिंग 2008।</strong> अभिनव बिंद्रा ने 2002 राष्ट्रमंडल खेलों में 10 मीटर एयर राइफल जोड़ी में स्वर्ण पदक और एकल में रजत पदक जीता। उन्होंने 2014 राष्ट्रमंडल खेलों में स्वर्ण पदक जीता। वह ओलंपिक में, भारत के पहले व्यक्तिगत स्वर्ण पदक विजेता हैं। उनकी आत्मकथा - \'ए शॉट एट हिस्ट्री: माई ऑब्सेसिव जर्नी टू ओलंपिक गोल्ड\'।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Who among the following gave the famous words, &lsquo;One Caste, One Religion, One God for all men&rsquo;?</p>",
                    question_hi: "<p>12. निम्नलिखित में से किसने प्रसिद्ध शब्द \'वन कास्ट, वन रिलिजन, वन गॉड फॉर ऑल मेन (सभी लोगों के लिए एक जाति, एक धर्म, एक भगवान)\' कहे थे?</p>",
                    options_en: ["<p>Sri Narayana Guru</p>", "<p>Jyotirao Phule</p>", 
                                "<p>Swami Vivekananda</p>", "<p>Raja Ram Mohan Roy</p>"],
                    options_hi: ["<p>श्री नारायण गुरु</p>", "<p>ज्योतिराव फुले</p>",
                                "<p>स्वामी विवेकानंद</p>", "<p>राजा राम मोहन राय</p>"],
                    solution_en: "<p>12.(a) <strong>Sri Narayana Guru</strong>. His philosophy formed the foundation of his social revolution against the caste system. He was a prominent Indian social reformer and was born in village Chempazhanti in Kerala. He wrote the books \'Advaita Deepika&rsquo;, &lsquo;Asrama&rsquo;, &lsquo;Thevarappathinkangal&rsquo;. Some of his other famous quotes - &ldquo;Ask not, think not, and say not caste&rdquo;, &ldquo;Gain freedom through education&rdquo;, &ldquo;Whatever be the religion, it is enough if man becomes virtuous\'.</p>",
                    solution_hi: "<p>12.(a) <strong>श्री नारायण गुरु।</strong> उनके दर्शन ने जाति व्यवस्था के खिलाफ उनकी सामाजिक क्रांति की नींव रखी। वह एक प्रमुख भारतीय समाज सुधारक थे और उनका जन्म केरल के गाँव चेम्पाझंती में हुआ था। उन्होंने \'अद्वैत दीपिका\', \'आश्रम\', \'थेवरप्पथिनंगल\' किताबें लिखीं। उनके कुछ अन्य प्रसिद्ध कोट्स - \"पूछो मत, सोचो मत, और जाति मत कहो\", \"शिक्षा के माध्यम से स्वतंत्रता प्राप्त करें\", \"कोई भी धर्म हो, मनुष्य सदाचारी बन जाए तो बहुत है\"।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. By which Constitutional Amendment Act can the same person be appointed as the Governor of two or more states?</p>",
                    question_hi: "<p>13. निम्नलिखित में से किस संवैधानिक संशोधन अधिनियम के द्वारा एक ही व्यक्ति को दो या अधिक राज्यों का राज्यपाल नियुक्त किया जा सकता है?</p>",
                    options_en: ["<p>9<sup>th</sup> Constitutional Amendment Act</p>", "<p>11<sup>th</sup> Constitutional Amendment Act</p>", 
                                "<p>7<sup>th</sup> Constitutional Amendment Act</p>", "<p>18<sup>th</sup> Constitutional Amendment Act</p>"],
                    options_hi: ["<p>9वाँ संवैधानिक संशोधन अधिनियम</p>", "<p>11वाँ संवैधानिक संशोधन अधिनियम</p>",
                                "<p>7वाँ संवैधानिक संशोधन अधिनियम</p>", "<p>18वाँ संवैधानिक संशोधन अधिनियम</p>"],
                    solution_en: "<p>13.(c) <strong>7th Constitutional Amendment Act.</strong> Under Article 155 of the Indian Constitution, the Governor of a State shall be appointed by the President and hold office during the pleasure of the President. A person should have completed 35 years of age to be appointed as Governor. He is entitled to receive the emoluments, allowances, and privileges as may determined by Parliament. There is no provision for impeaching the Governor by the State Legislature.</p>",
                    solution_hi: "<p>13.(c) <strong>7 वां संविधान संशोधन अधिनियम। </strong>भारतीय संविधान के अनुच्छेद 155 के तहत, किसी राज्य के राज्यपाल की नियुक्ति राष्ट्रपति द्वारा की जाएगी और वह राष्ट्रपति प्रसादपर्यन्त पर बने रहेंगे। राज्यपाल के रूप में नियुक्त होने के लिए व्यक्ति की आयु 35 वर्ष होनी चाहिए। वह संसद द्वारा निर्धारित परिलब्धियाँ, भत्ते और विशेषाधिकार प्राप्त करने का हकदार है। राज्य विधानमंडल द्वारा राज्यपाल पर महाभियोग चलाने का कोई प्रावधान नहीं है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14.Dark patches on the sun are referred to as:</p>",
                    question_hi: "<p>14. सूर्य पर काले धब्बे, _____ कहलाते हैं।</p>",
                    options_en: ["<p>sun spots</p>", "<p>dark spots</p>", 
                                "<p>moon spots</p>", "<p>earth spots</p>"],
                    options_hi: ["<p>सौर कलंक</p>", "<p>काले धब्बे</p>",
                                "<p>चाँद के धब्बे</p>", "<p>पृथ्वी के धब्बे</p>"],
                    solution_en: "<p>14.(a) <strong>Sun spots.</strong> They appear dark because they are cooler than other parts of the Sun\'s surface. It is because they form in areas where magnetic fields are particularly strong. These magnetic fields are so strong that they keep some of the heat within the Sun from reaching the surface. The temperature of a sunspot is still very hot though around 6500 <math display=\"inline\"><mi>&#8457;</mi></math>. The hottest part of the Sun is its core, where temperatures top 27 million ℉. The glowing surface of the sun is known as the photosphere.</p>",
                    solution_hi: "<p>14.(a) <strong>सौर कलंक I</strong> ये काले दिखाई देते हैं क्योंकि वे सूर्य की सतह के अन्य भागों की तुलना में ठंडे हैं। ऐसा इसलिए है क्योंकि ये उन क्षेत्रों में बनते हैं जहां चुंबकीय क्षेत्र विशेष रूप से मजबूत होते हैं। ये चुंबकीय क्षेत्र इतने मजबूत हैं कि ये सूर्य के भीतर की कुछ गर्मी को सतह तक पहुंचने से रोकते हैं। एक सन स्पॉट का तापमान 6500℉ के आसपास होते हुए भी अभी भी बहुत गर्म है। सूर्य का सबसे गर्म हिस्सा इसका कोर है, जहां तापमान 27 मिलियन℉ से ऊपर है। सूर्य की चमकती सतह को प्रकाशमंडल के नाम से जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Which of the following materials were pillars in Stupas built during Ashoka&rsquo;s reign?</p>",
                    question_hi: "<p>15. अशोक के शासन काल में निर्मित स्तूपों में निम्नलिखित में से किस सामग्री के स्तंभ थे?</p>",
                    options_en: ["<p>Granite</p>", "<p>Stone</p>", 
                                "<p>Marble</p>", "<p>Bricks</p>"],
                    options_hi: ["<p>ग्रेनाइट</p>", "<p>पत्थर</p>",
                                "<p>संगमरमर</p>", "<p>ईंट</p>"],
                    solution_en: "<p>15.(b) <strong>Stone.</strong> The physical appearance of the Ashokan pillars underscores Buddhist doctrine. Most of the pillars were topped by sculptures of animals. Each pillar is also topped by an inverted lotus flower, which is the most pervasive symbol of Buddhism. The pillars vary from 40 to 50 feet in height. They are cut from two different types of stone - one for the shaft and another for the capital. The pillars weigh about 50 tons each. Only 19 of the original pillars survive and many are in fragments.</p>",
                    solution_hi: "<p>15.(b) <strong>पत्थर</strong> I अशोक के स्तंभों की भौतिक उपस्थिति बौद्ध सिद्धांत को रेखांकित करती है। अधिकांश स्तंभों के शीर्ष पर जानवरों की मूर्तियां थीं। प्रत्येक स्तंभ के शीर्ष पर एक उलटा कमल का फूल भी है, जो बौद्ध धर्म का सबसे व्यापक प्रतीक है। स्तंभों की ऊंचाई 40 से 50 फीट तक है। इन्हें दो अलग-अलग प्रकार के पत्थरों से काटा जाता है - एक शाफ्ट के लिए और दूसरा कैपिटल के लिए। प्रत्येक खंभे का वजन लगभग 50 टन है। मूल स्तंभों में से केवल 19 ही बचे हैं और कई खंडित हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Which of the following statements is correct regarding GDP?<br>I. GDP is used to measure human development.<br>II. Externalities are not taken into account while estimating GDP.<br>III. The services provided by housewives are included while estimating GDP.</p>",
                    question_hi: "<p>16. जीडीपी (GDP) के संबंध में निम्नलिखित में से कौन सा कथन सही है? <br>I. जीडीपी का उपयोग मानव विकास को मापने के लिए किया जाता है। <br>II. जीडीपी का प्राक्कलन करते समय बाह्यताओं को नहीं लिया जाता है। <br>III. जीडीपी का प्राक्कलन करते समय गृहिणियों द्वारा प्रदान की जाने वाली सेवाओं को शामिल किया जाता है।</p>",
                    options_en: ["<p>Only III</p>", "<p>Only II</p>", 
                                "<p>Only I and II</p>", "<p>Only I</p>"],
                    options_hi: ["<p>केवल III</p>", "<p>केवल II</p>",
                                "<p>केवल I और II</p>", "<p>केवल I</p>"],
                    solution_en: "<p>16.(b) <strong>Only II.</strong> GDP (Gross Domestic Product ) provides a measure of the size of the economy, which reflects the total economic activity in a country. It gives information about the size of the economy and how an economy is performing. It does not look at the services that happen to go unpaid (unpaid internships, housekeeping, etc). Products that have been resold (like old homes) are not measured as part of GDP.</p>",
                    solution_hi: "<p>16.(b) <strong>केवल II.</strong> GDP ( सकल घरेलू उत्पाद) अर्थव्यवस्था के आकार का एक माप प्रदान करता है, जो किसी देश में कुल आर्थिक गतिविधि को दर्शाता है। यह अर्थव्यवस्था के आकार और एक अर्थव्यवस्था कैसा प्रदर्शन कर रही है, इसके बारे में जानकारी देता है। यह उन सेवाओं पर ध्यान नहीं देता है जो अवैतनिक होती हैं (अवैतनिक इंटर्नशिप, हाउसकीपिंग, आदि)। जिन उत्पादों को दोबारा बेचा गया है (जैसे पुराने घर) उन्हें GDP के हिस्से के रूप में नहीं मापा जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Who among the following was the son of Ustad Allauddin Khan?</p>",
                    question_hi: "<p>17. उस्ताद अलाउद्दीन खान का पुत्र निम्नलिखित में से कौन था?</p>",
                    options_en: ["<p>Aamir Khan</p>", "<p>Ali Akbar Khan</p>", 
                                "<p>Amjad Ali Khan</p>", "<p>Jakir Husain</p>"],
                    options_hi: ["<p>आमिर खान</p>", "<p>अली अकबर खान</p>",
                                "<p>अमजद अली खान</p>", "<p>जाकिर हुसैन</p>"],
                    solution_en: "<p>17.(b) <strong>Ali Akbar Khan.</strong> He became the court musician for the Maharaja of Jodhpur and was eventually conveyed the title \"Ustad\". His Awards: Padma Vibhushan (1989), Padma Bhushan (1967). Ustad Allauddin Khan was popularly known as Baba Allauddin Khan was a Bengali sarod player (Maihar Gharana). His awards: Sangeet Natak Akademi award (1954), Padma Bhushan (1958), Padma Vibhushan(1971).</p>",
                    solution_hi: "<p>17.(b) <strong>अली अकबर खान I</strong> वह जोधपुर के महाराजा के दरबारी संगीतकार बने और अंततः उन्हें \"उस्ताद\" की उपाधि दी गई। उनके पुरस्कार: पद्म विभूषण (1989), पद्म भूषण (1967)। उस्ताद अलाउद्दीन खान, जिन्हें बाबा अलाउद्दीन खान के नाम से जाना जाता था, एक बंगाली सरोद वादक (मैहर घराना) थे। उनके पुरस्कार: संगीत नाटक अकादमी पुरस्कार (1954), पद्म भूषण (1958), पद्म विभूषण (1971)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. In which Five-Year Plan was the family planning programme undertaken?</p>",
                    question_hi: "<p>18. परिवार-नियोजन कार्यक्रम किस पंचवर्षीय योजना में प्रारंभ किया गया था ?</p>",
                    options_en: ["<p>4th Five-Year Plan</p>", "<p>5th Five-Year Plan</p>", 
                                "<p>7th Five-Year Plan</p>", "<p>2nd Five-Year Plan</p>"],
                    options_hi: ["<p>चौथी पंचवर्षीय योजना</p>", "<p>पाँचवीं पंचवर्षीय योजना</p>",
                                "<p>सातवीं पंचवर्षीय योजना</p>", "<p>दूसरी पंचवर्षीय योजना</p>"],
                    solution_en: "<p>18.(a) <strong>4th Five-Year Plan</strong> (1969&ndash;1974). Its main objectives of the fourth five-year plan were to achieve self-reliance. Implementation of Family Planning Programmes were amongst major targets of the Plan. It was a plan based on the Gadgil Formula. The government nationalized 14 major Indian Banks and the Green Revolution boosted agriculture.</p>",
                    solution_hi: "<p>18.(a) <strong>चौथी पंचवर्षीय योजना</strong> (1969-1974)। योजना का मुख्य उद्देश्य आत्मनिर्भरता प्राप्त करना था। परिवार नियोजन कार्यक्रमों का कार्यान्वयन योजना के प्रमुख लक्ष्यों में से एक था। यह गाडगिल फॉर्मूला पर आधारित योजना थी। सरकार ने 14 प्रमुख भारतीय बैंकों का राष्ट्रीयकरण किया और हरित क्रांति ने कृषि को बढ़ावा दिया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Which of the following is NOT an Indian multinational company?</p>",
                    question_hi: "<p>19. निम्नलिखित में से कौन भारतीय बहुराष्ट्रीय कंपनी नहीं है?</p>",
                    options_en: ["<p>Arvind Mills Limited</p>", "<p>General Motors Company</p>", 
                                "<p>Tata Consultancy Services Limited</p>", "<p>Asian Paints Limited</p>"],
                    options_hi: ["<p>अरविंद मिल्स लिमिटेड</p>", "<p>जनरल मोटर्स कंपनी</p>",
                                "<p>टाटा कंसल्टेंसी सर्विसेज लिमिटेड</p>", "<p>एशियन पेंट्स लिमिटेड</p>"],
                    solution_en: "<p>19.(b) <strong>General Motors Company,</strong> now General Motors (GM), is an American multinational automotive manufacturing company headquartered in Detroit, Michigan, United States. Some top Indian multinational companies - Aditya Birla Group, Infosys, HCL Technologies, Wipro, Dabur, Bharat Forge, Hero MotoCorp, Reliance Industries, Mahindra Group, Essar Group, etc.</p>",
                    solution_hi: "<p>19.(b) <strong>जनरल मोटर्स कंपनी,</strong> अब जनरल मोटर्स (GM), एक अमेरिकी बहुराष्ट्रीय ऑटोमोटिव विनिर्माण कंपनी है, इसका मुख्यालय डेट्रॉइट, मिशिगन, संयुक्त राज्य अमेरिका में है। कुछ शीर्ष भारतीय बहुराष्ट्रीय कंपनियाँ - आदित्य बिड़ला समूह, इंफोसिस, HCL टेक्नोलॉजीज, विप्रो, डाबर, भारत फोर्ज, हीरो मोटोकॉर्प, रिलायंस इंडस्ट्रीज, महिंद्रा समूह, एस्सार समूह, आदि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. The HPCL Rajasthan Refinery Ltd (HRRL) is being constructed in which district (as of December 2022)?</p>",
                    question_hi: "<p>20. एच.पी.सी.एल. राजस्थान रिफाइनरी लिमिटेड (एच.आर.आर.एल.) का निर्माण किस जिले में किया जा रहा है (दिसंबर 2022 तक)?</p>",
                    options_en: ["<p>Barmer</p>", "<p>Jalore</p>", 
                                "<p>Jaisalmer</p>", "<p>Bikaner</p>"],
                    options_hi: ["<p>बाड़मेर</p>", "<p>जालौर</p>",
                                "<p>जैसलमेर</p>", "<p>बीकानेर</p>"],
                    solution_en: "<p>20.(a) <strong>Barmer.</strong> HRRL is a Joint Venture between Hindustan Petroleum Corporation Limited (HPCL) and Government of Rajasthan (GOR). HRRL is setting up a Greenfield 9 MMTPA refinery cum petrochemical complex at Pachpadra in Barmer district of Rajasthan. Some leading industries of Rajasthan are Agriculture, Minerals, and Cement.</p>",
                    solution_hi: "<p>20.(a) <strong>बाड़मेर</strong> I HRRL हिंदुस्तान पेट्रोलियम कॉर्पोरेशन लिमिटेड (HPCL) और राजस्थान सरकार (GOR) के बीच एक संयुक्त उद्यम है। HRRL राजस्थान के बाड़मेर जिले के पचपदरा में एक ग्रीनफील्ड 9 MMTPA रिफाइनरी सह पेट्रोकेमिकल कॉम्प्लेक्स स्थापित कर रहा है। राजस्थान के कुछ प्रमुख उद्योग कृषि, खनिज और सीमेंट हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. A follower of Gokhale, Narayan Malhar Joshi founded the Social Service League in Bombay, in which of the following years?</p>",
                    question_hi: "<p>21. गोखले के अनुयायी, नारायण मल्हार जोशी ने निम्नलिखित में से किस वर्ष में बंबई में सोशल सर्विस लीग की स्थापना की?</p>",
                    options_en: ["<p>1914</p>", "<p>1913</p>", 
                                "<p>1911</p>", "<p>1912</p>"],
                    options_hi: ["<p>1914</p>", "<p>1913</p>",
                                "<p>1911</p>", "<p>1912</p>"],
                    solution_en: "<p>21.(c) <strong>1911.</strong> The Social Service League founded by Narayan Malhar Joshi. He was the co - founder of All India Trade Union Congress (AITUC established In 1920), along with Lala Lajpat Rai. He was the General Secretary of AITUC from 1925 to 1929. In 1931, NM Joshi left AITUC and formed All India Trade Union Federation (AITUF).</p>",
                    solution_hi: "<p>21.(c) <strong>1911</strong> I नारायण मल्हार जोशी द्वारा स्थापित सोशल सर्विस लीग। वह लाला लाजपत राय के साथ ऑल इंडिया ट्रेड यूनियन कांग्रेस (AITUC की स्थापना 1920 में हुई) के सह-संस्थापक थे। वह 1925 से 1929 तक AITUC के महासचिव रहे। 1931 में, एनएम जोशी ने AITUC छोड़ दिया और ऑल इंडिया ट्रेड यूनियन फेडरेशन (AITUF) का गठन किया।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. &lsquo;Unbreakable&rsquo; is an autobiography of an Indian Boxer ____ and co-authored by Dina Serto.</p>",
                    question_hi: "<p>22. \'अनब्रेकेबल\' एक भारतीय मुक्केबाज ______ की आत्मकथा है और दीना सर्टो इसकी सह लेखिका हैं।</p>",
                    options_en: ["<p>Mary Kom</p>", "<p>Laishram Sarita Devi</p>", 
                                "<p>Lovlina Borgohain</p>", "<p>Vijender Singh</p>"],
                    options_hi: ["<p>मैरी कॉम</p>", "<p>लैशराम सरिता देवी</p>",
                                "<p>लवलीना बोर्गोहेन</p>", "<p>विजेंद्र सिंह</p>"],
                    solution_en: "<p>22.(a) <strong>Mary Kom.</strong> Her autobiography tells the story of her incredible struggle, sacrifice and success. Some Autobiographies &amp; their Authors : &ldquo;The Race of My Life&rdquo; (Milkha Singh), &ldquo;A Shot at History&rdquo; (Abhinav Bindra), &ldquo;Playing It My Way&rdquo; (Sachin Tendulkar), &ldquo;The Test of My Life&rdquo; (Yuvraj Singh), &ldquo;Ace Against Odds&rdquo; (Sania Mirza), &ldquo;Playing to Win&rdquo; (Saina Nehwal), &ldquo;Born Again on the Mountain&rdquo; (Arunima Sinha), &ldquo;Mind Master&rdquo; (Vishwanathan Anand).</p>",
                    solution_hi: "<p>22.(a) <strong>मैरी कॉम।</strong> उनकी आत्मकथा उनके अविश्वसनीय संघर्ष, बलिदान और सफलता की कहानी बताती है। कुछ आत्मकथाएँ और उनके लेखक: \"द रेस ऑफ माई लाइफ\" (मिल्खा सिंह), \"ए शॉट एट हिस्ट्री\" (अभिनव बिंद्रा), \"प्लेइंग इट माई वे\" (सचिन तेंदुलकर), \"द टेस्ट ऑफ माई लाइफ\" (युवराज सिंह) \"ऐस अगेंस्ट ऑड्स\" (सानिया मिर्जा), \"प्लेइंग टू विन\" (साइना नेहवाल), \"बॉर्न अगेन ऑन द माउंटेन\" (अरुणिमा सिन्हा), \"माइंड मास्टर\" (विश्वनाथन आनंद)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Conservation of biological diversity and sustainable use of its components are the main objectives of which of the following treaties/protocols?</p>",
                    question_hi: "<p>23. निम्नलिखित में से किस संधि/प्रोटोकॉल का मुख्य उद्देश्य जैविक विविधता का संरक्षण और इसके घटकों का सतत उपयोग है?</p>",
                    options_en: ["<p>Nagoya protocol</p>", "<p>Rio protocol</p>", 
                                "<p>Geneva protocol</p>", "<p>Montreal protocol</p>"],
                    options_hi: ["<p>नागोया प्रोटोकॉल</p>", "<p>रियो प्रोटोकॉल</p>",
                                "<p>जिनेवा प्रोटोकॉल</p>", "<p>मॉन्ट्रियल प्रोटोकॉल</p>"],
                    solution_en: "<p>23.(a) <strong>Nagoya protocol.</strong> This Protocol was adopted on 29 October 2010 in Nagoya (Japan) and entered into force on 12 October 2014. Rio Protocol (1992 Rio Summit or the Earth Summit) - It was held in Rio de Janeiro, Brazil. Montreal Protocol - It was finalized in 1987, is a global agreement to protect the stratospheric ozone layer by phasing out the production and consumption of ozone-depleting substances (ODS).</p>",
                    solution_hi: "<p>23.(a) <strong>नागोया प्रोटोकॉल।</strong> यह प्रोटोकॉल 29 अक्टूबर 2010 को नागोया (जापान) में अपनाया गया और 12 अक्टूबर 2014 को लागू हुआ। रियो प्रोटोकॉल (1992 रियो शिखर सम्मेलन या पृथ्वी शिखर सम्मेलन) - यह ब्राजील के रियो डी जनेरियो में आयोजित किया गया था। मॉन्ट्रियल प्रोटोकॉल - इसे 1987 में अंतिम रूप दिया गया था, यह ओजोन-क्षयकारी पदार्थों (ODS) के उत्पादन और खपत को चरणबद्ध करके समतापमंडलीय ओजोन परत की रक्षा करने के लिए एक वैश्विक समझौता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. The Hindustan Republican Association was reorganized into Hindustan Socialist Republican Association (HSRA) at Feroz Shah Kotla Ground in Delhi on 8th and 9th September _____.</p>",
                    question_hi: "<p>24. 8 और 9 सितंबर _____ को दिल्ली के फ़िरोज़ शाह कोटला मैदान में हिंदुस्तान रिपब्लिकन एसोसिएशन को हिंदुस्तान सोशलिस्ट रिपब्लिकन एसोसिएशन (HSRA) में पुनर्गठित किया गया था।</p>",
                    options_en: ["<p>1927</p>", "<p>1930</p>", 
                                "<p>1929</p>", "<p>1928</p>"],
                    options_hi: ["<p>1927</p>", "<p>1930</p>",
                                "<p>1929</p>", "<p>1928</p>"],
                    solution_en: "<p>24.(d) <strong>1928.</strong> Under Chandrashekhar Azad&rsquo;s leadership, all young revolutionaries, Bejoy Kumar Sinha, Shiv Varma, Jaidev Kapur, Bhagat Singh, Bhagwati Charan Vohra, and Sukhdev met in Feroz Shah Kotla ground in Delhi and reorganized Hindustan Republican Association (HRA) into Hindustan Socialist Republican Association (HSRA). The HRA was formed with the efforts of Ram Prasad Bismil in Allahabad in 1923, under the guidance of Lala Har Dayal, a prominent nationalist leader.</p>",
                    solution_hi: "<p>24.(d) <strong>1928</strong> । चन्द्रशेखर आज़ाद के नेतृत्व में, सभी युवा क्रांतिकारी, बिजय कुमार सिन्हा, शिव वर्मा, जयदेव कपूर, भगत सिंह, भगवती चरण वोहरा और सुखदेव दिल्ली के फ़िरोज़ शाह कोटला मैदान में मिले और हिंदुस्तान रिपब्लिकन एसोसिएशन (HRA) को हिंदुस्तान सोशलिस्ट रिपब्लिकन एसोसिएशन (HSRA) में पुनर्गठित किया I HRA का गठन 1923 में प्रमुख राष्ट्रवादी नेता लाला हर दयाल के मार्गदर्शन में राम प्रसाद बिस्मिल के प्रयासों से इलाहाबाद में किया गया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Gopika Verma, the famous _____ dancer received various awards, including the Sangeet Natak Akademi Award in 2018.</p>",
                    question_hi: "<p>25. गोपिका वर्मा, जो कि प्रसिद्ध _____ नृत्यांगना हैं, को 2018 में संगीत नाटक अकादमी पुरस्कार सहित विभिन्न पुरस्कार प्राप्त हुए।</p>",
                    options_en: ["<p>Mohiniattam</p>", "<p>Odissi</p>", 
                                "<p>Bharatanatyam</p>", "<p>Sattriya</p>"],
                    options_hi: ["<p>मोहिनीअट्टम</p>", "<p>ओडिसी</p>",
                                "<p>भरतनाट्यम</p>", "<p>सत्रिया</p>"],
                    solution_en: "<p>25.(a) <strong>Mohiniattam</strong> (classical dance of Kerala). It is performed by women in honor of the Hindu god Vishnu in his incarnation as the enchantress Mohini. It was patronized by Travancore ruler Maharaja Swathi Thirunal. Vallathol Narayana Menon is the poet who revived Mohiniattam. The famous exponents of Mohiniattam are - Kalamandalam Kalyanikutty Amma, Sunanda Nair, Pallavi Krishnan, etc.</p>",
                    solution_hi: "<p>25.(a) <strong>मोहिनीअट्टम</strong> (केरल का शास्त्रीय नृत्य)। यह महिलाओं द्वारा हिंदू भगवान विष्णु के जादुई मोहिनी अवतार के सम्मान में किया जाता है। इसे त्रावणकोर के शासक महाराजा स्वाति थिरुनल द्वारा संरक्षण दिया गया था। वल्लाथोल नारायण मेनन वह कवि हैं जिन्होंने मोहिनीअट्टम को पुनर्जीवित किया। मोहिनीअट्टम के प्रसिद्ध प्रतिपादक हैं - कलामंडलम कल्याणिकुट्टी अम्मा, सुनंदा नायर, पल्लवी कृष्णन आदि।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following is the first national park that was established in India?</p>",
                    question_hi: "<p>26. निम्नलिखित में से कौन-सा भारत में स्थापित प्रथम राष्ट्रीय उद्यान है?</p>",
                    options_en: ["<p>Gir National Park</p>", "<p>Kaziranga National Park</p>", 
                                "<p>Jim Corbett National Park</p>", "<p>Rajaji National Park</p>"],
                    options_hi: ["<p>गिर राष्ट्रीय उद्यान</p>", "<p>काजीरंगा राष्ट्रीय उद्यान</p>",
                                "<p>जिम कॉर्बेट राष्ट्रीय उद्यान</p>", "<p>राजाजी राष्ट्रीय उद्यान</p>"],
                    solution_en: "<p>26.(c) <strong>Jim Corbett National Park.</strong> It was established in 1936 as Hailey National Park to protect the endangered Bengal tigers. It is located in Nainital district of Uttarakhand. The Hemis National Park (in Ladakh) is the largest national park in India. South Button Island National Park (in the Andaman and Nicobar Islands) is the smallest National Park in India.</p>",
                    solution_hi: "<p>26.(c) <strong>जिम कॉर्बेट नेशनल पार्क।</strong> इसकी स्थापना 1936 में लुप्तप्राय बंगाल बाघों की रक्षा के लिए हैली नेशनल पार्क के रूप में की गई थी। यह उत्तराखंड के नैनीताल जिले में स्थित है। हेमिस राष्ट्रीय उद्यान (लद्दाख में) भारत का सबसे बड़ा राष्ट्रीय उद्यान है। साउथ बटन आइलैंड राष्ट्रीय उद्यान (अंडमान और निकोबार द्वीप समूह में) भारत का सबसे छोटा राष्ट्रीय उद्यान है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. The government budget is prepared under which article of the Constitution of India?</p>",
                    question_hi: "<p>27. भारत के संविधान के किस अनुच्छेद के तहत सरकारी बजट तैयार किया जाता है?</p>",
                    options_en: ["<p>Article 109</p>", "<p>Article 101</p>", 
                                "<p>Article 100</p>", "<p>Article 112</p>"],
                    options_hi: ["<p>अनुच्छेद 109</p>", "<p>अनुच्छेद 101</p>",
                                "<p>अनुच्छेद 100</p>", "<p>अनुच्छेद 112</p>"],
                    solution_en: "<p>27.(d) <strong>Article 112.</strong> The budget is presented by the Finance Minister of India in the Parliament. The Indian Union Budget is prepared by the Ministry of Finance in consultation with Niti Aayog and other concerned ministries. The Constitution nowhere mentions the word \'budget\' and refers to it as Annual Financial Statement (AFS).</p>",
                    solution_hi: "<p>27.(d) <strong>अनुच्छेद 112 । </strong>बजट भारत के वित्त मंत्री द्वारा संसद में पेश किया जाता है। भारतीय केंद्रीय बजट वित्त मंत्रालय द्वारा नीति आयोग और अन्य संबंधित मंत्रालयों के परामर्श से तैयार किया जाता है। संविधान में कहीं भी \'बजट\' शब्द का उल्लेख नहीं है और इसे वार्षिक वित्तीय विवरण (AFS) के रूप में संदर्भित किया गया है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who among the following is the exponent of mandolin?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन मैंडोलिन से जुड़ा हुआ प्रसिद्ध व्यक्ति है?</p>",
                    options_en: ["<p>N Rajam</p>", "<p>N Ramani</p>", 
                                "<p>U Srinivas</p>", "<p>Annapurna Devi</p>"],
                    options_hi: ["<p>एन. राजम</p>", "<p>एन. रमानी</p>",
                                "<p>यू. श्रीनिवास</p>", "<p>अन्नपूर्णा देवी</p>"],
                    solution_en: "<p>28.(c) <strong>U Srinivas.</strong> He received Padma Shri award in 1998 and the Sangeet Natak Akademi Award in 2010. Some other exponents of Mandolin - S Balamurali Krishna, Nagen Dey, Khagen Dey. Mandolin is a musical instrument of the lute family that has a usually pear-shaped body and fretted neck and four to six pairs of strings. N Ramani (Flute artist). Annapurna Devi (Surbahar artist). N. Rajam (Violin artist).</p>",
                    solution_hi: "<p>28.(c) <strong>यू श्रीनिवास।</strong> उन्हें 1998 में पद्म श्री पुरस्कार और 2010 में संगीत नाटक अकादमी पुरस्कार मिला। मैंडोलिन के कुछ अन्य प्रतिपादक - एस बालामुरली कृष्णा, नागेन डे, खगेन डे। मैंडोलिन ल्यूट परिवार का एक संगीत वाद्ययंत्र है जिसमें आमतौर पर नाशपाती के आकार का शरीर और झल्लाहट वाली गर्दन और 4 से 6 जोड़े तार होते हैं। एन रमानी (बांसुरी कलाकार)। अन्नपूर्णा देवी (सुरबहार कलाकार) I एन राजम (वायलिन कलाकार)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. The terms Mahari and Gotipua are associated with which Indian classical dance form?</p>",
                    question_hi: "<p>29. महरी और गोटीपुआ शब्द किस भारतीय शास्त्रीय नृत्य शैली से संबंधित हैं?</p>",
                    options_en: ["<p>Kuchipudi</p>", "<p>Odissi</p>", 
                                "<p>Sattriya</p>", "<p>Manipuri</p>"],
                    options_hi: ["<p>कुचिपुड़ी</p>", "<p>ओडिसी</p>",
                                "<p>सत्रिया</p>", "<p>मणिपुरी</p>"],
                    solution_en: "<p>29.(b) <strong>Odissi.</strong> Mahari dance used to be performed at the temple of Lord Jagannatha at Puri by devadasi dancers called &lsquo;maharis&rsquo;. Odissi dance is one of the oldest forms of classical dance in India, traces its origins to Natya Shastra as early as 2nd Century BCE. Some Famous Folk Dances of Odisha - Paika, Chaitee Ghoda, Kela Keluni, Danda Nacha, etc.</p>",
                    solution_hi: "<p>29.(b) <strong>ओडिसी</strong> I महरी नृत्य, पुरी में भगवान जगन्नाथ के मंदिर में देवदासी नर्तकियों द्वारा किया जाता था जिन्हें \'महरि\' कहा जाता था। ओडिसी नृत्य भारत में शास्त्रीय नृत्य के सबसे पुराने रूपों में से एक है, इसकी उत्पत्ति ईसा पूर्व दूसरी शताब्दी में नाट्य शास्त्र से मानी जाती है। ओडिशा के कुछ प्रसिद्ध लोक नृत्य - पाइका, चैती घोड़ा, केला केलुनी, डंडा नाचा, आदि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Who among the following was the Chief Guest in the Republic Day Parade organized at Rajpath in the year 1955?</p>",
                    question_hi: "<p>30. वर्ष 1955 में राजपथ पर आयोजित गणतंत्र दिवस परेड में मुख्य अतिथि निम्नलिखित में से कौन थे?</p>",
                    options_en: ["<p>Abdel Fatah-al-Sisi</p>", "<p>General Malik Ghulam Muhammad</p>", 
                                "<p>Queen Elizabeth II</p>", "<p>Nelson Mandela</p>"],
                    options_hi: ["<p>अब्देल फतह-अल-सीसी (Abdel Fatah-al-Sisi)</p>", "<p>जनरल मलिक गुलाम मुहम्मद (General Malik Ghulam Muhammad)</p>",
                                "<p>महारानी एलिजाबेथ II (Queen Elizabeth II)</p>", "<p>नेल्सन मंडेला (Nelson Mandela)</p>"],
                    solution_en: "<p>30.(b) <strong>General Malik Ghulam Muhammad. </strong>Chief Guests during Republic Day Parade and Years : Emmanuel Macron (President of France) - 2024, Abdel Fattah el-Sisi (President of Egypt) - 2023. No chief guests were invited in the years 2021 and 2022 due to COVID-19 pandemic. Rajpath became the permanent venue for the parade of 26th January in 1955. Rajpath was known by the name &lsquo;Kingsway&rsquo; at that time, now known as Kartavya Path. The first parade was held on 26th January 1950, President of Indonesia Dr. Sukarno was invited as a guest.</p>",
                    solution_hi: "<p>30.(b) <strong>जनरल मलिक गुलाम मुहम्मद।</strong> गणतंत्र दिवस परेड के दौरान मुख्य अतिथि और वर्ष : इमैनुएल मैक्रॉन (फ्रांस के राष्ट्रपति) - 2024, अब्देल फतह अल-सिसी (मिस्र के राष्ट्रपति) - 2023। वर्ष 2021 और 2022 में COVID-19 महामारी के कारण किसी भी मुख्य अतिथि को आमंत्रित नहीं किया गया था। 1955 में राजपथ 26 जनवरी की परेड का स्थायी स्थल बन गया। राजपथ को उस समय \'किंग्सवे\' नाम से जाना जाता था, जो अब कर्तव्य पथ के नाम से जाना जाता है। पहली परेड 26 जनवरी 1950 को आयोजित की गई थी, इसमें इंडोनेशिया के राष्ट्रपति डॉ. सुकर्णो को अतिथि के रूप में आमंत्रित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. _____ is related to hockey</p>",
                    question_hi: "<p>31. ____ हॉकी से संबंधित है।</p>",
                    options_en: ["<p>Thomas Cup</p>", "<p>Ranji Trophy</p>", 
                                "<p>Aga Khan Cup</p>", "<p>Davis Cup</p>"],
                    options_hi: ["<p>थॉमस कप</p>", "<p>रणजी ट्रॉफी</p>",
                                "<p>आगा खान कप</p>", "<p>डेविस कप</p>"],
                    solution_en: "<p>31.(c) <strong>Aga Khan Cup. </strong>Other Trophies related to Hockey - Dhyan Chand Trophy, Gyanuati Devi Trophy, MCC Trophy, Maharaja Ranjit Singh Gold Cup, Rangaswami Cup, Gurmeet Trophy, Beighton Cup. Some Famous Indian Hockey Players - Dhyan Chand, Balbir Singh, Mohammad Shahid, Dhanraj Pillay, PR Sreejesh, Grahanandan Singh, Keshav Dutt, Prithipal Singh, Shankar Lakshman, Ranganathan Francis, Randhir Singh Gentle. Thomas Cup - Badminton. Ranji Trophy - Cricket. Davis Cup - Lawn Tennis.</p>",
                    solution_hi: "<p>31.(c) <strong>आगा खान कप।</strong> हॉकी से संबंधित अन्य ट्रॉफियां - ध्यानचंद ट्रॉफी, ज्ञानुती देवी ट्रॉफी, एमसीसी ट्रॉफी, महाराजा रणजीत सिंह गोल्ड कप, रंगास्वामी कप, गुरुमीत ट्रॉफी, बीटन कप। कुछ प्रसिद्ध भारतीय हॉकी खिलाड़ी - ध्यानचंद, बलबीर सिंह, मोहम्मद शाहिद, धनराज पिल्ले, पीआर श्रीजेश, ग्रहनंदन सिंह, केशव दत्त, पृथ्वीपाल सिंह, शंकर लक्ष्मण, रंगनाथन फ्रांसिस, रणधीर सिंह जेंटल। थॉमस कप - बैडमिंटन I रणजी ट्रॉफी - क्रिकेट। डेविस कप - लॉन टेनिस।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. The Make in India Scheme of the Government is administered under the _____ .</p>",
                    question_hi: "<p>32. सरकार की मेक इन इंडिया योजना, _____ द्वारा प्रशासित की जाती है।</p>",
                    options_en: ["<p>Ministry of External Affairs</p>", "<p>Ministry of Commerce &amp; Industry</p>", 
                                "<p>Ministry of Finance</p>", "<p>Ministry of Electronics &amp; Information Technology</p>"],
                    options_hi: ["<p>विदेश मंत्रालय</p>", "<p>वाणिज्य और उद्योग मंत्रालय</p>",
                                "<p>वित्त मत्रांलय</p>", "<p>इलेक्ट्रॉनिक्स और सूचना प्रौद्योगिकी मंत्रालय</p>"],
                    solution_en: "<p>32.(b) <strong>Ministry of Commerce &amp; Industry. </strong>&lsquo;Make in India&rsquo; initiative was launched globally in September 2014 as a part of India&rsquo;s renewed focus on Manufacturing. The objective of the Initiative is to promote India as the most preferred global manufacturing destination. It was launched by the Prime Minister of India, Narendra Modi.</p>",
                    solution_hi: "<p>32.(b) <strong>वाणिज्य और उद्योग मंत्रालय। </strong>विनिर्माण पर भारत के नए सिरे से ध्यान केंद्रित करने के एक भाग के रूप में सितंबर 2014 में वैश्विक स्तर पर \'मेक इन इंडिया\' पहल शुरू की गई थी। इस पहल का उद्देश्य भारत को सबसे पसंदीदा वैश्विक विनिर्माण गंतव्य के रूप में बढ़ावा देना है। इसे भारत के प्रधान मंत्री नरेंद्र मोदी द्वारा लॉन्च किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. As per Census of India 2011, identify the state in which the population density was recorded more than 100 people / sq.km.</p>",
                    question_hi: "<p>33. भारत की जनगणना 2011 के अनुसार, निम्न में से उस राज्य की पहचान कीजिए जिसमें जनसंख्या घनत्व 100 व्यक्ति/वर्ग किमी से अधिक दर्ज किया गया था।</p>",
                    options_en: ["<p>Arunachal Pradesh</p>", "<p>Mizoram</p>", 
                                "<p>Sikkim</p>", "<p>Rajasthan</p>"],
                    options_hi: ["<p>अरुणाचल प्रदेश</p>", "<p>मिजोरम</p>",
                                "<p>सिक्किम</p>", "<p>राजस्थान</p>"],
                    solution_en: "<p>33.(d) <strong>Rajasthan.</strong> As per Census 2011, the population density of India was 382 per sq km with a decadal growth of 17.72 percent. Bihar is the most thickly populated state (1106 persons/sq km) followed by West Bengal (1028 persons/sq km) and Kerala (860 persons/sq km). The state with the lowest population density is Arunachal Pradesh (17 persons/sq km). The Union Territories with the highest and the lowest population densities are The National Capital Territory of Delhi (11297 persons/sq km) and Andaman &amp; Nicobar Islands (46 persons/sq km) respectively.</p>",
                    solution_hi: "<p>33.(d) <strong>राजस्थान</strong> I 2011 की जनगणना के अनुसार, भारत का जनसंख्या घनत्व 17.72 प्रतिशत की दशकीय वृद्धि के साथ 382 प्रति वर्ग किमी था। बिहार सबसे घनी आबादी वाला राज्य (1106 व्यक्ति/वर्ग किमी) है, इसके बाद पश्चिम बंगाल (1028 व्यक्ति/वर्ग किमी) और केरल (860 व्यक्ति/वर्ग किमी) हैं। सबसे कम जनसंख्या घनत्व वाला राज्य अरुणाचल प्रदेश (17 व्यक्ति/वर्ग किमी) है। उच्चतम और सबसे कम जनसंख्या घनत्व वाले केंद्र शासित प्रदेश राष्ट्रीय राजधानी क्षेत्र दिल्ली (11297 व्यक्ति/वर्ग किमी) और अंडमान हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. What was the major approach of the Second Five-Year Plan towards industrialisation?</p>",
                    question_hi: "<p>34. औद्योगीकरण की दिशा में दूसरी पंचवर्षीय योजना का प्रमुख दृष्टिकोण क्या था?</p>",
                    options_en: ["<p>Focus on infrastructure so as to create employment opportunities in the urban areas</p>", "<p>Expansion of the public sector so as to create a base of heavy industry</p>", 
                                "<p>Focus on mass consumption goods so as to control inflation</p>", "<p>Expansion of the cottage industry so as to create employment opportunities in the rural areas</p>"],
                    options_hi: ["<p>शहरी क्षेत्रों में रोजगार के अवसरों के सृजन के लिए बुनियादी ढाँचे पर जोर</p>", "<p>भारी उद्योग का आधार बनाने के लिए सार्वजनिक क्षेत्र का विस्तार</p>",
                                "<p>महँगाई को नियंत्रित करने के लिए जन उपभोग की वस्तुओं पर जोर</p>", "<p>ग्रामीण क्षेत्रों में रोजगार के अवसर सृजित करने के लिए कुटीर उद्योग का विस्तार</p>"],
                    solution_en: "<p>34.(b) Second Five-Year Plan (1956-61) followed the &lsquo;Mahalanobis Model&rsquo;. This five-year plan revolved around the idea of developing the public sector and rapid industrialisation. The government imposed tariffs on imports to protect domestic industries under this plan. The target growth rate was 4.5% and the actual growth rate was slightly less than expected, 4.27% .</p>",
                    solution_hi: "<p>34.(b) दूसरी पंचवर्षीय योजना (1956-61) ने \'महालनोबिस मॉडल\' का पालन किया। यह पंचवर्षीय योजना सार्वजनिक क्षेत्र के विकास और तेजी से औद्योगीकरण के विचार के इर्द-गिर्द घूमती थी। सरकार ने इस योजना के तहत घरेलू उद्योगों की सुरक्षा के लिए आयात पर शुल्क लगाया। लक्ष्य वृद्धि दर 4.5% थी और वास्तविक विकास दर अपेक्षा से थोड़ा कम, 4.27% थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which of the following rivers is known as Dakshin Ganga?</p>",
                    question_hi: "<p>35. निम्नलिखित में से किस नदी को दक्षिण गंगा के नाम से जाना जाता है?</p>",
                    options_en: ["<p>Tapi</p>", "<p>Narmada</p>", 
                                "<p>Kaveri</p>", "<p>Godavari</p>"],
                    options_hi: ["<p>तापी</p>", "<p>नर्मदा</p>",
                                "<p>कावेरी</p>", "<p>गोदावरी</p>"],
                    solution_en: "<p>35.(d) <strong>Godavari</strong> is the largest peninsular river of India. It rises from Trimbakeshwar in the Nashik district of Maharashtra and drains into Bay of Bengal. Its length is 1465 km. It is also the second largest river in India after Ganga. Principal Tributaries - Pravara, Purna, Manjra, Penganga, Wardha, Wainganga, Pranhita (combined flow of Wainganga, Penganga, Wardha), Indravati, Maner and Sabri.</p>",
                    solution_hi: "<p>35.(d) <strong>गोदावरी</strong> भारत की सबसे बड़ी प्रायद्वीपीय नदी है। यह महाराष्ट्र के नासिक जिले में त्र्यंबकेश्वर से निकलती है और बंगाल की खाड़ी में गिरती है। इसकी लंबाई 1465 किमी है। यह गंगा के बाद भारत की दूसरी सबसे बड़ी नदी भी है। प्रमुख सहायक नदियाँ - प्रवरा, पूर्णा, मंजरा, पेनगंगा, वर्धा, वैनगंगा, प्राणहिता (वैनगंगा, पेंगंगा, वर्धा का संयुक्त प्रवाह), इंद्रावती, मनेर और साबरी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. In which state is the Kundang Lem Festival celebrated?</p>",
                    question_hi: "<p>36. कुंदांग लेम (Kundang Lem) उत्सव किस राज्य में मनाया जाता है?</p>",
                    options_en: ["<p>Madhya Pradesh</p>", "<p>Punjab</p>", 
                                "<p>Kerala</p>", "<p>Nagaland</p>"],
                    options_hi: ["<p>मध्य प्रदेश</p>", "<p>पंजाब</p>",
                                "<p>केरल</p>", "<p>नागालैंड</p>"],
                    solution_en: "<p>36.(d) <strong>Nagaland.</strong> Kundang Lem Festival monitored by the Haongang Clan. It is observed in the eighth month (April) of the Chang Calendar and is observed for five days only. Some Important Tribal Festivals of Nagaland - Mimkut, Bushu Jiba, Hega, Sekrenyi, Mony&uuml;, Aoleang, Moats&uuml;, Miu, Tuluni, Naknyul&uuml;m, Mongmong, Ngada.</p>",
                    solution_hi: "<p>36.(d) <strong>नागालैंड</strong> I कुंडांग लेम महोत्सव की निगरानी हाओंगांग कबीले द्वारा की जाती है। यह चांग कैलेंडर के आठवें महीने (अप्रैल) में मनाया जाता है और केवल पांच दिनों के लिए मनाया जाता है। नागालैंड के कुछ महत्वपूर्ण जनजातीय त्योहारों में शामिल हैं - मिमकुट, बुशु जिबा, हेगा, सेक्रेनी, मोन्यू, एओलंग, मोआत्सु, मिउ, तुलुनी, नाकन्युलुम, मोंगमोंग, नगाडा।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which state is the leading producer of Jowar as of 2022?</p>",
                    question_hi: "<p>37. 2022 तक की स्थिति के अनुसार, ज्वार का अग्रणी उत्पादक राज्य कौन सा था?</p>",
                    options_en: ["<p>Andhra Pradesh</p>", "<p>Uttar Pradesh</p>", 
                                "<p>Maharashtra</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>आंध्र प्रदेश</p>", "<p>उत्तर प्रदेश</p>",
                                "<p>महाराष्ट्र</p>", "<p>ओडिशा</p>"],
                    solution_en: "<p>37.(c) <strong>Maharashtra.</strong> Karnataka is the largest producer of &lsquo;Ragi&rsquo; in the country followed by Tamil Nadu, Uttarakhand. Rajasthan stands first as the largest producer of &lsquo;Bajra&rsquo; in India. Uttar Pradesh, Gujarat, Haryana, and Madhya Pradesh are India\'s other major Bajra-producing states. Jowar, Bajra, and Ragi are together known as Millets and are the Kharif crops.</p>",
                    solution_hi: "<p>37.(c) <strong>महाराष्ट्र</strong> I देश में कर्नाटक \'रागी\' का सबसे बड़ा उत्पादक है, इसके बाद तमिलनाडु, उत्तराखंड हैं। भारत में बाजरा के सबसे बड़े उत्पादक के रूप में राजस्थान पहले स्थान पर है। उत्तर प्रदेश, गुजरात, हरियाणा और मध्य प्रदेश भारत के अन्य प्रमुख बाजरा उत्पादक राज्य हैं। ज्वार, बाजरा और रागी को संयुक्त रूप से मिलेट के नाम से जाना जाता है और ये ख़रीफ़ की फसलें हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. In which of the following years was the Child Marriage Restraint Act passed in India?</p>",
                    question_hi: "<p>38. निम्नलिखित में से किस वर्ष में भारत में बाल विवाह निरोधक अधिनियम पारित किया गया था?</p>",
                    options_en: ["<p>1951</p>", "<p>1929</p>", 
                                "<p>1936</p>", "<p>1947</p>"],
                    options_hi: ["<p>1951</p>", "<p>1929</p>",
                                "<p>1936</p>", "<p>1947</p>"],
                    solution_en: "<p>38.(b) <strong>1929.</strong> Child Marriage Restraint Act was passed to eradicate the evil of Child Marriage. It came into force on 1st April 1930. The act fixed the marriageable age for girls at 16 years and 18 years for boys. It is popularly known as the Sharda Act after its sponsor, Harbilas Sarda which was later amended to 18 for girls and 21 for boys.</p>",
                    solution_hi: "<p>38.(b) <strong>1929</strong> । यह अधिनियम बाल विवाह की बुराई को मिटाने के लिए पारित किया गया था। यह 1 अप्रैल 1930 को लागू हुआ। इस अधिनियम ने लड़कियों के लिए विवाह योग्य आयु 16 वर्ष और लड़कों के लिए 18 वर्ष निर्धारित की। इसके प्रायोजक हरविलास शारदा के नाम पर इसे लोकप्रिय रूप से शारदा अधिनियम के नाम से जाना जाता है, जिसे बाद में लड़कियों के लिए 18 और लड़कों के लिए 21 कर दिया गया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which of the following type of biome have the subtype called as &lsquo;Tropical Savannah&rsquo;</p>",
                    question_hi: "<p>39. निम्नलिखित में से किस प्रकार के बायोम का \'उष्णकटिबंधीय सवाना\' नामक उप प्रकार होता है?</p>",
                    options_en: ["<p>desert</p>", "<p>forest</p>", 
                                "<p>aquatic</p>", "<p>grassland</p>"],
                    options_hi: ["<p>मरुस्थल</p>", "<p>वन</p>",
                                "<p>जलीय</p>", "<p>घासस्थल</p>"],
                    solution_en: "<p>39.(d) <strong>Grassland.</strong> Savannas are found to the north and south of tropical rainforest biomes. It is also known as Tropical wet and dry in the Koeppen classification. Savanna vegetation includes scrub, grasses, and occasional trees, which grow near water holes, seasonal rivers or aquifers. There are five major types of biomes - Aquatic, Grassland, Forest, Desert, and Tundra, though some of these biomes can be further divided into more specific categories, such as freshwater, marine, savanna, tropical rainforest, temperate rainforest, and taiga.</p>",
                    solution_hi: "<p>39.(d) <strong>घास का मैदान।</strong> सवाना उष्णकटिबंधीय वर्षावन बायोम के उत्तर और दक्षिण में पाए जाते हैं। कोप्पेन वर्गीकरण में इसे उष्णकटिबंधीय आर्द्र और शुष्क के रूप में भी जाना जाता है। सवाना वनस्पति में झाड़ियाँ, घास और सामयिक पेड़ शामिल हैं, जो पानी के छिद्रों, मौसमी नदियों या जलभृतों के नजदीक उगते हैं। बायोम के पांच प्रमुख प्रकार हैं - जलीय, घास का मैदान, वन, रेगिस्तान और टुंड्रा, हालांकि इनमें से कुछ बायोम को और अधिक विशिष्ट श्रेणियों में विभाजित किया जा सकता है, जैसे मीठा पानी, समुद्री, सवाना, उष्णकटिबंधीय वर्षावन, समशीतोष्ण वर्षावन और टैगा।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which Ministry is responsible for collecting data and publishing literacy rates in India?</p>",
                    question_hi: "<p>40. भारत में डेटा एकत्र करने और साक्षरता दर प्रकाशित करने के लिए कौन-सा मंत्रालय उत्&zwj;तरदायी है?</p>",
                    options_en: ["<p>Ministry of Statistics and Programme Implementation</p>", "<p>Ministry of Rural Development</p>", 
                                "<p>Ministry of External Affairs</p>", "<p>Ministry of Education</p>"],
                    options_hi: ["<p>सांख्यिकी और कार्यक्रम कार्यान्वयन मंत्रालय</p>", "<p>ग्रामीण विकास मंत्रालय</p>",
                                "<p>विदेश मंत्रालय</p>", "<p>शिक्षा मंत्रालय</p>"],
                    solution_en: "<p>40.(a) <strong>Ministry of Statistics and Programme Implementation. </strong>It came into existence as an Independent Ministry on 15 October 1999 after the merger of the Department of Statistics and the Department of Programme Implementation. The Ministry has two wings, one relating to Statistics and the other Programme Implementation. The Statistics Wing called the National Statistical Office (NSO) consists of the Central Statistical Office (CSO), the Computer center and the National Sample Survey Office (NSSO).</p>",
                    solution_hi: "<p>40.(a) <strong>सांख्यिकी और कार्यक्रम कार्यान्वयन मंत्रालय।</strong> यह सांख्यिकी विभाग और कार्यक्रम कार्यान्वयन विभाग के विलय के बाद 15 अक्टूबर 1999 को एक स्वतंत्र मंत्रालय के रूप में अस्तित्व में आया। मंत्रालय के दो विंग हैं, एक सांख्यिकी से संबंधित और दूसरा कार्यक्रम कार्यान्वयन से संबंधित। सांख्यिकी विंग जिसे राष्ट्रीय सांख्यिकी कार्यालय (NSO) कहा जाता है, में केंद्रीय सांख्यिकी कार्यालय (CSO), संगणक केन्द्र और राष्ट्रीय नमूना सर्वेक्षण कार्यालय (NSSO) शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. The collegium system of appointment of Judges to the High Court and Supreme Court came into existence by virtue of _____.</p>",
                    question_hi: "<p>41. ____ उच्च न्यायालय और सर्वोच्च न्यायालय में न्यायाधीशों की नियुक्ति की कॉलेजियम प्रणाली अस्तित्व में आई।</p>",
                    options_en: ["<p>a decision by the Cabinet Committee</p>", "<p>a Constitutional Amendment</p>", 
                                "<p>an act of Parliament</p>", "<p>a result of three separate cases before the Supreme Court</p>"],
                    options_hi: ["<p>मंत्रिमंडल समिति के निर्णय के आधार पर</p>", "<p>एक संवैधानिक संशोधन के आधार पर</p>",
                                "<p>संसद के एक अधिनियम के आधार पर</p>", "<p>सर्वोच्च न्यायालय के समक्ष तीन अलग-अलग मामलों के परिणाम स्वरूप</p>"],
                    solution_en: "<p>41.(d) The collegium system appointments and transfers of judges are decided by a forum of the Chief Justice of India and the four senior-most judges of the Supreme Court. The Chief Justice of India and the Judges of the Supreme Court are appointed by the President under Article 124 (2) of the Indian Constitution. The Constitution provides that a judge can be removed only by an order of the President, based on a motion passed by both Houses of Parliament. The procedure for removal of judges is elaborated in the Judges Inquiry Act, 1968.</p>",
                    solution_hi: "<p>41.(d) कॉलेजियम प्रणाली एक ऐसी प्रणाली है जिसके तहत न्यायाधीशों की नियुक्तियों और स्थानांतरणों का निर्णय भारत के मुख्य न्यायाधीश और सर्वोच्च न्यायालय के चार वरिष्ठतम न्यायाधीशों के एक मंच द्वारा किया जाता है। भारत के मुख्य न्यायाधीश और सर्वोच्च न्यायालय के न्यायाधीशों की नियुक्ति भारतीय संविधान के अनुच्छेद 124(2) के तहत राष्ट्रपति द्वारा की जाती है। संविधान में प्रावधान है कि किसी न्यायाधीश को संसद के दोनों सदनों द्वारा पारित प्रस्ताव के आधार पर केवल राष्ट्रपति के आदेश से ही हटाया जा सकता है। न्यायाधीशों को हटाने की प्रक्रिया न्यायाधीश जाँच अधिनियम, 1968 में विस्तृत है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. The last ruler of the Mauryan Empire was:</p>",
                    question_hi: "<p>42. मौर्य साम्राज्य का अंतिम शासक _____ था।</p>",
                    options_en: ["<p>Dasharatha Maurya</p>", "<p>Shalishuka</p>", 
                                "<p>Samprati</p>", "<p>Brihadratha</p>"],
                    options_hi: ["<p>दशरथ मौर्य (Dasharatha Maurya)</p>", "<p>शालिशुक (Shalishuka)</p>",
                                "<p>सम्प्रति (Samprati)</p>", "<p>बृहद्रथ (Brihadratha)</p>"],
                    solution_en: "<p>42.(d) <strong>Brihadratha.</strong> He ruled from 187 to 180 BCE. He was assassinated by his own General, Pushyamitra Sunga, who founded the Sunga Empire. He was the successor of Shatadhanavan, who ruled between 195 and 187 BC. The Maurya dynasty was founded by Chandragupta Maurya and dominated the Indian subcontinent between 322 and 185 BC. Ashoka was the most popular ruler of the dynasty who ruled from 268 to 232 BCE.</p>",
                    solution_hi: "<p>42.(d) <strong>बृहद्रथ</strong> I उन्होंने 187 से 180 ईसा पूर्व तक शासन किया। उनकी हत्या उनके ही सेनापति पुष्यमित्र शुंग ने की थी, जिन्होंने शुंग साम्राज्य की स्थापना की थी। वह शतधनवन के उत्तराधिकारी थे, जिन्होंने 195 और 187 ईसा पूर्व के बीच शासन किया था। मौर्य राजवंश की स्थापना चंद्रगुप्त मौर्य ने की थी और यह 322 से 185 ईसा पूर्व के बीच भारतीय उपमहाद्वीप पर प्रभुत्व था। अशोक राजवंश का सबसे लोकप्रिय शासक था जिसने 268 से 232 ईसा पूर्व तक शासन किया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. The Anglo-Mysore Wars were a series of ______ wars fought during the 18th century between the rulers of Mysore and the English East India Company.</p>",
                    question_hi: "<p>43. एंग्लो-मैसूर युद्ध 18वीं शताब्दी के दौरान मैसूर के शासकों और अँग्रेजी ईस्ट इंडिया कंपनी के बीच लड़े गए ____ युद्धों की एक शृंखला थी।</p>",
                    options_en: ["<p>three</p>", "<p>six</p>", 
                                "<p>two</p>", "<p>four</p>"],
                    options_hi: ["<p>तीन</p>", "<p>छ:</p>",
                                "<p>दो</p>", "<p>चार</p>"],
                    solution_en: "<p>43.(d) <strong>four.</strong> First Anglo Mysore War (1767 - 1769) - The Britishers were defeated by Haider Ali and the Treaty of Madras was signed between them. Second Anglo Mysore War (1780 - 1784) - fought between the British army (led by Warren Hastings) and Haider Ali. Ali was defeated at Porto Novo by Eyre Coote in 1781. Tipu Sultan signed the treaty of Mangalore in March 1784. Third Anglo Mysore War (1790 - 1792) - It began in 1790 and ended with Tipu\'s defeat in 1792. The Treaty of Seringapatam was signed in 1792. Fourth Anglo Mysore War (1799) - Fought between the British army ( led by Lord Wellesley) and Tipu Sultan. Tipu Sultan was killed.</p>",
                    solution_hi: "<p>43.(d) <strong>चार</strong> I प्रथम आंग्ल मैसूर युद्ध (1767 - 1769) - हैदर अली द्वारा अंग्रेज़ों को पराजित किया गया और उनके बीच मद्रास की संधि पर हस्ताक्षर किये गये। द्वितीय आंग्ल मैसूर युद्ध (1780 - 1784) - हैदर अली और ब्रिटिश सेना (वारेन हेस्टिंग्सके नेतृत्व वाली ) के बीच लड़ा गया। 1781 में अली को पोर्टो नोवो में आयर कूट द्वारा पराजित किया गया। टीपू सुल्तान ने मार्च 1784 में मैंगलोर की संधि पर हस्ताक्षर किए। तीसरा आंग्ल मैसूर युद्ध (1790 - 1792) - यह 1790 में शुरू हुआ और 1792 में टीपू की हार के साथ समाप्त हुआ। 1792 में श्रीरंगपट्टनम की संधि पर हस्ताक्षर किए गए। चौथा आंग्ल मैसूर युद्ध (1799) - टीपू सुल्तान और ब्रिटिश सेना (लॉर्ड वेलेस्ली के नेतृत्व वाली) के बीच हुआ। टीपू सुल्तान मारा गया ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. From which of the following countries were the principles of liberty, equality and fraternity taken in the Indian Constitution?</p>",
                    question_hi: "<p>44. भारतीय संविधान में स्वतंत्रता, समानता और बंधुत्व के सिद्धांत निम्नलिखित में से किस देश से लिए गए हैं?</p>",
                    options_en: ["<p>United Kingdom</p>", "<p>France</p>", 
                                "<p>Ireland</p>", "<p>United States of America</p>"],
                    options_hi: ["<p>यूनाइटेड किंगडम से</p>", "<p>फ्रांस से</p>",
                                "<p>आयरलैंड से</p>", "<p>संयुक्त राज्य अमेरिका से</p>"],
                    solution_en: "<p>44.(b) <strong>France.</strong> The Concept of Republic is also taken from the French Constitution. US Constitution - Preamble, Fundamental rights, Federal structure, Electoral college, Judicial review. British Constitution - Parliamentary form of government, The idea of single citizenship, Concept of the rule of law, Legislative process, Institution of the speaker and his role. Constitution of Ireland - Directive Principles of State Policy (DPSP), Nomination of Rajya Sabha members, Method of President&rsquo;s election.</p>",
                    solution_hi: "<p>44.(b) <strong>फ़्रांस।</strong> गणतंत्र की अवधारणा भी फ्रांसीसी संविधान से ली गई है। अमेरिकी संविधान - प्रस्तावना, मौलिक अधिकार, संघीय संरचना, निर्वाचन मंडल, न्यायिक समीक्षा। ब्रिटिश संविधान - सरकार का संसदीय स्वरूप, एकल नागरिकता का विचार, कानून के शासन की अवधारणा, विधायी प्रक्रिया, स्पीकर की संस्था और उसकी भूमिका। आयरलैंड का संविधान - राज्य के नीति निर्देशक सिद्धांत (DPSP), राज्यसभा सदस्यों का नामांकन, राष्ट्रपति के चुनाव की विधि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following classical dances is associated with Saint Srimanta Sankardev?</p>",
                    question_hi: "<p>45. निम्नलिखित में से कौन सा शास्त्रीय नृत्य संत श्रीमंत शंकरदेव से संबंधित है?</p>",
                    options_en: ["<p>Mohiniyattam</p>", "<p>Odissi</p>", 
                                "<p>Sattriya</p>", "<p>Kathakali</p>"],
                    options_hi: ["<p>मोहिनीअट्टम</p>", "<p>ओडिसी</p>",
                                "<p>सत्रिया</p>", "<p>कथकली</p>"],
                    solution_en: "<p>45.(c) <strong>Sattriya.</strong> The dance has its origin in the \'Sattras\' established by Mahapurush Srimanta Sankardev in the 15th and 16th centuries. The themes are based on Radha-Krishna stories. Famous Exponents of Sattriya Dance - Guru Jatin Goswami, Guru Ghanakanta Bora, Mallika Kandali, Manik Barbayan and Bhabananda Barbayan, Anwesha Mahanta, Moniram Dutta, Sharodi Saikia, Bhupen Hazarika, Anita Sharma.</p>",
                    solution_hi: "<p>45.(c) <strong>सत्रिया</strong> I इस नृत्य की उत्पत्ति 15वीं और 16वीं शताब्दी में महापुरुष श्रीमंत शंकरदेव द्वारा स्थापित \'सत्रस\' में हुई है। विषय वस्तु राधा-कृष्ण की कहानियों पर आधारित हैं। सत्त्रिया नृत्य के प्रसिद्ध प्रतिपादक - गुरु जतिन गोस्वामी, गुरु घनकांता बोरा, मल्लिका कंडाली, माणिक बारबायन और भाबानंद बारबायन, अन्वेषा महंत, मोनीराम दत्ता, शारोदी सैकिया, भूपेन हजारिका, अनीता शर्मा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. In a pond ecosystem, the first trophic level includes _____.</p>",
                    question_hi: "<p>46. एक तालाब पारिस्थितिकी तंत्र में पहले पोषी स्तर में _____ शामिल होता है।</p>",
                    options_en: ["<p>grasshopper</p>", "<p>zooplankton</p>", 
                                "<p>phytoplankton</p>", "<p>cow</p>"],
                    options_hi: ["<p>टिड्डी</p>", "<p>प्राणिप्लवक</p>",
                                "<p>पादप प्लवक</p>", "<p>गाय</p>"],
                    solution_en: "<p>46.(c) <strong>Phytoplankton.</strong> Based on the source of their nutrition or food, organisms occupy a specific place in the food chain that is known as their trophic level. Producers (algae, aquatic plants) belong to the first trophic level, Primary consumers (zooplanktons, snails, insects, small fishes, tadpoles), secondary consumers (frogs, big fishes, water snakes, crabs) to the second, Decomposers (fungi, bacteria and flagellates) to the third. The amount of energy decreases at successive trophic levels.</p>",
                    solution_hi: "<p>46.(c) <strong>फाइटोप्लांकटन</strong> । अपने पोषण या भोजन के स्रोत के आधार पर, जीव खाद्य श्रृंखला में एक विशिष्ट स्थान पर कब्जा कर लेते हैं जिसे उनके पोषी स्तर के रूप में जाना जाता है। उत्पादक (शैवाल, जलीय पौधे) पहले पोषी स्तर से संबंधित हैं, प्राथमिक उपभोक्ता (ज़ूप्लांकटन, घोंघे, कीड़े, छोटी मछलियाँ, टैडपोल), द्वितीयक उपभोक्ता (मेंढक, बड़ी मछलियाँ, पानी के साँप, केकड़े) दूसरे पोषी स्तर से, अपघटक(कवक, बैक्टीरिया और फ्लैगेलेट्स) तीसरे स्थान पर। क्रमिक पोषण स्तर पर ऊर्जा की मात्रा घटती जाती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. The Fundamental Rights can be suspended during the operation of a National Emergency except the rights guaranteed by ______.</p>",
                    question_hi: "<p>47. ____ द्वारा गारंटीकृत अधिकारों के अलावा मौलिक अधिकारों को राष्ट्रीय आपातकाल के संचालन के दौरान निलंबित किया जा सकता है।</p>",
                    options_en: ["<p>Articles 14 and 15</p>", "<p>Articles 20 and 21</p>", 
                                "<p>Article 23 and 24</p>", "<p>Articles 18 and 19</p>"],
                    options_hi: ["<p>अनुच्छेद 14 और 15</p>", "<p>अनुच्छेद 20 और 21</p>",
                                "<p>अनुच्छेद 23 और 24</p>", "<p>अनुच्छेद 18 और 19</p>"],
                    solution_en: "<p>47.(b) <strong>Articles 20 and 21.</strong> Article 20 - Protection in respect of conviction for offenses. Article 21 - Protection of life and personal liberty cannot be suspended. The express bar regarding the protection of Article 20 and Article 21 during an emergency is contained in Article 359 of the Constitution. Article 359 of the Constitution authorizes the President of India to suspend the right to move any court for the enforcement of Fundamental Rights during National Emergency.</p>",
                    solution_hi: "<p>47.(b) <strong>अनुच्छेद 20 और 21 ।</strong> अनुच्छेद 20 - अपराधों के लिए दोषसिद्धि के संबंध में संरक्षण। अनुच्छेद 21 - जीवन और व्यक्तिगत स्वतंत्रता की सुरक्षा को निलंबित नहीं किया जा सकता। आपातकाल के दौरान अनुच्छेद 20 और अनुच्छेद 21 की सुरक्षा के संबंध में एक्सप्रेस बार संविधान के अनुच्छेद 359 में निहित है। संविधान का अनुच्छेद 359 भारत के राष्ट्रपति को राष्ट्रीय आपातकाल के दौरान मौलिक अधिकारों को लागू करने के लिए किसी भी अदालत में जाने के अधिकार को निलंबित करने का अधिकार देता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. &lsquo;Wings of Fire&rsquo; is an autobiography of which of the following personalities?</p>",
                    question_hi: "<p>48. \'विंग्स ऑफ फायर\' निम्नलिखित में से किस शख्सियत की आत्मकथा है?</p>",
                    options_en: ["<p>Inder Kumar Gujral</p>", "<p>APJ Abdul Kalam</p>", 
                                "<p>K Sivan</p>", "<p>Pranab Mukherjee</p>"],
                    options_hi: ["<p>इंद्र कुमार गुजराल</p>", "<p>ए.पी.जे. अब्दुल कलाम</p>",
                                "<p>के. सिवन</p>", "<p>प्रणब मुखर्जी</p>"],
                    solution_en: "<p>48.(b) <strong>APJ Abdul Kalam.</strong> He is the first scientist to become the President of India. He served as the President of India from 2002 to 2007. The concept of the PURA (Providing Urban Amenities to Rural Areas) scheme was given by him. He was honored with the Bharat Ratna award in 1997. Notable works of A.P.J Abdul Kalam are - &ldquo;India 2020&rdquo;, &ldquo;Ignited Minds&rdquo;, &ldquo;Indomitable Spirit&rdquo;, &ldquo;Advantage India: From Challenge to Opportunity&rdquo;.</p>",
                    solution_hi: "<p>48.(b) <strong>एपीजे अब्दुल कलाम।</strong> वह भारत के राष्ट्रपति बनने वाले पहले वैज्ञानिक हैं। उन्होंने 2002 से 2007 तक भारत के राष्ट्रपति के रूप में कार्य किया। PURA (ग्रामीण क्षेत्रों में शहरी सुविधाएं प्रदान करना) योजना की अवधारणा उनके द्वारा दी गई थी। उन्हें 1997 में भारत रत्न पुरस्कार से सम्मानित किया गया था। ए.पी.जे. अब्दुल कलाम की उल्लेखनीय कृतियाँ हैं - \"इंडिया 2020\", \"इग्नाइटेड माइंड्स\", \"इंडोमेबल स्पिरिट\", \"एडवांटेज इंडिया: फ्रॉम चैलेंज टू अपॉर्चुनिटी\"।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following is/are NOT a part of the financial sector?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन सा/से वित्तीय क्षेत्र का हिस्सा नहीं है/हैं?</p>",
                    options_en: ["<p>Department of Agriculture &amp; Farmers Welfare</p>", "<p>Investment banks</p>", 
                                "<p>Commercial banks</p>", "<p>Stock exchange operations</p>"],
                    options_hi: ["<p>कृषि एवं किसान कल्याण विभाग</p>", "<p>निवेश बैंक</p>",
                                "<p>वाणिज्यिक बैंक</p>", "<p>स्टॉक एक्सचेंज संचालन</p>"],
                    solution_en: "<p>49.(a) <strong>Department of Agriculture &amp; Farmers Welfare. </strong>Parts of Financial System - Financial Assets, Financial Intermediaries/Financial Institutions, Financial Markets, Financial Rates of Return, Financial Instruments, Financial Services, etc. The Indian financial system is broadly classified into two broad groups - Organized sector and Unorganized sector.</p>",
                    solution_hi: "<p>49.(a) ​​<strong>कृषि और किसान कल्याण विभाग।</strong> वित्तीय प्रणाली के भाग - वित्तीय परिसंपत्तियाँ, वित्तीय मध्यस्थ/वित्तीय संस्थान, वित्तीय बाजार, रिटर्न की वित्तीय दरें, वित्तीय उपकरण, वित्तीय सेवाएँ, आदि। भारतीय वित्तीय प्रणाली को सामान्यतः दो व्यापक समूहों में वर्गीकृत किया गया है - संगठित क्षेत्र और असंगठित क्षेत्र।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "50",
                    section: "misc",
                    question_en: "<p>50. The phenomenon of change of liquid into vapours at any temperature below its boiling point is called:</p>",
                    question_hi: "<p>50. क्वथनांक से नीचे किसी भी तापमान पर, द्रव के वाष्प में परिवर्तित होने की घटना को निम्नलिखित में से क्या कहा जाता है?</p>",
                    options_en: ["<p>Melting</p>", "<p>Fusion</p>", 
                                "<p>Sublimation</p>", "<p>Evaporation</p>"],
                    options_hi: ["<p>गलन</p>", "<p>संलयन</p>",
                                "<p>ऊर्ध्वपातन</p>", "<p>वाष्पीकरण</p>"],
                    solution_en: "<p>50.(d) <strong>Evaporation.</strong> The reverse action of evaporation, where water vapor becomes liquid, is known as Condensation. Fusion or Melting - It is the process of converting a solid into its liquid state on heating. While solidification is the reverse of fusion. Sublimation - It is the process of changing from a solid to a gas without passing through an intermediate liquid phase. The reverse process of sublimation is called Deposition, where a substance in gas form changes its state to become a solid.</p>",
                    solution_hi: "<p>50.(d) <strong>वाष्पीकरण</strong> I वाष्पीकरण की विपरीत क्रिया, जहां जल वाष्प तरल हो जाता है, संघनन के रूप में जाना जाता है। संलयन या पिघलना - यह गर्म करने पर किसी ठोस को तरल अवस्था में परिवर्तित करने की प्रक्रिया है। जबकि जमना संलयन के विपरीत है। उर्ध्वपातन (Sublimation) - यह किसी मध्यवर्ती तरल चरण से गुजरे बिना ठोस से गैस में बदलने की प्रक्रिया है। उर्ध्वपातन की विपरीत प्रक्रिया को निक्षेपण (Deposition) कहा जाता है, जहां गैस के रूप में कोई पदार्थ ठोस बनने के लिए अपनी अवस्था बदलता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>