<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> A sphere of diameter 18 cm has been melted to make cylinder of radius 3 cm and height 2 cm. The total number of cylinder made out will be:</span></p>\\n",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> 18 &#2360;&#2375;&#2350;&#2368; </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2328;&#2354;&#2366;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3 &#2360;&#2375;&#2350;&#2368; </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2 &#2360;&#2375;&#2350;&#2368; </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2354;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368; </span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>40</p>\\n", "<p>100</p>\\n", 
                                "<p>54</p>\\n", "<p>45</p>\\n"],
                    options_hi: ["<p>40</p>\\n", "<p>100</p>\\n",
                                "<p>54</p>\\n", "<p>45</p>\\n"],
                    solution_en: "<p>1.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Volume of sphere = n </span><span style=\"font-family: Cambria Math;\">&times; Volume of cylinder</span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\">&times; &pi; </span><span style=\"font-weight: 400;\">&times; 9 &times; 9 &times; 9</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">n &times; &pi; </span><span style=\"font-weight: 400;\">&times; </span><span style=\"font-weight: 400;\">3&sup2;</span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&times; 2</span></p>\\r\\n<p><span style=\"font-weight: 400;\">n</span><span style=\"font-weight: 400;\"> = 2 &times; 9 &times; 3 = 54</span></p>\\n",
                    solution_hi: "<p>1.(c)</p>\\r\\n<p><span style=\"font-weight: 400;\">&#2327;&#2379;&#2354;&#2375; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; = </span><span style=\"font-weight: 400;\">n</span><span style=\"font-weight: 400;\"> &times; &#2348;&#2375;&#2354;&#2344; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344;</span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\">&times; &pi; </span><span style=\"font-weight: 400;\">&times; 9 &times; 9 &times; 9</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">n &times; &pi; </span><span style=\"font-weight: 400;\">&times; </span><span style=\"font-weight: 400;\">3&sup2;</span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&times; 2</span></p>\\r\\n<p><span style=\"font-weight: 400;\">n</span><span style=\"font-weight: 400;\"> = 2 &times; 9 &times; 3 = 54</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> If &Delta;ABC &cong;</span><span style=\"font-family: Cambria Math;\"> &Delta;PQR, and &ang;C = 80&deg;, &ang;A = 10&deg;, then what will be the measure of &ang;Q ?</span></p>\\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> &Delta;ABC &cong; &Delta;PQR </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &ang;C = 80&deg;, &ang;A = 10&deg;, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> &ang;Q </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368; </span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>80&deg;</p>\\n", "<p>30&deg;</p>\\n", 
                                "<p>100&deg;</p>\\n", "<p>90&deg;</p>\\n"],
                    options_hi: ["<p>80&deg;</p>\\n", "<p>30&deg;</p>\\n",
                                "<p>100&deg;</p>\\n", "<p>90&deg;</p>\\n"],
                    solution_en: "<p>2.(d)</p>\\r\\n<p><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">&Delta;</span>ABC</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&#10861;</span><span style=\"font-weight: 400;\"> <span style=\"font-family: Cambria Math;\">&Delta;</span></span><span style=\"font-weight: 400;\">PQR</span></p>\\r\\n<p><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">So</span>, </span><span style=\"font-weight: 400;\">&ang;A</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">&ang;P</span><span style=\"font-weight: 400;\">, </span><span style=\"font-weight: 400;\">&ang;B</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">&ang;Q</span><span style=\"font-weight: 400;\"> <span style=\"font-family: Cambria Math;\">and </span></span><span style=\"font-weight: 400;\">&ang;C</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">&ang;R</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&ang;B</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">&ang;Q</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">180&deg;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> (</span><span style=\"font-weight: 400;\">80&deg; + 10&deg;)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">= 90&deg;</span></p>\\n",
                    solution_hi: "<p>2.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">&Delta;</span>ABC</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&#10861;</span><span style=\"font-weight: 400;\"> <span style=\"font-family: Cambria Math;\">&Delta;</span></span><span style=\"font-weight: 400;\">PQR</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&#2311;&#2360;&#2354;&#2367;&#2319;, </span><span style=\"font-weight: 400;\">&ang;A</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">&ang;P</span><span style=\"font-weight: 400;\">, </span><span style=\"font-weight: 400;\">&ang;B</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">&ang;Q</span><span style=\"font-weight: 400;\">&nbsp; &#2324;&#2352; </span><span style=\"font-weight: 400;\">&ang;C</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">&ang;R</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&ang;B</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">&ang;Q</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">180&deg;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> (</span><span style=\"font-weight: 400;\">80&deg; + 10&deg;)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">= 90&deg;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Simplify: </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><mn>12</mn><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math>?</span></p>\\n",
                    question_hi: "<p>3. <span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> : </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><mn>12</mn><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math>?</span></p>\\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>\\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>\\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>\\n"],
                    solution_en: "<p>3.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If </span><span style=\"font-family: Cambria Math;\">&nbsp;(</span><span style=\"font-weight: 400;\">a + b + c) </span><span style=\"font-weight: 400;\">= 0, <span style=\"font-family: Cambria Math;\">Then</span><span style=\"font-family: Cambria Math;\">&nbsp;</span></span><span style=\"font-weight: 400;\">a&sup3;</span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">b&sup3;</span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">c&sup3;</span><span style=\"font-weight: 400;\">&nbsp;= 3</span><span style=\"font-weight: 400;\">abc</span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><mn>12</mn><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math></p>\\r\\n<p><span style=\"font-weight: 400;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>12</mn><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math></span></p>\\n",
                    solution_hi: "<p>3.(d)</p>\\r\\n<p><span style=\"font-weight: 400;\">&#2351;&#2342;&#2367; (</span><span style=\"font-weight: 400;\">a + b + c) </span><span style=\"font-weight: 400;\">= 0, &#2340;&#2348;&nbsp; </span><span style=\"font-weight: 400;\">a&sup3;</span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">b&sup3;</span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">c&sup3;</span><span style=\"font-weight: 400;\">&nbsp;= 3</span><span style=\"font-weight: 400;\">abc</span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><mn>12</mn><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math></p>\\r\\n<p><span style=\"font-weight: 400;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>12</mn><mo>(</mo><mn>1</mn><mo>-</mo><mi>m</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>m</mi><mo>-</mo><mi>n</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>n</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> The following bar graph shows the monthly profits of different six companies.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701508824/word/media/image1.png\" width=\"351\" height=\"247\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Reference:</strong> MONTHLY PROFITS OF DIFFERENT COMPANIES Monthly profit of different companies, PROFIT (IN LAKHS), COMPANIES</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">What is the ratio of the monthly profit of companies P to that of company U ?</span></p>\\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2344;&#2366;&#2347;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701508824/word/media/image2.png\" width=\"355\" height=\"260\"></p>\\r\\n<p><strong><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2342;&#2352;&#2381;&#2349;</span></strong><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\"> MONTHLY PROFITS OF DIFFERENT COMPANIES </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">, PROFIT (IN LAKHS) - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2344;&#2366;&#2347;&#2366;</span><span style=\"font-family: Cambria Math;\"> ( </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">), COMPANIES </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2366;&#2306;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2344;&#2366;&#2347;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> U </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2344;&#2366;&#2347;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>3 : 2</p>\\n", "<p>5 : 2</p>\\n", 
                                "<p>2 : 5</p>\\n", "<p>2 : 3</p>\\n"],
                    options_hi: ["<p>3 : 2</p>\\n", "<p>5 : 2</p>\\n",
                                "<p>2 : 5</p>\\n", "<p>2 : 3</p>\\n"],
                    solution_en: "<p>4.(b)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required ratio = 800 : 320 </span><span style=\"font-family: Cambria Math;\">= 5 : 2 </span></p>\\n",
                    solution_hi: "<p>4.(b)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 800 : 320 </span><span style=\"font-family: Cambria Math;\">= 5 : 2 </span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Rajender is working as a delivery boy in a multinational company. The company is paying him &#8377;90 per delivery. In a fortnight comprising 15 consecutive working days, his average earnings per day was &#8377;540. If his average earnings of the first 7 days was &#8377;519 per day and that of the last 7 days was &#8377;546 per day, then at was his earning on the 8th day of the fortnight ? </span></p>\\n",
                    question_hi: "<p>5. <span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2354;&#2381;&#2335;&#2368;&#2344;&#2375;&#2358;&#2344;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2354;&#2368;&#2357;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2377;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2354;&#2368;&#2357;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> &#8377; 90 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> 15 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2357;&#2360;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2326;&#2357;&#2366;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> &#8377;540 </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> &#8377;519 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2326;&#2367;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> &#8377;546 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2326;&#2357;&#2366;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 8</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368; </span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>&#8377;571</p>\\n", "<p>&#8377;681</p>\\n", 
                                "<p>&#8377;661</p>\\n", "<p>&#8377;645</p>\\n"],
                    options_hi: ["<p>&#8377;571</p>\\n", "<p>&#8377;681</p>\\n",
                                "<p>&#8377;661</p>\\n", "<p>&#8377;645</p>\\n"],
                    solution_en: "<p>5.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">His earning on 8th day&nbsp;</span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-weight: 400;\">540 + {(</span><span style=\"font-weight: 400;\">+ 21) </span><span style=\"font-weight: 400;\">&times; 7}</span><span style=\"font-weight: 400;\"> + {(</span><span style=\"font-weight: 400;\">- 6 </span><span style=\"font-weight: 400;\">) &times; 7}</span></p>\\r\\n<p><span style=\"font-weight: 400;\">= 540 + 105 = 645 &#8377;</span></p>\\n",
                    solution_hi: "<p>5.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">8 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-weight: 400;\">540 + {(</span><span style=\"font-weight: 400;\">+ 21) </span><span style=\"font-weight: 400;\">&times; 7}</span><span style=\"font-weight: 400;\"> + {(</span><span style=\"font-weight: 400;\">- 6 </span><span style=\"font-weight: 400;\">) &times; 7}</span></p>\\r\\n<p><span style=\"font-weight: 400;\">= 540 + 105 = 645 &#8377;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> If x&sup2; + y &sup2; + z<span style=\"font-weight: 400;\">&sup2;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 628 and xy + yz + zx = 24, then find the value of (x + y + z).</span></p>\\n",
                    question_hi: "<p>6. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> x&sup2; + y&sup2; + z&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 628 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> xy + yz + zx = 24, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> (x + y + z) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p>22</p>\\n", "<p>28</p>\\n", 
                                "<p>26</p>\\n", "<p>24</p>\\n"],
                    options_hi: ["<p>22</p>\\n", "<p>28</p>\\n",
                                "<p>26</p>\\n", "<p>24</p>\\n"],
                    solution_en: "<p>6.(c)</p>\\r\\n<p><span style=\"font-weight: 400;\">(x + y + z)<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">x<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">y<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\"> +&nbsp;</span><span style=\"font-weight: 400;\">z<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;+ 2(</span><span style=\"font-weight: 400;\">xy + yz + zx</span><span style=\"font-weight: 400;\">)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(x + y + z)</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>628</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>24</mn></msqrt></math></span></p>\\r\\n<p><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>676</mn></msqrt></math></span><span style=\"font-weight: 400;\"> = 26</span></p>\\n",
                    solution_hi: "<p>6.(c)</p>\\r\\n<p><span style=\"font-weight: 400;\">(x + y + z)<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">x<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">y<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\"> +&nbsp;</span><span style=\"font-weight: 400;\">z<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;+ 2(</span><span style=\"font-weight: 400;\">xy + yz + zx</span><span style=\"font-weight: 400;\">)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(x + y + z)</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>628</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>24</mn></msqrt></math></span></p>\\r\\n<p><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>676</mn></msqrt></math></span><span style=\"font-weight: 400;\"> = 26</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">The following pie chart represents the percentage-wise break up of research scholars in terms of their specialisation in Ph.D. in Physics. Total number of research scholars is 8000. Study the pie chart carefully and answer the question given below:</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701508824/word/media/image3.png\" width=\"250\" height=\"236\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><strong>Reference</strong>: Nuclear Physics, Solid state Physics, Quantum Physics, Nanotechnology, Astrophysics, Meteorology.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The number of research scholars having Meteorology as their specialization is approximately (correct up to two decimal places) what percent of the number of research scholars with Astrophysics specialization?</span></p>\\n",
                    question_hi: "<p>7. <span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2380;&#2340;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2368;&#2319;&#2330;&#2337;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2379;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2375;&#2359;&#2332;&#2381;&#2334;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2379;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2379;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 8000 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2381;&#2351;&#2366;&#2344;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2397;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701508824/word/media/image4.png\" width=\"248\" height=\"226\"></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2342;&#2352;&#2381;&#2349;</span><span style=\"font-family: Cambria Math;\">: Nuclear Physics </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2350;&#2366;&#2339;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2380;&#2340;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\">, Solid state Physics </span><span style=\"font-family: Nirmala UI;\">&#2336;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2380;&#2340;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\">,Quantum Physics- </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2357;&#2366;&#2306;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2380;&#2340;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\">, Nanotechnology </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2376;&#2344;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2380;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\">, Astrophysics </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2327;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2380;&#2340;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\">, Meteorology - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2380;&#2360;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2380;&#2360;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2375;&#2359;&#2332;&#2381;&#2334;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2379;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2349;&#2327;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2327;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2380;&#2340;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2375;&#2359;&#2332;&#2381;&#2334;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2379;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2375;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>72.41%</p>\\n", "<p>71.91%</p>\\n", 
                                "<p>70.41%</p>\\n", "<p>73.91%</p>\\n"],
                    options_hi: ["<p>72.41%</p>\\n", "<p>71.91%</p>\\n",
                                "<p>70.41%</p>\\n", "<p>73.91%</p>\\n"],
                    solution_en: "<p>7.(d)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>23</mn></mfrac></math>&times; 100</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 73.91%</span></p>\\n",
                    solution_hi: "<p>7.(d)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>23</mn></mfrac></math>&times; 100</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 73.91%</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">The given expression is equal to:&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mo>&sup2;</mo><mi>A</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math></span></p>\\n",
                    question_hi: "<p>8. <span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; <span style=\"font-family: Cambria Math;\">? </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mo>&sup2;</mo><mi>A</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math></span></p>\\n",
                    options_en: ["<p>2 cosecA cotA</p>\\n", "<p>2 cosA cotA</p>\\n", 
                                "<p>2 cosecA tanA</p>\\n", "<p>2 cosA sinA</p>\\n"],
                    options_hi: ["<p>2 cosecA cotA</p>\\n", "<p>2 cosA cotA</p>\\n",
                                "<p>2 cosecA tanA</p>\\n", "<p>2 cosA sinA</p>\\n"],
                    solution_en: "<p>8.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>-</mo><mn>1</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mi>cos</mi><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac></math></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\">2 cosecA. cotA</span></span></p>\\n",
                    solution_hi: "<p>8.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>-</mo><mn>1</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mi>cos</mi><mi>A</mi></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mi>sin</mi><mi>A</mi></mrow></mfrac></math></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\">2 cosecA. cotA</span></span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> The speed of a boat in standing water is 9 km/h and the speed of the stream is 1.5 km/h. A person rows to a place at a distance of 105 km and comes back to the starting point.The total time taken by him is:</span></p>\\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> 9 km/h </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> 1.5 km/h </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 105 km </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>24 hours</p>\\n", "<p>22 hours</p>\\n", 
                                "<p>21 hours</p>\\n", "<p>23 hours</p>\\n"],
                    options_hi: ["<p>24 <span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p>22 <span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                                "<p>21 <span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p>23 <span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    solution_en: "<p>9.(a)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Total time = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mrow><mo>(</mo><mn>9</mn><mo>+</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow></mfrac><mo>+</mo><mfrac><mn>105</mn><mrow><mo>(</mo><mn>9</mn><mo>-</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 10 + 14 = 24 hours</span></p>\\n",
                    solution_hi: "<p>9.(a)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mrow><mo>(</mo><mn>9</mn><mo>+</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow></mfrac><mo>+</mo><mfrac><mn>105</mn><mrow><mo>(</mo><mn>9</mn><mo>-</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 10 + 14 = 24 </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> Kalyan invested a sum of &#8377;12,000 for two years compounded at 5% and 10%, respectively. What is the compound interest (in &#8377;) at the end of two years ?</span></p>\\n",
                    question_hi: "<p>10. <span style=\"font-family: Nirmala UI;\">&#2325;&#2354;&#2381;&#2351;&#2366;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 5% </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> &#8377;12,000 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> (&#8377; </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>1,880</p>\\n", "<p>1,920</p>\\n", 
                                "<p>1,900</p>\\n", "<p>1,860</p>\\n"],
                    options_hi: ["<p>1,880</p>\\n", "<p>1,920</p>\\n",
                                "<p>1,900</p>\\n", "<p>1,860</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Net % interest rate = 5 + 10 +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&times;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 15.5%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C.I. = 12,000 &times; 15.5% = 1860 &#8377;</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> % </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 5 + 10 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&times;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 15.5%</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> = 12,000 &times; 15.5% = 1860 &#8377;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> A person invested a certain amount at a rate of 12% simple interest and another amount at a rate of 10% simple interest. He received an annual interest of &#8377;130. However, if he had interchanged the amounts invested, he would have received &#8377;4 more as interest. How much did he invest at the rate of 12% simple interest originally ?</span></p>\\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;130 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2354;&#2366;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> &#8377;4 </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366; </span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>&#8377;500</p>\\n", "<p>&#8377;650</p>\\n", 
                                "<p>&#8377;560</p>\\n", "<p>&#8377;600</p>\\n"],
                    options_hi: ["<p>&#8377;500</p>\\n", "<p>&#8377;650</p>\\n",
                                "<p>&#8377;560</p>\\n", "<p>&#8377;600</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(y - x) </span><span style=\"font-weight: 400;\">&times; 2%&nbsp; = 4 &#8377;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(y - x)</span><span style=\"font-weight: 400;\">&nbsp; = 200&#8377; &rArr;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">y</span><span style=\"font-weight: 400;\"> = 200 + </span><span style=\"font-weight: 400;\">x</span></p>\\r\\n<p><span style=\"font-weight: 400;\">Now,&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> &times; 12% + </span><span style=\"font-weight: 400;\">y</span><span style=\"font-weight: 400;\"> &times; 10% = 130</span></p>\\r\\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> &times; 12% + (</span><span style=\"font-weight: 400;\">200 + x)</span><span style=\"font-weight: 400;\"> &times; 10% = 130</span></p>\\r\\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> &times; 22% = 110&nbsp; </span></p>\\r\\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 500&#8377;</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(y - x) </span><span style=\"font-weight: 400;\">&times; 2%&nbsp; = 4 &#8377;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(y - x)</span><span style=\"font-weight: 400;\">&nbsp; = 200&#8377; &rArr;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">y</span><span style=\"font-weight: 400;\"> = 200 + </span><span style=\"font-weight: 400;\">x</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&#2309;&#2348;,&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> &times; 12% + </span><span style=\"font-weight: 400;\">y</span><span style=\"font-weight: 400;\"> &times; 10% = 130</span></p>\\r\\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> &times; 12% + (</span><span style=\"font-weight: 400;\">200 + x)</span><span style=\"font-weight: 400;\"> &times; 10% = 130</span></p>\\r\\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> &times; 22% = 110&nbsp; </span></p>\\r\\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 500&#8377;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">The sum of the amount that Sunaina and Ranjan have is &#8377;23 and the difference of their amount is &#8377;7. The ratio of the amount that Sunaina and Ranjan have is:</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    question_hi: "<p>12. <span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2344;&#2376;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2306;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> &#8377;23 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;7 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2344;&#2376;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2306;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>5 : 3</p>\\n", "<p>7 : 15</p>\\n", 
                                "<p>15 : 8</p>\\n", "<p>2 : 17</p>\\n"],
                    options_hi: ["<p>5 : 3</p>\\n", "<p>7 : 15</p>\\n",
                                "<p>15 : 8</p>\\n", "<p>2 : 17</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">.(c)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required ratio = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mn>23</mn><mo>+</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>)</mo><mo>:</mo><mo>(</mo><mfrac><mrow><mn>23</mn><mo>-</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>)</mo></math> </span><span style=\"font-family: Cambria Math;\">= 15 : 8</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">.(c)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mn>23</mn><mo>+</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>)</mo><mo>:</mo><mo>(</mo><mfrac><mrow><mn>23</mn><mo>-</mo><mn>7</mn></mrow><mn>2</mn></mfrac><mo>)</mo></math> </span><span style=\"font-family: Cambria Math;\">= 15 : 8</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">A renowned brand marked its goods at 40% above the cost price but allows a discount of 7.5% for UPI payments to the customers. What actual profit is made on a shirt, if a customer pays &#8377;1,813 to buy it using UPI ?</span></p>\\n",
                    question_hi: "<p>13. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2352;&#2366;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 40% </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2306;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2352;&#2366;&#2361;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> UPI </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 7.5% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2352;&#2366;&#2361;&#2325;</span><span style=\"font-family: Cambria Math;\"> UPI </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,813 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>&#8377;413</p>\\n", "<p>&#8377;348</p>\\n", 
                                "<p>&#8377;378</p>\\n", "<p>&#8377;393</p>\\n"],
                    options_hi: ["<p>&#8377;413</p>\\n", "<p>&#8377;348</p>\\n",
                                "<p>&#8377;378</p>\\n", "<p>&#8377;393</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required profit % = 40 - </span><span style=\"font-family: Cambria Math;\">7.5 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>7</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 29.5%</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Profit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1813</mn><mrow><mn>129</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math>&times; 29.5</span><span style=\"font-family: Cambria Math;\">&nbsp;= 413 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> % = 40 - </span><span style=\"font-family: Cambria Math;\">7.5 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>7</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 29.5%</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1813</mn><mrow><mn>129</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math>&times; 29.5 </span><span style=\"font-family: Cambria Math;\">= 413 &#8377;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">The marked price of a T-shirt is &#8377;560. The shopkeeper Krishna allows a discount of 16% and gains 12%. What would be the gain of Krishna if no discount is allowed ?</span></p>\\n",
                    question_hi: "<p>14. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2358;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;560 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2371;&#2359;&#2381;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> 16% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2371;&#2359;&#2381;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366; </span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">%.</span></p>\\n", "<p>31<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math><span style=\"font-family: Cambria Math;\">%.</span></p>\\n", 
                                "<p>33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">%.</span></p>\\n", "<p>11<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math>%</p>\\n"],
                    options_hi: ["<p>16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">%.</span></p>\\n", "<p>31<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math><span style=\"font-family: Cambria Math;\">%.</span></p>\\n",
                                "<p>33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> <span style=\"font-family: Cambria Math;\">%.</span></p>\\n", "<p>11<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math>%</p>\\n"],
                    solution_en: "<p>14.(c)</p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Cost price = 560 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>16</mn></mrow><mrow><mn>100</mn><mo>+</mo><mn>12</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 420 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required profit percentage = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>560</mn><mo>-</mo><mn>420</mn></mrow><mn>420</mn></mfrac></math>&times; 100 </span><span style=\"font-family: Cambria Math;\">= 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">%</span></p>\\n",
                    solution_hi: "<p>14.(c)</p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 560 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>16</mn></mrow><mrow><mn>100</mn><mo>+</mo><mn>12</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 420 &#8377;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>560</mn><mo>-</mo><mn>420</mn></mrow><mn>420</mn></mfrac></math>&times; 100 </span><span style=\"font-family: Cambria Math;\">= 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">%</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> At one end of a diameter PQ of a circle of radius 8 cm a tangent APB is drawn to the circle. Find the length of the chord RS, which is parallel to AB and at a distance of 12 cm from P.</span></p>\\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">8 &#2360;&#2375;&#2350;&#2368; </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> PQ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2346;&#2352;&#2381;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> APB </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2368;&#2306;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> RS </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> AB </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 12 &#2360;&#2375;&#2350;&#2368; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    options_en: ["<p>6&radic;3 cm</p>\\n", "<p>4&radic;3 cm</p>\\n", 
                                "<p>2&radic;3 cm</p>\\n", "<p>8&radic;3 cm</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"> 6&radic;3 &#2360;&#2375;&#2350;&#2368;</span></p>\\n", "<p>4&radic;3 &#2360;&#2375;&#2350;&#2368;</p>\\n",
                                "<p>2&radic;3 &#2360;&#2375;&#2350;&#2368;</p>\\n", "<p>8&radic;3 &#2360;&#2375;&#2350;&#2368;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701508824/word/media/image5.png\" width=\"186\" height=\"165\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Length of PC = 12 cm &hellip;(given)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">PO = OR = OQ = radius = 8 cm</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">So, OC = 12 - </span><span style=\"font-family: Cambria Math;\">8 = 4 cm</span></p>\\r\\n<p><span style=\"font-weight: 400;\">Now, in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-weight: 400;\">RCO</span><span style=\"font-weight: 400;\">&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">RC = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-weight: 400;\"> cm</span></p>\\r\\n<p><span style=\"font-weight: 400;\">RS = 2RC = </span><span style=\"font-weight: 400;\">8</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-weight: 400;\"> cm</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1701508824/word/media/image5.png\" width=\"186\" height=\"165\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">PC </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 12 &#2360;&#2375;&#2350;&#2368;&hellip;(</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\">)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">PO = OR = OQ = </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 8 &#2360;&#2375;&#2350;&#2368;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, OC = 12 - </span><span style=\"font-family: Cambria Math;\">8 = 4 &#2360;&#2375;&#2350;&#2368;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-weight: 400;\">RCO</span><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306;&nbsp;&nbsp;&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">RC = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-weight: 400;\"> <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;</span></span></p>\\r\\n<p><span style=\"font-weight: 400;\">RS = 2RC = </span><span style=\"font-weight: 400;\">8</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-weight: 400;\"> <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;</span></span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16.<span style=\"font-family: Cambria Math;\"> If the mathematical symbols in the expression given below are changed from &lsquo;+&rsquo; to &lsquo;&divide;</span><span style=\"font-family: Cambria Math;\">&rsquo; &lsquo;-&rsquo; to</span><span style=\"font-family: Cambria Math;\">&rsquo; &times;</span><span style=\"font-family: Cambria Math;\">&rsquo; &rsquo;&divide;&rsquo; &nbsp;to &lsquo;-&rsquo; and&nbsp; &rsquo;&times;</span><span style=\"font-family: Cambria Math;\">&rsquo; to &lsquo;+&rsquo;. then what will be the value of the expression ?</span></p>\\r\\n<p><span style=\"font-weight: 400;\">87 &times; </span><span style=\"font-weight: 400;\">256 + 16 - 77</span><span style=\"font-weight: 400;\"> &divide; </span><span style=\"font-weight: 400;\">1359</span></p>\\n",
                    question_hi: "<p>16.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2368;&#2325;</span><span style=\"font-family: Cambria Math;\"> \'+\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'&divide;&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, &lsquo;-&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &lsquo;&times;&rsquo; \'</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, &lsquo;&divide;&rsquo; \'</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\">\' &lsquo;-&rsquo; \' </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">,</span><span style=\"font-family: Nirmala UI;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\">&nbsp;&lsquo;&times;&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'+\' </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352; </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\r\\n<p><span style=\"font-weight: 400;\">87 &times; </span><span style=\"font-weight: 400;\">256 + 16 - 77</span><span style=\"font-weight: 400;\"> &divide; </span><span style=\"font-weight: 400;\">1359</span></p>\\n",
                    options_en: ["<p>20</p>\\n", "<p>-40</p>\\n", 
                                "<p>40</p>\\n", "<p>- 20</p>\\n"],
                    options_hi: ["<p>20</p>\\n", "<p>-40</p>\\n",
                                "<p>40</p>\\n", "<p>- 20</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">87 &times; 256 + 16 </span><span style=\"font-family: Cambria Math;\"> 77 &divide; 1359</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">After changing sign, </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">87 + 256 &divide; 16 &times; 77 - </span><span style=\"font-family: Cambria Math;\">1359</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 87 + 16 &times; 77 - </span><span style=\"font-family: Cambria Math;\">1359</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 87 + 1232 - </span><span style=\"font-family: Cambria Math;\">1359</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= - </span><span style=\"font-family: Cambria Math;\">40</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">87 &times; 256 + 16 - </span><span style=\"font-family: Cambria Math;\">77 &divide; 1359</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\">, </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">87 + 256 &divide; 16 &times; 77 - </span><span style=\"font-family: Cambria Math;\">1359</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 87 + 16 &times; 77 - </span><span style=\"font-family: Cambria Math;\">1359</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 87 + 1232 - </span><span style=\"font-family: Cambria Math;\">1359</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= - </span><span style=\"font-family: Cambria Math;\">40</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">The following table gives the percentage of marks obtained by seven students in six different subjects in an examination. </span><span style=\"font-family: Cambria Math;\">The number in the brackets gives the maximum marks in each subject.</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_14462077211701515065540.png\" width=\"397\" height=\"170\"></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If a student secures 80% or more in a subject, the student is considered to have received a distinction in the subject. What is the maximum number of subjects in which any single student has received a distinction ?</span></p>\\n",
                    question_hi: "<p>17. <span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2368;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2359;&#2381;&#2336;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_10784094211701515007618.png\" width=\"399\" height=\"218\"></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 80% </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2360;&#2381;&#2335;&#2367;&#2306;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;&#2381;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2360;&#2381;&#2335;&#2367;&#2306;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>4</p>\\n", "<p>6</p>\\n", 
                                "<p>5</p>\\n", "<p>3</p>\\n"],
                    options_hi: ["<p>4</p>\\n", "<p>6</p>\\n",
                                "<p>5</p>\\n", "<p>3</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">17</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">There are max. 4 subjects in which any single student receives distinction.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">i.e. Aman = Maths, Chemistry, Physics and History </span><span style=\"font-family: \'Cambria Math\';\">and Muskan = Maths, Physics, Geography and Computer science</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2320;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2344;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2366;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2350;&#2344;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2360;&#2366;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2380;&#2340;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2340;&#2367;&#2361;&#2366;&#2360; </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2360;&#2381;&#2325;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2380;&#2340;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2370;&#2327;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18.<span style=\"font-family: Cambria Math;\"> The angle subtended at the centre of a circle by an arc of the circle measures 128.5&deg;. Find the angle subtended at the circumference of the circle by the same arc.</span></p>\\n",
                    question_hi: "<p>18. <span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> 128.5&deg; </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p>65.25&deg;</p>\\n", "<p>65.5&deg;</p>\\n", 
                                "<p>64.25&deg;</p>\\n", "<p>64.5&deg;</p>\\n"],
                    options_hi: ["<p>65.25&deg;</p>\\n", "<p>65.5&deg;</p>\\n",
                                "<p>64.25&deg;</p>\\n", "<p>64.5&deg;</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">18</span><span style=\"font-family: Cambria Math;\">.(c)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required angle = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>128</mn><mo>.</mo><mn>5</mn><mo>&deg;</mo></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 64.25&deg;</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18</span><span style=\"font-family: Cambria Math;\">.(c)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2368;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>128</mn><mo>.</mo><mn>5</mn><mo>&deg;</mo></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 64.25&deg;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. <span style=\"font-family: Cambria Math;\">The average age of 25 students in a class is 14 years. Five new students with an average age of 14.6 years join this class. What is the average age of 30 students in the class ?</span></p>\\n",
                    question_hi: "<p>19. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 25 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2342;&#2381;&#2351;&#2366;&#2352;&#2381;&#2341;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> 14 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> 14.6 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2342;&#2381;&#2351;&#2366;&#2352;&#2381;&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 30 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2342;&#2381;&#2351;&#2366;&#2352;&#2381;&#2341;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>13.1 years</p>\\n", "<p>14.1 years</p>\\n", 
                                "<p>15.1 years</p>\\n", "<p>16.1 years</p>\\n"],
                    options_hi: ["<p>13.1 years</p>\\n", "<p>14.1 years</p>\\n",
                                "<p>15.1 years</p>\\n", "<p>16.1 years</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Required new average = 14 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>14</mn><mo>.</mo><mn>6</mn><mo>-</mo><mn>14</mn><mo>)</mo><mo>&times;</mo><mn>5</mn></mrow><mn>30</mn></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 14 + 0.1 = 14.1 years</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 14 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>14</mn><mo>.</mo><mn>6</mn><mo>-</mo><mn>14</mn><mo>)</mo><mo>&times;</mo><mn>5</mn></mrow><mn>30</mn></mfrac></math></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">= 14 + 0.1 = 14.1 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20.<span style=\"font-family: Cambria Math;\"> Find the distance (in cm) between the centres</span><span style=\"font-family: Cambria Math;\"> of two circles having radii of 40 cm and 30 cm, respectively, if the length of a/an transverse/indirect common tangent to the two circles is given as 10&radic;15 cm.&nbsp;</span></p>\\n",
                    question_hi: "<p>20.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 40 &#2360;&#2375;&#2350;&#2368; </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 30 &#2360;&#2375;&#2350;&#2368; </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> (&#2360;&#2375;&#2350;&#2368; </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2381;&#2352;&#2360;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> / </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2349;&#2351;&#2344;&#2367;&#2359;&#2381;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2346;&#2352;&#2381;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">10&radic;15 &#2360;&#2375;&#2350;&#2368; </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    options_en: ["<p>75</p>\\n", "<p>84</p>\\n", 
                                "<p>72</p>\\n", "<p>80</p>\\n"],
                    options_hi: ["<p>75</p>\\n", "<p>84</p>\\n",
                                "<p>72</p>\\n", "<p>80</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">20</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">(10&radic;15)&sup2;</span> =&nbsp; </span><span style=\"font-weight: 400;\">d<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> (</span><span style=\"font-weight: 400;\">40 + 30)<span style=\"font-family: Cambria Math;\">&sup2;</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">d<span style=\"font-weight: 400;\">&sup2;</span> = 1500 + 4900 </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">d = 80 cm</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">20</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">(10&radic;15)&sup2;</span> =&nbsp; </span><span style=\"font-weight: 400;\">d<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> (</span><span style=\"font-weight: 400;\">40 + 30)<span style=\"font-family: Cambria Math;\">&sup2;</span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">d<span style=\"font-weight: 400;\">&sup2;</span> = 1500 + 4900 </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">d = 80 &#2360;&#2375;&#2350;&#2368; </span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. <span style=\"font-family: Cambria Math;\">The given expression is equal to:<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin</mi><mi>A</mi><mo>)</mo></mrow></mfrac></math> </span></p>\\n",
                    question_hi: "<p>21. <span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin</mi><mi>A</mi><mo>)</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">________ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    options_en: ["<p>1 - sin A</p>\\n", "<p>1+ cos A</p>\\n", 
                                "<p>Sin A</p>\\n", "<p>Cos A</p>\\n"],
                    options_hi: ["<p>1 - sin A</p>\\n", "<p>1+ cos A</p>\\n",
                                "<p>Sin A</p>\\n", "<p>Cos A</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin</mi><mi>A</mi><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin</mi><mi>A</mi><mo>)</mo></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin</mi><mi>A</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>sin</mi><mi>A</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mi>A</mi></mrow></mfrac></math>= &nbsp;1 - sinA</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21</span><span style=\"font-family: Cambria Math;\">.(a)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin</mi><mi>A</mi><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin</mi><mi>A</mi><mo>)</mo></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin</mi><mi>A</mi><mo>)</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>sin</mi><mi>A</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mi>A</mi></mrow></mfrac></math> = &nbsp;1 - sinA<br></span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22.<span style=\"font-family: Cambria Math;\"> if x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">= - 1, then the value of </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mi>x</mi><mrow><mn>15</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><mrow><mo>&nbsp;</mo><msup><mi>x</mi><mrow><mn>15</mn><mo>&nbsp;</mo></mrow></msup></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> will be:</span></p>\\n",
                    question_hi: "<p>22.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">= - 1, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mi>x</mi><mrow><mn>15</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><mrow><mo>&nbsp;</mo><msup><mi>x</mi><mrow><mn>15</mn><mo>&nbsp;</mo></mrow></msup></mrow></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p>3</p>\\n", "<p>2</p>\\n", 
                                "<p>- 1</p>\\n", "<p>0</p>\\n"],
                    options_hi: ["<p>3</p>\\n", "<p>2</p>\\n",
                                "<p>- 1</p>\\n", "<p>0</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> - 1 </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">x&sup3; = 1 &#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>15</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> = 1</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">Now</span><span style=\"font-family: Cambria Math;\">, </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mi>x</mi><mrow><mn>15</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><mrow><mo>&nbsp;</mo><msup><mi>x</mi><mrow><mn>15</mn><mo>&nbsp;</mo></mrow></msup></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 1 + 1 = 2</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> - 1 </span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">x&sup3; = 1 &#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>15</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> = 1</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">, </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mi>x</mi><mrow><mn>15</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mfrac><mn>1</mn><mrow><mo>&nbsp;</mo><msup><mi>x</mi><mrow><mn>15</mn><mo>&nbsp;</mo></mrow></msup></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 1 + 1 = 2</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23.<span style=\"font-family: Cambria Math;\"> Two numbers are in the ratio of 1 : 2. If 7 is added to both, their ratio changes to 3 : 5. The greater number is:</span></p>\\n",
                    question_hi: "<p>23. <span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> 1 : 2 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 3 : 5 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\\n",
                    options_en: ["<p>30</p>\\n", "<p>18</p>\\n", 
                                "<p>14</p>\\n", "<p>28</p>\\n"],
                    options_hi: ["<p>30</p>\\n", "<p>18</p>\\n",
                                "<p>14</p>\\n", "<p>28</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>7</mn></mrow><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>7</mn></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">x = 7 &times; 2 = 14</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Greatest number (2x</span><span style=\"font-family: Cambria Math;\">) = 28</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\">.(d)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>7</mn></mrow><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>7</mn></mrow></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>5</mn></mfrac></math> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">x = 7 &times; 2 = 14</span></p>\\r\\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> (2x</span><span style=\"font-family: Cambria Math;\">) = 28</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. <span style=\"font-family: Cambria Math;\">A trader offers the following discount schemes for the purchase of his goods. Find the scheme which has the minimum discount.</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(i) Two successive discounts of 12% each</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(ii) Discount of 20%</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(iii) Buy 5 and get 1 free</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(iv) Buy 6 and get 2 free</span></p>\\n",
                    question_hi: "<p>24. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2368;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2368;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2381;&#2351;&#2370;&#2344;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(i) &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; 12% &#2325;&#2368; &#2342;&#2379; &#2325;&#2381;&#2352;&#2350;&#2367;&#2325; &#2331;&#2370;&#2335;&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(ii) 20% &#2325;&#2368; &#2331;&#2370;&#2335;&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(iii) 5 &#2326;&#2352;&#2368;&#2342;&#2375; &#2324;&#2352; 1&nbsp; &#2350;&#2369;&#2347;&#2381;&#2340; &#2346;&#2366;&#2319;&#2305;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(iv) 6 &#2326;&#2352;&#2368;&#2342;&#2375;&#2306; &#2324;&#2352; 2 &#2350;&#2369;&#2347;&#2381;&#2340; &#2346;&#2366;&#2319;&#2305;</span></p>\\n",
                    options_en: ["<p><span style=\"font-weight: 400;\">(ii)</span></p>\\n", "<p><span style=\"font-weight: 400;\">(iii)</span></p>\\n", 
                                "<p><span style=\"font-weight: 400;\">(iv)</span></p>\\n", "<p><span style=\"font-weight: 400;\">(i)</span></p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">(ii)</span></p>\\n", "<p><span style=\"font-weight: 400;\">(iii)</span></p>\\n",
                                "<p><span style=\"font-weight: 400;\">(iv)</span></p>\\n", "<p><span style=\"font-weight: 400;\">(i)</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(i)&nbsp; 12 + 12 </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&times;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math><span style=\"font-weight: 400;\">= 22.56 %</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(ii) 20%</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(iii) </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> = 16.66%</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(iv) </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn></mrow><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac></math><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> = 25%</span></p>\\r\\n<p><span style=\"font-weight: 400;\">Clearly , (iii) has a minimum discount.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(i)&nbsp; 12 + 12 </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&times;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math><span style=\"font-weight: 400;\">= 22.56 %</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(ii) 20%</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(iii) </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mn>5</mn><mo>)</mo></mrow></mfrac></math><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> = 16.66%</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(iv) </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn></mrow><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>6</mn><mo>)</mo><mo>&nbsp;</mo></mrow></mfrac></math><span style=\"font-weight: 400;\">&times; 100</span><span style=\"font-weight: 400;\"> = 25%</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335; &#2352;&#2370;&#2346; &#2360;&#2375;, (iii) &#2350;&#2375;&#2306; &#2344;&#2381;&#2351;&#2370;&#2344;&#2340;&#2350; &#2331;&#2370;&#2335; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <span style=\"font-family: Cambria Math;\">The value of sin&sup2; 30+ sin&sup2; 40+ sin&sup2; 45 + sin&sup2; 55+ sin&sup2; 35 + sin&sup2; 45 + sin&sup2; 50+ sin&sup2; 60 is:</span></p>\\n",
                    question_hi: "<p>25. <span style=\"font-family: Cambria Math;\">sin&sup2; 30 + sin&sup2; 40 + sin&sup2; 45 + sin&sup2; 55 + sin&sup2; 35 + sin&sup2; 45 + sin&sup2; 50 + sin&sup2; 60 </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p>2</p>\\n", "<p>4</p>\\n", 
                                "<p>3</p>\\n", "<p>1</p>\\n"],
                    options_hi: ["<p>2</p>\\n", "<p>4</p>\\n",
                                "<p>3</p>\\n", "<p>1</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">25</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If A + B = 90&deg; then </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">A</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">B</span><span style=\"font-weight: 400;\"> = 1</span></p>\\r\\n<p><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">30&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">40&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">45&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">55&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">35&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">45&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">50&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">60&deg;</span><span style=\"font-weight: 400;\">&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">30&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">60&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">40&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">50&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">45&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">45&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">55&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">35&deg;</span><span style=\"font-weight: 400;\">&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">= 1 + 1 + 1 + 1&nbsp; = 4</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">25</span><span style=\"font-family: Cambria Math;\">.(b)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">If A + B =&nbsp;</span><span style=\"font-weight: 400;\">90&deg;&nbsp; &#2340;&#2348; </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">A</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">B</span><span style=\"font-weight: 400;\"> = 1</span></p>\\r\\n<p><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">30&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">40&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">45&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">55&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\">35&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">45&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">50&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">60&deg;</span><span style=\"font-weight: 400;\">&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">30&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">60&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">40&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">50&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">45&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">45&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">55&deg;</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">35&deg;</span><span style=\"font-weight: 400;\">&nbsp;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">= 1 + 1 + 1 + 1&nbsp; = 4</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>