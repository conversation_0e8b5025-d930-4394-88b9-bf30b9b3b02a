<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. The Daroga system in 1792 was introduced by whom among the following Governor- Generals in India?</p>",
                    question_hi: "<p>1. भारत में 1792 में, निम्नलिखित में से किस गवर्नर-जनरल द्वारा दरोगा प्रणाली की शुरुआत की गई थी?</p>",
                    options_en: ["<p>Lord Cornwallis</p>", "<p>Lord Minto</p>", 
                                "<p>Lord Mayo</p>", "<p>Lord William Bentinck</p>"],
                    options_hi: ["<p>लॉर्ड कॉर्नवालिस</p>", "<p>लॉर्ड मिंटो</p>",
                                "<p>लॉर्ड मेयो</p>", "<p>लॉर्ड विलियम बेंटिक</p>"],
                    solution_en: "<p>1.(a) <strong>Lord Cornwallis</strong> (1786-93): The system of Daroga as part of Police reforms. Police of each district were placed under District Judge. Each district was divided into Thanas/Police circles which were headed by Daroga. He also abolished District Faujdari Courts and set up circuit courts in Calcutta, Dacca, Murshidabad, and Patna. During his tenure, the Third Mysore War (1790-92) and Treaty of Seringapatam (1792), Permanent Settlement (1793), Cornwallis Code (1793) took place.</p>",
                    solution_hi: "<p>1.(a) <strong>लॉर्ड कॉर्नवालिस</strong> (1786-93): पुलिस सुधारों के तहत दरोगा प्रणाली की शुरुआत की गई। प्रत्येक जिले की पुलिस को जिला न्यायाधीश के अधीन रखा गया। प्रत्येक जिले को थानों/पुलिस सर्किलों में विभाजित किया गया था, जिनका नेतृत्व दरोगा करते थे। उन्होंने जिला फौजदारी अदालतों को भी समाप्त कर दिया और कलकत्ता, ढाका, मुर्शिदाबाद और पटना में सर्किट कोर्ट स्थापित किए। उनके कार्यकाल के दौरान, तीसरा मैसूर युद्ध (1790-92) और श्रीरंगपट्टनम की संधि (1792), स्थायी बंदोबस्त (1793), कॉर्नवालिस कोड (1793) हुए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which of the following kingdoms was divided into subdivisions called Aharas or Rashtras that meant districts?</p>",
                    question_hi: "<p>2. निम्नलिखित में से किस साम्राज्य को अहार या राष्ट्र नामक उपविभागों में विभाजित किया गया था जिसका अर्थ जिला था?</p>",
                    options_en: ["<p>Kushana</p>", "<p>Parthian</p>", 
                                "<p>Satavahana</p>", "<p>Shaka</p>"],
                    options_hi: ["<p>कुषाण</p>", "<p>पार्थियन</p>",
                                "<p>सातवाहन</p>", "<p>शक</p>"],
                    solution_en: "<p>2.(c) <strong>Satavahana</strong>: Period: 60 BC - 225 AD; Capital - Pratishthana (Paithan) and Amravati; Founder - Simuka. The lowest level of administration was a grama which was under the charge of a Gramika. There were also officers called amatyas who were perhaps ministers or advisors of the king. Revenue was collected both in cash and kind. It majorly comprised present Andhra Pradesh, Telangana, and Maharashtra. Kushana Empire was divided into Satraps (provinces), ruled by a Mahakshatrapa (military governor).</p>",
                    solution_hi: "<p>2.(c) <strong>सातवाहन</strong>: काल: 60 ई.पू. - 225 ई.पू.; राजधानी - प्रतिष्ठान (पैठन) और अमरावती; संस्थापक - सिमुक। प्रशासन का निम्नतम स्तर ग्राम था जो ग्रामिक के अधीन था। अमात्य नामक अधिकारी भी होते थे जो संभवतः राजा के मंत्री या सलाहकार होते थे। राजस्व नकद और वस्तु दोनों रूपों में एकत्र किया जाता था। इसमें मुख्य रूप से वर्तमान आंध्र प्रदेश, तेलंगाना और महाराष्ट्र शामिल थे। कुषाण साम्राज्य क्षत्रपों (प्रांतों) में विभाजित किया गया था, जिस पर महाक्षत्रप (सैन्य गवर्नर) का शासन था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. ______, authored by Tolkappiyar is the earliest Tamil literature.</p>",
                    question_hi: "<p>3. _________, तोल्काप्पियार (Tolkappiyar) द्वारा लिखित सबसे प्रारंभिक तमिल साहित्य है।</p>",
                    options_en: ["<p>Tolkappiyam</p>", "<p>Ettutogai</p>", 
                                "<p>Kalittogai</p>", "<p>Narrinai</p>"],
                    options_hi: ["<p>तोल्काप्पियम (Tolkappiyam)</p>", "<p>एत्तुतोगई (Ettutogai)</p>",
                                "<p>कलित्टोकाई (Kalittogai)</p>", "<p>नारीनई (Narrinai)</p>"],
                    solution_en: "<p>3.(a) <strong>Tolkappiyam</strong>. Tamil has a literary tradition that goes back to the period of the Sangams. The most well-known surviving works from this period are the Tolkappiyam (a book of grammar attributed to Tolkappiyar), Ettu Togai (Eight Anthologies), Pattu Pattu (Ten Idylls). Epics Silappadikaram and Manimekalai authored by Ilango Adigal and Sattanar respectively. Thiru Kural a Tamil treatise par excellence by Thiruvalluvar. Perungadai proportion by Kongu Velir.</p>",
                    solution_hi: "<p>3.(a) <strong>तोल्काप्पियम </strong>(Tolkappiyam)। तमिल की साहित्यिक परंपरा संगम काल से चली आ रही है। इस काल की सबसे प्रसिद्ध रचनाओं तोल्काप्पियम (तोलकाप्पियर द्वारा रचित - व्याकरण का एक ग्रंथ), एत्तुतोगई (आठ संकलन), पट्टुपट्टू (दस आदर्श), महाकाव्य सिलप्पादिकारम और मणिमेकलाई क्रमशः इलगो अडिगल और सत्तानार द्वारा रचित, थिरु कुरल तिरुवल्लुवर द्वारा रचित -उत्कृष्ट तमिल ग्रंथ, कोंगू वेलिर द्वारा रचित- पेरुंगदाई शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which Indian political leader was called as Lok Nayak?</p>",
                    question_hi: "<p>4. किस भारतीय राजनेता को लोकनायक कहा जाता था?</p>",
                    options_en: ["<p>Ram Manohar Lohia</p>", "<p>Jai Prakash Narayan</p>", 
                                "<p>Bal Gangadhar Tilak</p>", "<p>Subhash Chandra Bose</p>"],
                    options_hi: ["<p>राम मनोहर लोहिया</p>", "<p>जय प्रकाश नारायण</p>",
                                "<p>बाल गंगाधर तिलक</p>", "<p>सुभाष चंद्र बोस</p>"],
                    solution_en: "<p>4.(b) <strong>Jai Prakash Narayan</strong>. He received the Ramon Magsaysay Award (1965) and Bharat Ratna (1998, posthumously). Famous Personalities &amp; their Nicknames: Bihar Kesri - Sri krishna Sinha, Bihar Vibhuti - Dr. Anurag Narayan Singh, Iron Man of India - Vallabhai Patel, Biswa Kavi - Rabindranath Tagore, Netaji - Subhash Chandra Bose. Lokmanya Tilak - Bal Gangadhar Tilak.</p>",
                    solution_hi: "<p>4.(b) <strong>जय प्रकाश नारायण। </strong>उन्हें रेमन मैक्सेसे पुरस्कार (1965) और भारत रत्न (1998, मरणोपरांत) से सम्मानित किया गया। प्रसिद्ध व्यक्तित्व और उनके उपनाम: बिहार केसरी - श्रीकृष्ण सिन्हा, बिहार विभूति - डॉ. अनुराग नारायण सिंह, भारत के लौह पुरुष - वल्लभभाई पटेल, विश्व कवि - रवींद्रनाथ टैगोर, नेता जी - सुभाष चंद्र बोस। लोकमान्य तिलक - बाल गंगाधर तिलक।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. The Pandya Kingdom was first mentioned by Megasthenes, who said that their kingdom was famous for pearls.Their capital was:</p>",
                    question_hi: "<p>5. पांड्य साम्राज्य का उल्लेख सबसे पहले मेगस्थनीज ने किया था, जिसने कहा था कि उनका साम्राज्य मोतियों के लिए प्रसिद्ध था। उनकी राजधानी _________ थी।</p>",
                    options_en: ["<p>Thanjavur</p>", "<p>Uraiyur</p>", 
                                "<p>Madurai</p>", "<p>Kaveripatnam</p>"],
                    options_hi: ["<p>तंजावुर</p>", "<p>उरैयूर</p>",
                                "<p>मदुरै</p>", "<p>कावेरीपट्टनम</p>"],
                    solution_en: "<p>5(c) <strong>Madurai</strong>. Megasthenes: A Greek diplomat and historian who mentioned the Pandya Kingdom in his book Indica around 300 BCE. The Pandya Kingdom was an ancient Tamil kingdom that ruled parts of southern India, especially Tamil Nadu, from the 6th century BCE to the 15th century CE. Chola Kingdom : Thanjavur was the capital, while Uraiyur served as the early capital. Kaveripatnam (Puhar) was an important port city of the Chola Kingdom.</p>",
                    solution_hi: "<p>5.(c) <strong>मदुरै</strong>। मेगस्थनीज : एक यूनानी राजनयिक और इतिहासकार था, जिन्होंने अपनी पुस्तक इंडिका में लगभग 300 ईसा पूर्व पांड्य साम्राज्य का उल्लेख किया था। पांड्य साम्राज्य एक प्राचीन तमिल साम्राज्य था, जिसने 6वीं शताब्दी ईसा पूर्व से 15वीं शताब्दी ईसा तक दक्षिणी भारत के कुछ हिस्सों, विशेष रूप से तमिलनाडु पर शासन किया था। चोल साम्राज्य : इसकी राजधानी तंजावुर थी, जबकि उरैयूर प्रारंभिक राजधानी के रूप में कार्य करता था। कावेरीपट्टनम (पुहार) चोल साम्राज्य का एक महत्वपूर्ण बंदरगाह शहर था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which of the following Sikh reform movements was started in 1873 in Amritsar?</p>",
                    question_hi: "<p>6. 1873 में अमृतसर में निम्नलिखित में से कौन-सा सिख सुधार आंदोलन आरंभ हुआ था?</p>",
                    options_en: ["<p>Babbar Akali Movement</p>", "<p>Akali Movement</p>", 
                                "<p>Singh Sabha Movement</p>", "<p>Gurudwara Movement</p>"],
                    options_hi: ["<p>बब्बर अकाली आंदोलन</p>", "<p>अकाली आंदोलन</p>",
                                "<p>सिंह सभा आंदोलन</p>", "<p>गुरुद्वारा आंदोलन</p>"],
                    solution_en: "<p>6.(c) <strong>Singh Sabha Movement</strong>. It was a reform organization for Sikhs, with the first Sabhas established in Amritsar in 1873 and Lahore in 1879. The movement aimed to eliminate superstitions, caste distinctions, and non-Sikh practices. It also promoted education among Sikhs by combining modern teaching with Sikh teachings. The Babbar Akali Movement: It was founded at the Sikh Educational Conference in Hoshiarpur in 1921. The Gurudwara Reform Movement (Akali Movement) was started in Amritsar in 1920.</p>",
                    solution_hi: "<p>6.(c) <strong>सिंह सभा आंदोलन</strong>। यह सिखों के लिए एक सुधार संगठन था, जिसकी पहली सभा 1873 में अमृतसर और 1879 में लाहौर में स्थापित की गई थी। इस आंदोलन का उद्देश्य अंधविश्वास, जाति भेद और गैर-सिख प्रथाओं को खत्म करना था। इसने सिख शिक्षाओं के साथ आधुनिक शिक्षण को जोड़कर सिखों के बीच शिक्षा को भी बढ़ावा दिया। बब्बर अकाली आंदोलन: इसकी स्थापना 1921 में होशियारपुर में सिख शैक्षिक सम्मेलन में की गई थी। गुरुद्वारा सुधार आंदोलन (अकाली आंदोलन) की शुरुआत 1920 में अमृतसर में हुई थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. Which of the following Acts introduced ‘provincial autonomy’ by discontinuing the application of dyarchy? ",
                    question_hi: "7.निम्नलिखित में से किस अधिनियम ने द्वैध शासन को समाप्त करके प्रांतीय स्वायत्तता (provincial autonomy) की शुरुआत की? ",
                    options_en: [" Government of India Act, 1919 ", " Charter Act of 1813 ", 
                                " India Council Act, 1909 ", " Government of India Act, 1935"],
                    options_hi: [" भारत सरकार अधिनियम, 1919 ", " 1813 का चार्टर एक्ट  ",
                                " भारत परिषद अधिनियम, 1909 ", " भारत सरकार अधिनियम, 1935"],
                    solution_en: "<p>7.(d) <strong>Government of India Act, 1935</strong>. The Act introduced responsible government in provinces, meaning the governor was required to act on the advice of ministers responsible to the provincial legislature. Government of India Act, 1919: Introduced dyarchy. Charter Act of 1813: Ended the East India Company\'s trade monopoly and allowed Christian missionaries to promote moral and religious reforms in India. India Council Act, 1909 (Morley-Minto Reforms): Introduced a system of communal representation for Muslims by accepting the concept of \'separate electorate\'.</p>",
                    solution_hi: "<p>7.(d) <strong>भारत सरकार अधिनियम, 1935</strong>. इस अधिनियम ने प्रांतों में उत्तरदायी सरकार की शुरुआत की, जिसका अर्थ था कि गवर्नर को प्रांतीय विधानमंडल के प्रति उत्तरदायी मंत्रियों की सलाह पर कार्य करना आवश्यक था। भारत सरकार अधिनियम, 1919: द्वैध शासन की शुरुआत की गई। 1813 का चार्टर अधिनियम: ईस्ट इंडिया कंपनी के व्यापार एकाधिकार को समाप्त कर दिया गया और ईसाई मिशनरियों को भारत में नैतिक और धार्मिक सुधारों को बढ़ावा देने की अनुमति दी गई। भारत परिषद अधिनियम, 1909 (मोर्ले-मिंटो सुधार): \'पृथक निर्वाचन क्षेत्र\' की अवधारणा को स्वीकार करके मुसलमानों के लिए सांप्रदायिक प्रतिनिधित्व की एक प्रणाली शुरू की गई।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. In which of the following Major Rock Edicts of Ashoka is the Kalinga war mentioned?</p>",
                    question_hi: "<p>8. निम्नलिखित में से अशोक के किस प्रमुख शिलालेख में कलिंग युद्ध का उल्लेख किया गया है?</p>",
                    options_en: ["<p>XII</p>", "<p>X</p>", 
                                "<p>XIII</p>", "<p>XI</p>"],
                    options_hi: ["<p>बारहवें</p>", "<p>दसवें</p>",
                                "<p>तेरहवें</p>", "<p>ग्यारहवें</p>"],
                    solution_en: "<p>8.(c) <strong>XIII</strong>. Major Rock Edict XIII of Ashoka discusses the Kalinga War, expressing remorse for the loss of life and suffering it caused. It announces his conversion to Buddhism and commitment to non-violence, highlighting the importance of Dharma (righteous living). The Kalinga War (261 BCE) marked a pivotal transformation in Ashoka, turning him into a champion of peace and Buddhism. Major Rock Edict XII: Deals with Ashoka\'s tolerance and respect for other religions. Major Rock Edict X : Discusses Ashoka\'s duties as a ruler and his commitment to Dharma. Major Rock Edict XI : Explains the principles of Dharma and moral conduct.</p>",
                    solution_hi: "<p>8.(c) <strong>तेरहवें</strong>। अशोक के प्रमुख शिलालेख तेरहवें में कलिंग युद्ध की चर्चा की गई है, जिसमें जीवन की नाश और इसके कारण हुई पीड़ा के लिए खेद व्यक्त किया गया है। इसमें उनके बौद्ध धर्म अपनाने और अहिंसा के प्रति प्रतिबद्धता की घोषणा की गई है, जिसमें धर्म (धार्मिक जीवन) के महत्व पर प्रकाश डालता है। कलिंग युद्ध (261 ई.पू.) ने अशोक में एक महत्वपूर्ण परिवर्तन ला दिया, जिसने उन्हें शांति और बौद्ध धर्म का समर्थक बना दिया। प्रमुख शिलालेख XII : अशोक की सहिष्णुता और अन्य धर्मों के प्रति सम्मान से संबंधित है। प्रमुख शिलालेख X : एक शासक के रूप में अशोक के कर्तव्यों और धर्म के प्रति उनकी प्रतिबद्धता पर चर्चा करता है। प्रमुख शिलालेख XI : धर्म और नैतिक आचरण के सिद्धांतों की व्याख्या करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Khudai Khidmatgar, a voluntary organisation, was established by which of the following leaders of India?</p>",
                    question_hi: "<p>9. भारत के निम्नलिखित में से किस नेता द्वारा एक स्वैच्छिक संगठन - खुदाई खिदमतगार स्थापित किया गया था?</p>",
                    options_en: ["<p>Muhammad Ali Jinnah</p>", "<p>Khan Abdul Ghaffar Khan</p>", 
                                "<p>Hakim Ajmal Khan</p>", "<p>Maulana Muhammad Ali</p>"],
                    options_hi: ["<p>मुहम्मद अली जिन्ना</p>", "<p>खान अब्दुल गफ्फार खान</p>",
                                "<p>हकीम अज़मल खान</p>", "<p>मौलाना मुहम्मद अली</p>"],
                    solution_en: "<p>9.(b) <strong>Khan Abdul Ghaffar Khan,</strong> also known as Badshah Khan or \"Frontier Gandhi,\" founded the Khudai Khidmatgar (\"Servants of God\") movement in 1929. This group of Pashtuns used non-violent methods to fight against British rule. Maulana Muhammad Ali was a prominent Indian Muslim leader, journalist, and scholar who played a key role in the Khilafat movement. Muhammad Ali Jinnah was the leader of the All-India Muslim League and later became the Governor-General of Pakistan. He is honored with the title \'Quaid-i-Azam.\'</p>",
                    solution_hi: "<p>9.(b)<strong> खान अब्दुल गफ्फार खान, </strong>जिन्हें बादशाह खान या \"सीमांत गांधी\" के नाम से भी जाना जाता है, इन्होंने 1929 में खुदाई खिदमतगार (\"ईश्वर के सेवक\") आंदोलन की शुरुआत की थी। पश्तूनों के इस समूह ने ब्रिटिश शासन के खिलाफ लड़ने के लिए अहिंसक तरीकों का प्रयोग किया था। मौलाना मुहम्मद अली एक प्रमुख भारतीय मुस्लिम नेता, पत्रकार और विद्वान थे जिन्होंने खिलाफत आंदोलन में महत्वपूर्ण भूमिका निभाई थी। मुहम्मद अली जिन्ना अखिल भारतीय मुस्लिम लीग के नेता थे और बाद में पाकिस्तान के गवर्नर-जनरल बने। उन्हें \'कायदे-ए-आजम\' की उपाधि से सम्मानित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Due to which of the following events did Rabindranath Tagore return the title of &lsquo;Knighthood&rsquo; to the British Government while expressing his anguish ?</p>",
                    question_hi: "<p>10. निम्नलिखित में से किस घटना के कारण रवीन्द्रनाथ टैगोर ने अपनी पीड़ा व्यक्त करते हुए ब्रिटिश सरकार को \'नाइटहुड\' की उपाधि लौटा दी थी?</p>",
                    options_en: ["<p>Charan Paduka Incident</p>", "<p>Partition of Bengal</p>", 
                                "<p>Jallianwala Bagh massacre</p>", "<p>Chauri Chaura Incident</p>"],
                    options_hi: ["<p>चरण पादुका प्रसंग</p>", "<p>बंगाल का विभाजन</p>",
                                "<p>जलियांवाला बाग हत्याकांड</p>", "<p>चौरी चौरा कांड</p>"],
                    solution_en: "<p>10.(c) <strong>Jallianwala Bagh massacre</strong>. It was a tragic event that occurred on April 13, 1919, in Amritsar during British colonial rule. British troops, under the command of General Reginald Dyer, opened fire on a large crowd of unarmed Indian civilians who had gathered at Jallianwala Bagh to peacefully protest the repressive Rowlatt Act. Chauri Chaura incident - It occurred at Chauri Chaura in the Gorakhpur district of the United Province, (modern Uttar Pradesh) in British India in 1922. Partition of Bengal - It was announced by the then Viceroy Lord Curzon in 1905.</p>",
                    solution_hi: "<p>10.(c) <strong>जलियाँवाला बाग हत्याकांड।</strong> यह 13 अप्रैल, 1919 को अमृतसर में ब्रिटिश औपनिवेशिक शासन के दौरान घटित एक दुखद घटना थी। ब्रिटिश सेना ने जनरल रेजिनाल डायर के नेतृत्व में, जालियाँवाला बाग में एकत्रित निर्दोष भारतीय नागरिकों की भीड़ पर अंधाधुंध गोलीबारी कर दी। ये लोग दमनकारी रोलेट एक्ट का विरोध करने के लिए शांतिपूर्ण प्रदर्शन कर रहे थे। चौरी चौरा कांड - यह 1922 में ब्रिटिश भारत के संयुक्त प्रांत (आधुनिक उत्तर प्रदेश) के गोरखपुर जिले के चौरी चौरा में घटित हुई थी। बंगाल का विभाजन - इसकी घोषणा तत्कालीन वायसराय लॉर्ड कर्जन ने 1905 में की थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. As per the Timurid tradition, Humayun had to share power with his brothers. Humayun had the control of Delhi, Agra and Central India, while his brother Kamran controlled which of the following regions?</p>",
                    question_hi: "<p>11. तैमूरी परंपरा के अनुसार, हुमायूँ को अपने भाइयों के साथ सत्ता साझा करनी थी। हुमायूँ का दिल्ली, आगरा और मध्य भारत पर नियंत्रण था, जबकि उसके भाई कामरान का नियंत्रण निम्नलिखित में से किस क्षेत्र पर था?</p>",
                    options_en: ["<p>Afghanistan and Punjab</p>", "<p>Deccan</p>", 
                                "<p>Gujarat and Rajasthan</p>", "<p>Bengal and Bihar</p>"],
                    options_hi: ["<p>अफगानिस्तान और पंजाब</p>", "<p>दक्कन</p>",
                                "<p>गुजरात और राजस्थान</p>", "<p>बंगाल और बिहार</p>"],
                    solution_en: "<p>11.(a) <strong>Afghanistan and Punjab.</strong> Kamran Mirza was the second son of Babur, the founder of the Mughal Empire. He was the brother of Humayun, who was born Nasir-ud-Din Muhammad in Kabul. Humayun was the second Emperor of the Mughal Empire, ruling over present-day Pakistan, Northern India, Afghanistan, and Bangladesh from 1530 to 1540 and again from 1555 to 1556.</p>",
                    solution_hi: "<p>11.(a)<strong> अफ़गानिस्तान और पंजाब।</strong> कामरान मिर्जा मुगल साम्राज्य के संस्थापक बाबर के दूसरे पुत्र थे। वे हुमायूँ के भाई थे, जिनका जन्म काबुल में नासिर-उद-दीन मुहम्मद के रूप में हुआ था । हुमायूँ मुगल साम्राज्य के दूसरे सम्राट थे, जिन्होंने वर्तमान पाकिस्तान, उत्तरी भारत, अफगानिस्तान और बांग्लादेश पर 1530 से 1540 तक और फिर 1555 से 1556 तक शासन किया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. During which of the following rules did Buddhism get split into two schools - Hinayana and Mahayana in the fourth Buddhist council?</p>",
                    question_hi: "<p>12. निम्नलिखित में से किस शासन के दौरान चतुर्थ बौद्ध संगीति में बौद्ध धर्म दो विचारधाराओं - हीनयान और महायान में विभाजित हो गया?</p>",
                    options_en: ["<p>Kushana</p>", "<p>Parthian</p>", 
                                "<p>Gupta</p>", "<p>Shaka</p>"],
                    options_hi: ["<p>कुषाण</p>", "<p>पार्थियन</p>",
                                "<p>गुप्त</p>", "<p>शक</p>"],
                    solution_en: "<p>12.(a) <strong>Kushana</strong>. The Fourth Buddhist Council was held in 72 AD at Kundalvana, Kashmir, under the Kushan king Kanishka. It was led by Vasumitra, with Asvaghosa as his deputy, and marked the division of Buddhism into two sects: Mahayana and Hinayana. Hinayana is a belief system without a deity, where karma takes the place of God, and its oldest school is Sthaviravada. Mahayana, known as the Greater Wheel, was founded by Nagarjuna. Its followers believed that salvation could be attained with the help of Buddha and Bodhisattvas.</p>",
                    solution_hi: "<p>12.(a) <strong>कुषाण</strong>। चौथी बौद्ध परिषद 72 ई. में कुषाण राजा कनिष्क के शासनकाल में कश्मीर के कुंडलवन में आयोजित की गई थी। इसका नेतृत्व वसुमित्र ने किया था, अश्वघोष उनके प्रतिनिधि थे, और इसने बौद्ध धर्म को दो संप्रदायों में विभाजित कर दिया: महायान और हीनयान। हीनयान एक ऐसा विश्वास तंत्र है जिसमें कोई देवता नहीं है, जहाँ कर्म ईश्वर का स्थान ले लेता है, और इसका सबसे पुराना संप्रदाय स्थविरवाद है। महायान, जिसे महान चक्र के रूप में जाना जाता है, की स्थापना नागार्जुन ने की थी। इसके अनुयायियों का मानना ​​था कि बुद्ध और बोधिसत्वों की मदद से मोक्ष प्राप्त किया जा सकता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. In peninsular India, which of the following kingdoms was the local power that ruled over northern Maharashtra and Vidarbha?</p>",
                    question_hi: "<p>13. प्रायद्वीपीय भारत में, निम्नलिखित में से कौन-सा साम्राज्य उत्तरी महाराष्ट्र और विदर्भ पर शासन करने वाली स्थानीय शक्ति थी?</p>",
                    options_en: ["<p>Vakatakas</p>", "<p>Maukharis</p>", 
                                "<p>Maitrakas</p>", "<p>Chalukyas</p>"],
                    options_hi: ["<p>वाकाटक</p>", "<p>मौखरि</p>",
                                "<p>मैत्रक</p>", "<p>चालुक्य</p>"],
                    solution_en: "<p>13.(a) <strong>Vakatakas</strong>. Vindhyashakti founded the Vakataka dynasty in the third century. Their history is mainly known through land grant charters issued to Brahmanas. The Maukhari dynasty ruled over (modern day) Uttar Pradesh and parts of Bihar. Maitraka dynasty - Gujarat and Saurashtra (Kathiawar). Chalukya dynasty - ruled large parts of southern and central India.</p>",
                    solution_hi: "<p>13.(a) <strong>वाकाटक</strong>। विंध्यशक्ति ने तीसरी शताब्दी में वाकाटक वंश की स्थापना की। उनका इतिहास मुख्य रूप से ब्राह्मणों को जारी किए गए भूमि अनुदान घोषणापत्र के माध्यम से जाना जाता था। मौखरी वंश ने (आधुनिक समय) उत्तर प्रदेश और बिहार के कुछ हिस्सों पर शासन किया। मैत्रक वंश - गुजरात और सौराष्ट्र (कच्छ)। चालुक्य वंश - दक्षिणी और मध्य भारत के बड़े हिस्से पर शासन किया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. In which of the following years was VD Savarkar sent to Andaman Jail by the British?</p>",
                    question_hi: "<p>14. निम्नलिखित में से किस वर्ष में वी.डी. सावरकर को अंग्रेजों द्वारा अंडमान जेल भेजा गया था?</p>",
                    options_en: ["<p>1910</p>", "<p>1922</p>", 
                                "<p>1923</p>", "<p>1911</p>"],
                    options_hi: ["<p>1910</p>", "<p>1922</p>",
                                "<p>1923</p>", "<p>1911</p>"],
                    solution_en: "<p>14.(d) <strong>1911</strong>. Vinayak Damodar Savarkar was sent to the Cellular Jail on the punishment of \'Kala Pani&rsquo; under the Nashik Conspiracy Case for the murder of the Collector of Nashik District, Jackson. While serving his sentence in the Cellular Jail, he wrote the epic \'Kamala\' on the prison walls.</p>",
                    solution_hi: "<p>14.(d) <strong>1911</strong>. विनायक दामोदर सावरकर को नासिक जिले के कलेक्टर जैक्सन की हत्या के लिए नासिक षडयंत्र केस के तहत \'काला पानी\' की सजा पर सेलुलर जेल में भेजा गया था। सेलुलर जेल में अपनी सजा काटते हुए उन्होंने जेल की दीवारों पर महाकाव्य \'कमला\' लिखा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In 10<sup>th</sup> century India, Queen Didda became the ruler of which of the following regions of north India?</p>",
                    question_hi: "<p>15. 10वीं शताब्दी में भारत में महारानी दिद्दा उत्तर भारत के निम्नलिखित में से किस क्षेत्र की शासिका बनीं?</p>",
                    options_en: ["<p>Kannauj</p>", "<p>Malwa</p>", 
                                "<p>Delhi</p>", "<p>Kashmir</p>"],
                    options_hi: ["<p>कन्नौज</p>", "<p>मालवा</p>",
                                "<p>दिल्ली</p>", "<p>कश्मीर</p>"],
                    solution_en: "<p>15.(d) <strong>Kashmir</strong>. Didda was the ruler of Kashmir from 980 CE to 1003 CE. Most knowledge relating to Didda is obtained from the Rajatarangini. Ashish Kaul\'s novel Didda: The Warrior Queen of Kashmir is based on Didda\'s life.</p>",
                    solution_hi: "<p>15.(d) <strong>कश्मीर</strong>। दिद्दा 980 ई. से 1003 ई. तक कश्मीर की शासक थीं। दिद्दा से संबंधित अधिकांश ज्ञान राजतरंगिणी से प्राप्त होता है। आशीष कौल का उपन्यास दिद्दा: द वॉरियर क्वीन ऑफ कश्मीर दिद्दा के जीवन पर आधारित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>