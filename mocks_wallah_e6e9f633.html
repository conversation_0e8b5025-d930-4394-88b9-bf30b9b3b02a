<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The ratio of per kg market price of rice to that of wheat flour is 7 : 9 and the ratio of their quantities consumed by a family is 9 : 11. Find the ratio of the family&rsquo;s expenditure on rice to that on wheat flour.</p>",
                    question_hi: "<p>1. चावल के प्रति किलोग्राम बाजार मूल्य और गेहूं के आटे के प्रति किलोग्राम बाजार मूल्य का अनुपात 7:9 है और एक परिवार द्वारा खपत की गयी उनकी मात्रा का अनुपात 9:11 है। चावल पर परिवार के खर्च का गेहूं के आटे पर खर्च का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>7 : 9</p>", "<p>7 : 11</p>", 
                                "<p>11 : 9</p>", "<p>9 : 11</p>"],
                    options_hi: ["<p>7 : 9</p>", "<p>7 : 11</p>",
                                "<p>11 : 9</p>", "<p>9 : 11</p>"],
                    solution_en: "<p>1.(b)<br>Expenditure = Price per kg &times; Quantity consumed<br>Expenditure on rice = 7 &times; 9 unit<br>Expenditure on wheat = 9 &times; 11 unit<br>Required ratio = 7 &times; 9 : 9 &times; 11 = 7 : 11</p>",
                    solution_hi: "<p>1.(b)<br>व्यय = प्रति किलो कीमत &times; खपत की गई मात्रा<br>चावल पर व्यय = 7 &times; 9 इकाई<br>गेहूँ पर व्यय = 9 &times; 11 इकाई<br>आवश्यक अनुपात = 7 &times; 9 : 9 &times; 11 = 7 : 11</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The number of marbles in three bags is in the ratio of 2 : 3 : 4. If 12 marbles are added to each bag, then the ratio changed to 8 : 11 : 14. The total number of marbles in the beginning was:</p>",
                    question_hi: "<p>2. तीन थैलों में कंचों की संख्या का अनुपात 2 : 3 : 4 है। यदि प्रत्येक थैले में 12 कंचे और डाले जाते हैं, तो अनुपात बदलकर 8 : 11 : 14 हो जाता है। शुरुआत में कंचों की कुल संख्या कितनी थी ?</p>",
                    options_en: ["<p>216</p>", "<p>162</p>", 
                                "<p>200</p>", "<p>126</p>"],
                    options_hi: ["<p>216</p>", "<p>162</p>",
                                "<p>200</p>", "<p>126</p>"],
                    solution_en: "<p>2.(b)<br>Let marbles initially in each bag = 2x&nbsp;, 3x , 4x<br>After adding 12 marbles to each bag<br>Marbles in each bag = 8<math display=\"inline\"><mi>y</mi></math> , 11y , 14y<br>According to the question,<br>2x&nbsp;+ 12 = 8y <br>8y&nbsp;- 2x = 12-------(i)<br>And, 3<math display=\"inline\"><mi>x</mi></math> + 12 = 11y <br>11y&nbsp;- 3x = 12-------(ii)<br>By the equation (i) &times; 3 - (ii) &times; 2 we get;<br>24y&nbsp;- 6x - (22y - 6x) = 36 - 24<br>2y&nbsp;= 12 &rArr; y = 6 <br>Putting the value of <math display=\"inline\"><mi>y</mi></math> in equation (i) we get;<br>8 &times; (6) - 2x&nbsp;= 12<br>2x&nbsp;= 48 - 12 = 36<br>x = 18<br>Now, total marbles initially has = (2x&nbsp;+ 3x + 4x) = 9x<br>= 9 &times; 18 = 162</p>",
                    solution_hi: "<p>2.(b)<br>मान लीजिए प्रत्येक थैलों में प्रारंभ में कंचे = 2<math display=\"inline\"><mi>x</mi></math> , 3x , 4x <br>प्रत्येक थैलों में 12 कंचे डालने के बाद<br>प्रत्येक थैले में कंचे = 8<math display=\"inline\"><mi>y</mi></math> , 11y , 14y<br>प्रश्न के अनुसार,<br>2x&nbsp;+ 12 = 8y <br>8y&nbsp;- 2x = 12-------(i)<br>और, 3<math display=\"inline\"><mi>x</mi></math> + 12 = 11y <br>11y&nbsp;- 3x = 12-------(ii)<br>समीकरण (i) &times; 3 - (ii) &times; 2 से हमें प्राप्त होता है;<br>24y&nbsp;- 6x - (22y - 6x) = 36 - 24<br>2y&nbsp;= 12 &rArr; y = 6 <br>समीकरण (i) में y का मान रखने पर हमें प्राप्त होता है;<br>8 &times; (6) - 2x&nbsp;= 12<br>2x&nbsp;= 48 - 12 = 36<br>x = 18<br>अब, प्रारंभ में कुल कंचों की संख्या = (2x&nbsp;+ 3x + 4x) = 9x<br>= 9 &times; 18 = 162</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. If 30% of P = 0.6 of Q = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of R, then P : Q : R is:</p>",
                    question_hi: "<p>3. यदि P का 30% = Q का 0.6 = R का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> है, तो P : Q : R है:</p>",
                    options_en: ["<p>10 : 5 : 9</p>", "<p>10 : 5 : 7</p>", 
                                "<p>5 : 10 : 9</p>", "<p>5 : 10 : 7</p>"],
                    options_hi: ["<p>10 : 5 : 9</p>", "<p>10 : 5 : 7</p>",
                                "<p>5 : 10 : 9</p>", "<p>5 : 10 : 7</p>"],
                    solution_en: "<p>3.(a) 30% of P = 0.6 of Q = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of R<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; P = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>10</mn></mfrac></math> &times; Q = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>3</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>P</mi></mrow><mrow><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>Q</mi></mrow><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>3</mn></mfrac></math><br>P : Q : R = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>6</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>1</mn></mfrac></math><br>P : Q : R = 10 : 5 : 9</p>",
                    solution_hi: "<p>3.(a) P का 30% = Q का 0.6 = R का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; P = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>10</mn></mfrac></math> &times; Q = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>3</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>P</mi></mrow><mrow><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>Q</mi></mrow><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>3</mn></mfrac></math><br>P : Q : R = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>6</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>1</mn></mfrac></math><br>P : Q : R = 10 : 5 : 9</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The ratio of the I class and II class AC train coach fares between two stations is 5 : 3, and that of the number of passengers travelling between the two stations by I class and II classes AC coach is 3 : 10. If on a particular day ₹37,080 is collected from passengers travelling between the two stations, then find the amount collected from second class AC coach passengers.</p>",
                    question_hi: "<p>4. दो स्टेशनों के बीच श्रेणी I और श्रेणी II एसी ट्रेन कोच के किराए का अनुपात 5 : 3 है, और श्रेणी I और श्रेणी II एसी कोच में दो स्टेशनों के बीच यात्रा कर रहे यात्रियों की संख्या का अनुपात 3 : 10 है। यदि किसी दिन दोनों स्टेशनों के बीच यात्रा कर रहे यात्रियों से ₹37,080 वसूल किए जाते हैं, तो श्रेणी II एसी कोच के यात्रियों से वसूल की गई धनराशि की गणना कीजिए।</p>",
                    options_en: ["<p>₹24,720</p>", "<p>₹27,420</p>", 
                                "<p>₹20,247</p>", "<p>₹22,047</p>"],
                    options_hi: ["<p>₹24,720</p>", "<p>₹27,420</p>",
                                "<p>₹20,247</p>", "<p>₹22,047</p>"],
                    solution_en: "<p>4.(a)<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp;Class I : Class II<br>Fares&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;3&nbsp;<br>Passengers &rarr;&nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 10<br>-------------------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 15&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;30<br>&rArr; (15 + 30) unit = ₹37080<br>&rArr; 1 unit = 824<br>&rArr; 30unit = 824 &times; 30 = ₹24,720</p>",
                    solution_hi: "<p>4.(a)<br>अनुपात &rarr;&nbsp; श्रेणी I : श्रेणी II<br>किराए&nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; :&nbsp; &nbsp;3 <br>यात्रियों&nbsp; &rarr;&nbsp; &nbsp; &nbsp;3&nbsp; &nbsp; :&nbsp; 10<br>-------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 15&nbsp; &nbsp; :&nbsp; 30<br>&rArr; (15 + 30) इकाई = ₹37080<br>&rArr; 1 इकाई = 824<br>&rArr; 30इकाई = 824 &times; 30 = ₹24,720</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If 5 + x, 2x + 7, 6x + 9 and y are in proportion when x = 2,then find the value of y.</p>",
                    question_hi: "<p>5. यदि x = 2 होने पर 5 + x, 2x + 7, 6x + 9 तथा y समानुपात में हैं, तो y का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>45</p>", "<p>28</p>", 
                                "<p>33</p>", "<p>42</p>"],
                    options_hi: ["<p>45</p>", "<p>28</p>",
                                "<p>33</p>", "<p>42</p>"],
                    solution_en: "<p>5.(c) According to question,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>+</mo><mi>x</mi></mrow><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>x</mi><mo>+</mo><mn>9</mn></mrow><mi>y</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>+</mo><mn>2</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mn>9</mn></mrow><mi>y</mi></mfrac></math><br>7y = 21 &times; 11 <br><math display=\"inline\"><mi>y</mi></math> = 33</p>",
                    solution_hi: "<p>5.(c) प्रश्न के अनुसार,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>+</mo><mi>x</mi></mrow><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>x</mi><mo>+</mo><mn>9</mn></mrow><mi>y</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>+</mo><mn>2</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mn>9</mn></mrow><mi>y</mi></mfrac></math><br>7y = 21 &times; 11 <br><math display=\"inline\"><mi>y</mi></math> = 33</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If P (P &gt; 0) is the least possible number that must be subtracted from 7, 9, and 12 so that the resulting numbers are in continued proportion, then find the value of P<sup>2</sup> + 1.</p>",
                    question_hi: "<p>6. यदि P (P &gt; 0) वह न्यूनतम संभव संख्या है जिसे 7, 9 और 12 में से घटाए जाने पर परिणामी संख्याएँ सतत अनुपात में होंगी, तो P<sup>2</sup> + 1 का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>10</p>", "<p>12</p>", 
                                "<p>3</p>", "<p>8</p>"],
                    options_hi: ["<p>10</p>", "<p>12</p>",
                                "<p>3</p>", "<p>8</p>"],
                    solution_en: "<p>6.(a) If a b and c are in continue proportion then it can be write as <br>a : b :: b : c<br>&rArr; b<sup>2 </sup>= ac&nbsp;<br>Let least possible number be P and subtracted from each other <br>&rArr; (9 - P)<sup>2</sup> = (7 - P) (12 - P)<br>&rArr; P = 3<br>&there4; P<sup>2</sup> + 1 = 9 + 1 = 10</p>",
                    solution_hi: "<p>6.(a) यदि a b और c निरंतर अनुपात में हैं तो इसे इस प्रकार लिखा जा सकता है&nbsp;<br>a : b :: b : c<br>&rArr; b<sup>2 </sup>= ac&nbsp;<br>माना न्यूनतम संभव संख्या P है जिसे एक दूसरे से घटाया गया है <br>&rArr; (9 - P)<sup>2</sup> = (7 - P) (12 - P)<br>&rArr; P = 3<br>&there4; P<sup>2</sup> + 1 = 9 + 1 = 10</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Aman, Bheema and Rehman play cricket, the ratio of Aman\'s runs to Bheema\'s runs and Bheema\'s runs to Rehman\'s runs is 5 : 4. They make together 549 runs. How many runs did Rehman make ?</p>",
                    question_hi: "<p>7. अमन, भीम और रहमान क्रिकेट खेलते हैं, अमन के रन और भीम के रन तथा भीम के रन और रहमान के रन का अनुपात 5 : 4 है। वे मिलकर 549 रन बनाते हैं। रहमान ने कितने रन बनाए ?</p>",
                    options_en: ["<p>132</p>", "<p>158</p>", 
                                "<p>108</p>", "<p>144</p>"],
                    options_hi: ["<p>132</p>", "<p>158</p>",
                                "<p>108</p>", "<p>144</p>"],
                    solution_en: "<p>7.(d)<br>Ratio&nbsp; &rarr;&nbsp; Aman : Bheema : Rehman<br>Runs&nbsp; &rarr;&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;4 <br>Runs&nbsp; &rarr;&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;4<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;-----------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 16 <br>According to question,<br>&rArr; 61 units = 549<br>&rArr; 16 units = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>549</mn><mn>61</mn></mfrac></math> &times; 16 = 144 <br>Hence, Rehman makes runs 144</p>",
                    solution_hi: "<p>7.(d)<br>अनुपात&nbsp; &rarr; अमन : भीम : रहमान<br>रन&nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; 5&nbsp; &nbsp; :&nbsp; &nbsp;4&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;4 <br>रन&nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; 5&nbsp; &nbsp; :&nbsp; &nbsp;5&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;4<br>&mdash;----------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp;:&nbsp; 20&nbsp; :&nbsp; &nbsp; 16 <br>प्रश्न के अनुसार,<br>&rArr; 61 इकाई = 549<br>&rArr; 16 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>549</mn><mn>61</mn></mfrac></math> &times; 16 = 144<br>अत: रहमान 144 रन बनाता है</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. A bag contains coins of 1 rupee, 0.5 rupee and 0.25 rupee in the ratio of 5 : 6 : 8. If the total amount of money in the bag is ₹420, find the number of coins of type1 rupee, 0.5 rupee and 0.25 rupee are:</p>",
                    question_hi: "<p>8. एक बैग में 5 : 6 : 8 के अनुपात में 1 रुपये, 0.5 रुपये और 0.25 रुपये के सिक्के हैं। यदि बैग में कुल धनराशि ₹420 है, तो 1 रुपये, 0.5 रुपये और 0.25 रुपये के प्रकार के सिक्कों की संख्या क्रमशः ज्ञात कीजिए।</p>",
                    options_en: ["<p>212,232,312</p>", "<p>210,252,336</p>", 
                                "<p>216,252,292</p>", "<p>208,272,352</p>"],
                    options_hi: ["<p>212,232,312</p>", "<p>210,252,336</p>",
                                "<p>216,252,292</p>", "<p>208,272,352</p>"],
                    solution_en: "<p>8.(b) Ratio &rarr; ₹1 : ₹0.5 : ₹0.25<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5 :&nbsp; &nbsp; 6&nbsp; &nbsp;:&nbsp; &nbsp; 8<br>According to question,<br>&rArr; 5x &times; 1 + 6x &times; 0.5 + 8x &times; 0.25 = ₹ 420<br>&rArr; 10x = 420 &rArr; x = ₹ 42<br>Hence, <br>(₹1, ₹0.5, ₹0.25)<br>= 5 &times; 42, 6 &times; 42, 8 &times; 42 = 210, 252, 336</p>",
                    solution_hi: "<p>8.(b) अनुपात &rarr; ₹1 : ₹0.5 : ₹0.25<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; :&nbsp; &nbsp; 6&nbsp; &nbsp;:&nbsp; &nbsp; 8 <br>प्रश्न के अनुसार,<br>&rArr; 5x &times; 1 + 6x &times; 0.5 + 8x &times; 0.25 = ₹ 420<br>&rArr; 10x = 420 &rArr; x = ₹ 42<br>अतः <br>(₹1, ₹0.5, ₹0.25)<br>= 5 &times; 42, 6 &times; 42, 8 &times; 42 = 210, 252, 336</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The ratio of the number of boys to that of girls in a school is 7 : 9. If the number of girls in the school is 189, then the number of boys in that school is:</p>",
                    question_hi: "<p>9. एक स्कूल में लड़कों की संख्या का लड़कियों की संख्या से अनुपात 7 : 9 है। यदि स्कूल में लड़कियों की संख्या 189 है, तो उस स्कूल में लड़कों की संख्या कितनी है ?</p>",
                    options_en: ["<p>105</p>", "<p>147</p>", 
                                "<p>168</p>", "<p>126 </p>"],
                    options_hi: ["<p>105</p>", "<p>147</p>",
                                "<p>168</p>", "<p>126</p>"],
                    solution_en: "<p>9.(b) According to question,<br>&rArr; 9 units = 189 <br>&rArr; 7 units = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>189</mn><mn>9</mn></mfrac></math> &times; 7 = 147<br>Hence, Number of boys is 147</p>",
                    solution_hi: "<p>9.(b) प्रश्न के अनुसार,<br>&rArr; 9 इकाई = 189 <br>&rArr; 7 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>189</mn><mn>9</mn></mfrac></math> &times; 7 = 147<br>अतः, लड़कों की संख्या 147 है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. The resistance of a wire is directly proportional to its length and inversely proportional to the square of its radius. Two wires of the same material have the same resistance and their radii are in the ratio of 7 : 8. The corresponding lengths of the wires are 147 cm and L cm, respectively. Find the value of L.</p>",
                    question_hi: "<p>10. किसी तार का प्रतिरोध उसकी लंबाई के अनुक्रमानुपाती और उसकी त्रिज्या के वर्ग के व्युत्क्रमानुपाती है। एक ही पदार्थ के दो तारों का प्रतिरोध समान है और उनकी त्रिज्याएँ 7 : 8 के अनुपात में हैं। तारों की संगत लंबाई क्रमशः 147 cm और L cm है। L का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>168</p>", "<p>192</p>", 
                                "<p>128</p>", "<p>158</p>"],
                    options_hi: ["<p>168</p>", "<p>192</p>",
                                "<p>128</p>", "<p>158</p>"],
                    solution_en: "<p>10.(b) According to question <br>Resistance (R) &prop; <math display=\"inline\"><mfrac><mrow><mi>L</mi><mi>e</mi><mi>n</mi><mi>g</mi><mi>t</mi><mi>h</mi><mi>&#160;</mi></mrow><mrow><msup><mrow><mi>r</mi><mi>a</mi><mi>d</mi><mi>i</mi><mi>u</mi><mi>s</mi><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> <br>R = k<math display=\"inline\"><mfrac><mrow><mi>l</mi></mrow><mrow><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> &hellip;. (i)<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>r</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>k</mi><mo>&#160;</mo><mo>(</mo><mn>147</mn><mo>)</mo></mrow><mn>49</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>k</mi><mo>(</mo><mi>L</mi><mo>)</mo></mrow><mn>64</mn></mfrac></math><br>&rArr; L = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>147</mn><mo>&#215;</mo><mn>64</mn></mrow><mn>49</mn></mfrac></math> = 192cm</p>",
                    solution_hi: "<p>10.(b) प्रश्नानुसार<br>प्रतिरोध (R) &prop; <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2354;&#2306;&#2348;&#2366;&#2312;</mi><msup><mi>&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</mi><mn>2</mn></msup></mfrac></math> <br>R = k<math display=\"inline\"><mfrac><mrow><mi>l</mi></mrow><mrow><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> &hellip;. (i)<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>r</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>k</mi><mo>&#160;</mo><mo>(</mo><mn>147</mn><mo>)</mo></mrow><mn>49</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>k</mi><mo>(</mo><mi>L</mi><mo>)</mo></mrow><mn>64</mn></mfrac></math><br>&rArr; L = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>147</mn><mo>&#215;</mo><mn>64</mn></mrow><mn>49</mn></mfrac></math> = 192cm</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Arman leaves ₹2,17,600 to be divided among four sons, three daughters and five nephews. If each daughter receives 3 times as much as each nephew and each son receives 5 times as much as each nephew, how much does each son receive ?</p>",
                    question_hi: "<p>11. अरमान ने चार पुत्रों, तीन पुत्रियों और पाँच भतीजों में बांटने के लिए ₹2,17,600 छोड़े हैं। यदि प्रत्येक पुत्री को प्रत्येक भतीजे से 3 गुना अधिक धनराशि मिलती है और प्रत्येक पुत्र को प्रत्येक भतीजे से 5 गुना अधिक धनराशि मिलती है, तो प्रत्येक पुत्र को कितनी धनराशि मिलेगी ?</p>",
                    options_en: ["<p>₹18,200</p>", "<p>₹32,000</p>", 
                                "<p>₹36,000</p>", "<p>₹25,600</p>"],
                    options_hi: ["<p>₹18,200</p>", "<p>₹32,000</p>",
                                "<p>₹36,000</p>", "<p>₹25,600</p>"],
                    solution_en: "<p>11.(b) Let amount of money received by each nephew = x <br>According to question , <br>Amount of money received by each son = 5x<br>Amount of money received by each daughter = 3x<br>Now , <br>&rArr; 4(5x) + 3 (3x) + 5(x) = 2,17,600<br>&rArr; 20x + 9x + 5x = 2,17,600<br>&rArr; 34x = 2,17,600&nbsp;<br>&rArr; x = 6400<br>&there4; Amount of money received by each son = 5 &times; 6400 = ₹32000</p>",
                    solution_hi: "<p>11.(b) माना प्रत्येक भतीजे को प्राप्त धनराशि = x <br>प्रश्न के अनुसार, <br>प्रत्येक पुत्र को प्राप्त धनराशि = 5x<br>प्रत्येक पुत्री को प्राप्त धनराशि = 3x<br>अब , <br>&rArr; 4(5x) + 3 (3x) + 5(x) = 2,17,600<br>&rArr; 20x + 9x + 5x = 2,17,600<br>&rArr; 34x = 2,17,600&nbsp;<br>&rArr; x = 6400<br>&there4; प्रत्येक पुत्र को प्राप्त धनराशि = 5 &times; 6400 = ₹32000</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. If the numbers X + 2, X + 5, 2X &minus; 3 and 3X &minus; 5 are in proportion, then which of the following pairs of values is possible for X ?</p>",
                    question_hi: "<p>12. यदि संख्याएँ X + 2, X + 5, 2X &minus; 3 और 3X &minus; 5 समानुपात में हैं, तो निम्नलिखित में से किस युग्म वाले मान X के लिए संभव है ?</p>",
                    options_en: ["<p>3 and 2</p>", "<p>5 and 3</p>", 
                                "<p>5 and 1</p>", "<p>4 and 2</p>"],
                    options_hi: ["<p>3 और 2</p>", "<p>5 और 3</p>",
                                "<p>5 और 1</p>", "<p>4 और 2</p>"],
                    solution_en: "<p>12.(c) According to question ,<br>&rArr;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>X</mi><mo>+</mo><mn>2</mn></mrow><mrow><mi>X</mi><mo>+</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>X</mi><mo>-</mo><mn>3</mn></mrow><mrow><mn>3</mn><mi>X</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math><br>&rArr; (X + 2) (3X - 5) = (2X - 3) (X + 5)<br>&rArr; X<sup>2</sup> - 6X + 5 = 0<br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mo>(</mo><mo>-</mo><mn>6</mn><mo>)</mo><mo>&#177;</mo><msqrt><msup><mrow><mo>(</mo><mo>-</mo><mn>6</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn><mo>&#215;</mo><mn>1</mn><mo>&#215;</mo><mn>5</mn></msqrt></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>1</mn></mrow></mfrac></math> <br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#177;</mo><msqrt><mn>36</mn><mo>-</mo><mn>20</mn></msqrt></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#177;</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> <br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>+</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>-</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> = 5 , 1</p>",
                    solution_hi: "<p>12.(c) प्रश्न के अनुसार,<br>&rArr;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>X</mi><mo>+</mo><mn>2</mn></mrow><mrow><mi>X</mi><mo>+</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>X</mi><mo>-</mo><mn>3</mn></mrow><mrow><mn>3</mn><mi>X</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math><br>&rArr; (X + 2) (3X - 5) = (2X - 3) (X + 5)<br>&rArr; X<sup>2</sup> - 6X + 5 = 0<br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mo>(</mo><mo>-</mo><mn>6</mn><mo>)</mo><mo>&#177;</mo><msqrt><msup><mrow><mo>(</mo><mo>-</mo><mn>6</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn><mo>&#215;</mo><mn>1</mn><mo>&#215;</mo><mn>5</mn></msqrt></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>1</mn></mrow></mfrac></math> <br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#177;</mo><msqrt><mn>36</mn><mo>-</mo><mn>20</mn></msqrt></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#177;</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> <br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>+</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>-</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> = 5 , 1</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If <math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>42</mn></msqrt></math> :: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math> : x, then what is the value of x ?</p>",
                    question_hi: "<p>13. यदि <math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>42</mn></msqrt></math> :: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math> : x है, तो x का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac><msqrt><mn>42</mn></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac><msqrt><mn>42</mn></msqrt></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac><msqrt><mn>30</mn></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac><msqrt><mn>30</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac><msqrt><mn>42</mn></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac><msqrt><mn>42</mn></msqrt></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac><msqrt><mn>30</mn></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac><msqrt><mn>30</mn></msqrt></math></p>"],
                    solution_en: "<p>13.(d) According to question,<br><math display=\"inline\"><mfrac><mrow><msqrt><mn>5</mn></msqrt></mrow><mrow><msqrt><mn>42</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>7</mn></msqrt><mi>x</mi></mfrac></math><br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>7</mn></msqrt><mo>&#215;</mo><msqrt><mn>42</mn></msqrt></mrow><msqrt><mn>5</mn></msqrt></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><msqrt><mn>6</mn></msqrt></mrow><msqrt><mn>5</mn></msqrt></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>5</mn></msqrt><msqrt><mn>5</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>30</mn></msqrt></math></p>",
                    solution_hi: "<p>13.(d) प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><msqrt><mn>5</mn></msqrt></mrow><mrow><msqrt><mn>42</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>7</mn></msqrt><mi>x</mi></mfrac></math><br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>7</mn></msqrt><mo>&#215;</mo><msqrt><mn>42</mn></msqrt></mrow><msqrt><mn>5</mn></msqrt></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><msqrt><mn>6</mn></msqrt></mrow><msqrt><mn>5</mn></msqrt></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>5</mn></msqrt><msqrt><mn>5</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>30</mn></msqrt></math></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A person divided a certain sum among his three sons in the ratio 3 : 4 : 5. Had he divided the sum in the ratio <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> the son, who got the least share earlier, would have got ₹1,188 more. The sum (in ₹) was:</p>",
                    question_hi: "<p>14. एक व्यक्ति ने एक निश्चित राशि को अपने तीन पुत्रों में 3 : 4 : 5 के अनुपात में विभाजित किया। यदि वह राशि को <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>&nbsp;के अनुपात में विभाजित करता, तो वह पुत्र, जिसे पहले सबसे कम हिस्सा मिलता, उसे ₹1,188 अधिक मिलते। राशि की गणना करें (₹ में)।</p>",
                    options_en: ["<p>6768</p>", "<p>6876</p>", 
                                "<p>6678</p>", "<p>6687</p>"],
                    options_hi: ["<p>6768</p>", "<p>6876</p>",
                                "<p>6678</p>", "<p>6687</p>"],
                    solution_en: "<p>14.(a) Initial ratio &rarr; 3 : 4 : 5<br>New the ratio &rarr;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> or 20 : 15 : 12<br>Share of the first son = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> &times; 12 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>47</mn></mfrac></math><br>According to question,<br>(<math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> - 3) units = 1188<br>12 units = <math display=\"inline\"><mfrac><mrow><mn>1188</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math> &times; 12 &times; 47 = 6768</p>",
                    solution_hi: "<p>14.(a) प्रारंभिक अनुपात &rarr; 3 : 4 : 5<br>नया अनुपात &rarr;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> या 20 : 15 : 12<br>पहले बेटे का हिस्सा = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> &times; 12 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>47</mn></mfrac></math><br>प्रश्न के अनुसार,<br>(<math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> - 3) इकाई = 1188<br>12 इकाई = <math display=\"inline\"><mfrac><mrow><mn>1188</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math> &times; 12 &times; 47 = 6768</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Three friends M, N and R divided an amount of ₹93,775 in the ratio 2:4:5. How much more money (in₹) does R has</p>",
                    question_hi: "<p>15. तीन मित्र M, N और R ने ₹93,775 की राशि 2 : 4 : 5 के अनुपात में बांटी। R के पास M से कितनी अधिक राशि (₹ में) है ?</p>",
                    options_en: ["<p>35,705</p>", "<p>25,575</p>", 
                                "<p>18,775</p>", "<p>41,560</p>"],
                    options_hi: ["<p>35,705</p>", "<p>25,575</p>",
                                "<p>18,775</p>", "<p>41,560</p>"],
                    solution_en: "<p>15.(b)<br>M : N : R = 2 : 4 : 5<br>R - M = 5 - 2 = 3 unit<br>11 unit = 93,775<br>3 unit = <math display=\"inline\"><mfrac><mrow><mn>93775</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 3<br>= 8525 &times; 3 = ₹ 25,575<br>Hence, R has ₹ 25,575 more money than M.</p>",
                    solution_hi: "<p>15.(b)<br>M : N : R = 2 : 4 : 5<br>R - M = 5 - 2 = 3 इकाई<br>11 इकाई = 93,775<br>3 इकाई =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>93775</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 3<br>= 8525 &times; 3 = ₹ 25,575<br>अतः, R के पास M से ₹ ​​25,575 अधिक धन है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>