<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "1. The price of 3 notebooks and 6 pen is Rs. 3000. With the same money one can buy a notebooks and 12 pen. Raju wants to buy 15 pen, how much will he have to pay ?",
                    question_hi: "1. 3 नोटबुक और 6 पेन का मूल्य 3000 रुपए है। उतने ही पैसों से एक व्यक्ति एक नोटबुक और 12 पेन खरीद सकता है। यदि राजू 15 पेन खरीदना चाहता है, तो उसे कितना भुगतान करना होगा ? ",
                    options_en: [" Rs. 3000", " Rs. 3500", 
                                " Rs. 2000", " Rs. 2500"],
                    options_hi: [" 3000 रुपए", " 3500 रुपए",
                                " 2000 रुपए", " 2500 रुपए"],
                    solution_en: "1.(a)<br />Let the CP of notebook and pen be N and P respectively<br />ATQ,<br />3N + 6P = 3,000 ---------- (1)<br />N + 12P = 3,000  ---------- (2)<br />From equation(1) and (2) we have ;<br />3N + 6P = N + 12P<br />2N = 6P<br />N = 3P<br />Putting N= 3P in equation(2) we have ;<br />3P + 12P = 3,000<br />15P = 3,000<br />So, the cost price of 15 pens = ₹3,000",
                    solution_hi: "1.(a)<br />माना नोटबुक और पेन का क्रय मूल्य क्रमशः N और P है<br />प्रश्न के अनुसार,<br />3N + 6P = 3,000 ---------- (1)<br />N + 12P = 3,000  ---------- (2)<br />समीकरण (1) और (2) से हमारे पास है;<br />3N + 6P = N + 12P<br />2N = 6P<br />N = 3P<br />समीकरण(2) में N = 3P रखने पर हमें प्राप्त होता है;<br />3P + 12P = 3,000<br />15P = 3,000<br />तो, 15 पेन का क्रय मूल्य = ₹3,000",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. What is the remainder when <math display=\"inline\"><msup><mrow><mn>35</mn></mrow><mrow><mn>29</mn></mrow></msup></math> is divided by 10 ?</p>",
                    question_hi: "<p>2. <math display=\"inline\"><msup><mrow><mn>35</mn></mrow><mrow><mn>29</mn></mrow></msup></math> को 10 से भाग देने पर शेषफल कितना होगा ?</p>",
                    options_en: ["<p>5</p>", "<p>4</p>", 
                                "<p>6</p>", "<p>0</p>"],
                    options_hi: ["<p>5</p>", "<p>4</p>",
                                "<p>6</p>", "<p>0</p>"],
                    solution_en: "<p>2.(a)<br>Rem(<math display=\"inline\"><mfrac><mrow><mn>3</mn><msup><mrow><mn>5</mn></mrow><mrow><mn>29</mn></mrow></msup></mrow><mrow><mn>10</mn></mrow></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>5</mn><mn>29</mn></msup><mn>10</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>5</mn><mo>&#215;</mo><msup><mn>5</mn><mn>28</mn></msup></mrow><mn>10</mn></mfrac><mo>=</mo><mfrac><msup><mn>5</mn><mn>28</mn></msup><mn>2</mn></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><msup><mn>1</mn><mn>28</mn></msup></mrow><mn>2</mn></mfrac><mo>=</mo><mo>&#160;</mo><mn>1</mn><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>5</mn></math></p>",
                    solution_hi: "<p>2.(a)<br>शेष(<math display=\"inline\"><mfrac><mrow><mn>3</mn><msup><mrow><mn>5</mn></mrow><mrow><mn>29</mn></mrow></msup></mrow><mrow><mn>10</mn></mrow></mfrac></math>)&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>5</mn><mn>29</mn></msup><mn>10</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>5</mn><mo>&#215;</mo><msup><mn>5</mn><mn>28</mn></msup></mrow><mn>10</mn></mfrac><mo>=</mo><mfrac><msup><mn>5</mn><mn>28</mn></msup><mn>2</mn></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><msup><mn>1</mn><mn>28</mn></msup></mrow><mn>2</mn></mfrac><mo>=</mo><mo>&#160;</mo><mn>1</mn><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>5</mn></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "3. Which of the following numbers is not divisible by 11 ?",
                    question_hi: "3. निम्नलिखित में से कौन-सी संख्या 11 से विभाज्य नहीं है ?",
                    options_en: [" 2763", " 2651 ", 
                                " 2728 ", " 2563"],
                    options_hi: [" 2763", " 2651 ",
                                " 2728 ", " 2563"],
                    solution_en: "3.(a) After checking all options one by one, only option (a) satisfied.<br /> “The given number can only be completely divided by 11 if the difference of the sum of digits at odd position and sum of digits at even position in a number is 0 or 11.”",
                    solution_hi: "3.(a) सभी विकल्पों को एक-एक करके जांचने के बाद, केवल विकल्प (a) संतुष्ट है।<br /> \"दी गई संख्या को केवल 11 से पूर्ण रूप से विभाजित किया जा सकता है यदि किसी संख्या में विषम स्थान पर अंकों के योग और सम स्थान पर अंकों के योग का अंतर 0 या 11 है।\"",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Which of the following numbers is divisible by 11?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन-सी संख्या 11 से विभाज्य है?</p>",
                    options_en: ["<p>6589</p>", "<p>9164</p>", 
                                "<p>8962</p>", "<p>4857</p>"],
                    options_hi: ["<p>6589</p>", "<p>9164</p>",
                                "<p>8962</p>", "<p>4857</p>"],
                    solution_en: "<p>4.(a)<br><strong>Divisibility rule of 11 :</strong> <br>The difference of the sum of alternate digits of the given number should be divisible by 11.<br>Checking all the options one by one, we get 6589 i.e.(6 + 8) - (9 + 5) = 0 , which is divisible by 11 <br>So, the correct option is (a)</p>",
                    solution_hi: "<p>4.(a)<br><strong>11 का विभाज्यता नियम:</strong> <br>दी गई संख्या के एकान्तर अंकों के योग का अंतर 11 से विभाज्य होना चाहिए।.<br>सभी विकल्पों को एक-एक करके जांचने पर हमें 6589 मिलता है यानी (6 + 8) - (9 + 5) = 0, जो 11 से विभाज्य है<br>तो, सही विकल्प (a) है |</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. What is the remainder when 58<sup>29</sup> is divided by 5?</p>",
                    question_hi: "<p>5. 58<sup>29</sup> को 5 से भाग देने पर शेषफल कितना होगा?</p>",
                    options_en: ["<p>7</p>", "<p>3</p>", 
                                "<p>2</p>", "<p>4</p>"],
                    options_hi: ["<p>7</p>", "<p>3</p>",
                                "<p>2</p>", "<p>4</p>"],
                    solution_en: "<p>5.(b)<br>Rem(<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mi>&#160;</mi><mn>5</mn><msup><mrow><mn>8</mn></mrow><mrow><mn>29</mn></mrow></msup></mrow><mrow><mn>5</mn></mrow></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>3</mn><mn>29</mn></msup><mn>5</mn></mfrac><mo>=</mo><mfrac><mrow><mn>3</mn><mo>&#215;</mo><msup><mfenced><msup><mn>3</mn><mn>4</mn></msup></mfenced><mn>7</mn></msup></mrow><mn>5</mn></mfrac><mo>=</mo><mfrac><mrow><mn>3</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>5</mn></mfrac><mo>=</mo><mn>3</mn></math></p>",
                    solution_hi: "<p>5.(b)<br>शेषफल(<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mi>&#160;</mi><mn>5</mn><msup><mrow><mn>8</mn></mrow><mrow><mn>29</mn></mrow></msup></mrow><mrow><mn>5</mn></mrow></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>3</mn><mn>29</mn></msup><mn>5</mn></mfrac><mo>=</mo><mfrac><mrow><mn>3</mn><mo>&#215;</mo><msup><mfenced><msup><mn>3</mn><mn>4</mn></msup></mfenced><mn>7</mn></msup></mrow><mn>5</mn></mfrac><mo>=</mo><mfrac><mrow><mn>3</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>5</mn></mfrac><mo>=</mo><mn>3</mn></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Which of the following number is not divisible by 9?</p>",
                    question_hi: "<p>6. निम्नलिखित में से कौन-सी संख्या 9 से विभाज्य नहीं है?</p>",
                    options_en: ["<p>3789</p>", "<p>7857</p>", 
                                "<p>1845</p>", "<p>3658</p>"],
                    options_hi: ["<p>3789</p>", "<p>7857</p>",
                                "<p>1845</p>", "<p>3658</p>"],
                    solution_en: "<p>6.(d)<br><strong>Divisibility rule of 9:</strong> <br>The sum of the digits of the given no. should be divisible by 9<br>Checking all the options one by one, <br>we get 3658 i.e.3 + 6 + 5 + 8 = 22 , which is not divisible by 9<br>So, the correct option is (d)</p>",
                    solution_hi: "<p>6.(d)<br><strong>9 का विभाज्यता नियम:</strong><br>दी गई संख्या के अंकों का योग. 9 से विभाज्य होना चाहिए<br>सभी विकल्पों को एक-एक करके जाँचने पर,<br>हमें 3658 प्राप्त होता है अर्थात 3 + 6 + 5 + 8 = 22, जो 9 से विभाज्य नहीं है<br>तो, सही विकल्प (d) है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. For what value of M. 58524M is divisible by 9 ?</p>",
                    question_hi: "<p>7. M के किस मान के लिए 58524M, 9 से विभाज्य है?</p>",
                    options_en: ["<p>8</p>", "<p>2</p>", 
                                "<p>3</p>", "<p>7</p>"],
                    options_hi: ["<p>8</p>", "<p>2</p>",
                                "<p>3</p>", "<p>7</p>"],
                    solution_en: "<p>7.(c)<br>For <strong>58524M </strong>to be divisible by 9, the sum of its digits i.e. 5 + 8 + 5 + 2 + 4 + M = (24 + M) should be divisible by 9.<br><math display=\"inline\"><mo>&#8658;</mo></math> M = 3 As 24 + 3 = 27, divisible by 9</p>",
                    solution_hi: "<p>7.(c)<br><strong>58524M </strong>को 9 से विभाज्य होने के लिए, इसके अंकों का योग यानी 5 + 8 + 5 + 2 + 4 + M = (24 + M) , 9 से विभाज्य होना चाहिए।<br><math display=\"inline\"><mo>&#8658;</mo></math> M = 3 चूँकि 24 + 3 = 27, 9 से विभाज्य</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Which of the following number is divisible by 9?</p>",
                    question_hi: "<p>8. निम्नलिखित में से कौन-सी संख्या 9 से विभाज्य है?</p>",
                    options_en: ["<p>1474</p>", "<p>4124</p>", 
                                "<p>4131</p>", "<p>1254</p>"],
                    options_hi: ["<p>1474</p>", "<p>4124</p>",
                                "<p>4131</p>", "<p>1254</p>"],
                    solution_en: "<p>8.(c)<br><strong>Divisibility rule of 9:</strong> <br>For any number to be divisible by 9, the sum of its digits should be divisible by 9.<br>(a) 1474 = 1 + 4 + 7 + 4 = 16, not divisible by 9.<br>(b) 4124 = 4 + 1 + 2 + 4 = 11, not divisible by 9.<br>(c) 4131 = 4 + 1 + 3 + 1 = 9, divisible by 9.<br>(d) 1254 = 1 + 2 + 5 + 4 = 12, not divisible by 9.<br>So, the correct option is (c)</p>",
                    solution_hi: "<p>8.(c)<br><strong>9 का विभाज्यता नियम:</strong><br>किसी भी संख्या को 9 से विभाज्य होने के लिए उसके अंकों का योग 9 से विभाज्य होना चाहिए।<br>(a) 1474 = 1 + 4 + 7 + 4 = 16, 9 से विभाज्य नहीं।<br>(b) 4124 = 4 + 1 + 2 + 4 = 11, 9 से विभाज्य नहीं।<br>(c) 4131 = 4 + 1 + 3 + 1 = 9, 9 से विभाज्य।<br>(d) 1254 = 1 + 2 + 5 + 4 = 12, 9 से विभाज्य नहीं।<br>तो, सही विकल्प (c) है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "9. How many two digit numbers are there which are divisible by 8?",
                    question_hi: "9. ऐसी कितनी दो अंकीय संख्याएं हैं जो 8 से विभाज्य हैं?",
                    options_en: [" 11", " 12", 
                                " 13", " 10"],
                    options_hi: [" 11", " 12",
                                " 13", " 10"],
                    solution_en: "9.(a)<br />Two digit number which is divisible by 8 = 16, 24, 40……..96<br />First term(a) = 16, last term(l) = 96, common difference(d) = 24-16 = 8<br />l = a+(n-1)d<br />96 = 16 + (n-1)8<br />80 = (n-1)8<br />10 = n-1<br />n = 11<br />So, total no of two digit no which is divisible by 8 = 11",
                    solution_hi: "9.(a)<br />8 से विभाज्य , दो अंकों की संख्या  = 16, 24, 40……..96<br />पहला पद (a) = 16, अंतिम पद (l) = 96, सामान्य अंतर (d) = 24 -16 = 8<br />l = a + (n-1) d<br />96 = 16 + (n-1) 8<br />80 = (n-1) 8<br />10 = n -1<br />n = 11<br />अतः, 8 से विभाज्य दो अंकों की संख्या की कुल संख्या = 11",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "10.Which of the following numbers is not divisible by 8 ?",
                    question_hi: "<p>10. निम्नलिखित में से कौन-सी संख्या 8 से विभाज्य नहीं है ?</p>",
                    options_en: ["<p>5896</p>", "<p>6044</p>", 
                                "<p>6032</p>", "<p>5968</p>"],
                    options_hi: ["<p>5896</p>", "<p>6044</p>",
                                "<p>6032</p>", "<p>5968</p>"],
                    solution_en: "<p>10.(b)<br><strong>Divisibility rule of 8:</strong><br>Number is divisible by 8, if its last 3 digits are divisible by 8.<br>Checking all the options, we have ;<br>(a) 5896 &rarr; 896 is divisible by 8<br>(b) 6044 &rarr; 044 is not divisible by 8<br>(c) 6032 &rarr; 032 is divisible by 8<br>(d) 5968 &rarr; 968 is divisible by 8<br>Clearly, we can see that 6044 is the only number which is not divisible by 8.</p>",
                    solution_hi: "<p>10.(b)<br><strong>8 का विभाज्यता नियम:</strong><br>वह संख्या 8 से विभाज्य है, यदि उसके अंतिम 3 अंक 8 से विभाज्य हैं।<br>सभी विकल्पों की जाँच करने पर, हम प्राप्त करते है। ;<br>(a) 5896 &rarr; 896, 8 से विभाज्य है<br>(b) 6044 &rarr; 044, 8 से विभाज्य नहीं है<br>(c) 6032 &rarr; 032, 8 से विभाज्य है<br>(d) 5968 &rarr; 968, 8 से विभाज्य है<br>स्पष्ट रूप से, हम देख सकते हैं कि 6044 एकमात्र संख्या है जो 8 से विभाज्य नहीं है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Which of the following number is divisible by 4?</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन-सी संख्या 4 से विभाज्य है?</p>",
                    options_en: ["<p>1734</p>", "<p>2342</p>", 
                                "<p>4348</p>", "<p>1570</p>"],
                    options_hi: ["<p>1734</p>", "<p>2342</p>",
                                "<p>4348</p>", "<p>1570</p>"],
                    solution_en: "<p>11.(c)<br><strong>Divisibility rule of 4 :</strong> <br>If the last two digits of any no is divisible by 4, the number will be divisible by 4.<br>Now, we have ;<br>(a) 1734 &rarr; 34 is not divisible by 4<br>(b) 2342 &rarr; 42 is not divisible by 4<br>(c) 4348 &rarr; 48 is divisible by 4<br>(d) 1570 &rarr; 70 is not divisible by 4<br>Clearly, we can see that 4348 is the only no. which is divisible by 4.</p>",
                    solution_hi: "<p>11.(c)<br><strong>4 की विभाज्यता नियम:</strong> <br>यदि किसी संख्या के अंतिम दो अंक 4 से विभाज्य हैं, तो वह संख्या 4 से विभाज्य होगी।<br>अब हमारे पास है ;<br>(a) 1734 &rarr; 34, 4 से विभाज्य नहीं है<br>(b) 2342 &rarr; 42, 4 से विभाज्य नहीं है<br>(c) 4348 &rarr; 48, 4 से विभाज्य है<br>(d) 1570 &rarr; 70, 4 से विभाज्य नहीं है<br>स्पष्ट रूप से, हम देख सकते हैं कि 4348 ही एकमात्र संख्या है। जो 4 से विभाज्य है.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Which among the following is divisible by 4, 7 and 23?</p>",
                    question_hi: "<p>12. निम्नलिखित में से से कौन-सी संख्या 4, 7 और 23 से विभाज्य है?</p>",
                    options_en: ["<p>6245</p>", "<p>6490</p>", 
                                "<p>6457</p>", "<p>6440</p>"],
                    options_hi: ["<p>6245</p>", "<p>6490</p>",
                                "<p>6457</p>", "<p>6440</p>"],
                    solution_en: "<p>12.(d)<br><strong>Divisibility of 4 : </strong>The last two digits of the number must be divisible by 4.<br>Checking all the options one by one, we have ;<br>(a) 6245 &rarr; Last two digit = 45 (not divisible by 4)<br>(b) 6490 &rarr; Last two digits = 90 (not divisible by 4)<br>(c) 6457 &rarr; Last two digit = 57 (not divisible by 4)<br>(d) 6440 &rarr; Last two digits = 40 (divisible by 4).<br>So, 6440 is the only number which is divisible by 4, 7, 23</p>",
                    solution_hi: "<p>12.(d)<br><strong>4 की विभाज्यता:</strong> संख्या के अंतिम दो अंक 4 से विभाज्य होने चाहिए।<br>एक-एक करके सभी विकल्पों की जाँच करने पर, हमारे पास है;<br>(a) 6245 &rarr; अंतिम दो अंक = 45 (4 से विभाज्य नहीं)<br>(b) 6490 &rarr; अंतिम दो अंक = 90 (4 से विभाज्य नहीं)<br>(c) 6457 &rarr; अंतिम दो अंक = 57 (4 से विभाज्य नहीं)<br>(d) 6440 &rarr; अंतिम दो अंक = 40 (4 से विभाज्य)।<br>तो, 6440 एकमात्र संख्या है जो 4, 7, 23 से विभाज्य है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "13. Find the greatest number which on dividing 60 and 90 leaves remainders 2 and 3 respectively.",
                    question_hi: "13. वह सबसे बड़ी संख्या ज्ञात कीजिए जिससे 60 और 90 को भाग देने पर क्रमशः 2 और 3 शेषफल बचे।",
                    options_en: [" 17", " 29", 
                                " 19", " 23"],
                    options_hi: [" 17", " 29",
                                " 19", " 23"],
                    solution_en: "13.(b)<br />60 - 2 = 58, 90 - 3 = 87<br />Required greatest no = HCF of (58, 87) = 29",
                    solution_hi: "13.(b)<br />60 - 2 = 58, 90 - 3 = 87<br />आवश्यक सबसे बड़ी संख्या =(58, 87) का HCF = 29",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "14. An electric wire of 60 meters is cut into equal segments of 5 meters each. Calculate the number of segments.",
                    question_hi: "14. 60 मीटर लंबे एक बिजली के तार को 5-5 मीटर के बराबर खंडों में काटा जाता है। खंडों की संख्या की गणना कीजिए।",
                    options_en: ["  8", "  10", 
                                "  12", "  6"],
                    options_hi: ["  8", "  10",
                                "  12", "  6"],
                    solution_en: "14.(c)<br />No of segments = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 12",
                    solution_hi: "14.(c)<br />खंडों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 12",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. If the number 34564A is divisible by 11, then what is the value of A ?</p>",
                    question_hi: "<p>15. यदि संख्या 34564A, 11 से विभाज्य है, तो A का मान क्या है?</p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>7</p>", "<p>6</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>7</p>", "<p>6</p>"],
                    solution_en: "<p>15.(a) <strong>divisibility of 11 : </strong>- the difference of the sum of digits at odd position and sum of digits at even position in a number is 0 or 11.<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>3</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfenced><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfenced><mrow><mn>4</mn><mo>+</mo><mn>6</mn><mo>+</mo><mi>A</mi></mrow></mfenced></math> = 0<br>12 - 10 - A = 0<br>&nbsp;2 = A</p>",
                    solution_hi: "<p>15.(a) <strong>11 की विभाज्यता :-</strong> किसी संख्या में विषम स्थान पर अंकों के योग और सम स्थान पर अंकों के योग का अंतर 0 या 11 होता है।<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><mn>3</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>4</mn></mrow></mfenced><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfenced><mrow><mn>4</mn><mo>+</mo><mn>6</mn><mo>+</mo><mi>A</mi></mrow></mfenced></math> = 0<br>&nbsp;12 - 10 - A = 0<br>&nbsp;2 = A</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>