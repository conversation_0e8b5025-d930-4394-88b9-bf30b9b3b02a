<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 22</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">22</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 20
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 21,
                end: 21
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">sinA</span><span style=\"font-family: Cambria Math;\"> &minus; 4 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>3</mn></msup><mi>A</mi></math> </span><span style=\"font-family: Cambria Math;\">&nbsp;= ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Morning)</span></p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">sinA</span><span style=\"font-family: Cambria Math;\"> &minus; 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>3</mn></msup></math> </span><span style=\"font-family: Cambria Math;\">A = ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Morning)</span></p>\n",
                    options_en: ["<p>cot3A</p>\n", "<p>sin3A</p>\n", 
                                "<p>cos3A</p>\n", "<p>tan3A</p>\n"],
                    options_hi: ["<p>cot3A</p>\n", "<p>sin3A</p>\n",
                                "<p>cos3A</p>\n", "<p>tan3A</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putting A = 30&deg; we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 sin30&deg; - 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>3</mn></msup><mn>30</mn><mo>&deg;</mo></math> </span><span style=\"font-family: Cambria Math;\"> = 3 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&nbsp; - 4&times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>8</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math>= 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Checking all the options </span><span style=\"font-family: Cambria Math;\">by putting</span><span style=\"font-family: Cambria Math;\"> A = 30&deg; . we get only one option (b), which gives the same value as that in the </span><span style=\"font-family: Cambria Math;\">above </span><span style=\"font-family: Cambria Math;\">i.e</span><span style=\"font-family: Cambria Math;\"> sin 3A = sin90&deg; = 1</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = 30&deg; </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> =</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 sin30&deg; - 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>3</mn></msup><mn>30</mn><mo>&deg;</mo></math> </span><span style=\"font-family: Cambria Math;\"> = 3 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> - 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>-</mo><mfrac><mn>4</mn><mn>8</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = 30&deg; </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2305;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> (b), </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> 1 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> sin 3A = sin90&deg; = 1</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Find &theta; </span><span style=\"font-family: Cambria Math;\">, if&nbsp; cos&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math>.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Afternoon)</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-weight: 400;\">&nbsp;&#2351;&#2342;&#2367; cos&theta;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&nbsp;</mo><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></span> , &nbsp;<span style=\"font-weight: 400;\">&#2340;&#2379; </span>&theta; <span style=\"font-weight: 400;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Afternoon)</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>&pi;</mi></mrow><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&pi;</mi></mrow><mn>6</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>&pi;</mi></mrow><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>&pi;</mi></mrow><mn>3</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>&pi;</mi></mrow><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&pi;</mi></mrow><mn>6</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>&pi;</mi></mrow><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>&pi;</mi></mrow><mn>3</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cos</span><span style=\"font-family: Cambria Math;\">&nbsp;&theta;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">=&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mi>cos</mi><mfrac><mi>&pi;</mi><mn>6</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cos&theta;</span><span style=\"font-family: Cambria Math;\"> = cos( &pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>-</mo><mfrac><mi>&pi;</mi><mn>6</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">) = cos<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&pi;</mi></mrow><mn>6</mn></mfrac></math></span></p>\r\n<p>&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&pi;</mi></mrow><mn>6</mn></mfrac></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cos</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&theta;</mi></math>= - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = - cos&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&pi;</mi><mn>6</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cos</span><span style=\"font-family: Cambria Math;\"> &theta;= cos(&pi; </span><span style=\"font-family: Cambria Math;\">-&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&pi;</mi><mn>6</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">) = cos&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&pi;</mi></mrow><mn>6</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&pi;</mi></mrow><mn>6</mn></mfrac></math></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> If cos(x - y) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> and sin(x + y) = 1, where x and y are positive acute angles and x &ge; y, then x and y are : (</span><span style=\"font-family: Cambria Math;\"> 0&deg;&lt; (x + y) &le; 90&deg; </span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Evening)</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> cos(x - y) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> sin(x + y) = 1, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2361;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2366;&#2340;&#2381;&#2350;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2381;&#2351;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> x &ge; y </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">: </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">: (0&deg;</span><span style=\"font-family: Cambria Math;\"> &lt; (x + y) &le; 90&deg;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 02/06/2022(Evening)</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">70&deg; ; 20&deg;</span></p>\n", "<p>50&deg; <span style=\"font-family: Cambria Math;\">; 40&deg;</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">80&deg; ; 10&deg;</span></p>\n", "<p>60&deg;<span style=\"font-family: Cambria Math;\"> ; 30&deg;</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">70&deg; ; 20&deg;</span></p>\n", "<p>50&deg;<span style=\"font-family: Cambria Math;\"> ; 40&deg;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">80&deg;; 10&deg;</span></p>\n", "<p>60&deg;<span style=\"font-family: Cambria Math;\"> ; 30&deg;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cos (x - y) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = cos 30&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr;x - y = 30&deg;</span><span style=\"font-family: Cambria Math;\">&mdash; (1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin (x + y) = 1 = sin 90&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr;x + y = 90&deg;</span><span style=\"font-family: Cambria Math;\"> &mdash; (2)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">From </span><span style=\"font-family: Cambria Math;\">eqn</span><span style=\"font-family: Cambria Math;\"> (1) and (2) we have,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&deg;</mo></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 60&deg;</span><span style=\"font-family: Cambria Math;\"> , y = 60&deg;</span><span style=\"font-family: Cambria Math;\"> - 30&deg;</span><span style=\"font-family: Cambria Math;\"> = 30&deg;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cos (x - y) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = cos 30&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr;x - y</span><span style=\"font-family: Cambria Math;\"> = 30&deg;</span><span style=\"font-family: Cambria Math;\">&mdash; (1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin (x + y) = 1 = sin 90&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr;x + y = 90&deg;</span><span style=\"font-family: Cambria Math;\"> &mdash; (2)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> (1) </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> (2) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&deg;</mo></mrow><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 60&deg;</span><span style=\"font-family: Cambria Math;\"> , y = 60&deg;</span><span style=\"font-family: Cambria Math;\"> - 30&deg;</span><span style=\"font-family: Cambria Math;\"> = 30&deg;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> If </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cot&theta;</span><span style=\"font-family: Cambria Math;\"> = 5, then </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> is equal to_______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Morning)</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cot&theta;</span><span style=\"font-family: Cambria Math;\"> = 5, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cosec&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">_______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Morning)</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>5</mn><mo>+</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>)</mo></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mn>3</mn><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mn>5</mn><mo>+</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>)</mo></math></p>\n", "<p>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></math>)</p>\n"],
                    options_hi: ["<p>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>5</mn><mo>+</mo><mfrac><mn>1</mn><mn>5</mn></mfrac></math>)</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mn>3</mn><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mn>5</mn><mo>+</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>)</mo></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>3</mn><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>)</mo></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cosec &theta; + cot &theta; = 5 &mdash; (1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cosec &theta; - cot &theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &mdash; (2)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">From equation (1) </span><span style=\"font-family: Cambria Math;\">and (</span><span style=\"font-family: Cambria Math;\">2)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2Cosec &theta; = 5+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cosec&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mn>5</mn><mo>+</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>)</mo></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cosec &theta; + cot &theta; = 5 &mdash; (1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cosec &theta; - cot &theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &mdash; (2)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> (1) </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> (2) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2Cosec &theta; = 5+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cosec&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mn>5</mn><mo>+</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>)</mo></math></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> If 1+ tan&theta;</span><span style=\"font-family: Cambria Math;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">, then&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mo>-</mo><mn>1</mn></math></span><span style=\"font-family: Cambria Math;\">= ________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Aft</span><span style=\"font-family: Cambria Math;\">ernoon)</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 1+ tan&theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>cot &theta; - 1 </span><span style=\"font-family: Cambria Math;\">= ________ </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Afternoon)</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 + tan&theta;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan&theta;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>-</mo></math>1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>cot&theta;</span><span style=\"font-family: Cambria Math;\"> - 1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mn>1</mn></math></span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac><mo>-</mo><mn>1</mn></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 + tan&theta;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>&nbsp; </span><span style=\"font-family: Cambria Math;\">- 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>cot&theta;</span><span style=\"font-family: Cambria Math;\"> - 1 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> - 1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math> - 1 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> What is the value of the expression 100(sin15&deg; </span><span style=\"font-family: Cambria Math;\">cos15&deg; </span><span style=\"font-family: Cambria Math;\">)?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Evening)</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> 100(sin15&deg;&nbsp;</span><span style=\"font-family: Cambria Math;\"> cos15&deg; </span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 03/06/2022(Evening)</span></p>\n",
                    options_en: ["<p>50</p>\n", "<p>75</p>\n", 
                                "<p>100</p>\n", "<p>25</p>\n"],
                    options_hi: ["<p>50</p>\n", "<p>75</p>\n",
                                "<p>100</p>\n", "<p>25</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin 2A = 2 </span><span style=\"font-family: Cambria Math;\">sinA</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cosA</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">100 (sin 15&deg; cos 15&deg;) = 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>30</mn><mo>&deg;</mo></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 25</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin 2A = 2 </span><span style=\"font-family: Cambria Math;\">sinA</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cosA</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">100 (sin 15&deg; cos 15&deg;) = 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>30</mn><mo>&deg;</mo></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 100 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 25</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\"> If tan&theta; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>tan</mi><mi>&theta;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 2, then the value of&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mfrac><mn>1</mn><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">is _______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Morning)</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> tan&theta; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>tan</mi><mi>&theta;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 2, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mfrac><mn>1</mn><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math>&nbsp; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> _______ </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Morning)</span></p>\n",
                    options_en: ["<p>&minus;4</p>\n", "<p>2</p>\n", 
                                "<p>4</p>\n", "<p>3</p>\n"],
                    options_hi: ["<p>&minus;4</p>\n", "<p>2</p>\n",
                                "<p>4</p>\n", "<p>3</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan&theta;</span><span style=\"font-family: Cambria Math;\"> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>tan</mi><mi>&theta;</mi></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>2</mn></msup></math> </span><span style=\"font-family: Cambria Math;\">- 2 = 2</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan&theta;</span><span style=\"font-family: Cambria Math;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>tan</mi><mi>&theta;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mfrac><mn>1</mn><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>2</mn></msup></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> - 2 = 2</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> The value of sin18&deg;&nbsp;</span><span style=\"font-family: Cambria Math;\"> is given as&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">. Find the value of cosec 18&deg; </span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Afternoon)</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> sin18&deg; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math></span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> cosec 18&deg; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Afternoon)</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math> </span></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin18&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cosec18&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>5</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin18&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cosec18&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mrow><msqrt><mn>5</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>(</mo><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>5</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>1</mn></math></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> If A = 30&deg;, what is the value of:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mfrac><mrow><mo>(</mo><mn>6</mn><mi>sin</mi><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo></mrow><mrow><mn>12</mn><mi>sin</mi><mi>A</mi></mrow></mfrac></mstyle></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Evening)</span></p>\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A = 30&deg; </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mfrac><mrow><mn>6</mn><mi>sin</mi><mi>A</mi><mo>&nbsp;</mo><mo>+</mo><mn>9</mn><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>A</mi></mrow><mrow><mn>12</mn><mi>sin</mi><mi>A</mi></mrow></mfrac></mstyle></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 06/06/2022(Evening)</span></p>\n",
                    options_en: ["<p>6</p>\n", "<p>&minus;6</p>\n", 
                                "<p>3</p>\n", "<p>&minus;3</p>\n"],
                    options_hi: ["<p>6</p>\n", "<p>&minus;6</p>\n",
                                "<p>3</p>\n", "<p>&minus;3</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>6</mn><mi>sin</mi><mi>A</mi><mo>+</mo><mn>9</mn><mo>&nbsp;</mo><mi>cos</mi><mi>e</mi><mi>s</mi><mi>A</mi><mo>-</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo></mrow><mrow><mn>12</mn><mi>sin</mi><mi>A</mi></mrow></mfrac></math></p>\r\n<p>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>sin</mi><mn>30</mn><mo>&deg;</mo><mo>+</mo><mn>9</mn><mi>cos</mi><mi>e</mi><mi>c</mi><mn>30</mn><mo>&deg;</mo><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mn>30</mn><mo>&deg;</mo></mrow><mrow><mn>12</mn><mi>sin</mi><mn>30</mn><mo>&deg;</mo></mrow></mfrac></math></p>\r\n<p>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>+</mo><mn>9</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>12</mn><mo>&nbsp;</mo><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>18</mn><mo>&nbsp;</mo><mo>-</mo><mn>3</mn></mrow><mn>6</mn></mfrac></math> = 3</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&nbsp;</mo><mi>sin</mi><mn>30</mn><mo>&deg;</mo><mo>+</mo><mo>&nbsp;</mo><mn>9</mn><mi>cos</mi><mi>e</mi><mi>c</mi><mn>30</mn><mo>&deg;</mo><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mn>30</mn><mo>&deg;</mo></mrow><mrow><mn>12</mn><mo>&nbsp;</mo><mi>sin</mi><mn>30</mn><mo>&deg;</mo></mrow></mfrac></math></p>\r\n<p>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>+</mo><mn>9</mn><mo>&times;</mo><mn>2</mn><mo>-</mo><msup><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>12</mn><mo>&nbsp;</mo><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>18</mn><mo>-</mo><mn>3</mn></mrow><mn>6</mn></mfrac></math> = 3</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> If&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>5</mn><mn>3</mn></mfrac><mspace linebreak=\"newline\"></mspace></math> &nbsp;</span><span style=\"font-family: Cambria Math;\">, then what is the value of cot2&theta; .</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 </span><span style=\"font-family: Cambria Math;\">( Morning</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi></math></span><span style=\"font-family: Cambria Math;\">=&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">&nbsp; cot 2&theta; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 </span><span style=\"font-family: Cambria Math;\">( Morning</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi></math>+ </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi></math><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;=&nbsp; </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math></span><span style=\"font-weight: 400;\">=&nbsp; </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></span></p>\r\n<p><span style=\"font-weight: 400;\">6 - 3 </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></math><span style=\"font-weight: 400;\">&nbsp;= 5</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></math></span></p>\r\n<p><span style=\"font-weight: 400;\">6 = 8 </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></math></span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></math></span><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>8</mn></mfrac></math></span><span style=\"font-weight: 400;\">= </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span></p>\r\n<p><span style=\"font-weight: 400;\">sin&theta;</span><span style=\"font-weight: 400;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math><span style=\"font-weight: 400;\"> = sin60</span><span style=\"font-weight: 400;\">&deg;</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;&theta;= 60</span><span style=\"font-weight: 400;\">&deg;</span></p>\r\n<p><span style=\"font-weight: 400;\">Cot2&theta;</span><span style=\"font-weight: 400;\"> = cot(2&times;60</span><span style=\"font-weight: 400;\">&deg;</span><span style=\"font-weight: 400;\">) = cot120</span><span style=\"font-weight: 400;\">&deg;</span><span style=\"font-weight: 400;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10.b</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>5</mn><mn>3</mn></mfrac></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math><span style=\"font-weight: 400;\">+ </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math></span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>3</mn></mfrac></math><span style=\"font-weight: 400;\">&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></math></span></p>\r\n<p><span style=\"font-weight: 400;\">6 = 8 </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></math></span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></math></span><span style=\"font-weight: 400;\"> =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>8</mn></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>\r\n<p><span style=\"font-weight: 400;\">sin&theta;</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-weight: 400;\"> = sin60</span><span style=\"font-weight: 400;\">&deg;</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;&theta;= 60</span><span style=\"font-weight: 400;\">&deg;</span></p>\r\n<p><span style=\"font-weight: 400;\">Cot2&theta;</span><span style=\"font-weight: 400;\"> = cot(2&times;60</span><span style=\"font-weight: 400;\">&deg;</span><span style=\"font-weight: 400;\">) = cot120</span><span style=\"font-weight: 400;\">&deg;</span><span style=\"font-weight: 400;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Select the correct identity from the following options.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 </span><span style=\"font-family: Cambria Math;\">( Afternoon</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2360;&#2350;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 </span><span style=\"font-family: Cambria Math;\">( Afternoon</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></math> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi></math><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi></math> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></math> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi></math> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi></math> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">On observing the options, we find that option(c) is correct </span><span style=\"font-family: Cambria Math;\">i.e</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><msup><mi>tan</mi><mn>2</mn></msup><mi>A</mi><mo>=</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>A</mi></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2354;&#2379;&#2325;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (c) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 1 +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>2</mn></msup></math> </span><span style=\"font-family: Cambria Math;\">A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">A</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">The value of sin35&deg; </span><span style=\"font-family: Cambria Math;\">cos55&deg;&nbsp;</span><span style=\"font-family: Cambria Math;\"> + cos35&deg; </span><span style=\"font-family: Cambria Math;\">sin55&deg;&nbsp;</span><span style=\"font-family: Cambria Math;\"> is :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 </span><span style=\"font-family: Cambria Math;\">( Evening</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> sin 35&deg;</span><span style=\"font-family: Cambria Math;\">cos55&deg;&nbsp;</span><span style=\"font-family: Cambria Math;\"> + cos35&deg; </span><span style=\"font-family: Cambria Math;\">sin55&deg;&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 </span><span style=\"font-family: Cambria Math;\">( Evening</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    options_en: ["<p>0</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>0</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", "<p>1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Formula </span><span style=\"font-family: Cambria Math;\">used :</span><span style=\"font-family: Cambria Math;\"> sin (A + B) = </span><span style=\"font-family: Cambria Math;\">sinA</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cosB</span><span style=\"font-family: Cambria Math;\"> +</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cosA</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">sinB</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sin 35&deg; cos 55&deg;+ cos 35&deg; sin 55&deg; = sin (35&deg; +55&deg;) = sin90</span><span style=\"font-family: Cambria Math;\">&deg; =</span><span style=\"font-family: Cambria Math;\"> 1</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> sin (A + B) = </span><span style=\"font-family: Cambria Math;\">sinA</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cosB</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">cosA</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">sinB</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sin 35&deg; cos 55&deg;+ cos 35&deg; sin 55&deg; = sin (35&deg; +55&deg;) = sin90</span><span style=\"font-family: Cambria Math;\">&deg; =</span><span style=\"font-family: Cambria Math;\"> 1</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> tan (&pi; + &theta;) </span><span style=\"font-family: Cambria Math;\">= ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 08/06/2022 </span><span style=\"font-family: Cambria Math;\">( Morning</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Cambria Math;\"> tan (&pi; + &theta;) </span><span style=\"font-family: Cambria Math;\">= ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 08/06/2022 </span><span style=\"font-family: Cambria Math;\">( Morning</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">sec&theta;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">cosec&theta;</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">cot&theta;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">tan&theta;</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">sec&theta;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">cosec&theta;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">cot&theta;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">tan&theta;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan (&pi; + &theta;) = </span><span style=\"font-family: Cambria Math;\">tan&theta;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan (&pi; + &theta;) = </span><span style=\"font-family: Cambria Math;\">tan&theta;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> I</span><span style=\"font-family: Cambria Math;\">f&nbsp; sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math> , 0 &lt; &theta; &lt; 90&deg;,&nbsp; then</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi></mrow><mrow><mn>2</mn><mi>sin</mi><mi>&theta;</mi><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></mstyle></math>&nbsp; &times;</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math>= ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 08/06/2022 </span><span style=\"font-family: Cambria Math;\">( Afternoon</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367; sin&theta; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac></math> , 0&lt;&theta;&lt;90&deg; </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi></mrow><mrow><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&theta;</mi><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac><mo>&times;</mo><mfrac><mn>1</mn><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math></span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 08/06/2022 </span><span style=\"font-family: Cambria Math;\">( Afternoon</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>295</mn><mn>3456</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>590</mn><mn>3542</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>695</mn><mn>3542</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>595</mn><mn>3456</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>295</mn><mn>3456</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>590</mn><mn>3542</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>695</mn><mn>3542</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>595</mn><mn>3456</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin</span><span style=\"font-family: Cambria Math;\">&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac><mo>=</mo><mfrac><mi>p</mi><mi>h</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">b=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>13</mn><mn>2</mn></msup><mo>-</mo><msup><mn>12</mn><mn>2</mn></msup></msqrt><mo>=</mo><mn>5</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi></mrow><mrow><mn>2</mn><mi>sin</mi><mi>&theta;</mi><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></mstyle></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mfrac><mrow><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>12</mn><mn>13</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>13</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>2</mn><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>12</mn><mn>13</mn></mfrac></mstyle><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>13</mn></mfrac></mstyle></mrow></mfrac></mstyle></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>12</mn><mn>5</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>144</mn><mn>169</mn></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>25</mn><mn>169</mn></mfrac></mstyle></mrow><mrow><mn>2</mn><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>60</mn><mn>169</mn></mfrac></mstyle></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>25</mn><mn>144</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>144</mn><mo>-</mo><mn>25</mn></mrow><mn>120</mn></mfrac><mo>&times;</mo><mfrac><mn>25</mn><mn>144</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>595</mn><mn>3456</mn></mfrac></math></span></p>\r\n<p>&nbsp;</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin&theta;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>13</mn></mfrac><mo>=</mo><mfrac><mi>p</mi><mi>h</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">b =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>13</mn><mn>2</mn></msup><mo>-</mo><msup><mn>12</mn><mn>2</mn></msup></msqrt></math></span><span style=\"font-family: Cambria Math;\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>169</mn><mo>-</mo><mn>144</mn></msqrt><mo>=</mo><mn>5</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi></mrow><mrow><mn>2</mn><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac><mo>&times;</mo><mfrac><mn>1</mn><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>12</mn><mn>13</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>13</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>12</mn><mn>13</mn></mfrac></mstyle><mo>&times;</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>13</mn></mfrac></mstyle></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>12</mn><mn>5</mn></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mn>144</mn><mo>-</mo><mn>25</mn></mrow><mn>169</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>120</mn><mn>169</mn></mfrac></mstyle></mfrac><mo>&times;</mo><mfrac><mn>25</mn><mn>144</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>119</mn><mn>120</mn></mfrac><mo>&times;</mo><mfrac><mn>25</mn><mn>144</mn></mfrac><mo>=</mo><mfrac><mn>595</mn><mn>3456</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15.&nbsp; </span><span style=\"font-weight: 400;\">If </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></math><span style=\"font-weight: 400;\">, then the value of cosec</span><span style=\"font-weight: 400;\">(&theta;</span><span style=\"font-weight: 400;\">+30&deg;)</span><span style=\"font-weight: 400;\"> is ______. [</span><span style=\"font-weight: 400;\"> &theta; is an acute angle.]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 08/06/2022 </span><span style=\"font-family: Cambria Math;\">( Evening</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15.&nbsp; </span><span style=\"font-weight: 400;\">&#2351;&#2342;&#2367; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi></math></span><span style=\"font-weight: 400;\">=</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math></span><span style=\"font-weight: 400;\"> &#2361;&#2376;, &#2340;&#2379; cosec (&theta;</span><span style=\"font-weight: 400;\">+</span><span style=\"font-weight: 400;\"> 30&deg;) &#2325;&#2366; &#2350;&#2366;&#2344; _______ &#2361;&#2376;&#2404; [ &theta;&nbsp;</span><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2344;&#2381;&#2351;&#2370;&#2344; &#2325;&#2379;&#2339; &#2361;&#2376;&#2404;]</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 08/06/2022 </span><span style=\"font-family: Cambria Math;\">( Evening</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sec&theta;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math></span><span style=\"font-family: Cambria Math;\"> or &theta;</span><span style=\"font-family: Cambria Math;\"> = 30&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cosec(&theta;</span><span style=\"font-family: Cambria Math;\"> + 30&deg;) = Cosec60&deg; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sec</span><span style=\"font-family: Cambria Math;\"> &theta;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> or &nbsp;&theta;</span><span style=\"font-family: Cambria Math;\">= 30&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cosec(</span><span style=\"font-family: Cambria Math;\"> &theta;+ 30&deg;) = Cosec60&deg; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16.<span style=\"font-family: Cambria Math;\"> If&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi></math></span><span style=\"font-family: Cambria Math;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">, where 0 &le; &theta; &le;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&pi;</mi><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>4</mn></msup><mi>&theta;</mi><mo>-</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>4</mn></msup><mi>&theta;</mi></math></span><span style=\"font-family: Cambria Math;\">&nbsp;is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 09/06/2022 </span><span style=\"font-family: Cambria Math;\">( Morning</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    question_hi: "<p>16. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2361;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> 0 &le; &theta; &le;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&pi;</mi><mn>2</mn></mfrac></math> &nbsp;<span style=\"font-weight: 400;\">&#2340;&#2379;</span> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><mi>o</mi><mi>e</mi><mi>s</mi><msup><mi>c</mi><mn>4</mn></msup><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>4</mn></msup><mi>&theta;</mi></math> </span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 09/06/2022 </span><span style=\"font-family: Cambria Math;\">( Morning</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">We know that&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi></math></span><span style=\"font-family: Cambria Math;\">= 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So,&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>4</mn></msup><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>4</mn></msup><mi>&theta;</mi></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mo>(</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>)</mo><mo>(</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>)</mo></mstyle></math> = 1&nbsp; &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> ,&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>=</mo><mo>&nbsp;</mo><mn>1</mn></mstyle></math></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>4</mn></msup><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>4</mn></msup><mi>&theta;</mi></math></span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&theta;</mi><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&theta;</mi><mo>)</mo></math> = 1 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">If &alpha; is an acute angle, which of the following options will NOT necessarily be equal to&nbsp;</span><span style=\"font-weight: 400;\">the value of cosec &alpha;?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 09/06/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>17. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> &alpha; </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2381;&#2351;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2367;&#2357;&#2366;&#2352;&#2381;&#2351;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> cosec &alpha; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 09/06/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>&alpha;</mi></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&alpha;</mi></msqrt></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&alpha;</mi></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mi>&alpha;</mi></mrow><mrow><mi>cos</mi><mi>&alpha;</mi></mrow></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>&alpha;</mi></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&alpha;</mi></msqrt></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&alpha;</mi></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&alpha;</mi></mrow><mrow><mi>cos</mi><mi>&alpha;</mi></mrow></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In option (a) we get , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>&alpha;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = sec&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&alpha;</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In option (b) we get ,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&alpha;</mi></msqrt><mo>=</mo><msqrt><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&alpha;</mi><mo>&nbsp;</mo></msqrt><mo>=</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mo>&nbsp;</mo><mi>&alpha;</mi></math>&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In option (c) we get , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mo>&nbsp;</mo><mi>&alpha;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = cosec&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&alpha;</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In option (d) we get ,&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mi>&alpha;</mi></mrow><mrow><mi>cos</mi><mo>&nbsp;</mo><mi>&alpha;</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>&alpha;</mi></mrow><mrow><mi>sin</mi><mi>&alpha;</mi><mo>&nbsp;</mo><mi>cos</mi><mi>&alpha;</mi></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&nbsp;cosec&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&alpha;</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, clearly we can see that cosec&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&alpha;</mi></math> &ne;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mo>&nbsp;</mo><mi>&alpha;</mi></mrow></mfrac></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (a) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>&alpha;</mi></mrow></mfrac><mo>=</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&alpha;</mi></math>&nbsp; &nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (b) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;&nbsp; &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mi>t</mi><mn>2</mn></msup><mi>&alpha;</mi></msqrt><mo>=</mo><msqrt><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&alpha;</mi></msqrt><mo>=</mo><mo>&nbsp;</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&alpha;</mi></math> &nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (c) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&alpha;</mi></mrow></mfrac><mo>=</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&alpha;</mi></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (d) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mi>&alpha;</mi></mrow><mrow><mi>cos</mi><mi>&alpha;</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>cos</mi><mi>&alpha;</mi></mrow><mrow><mi>sin</mi><mi>&alpha;</mi><mo>&nbsp;</mo><mi>cos</mi><mi>&alpha;</mi></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&alpha;</mi></mrow></mfrac><mo>=</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&alpha;</mi></math><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367; </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&alpha;</mi><mo>&nbsp;</mo></math>&ne;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>&alpha;</mi></mrow></mfrac></math>&nbsp;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mo>)</mo></mrow><mn>2</mn></msup></math>= , 0&deg; &lt; &theta;</span><span style=\"font-family: Cambria Math;\"> &lt; 90&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 09/06/2022 (Evening)</span></p>\n",
                    question_hi: "<p>18.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo></math>, 0&deg;&lt; &theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> &lt; 90&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 09/06/2022 (Evening)</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&theta;</mi></mrow></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></math></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></math> </span></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&theta;</mi></mrow></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">18.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>-</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>1</mn><mrow><mi>sin</mi><mi>&theta;</mi></mrow></mfrac><mo>-</mo><mfrac><mrow><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo></mrow><mrow><mi>sin</mi><mi>&theta;</mi></mrow></mfrac><mo>)</mo></mrow><mn>2</mn></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>&theta;</mi><mo>-</mo><mi>co</mi><mi>t</mi><mi>&theta;</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mi>sin</mi><mi>&theta;</mi></mrow></mfrac><mo>-</mo><mfrac><mrow><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mi>sin</mi><mi>&theta;</mi></mrow></mfrac><msup><mo>)</mo><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac></math></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. <span style=\"font-family: Cambria Math;\">Using the formula </span><span style=\"font-family: Cambria Math;\">, tan<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math>=&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>x</mi><mo>&nbsp;</mo></mrow><mrow><mi>sin</mi><mi>x</mi></mrow></mfrac></math>&nbsp;find the value of tan22.5&deg;.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 10/06/2022 </span><span style=\"font-family: Cambria Math;\">( Morning</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    question_hi: "<p>19. <span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2340;&#2381;&#2352;&nbsp;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mfrac><mi>x</mi><mn>2</mn></mfrac><mo>=</mo><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>x</mi></mrow><mrow><mi>sin</mi><mi>x</mi></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\">, tan22.5&deg; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 10/06/2022 </span><span style=\"font-family: Cambria Math;\">( Morning</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>1</mn></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><msqrt><mn>2</mn></msqrt></mrow><mn>2</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Put x = 45&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>&deg;</mo></mrow><mn>2</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mn>45</mn><mo>&deg;</mo></mrow><mrow><mi>sin</mi><mn>45</mn><mo>&deg;</mo></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan22.5&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan22.5&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>-</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp;1</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 45&deg; </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>&deg;</mo></mrow><mn>2</mn></mfrac><mo>=</mo><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mn>45</mn><mo>&deg;</mo></mrow><mrow><mi>sin</mi><mn>45</mn><mo>&deg;</mo></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan22.5&deg; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">tan22.5&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn></math></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. <span style=\"font-family: Cambria Math;\">If </span><span style=\"font-family: Cambria Math;\">, sin&theta; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> and &theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> is an acute angle, find the value of cos3</span><span style=\"font-family: Cambria Math;\">&theta;.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 10/06/2022 </span><span style=\"font-family: Cambria Math;\">( Morning</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    question_hi: "<p>20. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367; sin&theta;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">,&nbsp; </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &theta; </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2381;&#2351;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> cos3&theta;&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 10/06/2022 </span><span style=\"font-family: Cambria Math;\">( Morning</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", "<p>1</p>\n", 
                                "<p>-1</p>\n", "<p>0</p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", "<p>1</p>\n",
                                "<p>-1</p>\n", "<p>0</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">20.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math>&nbsp; </span><span style=\"font-family: Cambria Math;\">, &theta;</span><span style=\"font-family: Cambria Math;\">= 60&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cos3&theta;</span><span style=\"font-family: Cambria Math;\">&nbsp;= cos 180&deg; = -1</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">20.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin&theta;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">, &nbsp;&theta;</span><span style=\"font-family: Cambria Math;\">= 60&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cos3&theta;</span><span style=\"font-family: Cambria Math;\">= cos 180&deg; = -1</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. <span style=\"font-family: Cambria Math;\">If </span><span style=\"font-family: Cambria Math;\">cot&theta;</span><span style=\"font-family: Cambria Math;\"> = cot 30&deg; cot 60&deg; and &theta; is an acute angle, then 2&theta; is equal to:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 10/06/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>21. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cot&theta;</span><span style=\"font-family: Cambria Math;\"> = cot 30&deg; cot 60&deg; </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &theta; </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2381;&#2351;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 2&theta; ________ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 10/06/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>90&deg;</p>\n", "<p>60&deg;</p>\n", 
                                "<p>45&deg;</p>\n", "<p>30&deg;</p>\n"],
                    options_hi: ["<p>90&deg;</p>\n", "<p>60&deg;</p>\n",
                                "<p>45&deg;</p>\n", "<p>30&deg;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cot&theta;</span><span style=\"font-family: Cambria Math;\"> = Cot30&deg;. Cot60&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cot</span><span style=\"font-family: Cambria Math;\"> &theta;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>&times;</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cot</span><span style=\"font-family: Cambria Math;\"> &theta;= 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&theta;= 45&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2&theta;=90&deg;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cot&theta;</span><span style=\"font-family: Cambria Math;\"> = Cot30&deg;. Cot60&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cot</span><span style=\"font-family: Cambria Math;\"> &theta;=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>&times;</mo><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cot&theta;</span><span style=\"font-family: Cambria Math;\"> = 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&theta;= 45&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2&theta;= 90&deg;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "misc",
                    question_en: "<p>22. <span style=\"font-family: Cambria Math;\">The value of sin 73&deg;+ cos 137&deg; is :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 10/06/2022 (Evening)</span></p>\n",
                    question_hi: "<p>22. sin73&deg; + cos137&deg; <span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 10/06/2022 (Evening)</span></p>\n",
                    options_en: ["<p>sin 13&deg;</p>\n", "<p>cos 13&deg;</p>\n", 
                                "<p>cos 18&deg;</p>\n", "<p>sin 18&deg;</p>\n"],
                    options_hi: ["<p>sin 13&deg;</p>\n", "<p>cos 13&deg;</p>\n",
                                "<p>cos 18&deg;</p>\n", "<p>sin 18&deg;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin73&deg; + Cos137&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cos137&deg; = </span><span style=\"font-family: Cambria Math;\">Cos(</span><span style=\"font-family: Cambria Math;\">90&deg; + 47&deg;) = - Sin47&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin73&deg; - Sin47&deg; = 2cos (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>73</mn><mo>+</mo><mn>47</mn></mrow><mn>2</mn></mfrac></math>) sin(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>73</mn><mo>-</mo><mn>47</mn></mrow><mn>2</mn></mfrac></math>)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mo>&nbsp;</mo><mi>cos</mi><mo>(</mo><mfrac><mrow><mn>120</mn><mo>&deg;</mo></mrow><mn>2</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mi>sin</mi><mo>(</mo><mfrac><mrow><mn>26</mn><mo>&deg;</mo></mrow><mn>2</mn></mfrac><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2cos60&deg;.sin13&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; sin 13&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= sin 13&deg;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin73&deg; + Cos137&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cos137&deg; = </span><span style=\"font-family: Cambria Math;\">Cos(</span><span style=\"font-family: Cambria Math;\">90&deg; + 47&deg;) = -Sin47&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sin73&deg; - Sin47&deg; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>cos</mi><mo>(</mo><mfrac><mrow><mi>A</mi><mo>+</mo><mi>B</mi></mrow><mn>2</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mi>sin</mi><mo>(</mo><mfrac><mrow><mi>A</mi><mo>-</mo><mi>B</mi></mrow><mn>2</mn></mfrac><mo>)</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>cos</mi><mfrac><mrow><mn>73</mn><mo>+</mo><mn>47</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mi>sin</mi><mfrac><mrow><mn>73</mn><mo>-</mo><mn>47</mn></mrow><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2cos60&deg;.sin13&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; sin 13&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= sin 13&deg;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>