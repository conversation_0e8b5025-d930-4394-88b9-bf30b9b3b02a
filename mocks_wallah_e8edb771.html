<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the most appropriate synonym of the given word.<br>Attire</p>",
                    question_hi: "<p>1. Select the most appropriate synonym of the given word.<br>Attire</p>",
                    options_en: ["<p>Dress</p>", "<p>Theatre</p>", 
                                "<p>Hotel</p>", "<p>Park</p>"],
                    options_hi: ["<p>Dress</p>", "<p>Theatre</p>",
                                "<p>Hotel</p>", "<p>Park</p>"],
                    solution_en: "<p>1.(a) <strong>Dress-</strong> a piece of clothing that covers the top half of the body and hangs down over the legs.<br><strong>Attire-</strong> clothes, especially of a particular or formal type.<br><strong>Theatre-</strong> a room or hall for lectures with seats in tiers.<br><strong>Hotel-</strong> a building where you pay to have a room to sleep in, and where you can sometimes eat meals.<br><strong>Park-</strong> a large public garden or area of land used for recreation.</p>",
                    solution_hi: "<p>1.(a) <strong>Dress</strong> (पोशाक) - a piece of clothing that covers the top half of the body and hangs down over the legs.<br><strong>Attire</strong> (पोशाक) - clothes, especially of a particular or formal type.<br><strong>Theatre</strong> (रंगमंच) - a room or hall for lectures with seats in tiers.<br><strong>Hotel</strong> (विश्रामालय/भोजनालय) - a building where you pay to have a room to sleep in, and where you can sometimes eat meals.<br><strong>Park</strong> (उद्यान/ उपवन/ बगीचा) - a large public garden or area of land used for recreation.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the most appropriate synonym of the given word.<br>Hypocrisy</p>",
                    question_hi: "<p>2. Select the most appropriate synonym of the given word.<br>Hypocrisy</p>",
                    options_en: ["<p>Deceit</p>", "<p>Truth</p>", 
                                "<p>Sincerity</p>", "<p>Harm</p>"],
                    options_hi: ["<p>Deceit</p>", "<p>Truth</p>",
                                "<p>Sincerity</p>", "<p>Harm</p>"],
                    solution_en: "<p>2.(a) <strong>Deceit-</strong> the action or practice of deceiving someone by concealing or misrepresenting the truth.<br><strong>Hypocrisy-</strong> a situation in which someone pretends to believe something that they do not really believe.<br><strong>Sincerity-</strong> the absence of pretense, deceit or hypocrisy.<br><strong>Harm-</strong> physical or other injury or damage.</p>",
                    solution_hi: "<p>2.(a) <strong>Deceit</strong> (छल/झूठ/धोखा) - the action or practice of deceiving someone by concealing or misrepresenting the truth.<br><strong>Hypocrisy</strong> (पाखंड) - a situation in which someone pretends to believe something that they do not really believe.<br><strong>Sincerity</strong> (ईमानदारी) - the absence of pretense, deceit or hypocrisy.<br><strong>Harm</strong> (हानि/क्षति) - physical or other injury or damage.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the most appropriate synonym of the given word.<br>Bliss</p>",
                    question_hi: "<p>3. Select the most appropriate synonym of the given word.<br>Bliss</p>",
                    options_en: ["<p>Consecration</p>", "<p>Genesis</p>", 
                                "<p>Avail</p>", "<p>Pleasure</p>"],
                    options_hi: ["<p>Consecration</p>", "<p>Genesis</p>",
                                "<p>Avail</p>", "<p>Pleasure</p>"],
                    solution_en: "<p>3.(d) <strong>Pleasure-</strong> a feeling of happy satisfaction and enjoyment.<br><strong>Bliss-</strong> perfect happiness.<br><strong>Consecration-</strong> the act or process of officially making something holy and able to be used for religious ceremonies.<br><strong>Genesis-</strong> the origin or mode of formation of something.<br><strong>Avail-</strong> take advantage of an opportunity or available resource.</p>",
                    solution_hi: "<p>3.(d) <strong>Pleasure</strong> (सुख) - a feeling of happy satisfaction and enjoyment.<br><strong>Bliss</strong> (परमानंद) - perfect happiness.<br><strong>Consecration</strong> (अभिषेक करना ) - the act or process of officially making something holy and able to be used for religious ceremonies.<br><strong>Genesis</strong> (उत्पत्ति) - the origin or mode of formation of something.<br><strong>Avail</strong> (लाभ उठाना) - take advantage of an opportunity or available resource.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate synonym of the bracketed word in the following sentence to fill in the blank.<br>The meeting was brought to a _____ (rapid) conclusion.</p>",
                    question_hi: "<p>4. Select the most appropriate synonym of the bracketed word in the following sentence to fill in the blank.<br>The meeting was brought to a _____ (rapid) conclusion.</p>",
                    options_en: ["<p>grim</p>", "<p>stubborn</p>", 
                                "<p>pervious</p>", "<p>hasty</p>"],
                    options_hi: ["<p>grim</p>", "<p>stubborn</p>",
                                "<p>pervious</p>", "<p>hasty</p>"],
                    solution_en: "<p>4.(d) <strong>Hasty-</strong> doing something very quickly.<br><strong>Rapid-</strong> happening very quickly.<br><strong>Grim-</strong> something that is serious, harsh or unfriendly.<br><strong>Stubborn-</strong> unwilling to change mind or behaviour.<br><strong>Pervious-</strong> allowing water to pass through.</p>",
                    solution_hi: "<p>4.(d) <strong>Hasty</strong> (जल्दबाज़ी) - doing something very quickly.<br><strong>Rapid</strong> (तेजी से) - happening very quickly.<br><strong>Grim</strong> (गंभीर) - something that is serious, harsh or unfriendly.<br><strong>Stubborn</strong> (जिद्दी) - unwilling to change mind or behaviour.<br><strong>Pervious</strong> (प्रवेश्य/वेधनीय) - allowing water to pass through.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the most appropriate synonym of the underlined word.<br>The girl tried to <span style=\"text-decoration: underline;\">alleviate </span>her brother\'s problem.</p>",
                    question_hi: "<p>5. Select the most appropriate synonym of the underlined word.<br>The girl tried to <span style=\"text-decoration: underline;\">alleviate </span>her brother\'s problem.</p>",
                    options_en: ["<p>increase</p>", "<p>soar</p>", 
                                "<p>worsen</p>", "<p>reduce</p>"],
                    options_hi: ["<p>increase</p>", "<p>soar</p>",
                                "<p>worsen</p>", "<p>reduce</p>"],
                    solution_en: "<p>5.(d) <strong>Reduce-</strong> to make something smaller or less in amount.<br><strong>Alleviate-</strong> to make something less severe.<br><strong>Increase-</strong> to make something larger.<br><strong>Soar-</strong> rise high in the air.<br><strong>Worsen-</strong> make or become worse.</p>",
                    solution_hi: "<p>5.(d) <strong>Reduce</strong> (कम करना) - to make something smaller or less in amount.<br><strong>Alleviate</strong> (कम करना) - to make something less severe.<br><strong>Increase</strong> (बढ़ाना) - to make something larger.<br><strong>Soar</strong> (ऊंचाई पर उड़ना/उड़ान भरना) - rise high in the air.<br><strong>Worsen</strong> (बिगड़ना) - make or become worse.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the most appropriate synonym of the underlined word.<br>John is very <span style=\"text-decoration: underline;\">generous</span>; he regularly gives alms to the poor.</p>",
                    question_hi: "<p>6. Select the most appropriate synonym of the underlined word.<br>John is very <span style=\"text-decoration: underline;\">generous</span>; he regularly gives alms to the poor.</p>",
                    options_en: [" great ", " popular ", 
                                " kind ", " shrewd"],
                    options_hi: [" great ", " popular ",
                                " kind ", " shrewd"],
                    solution_en: "<p>6.(c) <strong>Kind-</strong> having a gentle nature and desire to help others.<br><strong>Generous-</strong> willing to give money, help, kindness, etc., especially more than is usual or expected.<br><strong>Great-</strong> something that is very large, excellent or impressive.<br><strong>Popular-</strong> well-liked or admired by many people.<br><strong>Shrewd-</strong> showing sharp judgment and intelligence.</p>",
                    solution_hi: "<p>6.(c) <strong>Kind</strong> (दयालु) - having a gentle nature and desire to help others.<br><strong>Generous</strong> (उदार) - willing to give money, help, kindness, etc., especially more than is usual or expected.<br><strong>Great</strong> (महान) - something that is very large, excellent or impressive.<br><strong>Popular</strong> (लोकप्रिय) - well-liked or admired by many people.<br><strong>Shrewd</strong> (चतुर) - showing sharp judgment and intelligence.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate synonym of the given word.<br>Rectify</p>",
                    question_hi: "<p>7. Select the most appropriate synonym of the given word.<br>Rectify</p>",
                    options_en: ["<p>Consider</p>", "<p>Correct</p>", 
                                "<p>Connect</p>", "<p>Count</p>"],
                    options_hi: ["<p>Consider</p>", "<p>Correct</p>",
                                "<p>Connect</p>", "<p>Count</p>"],
                    solution_en: "<p>7.(b) <strong>Correct</strong><br><strong>Rectify-</strong> to correct something that is wrong.<br><strong>Consider-</strong> to think carefully about a decision.<br><strong>Connect-</strong> to join together two things.<br><strong>Count-</strong> to determine the total number of items.</p>",
                    solution_hi: "<p>7.(b) <strong>Correct</strong><br><strong>Rectify</strong> (सुधार करना) - to correct something that is wrong.<br><strong>Consider</strong> (विचार करना) - to think carefully about a decision.<br><strong>Connect</strong> (जोड़ना) - to join together two things.<br><strong>Count</strong> (गिनना) - to determine the total number of items.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate synonym of the given word.<br>Luminous</p>",
                    question_hi: "<p>8. Select the most appropriate synonym of the given word.<br>Luminous</p>",
                    options_en: ["<p>Luxurious</p>", "<p>Laborious</p>", 
                                "<p>Amiable</p>", "<p>Radiant</p>"],
                    options_hi: ["<p>Luxurious</p>", "<p>Laborious</p>",
                                "<p>Amiable</p>", "<p>Radiant</p>"],
                    solution_en: "<p>8.(d) <strong>Radiant-</strong> shining or glowing brightly.<br><strong>Luminous-</strong> bright or shining.<br><strong>Luxurious-</strong> extremely comfortable or elegant, especially when involving great expense.<br><strong>Laborious-</strong> requiring considerable time and effort.<br><strong>Amiable-</strong> friendly and pleasant.</p>",
                    solution_hi: "<p>8.(d) <strong>Radiant</strong> (दीप्तिमान) - shining or glowing brightly.<br><strong>Luminous</strong> (चमकदार) - bright or shining.<br><strong>Luxurious</strong> (विलासितापूर्ण) - extremely comfortable or elegant, especially when involving great expense.<br><strong>Laborious</strong> (श्रमसाध्य) - requiring considerable time and effort.<br><strong>Amiable</strong> (मिलनसार) - friendly and pleasant.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate synonym of the given word.<br>Alert</p>",
                    question_hi: "<p>9. Select the most appropriate synonym of the given word.<br>Alert</p>",
                    options_en: ["<p>Dull</p>", "<p>Smart</p>", 
                                "<p>Inactive</p>", "<p>Vigilant</p>"],
                    options_hi: ["<p>Dull</p>", "<p>Smart</p>",
                                "<p>Inactive</p>", "<p>Vigilant</p>"],
                    solution_en: "<p>9.(d) <strong>Vigilant-</strong> ​very careful to notice any signs of danger or trouble.<br><strong>Alert-</strong> being watchful and attentive.<br><strong>Dull-</strong> boring or uninteresting.<br><strong>Smart-</strong> intelligent or clever.<br><strong>Inactive-</strong> not being engaged in any activity.</p>",
                    solution_hi: "<p>9.(d) <strong>Vigilant</strong> (सतर्क) - ​very careful to notice any signs of danger or trouble.<br><strong>Alert</strong> (सजग) - being watchful and attentive.<br><strong>Dull</strong> (सुस्त) - boring or uninteresting.<br><strong>Smart</strong> (होशियार) - intelligent or clever.<br><strong>Inactive</strong> (निष्क्रिय) - not being engaged in any activity.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate synonym of the given word.<br>Retract</p>",
                    question_hi: "<p>10. Select the most appropriate synonym of the given word.<br>Retract</p>",
                    options_en: ["<p>Revoke</p>", "<p>Disturb</p>", 
                                "<p>Please</p>", "<p>Implement</p>"],
                    options_hi: ["<p>Revoke</p>", "<p>Disturb</p>",
                                "<p>Please</p>", "<p>Implement</p>"],
                    solution_en: "<p>10.(a) <strong>Revoke-</strong> to take back a decision.<br><strong>Retract-</strong> to take back or withdraw something.<br><strong>Disturb-</strong> to interfere with someone&rsquo;s peace.<br><strong>Please-</strong> cause to feel happy or satisfied.<br><strong>Implement-</strong> to put a plan or agreement into action.</p>",
                    solution_hi: "<p>10.(a) <strong>Revoke</strong> (निरस्त करना) - to take back a decision.<br><strong>Retract</strong> (वापस लेना) - to take back or withdraw something.<br><strong>Disturb</strong> (परेशान करना) - to interfere with someone&rsquo;s peace.<br><strong>Please</strong> (कृपया) - cause to feel happy or satisfied.<br><strong>Implement</strong> (लागू करना) - to put a plan or agreement into action.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate synonym of the underlined word.<br>For Rana, unable to see a thing, the <span style=\"text-decoration: underline;\">parade </span>seemed to last forever.</p>",
                    question_hi: "<p>11. Select the most appropriate synonym of the underlined word.<br>For Rana, unable to see a thing, the <span style=\"text-decoration: underline;\">parade </span>seemed to last forever.</p>",
                    options_en: ["<p>darkness</p>", "<p>procession</p>", 
                                "<p>concealment</p>", "<p>hiding</p>"],
                    options_hi: ["<p>darkness</p>", "<p>procession</p>",
                                "<p>concealment</p>", "<p>hiding</p>"],
                    solution_en: "<p>11.(b) <strong>Procession-</strong> a group of people walking together for a special event or ceremony.<br><strong>Parade-</strong> a public procession, especially one celebrating a special day or event.<br><strong>Darkness-</strong> absence of light.<br><strong>Concealment-</strong> the act of hiding something.<br><strong>Hiding-</strong> keeping someone or something out of sight.</p>",
                    solution_hi: "<p>11.(b) <strong>Procession</strong> (जुलूस) - a group of people walking together for a special event or ceremony.<br><strong>Parade</strong> (जुलूस) - a public procession, especially one celebrating a special day or event.<br><strong>Darkness</strong> (अंधकार) - absence of light.<br><strong>Concealment</strong> (छिपाना) - the act of hiding something.<br><strong>Hiding</strong> (छिपाना) - keeping someone or something out of sight.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate synonym of the given word.<br>Estimate</p>",
                    question_hi: "<p>12. Select the most appropriate synonym of the given word.<br>Estimate</p>",
                    options_en: ["<p>Appropriate</p>", "<p>Evaluate</p>", 
                                "<p>Profit</p>", "<p>Verify</p>"],
                    options_hi: ["<p>Appropriate</p>", "<p>Evaluate</p>",
                                "<p>Profit</p>", "<p>Verify</p>"],
                    solution_en: "<p>12.(b) <strong>Evaluate-</strong> to judge or calculate the quality, importance, amount of sth.<br><strong>Estimate-</strong> a guess or judgement about something.<br><strong>Appropriate-</strong> suitable or right for a particular situation.<br><strong>Profit-</strong> financial gain.<br><strong>Verify-</strong> to prove that something exists or is true.</p>",
                    solution_hi: "<p>12.(b) <strong>Evaluate</strong> (मूल्यांकन करना) - to judge or calculate the quality, importance, amount of sth.<br><strong>Estimate</strong> (आकलन करना) - a guess or judgement about something.<br><strong>Appropriate</strong> (उपयुक्त) - suitable or right for a particular situation.<br><strong>Profit</strong> (लाभ) - financial gain.<br><strong>Verify</strong> (सत्यापित करना) - to prove that something exists or is true.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate synonym of the underlined word.<br>The removal of <span style=\"text-decoration: underline;\">petty </span>restrictions has made life easier.</p>",
                    question_hi: "<p>13. Select the most appropriate synonym of the underlined word.<br>The removal of <span style=\"text-decoration: underline;\">petty </span>restrictions has made life easier.</p>",
                    options_en: ["<p>Consequential</p>", "<p>Small</p>", 
                                "<p>Large</p>", "<p>Big</p>"],
                    options_hi: ["<p>Consequential</p>", "<p>Small</p>",
                                "<p>Large</p>", "<p>Big</p>"],
                    solution_en: "<p>13.(b) <strong>Small</strong><br><strong>Petty-</strong> small or of little importance.<br><strong>Consequential-</strong> happening as a result of something.</p>",
                    solution_hi: "<p>13.(b) <strong>Small</strong><br><strong>Petty</strong> (क्षुद्र) - small or of little importance.<br><strong>Consequential</strong> (परिणामी) - happening as a result of something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate synonym of the given word.<br>Absurd</p>",
                    question_hi: "<p>14. Select the most appropriate synonym of the given word.<br>Absurd</p>",
                    options_en: ["<p>Abstruse</p>", "<p>Irrational</p>", 
                                "<p>Arbitrary</p>", "<p>Sudden</p>"],
                    options_hi: ["<p>Abstruse</p>", "<p>Irrational</p>",
                                "<p>Arbitrary</p>", "<p>Sudden</p>"],
                    solution_en: "<p>14.(b) <strong>Irrational-</strong> not making sense or not logical.<br><strong>Absurd-</strong> something that is ridiculous.<br><strong>Abstruse-</strong> difficult to understand.<br><strong>Arbitrary-</strong> something that is chosen without a clear reason or rule.<br><strong>Sudden-</strong> happening quickly or unexpectedly.</p>",
                    solution_hi: "<p>14.(b) <strong>Irrational</strong> (अतार्किक) - not making sense or not logical.<br><strong>Absurd</strong> (बेतुका/निरर्थक) - something that is ridiculous.<br><strong>Abstruse</strong> (जटिल/कठिन) - difficult to understand.<br><strong>Arbitrary</strong> (मनमाना) - something that is chosen without a clear reason or rule.<br><strong>Sudden</strong> (अचानक) - happening quickly or unexpectedly.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Select the most appropriate synonym of the given word.<br>Evasive</p>",
                    question_hi: "<p>15. Select the most appropriate synonym of the given word.<br>Evasive</p>",
                    options_en: ["<p>Cohesive</p>", "<p>Ambiguous</p>", 
                                "<p>Abrasive</p>", "<p>Accessible</p>"],
                    options_hi: ["<p>Cohesive</p>", "<p>Ambiguous</p>",
                                "<p>Abrasive</p>", "<p>Accessible</p>"],
                    solution_en: "<p>15.(b) <strong>Ambiguous</strong> - having multiple possible meanings or interpretations.<br><strong>Evasive</strong> - answering questions in a way that is not clear or direct.<br><strong>Cohesive</strong> - united and working together effectively.<br><strong>Abrasive</strong> - rude and unfriendly.<br><strong>Accessible</strong> - that can be easily reached or understood.</p>",
                    solution_hi: "<p>15.(b) <strong>Ambiguous</strong> (अस्पष्ट) - having multiple possible meanings or interpretations.<br><strong>Evasive</strong> (अस्पष्ट) - answering questions in a way that is not clear or direct.<br><strong>Cohesive</strong> (एकजुट) - united and working together effectively.<br><strong>Abrasive</strong> (सख्त) - rude and unfriendly.<br><strong>Accessible</strong> (उपलब्ध) - that can be easily reached or understood.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>