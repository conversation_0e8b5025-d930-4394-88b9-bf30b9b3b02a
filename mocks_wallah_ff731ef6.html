<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Find the number of triangle in the figure given below:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927323533.png\" alt=\"rId4\" width=\"199\" height=\"102\"></p>",
                    question_hi: "<p>1. नीचे दी गई आकृति में त्रिभुज की संख्या ज्ञात कीजिए<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927323533.png\" alt=\"rId4\" width=\"199\" height=\"102\"></p>",
                    options_en: ["<p>42 triangles</p>", "<p>44 triangles</p>", 
                                "<p>40 triangles</p>", "<p>46 triangles</p>"],
                    options_hi: ["<p>42 त्रिभुज</p>", "<p>44 त्रिभुज</p>",
                                "<p>40 त्रिभुज</p>", "<p>46 त्रिभुज</p>"],
                    solution_en: "<p>1.(b)<br>44 triangles in the given figure.</p>",
                    solution_hi: "<p>1.(b)<br>दी गई आकृति में 44 त्रिभुज हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Read the given statement and conclusion carefully. Assuming that the information given in the statement is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow from the statements.<br><strong>Statements :</strong><br>1. All buildings are rooms. <br>2. No room is a tower. <br>3. Some towers are poles. <br><strong>Conclusions :</strong><br>1. No buildings are towers. <br>2. No pole is a room. <br>3. No pole is a building.</p>",
                    question_hi: "<p>2. दिए गए कथन और निष्कर्ष को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथन में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, बताइये कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है<br><strong>कथन:</strong><br>1. सभी इमारतें कमरे हैं<br>2. कोई कमरा टावर नहीं है<br>3. कुछ टावर पोल हैं।<br><strong>निष्कर्ष:</strong><br>1. कोई इमारत टावर नहीं है।<br>2. कोई पोल कमरा नहीं है<br>3. कोई पोल इमारत नहीं है</p>",
                    options_en: ["<p>Only conclusions A and B follow.</p>", "<p>Only conclusions A and C follow.</p>", 
                                "<p>All conclusions A, B and C follow.</p>", "<p>Conclusion A alone follows.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष A और B अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष A और C अनुसरण करते हैं।</p>",
                                "<p>सभी निष्कर्ष A, B और C अनुसरण करते हैं</p>", "<p>निष्कर्ष A अकेला अनुसरण करता है</p>"],
                    solution_en: "<p>2.(d)<br><strong id=\"docs-internal-guid-52bee89d-7fff-2f3b-8ec1-1f2426ec8a7f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXegWepSeH3SV5VphZ-TfnGtUhUPoeYqLrFjUKzmWRv60HTKzsrl_WnPIP_39I7P49XiIwUb1wgRifKprBhw1GzzTR3BxPcZ6wvC-Fqjq0stFgaadbA_gkH2eKIYp7jbjM15bDncnPIyY57YUlLe-GC2TZOE?key=5ALY1GFPMfEyzaUFTNNH0Q\" width=\"142\" height=\"101\"></strong></p>",
                    solution_hi: "<p>2.(d)<br><strong id=\"docs-internal-guid-225a9b7c-7fff-5259-43d5-bb3b78c18998\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcFOz7lVpd2PkftgremjEHOBKLR8FQ16daBWvdWACuwuUnsJ1oxQQKYSx5NiByf7Cv3Wbq82EXDagnIwA6wnnNuVJQit8IQtillXFha4RoSjpTZaklp8kyH2ZhdO0V_h16VEeb1N3_o6JKmGLdNqCPS_dI?key=5ALY1GFPMfEyzaUFTNNH0Q\" width=\"156\" height=\"109\"></strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Read the given statement and conclusions carefully and decide which of the conclusions logically follow (s) from the statement.<br><strong>Statement :</strong><br>Rapid growth in population in many developing countries is depleting the natural resources.<br><strong>Conclusion :</strong><br>1. The population of developing countries will not continue to increase in future <br>2. Management of natural resources and population growth are major challenges for developing countries.</p>",
                    question_hi: "<p>3. दिए गए कथन और निष्कर्षों को ध्यान से पढ़ें और बताइये कि कौन सा निष्कर्ष कथन का तार्किक रूप से अनुसरण करता है।<br><strong>कथन :</strong><br>अनेक विकासशील देशों में जनसंख्या में तीव्र वृद्धि से प्राकृतिक संसाधनों का ह्रास हो रहा है।<br><strong>निष्कर्ष :</strong><br>1. भविष्य में विकासशील देशों की जनसंख्या में वृद्धि जारी नहीं रहेगी<br>2. विकासशील देशों के लिए प्राकृतिक संसाधनों का प्रबंधन और जनसंख्या वृद्धि प्रमुख चुनौतियां हैं</p>",
                    options_en: ["<p>Only I follows</p>", "<p>Neither I nor II follows</p>", 
                                "<p>Both I and II follow</p>", "<p>Only II follows</p>"],
                    options_hi: ["<p>केवल I अनुसरण करता है</p>", "<p>न तो I और न ही II अनुसरण करता है</p>",
                                "<p>I और II दोनों अनुसरण करते हैं</p>", "<p>केवल II अनुसरण करता है</p>"],
                    solution_en: "<p>3.(d)<br>Only II follows.<br>Rapid growth in population in developing countries is depleting the natural resources then Management of natural resources and population growth are major challenges for developing countries because they are limited.</p>",
                    solution_hi: "<p>3.(d)<br>केवल II अनुसरण करता है।<br>विकासशील देशों में जनसंख्या में तीव्र वृद्धि से प्राकृतिक संसाधनों का ह्रास हो रहा है तो प्राकृतिक संसाधनों का प्रबंधन और जनसंख्या वृद्धि विकासशील देशों के लिए प्रमुख चुनौतियाँ हैं क्योंकि वे सीमित हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Read the given statement and assumptions carefully and decide which assumption(s) is/are implicit from the statement.<br><strong>Statement :</strong><br>We should always focus on the present.<br><strong>Assumptions :</strong><br>1. People often tend to think about the past and future. <br>2. The present is more important than the past or future.</p>",
                    question_hi: "<p>4. दिए गए कथनों और पूर्वधारणाओं को ध्यानपूर्वक पढ़िए और बताइये कि कौन-सी धारणा/धारणाएं कथन में अंतर्निहित हैं।<br><strong>कथन :</strong><br>हमें हमेशा वर्तमान पर ध्यान देना चाहिए।<br><strong>अवधारणा:</strong><br>1. लोग अक्सर भूत और भविष्य के बारे में सोचते हैं<br>2. अतीत या भविष्य की तुलना में वर्तमान अधिक महत्वपूर्ण है।</p>",
                    options_en: ["<p>Both I and II are implicit.</p>", "<p>Neither I nor II is implicit.</p>", 
                                "<p>Only assumption I is implicit.</p>", "<p>Only assumption II is implicit</p>"],
                    options_hi: ["<p>I और II दोनों निहित हैं</p>", "<p>न तो I और न ही II निहित है।</p>",
                                "<p>केवल धारणा I निहित है।</p>", "<p>केवल धारणा II निहित है</p>"],
                    solution_en: "<p>4.(a) <br>Both I and II are implicit.<br>We always focus on the present because we think about the past and future and often about it .<br>Also the present is more important than the past or future.</p>",
                    solution_hi: "<p>4.(a) <br>I और II दोनों निहित हैं।<br>हम हमेशा वर्तमान पर ध्यान केंद्रित करते हैं क्योंकि हम अतीत और भविष्य के बारे में सोचते हैं और अक्सर इसके बारे में सोचते हैं।<br>साथ ही वर्तमान अतीत या भविष्य से अधिक महत्वपूर्ण है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In a certain code language, if GROUND is written as FPWQTI and STICKER is written as TGMEKVU, then what will STUDENT be written as ?</p>",
                    question_hi: "<p>5. एक निश्चित कूट भाषा में, यदि GROUND को FPWQTI और STICKER को TGMEKVU लिखा जाता है, तो STUDENT को क्या लिखा जाएगा ?</p>",
                    options_en: ["<p>UVHGPVW</p>", "<p>VPGFWVU</p>", 
                                "<p>UPGHWVY</p>", "<p>UVWFGPV</p>"],
                    options_hi: ["<p>UVHGPVW</p>", "<p>VPGFWVU</p>",
                                "<p>UPGHWVY</p>", "<p>UVWFGPV</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927323777.png\" alt=\"rId6\" width=\"140\" height=\"82\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927323900.png\" alt=\"rId7\" width=\"139\" height=\"69\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927324056.png\" alt=\"rId8\" width=\"141\" height=\"68\"></p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927323777.png\" alt=\"rId6\" width=\"140\" height=\"82\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927323900.png\" alt=\"rId7\" width=\"139\" height=\"69\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927324056.png\" alt=\"rId8\" width=\"141\" height=\"68\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Pointing to a woman, a girl named Rita said, &ldquo;I am the daughter of the daughter-in-law of her mother&rdquo;. What is the relation of that woman to the Rita ?</p>",
                    question_hi: "<p>6. एक महिला की ओर इशारा करते हुए रीता नाम की एक लड़की ने कहा, \"मैं उसकी माँ की बहू की बेटी हूँ\"। उस महिला का रीता से क्या संबंध है ?</p>",
                    options_en: ["<p>Mother-in-law</p>", "<p>Paternal aunt</p>", 
                                "<p>Maternal aunt</p>", "<p>Sister</p>"],
                    options_hi: ["<p>सास</p>", "<p>चाची</p>",
                                "<p>मामी</p>", "<p>बहन</p>"],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927324197.png\" alt=\"rId9\" width=\"159\" height=\"107\"><br>The Woman is a paternal aunt of Rita .</p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927324303.png\" alt=\"rId10\" width=\"158\" height=\"106\"><br>महिला रीता की चाची है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Read the given statements and conclusions carefully and decide which of the conclusions logically follow(s) from the statements(s).<br><strong>Statements :</strong><br>1. Some cotton clothes are silk clothes. <br>2. All silk clothes are woolen clothes. <br>3. No woolen cloth is a nylon cloth. <br>4. Some nylon clothes are polyester. <br><strong>Conclusions :</strong><br>1. Some cotton cloths are nylon cloth. <br>2. No silk cloth is a nylon cloth. <br>3. Some cotton cloths are woolen cloth.</p>",
                    question_hi: "<p>7. दिए गए कथनों और निष्कर्षों को ध्यान से पढ़ें और तय करें कि कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>1. कुछ सूती कपड़े रेशमी कपड़े हैं।<br>2. सभी रेशमी कपड़े ऊनी कपड़े हैं।<br>3. कोई ऊनी कपड़ा नॉयलॉन का कपड़ा नहीं है<br>4. कुछ नायलॉन के कपड़े पॉलिएस्टर हैं।<br><strong>निष्कर्ष:</strong><br>1. कुछ सूती कपड़े नायलॉन के कपड़े हैं<br>2. कोई रेशमी कपड़ा नॉयलॉन का कपड़ा नहीं है।<br>3. कुछ सूती कपड़े ऊनी कपड़े हैं।</p>",
                    options_en: ["<p>Only conclusions II and III will follow.</p>", "<p>Only conclusions I and II will follow.</p>", 
                                "<p>All 3 conclusions I, II and III, will follow.</p>", "<p>Only conclusions I and III will follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष II और III अनुसरण करेंगे।</p>", "<p>केवल निष्कर्ष I और II अनुसरण करेंगे।</p>",
                                "<p>सभी 3 निष्कर्ष I, II और III, अनुसरण करेंगे।</p>", "<p>केवल निष्कर्ष I और III अनुसरण करेंगे।</p>"],
                    solution_en: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927324458.png\" alt=\"rId11\" width=\"186\" height=\"93\"><br>Only conclusions II and III will follow.</p>",
                    solution_hi: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927324582.png\" alt=\"rId12\" width=\"193\" height=\"107\"><br>केवल निष्कर्ष II और III अनुसरण करेंगे।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "8. Select the option that is related to the third term in the same way as the second term is related to the first term.<br />Cloud  : Rain  :: Grief  : ?",
                    question_hi: "8. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br />बादल :  वर्षा ::  शोक : ?",
                    options_en: [" Sad ", " Heart  ", 
                                " Misfortune ", " Tears "],
                    options_hi: [" दुखी", " हृदय ",
                                " दुर्भाग्य", " आंसू"],
                    solution_en: "8.(d)<br />Clouds are made of water droplets. Within a cloud and with the help of a cloud  fall the rain. Similarly crying helps in the healing process of grief .",
                    solution_hi: "8.(d)<br />बादल पानी की बूंदों से बनते हैं। बादल के भीतर और बादल की सहायता से वर्षा होती है। इसी तरह रोना दुख की उपचार प्रक्रिया में मदद करता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. P, Q, R and S are facing north and A, B, C and D are facing them, not necessarily in the same order. S is sitting second to the right of R. The person who faces Q is not a neighbour of B, D is facing S and sitting next to B. A is sitting on an extreme position. P and C are not facing each other. R is at one end but C is not. Who is facing the person second to the right of Q ?</p>",
                    question_hi: "<p>9. P, Q, R और S उत्तर की ओर उन्मुख हैं और A, B, C और D उनका सामना कर रहे हैं, जरूरी नहीं कि इसी क्रम में हों। S, R के दायें से दूसरे स्थान पर बैठा है। वह व्यक्ति जो Q की ओर उन्मुख है, B का पड़ोसी नहीं है, D का मुख S की ओर है और वह B के बगल में बैठा है। A अंतिम स्थिति में बैठा है। P और C एक दूसरे की ओर उन्मुख नहीं हैं। R एक छोर पर है लेकिन C नहीं है। Q के दायें से दूसरे स्थान पर बैठे व्यक्ति की ओर उन्मुख कौन है ?</p>",
                    options_en: ["<p>C</p>", "<p>B</p>", 
                                "<p>A</p>", "<p>D</p>"],
                    options_hi: ["<p>C</p>", "<p>B</p>",
                                "<p>A</p>", "<p>D</p>"],
                    solution_en: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927324723.png\" alt=\"rId13\" width=\"103\" height=\"83\"><br>B is facing the person second to the right of Q</p>",
                    solution_hi: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927324723.png\" alt=\"rId13\" width=\"103\" height=\"83\"><br>B, Q के दायें से दूसरे स्थान पर बैठे व्यक्ति की ओर उन्मुख है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Eight persons A to H, not necessarily in same order, are sitting at a round table facing the centre. There are two persons between F and D. H is not an immediate neighbour of A or F. A is third to the right of F. There are three persons between H and G. C sits second to the left of E. Who is third to the right of D ?</p>",
                    question_hi: "<p>10. आठ व्यक्ति A से H, आवश्यक नहीं इसी क्रम में हों, एक गोल मेज पर केंद्र की ओर मुख करके बैठे हैं। F और D के बीच दो व्यक्ति हैं। H, A या F का निकटतम पड़ोसी नहीं है। A, F के दायें से तीसरे स्थान पर है। H और G के बीच तीन व्यक्ति हैं। C, E के बाएं से दूसरे स्थान पर बैठा है। कौन D के तीसरे दाईं ओर है ?</p>",
                    options_en: ["<p>F</p>", "<p>A</p>", 
                                "<p>H</p>", "<p>E</p>"],
                    options_hi: ["<p>F</p>", "<p>A</p>",
                                "<p>H</p>", "<p>E</p>"],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927324839.png\" alt=\"rId14\" width=\"97\" height=\"98\"><br>F is third to the right of D</p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927324839.png\" alt=\"rId14\" width=\"97\" height=\"98\"><br>F, D के दायें से तीसरे स्थान पर है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a test series, South Africa defeated England twice, Bangladesh defeated South Africa twice, England defeated Bangladesh twice, South Africa defeated Pakistan twice and Bangladesh defeated Pakistan twice. Which country lost the highest number of times ?</p>",
                    question_hi: "<p>11. एक टेस्ट सीरीज में दक्षिण अफ्रीका ने इंग्लैंड को दो बार, बांग्लादेश ने दक्षिण अफ्रीका को दो बार, इंग्लैंड ने बांग्लादेश को दो बार, दक्षिण अफ्रीका ने पाकिस्तान को दो बार और बांग्लादेश ने पाकिस्तान को दो बार हराया। सबसे ज्यादा बार किस देश को हार का सामना करना पड़ा ?</p>",
                    options_en: ["<p>England</p>", "<p>South Africa</p>", 
                                "<p>Bangladesh</p>", "<p>Pakistan</p>"],
                    options_hi: ["<p>इंगलैंड</p>", "<p>दक्षिण अफ्रीका</p>",
                                "<p>बांग्लादेश</p>", "<p>पाकिस्तान</p>"],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927324980.png\" alt=\"rId15\" width=\"250\" height=\"135\"><br>Pakistan lost the highest number of times.</p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325078.png\" alt=\"rId16\" width=\"211\" height=\"168\"><br>पाकिस्तान को सबसे ज्यादा बार हार का सामना करना पड़ा।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the number from among the given options that can replace the question mark in the following series.<br>6, 30, 156, ?</p>",
                    question_hi: "<p>12. दिए गए विकल्पों में से उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न को प्रतिस्थापित कर सकती है।<br>6, 30, 156, ?</p>",
                    options_en: ["<p>930</p>", "<p>942</p>", 
                                "<p>642</p>", "<p>630</p>"],
                    options_hi: ["<p>930</p>", "<p>942</p>",
                                "<p>642</p>", "<p>630</p>"],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325233.png\" alt=\"rId17\" width=\"248\" height=\"66\"></p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325233.png\" alt=\"rId17\" width=\"248\" height=\"66\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Find the next two terms of the given number series:<br>1 2 5 4 9 8 13 16 17 32 21</p>",
                    question_hi: "<p>13. दी गई संख्या श्रंखला के अगले दो पद ज्ञात कीजिए<br>1 2 5 4 9 8 13 16 17 32 21</p>",
                    options_en: ["<p>27 48</p>", "<p>25 64</p>", 
                                "<p>64 25</p>", "<p>48 27</p>"],
                    options_hi: ["<p>27 48</p>", "<p>25 64</p>",
                                "<p>64 25</p>", "<p>48 27</p>"],
                    solution_en: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325390.png\" alt=\"rId18\" width=\"292\" height=\"79\"></p>",
                    solution_hi: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325390.png\" alt=\"rId18\" width=\"292\" height=\"79\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, MUSIC is coded as LATEB and TORRID as SUSQEF. How will DEBATE be coded ?</p>",
                    question_hi: "<p>14. एक निश्चित कोड भाषा में, MUSIC को LATEB और TORRID को SUSQEF के रूप में कोडित किया जाता है। DEBATE को कैसे कोडित किया जाएगा ?</p>",
                    options_en: ["<p>CICUSI</p>", "<p>BADUSI</p>", 
                                "<p>CICUVI</p>", "<p>FIDEVI</p>"],
                    options_hi: ["<p>CICUSI</p>", "<p>BADUSI</p>",
                                "<p>CICUVI</p>", "<p>FIDEVI</p>"],
                    solution_en: "<p>14.(a)<br>Solve consonant and vowel separately <br>M - 1 = L, U + 1 = A (Only from vowel AEIOU), S + 1 = T, I - 1 = E, C - 1 = B<br>T - 1 = S, O + 1 = U, R + 1 = S, R - 1 = Q, I - 1 = E, D + 1 = F (only from consonant)<br>Similarly, D - 1 = C, E + 1 = I, B + 1 = C, A - 1 = U, T - 1 = S, E + 1 = I&nbsp;<br>Hence , CICUSI is the correct answer.</p>",
                    solution_hi: "<p>14.(a)<br>व्यंजन और स्वर को अलग-अलग हल करें<br>M - 1 = L, U + 1 = A (केवल स्वर से AEIOU), S + 1 = T, I - 1 = E, C - 1 = B<br>T - 1 = S, O + 1 = U, R + 1 = S, R - 1 = Q, I - 1 = E, D + 1 = F(केवल व्यंजन से)<br>उसी प्रकार, D - 1 = C, E + 1 = I, B + 1 = C, A - 1 = U, T - 1 = S, E + 1 = I&nbsp;<br>उपरोक्त अनुक्रम की सहायता से DEBATE को CICUSI लिखा जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "15. Out of the four locations listed, three are alike in some manner and one is different. Select the odd one.",
                    question_hi: "15. सूचीबद्ध चार स्थानों में से तीन किसी न किसी रूप में एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये ",
                    options_en: [" Rajghat, New Delhi ", " Mani Bhawan, Mumbai ", 
                                " Rajpath, New Delhi ", " Sabarmati Ashram, Ahmedabad "],
                    options_hi: [" राजघाट, नई दिल्ली", " मणि भवन, मुंबई",
                                " राजपथ, नई दिल्ली", " साबरमती आश्रम, अहमदाबाद"],
                    solution_en: "15.(c)<br />Rajpath is different from others.except rajghat all are memorial places.",
                    solution_hi: "15.(c)<br />राजपथ दूसरों से अलग है। राजपथ को छोड़कर सभी स्मारक स्थल हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In the given diagram, the circle represents parliamentarians from North India, the square represents parliamentarians who have crossed the age of 70 and the triangle represents postgraduates among the parliamentarians. Which area represents postgraduates below 70 from the rest of India ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325518.png\" alt=\"rId19\" width=\"164\" height=\"129\"></p>",
                    question_hi: "<p>16. दिए गए आरेख में, वृत्त उत्तर भारत के सांसदों का प्रतिनिधित्व करता है, वर्ग उन सांसदों का प्रतिनिधित्व करता है जिन्होंने 70 वर्ष की आयु पार कर ली है और त्रिभुज सांसदों के बीच स्नातकोत्तर का प्रतिनिधित्व करता है। कौन सा क्षेत्र शेष भारत से 70 से नीचे के स्नातकोत्तर का प्रतिनिधित्व करता है ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325518.png\" alt=\"rId19\" width=\"164\" height=\"129\"></p>",
                    options_en: ["<p>D</p>", "<p>F</p>", 
                                "<p>C</p>", "<p>E</p>"],
                    options_hi: ["<p>D</p>", "<p>F</p>",
                                "<p>C</p>", "<p>E</p>"],
                    solution_en: "<p>16.(b)<br>Circle - parliamentarians from North india<br>square - 70 + age parliamentarians <br>Triangle - postgraduates parliamentarians<br>So that , postgraduates below 70 from the rest of India = F</p>",
                    solution_hi: "<p>16.(b)<br>वृत्त- उत्तर भारत के सांसद<br>वर्ग - 70+ आयु के सांसद<br>त्रिभुज - स्नातकोत्तर सांसद<br>इस कारण से , शेष भारत से 70 से नीचे के स्नातकोत्तर = F</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Which of the given options will replace the question mark (?) in the following series:<br>A30L, J30E, A31T, O31R, ?</p>",
                    question_hi: "<p>17. दिए गए विकल्पों में से कौन सा विकल्प निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगा<br>A30L, J30E, A31T, O31R, ?</p>",
                    options_en: ["<p>N30R</p>", "<p>J31Y</p>", 
                                "<p>D31R</p>", "<p>M31Y</p>"],
                    options_hi: ["<p>N30R</p>", "<p>J31Y</p>",
                                "<p>D31R</p>", "<p>M31Y</p>"],
                    solution_en: "<p>17.(c)<br>Here we used first and last letter of alternate month the number in between is number of days of that month<br>A30L = APRIL (used A and L, has 30 days)<br>J30E = JUNE,<br>Similarly, <br>AUGUST = A31T,<br>OCTOBER = O31R<br>DECEMBER = D31R</p>",
                    solution_hi: "<p>17.(c)<br>यहां हमने वैकल्पिक महीने के पहले और आखिरी अक्षर का इस्तेमाल किया है, बीच की संख्या उस महीने में दिनों की संख्या है।<br>A30L = APRIL<br>J30E = JUNE,<br>इसी तरह, <br>AUGUST = A31T,<br>OCTOBER = O31R<br>DECEMBER = D31RAUGUST= A31T,<br>OCTOBER= O31R<br>DECEMBER=D31R</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. There are six persons i.e. A, B, C, D, E, F working in different offices i.e. K, L, M, N, O, P not necessarily in the same order. There are three males and three females. B is the wife of E and is working in office N. None of the women works in L or P. C is the sister of D and is working in office K. F is working in office M. D is working in L. No man works in O. Who works in office P.</p>",
                    question_hi: "<p>18. छह व्यक्ति अर्थात A, B, C, D, E, F विभिन्न कार्यालयों में कार्य कर रहे हैं अर्थात K, L, M, N, O, P आवश्यक नहीं इसी क्रम में हो। इनमें तीन नर और तीन मादा हैं। B, E की पत्नी है और कार्यालय N में कार्यरत है। कोई भी महिला L या P में कार्य नहीं करती है। C, D की बहन है और कार्यालय K में कार्य कर रही है। F कार्यालय M में कार्य कर रहा है। D, L में कार्य कर रहा है। नहीं आदमी O में काम करता है। जो ऑफिस P में काम करता है।</p>",
                    options_en: ["<p>F</p>", "<p>B</p>", 
                                "<p>E</p>", "<p>A</p>"],
                    options_hi: ["<p>F</p>", "<p>B</p>",
                                "<p>E</p>", "<p>A</p>"],
                    solution_en: "<p>18.(c)<br>person office<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325618.png\" alt=\"rId20\" width=\"74\" height=\"141\"></p>",
                    solution_hi: "<p>18.(c)<br>व्यक्ति कार्यालय<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325618.png\" alt=\"rId20\" width=\"74\" height=\"141\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the letter that can replace the question mark (?) in the following series.<br>B : H :: P : V :: N : ?</p>",
                    question_hi: "<p>19. उस अक्षर का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है।<br>B : H :: P : V :: N : ?</p>",
                    options_en: ["<p>M</p>", "<p>K</p>", 
                                "<p>P</p>", "<p>T</p>"],
                    options_hi: ["<p>M</p>", "<p>K</p>",
                                "<p>P</p>", "<p>T</p>"],
                    solution_en: "<p>19.(d)<br><strong>Logic- </strong>B + 6 = H , P + 6 = V , N + 6 = T</p>",
                    solution_hi: "<p>19.(d)<br><strong>तर्क- </strong>B + 6 = H , P + 6 = V , N + 6 = T</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option that can replace the question mark(?) in the given series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325721.png\" alt=\"rId21\" width=\"340\" height=\"93\"></p>",
                    question_hi: "<p>20. उस विकल्प का चयन करें जो दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325721.png\" alt=\"rId21\" width=\"340\" height=\"93\"></p>",
                    options_en: ["<p>7</p>", "<p>1</p>", 
                                "<p>6</p>", "<p>5</p>"],
                    options_hi: ["<p>7</p>", "<p>1</p>",
                                "<p>6</p>", "<p>5</p>"],
                    solution_en: "<p>20.(d)<br>In 1st fig. (4 - 3) = 1 and (7 - 5) = 2 now , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mn>2</mn><mo>)</mo><mi>&#160;</mi></mrow><mn>2</mn></msup><mo>=</mo><mn>27</mn></math><br>In 2nd fig. (6 - 2) = 4 and (5 - 3) = 2 now , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>4</mn><mo>+</mo><mn>2</mn><mo>)</mo><mi>&#160;</mi></mrow><mn>2</mn></msup><mo>=</mo><mn>216</mn></math><br>In 3rd fig. (4 - 1) = 3 and (9 - 5) = 4 now ,&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mo>+</mo><mn>4</mn><mo>)</mo><mi>&#160;</mi></mrow><mn>2</mn></msup><mo>=</mo><mn>343</mn></math></p>",
                    solution_hi: "<p>20.(d)<br>पहली आकृति में (4 - 3) = 1 और (7 - 5) = 2 अब , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mn>2</mn><mo>)</mo><mi>&#160;</mi></mrow><mn>2</mn></msup><mo>=</mo><mn>27</mn></math><br>दूसरी आकृति में (6 - 2) = 4 और (5 - 3) = 2 अब , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>4</mn><mo>+</mo><mn>2</mn><mo>)</mo><mi>&#160;</mi></mrow><mn>2</mn></msup><mo>=</mo><mn>216</mn></math><br>तीसरी आकृति में (4 - 1) = 3 और (9 - 5) = 4 अब , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>3</mn><mo>+</mo><mn>4</mn><mo>)</mo><mi>&#160;</mi></mrow><mn>2</mn></msup><mo>=</mo><mn>343</mn></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow (s) from the statement.<br><strong>Statement :</strong><br>1. Some Cars are buses. <br>2. All trains are cars. <br>3. All planes are trains. <br><strong>Conclusions :</strong><br>1. All planes are buses. <br>2. Some cars are planes.</p>",
                    question_hi: "<p>21. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन सा कथन का तार्किक रूप से अनुसरण करता है।<br><strong>कथन :</strong><br>1. कुछ कार बस हैं।<br>2. सभी ट्रेन कार हैं।<br>3. सभी प्लेन ट्रेन हैं।<br><strong>निष्कर्ष:</strong><br>1. सभी प्लेन बस हैं।<br>2. कुछ कार प्लेन हैं।</p>",
                    options_en: ["<p>Only conclusion 1 follows</p>", "<p>Both 1 and 2 follow</p>", 
                                "<p>Neither 1 nor 2 follows</p>", "<p>Only conclusion 2 follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष 1 अनुसरण करता है</p>", "<p>1 और 2 दोनों अनुसरण करते हैं</p>",
                                "<p>न तो 1 और न ही 2 अनुसरण करता है</p>", "<p>केवल निष्कर्ष 2 अनुसरण करता है</p>"],
                    solution_en: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325844.png\" alt=\"rId22\" width=\"265\" height=\"68\"></p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927325973.png\" alt=\"rId23\" width=\"240\" height=\"71\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the number that can replace the question mark(?) in the following series.<br>294, 180, 100, 48, ?</p>",
                    question_hi: "<p>22. उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है।<br>294, 180, 100, 48, ?</p>",
                    options_en: ["<p>15</p>", "<p>18</p>", 
                                "<p>22</p>", "<p>20</p>"],
                    options_hi: ["<p>15</p>", "<p>18</p>",
                                "<p>22</p>", "<p>20</p>"],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927326133.png\" alt=\"rId24\" width=\"176\" height=\"112\"><br>? = 48 - 30 = 18</p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927326133.png\" alt=\"rId24\" width=\"176\" height=\"112\"><br>? = 48 - 30 = 18</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the term that can replace the question mark(?) in the following series.<br>JX5, LU4, NR12, ?</p>",
                    question_hi: "<p>23. उस पद का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है।<br>JX5, LU4, NR12, ?</p>",
                    options_en: ["<p>PO11</p>", "<p>NX8</p>", 
                                "<p>GL15</p>", "<p>FL12</p>"],
                    options_hi: ["<p>PO11</p>", "<p>NX8</p>",
                                "<p>GL15</p>", "<p>FL12</p>"],
                    solution_en: "<p>23.(a)<br><strong>Logic -</strong> <br>J + 2 = L , L + 2 = N , N + 2 = P<br>X - 3 = U , U - 3 = R , R - 3 = 0<br>5 - 1 = 4 , 4 <math display=\"inline\"><mo>&#215;</mo></math> 3 = 12 , 12 - 1 = 11</p>",
                    solution_hi: "<p>23.(a)<br><strong>तर्क -</strong> <br>J + 2 = L , L + 2 = N , N + 2 = P<br>X - 3 = U , U - 3 = R , R - 3 = 0<br>5 - 1 = 4 , 4 <math display=\"inline\"><mo>&#215;</mo></math> 3 = 12 , 12 - 1 = 11</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "24. Three of the four sports given in options are similar in a certain way whereas the fourth is different. Select the option that is different from the rest.",
                    question_hi: "24. विकल्पों में दिए गए चार खेलों में से तीन एक निश्चित तरीके से समान हैं जबकि चौथा अलग है। उस विकल्प का चयन करें जो बाकी से अलग है।",
                    options_en: [" Tennis ", " Cricket ", 
                                " Football ", " Field hockey "],
                    options_hi: [" टेनिस", " क्रिकेट",
                                " फ़ुटबॉल", " फील्ड हॉकी"],
                    solution_en: "24.(a)<br />Tennis is different from others because tennis is played on the court.",
                    solution_hi: "24.(a)<br />टेनिस दूसरों से अलग है क्योंकि टेनिस कोर्ट पर खेला जाता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "25. Among the following words, which one will come first if they are arranged as per their order in an English dictionary ?",
                    question_hi: "25. निम्नलिखित शब्दों में से कौन सा शब्द अंग्रेजी शब्दकोश में उनके क्रम के अनुसार व्यवस्थित होने पर सबसे पहले आएगा ?",
                    options_en: [" Calculate ", " Calendar ", 
                                " Calf ", " Calculation "],
                    options_hi: [" गणना", " कैलेंडर",
                                " बछड़ा", " हिसाब"],
                    solution_en: "25.(a)<br /> arranged as per their order in an English dictionary = <br />Calculate - Calculation - Calendar - calf  ",
                    solution_hi: "25.(a)<br />अंग्रेजी शब्दकोश में उनके क्रम के अनुसार व्यवस्थित =<br />Calculate - Calculation - Calendar - calf  ",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>