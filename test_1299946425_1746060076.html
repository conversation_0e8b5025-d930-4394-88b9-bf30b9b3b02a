<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Based on the alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group?<br>(<strong>Note :</strong> The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>1. अंग्रेजी वर्णमाला क्रम के आधार पर, निम्नलिख चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा अक्षर-समूह उस समूह से संबंधित नहीं है?<br>(नोट: विषम का चयन व्यंजन/स्वरों की संख्या या अक्षर समूह में उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>LIF</p>",
                        "<p>QNL</p>",
                        "<p>WTQ</p>",
                        "<p>ROL</p>"
                    ],
                    options_hi: [
                        "<p>LIF</p>",
                        "<p>QNL</p>",
                        "<p>WTQ</p>",
                        "<p>ROL</p>"
                    ],
                    solution_en: "<p>1.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461275112.png\" alt=\"rId4\" width=\"349\" height=\"69\"><br>But, <br><strong id=\"docs-internal-guid-9bc33835-7fff-33c2-2f40-879b3e890b54\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfTO07yEW0UKR1nzLv4yYTsognJKtnlV_QBm_J6NixFFfgnCntg65KOU5ECm3_MhA5071rD_AsCpNksxLiQrtKWyKagR5RxREYkWkfMacLIU-LuCE-3e_BtcZvy-C6x4aXnvVw-iQ?key=iPlY4he48ciAftYll44lK3e0\" width=\"129\" height=\"79\"></strong></p>",
                    solution_hi: "<p>1.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461275112.png\" alt=\"rId4\" width=\"354\" height=\"70\"><br>लेकिन,<br><strong id=\"docs-internal-guid-9bc33835-7fff-33c2-2f40-879b3e890b54\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfTO07yEW0UKR1nzLv4yYTsognJKtnlV_QBm_J6NixFFfgnCntg65KOU5ECm3_MhA5071rD_AsCpNksxLiQrtKWyKagR5RxREYkWkfMacLIU-LuCE-3e_BtcZvy-C6x4aXnvVw-iQ?key=iPlY4he48ciAftYll44lK3e0\" width=\"129\" height=\"79\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. ETHICS\' is related to AVDKYU\' in a certain way based on the English alphabetical order. In the same way. \'CUSTOM\' is related to \"YWOVKO\'. To which of the following is PRINCIPLES\' related, following the same logic?</p>",
                    question_hi: "<p>2. अंग्रेजी वर्णमाला क्रम के आधार पर \'ETHICS\', \'AVDKYU\' से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, \'CUSTOM\', \'YWOVKO\' से संबंधित है। समान तर्क का अनुसरण करते हुए, \'PRINCIPLES\' निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: [
                        "<p>LTEPYKLMAU</p>",
                        "<p>LSEPYKLNAU</p>",
                        "<p>LTEPYKLNAU</p>",
                        "<p>LTFPYKLNAU</p>"
                    ],
                    options_hi: [
                        "<p>LTEPYKLMAU</p>",
                        "<p>LSEPYKLNAU</p>",
                        "<p>LTEPYKLNAU</p>",
                        "<p>LTFPYKLNAU</p>"
                    ],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461275245.png\" alt=\"rId5\" width=\"155\" height=\"77\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461275368.png\" alt=\"rId6\" width=\"165\" height=\"77\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461275476.png\" alt=\"rId7\" width=\"328\" height=\"103\"></p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461275245.png\" alt=\"rId5\" width=\"155\" height=\"77\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461275368.png\" alt=\"rId6\" width=\"165\" height=\"77\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461275476.png\" alt=\"rId7\" width=\"328\" height=\"103\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. A paper folded and cut as shown below. How will it appear when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461275843.png\" alt=\"rId8\" width=\"249\" height=\"80\"> </p>",
                    question_hi: "<p>3. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461275843.png\" alt=\"rId8\" width=\"249\" height=\"80\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276047.png\" alt=\"rId9\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276148.png\" alt=\"rId10\" width=\"90\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276267.png\" alt=\"rId11\" width=\"91\" height=\"89\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276366.png\" alt=\"rId12\" width=\"90\" height=\"92\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276047.png\" alt=\"rId9\" width=\"91\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276148.png\" alt=\"rId10\" width=\"91\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276267.png\" alt=\"rId11\" width=\"91\" height=\"89\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276366.png\" alt=\"rId12\" width=\"91\" height=\"93\"></p>"
                    ],
                    solution_en: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276267.png\" alt=\"rId11\" width=\"90\" height=\"88\"></p>",
                    solution_hi: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276267.png\" alt=\"rId11\" width=\"90\" height=\"88\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Read the given statements and conclusions carefully. Assuming that the information&nbsp;given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>All watches are dials.<br>All dials are phones.<br>Some phones are mobiles.<br><strong>Conclusions :</strong><br>(I) No watch is a phone.<br>(II) Some dials are mobiles.</p>",
                    question_hi: "<p>4. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है,&nbsp;भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्णय लें कि दिए गए निष्कर्षों में से कौन&nbsp;सा/से निष्कर्ष कथनों का तार्किक रूप सेअनु सरण करता है/करते हैं।<br><strong>कथन :</strong><br>सभी घड़ियाँ, डायल हैं।<br>सभी डायल, फोन हैं।<br>कुछ फोन, मोबाइल हैं।<br><strong>निष्कर्ष :</strong><br>(I) कोई भी घड़ी, फोन नहीं है।<br>(II) कुछ डायल, मोबाइल हैं।</p>",
                    options_en: [
                        "<p>Only conclusion II follows</p>",
                        "<p>Only conclusion I follows</p>",
                        "<p>None of the conclusions follow</p>",
                        "<p>Both conclusions I and II follow</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है</p>",
                        "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>",
                        "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं</p>"
                    ],
                    solution_en: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276511.png\" alt=\"rId13\" width=\"258\" height=\"84\"><br>None of the conclusion follow.</p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276630.png\" alt=\"rId14\" width=\"264\" height=\"93\"><br>कोई भी निष्कर्ष अनुसरण नहीं करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Three of the following numbers are alike in a certain way and one is different. Pick the odd one out.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>5. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए।<br>(<strong>नोट :</strong> गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>90 &ndash; 144</p>",
                        "<p>75 &ndash; 120</p>",
                        "<p>45 &ndash; 70</p>",
                        "<p>65 &ndash; 104</p>"
                    ],
                    options_hi: [
                        "<p>90 &ndash; 144</p>",
                        "<p>75 &ndash; 120</p>",
                        "<p>45 &ndash; 70</p>",
                        "<p>65 &ndash; 104</p>"
                    ],
                    solution_en: "<p>5.(c) <strong>Logic :- </strong>(1st number &divide; 5) &times; 8 = 2nd number<br>(90 - 144) :- (90 &divide; 5 ) &times; 8 &rArr; (18) &times; 8 = 144<br>(75 - 120) :- (75 &divide; 5 ) &times; 8 &rArr; (15) &times; 8 = 120<br>(65 - 104) :- (65 &divide; 5 ) &times; 8 &rArr; (13) &times; 8 = 104<br>But<br>(45 - 70) :- (45 &divide; 5 ) &times; 8 &rArr; (9) &times; 8 = 72 (&ne; 70)</p>",
                    solution_hi: "<p>5.(c) <strong>तर्क :-</strong> (पहली संख्या &divide; 5) &times; 8 = दूसरी संख्या<br>(90, 144) :- (90 &divide; 5 ) &times; 8 &rArr; (18) &times; 8 = 144<br>(75 - 120) :- (75 &divide; 5 ) &times; 8 &rArr; (15) &times; 8 = 120<br>(65 - 104) :- (65 &divide; 5 ) &times; 8 &rArr; (13) &times; 8 = 104<br>लेकिन <br>(45 - 70) :- (45 &divide; 5 ) &times; 8 &rArr; (9) &times; 8 = 72 (&ne; 70)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language, \'DZLV\' is coded as \'HDJT\' and \'CQYH\' is coded as \'GUWF\'. What is the code for MTBE\' in the given code language?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में, \'DZLV\' को \'HDJT\' लिखा जाता है और \'CQYH\' को \'GUWF\' लिखा जाता है। उस कूट भाषा में \'MTBE\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>QXZC</p>",
                        "<p>YZXB</p>",
                        "<p>QZYB</p>",
                        "<p>YAYB</p>"
                    ],
                    options_hi: [
                        "<p>QXZC</p>",
                        "<p>YZXB</p>",
                        "<p>QZYB</p>",
                        "<p>YAYB</p>"
                    ],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276758.png\" alt=\"rId15\" width=\"145\" height=\"117\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276874.png\" alt=\"rId16\" width=\"150\" height=\"122\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276974.png\" alt=\"rId17\" width=\"152\" height=\"123\"></p>",
                    solution_hi: "<p>6.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276758.png\" alt=\"rId15\" width=\"145\" height=\"117\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276874.png\" alt=\"rId16\" width=\"150\" height=\"122\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461276974.png\" alt=\"rId17\" width=\"144\" height=\"117\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. The question contains pairs of words that are related to each other in a certain way. Three of the following four word pairs are alike as these have the same relationship and thus form a group. Which word pair is the one that DOES NOT belong to that group?<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>7. प्रश्न में ऐसे शब्द युग्म हैं जो एक निश्चित तरीके से एक-दूसरे से संबंधित हैं। निम्नलिखित चार शब्द युग्मों में से तीन शब्द युग्म एक समान हैं क्योंकि इनमें संबंध समान है और इस प्रकार वे एक समूह बनाते हैं। निम्नलिखित में से कौन-सा शब्द युग्म उस समूह से संबंधित नहीं है?<br>(शब्दों को अर्थपूर्ण अंग्रेजी/हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों / व्यंजनों / स्वरों की संख्या के आधार पर एक-दूसरे से संबंधित नहीं होने चाहिए।)</p>",
                    options_en: [
                        "<p>Tired - exhausted</p>",
                        "<p>Loud - noisy</p>",
                        "<p>Close - open</p>",
                        "<p>Honest - truthful</p>"
                    ],
                    options_hi: [
                        "<p>थका - थका माँदा</p>",
                        "<p>ऊँचा स्वर - शोरगुल</p>",
                        "<p>बंद - खुला</p>",
                        "<p>ईमानदार - सत्यनिष्ठ</p>"
                    ],
                    solution_en: "<p>7.(c) words (Tired - exhausted , Loud - noisy, Honest - truthful) are synonym each other but (Close - open)&nbsp;is an antonym.</p>",
                    solution_hi: "<p>7.(c)&nbsp;शब्द (थका - थका माँदा, ऊँचा स्वर - शोरगुल, ईमानदार - सत्यनिष्ठ) एक दूसरे के पर्यायवाची हैं लेकिन (बंद - खुला)&nbsp;एक दूसरे का विलोम है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option that represents the letters that when sequentially placed from left to right in the blanks below will complete the letter series.<br>_ O _ O _ _ O _ P _ O _</p>",
                    question_hi: "<p>8. उस विकल्प का चयन कीजिए जो उन अक्षरों को प्रदर्शित करता है जिन्हें नीचे दिए गए रिक्&zwj;त स्&zwj;थानों में बाएं से दाएं क्रमिक रूप से रखने पर अक्षर श्रृंखला पूर्ण हो जाएगी।<br>_ O _ O _ _ O _ P _ O _</p>",
                    options_en: [
                        "<p>PPOOPOP</p>",
                        "<p>OOPPPOO</p>",
                        "<p>OPOPOOP</p>",
                        "<p>PPOOOPO</p>"
                    ],
                    options_hi: [
                        "<p>PPOOPOP</p>",
                        "<p>OOPPPOO</p>",
                        "<p>OPOPOOP</p>",
                        "<p>PPOOOPO</p>"
                    ],
                    solution_en: "<p>8.(c)<br><span style=\"text-decoration: underline;\"><strong>O</strong></span> O <strong><span style=\"text-decoration: underline;\">P</span></strong> /O <span style=\"text-decoration: underline;\"><strong>O P</strong></span> /O <span style=\"text-decoration: underline;\"><strong>O</strong></span> P /<span style=\"text-decoration: underline;\"><strong>O</strong></span> O <span style=\"text-decoration: underline;\"><strong>P</strong></span></p>",
                    solution_hi: "<p>8.(c)<br><span style=\"text-decoration: underline;\"><strong>O</strong></span> O <strong><span style=\"text-decoration: underline;\">P</span></strong> /O <span style=\"text-decoration: underline;\"><strong>O P</strong></span> /O <span style=\"text-decoration: underline;\"><strong>O</strong></span> P /<span style=\"text-decoration: underline;\"><strong>O</strong></span> O <span style=\"text-decoration: underline;\"><strong>P</strong></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language, FROM is coded as 8 and BITTERNESS is coded as 20.What is the code for CAULIFLOWER?</p>",
                    question_hi: "<p>9. एक निश्चित कूट भाषा में, FROM को 8 के रूप में और BITTERNESS को 20 के रूप में कूट बद्ध किया जाता है। CAULIFLOWER के लिए कूट क्या है?</p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>12</p>",
                        "<p>22</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>12</p>",
                        "<p>22</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>9.(c) <strong>Logic :- </strong>(Number of letters in word) &times; 2<br>FROM :- (4) &times; 2 = 8<br>BITTERNESS :- (10) &times; 2 = 20<br>Similarly,<br>CAULIFLOWER :- (11) &times; 2 = 22</p>",
                    solution_hi: "<p>9.(c) <strong>तर्क :-</strong> (शब्द में अक्षरों की संख्या) &times; 2<br>FROM :- (4) &times; 2 = 8<br>BITTERNESS :- (10) &times; 2 = 20<br>इसी प्रकार,<br>CAULIFLOWER :- (11) &times; 2 = 22</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the number from among the given options that can replace the question mark (?) in the following series.<br>23, ?, 89, 177, 353, 705</p>",
                    question_hi: "<p>10. दिए गए विकल्पों में से उस संख्या का चयन कीजिए जो निम्नलिखित शृंखला में प्रश्न चिह्न (?) के स्थान पर आ सकती है।<br>23, ?, 89, 177, 353, 705</p>",
                    options_en: [
                        "<p>45</p>",
                        "<p>47</p>",
                        "<p>49</p>",
                        "<p>42</p>"
                    ],
                    options_hi: [
                        "<p>45</p>",
                        "<p>47</p>",
                        "<p>49</p>",
                        "<p>42</p>"
                    ],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461277095.png\" alt=\"rId18\" width=\"345\" height=\"52\"></p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461277095.png\" alt=\"rId18\" width=\"345\" height=\"52\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language, \'strength to innovate\' is coded as \'42 64 32\' and \'strength to create\' is coded as \'32 42 24. What is the code for \'create\' in that language?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में, strength to innovate\' को \'42 64 32\' लिखा जाता है और \'strength to create\' को \'32 42 24 लिखा जाता है। तो उस कूट भाषा में \'create\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>32</p>",
                        "<p>64</p>",
                        "<p>42</p>",
                        "<p>24</p>"
                    ],
                    options_hi: [
                        "<p>32</p>",
                        "<p>64</p>",
                        "<p>42</p>",
                        "<p>24</p>"
                    ],
                    solution_en: "<p>11.(d) <br>strength to innovate &rarr; 42 64 32&hellip;&hellip;..(i)<br>strength to create &rarr; 32 42 24&hellip;&hellip;(ii)<br>From (i) and (ii) &lsquo;strength to&rsquo; and &lsquo;32 42&rsquo; are common. The code of &lsquo;create&rsquo; = &lsquo;24&rsquo;.</p>",
                    solution_hi: "<p>11.(d) <br>strength to innovate &rarr; 42 64 32&hellip;&hellip;..(i)<br>strength to create &rarr; 32 42 24&hellip;&hellip;(ii)<br>(i) और (ii) से \'strength to \' और \'32 42\' उभयनिष्ठ हैं। \'create\' का कोड = \'24\'.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. If 1 January 2020 was a Wednesday, then what day of the week was it on 1 January&nbsp;2021 ?</p>",
                    question_hi: "<p>12. यदि 1 जनवरी 2020 को बुधवार था, तो 1 जनवरी 2021 को सप्ताह का कौन सा दिन था ?</p>",
                    options_en: [
                        "<p>Wednesday</p>",
                        "<p>Thursday</p>",
                        "<p>Tuesday</p>",
                        "<p>Friday</p>"
                    ],
                    options_hi: [
                        "<p>बुधवार</p>",
                        "<p>गुरूवार</p>",
                        "<p>मंगलवार</p>",
                        "<p>शुक्रवार</p>"
                    ],
                    solution_en: "<p>12.(d) The number of odd days in moving from 1st January 2020 to 1st January 2021 = 2<br>Wednesday + 2 = Friday.</p>",
                    solution_hi: "<p>12.(d) 1 जनवरी 2020 से 1 जनवरी 2021 तक जाने में विषम दिनों की संख्या = 2<br>बुधवार + 2 = शुक्रवार.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In this question, a statement is followed by two courses of action, numbered I and II. You must assume everything in the statement to be true and on the basis of the information given in the statement, decide which of the courses of action logically follow(s) for pursuing.<br><strong>Statement:</strong><br>Due to water pipeline work planned in Grant Road for the next 3 days, the road will be closed for traffic till further notice.<br><strong>Courses of Action</strong><br>(I) Traffic police should increase the number of personnel present at Grant Road to physically coordinate traffic diversion effectively. <br>(II) Traffic police should issue a notification in major newspapers regarding traffic diversions and alternate routes that can be taken by the general public till the Grant Road closure continues.</p>",
                    question_hi: "<p>13. इस प्रश्न में, एक कथन के बाद। और ॥ क्रमांकित दो कार्रवाइयां दी गई हैं। आपको कथन में दी गई सभी जानकारियों को सत्य मानना होगा और कथन में दी गई जानकारी के आधार पर निश्चय करना होगा, कि तार्किक रूप से किस/किन कार्रवाई/कार्रवाइयों का अनुसरण किया जाना चाहिए। <br><strong>कथनः</strong><br>अगले 3 दिनों के लिए ग्रांट रोड में पानी की पाइपलाइन कार्य योजना के कारण, सड़क अगली सूचना तक यातायात के लिए बंद रहेगी।<br><strong>कार्रवाई:</strong><br>(I) ट्रैफिक पुलिस को ट्रैफिक डायवर्जन को प्रभावी ढंग से समन्वयित करने के लिए ग्रांट रोड पर मौजूद कर्मियों की संख्या में वृद्धि करनी चाहिए।<br>(II) ट्रैफिक पुलिस को प्रमुख समाचार पत्रों में ट्रैफिक डायवर्जन और वैकल्पिक मार्गों के बारे में एक अधिसूचना जारी करनी चाहिए, जिसे ग्रांट रोड बंद रहने तक आम जनता उपयोग में ले सकती है।</p>",
                    options_en: [
                        "<p>Only II follows</p>",
                        "<p>Only I follows</p>",
                        "<p>Both I and II follow</p>",
                        "<p>Neither I nor II follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल ।। अनुसरण करता है</p>",
                        "<p>केवल । अनुसरण करता है</p>",
                        "<p>। और II दोनों अनुसरण करते हैं</p>",
                        "<p>न तो। और न ही ॥ अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>13.(c) <strong>Statement :-</strong> Due to water pipeline work planned in Grant Road for the next 3 days, the road will be closed for traffic till further notice.<br><strong>Courses of Action 1 :-</strong> The traffic police should deploy additional personnel at Grant Road to efficiently manage and coordinate traffic diversions.<br><strong>Course of Action 2 :-</strong>The traffic police should publish a notification in leading newspapers to inform the public about traffic diversions and alternative routes available during the closure of Grant Road.&nbsp;Both I and II follow.</p>",
                    solution_hi: "<p>13.(c) <strong>कथन:- </strong>अगले 3 दिनों के लिए ग्रांट रोड में योजनाबद्ध पानी की पाइपलाइन के काम के कारण, आगे के नोटिस तक यातायात के लिए सड़क बंद हो जाएगी।<br><strong>कार्रवाई 1 :-</strong>ट्रैफिक पुलिस को ट्रैफ़िक राइड को कुशलतापूर्वक प्रबंधित करने और यातायात विविधताओं का समन्वय करने के लिए ग्रांट रोड पर अतिरिक्त कर्मियों को तैनात करना चाहिए।<br><strong>कार्रवाई 2 :-</strong>एक ट्रैफिक पुलिस को ग्रांट रोड के बंद होने के दौरान उपलब्ध ट्रैफ़िक विविधताओं और वैकल्पिक मार्गों के बारे में जनता को सूचित करने के लिए प्रमुख समाचार पत्रों में एक अधिसूचना प्रकाशित करना चाहिए।&nbsp;कार्रवाई I और II दोनों कथन का पालन करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Six numbers 1, 2, 3, 4, 5 and 6 are written on different faces of a dice. Three positions of this dice are shown in the given figure. Find the number on the face opposite to the number 3.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461277253.png\" alt=\"rId19\" width=\"228\" height=\"86\"></p>",
                    question_hi: "<p>14 एक पासे के अलग-अलग फलकों पर छह अंक 1, 2, 3, 4, 5 और 6 लिखे गए हैं। दी गई आकृति में इस पासे की तीन स्थितियों को दिखाया गया है। संख्या 3 के विपरीत फलक पर आने वाली संख्या ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461277253.png\" alt=\"rId19\" width=\"228\" height=\"86\"></p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>14.(d) From 1st and 3rd dice &lsquo;2&rsquo; and &lsquo;1&rsquo; are common, the opposite face of &lsquo;3&rsquo; will be &lsquo;6&rsquo;.</p>",
                    solution_hi: "<p>14.(d) पहले और तीसरे पासे से \'2\' और \'1\' उभयनिष्ठ हैं, \'3\' का विपरीत फलक \'6\' होगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Brinda starts from Point A and drives 19 km towards West. She then takes a left turn, drives 22 km, turns left and drives 25 km. She takes a final left turn, drives 22 km and stops at Point P. How far (shortest distance) and towards which direction should she drive to reach Point A again? (All turns are 90 degrees turns only unless specified.)</p>",
                    question_hi: "<p>15. बृंदा, बिंदु A से चलना आरंभ करती है और पश्चिम की ओर 19 km चलती है। फिर वह बाईं ओर मुड़ती है, 22 km चलती है, बाई ओर मुड़ती है और 25 km चलती है। अंतिम बार वह बाईं ओर मुड़ती है, 22 km चलती है और बिंदु P पर रुक जाती है। बिंदु A पर दोबारा पहुंचने के लिए उसे कितनी दूरी तक (न्यूनतम दूरी) और किस दिशा में गाड़ी चलानी चाहिए? (जब तक निर्दिष्ट न किए जाएं, सभी मोड़ 90 डिग्री के मोड़ हैं।)</p>",
                    options_en: [
                        "<p>7 km towards the West</p>",
                        "<p>7 km towards the South</p>",
                        "<p>6 km towards the West</p>",
                        "<p>6 km towards the East</p>"
                    ],
                    options_hi: [
                        "<p>7 km पश्चिम की ओर</p>",
                        "<p>7 km दक्षिण की ओर</p>",
                        "<p>6 km पश्चिम की ओर</p>",
                        "<p>6 km पूर्व की ओर</p>"
                    ],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461277510.png\" alt=\"rId20\" width=\"281\" height=\"125\"></p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461277771.png\" alt=\"rId21\" width=\"283\" height=\"127\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Which two numbers should be interchanged to make the given equation correct?<br>21 &times; 4 + (84 &divide; 7) &times; 2 - 15 &times; 5 + 42 = 105<br>(Note: Interchange should be done of entire number and not individual digits of a given number.)</p>",
                    question_hi: "<p>16. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए?<br>21 &times; 4 + (84 &divide; 7) &times; 2 - 15 &times; 5 + 42 = 105<br>(ध्यान दें: संपूर्ण संख्या को आपस में बदला जाना चाहिए, न कि दी गई संख्या के अलग-अलग अंकों को।)</p>",
                    options_en: [
                        "<p>4 and 2</p>",
                        "<p>84 and 42</p>",
                        "<p>4 and 5</p>",
                        "<p>7 and 21</p>"
                    ],
                    options_hi: [
                        "<p>4 और 2</p>",
                        "<p>84 और 42</p>",
                        "<p>4 और 5</p>",
                        "<p>7 और 21</p>"
                    ],
                    solution_en: "<p>16.(b)<br>Checking all the options given in the question one by one, option (b) gets satisfied.<br>21 &times; 4 + (84 &divide; 7) &times; 2 - 15 &times; 5 + 42 = 105<br>Interchanging 84 and 42 in the above expression, we have;<br>21 &times; 4 + (42 &divide; 7) &times; 2 - 15 &times; 5 + 84 = 105<br>84 + 12 - 75 + 84 = 105<br>105 = 105<br>LHS = RHS</p>",
                    solution_hi: "<p>16.(b)<br>एक -एक करके प्रश्न में दिए गए सभी विकल्पों की जाँच करने पर , विकल्प (b) संतुष्ट करता है।<br>21 &times; 4 + (84 &divide; 7) &times; 2 - 15 &times; 5 + 42 = 105<br>उपरोक्त समीकरण में 84 और 42 को आपस में बदलने पर <br>21 &times; 4 + (42 &divide; 7) &times; 2 - 15 &times; 5 + 84 = 105<br>84 + 12 - 75 + 84 = 105<br>105 = 105<br>LHS = RHS</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. If each consonant in the word VERIFY is changed to the letter immediately succeeding it in the English alphabetical order and each vowel is changed to the letter immediately preceding it in the English alphabetical order, then how many consonants are present in the new group of letters thus formed ?</p>",
                    question_hi: "<p>17. यदि शब्द VERIFY के प्रत्येक व्यंजन को अंग्रेजी वर्णमाला क्रम में उसके ठीक बाद वाले अक्षर से बदल दिया जाए, और प्रत्येक स्वर को अंग्रेजी वर्णमाला क्रम में उसके ठीक पहले वाले अक्षर से बदल दिया जाए, तो इस प्रकार बने अक्षरों के नए समूह में कितने व्यंजन मौजूद होंगे ?</p>",
                    options_en: [
                        "<p>Four</p>",
                        "<p>Six</p>",
                        "<p>Five</p>",
                        "<p>Three</p>"
                    ],
                    options_hi: [
                        "<p>चार</p>",
                        "<p>छह</p>",
                        "<p>पांच</p>",
                        "<p>तीन</p>"
                    ],
                    solution_en: "<p>17.(b)<br>Applying the given rules:<br>Each vowel - 1 And Each consonant +1. <br>According to the question,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461277923.png\" alt=\"rId22\" width=\"209\" height=\"86\"><br>Number of consonants in the new letter group = 6</p>",
                    solution_hi: "<p>17.(b)<br>नियमों को लागू करने पर :<br>प्रत्येक स्वर - 1<br>प्रत्येक व्यंजन + 1<br>प्रश्न के अनुसार ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461277923.png\" alt=\"rId22\" width=\"209\" height=\"86\"><br>नए अक्षर समूह में व्यंजनो की संख्या = 6</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278023.png\" alt=\"rId23\" width=\"300\" height=\"63\"> </p>",
                    question_hi: "<p>18. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278023.png\" alt=\"rId23\" width=\"300\" height=\"63\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278115.png\" alt=\"rId24\" width=\"91\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278221.png\" alt=\"rId25\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278323.png\" alt=\"rId26\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278431.png\" alt=\"rId27\" width=\"90\" height=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278115.png\" alt=\"rId24\" width=\"91\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278221.png\" alt=\"rId25\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278323.png\" alt=\"rId26\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278431.png\" alt=\"rId27\" width=\"90\" height=\"90\"></p>"
                    ],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278115.png\" alt=\"rId24\" width=\"90\" height=\"90\"></p>",
                    solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278115.png\" alt=\"rId24\" width=\"90\" height=\"90\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. How many squares are there in the figure shown below?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278571.png\" alt=\"rId28\" width=\"151\" height=\"157\"></p>",
                    question_hi: "<p>19. नीचे दर्शायी गयी आकृति में कितने वर्ग हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461278571.png\" alt=\"rId28\" width=\"151\" height=\"157\"></p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>14</p>",
                        "<p>12</p>",
                        "<p>13</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>14</p>",
                        "<p>12</p>",
                        "<p>13</p>"
                    ],
                    solution_en: "<p>19.(d)<br><img src=\"data:image/png;base64,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\" width=\"160\" height=\"164\"><br><strong>There are 13 squares =</strong> ABFE, DCFF&rsquo;, DGQR, ZOO&rsquo;Y, OMM&rsquo;O&rsquo;, MLL&rsquo;M&rsquo;, ZMWX, MI&rsquo;VW,JII&rsquo;M, PNMO, NKLM, DHUT, PKL&rsquo;O&rsquo;.</p>",
                    solution_hi: "<p>19.(d)<br>&nbsp;<img src=\"data:image/png;base64,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\" width=\"160\" height=\"164\"><br><strong>कुल 13 वर्ग हैं =</strong> ABFE, DCFF&rsquo;, DGQR, ZOO&rsquo;Y, OMM&rsquo;O&rsquo;, MLL&rsquo;M&rsquo;, ZMWX, MI&rsquo;VW,JII&rsquo;M, PNMO, NKLM, DHUT, PKL&rsquo;O&rsquo;.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the set, from the given options, in which the numbers are related in the same way as are the numbers of the following sets.<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(6, 13, 78)<br>(5, 22, 110)</p>",
                    question_hi: "<p>20. उस समुच्चय को चुनिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित समुच्चयों की संख्याएं संबंधित हैं।<br>(<strong>ध्यान दें</strong>: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(6, 13, 78)<br>(5, 22, 110)</p>",
                    options_en: [
                        "<p>(24, 9, 206)</p>",
                        "<p>(7, 13, 90)</p>",
                        "<p>(8, 19, 162)</p>",
                        "<p>(8, 12, 96)</p>"
                    ],
                    options_hi: [
                        "<p>(24, 9, 206)</p>",
                        "<p>(7, 13, 90)</p>",
                        "<p>(8, 19, 162)</p>",
                        "<p>(8, 12, 96)</p>"
                    ],
                    solution_en: "<p>20.(d)<br><strong>Logic :-</strong> 1st no. &times; 2nd no. = 3rd no. <br>(6, 13, 78) &rarr; 6 &times; 13 = 78 <br>(5, 22, 110) &rarr; 5 &times; 22 = 110<br>Similarly <br>(8, 12, 96) &rarr; 8 &times; 12 =<strong> 96</strong></p>",
                    solution_hi: "<p>20.(d)<br><strong>तर्क :- </strong>1st no. &times; 2nd no. = 3rd no. <br>(6, 13, 78) &rarr; 6 &times; 13 = 78 <br>(5, 22, 110) &rarr; 5 &times; 22 = 110<br>इसी प्रकार <br>(8, 12, 96) &rarr; 8 &times; 12 = <strong>96</strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the correct mirror image of the given combination when the mirror is placed at&nbsp;MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279083.png\" alt=\"rId30\" width=\"132\" height=\"102\"></p>",
                    question_hi: "<p>21. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन&nbsp;कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279083.png\" alt=\"rId30\" width=\"132\" height=\"102\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279294.png\" alt=\"rId31\" width=\"122\" height=\"21\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279416.png\" alt=\"rId32\" width=\"120\" height=\"21\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279635.png\" alt=\"rId33\" width=\"122\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279802.png\" alt=\"rId34\" width=\"120\" height=\"20\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279294.png\" alt=\"rId31\" width=\"122\" height=\"21\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279416.png\" alt=\"rId32\" width=\"120\" height=\"21\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279635.png\" alt=\"rId33\" width=\"122\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279802.png\" alt=\"rId34\" width=\"120\" height=\"20\"></p>"
                    ],
                    solution_en: "<p>21.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279635.png\" alt=\"rId33\" width=\"122\" height=\"20\"></p>",
                    solution_hi: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279635.png\" alt=\"rId33\" width=\"122\" height=\"20\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. In the following number-pairs, the second number is obtained by applying certain mathematical operations to the first number. Which numbers should replace X and Y so that the pattern followed by the two numbers on the left side of :: is the same as that on the right side of :: ?<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13-Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>X : 803 :: 469 : Y</p>",
                    question_hi: "<p>22. निम्नलिखित संख्या-युग्मों में, पहली संख्या पर कुछ गणितीय संक्रियाएं करके दूसरी संख्या प्राप्त की जाती है। X और Y के स्थान पर कौन-सी संख्याएं आनी चाहिए ताकि :: के बाईं ओर दो संख्याओं द्वारा जिस पैटर्न का अनुसरण किया जाता है, उसी पैटर्न का अनुसरण :: के दाईं ओर किया जाता हो ?<br>(<strong>ध्यान दें:</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण 13- संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>X : 803 :: 469 : Y</p>",
                    options_en: [
                        "<p>X = 545, Y = 727</p>",
                        "<p>X = 512, Y = 792</p>",
                        "<p>X = 558, Y = 712</p>",
                        "<p>X = 526, Y = 758</p>"
                    ],
                    options_hi: [
                        "<p>X = 545, Y = 727</p>",
                        "<p>X = 512, Y = 792</p>",
                        "<p>X = 558, Y = 712</p>",
                        "<p>X = 526, Y = 758</p>"
                    ],
                    solution_en: "<p>22.(a) X : 803 : : 469 : Y<br>After checking all options one by one, only option (a) satisfies, <br><strong>Logic:</strong>- 2nd no. - 1st no. = 258<br>545 : 803 :- 803 - 545 = 258<br>469 : 727 :- 727 - 469 = 258</p>",
                    solution_hi: "<p>22.(a) X : 803 : : 469 : Y<br>एक -एक करके सभी विकल्पों की जाँच करने के बाद, केवल विकल्प (a) संतुष्ट करता है,<br><strong>तर्क:- </strong>दूसरी संख्या - पहली संख्या = 258<br>545 : 803 :- 803 - 545 = 258<br>469 : 727 :- 727 - 469 = 258</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. How many people play either only volleyball or only chess as per the given Venn diagram?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461279929.png\" alt=\"rId35\" width=\"210\" height=\"165\"> </p>",
                    question_hi: "<p>23. दिए गए वेन आरेख के अनुसार ऐसे कितने लोग हैं जो या तो केवल वॉलीबॉल या केवल शतरंज खेलते हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461280066.png\" alt=\"rId36\" width=\"193\" height=\"147\"></p>",
                    options_en: [
                        "<p>81</p>",
                        "<p>88</p>",
                        "<p>15</p>",
                        "<p>66</p>"
                    ],
                    options_hi: [
                        "<p>81</p>",
                        "<p>88</p>",
                        "<p>15</p>",
                        "<p>66</p>"
                    ],
                    solution_en: "<p>23.(d) people who play either only volleyball or only chess <br>23 + 43 = 66</p>",
                    solution_hi: "<p>23.(d) जो लोग या तो केवल वॉलीबॉल या केवल शतरंज खेलते हैं <br>23 + 43 = 66</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Which of the following terms will replace the question mark (?) in the given series?<br>JHOQ, NFSO, ?, VBAK, ZZEI</p>",
                    question_hi: "<p>24. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्&zwj;न चिह्न (?) का स्थान लेगा?<br>JHOQ, NFSO, ?, VBAK, ZZEI</p>",
                    options_en: [
                        "<p>RCWM</p>",
                        "<p>RDMW</p>",
                        "<p>RCXM</p>",
                        "<p>RDWM</p>"
                    ],
                    options_hi: [
                        "<p>RCWM</p>",
                        "<p>RDMW</p>",
                        "<p>RCXM</p>",
                        "<p>RDWM</p>"
                    ],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461280316.png\" alt=\"rId37\" width=\"339\" height=\"77\"></p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461280316.png\" alt=\"rId37\" width=\"339\" height=\"77\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the option in which the given figure is embedded (rotation is NOT allowed)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461280596.png\" alt=\"rId38\" width=\"95\" height=\"90\"></p>",
                    question_hi: "<p>25. उस विकल्प आकृति को चुनिए जिसमें दी गई आकृति निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461280596.png\" alt=\"rId38\" width=\"95\" height=\"90\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461280712.png\" alt=\"rId39\" width=\"90\" height=\"89\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461280821.png\" alt=\"rId40\" width=\"90\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461280939.png\" alt=\"rId41\" width=\"91\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461281070.png\" alt=\"rId42\" width=\"91\" height=\"88\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461280712.png\" alt=\"rId39\" width=\"91\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461280821.png\" alt=\"rId40\" width=\"90\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461280939.png\" alt=\"rId41\" width=\"90\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461281070.png\" alt=\"rId42\" width=\"91\" height=\"88\"></p>"
                    ],
                    solution_en: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461281180.png\" alt=\"rId43\" width=\"103\" height=\"102\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461281180.png\" alt=\"rId43\" width=\"103\" height=\"102\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Who of the following singer popularized the Sambalpuri folk song \'Rangabati Rangabati\'?",
                    question_hi: "26. निम्नलिखित में से किस गायक ने संबलपुरी लोक गीत \'रंगबती रंगबती\' को लोकप्रिय बनाया?",
                    options_en: [
                        "  Sunidhi Chauhan",
                        "  Krishna Patel",
                        "  Jonita Gandhi",
                        "  Vani Jairam"
                    ],
                    options_hi: [
                        " सुनिधि चौहान",
                        " कृष्णा पटेल",
                        " जोनिता गांधी",
                        " वाणी जयराम"
                    ],
                    solution_en: "<p>26.(b) <strong>Krishna Patel. </strong>Mitrabhanu Gauntia wrote the Sambalpuri song, which was performed by Jitendra Haripal (Padma Shri, 2017) and Krishna Patel and was produced by Prabhudatta Pradhan. In the middle of the 1970s, the song was initially recorded for All India Radio. Other folk songs - Ghumura Geet, Danda Nacha Geet, Changu Badya, and Dalkahi Geet.</p>",
                    solution_hi: "<p>26.(b)<strong> कृष्णा पटेल।</strong> मित्रभानु गौंटिया ने संबलपुरी गीत लिखा, जिसे जितेंद्र हरिपाल (पद्म श्री, 2017) और कृष्णा पटेल ने गाया था और प्रभुदत्त प्रधान ने इसका निर्माण किया था। 1970 के दशक के मध्य में, यह गीत पहली बार ऑल इंडिया रेडियो के लिए रिकॉर्ड किया गया था। अन्य लोकगीत - घुमुरा गीत, डंडा नाचा गीत, चंगु बड्या एवं दलखाई गीत।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Industries of strategic and national importance are usually placed in the ________&nbsp;sector.</p>",
                    question_hi: "<p>27. रणनीतिक और राष्ट्रीय महत्व के उद्योगों को आमतौर पर ________क्षेत्र में रखा जाता है।</p>",
                    options_en: [
                        "<p>Public</p>",
                        "<p>Co-operative</p>",
                        "<p>Private</p>",
                        "<p>Joint</p>"
                    ],
                    options_hi: [
                        "<p>सार्वजनिक</p>",
                        "<p>सहकारी</p>",
                        "<p>निजी</p>",
                        "<p>संयुक्त</p>"
                    ],
                    solution_en: "<p>27.(a) <strong>Public</strong> sector enterprises are government/state controlled companies or corporations funded by governments. The private sector is the part of the economy that is not state-controlled and is run by individuals and companies for profit. Co-operative sector industries are operated and owned by the suppliers or producers of raw materials, workers or both. A Joint sector is a business or industry that is jointly owned, controlled, and managed by the government and private entrepreneurs.</p>",
                    solution_hi: "<p>27.(a) <strong>सार्वजनिक</strong> क्षेत्र के उद्यम सरकार/राज्य द्वारा नियंत्रित कंपनियाँ या निगम हैं जिन्हें सरकार द्वारा वित्तपोषित किया जाता है। निजी क्षेत्र अर्थव्यवस्था का वह भाग है जो राज्य द्वारा नियंत्रित नहीं है और इसे व्यक्तियों और कंपनियों द्वारा लाभ के लिए चलाया जाता है। सहकारी क्षेत्र के उद्योगों का संचालन और स्वामित्व कच्चे माल, श्रमिकों या दोनों के आपूर्तिकर्ताओं या उत्पादकों द्वारा किया जाता है। संयुक्त क्षेत्र एक व्यवसाय या उद्योग है जिसका स्वामित्व, नियंत्रण और प्रबंधन सरकार और निजी उद्यमियों द्वारा संयुक्त रूप से किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28 Which state won the PATA Gold Award 2024 for its innovative \'Holiday&nbsp;Heist\' campaign?</p>",
                    question_hi: "<p>28. किस राज्य ने अपनी अभिनव \'हॉलिडे हेस्ट\' अभियान के लिए PATA गोल्ड अवार्ड 2024 जीता?</p>",
                    options_en: [
                        "<p>Rajasthan</p>",
                        "<p>Kerala</p>",
                        "<p>Goa</p>",
                        "<p>Himachal Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>राजस्थान</p>",
                        "<p>केरल</p>",
                        "<p>गोवा</p>",
                        "<p>हिमाचल प्रदेश</p>"
                    ],
                    solution_en: "<p>28.(b) <strong>Kerala.</strong> Kerala Tourism clinched the PATA (Pacific Asia Travel Association) Gold Award 2024 in the Marketing category for its innovative \'Holiday Heist\' campaign. The campaign aimed to highlight Kerala\'s unique tourism offerings by blending storytelling and digital innovation, enhancing its appeal to global audiences.</p>",
                    solution_hi: "<p>28.(b) <strong>केरल। </strong>केरल पर्यटन ने PATA (पैसिफिक एशिया ट्रैवल एसोसिएशन) गोल्ड अवार्ड 2024 को मार्केटिंग श्रेणी में \'हॉलिडे हेस्ट\' अभियान के लिए जीता। इस अभियान का उद्देश्य केरल की अद्वितीय पर्यटन पेशकशों को प्रस्तुत करना था, जिसमें कहानी कहने और डिजिटल नवाचार का संगम था, जो वैश्विक दर्शकों के लिए इसकी आकर्षण को बढ़ाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. According to the Indian Constitution, Right against exploitation, prohibiting all forms of __________ and traffic in human beings.</p>",
                    question_hi: "<p>29. भारतीय संविधान के अनुसार, शोषण के विरुद्ध अधिकार, सभी प्रकार के __________ और मानव तस्करी पर रोक लगाता है।</p>",
                    options_en: [
                        "<p>Work</p>",
                        "<p>Services</p>",
                        "<p>Forced Labour</p>",
                        "<p>Labour</p>"
                    ],
                    options_hi: [
                        "<p>काम</p>",
                        "<p>सेवाएँ</p>",
                        "<p>जबरन श्रम</p>",
                        "<p>श्रम</p>"
                    ],
                    solution_en: "<p>29.(c) <strong>Forced Labour.</strong> The Right against Exploitation is a fundamental right in the Indian Constitution that protects individuals from various forms of exploitation. Related Articles: Article 23 - Prohibition of traffic in human beings and forced labour. Article 24 - Prohibition of employment of children in factories, etc.</p>",
                    solution_hi: "<p>29.(c)<strong> जबरन श्रम। </strong>शोषण के विरुद्ध अधिकार भारतीय संविधान में एक मौलिक अधिकार है, जो व्यक्तियों को विभिन्न प्रकार के शोषण से बचाता है। संबंधित अनुच्छेद: अनुच्छेद 23 - मानव तस्करी एवं बलात् श्रम पर प्रतिबंध। अनुच्छेद 24 - कारखानों आदि में बालकों के नियोजन का निषेध।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which Assamese artist was known as Sudhakantha?</p>",
                    question_hi: "<p>30. किस असमिया कलाकार को सुधाकंठ कहा जाता था?</p>",
                    options_en: [
                        "<p>Hiteswar Saikia</p>",
                        "<p>Hem Barua</p>",
                        "<p>Hema Bharali</p>",
                        "<p>Bhupen Hazarika</p>"
                    ],
                    options_hi: [
                        "<p>हितेश्वर सैकिया</p>",
                        "<p>हेम बरुआ</p>",
                        "<p>हेमा भराली</p>",
                        "<p>भूपेन हजारिका</p>"
                    ],
                    solution_en: "<p>30(d) <strong>Bhupen Hazarika.</strong> \"Sudhakantha\" literally means \"the nightingale\". His awards : Padma Bhushan (2001), Padma Vibhushan (2012), Bharat Ratna (2019), National Film Award (1977), Padma Shri (1977), Dadasaheb Phalke Award (1992). Hiteswar Saikia was the 10th Chief Minister of Assam.</p>",
                    solution_hi: "<p>30(d) <strong>भूपेन हजारिका।</strong> \"सुधाकंठ\" का शाब्दिक अर्थ है \"कोकिला\"। उनके प्राप्त पुरस्कार: पद्म भूषण (2001), पद्म विभूषण (2012), भारत रत्न (2019), राष्ट्रीय फिल्म पुरस्कार (1977), पद्मश्री (1977), दादा साहब फाल्के पुरस्कार (1992)। हितेश्वर सैकिया असम के 10वें मुख्यमंत्री थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Hot local wind that flows over north India in summer is known as :</p>",
                    question_hi: "<p>31. गर्मियों में उत्तर भारत में बहने वाली गर्म स्थानीय हवा _________ कहलाती है।</p>",
                    options_en: [
                        "<p>Loo</p>",
                        "<p>Chinook</p>",
                        "<p>Mango showers</p>",
                        "<p>Purga</p>"
                    ],
                    options_hi: [
                        "<p>लू</p>",
                        "<p>चिनूक</p>",
                        "<p>आम्र वर्षा</p>",
                        "<p>पुर्गा</p>"
                    ],
                    solution_en: "<p>31.(a) <strong>Loo.</strong> These are strong, gusty, hot, dry winds blowing during the day over the north and northwestern India. In West Bengal, similar storms are known as &lsquo;Kaal Baisakhi&rsquo;. In Kerala and Karnataka, pre-monsoon showers, which help in the early ripening of mangoes, are referred to as &lsquo;mango showers&rsquo;. Chinook is the warm and dry local wind blowing on the leeward side or eastern side of Rockies (Prairies) in North America. The Buran wind blows across eastern Asia, and it is referred to as Purga when it occurs over the tundra.</p>",
                    solution_hi: "<p>31.(a) <strong>लू।</strong> ये उत्तर और उत्तर-पश्चिमी भारत में दिन के समय चलने वाली तेज़, झोंकेदार, गर्म, शुष्क हवाएँ हैं। पश्चिम बंगाल में, इसी तरह के तूफ़ानों को \'काल बैसाखी\' के नाम से जाना जाता है। केरल और कर्नाटक में, प्री-मॉनसून वर्षा, जो आमों को जल्दी पकने में मदद करती है, को \'मैंगो शावर\' (आम्र वर्षा) कहा जाता है। चिनूक उत्तरी अमेरिका में रॉकीज़ (प्रेयरीज़) के पूर्वी हिस्से या हवा के विपरीत दिशा में चलने वाली गर्म एवं शुष्क स्थानीय हवा है। बुरान हवा पूर्वी एशिया में चलती है, और जब यह टुंड्रा पर होती है तो इसे पुर्गा कहा जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following is a poor conductor of electricity ?</p>",
                    question_hi: "<p>32. इनमें से कौन सा विद्युत का सबसे खराब चालक है ?</p>",
                    options_en: [
                        "<p>Tap water</p>",
                        "<p>Water from pond</p>",
                        "<p>Water from hand pump</p>",
                        "<p>Distilled water</p>"
                    ],
                    options_hi: [
                        "<p>नल का पानी</p>",
                        "<p>तालाब का पानी</p>",
                        "<p>हैंडपंप का पानी</p>",
                        "<p>आसुत जल</p>"
                    ],
                    solution_en: "<p>32.(d) <strong>Distilled water</strong> is a poor conductor of electricity because it lacks dissolved salts and minerals that enhance conductivity. Good conductors of electricity are materials that facilitate the easy flow of electric current, typically containing free-moving electrons. In contrast, poor conductors resist the flow of electric current, making it difficult for electricity to pass through them.</p>",
                    solution_hi: "<p>32.(d) <strong>आसुत जल </strong>विद्युत का खराब चालक है क्योंकि इसमें घुलनशील लवण और खनिज नहीं होते जो चालकता को बढ़ाते हैं। विद्युत के अच्छे चालक वे पदार्थ होते हैं जो विद्युत धारा के आसान प्रवाह को सुगम बनाते हैं, जिनमें सामान्यतः मुक्त गतिशील इलेक्ट्रॉन होते हैं। इसके विपरीत, खराब चालक विद्युत धारा के प्रवाह का प्रतिरोध करते हैं, जिससे उनमें से विद्युत का प्रवाह कठिन हो जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. In 1812, who described the hypothesis that equal volumes of different gases contain an equal number of molecules?</p>",
                    question_hi: "<p>33. 1812 में, इस परिकल्पना का वर्णन किसने किया कि विभिन्न गैसों के समान आयतनों में समान संख्या में अणु होते हैं?</p>",
                    options_en: [
                        "<p>John Dalton</p>",
                        "<p>Albert Einstein</p>",
                        "<p>James Watt</p>",
                        "<p>Amedeo Avogadro</p>"
                    ],
                    options_hi: [
                        "<p>जॉन डाल्टन (John Dalton)</p>",
                        "<p>अल्बर्ट आइंस्टीन (Albert Einstein)</p>",
                        "<p>जेम्स वॉट (James Watt)</p>",
                        "<p>एमेडियो अवोगाद्रो (Amedeo Avogadro)</p>"
                    ],
                    solution_en: "<p>33.(d) <strong>Amedeo Avogadro.</strong> John Dalton presented his famous atomic theory in 1803, in which he described the atom as the smallest and indivisible particle of matter. This theory became the foundation of chemistry. James Watt, on the other hand, made important improvements to the steam engine, which accelerated the Industrial Revolution.</p>",
                    solution_hi: "<p>33.(d) <strong>एमेडियो अवोगाद्रो </strong>(Amedeo Avogadro)। जॉन डाल्टन ने 1803 में अपना प्रसिद्ध परमाणु सिद्धांत प्रस्तुत किया, जिसमें उन्होंने परमाणु को पदार्थ का सबसे छोटा और अविभाज्य कण बताया। यह सिद्धांत रसायन विज्ञान की नींव बना। दूसरी ओर, जेम्स वाट ने भाप इंजन में महत्वपूर्ण सुधार किए, जिसने औद्योगिक क्रांति को गति प्रदान की। &lsquo;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who among the following has been credited with developing a notation system for Indian classical music?</p>",
                    question_hi: "<p>34. निम्नलिखित में से किसे भारतीय शास्त्रीय संगीत के लिए एक संकेतन प्रणाली विकसित करने का श्रेय दिया गया है?</p>",
                    options_en: [
                        "<p>Kumar Gandharva</p>",
                        "<p>Vishnu Narayan Bhatkhande</p>",
                        "<p>Bal Gandharva</p>",
                        "<p>Bhimsen Joshi</p>"
                    ],
                    options_hi: [
                        "<p>कुमार गंधर्व</p>",
                        "<p>विष्णु नारायण भातखंडे</p>",
                        "<p>बाल गंधर्व</p>",
                        "<p>भीमसेन जोशी</p>"
                    ],
                    solution_en: "<p>34.(b)<strong> Vishnu Narayan Bhatkhande</strong> was the first person to write a modern treatise on Hindustani classical music. In 1918, he founded the renowned Madhav Sangeet Vidyalaya in Gwalior, Madhya Pradesh.</p>",
                    solution_hi: "<p>34.(b) <strong>विष्णु नारायण भातखंडे</strong> हिंदुस्तानी शास्त्रीय संगीत पर आधुनिक ग्रंथ लिखने वाले पहले व्यक्ति थे। 1918 में उन्होंने ग्वालियर, मध्य प्रदेश में प्रसिद्ध माधव संगीत संस्थान की स्थापना की थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. What is the main sense organ that virtual reality displays are aiming for?</p>",
                    question_hi: "<p>35. वह मुख्य संवेदी अंग कौन-सा है जिसके लिए वर्चुअल रियलिटी डिस्प्ले (Virtual reality displays) लक्षित करते हैं?</p>",
                    options_en: [
                        "<p>Taste</p>",
                        "<p>Smell</p>",
                        "<p>Vision</p>",
                        "<p>Touch</p>"
                    ],
                    options_hi: [
                        "<p>स्वाद</p>",
                        "<p>गंध</p>",
                        "<p>दृष्टि</p>",
                        "<p>स्पर्श</p>"
                    ],
                    solution_en: "<p>35.(c) <strong>Vision.</strong> Virtual reality (VR) aiming to create an immersive visual experience that simulates a virtual environment. While some VR systems may also incorporate audio and haptic feedback to engage the senses of hearing and touch, the main focus is on visual stimulation.</p>",
                    solution_hi: "<p>35.(c) <strong>दृष्टि। </strong>वर्चुअल रियलिटी (VR) का उद्देश्य एक ऐसा इमर्सिव विज़ुअल अनुभव बनाना है जो एक वर्चुअल वातावरण का अनुकरण करता है। जबकि कुछ VR सिस्टम श्रवण और स्पर्श की इंद्रियों को संलग्न करने के लिए ऑडियो और हैप्टिक फीडबैक को भी शामिल कर सकते हैं, इसका मुख्य ध्यान विज़ुअल स्टिमुलेशन पर होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following holy days in Christianity is a mourning day ?</p>",
                    question_hi: "<p>36.निम्नलिखित में से ईसाई धर्म में कौन सा पवित्र दिन शोक दिवस है?</p>",
                    options_en: [
                        "<p>Good Friday</p>",
                        "<p>Christmas</p>",
                        "<p>Easter</p>",
                        "<p>Maundy Thursday</p>"
                    ],
                    options_hi: [
                        "<p>गुड फ्राइडे</p>",
                        "<p>क्रिसमस</p>",
                        "<p>ईस्टर</p>",
                        "<p>मौन्डी थर्सडे</p>"
                    ],
                    solution_en: "<p>36.(a) <strong>Good Friday</strong> is a solemn holy day in Christianity, observed to commemorate the crucifixion and death of Jesus Christ. It is traditionally a day of mourning, reflection, and fasting. Christmas celebrates the birth of Jesus Christ, while Easter commemorates his resurrection. Maundy Thursday honors the Last Supper of Jesus Christ with his disciples.</p>",
                    solution_hi: "<p>36.(a)<strong> गुड फ्राइडे </strong>ईसाई धर्म में एक पवित्र दिन है, जिसे ईसा मसीह के सूली पर चढ़ने तथा मृत्यु की याद में मनाया जाता है। यह पारंपरिक रूप से शोक, चिंतन और उपवास का दिन है। क्रिसमस, ईसा मसीह के जन्म का उत्सव है, जबकि ईस्टर उनके पुनर्जीवन का स्मरण करता है। मौंडी थर्सडे को ईसा मसीह और उनके शिष्यों के अंतिम भोज का सम्मान किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. The Trimbakeshwar Temple, which is situated in Nashik, Maharashtra, was built by :</p>",
                    question_hi: "<p>37. महाराष्ट्र के नासिक स्थित त्र्यंबकेश्वर मंदिर किसके द्वारा बनवाया गया था?</p>",
                    options_en: [
                        "<p>Maharaja Ganga Singh</p>",
                        "<p>Maharana Pratap</p>",
                        "<p>Balaji Baji Rao</p>",
                        "<p>Shivaji</p>"
                    ],
                    options_hi: [
                        "<p>महाराजा गंगा सिंह</p>",
                        "<p>महाराणा प्रताप</p>",
                        "<p>बालाजी बाजी राव</p>",
                        "<p>शिवाजी</p>"
                    ],
                    solution_en: "<p>37.(c) <strong>Balaji Baji Rao.</strong> Trimbakeshwar Temple, a renowned religious center, houses one of the twelve Jyotirlingas and is located at the foot of Brahmagiri Hill in the city of Trimbak. Other notable temples and their builders include the Brihadeeswara Temple (Raja Raja Chola I of the Chola dynasty), Konark Sun Temple (King Narasimhadeva I of the Eastern Ganga Dynasty), and the Kailasa Temple at Ellora (Krishna I of the Rashtrakuta dynasty).</p>",
                    solution_hi: "<p>37.(c) <strong>बालाजी बाजी राव।</strong> त्र्यंबकेश्वर मंदिर, एक प्रसिद्ध धार्मिक केंद्र है, जो बारह ज्योतिर्लिंगों में से एक है और यह त्र्यंबक शहर में ब्रह्मगिरी पहाड़ी की तराई में स्थित है। अन्य उल्लेखनीय मंदिरों और उनके निर्माताओं में बृहदेश्वर मंदिर (चोल वंश के राजा राजा चोल प्रथम), कोणार्क सूर्य मंदिर (पूर्वी गंगा राजवंश के राजा नरसिंहदेव प्रथम) और एलोरा में कैलास मंदिर (राष्ट्रकूट वंश के कृष्ण प्रथम) शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. In a symport:</p>",
                    question_hi: "<p>38. एक सिमपोर्ट (symport) में:</p>",
                    options_en: [
                        "<p>both molecules cross the membrane in the same direction</p>",
                        "<p>a molecule moves across a membrane independent of other molecules</p>",
                        "<p>both molecules cross the membrane in the opposite direction</p>",
                        "<p>a molecule moves across a membrane dependent of other molecules</p>"
                    ],
                    options_hi: [
                        "<p>दोनों अणु एक ही दिशा में झिल्ली को पार करते हैं</p>",
                        "<p>एक अणु अन्य अणुओं से स्वतंत्र एक झिल्ली को पार करता है</p>",
                        "<p>दोनों अणु झिल्ली को विपरीत दिशा में पार करते हैं</p>",
                        "<p>एक अणु अन्य अणुओं के अनुसार झिल्ली को पार करता है</p>"
                    ],
                    solution_en: "<p>38.(a) Symport: Two or more molecules move together in the same direction across the membrane using the same transport protein. Antiport: Two or more molecules move in opposite directions across the membrane. Uniport: A single molecule moves across the membrane independently of other molecules.</p>",
                    solution_hi: "<p>38.(a) सिमपोर्ट: दो या दो से अधिक अणु एक ही परिवहन प्रोटीन का उपयोग करके झिल्ली के पार एक ही दिशा में एक साथ चलते हैं। एंटीपोर्ट: दो या दो से अधिक अणु झिल्ली के पार विपरीत दिशाओं में चलते हैं। यूनिपोर्ट: एकल अणु ,अन्य अणुओं से स्वतंत्र रूप से झिल्ली के पार चलता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. In the 11th century, under Maḥmūd of Ghazni, Ghaznī, a small town in ______, became&nbsp;the capital of the vast empire of the Ghaznavids.</p>",
                    question_hi: "<p>39. 11वीं शताब्दी में, ग़ज़नी के महमूद के अधीन, गजनी, ________ में एक छोटा-सा शहर, गजनवी के विशाल साम्राज्य की राजधानी बन गया।</p>",
                    options_en: [
                        "<p>Egypt</p>",
                        "<p>Persia</p>",
                        "<p>Afghanistan</p>",
                        "<p>Turkey</p>"
                    ],
                    options_hi: [
                        "<p>मिस्र</p>",
                        "<p>फारस</p>",
                        "<p>अफ़ग़ानिस्तान</p>",
                        "<p>टर्की</p>"
                    ],
                    solution_en: "<p>39.(c) <strong>Afghanistan.</strong> Mahmud of Ghazni was a Turkish conqueror who attacked India 17 times between 1000 to 1027 AD. In 1018 he plundered the holy city of Mathura and also attacked Kannauj. In 1019 and 1029 he undertook two raids on Gangetic valley. In 1025 he attacked Somnath (a town on the coast of Kathiawar). His last invasion was in 1027 to punish the Jats who obstructed him on his return journey from Somanath. He died in 1030.</p>",
                    solution_hi: "<p>39.(c) <strong>अफ़गानिस्तान।</strong> महमूद ग़ज़नी एक तुर्की विजेता था जिसने 1000 से 1027 ई. के बीच भारत पर 17 बार आक्रमण किया। 1018 में उसने मथुरा के पवित्र शहर को लूटा और कन्नौज पर भी आक्रमण किया। 1019 और 1029 में उसने गंगा घाटी पर दो बार आक्रमण किया। 1025 में उसने सोमनाथ (काठियावाड़ के तट पर स्थित एक शहर) पर आक्रमण किया। उसका अंतिम आक्रमण 1027 में जाटों को दण्डित करने के लिए हुआ था जिन्होंने सोमनाथ से उसकी वापसी यात्रा में बाधा डाली थी। 1030 में उसकी मृत्यु हो गई।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Consider the following statements and select the correct option:<br>i. Marginal Worker is a person who works for less than 183 days (or six months) in a year.<br>ii. Main Worker is a person who works for atleast 300 days (or ten months) in a year.</p>",
                    question_hi: "<p>40. निम्नलिखित कथनों पर विचार कीजिए और सही विकल्प का चयन कीजिए।<br>i. सीमांत श्रमिक, वह व्यक्ति है जो एक वर्ष में 183 दिन (या छह महीने) से कम कार्य करता है।<br>ii. मुख्य श्रमिक, वह व्यक्ति होता है जो एक वर्ष में कम से कम 300 दिन (या दस महीने) कार्य करता है।</p>",
                    options_en: [
                        "<p>Both i and ii are correct.</p>",
                        "<p>Only i is correct.</p>",
                        "<p>Both i and ii are incorrect.</p>",
                        "<p>Only ii is correct.</p>"
                    ],
                    options_hi: [
                        "<p>I और II, दोनों सही हैं।</p>",
                        "<p>केवल I सही है।</p>",
                        "<p>I और II, दोनों गलत हैं।</p>",
                        "<p>केवल II सही है।</p>"
                    ],
                    solution_en: "<p>40.(b) <strong>Only i is correct.</strong> The population of India according to their economic status is divided into three groups,&nbsp;namely; main workers, marginal workers and non-workers. A Main Worker is a person who works for at least 183 days ( or six months) in a year. Marginal Worker is a person who works for less than 183 days ( or six months) in a year.<br>The proportion of workers (both main and marginal) is only 39.8 per cent (2011) while about 60% were non-workers.</p>",
                    solution_hi: "<p>40.(b) <strong>केवल I सही है। </strong>भारत की जनसंख्या को उनकी आर्थिक स्थिति के अनुसार तीन समूहों में विभाजित किया गया है, अर्थात्; मुख्य श्रमिक, सीमांत श्रमिक और गैर-श्रमिक। मुख्य श्रमिक वह व्यक्ति है जो एक वर्ष में कम से कम 183 दिन (या छह महीने) काम करता है। सीमांत श्रमिक वह व्यक्ति है जो एक वर्ष में 183 दिन (या छह महीने) से कम काम करता है। श्रमिकों (मुख्य और सीमांत दोनों) का अनुपात केवल 39.8 प्रतिशत (2011) है जबकि लगभग 60% गैर-श्रमिक थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. In basketball, _________ is when a referee throws the ball up at the centre circle to determine which team gets possession.</p>",
                    question_hi: "<p>41. बास्केटबॉल में, जब एक रेफरी यह निर्धारित करने के लिए गेंद को सेंटर सर्कल में ऊपर फेंकता है कि किस टीम को गेंद प्राप्त होती है उसे _________कहा जाता है।</p>",
                    options_en: [
                        "<p>a hook ball</p>",
                        "<p>a dunk ball</p>",
                        "<p>a jump ball</p>",
                        "<p>a high ball</p>"
                    ],
                    options_hi: [
                        "<p>हुक बॉल</p>",
                        "<p>डंक बॉल</p>",
                        "<p>जम्प बॉल</p>",
                        "<p>हाई बॉल</p>"
                    ],
                    solution_en: "<p>41.(c) <strong>a jump ball.</strong> Basketball is played between two teams consist of twelve (12) members each. But only five players from each team may be on the court at one time. Shooting with either one hand or both hands is done in the following ways - Jump shot, Dunk shot, Free throw, Layup, Three-point shot, Hook shot. A hook shot is where the player swings the ball up and over their head in a sweeping motion with one hand. A dunk shot is where a player jumps and pushes the ball through the basket while it\'s still in their hand.</p>",
                    solution_hi: "<p>41.(c) <strong>जम्प बॉल।</strong> बास्केटबॉल दो टीमों के बीच खेला जाता है, जिनमें से प्रत्येक में बारह (12) सदस्य होते हैं। लेकिन प्रत्येक टीम के केवल पाँच खिलाड़ी ही एक समय में कोर्ट पर हो सकते हैं। एक हाथ या दोनों हाथों से फेंक सकते है जो निम्न तरीकों से की जाती है - जंप शॉट, डंक शॉट, फ़्री थ्रो, लेअप, थ्री-पॉइंट शॉट, हुक शॉट। हुक शॉट वह होता है जिसमें खिलाड़ी एक हाथ से गेंद को ऊपर और अपने सिर के ऊपर घुमाता है। डंक शॉट वह होता है जिसमें खिलाड़ी कूदता है और गेंद को बास्केट में धकेलता है जबकि गेंद अभी भी उसके हाथ में है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. What is the minimum age to become a member of Rajya Sabha?</p>",
                    question_hi: "<p>42. राज्य सभा का सदस्य बनने के लिए न्यूनतम आयु कितनी है?</p>",
                    options_en: [
                        "<p>35</p>",
                        "<p>40</p>",
                        "<p>25</p>",
                        "<p>30</p>"
                    ],
                    options_hi: [
                        "<p>35</p>",
                        "<p>40</p>",
                        "<p>25</p>",
                        "<p>30</p>"
                    ],
                    solution_en: "<p>42.(d) <strong>30. </strong>The Rajya Sabha, or Council of States, is the upper house of the Indian Parliament. The minimum ages for other Constitutional positions are: President (35), Governor (35), Lok Sabha member (25), Member of Legislative Assembly (25), and Member of Legislative Council (30).</p>",
                    solution_hi: "<p>42.(d) <strong>30.</strong> राज्य सभा या राज्य परिषद भारतीय संसद का उच्च सदन है। अन्य संवैधानिक पदों के लिए न्यूनतम आयु सीमा : राष्ट्रपति (35), राज्यपाल (35), लोकसभा सदस्य (25), विधान सभा सदस्य (25), और विधान परिषद सदस्य (30)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. In which year did Eric A. Cornell, Wolfgang Ketterle and Carl E. Wieman receive the Nobel Prize in Physics for the achieving \'Bose-Einstein condensation in dilute gases of alkali atoms\'?</p>",
                    question_hi: "<p>43. एरिक ए. कॉर्नेल (Eric A. Cornell), वोल्फगैंग केटरले (Wolfgang Ketterle) और कार्ल ई. वीमन (Carl E. Wieman) को क्षार परमाणुओं की तनु गैसों में \'बोस-आइंस्टीन संघनन (Bose-Einstein condensation)\' प्राप्त करने के लिए भौतिकी का नोबेल पुरस्कार किस वर्ष में मिला था?</p>",
                    options_en: [
                        "<p>2001</p>",
                        "<p>2003</p>",
                        "<p>2002</p>",
                        "<p>2000</p>"
                    ],
                    options_hi: [
                        "<p>2001 में</p>",
                        "<p>2003 में</p>",
                        "<p>2002 में</p>",
                        "<p>2000 में</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>2001.</strong> A Bose-Einstein Condensate (BEC) is a state of matter formed when particles known as bosons are cooled to temperatures near absolute zero. This achievement allowed atoms to occupy the same quantum state, forming a new state of matter and advancing quantum phenomena at ultra-low temperatures.</p>",
                    solution_hi: "<p>43.(a) <strong>2001. </strong>बोस-आइंस्टीन कंडेनसेट (BEC) पदार्थ की एक अवस्था है जो तब बनती है जब बोसान नामक कणों को परम शून्य के नजदीक तापमान तक ठंडा किया जाता है। इस उपलब्धि ने परमाणुओं को एक ही क्वांटम अवस्था में रहने की अनुमति दी, जिससे पदार्थ की एक नई अवस्था बनी और अत्यंत कम तापमान पर क्वांटम परिघटनाओं के अध्ययन को आगे बढ़ाया गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. In which year was the Department of Sports transformed into the Department of Youth Affairs and Sports?</p>",
                    question_hi: "<p>44. किस वर्ष खेल विभाग को युवा मामले एवं खेल विभाग में परिवर्तित किया गया था?</p>",
                    options_en: [
                        "<p>1983</p>",
                        "<p>1982</p>",
                        "<p>1985</p>",
                        "<p>1984</p>"
                    ],
                    options_hi: [
                        "<p>1983</p>",
                        "<p>1982</p>",
                        "<p>1985</p>",
                        "<p>1984</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>1985. </strong>The Ministry of Youth Affairs &amp; Sports was initially set up as the Department of Sports in 1982 at the time of organization of the IX Asian Games in New Delhi . Its name was changed to the Department of Youth affairs &amp; sports during celebration of the International Youth Year, 1985. It became a Ministry on 27th May, 2000. Subsequently, the Ministry has been bifurcated in Deptt. of Youth Affairs and Deptt. of Sports under two separate secretaries w.e.f. 30th April, 2008.</p>",
                    solution_hi: "<p>44.(c) <strong>1985.</strong> युवा मामले और खेल मंत्रालय की स्थापना 1982 में नई दिल्ली में 9वें एशियाई खेलों के आयोजन के समय खेल विभाग के रूप में की गई थी। अंतर्राष्ट्रीय युवा वर्ष, 1985 के उत्सव के दौरान इसका नाम बदलकर युवा मामले और खेल विभाग कर दिया गया। 27 मई, 2000 को यह एक मंत्रालय बन गया। इसके बाद, 30 अप्रैल, 2008 से मंत्रालय को दो अलग-अलग सचिवों के अधीन युवा मामले विभाग और खेल विभाग में विभाजित कर दिया गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Who released the Global Risk Report 2024?</p>",
                    question_hi: "<p>45. वैश्विक जोखिम रिपोर्ट 2024 किसने जारी की ?</p>",
                    options_en: [
                        "<p>United Nations</p>",
                        "<p>World Bank</p>",
                        "<p>World Economic Forum</p>",
                        "<p>International Monetary Fund</p>"
                    ],
                    options_hi: [
                        "<p>संयुक्त राष्ट्र</p>",
                        "<p>विश्व बैंक</p>",
                        "<p>विश्व आर्थिक मंच</p>",
                        "<p>अंतर्राष्ट्रीय मुद्रा कोष</p>"
                    ],
                    solution_en: "<p>45.(c) <strong>World Economic Forum. </strong>It is a Swiss nonprofit foundation established in 1971, based in Geneva, Switzerland. Some major reports published by the World Economic Forum (WEF) are: Energy Transition Index, Global Competitiveness Report, Global Gender Gap Report, and Global Travel and Tourism Report.</p>",
                    solution_hi: "<p>45.(c) <strong>विश्व आर्थिक मंच।</strong> यह 1971 में स्थापित एक स्विस गैर-लाभकारी संस्था है, जिसका मुख्यालय जिनेवा, स्विटजरलैंड में है। विश्व आर्थिक मंच (WEF) द्वारा प्रकाशित कुछ प्रमुख रिपोर्टें हैं: ऊर्जा संक्रमण सूचकांक, वैश्विक प्रतिस्पर्धात्मकता रिपोर्ट, वैश्विक लिंग अंतर रिपोर्ट और वैश्विक यात्रा और पर्यटन रिपोर्ट।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. What was India\'s annual exponential growth rate as per the 2011 Census?</p>",
                    question_hi: "<p>46. वर्ष 2011 की जनगणना के अनुसार भारत की वार्षिक चरघातांकीय वृद्&zwj;धि दर क्या थी?</p>",
                    options_en: [
                        "<p>1.10%</p>",
                        "<p>1.64%</p>",
                        "<p>4.34%</p>",
                        "<p>3.54%</p>"
                    ],
                    options_hi: [
                        "<p>1.10%</p>",
                        "<p>1.64%</p>",
                        "<p>4.34%</p>",
                        "<p>3.54%</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>1.64%</strong>. The 2011 Census was the 15th census in India and the 7th since the country gained Independence. Its slogan was &ldquo;Our Census, Our Future&rdquo;. Important Facts about Census 2011: Total Population of India - 1.21 billion, Density - 382 persons per square km, Highest literacy - Kerala (94%), Highest Population - Uttar Pradesh, Lowest Population - Sikkim, Highest Population Density (Statewise) - Bihar (1106), and Lowest Population Density - Arunachal Pradesh (17).</p>",
                    solution_hi: "<p>46.(b) <strong>1.64%. </strong>2011 की जनगणना भारत की 15वीं जनगणना थी और देश की आज़ादी के बाद से यह 7वीं जनगणना थी। इस जनगणना का नारा था \"हमारी जनगणना, हमारा भविष्य\"। जनगणना 2011 के बारे में महत्वपूर्ण तथ्य: भारत की कुल जनसंख्या - 1.21 बिलियन, घनत्व - 382 व्यक्ति प्रति वर्ग किमी, उच्चतम साक्षरता - केरल (94%), उच्चतम जनसंख्या - उत्तर प्रदेश, सबसे कम जनसंख्या - सिक्किम, उच्चतम जनसंख्या घनत्व (राज्यवार) - बिहार (1106), और सबसे कम जनसंख्या घनत्व - अरुणाचल प्रदेश (17)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following dynasties&rsquo; genealogy was found in the Bijolia inscription?</p>",
                    question_hi: "<p>47. निम्नलिखित में से किस राजवंश की वंशावली बिजौलिया शिलालेख में पाई गई है?</p>",
                    options_en: [
                        "<p>Gahadavala</p>",
                        "<p>Parmara</p>",
                        "<p>Chandela</p>",
                        "<p>Chahamana</p>"
                    ],
                    options_hi: [
                        "<p>गहड़वाल</p>",
                        "<p>परमार</p>",
                        "<p>चंदेल</p>",
                        "<p>चाहमान</p>"
                    ],
                    solution_en: "<p>47.(d) <strong>Chahamana. </strong>Bijoliya Inscription reveals that Chauhans are Vats Gotriya Brahman. This inscription provides information about the names of Jabalipur, Shakmbhari, Srimal. An inscription of Vikram Samvat 1226 Falgun Krishna Tritiya is inscribed on a rock near the northern wall of the ancient Parshvanath temple located in Bijolia.</p>",
                    solution_hi: "<p>47.(d) <strong>चाहमान।</strong> बिजौलिया अभिलेख से पता चलता है कि चौहान वत्स गोत्रीय ब्राह्मण थे। इस अभिलेख से जाबालिपुर, शाकम्भरी, श्रीमाल के नामों की जानकारी मिलती है। बिजौलिया स्थित प्राचीन पार्श्वनाथ मंदिर की उत्तरी दीवार के पास एक चट्टान पर विक्रम संवत 1226 फाल्गुन कृष्ण तृतीया का एक शिलालेख अंकित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which institution partnered with the Uttar Pradesh government for disaster management on February 13, 2024 ?</p>",
                    question_hi: "<p>48. 13 फरवरी, 2024 को आपदा प्रबंधन के लिए किस संस्थान ने उत्तर प्रदेश सरकार के साथ साझेदारी की?</p>",
                    options_en: [
                        "<p>IIT Kanpur</p>",
                        "<p>IIT Roorkee</p>",
                        "<p>IIT Bombay</p>",
                        "<p>IIT Delhi</p>"
                    ],
                    options_hi: [
                        "<p>IIT कानपुर</p>",
                        "<p>IIT रुड़की</p>",
                        "<p>IIT बॉम्बे</p>",
                        "<p>IIT दिल्ली</p>"
                    ],
                    solution_en: "<p>48.(b) <strong>IIT Roorkee.</strong> This collaboration aims to enhance disaster management in the state by leveraging IIT Roorkee&rsquo;s expertise in research and training. The partnership is expected to bolster the state&rsquo;s resilience against natural calamities over the next five years.</p>",
                    solution_hi: "<p>48.(b) <strong>IIT रुड़की</strong>। इस सहयोग का उद्देश्य अनुसंधान और प्रशिक्षण में IIT रुड़की की विशेषज्ञता का लाभ उठाकर राज्य में आपदा प्रबंधन को बढ़ाना है। इस साझेदारी से अगले पाँच वर्षों में प्राकृतिक आपदाओं के विरुद्ध राज्य की तन्यकता को बढ़ावा मिलने की उम्मीद है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Select the correct alternative on the basis of the statements given about &lsquo;Rasa&rsquo;. <br><strong>Statement I :</strong> It is the aesthetic experience that one derives while enjoying an artistic presentation. <br><strong>Statement II : </strong>It deals with the creation of human sentiments in the spectator or the aesthete.</p>",
                    question_hi: "<p>49. रस के संबंध में दिए गए कथनों के आधार पर सही विकल्प का चयन करें। <br><strong>कथन I :</strong> यह एक कलात्मक प्रस्तुति का आनंद लेने के दौरान प्राप्त होने वाला सौंदर्यपरक अनुभव है। <br><strong>कथन II :</strong> यह दर्शक या सौंदर्यप्रेमी में मानवीय भावनाओं के निर्माण से संबंधित है।</p>",
                    options_en: [
                        "<p>Only Statement I is true.</p>",
                        "<p>Both Statement I and Statement II are true</p>",
                        "<p>Only Statement II is true.</p>",
                        "<p>Neither Statement I nor Statement II is true.</p>"
                    ],
                    options_hi: [
                        "<p>केवल कथन I सत्य है।</p>",
                        "<p>कथन I और कथन II, दोनों सत्य हैं।</p>",
                        "<p>केवल कथन II सत्य है।</p>",
                        "<p>न तो कथन I और न ही कथन II सत्य है।</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>Both Statement I and Statement II are true.</strong> Rasa relates to human senses and evokes emotions in the mind through Natya (drama). However, the concept extends beyond Natya to encompass dance, music, cinema, and literature. Rasa signifies aesthetic pleasure or the joy experienced during a skillful performance, enhanced by exquisite poetry, music, and action.</p>",
                    solution_hi: "<p>49.(b)<strong> कथन I और कथन II दोनों सत्य हैं। </strong>रस मानवीय इंद्रियों से संबंधित है और नाट्य (नाटक) के माध्यम से मन में भावनाओं को जगाता है। हालाँकि, यह अवधारणा नाट्य से आगे बढ़कर नृत्य, संगीत, सिनेमा और साहित्य को भी शामिल करती है। रस सौंदर्य आनंद या एक कुशल प्रदर्शन के दौरान अनुभव किए जाने वाले आनंद को दर्शाता है, जिसे उत्कृष्ट कविता, संगीत और क्रिया द्वारा बढ़ाया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. In which musical note did Newland put the metals Co and Ni with halogens?</p>",
                    question_hi: "<p>50. न्यूलैंड ने धातु Co और Ni को हैलोजन के साथ किस म्यूजिकल नोट में रखा था?</p>",
                    options_en: [
                        "<p>Fa</p>",
                        "<p>Do</p>",
                        "<p>Re</p>",
                        "<p>Mi</p>"
                    ],
                    options_hi: [
                        "<p>Fa</p>",
                        "<p>Do</p>",
                        "<p>Re</p>",
                        "<p>Mi</p>"
                    ],
                    solution_en: "<p>50.(b)<strong> Do</strong>. The Newland table was created by John Newlands in 1866 based on his Law of Octaves. He observed that every eighth element exhibited properties similar to the first, drawing an analogy to the octaves in music.</p>",
                    solution_hi: "<p>50.(b)<strong> Do.</strong> न्यूलैंड तालिका जॉन न्यूलैंड द्वारा 1866 में उनके अष्टक नियम के आधार पर बनाई गई थी। उन्होंने देखा कि प्रत्येक आठवाँ तत्व पहले तत्व के समान गुण प्रदर्शित करता है, जो संगीत में अष्टक के समान है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A diver rowing at the speed of 3 km/h in still water takes double the time going 50 km upstream compared to going 50 km downstream. The speed of the diver against the stream is:</p>",
                    question_hi: "<p>51. स्थिर जल में 3 km/h की चाल से नाव चला रहे एक नाविक को धारा के प्रतिकूल 50 km जाने में, धारा के अनुकूल 50 km जाने की तुलना में दोगुना समय लगता है। धारा के प्रतिकूल नाविक की चाल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                        "<p>1 km/h</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                        "<p>2 km/h</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                        "<p>1 km/h</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> km/h</p>",
                        "<p>2 km/h</p>"
                    ],
                    solution_en: "<p>51.(d)<br>Speed of diver = 3 km/hr<br>Let speed of water = x km/hr<br>Downstream speed = 3 + x km/hr<br>Upstream speed = 3 - x km/hr<br>The diver takes double the time upstream compared to downstream.<br>Using the formula for time:<br>Time= <math display=\"inline\"><mfrac><mrow><mi>D</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>S</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi></mrow></mfrac></math><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>3</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math> = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>3</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math><br>On solving<br>x = 1 km/hr<br>Speed of the diver against the stream = 3 - 1 = 2 km/hr</p>",
                    solution_hi: "<p>51.(d)<br>नाविक की चाल = 3 km/hr<br>माना पानी की चाल = x km/hr<br>अनुकूल चाल = 3 + x km/hr<br>प्रतिकूल चाल = 3 - x km/hr<br>नाविक अनुकूल की तुलना में प्रतिकूल में दोगुना समय लेता है।<br>समय = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2330;&#2366;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math><br>&rArr; <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>3</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math> = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mrow><mn>3</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math><br>हल करने पर<br>x = 1 km/hr<br>धारा के प्रतिकूल नाविक की चाल = 3 - 1 = 2 km/hr</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Study the given table and answer the question that follows. <br>The given table shows the number of candidates who appeared (both male and female) in a Public Examination and the percentage of those who qualified in the examination from two states X and Z. Few values are missing in the table (indicated by ------). You will be required to fill them up according to the question.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461281358.png\" alt=\"rId44\" width=\"358\" height=\"151\"> <br>The number of appeared candidates from State Z increased by 100% from 2008 to 2009. If the total number of qualified candidates from State Z in 2008 and 2009 together is 552, what is the number of appeared candidates from State Z in 2009?</p>",
                    question_hi: "<p>52. दी गई तालिका का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br>दी गई तालिका दो राज्यों X और Z से एक लोक सेवा परीक्षा में उपस्थित होने वाले उम्मीदवारों (पुरुष और महिला दोनों) की संख्या तथा परीक्षा में उत्तीर्ण होने वाले उम्मीदवारों का प्रतिशत दर्शाती है। तालिका में कुछ मान लुप्त हैं (------ द्वारा दर्शाया गया है)। आपको प्रश्न के अनुसार इन्हें पूर्ण करना होगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461281471.png\" alt=\"rId45\" width=\"444\" height=\"136\"> <br>वर्ष 2008 से 2009 तक राज्य Z से उपस्थित होने वाले उम्मीदवारों की संख्या में 100% की वृद्धि हुई। यदि 2008 और 2009 में राज्य Z से उत्तीर्ण उम्मीदवारों की कुल संख्या 552 है, तो वर्ष 2009 में राज्य Z से उपस्थित उम्मीदवारों की संख्या कितनी है?</p>",
                    options_en: [
                        "<p>360</p>",
                        "<p>240</p>",
                        "<p>480</p>",
                        "<p>600</p>"
                    ],
                    options_hi: [
                        "<p>360</p>",
                        "<p>240</p>",
                        "<p>480</p>",
                        "<p>600</p>"
                    ],
                    solution_en: "<p>52.(c) Let the no. of appeared candidates in 2008 be 100 unit.<br>Then, no. of appeared candidates in 2009 = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 200 unit<br>According to the question,<br>Qualified candidates in the years 2008 = 100 &times; 70% = 70 unit<br>Qualified candidates in the years 2009 = 200 &times; 80% = 160 unit<br>Now,<br>&rArr; (70 + 160) unit = 552<br>&rArr; 230 unit = 552<br>&rArr; 200 unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>552</mn><mn>230</mn></mfrac></math> &times; 200 = 480</p>",
                    solution_hi: "<p>52.(c) माना कि 2008 में उपस्थित अभ्यर्थियों की संख्या 100 इकाई <br>तो, 2009 में उपस्थित उम्मीदवारों की संख्या = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 200 इकाई <br>प्रश्न के अनुसार,<br>वर्ष 2008 में उत्तीर्ण उम्मीदवार = 100 &times; 70% = 70 इकाई&nbsp;<br>वर्ष 2009 में उत्तीर्ण उम्मीदवार = 200 &times; 80% = 160 इकाई&nbsp;<br>अब,<br>&rArr; (70 + 160) इकाई = 552<br>&rArr; 230 इकाई = 552<br>&rArr; 200 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>552</mn><mn>230</mn></mfrac></math> &times; 200 = 480</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. How many factors of 14,400 are divisible by 18 but not by 36?</p>",
                    question_hi: "<p>53. 14,400 के कितने गुणनखंड 18 से विभाज्य हैं, लेकिन 36 से नहीं?</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>53.(a)<br>Factor of 14400 = 2<sup>6</sup>&nbsp;&times; 3<sup>2</sup> &times; 5<sup>2</sup><br>&rArr; 2<sup>6</sup> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 18 &times; (2<sup>5</sup> &times; 5<sup>2</sup>)<br>Divisible by 18 then remaining factor = 6 &times; 3 = 18<br>&rArr; 2<sup>6</sup> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 36 &times; (2<sup>4</sup> &times; 5<sup>2</sup>)<br>Divisible by 36 then remaining factor = 5 &times; 3 = 15<br>Required factor divisible by 18 but not by 36 = 18 - 15 = 3</p>",
                    solution_hi: "<p>53.(a)<br>14400 का गुणनखंड = 2<sup>6</sup>&nbsp;&times; 3<sup>2</sup> &times; 5<sup>2</sup><br>&rArr; 2<sup>6</sup> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 18 &times; (2<sup>5</sup> &times; 5<sup>2</sup>)<br>18 से विभाज्य तो शेष गुणनखंड = 6 &times; 3 = 18<br>&rArr; 2<sup>6</sup> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 36 &times; (2<sup>4</sup> &times; 5<sup>2</sup>)<br>36 से विभाज्य तो शेष गुणनखंड = 5 &times; 3 = 15<br>आवश्यक गुणनखंड 18 से विभाज्य है लेकिन 36 से नहीं = 18 - 15 = 3</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The value of&nbsp;&nbsp;(256)<sup>0.16</sup> &times; (256)<sup>0.09</sup> is</p>",
                    question_hi: "<p>54. (256)<sup>0.16</sup> &times; (256)<sup>0.09</sup>&nbsp;का मान क्या है ?</p>",
                    options_en: [
                        "<p>64</p>",
                        "<p>256</p>",
                        "<p>16</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>64</p>",
                        "<p>256</p>",
                        "<p>16</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>54.(d)<br>(256)<sup>0.16</sup> &times; (256)<sup>0.09</sup><br>= <math display=\"inline\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mrow><mn>8</mn></mrow></msup><mo>)</mo></mrow><mrow><mfrac><mrow><mn>16</mn></mrow><mrow><mn>100</mn></mrow></mfrac></mrow></msup></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>8</mn></msup><mo>)</mo></mrow><mfrac><mn>9</mn><mn>100</mn></mfrac></msup></math><br>= <math display=\"inline\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mrow><mn>8</mn></mrow></msup><mo>)</mo></mrow><mrow><mfrac><mrow><mn>16</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>9</mn></mrow><mrow><mn>100</mn></mrow></mfrac></mrow></msup></math><br>= <math display=\"inline\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mrow><mn>8</mn></mrow></msup><mo>)</mo></mrow><mrow><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></mrow></msup></math>= (2)<sup>2</sup> = 4</p>",
                    solution_hi: "<p>54.(d)<br>(256)<sup>0.16</sup> &times; (256)<sup>0.09</sup><br>= <math display=\"inline\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mrow><mn>8</mn></mrow></msup><mo>)</mo></mrow><mrow><mfrac><mrow><mn>16</mn></mrow><mrow><mn>100</mn></mrow></mfrac></mrow></msup></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>8</mn></msup><mo>)</mo></mrow><mfrac><mn>9</mn><mn>100</mn></mfrac></msup></math><br>= <math display=\"inline\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mrow><mn>8</mn></mrow></msup><mo>)</mo></mrow><mrow><mfrac><mrow><mn>16</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>9</mn></mrow><mrow><mn>100</mn></mrow></mfrac></mrow></msup></math><br>= <math display=\"inline\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mrow><mn>8</mn></mrow></msup><mo>)</mo></mrow><mrow><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></mrow></msup></math>= (2)<sup>2</sup> = 4</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. In △ABC, right angled at B if tanC = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>, then find&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>C</mi><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>C</mi></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mn>2</mn></msup><mi>C</mi></mrow></mfrac></mstyle></math></p>",
                    question_hi: "<p>55. △ABC में, B पर समकोण है। यदि tanC = &nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>C</mi><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>C</mi></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mn>2</mn></msup><mi>C</mi></mrow></mfrac></mstyle></math> ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>55.(a)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>C</mi><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>C</mi></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mn>2</mn></msup><mi>C</mi></mrow></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">a</mi><msup><mrow><mi mathvariant=\"bold-italic\">n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">C</mi></mrow></mfrac></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>55.(a)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>C</mi><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>C</mi></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mn>2</mn></msup><mi>C</mi></mrow></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">a</mi><msup><mrow><mi mathvariant=\"bold-italic\">n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">C</mi></mrow></mfrac></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A dishonest shopkeeper pretends to sell his goods at cost price. However, he uses a false weight on which 984 gm is written, but actually weighs lesser. Using this false weight, the shopkeeper makes a gain of 23%. The actual measure of the weight used is:</p>",
                    question_hi: "<p>56. एक बेईमान दुकानदार अपना माल क्रय मूल्य पर बेचने का दिखावा करता है। हालाँकि, वह गलत वजन का उपयोग करता है जिस पर 984 gm लिखा होता है, लेकिन वास्तव में इसका वजन कम होता है। इस गलत वजन का उपयोग करके, दुकानदार को 23% का लाभ होता है। प्रयुक्त वजन का वास्तविक माप कितना है?</p>",
                    options_en: [
                        "<p>935 gm</p>",
                        "<p>800 gm</p>",
                        "<p>850 gm</p>",
                        "<p>900 gm</p>"
                    ],
                    options_hi: [
                        "<p>935 gm</p>",
                        "<p>800 gm</p>",
                        "<p>850 gm</p>",
                        "<p>900 gm</p>"
                    ],
                    solution_en: "<p>56.(b)<br>We know that , <br>Price &prop; <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi></mrow><mrow><mi>W</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi><mi>&#160;</mi><mi>&#160;</mi></mrow></mfrac></math><br>A/Q<br>P%&nbsp; &nbsp; &nbsp; &rarr; 100 : 123<br>Weight &rarr; 123 : 100<br>So, <br>123 unit &rarr; 984 gm<br>100 unit &rarr; 800 gm<br>Actual weight = 800 gm</p>",
                    solution_hi: "<p>56.(b)<br>जैसा की हम जानते है , <br>कीमत &prop; <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi></mrow><mrow><mi>&#2357;</mi><mi>&#2332;</mi><mi>&#2344;</mi><mi>&#160;</mi><mi>&#160;</mi><mi>&#160;</mi></mrow></mfrac></math><br>प्रश्न के अनुसार <br>लाभ % &rarr; 100 : 123<br>वज़न&nbsp; &nbsp; &nbsp;&rarr; 123 : 100<br>इसलिए, <br>123 इकाई &rarr; 984 ग्राम<br>100 इकाई &rarr; 800 ग्राम<br>वास्तविक वज़न = 800 ग्राम</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If cos<sup>4</sup>&theta; - sin<sup>4</sup>&theta; = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>, then find the value of sin4&theta;.</p>",
                    question_hi: "<p>57. यदि cos<sup>4</sup>&theta; - sin<sup>4</sup>&theta; = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&nbsp;है, तो sin4&theta; का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>57.(a) <br>cos<sup>4</sup>&theta; - sin<sup>4</sup>&theta; = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>(cos<sup>2</sup>&theta;)<sup>2</sup> - (sin<sup>2</sup>&theta;)<sup>2</sup> = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>(cos<sup>2</sup>&theta; + sin<sup>2</sup>&theta;)(cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta;) = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>(cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta;) = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>Cos2&theta; =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> [Cos2&theta; = cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta;]<br>Cos<sup>2</sup>2&theta; = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>Now, sin<sup>2</sup>2&theta; = 1 - Cos<sup>2</sup>2&theta; = 1 - <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>sin<sup>2</sup>2&theta; = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>Sin2&theta; =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>Now, sin4&theta; = 2sin2&theta;cos2&theta; <br>Putting the value of Sin2&theta; and cos2&theta;<br>sin4&theta; = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac></math></p>",
                    solution_hi: "<p>57.(a) <br>cos<sup>4</sup>&theta; - sin<sup>4</sup>&theta; = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>(cos<sup>2</sup>&theta;)<sup>2</sup> - (sin<sup>2</sup>&theta;)<sup>2</sup> = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>(cos<sup>2</sup>&theta; + sin<sup>2</sup>&theta;)(cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta;) = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>(cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta;) = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>Cos2&theta; =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> [Cos2&theta; = cos<sup>2</sup>&theta; - sin<sup>2</sup>&theta;]<br>Cos<sup>2</sup>2&theta; = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>Now, sin<sup>2</sup>2&theta; = 1 - Cos<sup>2</sup>2&theta; = 1 - <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>sin<sup>2</sup>2&theta; = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>Sin2&theta; =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>अब, sin4&theta; = 2sin2&theta;cos2&theta; <br>Sin2&theta; और cos2&theta; का मान रखने पर <br>sin4&theta; = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>25</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Given that one cubic cm of marble weighs 25 gms, the weight of a cuboidal marble block 28 cm in width and 5 cm thick is 112 kg. The length of the block is:</p>",
                    question_hi: "<p>58. यदि एक घन सेंटीमीटर संगमरमर का भार 25 ग्राम है, 28 सेंटीमीटर चौड़े और 5 सेंटीमीटर मोटे संगमरमर के एक ब्लॉक का भार 112 किलोग्राम है। ब्लॉक की लंबाई क्या है ?</p>",
                    options_en: [
                        "<p>26.5 cm</p>",
                        "<p>36 cm</p>",
                        "<p>32 cm</p>",
                        "<p>37.5 cm</p>"
                    ],
                    options_hi: [
                        "<p>26.5 सेंटीमीटर</p>",
                        "<p>36 सेंटीमीटर</p>",
                        "<p>32 सेंटीमीटर</p>",
                        "<p>37.5 सेंटीमीटर</p>"
                    ],
                    solution_en: "<p>58.(c)<br>Let Length of the block = l cm<br>weighs = 25 grams/ cm<sup>3</sup><br>Total weight = 112 kg = 112000 grams<br>Volume of block = l &times; 28 &times; 5 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>112000</mn><mn>25</mn></mfrac></math> = 4480<br>&rArr; length (I) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4480</mn><mrow><mn>28</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></math> = 32 cm</p>",
                    solution_hi: "<p>58.(c)<br>माना ब्लॉक की लंबाई = l सेमी. <br>भार = 25 ग्राम/ सेमी<sup>3</sup><br>कुल वजन = 112 किलोग्राम = 112000 ग्राम<br>ब्लॉक की आयतन = l &times; 28 &times; 5 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>112000</mn><mn>25</mn></mfrac></math> = 4480<br>&rArr; लंबाई (I) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4480</mn><mrow><mn>28</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></math> = 32 cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Three bells toll at intervals of 12, 15 and 18 minutes, respectively. If all the three bells toll together at 8 a.m., when will they toll together next ?</p>",
                    question_hi: "<p>59. तीन घंटियाँ क्रमशः 12, 15 और 18 मिनट के अंतराल पर बजती हैं। यदि तीनों घंटियाँ 8 a.m. पर एक साथ बजती हैं, तो वे अगली बार एक साथ कब बजेगी?</p>",
                    options_en: [
                        "<p>8 a.m.</p>",
                        "<p>11 a.m.</p>",
                        "<p>3 p.m.</p>",
                        "<p>10 a.m.</p>"
                    ],
                    options_hi: [
                        "<p>8 a.m.</p>",
                        "<p>11 a.m.</p>",
                        "<p>3 p.m.</p>",
                        "<p>10 a.m.</p>"
                    ],
                    solution_en: "<p>59.(b) LCM of (12, 15 , 18) = 180 minutes = 3 hours<br>Now, They toll together next = 8 a.m. + 3 hours = 11 a.m.</p>",
                    solution_hi: "<p>59.(b) (12, 15 , 18) का LCM = 180 मिनट = 3 घंटे<br>अब, वे अगली बार एक साथ बजेगी = 8 a.m. + 3 घंटे = 11 a.m.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The sum of the length, the breadth and the height of a cuboid is 38 cm and the length of its diagonal is 22 cm. Find the total surface area of the cuboid.</p>",
                    question_hi: "<p>60. एक घनाभ की लंबाई, चौड़ाई और ऊंचाई का योगफल 38 cm है और इसके विकर्ण की लंबाई 22 सेमी है। घनाभ का कुल पृष्ठीय क्षेत्रफल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>480 cm&sup2;</p>",
                        "<p>432 cm&sup2;</p>",
                        "<p>960 cm&sup2;</p>",
                        "<p>864 cm&sup2;</p>"
                    ],
                    options_hi: [
                        "<p>480 cm&sup2;</p>",
                        "<p>432 cm&sup2;</p>",
                        "<p>960 cm&sup2;</p>",
                        "<p>864 cm&sup2;</p>"
                    ],
                    solution_en: "<p>60.(c)<br>l + b + h = 38 cm<br>&rArr; l<sup>2</sup> + b<sup>2</sup> + h<sup>2</sup> + 2lb + 2bh + 2 hl = 1444<br>Length of diagonal = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">l</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">h</mi><mn>2</mn></msup></msqrt></math> = 22<br>&rArr; l<sup>2</sup> + b<sup>2</sup> + h<sup>2</sup> = 484<br>&rArr; lb + bh + hl = 480<br>Total surface area of the cuboid = 2(lb + bh + hl) <br>= 2 &times;&nbsp;480 = 960 cm<sup>2</sup></p>",
                    solution_hi: "<p>60.(c)<br>l + b + h = 38 cm<br>&rArr; l<sup>2</sup> + b<sup>2</sup> + h<sup>2</sup> + 2lb + 2bh + 2 hl = 1444<br>विकर्ण की लंबाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">l</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">h</mi><mn>2</mn></msup></msqrt></math> = 22<br>&rArr; l<sup>2</sup> + b<sup>2</sup> + h<sup>2</sup> = 484<br>&rArr; lb + bh + hl = 480<br>घनाभ का कुल पृष्ठीय क्षेत्रफल = 2(lb + bh + hl) <br>= 2 &times;&nbsp;480 = 960 cm<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The expenditure of Swarna is 125% more than her savings. If her expenditure decreases by 6% and savings increases by 26.5%, then by what percentage does her income increase ?</p>",
                    question_hi: "<p>61. स्वर्णा का व्यय उसकी बचत से 125% अधिक है। यदि उसके व्यय में 6% की कमी हो जाती है और बचत में 26.5% की वृद्धि हो जाती है, तो उसकी आय में कितने प्रतिशत की वृद्धि होती है ?</p>",
                    options_en: [
                        "<p>20.5%</p>",
                        "<p>18.5%</p>",
                        "<p>8%</p>",
                        "<p>4%</p>"
                    ],
                    options_hi: [
                        "<p>20.5%</p>",
                        "<p>18.5%</p>",
                        "<p>8%</p>",
                        "<p>4%</p>"
                    ],
                    solution_en: "<p>61.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461281707.png\" alt=\"rId46\" width=\"188\" height=\"95\"><br>Percentage increase in their income = <math display=\"inline\"><mfrac><mrow><mn>135</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>130</mn></mrow><mrow><mn>130</mn></mrow></mfrac></math> &times; 100 = 4%</p>",
                    solution_hi: "<p>61.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461281707.png\" alt=\"rId46\" width=\"188\" height=\"95\"><br>उनके आय मे प्रतिशत वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>135</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>130</mn></mrow><mrow><mn>130</mn></mrow></mfrac></math> &times;100 = 4%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. If a regular polygon has 35 diagonals, then the sum of its interior angles is:</p>",
                    question_hi: "<p>62. यदि एक सम बहुभुज में 35 विकर्ण हैं, तो उसके अंतः कोणों का योग क्या है?</p>",
                    options_en: [
                        "<p>1620&deg;</p>",
                        "<p>1440&deg;</p>",
                        "<p>1980&deg;</p>",
                        "<p>1800&deg;</p>"
                    ],
                    options_hi: [
                        "<p>1620&deg;</p>",
                        "<p>1440&deg;</p>",
                        "<p>1980&deg;</p>",
                        "<p>1800&deg;</p>"
                    ],
                    solution_en: "<p>62.(b)<br>Number of diagonal for a n sided polygon = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mi>n</mi><mo>(</mo><mi>n</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>&rArr; 35 = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mi>n</mi><mo>(</mo><mi>n</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math> <br>&rArr; n(n - 3) = 70<br>&rArr; n<sup>2</sup>&nbsp;- 3n - 70 = 0<br>&rArr; n<sup>2</sup>&nbsp;- 10n + 7n - 70 = 0<br>&rArr; n(n - 10) + 7(n - 10) = 0&nbsp;<br>&rArr; (n - 10)(n + 7) = 0&nbsp;<br>So , n = 10<br>The sum of the interior angles of a polygon (S) = (n &minus; 2) &times; 180&deg;&nbsp;<br>= (10 &minus; 2 ) &times; 180&deg;<br>= 8 &times; 180&deg; = 1440&deg;</p>",
                    solution_hi: "<p>62.(b)<br>n भुजा वाले बहुभुज के विकर्णों की संख्या = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mi>n</mi><mo>(</mo><mi>n</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>&rArr; 35 = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mi>n</mi><mo>(</mo><mi>n</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math> <br>&rArr; n(n - 3) = 70<br>&rArr; n<sup>2</sup>&nbsp;- 3n - 70 = 0<br>&rArr; n<sup>2</sup>&nbsp;- 10n + 7n - 70 = 0<br>&rArr; n(n - 10) + 7(n - 10) = 0&nbsp;<br>&rArr; (n - 10)(n + 7) = 0&nbsp;<br>तो, n = 10<br>बहुभुज के आंतरिक कोणों का योग (S) = (n &minus; 2) &times; 180&deg;&nbsp;<br>= (10 &minus; 2) &times; 180&deg;<br>= 8 &times; 180&deg; = 1440&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If an increase of 40% is observed on an amount in 4 years at simple interest, what will be the compound interest on ₹80,000 after 4 years at the same rate of interest?</p>",
                    question_hi: "<p>63. यदि किसी राशि में साधारण ब्याज पर 4 वर्ष में 40% की वृद्धि होती है, तो समान ब्याज दर पर 4 वर्ष बाद ₹80,000 पर चक्रवृद्धि ब्याज क्या होगा?</p>",
                    options_en: [
                        "<p>₹ 39,724</p>",
                        "<p>₹ 29,728</p>",
                        "<p>₹ 17,128</p>",
                        "<p>₹ 37,128</p>"
                    ],
                    options_hi: [
                        "<p>₹ 39,724</p>",
                        "<p>₹ 29,728</p>",
                        "<p>₹ 17,128</p>",
                        "<p>₹ 37,128</p>"
                    ],
                    solution_en: "<p>63.(d) 40 % = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> <br>Let Principal = 5 unit , Amount after 4 year = 7 unit <br>&there4;Rate of interest(R) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mo>.</mo><mi>I</mi><mo>.</mo><mo>&#215;</mo><mn>100</mn></mrow><mrow><mi>P</mi><mo>&#215;</mo><mi>T</mi><mi>&#160;</mi></mrow></mfrac></math><br>&rArr; R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 10 %<br>Equivalent Rate of interest for 4 cycle of 10% = 46.41%<br><math display=\"inline\"><mo>&#8756;</mo></math> C.I. = 80,000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>46</mn><mo>.</mo><mn>41</mn></mrow><mn>100</mn></mfrac></math> = 8 &times; 4641 = ₹37128</p>",
                    solution_hi: "<p>63.(d) 40 % = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> <br>माना मूलधन = 5 इकाई , 4 वर्ष के बाद राशि = 7 इकाई <br>&there4;ब्याज दर (R) = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2348;&#2381;&#2351;&#2366;&#2332;</mi><mo>&#215;</mo><mn>100</mn></mrow><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi><mo>&#160;&#160;</mo></mrow></mfrac></math><br>&rArr; R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 10 %<br>10% के 4 चक्र के लिए कुल चक्रवृद्धि ब्याज = 46.41%<br><math display=\"inline\"><mo>&#8756;</mo></math> चक्रवृद्धि ब्याज = 80,000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>46</mn><mo>.</mo><mn>41</mn></mrow><mn>100</mn></mfrac></math> = 8 &times; 4641 = ₹37128</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If (a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> then what is the value of (a<sup>6</sup> + a<sup>-6</sup>) ?</p>",
                    question_hi: "<p>64. यदि (a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> है, तो (a<sup>6</sup> + a<sup>-6</sup>) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>3048190</p>",
                        "<p>3048542</p>",
                        "<p>3048132</p>",
                        "<p>3048625</p>"
                    ],
                    options_hi: [
                        "<p>3048190</p>",
                        "<p>3048542</p>",
                        "<p>3048132</p>",
                        "<p>3048625</p>"
                    ],
                    solution_en: "<p>64.(a) (a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>a<sup>2</sup> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> = (7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>)<sup>2</sup> - 2 = 49 &times; 3 - 2 =145<br>(a<sup>6</sup> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>6</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math>) = 145<sup>3</sup> - 3 &times; 145 <br>= 3048625 - 435 = 3048190</p>",
                    solution_hi: "<p>64(a) (a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>a<sup>2</sup> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> = (7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>)<sup>2</sup> - 2 = 49 &times; 3 - 2 =145<br>(a<sup>6</sup> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>6</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math>) = 145<sup>3</sup> - 3 &times; 145 <br>= 3048625 - 435 = 3048190</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. In a circle, the chords AB and CD intersect each other at point L (internally). If AL = 9 cm, LB = 7 cm and LD = 3 cm, then find CL (in cm).</p>",
                    question_hi: "<p>65. एक वृत्त में, जीवा AB और CD एक दूसरे को बिंदु L पर (आंतरिक रूप से) प्रतिच्छेद करती हैं। यदि AL = 9 cm, LB = 7 cm और LD = 3 cm है, तो CL (cm में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>18</p>",
                        "<p>21</p>",
                        "<p>20</p>",
                        "<p>19</p>"
                    ],
                    options_hi: [
                        "<p>18</p>",
                        "<p>21</p>",
                        "<p>20</p>",
                        "<p>19</p>"
                    ],
                    solution_en: "<p>65.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461281862.png\" alt=\"rId47\" width=\"135\" height=\"118\"><br>DL &times; LC = BL &times; LA<br>3 &times; LC = 7 &times; 9<br>LC = 21 cm</p>",
                    solution_hi: "<p>65.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740461281862.png\" alt=\"rId47\" width=\"135\" height=\"118\"><br>DL &times; LC = BL &times; LA<br>3 &times; LC = 7 &times; 9<br>LC = 21 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A shopkeeper of a subsidised canteen mixed three varieties of rice, X, Y and Z, in the ratio of 4 : 1 : 3 to make a packet of 4 kg of rice. The cost prices per kg of X, Y and Z varieties of rice are ₹2, ₹4 and ₹6, respectively. What is the cost price (in ₹) of the packet of rice?</p>",
                    question_hi: "<p>66. सब्सिडी वाली कैंटीन के एक दुकानदार ने 4 kg चावल का एक पैकेट बनाने के लिए 4 : 1 : 3 के अनुपात में चावल की तीन किस्मों, X, Y और Z को मिलाया। X, Y और Z किस्मों के चावल का प्रति kg क्रय मूल्य क्रमशः ₹2, ₹4 और ₹6 हैं। चावल के पैकेट का क्रय मूल्य (₹ में) क्या होगा?</p>",
                    options_en: [
                        "<p>10</p>",
                        "<p>15</p>",
                        "<p>30</p>",
                        "<p>18</p>"
                    ],
                    options_hi: [
                        "<p>10</p>",
                        "<p>15</p>",
                        "<p>30</p>",
                        "<p>18</p>"
                    ],
                    solution_en: "<p>66.(b) Let the three varieties of rice be X = 4a , Y = a and Z = 3a<br>According to question,<br>&rArr; 8a = 4 kg <br>&rArr; a = 0.5 kg <br>Then,<br>X = 4 &times; 0.5 = 2 kg , Y = 0.5kg and Z = 3 &times; 0.5 = 1.5 kg<br>Now,<br>Cost of the packet = 2 &times; ₹ 2 + 0.5 &times; ₹ 4 + 1.5 &times; ₹ 6 <br>= 4 + 2 + 9 = ₹ 15</p>",
                    solution_hi: "<p>66.(b) माना कि चावल की तीन किस्में X = 4a&nbsp;, Y = a और Z = 3a हैं<br>प्रश्न के अनुसार,<br>&rArr; 8a = 4 kg <br>&rArr; a = 0.5 kg <br>तब,<br>X = 4 &times; 0.5 = 2 kg , Y = 0.5 kg और Z = 3 &times; 0.5 = 1.5 kg<br>अब,<br>पैकेट की कीमत = 2 &times; ₹ 2 + 0.5 &times; ₹ 4 + 1.5 &times; ₹ 6 <br>= 4 + 2 + 9 = ₹ 15</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. An alloy contains the metals A, B and C in the ratio 2 : 3 : 1 and another contains the metals B, C and D in the ratio 5 : 4 : 3. If equal weights of both alloys are mixed together to form a third alloy, then how much part of the metal B is in the new alloy?</p>",
                    question_hi: "<p>67. एक मिश्र धातु में धातु A, B और C का अनुपात 2 : 3 : 1 है और दूसरी मिश्रधातु में धातु B, C और D का अनुपात 5 : 4 : 3 है। यदि दोनों मिश्र धातुओं के बराबर वजन को एक साथ मिलाकर एक तीसरी मिश्रधातु बनाई जाती है, तो नई मिश्रधातु में धातु में B का कितना भाग होगा?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>67.(d)<br>Ratio&nbsp; &rarr; A : B : C : D<br>Alloy<sub>1</sub> &rarr; 2 : 3 : 1 )&nbsp; &nbsp; &nbsp; &times; 2<br>Alloy<sub>2</sub> &rarr;&nbsp; &nbsp; : 5 : 4 : 3<br>For equal weight,<br>Ratio&nbsp; &rarr; A : B : C : D<br>Alloy<sub>1</sub> &rarr; 4 : 6 : 2 <br>Alloy<sub>2</sub> &rarr;&nbsp; &nbsp; : 5 : 4 : 3<br>-----------------------------------------<br>Mixture &rarr;&nbsp;&nbsp;4 : 11 : 6 : 3<br>Part of metal B in new alloy = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mrow><mn>4</mn><mo>+</mo><mn>11</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>24</mn></mfrac></math></p>",
                    solution_hi: "<p>67.(d)<br>अनुपात&nbsp; &nbsp; &rarr; A : B : C : D<br>मिश्रधातु <sub>1</sub> &rarr; 2 : 3 : 1 )&nbsp; &nbsp; &nbsp;&times; 2<br>मिश्रधातु <sub>2</sub> &rarr;&nbsp; &nbsp; : 5 : 4 : 3<br>समान वजन के लिए,<br>अनुपात&nbsp; &nbsp; &rarr; A : B : C : D<br>मिश्रधातु <sub>1</sub> &rarr; 4 : 6 : 2 <br>मिश्रधातु <sub>2</sub> &rarr;&nbsp; &nbsp; : 5 : 4 : 3<br>-----------------------------------------<br>मिश्रण &rarr; 4 : 11 : 6 : 3<br>नये मिश्रधातु में धातु B का भाग = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mrow><mn>4</mn><mo>+</mo><mn>11</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>24</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Two trains travelling in the same direction at 40 km/h and 22 km/h cross each other completely in 1 minute. If the length of the first train is 125 m, what is the length of the second train ?</p>",
                    question_hi: "<p>68. 40 km/h और 22 km/h की चाल से एक ही दिशा में चल रही दो रेलगाड़ियां 1 मिनट में एक-दूसरे को पूरी तरह से पार कर जाती हैं। यदि पहली रेलगाड़ी की लंबाई 125 m है, तो दूसरी रेलगाड़ी की लंबाई कितनी है ?</p>",
                    options_en: [
                        "<p>175 m</p>",
                        "<p>185 m</p>",
                        "<p>150 m</p>",
                        "<p>125 m</p>"
                    ],
                    options_hi: [
                        "<p>175 m</p>",
                        "<p>185 m</p>",
                        "<p>150 m</p>",
                        "<p>125 m</p>"
                    ],
                    solution_en: "<p>68.(a) Let the length of the train be x&nbsp;m<br>Relative speed = 40 - 22 = 18 km/h = 18 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 5 m/s<br>&rArr; Time = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>Distance</mi><mi>speed</mi></mfrac></math><br>&rArr; 1 &times; 60 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>125</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mn>5</mn></mfrac></math><br>&rArr; 300 = 125 + x<br>&rArr; x = 300 - 125 = 175 m</p>",
                    solution_hi: "<p>68.(a) माना ट्रेन कि लम्बाई x&nbsp;m है।<br>सापेक्ष गति = 40 - 22 = 18 km/h = 18 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 5 m/s<br>&rArr; समय = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi>&#2327;&#2340;&#2367;</mi></mfrac></math><br>&rArr; 1 &times; 60 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>125</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mn>5</mn></mfrac></math><br>&rArr; 300 = 125 + x<br>&rArr; x = 300 - 125 = 175 m</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The salaries of P and Q together amount to ₹2,000. P spends 95% of his salary and Q spends 85% of his salary. If now their savings are the same, then P\'s salary is :</p>",
                    question_hi: "<p>69. P और Q का वेतन मिलाकर ₹2,000 है। P अपने वेतन का 95% खर्च करता है और Q अपने वेतन का 85% खर्च करता है। यदि अब उनकी बचत समान है, तो P का वेतन क्या है?</p>",
                    options_en: [
                        "<p>₹1,500</p>",
                        "<p>₹500</p>",
                        "<p>₹1,250</p>",
                        "<p>₹750</p>"
                    ],
                    options_hi: [
                        "<p>₹1,500</p>",
                        "<p>₹500</p>",
                        "<p>₹1,250</p>",
                        "<p>₹750</p>"
                    ],
                    solution_en: "<p>69.(a)<br>Ratio &rArr; Income =&nbsp; expenditure + saving<br>P&nbsp; &nbsp; &nbsp; &nbsp;&rArr;&nbsp; &nbsp; 100&nbsp; &nbsp; =&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;95&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;+&nbsp; &nbsp; &nbsp; 5) &times; 3<br>Q&nbsp; &nbsp; &nbsp; &nbsp;&rArr;&nbsp; &nbsp; 100&nbsp; &nbsp; =&nbsp; &nbsp; &nbsp; &nbsp; 85&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; +&nbsp; &nbsp; &nbsp;15) &times; 1<br>--------------------------------------------------------------<br>According to question ,<br>Total salaries of P and Q (300 + 100 ) units = (400 units) &rArr; 2000 Rs. <br>Then , P&rsquo;s salary (300 units) = 1,500 Rs.</p>",
                    solution_hi: "<p>69.(a)<br>अनुपात &rArr;&nbsp; &nbsp; आय&nbsp; &nbsp; =&nbsp; &nbsp; &nbsp; व्यय&nbsp; &nbsp;+&nbsp; &nbsp;बचत<br>P&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rArr;&nbsp; &nbsp; 100&nbsp; &nbsp; =&nbsp; &nbsp; &nbsp; &nbsp; 95&nbsp; &nbsp;+&nbsp; &nbsp; &nbsp; 5) &times; 3<br>Q&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rArr;&nbsp; &nbsp; 100&nbsp; &nbsp; =&nbsp; &nbsp; &nbsp; &nbsp; 85&nbsp; &nbsp;+&nbsp; &nbsp; &nbsp;15) &times; 1<br>--------------------------------------------------------------<br>प्रश्न के अनुसार,<br>P और Q (300 + 100 ) इकाई का कुल वेतन = (400 इकाई ) &rArr; 2000 Rs. <br>फिर, &lsquo;P&rsquo; का वेतन (300 इकाई ) = 1,500 Rs.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A man bought an equal number of oranges at the rate of 3 for one rupee and at the rate of 2 for one rupee. At what price per dozen should he sell them to earn a profit of 20%?</p>",
                    question_hi: "<p>70. एक व्यक्ति ने एक रुपए में 3 की दर से और एक रुपए में 2 की दर से समान संख्या में संतरे खरीदे। 20% का लाभ अर्जित के लिए उसे उन्हें प्रति दर्जन किस मूल्य पर बेचना चाहिए?</p>",
                    options_en: [
                        "<p>₹7</p>",
                        "<p>₹4</p>",
                        "<p>₹5</p>",
                        "<p>₹6</p>"
                    ],
                    options_hi: [
                        "<p>₹7</p>",
                        "<p>₹4</p>",
                        "<p>₹5</p>",
                        "<p>₹6</p>"
                    ],
                    solution_en: "<p>70.(d)<br>Let the number of oranges in both cases be 12 i.e. LCM of (3,2)<br>CP of 12 oranges at the rate of 3 = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = ₹4<br>CP of 12 oranges at the rate of 2 = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹6<br>CP of 24 oranges = 4 + 6 = ₹10<br>Then, CP of 12 oranges = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹5<br>Required SP to earn a profit of 20% = 5 &times; <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹6</p>",
                    solution_hi: "<p>70.(d)<br>माना दोनों मामलों में संतरों की संख्या 12 (3 और 2 का LCM) है। <br>3 की दर से 12 संतरों का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = ₹4<br>2 की दर से 12 संतरों का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹6<br>24 संतरों का क्रय मूल्य = 4 + 6 = ₹10<br>फिर, 12 संतरों का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹5<br>20% का लाभ अर्जित करने के लिए आवश्यक क्रय मूल्य = 5 &times; <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹6</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. What is the effective discount percentage if a shopkeeper offers a scheme discount &lsquo;buy five and get one free\' ?</p>",
                    question_hi: "<p>71. यदि एक दुकानदार &lsquo;पाँच खरीदें, एक मुफ्त पाएँ\' की एक छूट योजना की घोषणा करता है, तो प्रभावी छूट प्रतिशत कितना होगा?</p>",
                    options_en: [
                        "<p>15.67%</p>",
                        "<p>16.33%</p>",
                        "<p>15.33%</p>",
                        "<p>16.67%</p>"
                    ],
                    options_hi: [
                        "<p>15.67%</p>",
                        "<p>16.33%</p>",
                        "<p>15.33%</p>",
                        "<p>16.67%</p>"
                    ],
                    solution_en: "<p>71.(d)<br>Buy 5 get 1 free<br>Let MP = 6 unit<br>Then, SP = 5 unit (as 1 unit is free)<br>Discount % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 100 = 16.67%</p>",
                    solution_hi: "<p>71.(d)<br>5 खरीदें 1 मुफ्त पाएं<br>मान लीजिए अंकित मूल्य = 6 इकाई<br>फिर, विक्रय मूल्य = 5 यूनिट (क्योंकि 1 यूनिट मुफ़्त है)<br>छूट % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 100 = 16.67%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A group of people consists of men, women, and children. 40% of them are men, 35% are women and the rest are children, and their average weights are 70 kg, 60 kg and 30 kg, respectively. The average weight of the group is:</p>",
                    question_hi: "<p>72. कुछ लोगों के एक समूह में पुरुष, महिलाएं और बच्चे शामिल हैं। उनमें से 40% पुरुष है, 35% महिलाएं हैं और शेष बच्चे हैं, और उनका औसत भार क्रमशः 70 kg, 60 kg और 30 kg है। समूह का औसत भार कितना है ?</p>",
                    options_en: [
                        "<p>45.5 kg</p>",
                        "<p>46.5 kg</p>",
                        "<p>55.5 kg</p>",
                        "<p>56.5 kg</p>"
                    ],
                    options_hi: [
                        "<p>45.5 kg</p>",
                        "<p>46.5 kg</p>",
                        "<p>55.5 kg</p>",
                        "<p>56.5 kg</p>"
                    ],
                    solution_en: "<p>72.(d)<br>Let , total no. of peoples = 100 unit <br>No. of men = 40 unit <br>No. of women = 35 unit <br>No. of children = 25 unit <br>Average weight = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>70</mn><mo>+</mo><mn>35</mn><mo>&#215;</mo><mn>60</mn><mo>+</mo><mn>25</mn><mo>&#215;</mo><mn>30</mn></mrow><mn>100</mn></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>5650</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 56.50</p>",
                    solution_hi: "<p>72.(d) <br>माना , कुल लोगों की संख्या = 100 इकाई <br>पुरुषों की संख्या = 40 इकाई <br>महिलाओं की संख्या = 35 इकाई <br>बच्चों की संख्या = 25 इकाई <br>औसत भार = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>70</mn><mo>+</mo><mn>35</mn><mo>&#215;</mo><mn>60</mn><mo>+</mo><mn>25</mn><mo>&#215;</mo><mn>30</mn></mrow><mn>100</mn></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>5650</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 56.50</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. For a data, if the mean is 28.5 and the median is 32, then the mode using empirical formula is :</p>",
                    question_hi: "<p>73. किसी आकड़ें के लिए, यदि माध्य 28.5 है और माध्यक 32 है, तो आनुभविक सूत्र (empirical formula) का प्रयोग करते हुए बहुलक ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>41</p>",
                        "<p>40</p>",
                        "<p>42</p>",
                        "<p>39</p>"
                    ],
                    options_hi: [
                        "<p>41</p>",
                        "<p>40</p>",
                        "<p>42</p>",
                        "<p>39</p>"
                    ],
                    solution_en: "<p>73(d)<br>Mode = 3 &times; Median &ndash; 2 &times; Mean <br>Mode = 3 &times; 32 &ndash; 2 &times; 28.5<br>= 96 - 57 <br>= 39</p>",
                    solution_hi: "<p>73.(d)<br>बहुलक = 3 &times; माध्यक - 2 &times; माध्य <br>बहुलक = 3 &times; 32 - 2 &times; 28.5<br>= 96 - 57 <br>= 39</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A clock is sold for ₹550 cash or in the instalment scheme, for ₹250 cash down payment and ₹310 after one month. Find the rate of interest charged in the instalment scheme.</p>",
                    question_hi: "<p>74. एक घड़ी ₹550 नकद या किश्त योजना में ₹250 नकद डाउन पेमेंट और एक महीने बाद ₹310 में बेची&nbsp;जाती है। किश्त योजना में लिए जाने वाले ब्याज की दर ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>35%</p>",
                        "<p>45%</p>",
                        "<p>40%</p>",
                        "<p>20%</p>"
                    ],
                    options_hi: [
                        "<p>35%</p>",
                        "<p>45%</p>",
                        "<p>40%</p>",
                        "<p>20%</p>"
                    ],
                    solution_en: "<p>74.(c)<br>Interest = 550 - (250 + 310) = 10<br>Financed amount = 550 - 250 = 300</p>\n<p dir=\"ltr\">Time = 1 month = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>12</mn><mi>&#160;</mi></mrow></mfrac></math> year</p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>10</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>300</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>R</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><mn>100</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>12</mn></mrow></mfrac></math></p>\n<p dir=\"ltr\">R = 40%</p>\n<p dir=\"ltr\">&nbsp;</p>",
                    solution_hi: "<p>74.(c)<br>ब्याज = 550 - (250 + 310) = 10<br>वित्तपोषित राशि = 550 - 250 = 300</p>\n<p>समय = 1 महीना = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>12</mn><mi>&#160;</mi></mrow></mfrac></math> वर्ष</p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>10</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>300</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>R</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>1</mn></mrow><mrow><mn>100</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>12</mn></mrow></mfrac></math></p>\n<p dir=\"ltr\">R = 40%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The sum of the first 8 prime numbers divided by 7 is equal to ________ .</p>",
                    question_hi: "<p>75. प्रथम 8 अभाज्य संख्याओं का योग 7 से भाग देने पर ________ बराबर होता है।</p>",
                    options_en: [
                        "<p>14</p>",
                        "<p>13</p>",
                        "<p>11</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>14</p>",
                        "<p>13</p>",
                        "<p>11</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>75.(c)<br>First 8 prime numbers = 2,3,5,7,11,13,17,19<br>Sum = 2 + 3 + 5 + 7 + 11 + 13 + 17 + 19 = 77<br>Required number = <math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 11</p>",
                    solution_hi: "<p>75.(c)<br>पहली आठ अभाज्य संख्याएं = 2,3,5,7,11,13,17,19<br>राशि = 2 + 3 + 5 + 7 + 11 + 13 + 17 + 19 = 77<br>आवश्यक संख्या = <math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 11</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate ANTONYM of the given word.<br>Resistance</p>",
                    question_hi: "<p>76. Select the most appropriate ANTONYM of the given word.<br>Resistance</p>",
                    options_en: [
                        "<p>Tenderness</p>",
                        "<p>Tolerance</p>",
                        "<p>Reliance</p>",
                        "<p>Awareness</p>"
                    ],
                    options_hi: [
                        "<p>Tenderness</p>",
                        "<p>Tolerance</p>",
                        "<p>Reliance</p>",
                        "<p>Awareness</p>"
                    ],
                    solution_en: "<p>76.(b) <strong>Tolerance -</strong> the quality of allowing people to do or believe what they want although you do not agree with it.<br><strong>Resistance -</strong> the refusal to accept or comply with something. <br><strong>Tenderness -</strong> gentleness and kindness.<br><strong>Reliance -</strong> the fact of depending on someone or something.<br><strong>Awareness -</strong> the mental state of knowing about something.</p>",
                    solution_hi: "<p>76.(b) <strong>Tolerance</strong> (सहनशीलता) - the quality of allowing people to do or believe what they want although you do not agree with it.<br><strong>Resistance </strong>(प्रतिरोध) - the refusal to accept or comply with something. <br><strong>Tenderness </strong>(दयालुता) - gentleness and kindness.<br><strong>Reliance</strong> (निर्भरता) - the fact of depending on someone or something.<br><strong>Awareness </strong>(जागरूकता) - the mental state of knowing about something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. The following sentence has been divided into four segments. Identify the segment that contains an adverbial usage error. <br>Mrs. Gomathi was / that tired and weak / that she could / scarcely walk.</p>",
                    question_hi: "<p>77. The following sentence has been divided into four segments. Identify the segment that contains an adverbial usage error. <br>Mrs. Gomathi was / that tired and weak / that she could / scarcely walk.</p>",
                    options_en: [
                        "<p>Mrs. Gomathi was</p>",
                        "<p>that tired and weak</p>",
                        "<p>that she could</p>",
                        "<p>scarcely walk</p>"
                    ],
                    options_hi: [
                        "<p>Mrs. Gomathi was</p>",
                        "<p>that tired and weak</p>",
                        "<p>that she could</p>",
                        "<p>scarcely walk</p>"
                    ],
                    solution_en: "<p>77.(b) that tired and weak<br>Use of &lsquo;that&rsquo; with &lsquo;that&rsquo; is incorrect here. &lsquo;So&hellip;that&rsquo; is the correct conjunction pair. Hence, &lsquo;so tired and weak&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(b) that tired and weak<br>यहाँ &lsquo;that&rsquo; के साथ &lsquo;that&rsquo; का प्रयोग incorrect है। &lsquo;So&hellip;that&rsquo; सही conjunction pair है। इसलिए, &lsquo;so tired and weak&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>They <span style=\"text-decoration: underline;\"><strong>should have calmly thought of</strong></span> the advantages that would accrue to them.</p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>They <span style=\"text-decoration: underline;\"><strong>should have calmly thought of</strong></span> the advantages that would accrue to them.</p>",
                    options_en: [
                        "<p>should have been calm in thinking about</p>",
                        "<p>should be calmly thought of</p>",
                        "<p>shall have to calmly thought of</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>should have been calm in thinking about</p>",
                        "<p>should be calmly thought of</p>",
                        "<p>shall have to calmly thought of</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>78.(d) No improvement. The given sentence is grammatically correct.</p>",
                    solution_hi: "<p>78.(d) No improvement./ दिया गया वाक्य grammatically सही है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate meaning of the idiom.<br>Moved from pillar to post</p>",
                    question_hi: "<p>79. Select the most appropriate meaning of the idiom.<br>Moved from pillar to post</p>",
                    options_en: [
                        "<p>went to pillars</p>",
                        "<p>went to post</p>",
                        "<p>went to money lender</p>",
                        "<p>tried his best</p>"
                    ],
                    options_hi: [
                        "<p>went to pillars</p>",
                        "<p>went to post</p>",
                        "<p>went to money lender</p>",
                        "<p>tried his best</p>"
                    ],
                    solution_en: "<p>79.(d) tried his best<br>Moved from pillar to post- tried his best<br>E.g.- The poor man moved from pillar to post to get money.</p>",
                    solution_hi: "<p>79.(d) tried his best<br>Moved from pillar to post- पूरी कोशिश की। <br>E.g.- The poor man moved from pillar to post to get money./ गरीब आदमी ने पैसा पाने के लिए पूरी कोशिश की। </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence: <br>John said, &ldquo;I shall be 21 tomorrow.&rdquo;</p>",
                    question_hi: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence: <br>John said, &ldquo;I shall be 21 tomorrow.&rdquo;</p>",
                    options_en: [
                        "<p>John said that he would be 21 tomorrow.</p>",
                        "<p>John said that he would be 21 the following day</p>",
                        "<p>John said that he should be 21 the following day.</p>",
                        "<p>John said that I shall be 21 tomorrow.</p>"
                    ],
                    options_hi: [
                        "<p>John said that he would be 21 tomorrow.</p>",
                        "<p>John said that he would be 21 the following day</p>",
                        "<p>John said that he should be 21 the following day.</p>",
                        "<p>John said that I shall be 21 tomorrow.</p>"
                    ],
                    solution_en: "<p>80.(b) John said that he would be 21 the following day<br>(a.) John said that he would be 21 tomorrow.(Tomorrow has not been changed)<br>(c). John said that he should be 21 the following day.(Meaning change)<br>(d). John said that I shall be 21 tomorrow.(Tomorrow not changed)</p>",
                    solution_hi: "<p>90.(b) John said that he would be 21 the following day<br>(a.) John said that he would be 21 tomorrow.(Tomorrow को बदला नहीं गया )<br>(c). John said that he should be 21 the following day.(अर्थ बदल गया )<br>(d). John said that I shall be 21 tomorrow.(Tomorrow को बदला नहीं गया )</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that expresses the given sentence in passive voice.&nbsp;</p>\n<p>The tour guide always answers the visitors\' questions.</p>",
                    question_hi: "<p>81. Select the option that expresses the given sentence in passive voice.</p>\n<p>The tour guide always answers the visitors\' questions.</p>",
                    options_en: [
                        "<p>The visitors\' question was always answered by the tour guide.</p>",
                        "<p>The visitors\' questions are answered by the tour guide always.</p>",
                        "<p>The visitors\' questions are always answered by the tour guide.</p>",
                        "<p>The visitors\' questions are answered always by the tour guide.</p>"
                    ],
                    options_hi: [
                        "<p>The visitors\' question was always answered by the tour guide.</p>",
                        "<p>The visitors\' questions are answered by the tour guide always.</p>",
                        "<p>The visitors\' questions are always answered by the tour guide.</p>",
                        "<p>The visitors\' questions are answered always by the tour guide.</p>"
                    ],
                    solution_en: "<p>81.(c) The visitors\' questions are always answered by the tour guide. (Correct)<br>(a) The visitors\' question <span style=\"text-decoration: underline;\">was</span> always answered by the tour guide. (Incorrect Tense)<br>(b) The visitors\' questions are answered by the tour guide always. (&lsquo;Always&rsquo; is missing)<br>(d) The visitors\' questions are answered <span style=\"text-decoration: underline;\">always</span> by the tour guide. (Incorrect Use of &lsquo;Always&rsquo;)</p>",
                    solution_hi: "<p>81.(c) The visitors\' questions are always answered by the tour guide. (Correct)<br>(a) The visitors\' question <span style=\"text-decoration: underline;\">was</span> always answered by the tour guide. (गलत Tense)<br>(b) The visitors\' questions are answered by the tour guide always. (&lsquo;Always&rsquo; missing है)<br>(d) The visitors\' questions are answered<span style=\"text-decoration: underline;\"> always</span> by the tour guide. (&lsquo;Always&rsquo; का गलत प्रयोग)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Identify the segment in the sentence, which contains the grammatical error.<br>The first inning of the final match between India-Pakistan was very sensational.</p>",
                    question_hi: "<p>82. Identify the segment in the sentence, which contains the grammatical error.<br>The first inning of the final match between India-Pakistan was very sensational.</p>",
                    options_en: [
                        "<p>The first inning</p>",
                        "<p>of the match</p>",
                        "<p>was very sensational</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>The first inning</p>",
                        "<p>of the match</p>",
                        "<p>was very sensational</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>82.(a) The first inning.<br>Some Nouns like <span style=\"text-decoration: underline;\">Cattle, Baggage, Scenery, Furniture, Innings</span>, etc. are always written in the singular <span style=\"text-decoration: underline;\">form</span> so we can\'t make their plural form. For example, &lsquo;furniture&rsquo; is correct but &lsquo;furnitures&rsquo; is incorrect. However, these nouns always take a <span style=\"text-decoration: underline;\">verb in its singular form</span>. Similarly, in the given sentence, &lsquo;innings&rsquo; is the correct noun that takes &lsquo;was&rsquo; as a singular verb.</p>",
                    solution_hi: "<p>82.(a) The first inning.<br>कुछ Nouns जैसे Cattle, Baggage, Scenery, Furniture, Innings आदि हमेशा singular form (एकवचन रूप) में लिखे जाते हैं इसलिए हम उनका plural form नहीं बना सकते। उदाहरण के लिए, &lsquo;furniture&rsquo; सही है लेकिन &lsquo;furnitures&rsquo; गलत है। हालाँकि, ये nouns हमेशा verb को उसके singular form में लेती हैं। इसी प्रकार, दिए गए वाक्य में, \'innings\' सही noun है जो \'was\' को singular verb के रूप में लेती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate synonym of the given word.<br>Dainty</p>",
                    question_hi: "<p>83. Select the most appropriate synonym of the given word.<br>Dainty</p>",
                    options_en: [
                        "<p>Elegant</p>",
                        "<p>Sable</p>",
                        "<p>Fine</p>",
                        "<p>Vulgar</p>"
                    ],
                    options_hi: [
                        "<p>Elegant</p>",
                        "<p>Sable</p>",
                        "<p>Fine</p>",
                        "<p>Vulgar</p>"
                    ],
                    solution_en: "<p>83.(a) <strong>Elegant-</strong> graceful and stylish in appearance or manner.<br><strong>Dainty-</strong> delicate, graceful, or refined.<br><strong>Sable-</strong> a dark brown or black color.<br><strong>Fine- </strong>of high quality or very thin or delicate.<br><strong>Vulgar-</strong> lacking sophistication or good taste.</p>",
                    solution_hi: "<p>83.(a) <strong>Elegant</strong> (सुरुचिपूर्ण) - graceful and stylish in appearance or manner.<br><strong>Dainty</strong> (सुन्दर) - delicate, graceful, or refined.<br><strong>Sable</strong> (काला रंग) - a dark brown or black color.<br><strong>Fine</strong> (अच्छा) - of high quality or very thin or delicate.<br><strong>Vulgar</strong> (अश्लील) - lacking sophistication or good taste.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Given below are four jumbled sentences. Find the correct order: <br><strong>P :</strong> He wanted to do something to deliver humanity from all such misery. He reflected on this problem for long.&nbsp;<br><strong>Q : </strong>Siddhartha was greatly touched as he saw an old man, a sick man and a dead body.&nbsp;<br><strong>R : </strong>At last on hearing some words from the mouth of a hermit which encouraged him to renounce the world, he decided to leave the palace and go into the forest for meditation.&nbsp;<br><strong>S : </strong>Before going, he had a lasting glance on his beloved wife Yashodhra and son, Rahul, who were enjoying a sound sleep at midnight.</p>",
                    question_hi: "<p>84. Given below are four jumbled sentences. Find the correct order: <br><strong>P :</strong> He wanted to do something to deliver humanity from all such misery. He reflected on this problem for long.&nbsp;<br><strong>Q : </strong>Siddhartha was greatly touched as he saw an old man, a sick man and a dead body.&nbsp;<br><strong>R :</strong> At last on hearing some words from the mouth of a hermit which encouraged him to renounce the world, he decided to leave the palace and go into the forest for meditation.&nbsp;<br><strong>S :</strong> Before going, he had a lasting glance on his beloved wife Yashodhra and son, Rahul, who were enjoying a sound sleep at midnight.<br>Options:</p>",
                    options_en: [
                        "<p>PRSQ</p>",
                        "<p>RPQS</p>",
                        "<p>PQRS</p>",
                        "<p>QPRS</p>"
                    ],
                    options_hi: [
                        "<p>PRSQ</p>",
                        "<p>RPQS</p>",
                        "<p>PQRS</p>",
                        "<p>QPRS</p>"
                    ],
                    solution_en: "<p>84.(d) QPRS <br>Sentence Q is the starting line of the parajumble because it tells the subject of the parajumble that is about the life of Buddha when he saw an old man, a sick man and a dead body. Then, Sentence P tells that after he saw all this he wanted to deliver humanity from all such misery. So, P follows Q Going through the options, only option (d) shows P follows Q so Option (d) is the correct answer.</p>",
                    solution_hi: "<p>84.(d) QPRS <br>वाक्य Q parajumble की शुरुआती लाइन है क्योंकि यह parajumble के विषय को बताता है जो बुद्ध के जीवन के बारे में है जब उन्होंने एक बूढ़े आदमी, एक बीमार आदमी और एक शव को देखा। फिर, वाक्य P बताता है कि यह सब देखने के बाद वह मानवता को ऐसे सभी दुखों से मुक्ति दिलाना चाहता थे। इसलिए Q के बाद P आएगा। Options के माध्यम से जाने पर, केवल option (d) से पता चलता है कि Q के बाद P आएगा, इसलिए option (d) सही उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>85. Select the INCORRECTLY spelt word.</p>",
                    options_en: [
                        "<p>Dilemma</p>",
                        "<p>Enterpreneur</p>",
                        "<p>Accommodate</p>",
                        "<p>Disseminate</p>"
                    ],
                    options_hi: [
                        "<p>Dilemma</p>",
                        "<p>Enterpreneur</p>",
                        "<p>Accommodate</p>",
                        "<p>Disseminate</p>"
                    ],
                    solution_en: "<p>85.(b) Enterpreneur <br>&lsquo;Entrepreneur&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>85.(b) Enterpreneur <br>&lsquo;Entrepreneur&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that expresses the given sentence in a passive voice.</p>\n<p>The French had regarded the scholar as a usurper.</p>",
                    question_hi: "<p>86. Select the option that expresses the given sentence in a passive voice.</p>\n<p>&nbsp;The French had regarded the scholar as a usurper.</p>",
                    options_en: [
                        "<p>The scholar was being regarded as a usurper by the French.</p>",
                        "<p>The scholar has been regarded as a usurper by the French.</p>",
                        "<p>The scholar was regarded as a usurper by the French.</p>",
                        "<p>The scholar had been regarded as a usurper by the French.</p>"
                    ],
                    options_hi: [
                        "<p>The scholar was being regarded as a usurper by the French.</p>",
                        "<p>The scholar has been regarded as a usurper by the French.</p>",
                        "<p>The scholar was regarded as a usurper by the French.</p>",
                        "<p>The scholar had been regarded as a usurper by the French.</p>"
                    ],
                    solution_en: "<p>86.(d) The scholar had been regarded as a usurper by the French. (Correct)<br>(a) The scholar <span style=\"text-decoration: underline;\">was being</span> regarded as a usurper by the French. (Incorrect Helping verb)<br>(b) The scholar <span style=\"text-decoration: underline;\">has been</span> regarded as a usurper by the French. (Incorrect Tense)<br>(c) The scholar <span style=\"text-decoration: underline;\">was</span> regarded as a usurper by the French. (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>86.(d) The scholar had been regarded as a usurper by the French. (Correct)<br>(a) The scholar <span style=\"text-decoration: underline;\">was being</span> regarded as a usurper by the French. (गलत Helping verb)<br>(b) The scholar <span style=\"text-decoration: underline;\">has been</span> regarded as a usurper by the French. (गलत Tense)<br>(c) The scholar <span style=\"text-decoration: underline;\">was</span> regarded as a usurper by the French. (गलत Helping Verb)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Identify the most appropriate meaning of the given idiom.<br>Bread and butter</p>",
                    question_hi: "<p>87. Identify the most appropriate meaning of the given idiom.<br>Bread and butter</p>",
                    options_en: [
                        "<p>Comfortable living</p>",
                        "<p>Means of livelihood</p>",
                        "<p>Money making</p>",
                        "<p>The breakfast</p>"
                    ],
                    options_hi: [
                        "<p>Comfortable living</p>",
                        "<p>Means of livelihood</p>",
                        "<p>Money making</p>",
                        "<p>The breakfast</p>"
                    ],
                    solution_en: "<p>87.(b) <strong>Bread and butter- </strong>means of livelihood.<br>E.g.- Teaching is his bread and butter because it is his main source of income.</p>",
                    solution_hi: "<p>87.(b)<strong> Bread and butter-</strong> means of livelihood./आजीविका का साधन<br>E.g.- Teaching is his bread and butter because it is his main source of income.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the word which means the same as the group of words given. <br>One who intervenes between two or more parties to settle differences</p>",
                    question_hi: "<p>88. Select the word which means the same as the group of words given. <br>One who intervenes between two or more parties to settle differences</p>",
                    options_en: [
                        "<p>Neutral</p>",
                        "<p>Intermediary</p>",
                        "<p>Judge</p>",
                        "<p>Connoisseur</p>"
                    ],
                    options_hi: [
                        "<p>Neutral</p>",
                        "<p>Intermediary</p>",
                        "<p>Judge</p>",
                        "<p>Connoisseur</p>"
                    ],
                    solution_en: "<p>88.(b) <strong>Intermediary-</strong> a person who acts as a link between people in order to try and bring about an agreement, arbiter<br><strong>Neutral-</strong> having no strongly marked or positive characteristics or features, unbiased<br><strong>Judge-</strong> form an opinion or conclusion about.<br><strong>Connoisseur-</strong> an expert judge in matters of taste.</p>",
                    solution_hi: "<p>88.(b) <strong>Intermediary- </strong>एक व्यक्ति जो एक समझौते में लोगों के बीच एक कड़ी के रूप में कार्य करता है<br><strong>Neutral- </strong>निष्पक्ष, तटस्थ<br><strong>Judge-</strong> न्यायाधीश<br><strong>Connoisseur- </strong>पारखी। </p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. The underlined phrase in the given sentence has been used incorrectly. Select the option that can correctly replace the underlined segment.<br>I am <span style=\"text-decoration: underline;\">down with</span> her complaints.</p>",
                    question_hi: "<p>89. The underlined phrase in the given sentence has been used incorrectly. Select the option that can correctly replace the underlined segment.<br>I am <span style=\"text-decoration: underline;\">down with</span> her complaints.</p>",
                    options_en: [
                        "<p>looked down with</p>",
                        "<p>passed out with</p>",
                        "<p>fed up with</p>",
                        "<p>held down with</p>"
                    ],
                    options_hi: [
                        "<p>looked down with</p>",
                        "<p>passed out with</p>",
                        "<p>fed up with</p>",
                        "<p>held down with</p>"
                    ],
                    solution_en: "<p>89.(c) <strong>fed up with</strong><br>&lsquo;Fed up with&rsquo; means being irritated or frustrated. The given sentence states that the person is frustrated or annoyed with someone\'s complaints. Hence &lsquo;fed up with&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(c) <strong>fed up with</strong><br>&lsquo;Fed up with&rsquo; का अर्थ है, चिड़चिड़ा (irritated) या निराश होना (frustrated)। दिए गए sentence में बताया गया है कि व्यक्ति किसी की शिकायतों से निराश (frustrated) या परेशान (annoyed) है। अतः, &lsquo;fed up with&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that can be used as a one-word substitute for the given group of words.<br>A professional rider in horse races</p>",
                    question_hi: "<p>90. Select the option that can be used as a one-word substitute for the given group of words.<br>A professional rider in horse races</p>",
                    options_en: [
                        "<p>Batsman</p>",
                        "<p>Jockey</p>",
                        "<p>Stuntman</p>",
                        "<p>Southpaw</p>"
                    ],
                    options_hi: [
                        "<p>Batsman</p>",
                        "<p>Jockey</p>",
                        "<p>Stuntman</p>",
                        "<p>Southpaw</p>"
                    ],
                    solution_en: "<p>90.(b) <strong>Jockey -</strong> a professional rider in horse races.<br><strong>Batsman -</strong> a person who tries to hit the ball and score runs.<br><strong>Stuntman -</strong> a man who performs stunts.<br><strong>Southpaw -</strong> a person who uses the left hand with greater skill than the right.</p>",
                    solution_hi: "<p>90.(b) <strong>Jockey</strong> (घुड़दौड़ का सवार) - a professional rider in horse races.<br><strong>Batsman </strong>(बल्लेबाज) - a person who tries to hit the ball and score runs.<br><strong>Stuntman</strong> (करतब दिखानेवाला) - a man who performs stunts.<br><strong>Southpaw </strong>(खब्बा) - a person who uses the left hand with greater skill than the right.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate synonym of the given word.<br>Infuriate</p>",
                    question_hi: "<p>91. Select the most appropriate synonym of the given word.<br>Infuriate</p>",
                    options_en: [
                        "<p>lazy</p>",
                        "<p>disgrace</p>",
                        "<p>threaten</p>",
                        "<p>enrage</p>"
                    ],
                    options_hi: [
                        "<p>lazy</p>",
                        "<p>disgrace</p>",
                        "<p>threaten</p>",
                        "<p>enrage</p>"
                    ],
                    solution_en: "<p>91.(d) <strong>Enrage -</strong> to make somebody very angry<br><strong>Lazy -</strong> moving slowly or without much energy<br><strong>Disgrace -</strong> the state of not being respected by other people, usually because you have behaved badly<br><strong>Threaten -</strong> to warn that you may hurt, kill or punish somebody if he/she does not do what you want</p>",
                    solution_hi: "<p>91.(d) <strong>Enrage -</strong> किसी को बहुत क्रोधित करना<br><strong>Lazy -</strong> आलसी <br><strong>Disgrace - </strong>अन्य लोगों द्वारा सम्मान न किए जाने की स्थिति, आमतौर पर क्योंकि आपने बुरा व्यवहार किया है।<br><strong>Threaten - </strong>चेतावनी देना या दंडित करना , डराना, धमकाना</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate antonym of the given word.<br>Fickle</p>",
                    question_hi: "<p>92. Select the most appropriate antonym of the given word.<br>Fickle</p>",
                    options_en: [
                        "<p>subtle</p>",
                        "<p>crafty</p>",
                        "<p>stable</p>",
                        "<p>flighty</p>"
                    ],
                    options_hi: [
                        "<p>subtle</p>",
                        "<p>crafty</p>",
                        "<p>stable</p>",
                        "<p>flighty</p>"
                    ],
                    solution_en: "<p>92.(c) <strong>Stable- </strong>steady, firm and unlikely to change<br><strong>Fickle- </strong>changing frequently<br><strong>Subtle</strong>- so delicate or precise as to be difficult to analyse or describe.<br><strong>Craft- </strong>clever at achieving one\'s aims by indirect or deceitful methods.<br><strong>Flighty- </strong>fickle and irresponsible.</p>",
                    solution_hi: "<p>92.(c) <strong>Stable-</strong> स्थिर, दृढ़ <br><strong>Fickle- </strong>बार-बार बदलता रहता है<br><strong>Subtle-</strong> इतना नाजुक या सटीक कि उसका विश्लेषण या वर्णन करना कठिन हो।<br><strong>Craft- </strong>अप्रत्यक्ष या कपटपूर्ण तरीकों से अपने लक्ष्य को प्राप्त करने में चतुर। <br>Flighty- चंचल और गैरजिम्मेदार।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Given below are four jumbled sentences. Find the correct order: <br><strong>P : </strong>There was once a time when people looked forward to lazy evenings.&nbsp;<br><strong>Q :</strong> They had a simple choice of programmes on Doordarshan.&nbsp;<br><strong>R :</strong> It was entirely up to them- to watch or not to watch the selected presentation.&nbsp;<br><strong>S : </strong>Today, the satellite and Cable Television have stormed the media world of information and entertainment.</p>",
                    question_hi: "<p>93. Given below are four jumbled sentences. Find the correct order: <br><strong>P : </strong>There was once a time when people looked forward to lazy evenings.&nbsp;<br><strong>Q :</strong> They had a simple choice of programmes on Doordarshan.&nbsp;<br><strong>R :</strong> It was entirely up to them- to watch or not to watch the selected presentation.&nbsp;<br><strong>S : </strong>Today, the satellite and Cable Television have stormed the media world of information and entertainment.</p>",
                    options_en: [
                        "<p>PSQR</p>",
                        "<p>RQPS</p>",
                        "<p>PQRS</p>",
                        "<p>QPRS</p>"
                    ],
                    options_hi: [
                        "<p>PSQR</p>",
                        "<p>RQPS</p>",
                        "<p>PQRS</p>",
                        "<p>QPRS</p>"
                    ],
                    solution_en: "<p>93.(c) <strong>PQRS</strong> <br>Sentence P is the starting line of the parajumble because it tells the subject of the parajumble that is about the time when people looked forward to lazy evenings. Then, Sentence Q tells that they had limited and simple choices of programmes on Doordarshan.So, Q follows P. Going through the options, only option (c) shows Q follows P so Option (c) is the correct answer.</p>",
                    solution_hi: "<p>93.(c) <strong>PQRS</strong><br>वाक्य P parajumble की शुरुआती लाइन है क्योंकि यह parajumble के विषय को बताता है जो उस समय के बारे में है जब लोग आलस भरी शाम की प्रतीक्षा करते थे। फिर, वाक्य Q बताता है कि उनके पास दूरदर्शन पर कार्यक्रमों के सीमित और सरल विकल्प थे। इसलिए, P के बाद Q आएगा। Options के माध्यम से जाने पर, केवल option(c) दिखाता है कि P के बाद Q आएगा, इसलिए option(c) सही उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank.<br>Man is essentially a_______animal and tends to associate with others.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank.<br>Man is essentially a_______animal and tends to associate with others.</p>",
                    options_en: [
                        "<p>sentimental</p>",
                        "<p>gregarious</p>",
                        "<p>selfish</p>",
                        "<p>perverse</p>"
                    ],
                    options_hi: [
                        "<p>sentimental</p>",
                        "<p>gregarious</p>",
                        "<p>selfish</p>",
                        "<p>perverse</p>"
                    ],
                    solution_en: "<p>94.(b)<strong> gregarious</strong><br>&lsquo;Gregarious&rsquo; means those who like to live in groups. The given sentence states that man likes to&nbsp;live in groups. Hence, &lsquo;gregarious&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>94.(b)<strong>&nbsp;&lsquo;Gregarious&rsquo;</strong>- मिलनसार। दिए गए वाक्य में कहा गया है कि man likes to live in groups,<br>इसलिए, &lsquo;gregarious&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>By the time I __________ India, the construction of my new house will have been completed.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>By the time I __________ India, the construction of my new house will have been completed.</p>",
                    options_en: [
                        "<p>will reach</p>",
                        "<p>reach</p>",
                        "<p>reaches</p>",
                        "<p>reached</p>"
                    ],
                    options_hi: [
                        "<p>will reach</p>",
                        "<p>reach</p>",
                        "<p>reaches</p>",
                        "<p>reached</p>"
                    ],
                    solution_en: "<p>95(b) <strong>reach.</strong> <br>&ldquo;<span style=\"text-decoration: underline;\">By the time</span> + V<sub>1</sub>(<span style=\"text-decoration: underline;\">first form of the verb</span>), <span style=\"text-decoration: underline;\">will have +</span> V<sub>3</sub>(<span style=\"text-decoration: underline;\">third form of the verb</span>)&rdquo; is grammatically the correct structure for the given sentence. Hence, &lsquo;reach(V<sub>1</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(b) <strong>reach.</strong> <br><span style=\"text-decoration: underline;\">&ldquo;By the time</span> + V<sub>1</sub>(<span style=\"text-decoration: underline;\">first form of the verb</span>), <span style=\"text-decoration: underline;\">will have +</span> V<sub>3</sub>(<span style=\"text-decoration: underline;\">third form of the verb</span>)&rdquo; दिए गए वाक्य के लिए grammatically (व्याकरणिक रूप) से सही संरचना है। इसलिए, \'&lsquo;reach(V<sub>1</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :-</strong> <br>The ___(96)___ of Bengal tigers left in the world has___(97)___ from 100,000 to 4,000 over the last century.The main threats are ___(98)___ of habitat, poaching and the trade in tiger parts for Eastern medicines. Most Bengal tigers live in protected areas of India. Anti-poaching task-forces have been ____(99)___ up and there is also a trade ____(100)__ on tiger products in many countries, as a measure to save this rare species.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test :-</strong> <br>The ___(96)___ of Bengal tigers left in the world has___(97)___ from 100,000 to 4,000 over the last century.The main threats are ___(98)___ of habitat, poaching and the trade in tiger parts for Eastern medicines. Most Bengal tigers live in protected areas of India. Anti-poaching task-forces have been ____(99)___ up and there is also a trade ____(100)__ on tiger products in many countries, as a measure to save this rare species.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: [
                        "<p>form</p>",
                        "<p>kind</p>",
                        "<p>glory</p>",
                        "<p>number</p>"
                    ],
                    options_hi: [
                        "<p>form</p>",
                        "<p>kind</p>",
                        "<p>glory</p>",
                        "<p>number</p>"
                    ],
                    solution_en: "<p>96.(d) <strong>number.</strong> <br>&lsquo;Number&rsquo; means a word or symbol that indicates a quantity. The given passage talks about the number of Bengal tigers.</p>",
                    solution_hi: "<p>96.(d) <strong>number.</strong> <br>&lsquo;Number&rsquo; का अर्थ है एक शब्द या प्रतीक जो एक मात्रा (quantity) को दर्शाता है। दिया गया passage बंगाल के बाघों की संख्या के बारे में है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :-</strong> <br>The ___(96)___ of Bengal tigers left in the world has___(97)___ from 100,000 to 4,000 over the last century.The main threats are ___(98)___ of habitat, poaching and the trade in tiger parts for Eastern medicines. Most Bengal tigers live in protected areas of India. Anti-poaching task-forces have been ____(99)___ up and there is also a trade ____(100)__ on tiger products in many countries, as a measure to save this rare species.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test :-</strong> <br>The ___(96)___ of Bengal tigers left in the world has___(97)___ from 100,000 to 4,000 over the last century.The main threats are ___(98)___ of habitat, poaching and the trade in tiger parts for Eastern medicines. Most Bengal tigers live in protected areas of India. Anti-poaching task-forces have been ____(99)___ up and there is also a trade ____(100)__ on tiger products in many countries, as a measure to save this rare species.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: [
                        "<p>limited</p>",
                        "<p>shrunk</p>",
                        "<p>abolished</p>",
                        "<p>eliminated</p>"
                    ],
                    options_hi: [
                        "<p>limited</p>",
                        "<p>shrunk</p>",
                        "<p>abolished</p>",
                        "<p>eliminated</p>"
                    ],
                    solution_en: "<p>97.(b)<strong> shrunk.</strong> <br>&lsquo;Shrunk&rsquo; means to become smaller or make something smaller. The given passage states that the number of Bengal tigers left in the world has shrunk(become less) from 100,000 to 4,000 over the last century.</p>",
                    solution_hi: "<p>97.(b)<strong> shrunk.</strong><br>&lsquo;Shrunk&rsquo; का अर्थ है छोटा होना या किसी चीज़ को छोटा करना। दिए गए passage में कहा गया है कि पिछली कुछ सदी में दुनिया में बंगाल टाइगर की संख्या 100,000 से घटकर 4,000 बची है ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :-</strong> <br>The ___(96)___ of Bengal tigers left in the world has___(97)___ from 100,000 to 4,000 over the last century.The main threats are ___(98)___ of habitat, poaching and the trade in tiger parts for Eastern medicines. Most Bengal tigers live in protected areas of India. Anti-poaching task-forces have been ____(99)___ up and there is also a trade ____(100)__ on tiger products in many countries, as a measure to save this rare species.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test :-</strong> <br>The ___(96)___ of Bengal tigers left in the world has___(97)___ from 100,000 to 4,000 over the last century.The main threats are ___(98)___ of habitat, poaching and the trade in tiger parts for Eastern medicines. Most Bengal tigers live in protected areas of India. Anti-poaching task-forces have been ____(99)___ up and there is also a trade ____(100)__ on tiger products in many countries, as a measure to save this rare species.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: [
                        "<p>prevention</p>",
                        "<p>encroaching</p>",
                        "<p>condition</p>",
                        "<p>shift</p>"
                    ],
                    options_hi: [
                        "<p>prevention</p>",
                        "<p>encroaching</p>",
                        "<p>condition</p>",
                        "<p>shift</p>"
                    ],
                    solution_en: "<p>98.(d) shift.<br>&lsquo;Shift&rsquo; means to move or be moved from one position or place to another. The given passage states that the main threats are shift of habitat, poaching, and the trade in tiger parts.</p>",
                    solution_hi: "<p>98.(d) <strong>shift.</strong> <br>&lsquo;Shift&rsquo; का अर्थ है एक स्थिति या स्थान से दूसरे स्थान पर जाना या ले जाना। दिए गए passage में कहा गया है कि मुख्य खतरे आवास में बदलाव, अवैध शिकार और बाघ के अंगों के व्यापार हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99<strong>. Cloze Test :-</strong> <br>The ___(96)___ of Bengal tigers left in the world has___(97)___ from 100,000 to 4,000 over the last century.The main threats are ___(98)___ of habitat, poaching and the trade in tiger parts for Eastern medicines. Most Bengal tigers live in protected areas of India. Anti-poaching task-forces have been ____(99)___ up and there is also a trade ____(100)__ on tiger products in many countries, as a measure to save this rare species.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test :-</strong> <br>The ___(96)___ of Bengal tigers left in the world has___(97)___ from 100,000 to 4,000 over the last century.The main threats are ___(98)___ of habitat, poaching and the trade in tiger parts for Eastern medicines. Most Bengal tigers live in protected areas of India. Anti-poaching task-forces have been ____(99)___ up and there is also a trade ____(100)__ on tiger products in many countries, as a measure to save this rare species.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: [
                        "<p>set</p>",
                        "<p>brought</p>",
                        "<p>swept</p>",
                        "<p>deployed</p>"
                    ],
                    options_hi: [
                        "<p>set</p>",
                        "<p>brought</p>",
                        "<p>swept</p>",
                        "<p>deployed</p>"
                    ],
                    solution_en: "<p>99.(d) <strong>Deployed.</strong> <br>&lsquo;Deployed&rsquo; means to put soldiers or weapons in a position where they are ready to fight. The given passage states that anti-poaching task-forces have been deployed up.</p>",
                    solution_hi: "<p>99.(d) <strong>Deployed.</strong> <br>&lsquo;Deployed&rsquo; का अर्थ है सैनिकों या हथियारों को ऐसी स्थिति में रखना जहां वे लड़ने के लिए तैयार हों। दिए गए passage में कहा गया है कि अवैध शिकार विरोधी कार्य-बल (anti-poaching task-force ) तैनात किए गए हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :-</strong> <br>The ___(96)___ of Bengal tigers left in the world has___(97)___ from 100,000 to 4,000 over the last century.The main threats are ___(98)___ of habitat, poaching and the trade in tiger parts for Eastern medicines. Most Bengal tigers live in protected areas of India. Anti-poaching task-forces have been ____(99)___ up and there is also a trade ____(100)__ on tiger products in many countries, as a measure to save this rare species.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test :-</strong> <br>The ___(96)___ of Bengal tigers left in the world has___(97)___ from 100,000 to 4,000 over the last century.The main threats are ___(98)___ of habitat, poaching and the trade in tiger parts for Eastern medicines. Most Bengal tigers live in protected areas of India. Anti-poaching task-forces have been ____(99)___ up and there is also a trade ____(100)__ on tiger products in many countries, as a measure to save this rare species.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: [
                        "<p>agreement</p>",
                        "<p>contract</p>",
                        "<p>ban</p>",
                        "<p>link</p>"
                    ],
                    options_hi: [
                        "<p>agreement</p>",
                        "<p>contract</p>",
                        "<p>ban</p>",
                        "<p>link</p>"
                    ],
                    solution_en: "<p>100.(c)<strong> ban.</strong> <br>&lsquo;Ban&rsquo; means to officially say that something is not allowed. The given passage states that there is also a trade ban on tiger products in many countries. Hence, &lsquo;ban&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) <strong>ban.</strong><br>&lsquo;Ban&rsquo; का अर्थ आधिकारिक रूप से यह कहना है कि किसी चीज़ की अनुमति नहीं है। दिए गए passage में कहा गया है कि कई देशों में बाघ उत्पादों के व्यापार पर भी प्रतिबंध है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>