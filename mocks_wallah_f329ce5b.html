<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">17:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 17</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">17</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 17 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 15
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 16,
                end: 16
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Eight persons can finish a work in 20 days. After 5 days they were requested to complete the work in the next 8 days. How many more persons should join the group to fulfill the requirement?</p>",
                    question_hi: "<p>1. आठ व्यक्ति 20 दिनों में एक काम पूरा कर सकते हैं। 5 दिनों काम करने के बाद उन्हें अगले 8 दिनों में काम पूरा करने का अनुरोध किया गया। आवश्यकता को पूरा करने के लिए कितने और व्यक्तियों को समूह में शामिल करना चाहिए?</p>",
                    options_en: ["<p>23</p>", "<p>15</p>", 
                                "<p>7</p>", "<p>12</p>"],
                    options_hi: ["<p>23</p>", "<p>15</p>",
                                "<p>7</p>", "<p>12</p>"],
                    solution_en: "<p>1.(c) <br>Total work = <math display=\"inline\"><mn>8</mn><mo>&#215;</mo><mn>20</mn></math>= 160 units<br>Work done by 8 workers in 5 days = 8 <math display=\"inline\"><mo>&#215;</mo><mi>&#160;</mi></math>5 = 40 units<br>Remaining work = 120 units<br>Let x more workers be employed to complete remaining work in the next 8 days.<br>120 = (8 + x)<math display=\"inline\"><mi>&#160;</mi><mo>&#215;</mo></math> 8 &rArr; x = 7</p>",
                    solution_hi: "<p>1.(c)<br>कुल कार्य 8 &times; 20 = <math display=\"inline\"><mn>8</mn><mo>&#215;</mo><mn>20</mn></math>= 160 इकाई <br>8 श्रमिकों द्वारा 5 दिनों में किया गया कार्य = 8 &times; 5 = 40 इकाई&nbsp;<br>शेष कार्य = 120 इकाई <br>माना शेष कार्य को अगले 8 दिनों में पूरा करने के लिए x और श्रमिकों को लगाया जाता है तो ,&nbsp;<br>120 = (8 + x) &times; 8 &rArr; x = 7</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A, B and C can individually complete a piece of work in 24 days, 15 days and 12 days respectively. B and C started the work and worked for 3 days and left. The number of days required by A alone to complete the remaining work is:</p>",
                    question_hi: "<p>2. A, B और C अकेले-अकेले किसी कार्य को क्रमशः 24, 15 और 12 दिनों में पूरा कर सकते हैं | B और C ने कार्य करना शुरू किया तथा 3 दिनों तक कार्य करके छोड़ दिया| शेष कार्य अकेले पूरा करने में A को कितने दिन लगेंगे ?</p>",
                    options_en: ["<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>18</p>", 
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>11</p>"],
                    options_hi: ["<p>15<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>18</p>",
                                "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>11</p>"],
                    solution_en: "<p>2.(c) A can complete the work in 24 days , B can complete the work in 15 days ,C can complete the work in 12 days<br><strong id=\"docs-internal-guid-f2a83055-7fff-5834-e4fe-fdb3380f404c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXegDo5jpbo0S76hvGeDkMqUuayHVfJEXlnhiDjP_9binlJSlQUo5mpxvUqrNvvlmgd_vMK-nFnzSQSya63ZTufuhPyOtYCEO3DbuJckQ7LJXc13RHQl_0L8rfssujjjxJYuAHw8eQ?key=TEZl7dUbZT0SELazDwt-8DJg\" width=\"178\" height=\"80\"></strong><br>Here, total work done be lcm(24,15,12) = 120 units<br>Thus, efficiency of A,B and C is <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math>= 5, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>120</mn><mn>15</mn></mfrac></math>= 8,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>12</mn></mfrac></math>= 10 respectively.<br>Work done by B and C in 3 days is (18 &times; 3) = 54 units<br>Work remaining = 120 - 54= 66 units<br>A will do 66 units work in <math display=\"inline\"><mfrac><mrow><mn>66</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>days = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>13</mn><mfrac><mn>1</mn><mn>5</mn></mfrac></math>days</p>",
                    solution_hi: "<p>2.(c) <br>A 24 दिनों में काम पूरा कर सकता है <br>B उस काम को 15 दिनों में पूरा कर सकता है<br>C उस काम को 12 दिनों में पूरा कर सकता है<br><strong id=\"docs-internal-guid-f2a83055-7fff-5834-e4fe-fdb3380f404c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXegDo5jpbo0S76hvGeDkMqUuayHVfJEXlnhiDjP_9binlJSlQUo5mpxvUqrNvvlmgd_vMK-nFnzSQSya63ZTufuhPyOtYCEO3DbuJckQ7LJXc13RHQl_0L8rfssujjjxJYuAHw8eQ?key=TEZl7dUbZT0SELazDwt-8DJg\" width=\"178\" height=\"80\"></strong><br>यहाँ, किया गया कुल कार्य = (24,15,12) का LCM = 120 इकाई <br>इस प्रकार, A, B और C की दक्षता क्रमशः <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math>= 5,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>15</mn></mfrac></math>= 8,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>12</mn></mfrac></math>= 10 है।<br>B और C द्वारा 3 दिनों में किया गया कार्य = (18 &times; 3) = 54 इकाई <br>शेष कार्य = 120 - 54 = 66 इकाई <br>A द्वारा 66 इकाई काम को करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>66</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>दिन = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>13</mn><mfrac><mn>1</mn><mn>5</mn></mfrac></math>दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A can complete a certain piece of work in 40 days. B is 25% more efficient than A and C is 28% more efficient than B. They work together for 5 days. The remaining work will be completed by B alone, in:</p>",
                    question_hi: "<p>3. A किसी निश्चित कार्य को 40 दिनों में पूरा कर सकता है | B, A की तुलना में 25% अधिक कार्य कुशल है तथा C, B की तुलना में 28% अधिक कार्य कुशल है | वे 5 दिनों तक एक साथ कार्य करते हैं | शेष कार्य अकेले पूरा करने में B को कितने दिन लगेंगे ?</p>",
                    options_en: ["<p>20<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>days</p>", "<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>days</p>", 
                                "<p>16<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>days</p>", "<p>20<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>days</p>"],
                    options_hi: ["<p>20<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p>16<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>20<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>3.(c) A can complete the work in 40 days.<br>We know that,<br>efficiency &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>k</mi><mi>e</mi><mi>n</mi></mrow></mfrac></math><br>It is given that B is 25% more efficient than A and C is 28% more efficient than B<br>So, their efficiency can be stated as follows:<br>A:B:C = 4 : 5 : 6.4<br>Total work done by A in 40 days= 4 &times; 40 = 160 units<br>Total work done in 5 days = 5 &times; (15.4) = 77 units<br>Remaining work = 83 units<br>B can complete remaining work in <math display=\"inline\"><mfrac><mrow><mn>83</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>3</mn><mn>5</mn></mfrac></math>days.</p>",
                    solution_hi: "<p>3.(c) <br>A , 40 दिनों में एक काम पूरा कर सकता है।<br>हम जानते हैं की,<br>दक्षता समय के साथ व्युत्क्रमानुपातिक होता है |<br>यह दिया गया है कि B, A से 25% अधिक कुशल है और C, B से 28% अधिक कुशल है<br>अतः उनकी दक्षता को इस प्रकार कहा जा सकता है : - A : B : C = 4 : 5 : 6.4<br>A द्वारा 40 दिनों में किया गया कुल कार्य = 4 &times; 40 = 160 यूनिट<br>5 दिनों में किया गया कुल कार्य = 5(15.4) = 77 इकाई <br>शेष कार्य = 83 इकाई <br>शेष कार्य को करने मे B द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>83</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>3</mn><mn>5</mn></mfrac></math>दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><strong id=\"docs-internal-guid-c11d7746-7fff-5009-59ed-02ecf533c811\">4.</strong> &nbsp;A can finish work in 20 days and B can finish the same work in 25 days. They began working together, but B left the work after 5 days. How many more days will A take to finish the remaining work?</p>",
                    question_hi: "<p>4. A 20 दिनों में कार्य समाप्त कर सकता है तथा B इसी कार्य को 25 दिनों में पूरा कर सकता है | उन्होंने एक साथ कार्य करना शुरू किया, लेकिन B ने 5 दिनों के बाद कार्य छोड़ दिया | शेष कार्य पूरा करने में A को कितने अतिरिक्त दिन लगेंगे ?</p>",
                    options_en: ["<p>8</p>", "<p>21</p>", 
                                "<p>16</p>", "<p>11</p>"],
                    options_hi: ["<p>8</p>", "<p>21</p>",
                                "<p>16</p>", "<p>11</p>"],
                    solution_en: "<p>4.(d) A can finish the work in 20 days<br>B can finish the work in 25 days<br>Total work = LCM(20,25) = 100 units<br>Efficiency of A = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>= 5unit/day<br>Efficiency of B = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>= 4unit/day<br>Work done by A and B together in 5 days = (5 + 4) &times; 5 = 45 units<br>Remaining work = 100 - 45 = 55 units<br>A complete remaining work in <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>= 11 days.</p>",
                    solution_hi: "<p>4.(d) A कार्य को 20 दिनों में समाप्त कर सकता है<br>B उस कार्य को 25 दिनों में समाप्त कर सकता है<br>कुल कार्य = LCM(20,25) = 100 इकाई <br>A की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>20</mn></mrow></mfrac><mi>&#160;</mi></math>= 5 इकाई <br>B की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = 4 इकाई <br>A और B द्वारा 5 दिनों में किया गया कार्य = (5 + 4) &times; 5 = 45 इकाई&nbsp;<br>शेष कार्य = 100 - 45 = 55 इकाई <br>शेष कार्य को करने मे A द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 11 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A can complete a certain work in 30 days. B is 25% more efficient than A and C is 20% more efficient than B. They all worked together for 3 days. B alone will complete the remaining work in:</p>",
                    question_hi: "<p>5. A किसी निश्चित कार्य को 30 दिनों में पूरा कर सकता है | B, A की तुलना में 25% अधिक कार्य कुशल है तथा C, B की तुलना में 20% अधिक कार्य कुशल है | उन सभी ने एक साथ तीन दिनों तक कार्य किया | शेष कार्य पूरा करने में B को अकेले कितने दिन लगेंगे ?</p>",
                    options_en: ["<p>15 days</p>", "<p>12 days</p>", 
                                "<p>20 days</p>", "<p>18 days</p>"],
                    options_hi: ["<p>15</p>", "<p>12</p>",
                                "<p>20</p>", "<p>18</p>"],
                    solution_en: "<p>5.(a) A can complete work in 30 days.<br>Efficiency of A:B = 4:5<br>Efficiency of B:C = 5:6<br>Efficiency A:B:C= 4:5:6<br>Total work done by A in 30 days= 4 &times; 30 = 120 units<br>In 3 days, they will together complete (3[4 + 5 + 6])= 45 units of work&nbsp;<br>Remaining work = 75 units.<br>B complete remaining work in <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 15 days.</p>",
                    solution_hi: "<p>5.(a) A 30 दिनों में काम पूरा कर सकता है।<br>A और B की क्षमता का अनुपात = 4 : 5<br>B और C की क्षमता का अनुपात = 5 : 6<br>A : B : C = 4 : 5 : 6<br>A द्वारा 30 दिनों में किया गया कुल कार्य= 4 &times; 30 = 120 इकाई&nbsp;<br>3 दिनों में, एक साथ पूरा किया गया काम = (3[4 + 5 + 6]) = 45 इकाई <br>शेष कार्य = 75 इकाई <br>शेष कार्य को करने मे B द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 15 दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A and B, working together can complete a work in d days. Working alone, A takes (8 + d) days and B takes (18 + d) days to complete the same work. A works for 4 days. The remaining work is completed by B alone, in:</p>",
                    question_hi: "<p>6. एक साथ कार्य करते हुए A और B किसी कार्य को d दिनों में पूरा कर सकते हैं | अकेले कार्य करते हुए इसी कार्य को पूरा करने में A को ( 8 + d ) तथा B को ( 18 + d ) दिन लगते हैं | A 4 दिनों तक कार्य करता है | शेष कार्य B अकेले कितने दिनों में पूरा करेगा ?</p>",
                    options_en: ["<p>24days</p>", "<p>16days</p>", 
                                "<p>18days</p>", "<p>20days</p>"],
                    options_hi: ["<p>24</p>", "<p>16</p>",
                                "<p>18</p>", "<p>20</p>"],
                    solution_en: "<p>6.(a) <br>Working together A and B takes &lsquo;d&rsquo; days.<br>Working alone, A takes &lsquo;8 + d&rsquo; days<br>And B takes &lsquo;18 + d&rsquo; days.<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>d</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>8</mn><mo>+</mo><mi>d</mi></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><mn>18</mn><mo>+</mo><mi>d</mi></mrow></mfrac></math><br>d = 12 days.<br>Now, working alone A takes &lsquo;20 days&rsquo; and B takes &lsquo;30 days&rsquo;.<br>Total work = lcm(20,30) = 60 units<br>Efficiency of A = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 3 <br>Efficiency of B = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = 2<br>In 4 days, A does 12 units of work<br>B complete work in <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>-</mo><mn>12</mn></mrow><mn>2</mn></mfrac></math>= 24 days.</p>",
                    solution_hi: "<p>6. (a) A और B को एक साथ कार्य करने में \'d\' दिन लगते हैं।<br>अकेले कार्य करते हुए, A को \'8 + d\' दिन लगते हैं और B को \'18 + d\' दिन लगते हैं।<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>d</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>8</mn><mo>+</mo><mi>d</mi></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mrow><mn>18</mn><mo>+</mo><mi>d</mi></mrow></mfrac></math><br>d = 12 दिन।<br>अब, अकेले काम करने में A को \'20 दिन\' लगते हैं और B को \'30 दिन\' लगते हैं।<br>कुल कार्य = lcm(20,30) = 60 इकाई <br>A की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>20</mn></mrow></mfrac><mi>&#160;</mi></math>= 3 <br>B की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>30</mn></mrow></mfrac><mi>&#160;</mi></math>= 2<br>4 दिनों में, A 12 यूनिट कार्य करता है<br>शेष कार्य को करने मे B द्वारा लिया गया समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>-</mo><mn>12</mn></mrow><mn>2</mn></mfrac></math> = 24 दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Four men and 6 women can complete a certain piece of work in 5 days whereas three men and 4 women can complete it in 7 days. How many should assist 25 women to complete 2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>times the same work in 5 days?</p>",
                    question_hi: "<p>7. 4 पुरुष और 6 महिलाएं एक निश्चित काम को 5 दिनों में पूरा कर सकते हैं, जबकि तीन पुरुष और 4 महिलाएं इसे 7 दिनों में पूरा कर सकती हैं। समान कार्य के 2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>गुना कार्य को 5 दिनों में पूरा करने के लिए कितनी और पुरुष को 25 महिलाओं की सहायता करनी होगी?</p>",
                    options_en: ["<p>8</p>", "<p>10</p>", 
                                "<p>4</p>", "<p>5</p>"],
                    options_hi: ["<p>8</p>", "<p>10</p>",
                                "<p>4</p>", "<p>5</p>"],
                    solution_en: "<p>7.(d) <br>Let efficiency of 1men = m units and efficiency of 1 woman = w units<br>According to question:<br>5(4m + 6w) = 7( 3m + 4w )<br><math display=\"inline\"><mo>&#8658;</mo></math>Efficiency of 1 men = 2women<br>Total work = 5[4(2w) + 6w] = 70w units.<br>Let x men assist 25 women to complete <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>times 70w units of work in 5 days.<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math>&times; 70W = 5[25w + x(2w)]<br><math display=\"inline\"><mo>&#8658;</mo></math> 175w = 125w+ 10wx<br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi></math>50 = 10x &rArr; x = 5</p>",
                    solution_hi: "<p>7.(d) माना 1 पुरुष की क्षमता = m इकाई और 1 महिला की दक्षता = w इकाई <br>प्रश्न के अनुसार:<br>5(4m + 6w) = 7(3m + 4w)<br>1 पुरुषों की क्षमता = 2 महिलाओं की क्षमता <br>कुल कार्य = 5[4(2w) + 6w] = 70w इकाइयाँ।<br>माना x पुरुष 25 महिलाओं को 5 दिनों में 70w इकाइयों का <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> गुना काम पूरा करने में सहायता करते हैं। <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math>&times; 70W = 5[25w + x(2w)]<br><math display=\"inline\"><mo>&#8658;</mo></math> 175w = 125w+ 10wx &rArr; 50 = 10x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. To complete a certain task, X is 40% more efficient than Y, and Z is 40% less efficient than Y. Working together, they can complete the task in 21 days. Y and Z worked together for 35 days. The remaining work will be completed by A alone in:</p>",
                    question_hi: "<p>8. किसी निश्चित कार्य को पूरा करने के लिए, X, Y की तुलना में 40% अधिक कार्य कुशल है तथा Z, Y से 40% कम कार्य कुशल है| एक साथ कार्य करते हुए, वे इस कार्य को 21 दिनों में पूरा कर सकते हैं | Y तथा Z ने 35 दिनों तक एक साथ कार्य किया | शेष कार्य A अकेला कितने दिनों में पूरा करेगा ?</p>",
                    options_en: ["<p>8 days</p>", "<p>6 days</p>", 
                                "<p>5 days</p>", "<p>4 days</p>"],
                    options_hi: ["<p>8</p>", "<p>6</p>",
                                "<p>5</p>", "<p>4</p>"],
                    solution_en: "<p>8.(c) According to question, X:Y:Z = 7:5:3 = 15 units<br>Total work = 21<math display=\"inline\"><mo>&#215;</mo></math>(15) = 315 units<br>In 35 days, Y and Z completed = 35<math display=\"inline\"><mo>&#215;</mo></math>8 <br>= 280 units<br>Remaining work = 35 units<br>X can complete 35 units work in <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mi>&#160;</mi></math><br>= 5 days.</p>",
                    solution_hi: "<p>8.(c) प्रश्न के अनुसार, <br>X:Y:Z = 7:5:3 = 15 इकाई <br>कुल कार्य = 21 &times; (15) = 315 इकाई <br>35 दिनों में, Y और Z ने पूरा किया= 35 &times; 8 = 280 इकाइयाँ<br>शेष कार्य = 35 इकाई <br>शेष कार्य को करने मे X द्वारा लिया गया समय =<math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>= 5 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Sixteen men can finish a work in 8 days. Eight men and nine women working together can finish the same work in 10 days. In how many days will twenty women finish the same work?</p>",
                    question_hi: "<p>9. सोलह पुरुष किसी कार्य को 8 दिनों में पूरा कर सकते हैं | आठ पुरुष तथा नौ महिलाएं एक साथ कार्य करके इसी कार्य को 10 दिनों में पूरा कर सकती हैं | इस कार्य को 20 महिलाएं कितने दिनों में पूरा करेंगी ?</p>",
                    options_en: ["<p>13</p>", "<p>12</p>", 
                                "<p>9</p>", "<p>11</p>"],
                    options_hi: ["<p>13</p>", "<p>12</p>",
                                "<p>9</p>", "<p>11</p>"],
                    solution_en: "<p>9.(b)Let efficiency of a man = &lsquo;m&rsquo; and efficiency of a woman = &lsquo;w&rsquo; <br>Sixteen men can finish the work in 8 days, thus, total work = 16 &times; 8 &times; m<br>8 men and 9 women can finish work in 10 days, implies, total work = <br>10 &times; (8 &times; m+ 9 &times; w)<br><math display=\"inline\"><mo>&#8658;</mo></math>16 &times; 8 &times; m = 10 &times; (8 &times; m + 9 &times; w)<br><math display=\"inline\"><mo>&#8658;</mo></math> 48 &times; m = 90 &times; w<br><math display=\"inline\"><mo>&#8658;</mo></math> m:w = 15:8<br>20 women finish the work in = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>16</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>20</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math>= 12 days.</p>",
                    solution_hi: "<p>9.(b) माना , पुरुष की क्षमता = \'m\' और एक महिला की क्षमता = \'w\'<br>16 आदमी 8 दिनों में काम खत्म कर सकते हैं, इस प्रकार, कुल काम&nbsp;= 16 &times; 8 &times; m<br>8 पुरुष और 9 महिलाएं 10 दिनों में काम खत्म कर सकते हैं, जिसका मतलब है कि कुल काम = 10 &times; (8 &times;m + 9&times;w)<br><math display=\"inline\"><mo>&#8658;</mo></math>16 &times; 8 &times; m = 10 &times; (8 &times; m + 9 &times; w)<br><math display=\"inline\"><mo>&#8658;</mo></math> 48 &times; m = 90&times;w<br><math display=\"inline\"><mo>&#8658;</mo></math> m : w = 15 : 8<br>20 महिलाएं = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>16</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>20</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math>= 12 दिनों में कार्य पूरा करती हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A, B and C can individually complete a task in 24 days, 20 days and 18 days respectively. B and C start the task, and they work for 6 days and leave. The number of days required by A alone to finish the remaining task is:</p>",
                    question_hi: "<p>10. A, B और C अकेले कार्य करते हुए किसी कार्य को क्रमशः 24 दिन, 20 दिन तथा 18 दिनों में पूरा कर सकते हैं | B और C ने कार्य शुरू किया और उन्होंने 6 दिन कार्य करके छोड़ दिया | शेष कार्य अकेले पूरा करने में A को कितने दिन लगेंगे</p>",
                    options_en: ["<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>days</p>", "<p>15<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>days</p>", 
                                "<p>10 days</p>", "<p>8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>days</p>"],
                    options_hi: ["<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>15<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>10</p>", "<p>8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>10.(d) A, B and C can individually complete a task in 24 days, 20 days and 18 days respectively<br>Total work = lcm(24,20,18) = 360 units<br>Efficiency of A,B and C = <math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>24</mn></mrow></mfrac><mi>&#160;</mi></math>= 15units/day, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mrow><mn>20</mn><mo>&#160;</mo></mrow></mfrac></math>= 18units/day, <math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 20units/day respectively.<br>Work done by B and C in 6 days = 6 &times; (38) = 228 units<br>Remaining work = 360 - 228 = 132 units.<br>A can complete remaining work in <math display=\"inline\"><mfrac><mrow><mn>132</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>days</p>",
                    solution_hi: "<p>10.(d) A, B और C व्यक्तिगत रूप से एक कार्य को क्रमशः 24 दिनों, 20 दिनों और 18 दिनों में पूरा कर सकते हैं<br>कुल कार्य = lcm(24,20,18) = 360 इकाई <br>A,B और C की क्षमता क्रमशः = <math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math>=15, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mrow><mn>20</mn><mo>&#160;</mo></mrow></mfrac></math>= 18,&nbsp;<math display=\"inline\"><mfrac><mrow><mn>360</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math>= 20&nbsp;<br>B और C द्वारा 6 दिनों में किया गया कार्य = 6 &times; (38) = 228 इकाई&nbsp;<br>शेष कार्य = 360 - 228 = 132 इकाई ।<br>A शेष कार्य को <math display=\"inline\"><mfrac><mrow><mn>132</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math>= 8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>दिनों में पूरा कर सकता है |</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Amit and Sunil together can complete a work in 9 days, Sunil and Dinesh together can complete the same work in 12days, and Amit and Dinesh together can complete the same work in 18 days. In how many days will they complete the work if Amit, Sunil and Dinesh work together?</p>",
                    question_hi: "<p>11. अमित और सुनील एक साथ किसी कार्य को 9 दिनों में पूरा कर सकते हैं | सुनील तथा दिनेश एक साथ इस कार्य को 12 दिनों में पूरा कर सकते हैं तथा अमित और दिनेश इस कार्य को एक साथ 18 दिनों में पूरा कर सकते हैं | यदि अमित, सुनील तथा दिनेश एक साथ कार्य करें, तो वे इस कार्य को कितने दिनों में पूरा करेंगे ?</p>",
                    options_en: ["<p>14 days</p>", "<p>16 days</p>", 
                                "<p>12 days</p>", "<p>8 days</p>"],
                    options_hi: ["<p>14</p>", "<p>16</p>",
                                "<p>12</p>", "<p>8</p>"],
                    solution_en: "<p>11.(d) <br>Time taken by Amit+Sunil = 9 days<br>Time taken by Sunil+Dinesh = 12 days<br>Time taken by Dinesh+Amit = 18 days<br>Total work = lcm (9,12,18) = 36 units</p>\n<p>efficiency (Amit+Sunil) = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 4 unit</p>\n<p>efficiency (Sunil+Dinesh) =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 3 unit</p>\n<p>efficiency (Amit+Dinesh) =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 2 unit <br>Amit&rsquo;s efficiency = 1.5 unit/day<br>Sunil&rsquo;s efficiency = 2.5 unit/day<br>Dinesh&rsquo;s efficiency = 0.5 unit/day<br>Together they complete work in <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 8 days</p>",
                    solution_hi: "<p>11.(d) अमित और सुनील द्वारा लिया गया समय = 9 दिन<br>सुनील और दिनेश द्वारा लिया गया समय = 12 <br>दिनेश और अमित द्वारा लिया गया समय = 18 दिन<br>कुल कार्य = lcm (9,12,18) = 36 इकाई <br>अमित और सुनील की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 4 इकाई</p>\n<p>सुनील और दिनेश की दक्षता =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 3 इकाई</p>\n<p>अमित और दिनेश की दक्षता =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 2 इकाई <br>अमित की दक्षता = 1.5 इकाई /दिन<br>सुनील की दक्षता = 2.5 इकाई /दिन<br>दिनेश की दक्षता = 0.5 इकाई /दिन<br>वे मिलकर काम को <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math>= 8 दिनों में पूरा करते हैं|</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. A, B and C can individually complete a task in 20 days, 16 days and 30 days, respectively. If A and B started working on the task, and they worked for 4 days and left, then the number of days required by C to finish the remaining tasks is:</p>",
                    question_hi: "<p>12. A, B तथा C अकेले कार्य करते हुए किसी कार्य को क्रमशः 20, 16 तथा 30 दिनों में पूरा कर सकते हैं | यदि A और B ने यह कार्य शुरू किया तथा उन्होंने 4 दिन कार्य करके छोड़ दिया, तो शेष कार्य पूरा करने में C को कितने दिन लगेंगे ?</p>",
                    options_en: ["<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>days</p>", "<p>13 days</p>", 
                                "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>days</p>", "<p>10days</p>"],
                    options_hi: ["<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>13</p>",
                                "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>10</p>"],
                    solution_en: "<p>12.(a) <br>A alone can complete work in = 20 days<br>B alone can complete work in = 16 days<br>C alone can complete work in = 30 days<br>Total work = lcm(20,16,30) = 240 units <br>Individual efficiency of A, B and C = <math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> =12 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mn>16</mn><mo>&#160;</mo></mrow></mfrac></math>=15 and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>30</mn></mfrac></math>= 8<br>respectively.<br>Work done by A and B in 4 days = 27 &times; 4 = 108 units<br>Remaining work = 132 units<br>Days required by C to finish remaining work = <math display=\"inline\"><mfrac><mrow><mn>132</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math>days</p>",
                    solution_hi: "<p>12.(a) A द्वारा अकेले कार्य को पूरा करने में लगा समय = 20 दिन<br>B द्वारा अकेले कार्य को पूरा करने में लगा समय = 16 दिन<br>C द्वारा अकेले कार्य को पूरा करने में लगा समय = 30 दिन <br>कुल कार्य = Lcm (20,16,30) = 240 इकाइयाँ<br>A, B और C की व्यक्तिगत दक्षता क्रमशः =<math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>=12 ,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mn>16</mn><mo>&#160;</mo></mrow></mfrac></math>= 15 और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>30</mn></mfrac></math>= 8 है |<br>A और B द्वारा 4 दिनों में किया गया कार्य = 27 &times; 4 = 108 इकाई&nbsp;<br>शेष कार्य = 132 इकाई <br>शेष कार्य को पूरा करने के लिए C द्वारा आवश्यक दिन = <math display=\"inline\"><mfrac><mrow><mn>132</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math>दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Ten men or twelve women can finish the same work in 10 days. If 5 men and 2 women undertake the work together; how many days will they take to complete the work?</p>",
                    question_hi: "<p>13. दस पुरुष या बारह महिलाएं एक कार्य को 10 दिनों में पूरा कर सकती हैं | यदि 5 पुरुष तथा 2 महिलाओं ने उस कार्य को शुरू किया, तो कार्य पूरा करने में उन्हें कितने दिन लगेंगे ?</p>",
                    options_en: ["<p>40</p>", "<p>15</p>", 
                                "<p>60</p>", "<p>20</p>"],
                    options_hi: ["<p>40</p>", "<p>15</p>",
                                "<p>60</p>", "<p>20</p>"],
                    solution_en: "<p>13.(b) 10 men = 12 women<br><math display=\"inline\"><mo>&#8658;</mo></math> m:w = 6:5<br><math display=\"inline\"><mo>&#8658;</mo></math> Total work = work done by 10 men in 10 days = 10 &times; 6 &times; 10 = 600 units<br><math display=\"inline\"><mo>&#8658;</mo></math> Work done by 5 men and 2 women in 1 day = 5 &times; 6 + 2 &times; 5 = 40 units&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> time to complete 600 units work =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>40</mn></mfrac></math>days = 15 days</p>",
                    solution_hi: "<p>13.(b) 10 पुरुष = 12 महिलाएं<br>M: W = 6:5<br>कुल कार्य = 10 पुरुषों द्वारा 10 दिनों में किया गया कार्य = 10 &times; 6 &times; 10 = 600 इकाई <br>5 पुरुषों और 2 महिलाओं द्वारा 1 दिन में किया गया कार्य = 5 &times; 6 + 2 &times; 5 = 40 यूनिट<br>600 इकाइयों का कार्य पूरा करने का समय = <math display=\"inline\"><mfrac><mrow><mn>600</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> दिन = 15 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. If 18 men can cut a field in 35 days, then in how many days can 21 men cut the same field?</p>",
                    question_hi: "<p>14. यदि 18 पुरुष 35 दिनों में एक खेत को काट सकते हैं, तो 21 पुरुष यह खेत कितने दिनों में काट सकते हैं?</p>",
                    options_en: ["<p>27</p>", "<p>28</p>", 
                                "<p>30</p>", "<p>32</p>"],
                    options_hi: ["<p>27</p>", "<p>28</p>",
                                "<p>30</p>", "<p>32</p>"],
                    solution_en: "<p>14.(c) Short-trick:<br>M<sub>1</sub>D<sub>1</sub> = M<sub>2</sub>D<sub>2<br></sub><math display=\"inline\"><mo>&#8658;</mo></math>18 &times; 35 = 21 &times; D<sub>2</sub> &rArr; D<sub>2</sub> = 30 days</p>",
                    solution_hi: "<p>14.(c)&nbsp;M<sub>1</sub>D<sub>1</sub> = M<sub>2</sub>D<sub>2<br></sub><math display=\"inline\"><mo>&#8658;</mo></math>18 &times; 35 = 21 &times; D<sub>2</sub> &rArr; D<sub>2</sub> = 30 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. A can do a piece of work in 6 days. B can do it in 9 days. With the assistance of C they completed the work in 3 days. In how many days can C alone do the work?</p>",
                    question_hi: "<p>15. A 6 दिनों में एक काम कर सकता है। B इसे 9 दिनों में कर सकता हैं। C की सहायता से उन्होंने 3 दिनों में काम पूरा कर लिया। तो C अकेले कितने दिनों में यह काम करेगा ?</p>",
                    options_en: ["<p>16</p>", "<p>8</p>", 
                                "<p>18</p>", "<p>12</p>"],
                    options_hi: ["<p>16</p>", "<p>8</p>",
                                "<p>18</p>", "<p>12</p>"],
                    solution_en: "<p>15.(c) A can do work in 6 days, B can do the same work in 9 days and (A,B and C) together complete work in 3 days<br>Total work = lcm(6,9,3) = 18 units<br>A&rsquo;s efficiency = <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 3 unit/day<br>B&rsquo;s efficiency = <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 2 unit/day<br>(A + B + C)&rsquo;s efficiency = <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> =6 unit/day<br>C&rsquo;s efficiency = (6 - 3 - 2) = 1 unit/day<br>C alone can do work in <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math>= 18 days</p>",
                    solution_hi: "<p>15.(c) <br>A किसी काम को 6 दिनों में कर सकता है, B उसी काम को 9 दिनों में कर सकता है और (A,B और C) मिलकर उस काम को 3 दिनों में पूरा कर सकते हैं।<br>कुल कार्य = lcm(6,9,3) = 18 इकाइयाँ<br>A की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>= 3 इकाई <br>B की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 2 इकाई <br>(A + B + C) की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 6 इकाई /दिन<br>C की दक्षता = (6 - 3 - 2) = 1 इकाई /दिन<br>C द्वारा अकेले कुल कार्य को पूरा करने में लगा समय =<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>18</mn></mrow><mrow><mn>1</mn></mrow></mfrac><mi>&#160;</mi></math>= 18 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. Ram and Shyam can complete a task in 6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>days and 15 days respectively. They work together for 4 days and then Ram leaves. In how many days after Ram leaves, will Shyam complete the remaining task alone?</p>",
                    question_hi: "<p>16. राम तथा श्याम किसी कार्य को क्रमशः 6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिनों में तथा 15 दिनों में पूरा कर सकते हैं | उन्होंने 4 दिनों तक साथ कार्य किया तथा फिर राम चला गया | राम के जाने के कितने दिनों बाद श्याम अकेला इस कार्य को पूरा कर पायेगा ?</p>",
                    options_en: ["<p>2 days</p>", "<p>3 days</p>", 
                                "<p>4 days</p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>days</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>4</p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>16.(a) Ram can complete work in 6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>days = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>3</mn></mfrac></math>days<br>Shyam can complete work in 15 days<br>Total work = lcm(<math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>,15) = 60 units<br>Efficiency of Ram = 9 units/day<br>Efficiency of Shyam = 4 units/day<br>Work done in 4 days = 52 units <br>Shyam complete remaining work in <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 2 days</p>",
                    solution_hi: "<p>16.(a) राम द्वारा काम को पूरा करने में लिया गया समय = 6<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>दिन = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>3</mn></mfrac></math>दिन<br>श्याम द्वारा काम को पूरा करने में लिया गया समय = 15 दिन<br>कुल कार्य = lcm(<math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>,15) = 60 इकाइयाँ<br>राम की क्षमता = 9 इकाई /दिन<br>श्याम की क्षमता = 4 इकाई /दिन<br>4 दिनों में किया गया कार्य = 52 इकाई <br>श्याम द्वारा शेष कार्य को पूरा करने में लिया गया समय =<math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mi>&#160;</mi></math>= 2 दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "misc",
                    question_en: "<p>17. A, B and C can individually complete a task in 24 days, 16 days and 32 days respectively. If A and C start the work and worked for 6 days and left, then the number of days required by B to complete the remaining task is:</p>",
                    question_hi: "<p>17. A, B और C व्यक्तिगत रूप से किसी कार्य को क्रमशः 24, 16 और 32 दिनों में पूरा कर सकते हैं | यदि A और C ने कार्य शुरू किया तथा 6 दिनों तक कार्य करके छोड़ दिया, तो शेष कार्य पूरा करने में B को कितने दिन लगेंगे ?</p>",
                    options_en: ["<p>17<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><mi>&#160;</mi></mrow></mfrac></math></p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>12</p>", "<p>9</p>"],
                    options_hi: ["<p>17<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>9</p>"],
                    solution_en: "<p>17.(d) A can complete the task in 24 days<br>B can complete the task in 16 days<br>C can complete the task in 32 days<br>Total work = lcm(24,16,32) = 96 units <br>Efficiency of A = <math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math>= 4 units/day<br>Efficiency of B = <math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math>= 6 units/day<br>Efficiency of C = <math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math>= 3 units/day<br>Work done by A and C in 6 days = 42 units<br>Remaining work = 96 - 42 = 54 units<br>Time required by B to complete remaining work = <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 9 days</p>",
                    solution_hi: "<p>17.(d) A , 24 दिनों में कार्य पूरा कर सकता है<br>B उस कार्य को 16 दिनों में पूरा कर सकता है<br>C कार्य को 32 दिनों में पूरा कर सकता है<br>कुल कार्य = lcm (24,16,32) = 96 इकाई <br>A की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math>= 4 इकाई /दिन<br>B की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math>= 6 इकाई /दिन<br>C की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>96</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math>= 3 इकाई /दिन<br>और C द्वारा 6 दिनों में किया गया कार्य = 42 इकाई <br>शेष कार्य = 96 - 42 = 54 इकाई&nbsp;<br>शेष कार्य को पूरा करने के लिए B द्वारा आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>6</mn></mrow></mfrac><mi>&#160;</mi></math>= 9 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>