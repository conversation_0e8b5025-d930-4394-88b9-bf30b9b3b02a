<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A can complete a piece of work alone in 200 days, while B can complete the same piece of work alone in 100 days. In every three-day cycle, both A and B work on day 1, only A works on day 2, and only B works on day 3. This cycle continues till the work is completed. How many days in all does it take the duo to complete the work?</p>",
                    question_hi: "<p>1. A अकेले किसी कार्य को 200 दिनों में पूरा कर सकता है, जबकि B उसी कार्य को अकेले 100 दिनों में पूरा कर सकता है। प्रत्येक तीन-दिवसीय चक्र में पहले दिन A और B दोनों काम करते हैं, दूसरे दिन केवल A काम करता है और तीसरे दिन केवल B काम करता है। यह चक्र, कार्य पूरा होने तक जारी रहता है। दोनों मिलकर कार्य को पूरा करने में कितने दिन लेते हैं?</p>",
                    options_en: ["<p>100<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>99<math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p>100<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>100</p>"],
                    options_hi: ["<p>100<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>99<math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>100<math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>100</p>"],
                    solution_en: "<p>1.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363678031.png\" alt=\"rId5\" width=\"123\" height=\"166\"><br>Working pattern of A &amp; B is (A,B), (A), (B).............<br>Work done in 1 cycle(3 days) = (2+1)+1+2 = 6 unit<br>Work done in 33 cycle (99 days) = 6&times;33 = 198 unit<br>Remaining work = 200 - 198 = 2 unit which is completed by (A &amp; B) together in <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days.<br>So, the time taken to complete the whole work in 99<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days.</p>",
                    solution_hi: "<p>1.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363678143.png\" alt=\"rId6\" width=\"107\" height=\"157\"><br>A और B का कार्य पैटर्न (A,B), (A), (B).............है ।<br>1 चक्र(3 दिन) में किया गया कार्य = (2+1)+1+2 = 6 इकाई <br>33 चक्र (99 दिन) में किया गया कार्य = 6&times;33 = 198 इकाई <br>शेष कार्य = 200 - 198 = 2 इकाई जिसे (A और B) ने मिलकर <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिनों में पूरा किया। <br>अतः, पूरा कार्य करने मे लिया गया समय = 99<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A can do a work in 20 days and B in 30 days. If they work on it together for 2 days, then what is the fraction of the work that is left?</p>",
                    question_hi: "<p>2. A किसी काम को 20 दिन में और B उसी काम 30 दिन में कर सकता है। यदि वे 2 दिन तक मिलकर इस काम को करते हैं, तो जो कार्य शेष है उसका कितना भाग शेष है?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>"],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363678285.png\" alt=\"rId7\" width=\"190\" height=\"175\"><br>Work done by (A+B) in 2 days = (3+2)&times;2 =10 unit<br>Remaining work = 60 - 10 = 50 unit<br>Required fraction of work left = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363678461.png\" alt=\"rId8\" width=\"188\" height=\"184\"><br>(A+B) द्वारा 2 दिन में किया गया कार्य = (3+2)&times;2 =10 इकाई <br>शेष कार्य = 60 - 10 = 50 इकाई <br>शेष कार्य का भाग = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A and B can do a work in 13 days and 26 days, respectively. If they work for a day alternately, starting with A, then in how many days will the work be completed?</p>",
                    question_hi: "<p>3. A और B एक कार्य को क्रमश: 13 दिन और 26 दिन में पूरा कर सकते हैं। यदि A से प्रारंभ करते हुए वे एकांतर दिनों (बारी-बारी से एक दिन) में कार्य करते हैं, तो कार्य कितने दिनों में पूरा होगा?</p>",
                    options_en: ["<p>17</p>", "<p>16</p>", 
                                "<p>13</p>", "<p>14</p>"],
                    options_hi: ["<p>17</p>", "<p>16</p>",
                                "<p>13</p>", "<p>14</p>"],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363678663.png\" alt=\"rId9\" width=\"192\" height=\"171\"><br>Work done in 1 cycle(2 days) = 2+1 = 3 unit <br>Work done in 8 cycle(16 days) = 3&times;8 = 24 unit<br>Remaining work = 26 - 24 = 2 unit which is completed by A in <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 1 day<br>So, the total time taken to complete the whole work = 16+ 1 = 17 days</p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363678814.png\" alt=\"rId10\" width=\"161\" height=\"153\"><br>1 चक्र (2 दिन) में किया गया कार्य = 2+1 = 3 इकाई <br>8 चक्र (16 दिन) में किया गया कार्य = 3&times;8 = 24 इकाई <br>शेष कार्य = 26 - 24 = 2 इकाई जिसे A द्वारा <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 1 दिन में पूरा किया गया<br>अतः, पूरे कार्य को करने में लगा कुल समय = 16+ 1 = 17 दिन</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Arvind works 4 times as fast as Suresh. If Suresh can complete a work in 20 days independently, then find the number of days in which Arvind and Suresh can together finish the work.</p>",
                    question_hi: "<p>4. अरविंद, सुरेश से 4 गुना तेजी से काम करता है। यदि सुरेश स्वतंत्र रूप से किसी कार्य को 20 दिन में पूरा कर सकता है, तो अरविंद और सुरेश मिलकर उस कार्य को कितने दिन में पूरा कर सकते हैं?</p>",
                    options_en: ["<p>6</p>", "<p>3</p>", 
                                "<p>5</p>", "<p>4</p>"],
                    options_hi: ["<p>6</p>", "<p>3</p>",
                                "<p>5</p>", "<p>4</p>"],
                    solution_en: "<p>4.(d)<br>Efficiency of Arvind and Suresh = 4 : 1<br>Total work = 20 &times; 1 = 20 unit<br>So, the time taken by them to complete the whole work = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></math> = 4 days</p>",
                    solution_hi: "<p>4.(d)<br>अरविंद और सुरेश की दक्षता = 4 : 1<br>कुल कार्य = 20 &times; 1 = 20 इकाई<br>अतः, पूरा कार्य पूरा करने में उनके द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></math> = 4 दिन</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If 24 people can build 90 identical walls in 25 days, then how many more days will 27 people require to build 162 such walls?</p>",
                    question_hi: "<p>5. यदि 24 व्यक्ति 25 दिनों में 90 समान दीवारें बना सकते हैं, तो ऐसी 162 दीवारों को बनाने में 27 व्यक्तियों को और कितने दिन लगेंगे?</p>",
                    options_en: ["<p>16</p>", "<p>14</p>", 
                                "<p>15</p>", "<p>13</p>"],
                    options_hi: ["<p>16</p>", "<p>14</p>",
                                "<p>15</p>", "<p>13</p>"],
                    solution_en: "<p>5.(c)<br>Formula used : - <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>D</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>H</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>W</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>M</mi><mn>2</mn></msub><msub><mi>D</mi><mn>2</mn></msub><msub><mi>H</mi><mn>2</mn></msub></mrow><msub><mi>W</mi><mn>2</mn></msub></mfrac></math><br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mn>24</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>&#160;</mi><mn>25</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><mo>&#215;</mo><mi>x</mi></mrow><mn>162</mn></mfrac></math><br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>24</mn><mo>&#215;</mo><mn>25</mn><mo>&#215;</mo><mn>162</mn><mo>&#160;</mo></mrow><mrow><mn>27</mn><mo>&#215;</mo><mn>90</mn></mrow></mfrac></math>= 40 days<br>Required more days = 40 - 25 = 15 days</p>",
                    solution_hi: "<p>5.(c)<br>प्रयुक्त सूत्र:- <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>D</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>H</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>W</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>M</mi><mn>2</mn></msub><msub><mi>D</mi><mn>2</mn></msub><msub><mi>H</mi><mn>2</mn></msub></mrow><msub><mi>W</mi><mn>2</mn></msub></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>24</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>&#160;</mi><mn>25</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>27</mn><mo>&#215;</mo><mi>x</mi></mrow><mn>162</mn></mfrac></math><br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>24</mn><mo>&#215;</mo><mn>25</mn><mo>&#215;</mo><mn>162</mn><mo>&#160;</mo></mrow><mrow><mn>27</mn><mo>&#215;</mo><mn>90</mn></mrow></mfrac></math> = 40 दिन<br>अधिक दिनों की आवश्यकता = 40 - 25 = 15 दिन</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. 18 workers can complete a piece of work in 96 days. They start working together and after 26 days 10 more workers join them. In how many days in all will the work be completed?</p>",
                    question_hi: "<p>6. 18 कर्मचारी एक कार्य को 96 दिनों में पूरा कर सकते हैं। वे एक साथ काम करना शुरू करते हैं और 26 दिनों के बाद 10 और कर्मचारी उनके साथ जुड़ जाते हैं। कार्य को कुल मिलाकर कितने दिनों में पूरा किया जाएगा?</p>",
                    options_en: ["<p>69</p>", "<p>71</p>", 
                                "<p>72</p>", "<p>70</p>"],
                    options_hi: ["<p>69</p>", "<p>71</p>",
                                "<p>72</p>", "<p>70</p>"],
                    solution_en: "<p>6.(b)<br>Formula used : - <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>D</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>H</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>W</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>M</mi><mn>2</mn></msub><msub><mi>D</mi><mn>2</mn></msub><msub><mi>H</mi><mn>2</mn></msub></mrow><msub><mi>W</mi><mn>2</mn></msub></mfrac></math><br>According to the question,<br>18&nbsp; &times;&nbsp; 96 = 18 &times; 26 + (28 &times; x)<br>18&nbsp; &times;&nbsp; 96 - 18 &times; 26 = (28 &times; x)<br><math display=\"inline\"><mn>18</mn><mo>(</mo><mn>96</mn></math> - 26) = (28 &times; x)<br><math display=\"inline\"><mn>18</mn></math> &times; 70 = (28 &times; x)<br><math display=\"inline\"><mi>x</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>70</mn></mrow><mn>28</mn></mfrac></math> = 45 days<br>Time taken to complete the whole work = 45 + 26 = 71 days</p>",
                    solution_hi: "<p>6.(b)<br>प्रयुक्त सूत्र:-- <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>D</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>H</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>W</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>M</mi><mn>2</mn></msub><msub><mi>D</mi><mn>2</mn></msub><msub><mi>H</mi><mn>2</mn></msub></mrow><msub><mi>W</mi><mn>2</mn></msub></mfrac></math><br>प्रश्न के अनुसार,<br>18&nbsp; &times;&nbsp; 96 = 18 &times; 26 + (28 &times; x)<br>18&nbsp; &times;&nbsp; 96 - 18 &times; 26 = (28 &times; x)<br><math display=\"inline\"><mn>18</mn><mo>(</mo><mn>96</mn></math> - 26) = (28 &times; x)<br><math display=\"inline\"><mn>18</mn></math> &times; 70 = (28 &times; x)<br><math display=\"inline\"><mi>x</mi></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>70</mn></mrow><mn>28</mn></mfrac></math> = 45 दिन<br>संपूर्ण कार्य को पूरा करने में लगा समय = 45 + 26 = 71 दिन</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Mitu can do a piece of work in 12 hours, Titu and Situ together in 2 hours, and Mitu and Situ together in 3 hours. How long will Titu alone take to do it?</p>",
                    question_hi: "<p>7. मीतू एक काम को 12 घंटे में पूरा कर सकती है, इसी काम को टीटू और सीटू एक साथ मिलकर 2 घंटे में कर सकते हैं, तथा मीतू और सीटू एक साथ मिलकर 3 घंटे में कर सकते हैं। टीटू को अकेले इस काम को पूरा करने में कितना समय लगेगा?</p>",
                    options_en: ["<p>7 hours</p>", "<p>8 hours</p>", 
                                "<p>4 hours</p>", "<p>6 hours</p>"],
                    options_hi: ["<p>7 घंटे</p>", "<p>8 घंटे</p>",
                                "<p>4 घंटे</p>", "<p>6 घंटे</p>"],
                    solution_en: "<p>7.(c)<br><img src=\"data:image/png;base64,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\" width=\"256\" height=\"143\"><br>Efficiency of Situ = 4 - 1 = 3 unit<br>Efficiency of Titu = 6 - 3 = 3 unit<br>Time taken by Titu to complete the work = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 4 hours</p>",
                    solution_hi: "<p>7.(c)<br><img src=\"data:image/png;base64,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\" width=\"321\" height=\"182\"><br>सीटू की क्षमता = 4 - 1 = 3 इकाई<br>टीटू की क्षमता = 6 - 3 = 3 इकाई<br>टीटू द्वारा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 4 घंटे</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. A and B together can complete a piece of work in 25 days, B and C together can complete the same piece of work in 36 days, while C and A together can complete it in 30 days. If A, B, C, and D together can complete this piece of work in 18 days, then in how many days can D alone complete this piece of work?</p>",
                    question_hi: "<p>8. A और B मिलकर किसी कार्य को 25 दिनों में पूरा कर सकते हैं, B और C मिलकर उसी कार्य को 36 दिनों में पूरा कर सकते हैं, जबकि C और A मिलकर उसी कार्य को 30 दिनों में पूरा कर सकते हैं। यदि A, B, C और D मिलकर इस कार्य को 18 दिनों में पूरा कर सकते हैं, तो D अकेला इस कार्य को कितने दिनों में पूरा कर सकता है?</p>",
                    options_en: ["<p>225</p>", "<p>210</p>", 
                                "<p>200</p>", "<p>180</p>"],
                    options_hi: ["<p>225</p>", "<p>210</p>",
                                "<p>200</p>", "<p>180</p>"],
                    solution_en: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363679351.png\" alt=\"rId13\" width=\"320\" height=\"131\"><br>Efficiency of A+B+C = <math display=\"inline\"><mfrac><mrow><mn>91</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 45.5 unit<br>Efficiency of A+B+C+D = 50<br>Efficiency of D = 50 - 45.5 = 4.5 unit<br>Time taken by D alone = <math display=\"inline\"><mfrac><mrow><mn>900</mn></mrow><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 200 days</p>",
                    solution_hi: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363679689.png\" alt=\"rId14\" width=\"326\" height=\"143\"><br>A+B+C की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>91</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 45.5 इकाई<br>A+B+C+D की दक्षता = 50<br>D की दक्षता = 50 - 45.5 = 4.5 इकाई<br>अकेले D द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>900</mn></mrow><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 200 दिन</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Ravi and Sanju together can do a job in 2 days, Sanju and Mahesh can do it in 4 days, while Ravi and Mahesh can do it in 2 days. What is the number of days required for Ravi to do the same job alone?</p>",
                    question_hi: "<p>9. रवि और संजू एक साथ मिलकर किसी कार्य को 2 दिन में कर सकते हैं, संजू और महेश इसे 4 दिन में कर सकते हैं, जबकि रवि और महेश इसे 2 दिन में कर सकते हैं। रवि को उसी कार्य को अकेले करने में कितने दिन लगेंगे?</p>",
                    options_en: ["<p>9 days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#160;</mi></math>days</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days</p>", "<p>4 days</p>"],
                    options_hi: ["<p>9 दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#160;</mi></math>दिन</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>", "<p>4 दिन</p>"],
                    solution_en: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363679952.png\" alt=\"rId15\" width=\"378\" height=\"174\"><br>Efficiency of Ravi+Sanju+Mahesh = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math> = 2.5 unit<br>Efficiency of Ravi = 2.5 - 1 = 1.5 unit<br>Time taken by Ravi = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math>days</p>",
                    solution_hi: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363680337.png\" alt=\"rId16\" width=\"299\" height=\"146\"><br>रवि + संजू + महेश की दक्षता = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math>&nbsp;= 2.5 इकाई<br>रवि की दक्षता = 2.5 - 1 = 1.5 इकाई<br>रवि द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> दिन</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Manish, Nakul and Pintoo alone can complete a certain work in 21 days, 28 days and 15 days, respectively. Manish and Pintoo started the work together while Nakul joined them after 5 days and worked with them till the completion of the work. For how many days did Nakul work?</p>",
                    question_hi: "<p>10. मनीष, नकुल और पिंटू अकेले किसी काम को क्रमशः 21 दिन, 28 दिन और 15 दिन में पूरा कर सकते हैं। मनीष और पिंटू ने एक साथ काम शुरू किया जबकि नकुल ने 5 दिन के बाद उनके साथ काम करना शुरू किया और काम पूरा होने तक उनके साथ काम किया। नकुल ने कितने दिन तक काम किया?</p>",
                    options_en: ["<p>3 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>2 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p>5 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>3 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>2 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p>5 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363680476.png\" alt=\"rId17\" width=\"252\" height=\"160\"><br>5 days work of Manish and Pintu = 5 <math display=\"inline\"><mo>&#215;</mo></math> (20 + 28) = 240 unit<br>Remaining work = 420 - 240 = 180 unit<br>Time taken by all three to complete remaining work = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>20</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>28</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>63</mn></mfrac></math>= 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>days<br>Hence, number of days Nakul worked for = 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363680621.png\" alt=\"rId18\" width=\"244\" height=\"177\"><br>मनीष और पिंटू का 5 दिन का कार्य = 5 <math display=\"inline\"><mo>&#215;</mo></math> (20 + 28) = 240 इकाई<br>शेष कार्य = 420 - 240 = 180 इकाई<br>शेष कार्य को पूरा करने में तीनों द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>20</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>28</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>63</mn></mfrac></math>= 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>दिन<br>अतः, नकुल ने जितने दिन काम किया = 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Ronak and Riya can do a work in 12 and 18 days, respectively. They worked together for six days and then Ronak left. Find the time taken by Riya to complete the work.</p>",
                    question_hi: "<p>11. रौनक और रिया किसी कार्य को क्रमश: 12 और 18 दिनों में कर सकती हैं। उन्होंने ने छह दिनों तक साथ कार्य किया और फिर रौनक कार्य छोड़कर चली गई। कार्य को पूरा करने के लिए रिया द्वारा लिया गया समय ज्ञात कीजिए।</p>",
                    options_en: ["<p>5 days</p>", "<p>7 days</p>", 
                                "<p>6 days</p>", "<p>3 days</p>"],
                    options_hi: ["<p>5 दिन</p>", "<p>7 दिन</p>",
                                "<p>6 दिन</p>", "<p>3 दिन</p>"],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363680752.png\" alt=\"rId19\" width=\"242\" height=\"156\"><br>Work done by both in six days = 6 <math display=\"inline\"><mo>&#215;</mo></math> 5 = 30 unit<br>Remaining work = 36 - 30 = 6 unit<br>Time taken by Riya to do remaining work = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 3 days</p>",
                    solution_hi: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363680875.png\" alt=\"rId20\" width=\"248\" height=\"178\"><br>दोनों द्वारा छह दिनों में किया गया कार्य = 6 <math display=\"inline\"><mo>&#215;</mo></math> 5 = 30 इकाई<br>शेष कार्य = 36 - 30 = 6 इकाई<br>रिया द्वारा शेष कार्य करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 3 दिन</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Tarun is twice as good as Tripti in doing a work. Together, they can complete the work in 16 days. In how many days can Tripti alone complete the same work?</p>",
                    question_hi: "<p>12. तरुण किसी कार्य को करने में तृप्ति से दोगुना कार्यकुशल है। दोनों एक साथ मिलकर एक कार्य को 16 दिनों में पूरा कर सकते हैं। तृप्ति अकेले उस कार्य को कितने दिनों में पूरा कर सकती है?</p>",
                    options_en: ["<p>24</p>", "<p>48</p>", 
                                "<p>25</p>", "<p>50</p>"],
                    options_hi: ["<p>24</p>", "<p>48</p>",
                                "<p>25</p>", "<p>50</p>"],
                    solution_en: "<p>12.(b)<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> Tarun : Tripti<br>Effi. <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; :&nbsp; &nbsp; 1<br>Total work = total efficiency <math display=\"inline\"><mo>&#215;</mo></math> 16 = 3 &times; 16 = 48 unit<br>Time taken by tripti alone = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 48 days</p>",
                    solution_hi: "<p>12.(b)<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> तरूण : तृप्ति<br>दक्षता <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; :&nbsp; &nbsp;1<br>कुल कार्य = कुल दक्षता <math display=\"inline\"><mo>&#215;</mo></math> 16 = 3 &times; 16 = 48 इकाई<br>अकेले तृप्ति द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 48 दिन</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. P can do a piece of work in 30 days and Q in 40 days. They work together for 4 days and then Q leaves. The number of days taken by P to finish the remaining work is:</p>",
                    question_hi: "<p>13. P एक काम को 30 दिन में और Q, 40 दिन में कर सकता है। वे 4 दिन तक मिलकर काम करते हैं और फिर Q छोड़ देता है। शेष काम को पूरा करने में P को कितने दिन लगेंगे ?</p>",
                    options_en: ["<p>23 days</p>", "<p>28 days</p>", 
                                "<p>30 days</p>", "<p>7 days</p>"],
                    options_hi: ["<p>23 दिन</p>", "<p>28 दिन</p>",
                                "<p>30 दिन</p>", "<p>7 दिन</p>"],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363681023.png\" alt=\"rId21\" width=\"237\" height=\"143\"><br>Total work done in 4 days = 7 <math display=\"inline\"><mo>&#215;</mo></math> 4 = 28 unit<br>Remaining work = 120 - 28 = 92 unit<br>Time taken by P to finish remaining work = <math display=\"inline\"><mfrac><mrow><mn>92</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 23 days</p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363681023.png\" alt=\"rId21\" width=\"241\" height=\"145\"><br>4 दिनों में किया गया कुल कार्य = 7 <math display=\"inline\"><mo>&#215;</mo></math> 4 = 28 इकाई<br>शेष कार्य = 120 - 28 = 92 इकाई<br>शेष कार्य को पूरा करने में P द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>92</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 23 दिन</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Amita can build a room in the same amount of time that Bina and Sita working together can build. If Amita and Bina together could do it in 25 days and Sita alone in 35 days, then how many days are required for Bina alone to do the same work ?</p>",
                    question_hi: "<p>14. अमिता उतने ही समय में एक कमरा बना सकती है, जितने समय में बीना और सीता एकसाथ मिलकर उसे बना सकती हैं। यदि अमिता और बीना एकसाथ मिलकर इसे 25 दिन में कर सकती हैं और सीता अकेले 35 दिन में कर सकती हैं, तो बीना को अकेले उसी कार्य को करने में कितने दिन लगेंगे ?</p>",
                    options_en: ["<p>152 Days</p>", "<p>175 Days</p>", 
                                "<p>165 Days</p>", "<p>180 Days</p>"],
                    options_hi: ["<p>152 दिन</p>", "<p>175 दिन</p>",
                                "<p>165 दिन</p>", "<p>180 दिन</p>"],
                    solution_en: "<p>14.(b)<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> Amita : Bina + Sita<br>Time <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 1<br>Effi. <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 1<br>Total Effi. = 2 units &hellip; (i)<br>Now, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363681147.png\" alt=\"rId22\" width=\"271\" height=\"191\"><br>Effi. of Amita + Bina + Sita = 12 unit &hellip; (ii)<br>From (i) and (ii)<br>Effi. of Bina and Sita = 6 unit<br>Effi. of Bina = 6 - 5 = 1 unit<br>Time taken by Bina = <math display=\"inline\"><mfrac><mrow><mn>175</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 175 days</p>",
                    solution_hi: "<p>14.(b)<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> अमिता : बीना + सीता<br>समय <math display=\"inline\"><mo>&#8594;</mo></math> &nbsp;&nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;1<br>दक्षता <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;1<br>कुल दक्षता = 2 इकाई &hellip; (i)<br>अब, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363681271.png\" alt=\"rId23\" width=\"286\" height=\"211\"><br>अमिता + बीना + सीता की दक्षता = 12 इकाई&hellip; (ii)<br>(i) और (ii) से<br>बीना और सीता की दक्षता = 6 इकाई<br>बीना की दक्षता = 6 - 5 = 1 इकाई<br>बीना द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>175</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 175 दिन</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Akash and Vikash can complete a work in 40 days and 60 days, respectively. In how many days will the entire work be completed if they work on alternate days, starting with Akash ?</p>",
                    question_hi: "<p>15. आकाश और विकास एक कार्य को क्रमशः 40 दिन और 60 दिन में पूरा कर सकते हैं। आकाश से शुरू करते हुए, यदि वे एकांतर दिनों में बारी-बारी से कार्य करते हैं, तो संपूर्ण कार्य कितने दिनों में पूरा हो जाएगा ?</p>",
                    options_en: ["<p>48</p>", "<p>52</p>", 
                                "<p>50</p>", "<p>42</p>"],
                    options_hi: ["<p>48</p>", "<p>52</p>",
                                "<p>50</p>", "<p>42</p>"],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363681415.png\" alt=\"rId24\" width=\"290\" height=\"174\"><br>Work done in 2 days = 5 unit <br>Total work = 120unit <br>Number of 2 day cycle = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 24<br>Each cycle consists 2 days , hence total days = 2 &times; 24 = 48days</p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1737363681569.png\" alt=\"rId25\" width=\"280\" height=\"187\"><br>2 दिन में किया गया कार्य = 5 इकाई <br>कुल कार्य = 120 इकाई <br>2 दिन के चक्र की संख्या = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 24<br>प्रत्येक चक्र में 2 दिन होते हैं, इसलिए कुल दिन = 2 &times; 24 = 48 दिन</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>