<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Find the volume of a cone, the radius of whose base is 21 cm and height is 20 cm. Take ( &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2306;&#2325;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 21 cm </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 20 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> &pi;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    options_en: ["<p>9420 cm&sup3;</p>\n", "<p>9240 cm&sup3;</p>\n", 
                                "<p>9204 cm&sup3;</p>\n", "<p>8840 cm&sup3;</p>\n"],
                    options_hi: ["<p>9420 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;<span style=\"font-weight: 400;\">&sup3;</span></span></p>\n", "<p>9240 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;<span style=\"font-weight: 400;\">&sup3;</span></span></p>\n",
                                "<p>9204 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;<span style=\"font-weight: 400;\">&sup3;</span></span></p>\n", "<p>8840 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;<span style=\"font-weight: 400;\">&sup3;</span></span></p>\n"],
                    solution_en: "<p>1.(b) <span style=\"font-family: Cambria Math;\">Volume of Cone =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;r&sup2;h</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 21 &times; 21 &times; 20 = <span style=\"font-weight: 400;\">9240 cm&sup3;</span></p>\n",
                    solution_hi: "<p>1.(b) <span style=\"font-family: Cambria Math;\">&#2358;&#2306;&#2325;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;r&sup2;h</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 21 &times; 21 &times; 20 = <span style=\"font-weight: 400;\">9240 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;&sup3;</span></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">A toy is in the form of a cone mounted on a hemisphere. The radius of the hemisphere and that of the cone is 7 cm and height of the cone is 5 cm. The total surface area of </span><span style=\"font-family: Cambria Math;\">the toy (take <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&pi;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">) is (rounded off to two places of decimal):</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2367;&#2354;&#2380;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2306;&#2325;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2306;&#2325;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 7 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2306;&#2325;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2314;&#2306;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 5 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2367;&#2354;&#2380;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2351;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> ( <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&pi;</mi><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) (</span><span style=\"font-family: Cambria Math;\">&#2342;&#2358;&#2350;&#2354;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    options_en: ["<p>2648.81 cm&sup2;</p>\n", "<p>497.25 cm&sup2;</p>\n", 
                                "<p>2666.85 cm&sup2;</p>\n", "<p>486.65 cm&sup2;</p>\n"],
                    options_hi: ["<p>2648.81 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;<span style=\"font-weight: 400;\">&sup2;</span></span></p>\n", "<p>497.25 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;<span style=\"font-weight: 400;\">&sup2;</span></span></p>\n",
                                "<p>2666.85 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;<span style=\"font-weight: 400;\">&sup2;</span></span></p>\n", "<p>486.65 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;<span style=\"font-weight: 400;\">&sup2;</span></span></p>\n"],
                    solution_en: "<p>2.(b)</p>\r\n<p><span style=\"font-weight: 400;\">T.S.A of Toy = 2&pi;</span><span style=\"font-weight: 400;\">r&sup2;</span><span style=\"font-weight: 400;\">&nbsp;+ &pi;</span><span style=\"font-weight: 400;\">rl</span><span style=\"font-weight: 400;\"> = &pi;</span><span style=\"font-weight: 400;\">r(2r + l)</span></p>\r\n<p><span style=\"font-weight: 400;\">Slant Height of Cone (</span><span style=\"font-weight: 400;\">l)</span><span style=\"font-weight: 400;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup></msqrt></math><span style=\"font-weight: 400;\">&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>+</mo><mn>25</mn></msqrt><mo>&nbsp;</mo><mo>=</mo><msqrt><mo>&nbsp;</mo><mn>74</mn></msqrt><mo>&nbsp;</mo><mi>c</mi><mi>m</mi></math></p>\r\n<p><span style=\"font-weight: 400;\">T.S.A of toy = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-weight: 400;\"> &times; 7 (2 &times; 7 + </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>74</mn></msqrt></math></span><span style=\"font-weight: 400;\">)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= 497.25 </span><span style=\"font-weight: 400;\">cm&sup2;</span></p>\n",
                    solution_hi: "<p>2.(b) <span style=\"font-family: Cambria Math;\">&#2326;&#2367;&#2354;&#2380;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; </span><span style=\"font-weight: 400;\">= 2&pi;</span><span style=\"font-weight: 400;\">r&sup2;</span><span style=\"font-weight: 400;\">&nbsp;+ &pi;</span><span style=\"font-weight: 400;\">rl</span><span style=\"font-weight: 400;\"> = &pi;</span><span style=\"font-weight: 400;\">r(2r + l)</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2358;&#2306;&#2325;&#2369; &#2325;&#2368; &#2340;&#2367;&#2352;&#2381;&#2351;&#2325; &#2314;&#2305;&#2330;&#2366;&#2312; (</span><span style=\"font-weight: 400;\">l)</span><span style=\"font-weight: 400;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>r</mi><mn>2</mn></msup><mo>+</mo><msup><mi>h</mi><mn>2</mn></msup></msqrt></math><span style=\"font-weight: 400;\">&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>+</mo><mn>25</mn></msqrt><mo>&nbsp;</mo><mo>=</mo><msqrt><mo>&nbsp;</mo><mn>74</mn></msqrt><mo>&nbsp;</mo><mi>&#2360;&#2375;&#2350;&#2368;</mi></math></p>\r\n<p><span style=\"font-weight: 400;\">&#2326;&#2367;&#2354;&#2380;&#2344;&#2375; &#2325;&#2366; &#2325;&#2369;&#2354; &#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-weight: 400;\"> &times; 7 (2 &times; 7 + </span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>74</mn></msqrt></math></span><span style=\"font-weight: 400;\">)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">= 497.25 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;&sup2;</span></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">If the curved surface area of a sphere with radius <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn mathvariant=\"italic\">1</mn></msub></math> </span><span style=\"font-family: Cambria Math;\">unit </span><span style=\"font-family: Cambria Math;\">is the same as the total surface area of another hemisphere with radius <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn mathvariant=\"italic\">2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> , then the <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn mathvariant=\"italic\">2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> is_________.</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn mathvariant=\"italic\">1</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2325;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn mathvariant=\"italic\">2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn mathvariant=\"italic\">2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> __________ </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> |</span></p>\n",
                    options_en: ["<p>same as&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn mathvariant=\"italic\">1</mn></msub></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac><msub><mi>R</mi><mn>1</mn></msub></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msub><mi>R</mi><mn>1</mn></msub></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><msub><mi>R</mi><mn>1</mn></msub></math><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn mathvariant=\"italic\">1</mn></msub></math> &#2325;&#2375; &#2348;&#2352;&#2366;&#2348;&#2352;</span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac><msub><mi>R</mi><mn>1</mn></msub></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msub><mi>R</mi><mn>1</mn></msub></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><msub><mi>R</mi><mn>1</mn></msub></math><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>3.(b)&nbsp;C.S.A of Sphere = 4&pi;r&sup2;</p>\r\n<p>T.S.A of Hemisphere = 3&pi;r&sup2;</p>\r\n<p>According to Question,</p>\r\n<p>4 &times; &pi; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn>1</mn></msub><mo>&times;</mo><msub><mi>R</mi><mn>1</mn></msub></math> = 3 &times; &pi; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn>2</mn></msub><mo>&times;</mo><msub><mi>R</mi><mn>2</mn></msub></math></p>\r\n<p><span style=\"font-weight: 400;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn>2</mn></msub><mo>=</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac><msub><mi>R</mi><mn>1</mn></msub></math></p>\n",
                    solution_hi: "<p>3.(b) <span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2325;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 4&pi;r&sup2;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 3&pi;r&sup2;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p>4 &times; &pi; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn>1</mn></msub><mo>&times;</mo><msub><mi>R</mi><mn>1</mn></msub></math> = 3 &times; &pi; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn>2</mn></msub><mo>&times;</mo><msub><mi>R</mi><mn>2</mn></msub></math></p>\r\n<p><span style=\"font-weight: 400;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mn>2</mn></msub><mo>=</mo><mfrac><mn>2</mn><msqrt><mn>3</mn></msqrt></mfrac><msub><mi>R</mi><mn>1</mn></msub></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">How many cubes of side 4 cm can be formed by melting a solid metallic cuboid of length 12 cm, breadth 8 cm and height 8 cm?</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">12 cm </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">, 8 cm </span><span style=\"font-family: Cambria Math;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 8 cm </span><span style=\"font-family: Cambria Math;\">&#2314;&#2306;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2343;&#2366;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2336;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2367;&#2328;&#2354;&#2366;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> 4 cm </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>11</p>\n", "<p>14</p>\n", 
                                "<p>12</p>\n", "<p>13</p>\n"],
                    options_hi: ["<p>11</p>\n", "<p>14</p>\n",
                                "<p>12</p>\n", "<p>13</p>\n"],
                    solution_en: "<p>4.(c) <span style=\"font-family: Cambria Math;\">Volume of Cube = <span style=\"font-weight: 400;\">a&sup3;</span>&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Volume of Cuboid = <span style=\"font-weight: 400;\">lbh</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let number of cubes made by melting solid cuboid be x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to Question</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 &times; 4 &times; 4 &times; x </span><span style=\"font-family: Cambria Math;\">= 12 &times; 8 &times; 8 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 12 cubes</span></p>\n",
                    solution_hi: "<p>4.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> =&nbsp; <span style=\"font-weight: 400;\">a&sup3;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2328;&#2344;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> = <span style=\"font-weight: 400;\">lbh</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2336;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2367;&#2328;&#2354;&#2366;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 &times; 4 &times; 4 &times; x </span><span style=\"font-family: Cambria Math;\">= 12 &times; 8 &times; 8 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 12</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">The radius of a hemisphere is 21 cm. Its volume (in cm&sup3;</span><span style=\"font-family: Cambria Math;\">) will be_________ (Use &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 21 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">cm&sup3; </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) __________ </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> ( &pi; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    options_en: ["<p>19404</p>\n", "<p>22101</p>\n", 
                                "<p>21109</p>\n", "<p>20112</p>\n"],
                    options_hi: ["<p>19404</p>\n", "<p>22101</p>\n",
                                "<p>21109</p>\n", "<p>20112</p>\n"],
                    solution_en: "<p>5.(a) <span style=\"font-family: Cambria Math;\">Volume of Hemisphere =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac><mi>&pi;</mi><msup><mi>r</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&times;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> 21 &times; 21 &times; 21 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>407484</mn><mn>21</mn></mfrac></math>= 19404 cm&sup3;</span></p>\n",
                    solution_hi: "<p>5.(a) <span style=\"font-weight: 400;\">&#2309;&#2352;&#2381;&#2343;&#2327;&#2379;&#2354;&#2366; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\">&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac><mi>&pi;</mi><msup><mi>r</mi><mn>3</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&times;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-family: Cambria Math;\">&times;</span><span style=\"font-family: Cambria Math;\"> 21 &times; 21 &times; 21 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>407484</mn><mn>21</mn></mfrac></math>= 19404 cm&sup3;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">If the length of a rectangle is reduced by 15% and breadth is increased by 10%, what is the percentage change in the area of the rectangle?</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 15%</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>5.6% increment</p>\n", "<p>6.5% increment</p>\n", 
                                "<p>6.5% decrement</p>\n", "<p>5.6% decrement</p>\n"],
                    options_hi: ["<p>5.6% <span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n", "<p>6.5% <span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n",
                                "<p>6.5% <span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2350;&#2368;</span></p>\n", "<p>5.6% <span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2350;&#2368;</span></p>\n"],
                    solution_en: "<p>6.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Area of the rectangle = length &times; breadth</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Net % change </span><span style=\"font-family: Cambria Math;\">in area of rectangle</span></p>\r\n<p>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>-</mo><mn>15</mn><mo>+</mo><mn>10</mn><mo>-</mo><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn></mrow><mn>100</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>-</mo><mn>6</mn><mo>.</mo><mn>5</mn><mo>%</mo></math></p>\n",
                    solution_hi: "<p>6.(c) <span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Cambria Math;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> % </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mo>-</mo><mn>15</mn><mo>+</mo><mn>10</mn><mo>-</mo><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn></mrow><mn>100</mn></mfrac><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>-</mo><mn>6</mn><mo>.</mo><mn>5</mn><mo>%</mo></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">What is the height of a solid cylinder whose radius is 7 cm and total surface area is 1144 cm&sup2; ? (Use <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&pi;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2336;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 7 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> 1144 cm&sup2;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? (</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&pi;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    options_en: ["<p>19 cm</p>\n", "<p>12 cm</p>\n", 
                                "<p>22 cm</p>\n", "<p>18 cm</p>\n"],
                    options_hi: ["<p>19 cm</p>\n", "<p>12 cm</p>\n",
                                "<p>22 cm</p>\n", "<p>18 cm</p>\n"],
                    solution_en: "<p>7.(a) <span style=\"font-family: Cambria Math;\">r = 7 <span style=\"font-weight: 400;\">cm</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">Total surface area </span>= 2&pi;</span><span style=\"font-family: Cambria Math;\">r(r + h)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1144 = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mrow><mn>7</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 7(7 + h)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mrow><mn>1144</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn></mrow><mrow><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>22</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn></mrow></mfrac></math>= (7 + h) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">h = 26 - </span><span style=\"font-family: Cambria Math;\">7 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">h = 19 <span style=\"font-weight: 400;\">cm</span></span></p>\n",
                    solution_hi: "<p>7.(a) <span style=\"font-family: Cambria Math;\">r = 7 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 2&pi;</span><span style=\"font-family: Cambria Math;\">r(r + h)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1144 = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mrow><mn>7</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 7(7 + h)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mrow><mn>1144</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn></mrow><mrow><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>22</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn></mrow></mfrac></math>= (7 + h) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">h = 26 - </span><span style=\"font-family: Cambria Math;\">7 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">h = 19 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">What is the surface area (in cm&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">of a sphere whose diameter is 21 cm ?</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">21 cm </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> (cm&sup2;&nbsp;</span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>3465</p>\n", "<p>1386</p>\n", 
                                "<p>2772</p>\n", "<p>5544</p>\n"],
                    options_hi: ["<p>3465<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>1386</p>\n",
                                "<p>2772</p>\n", "<p>5544</p>\n"],
                    solution_en: "<p>8.(b) <span style=\"font-family: Cambria Math;\">surface area of a sphere = 4&pi;r&sup2;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&times;</mo><mfrac><mn>21</mn><mn>2</mn></mfrac><mo>&times;</mo><mfrac><mn>21</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 1386 cm&sup2;&nbsp;</span></p>\n",
                    solution_hi: "<p>8.(b) <span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 4&pi;r&sup2;</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&times;</mo><mfrac><mn>21</mn><mn>2</mn></mfrac><mo>&times;</mo><mfrac><mn>21</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 1386 cm&sup2;&nbsp;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">What is the change in the curved surface area of a cylinder when we double the radius and the height is halved?</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2325;&#2381;&#2352;&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2327;&#2369;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2343;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Doubled</p>\n", "<p>Four times</p>\n", 
                                "<p>No change</p>\n", "<p>Tripled</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2369;&#2344;&#2366;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2340;&#2367;&#2327;&#2369;&#2344;&#2366;</span></p>\n"],
                    solution_en: "<p>9.(c)&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>I</mi><mi>n</mi><mi>i</mi><mi>t</mi><mi>i</mi><mi>a</mi><mi>l</mi><mo>&nbsp;</mo><mo>(</mo><mi>C</mi><mo>.</mo><mi>S</mi><mo>.</mo><mi>A</mi><mo>)</mo><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>c</mi><mi>y</mi><mi>l</mi><mi>i</mi><mi>n</mi><mi>d</mi><mi>e</mi><mi>r</mi></mrow><mrow><mi>f</mi><mi>i</mi><mi>n</mi><mi>a</mi><mi>l</mi><mo>(</mo><mi>C</mi><mo>.</mo><mi>S</mi><mo>.</mo><mi>A</mi><mo>)</mo><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>c</mi><mi>y</mi><mi>l</mi><mi>i</mi><mi>n</mi><mi>d</mi><mi>e</mi><mi>r</mi></mrow></mfrac></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mrow><mn>2</mn><mi>&pi;</mi><msub><mi>r</mi><mn>1</mn></msub><msub><mi>h</mi><mn>1</mn></msub></mrow><mrow><mn>2</mn><mi>&pi;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><msub><mi>r</mi><mn>1</mn></msub><mo>&times;</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><msub><mi>h</mi><mn>1</mn></msub></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>1</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> ( No change )</span></p>\n",
                    solution_hi: "<p>9.(c)&nbsp;<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2348;&#2375;&#2354;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</mi><mo>&nbsp;</mo><mi>&#2357;&#2325;&#2381;&#2352;</mi><mo>&nbsp;</mo><mi>&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</mi><mo>&nbsp;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#2348;&#2375;&#2354;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2309;&#2306;&#2340;&#2367;&#2350;</mi><mo>&nbsp;</mo><mi>&#2357;&#2325;&#2381;&#2352;</mi><mo>&nbsp;</mo><mi>&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</mi><mo>&nbsp;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mrow><mn>2</mn><mi>&pi;</mi><msub><mi>r</mi><mn>1</mn></msub><msub><mi>h</mi><mn>1</mn></msub></mrow><mrow><mn>2</mn><mi>&pi;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><msub><mi>r</mi><mn>1</mn></msub><mo>&times;</mo><mo>&nbsp;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><msub><mi>h</mi><mn>1</mn></msub></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>1</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> ( </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> )</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> What is the volume of a cone whose height is 24 cm and radius is 14 cm?</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2306;&#2325;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 24 cm </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 14 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>4298 <span style=\"font-family: Cambria Math;\">cm&sup3; </span>&nbsp;</p>\n", "<p>9428 <span style=\"font-family: Cambria Math;\">cm&sup3;</span></p>\n", 
                                "<p>4982 <span style=\"font-family: Cambria Math;\">cm&sup3;</span></p>\n", "<p>4928 <span style=\"font-family: Cambria Math;\">cm&sup3;</span></p>\n"],
                    options_hi: ["<p>4298 <span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup3;</span></p>\n", "<p>9428 <span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup3;</span></p>\n",
                                "<p>4982 <span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup3;</span></p>\n", "<p>4928 <span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup3;</span></p>\n"],
                    solution_en: "<p>10.(d) <span style=\"font-family: Cambria Math;\">volume of a cone = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&pi;r&sup2;</span><span style=\"font-family: Cambria Math;\">h</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> &times; 14 &times; 14 &times; 24 = 4928 cm&sup3;</span></p>\n",
                    solution_hi: "<p>10.(d) <span style=\"font-family: Cambria Math;\">&#2358;&#2306;&#2325;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;r&sup2;h</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> &times; 14 &times; 14 &times; 24 = 4928 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup3;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> Calculate the cost to paint a water tank in the shape of a sphere having a radius of 7 cm, if the cost of painting is Rs.4.5/c<span style=\"font-weight: 400;\">m&sup2;</span></span><span style=\"font-family: Cambria Math;\">. ( &pi; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2306;&#2335;&#2368;&#2350;&#2368;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2354;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2306;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2306;&#2335;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> 4.5/</span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;<span style=\"font-weight: 400;\">&sup2;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> (&pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">) </span></p>\n",
                    options_en: ["<p>Rs. 2,772</p>\n", "<p>Rs. 2,277</p>\n", 
                                "<p>Rs. 2,272</p>\n", "<p>Rs. 2,727</p>\n"],
                    options_hi: ["<p>Rs. 2,772</p>\n", "<p>Rs. 2,277</p>\n",
                                "<p>Rs. 2,272</p>\n", "<p>Rs. 2,727</p>\n"],
                    solution_en: "<p>11.(a) <span style=\"font-family: Cambria Math;\">(C.S.A) of sphere = 4&pi;r<span style=\"font-weight: 400;\">&sup2;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">the cost of paint = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 7 &times; 7 &times; 4.5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2772 Rs.</span></p>\n",
                    solution_hi: "<p>11.(a) <span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2325;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 4&pi;r<span style=\"font-weight: 400;\">&sup2;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 7 &times; 7 &times; 4.5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 2772 </span><span style=\"font-family: Cambria Math;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> A sphere of radius 6 m is cut into two equal halves. Find the volume of each hemisphere so formed . (Use &pi; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> ) </span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> 6 m </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> ( &pi; </span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">))</span></p>\n",
                    options_en: ["<p>531.11 m&sup3;</p>\n", "<p>331.14 &nbsp;m&sup3;</p>\n", 
                                "<p>452.57 m&sup3;</p>\n", "<p>562.18 m&sup3;</p>\n"],
                    options_hi: ["<p>531.11 m&sup3;</p>\n", "<p>331.14 m&sup3;</p>\n",
                                "<p>452.57 m&sup3;</p>\n", "<p>562.18 m&sup3;</p>\n"],
                    solution_en: "<p>12.(c) <span style=\"font-family: Cambria Math;\">Radius of sphere \'r\' </span><span style=\"font-family: Cambria Math;\">= 6 m</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Volume of sphere =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;r&sup3;&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&times;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&times;</mo><msup><mn>6</mn><mn>3</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 905.14 m&sup3;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Volume of each hemisphere so formed </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mrow><mn>905</mn><mo>.</mo><mn>14</mn></mrow><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 452.57 m&sup3;</span></p>\n",
                    solution_hi: "<p>12.(c) <span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;&nbsp;</span><span style=\"font-family: Cambria Math;\"> \'r\'</span><span style=\"font-family: Cambria Math;\"> = 6 m</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;r&sup3;&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&times;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&times;</mo><msup><mn>6</mn><mn>3</mn></msup></math> = 905.14 m&sup3;&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2343;&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mrow><mn>905</mn><mo>.</mo><mn>14</mn></mrow><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 452.57 m&sup3;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">The cost of painting a solid cone at 30 paise per cm&sup2; is &#8377;660. Find the radius of the cone if its slant height is 15 cm. (Use &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2336;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2306;&#2325;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 30 </span><span style=\"font-family: Cambria Math;\">&#2346;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup2;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2306;&#2327;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> &#8377;660 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2306;&#2325;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2367;&#2352;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 15 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> | (&pi; </span><span style=\"font-family: Cambria Math;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    options_en: ["<p>40 cm</p>\n", "<p>50 cm</p>\n", 
                                "<p>30 cm</p>\n", "<p>20 cm</p>\n"],
                    options_hi: ["<p>40 cm</p>\n", "<p>50 cm</p>\n",
                                "<p>30 cm</p>\n", "<p>20 cm</p>\n"],
                    solution_en: "<p>13.(d) <span style=\"font-family: Cambria Math;\">Total surface area of cone = </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>660</mn><mo>&times;</mo><mn>100</mn></mrow><mn>30</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 2200 cm&sup2;</span></p>\r\n<p><span style=\"font-weight: 400;\">&pi;r(</span><span style=\"font-weight: 400;\">l + r</span><span style=\"font-weight: 400;\"> )= 2200 </span><span style=\"font-weight: 400;\">cm<span style=\"font-family: Cambria Math;\">&sup2;</span></span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&times; r(</span><span style=\"font-weight: 400;\">15 + r)</span><span style=\"font-weight: 400;\"> = 2200 cm<span style=\"font-family: Cambria Math;\">&sup2;</span></span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">r(</span><span style=\"font-weight: 400;\">15 + r</span><span style=\"font-weight: 400;\">)= 700</span></p>\r\n<p><span style=\"font-weight: 400;\">r<span style=\"font-family: Cambria Math;\">&sup2; </span></span><span style=\"font-weight: 400;\">+ 15r - 700</span><span style=\"font-weight: 400;\"> = 0</span></p>\r\n<p><span style=\"font-weight: 400;\">r<span style=\"font-family: Cambria Math;\">&sup2; </span>- 20r + 35r - 700</span><span style=\"font-weight: 400;\"> = 0</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">r</span><span style=\"font-weight: 400;\"> = - </span><span style=\"font-weight: 400;\">35</span><span style=\"font-weight: 400;\"> and 20&nbsp; &hellip;as radius can&rsquo;t be </span><span style=\"font-weight: 400;\">-ve</span><span style=\"font-weight: 400;\">.&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">Radius (</span><span style=\"font-weight: 400;\">r</span><span style=\"font-weight: 400;\">) = 20 cm.</span></p>\n",
                    solution_hi: "<p>13.(d) <strong>&nbsp;</strong><span style=\"font-weight: 400;\">&#2358;&#2306;&#2325;&#2369; &#2325;&#2366; &#2325;&#2369;&#2354; &#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>660</mn><mo>&times;</mo><mn>100</mn></mrow><mn>30</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 2200 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup2;</span></p>\r\n<p><span style=\"font-weight: 400;\">&pi;r(</span><span style=\"font-weight: 400;\">l + r</span><span style=\"font-weight: 400;\"> ) = 2200 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;&sup2;</span></span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&times; r(</span><span style=\"font-weight: 400;\">15 + r)</span><span style=\"font-weight: 400;\"> = 2200 <span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;&sup2;</span></span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">r(</span><span style=\"font-weight: 400;\">15 + r</span><span style=\"font-weight: 400;\">) = 700</span></p>\r\n<p><span style=\"font-weight: 400;\">r<span style=\"font-family: Cambria Math;\">&sup2; </span></span><span style=\"font-weight: 400;\">+ 15r - 700</span><span style=\"font-weight: 400;\"> = 0</span></p>\r\n<p><span style=\"font-weight: 400;\">r<span style=\"font-family: Cambria Math;\">&sup2; </span>- 20r + 35r - 700</span><span style=\"font-weight: 400;\"> = 0</span></p>\r\n<p><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">r</span><span style=\"font-weight: 400;\"> = - </span><span style=\"font-weight: 400;\">35</span><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&#2324;&#2352; 20&nbsp; &hellip;&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; -ve &#2344;&#2361;&#2368;&#2306; &#2361;&#2379; &#2360;&#2325;&#2340;&#2368;&#2404;&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366; (</span><span style=\"font-weight: 400;\">r</span><span style=\"font-weight: 400;\">) =&nbsp; 20 &#2360;&#2375;&#2350;&#2368;.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">Three solid cubes of sides 8 cm, 1 cm, and 6 cm, respectively, are melted to form a new cube. Find the longest diagonal of the new cube (in cm).</span></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2350;&#2358;</span><span style=\"font-family: Cambria Math;\">: 8 cm, 1 cm </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 6 cm </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2336;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2367;&#2328;&#2354;&#2366;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7</mn><msqrt><mn>3</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7</mn><msqrt><mn>2</mn></msqrt></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><msqrt><mn>2</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7</mn><msqrt><mn>3</mn></msqrt></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7</mn><msqrt><mn>2</mn></msqrt></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><msqrt><mn>3</mn></msqrt></math></p>\n"],
                    solution_en: "<p>14.(d) <span style=\"font-family: Cambria Math;\">Let the side of bigger cube = a cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Volume of 3 smaller cu</span><span style=\"font-family: Cambria Math;\">be = Volume of bigger cube</span></p>\r\n<p><span style=\"font-weight: 400;\">(8)&sup3;</span><span style=\"font-weight: 400;\">&nbsp;+ (</span><span style=\"font-weight: 400;\">1)&sup3;</span><span style=\"font-weight: 400;\">&nbsp;+ (</span><span style=\"font-weight: 400;\">6)&sup3;</span><span style=\"font-weight: 400;\">&nbsp;= (</span><span style=\"font-weight: 400;\">a)&sup3;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">512 + 1 + 216 = 729 &rarr; <span style=\"font-weight: 400;\">(9)&sup3;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Diagonal of cube = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>i</mi><mi>d</mi><mi>e</mi><mo>&nbsp;</mo><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\">cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Diagonal of bigger cube =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><msqrt><mn>3</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">cm</span></p>\n",
                    solution_hi: "<p>14.(d) <span style=\"font-weight: 400;\">&#2350;&#2366;&#2344;&#2366; &#2348;&#2337;&#2364;&#2375; &#2328;&#2344; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366; = a &#2360;&#2375;&#2350;&#2368;</span></p>\r\n<p><span style=\"font-weight: 400;\">3 &#2331;&#2379;&#2335;&#2375; &#2328;&#2344; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344; = &#2348;&#2337;&#2364;&#2375; &#2328;&#2344; &#2325;&#2366; &#2310;&#2351;&#2340;&#2344;</span></p>\r\n<p><span style=\"font-weight: 400;\">(8)&sup3;</span><span style=\"font-weight: 400;\">&nbsp;+ (</span><span style=\"font-weight: 400;\">1)&sup3;</span><span style=\"font-weight: 400;\">&nbsp;+ (</span><span style=\"font-weight: 400;\">6)&sup3;</span><span style=\"font-weight: 400;\">&nbsp;= (</span><span style=\"font-weight: 400;\">a)&sup3;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">512 + 1 + 216 = 729 &rarr; <span style=\"font-weight: 400;\">(9)&sup3;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2328;&#2344; &#2325;&#2366; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339;</span> = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2349;&#2369;&#2332;&#2366;</mi><mo>&nbsp;</mo><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo></math><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2348;&#2337;&#2364;&#2375; &#2328;&#2344; &#2325;&#2366; &#2357;&#2367;&#2325;&#2352;&#2381;&#2339;</span> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>9</mn><msqrt><mn>3</mn></msqrt></math> <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> Three cubes, each of side 5 cm, are joined end to end. What is the total surface area of the resulting cuboid?</span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> 5 cm </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2368;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>325 cm&sup2;</p>\n", "<p>380 cm&sup2;</p>\n", 
                                "<p>350 cm&sup2;</p>\n", "<p>375 cm&sup2;</p>\n"],
                    options_hi: ["<p>325 <span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup2;</span></p>\n", "<p>380 <span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup2;</span></p>\n",
                                "<p>350 <span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup2;</span></p>\n", "<p>375 <span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup2;</span></p>\n"],
                    solution_en: "<p>15.(c) <span style=\"font-family: Cambria Math;\">side of cube = 5 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Height of cuboid = 5 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Breadth of cuboid = 5 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Length of cuboid = 15 cm (because 3 cube are joined together)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">total surface area of cuboid </span><span style=\"font-family: Cambria Math;\">= 2(lb + bh + hl)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>2(15 &times; 5 + 5 &times; 5 + 5 &times; 15) = </span><span style=\"font-family: Cambria Math;\">350 <span style=\"font-weight: 400;\">cm&sup2;</span></span></p>\n",
                    solution_hi: "<p>15.(c) <span style=\"font-family: Cambria Math;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 5 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2328;&#2344;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 5 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2328;&#2344;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 5 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2328;&#2344;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 15 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Cambria Math;\">&#2328;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2369;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2328;&#2344;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 2(lb + bh + hl)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>2(15 &times; 5 + 5 &times; 5 + 5 &times; 15) = </span><span style=\"font-family: Cambria Math;\">350 <span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;</span>&sup2;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>