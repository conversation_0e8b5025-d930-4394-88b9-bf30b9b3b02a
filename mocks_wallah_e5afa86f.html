<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">If </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac><mo>=</mo><mfrac><mi mathvariant=\"normal\">r</mi><mi mathvariant=\"normal\">s</mi></mfrac><mo>=</mo><mfrac><mi mathvariant=\"normal\">t</mi><mi mathvariant=\"normal\">u</mi></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">, then what is the value of (4p + 3r + 7t): (4q + 3s + 7u)?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac><mo>=</mo><mfrac><mi mathvariant=\"normal\">r</mi><mi mathvariant=\"normal\">s</mi></mfrac><mo>=</mo><mfrac><mi mathvariant=\"normal\">t</mi><mi mathvariant=\"normal\">u</mi></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> (4p + 3r + 7t) : (4q + 3s + 7u) </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>4 : 11</p>\n", "<p>3 : 7</p>\n", 
                                "<p>2 : 5</p>\n", "<p>5 : 9</p>\n"],
                    options_hi: ["<p>4 : 11</p>\n", "<p>3 : 7</p>\n",
                                "<p>2 : 5</p>\n", "<p>5 : 9</p>\n"],
                    solution_en: "<p>1.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac><mo>=</mo><mfrac><mi mathvariant=\"normal\">r</mi><mi mathvariant=\"normal\">s</mi></mfrac><mo>=</mo><mfrac><mi mathvariant=\"normal\">t</mi><mi mathvariant=\"normal\">u</mi></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mfenced><mrow><mn>4</mn><mi mathvariant=\"normal\">p</mi><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">r</mi><mo>+</mo><mn>7</mn><mi mathvariant=\"normal\">t</mi></mrow></mfenced><mfenced><mrow><mn>4</mn><mi mathvariant=\"normal\">q</mi><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">s</mi><mo>+</mo><mn>7</mn><mi mathvariant=\"normal\">u</mi></mrow></mfenced></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mfenced><mrow><mn>4</mn><mo>&times;</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>&times;</mo><mn>2</mn><mo>+</mo><mn>7</mn><mo>&times;</mo><mn>2</mn></mrow></mfenced><mfenced><mrow><mn>4</mn><mo>&times;</mo><mn>5</mn><mo>+</mo><mn>3</mn><mo>&times;</mo><mn>5</mn><mo>+</mo><mn>7</mn><mo>&times;</mo><mn>5</mn></mrow></mfenced></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>(</mo><mn>14</mn><mo>)</mo></mrow><mrow><mn>5</mn><mo>(</mo><mn>14</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>1.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac><mo>=</mo><mfrac><mi mathvariant=\"normal\">r</mi><mi mathvariant=\"normal\">s</mi></mfrac><mo>=</mo><mfrac><mi mathvariant=\"normal\">t</mi><mi mathvariant=\"normal\">u</mi></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mfenced><mrow><mn>4</mn><mi mathvariant=\"normal\">p</mi><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">r</mi><mo>+</mo><mn>7</mn><mi mathvariant=\"normal\">t</mi></mrow></mfenced><mfenced><mrow><mn>4</mn><mi mathvariant=\"normal\">q</mi><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">s</mi><mo>+</mo><mn>7</mn><mi mathvariant=\"normal\">u</mi></mrow></mfenced></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mfenced><mrow><mn>4</mn><mo>&times;</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>&times;</mo><mn>2</mn><mo>+</mo><mn>7</mn><mo>&times;</mo><mn>2</mn></mrow></mfenced><mfenced><mrow><mn>4</mn><mo>&times;</mo><mn>5</mn><mo>+</mo><mn>3</mn><mo>&times;</mo><mn>5</mn><mo>+</mo><mn>7</mn><mo>&times;</mo><mn>5</mn></mrow></mfenced></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>(</mo><mn>14</mn><mo>)</mo></mrow><mrow><mn>5</mn><mo>(</mo><mn>14</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">Rahul spends some part of his income and saves the remaining part. The ratio of his expenditure and&nbsp; </span><span style=\"font-family: Cambria Math;\">saving is 5 : 4. If Rahul\'s income is Rs. 36000, then what will be his expenditure?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2361;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 5 : 4 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2361;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> Rs.36000 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs. 15000</p>\n", "<p>Rs. 25000</p>\n", 
                                "<p>Rs. 20000</p>\n", "<p>Rs. 16000</p>\n"],
                    options_hi: ["<p>Rs. 15000</p>\n", "<p>Rs. 25000</p>\n",
                                "<p>Rs. 20000</p>\n", "<p>Rs. 16000</p>\n"],
                    solution_en: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let expenditure of the Rahul = 5x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then, savings of the Rahul = 4x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">so, Income of the Rahul (5x + 4x) =&#8377; 36000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36000</mn><mn>9</mn></mfrac></math><span style=\"font-weight: 400;\">= 4000</span></span><span style=\"font-family: Cambria Math;\"> Rs</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Therefore, expenditure of the rahul = &#8377; 20,000</span></p>\n",
                    solution_hi: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2361;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> = 5x</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2361;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 4x</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2340;&#2379;, &#2352;&#2366;&#2361;&#2369;&#2354;&nbsp; &#2325;&#2368; &#2310;&#2351;&nbsp; (5x + 4x) = &#8377; 36000&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36000</mn><mn>9</mn></mfrac></math><span style=\"font-weight: 400;\">= 4000</span></span><span style=\"font-family: Cambria Math;\"> Rs</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2311;&#2360;&#2354;&#2367;&#2319;, &#2352;&#2366;&#2361;&#2369;&#2354; &#2325;&#2366; &#2326;&#2352;&#2381;&#2330;&nbsp;</span> = &#8377; 20,000</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">If P : Q = 2 : 3, Q : R = 4 : 5 and R : S = 2 : 1, then what is the value of P : R : S? </span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">P : Q = 2 : 3, Q : R = 4 : 5 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> R : S = 2 : 1 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> P : R : S </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\n",
                    options_en: ["<p>10 : 5 : 4</p>\n", "<p>16 : 30 : 15</p>\n", 
                                "<p>9 : 10 : 15</p>\n", "<p>12 : 25 : 10</p>\n"],
                    options_hi: ["<p>10 : 5 : 4</p>\n", "<p>16 : 30 : 15</p>\n",
                                "<p>9 : 10 : 15</p>\n", "<p>12 : 25 : 10</p>\n"],
                    solution_en: "<p>3.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> P&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;Q&nbsp; &nbsp; :&nbsp; &nbsp; R&nbsp; &nbsp; :&nbsp; &nbsp; S</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2&nbsp; &nbsp; :&nbsp; &nbsp; 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> -----------------------------------------</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;16&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;24&nbsp; &nbsp; :&nbsp; 30&nbsp; &nbsp;:&nbsp; &nbsp;15</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the ratio of </span><span style=\"font-family: Cambria Math;\">P : R : S = 16 : 30 : 15 </span></p>\n",
                    solution_hi: "<p>3.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&#2309;&#2344;&#2369;&#2346;&#2366;&#2340; </strong>&rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> P&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;Q&nbsp; &nbsp; :&nbsp; &nbsp; R&nbsp; &nbsp; :&nbsp; &nbsp; S</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; :&nbsp; &nbsp; 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> -----------------------------------------</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;16&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;24&nbsp; &nbsp; :&nbsp; 30&nbsp; &nbsp;:&nbsp; &nbsp;15</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">P : R : S </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 16 : 30 : 15 </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4</span><span style=\"font-family: Cambria Math;\">.Three numbers are in the ratio of 2:3:5. If the sum of the largest and the smallest number is 9800, then what is the difference between the largest and the smallest number?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Kokila;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> 2: 3:5 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2379;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> 9800 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2396;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352; </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2379;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>3600</p>\n", "<p>4200</p>\n", 
                                "<p>3300</p>\n", "<p>2400</p>\n"],
                    options_hi: ["<p>3600</p>\n", "<p>4200</p>\n",
                                "<p>3300</p>\n", "<p>2400</p>\n"],
                    solution_en: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the three numbers = 2x , 3x and 5x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sum of largest and smallest number (7x) = 9800 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Cambria Math;\">x = 1400</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">therefore , the difference between the largest and the smallest number (3x) = 4200 </span></p>\n",
                    solution_hi: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> = 2x, 3x </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 5x </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2379;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> (7x) = 9800 <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><mo>&rArr;</mo></math> </span><span style=\"font-family: \'Cambria Math\';\">x = 1400</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2379;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> (3x) = 4200 </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> The expenditure and savings of a person are in ratio of 4 : 3. His savings increase by <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> of his income and income remains the same. What is the new ratio of his expenditure and savings?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> 4:3 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2338;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2361;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>12 : 17</p>\n", "<p>14 : 17</p>\n", 
                                "<p>13 : 22</p>\n", "<p>15 : 13</p>\n"],
                    options_hi: ["<p>12 : 17</p>\n", "<p>14 : 17</p>\n",
                                "<p>13 : 22</p>\n", "<p>15 : 13</p>\n"],
                    solution_en: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Income : expenditure : savings</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Initial &rarr;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; 7&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;3 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Change &rarr;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;- 1.4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;+ 1.4 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">---------------------------------------------------- </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Final &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\"> 7&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;2.6&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 4.4 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">His savings increased <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac><mi>t</mi><mi>h</mi></math></span><span style=\"font-family: Cambria Math;\"> of their total income and income remains same</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Therefore, the new ratio of his expenditure and savings = 2.6 : 4.4 = 13 : 22</span></p>\n",
                    solution_hi: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> &rarr;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Kokila;\">&#2310;&#2351;&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\"> :&nbsp; &nbsp; </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> &rarr;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; 7&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;3 </span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;<span style=\"font-family: Cambria Math;\">&rarr;</span></span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">- 1.4&nbsp; &nbsp; &nbsp; &nbsp; + 1.4 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">---------------------------------------</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> &rarr;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 7&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 2.6&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;4.4 </span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2338;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2361;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 2.6 : 4.4 = 13 : 22</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> A family consists of a father, mother and son. The ratio of their present ages is 8: 5:2 and the present age of the father is 48 years. What will be the average age of the family after 3 years?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 8 : 5 : 2 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> 48 </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2358;&#2381;&#2330;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> ? </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>30 years</p>\n", "<p>31 years</p>\n", 
                                "<p>33 years</p>\n", "<p>35 years</p>\n"],
                    options_hi: ["<p>30 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p>31 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n",
                                "<p>33 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p>35 <span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n"],
                    solution_en: "<p>6.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the ages of the father, mother and his son is 8x , 5x , 2x respectively </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Age of father (8x) = 48 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: \'Cambria Math\';\">x = 6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, ages of father mother and his son is 48 years , 30 years and 12 years respectively</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">After three years average of the family </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>48</mn><mo>+</mo><mn>30</mn><mo>+</mo><mn>12</mn><mo>+</mo><mo>(</mo><mn>3</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>99</mn><mn>3</mn></mfrac><mo>&nbsp;</mo></math>=33 years</span></p>\n",
                    solution_hi: "<p>6.(c)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 8x, 5x, 2x </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> (8x) = 48<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Cambria Math;\">x = 6</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 48 </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\">, 30 </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2340;&#2368;&#2344; &#2357;&#2352;&#2381;&#2359; &#2348;&#2366;&#2342; &#2346;&#2352;&#2367;&#2357;&#2366;&#2352; &#2325;&#2366; &#2324;&#2360;&#2340;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>48</mn><mo>+</mo><mn>30</mn><mo>+</mo><mn>12</mn><mo>+</mo><mo>(</mo><mn>3</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>99</mn><mn>3</mn></mfrac><mo>&nbsp;</mo></math> = 33 <span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2359;</span></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> Weight of Ayush and Abhishek are in the ratio of 8 : 5. Abhishek\'s weight increases by 40 percent and total weight of Ayush and Abhishek both increases by 60 percent. If total weight becomes 104 kg, then what is the weight of Ayush after the increment?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2367;&#2359;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> 8 : 5 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2367;&#2359;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> 40 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2338;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2367;&#2359;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> 60 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2338;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> 104 kg </span><span style=\"font-family: Kokila;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>60 kg</p>\n", "<p>75 kg</p>\n", 
                                "<p>69 kg</p>\n", "<p>72 kg</p>\n"],
                    options_hi: ["<p>60 kg</p>\n", "<p>75 kg</p>\n",
                                "<p>69 kg</p>\n", "<p>72 kg</p>\n"],
                    solution_en: "<p>7.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let , weight of Ayush and abhishek be 8x</span><span style=\"font-family: Cambria Math;\"> and 5x</span><span style=\"font-family: Cambria Math;\"> respectively .</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Increased weight of Abhishek = <span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> &times;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>100</mn></mfrac></math><span style=\"font-weight: 400;\"> = 7</span><span style=\"font-weight: 400;\">x</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Overall increased weight = (<span style=\"font-weight: 400;\">8x + 5x) </span><span style=\"font-weight: 400;\">&times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>160</mn><mn>100</mn></mfrac></math><span style=\"font-weight: 400;\">= 20.8 </span><span style=\"font-weight: 400;\">x</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question , </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">20.8 x</span><span style=\"font-family: Cambria Math;\"> = 104 kg. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Cambria Math;\">x&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 5 kg.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Increased weight of Ayush <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math> </span><span style=\"font-family: Cambria Math;\">20.8x</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> 7</span><span style=\"font-weight: 400;\">x</span></span><span style=\"font-family: Cambria Math;\"> = 13.8x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13.8x</span><span style=\"font-family: Cambria Math;\"> = 13.8 &times; 5 = 69 kg.</span></p>\n",
                    solution_hi: "<p>7.(c)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2369;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2367;&#2359;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 8x</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\">&nbsp; 5x</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2349;&#2367;&#2359;&#2375;&#2325; &#2325;&#2366; &#2348;&#2338;&#2364;&#2366; &#2361;&#2369;&#2310; &#2357;&#2332;&#2344;</span> = <span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> &times;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>100</mn></mfrac></math><span style=\"font-weight: 400;\"> = 7</span><span style=\"font-weight: 400;\">x</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2350;&#2327;&#2381;&#2352; &#2348;&#2338;&#2364;&#2366; &#2361;&#2369;&#2310; &#2357;&#2332;&#2344;</span> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math> (<span style=\"font-weight: 400;\">8x + 5x) </span><span style=\"font-weight: 400;\">&times; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>160</mn><mn>100</mn></mfrac></math><span style=\"font-weight: 400;\">= 20.8 </span><span style=\"font-weight: 400;\">x</span></span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; ,&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">20.8x</span><span style=\"font-family: Cambria Math;\"> = 104 kg. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Cambria Math;\">x&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 5 kg.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2310;&#2351;&#2369;&#2359; &#2325;&#2366; &#2348;&#2338;&#2364;&#2366; &#2361;&#2369;&#2310; &#2357;&#2332;&#2344;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math> </span><span style=\"font-family: Cambria Math;\">20.8x</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> 7</span><span style=\"font-weight: 400;\">x</span></span><span style=\"font-family: Cambria Math;\"> = 13.8x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13.8x</span><span style=\"font-family: Cambria Math;\"> = 13.8 &times; 5 = 69 kg.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">The ratio of number of boys and girls in a college is 3 : 2. If 40 boys leave the college and 40 girls join the college, then their ratio will becomes 2: 3. What is the initial number of boys in the college?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2354;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 3 : 2 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 40 </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2354;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2379;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2375;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 40 </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2354;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2352;&#2381;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 2:3 </span><span style=\"font-family: Kokila;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2354;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>120</p>\n", "<p>150</p>\n", 
                                "<p>225</p>\n", "<p>75</p>\n"],
                    options_hi: ["<p>120</p>\n", "<p>150</p>\n",
                                "<p>225</p>\n", "<p>75</p>\n"],
                    solution_en: "<p>8.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the no. of boys and girls in the </span><span style=\"font-family: Cambria Math;\">college </span><span style=\"font-family: Cambria Math;\">be 3x and 2x respectively</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then, according to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>40</mn></mrow><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>40</mn></mrow></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Cambria Math;\">5x = 200 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math></span><span style=\"font-family: \'Cambria Math\';\">x = 40</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, initial no. of boys in the </span><span style=\"font-family: Cambria Math;\">college </span><span style=\"font-family: Cambria Math;\">(3x) = 120</span></p>\n",
                    solution_hi: "<p>8.(a)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2354;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 3x&nbsp; </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\">&nbsp; 2x </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>40</mn></mrow><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>40</mn></mrow></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Cambria Math;\">5x = 200 <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><mo>&rArr;</mo></math></span><span style=\"font-family: \'Cambria Math\';\">x = 40</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2354;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2337;&#2364;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> (3x) = 120</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9</span><span style=\"font-family: Cambria Math;\">.The ratio of income of Jitendra, and Narendra is 4 : 7 and the ratio of their expenditure is 6: 5 respectively. If Jitendra saves Rs.400 out of Rs.2,800 income, then find the savings of Narendra.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2340;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 4 : 7 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 6 : 5 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2340;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> Rs. 2,800 </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> Rs.400 </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>Rs. 2,900</p>\n", "<p>Rs. 2,000</p>\n", 
                                "<p>Rs. 3,900</p>\n", "<p>Rs, 4,900</p>\n"],
                    options_hi: ["<p>Rs. 2,900</p>\n", "<p>Rs. 2,000</p>\n",
                                "<p>Rs. 3,900</p>\n", "<p>Rs, 4,900</p>\n"],
                    solution_en: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the income of jitendra and narendra = 4x and 7x </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">And expenditure of jitendra and narendra = 6y and 5y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4x = 2800 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: \'Cambria Math\';\">x = 700</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Savings of jitendra <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> 4x - 6y = 400 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: \'Cambria Math\';\">y = 400</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Savings of narendra <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math> </span><span style=\"font-family: Cambria Math;\">7x - 5y = 4900 - 2000 = 2900 Rs.</span></p>\n",
                    solution_hi: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2340;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 4x </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 7x</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2340;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> = 6y </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 5y</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4x = 2800 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math></span><span style=\"font-family: \'Cambria Math\';\">x = 700</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2340;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> &rArr; </span><span style=\"font-family: Cambria Math;\">4x - 6y = 400 &rArr; </span><span style=\"font-family: \'Cambria Math\';\">y = 400</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> &rArr; </span><span style=\"font-family: Cambria Math;\">7x - 5y = 4900 - 2000 = 2900 Rs.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Cambria Math;\">50% of A is equal to 80% of B. What is the ratio of A and B?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10. A </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">50% , B </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 80% </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> | A </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>5 : 8</p>\n", "<p>5 : 4</p>\n", 
                                "<p>4 : 5</p>\n", "<p>8 : 5</p>\n"],
                    options_hi: ["<p>5 : 8</p>\n", "<p>5 : 4</p>\n",
                                "<p>4 : 5</p>\n", "<p>8 : 5</p>\n"],
                    solution_en: "<p>10.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><span style=\"font-weight: 400;\">A</span><span style=\"font-weight: 400;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math><span style=\"font-weight: 400;\">B</span><span style=\"font-weight: 400;\"> </span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">A : B </span><span style=\"font-weight: 400;\">=8 : 5</span></p>\n",
                    solution_hi: "<p>10. (d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><span style=\"font-weight: 400;\">A</span><span style=\"font-weight: 400;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math><span style=\"font-weight: 400;\">B</span><span style=\"font-weight: 400;\"> </span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">A : B </span><span style=\"font-weight: 400;\">=8 : 5</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11.</span><span style=\"font-family: Cambria Math;\"> If P : Q = 1 : 3, Q : R = 3 : 4 and R : S = 2 : 1, then what is the value of P : S ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">P : Q = 1 : 3, Q : R = 3 : 4 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> R : S = 2 : 1, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> P : S </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>3 : 1</p>\n", "<p>2 : 1</p>\n", 
                                "<p>1 : 2</p>\n", "<p>3 : 2</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>3 : 1</p>\n", "<p>2 : 1</p>\n",
                                "<p>1 : 2</p>\n", "<p>3 : 2</p>\n"],
                    solution_en: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">P&nbsp; &nbsp; &nbsp; &nbsp;Q&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; R&nbsp; &nbsp; &nbsp; &nbsp; S</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1&nbsp; &nbsp;:&nbsp; &nbsp;3 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp; :&nbsp; 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2&nbsp; &nbsp; &nbsp; :&nbsp; 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">--------------------------------</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; 6&nbsp; :&nbsp; 18&nbsp; &nbsp;: 24&nbsp; &nbsp;:&nbsp; &nbsp; 12</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Therefore,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> P : S = 1 : 2 </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">P&nbsp; &nbsp; &nbsp; &nbsp;Q&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; R&nbsp; &nbsp; &nbsp; &nbsp; S</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1&nbsp; &nbsp;:&nbsp; &nbsp;3 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp; :&nbsp; 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">--------------------------------</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; 6&nbsp; :&nbsp; 18&nbsp; &nbsp;: 24&nbsp; &nbsp;:&nbsp; &nbsp; 12</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> P : S = 1 : 2 </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">The ratio of incomes of P and Q is 1 : 2. Ratio of income of Q and R is 3 : 2. If one-third of P\'s income is Rs. 4400 less than the half of P\'s income, then what is the Q\'s income?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 1:2 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> R </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 3:2 </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2367;&#2361;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2343;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> Rs. 4400 </span><span style=\"font-family: Kokila;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs. 56600</p>\n", "<p>Rs. 52800</p>\n", 
                                "<p>Rs. 46600</p>\n", "<p>Rs. 41200</p>\n"],
                    options_hi: ["<p>Rs. 56600</p>\n", "<p>Rs. 52800</p>\n",
                                "<p>Rs. 46600</p>\n", "<p>Rs. 41200</p>\n"],
                    solution_en: "<p>12.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let income of P = x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>x + 4400 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>x= 4400 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = Rs. 26,400</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mi>Q</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&rArr;Q = 26400 &times; 2 = Rs. 52800</span></p>\n",
                    solution_hi: "<p>12.(b)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> = x</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>x + 4400 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>x= 4400 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 26,400 <span style=\"font-weight: 400;\">&#2352;&#2369;&#2346;&#2351;&#2375;&#2404; </span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mi>Q</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&rArr; Q = 26400 &times; 2 = 52800 <span style=\"font-weight: 400;\">&#2352;&#2369;&#2346;&#2351;&#2375;&#2404; </span></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\"> The incomes of A and B are in the ratio 4 : 5 and their expenditures are in the ratio 5 : 4. If A saves Rs.6000 and B saves Rs 12000, then what will be the income of A ? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> 4 : 5 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> 5 : 4 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A, Rs.6000 </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B, Rs.12000 </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs. 18000</p>\n", "<p>Rs. 20000</p>\n", 
                                "<p>Rs. 12000</p>\n", "<p>Rs. 16000</p>\n"],
                    options_hi: ["<p>Rs. 18000</p>\n", "<p>Rs. 20000</p>\n",
                                "<p>Rs. 12000</p>\n", "<p>Rs. 16000</p>\n"],
                    solution_en: "<p>13.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio &rarr;&nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; A&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; B&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Income &rarr;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp;(4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 5 </span><span style=\"font-family: Cambria Math;\">) &times; 2 </span><span style=\"font-family: Cambria Math;\">= 8 : 10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Expenditure &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5&nbsp; :&nbsp; &nbsp; 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">---------------------------------</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Saving &rarr; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">3&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 6 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the questions , </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 unit = 6000 &#8377; &amp; 6 unit = </span><span style=\"font-family: Cambria Math;\">12000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Income of A (8 units) = 16000 &#8377;</span></p>\n",
                    solution_hi: "<p>13.(d)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math> &nbsp; </span><span style=\"font-family: Cambria Math;\">A&nbsp; &nbsp;:&nbsp; &nbsp; B </span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math> &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">( 4&nbsp; &nbsp;:&nbsp; &nbsp; 5 </span><span style=\"font-family: Cambria Math;\">)&times;2 </span><span style=\"font-family: Cambria Math;\">= 8 : 10</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math>&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">5&nbsp; &nbsp;:&nbsp; &nbsp; 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">---------------------------------</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> &nbsp; &rarr;&nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">3&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;6 </span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Kokila;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> = 6000 &#8377; &amp; 6 </span><span style=\"font-family: Kokila;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">12000 </span><span style=\"font-family: Cambria Math;\">&#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> (8 </span><span style=\"font-family: Kokila;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\">) = 16000 &#8377;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> If the difference between the two numbers is 20 percent of their sum, then what is the ratio of larger number and smaller number ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2379;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>6 : 5</p>\n", "<p>5 : 4</p>\n", 
                                "<p>4 : 3</p>\n", "<p>3 : 2</p>\n"],
                    options_hi: ["<p>6 : 5</p>\n", "<p>5 : 4</p>\n",
                                "<p>4 : 3</p>\n", "<p>3 : 2</p>\n"],
                    solution_en: "<p>14.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the </span><span style=\"font-family: Cambria Math;\">larger </span><span style=\"font-family: Cambria Math;\">number be x </span><span style=\"font-family: Cambria Math;\">and the </span><span style=\"font-family: Cambria Math;\">smaller </span><span style=\"font-family: Cambria Math;\">number be y . </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x - y =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>100</mn></mfrac></math>&times; (x+y)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x - y = 1 &hellip;&hellip;.e.q .(1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x + y = 5 &hellip;&hellip;.e.q .(2)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">On solving e.q .(1) and (2); we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 3 and y = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">required ratio, x </span><span style=\"font-family: Cambria Math;\">: y = 3 : 2</span></p>\n",
                    solution_hi: "<p>14.(d)</p>\r\n<p><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2379;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x - y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>100</mn></mfrac></math>&times; (x+y)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x - y = 1 &hellip;&hellip;.</span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">(1)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x + y = 5 &hellip;&hellip;.</span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">(2)</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> (1) </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> (2) </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">; </span><span style=\"font-family: Kokila;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 3 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> y = 2</span></p>\r\n<p><span style=\"font-family: Kokila;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\">, x </span><span style=\"font-family: Cambria Math;\">: y = 3 : 2</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15.</span><span style=\"font-family: Cambria Math;\"> If a : b : c = 2 : 3 : 5 and 5b - a + 2c = 115, then what is the value of b ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Kokila;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> a : b : c = 2 : 3 : 5 </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 5b - a + 2c = 115, </span><span style=\"font-family: Kokila;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> b </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\n",
                    options_en: ["<p>15</p>\n", "<p>12</p>\n", 
                                "<p>6</p>\n", "<p>24</p>\n"],
                    options_hi: ["<p>15</p>\n", "<p>12</p>\n",
                                "<p>6</p>\n", "<p>24</p>\n"],
                    solution_en: "<p>15.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">a : b : c = 2 : 3 : 5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5b - a + 2c = 115</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 &times; 3x - 2x + 2&times; 5x = 115</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Value of b (3x</span><span style=\"font-family: Cambria Math;\">) = 15</span></p>\n",
                    solution_hi: "<p>15.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">a : b : c = 2 : 3 : 5 </span></p>\r\n<p><span style=\"font-weight: 400;\">5b - a + 2c = 115</span></p>\r\n<p><span style=\"font-weight: 400;\">5 &times; 3x - 2x + 2 &times; 5x = 115</span></p>\r\n<p><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> = 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">b </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> (3x</span><span style=\"font-family: Cambria Math;\">) = 15</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>