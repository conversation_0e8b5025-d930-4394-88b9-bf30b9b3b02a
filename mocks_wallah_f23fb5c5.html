<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">90:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: " <p>1. Select the most appropriate ANTONYM of the given word. </span></p> <p><span style=\"font-family:Times New Roman\">Traditional </span></p>",
                    question_hi: "",
                    options_en: [" <p> Earthly </span></p>", " <p> Customary </span></p>", 
                                " <p> Modern </span></p>", " <p> Conventional </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>1.(c) Modern</span></p> <p><span style=\"font-family:Times New Roman\">Modern- </span><span style=\"font-family:Times New Roman\">of the present or recent times</span></p> <p><span style=\"font-family:Times New Roman\">Traditional- following or belonging to the customs or ways of behaving that have continued in a group of people. </span></p> <p><span style=\"font-family:Times New Roman\">Earthly- happening in or related to the physical world or real life. </span></p> <p><span style=\"font-family:Times New Roman\">Customary- </span><span style=\"font-family:Times New Roman\">according to custom, usual</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Conventional- </span><span style=\"font-family:Times New Roman\">always behaving in a traditional or normal way</span></p>",
                    solution_hi: " <p>1.(c) Modern</span></p> <p><span style=\"font-family:Times New Roman\">Modern- </span><span style=\"font-family:Times New Roman\">of the present or recent times</span></p> <p><span style=\"font-family:Times New Roman\">Traditional- following or belonging to the customs or ways of behaving that have continued in a group of people. </span></p> <p><span style=\"font-family:Times New Roman\">Earthly- happening in or related to the physical world or real life. </span></p> <p><span style=\"font-family:Times New Roman\">Customary- </span><span style=\"font-family:Times New Roman\">according to custom, usual</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Conventional- </span><span style=\"font-family:Times New Roman\">always behaving in a traditional or normal way</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: " <p>2. Select the option which means the same as the group of words given. </span></p> <p><span style=\"font-family:Times New Roman\">A hunting weapon which comes back, used by Australian Aborigines </span></p>",
                    question_hi: "",
                    options_en: [" <p> harpoon </span></p>", " <p> cutlass </span></p>", 
                                " <p> slingshot </span></p>", " <p> boomerang </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>(d) Boomerang</span></p> <p><span style=\"font-family:Times New Roman\">Boomerang- A hunting weapon which comes back, used by Australian Aborigines </span></p> <p><span style=\"font-family:Times New Roman\">Harpoon- </span><span style=\"font-family:Times New Roman\">a long thin weapon with a sharp pointed end and a rope tied to it that is used to catch large sea animals (whales)</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Cutlass- </span><span style=\"font-family:Times New Roman\">a short sword with a slightly curved blade, formerly used by sailors.</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Slingshot- </span><span style=\"font-family:Times New Roman\">a forked stick</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    solution_hi: " <p>(d) Boomerang</span></p> <p><span style=\"font-family:Times New Roman\">Boomerang- A hunting weapon which comes back, used by Australian Aborigines </span></p> <p><span style=\"font-family:Times New Roman\">Harpoon- </span><span style=\"font-family:Times New Roman\">a long thin weapon with a sharp pointed end and a rope tied to it that is used to catch large sea animals (whales)</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Cutlass- </span><span style=\"font-family:Times New Roman\">a short sword with a slightly curved blade, formerly used by sailors.</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Slingshot- </span><span style=\"font-family:Times New Roman\">a forked stick</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. The following sentence has been split into four segments. Identify the segment that <span style=\"font-family: Times New Roman;\">contains a grammatical error. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">It would be / more better if / she sang / with an orchestra. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>more better if</p>\n", "<p>she sang</p>\n", 
                                "<p>with an orchestra</p>\n", "<p>It would be</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>(a) <span style=\"font-family: Times New Roman;\">When we do a comparison between two things, we don&rsquo;t use &lsquo;more&rsquo; with a comparative degree(better) because it becomes a case of superfluousness(not necessary). Hence, &lsquo;it would be better if&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>(a) <span style=\"font-family: Times New Roman;\">When we do a comparison between two things, we don&rsquo;t use &lsquo;more&rsquo; with a comparative degree(better) because it becomes a case of superfluousness(not necessary). Hence, &lsquo;it would be better if&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: " <p>4. Select the option that expresses the given sentence in reported speech.  </span></p> <p><span style=\"font-family:Times New Roman\">\"Our teacher returned from Mumbai last month\", she said. </span></p>",
                    question_hi: "",
                    options_en: [" <p> She said that her teacher had returned from Mumbai the earlier month. </span></p>", " <p> Her teacher returned from Mumbai last month, she said. </span></p>", 
                                " <p> She said that their teacher had returned from Mumbai the previous month. </span></p>", " <p> She said that her teacher had returned from Mumbai the previous month. </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>4.(c) She said that their teacher had returned from Mumbai the previous month. (Correct)</span></p>",
                    solution_hi: " <p>4.(c) She said that their teacher had returned from Mumbai the previous month. (Correct)</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the correct passive form of the given sentence.</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the evening we water the plants. </span></p>",
                    question_hi: "",
                    options_en: ["<p>The plants will be watered in the evening.</p>", "<p>The plants are watered in the evening.</p>", 
                                "<p>The plants have been watered in the evening.</p>", "<p>The plants were watered in the evening.</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>5.(b) The plants are watered in the evening. (Correct)</p>\r\n<p><span style=\"font-weight: 400;\">The plants </span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\">will</span> </span><span style=\"font-weight: 400;\">be watered in the evening. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The plants </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">have been</span></span><span style=\"font-weight: 400;\"> watered in the evening. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The plants </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">were</span></span><span style=\"font-weight: 400;\"> watered in the evening. (Incorrect Tense)</span></p>",
                    solution_hi: "<p>5.(b) The plants are watered in the evening. (Correct)</p>\r\n<p><span style=\"font-weight: 400;\">The plants </span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\">will</span> </span><span style=\"font-weight: 400;\">be watered in the evening. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The plants </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">have been</span></span><span style=\"font-weight: 400;\"> watered in the evening. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">The plants </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">were</span></span><span style=\"font-weight: 400;\"> watered in the evening. (Incorrect Tense)</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6. Select the option that can be used as a one-word substitute for the given group of words.  </span></p> <p><span style=\"font-family:Times New Roman\">Causing a strong dislike </span></p>",
                    question_hi: "",
                    options_en: [" <p> Redundant </span></p>", " <p> Repulsive </span></p>", 
                                " <p> Hateful </span></p>", " <p> Revealing </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>(b) Repulsive</span></p> <p><span style=\"font-family:Times New Roman\">Repulsive- Causing a strong dislike  </span></p> <p><span style=\"font-family:Times New Roman\">Redundant- </span><span style=\"font-family:Times New Roman\">no longer needed for a job and therefore out of work</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Hateful- </span><span style=\"font-family:Times New Roman\">extremely unpleasant</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Revealing- </span><span style=\"font-family:Times New Roman\">allowing something to be known that was secret or unknown before</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    solution_hi: " <p>(b) Repulsive</span></p> <p><span style=\"font-family:Times New Roman\">Repulsive- Causing a strong dislike  </span></p> <p><span style=\"font-family:Times New Roman\">Redundant- </span><span style=\"font-family:Times New Roman\">no longer needed for a job and therefore out of work</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Hateful- </span><span style=\"font-family:Times New Roman\">extremely unpleasant</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Revealing- </span><span style=\"font-family:Times New Roman\">allowing something to be known that was secret or unknown before</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate option to substitute the underlined segment in the given <span style=\"font-family: Times New Roman;\">sentence. If there is no need to substitute it, select &lsquo;No substitution&rsquo;. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Mr. Sharma is </span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Times New Roman;\">kind as</span></strong></span><span style=\"font-family: Times New Roman;\"> his father. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>as kind as</p>\n", "<p>No substitution</p>\n", 
                                "<p>the kindest as</p>\n", "<p>kinder as</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>7.(a) As kind as</p>\r\n<p><span style=\"font-family: Times New Roman;\">The phrase &lsquo;as___as&rsquo; is used with an adjective or adverb to show similarity or equality of one thing with another. The given sentence states the similarity between Mr. Sharma and his father. Hence, &lsquo;Mr. Sharma is as kind as his father&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>7.(a) As kind as</p>\r\n<p><span style=\"font-family: Times New Roman;\">The phrase &lsquo;as___as&rsquo; is used with an adjective or adverb to show similarity or equality of one thing with another. The given sentence states the similarity between Mr. Sharma and his father. Hence, &lsquo;Mr. Sharma is as kind as his father&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: " <p>8. Select the most appropriate meaning of the given idiom.  </span></p> <p><span style=\"font-family:Times New Roman\">Not one’s cup of tea </span></p>",
                    question_hi: "",
                    options_en: [" <p> Not absolutely true </span></p>", " <p> Not according to one’s interest </span></p>", 
                                " <p> Beyond one’s comprehension </span></p>", " <p> Of no concern for someone </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>8.(b) Not one’s cup of tea- not according to one’s interest  </span></p> <p><span style=\"font-family:Times New Roman\">Example: Playing cricket was never my cup of tea since childhood.</span></p>",
                    solution_hi: " <p>8.(b) Not one’s cup of tea- not according to one’s interest  </span></p> <p><span style=\"font-family:Times New Roman\">Example: Playing cricket was never my cup of tea since childhood.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Given below are four jumbled sentences. Out of the given options, pick the one that gives <span style=\"font-family: Times New Roman;\">their correct order. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A. The man who had caught me was a dreadful sight. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B. Suddenly I was picked up, whirled through the air, and made to sit on a tombstone. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">C. He was about forty years old, hard-faced and fierce. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">D. I was only a child, and small for my age. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>CBAD</p>\n", "<p>BACD</p>\n", 
                                "<p>DBAC</p>\n", "<p>DCBA</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>9.(c) DBAC</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence D will be the starting line because it contains the main idea of the parajumble i.e. the phase of childhood. However, Sentence B states that suddenly the narrator was picked up, whirled through the air, and made to sit on a tombstone. So, B will follow D. Further, Sentence A states that the man who had caught the narrator was a dreadful sight &amp; Sentence C states that he was about forty years old. So, C will follow A. Going through the options, option c has the correct sequence. </span></p>\n",
                    solution_hi: "<p>9.(c) DBAC</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence D will be the starting line because it contains the main idea of the parajumble i.e. the phase of childhood. However, Sentence B states that suddenly the narrator was picked up, whirled through the air, and made to sit on a tombstone. So, B will follow D. Further, Sentence A states that the man who had caught the narrator was a dreadful sight &amp; Sentence C states that he was about forty years old. So, C will follow A. Going through the options, option c has the correct sequence. </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the <span style=\"font-family: Times New Roman;\">right order to form a meaningful and coherent paragraph. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A. The whole area has been designed like a garden from Alice in Wonderland. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B. It&rsquo;s about having fun and learning, too; all the activities help to explain the modern world </span><span style=\"font-family: Times New Roman;\">of technology to the young visitors. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">C. But &lsquo;The World of Children&rsquo; isn&rsquo;t just about playing games and having joy rides. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">D. There are several different attractions in this vast area and there is a lot to see and do. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>DACB</p>\n", "<p>BCDA</p>\n", 
                                "<p>CBDA</p>\n", "<p>ADBC</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>10.(a) DACB</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence D will be the starting line because it contains the main idea of the parajumble i.e. several different attractions. However, Sentence A states that the whole area has been designed like a garden. So, A will follow D. Further, Sentence C states that &lsquo;The World of Children&rsquo; isn&rsquo;t just about playing games &amp; Sentence B states that it&rsquo;s about having fun and learning. So, B will follow C. Going through the options, option a has the correct sequence. </span></p>\n",
                    solution_hi: "<p>10.(a) DACB</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence D will be the starting line because it contains the main idea of the parajumble i.e. several different attractions. However, Sentence A states that the whole area has been designed like a garden. So, A will follow D. Further, Sentence C states that &lsquo;The World of Children&rsquo; isn&rsquo;t just about playing games &amp; Sentence B states that it&rsquo;s about having fun and learning. So, B will follow C. Going through the options, option a has the correct sequence. </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the INCORRECTLY spelt word.</p>\n",
                    question_hi: "",
                    options_en: ["<p>rogue</p>\n", "<p>rocket</p>\n", 
                                "<p>rockiry</p>\n", "<p>roaster</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>11.(c) Rockery(<span style=\"font-family: Times New Roman;\">a heaped arrangement of rough stones with soil between them, planted with rock plants, especially alpines)</span><span style=\"font-family: Times New Roman;\"> is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">rogue - a dishonest or unprincipled person</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">roaster - a container, oven, furnace, or apparatus for roasting something</span></p>\n",
                    solution_hi: "<p>11.(c) Rockery(<span style=\"font-family: Times New Roman;\">a heaped arrangement of rough stones with soil between them, planted with rock plants, especially alpines)</span><span style=\"font-family: Times New Roman;\"> is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">rogue - a dishonest or unprincipled person</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">roaster - a container, oven, furnace, or apparatus for roasting something</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: " <p>12. Select the most appropriate option to fill in the blank.  </span></p> <p><span style=\"font-family:Times New Roman\">Scientists all over the world are engaged in ______ how to find a vaccine for COVID-19. </span></p>",
                    question_hi: "",
                    options_en: [" <p> researching </span></p>", " <p> scrutinizing </span></p>", 
                                " <p> investigating </span></p>", " <p> inspecting </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>12.(c) Investigating means trying to find out all the facts about something. The given sentence states that scientists all over the world are engaged in </span><span style=\"font-family:Times New Roman\">trying to find out all the facts about</span><span style=\"font-family:Times New Roman\"> a vaccine for COVID-19. Hence, ‘investigating’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p>12.(c) Investigating means trying to find out all the facts about something. The given sentence states that scientists all over the world are engaged in </span><span style=\"font-family:Times New Roman\">trying to find out all the facts about</span><span style=\"font-family:Times New Roman\"> a vaccine for COVID-19. Hence, ‘investigating’ is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: " <p>13. Select the most appropriate synonym of the given word.  </span></p> <p><span style=\"font-family:Times New Roman\">REPULSIVE </span></p>",
                    question_hi: "",
                    options_en: [" <p> respectable </span></p>", " <p> disgusting </span></p>", 
                                " <p> attractive </span></p>", " <p> agreeable </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>13.(b) Disgusting</span></p> <p><span style=\"font-family:Times New Roman\">Disgusting- </span><span style=\"font-family:Times New Roman\">very unpleasant</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Respectable- </span><span style=\"font-family:Times New Roman\">considered by society to be good, proper or correct</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Attractive- </span><span style=\"font-family:Times New Roman\">that pleases or interests you</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Agreeable- </span><span style=\"font-family:Times New Roman\">ready to agree</span></p>",
                    solution_hi: " <p>13.(b) Disgusting</span></p> <p><span style=\"font-family:Times New Roman\">Disgusting- </span><span style=\"font-family:Times New Roman\">very unpleasant</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Respectable- </span><span style=\"font-family:Times New Roman\">considered by society to be good, proper or correct</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Attractive- </span><span style=\"font-family:Times New Roman\">that pleases or interests you</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Agreeable- </span><span style=\"font-family:Times New Roman\">ready to agree</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate option to substitute the underlined segment in the given <span style=\"font-family: Times New Roman;\">sentence. If there is no need to substitute it, select \'No substitution required\'. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">He won&rsquo;t come to the party unless you </span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Times New Roman;\">will invite</span></span></strong><span style=\"font-family: Times New Roman;\"> him personally. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>would invite</p>\n", "<p>invite</p>\n", 
                                "<p>No substitution required</p>\n", "<p>invited</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>14.(b) Unless is followed by simple present tense. He won&rsquo;t come to the party unless you invite him personally will be the most appropriate answer.</p>\n",
                    solution_hi: "<p>14.(b) Unless is followed by simple present tense. He won&rsquo;t come to the party unless you invite him personally will be the most appropriate answer.</p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: " <p>15. Select the most appropriate meaning of the idiomatic expression given.  </span></p> <p><span style=\"font-family:Times New Roman\">To cross one’s mind </span></p>",
                    question_hi: "",
                    options_en: [" <p> To forget something </span></p>", " <p> To put a thought away from one’s mind </span></p>", 
                                " <p> To move from one idea to another </span></p>", " <p> To think of something </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>15.(d) To cross one’s mind- to think of something </span></p> <p><span style=\"font-family:Times New Roman\">Example: When I was preparing for my presentation a good idea just crossed my mind. </span></p>",
                    solution_hi: " <p>15.(d) To cross one’s mind- to think of something </span></p> <p><span style=\"font-family:Times New Roman\">Example: When I was preparing for my presentation a good idea just crossed my mind. </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: " <p>16. Select the most appropriate word to fill in the blank.  </span></p> <p><span style=\"font-family:Times New Roman\">The marking of answer papers is ______ out by the course teacher. </span></p>",
                    question_hi: "",
                    options_en: [" <p> done </span></p>", " <p> carried </span></p>", 
                                " <p> checked </span></p>", " <p> crossed </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>16.(b) Carried means to officially approve of something. The phrase carried out means to do a particular piece of work. The given sentence states that the marking of answer papers is carried out by the course teacher. Hence, ‘carried’ is the most appropriate answer.</span></p>",
                    solution_hi: " <p>16.(b) Carried means to officially approve of something. The phrase carried out means to do a particular piece of work. The given sentence states that the marking of answer papers is carried out by the course teacher. Hence, ‘carried’ is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: " <p>17. Select the most appropriate ANTONYM of the given word.  </span></p> <p><span style=\"font-family:Times New Roman\">BUOYANT </span></p>",
                    question_hi: "",
                    options_en: [" <p> enthusiastic </span></p>", " <p> lively </span></p>", 
                                " <p> weighted </span></p>", " <p> bouncy </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>17.(c) Weighted</span></p> <p><span style=\"font-family:Times New Roman\">Weighted- T</span><span style=\"font-family:Times New Roman\">o hold something down with a heavy object or objects</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Buoyant- </span><span style=\"font-family:Times New Roman\">Floating or able to float or able to keep things floating</span></p> <p><span style=\"font-family:Times New Roman\">Enthusiastic- </span><span style=\"font-family:Times New Roman\">Full of excitement and interest in something</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Lively- </span><span style=\"font-family:Times New Roman\">Full of energy, interest, excitement, etc.</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Bouncy- </span><span style=\"font-family:Times New Roman\"> Full of energy, lively</span></p>",
                    solution_hi: " <p>17.(c) Weighted</span></p> <p><span style=\"font-family:Times New Roman\">Weighted- T</span><span style=\"font-family:Times New Roman\">o hold something down with a heavy object or objects</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Buoyant- </span><span style=\"font-family:Times New Roman\">Floating or able to float or able to keep things floating</span></p> <p><span style=\"font-family:Times New Roman\">Enthusiastic- </span><span style=\"font-family:Times New Roman\">Full of excitement and interest in something</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Lively- </span><span style=\"font-family:Times New Roman\">Full of energy, interest, excitement, etc.</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Bouncy- </span><span style=\"font-family:Times New Roman\"> Full of energy, lively</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the INCORRECTLY spelt word</p>\n",
                    question_hi: "",
                    options_en: ["<p>Terrible</p>\n", "<p>Uprooted</p>\n", 
                                "<p>Haunted</p>\n", "<p>Tendansy</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>18.(d) Tendency(<span style=\"font-family: Times New Roman;\">something that a person or thing usually does; a way of behaving)</span><span style=\"font-family: Times New Roman;\"> is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Terrible - extremely bad or serious</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Uprooted - pull (something, especially a tree or plant) out of the ground</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Haunted - inhabited or frequented by ghosts</span></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>18.(d) Tendency(<span style=\"font-family: Times New Roman;\">something that a person or thing usually does; a way of behaving)</span><span style=\"font-family: Times New Roman;\"> is the correct spelling</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Terrible - extremely bad or serious</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Uprooted - pull (something, especially a tree or plant) out of the ground</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Haunted - inhabited or frequented by ghosts</span></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. In the given sentence, identify the segment which contains the grammatical error.</p>\r\n<p><span style=\"font-family: Times New Roman;\">Cyclone Amphan roared into West Bengal on Wednesday leave behind a trail of devastation </span><span style=\"font-family: Times New Roman;\">across a large area of the State. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>leave behind</p>\n", "<p>a trail of devastation</p>\n", 
                                "<p>roared into West Bengal</p>\n", "<p>across a large area</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>19.(a) The later part of the sentence must be written in the past tense as the first part (roared) is in past tense.So the usage of leave (1st form of the verb) is incorrect.</p>\n",
                    solution_hi: "<p>19.(a) The later part of the sentence must be written in the past tense as the first part (roared) is in past tense.So the usage of leave (1st form of the verb) is incorrect.</p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: " <p>20. Select the most appropriate synonym of the given word.  </span></p> <p><span style=\"font-family:Times New Roman\">Ban </span></p>",
                    question_hi: "",
                    options_en: [" <p> Ascertain </span></p>", " <p> Prohibit </span></p>", 
                                " <p> Abandon </span></p>", " <p> Band </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>20.(b)Prohibit</span></p> <p><span style=\"font-family:Times New Roman\">Ban- </span><span style=\"font-family:Times New Roman\">to say that something is not allowed by law; to forbid</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Ascertain- </span><span style=\"font-family:Times New Roman\">to find something out</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Abandon- </span><span style=\"font-family:Times New Roman\">to leave somebody/something that you are responsible for</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Band- </span><span style=\"font-family:Times New Roman\">a group of people who do something together</span></p>",
                    solution_hi: " <p>20.(b)Prohibit</span></p> <p><span style=\"font-family:Times New Roman\">Ban- </span><span style=\"font-family:Times New Roman\">to say that something is not allowed by law; to forbid</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Ascertain- </span><span style=\"font-family:Times New Roman\">to find something out</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Abandon- </span><span style=\"font-family:Times New Roman\">to leave somebody/something that you are responsible for</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Band- </span><span style=\"font-family:Times New Roman\">a group of people who do something together</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. Comprehension:</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Times New Roman;\">alternatives given. Select the most appropriate option for each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Every Monday, on his way back from work, Bepin Chaudhury would drop in at Kali Charan&rsquo;s in </span><span style=\"font-family: Times New Roman;\">New Market (1)______ books. Crime stories, ghost stories, and thrillers. (2)______ had to buy at least five at a time to last him (3)______ the week. He lived alone, was not a good mixer, had </span><span style=\"font-family: Times New Roman;\">few (4)______ , and didn\'t like spending time in idle chat. Today, at Kali Charan\'s, Bepin Babu </span><span style=\"font-family: Times New Roman;\">had a (5)______ that somebody was observing him from close quarters. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. (1) </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>buying</p>\n", "<p>bought</p>\n", 
                                "<p>to buy</p>\n", "<p>buy</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>21.(c) To buy</p>\r\n<p><span style=\"font-family: Times New Roman;\">&ldquo;to + V</span><span style=\"font-family: Times New Roman;\">1</span><span style=\"font-family: Times New Roman;\">&rdquo; is grammatically the correct structure for the given sentence. Hence, &lsquo;to buy&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>21.(c) To buy</p>\r\n<p><span style=\"font-family: Times New Roman;\">&ldquo;to + V</span><span style=\"font-family: Times New Roman;\">1</span><span style=\"font-family: Times New Roman;\">&rdquo; is grammatically the correct structure for the given sentence. Hence, &lsquo;to buy&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. Comprehension:</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Times New Roman;\">alternatives given. Select the most appropriate option for each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Every Monday, on his way back from work, Bepin Chaudhury would drop in at Kali Charan&rsquo;s in </span><span style=\"font-family: Times New Roman;\">New Market (1)______ books. Crime stories, ghost stories, and thrillers. (2)______ had to buy at least five at a time to last him (3)______ the week. He lived alone, was not a good mixer, had </span><span style=\"font-family: Times New Roman;\">few (4)______ , and didn\'t like spending time in idle chat. Today, at Kali Charan\'s, Bepin Babu </span><span style=\"font-family: Times New Roman;\">had a (5)______ that somebody was observing him from close quarters. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. (2) </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>They</p>\n", "<p>He</p>\n", 
                                "<p>Him</p>\n", "<p>She</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>22.(b) He</p>\r\n<p><span style=\"font-family: Times New Roman;\">&lsquo;He&rsquo; is a singular pronoun used for a male person which is Bepin Chaudhury in the given passage. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Hence, &lsquo;he&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>22.(b) He</p>\r\n<p><span style=\"font-family: Times New Roman;\">&lsquo;He&rsquo; is a singular pronoun used for a male person which is Bepin Chaudhury in the given passage. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Hence, &lsquo;he&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. Comprehension:</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Times New Roman;\">alternatives given. Select the most appropriate option for each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Every Monday, on his way back from work, Bepin Chaudhury would drop in at Kali Charan&rsquo;s in </span><span style=\"font-family: Times New Roman;\">New Market (1)______ books. Crime stories, ghost stories, and thrillers. (2)______ had to buy at least five at a time to last him (3)______ the week. He lived alone, was not a good mixer, had </span><span style=\"font-family: Times New Roman;\">few (4)______ , and didn\'t like spending time in idle chat. Today, at Kali Charan\'s, Bepin Babu </span><span style=\"font-family: Times New Roman;\">had a (5)______ that somebody was observing him from close quarters. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. (3)</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>within</p>\n", "<p>through</p>\n", 
                                "<p>across</p>\n", "<p>by</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>23.(b) Through</p>\r\n<p><span style=\"font-family: Times New Roman;\">Through means from the beginning to end of something.The given passage states that he had to buy at least five at a time to last him from the beginning to the end of the week. Hence, &lsquo;through&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>23.(b) Through</p>\r\n<p><span style=\"font-family: Times New Roman;\">Through means from the beginning to end of something.The given passage states that he had to buy at least five at a time to last him from the beginning to the end of the week. Hence, &lsquo;through&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. Comprehension:</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Times New Roman;\">alternatives given. Select the most appropriate option for each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Every Monday, on his way back from work, Bepin Chaudhury would drop in at Kali Charan&rsquo;s in </span><span style=\"font-family: Times New Roman;\">New Market (1)______ books. Crime stories, ghost stories, and thrillers. (2)______ had to buy at least five at a time to last him (3)______ the week. He lived alone, was not a good mixer, had </span><span style=\"font-family: Times New Roman;\">few (4)______ , and didn\'t like spending time in idle chat. Today, at Kali Charan\'s, Bepin Babu </span><span style=\"font-family: Times New Roman;\">had a (5)______ that somebody was observing him from close quarters. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. (4) </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>friends</p>\n", "<p>students</p>\n", 
                                "<p>servants</p>\n", "<p>followers</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>24.(a) Friends</p>\r\n<p><span style=\"font-family: Times New Roman;\">The given passage states that he lived alone and was not a good mixer, so he had a few friends. Hence, &lsquo;friends&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>24.(a) Friends</p>\r\n<p><span style=\"font-family: Times New Roman;\">The given passage states that he lived alone and was not a good mixer, so he had a few friends. Hence, &lsquo;friends&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.Comprehension:</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Times New Roman;\">alternatives given. Select the most appropriate option for each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Every Monday, on his way back from work, Bepin Chaudhury would drop in at Kali Charan&rsquo;s in </span><span style=\"font-family: Times New Roman;\">New Market (1)______ books. Crime stories, ghost stories, and thrillers. (2)______ had to buy at least five at a time to last him (3)______ the week. He lived alone, was not a good mixer, had </span><span style=\"font-family: Times New Roman;\">few (4)______ , and didn\'t like spending time in idle chat. Today, at Kali Charan\'s, Bepin Babu </span><span style=\"font-family: Times New Roman;\">had a (5)______ that somebody was observing him from close quarters. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Select the most appropriate option for blank no.(5) </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>intuition</p>\n", "<p>connotation</p>\n", 
                                "<p>feeling</p>\n", "<p>perception</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>25.(c) Feeling</p>\r\n<p><span style=\"font-family: Times New Roman;\">Feeling means something that you feel in your mind or body. The given passage states that Bepin Babu </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">had a feeling in his mind that somebody was observing him from close quarters. Hence, &lsquo;feeling&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>25.(c) Feeling</p>\r\n<p><span style=\"font-family: Times New Roman;\">Feeling means something that you feel in your mind or body. The given passage states that Bepin Babu </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">had a feeling in his mind that somebody was observing him from close quarters. Hence, &lsquo;feeling&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>