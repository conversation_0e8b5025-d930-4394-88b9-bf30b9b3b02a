<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">20:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 20 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. What is the value of [7&divide;14 &times; 8 + (5 &divide; 15 &times; 6 &ndash; 4 &times; 3)] ?</p>\n",
                    question_hi: "<p>1. [7&divide;14 &times; 8 + (5 &divide; 15 &times; 6 &ndash; 4 &times; 3)]<span style=\"font-family: Palanquin Dark;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>&ndash; 4</p>\n", "<p>&ndash; 2</p>\n", 
                                "<p>&ndash; 6</p>\n", "<p>&ndash; 5</p>\n"],
                    options_hi: ["<p>&ndash; 4</p>\n", "<p>&ndash; 2</p>\n",
                                "<p>&ndash; 6</p>\n", "<p>&ndash; 5</p>\n"],
                    solution_en: "<p>1.(c)</p>\r\n<p>[7&divide;14 &times;8+(5&divide;15&times;6 &ndash; 4&times; 3)]&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>8</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>-</mo><mn>12</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>-</mo><mn>12</mn><mo>&nbsp;</mo><mo>=</mo><mo>-</mo><mo>&nbsp;</mo><mn>6</mn></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\n",
                    solution_hi: "<p>1.(c)</p>\r\n<p>[7&divide;14 &times;8+(5&divide;15&times;6 &ndash; 4&times; 3)]</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>8</mn><mo>+</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>-</mo><mn>12</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>-</mo><mn>12</mn><mo>&nbsp;</mo><mo>=</mo><mo>-</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo></math><span style=\"font-family: Palanquin Dark;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Palanquin Dark;\"> What is the value of y in the following equation?</span></p>\r\n<p><span style=\"font-weight: 400;\">(1&divide; </span><span style=\"font-weight: 400;\">23) of [ 2 { 3 + 4 ( 35 &divide; </span><span style=\"font-weight: 400;\">7) } ] = y</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Palanquin Dark;\">2 &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; &#2350;&#2375;&#2306; y &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\r\n<p><span style=\"font-weight: 400;\">(1 &divide; </span><span style=\"font-weight: 400;\">23) &#2325;&#2366; [ 2 { 3 + 4 ( 35 &divide; </span><span style=\"font-weight: 400;\">7 ) } ] = y</span></p>\n",
                    options_en: ["<p>23</p>\n", "<p>5</p>\n", 
                                "<p>2</p>\n", "<p>21</p>\n"],
                    options_hi: ["<p>23</p>\n", "<p>5</p>\n",
                                "<p>2</p>\n", "<p>21</p>\n"],
                    solution_en: "<p>2.(c)</p>\r\n<p><span style=\"font-weight: 400;\">(1&divide; </span><span style=\"font-weight: 400;\">23) of [ 2 { 3 + 4 ( 35 &divide; </span><span style=\"font-weight: 400;\">7) } ] = y</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>23</mn></mfrac></math>&times; 2{3 + 4&times;5} = y</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>23</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times; 2{23} = 2</span></p>\n",
                    solution_hi: "<p>2.(c)</p>\r\n<p><span style=\"font-weight: 400;\">(1 &divide; </span><span style=\"font-weight: 400;\">23) &#2325;&#2366; [ 2 { 3 + 4 ( 35 &divide; </span><span style=\"font-weight: 400;\">7 ) } ] = y</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>23</mn></mfrac></math>&times; 2{3 + 4&times;5} = y</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>23</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times; 2{23} = 2</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Palanquin Dark;\">The value of </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>2</mn><mn>7</mn></mfrac><mo>+</mo><mn>14</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>+</mo><mn>5</mn><mfrac><mn>3</mn><mn>9</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> is :</span></p>\n",
                    question_hi: "<p>3.&nbsp;<span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>2</mn><mn>7</mn></mfrac><mo>+</mo><mn>14</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>+</mo><mn>5</mn><mfrac><mn>3</mn><mn>9</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376; ?</span></p>\n",
                    options_en: ["<p>21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>63</mn></mfrac></math></p>\n", "<p>25<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>63</mn></mfrac></math></p>\n", 
                                "<p>23<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>63</mn></mfrac></math></p>\n", "<p>22<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>63</mn></mfrac></math></p>\n"],
                    options_hi: ["<p>21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>63</mn></mfrac></math></p>\n", "<p>25<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>63</mn></mfrac></math></p>\n",
                                "<p>23<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>63</mn></mfrac></math></p>\n", "<p>22<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>63</mn></mfrac></math></p>\n"],
                    solution_en: "<p>3.(c)<span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>2</mn><mn>7</mn></mfrac><mo>+</mo><mn>14</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>+</mo><mn>5</mn><mfrac><mn>3</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>23</mn><mn>7</mn></mfrac><mo>+</mo><mfrac><mn>130</mn><mn>9</mn></mfrac><mo>+</mo><mfrac><mn>48</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>23</mn><mn>7</mn></mfrac><mo>+</mo><mfrac><mn>178</mn><mn>9</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mn>1390</mn><mn>63</mn></mfrac><mo>=</mo><mn>23</mn><mfrac><mn>4</mn><mn>63</mn></mfrac></math></p>\n",
                    solution_hi: "<p>3.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>2</mn><mn>7</mn></mfrac><mo>+</mo><mn>14</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>+</mo><mn>5</mn><mfrac><mn>3</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>23</mn><mn>7</mn></mfrac><mo>+</mo><mfrac><mn>130</mn><mn>9</mn></mfrac><mo>+</mo><mfrac><mn>48</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>23</mn><mn>7</mn></mfrac><mo>+</mo><mfrac><mn>178</mn><mn>9</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mn>1390</mn><mn>63</mn></mfrac><mo>=</mo><mn>23</mn><mfrac><mn>4</mn><mn>63</mn></mfrac></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Palanquin Dark;\">Simplify the following expression.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">12 + 2.5 + (1.5 of 4) &ndash; 2.6</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Palanquin Dark;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2357;&#2381;&#2351;&#2306;&#2332;&#2325; &#2325;&#2379; &#2360;&#2352;&#2354; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">12 + 2.5 + (1.5 of 4) &ndash; 2.6</span></p>\n",
                    options_en: ["<p>17.9</p>\n", "<p>18.9</p>\n", 
                                "<p>18.1</p>\n", "<p>17.1</p>\n"],
                    options_hi: ["<p>17.9</p>\n", "<p>18.9</p>\n",
                                "<p>18.1</p>\n", "<p>17.1</p>\n"],
                    solution_en: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">12 + 2.5 + (1.5 of 4) &ndash; 2.6</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">14.5 + 6 - 2.6</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">17.9</span></p>\n",
                    solution_hi: "<p>4.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">12 + 2.5 + (1.5 of 4) &ndash; 2.6</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">14.5 + 6 - 2.6</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">17.9</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Palanquin Dark;\"> What should come in place of the question mark (?) in the following mathematical statement?</span></p>\r\n<p><span style=\"font-weight: 400;\">?&sup3; </span><span style=\"font-weight: 400;\">&divide; </span><span style=\"font-weight: 400;\">16 = 108</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Palanquin Dark;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2357;&#2366;&#2330;&#2325; &#2330;&#2367;&#2344;&#2381;&#2361; (?) &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2346;&#2352; &#2325;&#2380;&#2344;-&#2360;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2310; &#2360;&#2325;&#2340;&#2368; &#2361;&#2376; ? </span></p>\r\n<p><span style=\"font-weight: 400;\">?&sup3; </span><span style=\"font-weight: 400;\">&divide; </span><span style=\"font-weight: 400;\">16 = 108</span></p>\n",
                    options_en: ["<p>10</p>\n", "<p>17</p>\n", 
                                "<p>12</p>\n", "<p>8</p>\n"],
                    options_hi: ["<p>10</p>\n", "<p>17</p>\n",
                                "<p>12</p>\n", "<p>8</p>\n"],
                    solution_en: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>3</mn></msup><mn>16</mn></mfrac></math> = 108</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x<span style=\"font-weight: 400;\">&sup3; </span>= 108 &times; 16 = 3&times;3&times;2&times;2&times;3&times;2&times;2&times;2&times;2</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = 2&times;2&times;3 = 12</span></p>\n",
                    solution_hi: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>3</mn></msup><mn>16</mn></mfrac></math> = 108</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&nbsp;x<span style=\"font-weight: 400;\">&sup3; </span>= 108 &times; 16 = 3&times;3&times;2&times;2&times;3&times;2&times;2&times;2&times;2</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = 2&times;2&times;3 = 12</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Palanquin Dark;\">What is the value of A, if A = [50 &ndash; 4 &times; 5 + {6 &divide;&nbsp;</span><span style=\"font-family: Palanquin Dark;\">3 &ndash; 1}]&nbsp;</span></p>\n",
                    question_hi: "<p>6<span style=\"font-family: Palanquin Dark;\"> &#2351;&#2342;&#2367; A = [50 &ndash; 4 &times; 5 + {6 &divide; 3 &ndash; 1}] </span><span style=\"font-family: Palanquin Dark;\">&nbsp;</span><span style=\"font-family: Palanquin Dark;\">&#2340;&#2379; A &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;|</span></p>\n",
                    options_en: ["<p>30</p>\n", "<p>71</p>\n", 
                                "<p>31</p>\n", "<p>29</p>\n"],
                    options_hi: ["<p>30</p>\n", "<p>71</p>\n",
                                "<p>31</p>\n", "<p>29</p>\n"],
                    solution_en: "<p>6.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A = [50 - 4&times;5 + {6&divide;3 - 1}]</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A= 50 - 20 + 1 = 31</span></p>\n",
                    solution_hi: "<p>6.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A = [50 - 4&times;5 + {6&divide;3 - 1}]</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A= 50 - 20 + 1 = 31</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Palanquin Dark;\"> What is the value of </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mfrac><mn>33</mn><mn>16</mn></mfrac><mo>&ndash;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mo>(</mo><mfrac><mn>6</mn><mn>11</mn></mfrac><mo>&ndash;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mfrac><mn>7</mn><mn>22</mn></mfrac><mo>)</mo><mo>]</mo></math> <span style=\"font-family: Palanquin Dark;\">?</span></p>\n",
                    question_hi: "<p>7.&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mfrac><mn>33</mn><mn>16</mn></mfrac><mo>&ndash;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mo>(</mo><mfrac><mn>6</mn><mn>11</mn></mfrac><mo>&ndash;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mfrac><mn>7</mn><mn>22</mn></mfrac><mo>)</mo><mo>]</mo></math><span style=\"font-family: Palanquin Dark;\"> &#2325;&#2366; &#2350;&#2366;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2352;&#2375;&#2306; &#2361;&#2376;&#2404; </span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6871</mn><mn>3605</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6805</mn><mn>2987</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6907</mn><mn>3971</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>6961</mn><mn>2904</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6871</mn><mn>3605</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6805</mn><mn>2987</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6907</mn><mn>3971</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6961</mn><mn>2904</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Palanquin Dark;\">7.(d)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mfrac><mn>33</mn><mn>16</mn></mfrac><mo>&ndash;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mo>(</mo><mfrac><mn>6</mn><mn>11</mn></mfrac><mo>&ndash;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mfrac><mn>7</mn><mn>22</mn></mfrac><mo>)</mo><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mfrac><mn>33</mn><mn>16</mn></mfrac><mo>&ndash;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mo>(</mo><mfrac><mrow><mn>114</mn><mo>-</mo><mn>55</mn></mrow><mn>132</mn></mfrac><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mo>&nbsp;</mo><mfrac><mn>33</mn><mn>16</mn></mfrac><mo>&ndash;</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>59</mn><mn>132</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mfrac><mrow><mn>1089</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>220</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>236</mn></mrow><mn>528</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mfrac><mn>1105</mn><mn>528</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mi>x</mi><mfrac><mn>1105</mn><mn>528</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>1105</mn><mrow><mn>726</mn><mo>&nbsp;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>2541</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4420</mn></mrow><mn>2904</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>6961</mn><mn>2904</mn></mfrac></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Palanquin Dark;\">7.(d)&nbsp;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mfrac><mn>33</mn><mn>16</mn></mfrac><mo>&ndash;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mo>(</mo><mfrac><mn>6</mn><mn>11</mn></mfrac><mo>&ndash;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mfrac><mn>7</mn><mn>22</mn></mfrac><mo>)</mo><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mfrac><mn>33</mn><mn>16</mn></mfrac><mo>&ndash;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mo>(</mo><mfrac><mrow><mn>114</mn><mo>-</mo><mn>55</mn></mrow><mn>132</mn></mfrac><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mo>&nbsp;</mo><mfrac><mn>33</mn><mn>16</mn></mfrac><mo>&ndash;</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>12</mn></mfrac><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>59</mn><mn>132</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mfrac><mrow><mn>1089</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>220</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>236</mn></mrow><mn>528</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mo>[</mo><mfrac><mn>1105</mn><mn>528</mn></mfrac><mo>]</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>8</mn><mn>11</mn></mfrac><mi>x</mi><mfrac><mn>1105</mn><mn>528</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mn>7</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>1105</mn><mrow><mn>726</mn><mo>&nbsp;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mn>2541</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4420</mn></mrow><mn>2904</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>6961</mn></mrow><mn>2904</mn></mfrac></math></span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Palanquin Dark;\"> P = 18 &times; </span><span style=\"font-family: Palanquin Dark;\">9 &divide; </span><span style=\"font-family: Palanquin Dark;\">6 + 5 &times;</span><span style=\"font-family: Palanquin Dark;\"> 15 &divide; </span><span style=\"font-family: Palanquin Dark;\">25 and Q = 15 &divide;</span><span style=\"font-family: Palanquin Dark;\"> 5 </span><span style=\"font-family: Palanquin Dark;\">&times; 5 &ndash; 25 &divide;</span><span style=\"font-family: Palanquin Dark;\"> 5, what is the value of (P &divide;</span><span style=\"font-family: Palanquin Dark;\"> Q) ?</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Palanquin Dark;\"> P = 18 &times; 9 &divide; 6 + 5 &times; 15 &divide; 25</span><span style=\"font-family: Palanquin Dark;\"> &#2324;&#2352; Q = 15 &divide; 5 &times; 5 &ndash; 25 &divide; 5</span><span style=\"font-family: Palanquin Dark;\">, (P &divide; </span><span style=\"font-family: Palanquin Dark;\"> Q) &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>2.5</p>\n", "<p>2</p>\n", 
                                "<p>3</p>\n", "<p>1.5</p>\n"],
                    options_hi: ["<p>2.5</p>\n", "<p>2</p>\n",
                                "<p>3</p>\n", "<p>1.5</p>\n"],
                    solution_en: "<p>8.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P = 18 &times; 9 &divide; 6 + 5 &times; 15 &divide; 25 and Q = 15 &divide; 5 &times; 5 &ndash; 25 &divide; 5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P = 27 + 3 = 30 , Q = 15 - 5 = 10</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P &divide; Q = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>10</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 3 </span></p>\n",
                    solution_hi: "<p>8.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P = 18 &times; 9 &divide; 6 + 5 &times; 15 &divide; 25 <span style=\"font-weight: 400;\">&#2324;&#2352;&nbsp;</span> Q = 15 &divide; 5 &times; 5 &ndash; 25 &divide; 5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P = 27 + 3 = 30 , Q = 15 - 5 = 10</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P &divide; Q =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>10</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\">= 3 </span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Palanquin Dark;\">What is the value of p in the following equation ?</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">64 &ndash; [ 16 + { 23 &ndash; (12 + 10) } ] = p</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Palanquin Dark;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; &#2350;&#2375;&#2306; p &#2325;&#2366; &#2350;&#2366;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">64 &ndash; [ 16 + { 23 &ndash; (12 + 10) } ] = p</span></p>\n",
                    options_en: ["<p>47</p>\n", "<p>49</p>\n", 
                                "<p>81</p>\n", "<p>3</p>\n"],
                    options_hi: ["<p>47</p>\n", "<p>49</p>\n",
                                "<p>81</p>\n", "<p>3</p>\n"],
                    solution_en: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">64 - [16 + {23 - (12 + 10)}] = p</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">64 - 17 = 47 = p</span></p>\n",
                    solution_hi: "<p>9.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">64 - [16 + {23 - (12 + 10)}] = p</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">64 - 17 = 47 = p</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. <span style=\"font-family: Palanquin Dark;\">The value of (82&sup2; +</span><span style=\"font-family: Palanquin Dark;\"> 18 &times; </span><span style=\"font-family: Palanquin Dark;\">26 ) &divide; </span><span style=\"font-family: Palanquin Dark;\">16 is:</span></p>\n",
                    question_hi: "<p>10. <span style=\"font-family: Palanquin Dark;\">(82&sup2;</span><span style=\"font-family: Palanquin Dark;\"> + 18 &times; </span><span style=\"font-family: Palanquin Dark;\">26 ) &divide;</span><span style=\"font-family: Palanquin Dark;\">16 &#2325;&#2366; &#2350;&#2366;&#2344; &#2361;&#2376;:</span></p>\n",
                    options_en: ["<p>451.8</p>\n", "<p>444.3</p>\n", 
                                "<p>449.5</p>\n", "<p>447.1</p>\n"],
                    options_hi: ["<p>451.8</p>\n", "<p>444.3</p>\n",
                                "<p>449.5</p>\n", "<p>447.1</p>\n"],
                    solution_en: "<p>10.(c)</p>\r\n<p><span style=\"font-weight: 400;\">(82&sup2; + 18 &times; 26 ) &divide;16</span></p>\r\n<p><span style=\"font-weight: 400;\">(6724 + 468) &divide; 16 = 7192 &divide; 16 = 449.5</span></p>\n",
                    solution_hi: "<p>10.(c)</p>\r\n<p><span style=\"font-weight: 400;\">(82&sup2; + 18 &times; 26 ) &divide;16</span></p>\r\n<p><span style=\"font-weight: 400;\">(6724 + 468) &divide; 16 = 7192 &divide; 16 = 449.5</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Palanquin Dark;\">The value of </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>14</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>+</mo><mn>16</mn><mfrac><mn>5</mn><mn>27</mn></mfrac><mo>+</mo><mn>2</mn><mfrac><mn>5</mn><mn>54</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> is :</span></p>\n",
                    question_hi: "<p>11. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>14</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>+</mo><mn>16</mn><mfrac><mn>5</mn><mn>27</mn></mfrac><mo>+</mo><mn>2</mn><mfrac><mn>5</mn><mn>54</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">&#2325;&#2366; &#2350;&#2366;&#2344; &#2361;&#2376; :</span></p>\n",
                    options_en: ["<p>32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>18</mn></mfrac></math></p>\n", "<p>32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>18</mn></mfrac></math></p>\n", 
                                "<p>31<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>18</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> </span></p>\n", "<p>32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>18</mn></mfrac></math></p>\n"],
                    options_hi: ["<p>32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>18</mn></mfrac></math></p>\n", "<p>32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>18</mn></mfrac></math></p>\n",
                                "<p>31<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>18</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> </span></p>\n", "<p>32<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>18</mn></mfrac></math></p>\n"],
                    solution_en: "<p>11.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>14</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>+</mo><mn>16</mn><mfrac><mn>5</mn><mn>27</mn></mfrac><mo>+</mo><mn>2</mn><mfrac><mn>5</mn><mn>54</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>32</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>+</mo><mfrac><mn>5</mn><mn>27</mn></mfrac><mo>+</mo><mfrac><mn>5</mn><mn>54</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>32</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mrow><mn>24</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn></mrow><mn>54</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>32</mn><mfrac><mn>39</mn><mn>54</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>32</mn><mfrac><mn>13</mn><mn>18</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\n",
                    solution_hi: "<p>11.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>14</mn><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>+</mo><mn>16</mn><mfrac><mn>5</mn><mn>27</mn></mfrac><mo>+</mo><mn>2</mn><mfrac><mn>5</mn><mn>54</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>32</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>+</mo><mfrac><mn>5</mn><mn>27</mn></mfrac><mo>+</mo><mfrac><mn>5</mn><mn>54</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mn>32</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mrow><mn>24</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn></mrow><mn>54</mn></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>32</mn><mfrac><mn>39</mn><mn>54</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>32</mn><mfrac><mn>13</mn><mn>18</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Palanquin Dark;\"> If&nbsp; A = 3 + 7 &times; </span><span style=\"font-family: Palanquin Dark;\">2, B = 12 &divide; </span><span style=\"font-family: Palanquin Dark;\">4 &ndash; 6 and C = 6 &times;</span><span style=\"font-family: Palanquin Dark;\"> 5 + 2 then what is the value of A &times;</span><span style=\"font-family: Palanquin Dark;\"> B + C ?</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Palanquin Dark;\"> &#2351;&#2342;&#2367; A= 3 + 7 &times;</span><span style=\"font-family: Palanquin Dark;\"> 2, B = 12 &divide; </span><span style=\"font-family: Palanquin Dark;\">4 &ndash; 6 &#2324;&#2352; C = 6 &times;</span><span style=\"font-family: Palanquin Dark;\"> 5 + 2 &#2361;&#2376; &#2340;&#2379; A &times;</span><span style=\"font-family: Palanquin Dark;\"> B + C &#2325;&#2366; &#2350;&#2366;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404; </span></p>\n",
                    options_en: ["<p>&ndash; 22</p>\n", "<p>&ndash; 19</p>\n", 
                                "<p>&ndash; 25</p>\n", "<p>&ndash; 26</p>\n"],
                    options_hi: ["<p>&ndash; 22</p>\n", "<p>&ndash; 19</p>\n",
                                "<p>&ndash; 25</p>\n", "<p>&ndash; 26</p>\n"],
                    solution_en: "<p>12.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A = 3 + 7 &times; </span><span style=\"font-family: Palanquin Dark;\">2, B = 12 &divide; </span><span style=\"font-family: Palanquin Dark;\">4 &ndash; 6 and C = 6 &times;</span><span style=\"font-family: Palanquin Dark;\"> 5 + 2 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A = 17 , B = -3 and C = 32</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A &times;</span><span style=\"font-family: Palanquin Dark;\"> B + C = 17&times;-3 + 32 = -51 + 32 = -19</span></p>\n",
                    solution_hi: "<p>12.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A = 3 + 7 &times; </span><span style=\"font-family: Palanquin Dark;\">2, B = 12 &divide; </span><span style=\"font-family: Palanquin Dark;\">4 &ndash; 6 <span style=\"font-weight: 400;\">&#2324;&#2352; </span>C = 6 &times;</span><span style=\"font-family: Palanquin Dark;\"> 5 + 2 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A = 17 , B = -3 <span style=\"font-weight: 400;\">&#2324;&#2352;</span> C = 32</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A &times;</span><span style=\"font-family: Palanquin Dark;\"> B + C = 17&times;-3 + 32 = -51 + 32 = -19</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Palanquin Dark;\"> What should come in place of the question mark (?) in the following mathematical statement?</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>19</mn><mo>&times;</mo><mn>650</mn><mo>&divide;</mo><mn>13</mn><mo>+</mo><mn>18</mn><mo>.</mo><mn>5</mn><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo></msqrt><mo>&times;</mo><mn>13</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>?</mo></math></span></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Palanquin Dark;\"> &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2360;&#2350;&#2368;&#2325;&#2352;&#2339; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2330;&#2367;&#2361;&#2381;&#2344; (?) &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2346;&#2352; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2310; &#2360;&#2325;&#2340;&#2368; &#2361;&#2376; ?</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>19</mn><mo>&times;</mo><mn>650</mn><mo>&divide;</mo><mn>13</mn><mo>+</mo><mn>18</mn><mo>.</mo><mn>5</mn><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo></msqrt><mo>&times;</mo><mn>13</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>?</mo></math></span></p>\n",
                    options_en: ["<p>416</p>\n", "<p>418</p>\n", 
                                "<p>413</p>\n", "<p>415</p>\n"],
                    options_hi: ["<p>416</p>\n", "<p>418</p>\n",
                                "<p>413</p>\n", "<p>415</p>\n"],
                    solution_en: "<p>13.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>19</mn><mo>&times;</mo><mn>650</mn><mo>&divide;</mo><mn>13</mn><mo>+</mo><mn>18</mn><mo>.</mo><mn>5</mn><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo></msqrt><mo>&times;</mo><mn>13</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><msqrt><mn>19</mn><mo>&times;</mo><mn>50</mn><mo>+</mo><mn>74</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>13</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><msqrt><mn>1024</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mn>13</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>32</mn><mo>&times;</mo><mn>13</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>416</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>13.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>19</mn><mo>&times;</mo><mn>650</mn><mo>&divide;</mo><mn>13</mn><mo>+</mo><mn>18</mn><mo>.</mo><mn>5</mn><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo></msqrt><mo>&times;</mo><mn>13</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><msqrt><mn>19</mn><mo>&times;</mo><mn>50</mn><mo>+</mo><mn>74</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>13</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><msqrt><mn>1024</mn></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mn>13</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>32</mn><mo>&times;</mo><mn>13</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>416</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Palanquin Dark;\">What is the value of&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>+</mo><mn>8</mn><mo>&divide;</mo><mn>4</mn><mo>&times;</mo><mn>3</mn></mrow><mrow><mn>18</mn><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mo>&divide;</mo><mn>2</mn><mo>&times;</mo><mn>5</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>?</mo></math></span><span style=\"font-family: Palanquin Dark;\"> ?</span></p>\n",
                    question_hi: "<p>14. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>+</mo><mn>8</mn><mo>&divide;</mo><mn>4</mn><mo>&times;</mo><mn>3</mn></mrow><mrow><mn>18</mn><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mo>&divide;</mo><mn>2</mn><mo>&times;</mo><mn>5</mn><mo>&nbsp;</mo></mrow></mfrac></math><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>26</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>28</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>26</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>41</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>26</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>28</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>26</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>41</mn></mfrac></math></p>\n"],
                    solution_en: "<p>14.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>+</mo><mn>8</mn><mo>&divide;</mo><mn>4</mn><mo>&times;</mo><mn>3</mn></mrow><mrow><mn>18</mn><mo>+</mo><mn>4</mn><mo>&divide;</mo><mn>2</mn><mo>&times;</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>17</mn><mo>+</mo><mn>2</mn><mo>&times;</mo><mn>3</mn></mrow><mrow><mn>18</mn><mo>+</mo><mn>2</mn><mo>&times;</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>17</mn><mo>+</mo><mn>6</mn></mrow><mrow><mn>18</mn><mo>+</mo><mn>10</mn></mrow></mfrac><mo>=</mo><mfrac><mn>23</mn><mn>28</mn></mfrac></math></p>\n",
                    solution_hi: "<p>14.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>+</mo><mn>8</mn><mo>&divide;</mo><mn>4</mn><mo>&times;</mo><mn>3</mn></mrow><mrow><mn>18</mn><mo>+</mo><mn>4</mn><mo>&divide;</mo><mn>2</mn><mo>&times;</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>17</mn><mo>+</mo><mn>2</mn><mo>&times;</mo><mn>3</mn></mrow><mrow><mn>18</mn><mo>+</mo><mn>2</mn><mo>&times;</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>17</mn><mo>+</mo><mn>6</mn></mrow><mrow><mn>18</mn><mo>+</mo><mn>10</mn></mrow></mfrac><mo>=</mo><mfrac><mn>23</mn><mn>28</mn></mfrac></math></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15.<span style=\"font-family: Palanquin Dark;\"> If A =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn><mo>&divide;</mo><mn>8</mn><mo>&times;</mo><mn>2</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>14</mn><mo>&divide;</mo><mn>7</mn></mrow></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> and B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&divide;</mo><mn>2</mn><mo>-</mo><mo>&nbsp;</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>&divide;</mo><mn>15</mn><mo>&times;</mo><mn>6</mn></mrow></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> , then what is the value of A &ndash; B ?</span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Palanquin Dark;\">&#2351;&#2342;&#2367; A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn><mo>&divide;</mo><mn>8</mn><mo>&times;</mo><mn>2</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>14</mn><mo>&divide;</mo><mn>7</mn></mrow></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> and B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&divide;</mo><mn>2</mn><mo>-</mo><mo>&nbsp;</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>&divide;</mo><mn>15</mn><mo>&times;</mo><mn>6</mn></mrow></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &#2361;&#2376;, &#2340;&#2379; A - B &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n"],
                    solution_en: "<p>15.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>A</mi><mo>-</mo><mi>B</mi><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn><mo>&divide;</mo><mn>8</mn><mo>&times;</mo><mn>2</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>14</mn><mo>&divide;</mo><mn>7</mn></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>6</mn><mo>&divide;</mo><mn>2</mn><mo>-</mo><mo>&nbsp;</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>&divide;</mo><mn>15</mn><mo>&times;</mo><mn>6</mn></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>-</mo><mo>(</mo><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac><mo>)</mo><mo>=</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\n",
                    solution_hi: "<p>15.(c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>A</mi><mo>-</mo><mi>B</mi><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn><mo>&divide;</mo><mn>8</mn><mo>&times;</mo><mn>2</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>14</mn><mo>&divide;</mo><mn>7</mn></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>6</mn><mo>&divide;</mo><mn>2</mn><mo>-</mo><mo>&nbsp;</mo><mn>4</mn></mrow><mrow><mn>5</mn><mo>&divide;</mo><mn>15</mn><mo>&times;</mo><mn>6</mn></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>-</mo><mo>(</mo><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac><mo>)</mo><mo>=</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>