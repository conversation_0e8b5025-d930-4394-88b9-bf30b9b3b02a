<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following is <strong>NOT </strong>found in human blood?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन मानव रक्त में <strong>नहीं </strong>पाया जाता है?</p>",
                    options_en: ["<p>Monocyte</p>", "<p>Leucocyte</p>", 
                                "<p>Lymphocyte</p>", "<p>Chondrocyte </p>"],
                    options_hi: ["<p>मोनोसाइट्स</p>", "<p>ल्युकोसैट</p>",
                                "<p>लिम्फोसाइट</p>", "<p>चोंड्रोसाइट</p>"],
                    solution_en: "<p>1.(d) <strong>Chondrocyte </strong>(a cell that produces cartilage). <strong>Cartilage </strong>- The flexible connective tissue present in the ears, nose, and joints of the body. <strong>Monocytes </strong>- The largest type of white blood cell and are responsible for engulfing and destroying foreign particles. <strong>Leukocytes </strong>- A type of white blood cell that is found in the blood and lymph nodes. <strong>Lymphocytes </strong>- A type of white blood cell in the immune system of most vertebrates.</p>",
                    solution_hi: "<p>1.(d) <strong>चोंड्रोसाइट </strong>(एक कोशिका जो उपास्थि का निर्माण करती है)। <strong>उपास्थि </strong>- कान, नाक और शरीर के जोड़ों में उपस्थित लचीला संयोजी ऊतक। <strong>मोनोसाइट्स </strong>- श्वेत रक्त कोशिका का सबसे बड़ा प्रकार और बाहरी कणों को निगलने और नष्ट करने के लिए उत्तरदायी हैं। <strong>ल्यूकोसाइट्स </strong>- एक प्रकार की श्वेत रक्त कोशिका है जो रक्त और लिम्फ नोड्स में पाई जाती है। <strong>लिम्फोसाइट्स </strong>- अधिकांश कशेरुकियों की प्रतिरक्षा प्रणाली में एक प्रकार की श्वेत रक्त कोशिका।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. The smallest blood vessel is called:</p>",
                    question_hi: "<p>2. सबसे छोटी रक्त वाहिका को _____ कहा जाता है।</p>",
                    options_en: ["<p>capillary</p>", "<p>artery</p>", 
                                "<p>vein</p>", "<p>vena cava</p>"],
                    options_hi: ["<p>केशिका (capillary)</p>", "<p>धमनी (artery)</p>",
                                "<p>शिरा (vein)</p>", "<p>महाशिरा (vena cava)</p>"],
                    solution_en: "<p>2.(a) <strong>Capillary </strong>connects arteries and veins. They are so small that only one red blood cell can pass through them at a time. Capillaries are found in all tissues of the body, and they are responsible for delivering oxygen and nutrients to the cells and removing waste products. The arteries carry oxygen and nutrients away from your heart, to your body\'s tissues. Veins carry oxygen-poor blood from the body\'s tissues back to the heart. The vena cava is the largest vein in the body. It carries oxygen-poor blood from the lower body back to the heart.</p>",
                    solution_hi: "<p>2.(a) <strong>केशिका </strong>। यह धमनियों और शिराओं को जोड़ती है। ये इतने छोटे होते हैं कि इनमे से एक समय में केवल एक ही लाल रक्त कोशिका गुजर सकती है। केशिकाएं शरीर के सभी ऊतकों में पाई जाती हैं, और ये कोशिकाओं तक ऑक्सीजन और पोषक तत्व पहुंचाने और अपशिष्ट उत्पादों को हटाने के लिए उत्तरदायी हैं। धमनियां ऑक्सीजन और पोषक तत्वों को हमारे हृदय से शरीर के ऊतकों तक ले जाती हैं।। शिरा शरीर के ऊतकों से ऑक्सीजन रहित रक्त को वापस हृदय तक ले जाती हैं। महाशिरा (vena cava) शरीर की सबसे बड़ी नस होती है। यह शरीर के निचले हिस्से से ऑक्सीजन रहित रक्त को वापस हृदय तक ले जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The normal systolic pressure of human blood is :</p>",
                    question_hi: "<p>3. मानव रक्त का सामान्य प्रकुंचन दाब (systolic pressure) कितना होता है ?</p>",
                    options_en: ["<p>80 mmHg</p>", "<p>130 mmHg</p>", 
                                "<p>120 mmHg</p>", "<p>110 mmHg</p>"],
                    options_hi: ["<p>80 mmHg</p>", "<p>130 mmHg</p>",
                                "<p>120 mmHg</p>", "<p>110 mmHg</p>"],
                    solution_en: "<p>3.(c) <strong>120 mmHg.</strong> Blood pressure is typically measured in millimeters of mercury (mmHg), and a blood pressure reading is expressed as systolic over diastolic pressure. Example - A blood pressure reading of 120/80 mmHg indicates a systolic pressure of 120 mmHg and a diastolic pressure of 80 mmHg. The ideal or normal blood pressure for adults is generally considered to be around 120/80 mmHg.</p>",
                    solution_hi: "<p>3.(c) <strong>120 mmHg। </strong>रक्तचाप आमतौर पर पारा के मिलीमीटर (mmHg) में मापा जाता है, और रक्तचाप रीडिंग को डायस्टोलिक दबाव पर सिस्टोलिक के रूप में व्यक्त किया जाता है। उदाहरण - 120/80 mmHg का रक्तचाप रीडिंग 120 mmHg का सिस्टोलिक दबाव और 80 mmHg का डायस्टोलिक दबाव इंगित करता है। वयस्कों के लिए आदर्श या सामान्य रक्तचाप आमतौर पर 120/80 mmHg के आसपास माना जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Identify the pigment that gives blood its colour.</p>",
                    question_hi: "<p>4. उस वर्णक की पहचान कीजिए, जो रक्त को उसका रंग देता है।</p>",
                    options_en: ["<p>Haemoglobin</p>", "<p>Xanthophyll</p>", 
                                "<p>Ferritin</p>", "<p>Chlorophyll</p>"],
                    options_hi: ["<p>हीमोग्लोबिन</p>", "<p>ज़ैंथोफिल</p>",
                                "<p>फेरिटि</p>", "<p>क्लोरोफिल</p>"],
                    solution_en: "<p>4.(a) <strong>Haemoglobin</strong>. The Red Blood Cells (RBCs) contain a red pigment called haemoglobin which transports oxygen and also gives blood its red colour.</p>",
                    solution_hi: "<p>4.(a) <strong>हीमोग्लोबिन</strong>। लाल रक्त कोशिकाओं (RBC) में हीमोग्लोबिन नामक एक लाल रंगद्रव्य होता है जो ऑक्सीजन का परिवहन करता है और रक्त को उसका लाल रंग भी देता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. _______ are the vessels which carry blood away from the heart to various organs of the body.</p>",
                    question_hi: "<p>5. ______ वे वाहिकाएँ हैं जो रक्त को हृदय से दूर शरीर के विभिन्न भागों तक ले जाती हैं।</p>",
                    options_en: ["<p>Veins</p>", "<p>Tracheas</p>", 
                                "<p>Arteries</p>", "<p>Plasma</p>"],
                    options_hi: ["<p>शिराएँ</p>", "<p>श्वासनली</p>",
                                "<p>धमनियाँ</p>", "<p>प्लाज्मा</p>"],
                    solution_en: "<p>5.(c) <strong>Arteries </strong>- They carry oxygenated blood, which is rich in nutrients and oxygen. <strong>The tracheas</strong> are the tubes that carry air from the throat to the lungs. They are made of cartilage and have smooth muscles in their walls.</p>",
                    solution_hi: "<p>5.(c) <strong>धमनियाँ </strong>- ये ऑक्सीजन युक्त रक्त&nbsp;ले जाती हैं, जो पोषक तत्वों और ऑक्सीजन से भरपूर होता है। <strong>श्वासनली </strong>(trachea) वे नलिकाएं हैं जो नलिका से फेफड़ों तक वायु ले जाती हैं। ये उपास्थि से बने होते हैं और उनकी भित्तीयों में चिकनी मांसपेशियां होती हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Fishes have ________ chambers of the heart.</p>",
                    question_hi: "<p>6. मछलियों के हृदय में _____ कक्ष होते हैं।</p>",
                    options_en: ["<p>three</p>", "<p>one</p>", 
                                "<p>two</p>", "<p>four</p>"],
                    options_hi: ["<p>तीन</p>", "<p>एक</p>",
                                "<p>दो</p>", "<p>चार</p>"],
                    solution_en: "<p>6.(c) <strong>Two </strong>(atrium and ventricle). In Fishes, the oxygenation of blood happens through the gills. Three chambered hearts - Found in amphibians and most reptiles except for crocodiles. Examples - Salamander, Lizard. Birds and mammals have four-chambered hearts including crocodiles.</p>",
                    solution_hi: "<p>6.(c) <strong>दो </strong>(आलिंद और निलय)। मछलियों में रक्त का ऑक्सीजनीकरण गलफड़ों के माध्यम से होता है। तीन कक्षीय हृदय - मगरमच्छों को छोड़कर उभयचरों और अधिकांश सरीसृपों में पाया जाता है। उदाहरण - सैलामैंडर, छिपकली। मगरमच्छों सहित पक्षियों और स्तनधारियों का हृदय चार - कक्षीय होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. _______________ are the vessels which carry carbon dioxide-rich blood back to the heart.</p>",
                    question_hi: "<p>7. ________ वे रक्त वाहिनियाँ हैं, जो कार्बन डाइऑक्साइड युक्त रक्त को वापस हृदय में ले जाती हैं।</p>",
                    options_en: ["<p>veins</p>", "<p>neurons</p>", 
                                "<p>capillaries</p>", "<p>arteries</p>"],
                    options_hi: ["<p>शिराएँ</p>", "<p>तंत्रिका कोशिकाएँ</p>",
                                "<p>केशिकाएँ</p>", "<p>धमनियों</p>"],
                    solution_en: "<p>7.(a) <strong>Veins </strong>- They carry deoxygenated blood (except pulmonary veins). Biggest vein - Vena Cava. <strong>Neurons </strong>: Fundamental unit of the nervous system specialized to transmit information to different parts of the body. <strong>Capillaries </strong>: They take waste products away from tissues.</p>",
                    solution_hi: "<p>7.(a) <strong>शिराएँ </strong>- ये ऑक्सीजन रहित रक्त&nbsp;(फुफ्फुस शिरा को छोड़कर) ले जाती हैं । सबसे बड़ी शिरा - वेना कावा। <strong>तंत्रिका कोशिकाएँ : </strong>तंत्रिका तंत्र की मूल इकाई जो शरीर के विभिन्न भागों तक सूचना प्रसारित करने के लिए विशेषीकृत है। <strong>केशिकाएँ </strong>: ये अपशिष्ट उत्पादों को ऊतकों से दूर ले जाती हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. The blood platelets are also known as .</p>",
                    question_hi: "<p>8. रक्त पट्टिकाणु (प्लेटलेट्स) को के नाम से भी जाना जाता है।</p>",
                    options_en: ["<p>thrombin</p>", "<p>thrombocytes</p>", 
                                "<p>erythrocytes</p>", "<p>leucocytes</p>"],
                    options_hi: ["<p>थ्रोम्बिन (thrombin)</p>", "<p>थ्रोम्बोसाइट (thrombocytes)</p>",
                                "<p>एरिथ्रोसाइट्स (erythrocytes)</p>", "<p>ल्युकोसाइट (leucocytes)</p>"],
                    solution_en: "<p>8.(b) <strong>Thrombocytes</strong>. They are cell fragments found in blood and spleen produced from megakaryocytes (special cells in the bone marrow). It helps to form blood clots, prevent bleeding and wounds healing. Blood normally contains 150,000 to 450,000 platelets per microliter of blood. <strong>Thrombin </strong>- An enzyme that helps in regulating hemostasis and maintaining blood coagulation.</p>",
                    solution_hi: "<p>8.(b) <strong>थ्रोम्बोसाइट्स</strong>। ये रक्त और प्लीहा में पाए जाने वाले कोशिका के टुकड़े हैं जो मेगाकार्योसाइट्स (अस्थि मज्जा में विशेष कोशिकाएं) से उत्पन्न होते हैं। यह रक्त के थक्के बनाने, रक्तस्राव को रोकने और घावों को भरने में मदद करता है। रक्त में सामान्यतः प्रति माइक्रोलीटर 150,000 से 450,000 प्लेटलेट्स होते हैं। <strong>थ्रोम्बिन </strong>- एक एंजाइम जो हेमोस्टेसिस को विनियमित करने और रक्त स्कंदन को बनाए रखने में मदद करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Muscles are of three types. Which of the following is NOT one of them ?</p>",
                    question_hi: "<p>9. मांसपेशियाँ तीन प्रकार की होती हैं। निम्नलिखित में से कौन उनमें से एक नहीं है?</p>",
                    options_en: ["<p>Sesamoids</p>", "<p>Smooth</p>", 
                                "<p>Cardiac</p>", "<p>Skeletal</p>"],
                    options_hi: ["<p>सेसमॉयड</p>", "<p>चिकनी</p>",
                                "<p>हृदय</p>", "<p>कंकाल</p>"],
                    solution_en: "<p>9.(a) <strong>Sesamoids</strong>. Muscles are tissues that can contract, and therefore they help in the movement of the other body parts. There are about 600 muscles in the human body. The three main types of muscle include skeletal (bones), smooth (organs) and cardiac (heart).</p>",
                    solution_hi: "<p>9.(a) <strong>सेसमॉयड </strong>(Sesamoids)। ये मांसपेशियां ऊतक होती हैं जो सिकुड़ सकती हैं, और इसलिए ये शरीर के अन्य अंगों की गति में मदद करती हैं। मानव शरीर में लगभग 600 मांसपेशियां होती हैं। तीन मुख्य प्रकार की मांसपेशियों में कंकाल (bones), चिकनी (organs) और कार्डियक (heart) शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. What is the pH of human blood ?</p>",
                    question_hi: "<p>10. मानव रक्त का pH कितना होता है?</p>",
                    options_en: ["<p>6.5</p>", "<p>5.8</p>", 
                                "<p>10</p>", "<p>7.4</p>"],
                    options_hi: ["<p>6.5</p>", "<p>5.8</p>",
                                "<p>10</p>", "<p>7.4</p>"],
                    solution_en: "<p>10.(d) <strong>7.4</strong>. pH (Potential of Hydrogen) is really a measure of the relative amount of free hydrogen and hydroxyl ions in the water.</p>",
                    solution_hi: "<p>10.(d)<strong> 7.4 । </strong>pH (हाइड्रोजन की क्षमता) वास्तव में जल में मुक्त हाइड्रोजन और हाइड्रॉक्सिल आयनों की सापेक्ष मात्रा का एक माप है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following is a type of connective tissue?<br>l. Blood <br>ll. Bone <br>lll. Ligament</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन-सा संयोजी ऊतक का एक प्रकार है?<br>I. रक्त, <br>ll. अस्थि <br>III. अस्थि-बंधन (Ligament)</p>",
                    options_en: ["<p>Only III</p>", "<p>Only II and III</p>", 
                                "<p>I, II and III</p>", "<p>Only I and II</p>"],
                    options_hi: ["<p>केवल III</p>", "<p>केवल II और III</p>",
                                "<p>। , II और III</p>", "<p>केवल I और II</p>"],
                    solution_en: "<p>11.(c)<strong> I, II and III. Connective tissues</strong> connect and help hold our body together. They provide internal support as well as give and maintain form of the body. The <strong>ligament </strong>is the connective tissue that joins two bones.</p>",
                    solution_hi: "<p>11.(c)<strong> I, II और III. संयोजी ऊतक </strong>शरीर को जोड़ते हैं और एक साथ रखने में मदद करते हैं। ये आंतरिक सहायता प्रदान करने के साथ-साथ शरीर का एक निश्चित आकार बनाए रखते हैं। <strong>अस्थि-बंधन</strong> (Ligament) ऐसा संयोजी ऊतक है जो दो अस्थियों को आपस में जोड़ता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which of the following is essential to form haemoglobin in blood?</p>",
                    question_hi: "<p>12. रक्त में हीमोग्लोबिन बनाने के लिए निम्न में क्या आवश्यक है?</p>",
                    options_en: ["<p>Fats</p>", "<p>Iron</p>", 
                                "<p>Calcium</p>", "<p>Protein</p>"],
                    options_hi: ["<p>वसा</p>", "<p>आयरन</p>",
                                "<p>कैल्शियम</p>", "<p>प्रोटीन</p>"],
                    solution_en: "<p>12.(b)<strong id=\"docs-internal-guid-f1ff188c-7fff-a092-a599-5891c45d1d35\"><strong id=\"docs-internal-guid-bfa9ffe0-7fff-3e0d-9848-7072ce5386eb\"> </strong>Iron. Haemoglobin (Hb) :</strong> It is a protein in red blood cells that binds to oxygen, enabling its transport throughout the body, crucial for oxygen delivery to tissues and organs. In its iron-bound state, haemoglobin gives blood its red colour.</p>",
                    solution_hi: "<p dir=\"ltr\">12.(b) <strong>आयरन। हीमोग्लोबिन (Hb): </strong>यह लाल रक्त कोशिकाओं में एक प्रोटीन है, जो पूरे शरीर में ऑक्सीजन के परिवहन को सक्षम बनाता है, जो ऊतकों और अंगों तक ऑक्सीजन पहुंचाने के लिए महत्वपूर्ण है। आयरन-युक्त अवस्था में, हीमोग्लोबिन, रक्त को लाल रंग प्रदान करता है।&nbsp;</p>\n<p>&nbsp;</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following is NOT a type of blood cell made by the bone marrow?</p>",
                    question_hi: "<p>13. निम्न में से कौन अस्थि मज्जा द्वारा निर्मित एक प्रकार की रक्त कोशिका नहीं है?</p>",
                    options_en: ["<p>Osteoclasts</p>", "<p>Red blood cells</p>", 
                                "<p>Platelets</p>", "<p>White blood cells</p>"],
                    options_hi: ["<p>ऑस्टियोक्लास्ट</p>", "<p>लाल रक्त कोशिकाएं</p>",
                                "<p>प्लेटलेट्स</p>", "<p>श्वेत रक्त कोशिकाएं</p>"],
                    solution_en: "<p>13.(a) <strong>Osteoclasts</strong>. It is a large multinucleated cell responsible for the dissolution and absorption of bone. Red blood cells, platelets, and white blood cells are types of blood cells made by bone marrow.</p>",
                    solution_hi: "<p>13.(a) <strong>ऑस्टियोक्लास्ट</strong>। यह हड्डी के विघटन और अवशोषण के लिए जिम्मेदार एक बड़ी बहुकेंद्रीय कोशिका है। लाल रक्त कोशिकाएं, प्लेटलेट्स और श्वेत रक्त कोशिकाएं, अस्थि मज्जा द्वारा निर्मित रक्त कोशिकाओं के प्रकार हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Lymph is a light clear fluid made up of white blood cells that attack harmful ______ in the blood.</p>",
                    question_hi: "<p>14. लसीका श्वेत रक्त कोशिकाओं से बना एक हल्का तरल पदार्थ है जो रक्त में हानिकारक ______ पर हमला करता है।</p>",
                    options_en: ["<p>protozoa</p>", "<p>fungi</p>", 
                                "<p>viruses</p>", "<p>bacteria</p>"],
                    options_hi: ["<p>प्रोटोजोआ</p>", "<p>कवक</p>",
                                "<p>वायरस</p>", "<p>जीवाणु</p>"],
                    solution_en: "<p>14.(d) <strong>Bacteria</strong>. The corpuscles found in the lymph are called lymphocytes. <strong>Diseases caused by Bacteria :</strong> Diphtheria, pneumonia, cholera, tetanus, tuberculosis, plague, and gonorrhea. Fungi : Ringworm and athlete\'s foot.</p>",
                    solution_hi: "<p>14.(d) <strong>जीवाणु । </strong>लसीका में पाए जाने वाले कणिकाओं को लिम्फोसाइट्स कहा जाता है। <strong>जीवाणुओं से होने वाले रोग:</strong> डिप्थीरिया, निमोनिया, हैजा, टेटनस, तपेदिक, प्लेग और गोनोरिया। कवक: रिंगवर्म और एथलीट फुट।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which of the following is a large bean-shaped lymphoid organ in the human body?</p>",
                    question_hi: "<p>15. निम्नलिखित में से कौन मानव शरीर में बीन के आकार का एक बड़ा लिम्फोइड अंग है?</p>",
                    options_en: ["<p>Thymus</p>", "<p>Spleen</p>", 
                                "<p>Lymph node</p>", "<p>Tonsil</p>"],
                    options_hi: ["<p>थाइमस</p>", "<p>प्लीहा</p>",
                                "<p>लिम्फ नोड</p>", "<p>टॉन्सिल</p>"],
                    solution_en: "<p>15.(b) <strong>Spleen </strong>stores erythrocytes, lymphocytes, and phagocytes. It filters the bloodstream by trapping blood-borne pathogens in its cells.</p>",
                    solution_hi: "<p>15.(b) <strong>प्लीहा </strong>(Spleen) जो एरिथ्रोसाइट्स, लिम्फोसाइट्स और फैगोसाइट्स को संग्रहीत करता है । यह अपनी कोशिकाओं में रक्त जनित रोगजनकों को रोककर रक्तप्रवाह को फिल्टर करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>