<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 50</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">50</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 49,
                end: 49
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. In which of the following cities of British India was the Communist Party of India founded, in 1925?</p>",
                    question_hi: "<p>1. 1925 में ब्रिटिश भारत के निम्नलिखित में से किस शहर में भारतीय कम्युनिस्ट पार्टी की स्थापना हुई थी?</p>",
                    options_en: ["<p>Calcutta</p>", "<p>Lucknow</p>", 
                                "<p>Kanpur</p>", "<p>Delhi</p>"],
                    options_hi: ["<p>कलकत्ता</p>", "<p>लखनऊ</p>",
                                "<p>कानपुर</p>", "<p>दिल्ली</p>"],
                    solution_en: "<p><strong>1.(c) Kanpur. </strong>The founding members of the party were M.N. Roy, Evelyn Trent Roy, Abani Mukherji, M. P. T. Acharya. The Communist Party of India (Marxist) was formed at the Seventh Congress of the Communist Party of India held in Calcutta from October 31 to November 7, 1964.</p>",
                    solution_hi: "<p><strong>1.(c) कानपुर।</strong> पार्टी के संस्थापक सदस्य एम.एन. रॉय, एवलिन ट्रेंट रॉय, अबनी मुखर्जी, एम.पी.टी. आचार्य थे। भारतीय कम्युनिस्ट पार्टी (मार्क्सवादी) का गठन 31 अक्टूबर से 7 नवंबर, 1964 तक कलकत्ता में आयोजित भारतीय कम्युनिस्ट पार्टी की सातवीं अधिवेशन में किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Who among the following won the &lsquo;67th Filmfare Award 2022 for Best Choreography&rsquo; for the film Atrangi Re?</p>",
                    question_hi: "<p>2. निम्नलिखित में से किसने फिल्म अतरंगी रे के लिए \'सर्वश्रेष्ठ कोरियोग्राफी के लिए 67वां फिल्मफेयर पुरस्कार 2022\' जीता?</p>",
                    options_en: ["<p>Remo D&rsquo; Souza</p>", "<p>Vijay Ganguly</p>", 
                                "<p>Farah Khan</p>", "<p>Prabhu Deva</p>"],
                    options_hi: ["<p>रेमो डिसूजा</p>", "<p>विजय गांगुली</p>",
                                "<p>फराह खान</p>", "<p>प्रभु देवा</p>"],
                    solution_en: "<p><strong>2.(b) Vijay Ganguly.</strong> 68th Filmfare Awards 2023: Best choreography - Kruti Mahesh (Gangubai Kathiawadi &ldquo;Song - Dholida&rdquo;), Best Film: Gangubai Kathiyawadi, Best Actor (Male) - Raj Kumar Rao (Badhaai Do), Best Actress - Alia Bhatt (Gangubai Kathiawadi), Best Music Album - Pritam (Brahmastra Part One: Shiva). 1st Filmfare Award (1954): Best Film - Do Bigha Zamin, Best Actor - Dilip Kumar (Daag), Best Actress - Meena Kumari (Baiju Bawra), Best Director - Bimal Roy (Do Bigha Zamin).</p>",
                    solution_hi: "<p><strong>2.(b) विजय गांगुली ।</strong> 68वें फ़िल्मफ़ेयर पुरस्कार 2023: सर्वश्रेष्ठ कोरियोग्राफी - कृति महेश (गंगूबाई काठियावाड़ी \"सॉन्ग - ढोलिडा\"), सर्वश्रेष्ठ फिल्म: गंगूबाई काठियावाड़ी, सर्वश्रेष्ठ अभिनेता (पुरुष) - राज कुमार राव (बधाई दो), सर्वश्रेष्ठ अभिनेत्री - आलिया भट्ट (गंगूबाई काठियावाड़ी), सर्वश्रेष्ठ संगीत एल्बम - प्रीतम (ब्रह्मास्त्र भाग एक: शिव)। पहला फ़िल्मफ़ेयर पुरस्कार (1954): सर्वश्रेष्ठ फ़िल्म - दो बीघा ज़मीन, सर्वश्रेष्ठ अभिनेता - दिलीप कुमार (दाग), सर्वश्रेष्ठ अभिनेत्री - मीना कुमारी (बैजू बावरा), सर्वश्रेष्ठ निर्देशक - बिमल रॉय (दो बीघा ज़मीन)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. During the census 2011, which of the following states recorded the lowest growth rate?</p>",
                    question_hi: "<p>3. 2011 की जनगणना के दौरान, निम्नलिखित में से किस राज्य ने सबसे कम वृद्धि दर दर्ज की गई थी?</p>",
                    options_en: ["<p>Nagaland</p>", "<p>Sikkim</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>नागालैंड</p>", "<p>सिक्किम</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p><strong>3.(a) Nagaland (-0.6%).</strong> As per Census 2011, the country&rsquo;s Decadal Growth Rate was 17.7%. States/UTs by Decadal Population Growth Rate: Meghalaya (27.9%), Arunachal Pradesh (26.0%), Dadra &amp; Nagar Haveli (55.9%), Daman &amp; Diu (53.8%), Bihar (25.4%), NCT of Delhi (21.2%), Uttar Pradesh (20.2%), Madhya Pradesh (20.3%).</p>",
                    solution_hi: "<p><strong>3.(a) नागालैंड। </strong>2011 की जनगणना के अनुसार, देश की दशकीय विकास दर 17.7% थी। दशकीय जनसंख्या वृद्धि दर के अनुसार राज्य/केंद्र शासित प्रदेश: मेघालय (27.9%), अरुणाचल प्रदेश (26.0%), दादरा और नगर हवेली (55.9%), दमन और दीव (53.8%), बिहार (25.4%), राष्ट्रीय राजधानी क्षेत्र दिल्ली (21.2%) %), उत्तर प्रदेश (20.2%), मध्य प्रदेश (20.3%)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. Which of the following statements is correct with regard to the effect of globalisation on India and other developing countries?",
                    question_hi: "4. भारत और अन्य विकासशील देशों पर भूमंडलीकरण के प्रभाव के संबंध में निम्नलिखित में से कौन सा कथन <br />सत्य है?",
                    options_en: [" Flow of information became highly restricted.", " It led to increased inequality between countries. ", 
                                " Flow of international capital got restricted. ", " It reduced interdependence between countries."],
                    options_hi: [" सूचना का प्रवाह अत्यधिक प्रतिबंधित हो गया है।", " इससे देशों के बीच असमानता बढ़ी है।",
                                " अंतर्राष्ट्रीय पूंजी का प्रवाह प्रतिबंधित हो गया है।", " इससे देशों के बीच परस्पर निर्भरता को कम किया है।"],
                    solution_en: "4.(b) Globalization means integrating the Indian economy with the world economy. It is the outcome of the policies of liberalization and privatization.  Impacts of globalization on India’s economy: Industrial - There has been a massive influx of both foreign capital investment and companies expanding to and offshoring in India, particularly in the pharmaceutical manufacturing, chemical and petroleum industries. Agricultural - The technological capabilities of farmers have increased - helping drive global exports of Indian products such as tea, coffee and sugar. ",
                    solution_hi: "4.(b) भूमंडलीकरण का अर्थ है भारतीय अर्थव्यवस्था को विश्व अर्थव्यवस्था के साथ एकीकृत करना। यह उदारीकरण और निजीकरण की नीतियों का परिणाम है। भारत की अर्थव्यवस्था पर वैश्वीकरण के प्रभाव: औद्योगिक - विदेशी पूंजी निवेश और भारत में विस्तार करने वाली और ऑफशोरिंग करने वाली कंपनियों, विशेष रूप से फार्मास्युटिकल विनिर्माण, रसायन और पेट्रोलियम उद्योगों में बड़े पैमाने पर बढ़ोत्तरी हुई है। कृषि - किसानों की तकनीकी क्षमताओं में वृद्धि हुई है - चाय, कॉफी और चीनी जैसे भारतीय उत्पादों के वैश्विक निर्यात को बढ़ाने में मदद मिल रही है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. According to the Planning Commission of India (2011-12), which state has the highest percentage of the population below the poverty line?</p>",
                    question_hi: "<p>5. भारत के योजना आयोग (2011-12) के अनुसार, किस राज्य की जनसंख्या का सर्वाधिक प्रतिशत गरीबी रेखा से नीचे है?</p>",
                    options_en: ["<p>Chhattisgarh</p>", "<p>Assam</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Bihar</p>"],
                    options_hi: ["<p>छत्तीसगढ़</p>", "<p>असम</p>",
                                "<p>मध्य प्रदेश</p>", "<p>बिहार</p>"],
                    solution_en: "<p><strong>5.(a) Chhattisgarh. </strong>State-Wise Percentage of Population Below Poverty Line 2011-12 (Tendulkar Methodology): lowest - Goa (5.09%), Kerala (7.05%), Sikkim (8.19%), Himachal Pradesh (8.06%). Highest - Arunachal Pradesh (34.67%), Bihar (33.74%), Jharkhand (36.96%), Manipur (36.89%), Assam (31.98%), Madhya Pradesh (31.65%).</p>",
                    solution_hi: "<p><strong>5.(a) छत्तीसगढ़ । </strong>2011-12 में गरीबी रेखा से नीचे की जनसंख्या का राज्यवार प्रतिशत (तेंदुलकर पद्धति): सबसे कम - गोवा (5.09%), केरल (7.05%), सिक्किम (8.19%), हिमाचल प्रदेश (8.06%)। उच्चतम - अरुणाचल प्रदेश (34.67%), बिहार (33.74%), झारखंड (36.96%), मणिपुर (36.89%), असम (31.98%), मध्य प्रदेश (31.65%)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. Guru Amubi Singh won the Sangeet Natak Akademi Award for which form of dance?",
                    question_hi: "<p>6. गुरु अमुबी सिंह ने किस प्रकार के नृत्य के लिए संगीत नाटक अकादमी पुरस्कार जीता?</p>",
                    options_en: [" Manipuri", "<p>Bihu</p>", 
                                "<p>Odissi</p>", "<p>Kathakali</p>"],
                    options_hi: ["<p>मणिपुरी</p>", "<p>बिहु</p>",
                                "<p>ओडिसी</p>", "<p>कथकली</p>"],
                    solution_en: "<p><strong>6.(a) Manipuri. </strong>Guru Amubi Singh received Padma Shri in 1970. Dance exponents: Manipuri - Atombapu Sharma, Guru Bipin Singh, Thingbaijam Babu Singh, Darshana Jhaveri, Haobam Ongbi Ngangbi Devi. Odissi - Mayadhar Raut, Deba Prasad Das, Geeta Mahalik. Kathakali - Vazhengada Vijayan and Mankompu Sivasankara Pillai.</p>",
                    solution_hi: "<p><strong>6.(a) मणिपुरी। </strong>गुरु अमुबी सिंह को 1970 में पद्म श्री प्राप्त हुआ। नृत्य प्रतिपादक: मणिपुरी - एटम्बापू शर्मा, गुरु बिपिन सिंह, थिंगबैजम बाबू सिंह, दर्शन झावेरी, हाओबाम ओंगबी नगांगबी देवी। ओडिसी - मायाधर राउत, देबा प्रसाद दास, गीता महालिक। कथकली - वाझेंगदा विजयन और मनकोम्पु शिवशंकर पिल्लई।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. The Teliaya Dam is located in which state of India?",
                    question_hi: "<p>7.<math display=\"inline\"><mi>&#160;</mi></math>तिलैया बांध, भारत के किस राज्य में स्थित है?</p>",
                    options_en: [" Andhra Pradesh ", "<p>Jharkhand</p>", 
                                "<p>Karnataka</p>", "<p>Rajasthan</p>"],
                    options_hi: ["<p>आंध्र प्रदेश</p>", "<p>झारखंड</p>",
                                "<p>कर्नाटक</p>", "<p>राजस्थान</p>"],
                    solution_en: "<p>7.(b) <strong>Jharkhand</strong>. Dams in India: Tehri Dam (Uttarakhand), Idukki (Eb)/Idukki Arch Dam (Kerala), Baglihar Dam (Jammu Kashmir), Subansiri Lower Dam (Arunachal Pradesh), Pong Dam (Himachal Pradesh), Kakki Dam (Kerala), Nagi Dam (Bihar), Ukai Dam (Gujrat), Aruna Dam (Maharashtra), Lakhya Dam (Karnataka).</p>",
                    solution_hi: "<p>7.(b) <strong>झारखंड</strong>। भारत में बांध: टिहरी बांध (उत्तराखंड), इडुक्की (Eb)/इडुक्की आर्क बांध (केरल), बगलिहार बांध (जम्मू कश्मीर), सुबनसिरी लोअरबांध (अरुणाचल प्रदेश), पोंग बांध (हिमाचल प्रदेश), कक्की बांध (केरल), नागी बांध (बिहार), उकाई बांध (गुजरात), अरुणा बांध (महाराष्ट्र), लाखिया बांध (कर्नाटक)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8.  Who inaugurated the 44th Chess Olympiad, which was organised in 2022?",
                    question_hi: "<p>8. 2022 में आयोजित 44वें शतरंज ओलंपियाड का उ&zwnj;द्घाटन किसने किया था?</p>",
                    options_en: [" MK Stalin", "<p>Anurag Thakur</p>", 
                                "<p>Viswanathan Anand</p>", "<p>Narendra Modi</p>"],
                    options_hi: ["<p>एम. के. स्टालिन</p>", "<p>अनुराग ठाकुर</p>",
                                "<p>विश्वनाथन आनंद</p>", "<p>नरेंद्र मोदी</p>"],
                    solution_en: "<p><strong>8.(d) Narendra Modi. </strong>The 44th Chess Olympiad was organized by the International Chess Federation (FIDE) in Chennai. First place - Uzbekistan. International Chess Federation (FIDE): Founded - 1924. Headquarters: Lausanne, Switzerland. All India Chess Federation (AICF): Founded - 1951.</p>",
                    solution_hi: "<p><strong>8.(d) नरेंद्र मोदी । </strong>44वें शतरंज ओलंपियाड का आयोजन अंतर्राष्ट्रीय शतरंज महासंघ (FIDE) द्वारा चेन्नई में किया गया था। प्रथम स्थान - उज्बेकिस्तान। अंतर्राष्ट्रीय शतरंज महासंघ (FIDE): स्थापना - 1924। मुख्यालय: लॉज़ेन, स्विट्जरलैंड। अखिल भारतीय शतरंज महासंघ (AICF): स्थापना - 1951।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "9. Who among the following dancers and choreographers is credited to bring fusion art into a dance where European style is mixed with Indian Classical Dance?",
                    question_hi: "<p>9. निम्नलिखित में से किस नर्तक और कोरियोग्राफर को भारतीय शास्त्रीय नृत्य के साथ यूरोपीय शैली के मिश्रण वाले नृत्य में संलयन कला लाने का श्रेय दिया जाता है?</p>",
                    options_en: [" Uday Shankar", "<p>Kelucharan Mohapatra</p>", 
                                "<p>Rukmini Devi Arundale</p>", "<p>Birju Maharaj</p>"],
                    options_hi: ["<p>उदय शंकर</p>", "<p>केलुचरण महापात्र</p>",
                                "<p>रुक्मिणी देवी अरुंडेल</p>", "<p>बिरजू महाराज</p>"],
                    solution_en: "<p><strong>9.(a) Uday Shankar.</strong> He received Padma Vibhushan in 1971. Dance exponents: Kathak - Birju Maharaj, Damayanti Joshi, Kumudini Lakhia. Bharatanatyam - T. Balasaraswati, Rukmini Devi Arundale, Vazhuvoor B. Ramiah Pillai. Odissi - Kelucharan Mohapatra, Pankaj Charan Das, Gangadhar Pradhan and Sharmila Biswas.</p>",
                    solution_hi: "<p><strong>9.(a) उदय शंकर । </strong>उन्हें 1971 में पद्म विभूषण मिला। नृत्य प्रतिपादक: कथक - बिरजू महाराज, दमयंती जोशी, कुमुदिनी लाखिया। भरतनाट्यम - टी. बालासरस्वती, रुक्मिणी देवी अरुंडेल, वझुवूर बी. रामैया पिल्लई। ओडिसी - केलुचरण महापात्र, पंकज चरण दास, गंगाधर प्रधान और शर्मिला विश्वास।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which scheme was rolled out to effectively integrate India&rsquo;s manufacturing capabilities with the demands of global supply chains and to promote domestic value addition and exports?</p>",
                    question_hi: "<p>10. वैश्विक आपूर्ति श्रृंखलाओं की माँगों के साथ भारत की विनिर्माण क्षमताओं को प्रभावी ढंग से एकीकृत करने और घरेलू मूल्यवर्धन एवं निर्यात को बढ़ावा देने के लिए कौन-सी योजना शुरू की गई थी?</p>",
                    options_en: ["<p>Startup India</p>", "<p>Merchandise Exports from India Scheme</p>", 
                                "<p>Make in India initiative</p>", "<p>Production Linked Incentive Scheme</p>"],
                    options_hi: ["<p>स्टार्टअप इंडिया</p>", "<p>मर्चेंडाइज एक्सपोर्ट फ्रॉम इंडिया स्कीम</p>",
                                "<p>मेक इन इंडिया पहल</p>", "<p>प्रोडक्शन लिंक्ड इन्सेंटिव स्कीम</p>"],
                    solution_en: "<p><strong>10.(d) Production Linked Incentive Scheme. </strong>Manufacturing related schemes of India: Make in India initiative (2014), National Logistics Policy (2022), Indian Footwear and Leather Development Programme, North East Industrial and Investment Promotion Policy, 2007. North East Industrial Development Scheme (2017), Udyami Bharat Scheme (2022).</p>",
                    solution_hi: "<p><strong>10.(d) प्रोडक्शन लिंक्ड इन्सेंटिव स्कीम। </strong>भारत की विनिर्माण संबंधी योजनाएँ: मेक इन इंडिया पहल (2014), राष्ट्रीय लॉजिस्टिक्स नीति (2022), भारतीय जूते और चमड़ा विकास कार्यक्रम, उत्तर पूर्व औद्योगिक और निवेश प्रोत्साहन नीति, 2007। उत्तर पूर्व औद्योगिक विकास योजना (2017), उद्यमी भारत योजना (2022)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11.  What is the name of the autobiography written by the 14th Dalai Lama?",
                    question_hi: "<p>11. 14वें दलाई लामा द्वारा लिखित आत्मकथा का नाम क्या है?</p>",
                    options_en: ["<p>My Country My Life</p>", "<p>Living with the Himalayan Masters</p>", 
                                "<p>Freedom in Exile</p>", "<p>Long Walk to Freedom</p>"],
                    options_hi: ["<p>माई कंट्री माई लाइफ</p>", "<p>लिविंग विद द हिमालयन मास्टर्स</p>",
                                "<p>फ्रीडम इन एक्ज़ाइल</p>", "<p>लांग वाक टु फ्रीडम</p>"],
                    solution_en: "<p><strong>11.(c) Freedom in Exile. </strong>His other books: &ldquo;The Art of Happiness&rdquo;, &ldquo;The Four Noble Truths&rdquo;, &ldquo;Beyond Religion: Ethics for a Whole World&rdquo;. Other Autobiographies: Lal Krishna Advani - &ldquo;My Country My Life&rdquo;. Nelson Mandela - &ldquo;Long Walk to Freedom&rdquo;. Swami Rama - &ldquo;Living with the Himalayan Masters&rdquo;.</p>",
                    solution_hi: "<p><strong>11.(c) फ्रीडम इन एक्ज़ाइल ।</strong> उनकी अन्य पुस्तकें: \"द आर्ट ऑफ हैप्पीनेस\", \"द फोर नोबल ट्रुथ\", \"बियॉन्ड रिलिजन: एथिक्स फॉर ए होल वर्ल्ड\"। अन्य आत्मकथाएँ: लाल कृष्ण आडवाणी - \"माई कंट्री माई लाइफ\"। नेल्सन मंडेला - \"लांग वॉक टू फ्रीडम\"। स्वामी राम - \"लिविंग विद् द हिमालयन मास्टर्स\"।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. From which country did India import wheat seeds during the Green Revolution?",
                    question_hi: "<p>12. हरित क्रांति के दौरान भारत ने किस देश से गेहूँ के बीज का आयात किया था?</p>",
                    options_en: [" USA", "<p>Russia</p>", 
                                "<p>Britain</p>", "<p>Mexico</p>"],
                    options_hi: ["<p>यूएसए</p>", "<p>रूस</p>",
                                "<p>ब्रिटेन</p>", "<p>मेक्सिको</p>"],
                    solution_en: "<p><strong>12.(d) Mexico. T</strong>he Green Revolution in India started in the late 1960s in Punjab. During this period, India saw a shift in its agricultural practices. The nation moved toward a more industrial system and adopted technologies like high-yielding variety (HYV) seeds, mechanized farm tools, irrigation systems, pesticides, and fertilizers. Father of Indian green revolution - Dr. MS Swaminathan. Father of the green revolution in the world - Norman Borlaug.</p>",
                    solution_hi: "<p><strong>12.(d) मेक्सिको । </strong>भारत में हरित क्रांति 1960 के दशक के अंत में पंजाब में शुरू हुई। इस अवधि के दौरान, भारत ने अपनी कृषि पद्धतियों में बदलाव देखा। राष्ट्र एक अधिक औद्योगिक प्रणाली की ओर बढ़ा और उच्च उपज देने वाले किस्म (HYV) बीज, मशीनीकृत कृषि उपकरण, सिंचाई प्रणाली, कीटनाशक और उर्वरक जैसी तकनीकों को अपनाया। भारतीय हरित क्रांति के जनक - डॉ. एम.एस. स्वामीनाथन। विश्व में हरित क्रांति के जनक - नॉर्मन बोरलॉग।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Ayushman Bharat Programme is an umbrella of ________ scheme launched in 2018.</p>",
                    question_hi: "<p>13. आयुष्मान भारत कार्यक्रम, 2018 में शुरू की गई ________ योजना का एक समूह है।</p>",
                    options_en: ["<p>employment</p>", "<p>education</p>", 
                                "<p>health</p>", "<p>livelihood</p>"],
                    options_hi: ["<p>रोज़गार</p>", "<p>शिक्षा</p>",
                                "<p>स्वास्थ्य</p>", "<p>आजीविका</p>"],
                    solution_en: "<p><strong>13.(c) health. </strong>Healthcare Schemes in India: Mission Indradhanush (2014), Pradhan Mantri Surakshit Matritva Abhiyan (2016), Pulse Polio Immunization Programme (1995), National Rural Health Mission (2005), National Urban Health Mission (2013), Janani Suraksha Yojana (2005).</p>",
                    solution_hi: "<p><strong>13.(c) स्वास्थ्य। </strong>भारत में स्वास्थ्य देखभाल योजनाएं: मिशन इंद्रधनुष (2014), प्रधान मंत्री सुरक्षित मातृत्व अभियान (2016), पल्स पोलियो टीकाकरण कार्यक्रम (1995), राष्ट्रीय ग्रामीण स्वास्थ्य मिशन (2005), राष्ट्रीय शहरी स्वास्थ्य मिशन (2013), जननी सुरक्षा योजना (2005)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following is NOT a type of budget of the Government?</p>",
                    question_hi: "<p>14. निम्नलिखित में से कौन सरकारी बजट नहीं है?</p>",
                    options_en: ["<p>Surplus budget</p>", "<p>Deficit budget</p>", 
                                "<p>Circle budget</p>", "<p>Balanced budget</p>"],
                    options_hi: ["<p>अधिशेष बजट</p>", "<p>घाटे का बजट</p>",
                                "<p>सर्किल बजट</p>", "<p>संतुलित बजट</p>"],
                    solution_en: "<p><strong>14.(c) Circle budget. </strong>Balanced Budget - A budget is deemed a balanced one if the expected government expenses equal the estimated government receipts during a given financial year. Surplus Budget - The budget presented by the government is considered a surplus budget in case the revenue expected by the government exceeds the expenditure the government makes during a given financial year. Deficit budget - A budget is deemed a deficit one when the expected government expense exceeds the revenue the government expects to accumulate in a given financial year.</p>",
                    solution_hi: "<p><strong>14.(c) सर्किल बजट। </strong>संतुलित बजट - एक बजट को संतुलित माना जाता है, यदि किसी दिए गए वित्तीय वर्ष के दौरान अपेक्षित सरकारी खर्च अनुमानित सरकारी प्राप्तियों के बराबर हो। अधिशेष बजट - सरकार द्वारा प्रस्तुत बजट को अधिशेष बजट माना जाता है, यदि सरकार द्वारा अपेक्षित राजस्व किसी दिए गए वित्तीय वर्ष के दौरान सरकार द्वारा किए गए व्यय से अधिक हो जाता है। घाटे का बजट - एक बजट को घाटे का बजट माना जाता है, जब अपेक्षित सरकारी व्यय सरकार द्वारा अपेक्षित राजस्व से अधिक हो जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Stainless steel cookware contains _______________.</p>",
                    question_hi: "<p>15. स्टेनलेस स्टील कुकवेयर में _______________ होता है।</p>",
                    options_en: ["<p>chromium</p>", "<p>magnesium</p>", 
                                "<p>calcium</p>", "<p>manganese</p>"],
                    options_hi: ["<p>क्रोमियम</p>", "<p>मैंगनीज</p>",
                                "<p>कैल्शियम</p>", "<p>मैग्निशियम</p>"],
                    solution_en: "<p><strong>15.(a) chromium. </strong>Stainless steel is an iron-based alloy that contains 18% of chromium and 8% nickel. There are three main classes of stainless steels: austenitic, ferritic, and martensitic. Uses of Stainless steel: Culinary uses, Kitchen sinks, Surgical tools and medical equipment, Airport roofs, Aircraft, Automotive and aerospace applications.</p>",
                    solution_hi: "<p><strong>15.(a) क्रोमियम।</strong> स्टेनलेस स्टील एक लौह-आधारित मिश्र धातु है जिसमें 18% क्रोमियम और 8% निकल होता है। स्टेनलेस स्टील के तीन मुख्य वर्ग हैं: ऑस्टेनिटिक, फेरिटिक और मार्टेंसिटिक। स्टेनलेस स्टील के उपयोग: पाककला उपयोग, रसोई सिंक, सर्जिकल उपकरण और चिकित्सा उपकरण, हवाई अड्डे की छतें, विमान, मोटर वाहन और एयरोस्पेस अनुप्रयोग।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Which of the following Articles of the Indian Constitution deals with the District Planning Committee?</p>",
                    question_hi: "<p>16. भारतीय संविधान का निम्नलिखित में से कौन-सा अनुच्छेद जिला योजना समिति से संबंधित है?</p>",
                    options_en: ["<p>243-XD</p>", "<p>243-ZD</p>", 
                                "<p>243-YD</p>", "<p>243- WD</p>"],
                    options_hi: ["<p>243-XD</p>", "<p>243-ZD</p>",
                                "<p>243-YD</p>", "<p>243- WD</p>"],
                    solution_en: "<p><strong>16.(b) 243-ZD. </strong>Part IXA The Municipalities Article (243P - 243ZG): Article 243 ZE - Committee for Metropolitan planning. Article 243Q - Constitution of Municipalities. Article 243 R - Composition of Municipalities. Article 243 W - Powers, authority and responsibilities of Municipalities, etc. Article 243 ZA - Elections to the Municipalities.</p>",
                    solution_hi: "<p><strong>16.(b) 243-ZD। </strong>भाग IXA नगर पालिकाएँ अनुच्छेद (243P - 243ZG): अनुच्छेद 243 ZE - महानगरीय योजना के लिए समिति। अनुच्छेद 243 Q - नगर पालिकाओं का गठन। अनुच्छेद 243 R - नगर पालिकाओं की संरचना। अनुच्छेद 243 W - नगर पालिकाओं की शक्तियां, अधिकार और जिम्मेदारियां। अनुच्छेद 243 ZA - नगर पालिकाओं के लिए चुनाव।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. &lsquo;Vrittayata&rsquo; is a major sub-division of ________ style of temple architecture.</p>",
                    question_hi: "<p>17. \'वृत्तायता\', मंदिर वास्तुकला की ________ शैली का एक प्रमुख उप-भाग है।</p>",
                    options_en: ["<p>Hoysala</p>", "<p>Nagara</p>", 
                                "<p>Dravidian</p>", "<p>Vijayanagara</p>"],
                    options_hi: ["<p>होयसल</p>", "<p>नागर</p>",
                                "<p>द्रविड़</p>", "<p>विजयनगर</p>"],
                    solution_en: "<p><strong>17.(c) Dravidian. </strong>The Dravidian style of temple architecture of South India was pioneered by the Pallavas who reigned in parts of Karnataka, Andhra Pradesh, and northern Tamil Nadu. Classification of Dravidian temples: square, usually called kuta; rectangular or shala or ayatasra; elliptical, called Gaja-Prishta or also called vrittayata; circular or vritta; and octagonal or ashtasra.</p>",
                    solution_hi: "<p><strong>17.(c) द्रविड़ । </strong>दक्षिण भारत की मंदिर वास्तुकला की द्रविड़ शैली की शुरुआत पल्लवों द्वारा की गई थी जिन्होंने कर्नाटक, आंध्र प्रदेश और उत्तरी तमिलनाडु के कुछ हिस्सों में शासन किया था। द्रविड़ मंदिरों का वर्गीकरण: वर्गाकार, जिसे आमतौर पर कुटा कहा जाता है; आयताकार या शाला या आयतस्र; अण्डाकार, जिसे गज-पृष्ठ कहा जाता है या वृत्तायत भी कहा जाता है; गोलाकार या वृत्त; और अष्टकोणीय या अष्टस्र ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. Which of the following statements about plastids is INCORRECT?",
                    question_hi: "18. प्लास्टिड (plastids) से संबंधित निम्नलिखित में से कौन-सा कथन गलत है?",
                    options_en: [" Leucoplasts are primarily organelles in which materials such as starch, oils and protein <br />     granules are stored.", " Leucoplasts are white or colourless plastids.", 
                                " Plastids do not have their own DNA and ribosomes.", " Chromoplasts are responsible for photosynthesis in plants."],
                    options_hi: [" ल्यूकोप्लास्ट, मुख्य रूप से वे अंगक होते हैं, जिनमें स्टार्च, तेल और प्रोटीन कणिकाओं जैसे पदार्थ जमा होते हैं।  ", " ल्यूकोप्लास्ट, सफेद या रंगहीन प्लास्टिड होते हैं। ",
                                " प्लास्टिड का अपना डीएनए (DNA) और राइबोसोम नहीं होता है।  ", " क्रोमोप्लास्ट, पौधों में प्रकाश संश्लेषण के लिए जिम्मेदार होते हैं।"],
                    solution_en: "18.(c) Plastid is a two-layered membrane-bound organelle found in the cells of plants. Plastids are found in all plant cells and in euglenoides. They bear some specific pigments, thus imparting specific colours to the plants. Based on the type of pigments plastids can be classified into chloroplasts, chromoplasts and leucoplasts. ",
                    solution_hi: "18.(c) प्लास्टिड पौधों की कोशिकाओं में पाया जाने वाला एक दो-परतीय झिल्ली-बद्ध अंग है। प्लास्टिड सभी पौधों की कोशिकाओं और यूग्लेनोइड्स में पाए जाते हैं। वे कुछ विशिष्ट रंग धारण करते हैं, इस प्रकार पौधों को विशिष्ट रंग प्रदान करते हैं। पिगमेंट के प्रकार के आधार पर प्लास्टिड को क्लोरोप्लास्ट, क्रोमोप्लास्ट और ल्यूकोप्लास्ट में वर्गीकृत किया जा सकता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "19. Which of the following dances means ‘dance of the enchantress’?",
                    question_hi: "<p>19. निम्नलिखित में से किस नृत्य का अर्थ \'मोहिनी का नृत्य\' है?</p>",
                    options_en: [" Kathak", " Sattriya", 
                                " Kuchipudi", "<p>Mohiniyattam</p>"],
                    options_hi: ["<p>कथक</p>", "<p>सत्रीया</p>",
                                "<p>कुचिपुड़ी</p>", "<p>मोहिनीअट्टम</p>"],
                    solution_en: "<p>19.(d) <strong>Mohiniyattam</strong> is a classical dance form of Kerala. The origin and popularity of this dance form is closely tagged to the great Tamil dance master Vadivelu. It is associated with Lord Vishnu. Famous Mohiniyattam exponents: Kalamandalam Kalyanikutty Amma, Bharati Shivaji, V. K. Hymavathy, Mandakini Trivedi and Gopika Varma.</p>",
                    solution_hi: "<p>19.(d<strong>) मोहिनीअट्टम </strong>केरल का एक शास्त्रीय नृत्य है। इस नृत्य शैली की उत्पत्ति और लोकप्रियता का गहरा संबंध महान तमिल नृत्य गुरु वदिवेलु से है। इसका संबंध भगवान विष्णु से है। प्रसिद्ध मोहिनीअट्टम प्रतिपादक: कलामंडलम कल्याणिकुट्टी अम्मा, भारती शिवाजी, वी.के. ह्यमावती, मंदाकिनी त्रिवेदी और गोपिका वर्मा।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. ‘Faster, More Inclusive and Sustainable Growth’ was the centre point of which FiveYear Plan?",
                    question_hi: "20. \'तीव्र, अधिक समावेशी और संधारणीय वृद्धि (Faster, More Inclusive and Sustainable Growth)\' किस पंचवर्षीय योजना का केंद्र बिंदु था?",
                    options_en: [" Tenth", " Twelfth", 
                                " Ninth", " Seventh"],
                    options_hi: [" दसवीं", " बारहवीं ",
                                " नौवीं ", " सातवीं"],
                    solution_en: "20.(b) Twelfth. The first Five-year Plan was launched in 1951. First Five Year Plan (1951 - 56): It was based on the Harrod-Domar Model, Target Growth - 2.1 %. Second FYP (1956 - 61) also called Mahalanobis Plan:  Target Growth - 4.5%. Seventh Plan (1985 - 90): Target Growth - 5.0%. Ninth Plan (1997- 2002) focussed on “Growth With Social Justice & Equality”, Target Growth - 6.5%. Tenth Plan (2002 - 2007): Target Growth - 8 %.<br /> ",
                    solution_hi: "20.(b) बारहवीं । पहली पंचवर्षीय योजना 1951 में शुरू की गई थी। पहली पंचवर्षीय योजना (1951 - 56): यह हैरोड-डोमर मॉडल पर आधारित थी, लक्ष्य वृद्धि - 2.1%। दूसरी FYP (1956 - 61) जिसे महालनोबिस योजना भी कहा जाता है: लक्ष्य वृद्धि - 4.5%। सातवीं योजना (1985 - 90): लक्ष्य वृद्धि - 5.0%। नौवीं योजना (1997-2002) \"सामाजिक न्याय के साथ विकास\" पर केंद्रित थी, लक्ष्य वृद्धि - 6.5%। दसवीं योजना (2002 - 2007): लक्ष्य वृद्धि - 8%।<br /> ",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. The Constitution (42nd Amendment) Act, 1976, Chapter IV-A introduced a code of ten _____________ for citizens.",
                    question_hi: "<p>21. संविधान (42वाँ संशोधन) अधिनियम, 1976, भाग IV-A ने नागरिकों के लिए दस _____________ का एक नियमसंग्रह पेश किया।</p>",
                    options_en: [" Fundamental Rights", "<p>Fundamental Duties</p>", 
                                "<p>Fundamental Principles</p>", "<p>Directive Principles</p>"],
                    options_hi: ["<p>मौलिक अधिकारों</p>", "<p>मौलिक कर्तव्यों</p>",
                                "<p>मौलिक सिद्धांतों</p>", "<p>निर्देशक सिद्धांतों</p>"],
                    solution_en: "<p><strong>21.(b) Fundamental Duties. </strong>The idea of Fundamental Duties is inspired from the Constitution of Russia (erstwhile Soviet Union). These were incorporated in the Constitution on the recommendations of the Swaran Singh Committee. Originally 10 in number, one more duty was added through the 86th Constitutional Amendment Act, 2002. All the eleven duties are listed in Article 51-A of the Constitution.</p>",
                    solution_hi: "<p>21.(b) मौलिक कर्तव्यों । मौलिक कर्तव्यों का विचार रूस (तत्कालीन सोवियत संघ) के संविधान से प्रेरित है। इन्हें स्वर्ण सिंह समिति की सिफ़ारिशों पर संविधान में शामिल किया गया था। मूल रूप से संख्या में 10, 86वें संवैधानिक संशोधन अधिनियम, 2002 के माध्यम से एक और कर्तव्य जोड़ा गया। सभी ग्यारह कर्तव्य संविधान के अनुच्छेद 51-A में सूचीबद्ध हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. Which of the following is NOT a national festival?</p>",
                    question_hi: "<p>22. निम्नलिखित में से कौन सा राष्ट्रीय पर्व नहीं है?</p>",
                    options_en: ["<p>Diwali</p>", "<p>Gandhi Jayanti</p>", 
                                "<p>Republic Day</p>", "<p>Independence Day</p>"],
                    options_hi: ["<p>दिवाली</p>", "<p>गांधी जयंती</p>",
                                "<p>गणतंत्र दिवस</p>", "<p>स्वतंत्रता दिवस</p>"],
                    solution_en: "<p><strong>22.(a) Diwali. </strong>Gandhi Jayanti is celebrated on 2nd October every year to mark the birth anniversary of Mohandas Karamchand Gandhi. Republic Day marks the adoption of India\'s constitution and the country\'s transition to a republic on January 26, 1950. Independence Day is celebrated annually on 15 August as a national holiday in India commemorating the nation\'s independence from the United Kingdom on 15 August 1947.</p>",
                    solution_hi: "<p><strong>22.(a) दिवाली। </strong>मोहनदास करमचंद गांधी की जयंती के उपलक्ष्य में हर साल 2 अक्टूबर को गांधी जयंती मनाई जाती है। गणतंत्र दिवस 26 जनवरी 1950 को भारत के संविधान को अपनाने और देश के गणतंत्र में परिवर्तित होने का प्रतीक है। 15 अगस्त 1947 को यूनाइटेड किंगडम से देश की आजादी की याद में भारत में राष्ट्रीय अवकाश के रूप में 15 अगस्त को हर साल स्वतंत्रता दिवस मनाया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. What does LPG cooking gas contain?</p>",
                    question_hi: "<p>23. एलपीजी (LPG) रसोई गैस में क्या होता है?</p>",
                    options_en: ["<p>Methane and ethylene</p>", "<p>Propane and ethane</p>", 
                                "<p>Propane and butane</p>", "<p>Methane and propane</p>"],
                    options_hi: ["<p>मेथेन और एथिलीन</p>", "<p>प्रोपेन और ईथेन</p>",
                                "<p>प्रोपेन और ब्यूटेन</p>", "<p>मेथेन और प्रोपेन</p>"],
                    solution_en: "<p><strong>23.(c) Propane and butane. </strong>Liquefied petroleum gas (LPG) is composed of hydrocarbons containing three or four carbon atoms. LPG is any of multiple liquid mixtures of the volatile hydrocarbons propane (C3H8), propylene (C3H6), butane (C4H10), and butylene (C4H8).</p>",
                    solution_hi: "<p><strong>23.(c) प्रोपेन और ब्यूटेन । </strong>तरलीकृत पेट्रोलियम गैस (LPG) तीन या चार कार्बन परमाणुओं वाले हाइड्रोकार्बन से बनी होती है। LPG वाष्पशील हाइड्रोकार्बन प्रोपेन (C3H8), प्रोपलीन (C3H6), ब्यूटेन (C4H10), और ब्यूटिलीन (C4H8) के कई तरल मिश्रणों में से एक है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Under which Constitutional Amendment Act were the Fundamental Duties first added to the Indian Constitution?</p>",
                    question_hi: "<p>24. सबसे पहले किस संविधान संशोधन अधिनियम के तहत मौलिक कर्तव्यों को भारतीय संविधान में जोड़ा गया था?</p>",
                    options_en: ["<p>40th Constitutional Amendment Act</p>", "<p>43rd Constitutional Amendment Act</p>", 
                                "<p>41st Constitutional Amendment Act</p>", "<p>42nd Constitutional Amendment Act</p>"],
                    options_hi: ["<p>40वाँ संविधान संशोधन अधिनियम</p>", "<p>43वाँ संविधान संशोधन अधिनियम</p>",
                                "<p>41वाँ संविधान संशोधन अधिनियम</p>", "<p>42वाँ संविधान संशोधन अधिनियम</p>"],
                    solution_en: "<p><strong>24.(d) 42nd Constitutional Amendment Act 1976:</strong> This act is also known as &ldquo;mini constitution&rdquo;. 41th Constitutional Amendment Act 1976 - Raise Retirement Age Limit of Chairman and Members of Joint Public Service Commissions and State Public Service Commissions from sixty to sixty two. 43rd Constitutional Amendment Act 1977 - Restored the jurisdiction of the Supreme Court and the High Courts in respect of judicial review and issue of writs.</p>",
                    solution_hi: "<p><strong>24.(d) 42वाँ संविधान संशोधन अधिनियम 1976:</strong> इस अधिनियम को \"लघु संविधान\" के नाम से भी जाना जाता है। 41वां संवैधानिक संशोधन अधिनियम 1976 - संयुक्त लोक सेवा आयोगों और राज्य लोक सेवा आयोगों के अध्यक्षों और सदस्यों की सेवानिवृत्ति आयु सीमा को 60 से बढ़ाकर 62 तक करना। 43वां संवैधानिक संशोधन अधिनियम 1977 - न्यायिक समीक्षा और रिट जारी करने के संबंध में सर्वोच्च न्यायालय और उच्च न्यायालयों के अधिकार क्षेत्र को बहाल किया गया ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. ________ delegates from all over the country congregated for the first session of the Indian National Congress at Bombay in December 1885.",
                    question_hi: "<p>25. दिसम्बर 1885 में बंबई में भारतीय राष्ट्रीय कांग्रेस के पहले अधिवेशन के लिए देश भर से ________ प्रतिनिधि एकत्रित हुए थ</p>",
                    options_en: [" 105", " 72", 
                                " 51", "<p>179</p>"],
                    options_hi: ["<p>105</p>", "<p>72</p>",
                                "<p>51</p>", "<p>179</p>"],
                    solution_en: "<p><strong>25.(b) 72. </strong>First Session of the Congress was held on 28 December 1885, at Gokuldas Tejpal Sanskrit College, Bombay. President - Womesh Chandra Bonnerjee. The Second Session of the Congress took place under the leadership of Dadabhai Naoroji in Calcutta. The number of delegates had increased to 434. 3rd Session (Madras): President - Badruddin Tyabji. 16th Session (Lahore): President - N.G. Chandavarkar.</p>",
                    solution_hi: "<p><strong>25.(b) 72 । </strong>कांग्रेस का पहला सत्र 28 दिसंबर 1885 को गोकुलदास तेजपाल संस्कृत कॉलेज, बॉम्बे में आयोजित किया गया था। अध्यक्ष - व्योमेश चंद्र बनर्जी। कांग्रेस का दूसरा अधिवेशन दादाभाई नौरोजी के नेतृत्व में कलकत्ता में हुआ। प्रतिनिधियों की संख्या बढ़कर 434 हो गई थी। तीसरा सत्र (मद्रास): अध्यक्ष - बदरुद्दीन तैयबजी। 16वाँ सत्र (लाहौर): अध्यक्ष - एन.जी. चंदावरकर ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Purushottam Dadheech is a famous and recognised Indian __________ dancer.</p>",
                    question_hi: "<p>26. पुरूषोत्तम दधीच एक प्रसिद्ध और सम्&zwj;मानित भारतीय ____________ नर्तक हैं।</p>",
                    options_en: ["<p>Kathak</p>", "<p>Kuchipudi</p>", 
                                "<p>Sattriya</p>", "<p>Kathakali</p>"],
                    options_hi: ["<p>कथक</p>", "<p>कुचिपुड़ी</p>",
                                "<p>सत्रिया</p>", "<p>कथकली</p>"],
                    solution_en: "<p><strong>26.(a) Kathak. </strong>His Awards: Padma Shri (2020), Sangeet Natak Akademi Award (2018). Dance exponents: Kathak - Shambhu Maharaj, Rani Karnaa, Munna Shukla, Uma Dogra. Kuchipudi - Manju Barggavee, Vedantam Ramalinga Sastry, Veernala Jayarama Rao. Sattriya - Indira P. P. Bora, Ghanakanta Bora, Jatin Goswami. Kathakali - Madavoor Vasudevan Nair, Kottakkal Sivaraman, and Thonnakkal Peethambaran.</p>",
                    solution_hi: "<p><strong>26.(a) कथक । </strong>उनके पुरस्कार: पद्म श्री (2020), संगीत नाटक अकादमी पुरस्कार (2018)। नृत्य प्रतिपादक: कथक - शंभू महाराज, रानी कर्ण, मुन्ना शुक्ला, उमा डोगरा। कुचिपुड़ी - मंजू बरगावी, वेदांतम रामलिंगा शास्त्री, वीरनाला जयाराम राव। सत्रिया - इंदिरा पी. पी. बोरा, घनकांत बोरा, जतिन गोस्वामी। कथकली - मदावूर वासुदेवन नायर, कोट्टक्कल शिवरामन, और थोन्नक्कल पीतांबरन।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which directive principle provides for a uniform civil code for citizens?</p>",
                    question_hi: "<p>27. कौन-सा नीति निदेशक सिद्धांत नागरिकों के लिए एक समान नागरिक संहिता प्रदान करता है?</p>",
                    options_en: ["<p>Article 44</p>", "<p>Article 41</p>", 
                                "<p>Article 42</p>", "<p>Article 43</p>"],
                    options_hi: ["<p>अनुच्छेद 44</p>", "<p>अनुच्छेद 41</p>",
                                "<p>अनुच्छेद 42</p>", "<p>अनुच्छेद 43</p>"],
                    solution_en: "<p><strong>27.(a) Article 44. </strong>Other Articles: Article 41 - Right to work, to education and to public assistance in certain cases. Article 42 - Provision for just and humane conditions of work and maternity relief. Article 43 - Living wage, etc., for workers. Article 43A - Participation of workers in management of Industries. Article 43B - Promotion of co-operative societies.</p>",
                    solution_hi: "<p><strong>27.(a) अनुच्छेद 44। </strong>अन्य अनुच्छेद: अनुच्छेद 41 - काम करने का, शिक्षा का और कुछ मामलों में सार्वजनिक सहायता का अधिकार। अनुच्छेद 42 - काम की न्यायसंगत और मानवीय परिस्थितियों और मातृत्व राहत का प्रावधान। अनुच्छेद 43 - श्रमिकों के लिए जीवन निर्वाह मजदूरी आदि। अनुच्छेद 43A - उद्योगों के प्रबंधन में श्रमिकों की भागीदारी। अनुच्छेद 43B - सहकारी समितियों को बढ़ावा देना।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. According to the K&ouml;ppen classification, which group of climate is generally found at higher latitudes or higher altitudes?</p>",
                    question_hi: "<p>28. कोपेन वर्गीकरण के अनुसार जलवायु का कौन-सा समूह सामान्यतः उच्चतर अक्षांशों या अत्यधिक ऊँचाई वाले स्थानों पर पाया जाता है?</p>",
                    options_en: ["<p>D group</p>", "<p>E group</p>", 
                                "<p>C group</p>", "<p>A group</p>"],
                    options_hi: ["<p>समूह D</p>", "<p>समूह E</p>",
                                "<p>समूह C</p>", "<p>समूह A</p>"],
                    solution_en: "<p><strong>28.(b) E group. </strong>Koeppen recognised five major climatic groups, four of them are based on temperature and one on precipitation. Climatic Groups According to Koeppen: A - Tropical, B - Dry Climates, C - Warm Temperate, D - Cold Snow Forest Climates, E - Cold Climates, H - HighLand. The climatic groups are subdivided into types, designated by small letters, based on seasonality of precipitation and temperature characteristics. The seasons of dryness are indicated by the small letters : f, m, w and s.</p>",
                    solution_hi: "<p><strong>28.(b) समूह E । </strong>कोप्पेन ने पांच प्रमुख जलवायु समूहों की पहचान की, उनमें से चार तापमान पर और एक वर्षा पर आधारित है। कोप्पेन के अनुसार जलवायु समूह: A - उष्णकटिबंधीय, B - शुष्क जलवायु, C - गर्म शीतोष्ण, D - ठंडी बर्फीली वन जलवायु, E - ठंडी जलवायु, H - उच्च भूमि। वर्षा की मौसमी प्रकृति और तापमान विशेषताओं के आधार पर जलवायु समूहों को छोटे अक्षरों द्वारा निर्दिष्ट प्रकारों में विभाजित किया गया है। शुष्कता के मौसम को छोटे अक्षरों से दर्शाया जाता है: f, m, w और s ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. The final event in decathlon is always:</p>",
                    question_hi: "<p>29. डेकाथिलॉन में अंतिम प्रतियोगिता सदैव _______ होती है।</p>",
                    options_en: ["<p>110 m hurdle</p>", "<p>1500 m race</p>", 
                                "<p>javelin throw</p>", "<p>800 m race</p>"],
                    options_hi: ["<p>110 m बाधा दौड़</p>", "<p>1500 m दौड़</p>",
                                "<p>भाला फेंक</p>", "<p>800 m दौड़</p>"],
                    solution_en: "<p><strong>29.(b) 1500 m race.</strong> Decathlon - An all round athletics test, the 10-event contest covers the whole range of athletics disciplines spread over two days. The first day consists of (in order): 100m, long jump, shot put, high jump and 400m. The second day&rsquo;s events are 110m hurdles, discus throw, pole vault, javelin throw and 1500m.</p>",
                    solution_hi: "<p><strong>29.(b) 1500 m दौड़ ।</strong> डेकाथलॉन - एक सर्वांगीण एथलेटिक्स परीक्षण, 10-इवेंट प्रतियोगिता दो दिनों में फैले एथलेटिक्स विषयों की पूरी श्रृंखला को कवर करती है। पहले दिन में (क्रम में) शामिल हैं: 100 मीटर, लंबी कूद, गोला फेंक, ऊंची कूद और 400 मीटर। दूसरे दिन की स्पर्धाएँ 110 मीटर बाधा दौड़, डिस्कस थ्रो, पोल वॉल्ट, भाला फेंक और 1500 मीटर हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. Which State has the maximum number of members in Rajya Sabha?",
                    question_hi: "<p>30. राज्यसभा में किस राज्य के सदस्यों की संख्या सर्वाधिक है?</p>",
                    options_en: [" Haryana", " Bihar", 
                                " Uttar Pradesh", " Punjab"],
                    options_hi: ["<p>हरियाणा</p>", "<p>बिहार</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>पंजाब</p>"],
                    solution_en: "<p><strong>30.(c) Uttar Pradesh (31).</strong> The Rajya Sabha is a permanent House and is not subject to dissolution. The Rajya Sabha was constituted on 3 April, 1952. The maximum strength of Rajya Sabha is 250 Members, of which 238 are to be elected and 12 are to be nominated by the President of India.</p>",
                    solution_hi: "<p><strong>30.(c) उत्तर प्रदेश (31)। </strong>राज्यसभा एक स्थायी सदन है और इसका विघटन नहीं होता है। राज्य सभा का गठन 3 अप्रैल, 1952 को हुआ था। राज्य सभा की अधिकतम संख्या 250 सदस्यों की है, जिनमें से 238 निर्वाचित होते हैं और 12 भारत के राष्ट्रपति द्वारा नामांकित होते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following Acts was passed by Governor General Lord William Bentinck in 1829</p>",
                    question_hi: "<p>31. निम्नलिखित में से कौन-सा अधिनियम 1829 में गवर्नर जनरल लॉर्ड विलियम बेंटिंक द्वारा पारित किया गया था?</p>",
                    options_en: ["<p>Indian Telegraph Act</p>", "<p>Bengal Sati Regulation Act</p>", 
                                "<p>Indian Slavery Act</p>", "<p>Criminal Tribes Act</p>"],
                    options_hi: ["<p>भारतीय टेलीग्राफ अधिनियम</p>", "<p>बंगाल सती रेगुलेशन अधिनियम</p>",
                                "<p>भारतीय दासता अधिनियम</p>", "<p>आपराधिक जनजाति अधिनियम</p>"],
                    solution_en: "<p><strong>31.(b) Bengal Sati Regulation Act -</strong> A Regulation for declaring the practice of sati or of burning or burying alive the widows of Hindus illegal and punishable by the Criminal Courts. Indian Telegraph Act: Enactment year - 1885. Criminal Tribes Act: Enactment year - 1871. Indian Slavery Act: Enactment year - 1843.</p>",
                    solution_hi: "<p><strong>31.(b) बंगाल सती रेगुलेशन अधिनियम </strong>- सती प्रथा या हिंदुओं की विधवाओं को जिंदा जलाने या दफनाने की प्रथा को अवैध और आपराधिक न्यायालयों द्वारा दंडनीय घोषित करने के लिए एक विनियमन। भारतीय टेलीग्राफ अधिनियम: अधिनियमन वर्ष - 1885। आपराधिक जनजाति अधिनियम: अधिनियमन वर्ष - 1871। भारतीय दासता अधिनियम: अधिनियमन वर्ष - 1843।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. Identify the Headquarters of the East Coast Railway Zone of India?",
                    question_hi: "<p>32. भारत के ईस्ट कोस्ट रेलवे ज़ोन (पूर्व तटीय रेलवे ज़ोन) के मुख्यालय की पहचान करें?</p>",
                    options_en: [" Bhubaneswar", "<p>Kolkata</p>", 
                                "<p>New Delhi</p>", "<p>Mumbai</p>"],
                    options_hi: ["<p>भुवनेश्वर</p>", "<p>कोलकाता</p>",
                                "<p>नई दिल्ली</p>", "<p>मुंबई</p>"],
                    solution_en: "<p><strong>32.(a) Bhubaneswar. R</strong>ailway Zones and Headquarters: Central Railway (Mumbai), Eastern Railway (Kolkata), Northern Railway (New Delhi), North Central Railway (Prayagraj), Southern Railway (Chennai), North Eastern Railway (Gorakhpur), North Western Railway (Jaipur), South Central Railway (Secunderabad), South East Central Railway (Bilaspur).</p>",
                    solution_hi: "<p><strong>32.(a) भुवनेश्वर। </strong>रेलवे जोन और मुख्यालय: मध्य रेलवे (मुंबई), पूर्वी रेलवे (कोलकाता), उत्तर रेलवे (नई दिल्ली), उत्तर मध्य रेलवे (प्रयागराज), दक्षिणी रेलवे (चेन्नई), उत्तर पूर्वी रेलवे (गोरखपुर), उत्तर पश्चिम रेलवे (जयपुर) , दक्षिण मध्य रेलवे (सिकंदराबाद), दक्षिण पूर्व मध्य रेलवे (बिलासपुर)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. In which of the following Articles of the Constitution of India, is it mentioned that &ldquo;it shall be the duty of every citizen of India to value and preserve the rich heritage of our composite culture&rdquo;?</p>",
                    question_hi: "<p>33. भारत के संविधान के निम्नलिखित में से किस अनुच्छेद में यह उल्लेख किया गया है कि \"हमारी समग्र संस्कृति की समृद्ध विरासत को महत्व देना और संरक्षित करना भारत के प्रत्येक नागरिक का कर्तव्य होगा\"?</p>",
                    options_en: ["<p>51A (f)</p>", "<p>51A (b)</p>", 
                                "<p>51A (h)</p>", "<p>51A (m)</p>"],
                    options_hi: ["<p>51A (f)</p>", "<p>51A (b)</p>",
                                "<p>51A (h)</p>", "<p>51A (m)</p>"],
                    solution_en: "<p><strong>33.(a) 51A (f). </strong>Other Fundamental duties: Article 51A (a) - To abide by the Constitution and respect its ideals and institutions, the National Flag and the National Anthem. Article 51A (b) - To cherish and follow the noble ideals which inspired our national struggle for freedom. Article 51A (h) - To develop the scientific temper, humanism and the spirit of inquiry and reform.</p>",
                    solution_hi: "<p><strong>33.(a) 51A (f)</strong>. अन्य मौलिक कर्तव्य: अनुच्छेद 51A (a) - संविधान का पालन करना और उसके आदर्शों और संस्थानों, राष्ट्रीय ध्वज और राष्ट्रगान का सम्मान करना। अनुच्छेद 51A (b) - उन महान आदर्शों को संजोना और उनका पालन करना जिन्होंने स्वतंत्रता के लिए हमारे राष्ट्रीय संघर्ष को प्रेरित किया। अनुच्छेद 51 A (h) - वैज्ञानिक दृष्टिकोण, मानवतावाद तथा जिज्ञासा एवं सुधार की भावना का विकास करना।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. When is National Unity Day observed?</p>",
                    question_hi: "<p>34. राष्ट्रीय एकता दिवस कब मनाया जाता है?</p>",
                    options_en: ["<p>31 November</p>", "<p>8 November</p>", 
                                "<p>31 October</p>", "<p>11 March</p>"],
                    options_hi: ["<p>31 नवंबर</p>", "<p>8 नवंबर</p>",
                                "<p>31 अक्टूबर</p>", "<p>11 मार्च</p>"],
                    solution_en: "<p><strong>34.(c) 31 October.</strong> National Unity Day or also known by the name of Rashtriya Ekta Diwas celebrated to commemorate the birth anniversary of Sardar Vallabhbhai Patel. First time National Unity Day was celebrated on 31st October 2014. Important Days: 15 January - Indian Army Day, 25 January - National Voters Day, 8 October - Indian Air Force Day, 7 December - Indian Armed Forces Flag Day.</p>",
                    solution_hi: "<p><strong>34.(c) 31 अक्टूबर। </strong>राष्ट्रीय एकता दिवस या जिसे राष्ट्रीय एकता दिवस के नाम से भी जाना जाता है, सरदार वल्लभभाई पटेल की जयंती के उपलक्ष्य में मनाया जाता है। पहली बार राष्ट्रीय एकता दिवस 31 अक्टूबर 2014 को मनाया गया था। महत्वपूर्ण दिन: 15 जनवरी - भारतीय सेना दिवस, 25 जनवरी - राष्ट्रीय मतदाता दिवस, 8 अक्टूबर - भारतीय वायु सेना दिवस, 7 दिसंबर - भारतीय सशस्त्र सेना झंडा दिवस।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Who among the following is credited for laying the foundation of microfinance institutions?</p>",
                    question_hi: "<p>35. निम्नलिखित में से किसे सूक्ष्म वित्त संस्&zwj;थाओं की नींव रखने का श्रेय दिया जाता है?</p>",
                    options_en: ["<p>Amartya Sen</p>", "<p>Jagdish Bhagwati</p>", 
                                "<p>PV Narasimha Rao</p>", "<p>Muhammad Yunus</p>"],
                    options_hi: ["<p>अमर्त्य सेन</p>", "<p>जगदीश भगवती</p>",
                                "<p>पी.वी. नरसिम्हा राव</p>", "<p>मुहम्मद यूनुस</p>"],
                    solution_en: "<p>35.(d<strong>) Muhammad Yunus</strong> awarded the Nobel Peace Prize for 2006 and Indira Gandhi Peace Prize (1998). Amartya Sen is an Indian economist, who was awarded the Nobel Prize in Economic Sciences in 1998. PV Narasimha Rao - He served as 9th prime minister of India from 1991 to 1996.</p>",
                    solution_hi: "<p>35.(d)<strong> मुहम्मद यूनुस </strong>को 2006 में नोबेल शांति पुरस्कार और इंदिरा गांधी शांति पुरस्कार (1998) से सम्मानित किया गया। अमर्त्य सेन एक भारतीय अर्थशास्त्री हैं, जिन्हें 1998 में आर्थिक विज्ञान में नोबेल पुरस्कार से सम्मानित किया गया था। पीवी नरसिम्हा राव - उन्होंने 1991 से 1996 तक भारत के 9वें प्रधान मंत्री के रूप में कार्य किया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Who among the following defeated Mughal emperor Humayun in the Battle of Kannauj in 1540?</p>",
                    question_hi: "<p>36. 1540 में कन्नौज की लड़ाई में निम्नलिखित में से किसने मुगल सम्राट हुमायूँ को पराजित किया था?</p>",
                    options_en: ["<p>Bahadur Shah</p>", "<p>Islam Shah</p>", 
                                "<p>Tatar Khan</p>", "<p>Sher Shah Suri</p>"],
                    options_hi: ["<p>बहादुर शाह</p>", "<p>इस्लाम शाह</p>",
                                "<p>तातार खान</p>", "<p>शेर शाह सूरी</p>"],
                    solution_en: "<p><strong>36.(d) Sher Shah Suri. </strong>The Battle of Kannauj is also known as the battle of Bilgram. The Battle of Chausa (1539) was fought between the Mughal emperor, Humayun, and the Afghan, Sher Shah Suri. It was fought at Chausa, 10 miles southwest of Buxar in modern-day Bihar, India. Sher Shah Suri defeated Humayun in this battle.</p>",
                    solution_hi: "<p><strong>36.(d) शेरशाह सूरी । </strong>कन्&zwj;नौज का युद्ध बिलग्राम के युद्ध के नाम से भी जाना जाता है। चौसा की लड़ाई (1539) मुगल सम्राट हुमायूँ और अफगान शेरशाह सूरी के बीच लड़ी गई थी। यह भारत के आधुनिक बिहार में बक्सर से 10 मील दक्षिण-पश्चिम में चौसा में लड़ा गया था। इस युद्ध में शेरशाह सूरी ने हुमायूँ को हरा दिया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. In which state is the Kanha National Park located?",
                    question_hi: "<p>37. कान्हा राष्ट्रीय उद्यान किस राज्&zwj;य में स्थित है?</p>",
                    options_en: [" Bihar", "<p>Himachal Pradesh</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Karnataka</p>"],
                    options_hi: ["<p>बिहार</p>", "<p>हिमाचल प्रदेश</p>",
                                "<p>मध्य प्रदेश</p>", "<p>कर्नाटक</p>"],
                    solution_en: "<p><strong>37.(c) Madhya Pradesh. </strong>National Parks in India: Bandhavgarh National Park and Pench National Park (Madhya Pradesh), Ranthambore National Park (Rajasthan), Bandipur National Park (Karnataka), Jim Corbett National Park (Uttarakhand), Kaziranga National Park (Assam), Khangchendzonga National Park (Sikkim), Tadoba Andhari National Park (Maharashtra).</p>",
                    solution_hi: "<p><strong>37.(c) मध्य प्रदेश। </strong>भारत में राष्ट्रीय उद्यान: बांधवगढ़ राष्ट्रीय उद्यान और पेंच राष्ट्रीय उद्यान (मध्य प्रदेश), रणथंभौर राष्ट्रीय उद्यान (राजस्थान), बांदीपुर राष्ट्रीय उद्यान (कर्नाटक), जिम कॉर्बेट राष्ट्रीय उद्यान (उत्तराखंड), काजीरंगा राष्ट्रीय उद्यान (असम), कंचनजंगा राष्ट्रीय उद्यान ( सिक्किम), ताडोबा अंधारी राष्ट्रीय उद्यान (महाराष्ट्र)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Identify the first Gurjara Partihara ruler who successfully defeated the Arab invaders.</p>",
                    question_hi: "<p>38. निम्नलिखित में से उस प्रथम गुर्जर प्रतिहार शासक का नाम बताइए, जिसने अरब आक्रमणकारियों को सफलतापूर्वक पराजित किया था।</p>",
                    options_en: ["<p>Nagabhata I</p>", "<p>Vatsaraja</p>", 
                                "<p>Mahendrapala I</p>", "<p>Mahipala</p>"],
                    options_hi: ["<p>नागभट्ट प्रथम</p>", "<p>वत्सराज</p>",
                                "<p>महेंद्रपाल प्रथम</p>", "<p>महीपाल</p>"],
                    solution_en: "<p><strong>38.(a) Nagabhata I.</strong> The Gurjara-Pratiharas, or the Pratiharas (8th century CE - 11th century CE) ruled over western and northern India. Bhoja or Mihira Bhoja was the most well-known king of this dynasty. Mahendrapala I was a ruler of the Gurjara-Pratihara dynasty, the son of Mihir Bhoja I.</p>",
                    solution_hi: "<p><strong>38.(a) नागभट्ट प्रथम । </strong>गुर्जर-प्रतिहार, या प्रतिहार (8वीं शताब्दी CE- 11वीं शताब्दी CE) ने पश्चिमी और उत्तरी भारत पर शासन किया। भोज या मिहिरा भोज इस राजवंश के सबसे प्रसिद्ध राजा थे। महेंद्रपाल प्रथम, मिहिर भोज प्रथम का पुत्र, गुर्जर-प्रतिहार वंश का शासक था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Ustad Amjad Ali Khan is the maestro in playing the ________.</p>",
                    question_hi: "<p>39. उस्ताद अमजद अली खान ________ बजाने में उस्ताद हैं।</p>",
                    options_en: ["<p>sitar</p>", "<p>sarod</p>", 
                                "<p>bansuri</p>", "<p>tabla</p>"],
                    options_hi: ["<p>सितार</p>", "<p>सरोद</p>",
                                "<p>बाँसुरी</p>", "<p>तबला</p>"],
                    solution_en: "<p><strong>39.(b) sarod. </strong>Ustad Amjad Ali Khan&rsquo;s Awards: Padma Vibhushan (2001), Padma Bhushan (1991), Padma Shri (1975). Musical Instrument and exponents: Sarod - Allauddin Khan, Radhika Mohan Maitra, Tejendra Majumdar. Sitar - Shamim Ahmed Khan, Debu Chaudhuri, Manilal Nag. Flute - Pt. Hari Prasad Chaurasiya, Devendra Murdeshwar, Ronu Majumdar, Pt. Rajendra Prasanna.</p>",
                    solution_hi: "<p><strong>39.(b) सरोद । </strong>उस्ताद अमजद अली खान के पुरस्कार: पद्म विभूषण (2001), पद्म भूषण (1991), पद्म श्री (1975)। संगीत वाद्ययंत्र और प्रतिपादक: सरोद - अलाउद्दीन खान, राधिका मोहन मैत्रा, तेजेंद्र मजूमदार। सितार - शमीम अहमद खान, देबू चौधरी, मणिलाल नाग। बांसुरी - पं. हरि प्रसाद चौरसिया, देवेन्द्र मुर्देश्वर, रोनू मजूमदार, पं. राजेंद्र प्रसन्ना ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which neighbouring country of India was the eighth most populous country in the world, as per the US Census Bureau, by July 2022?</p>",
                    question_hi: "<p>40. अमेरिकी (US) जनगणना ब्यूरो के अनुसार, जुलाई 2022 तक, भारत का कौन-सा पड़ोसी देश दुनिया का आठवां सबसे अधिक घनी आबादी वाला देश था?</p>",
                    options_en: ["<p>Nepal</p>", "<p>Bangladesh</p>", 
                                "<p>Bhutan</p>", "<p>Myanmar</p>"],
                    options_hi: ["<p>नेपाल</p>", "<p>बांग्लादेश</p>",
                                "<p>भूटान</p>", "<p>म्यांमार</p>"],
                    solution_en: "<p><strong>40.(b) Bangladesh.</strong> Top 10 Most Populous Countries (US Census Bureau): India, China, United States, Indonesia, Pakistan, Nigeria, Brazil, Bangladesh, Russia, and Mexico.</p>",
                    solution_hi: "<p><strong>40.(b) बांग्लादेश ।</strong> शीर्ष 10 सबसे अधिक आबादी वाले देश (अमेरिकी जनगणना ब्यूरो): भारत, चीन, संयुक्त राज्य अमेरिका, इंडोनेशिया, पाकिस्तान, नाइजीरिया, ब्राजील, बांग्लादेश, रूस और मैक्सिको।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Who became India&rsquo;s first weightlifter to win a gold medal at the IWF Junior World Championships, in 2022?</p>",
                    question_hi: "<p>41. 2022 में, IWF जूनियर विश्व चैंपियनशिप में स्वर्ण पदक जीतने वाले भारत के पहले भारोत्तोलक का नाम क्या था?</p>",
                    options_en: ["<p>Harshada Sharad Garud</p>", "<p>T Madhavan</p>", 
                                "<p>Anjali Patel</p>", "<p>Muna Nayak</p>"],
                    options_hi: ["<p>हर्षदा शरद गरुड़</p>", "<p>टी. माधवन</p>",
                                "<p>अंजलि पटेल</p>", "<p>मुना नायक</p>"],
                    solution_en: "<p><strong>41.(a) Harshada Sharad Garud (Maharashtra). I</strong>WF Junior World Championships 2022: Host - Heraklion, Greece. Medal: First place - Turkey (Gold - 2, Silver - 3, Bronze - 1), India (Gold -1, Silver - 1, Bronze - 1). International Weightlifting Federation (IWF): Formation - 1905. Headquarters - Lausanne, Switzerland.</p>",
                    solution_hi: "<p><strong>41.(a) हर्षदा शरद गरुड़ (महाराष्ट्र)। </strong>IWF जूनियर विश्व चैंपियनशिप 2022: मेजबान - हेराक्लिओन, ग्रीस। पदक: प्रथम स्थान - तुर्की (स्वर्ण - 2, रजत - 3, कांस्य - 1), भारत (स्वर्ण -1, रजत - 1, कांस्य - 1)। अंतर्राष्ट्रीय भारोत्तोलन महासंघ (IWF): गठन - 1905. मुख्यालय - लुसाने, स्विट्जरलैंड।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "42. Which of the following is the first published novel of the famous author Salman Rushdie?",
                    question_hi: "<p>42. प्रसिद्ध लेखक सलमान रुश्दी का पहला प्रकाशित उपन्यास निम्नलिखित में से कौन-सा है?</p>",
                    options_en: [" Grimus", "<p>The Moor&rsquo;s Last Sigh</p>", 
                                "<p>The Satanic Verses</p>", "<p>Shame</p>"],
                    options_hi: ["<p>ग्रिमस</p>", "<p>द मूर्स लास्ट साई</p>",
                                "<p>द सटैनिक वर्सेस</p>", "<p>शेम</p>"],
                    solution_en: "<p><strong>42.(a) Grimus. </strong>Salman Rushdie&rsquo;s other books: &ldquo;Midnight\'s Children&rdquo;, &ldquo;Victory City&rdquo;, &ldquo;Harun Aur Kahaniyo Ka Samunder&rdquo;, &ldquo;Quichotte&rdquo;, &ldquo;The Golden House&rdquo;. His Awards - Booker Prize (1981), Austrian State Prize for European Literature (1992).</p>",
                    solution_hi: "<p><strong>42.(a) ग्रिमस । </strong>सलमान रुश्दी की अन्य पुस्तकें: \"मिडनाइट्स चिल्ड्रेन\", \"विक्ट्री सिटी\", \"हारुन और कहानियों का समुंदर\", \"क्विचोटे\", \"द गोल्डन हाउस\"। उनके पुरस्कार - बुकर पुरस्कार (1981), यूरोपीय साहित्य के लिए ऑस्ट्रियाई राज्य पुरस्कार (1992)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "43. Ministers of state can attend the cabinet meeting ________.",
                    question_hi: "<p>43. राज्य मंत्री कैबिनेट बैठक में ________ भाग ले सकते हैं।</p>",
                    options_en: [" on his own will", " only when the speaker invites him", 
                                " only when invited", " because its compulsory for him"],
                    options_hi: ["<p>अपनी मर्जी से</p>", "<p>केवल जब अध्यक्ष उसे आमंत्रित करता है</p>",
                                "<p>केवल आमंत्रित होने पर</p>", "<p>क्योंकि यह उसके लिए अनिवार्य है</p>"],
                    solution_en: "<p><strong>43.(c) only when invited. </strong>The Constitution of India, under articles 74 and 75, provides for a Council of Ministers. Article 74(1) reads that &lsquo;there shall be a Council of Ministers with the Prime Minister as the head to aid and advise the President. Article 74 (2) - The question whether any, and if so what, advice was tendered by Ministers to the President shall not be inquired into in any court.</p>",
                    solution_hi: "<p><strong>43.(c) केवल आमंत्रित होने पर । </strong>भारत का संविधान, अनुच्छेद 74 और 75 के तहत, मंत्रिपरिषद का प्रावधान करता है। अनुच्छेद 74(1) में कहा गया है कि \'राष्ट्रपति को सहायता और सलाह देने के लिए एक मंत्रिपरिषद होगी जिसका प्रमुख प्रधानमंत्री होगा। अनुच्छेद 74 (2) - मंत्रियों द्वारा राष्ट्रपति को कोई सलाह दी गई हो, और यदि हां, तो कौनसी, उस पर कोई न्यायालय में जाँच नहीं की जाएगी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. In which of the following places were the remains of Mahaparinirvana Buddha found? It is at this location that a 6.1 m long reclining Buddha has been constructed.</p>",
                    question_hi: "<p>44. निम्नलिखित में से किस स्थान पर महापरिनिर्वाण बुद्ध के अवशेष मिले थे? यह वह स्थान है जहॉं पर 6.1 m लंबी शयन मुद्रा में बुद्ध की प्रतिमा का निर्माण किया गया है।</p>",
                    options_en: ["<p>Vaishali</p>", "<p>Bodhgaya</p>", 
                                "<p>Kushinagar</p>", "<p>Vethipada</p>"],
                    options_hi: ["<p>वैशाली</p>", "<p>बोधगया</p>",
                                "<p>कुशीनगर</p>", "<p>वेठीपाड़ा</p>"],
                    solution_en: "<p><strong>44.(c) Kushinagar.</strong> Siddhartha Gautama, the Lord Buddha was born in Lumbini, Nepal. He belonged to the Shakya clan. He attained enlightenment in Bodh Gaya, Bihar. Father name - Suddhodana. Mother name - Mahamaya. Wife name - Yasodhara. Son name - Rahula.</p>",
                    solution_hi: "<p><strong>44.(c) कुशीनगर ।</strong> सिद्धार्थ गौतम, भगवान बुद्ध का जन्म नेपाल के लुंबिनी में हुआ था। वह शाक्य वंश के थे। उन्हें बोधगया, बिहार में ज्ञान प्राप्त हुआ। पिता का नाम - शुद्धोदन। माता का नाम - महामाया। पत्नी का नाम - यशोधरा । बेटे का नाम - राहुल ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "45. The term \'checkmate\' is used in ________.",
                    question_hi: "<p>45. \'चेकमेट (checkmate)\' शब्द का उपयोग ________ में किया जाता है।</p>",
                    options_en: [" Badminton", "<p>Golf</p>", 
                                "<p>Chess</p>", "<p>Tennis</p>"],
                    options_hi: ["<p>बैडमिंटन</p>", "<p>गोल्फ़</p>",
                                "<p>शतरंज</p>", "<p>टेनिस</p>"],
                    solution_en: "<p><strong>45.(c) Chess. </strong>Chess terms - Stalemate, Castling, Blockade, Cramped, File, Gambit, Grandmaster, Howler. Badminton terms - Alley, Backcourt, Baseline, Bird or birdie, Flick. Golf terms - Eagle, Par, Bogey, Double Bogey, Bunker, Birdie. Tennis terms - Deuce, Counterpuncher, Jamming, Kick serve, Lob.</p>",
                    solution_hi: "<p><strong>45.(c) शतरंज ।</strong> शतरंज की शब्दावली- स्टेलेमेट, कैसलिंग, ब्लॉकेड, क्रैम्प्ड, फाइल, गैम्बिट, ग्रैंडमास्टर, हाउलर। बैडमिंटन की शब्दावली - एली, बैककोर्ट, बेसलाइन, बर्ड या बर्डी, फ्लिक। गोल्फ शब्द - ईगल, पार, बोगी, डबल बोगी, बंकर, बर्डी। टेनिस की शब्दावली - ड्यूस, काउंटरपंचर, जैमिंग, किक सर्व, लोब।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which king of the Mauryan dynasty succeeded Chandragupta Maurya?</p>",
                    question_hi: "<p>46. मौर्य वंश का कौन-सा राजा चंद्रगुप्त मौर्य का उत्तराधिकारी बना था?</p>",
                    options_en: ["<p>Brihadratha</p>", "<p>Samprati</p>", 
                                "<p>Ashoka</p>", "<p>Bindusara</p>"],
                    options_hi: ["<p>बृहद्रथ</p>", "<p>सम्प्रति</p>",
                                "<p>अशोक</p>", "<p>बिंदुसार</p>"],
                    solution_en: "<p>4<strong>6.(d) Bindusara.</strong> Mauryan empire: Founder - Chandragupta Maurya. Bindusara was succeeded by his son Ashoka. Brihadratha (last Emperor of the Mauryan Empire) was killed by his general, Pushyamitra Shunga, who founded the Shunga dynasty.</p>",
                    solution_hi: "<p><strong>46.(d) बिंदुसार ।</strong> मौर्य साम्राज्य: संस्थापक - चंद्रगुप्त मौर्य। बिन्दुसार का उत्तराधिकारी उसका पुत्र अशोक बना। बृहद्रथ (मौर्य साम्राज्य के अंतिम सम्राट) की हत्या उसके सेनापति पुष्यमित्र शुंग ने कर दी थी, जिसने शुंग राजवंश की स्थापना की थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. The famous Chinese Buddhist pilgrim, Fa Hein visited India during the reign of which of the following Gupta rulers?</p>",
                    question_hi: "<p>47. प्रसिद्ध चीनी बौद्ध तीर्थयात्री, फ़ाहियान (Fa Hein ) निम्नलिखित में से किस गुप्त शासक के शासनकाल में भारत आए थे?</p>",
                    options_en: ["<p>Sri Gupta</p>", "<p>Samudragupta</p>", 
                                "<p>Kumargupta</p>", "<p>Chandragupta II</p>"],
                    options_hi: ["<p>श्री गुप्त</p>", "<p>समुद्रगुप्त</p>",
                                "<p>कुमारगुप्त</p>", "<p>चंद्रगुप्त द्वितीय</p>"],
                    solution_en: "<p><strong>47.(d) Chandragupta II. F</strong>oreign Travelers in India: Megasthenes (Chandragupta Maurya), Hiuen Tsang (Harshabardhan), Ibn Battuta (Muhammad-bin-Tughlaq), Nicolo Conti (Devaraya I), Thomas Roe (Jahangir), Fernao Nunes (Achyuta Deva Raya).</p>",
                    solution_hi: "<p><strong>47.(d) चंद्रगुप्त द्वितीय । </strong>भारत में विदेशी यात्री: मेगस्थनीज (चंद्रगुप्त मौर्य), ह्वेन त्सांग (हर्षवर्धन), इब्न बतूता (मुहम्मद-बिन-तुगलक), निकोलो कोंटी (देवराय प्रथम), थॉमस रो (जहांगीर), फर्नाओ नून्स (अच्युता देव राय)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Who coined the name &lsquo;Pakistan&rsquo;?</p>",
                    question_hi: "<p>48. \'पाकिस्तान\' नाम किसने दिया था?</p>",
                    options_en: ["<p>Aga Khan</p>", "<p>Chaudhry Rehmat Ali</p>", 
                                "<p>Md Iqbal</p>", "<p>Md Ali Jinnah</p>"],
                    options_hi: ["<p>आगा खान</p>", "<p>चौधरी रहमत अली</p>",
                                "<p>मोहम्मद इकबाल</p>", "<p>मोहम्मद अली जिन्ना</p>"],
                    solution_en: "<p>48.(b)<strong> Chaudhry Rehmat Ali </strong>was the founder of the Pakistan National Movement. Muhammad Ali Jinnah (Quaid-i-Azam) was the founder of Pakistan. He served as the leader of the All-India Muslim League from 1913 until the inception of Pakistan on 14 August 1947, and then as the Dominion of Pakistan\'s first governor-general until his death. Mohammed Iqbal wrote the famous song \'saare Jahan se achha&rsquo;.</p>",
                    solution_hi: "<p>48.(b) <strong>चौधरी रहमत अली </strong>पाकिस्तान राष्ट्रीय आंदोलन के संस्थापक थे। मुहम्मद अली जिन्ना (कायद-ए-आज़म) पाकिस्तान के संस्थापक थे। उन्होंने 1913 से 14 अगस्त 1947 को पाकिस्तान की स्थापना तक अखिल भारतीय मुस्लिम लीग के नेता के रूप में कार्य किया, और फिर अपनी मृत्यु तक पाकिस्तान के पहले गवर्नर-जनरल के डोमिनियन के रूप में कार्य किया। मोहम्मद इकबाल ने प्रसिद्ध गीत \'सारे जहां से अच्छा\' लिखा था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. As per K&ouml;ppen climate classification, the climate of which place is classified as \'Cwg\' (meso thermal with dry winters)?</p>",
                    question_hi: "<p>49. कोपेन जलवायु वर्गीकरण के अनुसार किस स्थान की जलवायु को \'Cwg\' (शुष्क शीत ऋतु के साथ मध्यतापीय) के रूप में वर्गीकृत किया गया है?</p>",
                    options_en: ["<p>North-western Gujarat</p>", "<p>Extreme western Rajasthan</p>", 
                                "<p>South of Goa</p>", "<p>Gangetic plains</p>"],
                    options_hi: ["<p>उत्तर-पश्चिमी गुजरात</p>", "<p>अति पश्चिमी राजस्थान</p>",
                                "<p>गोवा का दक्षिण</p>", "<p>गंगा के मैदान</p>"],
                    solution_en: "<p><strong>49.(d) Gangetic plains. </strong>The most widely used classification of climate is the empirical climate classification scheme developed by V. Koeppen. Koeppen&rsquo;s 3 alphabet combination: This combination is being used to denote vegetation, precipitation, thermal and locational characteristics of a climate type. Examples - &lsquo;Amw&rsquo; (tropical monsoon with short dry winter), &lsquo;Dfc&rsquo; (micro thermal humid with short summer found in eastern Himalayan region of India).</p>",
                    solution_hi: "<p><strong>49.(d) गंगा के मैदान ।</strong> जलवायु का सबसे व्यापक रूप से उपयोग किया जाने वाला वर्गीकरण वी. कोप्पेन द्वारा विकसित अनुभवजन्य जलवायु वर्गीकरण योजना है। कोप्पेन का 3 वर्णमाला संयोजन: इस संयोजन का उपयोग जलवायु प्रकार की वनस्पति, वर्षा, थर्मल और स्थानीय विशेषताओं को दर्शाने के लिए किया जा रहा है। उदाहरण - &lsquo;Amw&rsquo; (छोटी शुष्क सर्दी वाला उष्णकटिबंधीय मानसून), &lsquo;Dfc&rsquo; (भारत के पूर्वी हिमालयी क्षेत्र में पाई जाने वाली कम गर्मी वाली सूक्ष्म तापीय आर्द्र)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "50",
                    section: "misc",
                    question_en: "<p>50. In order to preserve pickles, a mixture of 5% to 8% of acetic acid in water is used. This preservative is also commonly known as:</p>",
                    question_hi: "<p>50. अचार को परिरक्षित करने के उद्देश्य से, जल में 5% से 8% ऐसीटिक अम्ल के मिश्रण का उपयोग किया जाता है। इस परिरक्षक को सामान्यतः ________ भी कहा जाता है।</p>",
                    options_en: ["<p>oil</p>", "<p>salt</p>", 
                                "<p>vinegar</p>", "<p>lemon juice</p>"],
                    options_hi: ["<p>तेल</p>", "<p>लवण</p>",
                                "<p>विनेगर</p>", "<p>नींबू का रस</p>"],
                    solution_en: "<p><strong>50.(c) Vinegar</strong> is a combination of acetic acid and water made by a two-step fermentation process. Types of Vinegar: White Distilled - Made by fermentation of a distilled alcohol, which often originates from fermented grains. Balsamic - Made from fermented grapes (whole pressed grapes). Fermentation - The chemical process by which molecules such as glucose are broken down anaerobically.</p>",
                    solution_hi: "<p><strong>50.(c) विनेगर ।</strong> सिरका एसिटिक एसिड और पानी का एक संयोजन है जो दो-चरणीय किण्वन प्रक्रिया द्वारा बनाया जाता है। सिरका के प्रकार: सफेद आसुत - आसुत अल्कोहल के किण्वन द्वारा बनाया जाता है, जो अक्सर किण्वित अनाज से उत्पन्न होता है। बाल्समिक - किण्वित अंगूर (साबुत दबाए गए अंगूर) से बना है। किण्वन - वह रासायनिक प्रक्रिया जिसके द्वारा ग्लूकोज जैसे अणु अवायवीय रूप से टूट जाते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>