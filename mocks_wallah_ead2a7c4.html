<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language, &lsquo;TERRAIN&rsquo; is written as &lsquo;VETTAIP&rsquo; and &lsquo;TRAFFIC&rsquo; is written as &lsquo;VTAHHIE&rsquo;. How will &lsquo;MOTOR&rsquo; be written in that language?</p>",
                    question_hi: "<p>1. एक निश्चित कोड भाषा में, \'TERRAIN\' को \'VETTAIP\' लिखा जाता है और \'TRAFFIC\' को \'VTAHHIE\' लिखा जाता है। उसी भाषा में \'MOTOR\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>OOVOT</p>", "<p>VOOOT</p>", 
                                "<p>OOTVO</p>", "<p>OVOTO</p>"],
                    options_hi: ["<p>OOVOT</p>", "<p>VOOOT</p>",
                                "<p>OOTVO</p>", "<p>OVOTO</p>"],
                    solution_en: "<p>1.(a)<br><strong>Logic </strong>:- consonants increased by 2 and vowels remain the same.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100491511.png\" alt=\"rId4\" width=\"150\" height=\"81\"> and&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100491671.png\" alt=\"rId5\" width=\"148\" height=\"79\"><br><br>Similarly, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100491829.png\" alt=\"rId6\" width=\"113\" height=\"83\"></p>",
                    solution_hi: "<p>1.(a)<br><strong>तर्क </strong>: - व्यंजनों में 2 की वृद्धि होती है और स्वर वही रहते हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100491511.png\" alt=\"rId4\" width=\"150\" height=\"81\"> और <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100491671.png\" alt=\"rId5\" width=\"148\" height=\"79\"><br><br>उसी प्रकार, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100491829.png\" alt=\"rId6\" width=\"113\" height=\"83\"><br><br><br></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain code language, &lsquo;BLEACHERS&rsquo; is coded as &lsquo;63&rsquo; and &lsquo;ABACAS&rsquo; is coded as &lsquo;42&rsquo;. How will &lsquo;FIZZY&rsquo; be coded in the same language?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में \'BLEACHERS&rsquo; को \'63&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;ABACAS&rsquo; को &lsquo;42&rsquo; के रूप में कूटबद्ध किया जाता है। उस भाषा में \'FIZZY&rsquo; को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>44</p>", "<p>45</p>", 
                                "<p>35</p>", "<p>52</p>"],
                    options_hi: ["<p>44</p>", "<p>45</p>",
                                "<p>35</p>", "<p>52</p>"],
                    solution_en: "<p>2.(c)<br><strong>Logic </strong>:- no. of letters<math display=\"inline\"><mo>&#215;</mo></math>7 <br>BLEACHERS <math display=\"inline\"><mo>&#8594;</mo></math> 9 letters &rArr; 9 &times; 7 = 63<br>ABACUS <math display=\"inline\"><mo>&#8594;</mo></math> 6 letters &rArr; 6 &times; 7 = 42<br>FIZZY <math display=\"inline\"><mo>&#8594;</mo></math> 5 letters &rArr; 5 &times; 7 = 35</p>",
                    solution_hi: "<p>2.(c) <br><strong>तर्क :-</strong> अक्षरों की संख्या<math display=\"inline\"><mo>&#215;</mo></math>7 <br>BLEACHERS <math display=\"inline\"><mo>&#8594;</mo></math> 9 अक्षर &rArr; 9 &times; 7 = 63<br>ABACUS <math display=\"inline\"><mo>&#8594;</mo></math> 6 अक्षर &rArr; 6 &times; 7 = 42<br>FIZZY <math display=\"inline\"><mo>&#8594;</mo></math> 5 अक्षर &rArr; 5 &times; 7 = 35</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, &lsquo;ZCJV&rsquo; is coded as &lsquo;YBIU&rsquo; and &lsquo;GKMD&rsquo; is coded as &lsquo;FJLC&rsquo;. How will &lsquo;EYOR&rsquo; be coded in that language?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में, \'ZCJV\' को \'YBIU\' के रूप में कूटबद्ध किया जाता है और \'GKMD\' को \'FJLC\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'EYOR\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>QWMP</p>", "<p>DXNQ</p>", 
                                "<p>DWMP</p>", "<p>CXNQ</p>"],
                    options_hi: ["<p>QWMP</p>", "<p>DXNQ</p>",
                                "<p>DWMP</p>", "<p>CXNQ</p>"],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100491965.png\" alt=\"rId7\" width=\"91\" height=\"73\">&nbsp; and <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100492082.png\" alt=\"rId8\" width=\"96\" height=\"74\"></p>\n<p>Similarly, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100492235.png\" alt=\"rId9\" width=\"113\" height=\"85\"></p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100491965.png\" alt=\"rId7\" width=\"91\" height=\"73\"> &nbsp;और<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100492082.png\" alt=\"rId8\" width=\"96\" height=\"74\"></p>\n<p>इसी प्रकार, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100492235.png\" alt=\"rId9\" width=\"113\" height=\"85\"></p>\n<p><br><br><br></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language, &lsquo;EAGLE&rsquo; is written as &lsquo;DZFKD&rsquo; and &lsquo;DREAM&rsquo; is written as &lsquo;CQDZL&rsquo;. How will &lsquo;SINCE&rsquo; be written in that language?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में, \'EAGLE\' को \'DZFKD\' के रूप में लिखा जाता है और \'DREAM\' को \'CQDZL\' के रूप में लिखा जाता है। उसी भाषा में \'SINCE\' को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>RJOBD</p>", "<p>THMDF</p>", 
                                "<p>RHMBD</p>", "<p>TJODF</p>"],
                    options_hi: ["<p>RJOBD</p>", "<p>THMDF</p>",
                                "<p>RHMBD</p>", "<p>TJODF</p>"],
                    solution_en: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100492416.png\" alt=\"rId10\" width=\"167\" height=\"88\">&nbsp; ,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100492567.png\" alt=\"rId11\" width=\"161\" height=\"79\"><br>Similarly, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100492757.png\" alt=\"rId12\" width=\"201\" height=\"108\"></p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100492416.png\" alt=\"rId10\" width=\"167\" height=\"88\">&nbsp; ,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100492567.png\" alt=\"rId11\" width=\"161\" height=\"79\"><br>इसी प्रकार, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100492757.png\" alt=\"rId12\" width=\"201\" height=\"108\"><br><br><br></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "5. In a certain code language, \'EXCESS\' is written as \'726572121\', \'ENERGY\' is written as \'716720927\', how will \'ESCAPE\' be written in that language?",
                    question_hi: "5. एक निश्चित कूट भाषा में, \'EXCESS\' को \'726572121\' लिखा जाता है, \'ENERGY\' को \'716720927\' लिखा जाता है, उसी कूट भाषा में \'ESCAPE\' को कैसे लिखा जाएगा?",
                    options_en: [" 72153165 ", " 72151167 ", 
                                " 72153187 ", " 71933187"],
                    options_hi: [" 72153165 ", " 72151167 ",
                                " 72153187 ", " 71933187"],
                    solution_en: "<p>5.(c)<br><strong>Logic:-</strong> place value of each letter + 2<br><strong id=\"docs-internal-guid-0e31c8cf-7fff-db36-d26b-60db1bf473b7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcu-YU2cJMiXCqo_v2VlLHlPxtVCoPBW48R-KfPoCfFJiJQWye1BEXCsaKG4Q5ZwG-QknBIRUwKbddJEXkjHGLJGj25WlSYXgUs2u9wiIiLXpsWejSjruMYIsgZMu7fZec41S-RRQ?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"223\" height=\"101\"></strong><br><strong id=\"docs-internal-guid-3ce8f0b7-7fff-f046-6723-a6953445d171\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcGZL6szTmSsSrboWAZXmfkzGrNEaiTmHEaTeUn_LOn8kV6IuuqTcPjHc2ta9ykn6F9DIWc_moPYh0sN_iOUAlTdO0iY8WEWJ8GM5K0c8HplOCVx4aXOp3nTUjLyyplV8q_KQ7bCQ?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"227\" height=\"103\"></strong><br>Similarly,<strong id=\"docs-internal-guid-59c4b8cc-7fff-0a86-238e-67a0548dc978\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXewZt8n6UJFxXAkJF16Pn3gdXQhCfvBLoHtaWBux_4IN9__ig78VW7iaSNP2HN1p9UBU3E5mp7E_icSkV5QAysx1S4id6FgU2HW3fL3pK7Ae9pH21j3Mx4AnM2GCgDZFJ3zyktJyg?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"242\" height=\"109\"></strong></p>",
                    solution_hi: "<p>5.(c) तर्क :- प्रत्येक अक्षर का स्थानीय मान + 2<br><strong id=\"docs-internal-guid-c7992c80-7fff-0a36-d918-9af69d548eb6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcKYwbOS42T8n97JTc0OK7xRNwgP-RkGwOprq0sH_uwLAS6w_18BgNoZIWDpMc-vgh55Bn4K6xjxCehxkU3SEDtRyKGuh8EpCNvMIXrrxmmTtRJkshuGjhkdLwXbLNU51ToJNdeRA?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"221\" height=\"97\"></strong><br><strong id=\"docs-internal-guid-b7886ef7-7fff-2761-8980-a90fcf048882\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdwBt309lnZ2WF9jgyXrRhh3nKcMICE-6PVcKPhi6szJvziIBoVwv4mqWa5F70CmHYdoUyF4GJ7-WT4c8TGNsOgzBNLIXczyj55RwRUAdZDxh8nfWFAzvvESwrhvVy0wRDdjZTuCA?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"229\" height=\"108\"></strong><br>इसी प्रकार , <br><strong id=\"docs-internal-guid-0a1553a1-7fff-3bde-fd72-2a6b46a46d17\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeVZl6WyXvjCFhPjbv0ZuXCf3drxnQ1KlVtkw6VtAAVswAaxtnq3PhI6SLRSh461C1-Kv9ODR2ejJoEGlTfYih2e2fSnyet89dd-WqWhJy7t-6q8tj89L4AfONU8AKtVzFdPN8S?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"246\" height=\"116\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language, \'CLERK\' is written as \'BKEQJ\' and \'OFFICE\' is written as \'OEEIBE\'. How will \'POST\' be written in that language?</p>",
                    question_hi: "<p>6. एक निश्चित कोड भाषा में, \'CLERK\' को \'BKEQJ\' लिखा जाता है और \'OFFICE\' को \'OEEIBE\' लिखा जाता है। उसी भाषा में \'POST\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>OROS</p>", "<p>OORS</p>", 
                                "<p>RSOO</p>", "<p>OSOR</p>"],
                    options_hi: ["<p>OROS</p>", "<p>OORS</p>",
                                "<p>RSOO</p>", "<p>OSOR</p>"],
                    solution_en: "<p>6.(b)<br><strong>Logic:-</strong> (Consonant - 1) and vowels remain as it is.<br><strong id=\"docs-internal-guid-cb47a026-7fff-69eb-4aa5-131399e0835f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcv-5Loyk2AYtUcbKxOeasb00_0Aw5ZlaCWBKeguOV8MpjaRgmgQcYM47iiv_fZGvxY9TV7AFYSJiV1JEYjW41WtkD93XL1PcryYMjyMYA3zL674s6NPvVFdP21-7zu5ndddf30Nw?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"129\" height=\"87\">&nbsp; ,&nbsp; </strong><strong id=\"docs-internal-guid-bba666fc-7fff-7be9-5ad2-9eaf5b34c6ce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdC5m7nMK0fEmZhNTHyrBrNdbfInLObJoLGFyPaheAKBvZVc5ViNCFdMKcMKtzQtGJqfZUWa3F1Vp08knDf9jcjNit79ccqh80oArzboKzo8FBJmxeuKJWEYTSm3lKT0uU-wU-HeQ?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"131\" height=\"79\"></strong><br><br><strong id=\"docs-internal-guid-990de111-7fff-9210-8396-9dbad573978f\">Similarly, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcAKfKovNU56AtJr5718OoboeqxpYlhM-P4aRGRuoC9qxY5DDAAhttV4oaCTczjyQTq49nG5MW5FLcoezld3i_GzKJGxjn0klYtfFhv0_nfZeOIySbY4Y_qHuJGWxXjJ9ZgnoW5?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"125\" height=\"95\"></strong></p>",
                    solution_hi: "<p>6.(b)<br><strong>तर्क</strong>:- (व्यंजन-1) एवं स्वर यथावत रहेंगे ।<br><strong id=\"docs-internal-guid-cb47a026-7fff-69eb-4aa5-131399e0835f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcv-5Loyk2AYtUcbKxOeasb00_0Aw5ZlaCWBKeguOV8MpjaRgmgQcYM47iiv_fZGvxY9TV7AFYSJiV1JEYjW41WtkD93XL1PcryYMjyMYA3zL674s6NPvVFdP21-7zu5ndddf30Nw?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"129\" height=\"87\">&nbsp; ,&nbsp; </strong><strong id=\"docs-internal-guid-bba666fc-7fff-7be9-5ad2-9eaf5b34c6ce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdC5m7nMK0fEmZhNTHyrBrNdbfInLObJoLGFyPaheAKBvZVc5ViNCFdMKcMKtzQtGJqfZUWa3F1Vp08knDf9jcjNit79ccqh80oArzboKzo8FBJmxeuKJWEYTSm3lKT0uU-wU-HeQ?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"131\" height=\"79\"></strong><br><br><strong id=\"docs-internal-guid-990de111-7fff-9210-8396-9dbad573978f\">इसी प्रकार, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcAKfKovNU56AtJr5718OoboeqxpYlhM-P4aRGRuoC9qxY5DDAAhttV4oaCTczjyQTq49nG5MW5FLcoezld3i_GzKJGxjn0klYtfFhv0_nfZeOIySbY4Y_qHuJGWxXjJ9ZgnoW5?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"125\" height=\"95\"></strong><br><br><br><br><br></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language, \'DRAPE\' is coded as \'65432\' and \'TAPED\' is coded as \'24596\'. What is the code for \'R\' in that language?</p>",
                    question_hi: "<p>7. एक निश्चित कूट भाषा में, \'DRAPE\' को \'65432\' के रूप में कूटबद्ध किया जाता है और \'TAPED\' को \'24596\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'R\' के लिए क्या कूट होगा?</p>",
                    options_en: ["<p>2</p>", "<p>6</p>", 
                                "<p>3</p>", "<p>9</p>"],
                    options_hi: ["<p>2</p>", "<p>6</p>",
                                "<p>3</p>", "<p>9</p>"],
                    solution_en: "<p>7.(c)<br>D R A P E <strong id=\"docs-internal-guid-43773a15-7fff-7607-188d-9456b0b13434\">⟶</strong>&nbsp;6 5 4 3 2<br>T A P E D <strong id=\"docs-internal-guid-43773a15-7fff-7607-188d-9456b0b13434\">⟶</strong>&nbsp;2 4 5 9 6<br>From above code for &lsquo; R&rsquo; is 3.</p>",
                    solution_hi: "<p>7.(c)<br>D R A P E <strong id=\"docs-internal-guid-43773a15-7fff-7607-188d-9456b0b13434\">⟶</strong>&nbsp;6 5 4 3 2<br>T A P E D <strong id=\"docs-internal-guid-43773a15-7fff-7607-188d-9456b0b13434\">⟶</strong>&nbsp;2 4 5 9 6<br>उपरोक्त कोड से \'R\' के लिए कोड \'3\' है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain code language, &lsquo;BREAK&rsquo; is coded as &lsquo;57123&rsquo; and &lsquo;BROKE&rsquo; is coded as &lsquo;91753&rsquo;. What is the code for &lsquo;A&rsquo; in that language?</p>",
                    question_hi: "<p>8. एक निश्चित कूट भाषा में, \'BREAK\' को \'57123\' के रूप में कूटबद्ध किया जाता है और \'BROKE\' को \'91753\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'A\' के लिए क्या कूट होगा?</p>",
                    options_en: ["<p>2</p>", "<p>5</p>", 
                                "<p>9</p>", "<p>3</p>"],
                    options_hi: ["<p>2</p>", "<p>5</p>",
                                "<p>9</p>", "<p>3</p>"],
                    solution_en: "<p>8.(a)<br>B R E A K <math display=\"inline\"><mo>&#8594;</mo></math> 5 7 1 2 3<br>B R O K E <math display=\"inline\"><mo>&#8594;</mo></math> 9 1 7 5 3<br>From above code for &lsquo;A&rsquo; is 2</p>",
                    solution_hi: "<p>8.(a)<br>B R E A K <math display=\"inline\"><mo>&#8594;</mo></math> 5 7 1 2 3<br>B R O K E <math display=\"inline\"><mo>&#8594;</mo></math> 9 1 7 5 3<br>उपरोक्त कोड से \'A\' के लिए कोड &lsquo;2&rsquo; है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language, \'CASE\' is written as \'FTBD\' and \'CAKE\' is written as \'FLBD\'. How will \'CALF&rsquo; be written in that language?</p>",
                    question_hi: "<p>9. एक निश्चित कूट भाषा में, \'CASE\' को \'FTBD\' लिखा जाता है और &lsquo;CAKE\' को \'FLBD\' लिखा जाता है। इसी कूट भाषा में \'CALF&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>GMDB</p>", "<p>GBMD</p>", 
                                "<p>GMBD</p>", "<p>GBDM</p>"],
                    options_hi: ["<p>GMDB</p>", "<p>GBMD</p>",
                                "<p>GMBD</p>", "<p>GBDM</p>"],
                    solution_en: "<p>9.(c)<br><strong id=\"docs-internal-guid-08dd966f-7fff-a3b1-fb4b-d47650ffefd2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdKM6IZfVp7nFSlYjiKhr2V8F1Nj1Dird0Mw7cf0Owa2Bhj9xB-P5hxZq0sY45-1gj8fM-hEn62HXCYl-BQYGkOobrAo-FS50Lxi8WXWC0vnk9XIV2WKLc5EURfEI3pBSPj34JBsg?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"125\" height=\"92\"> , &nbsp;<strong id=\"docs-internal-guid-2463cee7-7fff-c870-70c5-4d41b61ee386\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfo29UIWTWTGxLSMCHVDpVLPxUJzXMYlnOEQ3vYTQPuUiW2Gq7zBOQdGsVwMRunOCKEDR8KBliNHStjm_lvPjfux48cJ1HsM7Xb9dT3eglx3ywLvYToUc6aiFWfjPWhvTkdE0RG?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"117\" height=\"95\"></strong></strong><br>Similarly<strong id=\"docs-internal-guid-e1879ebd-7fff-dcbe-621d-aaaa62d1c5b2\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeIfWyKKYKFIqGdXteup_rWZzcBbqya2X5xUmGrJ6O9i4E_BH_-hA2jSSKbK1irndL-oFtWbBeawTixFToIHYoieSoGMXsjPqYCGmMyB0b-kstGzTcMUtPbM2pbB1obaikdfhloJg?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"139\" height=\"102\"></strong><br><br></p>",
                    solution_hi: "<p>9.(c)<br><strong id=\"docs-internal-guid-08dd966f-7fff-a3b1-fb4b-d47650ffefd2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdKM6IZfVp7nFSlYjiKhr2V8F1Nj1Dird0Mw7cf0Owa2Bhj9xB-P5hxZq0sY45-1gj8fM-hEn62HXCYl-BQYGkOobrAo-FS50Lxi8WXWC0vnk9XIV2WKLc5EURfEI3pBSPj34JBsg?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"125\" height=\"92\"> , &nbsp;<strong id=\"docs-internal-guid-2463cee7-7fff-c870-70c5-4d41b61ee386\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfo29UIWTWTGxLSMCHVDpVLPxUJzXMYlnOEQ3vYTQPuUiW2Gq7zBOQdGsVwMRunOCKEDR8KBliNHStjm_lvPjfux48cJ1HsM7Xb9dT3eglx3ywLvYToUc6aiFWfjPWhvTkdE0RG?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"117\" height=\"95\"></strong></strong><br>इसी प्रकार,<strong id=\"docs-internal-guid-e1879ebd-7fff-dcbe-621d-aaaa62d1c5b2\"> <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeIfWyKKYKFIqGdXteup_rWZzcBbqya2X5xUmGrJ6O9i4E_BH_-hA2jSSKbK1irndL-oFtWbBeawTixFToIHYoieSoGMXsjPqYCGmMyB0b-kstGzTcMUtPbM2pbB1obaikdfhloJg?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"139\" height=\"102\"></strong><br><br><br></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language, FIGURE is coded as &lsquo;VSUGJW&rsquo; and COPIED is coded as &lsquo;YMLSWX&rsquo;. What will be the code for BLANKET?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में, FIGURE को \'VSUGJW\' के रूप में कूटबद्ध किया जाता है और COPIED को \'YMLSWX\' के रूप में कूटबद्ध किया जाता है। तो उसी कूट भाषा में BLANKET के लिए कूट क्या होगा?</p>",
                    options_en: ["<p>ZPANQWH</p>", "<p>ZPMNQHW</p>", 
                                "<p>ZPANQHW</p>", "<p>ZPNAQWH</p>"],
                    options_hi: ["<p>ZPANQWH</p>", "<p>ZPMNQHW</p>",
                                "<p>ZPANQHW</p>", "<p>ZPNAQWH</p>"],
                    solution_en: "<p>10.(a)<br><strong id=\"docs-internal-guid-b5135081-7fff-e070-02c3-6d8b1826b823\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf105nPGPEdgbiVyU_fZN--rSm0eF58LQrkHspja22S7XbU2KqIWaV1n3vIgCYz7UAaiYidHodgfP2WowCUe6yKwXOtitrmSTvcfZJmkBa7TN9PDbEH6VC3qlpdjq3A1EfzERAkTw?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"200\" height=\"112\"> </strong>and <strong id=\"docs-internal-guid-b5135081-7fff-e070-02c3-6d8b1826b823\"><strong id=\"docs-internal-guid-c6ad6599-7fff-899d-7add-135ab557a742\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdyNyHTAgBHm1PXAq-ZmDEEH5bkpgpIOy7UrBPEsKJtMApDVi8GFspQ1alp7dI7riu3dXY2YtroGeAkghrEUJ4CcM3mJ8croHhRP01aVrhpsughw0eD6i6XSKsrJBXzkO__avEVxw?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"206\" height=\"111\"></strong></strong><br><strong id=\"docs-internal-guid-41f8dc0c-7fff-ae93-286e-d53c09e2c092\"></strong></p>\n<p>Similarly<strong id=\"docs-internal-guid-41f8dc0c-7fff-ae93-286e-d53c09e2c092\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfcQc2EUN-Af4Evxzv7tYt-yabeuDdIM8MI5hLBhSsajJJVeJ4pyb55lytOZiBKooRkYKIPBIShHxpWZ90_ud5yotLnKfJULAJ8s6JIHEf78QehYQDsUp3SexgvPvRE95sDLUYa?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"224\" height=\"104\"></strong><br><br><br><br></p>",
                    solution_hi: "<p>10.(a)<br><strong id=\"docs-internal-guid-b5135081-7fff-e070-02c3-6d8b1826b823\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf105nPGPEdgbiVyU_fZN--rSm0eF58LQrkHspja22S7XbU2KqIWaV1n3vIgCYz7UAaiYidHodgfP2WowCUe6yKwXOtitrmSTvcfZJmkBa7TN9PDbEH6VC3qlpdjq3A1EfzERAkTw?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"200\" height=\"112\"> </strong>और<strong id=\"docs-internal-guid-b5135081-7fff-e070-02c3-6d8b1826b823\"><strong id=\"docs-internal-guid-c6ad6599-7fff-899d-7add-135ab557a742\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdyNyHTAgBHm1PXAq-ZmDEEH5bkpgpIOy7UrBPEsKJtMApDVi8GFspQ1alp7dI7riu3dXY2YtroGeAkghrEUJ4CcM3mJ8croHhRP01aVrhpsughw0eD6i6XSKsrJBXzkO__avEVxw?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"206\" height=\"111\"></strong></strong><br><strong id=\"docs-internal-guid-41f8dc0c-7fff-ae93-286e-d53c09e2c092\"></strong></p>\n<p>इसी प्रकार<strong id=\"docs-internal-guid-41f8dc0c-7fff-ae93-286e-d53c09e2c092\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfcQc2EUN-Af4Evxzv7tYt-yabeuDdIM8MI5hLBhSsajJJVeJ4pyb55lytOZiBKooRkYKIPBIShHxpWZ90_ud5yotLnKfJULAJ8s6JIHEf78QehYQDsUp3SexgvPvRE95sDLUYa?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"224\" height=\"104\"></strong></p>\n<p><br><br><br><br><br></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language, &lsquo;FRAMED&rsquo; is coded as &lsquo;497621&rsquo; and &lsquo;DREAMT&rsquo; is coded as &lsquo;769542&rsquo;. What is the code for &lsquo;T&rsquo; in that language?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में, &lsquo;FRAMED&rsquo; को &lsquo;497621&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;DREAMT&rsquo; को &lsquo;769542&rsquo; के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'T\' के लिए क्या कूट होगा?</p>",
                    options_en: ["<p>5</p>", "<p>1</p>", 
                                "<p>4</p>", "<p>7</p>"],
                    options_hi: ["<p>5</p>", "<p>1</p>",
                                "<p>4</p>", "<p>7</p>"],
                    solution_en: "<p>11.(a)<br>F R A M E D <math display=\"inline\"><mo>&#8594;</mo></math> 4 9 7 6 2 1<br>D R E A M T <math display=\"inline\"><mo>&#8594;</mo></math> 7 6 9 5 4 2<br>From above code for &lsquo;T&rsquo; is 5</p>",
                    solution_hi: "<p>11.(a)<br>F R A M E D <math display=\"inline\"><mo>&#8594;</mo></math> 4 9 7 6 2 1<br>D R E A M T <math display=\"inline\"><mo>&#8594;</mo></math> 7 6 9 5 4 2<br>उपरोक्त कोडिंग से \'T\' के लिए कोड 5 है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. In a certain code language, &lsquo;D&rsquo; is coded as &lsquo;16&rsquo;, &lsquo;B&rsquo; is coded as &lsquo;4&rsquo; and &lsquo;T&rsquo; is coded as &lsquo;400&rsquo;. How will &lsquo;H&rsquo; be coded in the same language?</p>",
                    question_hi: "<p>12. एक निश्चित कूट भाषा में, \'D\' को \'16\' के रूप में कूटबद्ध किया जाता है, \'B\' को \'4\' के रूप में कूटबद्ध किया जाता है और \'T\' को \'400\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'H\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>8</p>", "<p>64</p>", 
                                "<p>25</p>", "<p>36</p>"],
                    options_hi: ["<p>8</p>", "<p>64</p>",
                                "<p>25</p>", "<p>36</p>"],
                    solution_en: "<p>12.(b)<br><strong>Logic</strong> :- number = (alphabetical place value)<sup>2</sup><br>D <math display=\"inline\"><mo>&#8594;</mo></math> (4)<sup>2</sup> = 16<br>B <math display=\"inline\"><mo>&#8594;</mo></math> (2)<sup>2</sup> = 4<br>T <math display=\"inline\"><mo>&#8594;</mo></math> (20)<sup>2</sup> = 400<br>Similarly<br>H <math display=\"inline\"><mo>&#8594;</mo></math> (8)<sup>2</sup> = 64</p>",
                    solution_hi: "<p>12.(b)<br><strong>तर्क</strong> :- संख्या = (वर्णमाला में स्थानीय मान)<sup>2</sup><br>D <math display=\"inline\"><mo>&#8594;</mo></math> (4)<sup>2</sup> = 16<br>B <math display=\"inline\"><mo>&#8594;</mo></math> (2)<sup>2</sup> = 4<br>T <math display=\"inline\"><mo>&#8594;</mo></math> (20)<sup>2</sup> = 400<br>इसी प्रकार <br>H <math display=\"inline\"><mo>&#8594;</mo></math> (8)<sup>2</sup> = 64</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain code language, &lsquo;FUTURE&rsquo; is written as &lsquo;IXWXUH&rsquo; and &lsquo;ISLAND&rsquo; is written as &lsquo;LVODQG&rsquo;. How will &lsquo;JERSEY&rsquo; be written in that language?</p>",
                    question_hi: "<p>13. एक निश्चित कूट भाषा में, \'FUTURE\' को \'IXWXUH\' के रूप में लिखा जाता है और \'ISLAND\' को \'LVODQG\' के रूप में लिखा जाता है। उसी भाषा में &lsquo;JERSEY&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>KGTVGA</p>", "<p>KHTUHA</p>", 
                                "<p>MHTVGB</p>", "<p>MHUVHB</p>"],
                    options_hi: ["<p>KGTVGA</p>", "<p>KHTUHA</p>",
                                "<p>MHTVGB</p>", "<p>MHUVHB</p>"],
                    solution_en: "<p>13.(d)<br><strong id=\"docs-internal-guid-34cc592e-7fff-2c18-5bce-aec1a8614fa9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcQgV9iXVu4AONiDrDm7pMQMZwpIlpU0xBD-UOheqUecvjeETTEIskrB0NcZmxqnJQP9TO_wX6aTlZ570cTGPSeIuUr2u3yfItY4RhO8iTy5LbAkN7rzosidkXCb6rCkj4AWoxCMg?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"157\" height=\"69\"> &nbsp;and &nbsp;<strong id=\"docs-internal-guid-5f523a63-7fff-6f8e-2f94-7bceca5a9950\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXegLMpUIgbgjQTYqZH-6O8ELKI_CJesAR5QmnYDb_UHt3AM_xabjN3FMYvP0HjVdhAwm0D13Ek4209x4E6m3IboGUincK2OUJP-jqGIC60LowFQxdipn4BS3Toc44ribr9ijEN5?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"151\" height=\"70\"></strong></strong><br><strong id=\"docs-internal-guid-f11921a8-7fff-84f0-4e57-d78fa7481ef4\"></strong></p>\n<p>Similarly<strong id=\"docs-internal-guid-f11921a8-7fff-84f0-4e57-d78fa7481ef4\">, <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfDW9kBU8X20Amh6-7EjCno-ats9MV4o4K1voocTv1k2YdL_NSs6v64p7-Ok0psOJ0QnAplio6NhD0yFZ2eSqENCDxmwrecWm2IyV9AVciov37JbdS50HtSF9gtPEgH04YNbY95Kg?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"144\" height=\"76\"></strong><br><br><br></p>",
                    solution_hi: "<p>13.(d)<br><strong id=\"docs-internal-guid-34cc592e-7fff-2c18-5bce-aec1a8614fa9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcQgV9iXVu4AONiDrDm7pMQMZwpIlpU0xBD-UOheqUecvjeETTEIskrB0NcZmxqnJQP9TO_wX6aTlZ570cTGPSeIuUr2u3yfItY4RhO8iTy5LbAkN7rzosidkXCb6rCkj4AWoxCMg?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"157\" height=\"69\"> &nbsp;</strong>और<strong id=\"docs-internal-guid-34cc592e-7fff-2c18-5bce-aec1a8614fa9\"><strong id=\"docs-internal-guid-5f523a63-7fff-6f8e-2f94-7bceca5a9950\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXegLMpUIgbgjQTYqZH-6O8ELKI_CJesAR5QmnYDb_UHt3AM_xabjN3FMYvP0HjVdhAwm0D13Ek4209x4E6m3IboGUincK2OUJP-jqGIC60LowFQxdipn4BS3Toc44ribr9ijEN5?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"151\" height=\"70\"></strong></strong><br><strong id=\"docs-internal-guid-f11921a8-7fff-84f0-4e57-d78fa7481ef4\"></strong></p>\n<p>इसी प्रकार,<strong id=\"docs-internal-guid-f11921a8-7fff-84f0-4e57-d78fa7481ef4\"> <img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfDW9kBU8X20Amh6-7EjCno-ats9MV4o4K1voocTv1k2YdL_NSs6v64p7-Ok0psOJ0QnAplio6NhD0yFZ2eSqENCDxmwrecWm2IyV9AVciov37JbdS50HtSF9gtPEgH04YNbY95Kg?key=Wsm6QjQJLoypIPlz-0FYBjnM\" width=\"144\" height=\"76\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, &lsquo;bright colour rainbow&rsquo; is coded as &lsquo;mq bj st&rsquo; and &lsquo;sunny bright day&rsquo; is coded as &lsquo;nv bj fm&rsquo;. How is &lsquo;bright&rsquo; coded in that language?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में, \'bright colour rainbow\' को \'mq bj st\' के रूप में कूटबद्ध किया जाता है और \'sunny bright day\' को \'nv bj fm\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'bright\' को किस प्रकार कूटबद्ध किया है?</p>",
                    options_en: ["<p>bj</p>", "<p>nv</p>", 
                                "<p>st</p>", "<p>mq</p>"],
                    options_hi: ["<p>bj</p>", "<p>nv</p>",
                                "<p>st</p>", "<p>mq</p>"],
                    solution_en: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100496852.png\" alt=\"rId35\" width=\"329\" height=\"67\"><br>Hence, the code for bright is bj.</p>",
                    solution_hi: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732100496852.png\" alt=\"rId35\" width=\"329\" height=\"67\"><br>अतः, Bright के लिए कूट bj है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In a certain code language, \'open the door\' is coded as \'wu ko tn\' and \'door is solid\' is coded as \'ko gk mb\'. How is \'door\' coded in the given language?</p>",
                    question_hi: "<p>15. एक निश्चित कूट भाषा में, &lsquo;open the door&rsquo; को \'wu ko tn\' के रूप में कूटबद्ध किया गया है और &lsquo;door is solid&rsquo; को &lsquo;ko gk mb&rsquo; के रूप में कूटबद्ध किया गया है। दी गई भाषा में &lsquo;door&rsquo; को किस प्रकार से कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>tn</p>", "<p>ko</p>", 
                                "<p>gk</p>", "<p>wu</p>"],
                    options_hi: ["<p>tn</p>", "<p>ko</p>",
                                "<p>gk</p>", "<p>wu</p>"],
                    solution_en: "<p>15.(b)<br>Open the door <math display=\"inline\"><mo>&#8594;</mo></math> wu ko tn<br>Door is solid <math display=\"inline\"><mo>&#8594;</mo></math> ko gk mb<br>From above door is common.<br>Hence code for door is &lsquo;ko&rsquo;</p>",
                    solution_hi: "<p>15.(b)<br>Open the door <math display=\"inline\"><mo>&#8594;</mo></math> wu ko tn<br>Door is solid <math display=\"inline\"><mo>&#8594;</mo></math> ko gk mb<br>उपरोक्त कोडिंग में door उभयनिष्ट है । <br>अतः door के लिए कोड \'ko\' है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>