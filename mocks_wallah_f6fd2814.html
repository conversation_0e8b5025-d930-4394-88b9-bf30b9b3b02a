<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 27</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">27</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 25
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 26,
                end: 26
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the number from the given options that will come next in the following series.<br>0, 6, 24, 60, ____.</p>",
                    question_hi: "<p>1. दिए गए विकल्पों में से उस संख्या का चयन कीजिये जो निम्नलिखित श्रृंखला में आगे आएगी।<br>0, 6, 24, 60, ____.</p>",
                    options_en: ["<p>114</p>", "<p>144</p>", 
                                "<p>126</p>", "<p>120</p>"],
                    options_hi: ["<p>114</p>", "<p>144</p>",
                                "<p>126</p>", "<p>120</p>"],
                    solution_en: "<p>1.(d)<br>0 + 6 &times; 1 = 6<br>6 + 6 &times; 3 = 24<br>24 + 6 &times; 6 = 60<br>60 + 6 &times; 10 = <strong>120</strong></p>",
                    solution_hi: "<p>1.(d)<br>0 + 6 &times; 1 = 6<br>6 + 6 &times; 3 = 24<br>24 + 6 &times; 6 = 60<br>60 + 6 &times; 10 = <strong>120</strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the option that is related to the third term in the same way as the second term is related to the first term.<br>Day : Week :: Month : ?</p>",
                    question_hi: "<p>2. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br>दिन : सप्ताह :: महिना : ?</p>",
                    options_en: ["<p>Calendar</p>", "<p>Annual</p>", 
                                "<p>Weeks</p>", "<p>Year</p>"],
                    options_hi: ["<p>कैलेंडर</p>", "<p>वार्षिक</p>",
                                "<p>सप्ताह</p>", "<p>वर्ष</p>"],
                    solution_en: "<p>2.(d)<br>7 days a week, in the same way 30 days a month.</p>",
                    solution_hi: "<p>2.(d)<br>सप्ताह में 7 दिन, इसी तरह महीने में 30 दिन।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In the number set given below, all are alike in some manner, except one. Select the odd one.<br>2, 28, 48, 58,128</p>",
                    question_hi: "<p>3. नीचे दिए गए संख्या समुच्चय में एक को छोड़कर सभी किसी न किसी रूप में समान हैं। विजातीय का चयन कीजिये ।<br>2, 28, 48, 58,128</p>",
                    options_en: ["<p>128</p>", "<p>28</p>", 
                                "<p>48</p>", "<p>2</p>"],
                    options_hi: ["<p>128</p>", "<p>28</p>",
                                "<p>48</p>", "<p>2</p>"],
                    solution_en: "<p>3.(d)<br>Except 2, all are composite numbers. 2 is a prime number.</p>",
                    solution_hi: "<p>3.(d)<br>2 को छोड़कर, सभी भाज्य संख्याएँ हैं। 2 एक अभाज्य संख्या है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. &lsquo;Centimetre&rsquo; is related to &lsquo;Metre&rsquo; in the same way as &lsquo;Paisa&rsquo; is related to &lsquo;_______&rsquo;.</p>",
                    question_hi: "<p>4. &lsquo;सेंटीमीटर&rsquo; का संबंध \'मीटर\' से उसी प्रकार है जैसे \'पैसा\' का संबंध \'_______\' से है।</p>",
                    options_en: ["<p>Capital</p>", "<p>Wealth</p>", 
                                "<p>Rupee</p>", "<p>Coin</p>"],
                    options_hi: ["<p>धन</p>", "<p>सम्पदा</p>",
                                "<p>रुपया</p>", "<p>सिक्का</p>"],
                    solution_en: "<p>4.(c)<br>100 paise = 1 rupee</p>",
                    solution_hi: "<p>4.(c)<br>100 पैसे = 1 रुपया</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Consider the given statement and decide which of the given assumptions is/are implicit in the statement.<br><strong>Statement :</strong><br>&ldquo;Buy pure and natural honey of company Z&rdquo; an advertisement.<br><strong>Assumption : :</strong><br>I. Artificial honey can be prepared <br>II. People do not mind for paying more prices for pure and natural honey.</p>",
                    question_hi: "<p>5. दिए गए कथन पर विचार कीजिये और बताइये कि दी गई अवधारणा में से कौन सा कथन में निहित हैं।<br><strong>कथन :</strong><br>&ldquo;कंपनी Z का शुद्ध और प्राकृतिक शहद खरीदें&rdquo;- एक विज्ञापन<br><strong>अवधारणा :</strong><br>I. कृत्रिम शहद तैयार किया जा सकता है<br>II. शुद्ध और प्राकृतिक शहद के अधिक दाम चुकाने से लोग नहीं हिचकिचाते</p>",
                    options_en: ["<p>Both assumption I and II are implicit.</p>", "<p>Only assumption I is implicit.</p>", 
                                "<p>Neither assumption I nor II is implicit.</p>", "<p>Only assumption II is implicit.</p>"],
                    options_hi: ["<p>अवधारणा I और II दोनों अंतर्निहित हैं</p>", "<p>केवल अवधारणा I निहित है</p>",
                                "<p>न तो अवधारणा I और न ही II निहित है।</p>", "<p>केवल अवधारणा II निहित है</p>"],
                    solution_en: "<p>5.(b) <br>only assumption I is implicit because company advt. nothing tell about price .</p>",
                    solution_hi: "<p>5.(b)<br>केवल धारणा I निहित है क्योंकि कंपनी सलाह। कीमत के बारे में कुछ भी बताया नहीं है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Consider the given statements and decide which of the assumptions from the options is implicit in the statements<br>A. Some animals are colour-blind.<br>B. All colorfuls are colour-blind but not animals.</p>",
                    question_hi: "<p>6. दिए गए कथनों पर विचार कीजिये और बताइए कि विकल्पों में से कौन सी अवधारणा कथनों में निहित है।<br>A. कुछ जानवर वर्णांध होते हैं<br>B. सभी रंगीन वर्णांध हैं लेकिन जानवर नहीं है।</p>",
                    options_en: ["<p>All colour-blinds are animals.</p>", "<p>Some colour-blinds are animals.</p>", 
                                "<p>All colour-blinds are colourful.</p>", "<p>Some animals are colourful.</p>"],
                    options_hi: ["<p>सभी वर्णांध जानवर हैं</p>", "<p>कुछ वर्णांध जानवर हैं।</p>",
                                "<p>सभी वर्णांध रंगीन है</p>", "<p>कुछ जानवर रंगीन हैं</p>"],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472457656.png\" alt=\"rId4\" width=\"293\" height=\"106\"></p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472457760.png\" alt=\"rId5\" width=\"202\" height=\"86\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Consider the given statements and decide which of the assumptions from the options is <strong>NOT</strong> implicit in the statements.<br>All birds are wings. <br>Some wings are feathers. <br>Some feathers are birds.</p>",
                    question_hi: "<p>7. दिए गए कथनों पर विचार कीजिये और बताइये कि विकल्पों में से कौन सी धारणा कथनों में निहित <strong>नहीं</strong> है।<br>सभी पक्षी पंख हैं<br>कुछ पंख पर है<br>कुछ पर पक्षी हैं</p>",
                    options_en: ["<p>Some birds are feathers.</p>", "<p>Some birds are wings and feathers.</p>", 
                                "<p>Some feathers are wings.</p>", "<p>All wings are birds.</p>"],
                    options_hi: ["<p>कुछ पक्षी पर हैं।</p>", "<p>कुछ पक्षी पंख और पर हैं</p>",
                                "<p>कुछ पर पंख हैं।</p>", "<p>सभी पंख पक्षी हैं।</p>"],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472457867.png\" alt=\"rId6\" width=\"254\" height=\"99\"></p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472457988.png\" alt=\"rId7\" width=\"210\" height=\"92\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Study the given patterns carefully and select the number from among the given options that can replace the question mark(?).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458075.png\" alt=\"rId8\" width=\"188\" height=\"175\"></p>",
                    question_hi: "<p>8. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और दिए गए विकल्पों में से उस संख्या का चयन कीजिये जो प्रश्न चिह्न (?) को प्रतिस्थापित कर सके।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458075.png\" alt=\"rId8\" width=\"188\" height=\"175\"></p>",
                    options_en: ["<p>190</p>", "<p>808</p>", 
                                "<p>910</p>", "<p>901</p>"],
                    options_hi: ["<p>190</p>", "<p>808</p>",
                                "<p>910</p>", "<p>901</p>"],
                    solution_en: "<p>8.(c)<br><strong>Logic</strong> : pair of natural numbers <br>That is 12, 34, 56, 78, 910, 1112</p>",
                    solution_hi: "<p>8.(c)<br><strong>तर्क</strong> : प्राकृत संख्याओं का युग्म<br>यानी 12, 34, 56, 78, 910, 1112</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Four options are given below, of which three are similar in a certain manner while one is different from the rest. Find the odd one.</p>",
                    question_hi: "<p>9. नीचे चार विकल्प दिए गए हैं, जिनमें से तीन एक निश्चित तरीके से समान हैं जबकि एक बाकी से अलग है। विजातीय को चयन कीजिये ?</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", 
                                "<p><math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>9</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>",
                                "<p><math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>9</mn></msqrt></math></p>"],
                    solution_en: "<p>9.(d)<br>Only&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>9</mn></msqrt><mo>=</mo><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi></math><br>others do not get exact values of square root.</p>",
                    solution_hi: "<p>9.(d)<br>केवल&nbsp; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>9</mn></msqrt><mo>=</mo><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi></math><br>दूसने विकल्पों के वर्गमूल का सटीक मान नहीं मिलता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the number from among the following options that will come next in the following series.<br>44, 37, 29, ____.</p>",
                    question_hi: "<p>10. निम्नलिखित विकल्पों में से उस संख्या का चयन कीजिये जो निम्नलिखित श्रृंखला में आगे आएगी<br>44, 37, 29, ____.</p>",
                    options_en: ["<p>16</p>", "<p>20</p>", 
                                "<p>55</p>", "<p>11</p>"],
                    options_hi: ["<p>16</p>", "<p>20</p>",
                                "<p>55</p>", "<p>11</p>"],
                    solution_en: "<p>10.(b)<br>44 - 37 = 7<br>37 - 29 = 8<br>29 - x = 9 <math display=\"inline\"><mo>&#8658;</mo></math> x = <strong>20</strong></p>",
                    solution_hi: "<p>10.(b)<br>44 - 37 = 7<br>37 - 29 = 8<br>29 - x = 9 <math display=\"inline\"><mo>&#8658;</mo></math> x = <strong>20</strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. A question is given followed by two arguments. Decide which of the arguments is /are strong with respect to the question.<br><strong>Question :</strong><br>Should electronic gadgets be allowed in an examination hall?<br><strong>Arguments :</strong><br>I. No, it is easy to chat with their help in an examination. <br>II. Yes, electronic gadgets are costly and it is not safe to leave them outside the examination hall.</p>",
                    question_hi: "<p>11. एक प्रश्न के बाद दो तर्क दिए गए हैं। बताइये कि प्रश्न के संबंध में कौन-सा तर्क प्रबल हैं।<br><strong>प्रश्न:</strong><br>क्या परीक्षा हॉल में इलेक्ट्रॉनिक गैजेट की अनुमति होनी चाहिए?<br><strong>तर्क:</strong><br>I. नहीं, परीक्षा में उनकी सहायता से बात करना आसान है।<br>II. हां, इलेक्ट्रॉनिक गैजेट महंगे हैं और उन्हें परीक्षा हॉल के बाहर छोड़ना सुरक्षित नहीं है।</p>",
                    options_en: ["<p>Only argument II is strong.</p>", "<p>Neither argument I nor II are strong.</p>", 
                                "<p>Both argument I and II are strong.</p>", "<p>Only argument I is strong.</p>"],
                    options_hi: ["<p>केवल तर्क II प्रबल है</p>", "<p>न तो तर्क I और न ही II प्रबल हैं।</p>",
                                "<p>तर्क I और II दोनों प्रबल हैं।</p>", "<p>केवल तर्क I प्रबल है</p>"],
                    solution_en: "<p>11.(d) Only argument I is strong because in exam hall electronic gadgets are not used because they help cheat in exam hall.</p>",
                    solution_hi: "<p>11.(d) केवल तर्क I मजबूत है क्योंकि परीक्षा हॉल में इलेक्ट्रॉनिक गैजेट्स का उपयोग नहीं किया जाता है क्योंकि इलेक्ट्रॉनिक गैजेट्स, परीक्षा हॉल में नकल करने में मदद करते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Study the given pattern carefully and select the number from among the given options that can replace the question mark(?).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458171.png\" alt=\"rId9\" width=\"153\" height=\"121\"></p>",
                    question_hi: "<p>12. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और दिए गए विकल्पों में से उस संख्या का चयन करें जो प्रश्न चिह्न (?) को प्रतिस्थापित कर सके।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458171.png\" alt=\"rId9\" width=\"162\" height=\"128\"></p>",
                    options_en: ["<p>13</p>", "<p>12</p>", 
                                "<p>24</p>", "<p>9</p>"],
                    options_hi: ["<p>13</p>", "<p>12</p>",
                                "<p>24</p>", "<p>9</p>"],
                    solution_en: "<p>12.(d)<br>( 13 + 7 ) &divide; 5 = 4<br>( 13 + 7 ) &divide; 4 = 5<br>( x + 9) &divide; 3 = 6<br>x = 9</p>",
                    solution_hi: "<p>12.(d)<br>( 13 + 7 ) &divide; 5 = 4<br>( 13 + 7 ) &divide; 4 = 5<br>( x + 9) &divide; 3 = 6<br>x = 9</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Three of the following four letter - clusters are alike in a certain way and one is different Pick the odd one out.</p>",
                    question_hi: "<p>13. निम्नलिखित चार अक्षरों में से तीन-समूह एक निश्चित तरीके से समान हैं और एक अलग है। विजातीय को चुनिए ।</p>",
                    options_en: ["<p>HIKNR</p>", "<p>EFGIK</p>", 
                                "<p>FGILP</p>", "<p>JKMPT</p>"],
                    options_hi: ["<p>HIKNR</p>", "<p>EFGIK</p>",
                                "<p>FGILP</p>", "<p>JKMPT</p>"],
                    solution_en: "<p>13.(b) <strong>Logic:</strong><br>H + 1 = I, I + 2 = K, K + 3 = N, N + 4 = R <br>its logic is also apply in FGILP and JKMPT<br><math display=\"inline\"><mo>&#8594;</mo></math> its logic does not apply in EFGIK.</p>",
                    solution_hi: "<p>13.(b) <strong>तर्क:</strong><br>H + 1 = I, I + 2 = K, K + 3 = N, N + 4 = R <br>इसका तर्क FGILP और JKMPT में भी लागू होता है<br>इसका तर्क EFGIK में लागू नहीं होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. If A = 26 and T = 7, then AUTHENTIC = ?</p>",
                    question_hi: "<p>14. यदि A = 26 और T = 7, तब AUTHENTIC = ?</p>",
                    options_en: ["<p>266719221371824</p>", "<p>266719221471824</p>", 
                                "<p>266719221371825</p>", "<p>266719221371823</p>"],
                    options_hi: ["<p>266719221371824</p>", "<p>266719221471824</p>",
                                "<p>266719221371825</p>", "<p>266719221371823</p>"],
                    solution_en: "<p>14.(a)<br>A <math display=\"inline\"><mo>&#8594;</mo></math> 26, T &rarr; 7<br><strong>Logic</strong> <math display=\"inline\"><mo>&#8594;</mo></math> using opposite face value<br>AUTHENTIC<math display=\"inline\"><mo>&#8594;</mo></math>266719221371824</p>",
                    solution_hi: "<p>14.(a)<br>A <math display=\"inline\"><mo>&#8594;</mo></math> 26, T &rarr; 7<br><strong>तर्क</strong> <math display=\"inline\"><mo>&#8594;</mo></math> विपरीत अंकित मूल्य का उपयोग करना<br>AUTHENTIC<math display=\"inline\"><mo>&#8594;</mo></math>266719221371824</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the option that is related to the third term in the same way as the second term is related to the first term.<br>FLPX : GJST :: HMRW : ?</p>",
                    question_hi: "<p>15. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है<br>FLPX : GJST :: HMRW : ?</p>",
                    options_en: ["<p>WDHO</p>", "<p>NSTG</p>", 
                                "<p>ZKYR</p>", "<p>IKUS</p>"],
                    options_hi: ["<p>WDHO</p>", "<p>NSTG</p>",
                                "<p>ZKYR</p>", "<p>IKUS</p>"],
                    solution_en: "<p>15.(d)<br><strong>Logic</strong> <strong>:</strong><br>In FLPX : GJST<br>F + 1 = G, L - 2 = J, P + 3 = S, X - 4 = T<br>So that HMRW : <strong>IKUS</strong></p>",
                    solution_hi: "<p>15.(d)<br><strong>तर्क :</strong><br>FLPX में : GJST<br>F + 1 = G, L - 2 = J, P + 3 = S, X - 4 = T<br>इसलिए HMRW : <strong>IKUS</strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the Venn diagram that best represents the relationship between the following classes.<br>Guava , Potato , Vegetables</p>",
                    question_hi: "<p>16. वेन आरेख का चयन कीजिये जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है।<br>अमरूद, आलू, सब्जियां</p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458281.png\" alt=\"rId10\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458386.png\" alt=\"rId11\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458495.png\" alt=\"rId12\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458625.png\" alt=\"rId13\" width=\"322\" height=\"92\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458281.png\" alt=\"rId10\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458386.png\" alt=\"rId11\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458495.png\" alt=\"rId12\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458625.png\" alt=\"rId13\" width=\"322\" height=\"92\"></p>"],
                    solution_en: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458721.png\" alt=\"rId14\" width=\"232\" height=\"89\"><br>Potatoes are vegetables and guava is a fruit.</p>",
                    solution_hi: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458823.png\" alt=\"rId15\" width=\"239\" height=\"90\"><br>आलू सब्जियां हैं और अमरूद एक फल है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. In a certain code language, HUNGER is written as UHATRE. How will SWIMMING be written as in that language ?</p>",
                    question_hi: "<p>17. एक निश्चित कोड भाषा में, HUNGER को UHATRE के रूप में लिखा जाता है। SWIMMING को उसी भाषा में कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>UBFCCFCW</p>", "<p>FJVZZVAT</p>", 
                                "<p>GAPTTPTL</p>", "<p>FCKSSKSM</p>"],
                    options_hi: ["<p>UBFCCFCW</p>", "<p>FJVZZVAT</p>",
                                "<p>GAPTTPTL</p>", "<p>FCKSSKSM</p>"],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458938.png\" alt=\"rId16\" width=\"203\" height=\"122\"> ,<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459032.png\" alt=\"rId17\" width=\"262\" height=\"116\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458938.png\" alt=\"rId16\" width=\"203\" height=\"122\"> ,<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459032.png\" alt=\"rId17\" width=\"262\" height=\"116\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the option that is related to the third term in the same way as the second term is related to the first term.<br>Radio : Sound :: Television : ?</p>",
                    question_hi: "<p>18. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br>रेडियो : ध्वनि :: टेलीविजन : ?</p>",
                    options_en: ["<p>Images</p>", "<p>Channels</p>", 
                                "<p>Colour</p>", "<p>Serials</p>"],
                    options_hi: ["<p>बिम्ब</p>", "<p>चैनल</p>",
                                "<p>रंग</p>", "<p>सीरियल</p>"],
                    solution_en: "<p>18.(a) Radio&rsquo;s main function related to sound and television\'s main function related to <strong>images.</strong></p>",
                    solution_hi: "<p>18.(a)<br>रेडियो का मुख्य कार्य ध्वनि से संबंधित है और टेलीविजन का मुख्य कार्य <strong id=\"docs-internal-guid-3275c7d1-7fff-3d0c-98ba-8c03e0e2a03a\">बिम्ब </strong>से संबंधित है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the Venn diagram that best represents the relationship between the following classes.<br>Lawyers, Human Beings, Married People</p>",
                    question_hi: "<p>19. वेन आरेख का चयन कीजिये जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है।<br>वकील, मनुष्य, शादीशुदा लोग</p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458495.png\" alt=\"rId12\" width=\"145\" height=\"87\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459166.png\" alt=\"rId18\" width=\"94\" height=\"91\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459260.png\" alt=\"rId19\" width=\"83\" height=\"84\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459367.png\" alt=\"rId20\" width=\"218\" height=\"84\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472458495.png\" alt=\"rId12\" width=\"154\" height=\"92\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459166.png\" alt=\"rId18\" width=\"95\" height=\"92\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459260.png\" alt=\"rId19\" width=\"90\" height=\"91\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459367.png\" alt=\"rId20\" width=\"224\" height=\"86\"></p>"],
                    solution_en: "<p>19.(b)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459455.png\" alt=\"rId21\" width=\"266\" height=\"145\"></p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459658.png\" alt=\"rId22\" width=\"266\" height=\"145\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Study the given pattern carefully and select the number that can replace the question mark(?) in it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459781.png\" alt=\"rId23\" width=\"153\" height=\"113\"></p>",
                    question_hi: "<p>20. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और उस संख्या का चयन करें जो उसमें प्रश्न चिह्न (?) को प्रतिस्थापित कर सके।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459781.png\" alt=\"rId23\" width=\"153\" height=\"113\"></p>",
                    options_en: ["<p>12</p>", "<p>114</p>", 
                                "<p>9</p>", "<p>8</p>"],
                    options_hi: ["<p>12</p>", "<p>114</p>",
                                "<p>9</p>", "<p>8</p>"],
                    solution_en: "<p>20.(c)<br><strong>Logic</strong> : in each row the middle term is the square of the sum of the digits of the product of the other two.<br>So, 3 &times; 4 = 12, 1 + 2 = 3 and 3<sup>2</sup> = 9</p>",
                    solution_hi: "<p>20.(c)<br><strong>तर्क</strong> : प्रत्येक पंक्ति में मध्य पद अन्य दो के गुणनफल के अंकों के योग का वर्ग होता है।<br>तो, 3 &times; 4 = 12, 1 + 2 = 3 और 3<sup>2</sup> = 9</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Identify the day from the options that belongs to the following class.<br>15-08, 02-10, 26-01</p>",
                    question_hi: "<p>21. निम्नलिखित वर्ग से संबंधित विकल्पों में से दिन की पहचान कीजिये ।<br>15 - 08, 02 - 10, 26 - 01</p>",
                    options_en: ["<p>Wednesday</p>", "<p>Tuesday</p>", 
                                "<p>Monday</p>", "<p>Sunday</p>"],
                    options_hi: ["<p>बुधवार</p>", "<p>मंगलवार</p>",
                                "<p>सोमवार</p>", "<p>रविवार</p>"],
                    solution_en: "<p>21.(d)<br>As all the three dates mentioned are holidays, similarly Sunday is also a holiday.</p>",
                    solution_hi: "<p>21.(d)<br>जिस प्रकार उल्लिखित तीनों तिथियां अवकाश हैं, उसी प्रकार रविवार को भी अवकाश है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the number from among the given options that will come next in the following series.<br>17, 36, 76,158, ____.</p>",
                    question_hi: "<p>22. दिए गए विकल्पों में से उस संख्या का चयन कीजिये जो निम्नलिखित श्रृंखला में आगे आएगी।<br>17, 36, 76,158, ____.</p>",
                    options_en: ["<p>324</p>", "<p>344</p>", 
                                "<p>316</p>", "<p>350</p>"],
                    options_hi: ["<p>324</p>", "<p>344</p>",
                                "<p>316</p>", "<p>350</p>"],
                    solution_en: "<p>22.(a) <br><strong>Logic</strong> :<br>17 &times; 2 + 2 = 36<br>36 &times; 2 + 4 = 76<br>76 &times; 2 + 6 = 158<br>158 &times; 2 + 8 = <strong>324</strong></p>",
                    solution_hi: "<p>22.(a)<br><strong>तर्क :</strong><br>17 &times; 2 + 2 = 36<br>36 &times; 2 + 4 = 76<br>76 &times; 2 + 6 = 158<br>158 &times; 2 + 8 = <strong>324</strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Consider the given statements and decide which of the assumptions from the options is <strong>NOT</strong> implicit in the statements .<br>Some trucks are bikes. <br>Some tires are not bikes but trucks.</p>",
                    question_hi: "<p>23. दिए गए कथनों पर विचार कीजिये और बताइये कि विकल्पों में से कौन सी अवधारणा कथनों में निहित <strong>नहीं</strong> है<br>कुछ ट्रक बाइक हैं<br>कुछ टायर बाइक नहीं बल्कि ट्रक हैं</p>",
                    options_en: ["<p>Some tires are not trucks.</p>", "<p>Some bikes are tires.</p>", 
                                "<p>Some bikes are trucks.</p>", "<p>Some trucks are tyres</p>"],
                    options_hi: ["<p>कुछ टायर ट्रक नहीं हैं।</p>", "<p>कुछ बाइक टायर हैं।</p>",
                                "<p>कुछ बाइक ट्रक हैं</p>", "<p>कुछ ट्रक टायर हैं</p>"],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459871.png\" alt=\"rId24\" width=\"171\" height=\"110\"><br>Some bikes are tyres means some tyres are not bikes.</p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472459978.png\" alt=\"rId25\" width=\"149\" height=\"96\"><br>कुछ बाइक टायर हैं अर्थात कुछ टायर बाइक नहीं हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. How many triangles does this figure have?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472460076.png\" alt=\"rId26\" width=\"138\" height=\"124\"></p>",
                    question_hi: "<p>24. इस आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472460076.png\" alt=\"rId26\" width=\"138\" height=\"124\"></p>",
                    options_en: ["<p>11</p>", "<p>13</p>", 
                                "<p>15</p>", "<p>9</p>"],
                    options_hi: ["<p>11</p>", "<p>13</p>",
                                "<p>15</p>", "<p>9</p>"],
                    solution_en: "<p>24.(b)<br>Total number of triangle = 13</p>",
                    solution_hi: "<p>24.(b)<br>त्रिभुज की कुल संख्या = 13</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p>25. Select the option that is related to the third term in the same way as the second term is related to the first term.<br>Navy : Ship :: Army : ?</p>",
                    question_hi: "<p>25. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br>नौसेना : समुंद्री जहाज :: सेना : ?</p>",
                    options_en: ["<p>Guns</p>", "<p>Tank</p>", 
                                "<p>Soldier</p>", "<p>War</p>"],
                    options_hi: ["<p>बंदूके</p>", "<p>टैंक</p>",
                                "<p>सैनिक</p>", "<p>युद्ध</p>"],
                    solution_en: "<p>25.(b)<br>The Navy uses ships as a vehicle and the Army uses <strong>tanks</strong> as vehicles.</p>",
                    solution_hi: "<p>25.(b)<br>नौसेना जहाजों का उपयोग वाहन के रूप में करती है और सेना वाहनों के रूप में <strong>टैंकों</strong> का उपयोग करती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "6",
                    question_en: "<p>26. Select the number from the given options that can come next in the following series.<br>1, 12, 124, 1248, 12496, _______.</p>",
                    question_hi: "<p>26. दिए गए विकल्पों में से उस संख्या का चयन कीजिये जो निम्नलिखित श्रृंखला में आगे आ सकती है<br>1, 12, 124, 1248, 12496, _______.</p>",
                    options_en: ["<p>124982</p>", "<p>124962</p>", 
                                "<p>124972</p>", "<p>124978</p>"],
                    options_hi: ["<p>124982</p>", "<p>124962</p>",
                                "<p>124972</p>", "<p>124978</p>"],
                    solution_en: "<p>26.(c)<br><strong>Logic<math display=\"inline\"><mo>&#8594;</mo></math></strong>(previous number is same written +unit digit multiply with 2)<br>1212496 + 6 &times; 2 = 124972<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472460171.png\" alt=\"rId27\" width=\"495\" height=\"95\"></p>",
                    solution_hi: "<p>26.(c)<br><strong>तर्क</strong><math display=\"inline\"><mo>&#8594;</mo></math>(पिछली संख्या वही लिखित इकाई अंक है जिसे 2 से गुणा किया जाता है)<br>1212496 + 6 &times; 2 = 124972<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472460329.png\" alt=\"rId28\" width=\"506\" height=\"104\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "misc",
                    question_en: "<p>27. Study the given pattern carefully and select the number from the given options that can replace the question mark(?).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472460446.png\" alt=\"rId29\" width=\"103\" height=\"103\"></p>",
                    question_hi: "<p>27. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और दिए गए विकल्पों में से उस संख्या का चयन करें जो प्रश्न चिह्न (?) के स्थान पर आएगी ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728472460446.png\" alt=\"rId29\" width=\"103\" height=\"103\"></p>",
                    options_en: ["<p>22</p>", "<p>64</p>", 
                                "<p>32</p>", "<p>48</p>"],
                    options_hi: ["<p>22</p>", "<p>64</p>",
                                "<p>32</p>", "<p>48</p>"],
                    solution_en: "<p>27.(b)<br>2&sup3; = 8<br>1&sup3; = 1<br>3&sup3; = 27<br>So that 4&sup3; = <strong>64</strong> </p>",
                    solution_hi: "<p>27.(b)<br>2&sup3; = 8<br>1&sup3; = 1<br>3&sup3; = 27<br>इसलिए 4&sup3; = <strong>64</strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>