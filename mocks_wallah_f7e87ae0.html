<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. ___________ built the Taj Mahal Palace Hotel at Mumbai by integrating Indian and European styles.</p>",
                    question_hi: "<p>1.___________ ने भारतीय और यूरोपीय शैलियों को एकीकृत करके मुंबई में ताजमहल पैलेस होटल का निर्माण किया।</p>",
                    options_en: ["<p>Ratan Tata</p>", "<p>Jamsetji Tata</p>", 
                                "<p>RD Tata</p>", "<p>JRD Tata</p>"],
                    options_hi: ["<p>रतन टाटा</p>", "<p>जमशेदजी टाटा</p>",
                                "<p>आर.डी. टाटा</p>", "<p>जे.आर.डी. टाटा</p>"],
                    solution_en: "<p>1.(b) <strong>Jamsetji Tata. </strong>regarded as \"Father of Indian Industry\" was an Indian pioneer industrialist, founder of Tata Group with a textile mill in Central India in the 1870s. <strong>Taj Mahal Palace Hotel,</strong> Mumbai designed by Indian architects <strong>Raosaheb vaidya and DN Mirza. </strong>Its foundation stone was laid in 1898 and opened on 16 December 1903. First structure in India registered under <strong>Image trademark.</strong> It is built in Indo - saracenic style. Taj Hotel was epicenter of Mumbai <strong>Terror attack of 26/11.</strong></p>",
                    solution_hi: "<p>1.(b) <strong>जमशेदजी टाटा। </strong>\"भारतीय उद्योग के जनक\" माने जाने वाले एक भारतीय मार्ग-निर्माता उद्योगपति थे, जो 1870 के दशक में मध्य भारत में एक कपड़ा मिल के साथ टाटा समूह के संस्थापक थे। मुंबई मे स्थित <strong>ताज महल पैलेस होटल </strong>का डिजाइन भारतीय आर्किटेक्ट रावसाहेब <strong>वैद्य और डीएन मिर्जा </strong>द्वारा किया गया था। इसकी आधारशिला 1898 में रखी गई थी और 16 दिसंबर 1903 को उदघाटन किया गया था। <strong>इमेज ट्रेडमार्क </strong>के तहत पंजीकृत भारत में प्रथम संरचना है। इसे इंडो-सारासेनिक शैली में बनाया गया है। ताज होटल <strong>26/11 के</strong> मुंबई <strong>आतंकवादी हमले</strong> का केंद्र था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Who among the following is known as the &lsquo;Father of Indian Cinema&rsquo;?</p>",
                    question_hi: "<p>2. निम्नलिखित में से किसे \'भारतीय सिनेमा के जनक\' के रूप में जाना जाता है?</p>",
                    options_en: ["<p>Satyajit Ray</p>", "<p>Prithviraj Kapoor</p>", 
                                "<p>Dadasaheb Phalke</p>", "<p>Raj Kapoor</p>"],
                    options_hi: ["<p>सत्यजीत रे</p>", "<p>पृथ्वीराज कपूर</p>",
                                "<p>दादासाहेब फाल्के</p>", "<p>राज कपूर</p>"],
                    solution_en: "<p>2.(c) <strong>Dadasaheb Phalke -</strong> He had made India\'s first full-length feature film<strong> Raja Harishchandra</strong> (1913). Films by Dadasaheb Phalke - Kaliya Mardan, Lanka Dahan, Shree Pundalik. <strong>Dadasaheb Phalke Award</strong> established in 1969 - <strong>Devika Rani </strong>was the first recipient. Satyajit Ray Films - Pather Panchali, Jalsaghar, Charulata.<strong> Prithviraj Kapoor Films - </strong>Alam Ara (first Indian sound film made in 1931 directed by Ardeshir Irani), Mughal-E-Azam, Kal Aaj Aur Kal. <strong>Raj Kapoor Films -</strong>Aag, Barsaat Awaara.</p>",
                    solution_hi: "<p>2.(c) <strong>दादा साहब फाल्के -</strong> उन्होंने भारत की पहली पूर्ण-अवधि वाली फीचर फिल्म <strong>राजा हरिश्चंद्र</strong> (1913) बनाई थी। दादा साहब फाल्के की फ़िल्में - कालिया मर्दन, लंका दहन, श्री पुंडलिक। <strong>दादा साहब फाल्के पुरस्कार </strong>की स्थापना 1969 में हुई - <strong>देविका रानी </strong>पहली प्राप्तकर्ता थीं। सत्यजीत रे फ़िल्में - पाथेर पांचाली, जलसाघर, चारुलता। <strong>पृथ्वीराज कपूर की फ़िल्में -</strong> आलम आरा (अर्देशिर ईरानी द्वारा निर्देशित 1931 में बनी पहली भारतीय बोलती फ़िल्म), मुग़ल-ए-आज़म, कल आज और कल। <strong>राज कपूर की फ़िल्में -</strong>आग, बरसात आवारा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Who is called the \'Milkman of India&lsquo;?</p>",
                    question_hi: "<p>3. \' मिल्कमैन ऑफ इंडिया&rsquo; किसे कहा जाता है?</p>",
                    options_en: ["<p>Arjun Dev</p>", "<p>Verghese Kurien</p>", 
                                "<p>Charan Singh</p>", "<p>Manoj Kumar</p>"],
                    options_hi: ["<p>अर्जुन देव</p>", "<p>वर्गीज कुरियन</p>",
                                "<p>चरण सिंह</p>", "<p>मनोज कुमार</p>"],
                    solution_en: "<p>3.(b)<strong> Verghese Kurien </strong>(The Father of the White Revolution). He is famous for his <strong>&lsquo;Operation Flood&rsquo;. Awards -</strong> Ramon Magsaysay Award (1963), Krishi Ratna (1986), World Food Prize (1989), Padma Shri (1965), Padma Bhushan (1966), and Padma Vibhushan (1999).<strong> Norman Borlaug</strong> (father of the Green Revolution in the world).<strong> M S Swaminathan</strong> (Father of the Green Revolution in India). Green revolution leads to the increase in production of food grains (especially wheat and rice).</p>",
                    solution_hi: "<p>3.(b) <strong>वर्गीज कुरियन</strong> (श्वेत क्रांति के जनक)। ये \'<strong>ऑपरेशन फ्लड</strong>\' के लिए प्रसिद्ध हैं। <strong>पुरस्कार </strong>- रेमन मैग्सेसे पुरस्कार (1963), कृषि रत्न (1986), विश्व खाद्य पुरस्कार (1989), पद्म श्री (1965), पद्म भूषण (1966), और पद्म विभूषण (1999)। <strong>नॉर्मन बोरलॉग </strong>(विश्व में हरित क्रांति के जनक)।<strong> एम एस स्वामीनाथन</strong> (भारत में हरित क्रांति के जनक)। हरित क्रांति से खाद्यान्न (विशेषकर गेहूं और चावल) के उत्पादन में वृद्धि हुई।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Who stayed in India for 23 years (1802 - 25) painting portraits, landscapes and scenes of everyday life of common people?</p>",
                    question_hi: "<p>4. आम लोगों के दैनिक जीवन के चित्रों, परिदृश्यों और दृश्यों को चित्रित करते हुए 23 वर्षों (1802-25) तक भारत में कौन रहा?</p>",
                    options_en: ["<p>Walter Statesman</p>", "<p>George Chinnery</p>", 
                                "<p>William Hodges</p>", "<p>Walter Shergill</p>"],
                    options_hi: ["<p>वाल्टर स्टेट्समैन</p>", "<p>जॉर्ज चिन्नरी</p>",
                                "<p>विलियम होजेस</p>", "<p>वाल्टर शेरगिल</p>"],
                    solution_en: "<p>4.(b) <strong>George Chinnery. Other famous work By George Chinnery -</strong> The Hong merchant Howqua, Portrait of Thomas Beale, Watercolour on ivory of Catherine Sherson, Portrait of Gilbert Eliot. <strong>William Hodges famous painting -</strong> A View in Dusky Bay, New Zealand, The Marmalong Bridge, The Fort of Bidjegur, Landscape with Fishermen on a Lake, The Taj Mahal, Ghat on the Ganges at Varanasi, Uttar Pradesh.</p>",
                    solution_hi: "<p>4.(b)<strong> जॉर्ज चिन्नरी। जॉर्ज चिन्नरी की अन्य प्रसिद्ध कृतियाँ - </strong>द होंग मर्चेंट हाउक्वा, पोर्ट्रेट ऑफ़ थॉमस बीले, वाटरकलर ऑन ईवोरी ऑफ़ कैथरीन शेरसन, पोर्ट्रेट ऑफ़ गिल्बर्ट एलियट। <strong>विलियम होजेस की प्रसिद्ध पेंटिंग - </strong>ए व्यू इन डस्की बे, न्यूजीलैंड, द मार्मलॉन्ग ब्रिज, द फोर्ट ऑफ बिडजेगुर, लैंडस्केप विथ फिशरमैन ऑन अ लेक, द ताज महल, घाट ऑन द गंगेज़ एट वाराणसी, उत्तर प्रदेश।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which architect has designed the India Gate in New Delhi?</p>",
                    question_hi: "<p>5. नई दिल्ली में इंडिया गेट को किस वास्तुकार ने डिजाइन किया है?</p>",
                    options_en: ["<p>Albert Speer</p>", "<p>Edwin Lutyens</p>", 
                                "<p>Le Courbusier</p>", "<p>Laurie Baker</p>"],
                    options_hi: ["<p>अल्बर्ट स्पीयर</p>", "<p>एडविन लुटियंस</p>",
                                "<p>ली कार्जूबीयर</p>", "<p>लॉरी बेकर</p>"],
                    solution_en: "<p>5.(b)<strong> Edwin Lutyens - </strong>An English architect, known especially for his planning of New Delhi and his design of the Viceroy\'s House (now, Rashtrapati Bhawan) there. <strong>Albert Speer - </strong>A German architect who served as the Minister of Armaments and War Production in Nazi Germany during most of World War II (1939-45). <strong>Le Corbusier </strong>(Swiss-French architect) - Planned the dream city (Chandigarh) of India\'s first Prime Minister, Jawahar Lal Nehru. <strong>Laurie Baker - </strong>A British-born Indian architect widely recognised as one of the pioneers of sustainable and organic architecture.</p>",
                    solution_hi: "<p>5.(b) <strong>एडविन लुटियंस ।</strong> एक अंग्रेज वास्तुकार, जो विशेष रूप से नई दिल्ली की योजना बनाने और वहां के वायसराय हाउस (अब, राष्ट्रपति भवन) के डिजाइन के लिए जाने जाते हैं। <strong>अल्बर्ट स्पीयर - </strong>एक जर्मन वास्तुकार जिन्होंने द्वितीय विश्व युद्ध (1939-45) के दौरान नाजी जर्मनी में आयुध और युद्ध उत्पादन मंत्री के रूप में कार्य किया। <strong>ले कोर्बुज़िए</strong> (स्विस-फ़्रेंच वास्तुकार) - भारत के प्रथम प्रधान मंत्री जवाहर लाल नेहरू के सपनों के शहर (चंडीगढ़) की योजना बनाई। <strong>लॉरी बेकर -</strong> एक ब्रिटिश मूल के भारतीय वास्तुकार को व्यापक रूप से टिकाऊ और जैविक वास्तुकला के अग्रदूतों में से एक के रूप में मान्यता प्राप्त है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. What was the real name of the Hindi Literary writer Munshi Premchand?</p>",
                    question_hi: "<p>6. हिन्दी साहित्यकार मुंशी प्रेमचंद का वास्तविक नाम क्या था?</p>",
                    options_en: ["<p>Nawab Rai</p>", "<p>Atmaram</p>", 
                                "<p>Sachchidanand</p>", "<p>Dhanpat Rai</p>"],
                    options_hi: ["<p>नवाब राय</p>", "<p>आत्माराम</p>",
                                "<p>सच्चिदानंद</p>", "<p>धनपत राय</p>"],
                    solution_en: "<p>6.(d) <strong>Dhanpat Rai Srivastava.</strong> He was born on 31st July 1880 in Lamhi, Varanasi. <strong>Famous works by Munshi Premchand -</strong> Karmabhoomi, Kafan, Godan, Poos Ki Raat, Nirmala, Thakur Ka Kuan, Eidgah, Namak Ka Daroga.</p>",
                    solution_hi: "<p>6.(d) <strong>धनपत राय श्रीवास्तव। </strong>इनका जन्म 31 जुलाई 1880 को लमही, वाराणसी में हुआ था। <strong>मुंशी प्रेमचंद की प्रसिद्ध रचनाएँ - </strong>कर्मभूमि, कफ़न, गोदान, पूस की रात, निर्मला, ठाकुर का कुआँ, ईदगाह, नमक का दरोगा।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Who is known as the &lsquo;Grand Old Man of India&rsquo;?</p>",
                    question_hi: "<p>7. भारत के \'ग्रैंड ओल्ड मैन\' के रूप में किसे जाना जाता है?</p>",
                    options_en: ["<p>Bal Gangadhar Tilak</p>", "<p>Mahatma Gandhi</p>", 
                                "<p>Lala Lajpat Rai</p>", "<p>Dadabhai Naoroji</p>"],
                    options_hi: ["<p>बाल गंगाधर तिलक</p>", "<p>महात्मा गांधी</p>",
                                "<p>लाला लाजपत राय</p>", "<p>दादाभाई नौरोजी</p>"],
                    solution_en: "<p>7.(d) <strong>Dadabhai Naoroji - </strong>An Indian nationalist and critic of British economic policy in India. He had become the first Asian to be elected to the British parliament (July 1892). <strong>His books: </strong>&ldquo;The Drain of Wealth theory&rdquo; (1867), &ldquo;Poverty and Un-British rule in India&rdquo;.<strong> Bal Gangadhar Tilak -</strong> The British colonial authorities called him \"<strong>The father of the Indian unrest\". Mahatma Gandhi - </strong>India\'s Father of the Nation. <strong>Lala Lajpat Rai - </strong>Popularly known as Punjab Kesari, and also as Punjab da Sher.</p>",
                    solution_hi: "<p>7.(d) <strong>दादाभाई नौरोजी - </strong>एक भारतीय राष्ट्रवादी और भारत में ब्रिटिश आर्थिक नीति के आलोचक है। वह ब्रिटिश संसद (जुलाई 1892) के लिए चुने जाने वाले पहले एशियाई बने थे। <strong>उनकी पुस्तकें: </strong>&ldquo;द ड्रेन ऑफ वेल्थ थ्योरी&rdquo; (1867), &ldquo;पॉवर्टी एंड अन-ब्रिटिश रूल इन इंडिया&rdquo;। <strong>बाल गंगाधर तिलक - </strong>ब्रिटिश औपनिवेशिक अधिकारियों ने उन्हें \"भारतीय अशांति का जनक\" कहा। <strong>महात्मा गांधी -</strong> भारत के राष्ट्रपिता।<strong> लाला लाजपत राय - </strong>पंजाब केसरी और पंजाब दा शेर के नाम से भी प्रसिद्ध हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Amir Khusrau was born at _______ in Uttar Pradesh.</p>",
                    question_hi: "<p>8. अमीर खुसरो का जन्म उत्तर प्रदेश में _______ में हुआ था।</p>",
                    options_en: ["<p>Patiyali</p>", "<p>Agra</p>", 
                                "<p>Ghaziabad</p>", "<p>Kanpur</p>"],
                    options_hi: ["<p>पटियाली</p>", "<p>आगरा</p>",
                                "<p>गाजियाबाद</p>", "<p>कानपुर</p>"],
                    solution_en: "<p>8.(a) <strong>Patiyali.</strong> Amir Khusrau (Parrot of India) served as courtier, royal poet and court musician under Turkish Sultans. He is called the father of &lsquo;Qawwali&rsquo;. He introduced the ghazal style of a song into India. He was a spiritual follower of Delhi\'s Nizamuddin Auliya.</p>",
                    solution_hi: "<p>8.(a) <strong>पटियाली। </strong>अमीर खुसरो (तूती-ए-हिंद) ने तुर्की सुल्तानों के अधीन दरबारी, शाही कवि और दरबारी संगीतकार के रूप में कार्य किया। इन्हें \'कव्वाली\' का जनक कहा जाता है। इन्होंने भारत में गीत की ग़ज़ल शैली की शुरुआत की। ये दिल्ली के निज़ामुद्दीन औलिया के आध्यात्मिक अनुयायी थे।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Who was known as the &lsquo;Iron Man of India&rsquo;?</p>",
                    question_hi: "<p>9. \'भारत के लौह पुरुष\' के रूप में किसे जाना जाता था?</p>",
                    options_en: ["<p>Sardar Vallabhbhai Patel</p>", "<p>Jawaharlal Nehru</p>", 
                                "<p>Subhash Chandra Bose</p>", "<p>Narendra Modi</p>"],
                    options_hi: ["<p>सरदार वल्लभ भाई पटेल</p>", "<p>जवाहरलाल नेहरू</p>",
                                "<p>सुभाष चंद्र बोस</p>", "<p>नरेंद्र मोदी</p>"],
                    solution_en: "<p>9.(a)<strong> Sardar Vallabhbhai Patel </strong>for playing an important role in unification and integration of Indian princely states into the Indian federation. Born - 31st October 1875 in Nadiad (Gujarat). He was the First Home Minister and Deputy Prime Minister of India. Women of Bardoli bestowed the title &lsquo;Sardar&rsquo; on Vallabhbhai Patel. He was the Patron Saint of India&rsquo;s Civil Servants. National Unity Day (31 October) is celebrated to recognize the endeavors of him to unite the country.</p>",
                    solution_hi: "<p>9.(a) <strong>सरदार वल्लभभाई पटेल</strong> को भारतीय रियासतों के एकीकरण और भारतीय संघ में एकीकरण में महत्वपूर्ण भूमिका निभाने के लिए सम्मानित किया गया। जन्म - 31 अक्टूबर 1875 को नडियाद (गुजरात) में। वह भारत के पहले गृह मंत्री और उप प्रधान मंत्री थे। बारदोली की महिलाओं ने वल्लभभाई पटेल को \'सरदार\' की उपाधि दी। वह भारत के सिविल सेवकों के संरक्षक संत थे। देश को एकजुट करने के उनके प्रयासों को मान्यता देने के लिए राष्ट्रीय एकता दिवस (31 अक्टूबर) मनाया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following Indian pairs has won the Miss Universe title?</p>",
                    question_hi: "<p>10. निम्नलिखित में से किस भारतीय जोड़ी ने मिस यूनिवर्स का खिताब जीता है?</p>",
                    options_en: ["<p>Priyanka Chopra and Manushi Chhillar</p>", "<p>Sushmita Sen and Lara Dutta</p>", 
                                "<p>Reita Faria and Aishwarya Rai</p>", "<p>Lara Dutta and Diana Hayden</p>"],
                    options_hi: ["<p>प्रियंका चोपड़ा और मानुषी छिल्लर</p>", "<p>सुष्मिता सेन और लारा दत्ता</p>",
                                "<p>रीता फारिया और ऐश्वर्या राय</p>", "<p>लारा दत्ता और डायना हेडन</p>"],
                    solution_en: "<p>10.(b) <strong>Sushmita Sen and Lara Dutta. Miss Universe</strong> is an annual international beauty pageant that is run by a United States and Thailand based Miss Universe Organization. <strong>List of Miss World from India are - </strong>Manushi Chhillar (2017), Priyanka Chopra (2000), Yukta Mookhey (1999), Diana Hayden (1997), Aishwarya Rai (1994), Reita Faria (1966). <strong>List of Miss Universe from India - </strong>Susmita Sen (1994) Lara Dutta (2000) and Harnaaz Sandhu (2021).</p>",
                    solution_hi: "<p>10.(b) <strong>सुष्मिता सेन और लारा दत्ता। मिस यूनिवर्स</strong> एक वार्षिक अंतर्राष्ट्रीय सौंदर्य प्रतियोगिता है जो संयुक्त राज्य अमेरिका और थाईलैंड स्थित मिस यूनिवर्स संगठन द्वारा संचालित की जाती है। <strong>भारत से मिस वर्ल्ड की सूची इस प्रकार है -</strong> मानुषी छिल्लर (2017), प्रियंका चोपड़ा (2000), युक्ता मुखी (1999), डायना हेडन (1997), ऐश्वर्या राय (1994), रीता फारिया (1966)। <strong>भारत से मिस यूनिवर्स की सूची -</strong> सुष्मिता सेन (1994) लारा दत्ता (2000) और हरनाज़ संधू (2021)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Whose famous quote is this? &ldquo;If you cry because the sun has gone out of your life, your tears will prevent you from seeing the stars&rdquo;</p>",
                    question_hi: "<p>11. यह किसका प्रसिद्ध कथन है? &ldquo;यदि आप इसलिए रोते हैं कि सूर्य आपके जीवन से बाहर चला गया है, तो आपके आंसू आसमान के सितारों को देखने से रोक देंगे&rdquo;</p>",
                    options_en: ["<p>Rabindranath Tagore</p>", "<p>Amrita Pritam</p>", 
                                "<p>Sumitranandan Pant</p>", "<p>Sri Aurobindo</p>"],
                    options_hi: ["<p>रविंद्रनाथ टैगोर</p>", "<p>अमृता प्रीतम</p>",
                                "<p>सुमित्रा नंदन पंत</p>", "<p>श्री अरबिंदो</p>"],
                    solution_en: "<p>11.(a)<strong> Rabindranath Tagore</strong> was a poet who composed the National Anthem of India Bangladesh and won the Nobel Prize for Literature in 1913. <strong>Famous quotes -</strong> &ldquo;You cannot cross the sea merely by standing and staring at the water&rdquo;, &ldquo;It is very simple to be happy, but it is very difficult to be simple.&rdquo;</p>",
                    solution_hi: "<p>11.(a)<strong> रबींद्रनाथ टैगोर </strong>एक कवि थे जिन्होंने भारत बांग्लादेश के राष्ट्रगान की रचना की और 1913 में साहित्य के लिए नोबेल पुरस्कार जीता। <strong>प्रसिद्ध उद्धरण - </strong>\"केवल खड़े होकर और पानी को घूरते रहने से आप समुद्र पार नहीं कर सकते\", \"खुश रहना बहुत सरल है\" , लेकिन सरल होना बहुत कठिन है&rdquo;|</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. At which city&rsquo;s airport can one find the statue of Lal Bahadur Shastri?</p>",
                    question_hi: "<p>12. किस शहर के हवाई अड्डे पर लाल बहादुर शास्त्री की प्रतिमा स्थापित है?</p>",
                    options_en: ["<p>Chennai</p>", "<p>Hyderabad</p>", 
                                "<p>Shimla</p>", "<p>Varanasi</p>"],
                    options_hi: ["<p>चेन्नई</p>", "<p>हैदराबाद</p>",
                                "<p>शिमला</p>", "<p>वाराणसी</p>"],
                    solution_en: "<p>12.(d) <strong>Varanasi</strong>. Varanasi Airport which is also called Lal Bahadur Shastri Airport or Babatpur Airport. Chennai Airport - Madras International Meenambakkam Airport. Hyderabad Airport - Rajiv Gandhi International Airport. Delhi Airport - Indira Gandhi International Airport.</p>",
                    solution_hi: "<p>12.(d) <strong>वाराणसी। </strong>वाराणसी हवाई अड्डा जिसे लाल बहादुर शास्त्री हवाई अड्डा या बाबतपुर हवाई अड्डा भी कहा जाता है। चेन्नई हवाई अड्डा - मद्रास अंतर्राष्ट्रीय मीनंबक्कम हवाई अड्डा। हैदराबाद हवाई अड्डा - राजीव गांधी अंतर्राष्ट्रीय हवाई अड्डा। दिल्ली हवाई अड्डा - इंदिरा गांधी अंतर्राष्ट्रीय हवाई अड्डा।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Who is known as the &lsquo;Missile Man of India&rsquo;?</p>",
                    question_hi: "<p>13. \'भारत के मिसाइल मैन\' के रूप में किसे जाना जाता है?</p>",
                    options_en: ["<p>K Sivan</p>", "<p>Dr. APJ Abdul Kalam</p>", 
                                "<p>Bhisham Sahni</p>", "<p>CV Raman</p>"],
                    options_hi: ["<p>के सिवान</p>", "<p>डॉ एपीजे अब्दुल कलाम</p>",
                                "<p>भीष्म साहनी</p>", "<p>सीवी रमन</p>"],
                    solution_en: "<p>13.(b) <strong>Dr. APJ Abdul Kalam.</strong> He was born on 15th October 1931 at Rameswaram in Tamil Nadu. <strong>Awards and Honours :</strong> Padma Vibhushan (1990), Padma Bhushan (1981), Bharat Ratna (1997), Ramanujan Award (2000), Honorary Doctorate (2009). K Sivan was 9th Chairman of the Indian Space Research Organisation. Bhisham Sahni was an Indian writer, playwright in Hindi and an actor. CV Raman - was an Indian physicist known for his work in the field of light scattering (1930, Nobel Prize in Physics).</p>",
                    solution_hi: "<p>13.(b) <strong>डॉ. एपीजे अब्दुल कलाम।</strong> उनका जन्म 15 अक्टूबर 1931 को तमिलनाडु के रामेश्वरम में हुआ था। <strong>पुरस्कार और सम्मान:</strong> पद्म विभूषण (1990), पद्म भूषण (1981), भारत रत्न (1997), रामानुजन पुरस्कार (2000), मानद डॉक्टरेट (2009)। के सिवान भारतीय अंतरिक्ष अनुसंधान संगठन के 9वें अध्यक्ष थे। भीष्म साहनी एक भारतीय लेखक, हिंदी नाटककार और एक अभिनेता थे। सीवी रमन - एक भारतीय भौतिक वैज्ञानिक थे जो प्रकाश प्रकीर्णन के क्षेत्र में अपने काम के लिए जाने जाते थे (1930, भौतिकी में नोबेल पुरस्कार मिला)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which President of India is known as the Missile Man?</p>",
                    question_hi: "<p>14. भारत के किस राष्ट्रपति को मिसाइल मैन के नाम से जाना जाता है</p>",
                    options_en: ["<p>Dr. APJ Abdul Kalam</p>", "<p>Gyani Zail Singh</p>", 
                                "<p>Dr. Rajendra Prasad</p>", "<p>Zakir Husain</p>"],
                    options_hi: ["<p>डॉ. एपीजे अब्दुल कलाम</p>", "<p>ज्ञानी जैल सिंह</p>",
                                "<p>डॉ. राजेंद्र प्रसाद</p>", "<p>जाकिर हुसैन</p>"],
                    solution_en: "<p>14.(a) <strong>Dr. APJ Abdul Kalam -</strong> He played a crucial role in India\'s missile development program through Integrated Guided Missile Development Programme (IGMDP), which led to the development of missiles like: Prithvi, Trishul, Aakash, Nag and Agni series.<strong> Gyani Zail Singh -</strong> Seventh President of India (1982 - 1987). <strong>Dr. Rajendra Prasad -</strong> First President of India (1950 - 1962). <strong>Zakir Husain - </strong>Third President and first muslim president of India (1967 - 1969).</p>",
                    solution_hi: "<p>14.(a) <strong>डॉ. एपीजे अब्दुल कलाम - </strong>इन्होंने इंटीग्रेटेड गाइडेड मिसाइल डेवलपमेंट प्रोग्राम (IGMDP) के माध्यम से भारत के मिसाइल विकास कार्यक्रम में महत्वपूर्ण भूमिका निभाई, जिसके कारण पृथ्वी, त्रिशूल, आकाश, नाग और अग्नि श्रृंखला जैसी मिसाइलों का विकास हुआ।<strong> ज्ञानी जैल सिंह -</strong> भारत के सातवें राष्ट्रपति (1982 - 1987)। <strong>डॉ. राजेंद्र प्रसाद - </strong>भारत के प्रथम राष्ट्रपति (1950 - 1962)। <strong>ज़ाकिर हुसैन -</strong> भारत के तीसरे राष्ट्रपति और प्रथम मुस्लिम राष्ट्रपति (1967 - 1969)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Cuttack is associated with which eminent personalities out of the following ?</p>",
                    question_hi: "<p>15. कटक निम्नलिखित में से किस प्रख्यात व्यक्तित्व से संबंधित है?</p>",
                    options_en: ["<p>Subhash Chandra Bose</p>", "<p>Dr. Rajendra Prasad</p>", 
                                "<p>Ishwar Chandra Vidyasagar</p>", "<p>Jai Prakash Narayan</p>"],
                    options_hi: ["<p>सुभाष चंद्र बोस</p>", "<p>डॉ राजेंद्र प्रसाद</p>",
                                "<p>ईश्वर चंद्र विद्यासागर</p>", "<p>जय प्रकाश नारायण</p>"],
                    solution_en: "<p>15.(a) <strong>Subhash Chandra Bose</strong> was born in Cuttack (Odisha) on January 23, 1897. He established the Forward Bloc, a political organization, and led the Indian National Army (INA). <br>23 January - Parakram Diwas. Other personalities : <strong>Dr. Rajendra Prasad </strong>(First President of India); <strong>Ishwar Chandra Vidyasagar</strong> (strongly opposed polygamy, child marriage, widow remarriage); <strong>Jai Prakash Narayan </strong>(known as &ldquo;Loknayak&rdquo;).</p>",
                    solution_hi: "<p>15.(a) <strong>सुभाष चंद्र बोस का जन्म </strong>23 जनवरी, 1897 को कटक (ओडिशा) में हुआ था। उन्होंने एक राजनीतिक संगठन फॉरवर्ड ब्लॉक की स्थापना की और भारतीय राष्ट्रीय सेना (INA) का नेतृत्व किया।<br>23 जनवरी - पराक्रम दिवस। अन्य व्यक्तित्व: <strong>डॉ. राजेंद्र प्रसाद</strong> (भारत के प्रथम राष्ट्रपति); <strong>ईश्वर चंद्र</strong> <strong>विद्यासागर </strong>(बहुपत्नी प्रथा, बाल विवाह, विधवा पुनर्विवाह का कड़ा विरोध किया); <strong>जय प्रकाश नारायण</strong> (जिन्हें \"लोकनायक\" के नाम से जाना जाता है)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>