<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. GY 16 is related to DV 23 in a certain way. In the same way, OD 11 is related to LA 18. To which of the following is LW 13 related, following the same logic?</p>",
                    question_hi: "<p>1. किसी निश्चित तरीके से GY 16, DV 23 से संबंधित है। उसी प्रकार, OD 11, LA 18 से संबंधित है। उसी तर्क का पालन करते हुए, LW 13 निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: ["<p>IT 20</p>", "<p>IS 20</p>", 
                                "<p>HS 20</p>", "<p>HT 19</p>"],
                    options_hi: ["<p>IT 20</p>", "<p>IS 20</p>",
                                "<p>HS 20</p>", "<p>HT 19</p>"],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258418924.png\" alt=\"rId4\" width=\"200\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419096.png\" alt=\"rId5\" width=\"200\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419253.png\" alt=\"rId6\" width=\"200\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258418924.png\" alt=\"rId4\" width=\"200\">&nbsp; &nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419096.png\" alt=\"rId5\" width=\"200\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419253.png\" alt=\"rId6\" width=\"200\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain code language, &lsquo;write this sentence&rsquo; is coded as &lsquo;aw pm nl&rsquo; and &lsquo;this is mine&rsquo; is coded as &lsquo;nl wb cd&rsquo;. How is &lsquo;this&rsquo; coded in that code language?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में, &lsquo;write this sentence&rsquo; को &lsquo;aw pm nl&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;this is mine&rsquo; को &lsquo;nl wb cd&rsquo; के रूप में कूटबद्ध किया जाता है। उस कूट भाषा में &lsquo;this&rsquo; को किस प्रकार कूटबद्ध किया जाता है?</p>",
                    options_en: ["<p>aw</p>", "<p>wb</p>", 
                                "<p>nI</p>", "<p>cd</p>"],
                    options_hi: ["<p>aw</p>", "<p>wb</p>",
                                "<p>nI</p>", "<p>cd</p>"],
                    solution_en: "<p>2.(c)<br>&lsquo;write this sentence&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;aw pm nl&rsquo; &hellip;&hellip;&hellip; (i)<br>&lsquo;this is mine&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;nl wb cd&rsquo; &hellip;&hellip;&hellip;.. (ii)<br>From (i) and (ii) &lsquo;this&rsquo; and &lsquo;nl&rsquo; are common.<br>So, the code of &lsquo;this&rsquo; is &lsquo;nl&rsquo;.</p>",
                    solution_hi: "<p>2.(c)<br>&lsquo;write this sentence&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;aw pm nl&rsquo; &hellip;&hellip;&hellip; (i)<br>&lsquo;this is mine&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;nl wb cd&rsquo; &hellip;&hellip;&hellip;.. (ii)<br>(i) और (ii) से \'this\' और \'nl\' उभयनिष्ठ हैं।<br>तो, \'this\' का कोड \'nl\' है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?<br><strong>(NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into&nbsp;1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>3. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है?<br>(<strong>ध्यान दें</strong> : संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>540 &ndash; 188 &ndash; 128</p>", "<p>72 &ndash; 284 &ndash; 266</p>", 
                                "<p>81&ndash; 101&ndash; 92</p>", "<p>90 &ndash; 22&ndash; 12</p>"],
                    options_hi: ["<p>540 &ndash; 188 &ndash; 128</p>", "<p>72 &ndash; 284 &ndash; 266</p>",
                                "<p>81&ndash; 101&ndash; 92</p>", "<p>90 &ndash; 22&ndash; 12</p>"],
                    solution_en: "<p>3.(b) <strong>Logic</strong> :- (2nd number - 3rd number) &times; 9 = 1st number<br>(540 - 188 - 128) :- (188 - 128) &times; 9 &rArr; (60) &times; 9 = 540<br>(81- 101 - 92) :- (101 - 92) &times; 9 &rArr; (9) &times; 9 = 81<br>(90 - 22- 12) :- (22 - 12) &times; 9 &rArr; (10) &times; 9 = 90<br>But,<br>(72, 284, 266) :- (284 - 266) &times; 9 &rArr; (18) &times; 9 = 162(Not 72)</p>",
                    solution_hi: "<p>3.(b) <strong>तर्क</strong> :- (दूसरी संख्या - तीसरी संख्या)&times;9 = पहली संख्या<br>(540 - 188 - 128) :- (188 - 128) &times; 9 &rArr; (60) &times; 9 = 540<br>(81- 101 - 92) :- (101 - 92) &times; 9 &rArr; (9) &times; 9 = 81<br>(90 - 22- 12) :- (22 - 12) &times; 9 &rArr; (10) &times; 9 = 90<br>लेकिन,<br>(72, 284, 266) :- (284 - 266) &times; 9 &rArr; (18) &times; 9 = 162(72 नहीं)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the option that represents the letters that when placed from left to right in the blanks below will complete the letter series.<br>_ Y Z K X K X _ Z K X _ X Y _ K X K X Y Z _ X K X Y</p>",
                    question_hi: "<p>4. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्&zwj;हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर अक्षर श्रृंखला पूरी हो जाएगी।<br>_ Y Z K X K X _ Z K X _ X Y _ K X K X Y Z _ X K X Y</p>",
                    options_en: ["<p>XYZKK</p>", "<p>XYKZK</p>", 
                                "<p>KXYKZ</p>", "<p>KYXZK</p>"],
                    options_hi: ["<p>XYZKK</p>", "<p>XYKZK</p>",
                                "<p>KXYKZ</p>", "<p>KYXZK</p>"],
                    solution_en: "<p>4.(b)<br><span style=\"text-decoration: underline;\"><strong>X</strong></span> Y Z K X K/ X <span style=\"text-decoration: underline;\"><strong>Y</strong></span> Z K X <span style=\"text-decoration: underline;\"><strong>K</strong></span> /X Y <span style=\"text-decoration: underline;\"><strong>Z</strong></span> K X K/ X Y Z <span style=\"text-decoration: underline;\"><strong>K</strong></span> X K /X Y</p>",
                    solution_hi: "<p>4.(b)<br><span style=\"text-decoration: underline;\"><strong>X</strong></span> Y Z K X K/ X <span style=\"text-decoration: underline;\"><strong>Y</strong></span> Z K X <span style=\"text-decoration: underline;\"><strong>K</strong></span> /X Y <span style=\"text-decoration: underline;\"><strong>Z</strong></span> K X K/ X Y Z <span style=\"text-decoration: underline;\"><strong>K</strong></span> X K /X Y</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In a certain code language, if AMRITSAR is coded as MAIRSTRA, DURGAPUR is coded as UDGRPARU, then what will SHILLONG be coded as?</p>",
                    question_hi: "<p>5. एक निश्चित कूट भाषा में, \'AMRITSAR\' को \'MAIRSTRA\' लिखा जाता है, \'DURGAPUR\' को \'UDGRPARU\' लिखा जाता है। उसी कूट भाषा में \'SHILLONG\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>GNLIOLSH</p>", "<p>NGLOILSH</p>", 
                                "<p>HSLIOLGN</p>", "<p>HSLOILGN</p>"],
                    options_hi: ["<p>GNLIOLSH</p>", "<p>NGLOILSH</p>",
                                "<p>HSLIOLGN</p>", "<p>HSLOILGN</p>"],
                    solution_en: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419428.png\" alt=\"rId7\" width=\"207\" height=\"102\">&nbsp; &nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419560.png\" alt=\"rId8\" width=\"222\" height=\"104\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419667.png\" alt=\"rId9\" width=\"230\" height=\"105\"></p>",
                    solution_hi: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419428.png\" alt=\"rId7\" width=\"207\" height=\"102\">&nbsp; &nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419560.png\" alt=\"rId8\" width=\"222\" height=\"104\"><br>इसी तरह,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419667.png\" alt=\"rId9\" width=\"230\" height=\"105\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>Some herbs are trees.<br>Some trees are plants.<br><strong>Conclusions:</strong><br>(I) Some herbs are plants.<br>(II) At least some trees are herbs.</p>",
                    question_hi: "<p>6. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन:</strong><br>कुछ जड़ी-बूटियाँ, पेड़ हैं।<br>कुछ पेड़, पौधे हैं।<br><strong>निष्कर्ष:</strong><br>(I) कुछ जड़ी-बूटियाँ, पौधे हैं।<br>(II) कम से कम कुछ पेड़, जड़ी-बूटियाँ हैं।</p>",
                    options_en: ["<p>Both conclusion I and II follow</p>", "<p>Only conclusion II follows</p>", 
                                "<p>Neither conclusion I nor II follows</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>निष्&zwj;कर्ष I और II दोनों अनुसरण करते हैं</p>", "<p>केवल निष्&zwj;कर्ष II अनुसरण करता है</p>",
                                "<p>न तो निष्&zwj;कर्ष I और न ही II अनुसरण करता है</p>", "<p>केवल निष्&zwj;कर्ष I अनुसरण करता है</p>"],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419871.png\" alt=\"rId10\" width=\"352\" height=\"83\"><br>Only conclusion II follow.</p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258419970.png\" alt=\"rId11\" width=\"311\" height=\"74\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>BACK : CCBI :: TEST : UGRR :: WORK : ?</p>",
                    question_hi: "<p>7. उस विकल्प का चयन करें जो पांचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है।<br>BACK : CCBI :: TEST : UGRR :: WORK : ?</p>",
                    options_en: ["<p>YQQJ</p>", "<p>XQSI</p>", 
                                "<p>YQPI</p>", "<p>XQQI</p>"],
                    options_hi: ["<p>YQQJ</p>", "<p>XQSI</p>",
                                "<p>YQPI</p>", "<p>XQQI</p>"],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420108.png\" alt=\"rId12\" width=\"200\">&nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420243.png\" alt=\"rId13\" width=\"200\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420425.png\" alt=\"rId14\" width=\"200\"></p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420108.png\" alt=\"rId12\" width=\"200\">&nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420243.png\" alt=\"rId13\" width=\"200\"><br>इसी प्रकार, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420425.png\" alt=\"rId14\" width=\"200\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Arrange the given words in alphabetical order:<br>1. MISSION<br>2. MISSED<br>3. MISSING<br>4. MIST<br>5. MIGHT</p>",
                    question_hi: "<p>8. दिए गए शब्दों को अंग्रेज़ी वर्णमाला के क्रम में व्यवस्थित कीजिए।<br>1. MISSION<br>2. MISSED<br>3. MISSING<br>4. MIST<br>5. MIGHT</p>",
                    options_en: ["<p>5, 1, 3, 4, 2</p>", "<p>5, 4, 3, 2, 1</p>", 
                                "<p>5, 4, 3, 1, 2</p>", "<p>5, 2, 3, 1, 4</p>"],
                    options_hi: ["<p>5, 1, 3, 4, 2</p>", "<p>5, 4, 3, 2, 1</p>",
                                "<p>5, 4, 3, 1, 2</p>", "<p>5, 2, 3, 1, 4</p>"],
                    solution_en: "<p>8.(d)<br>The correct order is : <br>MIGHT(5) &rarr; MISSED(2) &rarr; MISSING(3) &rarr; MISSION(1) &rarr; MIST(4)</p>",
                    solution_hi: "<p>8.(d)<br>सही क्रम है: <br>MIGHT(5) &rarr; MISSED(2) &rarr; MISSING(3) &rarr; MISSION(1) &rarr; MIST(4)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the correct mirror image of the given figure when the mirror MN is placed to the right side of the figure<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420529.png\" alt=\"rId15\" width=\"130\" height=\"130\"></p>",
                    question_hi: "<p>9. दी गई आकृति के उस सही दर्पण प्रतिबिंब का चयन कीजिए, जो दर्पण MN को उस आकृति के दाईं ओर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420529.png\" alt=\"rId15\" width=\"130\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420628.png\" alt=\"rId16\" width=\"90\" height=\"93\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420726.png\" alt=\"rId17\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420820.png\" alt=\"rId18\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420940.png\" alt=\"rId19\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420628.png\" alt=\"rId16\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420726.png\" alt=\"rId17\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420820.png\" alt=\"rId18\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420940.png\" alt=\"rId19\" width=\"90\"></p>"],
                    solution_en: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420820.png\" alt=\"rId18\" width=\"90\"></p>",
                    solution_hi: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258420820.png\" alt=\"rId18\" width=\"90\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Which two signs should be interchanged to make the given equation correct?<br>10 - 3 + 2 &times; 12 &divide;&nbsp;6 = 6</p>",
                    question_hi: "<p>10. दिए गए विकल्पों में से किन दो चिह्नों को आपस में बदलने पर निम्नलिखित समीकरण सही होगा?<br>10 - 3 + 2 &times; 12 &divide; 6 = 6</p>",
                    options_en: ["<p>- and =</p>", "<p>&times; and +</p>", 
                                "<p>= and +</p>", "<p>+ and -</p>"],
                    options_hi: ["<p>- और =</p>", "<p>&times; और +</p>",
                                "<p>= और +</p>", "<p>+ और -</p>"],
                    solution_en: "<p>10.(b)<br><strong>Given </strong>: 10 - 3 + 2 &times; 12 &divide; 6 = 6<br>After going through all the options and interchanging &times; and+ we get,<br>10 - 3 &times; 2 + 12 &divide;&nbsp;6 <br>10 - 6 + 2 = 6<br>So, option(b) satisfies our answer</p>",
                    solution_hi: "<p>10.(b)<br><strong>दिया गया है :</strong> 10 - 3 + 2 &times; 12 &divide; 6 = 6<br>सभी विकल्पों की जांच करने के बाद, &lsquo;&times;&rsquo; और &rsquo;+&rsquo; को आपस में बदलने पर हमें प्राप्त होता है,<br>10 - 3 &times; 2 + 12 &divide; 6 <br>10 - 6 + 2 = 6<br>इसलिए, विकल्प (b) हमारे उत्तर को संतुष्ट करता है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "11. Select the option that represents the letters that, when placed from left to right in the blanks below, will complete the letter series.<br />D _ F D _ F _ F _ D _ F",
                    question_hi: "11.  उस विकल्प का चयन करें जो उन अक्षरों का निरूपण करता है, जो नीचे दिए गए रिक्त स्थानों में बाएं से दाएं रखे जाने पर अक्षर श्रृंखला को पूरा करेंगे।<br />D _ F D _ F _ F _ D _ F",
                    options_en: [" D F F D D", " F D D F F", 
                                " D F D D F", " F F D F F"],
                    options_hi: [" D F F D D", " F D D F F",
                                " D F D D F", " F F D F F"],
                    solution_en: "<p>11.(d) D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F / D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F / <span style=\"text-decoration: underline;\"><strong>D</strong></span> F <span style=\"text-decoration: underline;\"><strong>F</strong></span> / D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F</p>",
                    solution_hi: "<p>11.(d) D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F / D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F / <span style=\"text-decoration: underline;\"><strong>D</strong></span> F <span style=\"text-decoration: underline;\"><strong>F</strong></span> / D <span style=\"text-decoration: underline;\"><strong>F</strong></span> F</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. In a certain code language, TOWARD\' is coded as \'962147\', \'WANTED\' is coded as \'213957\'. What can be the code for \'WARRANT in that code language?</p>",
                    question_hi: "<p>12. एक निश्चित कूट भाषा में \'TOWARD\' को \'962147\' लिखा जाता है और \'WANTED को 213957\' लिखा जाता है। उसी कूट भाषा में \'WARRANT\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>2133129</p>", "<p>2144129</p>", 
                                "<p>2145129</p>", "<p>2144139</p>"],
                    options_hi: ["<p>2133129</p>", "<p>2144129</p>",
                                "<p>2145129</p>", "<p>2144139</p>"],
                    solution_en: "<p>12.(d)<br><strong>Logic</strong> : Letters are directly coded as their respective number <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421050.png\" alt=\"rId20\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421147.png\" alt=\"rId21\"></p>",
                    solution_hi: "<p>12.(d)<br><strong>तर्क:</strong> अक्षरों को सीधे उनकी संबंधित संख्या के रूप में कोडित किया जाता है <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421050.png\" alt=\"rId20\"><br>इसी तरह<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421147.png\" alt=\"rId21\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. \"If \'R &times; S\' means \'R is the sister of S\'.<br>If \'R + S\' means \'R is the brother of S\'.<br>If \'R &divide;&nbsp;S\' means \'R is the daughter of S\'.<br>If \'R - S\' means \'R is the husband of S\'.<br>How is H related to J in the expression \'H - F &times; I &divide; J ?</p>",
                    question_hi: "<p>13. यदि \'R &times; S\' का अर्थ है \'R, S की बहन है\'।<br>यदि \'R + S\' का अर्थ है \'R, S का भाई है\'।<br>यदि \'R &divide; S\' का अर्थ है \'R, S की बेटी है\'।<br>यदि \'R - S\' का अर्थ है \'R, S का पति है\'।<br>व्यंजक \' \'H - F &times; I &divide; J\' में, H का J से क्या संबंध है?</p>",
                    options_en: ["<p>Sister\'s husband</p>", "<p>Son</p>", 
                                "<p>Daughter\'s husband</p>", "<p>Father</p>"],
                    options_hi: ["<p>बहन का पति</p>", "<p>बेटा</p>",
                                "<p>बेटी का पति</p>", "<p>पिता</p>"],
                    solution_en: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421287.png\" alt=\"rId22\" width=\"171\" height=\"113\"><br>As we can see, H is the husband of J&rsquo;s daughter..</p>",
                    solution_hi: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421287.png\" alt=\"rId22\" width=\"171\" height=\"113\"><br>जैसा कि हम देख सकते हैं, H, J की बेटी का पति है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. By Interchanging the given two numbers (not digits) which of the following equations will be not correct?<br>2 and 3</p>",
                    question_hi: "<p>14. नीचे दी गई दो संख्याओं (न कि अंकों) को आपस में बदलने से निम्नलिखित में से कौन-सा समीकरण सही नहीं होगा ?<br>2 और 3</p>",
                    options_en: ["<p>5 - 3 &divide;&nbsp;1 &times; 2 + 7 = 6</p>", "<p>7 + 8 - 6 &divide; 2 &times; 3 = 11</p>", 
                                "<p>8 + 7 &times; 3 - 6 &divide; 2 = 22</p>", "<p>7 &times; 3 - 9 &divide; 2 + 4 = 15</p>"],
                    options_hi: ["<p>5 - 3 &divide; 1 &times; 2 + 7 = 6</p>", "<p>7 + 8 - 6 &divide; 2 &times; 3 = 11</p>",
                                "<p>8 + 7 &times; 3 - 6 &divide; 2 = 22</p>", "<p>7 &times; 3 - 9 &divide; 2 + 4 = 15</p>"],
                    solution_en: "<p>14.(c)<br>After going through all the options, option (c) satisfies. After interchanging 2 and 3 we get,<br>8 + 7 &times; 2 - 6 &divide; 3 <br>8 + 14 - 2 = 20<br>L.H.S. &ne;&nbsp; R.H.S.</p>",
                    solution_hi: "<p>14.(c)<br>सभी विकल्पों की जांच करने पर विकल्प (c) संतुष्ट करता है। 2 और 3 को आपस में बदलने पर हमें प्राप्त होता है,<br>8 + 7 &times; 2 - 6 &divide;&nbsp;3 <br>8 + 14 - 2 = 20<br>L.H.S. &ne;&nbsp; R.H.S.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "15. What should come in place of the question mark (?) in the given series based on the English alphabetical order ?<br />HAF, KDI, NGL, QJO, ?",
                    question_hi: "15. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br />HAF, KDI, NGL, QJO, ?",
                    options_en: ["  PQR", "  RTW", 
                                "  TMR", " SKP"],
                    options_hi: ["  PQR", "  RTW",
                                "  TMR", " SKP"],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421397.png\" alt=\"rId23\" width=\"346\" height=\"107\"></p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421397.png\" alt=\"rId23\" width=\"346\" height=\"107\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In the following question, select the figure given in the options which can be placed in place of the question mark (?).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421589.png\" alt=\"rId24\" width=\"366\" height=\"78\"></p>",
                    question_hi: "<p>16. निम्नलिखित प्रश्न में, विकल्पों में दी गई उस आकृति का चयन कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखा जा सकता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421589.png\" alt=\"rId24\" width=\"366\" height=\"78\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421705.png\" alt=\"rId25\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421810.png\" alt=\"rId26\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421907.png\" alt=\"rId27\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258422048.png\" alt=\"rId28\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421705.png\" alt=\"rId25\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421810.png\" alt=\"rId26\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258421907.png\" alt=\"rId27\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258422048.png\" alt=\"rId28\" width=\"90\"></p>"],
                    solution_en: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258422048.png\" alt=\"rId28\" width=\"90\"></p>",
                    solution_hi: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258422048.png\" alt=\"rId28\" width=\"90\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the option from the following that is related to the fifth word in the same way as the fourth word is related to the third word, and the second word is related to the first word.<br>Bangalore : City of Garden :: Srinagar : City of Lakes :: Kolkata : ?</p>",
                    question_hi: "<p>17. निम्नलिखित विकल्पों में से उस विकल्प का चयन कीजिए जो पांचवें शब्द से ठीक उसी प्रकार संबंधित है जिस प्रकार चौथा शब्द तीसरे शब्द से संबंधित है, और दूसरा शब्द पहले शब्द से संबंधित है।<br>बैंग्लोर : सिटी ऑफ गार्डन :: श्रीनगर : सिटी ऑफ लेक्स :: कोलकाता : ?</p>",
                    options_en: ["<p>City of Mountains</p>", "<p>City of Joy</p>", 
                                "<p>City of Temples</p>", "<p>City of Flowers</p>"],
                    options_hi: ["<p>सिटी ऑफ माउंटेन्स (City of Mountains)</p>", "<p>सिटी ऑफ जॉय (City of Joy)</p>",
                                "<p>सिटी ऑफ टेंपल्स (City of Temples)</p>", "<p>सिटी ऑफ फ्लावर्स (City of Flowers)</p>"],
                    solution_en: "<p>17.(b)<br>As Bangalore is known as city of garden, Sri Nagar is known as city of lakes similarly Kolkata is known as city of joy.</p>",
                    solution_hi: "<p>17.(b)<br>जिस प्रकार बैंग्लोर को सिटी ऑफ गार्डन कहा जाता है , श्रीनगर को सिटी ऑफ लेक्स कहा जाता है उसी प्रकार कोलकाता को सिटी ऑफ जॉय कहा जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. \'A + B\' means \'A is the brother of B\'.<br>\'A - B\' means \'A is the wife of B\'.<br>\'A &times;&nbsp;B\' means \'A is the father of B\'.<br>\'A &divide;&nbsp;B\' means \'A is the sister of B\'.<br>In \'T &times;&nbsp;X &divide; Y + Z\', how is T related to Z using the same meaning of the mathematical<br>operators as given above?</p>",
                    question_hi: "<p>18. &lsquo;A + B\' का अर्थ है \'A, B का भाई है।<br>\'A - B\' का अर्थ है \'A, B की पत्नी है\'।<br>\'A &times;&nbsp;B\' का अर्थ है \'A, B के पिता है।<br>\'A &divide; B\' का अर्थ है \'A, B की बहन है।<br>ऊपर दिए गए गणितीय संकारकों के समान अर्थ का उपयोग करते हुए ज्ञात करें कि \'T &times;&nbsp;X &divide; Y + Z\' में T का Z से क्या संबंध है?</p>",
                    options_en: ["<p>Father</p>", "<p>Father-in-law</p>", 
                                "<p>Brother</p>", "<p>Daughter</p>"],
                    options_hi: ["<p>पिता</p>", "<p>ससुर</p>",
                                "<p>भाई</p>", "<p>पुत्री</p>"],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258422165.png\" alt=\"rId29\" width=\"228\" height=\"144\"><br>T is the father of Z.</p>",
                    solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258422165.png\" alt=\"rId29\" width=\"228\" height=\"144\"><br>T, Z का पिता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the option in which the numbers share the same relationship as that shared by the given pairs of numbers.<br>2513 - 20;<br>4213 - 18</p>",
                    question_hi: "<p>19. उस विकल्प का चयन कीजिए, जिसमें संख्याएं वही संबंध साझा करती हैं, जो संख्याओं के दिए गए समूहों द्वारा साझा किया जाता है।<br>2513 - 20;<br>4213 - 18</p>",
                    options_en: ["<p>3122 - 14</p>", "<p>2521 - 10</p>", 
                                "<p>5131 - 22</p>", "<p>1462 - 26</p>"],
                    options_hi: ["<p>3122 - 14</p>", "<p>2521 - 10</p>",
                                "<p>5131 - 22</p>", "<p>1462 - 26</p>"],
                    solution_en: "<p>19.(a) <strong>Logic</strong> :- (Sum of digit of first number) &times; 2 - 2 = 2nd number<br>(2513 : 20) :- (2 + 5 + 1 + 3) &times; 2 - 2 &rArr; (11) &times; 2 - 2 = 20<br>(4213 : 18) :- (4 + 2 + 1 + 3) &times; 2 - 2 &rArr; (10) &times; 2 - 2 = 18<br>Similarly,<br>(3122- 14) :- (3 + 1 + 2 + 2) &times; 2 -2 &rArr; (8) &times; 2 - 2 = 14</p>",
                    solution_hi: "<p>19.(a) <strong>तर्क:-</strong> (पहली संख्या के अंकों का योग) &times; 2 - 2 = दूसरी संख्या<br>(2513 : 20) :- (2 + 5 + 1 + 3) &times; 2 - 2 &rArr; (11) &times; 2 - 2 = 20<br>(4213 : 18) :- (4 + 2 + 1 + 3) &times; 2 - 2 &rArr; (10) &times; 2 - 2 = 18<br>इसी प्रकार,<br>(3122 - 14) :- (3 + 1 + 2 + 2) &times; 2 -2 &rArr; (8) &times; 2 -2 = 14</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Which answer figure will complete the pattern in the question figure ? (rotation is not allowed)<br><strong id=\"docs-internal-guid-1669f013-7fff-5455-384d-166ebc940839\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfD0cVScBUByua1eQQQePtKQBEKki2lkfchHwNQJNfUySbRHmGqVh0Dz3WaXMQMYLo5KUKZCaDDSWtI6hAYhK-cdM5Z3HHrnVwSgUsNo9c8EOSrOx4RX41Nj-pNx2FXDmf2vuAzqw?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"100\" height=\"99\"></strong></p>",
                    question_hi: "<p>20. दी गईं विकल्प आकृतियों में से कौन-सी आकृति प्रश्न आकृति के पैटर्न को पूरा करेगी? (घूर्णन की अनुमति नहीं है)<br><strong id=\"docs-internal-guid-1669f013-7fff-5455-384d-166ebc940839\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfD0cVScBUByua1eQQQePtKQBEKki2lkfchHwNQJNfUySbRHmGqVh0Dz3WaXMQMYLo5KUKZCaDDSWtI6hAYhK-cdM5Z3HHrnVwSgUsNo9c8EOSrOx4RX41Nj-pNx2FXDmf2vuAzqw?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"100\" height=\"99\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-c935a8a6-7fff-ae94-4e18-d69c2fe66d8d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdzbgPVl3lJejIkmRJbu6npP0uWmlhrW7c3TtnzsJi4ABoOMtDZ4lXuam5y-3Jp_Hkt6SvaYwJAUisL5MYIJh3z2cHj1Nx_-Dtz5qaUL_iH3zaCn87gndX0OSJLcxRAlPP-E1eukQ?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"80\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-d676448d-7fff-051d-1d6d-43708536cdaf\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc2bBsD-tiOa_tL2qppQBR0ApVYP7AOXq_amkpa2m-4NwOCoU5j6hVgodl5phJdNbWtZ5dykh_OuHbkwRxKwzCq7yOqS2wz2tKWEuLBHbTXcmgx_ex_8Tp_HB8nQ6IyptOhhM0vVw?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"80\" height=\"75\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-81d6efcd-7fff-4e58-d04e-ebeab926f792\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPBbacwGU0Y6B4G-sK9Fg-8QzWmagcOESql5Xwlug15qoaVNY0HP8thULtdrmkoEoNhGoHldf-3qayDz9SaLU2kmgrWS4vFQ1FSP4llIza8H4OfVMhYnm3ZsR82cq0XEsktCW1zw?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"89\" height=\"82\"></strong></p>", "<p><strong id=\"docs-internal-guid-8a5ba930-7fff-b6f6-9f5e-3e7cfc8044ce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeuQ7fKnnfjszDfaXQDduxk0rdpplKnQt3pP8rI4mG25_TMEVsy4hXswDeXFWEmuARybw0WD3OrmvKcGLuwqUQzXnp1RzbalShbwW9vox7sHQkXDqXw3Uh35v4XdMpvCzMYHK0TEg?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"86\" height=\"79\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-c935a8a6-7fff-ae94-4e18-d69c2fe66d8d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdzbgPVl3lJejIkmRJbu6npP0uWmlhrW7c3TtnzsJi4ABoOMtDZ4lXuam5y-3Jp_Hkt6SvaYwJAUisL5MYIJh3z2cHj1Nx_-Dtz5qaUL_iH3zaCn87gndX0OSJLcxRAlPP-E1eukQ?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"80\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-d676448d-7fff-051d-1d6d-43708536cdaf\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc2bBsD-tiOa_tL2qppQBR0ApVYP7AOXq_amkpa2m-4NwOCoU5j6hVgodl5phJdNbWtZ5dykh_OuHbkwRxKwzCq7yOqS2wz2tKWEuLBHbTXcmgx_ex_8Tp_HB8nQ6IyptOhhM0vVw?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"80\" height=\"75\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-81d6efcd-7fff-4e58-d04e-ebeab926f792\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPBbacwGU0Y6B4G-sK9Fg-8QzWmagcOESql5Xwlug15qoaVNY0HP8thULtdrmkoEoNhGoHldf-3qayDz9SaLU2kmgrWS4vFQ1FSP4llIza8H4OfVMhYnm3ZsR82cq0XEsktCW1zw?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"89\" height=\"82\"></strong></p>", "<p><strong id=\"docs-internal-guid-8a5ba930-7fff-b6f6-9f5e-3e7cfc8044ce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeuQ7fKnnfjszDfaXQDduxk0rdpplKnQt3pP8rI4mG25_TMEVsy4hXswDeXFWEmuARybw0WD3OrmvKcGLuwqUQzXnp1RzbalShbwW9vox7sHQkXDqXw3Uh35v4XdMpvCzMYHK0TEg?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"86\" height=\"79\"></strong></p>"],
                    solution_en: "<p>20.(b)<br><strong id=\"docs-internal-guid-368d3c19-7fff-0930-93b3-ce1661529dde\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc2bBsD-tiOa_tL2qppQBR0ApVYP7AOXq_amkpa2m-4NwOCoU5j6hVgodl5phJdNbWtZ5dykh_OuHbkwRxKwzCq7yOqS2wz2tKWEuLBHbTXcmgx_ex_8Tp_HB8nQ6IyptOhhM0vVw?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"88\" height=\"82\"></strong></p>",
                    solution_hi: "<p>20.(b)<br><strong id=\"docs-internal-guid-368d3c19-7fff-0930-93b3-ce1661529dde\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc2bBsD-tiOa_tL2qppQBR0ApVYP7AOXq_amkpa2m-4NwOCoU5j6hVgodl5phJdNbWtZ5dykh_OuHbkwRxKwzCq7yOqS2wz2tKWEuLBHbTXcmgx_ex_8Tp_HB8nQ6IyptOhhM0vVw?key=z1i_PUq71169n4UrMDY5PGgS\" width=\"88\" height=\"82\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. &lsquo;Ample&rsquo; is related to &lsquo;Abundant&rsquo; in the same way as &lsquo;Substantial&rsquo; is related to &lsquo;________&rsquo;.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>21. \'बहुत\' (Ample), \'प्रचुरता\' (Abundant) से उसी प्रकार संबंधित है, जिस प्रकार \'पर्याप्&zwj;त\' (Substantial) \'________\' से संबंधित है। (शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्&zwj;दों को अक्षरों की संख्या/व्यंजनों/नोंस्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)</p>",
                    options_en: ["<p>Considerable</p>", "<p>Meager</p>", 
                                "<p>Scarce</p>", "<p>Venerable</p>"],
                    options_hi: ["<p>यथेष्&zwj;ट (Considerable)</p>", "<p>अल्&zwj;प (Meager)</p>",
                                "<p>अपर्याप्&zwj;त (Scarce)</p>", "<p>सम्&zwj;मानित (Venerable )</p>"],
                    solution_en: "<p>21.(a) As &lsquo;Ample&rsquo; is the synonym of &lsquo;Abundant&rsquo; similarly, &lsquo;Substantial&rsquo; is the synonym of &lsquo;Considerable&rsquo;.</p>",
                    solution_hi: "<p>21.(a) जैसे \'बहुत\' (Ample)\', \'प्रचुरता\' (Abundant) का पर्याय है, उसी प्रकार, \'पर्याप्&zwj;त\' (Substantial) \',यथेष्&zwj;ट (Considerable) का पर्याय है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. What should come in place of &lsquo;?&rsquo; in the given series?<br>16,&nbsp; 8.5,&nbsp; 9.5,&nbsp; 15.75,&nbsp; 33.5,&nbsp; ?</p>",
                    question_hi: "<p>22. दी गई शृंखला में \'?\' के स्थान पर क्या आना चाहिए?<br>16,&nbsp; 8.5,&nbsp; 9.5,&nbsp; 15.75,&nbsp; 33.5,&nbsp; ?</p>",
                    options_en: ["<p>69</p>", "<p>83.75</p>", 
                                "<p>67</p>", "<p>86.25</p>"],
                    options_hi: ["<p>69</p>", "<p>83.75</p>",
                                "<p>67</p>", "<p>86.25</p>"],
                    solution_en: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258422941.png\" alt=\"rId35\" width=\"427\" height=\"51\"></p>",
                    solution_hi: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258422941.png\" alt=\"rId35\" width=\"427\" height=\"51\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. This question consists of a pair of words which have a certain relationship to each other. Select the pair which does NOT have the same relationship.<br>Smart : Intelligent<br>1) Liable : Accountable<br>2) Modest : Humble<br>3) Nimble : Sluggish<br>4) Outbreak : Eruption</p>",
                    question_hi: "<p>23. इस प्रश्न में शब्द -युग्म है जिनका एक दूसरे से एक निश्चित संबंध है। उस युग्म का चयन कीजिए जिसमें समान संबंध नहीं है।<br>चतुर : बुद्धिमान<br>1) उत्तरदायी : जवाबदेह<br>2) विनम्र : नम्र<br>3) फुर्तीला : सुस्त<br>4) प्रकोप : विस्फोट</p>",
                    options_en: ["<p>1</p>", "<p>4</p>", 
                                "<p>2</p>", "<p>3</p>"],
                    options_hi: ["<p>1</p>", "<p>4</p>",
                                "<p>2</p>", "<p>3</p>"],
                    solution_en: "<p>23.(d) As the meaning of Smart : Intelligent is same, similarly the meaning of pairs of words in all the three options are same except Nimble : Sluggish.</p>",
                    solution_hi: "<p>23.(d) जिस प्रकार चतुर : बुद्धिमान का अर्थ एक ही है, उसी प्रकार फुर्तीला : सुस्त को छोड़कर तीनों विकल्पों में शब्द युग्मों का अर्थ भी एक ही है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements. <br><strong>Statements:</strong> <br>Some chickens are hens. <br>No hen is an egg. <br><strong>Conclusions:</strong> <br>(I) No chicken is an egg. <br>(II) All hens are chickens.</p>",
                    question_hi: "<p>24. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, चाहे वह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो और निर्णय लीजिए कि दिए गए निष्कर्षों में से कौन-सा/कौन-से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं। <br><strong>कथन:</strong> <br>कुछ चूजे, मुर्गियाँ हैं। <br>कोई मुर्गी, अंडा नहीं है। <br><strong>निष्कर्ष:</strong> <br>(I) कोई चूजा, अंडा नहीं है. <br>(II) सभी मुर्गियाँ, चूजे हैं।</p>",
                    options_en: ["<p>Only conclusion II follows</p>", "<p>Both conclusions I and II follow</p>", 
                                "<p>Neither conclusion I nor II follows</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (II) अनुसरण करता है</p>", "<p>दोनों निष्कर्ष (I) और (II) अनुसरण करते है</p>",
                                "<p>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है</p>", "<p>केवल निष्कर्ष (I) अनुसरण करता है</p>"],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258423030.png\" alt=\"rId36\" width=\"365\" height=\"76\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258423175.png\" alt=\"rId37\" width=\"367\" height=\"86\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p style=\"text-align: justify;\">25. Select the triad from among the given options that is analogous to the given triad.<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>505</mn><mo>-</mo><mn>604</mn><mo>-</mo><mn>217</mn></math></p>",
                    question_hi: "<p>25. दिए गए विकल्पों में से उस त्रयी का चयन कीजिए जो दी गई त्रयी के सदृश हो।<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>505</mn><mo>-</mo><mn>604</mn><mo>-</mo><mn>217</mn></math></p>",
                    options_en: ["<p><math display=\"inline\"><mn>432</mn><mo>-</mo><mn>513</mn><mo>-</mo><mn>711</mn></math></p>", "<p><math display=\"inline\"><mn>161</mn><mo>-</mo><mn>512</mn><mo>-</mo><mn>710</mn></math></p>", 
                                "<p><math display=\"inline\"><mn>125</mn><mo>-</mo><mn>223</mn><mo>-</mo><mn>342</mn></math></p>", "<p><math display=\"inline\"><mn>244</mn><mo>-</mo><mn>226</mn><mo>-</mo><mn>262</mn></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mn>432</mn><mo>-</mo><mn>513</mn><mo>-</mo><mn>711</mn></math></p>", "<p><math display=\"inline\"><mn>161</mn><mo>-</mo><mn>512</mn><mo>-</mo><mn>710</mn></math></p>",
                                "<p><math display=\"inline\"><mn>125</mn><mo>-</mo><mn>223</mn><mo>-</mo><mn>342</mn></math></p>", "<p><math display=\"inline\"><mn>244</mn><mo>-</mo><mn>226</mn><mo>-</mo><mn>262</mn></math></p>"],
                    solution_en: "<p>25.(d) <strong>Logic</strong> :- (Sum of digit in each set of number) = 10<br>(505 - 604 - 217) :- (5 + 0 + 5) = (6 + 0 + 4) = (2 + 1 + 7) = 10<br>Similarly,<br>(244- 226 - 262) :- (2 + 4 + 4) = (2 + 2 + 6) = (2 + 6 + 2) = 10</p>",
                    solution_hi: "<p>25.(d) <strong>तर्क</strong> :- (संख्या के प्रत्येक समूह में अंकों का योग) = 10<br>(505 - 604 - 217) :- (5 + 0 + 5) = (6 + 0 + 4) = (2 + 1 + 7) = 10<br>इसी प्रकार,<br>(244- 226 - 262) :- (2 + 4 + 4) = (2 + 2 + 6) = (2 + 6 + 2) = 10</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which company has appointed MS Dhoni as its brand ambassador to promote digital sports viewing?</p>",
                    question_hi: "<p>26. किस कंपनी ने डिजिटल खेल देखने को बढ़ावा देने के लिए एमएस धोनी को अपना ब्रांड एंबेसडर नियुक्त किया है?</p>",
                    options_en: ["<p>Viacom18</p>", "<p>Star Sports</p>", 
                                "<p>Sony Pictures Networks India</p>", "<p>ESPN</p>"],
                    options_hi: ["<p>वायाकॉम 18</p>", "<p>स्टार स्पोर्ट्स</p>",
                                "<p>सोनी पिक्चर्स नेटवर्क्स इंडिया</p>", "<p>ESPN</p>"],
                    solution_en: "<p>26.(a) Viacom18</p>",
                    solution_hi: "<p>26.(a) वायाकॉम18</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. World Thyroid Day is observed on ______.</p>",
                    question_hi: "<p>27. विश्व थायराइड दिवस ________ को मनाया जाता है।</p>",
                    options_en: ["<p>21 May</p>", "<p>17th June</p>", 
                                "<p>25 May</p>", "<p>23rd March</p>"],
                    options_hi: ["<p>21 मई</p>", "<p>17 जून</p>",
                                "<p>25 मई</p>", "<p>23 मार्च</p>"],
                    solution_en: "<p>27.(c) World Thyroid Day is celebrated on <strong>May 25th.</strong> 2022 theme: \'Measure Your Blood Pressure Accurately, Control It, Live Longer\'. <strong>17th June-</strong> World Day to Combat Desertification and Drought, <strong>23rd March</strong> -World Meteorological Day. <strong>21 May-</strong> International Tea Day every year.</p>",
                    solution_hi: "<p>27.(c) विश्व थायराइड दिवस <strong>25 मई</strong> को मनाया जाता है। थीम 2022: \'Measure Your Blood Pressure Accurately, Control It, Live Longer\'. <strong>17 जून</strong> &rarr; &lsquo;World Day to Combat Desertification and Drought&rsquo;, <strong>23 मार्च</strong> &rarr; विश्व मौसम विज्ञान दिवस, <strong>21 मई</strong> &rarr; अंतरराष्ट्रीय चाय दिवस ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The Government has approved the scheme of Rashtriya Uchchatar Shiksha Abhiyan (RUSA) for continuation till ___________.</p>",
                    question_hi: "<p>28. सरकार ने राष्ट्रीय उच्चतर शिक्षा अभियान (RUSA) की योजना को कब तक जारी रखने के लिए मंजूरी दी है।</p>",
                    options_en: ["<p>31 March 2023</p>", "<p>31 March 2025</p>", 
                                "<p>31 March 2026</p>", "<p>31 March 2027</p>"],
                    options_hi: ["<p>31 मार्च 2023</p>", "<p>31 मार्च 2025</p>",
                                "<p>31 मार्च 2026</p>", "<p>31 मार्च 2027</p>"],
                    solution_en: "<p>28.(c) <strong>31 March 2026.</strong> <br>The Government has approved the scheme of Rashtriya Uchchatar Shiksha Abhiyan (RUSA) for continuation till 31st March 2026. (RUSA) is a centrally sponsored scheme, working in mission mode for funding State Government Universities and Colleges to achieve the goals of equity, accessibility and excellence.</p>",
                    solution_hi: "<p>28.(c) <strong>31 मार्च 2026</strong>.&nbsp;<br>सरकार ने राष्ट्रीय उच्चतर शिक्षा अभियान&nbsp;(RUSA) की योजना को 31 मार्च 2026 तक जारी रखने के लिए मंजूरी दी है।(RUSA) एक केंद्र प्रायोजित योजना है, जो राज्य सरकार के विश्वविद्यालयों और कॉलेजों को इक्विटी, पहुंच और उत्कृष्टता के लक्ष्यों को प्राप्त करने के लिए वित्त पोषण के लिए मिशन मोड में काम कर रही है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. The Ghoom Monastery is located in:</p>",
                    question_hi: "<p>29. &lsquo;घूम मठ&rsquo; कहाँ स्थित है?</p>",
                    options_en: ["<p>Gangtok</p>", "<p>Namch</p>", 
                                "<p>Kalimpong</p>", "<p>Darjeeling</p>"],
                    options_hi: ["<p>गंगटोक</p>", "<p>नामचू</p>",
                                "<p>कलिम्पोंग</p>", "<p>दार्जिलिंग</p>"],
                    solution_en: "<p>29.(d) <strong>Ghoom Monastery: </strong>First Tibetan Monastery in <strong>Darjeeling</strong> (West Bengal). <strong>Famous Monasteries in India</strong> are : Hemis Monasteries (Ladakh), Tabo Monasteries (Himachal Pradesh), Tsuglagkhang Monastery (Dharamshala), Thiksey Monastery (Ladakh), Tawang Monastery (Arunachal Pradesh), Bylakuppe Monastery (Karnataka), Rumtek Monastery (Gangtok, Sikkkim) etc.</p>",
                    solution_hi: "<p>29.(d) <strong>घूम मठ:</strong> <strong>दार्जिलिंग </strong>(पश्चिम बंगाल) में पहला तिब्बती मठ है। <strong>भारत में प्रसिद्ध मठ</strong>- हेमिस मठ (लद्दाख), ताबो मठ (हिमाचल प्रदेश), सुलग्लगखंग मठ (धर्मशाला), ठिकसे मठ (लद्दाख), तवांग मठ (अरुणाचल प्रदेश), बाइलाकुप्पे मठ (कर्नाटक), रुमटेक मठ (गंगटोक, सिक्किम) आदि हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. What is the name of Google\'s generative AI chatbot?</p>",
                    question_hi: "<p>30. Google के जेनरेटिव AI चैटबॉट का नाम क्या है?</p>",
                    options_en: ["<p>Alexa</p>", "<p>Siri</p>", 
                                "<p>Cortana</p>", "<p>Bard</p>"],
                    options_hi: ["<p>एलेक्सा</p>", "<p>सिरी</p>",
                                "<p>कॉर्टाना</p>", "<p>बार्ड</p>"],
                    solution_en: "<p>30.(d) <strong>Bard</strong> is the original name of Gemini, Google\'s generative AI chatbot. Other AI chatbot and their Developer: Alexa &ndash; by Amazon, Siri &ndash; by Apple, Cortana &ndash; by Microsoft.</p>",
                    solution_hi: "<p>30.(d) <strong>बार्ड,</strong> गूगल के जनरेटिव AI चैटबॉट Gemini का मूल नाम है। अन्य प्रमुख AI चैटबॉट और उनके डेवलपर: एलेक्सा (Alexa) - अमेज़न द्वारा, सिरी (Siri) - एप्पल द्वारा, कॉर्टाना (Cortana) - माइक्रोसॉफ्ट द्वारा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who was the first woman chief secretary of Punjab?</p>",
                    question_hi: "<p>31. पंजाब की पहली महिला मुख्य सचिव कौन&nbsp;थी?</p>",
                    options_en: ["<p>Shivangi Singh</p>", "<p>Nupur Kulshrestha</p>", 
                                "<p>Vini Mahajan</p>", "<p>Priyanka Mohite</p>"],
                    options_hi: ["<p>शिवांगी सिंह</p>", "<p>नुपुर कुलश्रेष्ठ</p>",
                                "<p>विनी महाजन</p>", "<p>प्रियंका मोहिते</p>"],
                    solution_en: "<p>31.(c) <strong>Vini Mahajan</strong> was the first woman chief secretary of Punjab. <strong>Shivangi Singh</strong> First woman Rafale fighter jet pilot of the Indian Air Force tableau at the Republic Day Parade. <strong>Nupur Kulshrestha</strong> becomes the first woman to be promoted as Deputy Inspector General (DIG) of Indian Coast Guard.<strong> Priyanka Mohite is the first</strong> Indian woman to scale&nbsp;five peaks above 8,000 meters.</p>",
                    solution_hi: "<p>31.(c) <strong>विनी महाजन</strong> पंजाब की पहली महिला मुख्य सचिव थीं। <strong>शिवांगी सिंह</strong> गणतंत्र दिवस परेड में भारतीय वायु सेना की झांकी की&nbsp; पहली महिला राफेल फाइटर जेट पायलट। <strong>नुपुर कुलश्रेष्ठ</strong> भारतीय तटरक्षक बल के उप महानिरीक्षक (DIG) के रूप में पदोन्नत होने वाली&nbsp;पहली महिला बनीं। <strong>प्रियंका मोहिते</strong> 8,000 मीटर से ऊपर की पांच चोटियों को फतह करने वाली पहली भारतीय महिला हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Who among the following was one of the leaders of the Santhal rebellion?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन संथाल विद्रोह के नेताओं में से एक था?</p>",
                    options_en: ["<p>Sidhu Manjhi</p>", "<p>Surya Sen</p>", 
                                "<p>BR Ambedkar</p>", "<p>Swami Vivekanand</p>"],
                    options_hi: ["<p>सिद्धू मांझी</p>", "<p>सूर्य सेन</p>",
                                "<p>बीआर अम्बेडकर</p>", "<p>स्वामी विवेकानंद</p>"],
                    solution_en: "<p>32.(a) <strong>Sidhu Manjhi</strong> was one of the leaders of the Santhal rebellion. Sidhu Murmu and Kanhu Murmu were brothers and the leaders of the Santhal rebellion, which started in 1855, in present-day Jharkhand and Bengal in eastern India against both the British colonial authority and the corrupt \'zamindari\' system. <strong>Surya Sen&rarr;</strong> An Indian revolutionary best known for leading the 1930 Chittagong armory raid against the British government. <strong>Swami Vivekananda (Narendra nath dutta)</strong> &rarr; Ramakrishna Mission in May 1897 and Belur math in 1898.</p>",
                    solution_hi: "<p>32.(a) <strong>सिद्धू मांझी</strong> संथाल विद्रोह के नेताओं में से एक थे। सिद्धू मुर्मू और कान्हू मुर्मू भाई थे और संथाल विद्रोह के नेता थे, जो 1855 में&nbsp;शुरू हुआ था, वर्तमान में पूर्वी भारत में झारखंड और बंगाल में ब्रिटिश औपनिवेशिक सत्ता और भ्रष्ट \'ज़मींदारी\' प्रणाली दोनों के खिलाफ। <strong>सूर्य सेन&rarr;</strong> एक भारतीय क्रांतिकारी थे जिन्हें ब्रिटिश सरकार के खिलाफ 1930 के चटगांव शस्त्रागार छापे का नेतृत्व करने के लिए जाना जाता था। स्वामी <strong>विवेकानंद (नरेंद्र नाथ दत्ता)</strong> &rarr;उन्होंने मई 1897 में रामकृष्ण मिशन और 1898 में बेलूर मठ की स्थापना की।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. What is the name of NASA\'s all-electric plane?</p>",
                    question_hi: "<p>33. NASA के ऑल-इलेक्ट्रिक विमान का नाम क्या है?</p>",
                    options_en: ["<p>X-47</p>", "<p>X-57</p>", 
                                "<p>X-69</p>", "<p>X-82</p>"],
                    options_hi: ["<p>X-47</p>", "<p>X-57</p>",
                                "<p>X-69</p>", "<p>X-82</p>"],
                    solution_en: "<p>33.(b)<strong> X-57</strong>. The aircraft has been adapted from an Italian-made Tecnam P2006T twin-engine propeller plane and has been under development since 2015. In June 2023, NASA ended the X-57 project before flight tests due to unresolved safety concerns. Different Missions of NASA: Perseverance Rover - To investigate astrobiology on Mars. Mars Ingenuity Helicopter: It was sent to Mars to perform experimental flight tests to determine if powered, controlled flight at the Red Planet was possible. OSIRIS-REx is the first U.S. mission to collect a sample from an asteroid.</p>",
                    solution_hi: "<p>33.(b) <strong>X-57.</strong> यह विमान एक इटालियन निर्मित टेकनैम P2006T जुड़वां-इंजन प्रोपेलर विमान पर आधारित था और 2015 से विकासाधीन था। जून 2023 में, NASA ने सुरक्षा से जुड़ी अनसुलझी चिंताओं के कारण X-57 परियोजना को उड़ान परीक्षणों से पहले ही समाप्त कर दिया। NASA के विभिन्न मिशन: परसिवियरेंस रोवर &ndash; मंगल ग्रह पर एस्ट्रोबायोलॉजी (जैव-अंतरिक्ष विज्ञान) की जांच करने के लिए । मंगल इनजेन्युटी हेलीकॉप्टर &ndash; इसे मंगल ग्रह पर प्रायोगिक उड़ान परीक्षण करने के लिए भेजा गया था, ताकि यह पता लगाया जा सके कि लाल ग्रह पर संचालित, नियंत्रित उड़ान संभव है या नहीं।। OSIRIS-REx मिशन &ndash; किसी क्षुद्रग्रह से नमूना एकत्र करने वाला पहला अमेरिकी मिशन।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which city of India is known as \'Athens of the East\'?</p>",
                    question_hi: "<p>34. भारत के किस शहर को &lsquo;पूर्व का एथेंस&rsquo; कहा जाता है ?</p>",
                    options_en: ["<p>Madurai</p>", "<p>Jaisalmer</p>", 
                                "<p>Jodhpur</p>", "<p>Kochi</p>"],
                    options_hi: ["<p>मदुरै</p>", "<p>जैसलमेर</p>",
                                "<p>जोधपुर</p>", "<p>कोच्ची</p>"],
                    solution_en: "<p>34.(a) <strong>Madurai</strong> is called <strong>&ldquo;The Athens of the East&rdquo;</strong> due to similarities with Athens. It is Situated on the banks of river Vaigai. <strong>Jaisalmer</strong> is also known as the <strong>&ldquo;Golden City of India.</strong> Kochi is known as <strong>The Queen Of Arabian Sea.</strong> Jodhpur is known as a blue city.</p>",
                    solution_hi: "<p>34.(a) एथेंस के साथ समानता के कारण <strong>मदुरै</strong> को \"<strong>पूर्व का एथेंस</strong>\" कहा जाता है। यह वैगई नदी के तट पर स्थित है। जैसलमेर को \"भारत का सुनहरा शहर\" भी कहा जाता है । <strong>कोच्चि</strong> को अरब सागर की रानी के रूप में जाना जाता है। <strong>जोधपुर को नीले शहर</strong> के रूप में जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Who among the following never became the Vice President of India?</p>",
                    question_hi: "<p>35. निम्नलिखित में से कौन कभी भारत के उपराष्ट्रपति नहीं बने?</p>",
                    options_en: ["<p>Gulzarilal Nanda</p>", "<p>VV Giri</p>", 
                                "<p>Zakir Husain</p>", "<p>BD Jatti</p>"],
                    options_hi: ["<p>गुलजारीलाल नंदा</p>", "<p>वीवी गिरि</p>",
                                "<p>जाकिर हुसैन</p>", "<p>बीडी जट्टी</p>"],
                    solution_en: "<p>35.(a) <strong>Gulzarilal Nanda</strong> never became the Vice President of India. Gulzarilal Nanda was an Indian politician and&nbsp;economist who served as <strong>interim Prime Minister twice</strong> following the deaths of Jawaharlal Nehru in 1964 and Lal Bahadur Shastri in 1966 respectively.</p>",
                    solution_hi: "<p>35.(a) <strong>गुलजारीलाल</strong> नंदा कभी भी भारत के उपराष्ट्रपति नहीं बने। गुलजारीलाल नंदा एक भारतीय राजनीतिज्ञ और अर्थशास्त्री थे, जिन्होंने क्रमशः 1964 में जवाहरलाल नेहरू और 1966 में लाल बहादुर शास्त्री की मृत्यु के बाद <strong>दो बार अंतरिम प्रधान मंत्री</strong> के रूप में कार्य किया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Vibration of which among the following produces only odd harmonics?</p>",
                    question_hi: "<p>36. निम्नलिखित में से किसका कम्पन केवल विषम स्वर उत्पन्न करता है?</p>",
                    options_en: ["<p>An air column in a closed organ pipe</p>", "<p>A vibrating string fixed at two ends</p>", 
                                "<p>An air column in an open organ pipe</p>", "<p>A vibrating rod fixed at two ends</p>"],
                    options_hi: ["<p>बंद ऑर्गन पाइप में एक वायु स्तंभ</p>", "<p>सिरों पर स्थिर एक कंपन डोरी</p>",
                                "<p>खुले ऑर्गन पाइप में एक वायु स्तंभ</p>", "<p>दो सिरों पर स्थिर एक कंपन छड़</p>"],
                    solution_en: "<p>36.(a) In the first mode of vibration of the air column, there is one node and one antinode. In the second mode of&nbsp;vibration of the air column, two nodes and&nbsp;two antinodes are formed. This shows&nbsp;that only odd harmonics are present&nbsp;in the modes of vibrations of the air&nbsp;column closed at one end.</p>",
                    solution_hi: "<p>36.(a) वायु स्तंभ के कंपन के पहले मोड में, एक नोड और एक एंटीनोड होता है। वायु स्तंभ के कंपन के दूसरे मोड में, दो नोड और दो एंटीनोड बनते हैं , इससे पता चलता है कि एक छोर पर बंद वायु स्तंभ के कंपन मोड में केवल विषम हार्मोनिक्स मौजूद होते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. The 60th Amendment to the Constitution of India increased the ceiling of profession tax from ₹ 250 p.a. to ______ <br>p.a.</p>",
                    question_hi: "<p>37. भारत के संविधान के 60 वें संशोधन ने पेशे कर की अधिकतम सीमा को ₹ 250 प्रति वर्ष से बढ़ाकर _____ प्रति वर्ष कर दिया।</p>",
                    options_en: ["<p>₹7,000</p>", "<p>₹5,000</p>", 
                                "<p>₹1,000</p>", "<p>₹2,500</p>"],
                    options_hi: ["<p>₹7,000</p>", "<p>₹5,000</p>",
                                "<p>₹1,000</p>", "<p>₹2,500</p>"],
                    solution_en: "<p>37.(d) The 60th Amendment to the Constitution of India increased the ceiling of profession tax from ₹ 250 p.a. to ₹2,500p.a. The <strong>Sixtieth Amendment</strong> of the Constitution of India Act, 1988, amended article 276 of the Constitution relating to taxes on professions, trades, callings and employment.</p>",
                    solution_hi: "<p>37.(d) भारत के संविधान में 60वें संशोधन ने पेशा कर की सीमा ₹ 250 प्रति वर्ष से बढ़ाकर ₹ 2,500 प्रति वर्ष कर दी। भारत के संविधान अधिनियम, 1988 के <strong>साठवें संशोधन</strong> ने व्यवसायों, और रोजगार पर करों से संबंधित संविधान के अनुच्छेद 276 में संशोधन किया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which space agency has successfully tested the VSHORADS missile?</p>",
                    question_hi: "<p>38. किस अंतरिक्ष एजेंसी ने &lsquo;VSHORADS&rsquo;मिसाइल का सफल परीक्षण किया है?</p>",
                    options_en: ["<p>NASA</p>", "<p>DRDO</p>", 
                                "<p>JAXA</p>", "<p>ESA</p>"],
                    options_hi: ["<p>NASA</p>", "<p>DRDO</p>",
                                "<p>JAXA</p>", "<p>ESA</p>"],
                    solution_en: "<p>38.(b) <strong>The Defence Research and Development Organization (DRDO)</strong> conducted two successful tests of the VeryShort Range Air Defence System (VSHORADS) missile on 27 Sep 2022 Chandipur, Odisha. Headquarter (New Delhi). Founded (1958). Dr Samir V Kamat (As of Nov 2022, Secretary, Department of Defence R&amp;D and Chairman). <strong>NASA</strong> (Washington, USA). <em>JAXA</em> (Chofu-city, Tokyo). <strong>ESA</strong> (Paris, France).</p>",
                    solution_hi: "<p>38.(b) <strong>रक्षा अनुसंधान और विकास संगठन (DRDO)</strong> ने 27 सितंबर 2022 को चांदीपुर, ओडिशा में वेरीशॉर्ट रेंज एयर डिफेंस सिस्टम (VSHORADS) मिसाइल के दो सफल परीक्षण किए। मुख्यालय (नई दिल्ली), स्थापित (1958)। डॉ समीर वी कामत (नवंबर 2022 तक, सचिव, रक्षा विभाग आर &amp; डी और अध्यक्ष)। <strong>NASA</strong> (वाशिंगटन, यूएसए)। <strong>JAXA</strong> (चोफू-शहर, टोक्यो)। <strong>ESA</strong> (पेरिस, फ्रांस)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. What test is used where the elements present in the compound are converted from covalent form into the ionic form by fusing the compound with sodium metal?</p>",
                    question_hi: "<p>39. निम्न में से किस परीक्षण का उपयोग किया जाता है, जहां यौगिक में मौजूद तत्व सहसंयोजक रूप से आयनिक रूप में यौगिक को सोडियम धातु के साथ संगलित करके परिवर्तित किया जाता है?</p>",
                    options_en: ["<p>Flame test</p>", "<p>D-dimer test</p>", 
                                "<p>Blood test</p>", "<p>Lassaigne&rsquo;s test</p>"],
                    options_hi: ["<p>ज्वाला परीक्षण (Flame test)</p>", "<p>D-डिमर टेस्ट (D-dimer test)</p>",
                                "<p>रक्त परीक्षण (Blood test)</p>", "<p>लैसेन का परीक्षण (Lassaigne&rsquo;s test)</p>"],
                    solution_en: "<p>39.(d) <strong>Lassaigne\'s</strong> test helps to detect Nitrogen, Sulphur, and Halogens in organic compounds. A <strong>flame</strong> test is a qualitative analysis used by the chemist to identify the metal and metalloid ion in the sample. A <strong>D-dimer</strong> test is a blood test that can be used to help rule out the presence of a serious blood clot.</p>",
                    solution_hi: "<p>39.(d) <strong>लैसेन का परीक्षण</strong> कार्बनिक यौगिकों में नाइट्रोजन, सल्फर और हैलोजन का पता लगाने में मदद करता है। <strong>ज्वाला परीक्षण</strong> एक गुणात्मक विश्लेषण है जिसका उपयोग रसायनज्ञ द्वारा नमूने में धातु और धातु आयन की पहचान करने के लिए किया जाता है। <strong>D-डिमर</strong> परीक्षण एक रक्त परीक्षण है जिसका उपयोग गंभीर रक्त के थक्के की उपस्थिति को रद्द करने में मदद के लिए किया जा सकता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which organization was created in 1988 by the World Meteorological Organization (WMO) and the United Nations Environment Program (UNEP) to assess the science related to climate change?</p>",
                    question_hi: "<p>40. विश्व मौसम विज्ञान संगठन (WMO) और संयुक्त राष्ट्र पर्यावरण कार्यक्रम (UNEP) द्वारा 1988में जलवायु परिवर्तन से संबंधित विज्ञान का आकलन करने के लिए कौन सा संगठन बनाया गया था?</p>",
                    options_en: ["<p>Intergovernmental Panel on Climate Change (IPCC)</p>", "<p>International Climate Action Network (ICAN)</p>", 
                                "<p>Global Climate Growth Institute (GCGI)</p>", "<p>Australian Youth Climate Coalition (AYCC)</p>"],
                    options_hi: ["<p>जलवायु परिवर्तन पर अंतरसरकारी पैनल (IPCC)</p>", "<p>इंटरनेशनल क्लाइमेट एक्शन नेटवर्क (ICAN)</p>",
                                "<p>वैश्विक जलवायु विकास संस्थान (GCGI)</p>", "<p>ऑस्ट्रेलियाई युवा जलवायु गठबंधन(AYCC)</p>"],
                    solution_en: "<p>40.(a) <strong>IPCC</strong> Headquarter (Geneva). Chairperson (Hoesung Lee, Nov 2022), Founded (1988). <strong>ICAN-</strong> Formation (1989), Headquarter Bonn (Germany), Founder (Michael Oppenheimer). <strong>Global</strong> Green Growth Institute- Formation (2010). Headquarters (Seoul), <strong>AYCC-</strong> Formation (2006). Headquarters (Melbourne).</p>",
                    solution_hi: "<p>40.(a) <strong>IPCC</strong> मुख्यालय (जिनेवा)। अध्यक्ष (होसुंग ली, नवंबर 2022), स्थापित (1988)। <strong>ICAN--</strong> गठन (1989), मुख्यालय बॉन (जर्मनी), संस्थापक (माइकल ओपेनहाइमर)। <strong>ग्लोबल ग्रीन ग्रोथ इंस्टीट्यूट- गठन </strong>(2010)। मुख्यालय (सियोल), <strong>AYCC</strong> - गठन (2006)। मुख्यालय (मेलबोर्न)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following articles of the constitution of India provides for the creation of a GST Council?</p>",
                    question_hi: "<p>41. भारत के संविधान के निम्नलिखित अनुच्छेदों में से किस में GST परिषद के गठन का प्रावधान है?</p>",
                    options_en: ["<p>Article 246A</p>", "<p>Article 279A</p>", 
                                "<p>Article 269A</p>", "<p>Article 323A</p>"],
                    options_hi: ["<p>अनुच्छेद 246A</p>", "<p>अनुच्छेद 279A</p>",
                                "<p>अनुच्छेद 269A</p>", "<p>अनुच्छेद 323A</p>"],
                    solution_en: "<p>41.(b) <strong>Article 279A</strong> of the Constitution of India provides for the creation of a GST Council. <strong>Article 246</strong> of the Constitution deals with the division of power between the Union and the States. <strong>Article 269</strong> of the Indian Constitution deals with the taxes levied and collected by the union but assigned to the States. <strong>Article 323</strong> <strong>A</strong>-It provides for the establishment of an administrative tribunal for the Union and&nbsp;a separate administrative tribunal for each State or for two or more States.</p>",
                    solution_hi: "<p>41.(b) भारत के संविधान के <strong>अनुच्छेद 279A</strong> में GST परिषद के निर्माण का प्रावधान है। संविधान का <strong>अनुच्छेद 246</strong> संघ और राज्यों के बीच शक्तियों के विभाजन से संबंधित है।भारतीय संविधान का <strong>अनुच्छेद 269</strong> संघ द्वारा लगाए और एकत्र किए गए लेकिन राज्यों को सौंपे गए करों से संबंधित है।<strong>अनुच्छेद 323 ए</strong>- यह संघ के लिए एक प्रशासनिक न्यायाधिकरण और प्रत्येक राज्य या दो या दो से अधिक राज्यों के लिए एक अलग प्रशासनिक न्यायाधिकरण की स्थापना का प्रावधान करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Who was appointed as the Revenue Secretary of India in January 2025 ?</p>",
                    question_hi: "<p>42. जनवरी 2025 में भारत के राजस्व सचिव के रूप में किसे नियुक्त किया गया?</p>",
                    options_en: ["<p>Arunish Chawla</p>", "<p>Tuhin Kanta Pandey</p>", 
                                "<p>Nirmala Sitharaman</p>", "<p>Shaktikanta Das</p>"],
                    options_hi: ["<p>अरुणिश चावला</p>", "<p>तुहिन कांता पांडे</p>",
                                "<p>निर्मला सीतारमण</p>", "<p>शक्तिकांत दास</p>"],
                    solution_en: "<p>42.(b)&nbsp;<strong>Tuhin Kanta Pandey.</strong>&nbsp; The Revenue Secretary is the administrative head of the Department of Revenue, which operates under the Ministry of Finance, Government of India. The Revenue Secretary plays a crucial role in formulating and implementing policies related to taxation and revenue collection.</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>42.(b) <strong>तुहिन कांता पांडे।&nbsp;</strong>राजस्व सचिव वित्त मंत्रालय, भारत सरकार के अंतर्गत राजस्व विभाग के प्रशासनिक प्रमुख होते हैं। राजस्व सचिव कराधान और राजस्व संग्रह से संबंधित नीतियों के निर्माण और कार्यान्वयन में महत्वपूर्ण भूमिका निभाते हैं।</p>\n<p><strong id=\"docs-internal-guid-0c2a199f-7fff-9c9b-9ea9-12e9c1ffa7bd\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. The Tropic of Cancer does NOT pass through which of the following Indian states?</p>",
                    question_hi: "<p>43. कर्क रेखा निम्नलिखित में से किस भारतीय राज्य से नहीं गुजरती है?</p>",
                    options_en: ["<p>Chhattisgarh</p>", "<p>Assam</p>", 
                                "<p>Tripura</p>", "<p>Jharkhand</p>"],
                    options_hi: ["<p>छत्तीसगढ़</p>", "<p>असम</p>",
                                "<p>त्रिपुरा</p>", "<p>झारखंड</p>"],
                    solution_en: "<p>43.(b) The Tropic of Cancer passes through eight states in India: Gujarat (Jasdan), Rajasthan (Kalinjarh), Madhya Pradesh (Shajapur), Chhattisgarh (Sonhat), Jharkhand (Lohardaga), West Bengal (Krishnanagar), Tripura (Udaipur) and Mizoram (Champhai).</p>",
                    solution_hi: "<p>43.(b) कर्क रेखा भारत के आठ राज्यों से होकर गुजरती है: गुजरात (जसदन), राजस्थान (कालिंजरह), मध्य प्रदेश (शाजापुर), छत्तीसगढ़ (सोनहत), झारखंड (लोहरदगा), पश्चिम बंगाल (कृष्णानगर), त्रिपुरा और मिजोरम (चम्फाई) आदि ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which country will host the 2025 World Athletics Championships?</p>",
                    question_hi: "<p>44. 2025 विश्व एथलेटिक्स चैंपियनशिप की मेजबानी कौन सा देश करेगा?</p>",
                    options_en: ["<p>China</p>", "<p>USA</p>", 
                                "<p>Japan</p>", "<p>Croatia</p>"],
                    options_hi: ["<p>चीन</p>", "<p>यूएसए</p>",
                                "<p>जापान</p>", "<p>क्रोएशिया</p>"],
                    solution_en: "<p>44.(c) <strong>Japan</strong> Tokyo, the capital city of Japan will host the 2025 World Athletics Championships. 2022&rarr; Eugene (United States). 2023 &rarr; Budapest (Hungary).</p>",
                    solution_hi: "<p>44.(c) <strong>जापान</strong><br>जापान की राजधानी टोक्यो 2025 विश्व एथलेटिक्स चैंपियनशिप की मेजबानी करेगा। 2022 &rarr; यूजीन (अमेरिका)। 2023 &rarr; बुडापेस्ट (हंगरी)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Solids like fats, grease and oil that float on top of liquid wastewater is called ______.</p>",
                    question_hi: "<p>45. तरल अपशिष्ट जल के ऊपर तैरने वाले वसा, ग्रीस और तेल जैसे ठोस पदार्थ ______ कहलाते हैं।</p>",
                    options_en: ["<p>urea</p>", "<p>sludge</p>", 
                                "<p>compost</p>", "<p>peat</p>"],
                    options_hi: ["<p>यूरिया</p>", "<p>कीचड़</p>",
                                "<p>खाद</p>", "<p>पीट</p>"],
                    solution_en: "<p>45.(b) Solids like fats, grease and oil that float on top of liquid wastewater is called <strong>sludge</strong>. <strong>Urea,</strong> also known as carbamide, is a naturally occurring molecule that is produced by protein metabolism and found abundantly in mammalian urine. <strong>Compost </strong>is a mixture&nbsp;of ingredients used to fertilize and improve the soil. <strong>Peat</strong> is an accumulation of partially decayed vegetation or organic matter.</p>",
                    solution_hi: "<p>45.(b) तरल अपशिष्ट जल के ऊपर तैरने वाले वसा, ग्रीस और तेल जैसे ठोस पदार्थ आपंक कहलाते हैं। यूरिया, जिसे कार्बामाइड के रूप में भी जाना जाता है, एक प्राकृतिक रूप से पाया जाने वाला अणु है जो प्रोटीन चयापचय द्वारा निर्मित होता है और स्तनधारी मूत्र में प्रचुर मात्रा में पाया जाता है। खाद मिट्टी को उर्वरित करने और सुधारने के लिए उपयोग की जाने वाली सामग्री का मिश्रण है। पीट (Peat) आंशिक रूप से सड़ी हुई वनस्पति या कार्बनिक पदार्थों का संचय है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following dwarf planets lies in the main asteroid belt ?</p>",
                    question_hi: "<p>46. निम्नलिखित में से कौन सा बौना ग्रह मुख्य क्षुद्रग्रह बेल्ट में स्थित है?</p>",
                    options_en: ["<p>Eris</p>", "<p>Makemake</p>", 
                                "<p>Ceres</p>", "<p>Haumea</p>"],
                    options_hi: ["<p>एरिस (Eris)</p>", "<p>मेकमेक</p>",
                                "<p>सेरेस</p>", "<p>हाउमिया</p>"],
                    solution_en: "<p>46.(c) <strong>Ceres</strong> is a dwarf planet in the asteroid belt between the orbits of Mars and Jupiter. <strong>Eris</strong> is the most massive and second-largest known dwarf planet in the Solar System. <strong>Makemake</strong> is a dwarf planet and perhaps the second-largest Kuiper belt object in the classical population, with a diameter approximately two-thirds that of Pluto. <strong>Haumea</strong> is a dwarf planet located beyond Neptune\'s orbit.</p>",
                    solution_hi: "<p>46.(c) <strong>सेरेस</strong> मंगल और बृहस्पति की कक्षाओं के बीच क्षुद्रग्रह बेल्ट में एक बौना ग्रह है। <strong>एरिस</strong> सौर मंडल में सबसे बड़े पैमाने पर और&nbsp;दूसरा सबसे बड़ा ज्ञात बौना ग्रह है। <strong>मेकमेक</strong> एक बौना ग्रह है और शायद शास्त्रीय आबादी में दूसरा सबसे बड़ा कुइपर बेल्ट ऑब्जेक्ट है, जिसका व्यास प्लूटो के व्यास के लगभग दो-तिहाई है।&nbsp;<strong>हौमिया</strong> नेप्च्यून की कक्षा से परे स्थित एक बौना ग्रह है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. ______ was an important port city in ancient India.</p>",
                    question_hi: "<p>47. ________ प्राचीन भारत का एक महत्वपूर्ण बंदरगाह शहर था।</p>",
                    options_en: ["<p>Tamralipti</p>", "<p>Shravasti</p>", 
                                "<p>Ahichhatra</p>", "<p>Champa</p>"],
                    options_hi: ["<p>ताम्रलिप्त</p>", "<p>श्रावस्ती</p>",
                                "<p>अहिछत्र</p>", "<p>चंपा</p>"],
                    solution_en: "<p>47.(a) <strong>Tamralipti</strong> was a port city and capital of Suhma Kingdom in ancient Bengal, located on the coast of the Bay of Bengal. <strong>Shravasti</strong> was the capital of Kosala kingdom in ancient India and the place where the Buddha lived most after his enlightenment. <strong>Ahichchhatra,</strong> near the modern Ramnagar village in Aonla tehsil, Bareilly district in Uttar Pradesh. <strong>Champa</strong> was city of ancient India, the capital of the kingdom of Anga.</p>",
                    solution_hi: "<p>47.(a) <strong>ताम्रलिप्ति</strong> बंगाल की खाड़ी के तट पर स्थित प्राचीन बंगाल में एक बंदरगाह शहर और सुहमा साम्राज्य की राजधानी थी। <strong>श्रावस्ती </strong>प्राचीन भारत में कोसल साम्राज्य की राजधानी थी और वह स्थान जहां बुद्ध अपने ज्ञान प्राप्ति के बाद सबसे अधिक रहते थे। <strong>अहिच्छत्र</strong> उत्तर प्रदेश में बरेली जिले के आंवला तहसील के आधुनिक रामनगर गांव के पास स्थित है। प्राचीन भारत का <strong>चंपा</strong> शहर अंग राज्य की राजधानी है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Usually, which year of a series of years in an economic index is called the base year?</p>",
                    question_hi: "<p>48. आमतौर पर किसी आर्थिक सूचकांक में वर्षों की श्रृंखला के किस वर्ष को आधार वर्ष कहा जाता है?</p>",
                    options_en: ["<p>First year</p>", "<p>Twentieth year</p>", 
                                "<p>Tenth year</p>", "<p>Last year</p>"],
                    options_hi: ["<p>प्रथम वर्ष</p>", "<p>बीसवां वर्ष</p>",
                                "<p>दसवां वर्ष</p>", "<p>पिछले साल</p>"],
                    solution_en: "<p>48.(a) A base year is the first of a series of years in an economic or financial index. The index value of the base year is conventionally set to equal 100. The present base year for the gross domestic product is 2011-12.</p>",
                    solution_hi: "<p>48.(a) आधार वर्ष किसी आर्थिक या वित्तीय सूचकांक में वर्षों की श्रृंखला का पहला वर्ष होता है। आधार वर्ष का सूचकांक मूल्य (index value) पारंपरिक रूप से 100 के बराबर होता है। सकल घरेलू उत्पाद का वर्तमान आधार वर्ष 2011-12 है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. What do you call the process of change in the base pair sequence of an organism?</p>",
                    question_hi: "<p>49. किसी जीव के आधार युग्म अनुक्रम में परिवर्तन की प्रक्रिया को आप क्या कहते हैं?</p>",
                    options_en: ["<p>Pollination</p>", "<p>Decantation</p>", 
                                "<p>Mutation</p>", "<p>Ionisation</p>"],
                    options_hi: ["<p>परागण</p>", "<p>निस्तारण</p>",
                                "<p>उत्परिवर्तन</p>", "<p>आयनीकरण</p>"],
                    solution_en: "<p>49.(c) A <strong>mutation</strong> is a change in the DNA sequence of an organism. It can result from errors in DNA replication during cell division, exposure to mutagens or a viral infection. <strong>Pollination</strong> is a method where pollen grains are picked from an anther, which is the male part of a flower and transferred to the flower\'s female part called the stigma. <strong>Decantation</strong> is a process for the separation of mixtures of immiscible liquids or of a liquid and a solid mixture such as a suspension. <strong>Ionization</strong> is the process in which an atom/molecule acquires a positive/negative charge by losing or gaining electrons.</p>",
                    solution_hi: "<p>49.(c) एक <strong>उत्परिवर्तन</strong> एक जीव के डीएनए अनुक्रम में परिवर्तन है। यह कोशिका विभाजन के दौरान DNA प्रतिकृति में त्रुटियों, उत्परिवर्तजनों के संपर्क में आने या वायरल संक्रमण के परिणामस्वरूप हो सकता है। <strong>परागण</strong> एक ऐसी विधि है जिसमें परागकणों को परागकोष से चुना जाता है, जो एक फूल का नर भाग होता है और फूल के मादा भाग में स्थानांतरित किया जाता है जिसे स्टिग्मा कहा जाता है। <strong>निस्तारण</strong> एक अमिश्रणीय तरल पदार्थ या एक तरल और एक ठोस मिश्रण जैसे निलंबन के मिश्रण को अलग करने की एक प्रक्रिया है। <strong>आयनीकरण</strong> वह प्रक्रिया है जिसमें एक परमाणु/अणु इलेक्ट्रॉनों को खोकर या प्राप्त करके धनात्मक/ऋणात्मक आवेश प्राप्त कर लेता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "50. Which phenomenon deals with the scattering of light by molecules of a medium when they are excited to vibrational energy levels? ",
                    question_hi: "50. कौन सी घटना किसी माध्यम के अणुओं द्वारा कंपन ऊर्जा स्तरों के लिए उत्तेजित होने पर प्रकाश के प्रकीर्णन से संबंधित है? ",
                    options_en: [" Huygens Effect  ", " Maxwell Effect ", 
                                " Raman Effect     ", " Rayleigh Effect"],
                    options_hi: [" हाइजेंस प्रभाव", " मैक्सवेल प्रभाव",
                                " रमन प्रभाव", " रेले प्रभाव"],
                    solution_en: "<p>50.(c) <strong>Raman Effect</strong> deals with the scattering of light by molecules of a medium when they are excited to vibrational energy levels. Huygens\' principle states that every point on a wavefront may be considered as a source of secondary waves. <strong>Maxwell Effect</strong> is a phenomenon of electromagnetic induction in which an electric charge, near a solenoid in which current changes slowly, feels an electromotive force (e.m.f.) even if the magnetic field is practically static inside and null outside. <strong>Rayleigh Effect</strong> is a phenomenon of scattering of light or other electromagnetic radiation by particles much smaller than the wavelength of the radiation.</p>",
                    solution_hi: "<p>50.(c) <strong>रमन प्रभाव</strong> एक माध्यम के अणुओं द्वारा प्रकाश के प्रकीर्णन से संबंधित है जब वे कंपन ऊर्जा स्तरों के लिए उत्साहित होते हैं। हाइजेंस का सिद्धांत कहता है कि तरंग के प्रत्येक बिंदु को द्वितीयक तरंगों का स्रोत माना जा सकता है। <strong>मैक्सवेल प्रभाव</strong> विद्युत चुम्बकीय प्रेरण की एक घटना है जिसमें एक विद्युत आवेश, एक सोलनॉइड के पास, जिसमें धारा में धीरे-धीरे परिवर्तन होता है, एक विद्युत प्रभाव बल (e.m.f.) महसूस करता है, भले ही चुंबकीय क्षेत्र व्यावहारिक रूप से अंदर और बाहर शून्य हो। <strong>रेले प्रभाव (Rayleigh Effect)</strong> विकिरण की तरंग दैर्ध्य से बहुत छोटे कणों द्वारा प्रकाश या अन्य विद्युत चुम्बकीय विकिरण के विक्षेपण की घटना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If the numerator of a fraction is increased by 12% and its denominator is diminished by 8%, then the value of the fraction is <math display=\"inline\"><mfrac><mrow><mn>56</mn></mrow><mrow><mn>69</mn></mrow></mfrac></math> .Find the original fraction.</p>",
                    question_hi: "<p>51. यदि किसी भिन्न का अंश 12% बढ़ जाता है और उसका हर 8% कम हो जाता है, तो भिन्न का मान <math display=\"inline\"><mfrac><mrow><mn>56</mn></mrow><mrow><mn>69</mn></mrow></mfrac></math> हो जाता है। मूल भिन्न ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>51.(d)<br>Let fraction = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math><br>According to question<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mfrac><mn>112</mn><mn>100</mn></mfrac></mrow><mrow><mi>y</mi><mo>&#215;</mo><mfrac><mn>92</mn><mn>100</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>69</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>112</mn></mrow><mrow><mi>y</mi><mo>&#215;</mo><mn>92</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>69</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>56</mn><mo>&#215;</mo><mn>23</mn></mrow><mrow><mn>69</mn><mo>&#215;</mo><mn>28</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>51.(d)<br>माना , भिन्न = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math><br>प्रश्न के अनुसार<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mfrac><mn>112</mn><mn>100</mn></mfrac></mrow><mrow><mi>y</mi><mo>&#215;</mo><mfrac><mn>92</mn><mn>100</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>69</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>112</mn></mrow><mrow><mi>y</mi><mo>&#215;</mo><mn>92</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><mn>69</mn></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>56</mn><mo>&#215;</mo><mn>23</mn></mrow><mrow><mn>69</mn><mo>&#215;</mo><mn>28</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. What is the maximum value of 7 cosA + 24 sinA + 32 ?</p>",
                    question_hi: "<p>52. 7 cosA + 24 sinA + 32 का अधिकतम मान क्या है?</p>",
                    options_en: ["<p>39</p>", "<p>32</p>", 
                                "<p>25</p>", "<p>57</p>"],
                    options_hi: ["<p>39</p>", "<p>32</p>",
                                "<p>25</p>", "<p>57</p>"],
                    solution_en: "<p>52.(d) <br><strong>Concept:-</strong> maximum value of Acosx + Bsinx = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">A</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">B</mi><mn>2</mn></msup></msqrt></math><br>Maximum value (7 cosA + 24 sinA) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>24</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn></msqrt></math> = 25<br>So, maximum value of 7 cosA + 24 sinA + 32 = 25 + 32 = 57</p>",
                    solution_hi: "<p>52.(d) <br><strong>संकल्पना:-</strong> Acosx + Bsinx का अधिकतम मान = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">A</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">B</mi><mn>2</mn></msup></msqrt></math><br>(7 cosA + 24 sinA) का अधिकतम मान = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>24</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn></msqrt></math> = 25<br>तो, 7 cosA + 24 sinA + 32 का अधिकतम मान = 25 + 32 = 57</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. A, B and C can complete a work in 12, 15 and 20 days, respectively. How many days are required to finish the work if they work together?</p>",
                    question_hi: "<p>53. A, B और C एक काम को क्रमशः 12, 15 और 20 दिनों में पूरा कर सकते हैं। यदि वे एक साथ मिलकर काम करते हैं तो काम को पूरा करने में उन्हे कितने दिन का समय लगेगा?</p>",
                    options_en: ["<p>4 days</p>", "<p>3 days</p>", 
                                "<p>5 days</p>", "<p>6 days</p>"],
                    options_hi: ["<p>4 दिन</p>", "<p>3 दिन</p>",
                                "<p>5 दिन</p>", "<p>6 दिन</p>"],
                    solution_en: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258423327.png\" alt=\"rId38\" width=\"252\" height=\"127\"><br>Efficiency of A, B and C = 5 + 4 + 3 = 12 units<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 5 days</p>",
                    solution_hi: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258423494.png\" alt=\"rId39\" width=\"248\" height=\"134\"><br>A, B और C की दक्षता = 5 + 4 + 3 = 12 इकाई<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 5 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The speed of a boat in still water is 12 km/h, and the speed of the stream is 3 km/h. A man goes to a place by boat, 45 km and comes back to the starting point. Find the total time taken by him.</p>",
                    question_hi: "<p>54. स्थिर जल में एक नाव की चाल 12 km/h है, और धारा की चाल 3 km/h है। एक आदमी 45 km की दूरी तय करके प्रारंभिक बिंदु पर वापस आता है। उसके द्वारा लिया गया कुल समय ज्ञात कीजिए।</p>",
                    options_en: ["<p>6 hours</p>", "<p>7 hours</p>", 
                                "<p>8 hours</p>", "<p>9 hours</p>"],
                    options_hi: ["<p>6 घंटे</p>", "<p>7 घंटे</p>",
                                "<p>8 घंटे</p>", "<p>9 घंटे</p>"],
                    solution_en: "<p>54.(c)<br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mrow><mn>12</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mrow><mn>12</mn><mo>-</mo><mn>3</mn></mrow></mfrac></math> = time<br>Time = 3 + 5 = 8 hours</p>",
                    solution_hi: "<p>54.(c)<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mrow><mn>12</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mrow><mn>12</mn><mo>-</mo><mn>3</mn></mrow></mfrac></math> = समय<br>समय = 3 + 5 = 8 घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If the diagonal of a rectangle is 16 cm long and its perimeter is 48 cm long, find the area of the rectangle.</p>",
                    question_hi: "<p>55. यदि किसी आयत का विकर्ण 16 cm लंबा है और इसका परिमाप 48 cm लंबा है, तो आयत का क्षेत्रफल ज्ञात कीजिए।</p>",
                    options_en: ["<p>148 cm&sup2;</p>", "<p>120 cm&sup2;</p>", 
                                "<p>160 cm&sup2;</p>", "<p>140 cm&sup2;</p>"],
                    options_hi: ["<p>148 cm&sup2;</p>", "<p>120 cm&sup2;</p>",
                                "<p>160 cm&sup2;</p>", "<p>140 cm&sup2;</p>"],
                    solution_en: "<p>55.(c) Diagonal of a rectangle = 16 cm<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">l</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></msqrt></math> = 16<br>On squaring both side,<br><math display=\"inline\"><mo>&#8658;</mo></math> l<sup>2</sup> + b<sup>2</sup> = 256 &hellip;&hellip;(i)<br>Perimeter of a rectangle = 48 cm<br><math display=\"inline\"><mo>&#8658;</mo></math> 2(l + b) = 48<br><math display=\"inline\"><mo>&#8658;</mo></math> (l + b) = 24<br>On squaring both side,<br><math display=\"inline\"><mo>&#8658;</mo></math> l<sup>2</sup> + b<sup>2</sup> + 2 &times; l &times; b = 576<br><math display=\"inline\"><mo>&#8658;</mo></math> 256 + 2 &times; l &times; b = 576 [from eq .(i)]<br><math display=\"inline\"><mo>&#8658;</mo></math> l &times; b =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>576</mn><mo>-</mo><mn>256</mn></mrow><mn>2</mn></mfrac></math> = 160 cm<sup>2</sup><br>Hence, Area of a rectangle is 160 cm<sup>2</sup></p>",
                    solution_hi: "<p>55.(c) आयत का विकर्ण = 16 cm<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">l</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></msqrt></math> = 16<br>दोनों पक्षों का वर्ग करने पर,<br><math display=\"inline\"><mo>&#8658;</mo></math> l<sup>2</sup> + b<sup>2</sup> = 256 &hellip;&hellip;(i)<br>एक आयत का परिमाप = 48 cm<br><math display=\"inline\"><mo>&#8658;</mo></math> 2(l + b) = 48<br><math display=\"inline\"><mo>&#8658;</mo></math> (l + b) = 24<br>दोनों पक्षों का वर्ग करने पर,<br><math display=\"inline\"><mo>&#8658;</mo></math> l<sup>2</sup> + b<sup>2</sup> + 2 &times; l &times; b = 576<br><math display=\"inline\"><mo>&#8658;</mo></math> 256 + 2 &times; l &times; b = 576 [समी.(i) से]<br><math display=\"inline\"><mo>&#8658;</mo></math> l &times; b =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>576</mn><mo>-</mo><mn>256</mn></mrow><mn>2</mn></mfrac></math> = 160 cm<sup>2</sup><br>अतः, एक आयत का क्षेत्रफल 160 cm<sup>2</sup> है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Arun and Bhaskar run a race of 3 km. First, Arun gives Bhaskar a head start of 400 m and beats him by 30 seconds. While coming back, Arun gives Bhaskar a lead of 2.5 minutes and gets beaten by 500 m. What is the difference between the times in minutes in which Arun and Bhaskar can run the race for one side separately?</p>",
                    question_hi: "<p>56. अरुण और भास्कर ने 3 km की दौड़ लगाई। अरुण भास्कर को 400 m की बढ़त देता है और उसे 30 सेकंड से हरा देता है। वापस आते समय, अरुण भास्कर को 2.5 मिनट की बढ़त देता है और 500 m से हार जाता है। मिनट में, उस समय के बीच का अंतर कितना है, जिसमें अरुण और भास्कर एक तरफ की रेस के लिए अलग-अलग दौड़ लगा सकते हैं?</p>",
                    options_en: ["<p>2 min</p>", "<p>1.5 min</p>", 
                                "<p>2.5 min</p>", "<p>3 min</p>"],
                    options_hi: ["<p>2 मिनट</p>", "<p>1.5 मिनट</p>",
                                "<p>2.5 मिनट</p>", "<p>3 मिनट</p>"],
                    solution_en: "<p>56.(b)<br><strong>Case 1:</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Arun : Bhaskar <br>Distance <math display=\"inline\"><mo>&#8594;</mo></math> 3000 : 2600 <br>Time <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;t&nbsp; &nbsp; :&nbsp; &nbsp;t + 0.5 <br>Time taken to cover 3000 m = t minutes<br>For 2500 m = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">t</mi><mn>3000</mn></mfrac></math> &times; 2500 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math> minutes<br><strong>Case 2:</strong><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Arun&nbsp; :&nbsp; Bhaskar <br>Distance <math display=\"inline\"><mo>&#8594;</mo></math> 2500&nbsp; :&nbsp; 3000 <br>Time <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math>&nbsp; : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math> + 2.5&nbsp;<br>Now speed of Bhaskar will be same for both side<br>Hence, from <strong>case 1 </strong>and <strong>case 2</strong><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2600</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3000</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></mstyle><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>15</mn></mrow></mfrac></math><br>65t&nbsp;+ 195 = 90t + 45<br>25t&nbsp;= 150<br>t = 6 minute<br>Time taken by arun to travel 3000 m = 6 minute<br>Time taken by Bhaskar to travel 3000 m = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></math> = 7.5 minute<br>Required difference = 7.5 - 6 = 1.5 minutes</p>",
                    solution_hi: "<p>56.(b)<br><strong>Case 1:</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;अरुण : भास्कर <br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 3000 : 2600 <br>समय <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; t&nbsp; &nbsp; :&nbsp; &nbsp;t + 0.5 <br>अरुण द्वारा 3000 मीटर की दूरी तय करने में लिया गया समय = <math display=\"inline\"><mi>t</mi></math> मिनट<br>2500 मीटर के लिए = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">t</mi><mn>3000</mn></mfrac></math> &times; 2500 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math> &nbsp;मिनट<br><strong>Case 2:</strong><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; अरुण&nbsp; : भास्कर <br>दूरी <math display=\"inline\"><mo>&#8594;</mo></math> 2500&nbsp; : 3000 <br>समय <math display=\"inline\"><mo>&#8594;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math>&nbsp; : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></math> + 2.5&nbsp;<br>अब भास्कर की गति दोनों तरफ के लिए समान होगी<br>अतः , <strong>Case 1</strong> और <strong>Case 2</strong> से<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2600</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3000</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac></mstyle><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mrow><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi><mo>+</mo><mn>15</mn></mrow></mfrac></math><br>65t + 195 = 90t + 45<br>25t&nbsp;= 150<br>t = 6 मिनट<br>अरुण द्वारा 3000 मीटर की दूरी तय करने में लिया गया समय = 6 मिनट<br>भास्कर को 3000 मीटर की दूरी तय करने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">t</mi></mrow><mn>6</mn></mfrac><mo>+</mo><mn>2</mn><mo>.</mo><mn>5</mn></math>&nbsp;= 7.5 मिनट<br>आवश्यक अंतर = 7.5 - 6 = 1.5 मिनट</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If the seven digit number 3x6349y is divisible by 88, then what will be the value of (2x + 3y)?</p>",
                    question_hi: "<p>57. यदि सात अंकों की एक संख्या 3x6349y , 88 से विभाज्य है, तो (2x + 3y) का मान क्या होगा ?</p>",
                    options_en: ["<p>34</p>", "<p>36</p>", 
                                "<p>28</p>", "<p>32</p>"],
                    options_hi: ["<p>34</p>", "<p>36</p>",
                                "<p>28</p>", "<p>32</p>"],
                    solution_en: "<p>57.(d)<br>Co-prime factor of 88 = (8 , 11)<br>So , 3x6349y should be divisible by both 8 and 11.<br>Divisibility by 8:- 49y should be divisible by 8.<br>Possible value of &lsquo;y&rsquo; = 6 <br>Divisibility by 11 :-&nbsp;(3 + 6 + 4 + 6) - (x + 3 + 9) = 0 or 11<br>Possible value of &lsquo;x&rsquo; = 7<br>Therefore, 2x&nbsp;+ 3y = 2 &times; 7 + 3 &times; 6 = 14 + 18 = 32</p>",
                    solution_hi: "<p>57.(d) <br>88 का सह-अभाज्य गुणनखंड = (8 , 11)<br>इसलिए , 3x6349y को 8 और 11 दोनों से विभाज्य होना चाहिए।<br>8 से विभाज्यता :- 49y को 8 से विभाज्य होना चाहिए।<br>&lsquo;y&rsquo; का संभावित मान = 6 <br>11 से विभाज्यता :-&nbsp;(3 + 6 + 4 + 6) - (x + 3 + 9) = 0 या 11 <br>&lsquo;x&rsquo; का संभावित मान = 7<br>इसलिए , 2x&nbsp;+ 3y = 2 &times; 7 + 3 &times; 6 = 14 + 18 = 32</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Raju has ₹11,000 and starts saving ₹5,000 each week towards buying a new laptop. At the same time, Ramesh has ₹60,000 and begins spending ₹2,000 per week on supplies for his art class. Will there be a week when they have the same amount of money?</p>",
                    question_hi: "<p>58. राजू के पास ₹11,000 हैं और वह नया लैपटॉप खरीदने के लिए प्रति सप्ताह ₹5,000 की बचत करना शुरू करता है। उसी समय, रमेश के पास ₹60,000 हैं और वह अपनी कला कक्षा के लिए सामग्री पर प्रति सप्ताह ₹2,000 खर्च करना शुरू करता है। क्या कोई ऐसा सप्ताह होगा जब उनके पास समान धनराशि होगी?</p>",
                    options_en: ["<p>Yes, after 5 weeks</p>", "<p>No, they will never have the same amount</p>", 
                                "<p>Yes, after 7 weeks</p>", "<p>Yes, after 6 weeks</p>"],
                    options_hi: ["<p>हाँ, 5 सप्ताह के बाद</p>", "<p>नहीं, उनके पास कभी भी समान धनराशि नहीं होगी</p>",
                                "<p>हाँ, 7 सप्ताह के बाद</p>", "<p>हाँ, 6 सप्ताह के बाद</p>"],
                    solution_en: "<p>58.(d) According to question,<br>Amount of Raju after 6 weeks = 11000 + 7 &times; 5000 = ₹ 46000<br>Now, <br>Amount of Ramesh after 6 weeks = ₹60000 - 7 &times; 2000 = ₹ 46000</p>",
                    solution_hi: "<p>58.(d) प्रश्न के अनुसार,<br>6 सप्ताह के बाद राजू की राशि = 11000 + 7 &times; 5000 = ₹ 46000<br>अब, <br>6 सप्ताह के बाद रमेश की राशि = ₹ 60000 - 7 &times; 2000 = ₹ 46000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The average of 9 numbers is 3. If the average of the first 8 of these numbers is 2, the 9th number is:</p>",
                    question_hi: "<p>59. 9 संख्याओं का औसत 3 है। यदि इनमें से प्रथम 8 संख्याओं का औसत 2 है, तो 9वीं संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>11</p>", "<p>12</p>", 
                                "<p>14</p>", "<p>15</p>"],
                    options_hi: ["<p>11</p>", "<p>12</p>",
                                "<p>14</p>", "<p>15</p>"],
                    solution_en: "<p>59.(a)<br>9th no. = (9 &times; 3 - 8 &times; 2) = 11</p>",
                    solution_hi: "<p>59.(a)<br>9वीं संख्या = (9 &times; 3 - 8 &times; 2) = 11</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The sum of two numbers is 76. Three times the larger number is 46 more than four&nbsp;times the smaller one. Find the two numbers.</p>",
                    question_hi: "<p>60. दो संख्याओं का योग 76 है। बड़ी संख्या का तीन गुना, छोटी संख्या के चार गुने से 46 अधिक है। दोनों&nbsp;संख्याएँ ज्ञात कीजिए।</p>",
                    options_en: ["<p>44 and 32</p>", "<p>50 and 26</p>", 
                                "<p>40 and 36</p>", "<p>35 and 41</p>"],
                    options_hi: ["<p>44 और 32</p>", "<p>50 और 26</p>",
                                "<p>40 और 36</p>", "<p>35 और 41</p>"],
                    solution_en: "<p>60.(b) <br>Let the larger no and smaller no be x and y respectively<br>x + y = 76 ----------- (1)<br>3x - 4y = 46 ----------- (2)<br>Solving eqn (1) &amp; (2) we have ;<br>x = 50, y = 26</p>",
                    solution_hi: "<p>60.(b) <br>माना कि बड़ी संख्या और छोटी संख्या क्रमशः x और y हैं<br>x + y = 76 ----------- (1)<br>3x - 4y = 46 ----------- (2)<br>समीकरण (1) और (2) को हल करने पर हमे प्राप्त होता है<br>x = 50, y = 26</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The average of the present ages of P and Q is 56 years and their ages are in the ratio 5 : 3. What will be the age of Q seven years hence?</p>",
                    question_hi: "<p>61. P और Q की वर्तमान आयु का औसत 56 वर्ष है और उनकी आयु का अनुपात 5 : 3 है। सात वर्ष बाद Q की आयु क्या होगी ?</p>",
                    options_en: ["<p>54 years</p>", "<p>42 years</p>", 
                                "<p>52 years</p>", "<p>49 years</p>"],
                    options_hi: ["<p>54 वर्ष</p>", "<p>42 वर्ष</p>",
                                "<p>52 वर्ष</p>", "<p>49 वर्ष</p>"],
                    solution_en: "<p>61.(d)<br>Age of (P + Q) = 56 &times; 2 = 112<br>(5 + 3)unit = 112 year<br>8 unit = 112&nbsp;<math display=\"inline\"><mo>&#8658;</mo></math> 1 unit = 14 year<br>So, age of Q (3unit) = 14 &times; 3 = 42 years <br>Age of Q, 7year hence = 42 + 7 = 49 years</p>",
                    solution_hi: "<p>61.(d)<br>(P + Q) की आयु = 56 &times; 2 = 112<br>(5 + 3) इकाई = 112 वर्ष&nbsp;<br>8 यूनिट = 112 <math display=\"inline\"><mo>&#8658;</mo></math> 1 यूनिट= 14 वर्ष <br>तो, Q की आयु(3 यूनिट) = 14 &times; 3 = 42 वर्ष <br>Q की आयु, 7 <br>Q की आयु, 7 वर्ष बाद = 42 + 7 = 49 वर्ष</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. A trader marks his goods at 20% higher than the cost price. Further, he uses a weighing balance which shows 1200 grams for a kilogram. What is his profit percentage?</p>",
                    question_hi: "<p>62. एक व्यापारी अपनी वस्तुओं पर क्रय मूल्य से 20% अधिक मूल्&zwj;य अंकित करता है। इसके अलावा, वह एक ऐसे वजन करने वाले तुला का उपयोग करता है जो एक किलोग्राम के लिए 1200 ग्राम दिखाता है। उसका लाभ प्रतिशत क्या है?</p>",
                    options_en: ["<p>66%</p>", "<p>50%</p>", 
                                "<p>44%</p>", "<p>55%</p>"],
                    options_hi: ["<p>66%</p>", "<p>50%</p>",
                                "<p>44%</p>", "<p>55%</p>"],
                    solution_en: "<p>62.(c) According to question<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;CP : SP<br>Percentage <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 5&nbsp; :&nbsp; 6<br>Weight <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 1000 : 1200<br>_________________<br>Final ratio <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;25 : 36<br>Required profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>-</mo><mn>25</mn></mrow><mrow><mn>25</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 = 44%</p>",
                    solution_hi: "<p>62.(c) प्रश्न के अनुसार,<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;क्रय मूल : विक्रय मूल <br>प्रतिशत <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 6<br>भार&nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp;1000 : 1200<br>_________________<br>अंतिम अनुपात <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 25 : 36<br>आवश्यक लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>-</mo><mn>25</mn></mrow><mrow><mn>25</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 = 44%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. A train travelling at a speed of 60 km/h leaves station A at 7 : 00 a.m. and another train travelling at a speed of 84 km/h starts from station A at 9 : 30 a.m. in the same direction on a parallel track. At what distance (in km) from station A and after how many hours of travel of the second train will they be together ? Express your answer in the same order.</p>",
                    question_hi: "<p>63. 60 km/h की चाल से चल रही एक रेलगाड़ी स्टेशन A से 7 : 00 a.m पर निकलती है और 84 km/h की चाल से चल रही एक दूसरी रेलगाड़ी स्टेशन A से 9 : 30 a.m पर उसी दिशा में समानांतर पटरियों पर चलना शुरू करती है। स्टेशन A से कितनी दूरी (km में) पर और दूसरी रेलगाड़ी के कितने घंटे की यात्रा के बाद वे दोनों रेलगाड़ियां एक साथ होंगी? अपना उत्तर इसी क्रम में व्यक्त कीजिए।</p>",
                    options_en: ["<p>693, 8.25</p>", "<p>525, 6.25</p>", 
                                "<p>375, 6.25</p>", "<p>495, 8.25</p>"],
                    options_hi: ["<p>693, 8.25</p>", "<p>525, 6.25</p>",
                                "<p>375, 6.25</p>", "<p>495, 8.25</p>"],
                    solution_en: "<p>63.(b) Speed of the first train = 60 km/h<br>Speed of the second train = 84 km/h<br>According to question,<br>Relative speed = 84 - 60 = 24 km/h<br>The first train has traveled for 2.5 hours before the second train starts, <br>So, Distance = 60 &times; 2.5 = 150 km<br>Time taken by the second train to cover the distance,<br>Time = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 6.25 hours<br>Now,<br>Distance from station A = 84 &times; 6.25 = 525 km</p>",
                    solution_hi: "<p>63.(b) पहली ट्रेन की गति = 60 किमी/घंटा<br>दूसरी ट्रेन की गति = 84 किमी/घंटा<br>प्रश्न के अनुसार,<br>सापेक्ष गति = 84 - 60 = 24 किमी/घंटा<br>दूसरी ट्रेन शुरू होने से पहले पहली ट्रेन 2.5 घंटे की यात्रा कर चुकी होती है, <br>तो, दूरी = 60 &times; 2.5 = 150 किमी<br>दूरी तय करने में दूसरी ट्रेन द्वारा लिया गया समय,<br>समय = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 6.25 घंटे<br>अब,<br>स्टेशन A से दूरी = 84 &times; 6.25 = 525 किमी</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Three red lights on the same road start blinking at the same time and blink repeatedly after every 4, 6 and 8 minutes, respectively. How many times will they blink together in an interval of 3 hours, excluding blinking together at the starting moment?</p>",
                    question_hi: "<p>64. एक ही सड़क पर तीन लाल बत्तियाँ एक ही समय पर टिमटिमाने (blink) लगती हैं और क्रमशः प्रत्येक 4, 6 और 8 मिनट के बाद बार-बार टिमटिमाती हैं। शुरुआती क्षण में एक साथ टिमटिमाने को छोड़कर, वे 3 घंटे के अंतराल में कितनी बार एक साथ टिमटिमाएंगी?</p>",
                    options_en: ["<p>5</p>", "<p>7</p>", 
                                "<p>6</p>", "<p>4</p>"],
                    options_hi: ["<p>5</p>", "<p>7</p>",
                                "<p>6</p>", "<p>4</p>"],
                    solution_en: "<p>64.(b)<br>LCM (4,6,8) = 24<br>It means that in every 24 minute red light blinks<br>Now, 3 hours = 180 minute<br>So, in the interval of 3 hours red light blinks = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 7 times<br>(consider integer value only)</p>",
                    solution_hi: "<p>64.(b)<br>LCM (4,6,8) = 24<br>इसका मतलब है कि हर 24 मिनट में लाल बत्ती टिमटिमाती है<br>अब, 3 घंटे = 180 मिनट<br>अतः 3 घंटे के अंतराल में लाल बत्ती टिमटिमाती है = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 7 बार <br>(केवल पूर्णांक मान पर विचार करें)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Find the fourth proportional to 2, 6, 8.</p>",
                    question_hi: "<p>65. 2, 6, 8 का चतुर्थ समानुपाती ज्ञात कीजिए।</p>",
                    options_en: ["<p>36</p>", "<p>24</p>", 
                                "<p>12</p>", "<p>48</p>"],
                    options_hi: ["<p>36</p>", "<p>24</p>",
                                "<p>12</p>", "<p>48</p>"],
                    solution_en: "<p>65.(b) <br>Fourth proportional = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>bc</mi><mi mathvariant=\"normal\">a</mi></mfrac></math><br>Fourth proportional of 2, 6, 8 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>2</mn></mfrac></math> = 24</p>",
                    solution_hi: "<p>65.(b) <br>चतुर्थानुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>bc</mi><mi mathvariant=\"normal\">a</mi></mfrac></math><br>2, 6, 8 का चतुर्थानुपात= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>2</mn></mfrac></math>&nbsp;= 24</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. The distance between the centres of two circles of radii 4 cm and 2 cm is 10 cm. The length (in cm) of a transverse common tangent is:</p>",
                    question_hi: "<p>66. 4 cm और 2 cm त्रिज्या वाले दो वृत्तों के केंद्रों के बीच की दूरी 10 cm है। अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा (transverse common tangent) की लंबाई (cm में) कितनी है?</p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>11</p>", "<p>13</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>11</p>", "<p>13</p>"],
                    solution_en: "<p>66.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258423615.png\" alt=\"rId40\" width=\"385\" height=\"202\"><br>Length of transverse common tangent (PQ) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><mo>(</mo><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><msup><mo>)</mo><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>10</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>4</mn><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn><mo>-</mo><mn>36</mn></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> <br>= 8 cm</p>",
                    solution_hi: "<p>66.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258423615.png\" alt=\"rId40\" width=\"385\" height=\"202\"><br>अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा की लंबाई (PQ) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>-</mo><mo>(</mo><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><msup><mo>)</mo><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>10</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>4</mn><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn><mo>-</mo><mn>36</mn></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn></msqrt></math> <br>= 8 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A car&rsquo;s radiator should contain a solution of 52% antifreeze. A motorist has 8 litres of 40% antifreeze. How many litres (rounded off to 2 decimal places) of 95% antifreeze liquid should he add to his solution to produce a 52% antifreeze?</p>",
                    question_hi: "<p>67. एक कार के रेडिएटर में 52% एंटीफ्रीज का घोल होना चाहिए। एक मोटर चालक के पास 40% एंटीफ्रीज वाला 8 लीटर घोल है। 52% एंटीफ्रीज बनाने लिए उसे अपने घोल में 95% एंटीफ्रीज तरल की कितनी लीटर मात्रा मिलानी होगी (दो दशमलव स्थान तक पूर्णांकित)?</p>",
                    options_en: ["<p>1.92</p>", "<p>3.45</p>", 
                                "<p>4.26</p>", "<p>2.23</p>"],
                    options_hi: ["<p>1.92</p>", "<p>3.45</p>",
                                "<p>4.26</p>", "<p>2.23</p>"],
                    solution_en: "<p>67.(d) By using allegation method <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258423752.png\" alt=\"rId41\" width=\"249\" height=\"108\"><br><math display=\"inline\"><mo>&#8658;</mo></math> 43 Unit = 8liters<br><math display=\"inline\"><mo>&#8658;</mo></math>12 Unit =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>43</mn></mfrac></math> &times; 12 = 2.23</p>",
                    solution_hi: "<p>67.(d) एलिगेशन विधि का उपयोग करके <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744258423931.png\" alt=\"rId42\" width=\"200\"><br><math display=\"inline\"><mo>&#8658;</mo></math> 43 इकाई = 8लीटर <br><math display=\"inline\"><mo>&#8658;</mo></math>12 इकाई =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>43</mn></mfrac></math> &times; 12 = 2.23</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. If x is an integer such that x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math>, then find the value of x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>.</p>",
                    question_hi: "<p>68. यदि x इस प्रकार एक पूर्णांक है कि x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math> है, तो x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>68.(b) x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mfrac><mrow><mn>65</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mfrac><mrow><mn>4225</mn></mrow><mrow><mn>64</mn></mrow></mfrac><mo>-</mo><mn>4</mn></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>4225</mn><mo>-</mo><mn>256</mn></mrow><mn>64</mn></mfrac></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>3969</mn><mn>64</mn></mfrac></msqrt></math> = <math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>68.(b) x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mfrac><mrow><mn>65</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mfrac><mrow><mn>4225</mn></mrow><mrow><mn>64</mn></mrow></mfrac><mo>-</mo><mn>4</mn></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>4225</mn><mo>-</mo><mn>256</mn></mrow><mn>64</mn></mfrac></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>3969</mn><mn>64</mn></mfrac></msqrt></math> = <math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The compound interest received on Rs 24000 for 2 years (compounded half yearly) is Rs 25766.4. What is the annual interest rate?</p>",
                    question_hi: "<p>69. 2 वर्ष के लिए 24000 रुपए पर प्राप्त चक्रवृद्धि ब्याज (अर्धवार्षिक रूप से संयोजित) 25766.4 रुपए है। वार्षिक ब्याज दर कितनी है?</p>",
                    options_en: ["<p>60 percent</p>", "<p>20 percent</p>", 
                                "<p>40 percent</p>", "<p>55 percent</p>"],
                    options_hi: ["<p>60 प्रतिशत</p>", "<p>20 प्रतिशत</p>",
                                "<p>40 प्रतिशत</p>", "<p>55 प्रतिशत</p>"],
                    solution_en: "<p>69.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">P</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>49766</mn><mo>.</mo><mn>4</mn></mrow><mn>24000</mn></mfrac></math><br>Multiplying factor of one year = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mfrac><mrow><mn>49766</mn><mo>.</mo><mn>4</mn></mrow><mn>24000</mn></mfrac><mn>4</mn></mroot></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>10</mn></mfrac></math> <br>Rate = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; 100 = 20% <br>Therefore annually rate = 20 &times;&nbsp;2 = 40 %</p>",
                    solution_hi: "<p>69.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2352;&#2366;&#2358;&#2367;</mi><mi>&#160;</mi></mrow><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>49766</mn><mo>.</mo><mn>4</mn></mrow><mn>24000</mn></mfrac></math><br>एक वर्ष का गुणन कारक(multiplying factor) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mfrac><mrow><mn>49766</mn><mo>.</mo><mn>4</mn></mrow><mn>24000</mn></mfrac><mn>4</mn></mroot></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>10</mn></mfrac></math><br>दर = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; 100 = 20% <br>अतः वार्षिक दर = 20 &times; 2 = 40 %</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi><mo>+</mo><mi mathvariant=\"normal\">&#946;</mi></math> = 45&deg; and (tan &alpha; + 1) (tan &beta; + 1) = 2x, then x is:</p>",
                    question_hi: "<p>70. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi><mo>+</mo><mi mathvariant=\"normal\">&#946;</mi></math> = 45&deg; और (tan &alpha; + 1) (tan&beta;+ 1) = 2x है, तो x का मान क्या है?</p>",
                    options_en: ["<p>- 1</p>", "<p>0</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>- 1</p>", "<p>0</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>70.(c) <br>tan(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi><mo>+</mo><mi mathvariant=\"normal\">&#946;</mi></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan&#945;</mi><mo>+</mo><mi>tan&#946;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>tan&#945;</mi><mo>.</mo><mi>tan&#946;</mi></mrow></mfrac></math><br>Given that <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi><mo>+</mo><mi mathvariant=\"normal\">&#946;</mi></math>&nbsp;= 45&deg;<br>Then, tan 45&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan&#945;</mi><mo>+</mo><mi>tan&#946;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>tan&#945;</mi><mo>.</mo><mi>tan&#946;</mi></mrow></mfrac></math> = 1<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mtext>tan&#945;+tan&#946;</mtext></math> = 1 - tan&alpha;.tan&beta;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mtext>tan&#945;+tan&#946;</mtext></math> + tan&alpha;.tan&beta; = 1 --------(i)<br>Now, (tan <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi><mo>+</mo><mn>1</mn></math>) (tan&beta; + 1) = 2x<br><math display=\"inline\"><mo>&#8658;</mo></math> tan&alpha; + tan&beta; + tan&alpha; . tan&beta; + 1 = 2x<br>Putting the value from equation (i) we get;<br>1 + 1 = 2x <math display=\"inline\"><mo>&#8658;</mo></math> x = 1</p>",
                    solution_hi: "<p>70.(c) <br>tan(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi><mo>+</mo><mi mathvariant=\"normal\">&#946;</mi></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan&#945;</mi><mo>+</mo><mi>tan&#946;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>tan&#945;</mi><mo>.</mo><mi>tan&#946;</mi></mrow></mfrac></math><br>दिया गया है <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi><mo>+</mo><mi mathvariant=\"normal\">&#946;</mi></math> = 45&deg;<br>फिर, tan 45&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan&#945;</mi><mo>+</mo><mi>tan&#946;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>tan&#945;</mi><mo>.</mo><mi>tan&#946;</mi></mrow></mfrac></math> = 1<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mtext>tan&#945;+tan&#946;</mtext></math> = 1 - tan&alpha;.tan&beta;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mtext>tan&#945;+tan&#946;</mtext></math> + tan&alpha; . tan&beta; = 1 --------(i)<br>अब, (tan <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#945;</mi><mo>+</mo><mn>1</mn></math>) (tan&beta; + 1) = 2x<br><math display=\"inline\"><mo>&#8658;</mo></math> tan&alpha; + tan&beta; + tan&alpha; . tan&beta; + 1 = 2x<br>समीकरण (i) से मान रखने पर हमें प्राप्त होता है;<br>1 + 1 = 2x <math display=\"inline\"><mo>&#8658;</mo></math> x = 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. If the side of a cube is 3,which of the following options is equal to the ratio between the surface area and the volume of the cube ?</p>",
                    question_hi: "<p>71. यदि किसी घन की भुजा 3 है, तो निम्लिखित में से कौन सा विकल्प उस घन के पृष्ठीय क्षेत्रफल और आयतन के बीच अनुपात के बराबर है ?</p>",
                    options_en: ["<p>2 : 3</p>", "<p>2 : 1</p>", 
                                "<p>2 : 5</p>", "<p>2 : 7</p>"],
                    options_hi: ["<p>2 : 3</p>", "<p>2 : 1</p>",
                                "<p>2 : 5</p>", "<p>2 : 7</p>"],
                    solution_en: "<p>71.(b)<br>Ratio of surface area and volume of cube = 6a&sup2; : a&sup3;<br>= 6(3)&sup2; : (3)&sup3;&nbsp; = 2 : 1</p>",
                    solution_hi: "<p>71.(b)<br>घन के पृष्ठीय क्षेत्रफल और आयतन का अनुपात = 6a&sup2; : a&sup3;<br>= 6(3)&sup2; : (3)&sup3;&nbsp; = 2 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A coffee maker with marked price ₹4,000 is available on successive discounts of 15%, 10% and 5%. Find the selling price of the coffee maker.</p>",
                    question_hi: "<p>72. ₹4,000 अंकित मूल्य वाला एक कॉफी मेकर 15%, 10% और 5% की क्रमिक छूट पर उपलब्ध है। कॉफी मेकर का विक्रय मूल्य ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹2,905</p>", "<p>₹2,907</p>", 
                                "<p>₹2,900</p>", "<p>₹2,903</p>"],
                    options_hi: ["<p>₹2,905</p>", "<p>₹2,907</p>",
                                "<p>₹2,900</p>", "<p>₹2,903</p>"],
                    solution_en: "<p>72.(b)<br>Selling price of coffee maker = 4000 &times; <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math><br>= ₹ 2907</p>",
                    solution_hi: "<p>72.(b)<br>कॉफ़ी मेकर का विक्रय मूल्य = 4000 &times; <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>20</mn></mfrac></math><br>= ₹ 2907</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. If 6,18, x are sides of a triangle and x is an integer, then the number of possible values&nbsp;of x is:</p>",
                    question_hi: "<p>73. यदि 6,18, x एक त्रिभुज की भुजाएँ हैं और x एक पूर्णांक है, तो x के संभावित मानों की संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>14</p>", "<p>15</p>", 
                                "<p>13</p>", "<p>11</p>"],
                    options_hi: ["<p>14</p>", "<p>15</p>",
                                "<p>13</p>", "<p>11</p>"],
                    solution_en: "<p>73.(d)<br><strong>Concept</strong> :- sum of two sides of triangle &gt; third side &gt; difference of two sides of triangle <br>According to the question,<br>(6 + 18) &gt; x &gt; (18 - 6)<br>24 &gt; x &gt; 12<br>So, possible value of third side (x) = 13 , 14 , 15 , 16 , 17 , 18 , 19 , 20 , 21 , 22 , 23 = 11 values</p>",
                    solution_hi: "<p>73.(d)<br><strong>संकल्पना</strong> :- त्रिभुज की दो भुजाओं का योग &gt; तीसरी भुजा &gt; त्रिभुज की दो भुजाओं का अंतर <br>प्रश्न के अनुसार,<br>(6 + 18) &gt; x &gt; (18 - 6)<br>24 &gt; x &gt; 12<br>तो, तीसरी भुजा (x) का संभावित मान (x) = 13 , 14 , 15 , 16 , 17 , 18 , 19 , 20 , 21 , 22 , 23 = 11 मान</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. What is the amount (in ₹) of debt that will be discharged in 6 equal installments of ₹800 each, if the debt is due in 6 years at 5% per annum ?</p>",
                    question_hi: "<p>74. ऋण की वह धनराशि (₹ में) कितनी है जो प्रत्येक ₹800 की 6 समान किस्तों में चुकाई जाएगी, यदि ऋण 5% वार्षिक की दर से 6 वर्षों में देय हो ?</p>",
                    options_en: ["<p>6,600</p>", "<p>7,500</p>", 
                                "<p>8,000</p>", "<p>5,400</p>"],
                    options_hi: ["<p>6,600</p>", "<p>7,500</p>",
                                "<p>8,000</p>", "<p>5,400</p>"],
                    solution_en: "<p>74.(d)<br>Installment = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mn>100</mn><mi mathvariant=\"normal\">t</mi><mo>+</mo><mfrac><mrow><mi>rt</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></mrow></mfrac></math><br>800 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>6</mn><mo>+</mo><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>6</mn><mo>(</mo><mn>6</mn><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></mrow></mfrac></math><br>8 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">A</mi><mrow><mn>600</mn><mo>+</mo><mn>75</mn></mrow></mfrac></math> &rArr; A = 5400 ₹</p>",
                    solution_hi: "<p>74.(d) किश्त = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mn>100</mn><mi mathvariant=\"normal\">t</mi><mo>+</mo><mfrac><mrow><mi>rt</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></mrow></mfrac></math><br>800 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>6</mn><mo>+</mo><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>6</mn><mo>(</mo><mn>6</mn><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></mrow></mfrac></math><br>8 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">A</mi><mrow><mn>600</mn><mo>+</mo><mn>75</mn></mrow></mfrac></math> &rArr; A = 5400 ₹</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. What is the LCM of a&sup3;b - ab&sup3;, a&sup3;b&sup2; - a&sup2;b&sup3;, ab(a - b) ?</p>",
                    question_hi: "<p>75. a&sup3;b - ab&sup3;, a&sup3;b&sup2; - a&sup2;b&sup3;, ab(a - b) का लघुत्तम समापवर्त्य (LCM) ज्ञात करें।</p>",
                    options_en: ["<p>a&sup2;b&sup2; (a&sup2; + b&sup2;)</p>", "<p>a&sup2;b&sup2; (a&sup2; - b&sup2;)</p>", 
                                "<p>a&sup2;b&sup3; (a&sup2; + b&sup2;)</p>", "<p>a&sup3;b&sup2; (a&sup2; - b&sup2;)</p>"],
                    options_hi: ["<p>a&sup2;b&sup2; (a&sup2; + b&sup2;)</p>", "<p>a&sup2;b&sup2; (a&sup2; - b&sup2;)</p>",
                                "<p>a&sup2;b&sup3; (a&sup2; + b&sup2;)</p>", "<p>a&sup3;b&sup2; (a&sup2; - b&sup2;)</p>"],
                    solution_en: "<p>75.(b)<br>LCM of a&sup3;b - ab&sup3;, a&sup3;b&sup2; - a&sup2;b&sup3;, ab(a - b)<br>a&sup3;b - ab&sup3; =&nbsp; ab(a&sup2; - b&sup2;)<br>a&sup3;b&sup2; - a&sup2;b&sup3; = a&sup2;b&sup2;(a - b)<br>ab (a - b) = ab(a - b)<br>Hence, L.C.M = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></math></p>",
                    solution_hi: "<p>75.(b) a&sup3;b - ab&sup3;, a&sup3;b&sup2; - a&sup2;b&sup3;, ab(a - b) का लघुतम समापवर्त्य<br>a&sup3;b - ab&sup3; =&nbsp; ab(a&sup2; - b&sup2;)<br>a&sup3;b&sup2; - a&sup2;b&sup3; = a&sup2;b&sup2;(a - b)<br>ab (a - b) = ab(a - b)<br>अत: लघुतम समापवर्त्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>(</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mo>)</mo></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Parts of the following sentence have been given as options. Select the option that contains an error. <br>We were driving home when suddenly a deer runs across the road.</p>",
                    question_hi: "<p>76. Parts of the following sentence have been given as options. Select the option that contains an error. <br>We were driving home when suddenly a deer runs across the road.</p>",
                    options_en: ["<p>a deer runs across the road</p>", "<p>home</p>", 
                                "<p>We were driving</p>", "<p>when suddenly</p>"],
                    options_hi: ["<p>a deer runs across the road</p>", "<p>home</p>",
                                "<p>We were driving</p>", "<p>when suddenly</p>"],
                    solution_en: "<p>76.(a) a deer runs across the road<br>The given sentence is in the past tense, therefore, the past form(V<sub>2</sub>) of the verb will be used. Hence, &lsquo;a deer ran (V<sub>2</sub>) across the road&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(a) a deer runs across the road<br>दिया गया sentence past tense में है, इसलिए verb की past form(V<sub>2</sub>) प्रयोग किया जाएगा। इसलिए, &lsquo;a deer ran (V<sub>2</sub>) across the road&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate synonym of the given word.<br>Competent</p>",
                    question_hi: "<p>77. Select the most appropriate synonym of the given word.<br>Competent</p>",
                    options_en: ["<p>Dumb</p>", "<p>Unskilled</p>", 
                                "<p>Chatty</p>", "<p>Qualified</p>"],
                    options_hi: ["<p>Dumb</p>", "<p>Unskilled</p>",
                                "<p>Chatty</p>", "<p>Qualified</p>"],
                    solution_en: "<p>77.(d) <strong>Qualified-</strong> having the required abilities to perform a particular job.<br><strong>Competent-</strong> having the necessary ability to do something.<br><strong>Dumb-</strong> stupid or foolish.<br><strong>Unskilled-</strong> lacking necessary skills for a particular job<br><strong>Chatty-</strong> someone who talks a lot in a friendly or informal way.</p>",
                    solution_hi: "<p>77.(d) <strong>Qualified</strong> (योग्य) - having the required abilities to perform a particular job.<br><strong>Competent</strong> (सक्षम) - having the necessary ability to do something.<br><strong>Dumb</strong> (गूंगा) - stupid or foolish.<br><strong>Unskilled </strong>(अकुशल) - lacking necessary skills for a particular job<br><strong>Chatty</strong> (बातूनी) - someone who talks a lot in a friendly or informal way.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>What exactly have the <span style=\"text-decoration: underline;\">teachers being doing</span> wrong ?</p>",
                    question_hi: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>What exactly have the <span style=\"text-decoration: underline;\">teachers being doing</span> wrong ?</p>",
                    options_en: ["<p>teachers have been doing</p>", "<p>teachers are doing</p>", 
                                "<p>teachers been doing</p>", "<p>teachers being done</p>"],
                    options_hi: ["<p>teachers have been doing</p>", "<p>teachers are doing</p>",
                                "<p>teachers been doing</p>", "<p>teachers being done</p>"],
                    solution_en: "<p>78.(c) teachers been doing<br>&lsquo;Have been + V<sub>ing</sub> is the correct grammatical structure for the sentence which is in the present perfect continuous tense. Hence, &lsquo;teachers been doing(V<sub>ing</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(c) teachers been doing<br>&lsquo;Have been + V<sub>ing</sub>&rsquo; sentence के लिए सही grammatical structure है जो present perfect continuous tense में है। इसलिए, &lsquo;teachers been doing(V<sub>ing</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate meaning of the given idiom.<br>Black and white</p>",
                    question_hi: "<p>79. Select the most appropriate meaning of the given idiom.<br>Black and white</p>",
                    options_en: ["<p>In writing</p>", "<p>Doubtful</p>", 
                                "<p>In dispute</p>", "<p>A false invention</p>"],
                    options_hi: ["<p>In writing</p>", "<p>Doubtful</p>",
                                "<p>In dispute</p>", "<p>A false invention</p>"],
                    solution_en: "<p>79.(a) <strong>Black and White</strong> - in writing.<br>E.g.- Make sure you get the agreement in black and white before proceeding with the deal.</p>",
                    solution_hi: "<p>79.(a)<strong> Black and White </strong>- in writing./लिखित रूप में। <br>E.g.- Make sure you get the agreement in black and white before proceeding with the deal.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Choose the most appropriate option to change the narration (direct / indirect) of the given sentence.<br>Vipul said, &ldquo;Alas! How thoughtless I have been!&rdquo;</p>",
                    question_hi: "<p>80. Choose the most appropriate option to change the narration (direct / indirect) of the given sentence.<br>Vipul said, &ldquo;Alas! How thoughtless I have been!&rdquo;</p>",
                    options_en: ["<p>Vipul exclaimed how thoughtless I have been</p>", "<p>Vipul regretted upon my thoughtlessness.</p>", 
                                "<p>Vipul confessed with regret that he had been thoughtless.</p>", "<p>Vipul admitted that he had been thoughtless.</p>"],
                    options_hi: ["<p>Vipul exclaimed how thoughtless I have been</p>", "<p>Vipul regretted upon my thoughtlessness.</p>",
                                "<p>Vipul confessed with regret that he had been thoughtless.</p>", "<p>Vipul admitted that he had been thoughtless.</p>"],
                    solution_en: "<p>80.(c) Vipul confessed with regret that he had been thoughtless. <br>a. Vipul <strong>exclaimed how</strong> thoughtless I have been.(No connecting word)<br>b. Vipul regretted upon <strong>my</strong> thoughtlessness.(Incorrect pronoun)<br>d. Vipul <strong>admitted</strong> that he had been thoughtless.(Incorrect reporting verb)</p>",
                    solution_hi: "<p>80.(c) Vipul confessed with regret that he had been thoughtless. <br>a. Vipul <strong>exclaimed how</strong> thoughtless I have been.(कोई connecting word नहीं है)<br>b. Vipul regretted upon <strong>my</strong> thoughtlessness.(गलत सर्वनाम)<br>d. Vipul <strong>admitted</strong> that he had been thoughtless.(गलत reporting verb)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Identify the most appropriate synonym of the following word.<br>Implicate</p>",
                    question_hi: "<p>81. Identify the most appropriate synonym of the following word.<br>Implicate</p>",
                    options_en: ["<p>Accuse</p>", "<p>Interrogate</p>", 
                                "<p>Complain</p>", "<p>Confuse</p>"],
                    options_hi: ["<p>Accuse</p>", "<p>Interrogate</p>",
                                "<p>Complain</p>", "<p>Confuse</p>"],
                    solution_en: "<p>81.(a) <strong>Accuse</strong> - to say that someone has done something morally wrong, illegal, or unkind.<br><strong>Implicate</strong> - to show that someone or something is involved in something bad, especially a crime<br><strong>Interrogate</strong> - to ask somebody a lot of questions over a long period of time, especially in an aggressive way.<br><strong>Complain</strong> - a statement that you are not satisfied with something.<br><strong>Confuse</strong> - to make somebody unable to think clearly or to know what to do.</p>",
                    solution_hi: "<p>81.(a) <strong>Accuse</strong> (आरोप लगाना) - to say that someone has done something morally wrong, illegal, or unkind.<br><strong>Implicate</strong> (फंसाना) - to show that someone or something is involved in something bad, especially a crime<br><strong>Interrogate</strong> (पूछताछ करना) - to ask somebody a lot of questions over a long period of time, especially in an aggressive way.<br><strong>Complain</strong> (शिकायत करना) - a statement that you are not satisfied with something.<br><strong>Confuse</strong> (भ्रमित करना) - to make somebody unable to think clearly or to know what to do.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Who gave you the money?</p>",
                    question_hi: "<p>82. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Who gave you the money?</p>",
                    options_en: ["<p>By whom was you given the money?</p>", "<p>By whom were you given the money?</p>", 
                                "<p>Who gave the money to you?</p>", "<p>Who was given money by you?</p>"],
                    options_hi: ["<p>By whom was you given the money?</p>", "<p>By whom were you given the money?</p>",
                                "<p>Who gave the money to you?</p>", "<p>Who was given money by you?</p>"],
                    solution_en: "<p>82.(b) By whom were you given the money? <br>a. By whom <strong>was</strong> you given the money? <strong>(Incorrect word/s)</strong><br>c. Who gave the money to you? (Incorrect word/s)<br>d. Who was given money by you? (Meaning has changed)</p>",
                    solution_hi: "<p>82.(b) By whom were you given the money? <br>a. By whom <strong>was</strong> you given the money? <strong>(गलत शब्द)</strong><br>c. Who gave the money to you? (गलत शब्द)<br>d. Who was given money by you? (अर्थ बदल गया है।)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "83. Identify the INCORRECTLY spelt word in the given sentence and select the option that rectifies the spelling error. <br />Many promote bamboo planting for erosion prevention and to riverse the effects of global warming. ",
                    question_hi: "83. Identify the INCORRECTLY spelt word in the given sentence and select the option that rectifies the spelling error. <br />Many promote bamboo planting for erosion prevention and to riverse the effects of global warming. ",
                    options_en: [" Efeccts  ", " Prevension  ", 
                                " Reverse ", " Bambboo"],
                    options_hi: [" Efeccts  ", " Prevension  ",
                                " Reverse ", " Bambboo"],
                    solution_en: "83.(c) Reverse <br />‘Reverse’ is the correct spelling.",
                    solution_hi: "83.(c) Reverse <br />‘Reverse’ सही spelling है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. The meaning of which of the following options is &lsquo;Good luck&rsquo;?</p>",
                    question_hi: "<p>84. The meaning of which of the following options is &lsquo;Good luck&rsquo;?</p>",
                    options_en: ["<p>Break a leg</p>", "<p>All wet</p>", 
                                "<p>Second banana</p>", "<p>Hang in there</p>"],
                    options_hi: ["<p>Break a leg</p>", "<p>All wet</p>",
                                "<p>Second banana</p>", "<p>Hang in there</p>"],
                    solution_en: "<p>84.(a) <strong>Break a leg</strong> - good luck.</p>",
                    solution_hi: "<p>84.(a) <strong>Break a leg </strong>- good luck./शुभकामनाएँ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the word which means the same as the group of words given.<br>One who studies human societies and their culture.</p>",
                    question_hi: "<p>85. Select the word which means the same as the group of words given.<br>One who studies human societies and their culture.</p>",
                    options_en: ["<p>pathologist</p>", "<p>astrobiologist</p>", 
                                "<p>pharmacologist</p>", "<p>anthropologist</p>"],
                    options_hi: ["<p>pathologist</p>", "<p>astrobiologist</p>",
                                "<p>pharmacologist</p>", "<p>anthropologist</p>"],
                    solution_en: "<p>85.(d) <strong>anthropologist</strong> - One who studies human societies and their culture<br>(a) <strong>pathologist</strong> - a scientist who studies the causes and effects of diseases<br>(b) <strong>astrobiologist</strong> - An astrobiologist is a person who studies the possibility of life beyond Earth.<br>(c) <strong>pharmacologist-</strong> who create, develop, and test new medications.</p>",
                    solution_hi: "<p>85.(d) <strong>anthropologist</strong> - मानवविज्ञानी-&nbsp;One who studies human societies and their culture<br>(a) <strong>pathologist</strong> - रोगविज्ञानी- a scientist who studies the causes and effects of diseases<br>(b) <strong>astrobiologist</strong> - खगोल विज्ञानी-An astrobiologist is a person who studies the possibility of life beyond Earth.<br>(c) <strong>pharmacologist</strong> - औषध विज्ञानी- who create, develop, and test new medications.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate synonym of the given word.<br>Wonderful</p>",
                    question_hi: "<p>86. Select the most appropriate synonym of the given word.<br>Wonderful</p>",
                    options_en: ["<p>Synthetic</p>", "<p>Amazing</p>", 
                                "<p>Awful</p>", "<p>Upright</p>"],
                    options_hi: ["<p>Synthetic</p>", "<p>Amazing</p>",
                                "<p>Awful</p>", "<p>Upright</p>"],
                    solution_en: "<p>86.(b) <strong>Wonderful-</strong> extremely good.<br><strong>Amazing-</strong> Impressive or extraordinary.<br><strong>Synthetic-</strong> something that is made through the chemical process.<br><strong>Awful-</strong> very bad or unpleasant.<br><strong>Upright-</strong> strictly honest.</p>",
                    solution_hi: "<p>86.(b) <strong>Wonderful</strong> (अद्भुत) - extremely good.<br><strong>Amazing</strong> (आश्चर्यजनक) - Impressive or extraordinary.<br><strong>Synthetic</strong> (सिंथेटिक) - something that is made through the chemical process.<br><strong>Awful</strong> (अप्रिय) - very bad or unpleasant.<br><strong>Upright</strong> (ईमानदार) - strictly honest.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate ANTONYM of the underlined word in the sentence.<br>He laboured very hard but could not yield anything from the <span style=\"text-decoration: underline;\">barren</span> piece of land.</p>",
                    question_hi: "<p>87. Select the most appropriate ANTONYM of the underlined word in the sentence.<br>He laboured very hard but could not yield anything from the <span style=\"text-decoration: underline;\">barren</span> piece of land.</p>",
                    options_en: ["<p>Fruitless</p>", "<p>Sterile</p>", 
                                "<p>Fertile</p>", "<p>Vile</p>"],
                    options_hi: ["<p>Fruitless</p>", "<p>Sterile</p>",
                                "<p>Fertile</p>", "<p>Vile</p>"],
                    solution_en: "<p>87.(c) <strong>Fertile-</strong> producing or bearing many crops in great quantities.<br><strong>Barren-</strong> too poor to produce any vegetation.<br><strong>Fruitless-</strong> failing to achieve the desired result.<br><strong>Sterile-</strong> not able to produce children.<br><strong>Vile-</strong> extremely unpleasant.</p>",
                    solution_hi: "<p>87.(c) <strong>Fertile</strong> (उपजाऊ) - producing or bearing many crops in great quantities.<br><strong>Barren</strong> (अनुपजाऊ/बंजर) - too poor to produce any vegetation.<br><strong>Fruitless</strong> (निरर्थक) - failing to achieve the desired result.<br><strong>Sterile</strong> (बाँझ) - not able to produce children.<br><strong>Vile</strong> (अप्रिय) - extremely unpleasant.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate option to fill in the blank.<br>In both countries we have leaders of ____ ability.</p>",
                    question_hi: "<p>88. Select the most appropriate option to fill in the blank.<br>In both countries we have leaders of ____ ability.</p>",
                    options_en: ["<p>exceptional</p>", "<p>almighty</p>", 
                                "<p>powerful</p>", "<p>compelling</p>"],
                    options_hi: ["<p>exceptional</p>", "<p>almighty</p>",
                                "<p>powerful</p>", "<p>compelling</p>"],
                    solution_en: "<p>88.(a) <strong>exceptional</strong><br>&lsquo;Exceptional&rsquo; means much greater or better than usual. The given sentence states that we have leaders of exceptional ability in both countries. Hence &lsquo;exceptional&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>88.(a) <strong>exceptional</strong><br>&lsquo;Exceptional&rsquo; का अर्थ है सामान्य से कहीं अधिक या बेहतर। दिए गए sentence में कहा गया है कि हमारे पास दोनों देशों में असाधारण क्षमता वाले नेता हैं। अतः &lsquo;exceptional&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Identify the INCORRECTLY spelt word in the given sentence.<br>Generousity is a godly feature among many individuals on the earth.</p>",
                    question_hi: "<p>89. Identify the INCORRECTLY spelt word in the given sentence.<br>Generousity is a godly feature among many individuals on the earth.</p>",
                    options_en: ["<p>godly</p>", "<p>Generousity</p>", 
                                "<p>individuals</p>", "<p>feature</p>"],
                    options_hi: ["<p>godly</p>", "<p>Generousity</p>",
                                "<p>individuals</p>", "<p>feature</p>"],
                    solution_en: "<p>89.(b) Generousity <br>&lsquo;Generosity&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>89.(b) Generousity <br>&lsquo;Generosity&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the word which means the same as the group of words given.<br>One who hates women</p>",
                    question_hi: "<p>90. Select the word which means the same as the group of words given.<br>One who hates women</p>",
                    options_en: ["<p>philanthropist</p>", "<p>ascetic</p>", 
                                "<p>misogamist</p>", "<p>misogynist</p>"],
                    options_hi: ["<p>philanthropist</p>", "<p>ascetic</p>",
                                "<p>misogamist</p>", "<p>misogynist</p>"],
                    solution_en: "<p>90.(d) <strong>Misogynist-</strong> a person who dislikes, despises, or is strongly prejudiced against women.<br><strong>Philanthropist-</strong> a person who seeks to promote the welfare of others, especially by the generous donation of money to good causes<br><strong>Ascetic-</strong> characterized by severe self-discipline and abstention from all forms of indulgence, typically for religious reasons.<br><strong>Misogamist-</strong> a misogamist is a person who hates marriage.</p>",
                    solution_hi: "<p>90.(d) <strong>Misogynist-</strong> एक व्यक्ति जो महिलाओं को नापसंद करता है या दृढ़ता से उनके साथ पक्षपात करता है।<br><strong>Philanthropist-</strong> एक व्यक्ति जो दूसरों के कल्याण को बढ़ावा देना चाहता है<br><strong>Ascetic-</strong> तपस्वी, साधू <br><strong>Misogamist-</strong> जो विवाह से घृणा करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. <strong>Cloze Test</strong><br>While threatening the ___(91)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(92)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(93)___borders. Even within the ranks of territorial nation-states, the conditions for___(94)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(95)___for arithmetical majorities.<br><br>Select the most appropriate option to fill in the blank no. 91</p>",
                    question_hi: "<p>91. <strong>Cloze Test</strong><br>While threatening the ___(91)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(92)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(93)___borders. Even within the ranks of territorial nation-states, the conditions for___(94)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(95)___for arithmetical majorities.<br><br>Select the most appropriate option to fill in the blank no. 91</p>",
                    options_en: ["<p>integration</p>", "<p>integrity</p>", 
                                "<p>ingratiation</p>", "<p>inability</p>"],
                    options_hi: ["<p>integration</p>", "<p>integrity</p>",
                                "<p>ingratiation</p>", "<p>inability</p>"],
                    solution_en: "<p>91.(b) <strong>integrity</strong><br>We will use a &ldquo;noun&rdquo; after the article &ldquo;the.&rdquo;</p>",
                    solution_hi: "<p>91.(b) <strong>integrity</strong> /अखंडता<br>हम Article \"the के बाद \"noun\" का उपयोग करेंगे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. <strong>Cloze Test</strong><br>While threatening the ___(91)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(92)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(93)___borders. Even within the ranks of territorial nation-states, the conditions for___(94)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(95)___for arithmetical majorities.<br><br>Select the most appropriate option to fill in the blank no. 92</p>",
                    question_hi: "<p>92. <strong>Cloze Test</strong><br>While threatening the ___(91)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(92)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(93)___borders. Even within the ranks of territorial nation-states, the conditions for___(94)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(95)___for arithmetical majorities.<br><br>Select the most appropriate option to fill in the blank no. 92</p>",
                    options_en: ["<p>destabilized</p>", "<p>disintegrated</p>", 
                                "<p>demonstrated</p>", "<p>disdained</p>"],
                    options_hi: ["<p>destabilized</p>", "<p>disintegrated</p>",
                                "<p>demonstrated</p>", "<p>disdained</p>"],
                    solution_en: "<p>92.(c) demonstrated.<br>Demonstrated means to show something clearly by giving proof.</p>",
                    solution_hi: "<p>92.(c) demonstrated.<br>Demonstrated का अर्थ है प्रमाण देकर किसी बात को स्पष्ट रूप से दिखाना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. <strong>Cloze Test</strong><br>While threatening the ___(91)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(92)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(93)___borders. Even within the ranks of territorial nation-states, the conditions for___(94)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(95)___for arithmetical majorities.<br><br>Select the most appropriate option to fill in the blank no. 93</p>",
                    question_hi: "<p>93. <strong>Cloze Test</strong><br>While threatening the ___(91)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(92)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(93)___borders. Even within the ranks of territorial nation-states, the conditions for___(94)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(95)___for arithmetical majorities.</p>\n<p>Select the most appropriate option to fill in the blank no. 93</p>",
                    options_en: ["<p>under</p>", "<p>across</p>", 
                                "<p>over</p>", "<p>cross</p>"],
                    options_hi: ["<p>under</p>", "<p>across</p>",
                                "<p>over</p>", "<p>cross</p>"],
                    solution_en: "<p>93.(b) across.</p>",
                    solution_hi: "<p>93.(b) across.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. <strong>Cloze Test</strong><br>While threatening the ___(91)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(92)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(93)___borders. Even within the ranks of territorial nation-states, the conditions for___(94)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(95)___for arithmetical majorities.<br><br>Select the most appropriate option to fill in the blank no. 94</p>",
                    question_hi: "<p>94. <strong>Cloze Test</strong><br>While threatening the ___(91)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(92)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(93)___borders. Even within the ranks of territorial nation-states, the conditions for___(94)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(95)___for arithmetical majorities.<br><br>Select the most appropriate option to fill in the blank no. 94</p>",
                    options_en: ["<p>effable</p>", "<p>effective</p>", 
                                "<p>effusive</p>", "<p>effervescent</p>"],
                    options_hi: ["<p>effable</p>", "<p>effective</p>",
                                "<p>effusive</p>", "<p>effervescent</p>"],
                    solution_en: "<p>94.(b) effective.</p>",
                    solution_hi: "<p>94.(b) effective./प्रभावी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. <strong>Cloze Test</strong><br>While threatening the ___(91)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(92)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(93)___borders. Even within the ranks of territorial nation-states, the conditions for___(94)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(95)___for arithmetical majorities.<br><br>Select the most appropriate option to fill in the blank no. 95</p>",
                    question_hi: "<p>95. <strong>Cloze Test</strong><br>While threatening the ___(91)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(92)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(93)___borders. Even within the ranks of territorial nation-states, the conditions for___(94)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(95)___for arithmetical majorities.<br><br>Select the most appropriate option to fill in the blank no. 95</p>",
                    options_en: ["<p>decency</p>", "<p>parity</p>", 
                                "<p>legitimacy</p>", "<p>effectiveness</p>"],
                    options_hi: ["<p>decency</p>", "<p>parity</p>",
                                "<p>legitimacy</p>", "<p>effectiveness</p>"],
                    solution_en: "<p>95.(c) legitimacy.</p>",
                    solution_hi: "<p>95.(c) legitimacy./वैधता।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Comprehension</strong><br>Dhamaal is a mix of Sufi and African (mostly East African) musical and dance traditions. It refers particularly to the spiritual practices of the Siddis of Gujarat.<br>The Siddis begin almost every Dhamaal song by blowing into a conch shell. This is often followed by the slow playing of East African percussion instruments like the musindo and the slow thumping of feet that marks the onset of the singing and dancing Dhamaals. The ritual of foot thumping is a crucial part of spiritual East African dance and musical traditions.<br>The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan. According to Siddi folklore they arrived from Ethiopia through the Nubian Valley, Syria and the Indian Ocean to the coast of Kuda in the Bhavnagar district of Gujarat.<br>Usually, Dhamaal songs and dances are performed to celebrate the anniversary of the birth and death of spiritual leaders. They are performed in two ways - Dance Dhamaal and Baithaaki Dhamaal. The Baithaaki Dhamaal is performed in the sitting position and the Dance Dhamaal is performed in both sitting and dance positions.<br>During the performance of Baithaaki Dhamaal the focus is more on the lyrics and less on the musical instruments. During Dance Dhamaal the focus is more on the sounds of the instruments. These are often played in a frenzied manner and accompanied by frenzied dance movements. The spiritual songs that are sung during the Dhamaals are known as zikrs.<br>What is played in a Dhamaal song after blowing into a conch shell?</p>",
                    question_hi: "<p>96. <strong>Comprehension</strong><br>Dhamaal is a mix of Sufi and African (mostly East African) musical and dance traditions. It refers particularly to the spiritual practices of the Siddis of Gujarat.<br>The Siddis begin almost every Dhamaal song by blowing into a conch shell. This is often followed by the slow playing of East African percussion instruments like the musindo and the slow thumping of feet that marks the onset of the singing and dancing Dhamaals. The ritual of foot thumping is a crucial part of spiritual East African dance and musical traditions.<br>The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan. According to Siddi folklore they arrived from Ethiopia through the Nubian Valley, Syria and the Indian Ocean to the coast of Kuda in the Bhavnagar district of Gujarat.<br>Usually, Dhamaal songs and dances are performed to celebrate the anniversary of the birth and death of spiritual leaders. They are performed in two ways - Dance Dhamaal and Baithaaki Dhamaal. The Baithaaki Dhamaal is performed in the sitting position and the Dance Dhamaal is performed in both sitting and dance positions.<br>During the performance of Baithaaki Dhamaal the focus is more on the lyrics and less on the musical instruments. During Dance Dhamaal the focus is more on the sounds of the instruments. These are often played in a frenzied manner and accompanied by frenzied dance movements. The spiritual songs that are sung during the Dhamaals are known as zikrs.<br>What is played in a Dhamaal song after blowing into a conch shell?</p>",
                    options_en: ["<p>Baithaaki</p>", "<p>Lyre</p>", 
                                "<p>Table</p>", "<p>Musindo</p>"],
                    options_hi: ["<p>Baithaaki</p>", "<p>Lyre</p>",
                                "<p>Table</p>", "<p>Musindo</p>"],
                    solution_en: "<p>96.(d) <strong>Musindo</strong><br>It can be inferred from the passage that after blowing into a conch shell, the Siddis follow it with the slow playing of East African percussion instruments like the musindo.</p>",
                    solution_hi: "<p>96.(d) <strong>Musindo</strong><br>दिए गए passage से यह अनुमान लगाया जा सकता है कि सिद्दी लोग, शंख बजाने के बाद, अक्सर मुसिंडो(musindo) जैसे पूर्वी अफ्रीकी ताल वाद्ययंत्रों (percussion instruments) को धीमी गति से बजाते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Comprehension</strong><br>Dhamaal is a mix of Sufi and African (mostly East African) musical and dance traditions. It refers particularly to the spiritual practices of the Siddis of Gujarat.<br>The Siddis begin almost every Dhamaal song by blowing into a conch shell. This is often followed by the slow playing of East African percussion instruments like the musindo and the slow thumping of feet that marks the onset of the singing and dancing Dhamaals. The ritual of foot thumping is a crucial part of spiritual East African dance and musical traditions.<br>The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan. According to Siddi folklore they arrived from Ethiopia through the Nubian Valley, Syria and the Indian Ocean to the coast of Kuda in the Bhavnagar district of Gujarat.<br>Usually, Dhamaal songs and dances are performed to celebrate the anniversary of the birth and death of spiritual leaders. They are performed in two ways - Dance Dhamaal and Baithaaki Dhamaal. The Baithaaki Dhamaal is performed in the sitting position and the Dance Dhamaal is performed in both sitting and dance positions.<br>During the performance of Baithaaki Dhamaal the focus is more on the lyrics and less on the musical instruments. During Dance Dhamaal the focus is more on the sounds of the instruments. These are often played in a frenzied manner and accompanied by frenzied dance movements. The spiritual songs that are sung during the Dhamaals are known as zikrs.<br>Select the most suitable word from the passage which means \'excited\'.</p>",
                    question_hi: "<p>97. <strong>Comprehension</strong><br>Dhamaal is a mix of Sufi and African (mostly East African) musical and dance traditions. It refers particularly to the spiritual practices of the Siddis of Gujarat.<br>The Siddis begin almost every Dhamaal song by blowing into a conch shell. This is often followed by the slow playing of East African percussion instruments like the musindo and the slow thumping of feet that marks the onset of the singing and dancing Dhamaals. The ritual of foot thumping is a crucial part of spiritual East African dance and musical traditions.<br>The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan. According to Siddi folklore they arrived from Ethiopia through the Nubian Valley, Syria and the Indian Ocean to the coast of Kuda in the Bhavnagar district of Gujarat.<br>Usually, Dhamaal songs and dances are performed to celebrate the anniversary of the birth and death of spiritual leaders. They are performed in two ways - Dance Dhamaal and Baithaaki Dhamaal. The Baithaaki Dhamaal is performed in the sitting position and the Dance Dhamaal is performed in both sitting and dance positions.<br>During the performance of Baithaaki Dhamaal the focus is more on the lyrics and less on the musical instruments. During Dance Dhamaal the focus is more on the sounds of the instruments. These are often played in a frenzied manner and accompanied by frenzied dance movements. The spiritual songs that are sung during the Dhamaals are known as zikrs.<br>Select the most suitable word from the passage which means \'excited\'.</p>",
                    options_en: ["<p>Spiritual</p>", "<p>Ritual</p>", 
                                "<p>Frenzied</p>", "<p>Percussion</p>"],
                    options_hi: ["<p>Spiritual</p>", "<p>Ritual</p>",
                                "<p>Frenzied</p>", "<p>Percussion</p>"],
                    solution_en: "<p>97.(c) <strong>Frenzied-</strong> excited.<br><strong>Excited-</strong> feeling very enthusiastic for something.<br><strong>Spiritual-</strong> related to soul.<br><strong>Ritual-</strong> a series of established actions or ceremonies performed in a specific order.<br><strong>Percussion-</strong> musical instruments that make sound when they are hit or shaken.</p>",
                    solution_hi: "<p>97.(c) <strong>Frenzied</strong> (उन्मत्त/उत्साहित) - excited.<br><strong>Excited</strong> (उत्साहित) - feeling very enthusiastic for something.<br><strong>Spiritual</strong> (आध्यात्मिक) - related to soul.<br><strong>Ritual</strong> (अनुष्ठान) - a series of established actions or ceremonies performed in a specific order.<br><strong>Percussion</strong> (तालवाद्य) - musical instruments that make sound when they are hit or shaken.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Comprehension</strong><br>Dhamaal is a mix of Sufi and African (mostly East African) musical and dance traditions. It refers particularly to the spiritual practices of the Siddis of Gujarat.<br>The Siddis begin almost every Dhamaal song by blowing into a conch shell. This is often followed by the slow playing of East African percussion instruments like the musindo and the slow thumping of feet that marks the onset of the singing and dancing Dhamaals. The ritual of foot thumping is a crucial part of spiritual East African dance and musical traditions.<br>The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan. According to Siddi folklore they arrived from Ethiopia through the Nubian Valley, Syria and the Indian Ocean to the coast of Kuda in the Bhavnagar district of Gujarat.<br>Usually, Dhamaal songs and dances are performed to celebrate the anniversary of the birth and death of spiritual leaders. They are performed in two ways - Dance Dhamaal and Baithaaki Dhamaal. The Baithaaki Dhamaal is performed in the sitting position and the Dance Dhamaal is performed in both sitting and dance positions.<br>During the performance of Baithaaki Dhamaal the focus is more on the lyrics and less on the musical instruments. During Dance Dhamaal the focus is more on the sounds of the instruments. These are often played in a frenzied manner and accompanied by frenzied dance movements. The spiritual songs that are sung during the Dhamaals are known as zikrs.<br>What is the tone of the speaker?</p>",
                    question_hi: "<p>98. <strong>Comprehension</strong><br>Dhamaal is a mix of Sufi and African (mostly East African) musical and dance traditions. It refers particularly to the spiritual practices of the Siddis of Gujarat.<br>The Siddis begin almost every Dhamaal song by blowing into a conch shell. This is often followed by the slow playing of East African percussion instruments like the musindo and the slow thumping of feet that marks the onset of the singing and dancing Dhamaals. The ritual of foot thumping is a crucial part of spiritual East African dance and musical traditions.<br>The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan. According to Siddi folklore they arrived from Ethiopia through the Nubian Valley, Syria and the Indian Ocean to the coast of Kuda in the Bhavnagar district of Gujarat.<br>Usually, Dhamaal songs and dances are performed to celebrate the anniversary of the birth and death of spiritual leaders. They are performed in two ways - Dance Dhamaal and Baithaaki Dhamaal. The Baithaaki Dhamaal is performed in the sitting position and the Dance Dhamaal is performed in both sitting and dance positions.<br>During the performance of Baithaaki Dhamaal the focus is more on the lyrics and less on the musical instruments. During Dance Dhamaal the focus is more on the sounds of the instruments. These are often played in a frenzied manner and accompanied by frenzied dance movements. The spiritual songs that are sung during the Dhamaals are known as zikrs.<br>What is the tone of the speaker?</p>",
                    options_en: ["<p>Informative</p>", "<p>Biased</p>", 
                                "<p>Aggressive</p>", "<p>Sarcastic</p>"],
                    options_hi: ["<p>Informative</p>", "<p>Biased</p>",
                                "<p>Aggressive</p>", "<p>Sarcastic</p>"],
                    solution_en: "<p>98.(a) <strong>Informative</strong><br>In an informative tone, the author presents facts and details clearly and objectively, aiming to educate the reader about a specific topic.</p>",
                    solution_hi: "<p>98.(a) <strong>Informative</strong><br>Informative tone में, लेखक (author) facts और details को स्पष्ट एवं वस्तुनिष्ठ रूप से प्रस्तुत करता है, जिसका उद्देश्य पाठक (reader) को एक विशिष्ट विषय(specific topic) के बारे में educate करना होता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Comprehension</strong><br>Dhamaal is a mix of Sufi and African (mostly East African) musical and dance traditions. It refers particularly to the spiritual practices of the Siddis of Gujarat.<br>The Siddis begin almost every Dhamaal song by blowing into a conch shell. This is often followed by the slow playing of East African percussion instruments like the musindo and the slow thumping of feet that marks the onset of the singing and dancing Dhamaals. The ritual of foot thumping is a crucial part of spiritual East African dance and musical traditions.<br>The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan. According to Siddi folklore they arrived from Ethiopia through the Nubian Valley, Syria and the Indian Ocean to the coast of Kuda in the Bhavnagar district of Gujarat.<br>Usually, Dhamaal songs and dances are performed to celebrate the anniversary of the birth and death of spiritual leaders. They are performed in two ways - Dance Dhamaal and Baithaaki Dhamaal. The Baithaaki Dhamaal is performed in the sitting position and the Dance Dhamaal is performed in both sitting and dance positions.<br>During the performance of Baithaaki Dhamaal the focus is more on the lyrics and less on the musical instruments. During Dance Dhamaal the focus is more on the sounds of the instruments. These are often played in a frenzied manner and accompanied by frenzied dance movements. The spiritual songs that are sung during the Dhamaals are known as zikrs.<br>Who is NOT a spiritual leader of the Siddis?</p>",
                    question_hi: "<p>99. <strong>Comprehension</strong><br>Dhamaal is a mix of Sufi and African (mostly East African) musical and dance traditions. It refers particularly to the spiritual practices of the Siddis of Gujarat.<br>The Siddis begin almost every Dhamaal song by blowing into a conch shell. This is often followed by the slow playing of East African percussion instruments like the musindo and the slow thumping of feet that marks the onset of the singing and dancing Dhamaals. The ritual of foot thumping is a crucial part of spiritual East African dance and musical traditions.<br>The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan. According to Siddi folklore they arrived from Ethiopia through the Nubian Valley, Syria and the Indian Ocean to the coast of Kuda in the Bhavnagar district of Gujarat.<br>Usually, Dhamaal songs and dances are performed to celebrate the anniversary of the birth and death of spiritual leaders. They are performed in two ways - Dance Dhamaal and Baithaaki Dhamaal. The Baithaaki Dhamaal is performed in the sitting position and the Dance Dhamaal is performed in both sitting and dance positions.<br>During the performance of Baithaaki Dhamaal the focus is more on the lyrics and less on the musical instruments. During Dance Dhamaal the focus is more on the sounds of the instruments. These are often played in a frenzied manner and accompanied by frenzied dance movements. The spiritual songs that are sung during the Dhamaals are known as zikrs.<br>Who is NOT a spiritual leader of the Siddis?</p>",
                    options_en: ["<p>Baba Habash</p>", "<p>Mai Misra</p>", 
                                "<p>Gulam Ali</p>", "<p>Sidi Nabi Sultan</p>"],
                    options_hi: ["<p>Baba Habash</p>", "<p>Mai Misra</p>",
                                "<p>Gulam Ali</p>", "<p>Sidi Nabi Sultan</p>"],
                    solution_en: "<p>99.(c) <strong>Gulam Ali</strong><br>(Line/s from the passage- The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan.)</p>",
                    solution_hi: "<p>99.(c) <strong>Gulam Ali</strong><br>(Passage से ली गई line/s - The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan./सिद्दी इस्लाम के अनुयायी हैं और पूर्वी एवं मध्य अफ्रीका के मुस्लिम समुदायों से भारत आए थे। धमाल उनके आध्यात्मिक नेताओं की स्मृति में किया जाता है, जिनमें बावा गोर, माई मिश्रा, बाबा हबाश और सिदी नबी सुल्तान शामिल हैं।)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Comprehension</strong><br>Dhamaal is a mix of Sufi and African (mostly East African) musical and dance traditions. It refers particularly to the spiritual practices of the Siddis of Gujarat.<br>The Siddis begin almost every Dhamaal song by blowing into a conch shell. This is often followed by the slow playing of East African percussion instruments like the musindo and the slow thumping of feet that marks the onset of the singing and dancing Dhamaals. The ritual of foot thumping is a crucial part of spiritual East African dance and musical traditions.<br>The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan. According to Siddi folklore they arrived from Ethiopia through the Nubian Valley, Syria and the Indian Ocean to the coast of Kuda in the Bhavnagar district of Gujarat.<br>Usually, Dhamaal songs and dances are performed to celebrate the anniversary of the birth and death of spiritual leaders. They are performed in two ways - Dance Dhamaal and Baithaaki Dhamaal. The Baithaaki Dhamaal is performed in the sitting position and the Dance Dhamaal is performed in both sitting and dance positions.<br>During the performance of Baithaaki Dhamaal the focus is more on the lyrics and less on the musical instruments. During Dance Dhamaal the focus is more on the sounds of the instruments. These are often played in a frenzied manner and accompanied by frenzied dance movements. The spiritual songs that are sung during the Dhamaals are known as zikrs.<br>Identify the most suitable title for the passage.</p>",
                    question_hi: "<p>100. <strong>Comprehension</strong><br>Dhamaal is a mix of Sufi and African (mostly East African) musical and dance traditions. It refers particularly to the spiritual practices of the Siddis of Gujarat.<br>The Siddis begin almost every Dhamaal song by blowing into a conch shell. This is often followed by the slow playing of East African percussion instruments like the musindo and the slow thumping of feet that marks the onset of the singing and dancing Dhamaals. The ritual of foot thumping is a crucial part of spiritual East African dance and musical traditions.<br>The Siddis are followers of Islam and arrived in India from Muslim communities in East and Central Africa. Dhamaals are performed in memory of their spiritual leaders, among them Bava Gor, Mai Misra, Baba Habash and Sidi Nabi Sultan. According to Siddi folklore they arrived from Ethiopia through the Nubian Valley, Syria and the Indian Ocean to the coast of Kuda in the Bhavnagar district of Gujarat.<br>Usually, Dhamaal songs and dances are performed to celebrate the anniversary of the birth and death of spiritual leaders. They are performed in two ways - Dance Dhamaal and Baithaaki Dhamaal. The Baithaaki Dhamaal is performed in the sitting position and the Dance Dhamaal is performed in both sitting and dance positions.<br>During the performance of Baithaaki Dhamaal the focus is more on the lyrics and less on the musical instruments. During Dance Dhamaal the focus is more on the sounds of the instruments. These are often played in a frenzied manner and accompanied by frenzied dance movements. The spiritual songs that are sung during the Dhamaals are known as zikrs.<br>Identify the most suitable title for the passage.</p>",
                    options_en: ["<p>What\'s Missing in Dhamaal?</p>", "<p>The Saga of the Siddis</p>", 
                                "<p>Wonders of Indian Dance</p>", "<p>The Bhavnagar Treat</p>"],
                    options_hi: ["<p>What\'s Missing in Dhamaal?</p>", "<p>The Saga of the Siddis</p>",
                                "<p>Wonders of Indian Dance</p>", "<p>The Bhavnagar Treat</p>"],
                    solution_en: "<p>100.(b) <strong>The Saga of the Siddis</strong><br>It can be inferred from the passage that the most appropriate title for the passage is &lsquo;The Saga of the Siddis&rsquo;.</p>",
                    solution_hi: "<p>100.(b) <strong>The Saga of the Siddis</strong><br>दिए गए passage से यह अनुमान लगाया जा सकता है कि इस passage का सबसे उपयुक्त title &lsquo;The Saga of the Siddis&rsquo; है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>