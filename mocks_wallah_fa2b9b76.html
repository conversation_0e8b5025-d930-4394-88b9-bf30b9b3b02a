<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 19</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">19</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 17
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 18,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> If a = </span><span style=\"font-family: Cambria Math;\">2022 ,</span><span style=\"font-family: Cambria Math;\"> b = 2021 and c = 2020 , then value of a<sup>2</sup>+b<sup>2</sup>+c<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> - ab &ndash; bc &ndash; ca is :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> a = </span><span style=\"font-family: Cambria Math;\">2022 ,</span><span style=\"font-family: Cambria Math;\"> b = 2021 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> c = 2020 , </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> a<sup>2</sup>+b<sup>2</sup>+c<sup>2</sup></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">&ndash; ab &ndash; bc &ndash; ca </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    options_en: ["<p>2</p>\n", "<p>4</p>\n", 
                                "<p>3</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>2</p>\n", "<p>4</p>\n",
                                "<p>3</p>\n", "<p>1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Cambria Math;\">Formula </span><span style=\"font-family: Cambria Math;\">used :</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>-</mo><mi>b</mi><mi>c</mi><mo>-</mo><mi>c</mi><mi>a</mi><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mfenced open=\"[\" close=\"]\"><mrow><msup><mrow><mo>(</mo><mi>a</mi><mo>-</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>b</mi><mo>-</mo><mi>c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>c</mi><mo>-</mo><mi>a</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfenced><mspace linebreak=\"newline\"></mspace><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>-</mo><mi>b</mi><mi>c</mi><mo>-</mo><mi>c</mi><mi>a</mi><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mfenced open=\"[\" close=\"]\"><mrow><msup><mrow><mo>(</mo><mn>2022</mn><mo>-</mo><mn>2021</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>2021</mn><mo>-</mo><mn>2020</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>2022</mn><mo>-</mo><mn>2020</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfenced><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mfenced open=\"[\" close=\"]\"><mrow><mn>1</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>4</mn></mrow></mfenced><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>6</mn><mo>=</mo><mn>3</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2340;&#2381;</span><span style=\"font-family: Nirmala UI;\">&#2352;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>-</mo><mi>b</mi><mi>c</mi><mo>-</mo><mi>c</mi><mi>a</mi><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mfenced open=\"[\" close=\"]\"><mrow><msup><mrow><mo>(</mo><mi>a</mi><mo>-</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>b</mi><mo>-</mo><mi>c</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>c</mi><mo>-</mo><mi>a</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfenced><mspace linebreak=\"newline\"></mspace><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><msup><mi>c</mi><mn>2</mn></msup><mo>-</mo><mi>a</mi><mi>b</mi><mo>-</mo><mi>b</mi><mi>c</mi><mo>-</mo><mi>c</mi><mi>a</mi><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mfenced open=\"[\" close=\"]\"><mrow><msup><mrow><mo>(</mo><mn>2022</mn><mo>-</mo><mn>2021</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>2021</mn><mo>-</mo><mn>2020</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>2022</mn><mo>-</mo><mn>2020</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfenced><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mfenced open=\"[\" close=\"]\"><mrow><mn>1</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>4</mn></mrow></mfenced><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>6</mn><mo>=</mo><mn>3</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">If x<sup>2 </sup>+ 4y<sup>2 </sup>= 40 xy = 6 </span><span style=\"font-family: Cambria Math;\">and x &gt;</span><span style=\"font-family: Cambria Math;\"> 2y, then the value of x - 2y is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> x<sup>2 </sup>+ 4y<sup>2 </sup>= 40 xy = 6</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> x &gt; 2y </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> x - 2y </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    options_en: ["<p>1</p>\n", "<p>3</p>\n", 
                                "<p>2</p>\n", "<p>4</p>\n"],
                    options_hi: ["<p>1</p>\n", "<p>3</p>\n",
                                "<p>2</p>\n", "<p>4</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">(x</span><span style=\"font-family: Cambria Math;\"> - 2y)<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> = x<sup>2 </sup>+4y<sup>2</sup></span><span style=\"font-family: Cambria Math;\">- 4xy = 40 - 4&times;6 = 40 - 24 = 16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x</span><span style=\"font-family: Cambria Math;\"> - 2y = 4</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">(x</span><span style=\"font-family: Cambria Math;\"> - 2y)<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> = x<sup>2 </sup>+ 4y<sup>2</sup></span><span style=\"font-family: Cambria Math;\">- 4xy = 40 - 4&times;6 = 40 - 24 = 16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x</span><span style=\"font-family: Cambria Math;\"> - 2y = 4</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">The value </span><span style=\"font-family: Cambria Math;\">of</span><span style=\"font-family: Cambria Math;\">&nbsp;x<sup>2 </sup>+ y<sup>2 </sup>when&nbsp; x = 1, y = 2 is ______.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> x = 1, y = 2 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379; x<sup>2</sup> + y<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    options_en: ["<p>4</p>\n", "<p>5</p>\n", 
                                "<p>2</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>4</p>\n", "<p>5</p>\n",
                                "<p>2</p>\n", "<p>1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">Putting </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 1 and y = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x<sup>2 </sup>+ y<sup>2 </sup>= 1<sup>2</sup></span><span style=\"font-family: Cambria Math;\">+2<sup>2</sup>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 5</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">Putting</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> x = 1 <span style=\"font-weight: 400;\">&#2324;&#2352;</span> y = 2 <span style=\"font-weight: 400;\">&#2352;&#2326;&#2344;&#2375; &#2346;&#2352;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;x<sup>2 </sup>+ y<sup>2 </sup>= 1<sup>2</sup></span><span style=\"font-family: Cambria Math;\">+2<sup>2</sup>&nbsp;</span><span style=\"font-family: Cambria Math;\"> = 5</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">If x<sup>2 </sup>- 8x + 1 = 0</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">, what is the value of x<sup>8 </sup>- 3842x<sup>4 </sup>+ 1 </span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367; <span style=\"font-family: Cambria Math;\">x<sup>2 </sup>- 8x + 1 = 0</span><span style=\"font-family: Cambria Math;\"> </span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379; <span style=\"font-family: Cambria Math;\">x<sup>8 </sup>- 3842x<sup>4 </sup>+ 1</span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    options_en: ["<p>&minus;1</p>\n", "<p>0</p>\n", 
                                "<p>2</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>&minus;1</p>\n", "<p>0</p>\n",
                                "<p>2</p>\n", "<p>1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x<sup>2 </sup>- 8x</span><span style=\"font-family: Cambria Math;\"> + 1 = 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Dividing both sides by x.</span><span style=\"font-family: Cambria Math;\"> We get </span></p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mn>8</mn><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mn>64</mn><mo>-</mo><mn>2</mn><mo>=</mo><mn>62</mn><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>=</mo><mn>3844</mn><mo>-</mo><mn>2</mn><mo>=</mo><mn>3842</mn><mspace linebreak=\"newline\"></mspace><mi>M</mi><mi>u</mi><mi>l</mi><mi>t</mi><mi>i</mi><mi>p</mi><mi>l</mi><mi>y</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>b</mi><mi>o</mi><mi>t</mi><mi>h</mi><mo>&nbsp;</mo><mi>s</mi><mi>i</mi><mi>d</mi><mi>e</mi><mi>s</mi><mo>&nbsp;</mo><mi>b</mi><mi>y</mi><mo>&nbsp;</mo><msup><mi>x</mi><mn>4</mn></msup><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>8</mn></msup><mo>+</mo><mn>1</mn><mo>=</mo><mn>3842</mn><msup><mi>x</mi><mn>4</mn></msup><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>8</mn></msup><mo>-</mo><mo>&nbsp;</mo><mn>3842</mn><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mn>1</mn><mo>=</mo><mn>0</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x<sup>2 </sup>- 8x</span><span style=\"font-family: Cambria Math;\"> + 1 = 0</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;</span><span style=\"font-family: Nirmala UI;\">&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p>&nbsp;</p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mn>8</mn><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>=</mo><mn>64</mn><mo>-</mo><mn>2</mn><mo>=</mo><mn>62</mn><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>=</mo><mn>3844</mn><mo>-</mo><mn>2</mn><mo>=</mo><mn>3842</mn><mspace linebreak=\"newline\"></mspace><mi>&#2342;&#2379;&#2344;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2379;</mi><mo>&nbsp;</mo><msup><mi>x</mi><mn>4</mn></msup><mo>&nbsp;</mo><mi>&#2360;&#2375;</mi><mo>&nbsp;</mo><mi>&#2327;&#2369;&#2339;&#2366;</mi><mo>&nbsp;</mo><mi>&#2325;&#2352;&#2344;&#2375;</mi><mo>&nbsp;</mo><mi>&#2346;&#2352;</mi><mo>:</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>8</mn></msup><mo>+</mo><mn>1</mn><mo>=</mo><mn>3842</mn><msup><mi>x</mi><mn>4</mn></msup><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>8</mn></msup><mo>-</mo><mo>&nbsp;</mo><mn>3842</mn><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mn>1</mn><mo>=</mo><mn>0</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>144</mn><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>36</mn><mi>x</mi><mo>+</mo><mfrac><mn>9</mn><mn>4</mn></mfrac></math> <span style=\"font-family: Cambria Math;\">can</span><span style=\"font-family: Cambria Math;\"> be expressed as the square of ________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    question_hi: "<p>5. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>144</mn><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>36</mn><mi>x</mi><mo>+</mo><mfrac><mn>9</mn><mn>4</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> ________ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2367;&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>14</mn><mi>x</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mi>x</mi><mo>-</mo><mfrac><mn>9</mn><mn>4</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mi>x</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mi>x</mi><mo>-</mo><mn>9</mn></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>14</mn><mi>x</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mi>x</mi><mo>-</mo><mfrac><mn>9</mn><mn>4</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mi>x</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mi>x</mi><mo>-</mo><mn>9</mn></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>144</mn><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>36</mn><mi>x</mi><mo>+</mo><mfrac><mn>9</mn><mn>4</mn></mfrac><mo>=</mo><msup><mrow><mo>(</mo><mn>12</mn><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>&times;</mo><mn>12</mn><mo>&times;</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><msup><mrow><mo>(</mo><mn>12</mn><mi>x</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>s</mi><mi>o</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>S</mi><mi>q</mi><mi>u</mi><mi>a</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mn>144</mn><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>36</mn><mi>x</mi><mo>+</mo><mfrac><mn>9</mn><mn>4</mn></mfrac><mo>=</mo><mn>12</mn><mi>x</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>144</mn><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>36</mn><mi>x</mi><mo>+</mo><mfrac><mn>9</mn><mn>4</mn></mfrac><mo>=</mo><msup><mrow><mo>(</mo><mn>12</mn><mi>x</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>&times;</mo><mn>12</mn><mo>&times;</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><msup><mrow><mo>(</mo><mn>12</mn><mi>x</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2340;</mi><mo>:</mo><mo>&nbsp;</mo><mn>144</mn><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>36</mn><mi>x</mi><mo>+</mo><mfrac><mn>9</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2357;&#2352;&#2381;&#2327;</mi><mo>=</mo><mn>12</mn><mi>x</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>4</mn><mi>b</mi><mo>-</mo><mn>1</mn><mo>=</mo><mn>0</mn></math>-</span><span style=\"font-family: Cambria Math;\">then find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac><mo>+</mo><mn>3</mn><mi>b</mi><mo>-</mo><mfrac><mn>3</mn><mi>b</mi></mfrac><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> .</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>4</mn><mi>b</mi><mo>-</mo><mn>1</mn><mo>=</mo><mn>0</mn></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac><mo>+</mo><mn>3</mn><mi>b</mi><mo>-</mo><mfrac><mn>3</mn><mi>b</mi></mfrac><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;</span><span style=\"font-family: Nirmala UI;\">&#2319;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    options_en: ["<p>32</p>\n", "<p>30</p>\n", 
                                "<p>18</p>\n", "<p>24</p>\n"],
                    options_hi: ["<p>32</p>\n", "<p>30</p>\n",
                                "<p>18</p>\n", "<p>24</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">b<sup>2</sup> - 4b - 1 = 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Dividing both side by b</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">b - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>b</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Again, multiplying both sides by 3, we get</span></p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>b</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac><mo>=</mo><msup><mn>4</mn><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>=</mo><mn>18</mn><mspace linebreak=\"newline\"></mspace><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac><mo>+</mo><mn>3</mn><mi>b</mi><mo>-</mo><mfrac><mn>3</mn><mi>b</mi></mfrac><mo>=</mo><mn>18</mn><mo>+</mo><mn>12</mn><mo>=</mo><mn>30</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">b<sup>2</sup>- 4b - 1 = 0</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> b </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>b</mi><mo>-</mo><mfrac><mn>1</mn><mi>b</mi></mfrac><mo>=</mo><mn>4</mn></math></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>b</mi><mo>-</mo><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>=</mo><mn>12</mn><mspace linebreak=\"newline\"></mspace><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac><mo>=</mo><msup><mn>4</mn><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>=</mo><mn>18</mn><mspace linebreak=\"newline\"></mspace><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac><mo>+</mo><mn>3</mn><mi>b</mi><mo>-</mo><mfrac><mn>3</mn><mi>b</mi></mfrac><mo>=</mo><mn>18</mn><mo>+</mo><mn>12</mn><mo>=</mo><mn>30</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> The sum of two numbers is 17 and their product is 34.Find </span><span style=\"font-family: Cambria Math;\">the sum of their squares.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Morning)</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> 17 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2339;&#2344;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> 34 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Morning)</span></p>\n",
                    options_en: ["<p>224</p>\n", "<p>221</p>\n", 
                                "<p>225</p>\n", "<p>222</p>\n"],
                    options_hi: ["<p>224</p>\n", "<p>221</p>\n",
                                "<p>225</p>\n", "<p>222</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">It is given that,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a + b = </span><span style=\"font-family: Cambria Math;\">17 ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ab</span><span style=\"font-family: Cambria Math;\"> = 34</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a<sup>2</sup> + b<sup>2 </sup></span><span style=\"font-family: Cambria Math;\">=( a + b)<sup>2</sup>&nbsp;</span><span style=\"font-family: Cambria Math;\"> - 2ab </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>17<sup>2 </sup>- 2 &times; </span><span style=\"font-family: Cambria Math;\">34 =</span><span style=\"font-family: Cambria Math;\"> 289 - 68 = 221</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a + b = </span><span style=\"font-family: Cambria Math;\">17 ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ab</span><span style=\"font-family: Cambria Math;\"> = 34</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a<sup>2</sup> + b<sup>2 </sup></span><span style=\"font-family: Cambria Math;\">=( a + b)<sup>2</sup>&nbsp;</span><span style=\"font-family: Cambria Math;\"> - 2ab </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>17<sup>2 </sup>- 2 &times; </span><span style=\"font-family: Cambria Math;\">34 =</span><span style=\"font-family: Cambria Math;\"> 289 - 68 = 221</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> If 8x<sup>3</sup></span><span style=\"font-family: Cambria Math;\"> + 27y<sup>3</sup></span><span style=\"font-family: Cambria Math;\"> + 64z<sup>3</sup></span><span style=\"font-family: Cambria Math;\"> = 72xyz, then the relation between x, y and z can </span><span style=\"font-family: Cambria Math;\">be :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Morning)</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 8x<sup>3</sup> + 27y<sup>3</sup> + 64z<sup>3</sup> = 72xyz </span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> x, y </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> z </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Morning)</span></p>\n",
                    options_en: ["<p>2x + 3y = &minus;4z</p>\n", "<p>2x + y + z = 0</p>\n", 
                                "<p>2x &ndash; 3y + 4z = 0</p>\n", "<p>2x + 3y = 4z</p>\n"],
                    options_hi: ["<p>2x + 3y = &minus;4z</p>\n", "<p>2x + y + z = 0</p>\n",
                                "<p>2x &ndash; 3y + 4z = 0</p>\n", "<p>2x + 3y = 4z</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">Formula </span><span style=\"font-family: Cambria Math;\">used :</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>y</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>z</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>w</mi><mi>h</mi><mi>e</mi><mi>n</mi><mo>&nbsp;</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>+</mo><mi>z</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><msup><mi>x</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>64</mn><msup><mi>z</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>72</mn><mo>&nbsp;</mo><mi>x</mi><mi>y</mi><mi>z</mi><mspace linebreak=\"newline\"></mspace><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mi>y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>4</mn><msup><mi>z</mi><mn>3</mn></msup><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>3</mn><mi>y</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>4</mn><mi>z</mi><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>o</mi><mo>,</mo><mo>&nbsp;</mo><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mi>y</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mi>z</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mi>y</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mi>z</mi><mspace linebreak=\"newline\"></mspace></math></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a)</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2340;&#2381;</span><span style=\"font-family: Nirmala UI;\">&#2352;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>y</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>z</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mn>3</mn><mi>x</mi><mi>y</mi><mi>z</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>w</mi><mi>h</mi><mi>e</mi><mi>n</mi><mo>&nbsp;</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>+</mo><mi>z</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>8</mn><msup><mi>x</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>27</mn><msup><mi>y</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>64</mn><msup><mi>z</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>72</mn><mo>&nbsp;</mo><mi>x</mi><mi>y</mi><mi>z</mi><mspace linebreak=\"newline\"></mspace><msup><mrow><mo>(</mo><mn>2</mn><mi>x</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mi>y</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>4</mn><msup><mi>z</mi><mn>3</mn></msup><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>3</mn><mi>y</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>4</mn><mi>z</mi><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>o</mi><mo>,</mo><mo>&nbsp;</mo><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mi>y</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mi>z</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace><mn>2</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mi>y</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mi>z</mi></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> The value</span><span style=\"font-family: Cambria Math;\"> of&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfenced open=\"[\" close=\"]\"><mrow><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>0</mn><mo>.</mo><mn>064</mn></mrow></mfenced><mfenced open=\"[\" close=\"]\"><mrow><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>.</mo><mn>16</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>16</mn></mrow></mfenced></mfrac></math></span><span style=\"font-family: Cambria Math;\"> is _______</span><span style=\"font-family: Cambria Math;\">_ .</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Morning)</span></p>\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfenced open=\"[\" close=\"]\"><mrow><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>0</mn><mo>.</mo><mn>064</mn></mrow></mfenced><mfenced open=\"[\" close=\"]\"><mrow><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>.</mo><mn>16</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>16</mn></mrow></mfenced></mfrac></math> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Morning)</span></p>\n",
                    options_en: ["<p>5</p>\n", "<p>7</p>\n", 
                                "<p>3</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>5</p>\n", "<p>7</p>\n",
                                "<p>3</p>\n", "<p>1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>F</mi><mi>o</mi><mi>r</mi><mi>m</mi><mi>u</mi><mi>l</mi><mi>a</mi><mo>&nbsp;</mo><mi>u</mi><mi>s</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mo>:</mo><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>&ndash;</mo><mo>&nbsp;</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>&ndash;</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>)</mo><mspace linebreak=\"newline\"></mspace><mfrac><mfenced open=\"[\" close=\"]\"><mrow><mn>5</mn><mo>.</mo><msup><mn>4</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><msup><mn>4</mn><mn>3</mn></msup></mrow></mfenced><mfenced open=\"[\" close=\"]\"><mrow><mn>5</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>&times;</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup></mrow></mfenced></mfrac><mo>=</mo><mfrac><mrow><mfenced><mrow><mn>5</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><mn>4</mn></mrow></mfenced><mfenced open=\"[\" close=\"]\"><mrow><mn>5</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup><mo>+</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>&times;</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>+</mo><mn>0</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup></mrow></mfenced></mrow><mrow><mn>5</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup><mo>+</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>&times;</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>+</mo><mn>0</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup></mrow></mfrac><mo>=</mo><mn>5</mn></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2346;&#2381;&#2352;&#2351;&#2369;&#2325;&#2381;&#2340;</mi><mo>&nbsp;</mo><mi>&#2360;&#2370;&#2340;&#2381;&#2352;</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>&ndash;</mo><mo>&nbsp;</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>=</mo><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>&ndash;</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>)</mo><mspace linebreak=\"newline\"></mspace><mfrac><mfenced open=\"[\" close=\"]\"><mrow><mn>5</mn><mo>.</mo><msup><mn>4</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><msup><mn>4</mn><mn>3</mn></msup></mrow></mfenced><mfenced open=\"[\" close=\"]\"><mrow><mn>5</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>&times;</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup></mrow></mfenced></mfrac><mo>=</mo><mfrac><mrow><mfenced><mrow><mn>5</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>0</mn><mo>.</mo><mn>4</mn></mrow></mfenced><mfenced open=\"[\" close=\"]\"><mrow><mn>5</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup><mo>+</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>&times;</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>+</mo><mn>0</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup></mrow></mfenced></mrow><mrow><mn>5</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup><mo>+</mo><mn>5</mn><mo>.</mo><mn>4</mn><mo>&times;</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>+</mo><mn>0</mn><mo>.</mo><msup><mn>4</mn><mn>2</mn></msup></mrow></mfrac><mo>=</mo><mn>5</mn></math></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The identity 4(z + 7</span><span style=\"font-family: Cambria Math;\">)(</span><span style=\"font-family: Cambria Math;\">2z &ndash; 1) = Az<sup>2</sup></span><span style=\"font-family: Cambria Math;\">+ Bz + C holds for all real values of z. Find the value of A<sup>2</sup></span><span style=\"font-family: Cambria Math;\">&ndash; B &ndash; C .</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">z</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2360;&#2350;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 4(z + 7)(2z &ndash; 1) = A</span><span style=\"font-family: Cambria Math;\">+ Bz + C </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&ndash; B &ndash; C </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>&ndash;16</p>\n", "<p>40</p>\n", 
                                "<p>36</p>\n", "<p>16</p>\n"],
                    options_hi: ["<p>&ndash;16</p>\n", "<p>40</p>\n",
                                "<p>36</p>\n", "<p>16</p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4(</span><span style=\"font-family: Cambria Math;\"> Z + 7) (2Z</span><span style=\"font-family: Cambria Math;\"> - 1) = AZ<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + BZ</span><span style=\"font-family: Cambria Math;\"> + C</span></p>\r\n<p>(4z+28) (2z<span style=\"font-family: Cambria Math;\"> - 1) = Az<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + Bz</span><span style=\"font-family: Cambria Math;\"> + C</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8z<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> - 4z</span><span style=\"font-family: Cambria Math;\"> + 56z</span><span style=\"font-family: Cambria Math;\"> - 28 = Az<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + Bz</span><span style=\"font-family: Cambria Math;\"> + C </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8z<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + 52z</span><span style=\"font-family: Cambria Math;\"> - 28 = Az<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + Bz</span><span style=\"font-family: Cambria Math;\"> + C </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">On comparing both the sides, we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = 8, B = 52 and C = -28</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, A<sup>2</sup></span><span style=\"font-family: Cambria Math;\">- B - C = 8<sup>2 </sup></span><span style=\"font-family: Cambria Math;\">- 52 - (-28) = 64 - 52 + 28 = 92 - 52 = 40</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4(</span><span style=\"font-family: Cambria Math;\"> Z + 7) (2Z</span><span style=\"font-family: Cambria Math;\"> - 1) = AZ<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + BZ</span><span style=\"font-family: Cambria Math;\"> + C</span></p>\r\n<p>(4z+28) (2z<span style=\"font-family: Cambria Math;\"> - 1) = Az<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + Bz</span><span style=\"font-family: Cambria Math;\"> + C</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8z<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> - 4z</span><span style=\"font-family: Cambria Math;\"> + 56z</span><span style=\"font-family: Cambria Math;\"> - 28 = Az<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + Bz</span><span style=\"font-family: Cambria Math;\"> + C </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8z<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + 52z</span><span style=\"font-family: Cambria Math;\"> - 28 = Az<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + Bz</span><span style=\"font-family: Cambria Math;\"> + C </span></p>\r\n<p><span style=\"font-weight: 400;\">&#2342;&#2379;&#2344;&#2379;&#2306; &#2346;&#2325;&#2381;&#2359;&#2379;&#2306; &#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352;, &#2361;&#2350; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A = 8, B = 52 and C = -28</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span>, A<sup>2</sup></span><span style=\"font-family: Cambria Math;\">- B - C = 8<sup>2 </sup></span><span style=\"font-family: Cambria Math;\">- 52 - (-28) = 64 - 52 + 28 = 92 - 52 = 40</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If x=9, then the value of x<sup>5</sup></span><span style=\"font-family: Cambria Math;\">&ndash; 10x<sup>4</sup></span><span style=\"font-family: Cambria Math;\"> + 10x<sup>3</sup></span><span style=\"font-family: Cambria Math;\"> &ndash; 10x<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + 10x</span><span style=\"font-family: Cambria Math;\"> &ndash; 1 </span><span style=\"font-family: Cambria Math;\">is :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> x = 9, </span><span style=\"font-family: Nirmala UI;\">&#2340;</span><span style=\"font-family: Nirmala UI;\">&#2379; <span style=\"font-family: Cambria Math;\">x<sup>5</sup></span><span style=\"font-family: Cambria Math;\">&ndash; 10x<sup>4</sup></span><span style=\"font-family: Cambria Math;\"> + 10x<sup>3</sup></span><span style=\"font-family: Cambria Math;\"> &ndash; 10x<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + 10x</span><span style=\"font-family: Cambria Math;\"> &ndash; 1 </span></span><span style=\"font-family: Nirmala UI;\">&#2325;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>10</p>\n", "<p>8</p>\n", 
                                "<p>9</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>10</p>\n", "<p>8</p>\n",
                                "<p>9</p>\n", "<p>1</p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x<sup>5 </sup>- 10x<sup>4</sup></span><span style=\"font-family: Cambria Math;\"> + 10x<sup>3</sup></span><span style=\"font-family: Cambria Math;\"> - 10x<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + 10x - 1</span></p>\r\n<p><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">5</span></sup><span style=\"font-weight: 400;\"> - 9</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"> - </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"><sup> </sup>+ 9</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> - 9</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> + 9x + x -1</span></p>\r\n<p><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\">(x - 9) - </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"><sup> </sup>(x - 9) + </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> (x - 9) + x (x - 9) + x - 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putting x = </span><span style=\"font-family: Cambria Math;\">9 ,</span><span style=\"font-family: Cambria Math;\"> we get </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">0 + 9 - 1 = 8</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x<sup>5 </sup>- 10x<sup>4</sup></span><span style=\"font-family: Cambria Math;\"> + 10x<sup>3</sup></span><span style=\"font-family: Cambria Math;\"> - 10x<sup>2</sup></span><span style=\"font-family: Cambria Math;\"> + 10x - 1</span></p>\r\n<p><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">5</span></sup><span style=\"font-weight: 400;\"> - 9</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"> - </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"><sup> </sup>+ 9</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> - 9</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> + 9x + x -1</span></p>\r\n<p><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\">(x - 9) - </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"><sup> </sup>(x - 9) + </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> (x - 9) + x (x - 9) + x - 1</span></p>\r\n<p><span style=\"font-weight: 400;\">x = 9 &#2352;&#2326;&#2344;&#2375; &#2346;&#2352; &#2361;&#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">0 + 9 - 1 = 8</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If 2x + y = 8 and </span><span style=\"font-family: Cambria Math;\">xy</span><span style=\"font-family: Cambria Math;\"> = 6, then find the value of 4x<sup>2 </sup></span><span style=\"font-family: Cambria Math;\">+ y<sup>2</sup></span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> 2x + y = 8 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">xy</span><span style=\"font-family: Cambria Math;\"> = 6, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 4x<sup>2 </sup>+ y<sup>2</sup></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>24</p>\n", "<p>16</p>\n", 
                                "<p>28</p>\n", "<p>40</p>\n"],
                    options_hi: ["<p>24</p>\n", "<p>16</p>\n",
                                "<p>28</p>\n", "<p>40</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">y</span><span style=\"font-weight: 400;\"> = 8</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">On squaring, we get</span></p>\r\n<p><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2 </span></sup><span style=\"font-weight: 400;\">+ 4xy + </span><span style=\"font-weight: 400;\">y</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"><sup> </sup>= 64</span></p>\r\n<p><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">y</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> = 64 - </span><span style=\"font-weight: 400;\">4xy</span><span style=\"font-weight: 400;\"> = 64 - </span><span style=\"font-weight: 400;\">4(6)</span><span style=\"font-weight: 400;\"> = 64 - 24 = 40&nbsp;</span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">y</span><span style=\"font-weight: 400;\"> = 8</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2327; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352;, &#2361;&#2350; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;</span></p>\r\n<p><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2 </span></sup><span style=\"font-weight: 400;\">+ 4xy + </span><span style=\"font-weight: 400;\">y</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"><sup> </sup>= 64</span></p>\r\n<p><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">y</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> = 64 - </span><span style=\"font-weight: 400;\">4xy</span><span style=\"font-weight: 400;\"> = 64 - </span><span style=\"font-weight: 400;\">4(6)</span><span style=\"font-weight: 400;\"> = 64 - 24 = 40&nbsp;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If a + b = 25, then the value of (a &ndash; 15)</span><sup><span style=\"font-family: Cambria Math;\">3</span></sup><span style=\"font-family: Cambria Math;\"><sup> </sup>+ (b &ndash; 10)</span><sup><span style=\"font-family: Cambria Math;\">3</span></sup><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">is</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Evening)</span></p>\n",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> a + b = 25, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">a &ndash; 15)</span><sup><span style=\"font-family: Cambria Math;\">3</span></sup><span style=\"font-family: Cambria Math;\"> + (b &ndash; 10)</span><sup><span style=\"font-family: Cambria Math;\">3 </span></sup><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Evening)</span></p>\n",
                    options_en: ["<p>125</p>\n", "<p>0</p>\n", 
                                "<p>625</p>\n", "<p>225</p>\n"],
                    options_hi: ["<p>125</p>\n", "<p><span style=\"font-family: Cambria Math;\">0</span></p>\n",
                                "<p>625</p>\n", "<p>225</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a + b - 25 = 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a + b - 15 - 10 = 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a -</span><span style=\"font-family: Cambria Math;\"> 15 = - (b - 10)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now,&nbsp; </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">a - 15</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">b - 10</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> = - </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">b - 10</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> +&nbsp; </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">b - 10</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> = 0</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a + b - 25 = 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a + b - 15 - 10 = 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">a - 15 </span><span style=\"font-family: Cambria Math;\">= -</span><span style=\"font-family: Cambria Math;\"> (b - 10)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">, <span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">a - 15</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">b - 10</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> = - </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">b - 10</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> +&nbsp; </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">b - 10</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> = 0</span></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If p</span><sup><span style=\"font-family: Cambria Math;\">3</span></sup><span style=\"font-family: Cambria Math;\"> + 3p</span><sup><span style=\"font-family: Cambria Math;\">2</span></sup><span style=\"font-family: Cambria Math;\"><sup> </sup>+ 3p = 26, then the value of p</span><sup><span style=\"font-family: Cambria Math;\">2</span></sup><span style=\"font-family: Cambria Math;\"> + 2p </span><span style=\"font-family: Cambria Math;\">is ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Evening)</span></p>\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2367;</span><span style=\"font-family: Cambria Math;\">&nbsp; p<sup>3</sup> + 3p<sup>2</sup><sup> </sup>+ 3p = 26</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> p</span><sup><span style=\"font-family: Cambria Math;\">2</span></sup><span style=\"font-family: Cambria Math;\"> + 2p </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 30/05/2022 (Evening)</span></p>\n",
                    options_en: ["<p>8</p>\n", "<p>12</p>\n", 
                                "<p>10</p>\n", "<p>15</p>\n"],
                    options_hi: ["<p>8</p>\n", "<p><span style=\"font-family: Cambria Math;\">12</span></p>\n",
                                "<p>10</p>\n", "<p>15</p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Formula </span><span style=\"font-family: Cambria Math;\">used :</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-weight: 400;\">(x +y</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> +</span><span style=\"font-weight: 400;\">y</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> + 3xy(x + y)</span></p>\r\n<p><span style=\"font-weight: 400;\">(p + 1</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"><sup> </sup>- 1 = </span><span style=\"font-weight: 400;\">p</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"><sup> </sup>+ 3</span><span style=\"font-weight: 400;\">p</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"><sup> </sup>+ 3p = 26</span></p>\r\n<p><span style=\"font-weight: 400;\">(p+1</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"><sup> </sup>= </span><span style=\"font-weight: 400;\">3</span><sup><span style=\"font-weight: 400;\">3</span></sup></p>\r\n<p><span style=\"font-family: Cambria Math;\">p + 1 = 3 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">p = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now,&nbsp; </span><span style=\"font-weight: 400;\">p</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> + 2p = </span><span style=\"font-weight: 400;\">2</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> + 2&times;2 = 4 + 4 = 8</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2340;&#2381;</span><span style=\"font-family: Nirmala UI;\">&#2352;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">(x +y</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> +</span><span style=\"font-weight: 400;\">y</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> + 3xy(x + y)</span></span></p>\r\n<p><span style=\"font-weight: 400;\">(p + 1</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"><sup> </sup>- 1 = </span><span style=\"font-weight: 400;\">p</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"><sup> </sup>+ 3</span><span style=\"font-weight: 400;\">p</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"><sup> </sup>+ 3p = 26</span></p>\r\n<p><span style=\"font-weight: 400;\">(p+1</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"><sup> </sup>= </span><span style=\"font-weight: 400;\">3</span><sup><span style=\"font-weight: 400;\">3</span></sup></p>\r\n<p><span style=\"font-family: Cambria Math;\">p + 1 = 3 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">p = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2309;&#2348;</span> &nbsp;</span><span style=\"font-weight: 400;\">p</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> + 2p = </span><span style=\"font-weight: 400;\">2</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> + 2&times;2 = 4 + 4 = 8</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If a + b = 12 and </span><span style=\"font-family: Cambria Math;\">ab</span><span style=\"font-family: Cambria Math;\"> = 35, find the value of <span style=\"font-weight: 400;\">a</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"><sup> </sup>+ </span><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"><sup> </sup>&ndash; </span><span style=\"font-weight: 400;\">10</span><sup><span style=\"font-weight: 400;\">3</span></sup></span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 24</span><span style=\"font-family: Cambria Math;\">/05/2022 Afternoon</span></p>\n",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> a + b = 12 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ab</span><span style=\"font-family: Cambria Math;\"> = 35 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">a</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"><sup> </sup>+ </span><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"><sup> </sup>&ndash; </span><span style=\"font-weight: 400;\">10</span><sup><span style=\"font-weight: 400;\">3</span></sup></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 24</span><span style=\"font-family: Cambria Math;\">/05/2022 Afternoon</span></p>\n",
                    options_en: ["<p>2226</p>\n", "<p>2026</p>\n", 
                                "<p>2126</p>\n", "<p>1226</p>\n"],
                    options_hi: ["<p>2226</p>\n", "<p>2026</p>\n",
                                "<p>2126</p>\n", "<p>1226</p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p>a<sup>2</sup><strong> </strong><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">(a+b</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - 2ab&nbsp; = </span><span style=\"font-weight: 400;\">1</span><span style=\"font-weight: 400;\">2</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - 2 &times; 35 = 144 - 70 = 74</span></p>\r\n<p><span style=\"font-weight: 400;\">a</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"><sup> </sup>+ </span><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">a</span><span style=\"font-weight: 400;\"><sup>2</sup> </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\">- 2</span><span style=\"font-weight: 400;\">a</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">7</span><span style=\"font-weight: 400;\">4</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - 2 &times; </span><span style=\"font-weight: 400;\">3</span><span style=\"font-weight: 400;\">5</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> = 5476 - 2450 = 3026</span></p>\r\n<p><span style=\"font-weight: 400;\">a</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"><sup> </sup>- </span><span style=\"font-weight: 400;\">1</span><span style=\"font-weight: 400;\">0</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> = 3026 - 1000 = 2026 </span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p>a<sup>2</sup><strong> </strong><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">(a+b</span><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - 2ab&nbsp; = </span><span style=\"font-weight: 400;\">1</span><span style=\"font-weight: 400;\">2</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - 2 &times; 35 = 144 - 70 = 74</span></p>\r\n<p><span style=\"font-weight: 400;\">a</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"><sup> </sup>+ </span><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">a</span><span style=\"font-weight: 400;\"><sup>2</sup> </span><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\">)</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\">- 2</span><span style=\"font-weight: 400;\">a</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">7</span><span style=\"font-weight: 400;\">4</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> - 2 &times; </span><span style=\"font-weight: 400;\">3</span><span style=\"font-weight: 400;\">5</span><sup><span style=\"font-weight: 400;\">2</span></sup><span style=\"font-weight: 400;\"> = 5476 - 2450 = 3026</span></p>\r\n<p><span style=\"font-weight: 400;\">a</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"> + </span><span style=\"font-weight: 400;\">b</span><sup><span style=\"font-weight: 400;\">4</span></sup><span style=\"font-weight: 400;\"><sup> </sup>- </span><span style=\"font-weight: 400;\">1</span><span style=\"font-weight: 400;\">0</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> = 3026 - 1000 = 2026</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>a</mi><mi>b</mi></mfrac><mo>+</mo><mfrac><mi>b</mi><mi>a</mi></mfrac><mo>=</mo><mo>-</mo><mn>1</mn><mo>,</mo><mo>&nbsp;</mo><mi>a</mi><mo>&ne;</mo><mn>0</mn><mo>,</mo><mi>b</mi><mo>&ne;</mo><mn>0</mn></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> then the value of 6 (a<sup>3 </sup>- b<sup>3</sup> )</span><span style=\"font-family: Cambria Math;\">is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Morning)</span></p>\n",
                    question_hi: "<p>16<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>a</mi><mi>b</mi></mfrac><mo>+</mo><mfrac><mi>b</mi><mi>a</mi></mfrac><mo>=</mo><mo>-</mo><mn>1</mn><mo>,</mo><mo>&nbsp;</mo><mi>a</mi><mo>&ne;</mo><mn>0</mn><mo>,</mo><mi>b</mi><mo>&ne;</mo><mn>0</mn></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 6 (a<sup>3 </sup>- b<sup>3</sup> ) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Morning)</span></p>\n",
                    options_en: ["<p>6</p>\n", "<p>3</p>\n", 
                                "<p>0</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>6</p>\n", "<p>3</p>\n",
                                "<p>0</p>\n", "<p>1</p>\n"],
                    solution_en: "<p>16<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mi>a</mi><mi>b</mi></mfrac><mo>+</mo><mfrac><mi>b</mi><mi>a</mi></mfrac><mo>=</mo><mo>-</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac><mo>=</mo><mo>-</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>(</mo><mn>1</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>N</mi><mi>o</mi><mi>w</mi><mo>&nbsp;</mo><mi>p</mi><mi>u</mi><mi>t</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>v</mi><mi>a</mi><mi>l</mi><mi>u</mi><mi>e</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>e</mi><mi>q</mi><mo>.</mo><mo>&nbsp;</mo><mi>n</mi><mi>o</mi><mo>.</mo><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mi>i</mi><mi>n</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mi>o</mi><mi>v</mi><mi>e</mi><mo>&nbsp;</mo><mi>f</mi><mi>o</mi><mi>r</mi><mi>m</mi><mi>u</mi><mi>l</mi><mi>a</mi><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>a</mi><mi>b</mi><mspace linebreak=\"newline\"></mspace><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi></msqrt><mspace linebreak=\"newline\"></mspace><mn>6</mn><mo>&nbsp;</mo><mo>(</mo><msup><mi>a</mi><mn>3</mn></msup><mo>-</mo><msup><mi>b</mi><mn>3</mn></msup><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msqrt><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mo>-</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><msqrt><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>0</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace></math></p>\n",
                    solution_hi: "<p>16<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mi>a</mi><mi>b</mi></mfrac><mo>+</mo><mfrac><mi>b</mi><mi>a</mi></mfrac><mo>=</mo><mo>-</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac><mo>=</mo><mo>-</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>-</mo><mo>(</mo><mn>1</mn><mo>)</mo><mspace linebreak=\"newline\"></mspace><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2309;&#2348;</mi><mo>&nbsp;</mo><mi>&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340;</mi><mo>&nbsp;</mo><mi>&#2360;&#2370;&#2340;&#2381;&#2352;</mi><mo>&nbsp;</mo><mi>&#2350;&#2375;&#2306;</mi><mo>&nbsp;</mo><mi>&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</mi><mo>&nbsp;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2350;&#2366;&#2344;</mi><mo>&nbsp;</mo><mi>&#2337;&#2366;&#2354;&#2375;&#2306;</mi><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>a</mi><mi>b</mi><mspace linebreak=\"newline\"></mspace><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msqrt><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi></msqrt><mspace linebreak=\"newline\"></mspace><mn>6</mn><mo>&nbsp;</mo><mo>(</mo><msup><mi>a</mi><mn>3</mn></msup><mo>-</mo><msup><mi>b</mi><mn>3</mn></msup><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msqrt><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi></msqrt><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>(</mo><mo>-</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><msqrt><mo>-</mo><mn>3</mn><mi>a</mi><mi>b</mi></msqrt><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>0</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn><mspace linebreak=\"newline\"></mspace></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If p<sup>4 </sup>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4354</mn><mo>-</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>4</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\">then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>3</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> can be :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Morning)</span></p>\r\n<p>&nbsp;</p>\n",
                    question_hi: "<p>17<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> p<sup>4</sup></span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4354</mn><mo>-</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>4</mn></msup></mfrac></math> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>3</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Morning)</span></p>\r\n<p>&nbsp;</p>\n",
                    options_en: ["<p>536</p>\n", "<p>436</p>\n", 
                                "<p>416</p>\n", "<p>516</p>\n"],
                    options_hi: ["<p>536</p>\n", "<p>436</p>\n",
                                "<p>416</p>\n", "<p>516</p>\n"],
                    solution_en: "<p>17<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>4</mn></msup></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>4354</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4356</mn><mspace linebreak=\"newline\"></mspace><msup><mi>p</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>2</mn></msup></mfrac><mo>=</mo><msqrt><mn>4356</mn></msqrt><mo>=</mo><mn>66</mn><mspace linebreak=\"newline\"></mspace><mi>p</mi><mo>+</mo><mfrac><mn>1</mn><mi>p</mi></mfrac><mo>=</mo><mn>66</mn><mo>-</mo><mn>2</mn><mo>=</mo><msqrt><mn>64</mn></msqrt><mo>=</mo><mn>8</mn><mspace linebreak=\"newline\"></mspace><msup><mi>p</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac><mo>=</mo><msup><mn>8</mn><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>512</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>24</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>536</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace></math></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>17<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>p</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>4</mn></msup></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>4354</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4356</mn><mspace linebreak=\"newline\"></mspace><msup><mi>p</mi><mn>2</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>2</mn></msup></mfrac><mo>=</mo><msqrt><mn>4356</mn></msqrt><mo>=</mo><mn>66</mn><mspace linebreak=\"newline\"></mspace><mi>p</mi><mo>+</mo><mfrac><mn>1</mn><mi>p</mi></mfrac><mo>=</mo><mn>66</mn><mo>-</mo><mn>2</mn><mo>=</mo><msqrt><mn>64</mn></msqrt><mo>=</mo><mn>8</mn><mspace linebreak=\"newline\"></mspace><msup><mi>p</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>p</mi><mn>3</mn></msup></mfrac><mo>=</mo><msup><mn>8</mn><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>512</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>24</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>536</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18.&nbsp;<span style=\"font-weight: 400;\">&nbsp;If x + y = 14, then the value of </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3 </span></sup><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">y</span><sup><span style=\"font-weight: 400;\">3 </span></sup><span style=\"font-weight: 400;\">+ 42xy</span><span style=\"font-weight: 400;\"> is:</span></p>\r\n<p><span style=\"font-weight: 400;\">SSC CHSL 31/05/2022 (Morning)</span></p>\n",
                    question_hi: "<p>18. <span style=\"font-weight: 400;\">&#2351;&#2342;&#2367; x + y = 14 &#2361;&#2376;, &#2340;&#2379; </span><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3 </span></sup><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">y</span><sup><span style=\"font-weight: 400;\">3 </span></sup><span style=\"font-weight: 400;\">+ 42xy</span><span style=\"font-weight: 400;\"> &#2325;&#2366; &#2350;&#2366;&#2344; &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>2744</p>\n", "<p>2644</p>\n", 
                                "<p>2742</p>\n", "<p>2714</p>\n"],
                    options_hi: ["<p>2744</p>\n", "<p>2644</p>\n",
                                "<p>2742</p>\n", "<p>2714</p>\n"],
                    solution_en: "<p>18.(a)</p>\r\n<p><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\">+</span><span style=\"font-weight: 400;\">y</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> = (x + y)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow></mfenced></math></span><span style=\"font-weight: 400;\"> = 14 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mn>14</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow></mfenced></math></span><span style=\"font-weight: 400;\">&nbsp;= 2744 - 42xy</span></p>\r\n<p>&nbsp;</p>\r\n<p><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\">+</span><span style=\"font-weight: 400;\">y</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\">+42xy</span><span style=\"font-weight: 400;\"> =&nbsp; 2744 - 42xy + 42xy = 2744</span></p>\n",
                    solution_hi: "<p>18.(a)</p>\r\n<p><span style=\"font-weight: 400;\">x</span><sup><span style=\"font-weight: 400;\">3 </span></sup><span style=\"font-weight: 400;\">+ </span><span style=\"font-weight: 400;\">y</span><sup><span style=\"font-weight: 400;\">3</span></sup><span style=\"font-weight: 400;\"> = (x + y) </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow></mfenced></math><span style=\"font-weight: 400;\">= 14 </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced open=\"[\" close=\"]\"><mrow><msup><mn>14</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>x</mi><mi>y</mi></mrow></mfenced></math><span style=\"font-weight: 400;\">= 2744 - 42xy</span></p>\r\n<p><span style=\"font-weight: 400;\">x<sup>3</sup>+y<sup>3</sup>+42xy =&nbsp; 2744 - 42xy + 42xy = 2744</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "misc",
                    question_en: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If x = 5t and y=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>(t + 1)&nbsp;</span><span style=\"font-family: Cambria Math;\"> then the value of t for which x = 3y is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> x = 5t </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> y=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>(t + 1)&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> t </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> x = 3y </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 31/05/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>19<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>(</mo><mo>&nbsp;</mo><mi>t</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>3</mn><mi>y</mi><mo>=</mo><mi>t</mi><mo>+</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mi>t</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>t</mi><mo>&nbsp;</mo><mo>+</mo><mn>1</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>4</mn><mi>t</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>t</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>\n",
                    solution_hi: "<p>19<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>(</mo><mo>&nbsp;</mo><mi>t</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>3</mn><mi>y</mi><mo>=</mo><mi>t</mi><mo>+</mo><mn>1</mn><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>5</mn><mi>t</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>t</mi><mo>&nbsp;</mo><mo>+</mo><mn>1</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mn>4</mn><mi>t</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>t</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>