<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Sangeet Natak Akademi, the national academy for music, dance and drama, was the first national academy of the arts set up by the Republic of India. This academy is located in:</p>",
                    question_hi: "<p>1. संगीत नाटक अकादमी, संगीत, नृत्य और नाटक के लिए राष्ट्रीय अकादमी, भारत गणराज्य द्वारा स्थापित कला की प्रथम राष्ट्रीय अकादमी थी। यह अकादमी ______ में स्थित है।</p>",
                    options_en: ["<p>Bengaluru</p>", "<p>New Delhi</p>", 
                                "<p>Mumbai</p>", "<p>Bhopal</p>"],
                    options_hi: ["<p>बेंगलुरु</p>", "<p>नई दिल्ली</p>",
                                "<p>मुंबई</p>", "<p>भोपाल</p>"],
                    solution_en: "<p>1.(b) <strong>New Delhi.</strong> Sangeet Natak Akademi, is the first National Academy of the arts set-up by the Republic of India. It was created by a resolution of the Ministry of Education, Government of India, dated 31 May 1952 notified in the Gazette of India of June 1952. The Sangeet Natak Akademi Awards are the highest national recognition conferred on practicing artists.</p>",
                    solution_hi: "<p>1.(b) <strong>नई दिल्ली।</strong> संगीत नाटक अकादमी, भारत गणराज्य द्वारा स्थापित प्रथम राष्ट्रीय कला अकादमी है। यह भारत सरकार के शिक्षा मंत्रालय के द्वारा 31 मई 1952 के एक संकल्प द्वारा बनाया गया था, जिसे जून 1952 के भारत के राजपत्र में अधिसूचित किया गया था। संगीत नाटक अकादमी पुरस्कार अभ्यास करने वाले कलाकारों को दी जाने वाली सर्वोच्च राष्ट्रीय मान्यता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which of the following is NOT a classical dance style?</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन-सी शास्त्रीय नृत्य शैली नहीं है?</p>",
                    options_en: ["<p>Bharatanatyam</p>", "<p>Dhamar</p>", 
                                "<p>Odissi</p>", "<p>Mohiniattam</p>"],
                    options_hi: ["<p>भरतनाट्यम</p>", "<p>धमार</p>",
                                "<p>ओडिसी</p>", "<p>मोहिनीअट्टम</p>"],
                    solution_en: "<p>2.(b) <strong>Dhamar</strong> <strong>- </strong>It is one of the talas used in Hindustani classical music. It is associated with the dhrupad style and typically played on the pakhawaj and also tabla. <strong>Eight classical dances of India</strong> (Recognized by Sangeet Natak Academy) : Bharatanatyam (Tamil Nadu), Kathak (Uttar Pradesh), Kathakali (Kerala), Kuchipudi (Andhra Pradesh), Odissi (Odisha), Sattriya (Assam), Manipuri (Manipur), Mohiniyattam (Kerala). According to India\'s Ministry of Culture, Chhau is the 9th classical dance form in India.</p>",
                    solution_hi: "<p>2.(b) <strong>धमार</strong> - यह एक प्रकार का ताल है जो हिंदुस्तानी शास्त्रीय संगीत में इस्तेमाल होता है। यह ध्रुपद शैली से जुड़ा है और आमतौर पर पखावज और तबले पर भी बजाया जाता है। <strong>भारत के आठ शास्त्रीय नृत्य</strong> (संगीत नाटक अकादमी द्वारा मान्यता प्राप्त) : भरतनाट्यम (तमिलनाडु), कथक (उत्तर प्रदेश), कथकली (केरल), कुचिपुड़ी (आंध्र प्रदेश), ओडिसी (ओडिशा), सत्रिया (असम) , मणिपुर (मणिपुर) मोहिनीअट्टम (केरल)। भारत के संस्कृति मंत्रालय के अनुसार, छऊ भारत का 9वां शास्त्रीय नृत्य है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which South Indian classical dance form literally means &lsquo;Story-Play&rsquo; ?</p>",
                    question_hi: "<p>3. किस दक्षिण भारतीय शास्त्रीय नृत्य शैली का शाब्दिक अर्थ &lsquo;स्टोरी -प्ले&rsquo; है?</p>",
                    options_en: ["<p>Kathakali</p>", "<p>Mohiniyattam</p>", 
                                "<p>Bharatanatyam</p>", "<p>Kathak</p>"],
                    options_hi: ["<p>कथकली</p>", "<p>मोहिनीअट्टम</p>",
                                "<p>भरतनाट्यम</p>", "<p>कथक</p>"],
                    solution_en: "<p>3.(a) <strong>Kathakali</strong> - It is a major dance form that hails from the state of Kerala. <strong>Mohiniyattam (Kerala)</strong> literally interpreted as the dance of \'Mohini\', the celestial enchantress of the Hindu mythology. <strong>Bharatanatyam (Tamil Nadu)</strong> dance is known as ekaharya, where one dancer takes on many roles in a single performance. <strong>Kathak,</strong> one of the most elegant dance forms of India, revolves around the concept of storytelling.</p>",
                    solution_hi: "<p>3.(a) <strong>कथकली</strong> - यह केरल राज्य की एक प्रमुख नृत्य शैली है।<strong> मोहिनीअट्टम (केरल) </strong>का शाब्दिक अर्थ हिंदू पौराणिक कथाओं की दिव्य जादूगरनी \'मोहिनी\' के नृत्य रूप में है।। <strong>भरतनाट्यम (तमिलनाडु)</strong> नृत्&zwj;य को एकहार्य के रूप में भी जाना जाता है, जहां नर्तकी एकल प्रस्&zwj;तुति में अनेक भूमिकाएं करती है। <strong>कथक,</strong> भारत के सबसे सुंदर नृत्य रूपों में से एक है जो कहानी कहने की अवधारणा के आसपास घूमता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The Indian dance form &lsquo;Manipuri&rsquo; mostly portrays themes bases on:</p>",
                    question_hi: "<p>4. भारतीय नृत्य शैली \'मणिपुरी\' में मुख्यतः किस पर आधारित प्रसंगों का चित्रण किया जाता है?</p>",
                    options_en: ["<p>Krishna-Gopis</p>", "<p>Lord Brahma</p>", 
                                "<p>Lord Shiva</p>", "<p>Lord Vishnu</p>"],
                    options_hi: ["<p>कृष्ण-गोपियां</p>", "<p>भगवान ब्रह्मा</p>",
                                "<p>भगवान शिव</p>", "<p>भगवान विष्णु</p>"],
                    solution_en: "<p>4.(a) <strong>Krishna-Gopis. Manipuri: </strong>The dance form is based on Hindu Vaishnavism themes, and exquisite performances of love-inspired dance drama of Radha-Krishna called Raas Leela. The vital elements of this dance are the characteristic symbols (Kartal or Manjira) and double-headed drum (Pung or Manipuri Mridang) of sankirtan into the visual performance. The most popular forms of Manipuri dance are the Ras, the Sankirtana and the Thang-Ta.</p>",
                    solution_hi: "<p>4.(a) <strong>कृष्ण-गोपियां। मणिपुरी:</strong> यह नृत्य शैली हिंदू वैष्णव विषयों पर आधारित है, और राधा-कृष्ण के प्रेम-प्रेरित नृत्य नाटक जिसे रासलीला कहा जाता है, और उत्कृष्ट प्रदर्शन किया जाता है। इस नृत्य के महत्वपूर्ण तत्व दृश्य प्रदर्शन में संकीर्तन के विशिष्ट प्रतीक (करतल या मंजीरा) और दो सिर वाले ड्रम (पुंग या मणिपुरी मृदंग) हैं। मणिपुरी नृत्य के सबसे लोकप्रिय रूप रास, संकीर्तन और थांग-ता हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Below are four pairs, each representing a state and a folk dance. Which pairing of state and folk dance is incorrect?</p>",
                    question_hi: "<p>5.नीचे चार जोड़े हैं, जिनमें से प्रत्येक एक राज्य और एक लोक नृत्य का प्रतिनिधित्व करता है। राज्य एवं लोक नृत्य की कौन सी जोड़ी गलत है?</p>",
                    options_en: ["<p>Assam - Bihu</p>", "<p>Gujarat - Garba</p>", 
                                "<p>Uttarakhand - Tapali</p>", "<p>Chhattisgarh - Dagla</p>"],
                    options_hi: ["<p>असम - बिहु</p>", "<p>गुजरात - गरबा</p>",
                                "<p>उत्तराखंड - तपाली</p>", "<p>छत्तीसगढ़ - डगला</p>"],
                    solution_en: "<p>5.(c) <strong>Uttarakhand - Tapali.</strong> Tapali is the folk dance of Chhattisgarh. <strong>Folk Dance: Chhattisgarh - </strong>Goudi, Karma, Jhumar, Dagla, Pali, Navrani, Diwari, Mundari.<strong> Assam -</strong> Bagurumba, Jhumura Hobjanai, Naga dance, Natpuja, Tabal Chongli. <strong>Gujarat -</strong> Bhavai, Dandiya Raas,Tippani Juriun. <strong>Uttarakhand - </strong>Chappeli, Garhwali, Kajari, Kumayuni, Jhora, Raslila.</p>",
                    solution_hi: "<p>5.(c) <strong>उत्तराखंड</strong> <strong>- तपाली।</strong> तपाली छत्तीसगढ़ का लोक नृत्य है। <strong>लोक नृत्य: छत्तीसगढ़ -</strong> गौड़ी, करमा, झूमर, डगला, पाली, नवरानी, दिवारी, मुंडारी। <strong>असम</strong> <strong>- </strong>बागुरुम्बा, झुमुरा होबजनाई, नागा नृत्य, नटपूजा, तबल चोंगली। <strong>गुजरात</strong> <strong>- </strong>भवई, डांडिया रास, टिप्पानी जुरीउन। <strong>उत्तराखंड</strong> <strong>- </strong>चैपेली, गढ़वाली, कजरी, कुमायुनी, झोरा, रासलीला।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Raut Nacha is a famous tribal dance of:</p>",
                    question_hi: "<p>6. राउत नाचा (Raut Nacha) कहां का प्रसिद्ध आदिवासी नृत्य है?</p>",
                    options_en: ["<p>Madhya Pradesh</p>", "<p>Jharkhand</p>", 
                                "<p>Odisha</p>", "<p>Chhattisgarh</p>"],
                    options_hi: ["<p>मध्य प्रदेश</p>", "<p>झारखंड</p>",
                                "<p>ओडिशा</p>", "<p>छत्तीसगढ़</p>"],
                    solution_en: "<p>6.(d) <strong>Chhattisgarh.</strong> Raut Nacha is a dance of the Yadav community. It is performed at night to celebrate Lord Krishna\'s victory over the evil King Kansa. They perform the dance at the time of \'Dev Uthni ekadashi\'. Other Folk Dances - Karma, Panthi (Performed by the Satnami community), Saila, Gendi, Danda Nacha. Other tribal dances: Santhali - Santhali tribe (West Bengal), Bamboo - Cheraw tribe (Mizoram), Kaadar Nrittham - Kaadar tribe (Kerala).</p>",
                    solution_hi: "<p>6.(d) <strong>छत्तीसगढ़।</strong> राउत नाचा यादव समुदाय का एक नृत्य है। यह रात में दुष्ट राजा कंस पर भगवान कृष्ण की विजय के उपलक्ष्य मे मनाया जाता है। वे \'देवउठनी एकादशी\' के समय नृत्य करते हैं। अन्य लोक नृत्य - करमा, पंथी (सतनामी समुदाय द्वारा प्रस्तुत), सैला, गेंदी, डंडा नाचा। अन्य आदिवासी नृत्य: संथाली - संथाली जनजाति (पश्चिम बंगाल), बांस - चेराव जनजाति (मिजोरम), कादर नृत्यम - कादर जनजाति (केरल)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Kathak (a form of ancient classical dance of India) originated in :</p>",
                    question_hi: "<p>7. कथक (भारत के प्राचीन शास्त्रीय नृत्य का एक शैली) की उत्पत्ति _____ में हुई।</p>",
                    options_en: ["<p>Chhattisgarh</p>", "<p>Madhya Pradesh</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>छत्तीसगढ़</p>", "<p>मध्य प्रदेश</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>केरल</p>"],
                    solution_en: "<p>7.(c) <strong>Uttar Pradesh.</strong> Other dances of Uttar Pradesh: Nautanki, Raslila, Kajri, Jhora, Chappeli. Famous Kathak dancers - Birju Maharaj, Lacchu Maharaj, Damyanti Joshi, Shovana Narayan. Other 7 Classical Dances : Bharatnatyam (Tamil Nadu), Kuchipudi (Andhra Pradesh), Kathakali and Mohiniattam (Kerala), Odissi (Odisha), Manipuri (Manipur), Sattriya (Assam).</p>",
                    solution_hi: "<p>7.(c) <strong>उत्तर प्रदेश।</strong> उत्तर प्रदेश के अन्य नृत्य: नौटंकी, रासलीला, कजरी, झोड़ा, छपेली। प्रसिद्ध कथक नर्तक - बिरजू महाराज, लच्छू महाराज, दमयंती जोशी, शोवना नारायण। अन्य 7 शास्त्रीय नृत्य: भरतनाट्यम (तमिलनाडु), कुचिपुड़ी (आंध्र प्रदेश), कथकली और मोहिनीअट्टम (केरल), ओडिसी (ओडिशा), मणिपुरी (मणिपुर), सत्त्रिया (असम)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. What is the folk dance of Puducherry?</p>",
                    question_hi: "<p>8. पुडुचेरी का लोक नृत्य कौन सा है?</p>",
                    options_en: ["<p>Sattariya</p>", "<p>Karga</p>", 
                                "<p>Garadi</p>", "<p>Kapalik</p>"],
                    options_hi: ["<p>सत्त्रिया</p>", "<p>करगा</p>",
                                "<p>गराड़ी</p>", "<p>कापालिक</p>"],
                    solution_en: "<p>8.(c) <strong>Garadi</strong> - Its origin is a product of mythology. When Rama defeated Ravana, the vanars (monkeys) danced to celebrate his victory. <strong>Folk dances of Chattisgarh:</strong> Raut-Nacha, Panthi, Soowa, Karma. Sattriya - Classical dance of Assam. Karagam - Folk dance of Tamil Nadu. Kapalik - Folk dance of Chhattisgarh.</p>",
                    solution_hi: "<p>8.(c) <strong>गराड़ी</strong> - इसकी उत्पत्ति पौराणिक कथाओं से हुई है। जब राम ने रावण को हराया, तो वानरो (बंदरों) ने उनकी जीत का जश्न मनाने के लिए नृत्य किया। <strong>छत्तीसगढ़ के लोक नृत्य: </strong>राउत-नाचा, पंथी, सूवा, कर्मा। सत्त्रिया - असम का शास्त्रीय नृत्य। करागम - तमिलनाडु का लोक नृत्य। कापालिक - छत्तीसगढ़ का लोक नृत्य।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Parichakali is a popular folk dance of ________</p>",
                    question_hi: "<p>9. परिचकली ________ का एक लोकप्रिय लोक नृत्य है ।</p>",
                    options_en: ["<p>Karnataka</p>", "<p>Andhra Pradesh</p>", 
                                "<p>Lakshadweep</p>", "<p>Tamil Nadu</p>"],
                    options_hi: ["<p>कर्नाटक</p>", "<p>आंध्र प्रदेश</p>",
                                "<p>लक्षद्वीप</p>", "<p>तमिलनाडु</p>"],
                    solution_en: "<p>9.(c) <strong>Lakshadweep.</strong> Parichakali is a form of martial art, the swords and shields are used as props. Other Dance of Lakshadweep - Lava Dance, Kolkali Dance. Famous folk dances of india: Yakshagana (Karnataka), Vilasini Natyam (Andhra Pradesh), Kolattam (Tamil Nadu), Raslila (Uttar Pradesh).</p>",
                    solution_hi: "<p>9.(c) <strong>लक्षद्वीप।</strong> परिचकली मार्शल आर्ट का एक रूप है, तलवारों और ढालों का उपयोग सहारा के रूप में किया जाता है। लक्षद्वीप के अन्य नृत्य - लावा नृत्य, कोलकली नृत्य। भारत के प्रसिद्ध लोक नृत्य: यक्षगान (कर्नाटक), विलासिनी नाट्यम (आंध्र प्रदेश), कोलट्टम (तमिलनाडु), रासलीला (उत्तर प्रदेश)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. With which season is the Kajri folk dance related to?</p>",
                    question_hi: "<p>10. कजरी लोक नृत्य किस ऋतु से संबंधित है ?</p>",
                    options_en: ["<p>Monsoon</p>", "<p>Autumn</p>", 
                                "<p>Spring</p>", "<p>Winter</p>"],
                    options_hi: ["<p>मानसून</p>", "<p>शरद ऋतु</p>",
                                "<p>वसंत</p>", "<p>शीत</p>"],
                    solution_en: "<p>10.(a) <strong>Monsoon.</strong> Kajri (Uttar Pradesh):- A form of folk song and dance associated with the rainy season in North India. Jhumur dance <strong>(during the autumn season)</strong> - It is a traditional dance of tea tribe communities of Assam. <strong>Rouf dance -</strong> It is associated with Jammu and Kashmir, carried out by women to welcome the spring season.</p>",
                    solution_hi: "<p>10.(a) <strong>मानसून।</strong> कजरी (उत्तर प्रदेश):- उत्तर भारत में वर्षा ऋतु से जुड़ा लोक गीत और नृत्य का एक रूप है । झुमुर नृत्य <strong>(शरद ऋतु के दौरान) -</strong> यह असम के चाय जनजाति समुदायों का एक पारंपरिक नृत्य है। <strong>रउफ नृत्य -</strong> यह जम्मू-कश्मीर से संबंधित है, जो वसंत ऋतु के स्वागत के लिए महिलाओं द्वारा किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Maruni Dance is associated with ________ state of India.</p>",
                    question_hi: "<p>11. मारुनी नृत्य भारत के ________ राज्य से सम्बंधित है।</p>",
                    options_en: ["<p>Sikkim</p>", "<p>Uttarakhand</p>", 
                                "<p>Arunachal Pradesh</p>", "<p>Tripura</p>"],
                    options_hi: ["<p>सिक्किम</p>", "<p>उत्तराखंड</p>",
                                "<p>अरुणाचल प्रदेश</p>", "<p>त्रिपुरा</p>"],
                    solution_en: "<p>11.(a) <strong>Sikkim.</strong> Maruni is a Nepalese folk dance of the Magar community. Alongside Nepal, it is popular in Nepalese diasporic communities of India (Darjeeling, Assam, Sikkim), Bhutan and Myanmar. It is performed during the festival of Tihar. <strong>Folk dances of Sikkim -</strong> Sangini, Tamangselo, Gha to Kito and Singhni Cham.</p>",
                    solution_hi: "<p>11.(a) <strong>सिक्किम</strong> । मारुनी, मगर समुदाय का एक नेपाली लोक नृत्य है। नेपाल के साथ-साथ, यह भारत (दार्जिलिंग, असम, सिक्किम), भूटान और म्यांमार के नेपाली प्रवासी समुदायों में लोकप्रिय है। यह तिहाड़ उत्सव के दौरान किया जाता है। <strong>सिक्किम के लोक नृत्य - </strong>संगिनी, तमांगसेलो, घा तो किटो और सिंघनी चाम।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which of the following best describes the dance form &lsquo;Gotipua&rsquo;?</p>",
                    question_hi: "<p>12. निम्नलिखित में से कौन नृत्य शैली \'गोटीपुआ\' का सबसे अच्छा वर्णन करता है?</p>",
                    options_en: ["<p>Dance by young boys in Odisha</p>", "<p>Girls dancing Bharatnatyam in a temple</p>", 
                                "<p>Masked dance of Chhattisgarh</p>", "<p>Story of Krishna told in Pat-Chitras</p>"],
                    options_hi: ["<p>ओडिशा में युवा लड़कों का नृत्य</p>", "<p>मंदिर में लड़कियों का भरतनाट्यम करना</p>",
                                "<p>छत्तीसगढ़ का नकाबपोश नृत्य</p>", "<p>पट-चित्रो के माध्यम से श्रीकृष्ण की कहानी बताना</p>"],
                    solution_en: "<p>12.(a) <strong>Dance by young boys in Odisha. Gotipua </strong>(Odissi classical dance) -<strong> </strong>It has been performed by Boys dancers, who dress as girls to praise Jagannath and Krishna. <strong>Other dances of Odisha - </strong>Sambalpuri, Chhau, Gotipua, Odissi, Ghumura, Chaiti Ghoda, Bagha Nacha, Paika Nrutya, Danda Nata.</p>",
                    solution_hi: "<p>12.(a) <strong>ओडिशा में युवा लड़कों का नृत्य। गोटीपुआ </strong>(ओडिसी शास्त्रीय नृत्य) -<strong> </strong>यह लड़कों के नर्तकियों द्वारा प्रस्तुत किया जाता है, जो लड़कियों का भेष धारण करके जगन्नाथ और कृष्ण की स्तुति करते हैं। <strong>ओडिशा के अन्य नृत्य -</strong> संबलपुरी, छाऊ, गोटीपुआ, ओडिसी, घुमुरा, चैती घोड़ा, बाघा नाचा, पाइका नृत्य, डंडा नाता।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. &lsquo;Dumhal&rsquo; is a dance form practiced in the ________ region.</p>",
                    question_hi: "<p>13. \'दुम्हाल\' ________ क्षेत्र में प्रचलित एक नृत्य रूप है।</p>",
                    options_en: ["<p>Kashmir</p>", "<p>Mewat</p>", 
                                "<p>Mewar</p>", "<p>Saurashtra</p>"],
                    options_hi: ["<p>कश्मीर</p>", "<p>मेवात</p>",
                                "<p>मेवाड़</p>", "<p>सौराष्ट्र</p>"],
                    solution_en: "<p>13.(a) <strong>Kashmir. Dumhal</strong> is a dance performed in the Indian territory of Jammu and Kashmir by the <strong>Watal tribe.</strong> The dance is only performed by men. Drums are used to assist the music. <strong>Mewat</strong> is a region in <strong>Haryana</strong>,<strong> Mewar</strong> is a region in <strong>Rajasthan,</strong> and <strong>Saurashtra</strong> is a region in <strong>Gujarat.</strong></p>",
                    solution_hi: "<p>13.(a) <strong>कश्मीर। दुम्हाल</strong> भारतीय क्षेत्र जम्मू और कश्मीर में वाटल जनजाति द्वारा किया जाने वाला एक नृत्य है। यह नृत्य केवल पुरुषों द्वारा किया जाता है। संगीत में ढोल का प्रयोग किया जाता है। <strong>मेवात हरियाणा </strong>का एक क्षेत्र है, <strong>मेवाड़ राजस्थान</strong> का एक क्षेत्र है, और <strong>सौराष्ट्र गुजरात </strong>का एक क्षेत्र है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. &rsquo;Dollu Kunitha&rsquo; is a form of folk dance from the state of :</p>",
                    question_hi: "<p>14. डोल्लू कुनिथा\' किस राज्य का लोक नृत्य है?</p>",
                    options_en: ["<p>Assam</p>", "<p>Karnataka</p>", 
                                "<p>Chhattisgarh</p>", "<p>West Bengal</p>"],
                    options_hi: ["<p>असम</p>", "<p>कर्नाटक</p>",
                                "<p>छत्तीसगढ़</p>", "<p>पश्चिम बंगाल</p>"],
                    solution_en: "<p>14.(b) <strong>Karnataka. Dollu Kunitha -</strong> It is a popular folk dance associated with the worship of Sri Beeralingeshwara who is considered as a form of Lord Shiva. <strong>Other Folk dances of Karnataka:</strong> Yakshagana, Kolata, Veeragase, Suggi Kunitha. <strong>Eight main classical dances of India:</strong> Bharatanatyam (Tamil Nadu), Kathak (Northern India), Kathakali (Kerala), Kuchipudi (Andhra Pradesh), Manipuri (Manipur), Mohiniyattam (Kerala): Odissi (Odisha), Sattriya (Assam).</p>",
                    solution_hi: "<p>14.(b) <strong>कर्नाटक। डोलू कुनिथा -</strong> यह श्री बीरालिंगेश्वर की पूजा से जुड़ा एक लोकप्रिय लोक नृत्य है, जिन्हें भगवान शिव का एक रूप माना जाता है। <strong>कर्नाटक के अन्य लोक नृत्य:</strong> यक्षगान, कोलाटा, वीरागसे, सुग्गी कुनिथा। <strong>भारत के आठ प्रमुख शास्त्रीय नृत्य:</strong> भरतनाट्यम (तमिलनाडु), कथक (उत्तरी भारत), कथकली (केरल), कुचिपुड़ी (आंध्र प्रदेश), मणिपुरी (मणिपुर), मोहिनीअट्टम (केरल): ओडिसी (ओडिशा), सत्त्रिया (असम)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. What is the traditional classical dance of Assam?</p>",
                    question_hi: "<p>15. इनमे से कौन सा असम का पारंपरिक शास्त्रीय नृत्य है?</p>",
                    options_en: ["<p>Sattriya Nritya</p>", "<p>Kathak</p>", 
                                "<p>Chhau</p>", "<p>Bihu</p>"],
                    options_hi: ["<p>सत्त्रिया नृत्य</p>", "<p>कथक</p>",
                                "<p>छऊ</p>", "<p>बिहु</p>"],
                    solution_en: "<p>15.(a) <strong>Sattriya Nritya.</strong> In Assam, <strong>the Vaishnava saint Shankaradeva</strong> established Sattriya dance in its contemporary form in the 15th century A.D. It is based on the <strong>Bhakti Movement.</strong> Sangeet Natak Akademi designated Sattriya as a classical dance in the year 2000. Nritta, Nritya, and Natya are all forms of Sattriya dance. <strong>List of Classical dances -</strong> Bharatanatyam (Tamil Nadu), Kathak (Uttar Pradesh), Kathakali (Kerala), Kuchipudi (Andhra Pradesh), Manipuri (Manipur), Mohiniyattam (Kerala), Odissi (Odisha).</p>",
                    solution_hi: "<p>15.(a) <strong>सत्त्रिया नृत्य।</strong> असम में, <strong>वैष्णव संत शंकरदेव</strong> ने 15वीं शताब्दी में सत्त्रिया नृत्य को उसके समकालीन रूप में स्थापित किया। यह <strong>भक्ति आंदोलन</strong> पर आधारित है। संगीत नाटक अकादमी ने वर्ष 2000 में सत्त्रिया को शास्त्रीय नृत्य के रूप में नामित किया। नारिता , नृत्य और नाट्य सभी सत्त्रिया नृत्य के रूप हैं। <strong>शास्त्रीय नृत्यों की सूची -</strong> भरतनाट्यम (तमिलनाडु), कथक (उत्तर प्रदेश), कथकली (केरल), कुचिपुड़ी (आंध्र प्रदेश), मणिपुरी (मणिपुर), मोहिनीअट्टम (केरल), ओडिसी (ओडिशा)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>