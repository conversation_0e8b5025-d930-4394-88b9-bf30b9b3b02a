<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <strong>Cloze Test:</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (1)________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (2)________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (3)________ interests and inclinations. We are not likely to abandon the city as a (4)________ institution, but we need to make sure that our transport arrangements do not (5)________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No.1.</p>",
                    question_hi: "<p>1. <strong>Cloze Test:</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (1)________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (2)________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (3)________ interests and inclinations. We are not likely to abandon the city as a (4)________ institution, but we need to make sure that our transport arrangements do not (5)________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No.1.</p>",
                    options_en: ["<p>lazy</p>", "<p>small</p>", 
                                "<p>large</p>", "<p>dump</p>"],
                    options_hi: ["<p>lazy</p>", "<p>small</p>",
                                "<p>large</p>", "<p>dump</p>"],
                    solution_en: "<p>1.(c) large<br>The given passage states that Vibrant cultures are found in cities because it takes a large population to support museums, concert halls, sports teams, and night-life districts. Hence &lsquo;large&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(c) large<br>दिए गए passage में बताया गया है कि जीवंत संस्कृतियाँ (Vibrant cultures) शहरों में पाई जाती हैं क्योंकि संग्रहालयों (museums), concert halls, sports teams और night-life districts का समर्थन करने के लिए एक बड़ी आबादी (large population) की आवश्यकता होती है। अतः &lsquo;large&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2.<strong>Cloze Test:</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (1)________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (2)________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (3)________ interests and inclinations. We are not likely to abandon the city as a (4)________ institution, but we need to make sure that our transport arrangements do not (5)________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No.2.</p>",
                    question_hi: "<p>2.<strong>Cloze Test:</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (1)________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (2)________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (3)________ interests and inclinations. We are not likely to abandon the city as a (4)________ institution, but we need to make sure that our transport arrangements do not (5)________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No.2.</p>",
                    options_en: ["<p>of</p>", "<p>with</p>", 
                                "<p>for</p>", "<p>off</p>"],
                    options_hi: ["<p>of</p>", "<p>with</p>",
                                "<p>for</p>", "<p>off</p>"],
                    solution_en: "<p>2.(a) of<br>We generally use &lsquo;of &rsquo; with &lsquo;because&rsquo; to present the reason for an action. Hence &lsquo;of&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>2.(a) of<br>हम सामान्यतः किसी Action के reason को बताने के लिए &lsquo;of&rsquo; के साथ &lsquo;because&rsquo; का प्रयोग करते हैं। अतः &lsquo;of&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. <strong>Cloze Test:</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (1)________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (2)________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (3)________ interests and inclinations. We are not likely to abandon the city as a (4)________ institution, but we need to make sure that our transport arrangements do not (5)________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No.3.</p>",
                    question_hi: "<p>3. <strong>Cloze Test:</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (1)________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (2)________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (3)________ interests and inclinations. We are not likely to abandon the city as a (4)________ institution, but we need to make sure that our transport arrangements do not (5)________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No.3.</p>",
                    options_en: ["<p>declining</p>", "<p>feigning</p>", 
                                "<p>wasteful</p>", "<p>similar</p>"],
                    options_hi: ["<p>declining</p>", "<p>feigning</p>",
                                "<p>wasteful</p>", "<p>similar</p>"],
                    solution_en: "<p>3.(d) similar<br>&lsquo;Similar&rsquo; means almost the same. The given passage states that city dwellers can choose their friends and mates from among a large number of people of similar interests and inclinations. Hence &lsquo;similar&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>3.(d) similar<br>&lsquo;Similar&rsquo; का अर्थ है लगभग समान। दिए गए passage में बताया गया है कि शहरवासी (city dwellers) बड़ी संख्या में समान रुचियों (similar interests) और प्रवृत्तियों (inclinations) वाले लोगों में से अपने मित्र और साथी चुन सकते हैं। अतः &lsquo;similar&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <strong>Cloze Test:</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (1)________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (2)________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (3)________ interests and inclinations. We are not likely to abandon the city as a (4)________ institution, but we need to make sure that our transport arrangements do not (5)________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No.4.</p>",
                    question_hi: "<p>4. <strong>Cloze Test:</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (1)________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (2)________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (3)________ interests and inclinations. We are not likely to abandon the city as a (4)________ institution, but we need to make sure that our transport arrangements do not (5)________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No.4.</p>",
                    options_en: ["<p>religious</p>", "<p>psychological</p>", 
                                "<p>cultural</p>", "<p>educational</p>"],
                    options_hi: ["<p>religious</p>", "<p>psychological</p>",
                                "<p>cultural</p>", "<p>educational</p>"],
                    solution_en: "<p>4.(c) cultural<br>&lsquo;Cultural&rsquo; mean relating to the ideas, customs, and social behaviour of a society. The given passage states that we are not likely to abandon the city as a cultural institution. Hence &lsquo;cultural&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(c) cultural<br>&lsquo;Cultural&rsquo; का अर्थ है समाज के विचारों, रीति-रिवाजों (customs) और सामाजिक व्यवहार (social behaviour) से संबंधित। दिए गए passage में बताया गया है कि हम शहर को एक सांस्कृतिक संस्था (cultural institution) के रूप में त्यागने (abandon) की संभावना नहीं रखते हैं। अतः &lsquo;cultural&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5.<strong>Cloze Test:</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (1)________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (2)________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (3)________ interests and inclinations. We are not likely to abandon the city as a (4)________ institution, but we need to make sure that our transport arrangements do not (5)________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No.5.</p>",
                    question_hi: "<p>5. <strong>Cloze Test:</strong><br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (1)________ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (2)________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (3)________ interests and inclinations. We are not likely to abandon the city as a (4)________ institution, but we need to make sure that our transport arrangements do not (5)________ the city\'s other functions.<br>Select the most appropriate option to fill in blank No.5.</p>",
                    options_en: ["<p>support</p>", "<p>boost</p>", 
                                "<p>help</p>", "<p>damage</p>"],
                    options_hi: ["<p>support</p>", "<p>boost</p>",
                                "<p>help</p>", "<p>damage</p>"],
                    solution_en: "<p>5.(d) damage<br>Damage means to cause harm to something or someone. The given passage states that we need to make sure that our transport arrangements do not damage the city\'s other functions. Hence &lsquo;damage&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(d) damage<br>&lsquo;Damage&rsquo; का अर्थ है किसी चीज़ (something) या किसी व्यक्ति (someone) को हानि या नुकसान पहुँचाना। दिए गए passage में बताया गया है कि हमें यह सुनिश्चित करने की आवश्यकता है कि हमारी परिवहन व्यवस्था (transport arrangements) शहर के अन्य कार्यों को नुकसान न पहुँचाए। अतः &lsquo;damage&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <strong>Cloze Test:</strong><br>It is generally (6)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (7)____ or nothing to do with it. But this is not true. The teaching-learning (8)______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (9)______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (10)______ studied it. <br>Select the most appropriate option to fill in blank no.6.</p>",
                    question_hi: "<p>6. <strong>Cloze Test:</strong><br>It is generally (6)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (7)____ or nothing to do with it. But this is not true. The teaching-learning (8)______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (9)______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (10)______ studied it.<br>Select the most appropriate option to fill in blank no.6.</p>",
                    options_en: ["<p>planned</p>", "<p>believed</p>", 
                                "<p>hoped</p>", "<p>wished</p>"],
                    options_hi: ["<p>planned</p>", "<p>believed</p>",
                                "<p>hoped</p>", "<p>wished</p>"],
                    solution_en: "<p>6.(b) believed<br>&lsquo;Believed&rsquo; means held as an opinion. The given passage states that it is generally believed that the methodology of teaching-learning is the concern of teachers only. Hence, &lsquo;believed&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(b) believed<br>&lsquo;Believed&rsquo; का अर्थ है एक राय (opinion) के रूप में रखा जाना। दिए गए passage में बताया गया है कि आमतौर पर यह माना जाता है कि शिक्षण-अधिगम (teaching-learning) की पद्धति (methodology) केवल शिक्षकों की चिंता है। अतः, &lsquo;believed&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <strong>Cloze Test:</strong><br>It is generally (6)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (7)____ or nothing to do with it. But this is not true. The teaching-learning (8)______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (9)______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (10)______ studied it.<br>Select the most appropriate option to fill in blank no.7.</p>",
                    question_hi: "<p>7. <strong>Cloze Test:</strong><br>It is generally (6)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (7)____ or nothing to do with it. But this is not true. The teaching-learning (8)______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (9)______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (10)______ studied it.<br>Select the most appropriate option to fill in blank no.7.</p>",
                    options_en: ["<p>sparse</p>", "<p>little</p>", 
                                "<p>light</p>", "<p>minute</p>"],
                    options_hi: ["<p>sparse</p>", "<p>little</p>",
                                "<p>light</p>", "<p>minute</p>"],
                    solution_en: "<p>7.(b) little<br>&lsquo;Little&rsquo; means almost nothing. The given passage states that learners have little or nothing to do with it. Hence, &lsquo;little&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(b) little<br>&lsquo;Little&rsquo; का अर्थ है लगभग कुछ भी नहीं (nothing)। दिए गए passage में बताया गया है कि शिक्षार्थियों (learners) का इससे बहुत कम या कोई लेना-देना नहीं है। अतः, &lsquo;little&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <strong>Cloze Test:</strong><br>It is generally (6)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (7)____ or nothing to do with it. But this is not true. The teaching-learning (8)______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (9)______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (10)______ studied it.<br>Select the most appropriate option to fill in blank no.8.</p>",
                    question_hi: "<p>8. <strong>Cloze Test:</strong><br>It is generally (6)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (7)____ or nothing to do with it. But this is not true. The teaching-learning (8)______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (9)______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (10)______ studied it.<br>Select the most appropriate option to fill in blank no.8.</p>",
                    options_en: ["<p>forms</p>", "<p>customs</p>", 
                                "<p>breaks</p>", "<p>methods</p>"],
                    options_hi: ["<p>forms</p>", "<p>customs</p>",
                                "<p>breaks</p>", "<p>methods</p>"],
                    solution_en: "<p>8.(d) methods<br>&lsquo;Method&rsquo; is a particular way of doing something. The given passage states that the teaching-learning methods are concerns of learners as well. Hence, &lsquo;methods&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(d) methods<br>&lsquo;Method&rsquo; किसी काम को करने का एक विशेष तरीका (particular way) है। दिए गए passage में बताया गया है कि शिक्षण-अधिगम विधियाँ (teaching-learning methods) शिक्षार्थियों की भी चिंता का विषय हैं। अतः, &lsquo;methods&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <strong>Cloze Test:</strong><br>It is generally (6)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (7)____ or nothing to do with it. But this is not true. The teaching-learning (8)______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (9)______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (10)______ studied it.<br>Select the most appropriate option to fill in blank no.9.</p>",
                    question_hi: "<p>9.<strong>Cloze Test:</strong><br>It is generally (6)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (7)____ or nothing to do with it. But this is not true. The teaching-learning (8)______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (9)______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (10)______ studied it.<br>Select the most appropriate option to fill in blank no.9.</p>",
                    options_en: ["<p>recurrence</p>", "<p>relevance</p>", 
                                "<p>reference</p>", "<p>consistency</p>"],
                    options_hi: ["<p>recurrence</p>", "<p>relevance</p>",
                                "<p>reference</p>", "<p>consistency</p>"],
                    solution_en: "<p>9.(b) relevance<br>&lsquo;Relevance&rsquo; means the state of being closely connected or appropriate. The given passage states that it has more relevance for the learners of physical education. Hence, &lsquo;relevance&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(b) relevance<br>&lsquo;Relevance&rsquo; का अर्थ है निकटता से जुड़े या उपयुक्त होने की स्थिति। दिए गए passage में बताया गया है कि शारीरिक शिक्षा (physical education) के शिक्षार्थियों के लिए इसकी अधिक प्रासंगिकता (relevance) है। अतः, &lsquo;relevance&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. <strong>Cloze Test:</strong><br>It is generally (6)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (7)____ or nothing to do with it. But this is not true. The teaching-learning (8)______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (9)______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (10)______ studied it.<br>Select the most appropriate option to fill in blank no.10.</p>",
                    question_hi: "<p>10. <strong>Cloze Test:</strong><br>It is generally (6)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (7)____ or nothing to do with it. But this is not true. The teaching-learning (8)______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (9)______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (10)______ studied it.<br>Select the most appropriate option to fill in blank no.10.</p>",
                    options_en: ["<p>purely</p>", "<p>entirely</p>", 
                                "<p>merely</p>", "<p>hardly</p>"],
                    options_hi: ["<p>purely</p>", "<p>entirely</p>",
                                "<p>merely</p>", "<p>hardly</p>"],
                    solution_en: "<p>10.(c) merely<br>&lsquo;Merely&rsquo; means just. The given passage states that we mean that you have actually participated in the subject area, rather than merely studied it. Hence, &lsquo;merely&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>10.(c) merely<br>&lsquo;Merely&rsquo; का अर्थ है केवल (just)। दिए गए passage में बताया गया है कि हमारा तात्पर्य यह है कि आपने विषय क्षेत्र (subject area) में वास्तव में भाग लिया (participate) है, न कि केवल उसका अध्ययन (studied) किया है। अतः, &lsquo;merely&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <strong>Cloze Test:</strong><br>On the afternoon of 30 March 1981, many Americans experienced a (11)________ of d&eacute;j&agrave;-vu. D&eacute;j&agrave;-vu is a name given to the feeling that a person has seen or experienced an event before. Many Americans could not help having that feeling on this day. The (12)________ hit TVs and radios immediately. President Ronald Raegan had been shot. He was leaving a hotel in Washington DC, turned to wave at some of the people gathered to see him, and fell towards the ground. Before he could hit the ground, his Secret Service Agents (13)________ him into the car and rushed to the nearest hospital. This same story had been heard before. On 22 November 1963, President John F Kennedy was riding in a motorcade through Dallas, Texas. He smiled and waved to the crowd that had gathered to see him. (14)________ rang out from a nearby building. The President collapsed into the backseat of the car and was immediately taken to the nearest hospital. He was (15)________ dead at the hospital. Now, just seventeen years later, the news sounded eerily the same. <br>Select the most appropriate option to fill in blank No.11.</p>",
                    question_hi: "<p>11. <strong>Cloze Test:</strong><br>On the afternoon of 30 March 1981, many Americans experienced a (11)________ of d&eacute;j&agrave;-vu. D&eacute;j&agrave;-vu is a name given to the feeling that a person has seen or experienced an event before. Many Americans could not help having that feeling on this day. The (12)________ hit TVs and radios immediately. President Ronald Raegan had been shot. He was leaving a hotel in Washington DC, turned to wave at some of the people gathered to see him, and fell towards the ground. Before he could hit the ground, his Secret Service Agents (13)________ him into the car and rushed to the nearest hospital. This same story had been heard before. On 22 November 1963, President John F Kennedy was riding in a motorcade through Dallas, Texas. He smiled and waved to the crowd that had gathered to see him. (14)________ rang out from a nearby building. The President collapsed into the backseat of the car and was immediately taken to the nearest hospital. He was (15)________ dead at the hospital. Now, just seventeen years later, the news sounded eerily the same.<br>Select the most appropriate option to fill in blank No.11.</p>",
                    options_en: ["<p>dealing</p>", "<p>blinking</p>", 
                                "<p>recalling</p>", "<p>feeling</p>"],
                    options_hi: ["<p>dealing</p>", "<p>blinking</p>",
                                "<p>recalling</p>", "<p>feeling</p>"],
                    solution_en: "<p>11.(d) feeling<br>&lsquo;Feeling&rsquo; means an emotional state or reaction. The given passage states that many Americans experienced a feeling of d&eacute;j&agrave;-vu.Hence, &lsquo;feeling&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>11.(d) feeling<br>&lsquo;Feeling&rsquo; का अर्थ है एक भावनात्मक स्थिति (emotional state) या प्रतिक्रिया(reaction)। दिए गए passage में कहा गया है कि कई अमेरिकियों ने डेजा-वु(d&eacute;j&agrave;-vu) की भावना का अनुभव किया। अतः, &lsquo;feeling&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. <strong>Cloze Test:</strong><br>On the afternoon of 30 March 1981, many Americans experienced a (11)________ of d&eacute;j&agrave;-vu. D&eacute;j&agrave;-vu is a name given to the feeling that a person has seen or experienced an event before. Many Americans could not help having that feeling on this day. The (12)________ hit TVs and radios immediately. President Ronald Raegan had been shot. He was leaving a hotel in Washington DC, turned to wave at some of the people gathered to see him, and fell towards the ground. Before he could hit the ground, his Secret Service Agents (13)________ him into the car and rushed to the nearest hospital. This same story had been heard before. On 22 November 1963, President John F Kennedy was riding in a motorcade through Dallas, Texas. He smiled and waved to the crowd that had gathered to see him. (14)________ rang out from a nearby building. The President collapsed into the backseat of the car and was immediately taken to the nearest hospital. He was (15)________ dead at the hospital. Now, just seventeen years later, the news sounded eerily the same.<br>Select the most appropriate option to fill in blank No.12.</p>",
                    question_hi: "<p>12. <strong>Cloze Test:</strong><br>On the afternoon of 30 March 1981, many Americans experienced a (11)________ of d&eacute;j&agrave;-vu. D&eacute;j&agrave;-vu is a name given to the feeling that a person has seen or experienced an event before. Many Americans could not help having that feeling on this day. The (12)________ hit TVs and radios immediately. President Ronald Raegan had been shot. He was leaving a hotel in Washington DC, turned to wave at some of the people gathered to see him, and fell towards the ground. Before he could hit the ground, his Secret Service Agents (13)________ him into the car and rushed to the nearest hospital. This same story had been heard before. On 22 November 1963, President John F Kennedy was riding in a motorcade through Dallas, Texas. He smiled and waved to the crowd that had gathered to see him. (14)________ rang out from a nearby building. The President collapsed into the backseat of the car and was immediately taken to the nearest hospital. He was (15)________ dead at the hospital. Now, just seventeen years later, the news sounded eerily the same.<br>Select the most appropriate option to fill in blank No.12.</p>",
                    options_en: ["<p>arrow</p>", "<p>ball</p>", 
                                "<p>news</p>", "<p>sceptre</p>"],
                    options_hi: ["<p>arrow</p>", "<p>ball</p>",
                                "<p>news</p>", "<p>sceptre</p>"],
                    solution_en: "<p>12.(c) news<br>&lsquo;News&rsquo; means information about current events. The given passage states that the news hit TVs and radios immediately. Hence &lsquo;news&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(c) news<br>&lsquo;News&rsquo; का अर्थ है वर्तमान घटनाओं(current events) के बारे में जानकारी(information)। दिए गए passage में कहा गया है कि News तुरंत TV और Radio पर छा जाते हैं। अतः &lsquo;news&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. <strong>Cloze Test:</strong><br>On the afternoon of 30 March 1981, many Americans experienced a (11)________ of d&eacute;j&agrave;-vu. D&eacute;j&agrave;-vu is a name given to the feeling that a person has seen or experienced an event before. Many Americans could not help having that feeling on this day. The (12)________ hit TVs and radios immediately. President Ronald Raegan had been shot. He was leaving a hotel in Washington DC, turned to wave at some of the people gathered to see him, and fell towards the ground. Before he could hit the ground, his Secret Service Agents (13)________ him into the car and rushed to the nearest hospital. This same story had been heard before. On 22 November 1963, President John F Kennedy was riding in a motorcade through Dallas, Texas. He smiled and waved to the crowd that had gathered to see him. (14)________ rang out from a nearby building. The President collapsed into the backseat of the car and was immediately taken to the nearest hospital. He was (15)________ dead at the hospital. Now, just seventeen years later, the news sounded eerily the same.<br>Select the most appropriate option to fill in blank No.13.</p>",
                    question_hi: "<p>13. <strong>Cloze Test:</strong><br>On the afternoon of 30 March 1981, many Americans experienced a (11)________ of d&eacute;j&agrave;-vu. D&eacute;j&agrave;-vu is a name given to the feeling that a person has seen or experienced an event before. Many Americans could not help having that feeling on this day. The (12)________ hit TVs and radios immediately. President Ronald Raegan had been shot. He was leaving a hotel in Washington DC, turned to wave at some of the people gathered to see him, and fell towards the ground. Before he could hit the ground, his Secret Service Agents (13)________ him into the car and rushed to the nearest hospital. This same story had been heard before. On 22 November 1963, President John F Kennedy was riding in a motorcade through Dallas, Texas. He smiled and waved to the crowd that had gathered to see him. (14)________ rang out from a nearby building. The President collapsed into the backseat of the car and was immediately taken to the nearest hospital. He was (15)________ dead at the hospital. Now, just seventeen years later, the news sounded eerily the same.<br>Select the most appropriate option to fill in blank No.13.</p>",
                    options_en: ["<p>called</p>", "<p>pulled</p>", 
                                "<p>whisked</p>", "<p>pushed</p>"],
                    options_hi: ["<p>called</p>", "<p>pulled</p>",
                                "<p>whisked</p>", "<p>pushed</p>"],
                    solution_en: "<p>13.(c) whisked<br>&lsquo;Whisked&rsquo; means to take someone somewhere else quickly. The given passage states that his Secret Service Agents whisked him into the car and rushed to the nearest hospital before he could hit the ground. Hence &lsquo;whisked&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>13.(c) whisked<br>&lsquo;Whisked&rsquo; का अर्थ है किसी को शीघ्रता से (quickly) कहीं और ले जाना। दिए गए passage में कहा गया है कि उनके सीक्रेट सर्विस एजेंट्स ने उन्हें कार में बिठाया और जमीन पर गिरने से पहले ही उन्हें निकटतम अस्पताल ले गए। अतः &lsquo;whisked&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. <strong>Cloze Test:</strong><br>On the afternoon of 30 March 1981, many Americans experienced a (11)________ of d&eacute;j&agrave;-vu. D&eacute;j&agrave;-vu is a name given to the feeling that a person has seen or experienced an event before. Many Americans could not help having that feeling on this day. The (12)________ hit TVs and radios immediately. President Ronald Raegan had been shot. He was leaving a hotel in Washington DC, turned to wave at some of the people gathered to see him, and fell towards the ground. Before he could hit the ground, his Secret Service Agents (13)________ him into the car and rushed to the nearest hospital. This same story had been heard before. On 22 November 1963, President John F Kennedy was riding in a motorcade through Dallas, Texas. He smiled and waved to the crowd that had gathered to see him. (14)________ rang out from a nearby building. The President collapsed into the backseat of the car and was immediately taken to the nearest hospital. He was (15)________ dead at the hospital. Now, just seventeen years later, the news sounded eerily the same.<br>Select the most appropriate option to fill in blank No.14.</p>",
                    question_hi: "<p>14. <strong>Cloze Test:</strong><br>On the afternoon of 30 March 1981, many Americans experienced a (11)________ of d&eacute;j&agrave;-vu. D&eacute;j&agrave;-vu is a name given to the feeling that a person has seen or experienced an event before. Many Americans could not help having that feeling on this day. The (12)________ hit TVs and radios immediately. President Ronald Raegan had been shot. He was leaving a hotel in Washington DC, turned to wave at some of the people gathered to see him, and fell towards the ground. Before he could hit the ground, his Secret Service Agents (13)________ him into the car and rushed to the nearest hospital. This same story had been heard before. On 22 November 1963, President John F Kennedy was riding in a motorcade through Dallas, Texas. He smiled and waved to the crowd that had gathered to see him. (14)________ rang out from a nearby building. The President collapsed into the backseat of the car and was immediately taken to the nearest hospital. He was (15)________ dead at the hospital. Now, just seventeen years later, the news sounded eerily the same.<br>Select the most appropriate option to fill in blank No.14.</p>",
                    options_en: ["<p>Prayers</p>", "<p>Music</p>", 
                                "<p>Alarm</p>", "<p>Shots</p>"],
                    options_hi: ["<p>Prayers</p>", "<p>Music</p>",
                                "<p>Alarm</p>", "<p>Shots</p>"],
                    solution_en: "<p>14.(d) shots<br>&lsquo;Shots&rsquo; means firing of a gun or cannon. The given passage states that shots rang out from a nearby building. Hence, &lsquo;shots&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>14.(d) shots<br>&lsquo;Shots&rsquo; का अर्थ है बंदूक या तोप(cannon) से गोली चलना। दिए गए passage में कहा गया है कि पास की इमारत से गोलियां चलीं। अतः, &lsquo;shots&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <strong>Cloze Test:</strong><br>On the afternoon of 30 March 1981, many Americans experienced a (11)________ of d&eacute;j&agrave;-vu. D&eacute;j&agrave;-vu is a name given to the feeling that a person has seen or experienced an event before. Many Americans could not help having that feeling on this day. The (12)________ hit TVs and radios immediately. President Ronald Raegan had been shot. He was leaving a hotel in Washington DC, turned to wave at some of the people gathered to see him, and fell towards the ground. Before he could hit the ground, his Secret Service Agents (13)________ him into the car and rushed to the nearest hospital. This same story had been heard before. On 22 November 1963, President John F Kennedy was riding in a motorcade through Dallas, Texas. He smiled and waved to the crowd that had gathered to see him. (14)________ rang out from a nearby building. The President collapsed into the backseat of the car and was immediately taken to the nearest hospital. He was (15)________ dead at the hospital. Now, just seventeen years later, the news sounded eerily the same.<br>Select the most appropriate option to fill in blank No.15.</p>",
                    question_hi: "<p>15. <strong>Cloze Test:</strong><br>On the afternoon of 30 March 1981, many Americans experienced a (11)________ of d&eacute;j&agrave;-vu. D&eacute;j&agrave;-vu is a name given to the feeling that a person has seen or experienced an event before. Many Americans could not help having that feeling on this day. The (12)________ hit TVs and radios immediately. President Ronald Raegan had been shot. He was leaving a hotel in Washington DC, turned to wave at some of the people gathered to see him, and fell towards the ground. Before he could hit the ground, his Secret Service Agents (13)________ him into the car and rushed to the nearest hospital. This same story had been heard before. On 22 November 1963, President John F Kennedy was riding in a motorcade through Dallas, Texas. He smiled and waved to the crowd that had gathered to see him. (14)________ rang out from a nearby building. The President collapsed into the backseat of the car and was immediately taken to the nearest hospital. He was (15)________ dead at the hospital. Now, just seventeen years later, the news sounded eerily the same.<br>Select the most appropriate option to fill in blank No.15.</p>",
                    options_en: ["<p>shot</p>", "<p>pronounced</p>", 
                                "<p>acting</p>", "<p>almost</p>"],
                    options_hi: ["<p>shot</p>", "<p>pronounced</p>",
                                "<p>acting</p>", "<p>almost</p>"],
                    solution_en: "<p>15.(b) pronounced <br>&lsquo;Pronounced&rsquo; means declared. The given passage states that he was declared dead at the hospital. Hence &lsquo;pronounced&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(b) pronounced <br>&lsquo;Pronounced&rsquo; का अर्थ है घोषित (declared)। दिए गए passage में कहा गया है कि उसे अस्पताल में मृत घोषित(declared dead) कर दिया गया था। अतः &lsquo;pronounced&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>