<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following units is used for measuring the amount of a substance ?</p>",
                    question_hi: "<p>1. निम्नलिखित में से किस इकाई का उपयोग किसी पदार्थ की मात्रा को मापने के लिए किया जाता है?</p>",
                    options_en: ["<p>Mole</p>", "<p>Tesla</p>", 
                                "<p>Joule</p>", "<p>Lux</p>"],
                    options_hi: ["<p>मोल</p>", "<p>टेस्ला</p>",
                                "<p>जूल</p>", "<p>लक्स</p>"],
                    solution_en: "<p>1.(a) <strong>Mole.</strong> It is defined as 6.022 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mn>10</mn><mn>23</mn></msup></math>particles.<strong> Units of some physical quantities </strong>: Tesla - Magnetic Flux Density. Joule - Energy or work. Candela - luminous Intensity. Lux - illuminance.</p>",
                    solution_hi: "<p>1.(a) <strong>मोल।</strong> इसे 6.022 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mn>10</mn><mn>23</mn></msup></math>कणों के रूप में परिभाषित किया गया है। <strong>कुछ भौतिक राशियों की इकाइयाँ:&nbsp;टेस्ला </strong>- चुंबकीय प्रवाह घनत्व। <strong>जूल</strong> - ऊर्जा या कार्य। <strong>कैंडेला </strong>- ज्योति तीव्रता। <strong>लक्स</strong> - ज्योति।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. What is the SI unit of force?</p>",
                    question_hi: "<p>2. बल का SI मात्रक क्या है?</p>",
                    options_en: ["<p>Pascal</p>", "<p>Newton</p>", 
                                "<p>Kip</p>", "<p>Dyne</p>"],
                    options_hi: ["<p>पास्कल</p>", "<p>न्यूटन</p>",
                                "<p>कीप</p>", "<p>डाईन</p>"],
                    solution_en: "<p>2.(b) <strong>Newton.</strong> Force = mass &times; acceleration. SI unit = kg. m/<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mo>&#160;</mo></math>= Newton. <strong>Dyne</strong> is the unit of force in the centimeter-gram-second (CGS) system. <strong>Pascal</strong> (Pa) is the SI unit of pressure.</p>",
                    solution_hi: "<p>2.(b) <strong>न्यूटन। बल=</strong> द्रव्यमान &times; त्वरण। SI इकाई = किग्रा मी/वर्ग सेकंड = न्यूटन। <strong>डाईन</strong> सेंटीमीटर-ग्राम-सेकंड (CGS) प्रणाली में बल की इकाई है। <strong>पास्कल</strong> (Pa) दाब की SI इकाई है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following devices is used to measure relatively high temperatures, such as are encountered in furnaces?</p>",
                    question_hi: "<p>3. निम्नलिखित में से किस उपकरण का उपयोग अपेक्षाकृत उच्च तापमान को मापने के लिए किया जाता है जो भट्टियों में पाया जाता है?</p>",
                    options_en: ["<p>Ammeter</p>", "<p>Fluxmeter</p>", 
                                "<p>Pyrometer</p>", "<p>Bolometer</p>"],
                    options_hi: ["<p>एमीटर</p>", "<p>फ्लक्स मीटर</p>",
                                "<p>पाइरोमीटर</p>", "<p>बोलोमीटर</p>"],
                    solution_en: "<p>3.(c) <strong>Pyrometer. Ammeter -</strong> An instrument for measuring either direct or alternating current (SI Unit - Amperes). <strong>Fluxmeter </strong>- Measure the magnetic flux of a permanent magnet. <strong>Bolometer </strong>- Measuring radiant heat by means of a material having a temperature-dependent electrical resistance.</p>",
                    solution_hi: "<p>3.(c) <strong>पाइरोमीटर</strong>। <strong>एमीटर</strong> - प्रत्यक्ष या प्रत्यावर्ती धारा (SI यूनिट - एम्पीयर) को मापने के लिए एक उपकरण है । <strong>फ्लक्समीटर</strong> - स्थायी चुंबक के चुंबकीय प्रवाह को मापता है। <strong>बोलोमीटर</strong> - तापमान पर निर्भर विद्युत प्रतिरोध वाली पदार्थ के माध्यम से दीप्तिमान ऊष्मा को मापता है ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The momentum of an object of mass \'m&lsquo; moving with a velocity &lsquo;v&lsquo; is given by:</p>",
                    question_hi: "<p>4. द्रव्यमान \'m\' और वेग &lsquo;v&lsquo; से गतिमान किसी वस्तु का संवेग क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\"><msup><mrow><mo>(</mo><mi>m</mi><mi>v</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p><math display=\"inline\"><mi mathvariant=\"bold-italic\">m</mi><mi mathvariant=\"bold-italic\">v</mi></math></p>", 
                                "<p><math display=\"inline\"><mi>m</mi><msup><mrow><mi>v</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mi>m</mi><msup><mrow><mi>v</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msup><mrow><mo>(</mo><mi>m</mi><mi>v</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p><math display=\"inline\"><mi mathvariant=\"bold-italic\">m</mi><mi mathvariant=\"bold-italic\">v</mi></math></p>",
                                "<p><math display=\"inline\"><mi>m</mi><msup><mrow><mi>v</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mi>m</mi><msup><mrow><mi>v</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    solution_en: "<p>4.(b) <strong>mv. </strong>Momentum (p) = Mass (m) x Velocity (v). <strong>SI unit: </strong>kilogram metre per second (kgm/s). Momentum is the product of the mass of a particle and its velocity. Momentum is a vector quantity (both magnitude and direction). Kinetic Energy = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>mass x (velocity)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><maction actiontype=\"argument\"><mrow/></maction><mn>2</mn></msup></math> Potential Energy = mass x gravity x height.</p>",
                    solution_hi: "<p>4.(b) <strong>mv</strong>। संवेग (p) = द्रव्यमान (m) x वेग (v)। <strong>SI यूनिट </strong>: किलोग्राम मीटर प्रति सेकंड (किलोग्राम/सेकेंड)। <strong>संवेग</strong> किसी वस्तु के द्रव्यमान और उसके वेग का गुणनफल है। संवेग एक सदिश राशि (परिमाण और दिशा दोनों) है। गतिज ऊर्जा = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>द्रव्यमान x (वेग)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><maction actiontype=\"argument\"><mrow></mrow></maction><mn>2</mn></msup></math>। स्थितिज ऊर्जा = द्रव्यमान x गुरुत्वाकर्षण x ऊँचाई।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. What is the momentum of an object having mass of 14 kg and velocity 28 m/s?</p>",
                    question_hi: "<p>5. 14 kg द्रव्यमान और 28 m/s वेग के वाली वस्तु का संवेग कितना होगा ?</p>",
                    options_en: ["<p>392 kg - m/s</p>", "<p>1/392 kg - m/s</p>", 
                                "<p>0.5 kg - m/s</p>", "<p>2 kg - m/s</p>"],
                    options_hi: ["<p>392 kg - m/s</p>", "<p>1/392 kg - m/s</p>",
                                "<p>0.5 kg - m/s</p>", "<p>2 kg - m/s</p>"],
                    solution_en: "<p>5.(a) <strong>392 kg - m/s.</strong> Momentum = Mass <math display=\"inline\"><mo>&#215;</mo></math> Velocity &rArr; 14kg &times; 28m/s = <strong>392 kg - m/s.</strong></p>",
                    solution_hi: "<p>5.(a) <strong>392</strong> <strong>किग्रा - मी/से। </strong>संवेग = द्रव्यमान <math display=\"inline\"><mo>&#215;</mo></math> वेग &rArr; 14 किग्रा &times; 28 मी/से =<strong> 392 किग्रा - मी/से।</strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. What is the SI unit of power of a lens called?</p>",
                    question_hi: "<p>6. लेंस की क्षमता का SI मात्रक क्या कहलाता है?</p>",
                    options_en: ["<p>Presbyopic.</p>", "<p>Myopic</p>", 
                                "<p>Dioptre</p>", "<p>Hypermetropia</p>"],
                    options_hi: ["<p>प्रेस्बायोपिक।</p>", "<p>मायोपिक</p>",
                                "<p>डायोप्टर</p>", "<p>हाइपरमेट्रोपिया</p>"],
                    solution_en: "<p>6.(c) <strong>Dioptre</strong> is equal to the reciprocal of the focal length measured in meter. Presbyopia is a refractive error that makes it hard for middle-aged and older adults to see things up close. <strong>Hypermetropia</strong> is a defect of vision that causes the impossibility for rays of light to be focused on the retina, but behind it.</p>",
                    solution_hi: "<p>6.(c) <strong>डायोप्टर</strong> मीटर में मापी गई फोकल लंबाई के व्युत्क्रम के बराबर होता है। प्रेस्बायोपिया एक अपवर्तक त्रुटि है जो मध्यम आयु वर्ग और वृद्ध लोगों के लिए वस्तुओं को निकट से देखना कठिनाई पैदा करती है। हाइपरमेट्रोपिया एक दृष्टि दोष है जिसके कारण प्रकाश की किरणों को रेटिना पर, लेकिन इसके पीछे केंद्रित करना असंभव हो जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. The instrument used to measure current is called &hellip;&hellip;..</p>",
                    question_hi: "<p>7. धारा को मापने के लिए जिस उपकरण का प्रयोग किया जाता है उसे_________ कहते हैं</p>",
                    options_en: ["<p>Ammeter</p>", "<p>Voltmeter</p>", 
                                "<p>Battery</p>", "<p>Meter</p>"],
                    options_hi: ["<p>एमीटर</p>", "<p>वोल्टमीटर</p>",
                                "<p>बैटरी</p>", "<p>मीटर</p>"],
                    solution_en: "<p>7.(a) <strong>Ammeter</strong>. Measuring Instruments: <strong>Voltmeter</strong> - Measures the electric potential difference between two points. <strong>Binocular</strong> - Utilised to see faraway objects. <strong>Bolometer</strong> - Measures heat radiation. Cresco graph - Measures the growth in plants. <strong>Fathometer</strong> - Computes the ocean\'s depth. <strong>Lactometer</strong> - Determines the purity of milk. <strong>Manometer</strong> - To measure the difference in pressure between two points or between a certain point and the atmosphere.</p>",
                    solution_hi: "<p>7.(a) <strong>एमीटर।</strong> मापक उपकरण: <strong>वोल्टमीटर</strong> - दो बिंदुओं के बीच विद्युत विभवांतर को मापता है। <strong>दूरबीन</strong> - दूर की वस्तुओं को देखने के लिए उपयोग किया जाता है। <strong>बोलोमीटर</strong> - ऊष्मा विकिरण को मापता है। <strong>क्रेस्को</strong> ग्राफ - पौधों में हुई वृद्धि को मापने के लिए । <strong>फ़ैदोमीटर-</strong> समुद्र की गहराई मापने के लिए । <strong>लैक्टोमीटर</strong> - दूध की शुद्धता निर्धारित करता है। <strong>मैनोमीटर</strong> - दो बिंदुओं के बीच या एक निश्चित बिंदु और वायुमंडल के बीच दाब के अंतर को मापने के लिए।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. A voltmeter is an instrument which can detect the presence of _____</p>",
                    question_hi: "<p>8. वोल्टमीटर एक उपकरण है जो _____ की उपस्थिति का पता लगा सकता है</p>",
                    options_en: ["<p>Potential difference</p>", "<p>Heat</p>", 
                                "<p>Vibrations</p>", "<p>Magnetic field</p>"],
                    options_hi: ["<p>विभवांतर</p>", "<p>ऊष्मा</p>",
                                "<p>स्पंदन</p>", "<p>चुम्बकीय क्षेत्र</p>"],
                    solution_en: "<p>8.(a)<strong> Potential difference:</strong> Scientific instruments: <strong>Ammeter</strong> - Electric current strength is measured (in Amperes). <strong>Anemometer</strong> - Wind\'s speed. <strong>Dynamometer</strong> - Computes power, torque, and force. <strong>Electroscope</strong> - Detects the presence of an electric charge. <strong>Galvanometer</strong> - Measures small-amplitude electric currents. <strong>Rectifier</strong> - Used for the conversion of AC into DC.</p>",
                    solution_hi: "<p>8.(a) <strong>विभवांतर।</strong> वैज्ञानिक उपकरण: एमीटर - विद्युत धारा की क्षमता (एम्पीयर में) मापी जाती है। एनीमोमीटर - वायु की गति। <strong>डायनेमोमीटर</strong> - शक्ति, टॉर्क और बल की गणना करता है। <strong>इलेक्ट्रोस्कोप</strong> - विद्युत आवेश की उपस्थिति का पता लगाता है। <strong>गैल्वेनोमीटर</strong> - छोटे आयाम वाली विद्युत धाराओं को मापता है। <strong>रेक्टिफायर</strong> - AC को DC में परिवर्तित करने के लिए उपयोग किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which instrument is used to show the direction of flow of current in a circuit?</p>",
                    question_hi: "<p>9. किसी परिपथ में धारा के प्रवाह की दिशा दिखाने के लिए किस यंत्र का प्रयोग किया जाता है ?</p>",
                    options_en: ["<p>Voltmeter</p>", "<p>Ammeter</p>", 
                                "<p>Galvanometer</p>", "<p>Rheostat</p>"],
                    options_hi: ["<p>वोल्टमीटर</p>", "<p>एमीटर</p>",
                                "<p>गैल्वेनोमीटर</p>", "<p>रियोस्टेट</p>"],
                    solution_en: "<p>9.(c) <strong>Galvanometer. A voltmeter </strong>- Measuring electric potential difference between two points in an electric circuit.<strong> An ammeter </strong>- Measure the current at a specific point within a circuit. <strong>Rheostat</strong> - Variable resistor which is used to control current.</p>",
                    solution_hi: "<p>9.(c) <strong>गैल्वेनोमीटर। एक वोल्टमीटर -</strong> विद्युत परिपथ में दो बिंदुओं के बीच विद्युत विभव के अंतर को मापाने के लिए किया जाता है।<strong> एक एमीट</strong>र - सर्किट के अंदर एक निश्चित बिंदु पर धारा को मापने के लिए। <strong>रिओस्टेट </strong>(Rheostat) - यह एक परिवर्तनीय अवरोधक है जिसका उपयोग धारा को नियंत्रित करने के लिए किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following is used to detect cracks and flaws in metal blocks?</p>",
                    question_hi: "<p>10. निम्नलिखित में से किसका उपयोग धातु के ब्लॉकों में छिद्र एवं दरार का पता लगाने के लिए किया जाता है?</p>",
                    options_en: ["<p>Reverberation</p>", "<p>Ultrasound</p>", 
                                "<p>Echo</p>", "<p>Sound Navigation and Ranging (SONAR)</p>"],
                    options_hi: ["<p>अनुरणन</p>", "<p>पराध्वनि</p>",
                                "<p>प्रतिध्वनि</p>", "<p>ध्वनि नेविगेशन और रेंजिंग (SONAR)</p>"],
                    solution_en: "<p>10.(b) <strong>Ultrasound</strong>. It is a useful way of examining many of the body\'s internal organs. <strong>Reverberation</strong> is the accumulation of soundwaves in a space. <strong>Ec<em>h</em>o </strong>is a sound that is a copy of another sound. <strong>SONAR </strong>(Sound Navigation and Ranging) is a technology that uses acoustical waves to sense the location of objects in the ocean.</p>",
                    solution_hi: "<p>10.(b) <strong>पराध्वनि।</strong> यह शरीर के कई आंतरिक अंगों की जांच करने का एक उपयोगी तरीका है। <strong>अनुरणन</strong> (Reverberation ) किसी स्थान में ध्वनि तरंगों का संचय है।<strong> प्रतिध्वनि (Echo)</strong> वह ध्वनि है जो किसी अन्य ध्वनि की अनुकरण होती है। <strong>SONAR</strong> (साउंड नेविगेशन एंड रेंजिंग) एक ऐसी तकनीक है जो समुद्र में वस्तुओं की सही स्थिति को ज्ञात करने के लिए ध्वनिक तरंगों का उपयोग करती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. What is the SI unit of resistance?",
                    question_hi: "11. प्रतिरोध का SI मात्रक क्या है?",
                    options_en: [" Siemens ", " Ohm ", 
                                " Mho", " Coulomb"],
                    options_hi: [" सीमेन्स (Siemens)", " ओम (Ohm)",
                                " म्हो (Mho)", " कूलॉम (Coulomb)"],
                    solution_en: "<p>11.(b) <strong>Ohm.</strong> The voltage across a conductor is directly proportional to the current flowing through it, provided all physical conditions and temperatures remain constant. <strong>Mho</strong> is a unit for the siemens. while Siemens is the SI unit of conductance. <strong>Coulomb</strong> is the SI unit of Electric Charge.</p>",
                    solution_hi: "<p>11.(b) <strong>ओम।</strong> किसी चालक पर वोल्टेज उसके माध्यम से प्रवाहित होने वाली धारा के समानुपाती होता है, बशर्ते सभी भौतिक स्थितियाँ और तापमान स्थिर रहें।<strong> म्हो (Mho) </strong>सीमेंस की एक इकाई है, जबकि सीमेंस चालकता का SI मात्रक है। <strong>कूलॉम</strong> विद्युत आवेश का SI मात्रक है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Automobiles are fitted with a device that shows the distance travelled. identify it.</p>",
                    question_hi: "<p>12. ऑटोमोबाइल में एक उपकरण लगा होता है जो तय की गई दूरी को दर्शाता है, इसे चुनिए |</p>",
                    options_en: ["<p>Odometer</p>", "<p>Autometer</p>", 
                                "<p>RPM meter</p>", "<p>Speedometer</p>"],
                    options_hi: ["<p>ओडोमीटर</p>", "<p>ऑटोमीटर</p>",
                                "<p>RPM मीटर</p>", "<p>स्पीडोमीटर</p>"],
                    solution_en: "<p>12.(a) <strong>Odometer. Autometer </strong>- A small device inserted in a photocopier to enable the process of copying to begin and to record the number of copies made. <strong>Tachometer</strong> (RPM gauge) - An instrument measuring the rotation speed of a shaft or disk, as in a motor or other machine. <strong>Speedometer</strong> - An instrument that indicates the speed of a vehicle.</p>",
                    solution_hi: "<p>12.(a) <strong>ओडोमीटर। ऑटोमीटर</strong> - प्रतिलिपि बनाने की प्रक्रिया शुरू करने और बनाई गई प्रतियों की संख्या को रिकॉर्ड करने के लिए फोटोकॉपियर में डाला गया एक छोटा उपकरण है । <strong>टैकोमीटर</strong> (RPM गेज) - एक मोटर या अन्य मशीन की तरह शाफ्ट या डिस्क की घूर्णन गति को मापने वाला उपकरण है । <strong>स्पीडोमीटर</strong> - ऐसा उपकरण जो वाहन की गति को दर्शाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following instruments is used to measure wind speed?</p>",
                    question_hi: "<p>13. हवा की गति मापने के लिए निम्न में से किस उपकरण का उपयोग किया जाता है?</p>",
                    options_en: ["<p>Udometer</p>", "<p>Anemometer</p>", 
                                "<p>Ammeter</p>", "<p>Hygrometer</p>"],
                    options_hi: ["<p>यूडोमीटर</p>", "<p>एनीमोमीटर</p>",
                                "<p>एमीटर</p>", "<p>हाइग्रोमीटर</p>"],
                    solution_en: "<p>13.(b) <strong>Anemometer </strong>- It is discovered by Leon Battista Alberti. <strong>Ammeter</strong> - An instrument used for measuring either direct (DC) or alternating (AC) electric current. <strong>Hygrometer</strong> - Measuring the humidity (Amount of water vapour in the air). <strong>Udometer</strong> - an instrument used to gather and measure the amount of liquid precipitation over a predefined area, over a period of time.</p>",
                    solution_hi: "<p>13.(b) <strong>एनीमोमीटर</strong> - इसकी खोज लियोन बत्तीस्ता अल्बर्टी ने की थी। <strong>एमीटर</strong> - एक उपकरण जिसका उपयोग प्रत्यक्ष (DC) या प्रत्यावर्ती (AC) विद्युत प्रवाह को मापने के लिए किया जाता है। <strong>हाइग्रोमीटर</strong> - आर्द्रता मापना (हवा में जलवाष्प की मात्रा)। <strong>यूडोमीटर</strong> - एक समयावधि में पूर्वनिर्धारित क्षेत्र में तरल वर्षा की मात्रा को इकट्ठा करने और मापने के लिए उपयोग किया जाने वाला उपकरण है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following units is used to measure the intensity of sound?</p>",
                    question_hi: "<p>14. ध्वनि की तीव्रता को मापने के लिए निम्नलिखित में से किस इकाई का उपयोग किया जाता है?</p>",
                    options_en: ["<p>Decibel</p>", "<p>Pascal</p>", 
                                "<p>Curie</p>", "<p>Joule</p>"],
                    options_hi: ["<p>डेसिबल</p>", "<p>पास्कल</p>",
                                "<p>क्यूरी</p>", "<p>जूल</p>"],
                    solution_en: "<p>14.(a) <strong>Decibel</strong> (a relative unit of measurement equal to one tenth of a bel).<strong> The pasca</strong>l (Pa) is the SI unit of pressure. One pascal is equivalent to 1 newton (N) of force applied over an area of 1 square meter (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math>). The unit for measuring the amount of radioactivity was the curie (Ci). <strong>Joule</strong> - SI unit of work or energy.</p>",
                    solution_hi: "<p>14.(a) <strong>डेसिबल</strong> (यह एक बेल के दसवें हिस्से के बराबर माप की एक सापेक्ष इकाई है)। <strong>पास्कल (Pa)</strong> दाब SI मात्रक है। एक पास्कल 1 वर्ग मीटर (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>m</mi><mn>2</mn></msup></math>) के क्षेत्र पर लगाए गए 1 न्यूटन (N) बल के बराबर है। रेडियोधर्मिता की मात्रा मापने की इकाई क्यूरी (Ci) थी। <strong>जूल </strong>- कार्य या ऊर्जा की SI मात्रक ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The intensity of an earthquake is measured by____</p>",
                    question_hi: "<p>15. भूकम्प की तीव्रता__________में मापी जाती है</p>",
                    options_en: ["<p>Bar</p>", "<p>Richter Scale</p>", 
                                "<p>Mercalli scale</p>", "<p>Kilogram</p>"],
                    options_hi: ["<p>बार</p>", "<p>रिक्टर स्केल</p>",
                                "<p>मर्कल्ली स्केल</p>", "<p>किलोग्राम</p>"],
                    solution_en: "<p>15.(c)<strong> Mercalli scale.</strong> <strong>Richter scale</strong> - Measure the magnitude of earthquakes. <strong>Seismographs </strong>- Record the motion of the ground during an <strong>earthquake.</strong> An earthquake is the shaking of the surface of the earth resulting from a sudden release of energy in the earth\'s lithosphere that creates seismic waves.</p>",
                    solution_hi: "<p>15.(c) <strong>रिक्टर स्केल।</strong> यह भूकंप की तीव्रता को मापता है जबकि <strong>सिस्मोग्राफ</strong> भूकंप के दौरान जमीन की गति को रिकॉर्ड करता है। <strong>भूकंप</strong> -भूकम्प पृथ्वी की सतह के हिलने को कहते हैं। यह पृथ्वी के स्थलमण्डल (लिथोस्फ़ीयर) में ऊर्जा के अचानक मुक्त हो जाने के कारण उत्पन्न होने वाली भूकम्पीय तरंगों की वजह से होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>