<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following is NOT a stringed instrument ?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन-सा तार वाला एक वाद्य-यंत्र नहीं है?</p>",
                    options_en: ["<p>Mandolin</p>", "<p>Guitar</p>", 
                                "<p>Flute</p>", "<p>Violin</p>"],
                    options_hi: ["<p>सारंगी</p>", "<p>गिटार</p>",
                                "<p>बाँसुरी</p>", "<p>वायलिन</p>"],
                    solution_en: "<p>1.(c) <strong>Flute </strong>is a wind instrument where sound is produced by vibration of the air column inside it. Instruments without strings include the tabla, drums, and harmonium. Instruments with strings include the guitar, cello, banjo, ukulele, and harp.</p>",
                    solution_hi: "<p>1.(c) <strong>बांसुरी </strong>एक वायु वाद्य यंत्र है जिसमें ध्वनि उसके अंदर मौजूद वायु स्तंभ के कंपन से उत्पन्न होती है। बिना तार वाले वाद्य यंत्रों में तबला, ड्रम और हारमोनियम शामिल हैं। तार वाले वाद्य यंत्रों में गिटार, सेलो, बैंजो, यूकुलेले और वीणा शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. According to the National Crime Records Bureau data, from 2016 to 2019, suicide&nbsp;cases due to unemployment have increased by _________.</p>",
                    question_hi: "<p>2. राष्ट्रीय अपराध रिकॉर्ड ब्यूरो (National Crime Records Bureau) के 2016 से 2019 तक आँकड़ों के&nbsp;अनुसार, बेरोजगारी के कारण आत्महत्या के मामलों में _________ की वृद्धि हुई है।</p>",
                    options_en: ["<p>24%</p>", "<p>20%</p>", 
                                "<p>18%</p>", "<p>21%</p>"],
                    options_hi: ["<p>24%</p>", "<p>20%</p>",
                                "<p>18%</p>", "<p>21%</p>"],
                    solution_en: "<p>2.(a) <strong>24%</strong>. In 2019, the National Crime Records Bureau (NCRB) reported 2,851 suicides linked to unemployment in the country. Karnataka recorded the highest number with 553 cases, followed by Maharashtra with 452 and Tamil Nadu with 251. The NCRB is an Indian government agency responsible for collecting and analyzing crime data. Established in 1986 and headquartered in New Delhi, it operates with the motto \'Empowering Indian Police with Information Technology.\'</p>",
                    solution_hi: "<p>2.(a) <strong>24%. </strong>2019 में, राष्ट्रीय अपराध रिकॉर्ड ब्यूरो (NCRB) ने देश में बेरोजगारी से जुड़ी 2,851 आत्महत्याओं की सूचना दी। कर्नाटक में सबसे ज़्यादा 553 मामले दर्ज किए गए, उसके बाद महाराष्ट्र में 452 और तमिलनाडु में 251 मामले दर्ज किए गए। NCRB एक भारतीय सरकारी एजेंसी है जो अपराध डेटा एकत्र करने और उसका विश्लेषण करने के लिए जिम्मेदार है। इसकी स्थपना 1986 में हुई थी तथा इसका मुख्यालय नई दिल्ली में है। आदर्श वाक्य - \'सूचना प्रौद्योगिकी के साथ भारतीय पुलिस को सशक्त बनाना।\'</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following is NOT one of the main pillars of the &lsquo;Namami Gange&nbsp;Programme&rsquo;, launched by the Government of India in 2014?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन-सा, वर्ष 2014 में भारत सरकार द्वारा शुरू किए गए \'नमामि गंगे कार्यक्रम\' के&nbsp;मुख्य स्तंभों में से एक नहीं है?</p>",
                    options_en: ["<p>Sewerage Treatment Infrastructure</p>", "<p>Industrial Effluent Monitoring</p>", 
                                "<p>River Linkage Development</p>", "<p>River-Front Development</p>"],
                    options_hi: ["<p>मलजल उपचार अवसंरचना (Sewerage Treatment Infrastructure)</p>", "<p>औद्योगिक अपशिष्ट निगरानी (Industrial Effluent Monitoring)</p>",
                                "<p>नदी जोड़ो विकास (River Linkage Development)</p>", "<p>रिवरफ्रंट विकास (River-Front Development)</p>"],
                    solution_en: "<p>3.(c) <strong>River Linkage Development.</strong> The \'Namami Gange Programme\' is an integrated conservation mission approved as a flagship program by the Union Government in June 2014. It has a budget outlay of Rs. 20,000 crore and aims to achieve the twin objectives of effective pollution abatement and the conservation and rejuvenation of the National River Ganga. Main pillars of the Namami Gange Programme River-Surface Cleaning, Afforestation, Ganga Gram, and Bio-Diversity.</p>",
                    solution_hi: "<p>3.(c) <strong>नदी जोड़ो विकास</strong> (River Linkage Development)। \'नमामि गंगे कार्यक्रम\' एक एकीकृत संरक्षण मिशन है जिसे जून 2014 में केंद्र सरकार द्वारा एक प्रमुख कार्यक्रम के रूप में स्वीकृत किया गया था। इसका बजट परिव्यय रु. 20,000 करोड़ रुपये और इसका लक्ष्य प्रभावी प्रदूषण उन्मूलन और राष्ट्रीय नदी गंगा के संरक्षण और कायाकल्प के दोहरे उद्देश्यों को प्राप्त करना है। नमामि गंगे कार्यक्रम के मुख्य स्तंभ नदी सतह की सफाई, वनरोपण, गंगा ग्राम और जैव-विविधता हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following dances developed in the monasteries of Assam?</p>",
                    question_hi: "<p>4. निम्नलिखि त में से कौन सा नृत्य असम के मठों (monasteries) में विकसित हुआ?</p>",
                    options_en: ["<p>Bharatanatyam</p>", "<p>Kathak</p>", 
                                "<p>Sattriya</p>", "<p>Kuchipudi</p>"],
                    options_hi: ["<p>भरतनाट्यम</p>", "<p>कथक</p>",
                                "<p>सत्रीया</p>", "<p>कुचिपुड़ी</p>"],
                    solution_en: "<p>4.(c) <strong>Sattriya </strong>originated in the Sattra (monastery) as part of the neo-Vaishnavite movement started by Srimanta Sankardev in the 15th century. He promoted &ldquo;ek sharan naam dharma&rdquo; (devotional chanting of one God&rsquo;s name). In 2000, the Sangeet Natak Akademi recognized Sattriya as a classical dance. Other classical dances of India include Bharatnatyam (Tamil Nadu), Kathakali (Kerala), Kuchipudi (Andhra Pradesh), Kathak (North India), Mohiniyattam (Kerala), Manipuri (Manipur), and Odissi (Odisha).</p>",
                    solution_hi: "<p>4.(c) <strong>सत्रीया </strong>की उत्पत्ति 15वीं शताब्दी में श्रीमंत शंकरदेव द्वारा शुरू किए गए नव-वैष्णव आंदोलन के एक भाग के रूप में सत्रा (मठ) में हुई थी। उन्होंने \"एक शरण नाम धर्म\" (एक भगवान के नाम का भक्तिपूर्वक जप) को बढ़ावा दिया। वर्ष 2000 में, संगीत नाटक अकादमी ने सत्रीया को शास्त्रीय नृत्य के रूप में मान्यता दी। भारत के अन्य शास्त्रीय नृत्यों में भरतनाट्यम (तमिलनाडु), कथकली (केरल), कुचिपुड़ी (आंध्र प्रदेश), कथक (उत्तर भारत), मोहिनीअट्टम (केरल), मणिपुरी (मणिपुर), और ओडिसी (ओडिशा) शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. When was the Swarnajayanti Gram Swarozgar Yojana (SGSY) launched by the&nbsp;Government of India?</p>",
                    question_hi: "<p>5. भारत सरकार द्वारा स्वर्णजयंती ग्राम स्वरोजगार योजना (SGSY) कब शुरू की गई थी?</p>",
                    options_en: ["<p>1999</p>", "<p>2000</p>", 
                                "<p>1995</p>", "<p>1991</p>"],
                    options_hi: ["<p>1999</p>", "<p>2000</p>",
                                "<p>1995</p>", "<p>1991</p>"],
                    solution_en: "<p>5.(a) <strong>1999</strong>. The objective of the SGSY is to bring the assisted Swarozgaris above the poverty line by providing them income generating assets through bank credit and Government subsidy. Some important Government Schemes : Pradhan Mantri Jan Dhan Yojana (2014), Pradhan Mantri Rojgar Protsahan Yojana (2016), Make in India (2014), Atal Pension Yojana (2015), Digital India (2015).</p>",
                    solution_hi: "<p>5.(a) <strong>1999</strong>. SGSY का उद्देश्य सहायता प्राप्त स्वरोजगारियों को बैंक ऋण और सरकारी सब्सिडी के माध्यम से आय उत्पन्न करने वाली परिसंपत्तियाँ प्रदान करके गरीबी रेखा से ऊपर लाना है। कुछ महत्वपूर्ण सरकारी योजनाएँ : प्रधानमंत्री जन धन योजना (2014), प्रधानमंत्री रोजगार प्रोत्साहन योजना (2016), मेक इन इंडिया (2014), अटल पेंशन योजना (2015), डिजिटल इंडिया (2015)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. In the given main characteristics of farming which is NOT a characteristic of the Indian&nbsp;commercial farming?</p>",
                    question_hi: "<p>6. कृषि संबंधी दी गई मुख्य विशेषताओं में कौन-सी, भारतीय वाणिज्यिक कृषि की विशेषता नहीं है?</p>",
                    options_en: ["<p>Slash and burn</p>", "<p>Insecticides and pesticides</p>", 
                                "<p>Chemical fertilisers</p>", "<p>High yielding variety seeds</p>"],
                    options_hi: ["<p>झूम खेती</p>", "<p>कीटनाशक और पीड़कनाशी</p>",
                                "<p>रासायनिक उर्वरक</p>", "<p>उच्च पैदावार किस्म की बीजें</p>"],
                    solution_en: "<p>6.(a) <strong>Slash and burn</strong> farming is a form of shifting agriculture where natural vegetation is cut down and burned to clear the land for cultivation. In commercial farming, higher doses of modern inputs are used to obtain higher productivity. The degree of commercialization in agriculture varies from one region to another. For example, rice is a commercial crop in Haryana and Punjab, but in Odisha, it is a subsistence crop.</p>",
                    solution_hi: "<p>6.(a) <strong>झूम खेती या स्लैश-एंड-बर्न </strong>खेती एक प्रकार की स्थानांतरण कृषि है जिसमें प्राकृतिक वनस्पति को काटकर जला दिया जाता है ताकि कृषि के लिए भूमि को साफ किया जा सके। वाणिज्यिक कृषि में, उच्च उत्पादकता प्राप्त करने के लिए आधुनिक उत्पादक सामग्री की उच्च मात्रा का उपयोग किया जाता है। कृषि में व्यावसायीकरण का स्तर एक क्षेत्र से दूसरे क्षेत्र में भिन्न-भिन्न होती है। उदाहरण के लिए, हरियाणा और पंजाब में चावल एक वाणिज्यिक फसल है, लेकिन ओडिशा में यह एक निर्वाह फसल है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. In year 2023, the Government of India launched which of the following schemes to bring Indian-origin researchers to higher educational institutions in the country?</p>",
                    question_hi: "<p>7. वर्ष 2023 में, भारत सरकार ने भारतीय मूल के शोधकर्ताओं को देश के उच्च शिक्षण संस्थानों में लाने के लिए निम्नलिखित में से कौन-सी योजना शुरू की?</p>",
                    options_en: ["<p>VAPSI</p>", "<p>AMANTRAN</p>", 
                                "<p>SEEKHO</p>", "<p>VAIBHAV</p>"],
                    options_hi: ["<p>वापसी (VAPSI)</p>", "<p>आमंत्रण (AMANTRAN)</p>",
                                "<p>सीखो (SEEKHO)</p>", "<p>वैभव (VAIBHAV)</p>"],
                    solution_en: "<p>7.(d) <strong>VAIBHAV</strong>. The Vaishvik Bhartiya Vaigyanik (VAIBHAV) Fellowships Programme aims to connect the Indian STEMM diaspora with Indian academic and R&amp;D institutions for collaborative research. Government initiatives related to education include the National Education Policy (NEP) 2020, the STARS Project, and the Shiksha Parv Initiative.</p>",
                    solution_hi: "<p>7.(d) <strong>वैभव </strong>(VAIBHAV)। वैश्विक भारतीय वैज्ञानिक (VAIBHAV) फेलोशिप कार्यक्रम का उद्देश्य भारतीय STEMM समुदाय को सहयोगी अनुसंधान के लिए भारतीय शैक्षणिक और अनुसंधान एवं विकास संस्थानों से जोड़ना है। शिक्षा से संबंधित सरकारी पहलों में राष्ट्रीय शिक्षा नीति (NEP) 2020, स्टार्स परियोजना और शिक्षा पर्व पहल शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which of the following statements is correct about the Five-Year Plan?</p>",
                    question_hi: "<p>8. पंचवर्षीय योजना के संबंध में निम्नलिखित में से कौन-सा कथन सही है?</p>",
                    options_en: ["<p>All Five-Year Plans have equal rates of growth.</p>", "<p>The President was the ex-officio chairperson of the Planning Commission.</p>", 
                                "<p>The first Five-Year Plan was started in 1951.</p>", "<p>The Planning Commission was constituted in 1951.</p>"],
                    options_hi: ["<p>सभी पंचवर्षीय योजनाओं में विकास की दर समान है।</p>", "<p>राष्ट्रपति योजना आयोग के पदेन अध्यक्ष थे।</p>",
                                "<p>पहली पंचवर्षीय योजना 1951 में शुरू की गई थी।</p>", "<p>योजना आयोग का गठन 1951 में किया गया था।</p>"],
                    solution_en: "<p>8.(c) The Planning Commission was constituted in 1950, and the first Five-Year Plan began in 1951. Based on recommendations from KC Neogy\'s advisory board, the commission formulated India\'s Five-Year Plans, with the Prime Minister as its chairman and Gulzarilal Nanda as the first deputy chairman. It was headquartered at Yojana Bhawan, New Delhi, and its concept was inspired by the Russian model introduced by Joseph Stalin.</p>",
                    solution_hi: "<p>8.(c) योजना आयोग का गठन 1950 में हुआ था और प्रथम पंचवर्षीय योजना 1951 में शुरू हुई थी। के.सी. नियोगी के सलाहकार बोर्ड की सिफारिशों के आधार पर, आयोग ने भारत की पंचवर्षीय योजनाएँ तैयार कीं, जिसके अध्यक्ष प्रधानमंत्री और प्रथम उपाध्यक्ष गुलजारीलाल नंदा थे। इसका मुख्यालय योजना भवन, नई दिल्ली में था और इसकी अवधारणा जोसेफ स्टालिन द्वारा पेश किए गए रूसी मॉडल से प्रेरित थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which power were the Badami (or Vatapi) Chalukyas originally subjects of ?</p>",
                    question_hi: "<p>9. बादामी (या वातापी) चालुक्य मूल रूप से किस शक्ति के अधीन थे?</p>",
                    options_en: ["<p>Hoysalas</p>", "<p>Pandyas</p>", 
                                "<p>Kadambas</p>", "<p>Pallavas</p>"],
                    options_hi: ["<p>होयसल</p>", "<p>पांडव</p>",
                                "<p>कदंब</p>", "<p>पल्लव</p>"],
                    solution_en: "<p>9.(c) The <strong>Kadambas </strong>ruled from Banavasi in present-day Karnataka, marking a significant period in the early medieval history of the Deccan region. Badami was the capital of the early Chalukyan dynasty, which governed the area from 543 to 598 CE. With the decline of the Vakataka rule, the Chalukyas established their dominance in the Deccan.</p>",
                    solution_hi: "<p>9.(c) <strong>कदंब </strong>वंश ने वर्तमान कर्नाटक के बनवासी क्षेत्र से शासन किया, जो दक्कन क्षेत्र के प्रारंभिक मध्ययुगीन इतिहास में एक महत्वपूर्ण काल ​​था। बादामी प्रारंभिक चालुक्य वंश की राजधानी थी, जिसने 543 से 598 ई. तक इस क्षेत्र पर शासन किया था। वाकाटक शासन के पतन के साथ, चालुक्यों ने दक्कन में अपना प्रभुत्व स्थापित किया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. &lsquo;Hampi Utsava,&rsquo; which is celebrated in Karnataka, is also called ____________. The&nbsp;festival that captures the pomp, splendour and glory of the historical period of&nbsp;Karnataka is celebrated over a week.</p>",
                    question_hi: "<p>10. &lsquo;हम्पी उत्सव\', जो कर्नाटक में मनाया जाता है, को __________ भी कहा जाता है। कर्नाटक के&nbsp;ऐतिहासिक काल की धूमधाम, वैभव और महिमा को दर्शाने वाला यह त्योहार एक सप्ताह तक मनाया&nbsp;जाता है।</p>",
                    options_en: ["<p>Vijaya Utsava</p>", "<p>Sarhul Mahotsava</p>", 
                                "<p>Rajasi Utsava</p>", "<p>Nritya Mahotsav</p>"],
                    options_hi: ["<p>विजय उत्सव (Vijaya Utsava)</p>", "<p>सरहुल महोत्सव (Sarhul Mahotsava)</p>",
                                "<p>राजसी उत्सव (Rajasi Utsava)</p>", "<p>नृत्य महोत्सव (Nritya Mahotsav)</p>"],
                    solution_en: "<p>10.(a)<strong> Vijaya Utsava</strong> celebrates the grandeur of the Vijayanagara Empire and is held over a week in Karnataka. Festivals Of Karnataka: Vairamudi Festival, Kambala Festival, Karaga Festival, Ugadi. The Sarhul festival marks the beginning of the New Year and is celebrated by the Oraon, Munda, and Ho tribes of Jharkhand.</p>",
                    solution_hi: "<p>10.(a) <strong>विजय उत्सव</strong> (Vijaya Utsava) विजयनगर साम्राज्य की भव्यता के जश्न का उत्सव है और यह उत्सव कर्नाटक में एक सप्ताह तक चलता है। कर्नाटक के प्रमुख त्यौहार: वैरामुडी महोत्सव, कंबाला महोत्सव, करगा महोत्सव, उगादी। सरहुल त्योहार नए वर्ष की शुरुआत का प्रतीक है और इसे झारखंड के उरांव, मुंडा और हो जनजातियों द्वारा मनाया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Mohammad Yunus established which of the following banks in 1983?</p>",
                    question_hi: "<p>11. मोहम्मद यूनुस ने 1983 में निम्नलिखित में से किस बैंक की स्थापना की थी?</p>",
                    options_en: ["<p>Grameen Bank</p>", "<p>Indian Bank</p>", 
                                "<p>Union Bank</p>", "<p>State Bank</p>"],
                    options_hi: ["<p>ग्रामीण बैंक</p>", "<p>इंडियन बैंक</p>",
                                "<p>यूनियन बैंक</p>", "<p>स्टेट बैंक</p>"],
                    solution_en: "<p>11.(a) <strong>Grameen Bank\'s </strong>objective is to provide small loans, known as micro-credit, to poor people on easy terms. Muhammad Yunus and Grameen Bank were awarded the Nobel Peace Prize for 2006 for their work to &ldquo;create economic and social development from below&rdquo;.</p>",
                    solution_hi: "<p>11.(a) <strong>ग्रामीण बैंक</strong> का उद्देश्य गरीब लोगों को आसान शर्तों पर छोटे ऋण प्रदान करना है जिसे माइक्रो-क्रेडिट कहा जाता है। मुहम्मद यूनुस और ग्रामीण बैंक को \"नीचे से आर्थिक और सामाजिक विकास बनाने\" के उनके काम के लिए 2006 का नोबेल शांति पुरस्कार दिया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Who among the followings won gold at the International Shooting Sport Federation (ISSF) junior world cup 2023?</p>",
                    question_hi: "<p>12. निम्नलिखित में से किसने इंटरनेशनल शूटिंग स्पोर्ट फेडरेशन (International Shooting Sport&nbsp;Federation - ISSF) जूनियर विश्व कप 2023 में स्वर्ण पदक जीता?</p>",
                    options_en: ["<p>Umamahesh Maddineni</p>", "<p>Dhanush Srikanth</p>", 
                                "<p>Mahesh Pasupathy Anandakumar</p>", "<p>Rajkanwar Singh Sandhu</p>"],
                    options_hi: ["<p>उमामहेश मद्दिनेनी</p>", "<p>धनुष श्री कांत</p>",
                                "<p>महेश पसुपति आनंदकुमार</p>", "<p>राजकंवर सिंह संधू</p>"],
                    solution_en: "<p>12.(b) <strong>Dhanush Srikanth.</strong> India topped the ISSF Junior World Cup 2023 medals tally in Suhl, Germany, with a total of 15 medals - six gold, six silver and three bronze.</p>",
                    solution_hi: "<p>12.(b) <strong>धनुष श्रीकांत।</strong> जर्मनी के सुहल में आयोजित ISSF जूनियर विश्व कप 2023 की पदक तालिका में भारत कुल 15 पदकों (छः स्वर्ण, छः रजत और तीन कांस्य पदक) के साथ शीर्ष स्थान पर रहा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which is, by far, the closest dwarf planet orbiting at only 2.8 times Earth&rsquo;s distance&nbsp;from the sun?</p>",
                    question_hi: "<p>13. सूर्य से पृथ्वी की दूरी की केवल 2.8 गुना दूरी पर परिक्रमा करने वाला अब तक का सबसे निकटतम&nbsp;बौना ग्रह कौन-सा है?</p>",
                    options_en: ["<p>Ceres</p>", "<p>Makemake</p>", 
                                "<p>Eris</p>", "<p>Pluto</p>"],
                    options_hi: ["<p>सेरेस (Ceres)</p>", "<p>मेकमेक (Makemake)</p>",
                                "<p>इरिस (Eris)</p>", "<p>प्लूटो (Pluto)</p>"],
                    solution_en: "<p>13.(a) <strong>Ceres </strong>is the largest object in the asteroid belt between Mars and Jupiter, and it\'s the only dwarf planet located in the inner solar system. It was the first member of the asteroid belt to be discovered when Giuseppe Piazzi spotted it in 1801.</p>",
                    solution_hi: "<p>13.(a) <strong>सेरेस </strong>(<strong>Ceres</strong>) मंगल और बृहस्पति के बीच क्षुद्रग्रह बेल्ट में सबसे बड़ा पिंड है, और यह आंतरिक सौरमंडल में स्थित एकमात्र बौना ग्रह है। यह क्षुद्रग्रह बेल्ट का प्रथम सदस्य था जिसे 1801 में ग्यूसेप पियाज़ी ने खोजा था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following are needed for Skype to function on a computer?<br>1) A web cam<br>2) A microphone<br>3) A speaker</p>",
                    question_hi: "<p>14. स्काइप (Skype) के कंप्यूटर पर कार्य करने के लिए निम्नलिखित में से किसकी आवश्यकता होती है?<br>1) एक वेब कैमरे<br>2) एक माइक्रोफोन<br>3) एक स्पीकर</p>",
                    options_en: ["<p>Only 1 and 2</p>", "<p>Only 2 and 3</p>", 
                                "<p>1, 2 and 3</p>", "<p>Only 1 and 3</p>"],
                    options_hi: ["<p>केवल 1 और 2</p>", "<p>केवल 2 और 3</p>",
                                "<p>1, 2 और 3</p>", "<p>केवल 1 और 3</p>"],
                    solution_en: "<p>14.(c) <strong>1, 2 and 3.</strong> Skype is a communication platform that allows users to make voice and video calls, send instant messages, and share files over the internet. A web camera (for video calls and seeing the other person). A microphone (for speaking and transmitting your voice). A speaker (for hearing the other person).</p>",
                    solution_hi: "<p>14.(c) <strong>1, 2 और 3.</strong> स्काइप (Skype) एक संचार प्लेटफ़ॉर्म है जो उपयोगकर्ताओं को वॉयस (voice) और वीडियो कॉल करने, तात्कालिक संदेश भेजने, और इंटरनेट के माध्यम से फ़ाइलें साझा करने की सुविधा देता है। वेब कैमरा (वीडियो कॉल्स और दूसरे व्यक्ति को देखने के लिए), माइक्रोफोन (बोलने और अपनी आवाज़ ट्रांसमिट करने के लिए), स्पीकर (दूसरे व्यक्ति को सुनने के लिए)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. On 26 January 1950 India became:</p>",
                    question_hi: "<p>15. 26 जनवरी 1950 को भारत ____________ बन गया।</p>",
                    options_en: ["<p>a socialist and secular state</p>", "<p>a sovereign, democratic, republic state</p>", 
                                "<p>an independent state</p>", "<p>a unitary state</p>"],
                    options_hi: ["<p>एक समाजवादी और धर्मनिरपेक्ष राज</p>", "<p>एक संप्रभु, लोकतांत्रिक, गणतंत्र राज्य</p>",
                                "<p>एक स्वतंत्र राज्य</p>", "<p>एकात्मक राज्य</p>"],
                    solution_en: "<p>15.(b) The Constituent Assembly adopted the Constitution of India, drafted by a committee headed by Dr. B.R. Ambedkar, on November 26, 1949. India became a sovereign, democratic republic when the constitution came into effect on January 26, 1950, with Dr. Rajendra Prasad as the first President. The terms \'socialist,\' \'secular,\' and \'integrity\' were later added to the Preamble of Indian Constitution in 1976 through the 42nd Constitutional Amendment.</p>",
                    solution_hi: "<p>15.(b) संविधान सभा ने 26 नवंबर, 1949 को डॉ. बी.आर. अंबेडकर की अध्यक्षता वाली समिति द्वारा तैयार किए गए भारत के संविधान को अपनाया। 26 जनवरी, 1950 को संविधान लागू होने पर भारत एक संप्रभु, लोकतांत्रिक गणराज्य बन गया, जिसके प्रथम राष्ट्रपति डॉ.राजेंद्र प्रसाद थे। \'समाजवादी\', \'धर्मनिरपेक्ष\' और \'अखंडता\' जैसे शब्दों को बाद में 1976 में 42वें संविधान संशोधन के माध्यम से भारतीय संविधान की प्रस्तावना में जोड़ा गया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. The default width of a column in MS-Excel is:</p>",
                    question_hi: "<p>16. एमएस एक्सेल (MS-Excel) में कॉलम की डिफ़ॉल्ट चौड़ाई _________ होती है।</p>",
                    options_en: ["<p>6 characters</p>", "<p>7 characters</p>", 
                                "<p>9 characters</p>", "<p>8 characters</p>"],
                    options_hi: ["<p>6 केरेक्टर्स</p>", "<p>7 केरेक्टर्स</p>",
                                "<p>9 केरेक्टर्स</p>", "<p>8 केरेक्टर्स</p>"],
                    solution_en: "<p>16.(d) <strong>8 characters.</strong> In Microsoft Excel, the default width of a column is set to accommodate 8.43 characters, which is approximately 64 pixels. Here are some of the default settings in Microsoft Excel: Row Height: 15 points (or about 20 pixels). Font: Calibri, Font Size: 11. Cell Alignment: Horizontal Alignment- General , Vertical Alignment- Bottom. Cell Fill Color: No fill (white background). Page Margins: 1 inch (2.54 cm) on all sides. Zoom Level: 100%.</p>",
                    solution_hi: "<p>16.(d) <strong>8 केरेक्टर्स । </strong>माइक्रोसॉफ्ट एक्सेल में, कॉलम की डिफ़ॉल्ट चौड़ाई 8.43 अक्षरों के लिए सेट की गई है, जो लगभग 64 पिक्सल के बराबर होती है। माइक्रोसॉफ्ट एक्सेल में कुछ डिफ़ॉल्ट सेटिंग्स यहां दी गई हैं: पंक्ति (row) की ऊँचाई: 15 प्वाइंट (या लगभग 20 पिक्सल) । फॉन्ट: कैलिब्री, फॉन्ट साइज: 11, सेल एलाइनमेंट : हॉरिजेंटल एलाइनमेंट: जनरल, वर्टिकल अलाइनमेंट: बॉटम , सेल भरने का रंग: कोई भराव नहीं (व्हाइट बैकग्राउंड), पेज मार्जिन: सभी ओर 1 इंच (2.54 सेमी), जूम लेवल (Zoom level): 100% ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. ICC World Cup (cricket) 2023 was hosted by _________</p>",
                    question_hi: "<p>17. आईसीसी विश्व कप (क्रिकेट) 2023 की मेजबानी ___________ ने की।</p>",
                    options_en: ["<p>India and Bangladesh</p>", "<p>India</p>", 
                                "<p>India and Sri Lanka</p>", "<p>India, Sri Lanka and Bangladesh</p>"],
                    options_hi: ["<p>भारत और बांग्लादेश</p>", "<p>भारत</p>",
                                "<p>भारत और श्रीलंका</p>", "<p>भारत, श्रीलंका और बांग्लादेश</p>"],
                    solution_en: "<p>17.(b) <strong>India</strong>. The ICC World Cup (Cricket) 2023 was the 13th edition of the Cricket World Cup, a quadrennial One Day International (ODI) tournament organized by the International Cricket Council (ICC). Champions: Australia (6th title), Runners-up: India. Player of the Series: Virat Kohli (India), Most Runs: Virat Kohli (765), Most Wickets: Mohammed Shami (24). The 2027 Cricket World Cup will be jointly hosted by South Africa, Zimbabwe, and Namibia.</p>",
                    solution_hi: "<p>17.(b) <strong>भारत</strong>। आईसीसी विश्व कप (क्रिकेट) 2023, क्रिकेट विश्व कप का 13वाँ संस्करण था, जो अंतर्राष्ट्रीय क्रिकेट परिषद (ICC) द्वारा आयोजित चतुर्वर्षीय एकदिवसीय अंतर्राष्ट्रीय (ODI) टूर्नामेंट है। विजेता : ऑस्ट्रेलिया (छठा खिताब), उपविजेता: भारत। प्लेयर ऑफ़ द सीरीज़: विराट कोहली (भारत), सबसे ज़्यादा रन: विराट कोहली (765), सबसे ज़्यादा विकेट: मोहम्मद शमी (24)। 2027 क्रिकेट विश्व कप की मेज़बानी दक्षिण अफ़्रीका, ज़िम्बाब्वे और नामीबिया संयुक्त रूप से करेंगे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. According to Global Multidimensional poverty index (2022), which of the following groups of states is included in top 10 poorest states of India?</p>",
                    question_hi: "<p>18. वैश्विक बहुआयामी गरीबी सूचकांक (2022) के अनुसार, निम्नलिखित में से राज्यों का कौन-सा समूह&nbsp;भारत के शीर्ष 10 सबसे गरीब राज्यों में शामिल है?</p>",
                    options_en: ["<p>Karnataka and Tamil Nadu</p>", "<p>Manipur and Nagaland</p>", 
                                "<p>Meghalaya and Madhya Pradesh</p>", "<p>Punjab and Gujarat</p>"],
                    options_hi: ["<p>कर्नाटक और तमिलनाडु</p>", "<p>मणिपुर और नागालैंड</p>",
                                "<p>मेघालय और मध्य प्रदेश</p>", "<p>पंजाब और गुजरात</p>"],
                    solution_en: "<p>18.(c) <strong>Meghalaya and Madhya Pradesh.</strong> The Global Multidimensional Poverty Index (MPI) 2022 was released by the United Nations Development Programme (UNDP) and the Oxford Poverty and Human Development Initiative (OPHI). Bihar, Jharkhand, and Uttar Pradesh are identified as the poorest states in India according to this report.</p>",
                    solution_hi: "<p>18.(c) <strong>मेघालय और मध्य प्रदेश।</strong> संयुक्त राष्ट्र विकास कार्यक्रम (UNDP) एवं ऑक्सफोर्ड गरीबी और मानव विकास पहल (OPHI) द्वारा वैश्विक बहुआयामी गरीबी सूचकांक (MPI) 2022 जारी किया गया। इस रिपोर्ट के अनुसार बिहार, झारखंड और उत्तर प्रदेश को भारत के सबसे गरीब राज्यों के रूप में पहचाना गया है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. In which year did Robert Brown observe the zigzag movement of colloidal particles in&nbsp;solution?</p>",
                    question_hi: "<p>19. रॉबर्ट ब्राउन ने विलयन में कोलॉइडी कणों की टेढ़ि-मेढ़ी (zigzag) गति का निरीक्षण किस वर्ष किया था?</p>",
                    options_en: ["<p>1829</p>", "<p>1827</p>", 
                                "<p>1828</p>", "<p>1826</p>"],
                    options_hi: ["<p>1829</p>", "<p>1827</p>",
                                "<p>1828</p>", "<p>1826</p>"],
                    solution_en: "<p>19.(b) <strong>1827</strong>. Robert Brown observed the zigzag movement of colloidal particles, known as Brownian motion. This phenomenon involves the random movement of particles suspended in a fluid, caused by collisions with fast atoms or molecules in the gas or liquid. Brownian motion provided important evidence for the kinetic theory of heat and the existence of atoms and molecules.</p>",
                    solution_hi: "<p>19.(b) <strong>1827</strong>. रॉबर्ट ब्राउन ने कोलाइडी कणों की टेढ़ी-मेढ़ी गति देखी, जिसे ब्राउनी गति के नाम से जाना जाता है। इस घटना में तरल पदार्थ में निलंबित कणों की अनियमित गति शामिल होती है, जो गैस या तरल में परमाणुओं या अणुओं के साथ तीव्र टकराव के कारण होती है। ब्राउनी गति ने ऊष्मा के गतिज सिद्धांत तथा परमाणुओं और अणुओं के अस्तित्व के लिए महत्वपूर्ण साक्ष्य प्रदान किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. _________ is one of the first criticism of the caste system written by Mahatma Phule&nbsp;and published in the year 1873.</p>",
                    question_hi: "<p>20. _________ महात्मा फुले द्वारा लिखित और वर्ष 1873 में प्रकाशित जाति व्यवस्था की पहली आलोचना में से एक है।</p>",
                    options_en: ["<p>Jati ka Unmoolan</p>", "<p>Bharat Mein Jaati Evam Prajaati</p>", 
                                "<p>Gulami Ki Kahani</p>", "<p>Gulamgiri</p>"],
                    options_hi: ["<p>जाति का उन्मूलन</p>", "<p>भारत में जाति एवं प्रजाति</p>",
                                "<p>गुलामी की कहानी</p>", "<p>गुलामगिरी</p>"],
                    solution_en: "<p>20.(d) <strong>Gulamgiri</strong>, also known as Slavery, summarizes the sufferings of lower-caste people and is written in Marathi. Phule formed the Satyashodhak Samaj in 1873. He, together with his wife Savitribai Phule, opened a girls\' school in Poona.</p>",
                    solution_hi: "<p>20.(d) <strong>गुलामगिरी</strong>, जिसे दास-प्रथा के नाम से भी जाना जाता है, जो निम्न जाति के लोगों की पीड़ा का सारांश प्रस्तुत करता है और मराठी में लिखा गया है। ज्योतिबा फुले ने 1873 में सत्यशोधक समाज की स्थापना की थी। उन्होंने अपनी पत्नी सावित्रीबाई फुले के साथ मिलकर पूना में एक बालिका विद्यालय खोला था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Which of the following parts of the Indian Constitution is described as the &lsquo;Magna&nbsp;Carta of India&rsquo;?</p>",
                    question_hi: "<p>21. भारतीय संविधान के निम्नलिखित में से किस भाग को \'भारत का मैग्ना कार्टा (Magna Carta of India)\' कहा जाता है?</p>",
                    options_en: ["<p>Part III</p>", "<p>Part I</p>", 
                                "<p>Part IV</p>", "<p>Part II</p>"],
                    options_hi: ["<p>भाग III</p>", "<p>भाग I</p>",
                                "<p>भाग IV</p>", "<p>भाग II</p>"],
                    solution_en: "<p>21.(a) <strong>Part III </strong>of the Indian Constitution enshrines the Fundamental Rights, comprising Articles 12&ndash;35. They are inspired by the USA&rsquo;s Bill of Rights. Part I deals with the Union and its Territory. Part II - Citizenship (Articles 5&ndash;11). Part IV - the Directive Principles of State Policy (Articles 36&ndash;51).</p>",
                    solution_hi: "<p>21.(a) भारतीय संविधान के <strong>भाग III</strong> में मौलिक अधिकार शामिल हैं, जिनमें अनुच्छेद 12-35 शामिल हैं। यह संयुक्त राज्य अमेरिका के बिल ऑफ़ राइट्स से प्रेरित हैं। भाग I संघ और उसके क्षेत्र से संबंधित है। भाग II - नागरिकता (अनुच्छेद 5-11)। भाग IV - राज्य नीति के निर्देशक सिद्धांत (अनुच्छेद 36-51)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. The song &lsquo;Teri mitti&rsquo; is from which of the following movies?</p>",
                    question_hi: "<p>22. \'तेरी मिट्टी \' गीत निम्नलिखित में से किस फिल्म का है?</p>",
                    options_en: ["<p>Jai Ho</p>", "<p>Kesari</p>", 
                                "<p>Satya Mev Jayte</p>", "<p>Rajniti</p>"],
                    options_hi: ["<p>जय हो</p>", "<p>केसरी</p>",
                                "<p>सत्यमेव जयते</p>", "<p>राजनीति</p>"],
                    solution_en: "<p>22.(b) <strong>Kesari</strong>. It is based on the true story of the Battle of Saragarhi in 1897. The film was directed by Anurag Singh. &lsquo;Teri mitti&rsquo; song : Writer - Manoj Muntashir. Composer - Arko Pravo Mukherjee. Singer - B Praak.</p>",
                    solution_hi: "<p>22.(b) <strong>केसरी</strong>। यह 1897 में सारागढ़ी की लड़ाई की सच्ची कहानी पर आधारित है। इस फिल्म का निर्देशन अनुराग सिंह ने किया था। &lsquo;तेरी मिट्टी&rsquo; गीत : लेखक - मनोज मुंतशिर। संगीतकार - अर्को प्रावो मुखर्जी। गायक - बी प्राक।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Who was the chief coordinator for the G20 summit which held in India in 2023?</p>",
                    question_hi: "<p>23. 2023 में भारत में आयोजित G20 शिखर सम्मेलन के मुख्य समन्वयक (chief coordinator) कौन थे?</p>",
                    options_en: ["<p>Vijay Gokhale</p>", "<p>S Jaishankar</p>", 
                                "<p>Harsh Vardhan Shringla</p>", "<p>Vinay Kwatra</p>"],
                    options_hi: ["<p>विजय गोखले</p>", "<p>एस. जयशंकर</p>",
                                "<p>हर्षवर्धन श्रृंगला</p>", "<p>विनय क्वात्रा</p>"],
                    solution_en: "<p>23.(c) <strong>Harsh Vardhan Shringla. </strong>The G20 is an international forum comprising governments and central bank governors from 19 countries, the European Union (EU), and the African Union (AU). Founded in 1999, the G20 addresses global economic and financial issues. The 19th G20 Summit will be held in November 2024 in Rio de Janeiro, Brazil.</p>",
                    solution_hi: "<p>23.(c) <strong>हर्षवर्धन श्रृंगला।</strong> G20 एक अंतर्राष्ट्रीय मंच है जिसमें 19 देशों, यूरोपीय संघ (EU) और अफ्रीकी संघ (AU) की सरकारें और केंद्रीय बैंक के गवर्नर शामिल हैं। 1999 में स्थापित, G20 वैश्विक आर्थिक और वित्तीय मुद्दों पर ध्यान देता है। 19वां G20 शिखर सम्मेलन नवंबर 2024 में ब्राजील के रियो डी जनेरियो में आयोजित किया जाएगा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Which of the following statements about eutrophication is correct?</p>",
                    question_hi: "<p>24. यूट्रोफिकेशन के बारे में निम्नलिखित में से कौन-सा कथन सही है?</p>",
                    options_en: ["<p>It happens when too much nitrogen enriches the water, causing excessive growth of&nbsp;plants and algae.</p>", "<p>It happens when too much carbon enriches the water, causing excessive growth of&nbsp;bacteria and reduced growth of plants.</p>", 
                                "<p>It happens when too less nitrogen is present in the water, causing reduced growth of&nbsp;plants and algae.</p>", "<p>It is a beneficial process for environment restoration.</p>"],
                    options_hi: ["<p>यह तब होता है जब बहुत अधिक नाइट्रोजन पानी को समृद्ध करती है, जिससे पौधों और शैवाल की&nbsp;अत्यधिक वृद्धि होती है।</p>", "<p>यह तब होता है जब बहुत अधिक कार्बन पानी को समृद्ध करता है, जिससे बैक्टीरिया का अत्यधिक विकास होता है और पौधों की बढ़त कम हो जाती है।</p>",
                                "<p>यह तब होता है जब पानी में बहुत कम नाइट्रोजन मौजूद होता है, जिससे पौधों और शैवाल की वृद्धि&nbsp;कम हो जाती है।</p>", "<p>यह पर्यावरण की बहाली के लिए एक लाभकारी प्रक्रिया है।</p>"],
                    solution_en: "<p>24.(a) <strong>Eutrophication</strong>. It is the process where excess nutrients, particularly nitrogen and phosphorus, lead to an overgrowth of algae and aquatic plants. This growth can deplete oxygen in the water, harming aquatic life.</p>",
                    solution_hi: "<p>24.(a) <strong>यूट्रोफिकेशन</strong>। यह वह प्रक्रिया है जिसमें अतिरिक्त पोषक तत्व, विशेष रूप से नाइट्रोजन और फास्फोरस, शैवाल और जलीय पौधों की अत्यधिक वृद्धि का कारण बनते हैं। इसकी वृद्धि से जल में ऑक्सीजन की कमी हो सकती है, जिसके कारण जलीय जीवन प्रभावित हो सकता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Nehru Institute of Mountaineering is located at ________.</p>",
                    question_hi: "<p>25. नेहरू पर्वतारोहण संस्थान (Nehru Institute of Mountaineering) ________ में स्थित है।</p>",
                    options_en: ["<p>Jammu and Kashmir</p>", "<p>Himachal Pradesh</p>", 
                                "<p>Uttarakhand</p>", "<p>Leh</p>"],
                    options_hi: ["<p>जम्मू और कश्मीर</p>", "<p>हिमाचल प्रदेश</p>",
                                "<p>उत्तराखंड</p>", "<p>लेह</p>"],
                    solution_en: "<p>25.(c) <strong>Uttarakhand</strong>. The Nehru Institute of Mountaineering (NIM) was founded in 1965. The Himalayan Mountaineering Institute, located in Darjeeling, West Bengal. It was founded on November 4, 1954, by Pandit Jawaharlal Nehru to commemorate the first successful ascent of Mount Everest by Tenzing Norgay Sherpa and Sir Edmund Hillary.</p>",
                    solution_hi: "<p>25.(c) <strong>उत्तराखंड</strong>। नेहरू पर्वतारोहण संस्थान (NIM) की स्थापना 1965 में हुई थी। हिमालयन पर्वतारोहण संस्थान, पश्चिम बंगाल के दार्जिलिंग में स्थित है। इसकी स्थापना 4 नवंबर, 1954 को पंडित जवाहरलाल नेहरू ने तेनजिंग नोर्गे शेरपा और सर एडमंड हिलेरी द्वारा माउंट एवरेस्ट पर प्रथम सफलतापूर्ण चढ़ाई की स्मृति में की गई थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>