<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. A, B, C, D, E, F and G sit around a circular table facing the centre. F sits to the immediate left of G. E sits to the immediate left of C. C sits second to the left of A. B sits third to the left of G. How many people sit between F and E when counted from the left of E ?</p>",
                    question_hi: "<p>1. A, B, C, D, E, F और G एक गोल मेज के परितः उसके केंद्र की ओर अभिमुख होकर बैठे हैं। F, G के ठीक बाएं पड़ोस में बैठा है। E, C के ठीक बाएं पड़ोस में बैठा है। C, A के बाएं से दूसरे स्थान पर बैठा है। B, G के बाएं से तीसरे स्थान पर बैठा है। E के बाएं से गिनने पर F और E के बीच कितने लोग बैठे हैं?</p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>1.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799589072.png\" alt=\"rId4\" width=\"148\" height=\"133\"><br>Hence, two people sit between F and E when counted from left of E.</p>",
                    solution_hi: "<p>1.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799589072.png\" alt=\"rId4\" width=\"148\" height=\"133\"><br>अतः, E के बायीं ओर से गिनती करने पर F और E के बीच दो व्यक्ति बैठे हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. What should come in place of the question mark (?) in the given series based on the English alphabetical order ?<br>RTW PRU NPS LNQ ?</p>",
                    question_hi: "<p>2. अंग्रेजी वर्णानुक्रम पर आधारित दी गई श्रृंखला में प्रश्नवाचक चिह्न (?) के स्थान पर क्या आएगा?<br>RTW PRU NPS LNQ ?</p>",
                    options_en: [
                        "<p>JLO</p>",
                        "<p>JKO</p>",
                        "<p>JKP</p>",
                        "<p>JLP</p>"
                    ],
                    options_hi: [
                        "<p>JLO</p>",
                        "<p>JKO</p>",
                        "<p>JKP</p>",
                        "<p>JLP</p>"
                    ],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799589250.png\" alt=\"rId5\" width=\"276\" height=\"90\"></p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799589250.png\" alt=\"rId5\" width=\"276\" height=\"90\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799589619.png\" alt=\"rId6\" width=\"295\" height=\"61\"></p>",
                    question_hi: "<p>3. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799589619.png\" alt=\"rId6\" width=\"295\" height=\"61\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799589857.png\" alt=\"rId7\" width=\"91\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590029.png\" alt=\"rId8\" width=\"90\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590174.png\" alt=\"rId9\" width=\"91\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590373.png\" alt=\"rId10\" width=\"91\" height=\"92\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799589857.png\" alt=\"rId7\" width=\"91\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590029.png\" alt=\"rId8\" width=\"91\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590174.png\" alt=\"rId9\" width=\"91\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590373.png\" alt=\"rId10\" width=\"90\" height=\"91\"></p>"
                    ],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590373.png\" alt=\"rId10\" width=\"90\" height=\"91\"></p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590373.png\" alt=\"rId10\" width=\"90\" height=\"91\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different.<br>(Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>4. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी प्रकार समान हैं और एक असंगत है। उस असंगत अक्षर-समूह का चयन कीजिए। (ध्यान दें: असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>SMIG</p>",
                        "<p>MGCA</p>",
                        "<p>CWSR</p>",
                        "<p>KEAY</p>"
                    ],
                    options_hi: [
                        "<p>SMIG</p>",
                        "<p>MGCA</p>",
                        "<p>CWSR</p>",
                        "<p>KEAY</p>"
                    ],
                    solution_en: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590502.png\" alt=\"rId11\" width=\"92\" height=\"78\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590867.png\" alt=\"rId12\" width=\"106\" height=\"84\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590985.png\" alt=\"rId13\" width=\"95\" height=\"80\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799591169.png\" alt=\"rId14\" width=\"90\" height=\"70\"></p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590502.png\" alt=\"rId11\" width=\"92\" height=\"78\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590867.png\" alt=\"rId12\" width=\"106\" height=\"84\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799590985.png\" alt=\"rId13\" width=\"95\" height=\"80\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799591169.png\" alt=\"rId14\" width=\"90\" height=\"70\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799591295.png\" alt=\"rId15\" width=\"121\" height=\"120\"></p>",
                    question_hi: "<p>5. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799591295.png\" alt=\"rId15\" width=\"143\" height=\"142\"></p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>10</p>",
                        "<p>13</p>",
                        "<p>12</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>10</p>",
                        "<p>13</p>",
                        "<p>12</p>"
                    ],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799591429.png\" alt=\"rId16\" width=\"148\" height=\"142\"><br>Total triangles = 7 + ACG + ACB + CFG + EDF + HDF = 12&nbsp;</p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799591429.png\" alt=\"rId16\" width=\"159\" height=\"153\"><br>कुल त्रिभुज = 7 + ACG + ACB + CFG + EDF + HDF = 12</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What should come in place of ? in the given series based on the English alphabetical order?<br>PHO SRP VBQ ? BVS</p>",
                    question_hi: "<p>6. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में &lsquo;?\' के स्थान पर क्या आना चाहिए?<br>PHO SRP VBQ ? BVS</p>",
                    options_en: [
                        "<p>CFE</p>",
                        "<p>VXY</p>",
                        "<p>YLR</p>",
                        "<p>UMO</p>"
                    ],
                    options_hi: [
                        "<p>CFE</p>",
                        "<p>VXY</p>",
                        "<p>YLR</p>",
                        "<p>UMO</p>"
                    ],
                    solution_en: "<p>6.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799591598.png\" alt=\"rId17\" width=\"297\" height=\"79\"></p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799591598.png\" alt=\"rId17\" width=\"297\" height=\"79\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically<br>follow(s) from the statements.<br><strong>Statements:</strong><br>All wafers are candies. <br>All candies are biscuits.<br>Some wafers are juices.<br><strong>Conclusions:</strong><br>(I) Some candies are juices.<br>(II) Some biscuits are juices.</p>",
                    question_hi: "<p>7. दिए गए कथनों और निष्कर्षों को ध्यान से पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन -सा /से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है /करते हैं |<br><strong>कथनः</strong><br>सभी वेफर्स , कैंडी हैं।<br>सभी कैंडी, बिस्किट हैं।<br>कुछ वेफर्स , जूस हैं।<br><strong>निष्कर्षः</strong><br>(I) कुछ कैंडी, जूस हैं।<br>(II) कुछ बिस्किट, जूस हैं।</p>",
                    options_en: [
                        "<p>Only conclusion II follows</p>",
                        "<p>Only conclusion I follows</p>",
                        "<p>Both conclusions I and II follow</p>",
                        "<p>Neither conclusion I nor II follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                        "<p>केवल निष्कर्ष । अनुसरण करता है</p>",
                        "<p>निष्कर्ष। और ।। दोनों अनुसरण करते हैं</p>",
                        "<p>न तो निष्कर्ष। और न ही निष्कर्ष ।I अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799591803.png\" alt=\"rId18\" width=\"176\" height=\"116\"><br>Hence, both conclusions I and II follow.</p>",
                    solution_hi: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799592144.png\" alt=\"rId19\" width=\"189\" height=\"124\"><br>अतः, निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(5, 7, 74)<br>(6, 4, 52)<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /subtracting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>8. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएं संबंधित हैं।<br>(5, 7, 74)<br>(6, 4, 52)<br>(ध्यान दें : संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>(6, 2, 52)</p>",
                        "<p>(11, 5, 146)</p>",
                        "<p>(9, 5, 206)</p>",
                        "<p>(9, 5, 405)</p>"
                    ],
                    options_hi: [
                        "<p>(6, 2, 52)</p>",
                        "<p>(11, 5, 146)</p>",
                        "<p>(9, 5, 206)</p>",
                        "<p>(9, 5, 405)</p>"
                    ],
                    solution_en: "<p>8.(b) <strong>Logic :-</strong> (1st number)<sup>2</sup> + (2nd number)<sup>2</sup> = (3rd number)<br>(5, 7, 74) :- (5)<sup>2</sup> + (7)<sup>2</sup> &rArr; 25 + 49 = 74<br>(6, 4, 52) :- (6)<sup>2</sup> + (4)<sup>2</sup> &rArr; 36 + 16 = 52<br>Similarly,<br>(11, 5, 146) :- (11)<sup>2</sup> + (5)<sup>2</sup> &rArr; 121 + 25 = 146</p>",
                    solution_hi: "<p>8.(b) <strong>तर्क :-</strong> (पहली संख्या)<sup>2</sup> + (दूसरी संख्या)<sup>2</sup> = (तीसरी संख्या)<br>(5, 7, 74) :- (5)<sup>2</sup> + (7)<sup>2</sup> &rArr; 25 + 49 = 74<br>(6, 4, 52) :- (6)<sup>2</sup> + (4)<sup>2</sup> &rArr; 36 + 16 = 52<br>इसी प्रकार,<br>(11, 5, 146) :- (11)<sup>2</sup> + (5)<sup>2 </sup>&rArr; 121 + 25 = 146</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. LMN starts from Point A and drives 18 km towards East. He then takes a right turn, drives 13 km, turns right and drives 29 km. He then takes a right turn and drives 17 km. He takes a final right turn, drives 11 km and stops at Point P. How far (the shortest distance) and towards which direction should he drive in order to reach Point A again? (All turns are 90&deg; turns only, unless specified.)</p>",
                    question_hi: "<p>9. LMN बिंदु A से ड्राइव करना शुरू करता है और पूर्व की ओर 18 km तक ड्राइव करता है। फिर वह दायीं ओर मुड़ता है, 13 km तक ड्राइव करता है, दायीं ओर मुड़ता है और 29 km तक ड्राइव करता है। फिर वह दाएं मुड़ता है, 17 km तक ड्राइव करता है। वह अंत में दायीं ओर मुड़ता है, 11 km तक ड्राइव करता है और बिंदु P पर रुक जाता है। बिंदु A पर दोबारा पहुंचने के लिए उसे कितनी दूर (न्यूनतम दूरी) और किस दिशा में ड्राइव करना चाहिए? (जब तक निर्दिष्ट न किया जाए, सभी मोड़ केवल 90&deg; मोड़ हैं।)</p>",
                    options_en: [
                        "<p>1 km towards north</p>",
                        "<p>3 km towards south</p>",
                        "<p>9 km towards north</p>",
                        "<p>4 km towards south</p>"
                    ],
                    options_hi: [
                        "<p>1 km उत्तर की ओर</p>",
                        "<p>3 km दक्षिण की ओर</p>",
                        "<p>9 km उत्तर की ओर</p>",
                        "<p>4 km दक्षिण की ओर</p>"
                    ],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799592358.png\" alt=\"rId20\" width=\"185\" height=\"142\"><br>Hence, LMN should move 4 km towards South.</p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799592358.png\" alt=\"rId20\" width=\"185\" height=\"142\"><br>इसलिए, LMN को 4 km दक्षिण की ओर चलना चाहिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language,<br>A + B means &lsquo;A is the daughter of B&rsquo;,<br>A &ndash; B means &lsquo;A is the brother of B&rsquo;,<br>A &times; B means &lsquo;A is the wife of B&rsquo;,<br>and A % B means &lsquo;A is the father of B&rsquo;.<br>How is S related to H if &lsquo;S + D &times; F &ndash; G % H&rsquo;?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में,<br>A + B का अर्थ है कि, \'A, B की पुत्री है\',<br>A - B का अर्थ है कि, \'A, B का भाई है\',<br>A &times; B का अर्थ है कि, \'A, B की पत्नी है\',<br>और A % B का अर्थ है कि, \'A, B का पिता है\'।<br>यदि \'S + D &times; F - G % H\' है, तो S का H से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Mother\'s brother\'s daughter</p>",
                        "<p>Father\'s brother\'s wife</p>",
                        "<p>Mother\'s brother\'s wife</p>",
                        "<p>Father\'s brother&rsquo;s daughter</p>"
                    ],
                    options_hi: [
                        "<p>माता के भाई की पुत्री</p>",
                        "<p>पिता के भाई की पत्नी</p>",
                        "<p>माता के भाई की पत्नी</p>",
                        "<p>पिता के भाई की पुत्री</p>"
                    ],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799592494.png\" alt=\"rId21\" width=\"218\" height=\"95\"><br>Hence, &lsquo;S&rsquo; is the father&rsquo;s brother&rsquo;s daughter of &lsquo;H&rsquo;.</p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799592494.png\" alt=\"rId21\" width=\"218\" height=\"95\"><br>अतः, \'S\', \'H\' के पिता के भाई की पुत्रीहै।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Three of the following number-pairs are alike in some manner and hence form a group. Which number-pair does not belong to that group?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into&nbsp;1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>11. निम्नलिखित संख्या-युग्मों में से तीन किसी तरह से समान हैं और इसलिए एक समूह बनाते हैं। कौन-सा संख्या-युग्म उस समूह से संबंधित नहीं है?<br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 - 13 पर संक्रियाएं जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>17 &ndash; 306</p>",
                        "<p>21 &ndash; 462</p>",
                        "<p>14 &ndash; 196</p>",
                        "<p>12 &ndash; 156</p>"
                    ],
                    options_hi: [
                        "<p>17 &ndash; 306</p>",
                        "<p>21 &ndash; 462</p>",
                        "<p>14 &ndash; 196</p>",
                        "<p>12 &ndash; 156</p>"
                    ],
                    solution_en: "<p>11.(c)<br><strong>Logic:- </strong>1<sup>st</sup>no &times; (1<sup>st</sup>no. + 1) = 2ndno.<br>(17, 306) :- 17 &times; (17 + 1) &rArr; 17 &times; 18 = 306<br>(21, 462) :- 21 &times; (21 + 1) &rArr; 21 &times; 22 = 462<br>(12, 156) :- 12 &times; (12 + 1) &rArr; 12 &times; 13 = 156<br>But<br>(14, 196) :- 14 &times; (14 + 1) &rArr; 14 &times; 15 = 210 &ne; 196</p>",
                    solution_hi: "<p>11.(c)<br><strong>तर्क :-</strong> पहली संख्या &times; ( पहली संख्या + 1) = दूसरी संख्या <br>(17, 306) :- 17 &times; (17 + 1) &rArr; 17 &times; 18 = 306<br>(21, 462) :- 21 &times; (21 + 1) &rArr; 21 &times; 22 = 462<br>(12, 156) :- 12 &times; (12 + 1) &rArr; 12 &times; 13 = 156<br>लेकिन, <br>(14, 196) :- 14 &times; (14 + 1) &rArr; 14 &times; 15 = 210 &ne; 196</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Study the given diagram carefully and answer the question that follows. The numbers in different sections indicate the number of students who like to study different subjects. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799592610.png\" alt=\"rId22\" width=\"184\" height=\"170\"> <br>What is the difference between the number of students who like to study all the three subjects and that of students who like to study biology only?</p>",
                    question_hi: "<p>12. दिए गए आरेख का ध्यानपूर्वक अध्ययन करें और निम्नलिखित प्रश्&zwj;न का उत्तर दें। विभिन्न खंडों में दी गई संख्याएं अलग-अलग विषयों का अध्ययन करना पसंद करने वाले छात्रों की संख्या को दर्शाती हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799592709.png\" alt=\"rId23\" width=\"180\" height=\"143\"> <br>सभी तीनों विषयों का अध्ययन करना पसंद करने वाले छात्रों की संख्या और केवल जीव विज्ञान का अध्ययन करना पसंद करने वाले छात्रों की संख्या के बीच का अंतर क्या है?</p>",
                    options_en: [
                        " 16  ",
                        " 3",
                        " 19",
                        " 17"
                    ],
                    options_hi: [
                        " 16  ",
                        " 3",
                        " 19",
                        " 17"
                    ],
                    solution_en: "12.(a)<br />No. of students who like to study all three subjects = 4<br />No. of students who like to study biology only = 20 <br />Required difference = 20 - 4 = 16",
                    solution_hi: "12.(a)<br />उन छात्रों की संख्या जो तीनों विषयों का अध्ययन करना पसंद करते हैं = 4<br />केवल जीव विज्ञान पढ़ना पसंद करने वाले छात्रों की संख्या  = 20 <br />आवश्यक अंतर = 20 - 4 = 16",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. What would be the Roman numeral on the opposite side of \'V\' if the given sheet is folded to form a cube?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799592908.png\" alt=\"rId24\" width=\"124\" height=\"111\"></p>",
                    question_hi: "<p>13. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो के विपरीत फलक पर कौन-सी रोमन संख्या होगी?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799592908.png\" alt=\"rId24\" width=\"124\" height=\"111\"></p>",
                    options_en: [
                        "<p>VIII</p>",
                        "<p>VI</p>",
                        "<p>X</p>",
                        "<p>VII</p>"
                    ],
                    options_hi: [
                        "<p>VIII</p>",
                        "<p>VI</p>",
                        "<p>X</p>",
                        "<p>VII</p>"
                    ],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593166.png\" alt=\"rId25\" width=\"87\" height=\"127\"><br><strong>The opposite faces are :-</strong> V &harr; VIII , IX &harr; VII , VI &harr; X</p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593166.png\" alt=\"rId25\" width=\"87\" height=\"127\"><br><strong>विपरीत फलक हैं:- </strong>V &harr; VIII , IX &harr; VII , VI &harr; X</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. If \'A\' stands for \'&divide;\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for \'-\', what will come in place of the question mark \'?\" in the following equation?<br>(64 A 4) C 28 D 20 C (46 A 23) B 7 C 19 = ?</p>",
                    question_hi: "<p>14. यदि \'A\' का अर्थ \'&divide;\' है, \'B\' का अर्थ \'&times;\' है, \'C\' का अर्थ \'+\' है और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न-चिह्न \'?\' के स्थान पर क्या आएगा?&nbsp;<br>(64 A 4) C 28 D 20 C (46 A 23) B 7 C 19 = ?</p>",
                    options_en: [
                        "<p>41</p>",
                        "<p>57</p>",
                        "<p>69</p>",
                        "<p>76</p>"
                    ],
                    options_hi: [
                        "<p>41</p>",
                        "<p>57</p>",
                        "<p>69</p>",
                        "<p>76</p>"
                    ],
                    solution_en: "<p>14.(b)<br>(64 A 4) C 28 D 20 C (46 A 23) B 7 C 19<br>After applying signs as per instructions, we get <br>(64 &divide; 4) + 28 - 20 + (46 &divide; 23) &times; 7 + 19<br>= 16 + 8 + 2 &times; 7 + 19<br>= 24 + 14 + 19<br>= 57</p>",
                    solution_hi: "<p>14.(b)<br>(64 A 4) C 28 D 20 C (46 A 23) B 7 C 19<br>After applying signs as per instructions, we get <br>(64 &divide; 4) + 28 - 20 + (46 &divide; 23) &times; 7 + 19<br>= 16 + 8 + 2 &times; 7 + 19<br>= 24 + 14 + 19<br>= 57</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Each vowel in the word &lsquo;STAMINA&rsquo; is changed to the preceding letter in the English alphabetical order and each consonant is changed to the following letter in the English alphabetical order. How many letters are there in the English alphabetical order between the letter which is the third from the right and the fourth from the left in the group of letters thus formed?</p>",
                    question_hi: "<p>15. शब्द \'STAMINA\' में प्रत्येक स्वर को अंग्रेजी वर्णानुक्रम में उससे पहले वाले अक्षर में बदल दिया जाता है और प्रत्येक व्यंजन को अंग्रेजी वर्णानुक्रम में उससे बाद वाले अक्षर में बदल दिया जाता है। इस प्रकार बने अक्षरों के समूह में दाएं से तीसरे और बाएं से चौथे अक्षर के बीच अंग्रेजी वर्णानुक्रम में कितने अक्षर हैं?</p>",
                    options_en: [
                        "<p>Five</p>",
                        "<p>Seven</p>",
                        "<p>Four</p>",
                        "<p>Six</p>"
                    ],
                    options_hi: [
                        "<p>पाँच</p>",
                        "<p>सात</p>",
                        "<p>चार</p>",
                        "<p>छह</p>"
                    ],
                    solution_en: "<p>15.(a) <strong>Given</strong> : STAMINA<br>As per instructions given in question , we get word - TUZNHOZ<br>The third letter from the right is \'H\' and the fourth letter from the left end is \'N\'.<br>There are 5 letters between letters H and N.<br><br></p>",
                    solution_hi: "<p>15.(a)<strong> दिया गया :</strong> STAMINA<br>प्रश्न में दिए गए निर्देशों के अनुसार, हमें शब्द मिलता है - TUZNHOZ<br>दाएं से तीसरा अक्षर \'H\' है और बाएं छोर से चौथा अक्षर \'N\' है।<br>अक्षरों H और N के बीच 5 अक्षर है।<br><br></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. The sum of the ages of 6 children born at the interval of two years each is 90 years. What is the age (in years) of the eldest child?</p>",
                    question_hi: "<p>16. दो वर्षों के अंतराल पर पैदा हुए 6 बच्चों की आयु का योग 90 वर्ष है। सबसे बड़े बच्चे की आयु (वर्ष में) क्या है?</p>",
                    options_en: [
                        "<p>14</p>",
                        "<p>20</p>",
                        "<p>16</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>14</p>",
                        "<p>20</p>",
                        "<p>16</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>16.(b) Let the ages of six children are , (x - 4 ) , (x &nbsp;- 2 ) , x , (x + 2 ) , (x + 4 ) , ( x + 6 )<br>According to question ,&nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>&nbsp;x - 4 &nbsp;+ &nbsp;x &nbsp;- 2 &nbsp;+ x + x + 2 &nbsp;+ x + 4 + &nbsp;x + 6 = 90<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>6x + 6 = 90&nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>6x = 90 - 6 = 84 &nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>x = 14<br>Age of the eldest child = 14 + 6 = 20 years&nbsp;<br><br></p>",
                    solution_hi: "<p>16.(b) माना छह बच्चों की उम्र क्रमश: = &nbsp;(x - 4 ) , (x &nbsp;- 2 ) , x , (x + 2 ) , (x + 4 ) , ( x + 6 )<br>प्रश्न के अनुसार,&nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>x - 4 &nbsp;+ &nbsp;x &nbsp;- 2 &nbsp;+ x + x + 2 &nbsp;+ x + 4 + &nbsp;x + 6 = 90<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>6x + 6 = 90&nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>6x = 90 - 6 = 84&nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>x = 14<br>सबसे बड़े बच्चे की आयु = 14 + 6 = 20 वर्ष</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into&nbsp;1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>17. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है?<br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>540 &ndash; 188 &ndash; 128</p>",
                        "<p>72 &ndash; 284 &ndash; 266</p>",
                        "<p>81&ndash; 101&ndash; 92</p>",
                        "<p>90 &ndash; 22&ndash; 12</p>"
                    ],
                    options_hi: [
                        "<p>540 &ndash; 188 &ndash; 128</p>",
                        "<p>72 &ndash; 284 &ndash; 266</p>",
                        "<p>81&ndash; 101&ndash; 92</p>",
                        "<p>90 &ndash; 22&ndash; 12</p>"
                    ],
                    solution_en: "<p>17.(b) <strong>Logic :-</strong> (2nd number - 3rd number) &times; 9 = 1st number<br>(540 - 188 - 128) :- (188 - 128) &times; 9 &rArr; (60) &times; 9 = 540<br>(81- 101 - 92) :- (101 - 92) &times; 9 &rArr; (9) &times; 9 = 81<br>(90 - 22- 12) :- (22 - 12) &times; 9 &rArr; (10) &times; 9 = 90<br>But,<br>(72, 284, 266) :- (284 - 266) &times; 9 &rArr; (18) &times; 9 = 162(Not 72)</p>",
                    solution_hi: "<p>17.(b) <strong>तर्क :-</strong> (दूसरी संख्या - तीसरी संख्या) &times; 9 = पहली संख्या<br>(540 - 188 - 128) :- (188 - 128) &times; 9 &rArr; (60) &times; 9 = 540<br>(81- 101 - 92) :- (101 - 92) &times; 9 &rArr; (9) &times; 9 = 81<br>(90 - 22- 12) :- (22 - 12) &times; 9 &rArr; (10) &times; 9 = 90<br>लेकिन,<br>(72, 284, 266) :- (284 - 266) &times; 9 &rArr; (18) &times; 9 = 162(72 नहीं)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the option in which the given figure is embedded (rotation is not allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593341.png\" alt=\"rId26\" width=\"108\" height=\"94\"></p>",
                    question_hi: "<p>18. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593341.png\" alt=\"rId26\" width=\"115\" height=\"101\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593476.png\" alt=\"rId27\" width=\"112\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593615.png\" alt=\"rId28\" width=\"108\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593739.png\" alt=\"rId29\" width=\"106\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593885.png\" alt=\"rId30\" width=\"106\" height=\"99\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593476.png\" alt=\"rId27\" width=\"106\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593615.png\" alt=\"rId28\" width=\"106\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593739.png\" alt=\"rId29\" width=\"111\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799593885.png\" alt=\"rId30\" width=\"106\" height=\"99\"></p>"
                    ],
                    solution_en: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594108.png\" alt=\"rId31\" width=\"116\" height=\"101\"></p>",
                    solution_hi: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594108.png\" alt=\"rId31\" width=\"116\" height=\"101\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Which of the following numbers will replace the question mark (?) in the given series?<br>165 192 227 270 321 ?</p>",
                    question_hi: "<p>19. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?<br>165 192 227 270 321 ?</p>",
                    options_en: [
                        "<p>347</p>",
                        "<p>361</p>",
                        "<p>392</p>",
                        "<p>380</p>"
                    ],
                    options_hi: [
                        "<p>347</p>",
                        "<p>361</p>",
                        "<p>392</p>",
                        "<p>380</p>"
                    ],
                    solution_en: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594323.png\" alt=\"rId32\" width=\"248\" height=\"73\"></p>",
                    solution_hi: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594323.png\" alt=\"rId32\" width=\"248\" height=\"73\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. If 29 December 1956 was a Saturday, then what was the day of the week on 16 January&nbsp;1967?</p>",
                    question_hi: "<p>20. यदि 29 दिसंबर 1956 को शनिवार था, तो 16 जनवरी 1967 को सप्ताह का कौन-सा दिन था?</p>",
                    options_en: [
                        "<p>Sunday</p>",
                        "<p>Monday</p>",
                        "<p>Saturday</p>",
                        "<p>Friday</p>"
                    ],
                    options_hi: [
                        "<p>रविवार</p>",
                        "<p>सोमवार</p>",
                        "<p>शनिवार</p>",
                        "<p>शुक्रवार</p>"
                    ],
                    solution_en: "<p>20.(b) 29 December 1956 was Saturday. On going to 1966 the number of odd days is = +1 + 1 + 1 + 2 + 1 + 1 + 1 +2 + 1 + 1 = 12. We have reached till 29 December 1966, but we have to go to 16 January 1967, number of days between = 18. Total number of odd days = 30. On dividing 30 by 7 remainder = 2, Saturday + 2 = Monday.</p>",
                    solution_hi: "<p>20.(b) 29 दिसंबर 1956 को शनिवार था। 1966 में जाने पर विषम दिनों की संख्या है = +1 + 1 + 1 + 2 + 1 + 1 + 1 +2 + 1 + 1 = 12. हम 29 दिसंबर 1966 तक पहुंच गए हैं, लेकिन हमें 16 जनवरी तक जाना है 1967, बीच में दिनों की संख्या = 18, विषम दिनों की कुल संख्या = 30, 30 को 7 से विभाजित करने पर शेषफल = 2, शनिवार + 2 = सोमवार</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Which two numbers should be interchanged to make the given equation correct?<br>69 + 15 &minus; (14 &times; 4 ) &divide; 2 + (48 &divide; 3) = 58<br>(NOTE: Numbers must be interchanged and not the constituent digits e.g. if 2 and 3 are to be interchanged in the equation 43 &times; 3 + 4 &divide; 2, then interchanged equation is 43 &times; 2 + 4 &divide; 3)</p>",
                    question_hi: "<p>21. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए?<br>69 + 15 &minus; (14 &times; 4 ) &divide; 2 + (48 &divide; 3) = 58<br>(ध्यान दें: संख्याओं को आपस में बदला जाना चाहिए और घटक अंकों को नहीं बदला जाना चाहिए।&nbsp;उदा. यदि समीकरण 43 &times; 3 + 4 &divide; 2 में 2 और 3 को आपस में बदलना है, तो बदला हुआ समीकरण 43 &times; 2 + 4 &divide; 3 होगा)</p>",
                    options_en: [
                        "<p>48 and 15</p>",
                        "<p>4 and 2</p>",
                        "<p>3 and 4</p>",
                        "<p>69 and 48</p>"
                    ],
                    options_hi: [
                        "<p>48 और 15</p>",
                        "<p>4 और 2</p>",
                        "<p>3 और 4</p>",
                        "<p>69 और 48</p>"
                    ],
                    solution_en: "<p>21.(d)<strong> Given :-</strong> 69 + 15 - (14 &times; 4) &divide; 2 + (48 &divide; 3) = 58<br>After going through all the options, option d satisfies. After interchanging 69 and 48 we get<br>48 + 15 - (56) &divide; 2 + (69 &divide; 3)<br>63 - 28 + 23 = 58<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>21.(d) <strong>दिया गया :-</strong> 69 + 15 - (14 &times; 4) &divide; 2 + (48 &divide; 3) = 58<br>सभी विकल्पों की जांच करने पर विकल्प d संतुष्ट करता है। 69 और 48 को आपस में बदलने पर हमें प्राप्त होता है<br>48 + 15 - (56) &divide; 2 + (69 &divide; 3)<br>63 - 28 + 23 = 58<br>L.H.S. = R.H.S.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594483.png\" alt=\"rId33\" width=\"103\" height=\"129\"></p>",
                    question_hi: "<p>22. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखनेपर बनेगा। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594483.png\" alt=\"rId33\" width=\"101\" height=\"126\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594615.png\" alt=\"rId34\" width=\"91\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594787.png\" alt=\"rId35\" width=\"92\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594924.png\" alt=\"rId36\" width=\"92\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595048.png\" alt=\"rId37\" width=\"92\" height=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594615.png\" alt=\"rId34\" width=\"94\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594787.png\" alt=\"rId35\" width=\"93\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594924.png\" alt=\"rId36\" width=\"93\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595048.png\" alt=\"rId37\" width=\"93\" height=\"91\"></p>"
                    ],
                    solution_en: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594924.png\" alt=\"rId36\" width=\"109\" height=\"108\"></p>",
                    solution_hi: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799594924.png\" alt=\"rId36\" width=\"109\" height=\"108\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers.<br>40 &minus; 9</p>",
                    question_hi: "<p>23. उस विकल्प का चयन कीजिए, जिसमें संख्याएं वही संबंध साझा करती हैं, जो संख्याओं के दिए गए युग्म द्वारा साझा किया जाता है।<br>40 &ndash; 9</p>",
                    options_en: [
                        "<p>100 &minus; 6</p>",
                        "<p>120 &minus; 4</p>",
                        "<p>90 &minus; 4</p>",
                        "<p>60 &minus; 5</p>"
                    ],
                    options_hi: [
                        "<p>100 &minus; 6</p>",
                        "<p>120 &minus; 4</p>",
                        "<p>90 &minus; 4</p>",
                        "<p>60 &minus; 5</p>"
                    ],
                    solution_en: "<p>23.(c) <strong>Logic :-</strong> (1st number &times; 2nd number) = 360<br>(40 - 9) :- (40 &times; 9) = 360<br>Similarly,<br>(90 - 4) :- (90 &times; 4) = 360</p>",
                    solution_hi: "<p>23.(c) <strong>तर्क :-</strong> (पहली संख्या &times; दूसरी संख्या) = 360<br>(40 - 9) :- (40 &times; 9) = 360<br>इसी प्रकार,<br>(90 - 4) :- (90 &times; 4) = 360</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last. How would the paper look when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595166.png\" alt=\"rId38\" width=\"262\" height=\"89\"></p>",
                    question_hi: "<p>24. कागज की एक वर्गाकार शीट को दर्शाई गई दिशाओ में बिंदीदार रेखा पर अनुक्रमशः मोड़ा जाता है और आखिर में उसमें छेद किया जाता है। खोलने पर यह कागज कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595166.png\" alt=\"rId38\" width=\"262\" height=\"89\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595303.png\" alt=\"rId39\" width=\"100\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595456.png\" alt=\"rId40\" width=\"100\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595596.png\" alt=\"rId41\" width=\"104\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595717.png\" alt=\"rId42\" width=\"100\" height=\"96\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595303.png\" alt=\"rId39\" width=\"100\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595456.png\" alt=\"rId40\" width=\"100\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595596.png\" alt=\"rId41\" width=\"101\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595717.png\" alt=\"rId42\" width=\"100\" height=\"96\"></p>"
                    ],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595717.png\" alt=\"rId42\" width=\"115\" height=\"111\"></p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595717.png\" alt=\"rId42\" width=\"115\" height=\"111\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. FIGH is related to KNLM in a certain way based on the English alphabetical order. In the same way, RUST is related to WZXY. To which of the following options is HKIJ related, following the same logic?</p>",
                    question_hi: "<p>25. अंग्रेजी वर्णमाला क्रम के आधार पर FIGH, KNLM से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, RUST, WZXY से संबंधित है। समान तर्क का अनुसरण करते हुए, HKIJ निम्नलिखित में से किस विकल्प से संबंधित है?</p>",
                    options_en: [
                        "<p>MQNO</p>",
                        "<p>MPNO</p>",
                        "<p>LQNO</p>",
                        "<p>NPLO</p>"
                    ],
                    options_hi: [
                        "<p>MQNO</p>",
                        "<p>MPNO</p>",
                        "<p>LQNO</p>",
                        "<p>NPLO</p>"
                    ],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595869.png\" alt=\"rId43\" width=\"147\" height=\"79\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799596041.png\" alt=\"rId44\" width=\"152\" height=\"81\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799596255.png\" alt=\"rId45\" width=\"156\" height=\"81\"></p>",
                    solution_hi: "<p>25.(b) , <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799595869.png\" alt=\"rId43\" width=\"147\" height=\"79\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799596041.png\" alt=\"rId44\" width=\"152\" height=\"81\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799596255.png\" alt=\"rId45\" width=\"156\" height=\"81\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. In which of the following Indian states is Ghum Monastery located?",
                    question_hi: "26. घूम मठ भारत के किस राज्य में स्थित है?",
                    options_en: [
                        " Karnataka",
                        " Himachal Pradesh",
                        " West Bengal",
                        " Arunachal Pradesh"
                    ],
                    options_hi: [
                        " कर्नाटक",
                        " हिमाचल प्रदेश ",
                        " पश्चिम बंगाल ",
                        " अरुणाचल प्रदेश"
                    ],
                    solution_en: "<p>26.(c) <strong>West Bengal.</strong> Ghum or Ghoom Monastery is situated in Darjeeling (West Bengal). Old Ghoom Monastery is the popular name of Yiga Choeling. The monastery belongs to the Gelukpa or the Yellow Hat sect. The external structure of the building was established in 1850 by the Mongolian astrologer and monk Sokpo Sherab Gyatso, who was head of the monastery until 1905.</p>",
                    solution_hi: "<p>26.(c) <strong>पश्चिम बंगाल। </strong>घूम मठ दार्जिलिंग (पश्चिम बंगाल) में स्थित है। ओल्ड घूम मठ यिगा चोएलिंग का लोकप्रिय नाम है। यह मठ गेलुक्पा या येलो हैट संप्रदाय से संबंधित है। मठ की बाह्य संरचना 1850 में मंगोलियाई ज्योतिषी और भिक्षु सोकपो शेरब ग्यात्सो द्वारा स्थापित की गई थी, जो 1905 तक मठ के प्रमुख थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. \'Both Feet on the Ground\' is an autobiography of which of the following players?</p>",
                    question_hi: "<p>27. \'बोथ फीट ऑन द ग्राउंड\' निम्नलिखित में से किस खिलाड़ी की आत्मकथा है?</p>",
                    options_en: [
                        "<p>Cristiano Ronaldo</p>",
                        "<p>Lionel Messi</p>",
                        "<p>Bobby Moore</p>",
                        "<p>David Beckham</p>"
                    ],
                    options_hi: [
                        "<p>क्रिस्टियानो रोनाल्डो</p>",
                        "<p>लियोनेल मेसी</p>",
                        "<p>बॉबी मूर</p>",
                        "<p>डेविड बेकहम</p>"
                    ],
                    solution_en: "<p>27.(d) <strong>David Beckham.</strong> Other Autobiographies of famous players: Serve to win (Novak Djokovic), Unbreakable (Mary Kom), Golden Girl (P.T Usha), Ace against Odds (Sania Mirza), Standing My Ground (Matthew Hayden), and Six Machine (Chris Gayle).</p>",
                    solution_hi: "<p>27.(d) <strong>डेविड बेकहम। </strong>अन्य प्रसिद्ध खिलाड़ियों की आत्मकथाएँ: सर्व टू विन (नोवाक जोकोविच), अनब्रेकेबल (मैरी कॉम), गोल्डन गर्ल (पी.टी उषा), \'एस अगेंस्ट ऑड्स\' (सानिया मिर्जा), स्टैंडिंग माई ग्राउंड (मैथ्यू हेडन), और सिक्स मशीन (क्रिस गेल)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who is the founder of modern micro finance ?</p>",
                    question_hi: "<p>28. आधुनिक सूक्ष्म वित्त (modern micro finance) के जनक कौन हैं?</p>",
                    options_en: [
                        "<p>Muhammad Yunus</p>",
                        "<p>Rangarajan</p>",
                        "<p>YV Reddy</p>",
                        "<p>VKV Rao</p>"
                    ],
                    options_hi: [
                        "<p>मुहम्मद यूनुस</p>",
                        "<p>रंगराजन</p>",
                        "<p>वाई.वी. रेड्डी</p>",
                        "<p>वी.के.वी. राव</p>"
                    ],
                    solution_en: "<p>28.(a) <strong>Muhammad Yunus.</strong> Microfinance is a banking service that is provided to low-income individuals who have no other means of gaining financial services. Muhammad Yunus is a Bangladeshi economist, was awarded the Nobel Peace Prize in 2006 for founding the Grameen Bank (Founded - 1976, Dhaka, Bangladesh) and pioneering the concepts of microcredit and microfinance.</p>",
                    solution_hi: "<p>28.(a) <strong>मुहम्मद यूनुस।</strong> सूक्ष्म वित्त एक बैंकिंग सेवा है जो कम आय वाले व्यक्तियों को प्रदान की जाती है जिनके पास वित्तीय सेवाएँ प्राप्त करने का कोई अन्य साधन नहीं है। मुहम्मद यूनुस एक बांग्लादेशी अर्थशास्त्री हैं, जिन्हें ग्रामीण बैंक (स्थापना - 1976, ढाका, बांग्लादेश) की स्थापना और माइक्रो क्रेडिट तथा माइक्रो फाइनेंस की अवधारणाओं को आगे बढ़ाने के लिए 2006 में नोबेल शांति पुरस्कार से सम्मानित किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Seema switched on an electric circuit and went away to drink some water. She came back after 2 minutes and noticed that the circuit had 2.5 A of current flowing through it. The net charge that had flown through the circuit is:</p>",
                    question_hi: "<p>29. सीमा, विद्युत परिपथ को चालू करती है और जल पीने के लिए चली जाती है। वह 2 मिनट बाद वापस आती है और देखती है कि परिपथ में 2.5A की धारा प्रवाहित हो रही है। परिपथ से प्रवाहित शुद्ध आवेश ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>1.25 C</p>",
                        "<p>5 C</p>",
                        "<p>300 C</p>",
                        "<p>0.0833 C</p>"
                    ],
                    options_hi: [
                        "<p>1.25 C</p>",
                        "<p>5 C</p>",
                        "<p>300 C</p>",
                        "<p>0.0833 C</p>"
                    ],
                    solution_en: "<p>29.(c) <strong>300 C.</strong> Given, Current (I) = 2.5 A, Time (t) = 2 min = 2 &times; 60 = 120 sec.<br>Quantity of electricity charge (Q) = Current (I) &times; Time (t) = 2.5 &times; 120 = 300 C.</p>",
                    solution_hi: "<p>29.(c) <strong>300 C.</strong> दिया गया है, धारा (I) = 2.5 A, समय (t) = 2 मिनट = 2 &times; 60 = 120 सेकंड।<br>विद्युत आवेश की मात्रा (Q) = धारा (I) &times; समय (t) = 2.5 &times; 120 = 300 C.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Bishnupur group of temples is located in which state of India?</p>",
                    question_hi: "<p>30. बिष्णुपुर मंदिर समूह भारत के किस राज्य में स्थित है?</p>",
                    options_en: [
                        "<p>West Bengal</p>",
                        "<p>Madhya Pradesh</p>",
                        "<p>Assam</p>",
                        "<p>Bihar</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिम बंगाल</p>",
                        "<p>मध्य प्रदेश</p>",
                        "<p>असम</p>",
                        "<p>बिहार</p>"
                    ],
                    solution_en: "<p>30.(a) <strong>West Bengal.</strong> Bishnupur temples were built by Malla King Krishna Singh in 1726. These \'Eka-Ratna\' or \'single towered\' temples are made of rust-coloured laterite. Other Famous temples: Madhya Pradesh - Mahakaleshwar Jyotirlinga, Kandariya Mahadev, Adinath. Assam - Kamakhya Temple. Bihar - Vishnupad Temple, Mahabodhi Temple.</p>",
                    solution_hi: "<p>30.(a) <strong>पश्चिम बंगाल।</strong> बिष्णुपुर मंदिरों का निर्माण 1726 में मल्ल राजा कृष्ण सिंह द्वारा किया गया था। ये \'एक-रत्न\' या \'एकल मीनारदार\' मंदिर है जो जंग जैसे रंग के लेटराइट पत्थरों से बने हैं। अन्य प्रसिद्ध मंदिर: मध्य प्रदेश - महाकालेश्वर ज्योतिर्लिंग, कंदरिया महादेव, आदिनाथ। असम - कामाख्या मंदिर। बिहार - विष्णुपद मंदिर, महाबोधि मंदिर।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who authored the book \"MODIALOGUE: Conversations for a Viksit Bharat\", released in November 2024?</p>",
                    question_hi: "<p>31. नवंबर 2024 में प्रकाशित पुस्तक \"मोडियालॉग: कन्वर्सेशन फॉर ए विकसित भारत\" के लेखक कौन हैं?</p>",
                    options_en: [
                        "<p>Dr. Ashwin Fernandes</p>",
                        "<p>Dr. S. Jaishankar</p>",
                        "<p>Dr. Davendra Kumar Dhodawat</p>",
                        "<p>Dr. Vikram Singh</p>"
                    ],
                    options_hi: [
                        "<p>डॉ. अश्विन फर्नांडिस</p>",
                        "<p>डॉ. एस. जयशंकर</p>",
                        "<p>डॉ. दवेंद्र कुमार धोडावत</p>",
                        "<p>डॉ. विक्रम सिंह</p>"
                    ],
                    solution_en: "<p>31. (a) <strong>Dr. Ashwin Fernandes.</strong> The book examines Prime Minister Modi\'s communication strategies through his Mann Ki Baat radio program.</p>",
                    solution_hi: "<p>31.(a) <strong>डॉ. अश्विन फर्नांडिस।</strong> यह पुस्तक प्रधानमंत्री मोदी की मन की बात रेडियो कार्यक्रम के माध्यम से संचार रणनीतियों की जांच करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32._________ of a substance is the temperature and pressure at which three phases (gas, liquid, and solid) of that substance may co-exist in thermodynamic equilibrium.</p>",
                    question_hi: "<p>32. किसी पदार्थ का__________ वह तापमान और दाब होता है जिस पर उस पदार्थ के तीन चरण (गैस, द्रव और ठोस) ऊष्मागतिकीय साम्य में एक साथ रह सकते हैं।</p>",
                    options_en: [
                        "<p>Allotropic</p>",
                        "<p>Triple-point</p>",
                        "<p>Tri-state</p>",
                        "<p>Amphibious</p>"
                    ],
                    options_hi: [
                        "<p>अपररूप (Allotropic)</p>",
                        "<p>त्रिक बिंदु (Triple-point</p>",
                        "<p>त्रि-अवस्थी (Tri-state)</p>",
                        "<p>द्विधागति (Amphibious)</p>"
                    ],
                    solution_en: "<p>32.(b) <strong>Triple-point </strong>- It is the temperature and pressure at which the sublimation curve, fusion curve, and vaporization curve meet. Allotropy is the existence of an element in more than one form, having the same chemical properties but different physical properties.</p>",
                    solution_hi: "<p>32.(b) <strong>त्रिक-बिंदु </strong>- यह वह ताप और दाब है जिस पर उर्ध्वपातन वक्र, संलयन वक्र और वाष्पीकरण वक्र मिलते हैं। अपररूपता (एलोट्रोपी) किसी तत्व का एक से अधिक रूपों में विद्यमान होना है, जिनके रासायनिक गुण समान लेकिन भौतिक गुण भिन्न होते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. _______ was the founder of the Widow Remarriage Association (1861).</p>",
                    question_hi: "<p>33. _______विधवा पुनर्विवाह संघ (1861) के संस्थापक थे।</p>",
                    options_en: [
                        "<p>Keshub Chandra Sen</p>",
                        "<p>Mahadev Govind Ranade</p>",
                        "<p>Dr. Atma Ram Pandurang</p>",
                        "<p>Debendra Nath Tagore</p>"
                    ],
                    options_hi: [
                        "<p>केशव चंद्र सेन (Keshub Chandra Sen)</p>",
                        "<p>महादेव गोविंद रानाडे (Mahadev Govind Ranade)</p>",
                        "<p>डॉ आत्मा राम पांडुरंग (Dr. Atma Ram Pandurang)</p>",
                        "<p>देवेन्द्र नाथ टैगोर (Debendra Nath Tagore</p>"
                    ],
                    solution_en: "<p>33.(b) <strong>Mahadev Govind Ranade : </strong>He also founded the Poona Sarvajanik Sabha and Social Conference movement. He was one of the founding members of the Prarthana Samaj. The first Widow Remarriage Association was founded by Vishnu Shastri Pandit in the 1850s. The Hindu widow remarriage act was passed in the year 1856, during the tenure of Lord Canning due to the efforts of social reformer Ishwar Chandra Vidyasagar.</p>",
                    solution_hi: "<p>33.(b) <strong>महादेव गोविंद रानाडे :</strong> उन्होंने पूना सार्वजनिक सभा और सामाजिक सम्मेलन आंदोलन की भी स्थापना की। वे प्रार्थना समाज के संस्थापक सदस्यों में से एक थे। पहली विधवा पुनर्विवाह संघ की स्थापना 1850 के दशक में विष्णु शास्त्री पंडित ने की थी। समाज सुधारक ईश्वर चंद्र विद्यासागर के प्रयासों से लॉर्ड कैनिंग के कार्यकाल के दौरान वर्ष 1856 में हिंदू विधवा पुनर्विवाह अधिनियम पारित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. A disease like COVID-19 is a kind of:</p>",
                    question_hi: "<p>34. कोविड-19 जैसा रोग एक प्रकार का ______ है।</p>",
                    options_en: [
                        "<p>Foodborne disease</p>",
                        "<p>Protein Deficiency disease</p>",
                        "<p>Zoonotic disease</p>",
                        "<p>Waterborne disease</p>"
                    ],
                    options_hi: [
                        "<p>खाद्य जनित रोग</p>",
                        "<p>प्रोटीन की कमी से होने वाला रोग</p>",
                        "<p>पशुजन्य रोग</p>",
                        "<p>जलजनित रोग</p>"
                    ],
                    solution_en: "<p>34.(c) <strong>Zoonotic disease</strong>. COVID-19 is a zoonotic disease, meaning it can be transmitted between animals and humans. The SARS-CoV-2 virus, which causes COVID-19, is believed to have originated from an animal source before being transmitted to humans.</p>",
                    solution_hi: "<p>34.(c) <strong>पशुजन्य रोग।</strong> कोविड-19 एक पशुजन्य रोग है, जिसका अर्थ है कि यह जंतुओं और मनुष्यों के बीच फैल सकता है। SARS-CoV-2 वायरस, जो COVID-19 का कारण बनता है, माना जाता है कि यह मनुष्यों में फैलने से पहले एक पशु स्रोत से उत्पन्न हुआ था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Who among the following personalities is an awardee of Sahitya Natak Akademi for his contribution to the Chhau dance?</p>",
                    question_hi: "<p>35. निम्नलिखित में से किसे छऊ नृत्य में उनके योगदान के लिए साहित्य नाटक अकादमी पुरस्कार से सम्मानित किया गया है?</p>",
                    options_en: [
                        "<p>Chandra Shekhar Bhanj</p>",
                        "<p>Radha Reddy</p>",
                        "<p>Reba Vidyarthi</p>",
                        "<p>Chandralekha</p>"
                    ],
                    options_hi: [
                        "<p>चंद्र शेखर भंज</p>",
                        "<p>राधा रेड्डी</p>",
                        "<p>रेबा विद्यार्थी</p>",
                        "<p>चंद्रलेखा</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>Chandra Shekhar Bhanj</strong> received the Orissa Sangeet Natak Akademi Award in 1972. Chhau dance - It is found in three styles named after the location where they are performed, i.e. the Purulia Chau of Bengal, the Seraikella Chau of Jharkhand, and the Mayurbhanj Chau of Odisha. It is one of the Classical dances as per the Ministry of culture.</p>",
                    solution_hi: "<p>35.(a) <strong>चंद्र शेखर भंज</strong> को 1972 में उड़ीसा संगीत नाटक अकादमी पुरस्कार से सम्मानित किया गया था। छऊ नृत्य - यह तीन शैलियों में पाया जाता है, जिनका नाम उस स्थान के नाम पर रखा गया है जहां उनका प्रदर्शन किया जाता है, यानी बंगाल का पुरुलिया छऊ, झारखंड का सरायकेला छऊ , और ओडिशा के मयूरभंज। संस्कृति मंत्रालय के अनुसार यह शास्त्रीय नृत्यों में से एक है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following rivers passes through the maximum number of states in India?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन-सी नदी भारत में सर्वाधिक राज्यों से होकर गुजरती है?</p>",
                    options_en: [
                        "<p>Damodar</p>",
                        "<p>Indus</p>",
                        "<p>Ganga</p>",
                        "<p>Mahanadi </p>"
                    ],
                    options_hi: [
                        "<p>दामोदर</p>",
                        "<p>सिंधु</p>",
                        "<p>गंगा</p>",
                        "<p>महानदी</p>"
                    ],
                    solution_en: "<p>36.(c) <strong>Ganga</strong> rises in the Gangotri glacier near Gaumukh (4,023 m) in the Uttarkashi district of Uttarakhand. Here, it is known as the Bhagirathi. At Devprayag, the Bhagirathi and Alaknanda rivers join to form the Ganga. It has a length of 2,525 km, shared by Uttarakhand (110 km) and Uttar Pradesh (1,450 km), Bihar (445 km) and West Bengal (520 km). Tributaries: Ramganga, Gomati, Ghaghara, Gandak, Kosi and Mahananda, Yamuna (longest tributary).</p>",
                    solution_hi: "<p>36.(c) <strong>गंगा</strong> उत्तराखंड के उत्तरकाशी जिले में गौमुख (3,900 मीटर) के पास गंगोत्री ग्लेशियर से निकलती है। यहां इसे भागीरथी के नाम से जाना जाता है। देवप्रयाग में भागीरथी और अलकनंदा नदियाँ मिलकर गंगा का निर्माण करती हैं। इसकी लंबाई 2,525 किमी है जो उत्तराखंड (110 किमी) और उत्तर प्रदेश (1,450 किमी), बिहार (445 किमी) और पश्चिम बंगाल (520 किमी) द्वारा साझा की जाती है। सहायक नदियाँ: रामगंगा, गोमती, घाघरा, गंडक, कोसी और महानंदा, यमुना (सबसे लंबी सहायक नदी)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which of the following Sections of the Indian Penal Code deals with \'rape\'?</p>",
                    question_hi: "<p>37. भारतीय दंड संहिता की निम्नलिखित में से किस धारा का संबंध \'बलात्कार\' से है ?</p>",
                    options_en: [
                        "<p>Section 370</p>",
                        "<p>Section 375</p>",
                        "<p>Section 380</p>",
                        "<p>Section 385 </p>"
                    ],
                    options_hi: [
                        "<p>धारा 370</p>",
                        "<p>धारा 375</p>",
                        "<p>धारा 380</p>",
                        "<p>धारा 385</p>"
                    ],
                    solution_en: "<p>37.(b) <strong>Section 375. </strong>Section 370 of the Indian Penal Code (IPC) deals with the crime of human trafficking. Section 380 of the Indian Penal Code (IPC) deals with theft in a dwelling house, tent, or vessel that is used as a human dwelling or for the custody of property. Section 385 of the Indian Penal Code (IPC) deals with putting someone in fear of injury to commit extortion.</p>",
                    solution_hi: "<p>37.(b) <strong>धारा 375. </strong>भारतीय दंड संहिता (IPC) की धारा 370 मानव तस्करी के अपराध से संबंधित है। भारतीय दंड संहिता (IPC) की धारा 380 किसी आवासीय घर, टेंट या जहाज में चोरी से संबंधित है जिसका उपयोग मानव आवास या संपत्ति की रखवाली के लिए किया जाता है। भारतीय दंड संहिता (IPC) की धारा 385 जबरन वसूली करने के लिए किसी को चोट पहुंचाने का भय दिखाने से संबंधित है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which country topped the Sustainable Trade Index 2024 ?</p>",
                    question_hi: "<p>38. सतत व्यापार सूचकांक 2024 में कौन सा देश शीर्ष पर रहा ?</p>",
                    options_en: [
                        "<p>Sweden</p>",
                        "<p>New Zealand</p>",
                        "<p>Norway</p>",
                        "<p>Finland</p>"
                    ],
                    options_hi: [
                        "<p>स्वीडन</p>",
                        "<p>न्यूजीलैंड</p>",
                        "<p>नॉर्वे</p>",
                        "<p>फिनलैंड</p>"
                    ],
                    solution_en: "<p>38.(b) <strong>New Zealand</strong> ranked 1st in the Sustainable Trade Index 2024 for the third consecutive year, achieving the highest overall score of 100. India, with a score of 24, ranked 23rd globally. It is produced by the Hinrich Foundation and Institute for Management Development (IMD).</p>",
                    solution_hi: "<p>38.(b) <strong>न्यूजीलैंड</strong> लगातार तीसरे वर्ष सतत व्यापार सूचकांक 2024 में प्रथम स्थान पर रहा, जिसने 100 का उच्चतम समग्र स्कोर प्राप्त किया। भारत 24 के स्कोर के साथ विश्व स्तर पर 23वें स्थान पर रहा। इसे हिनरिच फाउंडेशन और इंस्टीट्यूट फॉर मैनेजमेंट डेवलपमेंट (IMD) द्वारा तैयार किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which of the following Indian cities is associated with the Sangam literature?</p>",
                    question_hi: "<p>39. निम्नलिखित में से किस भारतीय शहर का संबंध संगम साहित्य से है?</p>",
                    options_en: [
                        "<p>Madurai</p>",
                        "<p>Bodh Gaya</p>",
                        "<p>Ujjain</p>",
                        "<p>Sarnath</p>"
                    ],
                    options_hi: [
                        "<p>मदुरई</p>",
                        "<p>बोधगया</p>",
                        "<p>उज्जैन</p>",
                        "<p>सारनाथ</p>"
                    ],
                    solution_en: "<p>39.(a) <strong>Madurai</strong>. According to Tamil legends, there were three Sangams in ancient South India, known as Muchchangam. The First Sangam took place in Madurai, the Second Sangam in Kapadapuram, and the Third Sangam was again held in Madurai.</p>",
                    solution_hi: "<p>39.(a) <strong>मदुरै।</strong> तमिल किंवदंतियों के अनुसार, प्राचीन दक्षिण भारत में तीन संगम थे, जिन्हें मुच्चंगम के नाम से जाना जाता है।पहला संगम उत्सव मदुरै में आयोजित किया गया था।, दूसरा संगम कपाडपुरम में तथा तीसरा संगम पुनः मदुरै में आयोजित किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which of the following contains small droplets of liquid or particles of solid dispersed in a gas?</p>",
                    question_hi: "<p>40. निम्नलिखित में से किसमें गैस में परिक्षेपित द्रव की छोटी-छोटी बूंदें या ठोस के कण होते हैं ?</p>",
                    options_en: [
                        "<p>Aerosol</p>",
                        "<p>Gel</p>",
                        "<p>Vapour</p>",
                        "<p>Foam</p>"
                    ],
                    options_hi: [
                        "<p>ऐरोसॉल (Aerosol)</p>",
                        "<p>जेल (Gel)</p>",
                        "<p>वाष्प (Vapour)</p>",
                        "<p>फ़ोम (Foam)</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Aerosol</strong> Gel is a semi-solid material that is a mixture of solid particles suspended in a liquid. Vapour is diffused matter (such as smoke or fog) suspended floating in the air and impairing its transparency. Foam is a colloidal system of gas bubbles dispersed in a liquid.</p>",
                    solution_hi: "<p>40.(a) <strong>ऐरोसॉल ।</strong> जेल एक अर्ध-ठोस पदार्थ है जो द्रव में निलंबित ठोस कणों का मिश्रण होता है। वाष्प एक विसरित पदार्थ (जैसे धुआँ या कोहरा) है, जो हवा में चलायमान रहता है और इसकी पारदर्शिता को ख़राब करता है। फोम, द्रव में विसरित गैस के बुलबुले की एक कोलॉइडी तंत्र है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. The variation between a high air temperature and a low temperature that occurs during the same day is called _______ temperature.</p>",
                    question_hi: "<p>41 एक ही दिन के दौरान होने वाले उच्च वायु तापमान और निम्न वायु तापमान के बीच के अंतर को _______ तापमान कहा जाता है।</p>",
                    options_en: [
                        "<p>diurnal</p>",
                        "<p>annual</p>",
                        "<p>wet bulb</p>",
                        "<p>durian</p>"
                    ],
                    options_hi: [
                        "<p>दैनिक</p>",
                        "<p>वार्षिक</p>",
                        "<p>आर्द्र बल्ब</p>",
                        "<p>ड्यूरियन</p>"
                    ],
                    solution_en: "<p>41.(a) <strong>diurnal.</strong> The diurnal range of temperature is calculated by subtracting the daily minimum temperature from the daily maximum temperature. Wet bulb temperature is the lowest temperature to which air can be cooled by the evaporation of water into the air at a constant pressure.</p>",
                    solution_hi: "<p>41.(a) <strong>दैनिक।</strong> दैनिक तापमान की सीमा की गणना दैनिक अधिकतम तापमान से दैनिक न्यूनतम तापमान को घटाकर की जाती है। आर्द्र बल्ब (वेट बल्ब) तापमान वह न्यूनतम तापमान है जिस पर स्थिर दाब पर वायु में जल के वाष्पीकरण द्वारा वायु को ठंडा किया जा सकता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. How many major theories have been established on the origin of iron in India?</p>",
                    question_hi: "<p>42. भारत में लोहे की उत्पत्ति पर कितने प्रमुख सिद्धांत स्थापित (established) किए गए हैं?</p>",
                    options_en: [
                        "<p>five</p>",
                        "<p>four</p>",
                        "<p>three</p>",
                        "<p>two</p>"
                    ],
                    options_hi: [
                        "<p>पांच</p>",
                        "<p>चार</p>",
                        "<p>तीन</p>",
                        "<p>दो</p>"
                    ],
                    solution_en: "<p>42.(d) <strong>Two. </strong>There are two main theories about how iron came to India: the external origin theory and the indigenous origin theory. The external origin theory, by R.E.M. Wheeler suggests that iron was brought to India by the Achaemenids around the fifth century BCE. The indigenous origin theory posits that iron technology developed independently in India, based on local iron ore, tribal records, and archaeological findings.</p>",
                    solution_hi: "<p>42.(d) <strong>दो। </strong>भारत में लोहा कैसे आया, इसके बारे में दो मुख्य सिद्धांत हैं: बाह्य उत्पत्ति सिद्धांत और स्वदेशी उत्पत्ति सिद्धांत। आर.ई.एम. व्हीलर द्वारा प्रस्तुत बाह्य उत्पत्ति सिद्धांत से पता चलता है कि लोहा पाँचवीं शताब्दी ईसा पूर्व के आसपास एकेमेनिड्स द्वारा भारत लाया गया था। स्वदेशी उत्पत्ति सिद्धांत यह मानता है कि स्थानीय लौह अयस्क, आदिवासी अभिलेखों और पुरातात्विक निष्कर्षों के आधार पर भारत में लौह प्रौद्योगिकी स्वतंत्र रूप से विकसित हुई।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. &lsquo;Per Drop More Crop&rsquo; is the main tagline of which government scheme?</p>",
                    question_hi: "<p>43. \'पर ड्रॉप मोर क्रॉप (Per Drop More Crop)\' किस सरकारी योजना की मुख्य टैग लाइन है?</p>",
                    options_en: [
                        "<p>Pradhan Mantri Fasal Bima Yojana</p>",
                        "<p>Pradhan Mantri Krishi Sinchai Yojana</p>",
                        "<p>Atal Bhujal Yojana</p>",
                        "<p>National Action Plan for Climate Change</p>"
                    ],
                    options_hi: [
                        "<p>प्रधान मंत्री फसल बीमा योजना</p>",
                        "<p>प्रधान मंत्री कृषि सिंचाई योजना</p>",
                        "<p>अटल भू-जल योजना</p>",
                        "<p>जलवायु परिवर्तन पर राष्&zwj;ट्रीय कार्य योजना</p>"
                    ],
                    solution_en: "<p>43.(b) <strong>Pradhan Mantri Krishi Sinchai Yojana: </strong>launched - 1st July, 2015 with the motto of \"Har Khet Ko Paani\". Other schemes related to agriculture: Pradhan Mantri Fasal Bima Yojana (PMFBY): Launched - 2016. Pradhan Mantri Kisan Maandhan Yojna (PMKMY): Launched - 2019. Pradhan Mantri Fasal Bima Yojana (PMFBY): launched - 2016. Paramparagat Krishi Vikas Yojana (PKVY): Launched - 2015.</p>",
                    solution_hi: "<p>43.(b) <strong>प्रधान मंत्री कृषि सिंचाई योजना: </strong>1 जुलाई, 2015 को \"हर खेत को पानी\" के आदर्श वाक्य के साथ लॉन्च की गई। कृषि से संबंधित अन्य योजनाएं: प्रधानमंत्री फसल बीमा योजना (PMFBY): सुभारंभ - 2016। प्रधानमंत्री किसान मानधन योजना (PMKMY): सुभारंभ - 2019। प्रधानमंत्री फसल बीमा योजना (PMFBY): सुभारंभ - 2016। परंपरागत कृषि विकास योजना ( PKVY): सुभारंभ - 2015।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. In the state legislative assembly, the Money bill can be introduced with the prior permission of __________.</p>",
                    question_hi: "<p>44. राज्य विधान सभा में, धन विधेयक को _______ की पूर्व अनुमति से पेश किया जा सकता है।</p>",
                    options_en: [
                        "<p>Governor</p>",
                        "<p>Auditor General</p>",
                        "<p>Finance Minister</p>",
                        "<p>CAG</p>"
                    ],
                    options_hi: [
                        "<p>राज्यपाल</p>",
                        "<p>महालेखा परीक्षक</p>",
                        "<p>वित्त मंत्री</p>",
                        "<p>भारत के नियन्त्रक एवं महालेखा परीक्षक (CAG)</p>"
                    ],
                    solution_en: "<p>44.(a) <strong>Governor. </strong>Money Bills in State Legislatures : A Money Bill is a special type of financial bill that deals with matters like taxation, borrowing, or expenditure from the state\'s Consolidated Fund. Article 198 of the Constitution of India deals with the special procedure for money bills in state legislatures. Article 148 : Provision for the appointment of the Comptroller and Auditor General of India (CAG) by the President.</p>",
                    solution_hi: "<p>44.(a) <strong>राज्यपाल। </strong>राज्य विधानमंडलों में धन विधेयक: धन विधेयक एक विशेष प्रकार का वित्तीय विधेयक होता है जो राज्य के समेकित कोष से कराधान, उधार या व्यय जैसे मामलों से संबंधित होता है। भारत के संविधान का अनुच्छेद 198 राज्य विधानमंडलों में धन विधेयकों के लिए विशेष प्रक्रिया से संबंधित है। अनुच्छेद 148: राष्ट्रपति द्वारा भारत के नियंत्रक और महालेखा परीक्षक (CAG) की नियुक्ति का प्रावधान करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. World Malaria Day is observed on which date every year?</p>",
                    question_hi: "<p>45. विश्व मलेरिया दिवस हर साल किस तारीख को मनाया जाता है?</p>",
                    options_en: [
                        "<p>April 24</p>",
                        "<p>April 25</p>",
                        "<p>April 26</p>",
                        "<p>April 27</p>"
                    ],
                    options_hi: [
                        "<p>24 अप्रैल</p>",
                        "<p>25 अप्रैल</p>",
                        "<p>26 अप्रैल</p>",
                        "<p>27 अप्रैल</p>"
                    ],
                    solution_en: "<p>45.(b) <strong>April 25.</strong> World Malaria Day is observed annually to raise awareness about the prevention, treatment, and control of malaria. The theme for 2024 is \"Accelerating the fight against malaria for a more equitable world&rdquo;.</p>",
                    solution_hi: "<p>45.(b) <strong>25 अप्रैल।</strong> मलेरिया की रोकथाम, उपचार और नियंत्रण के बारे में जागरूकता बढ़ाने के लिए प्रतिवर्ष विश्व मलेरिया दिवस मनाया जाता है। 2024 का थीम \"अधिक न्यायसंगत दुनिया के लिए मलेरिया के खिलाफ लड़ाई में तेजी लाना\" है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. What is the full form of NE&Iota;&Alpha; ?</p>",
                    question_hi: "<p>46. NEIA का पूर्ण रूप क्या है ?</p>",
                    options_en: [
                        "<p>National Export Insurance Authority</p>",
                        "<p>National Emergency Insurance Account</p>",
                        "<p>National Export Insurance Account</p>",
                        "<p>National Export Issuance Account</p>"
                    ],
                    options_hi: [
                        "<p>नेशनल एक्सपोर्ट इंश्योरेंस अथॉरिटी (National Export Insurance Authority)</p>",
                        "<p>नेशनल इमरजेंसी इंश्योरेंस अकाउंट (National Emergency Insurance Account)</p>",
                        "<p>नेशनल एक्सपोर्ट इंश्योरेंस अकाउंट (National Export Insurance Account)</p>",
                        "<p>नेशनल एक्सपोर्ट इशूएन्स अकाउंट (National Export Issuance Account)</p>"
                    ],
                    solution_en: "<p>46.(c) <strong>National Export Insurance Account</strong> (NEIA) Trust aims to ensure the availability of credit risk cover for projects and other high-value exports. It was set up in 2006 to promote Medium and Long-Term (MLT) / project exports by enabling credit and political insurance.</p>",
                    solution_hi: "<p>46.(c)<strong> नेशनल एक्सपोर्ट इंश्योरेंस अकाउंट</strong> ((NEIA)) ट्रस्ट का उद्देश्य परियोजनाओं और अन्य उच्च मूल्य वाले निर्यातों के लिए ऋण जोखिम कवर की उपलब्धता सुनिश्चित करना है। इसे ऋण और राजनीतिक बीमा को सक्षम करके मध्यम और दीर्घकालिक (MLT) / परियोजना निर्यात को बढ़ावा देने के लिए 2006 में स्थापित किया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. When was the Duty-Free Tariff Preference (DFTP) Scheme for Least Developed Countries (LDCs) announced by India?</p>",
                    question_hi: "<p>47. भारत द्वारा अल्प विकसित देशों (LDCs) के लिए शुल्क-मुक्त टैरिफ वरीयता (DFTP) योजना की घोषणा कब की गई थी?</p>",
                    options_en: [
                        "<p>2010</p>",
                        "<p>2009</p>",
                        "<p>2006</p>",
                        "<p>2008</p>"
                    ],
                    options_hi: [
                        "<p>2010</p>",
                        "<p>2009</p>",
                        "<p>2006</p>",
                        "<p>2008</p>"
                    ],
                    solution_en: "<p>47.(d)<strong> 2008</strong>. Duty Free Tariff Preference (DFTP) scheme of 2008 : Prime Minister Manmohan Singh announced the DFTP scheme at the India-Africa Forum Summit on April 8, 2008. India became the first developing country to extend this facility to Least Developed Countries (LDCs). Objective : To grant tariff preferences on the exports of the Least Developed Countries on imports to India.</p>",
                    solution_hi: "<p>47.(d) <strong>2008</strong>. शुल्क-मुक्त टैरिफ वरीयता (DFTP) योजना 2008: प्रधानमंत्री मनमोहन सिंह ने 8 अप्रैल, 2008 को भारत-अफ्रीका फोरम शिखर सम्मेलन में DFTP योजना की घोषणा की थी। भारत सबसे कम विकसित देशों (LDC) को यह सुविधा देने वाला पहला विकासशील देश बन गया। उद्देश्य: भारत में आयात पर अल्प विकसित देशों के निर्यात पर टैरिफ वरीयता प्रदान करना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. The transportation mechanism in cells by which food or other useful molecules from the outside are carried inside the cell is known as ___________.</p>",
                    question_hi: "<p>48. कोशिकाओं में वह परिवहन तंत्र जिसके द्वारा बाहर से भोजन या अन्य उपयोगी अणुओं को कोशिका के अंदर ले जाया जाता है, ________ कहलाता है।</p>",
                    options_en: [
                        "<p>Osmosis</p>",
                        "<p>Fusion</p>",
                        "<p>Endocytosis</p>",
                        "<p>Exocytosis</p>"
                    ],
                    options_hi: [
                        "<p>परासरण (Osmosis)</p>",
                        "<p>संलयन (Fusion)</p>",
                        "<p>अंतःकोशिकता (Endocytosis)</p>",
                        "<p>बहिःकोशिकता (Exocytosis)</p>"
                    ],
                    solution_en: "<p>48.(c) <strong>Endocytosis.</strong> It is the process of engulfing a food particle or substance by a cell. The cell membrane is flexible and its shape can be changed as needed. For Example - An amoeba catches its food by changing its shape and using pseudopodia to capture food. Osmosis: It is the movement of water molecules across a semi-permeable membrane from an area of lower solute concentration to higher solute concentration, without energy expenditure. Exocytosis: It is the process by which cells expel substances, such as waste or secretory products, by merging vesicles with the cell membrane.</p>",
                    solution_hi: "<p>48.(c) <strong>अंतःकोशिकता</strong> (Endocytosis)। यह एक कोशिका द्वारा खाद्य के कण या पदार्थ को निगलने की प्रक्रिया है। कोशिका झिल्ली लचीली होती है और आवश्यकतानुसार अपना आकार बदल सकती है। उदाहरण के लिए - एक अमीबा अपने आकार को बदलकर और पादाभ जालिका (छिद्र) का उपयोग करके अपना भोजन पकड़ता है। परासरण (Osmosis): यह पानी के अणुओं का एक अर्ध-पारगम्य झिल्ली के पार से बिना ऊर्जा व्यय किए, कम सांद्रता वाले क्षेत्र से अधिक सांद्रता वाले क्षेत्र में स्थानांतरण की प्रक्रिया है। बहिःकोशिकता (Exocytosis): यह एक ऐसी प्रक्रिया है जिसमें कोशिकाएं कोशिका झिल्ली के साथ पुटिकाओं को विलय करके अपशिष्ट या स्रावी उत्पादों जैसे पदार्थों को बाहर निकालती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following cell organelles is involved in apoptosis?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन-सा कोशिकांग एपोप्टोसिस में शामिल होता है?</p>",
                    options_en: [
                        "<p>Golgi</p>",
                        "<p>ER</p>",
                        "<p>Lysosome</p>",
                        "<p>Mitochondria</p>"
                    ],
                    options_hi: [
                        "<p>गॉल्जी</p>",
                        "<p>ईआर (ER)</p>",
                        "<p>लाइसोसोम</p>",
                        "<p>माइटोकॉन्ड्रिया</p>"
                    ],
                    solution_en: "<p>49.(d) <strong>Mitochondria.</strong> Apoptosis, or programmed cell death, is a process where cells die through a series of molecular steps. Mitochondria play a crucial role in apoptosis (programmed cell death) by releasing cytochrome c, which triggers the activation of caspases, the enzymes that execute the cell death process. Mitochondria, known as the \"powerhouse of the cell,\" generate most of the energy for cellular functions. Lysosomes are membrane-bound organelles containing digestive enzymes. The Golgi apparatus processes proteins and lipids, while the endoplasmic reticulum is a membrane network that transports proteins and other molecules within the cell.</p>",
                    solution_hi: "<p>49.(d) <strong>माइटोकॉन्ड्रिया।</strong> एपोप्टोसिस, या कोशिका-नियोजित मृत्यु, एक प्रक्रिया है जिसमें कोशिकाएँ आणविक चरणों की एक श्रृंखला के माध्यम से मृत्यु होती हैं। माइटोकॉन्ड्रिया एपोप्टोसिस (क्रमबद्ध कोशिका मृत्यु) में एक महत्वपूर्ण भूमिका निभाते हैं, क्योंकि वे साइटोक्रोम c को छोड़ते हैं, जो कैस्पेस नामक एंजाइमों को सक्रिय करता है, जो कोशिका मृत्यु की प्रक्रिया को अंजाम देते हैं। माइटोकॉन्ड्रिया, जिसे \"कोशिका का ऊर्जागृह\" कहा जाता है, कोशिका कार्यों के लिए अधिकांश ऊर्जा उत्पन्न करता है। लाइसोसोम पाचक एंजाइम युक्त झिल्ली-बद्ध कोशिकांग हैं। गॉल्जी उपकरण प्रोटीन और लिपिड को संसाधित करता है, जबकि एंडोप्लाज्मिक रेटिकुलम एक झिल्ली नेटवर्क है जो कोशिका के भीतर प्रोटीन और अन्य अणुओं का परिवहन करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. A distinctive cross-shaped constellation best seen in the northern hemisphere during the summer and fall months around September is:</p>",
                    question_hi: "<p>50. सितंबर के आसपास गर्मियों और पतझड़ के महीनों के दौरान उत्तरी गोलार्ध में सबसे स्पष्ट रूप से देखा जा सकने वाला एक विशिष्ट क्रॉस - आकार (cross-shaped) का तारामंडल है:</p>",
                    options_en: [
                        "<p>Cygnus</p>",
                        "<p>Ursa Major</p>",
                        "<p>Pegasus</p>",
                        "<p>Cassiopeia</p>"
                    ],
                    options_hi: [
                        "<p>सिग्नस (Cygnus)</p>",
                        "<p>सप्तर्षिमंडल (Ursa Major)</p>",
                        "<p>पेगसस (Pegasus)</p>",
                        "<p>शर्मिष्ठा (Cassiopeia)</p>"
                    ],
                    solution_en: "<p>50.(a) <strong>Cygnus.</strong> It is a northern constellation on the plane of the Milky Way, deriving its name from the Latinized Greek word for swan. Ursa Major, also known as the Great Bear, is a constellation in the northern sky. It is visible throughout the year, but it is best seen from April to September. Pegasus is a constellation in the northern sky which appears in autumn. Cassiopeia is a constellation visible in the northern hemisphere year-round at latitudes above 34&deg;N.</p>",
                    solution_hi: "<p>50.(a)<strong> सिग्नस (Cygnus)। </strong>यह आकाशगंगा में स्थित एक उत्तरी तारामंडल है, जिसका नाम लैटिनकृत ग्रीक शब्द हंस से लिया गया है। उर्सा मेजर, जिसे ग्रेट बियर के नाम से भी जाना जाता है, उत्तरी आकाश में एक तारामंडल है। यह सम्पूर्ण वर्ष दिखाई देता है, लेकिन यह अप्रैल से सितंबर तक स्पष्ट दिखाई देता है। पेगासस उत्तरी आकाश में एक तारामंडल है जो शरद ऋतु में दिखाई देता है। कैसिओपिया एक तारामंडल है जो उत्तरी गोलार्ध में 34&deg;N से ऊपर के अक्षांशों पर साल भर दिखाई देता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Three pipes P, Q and R can fill a cistern in 40 minutes, 80 minutes and 120 minutes, respectively. Initially, all the pipes are opened. After how much time (in minutes) should the pipes Q and R be turned off so that the cistern will be completely filled in just half an hour?</p>",
                    question_hi: "<p>51. तीन पाइप P, Q और R एक टंकी को क्रमशः 40 मिनट, 80 मिनट और 120 मिनट में भर सकते हैं। शुरुआत में, सभी पाइप खोल दिए जाते हैं। कितने समय (मिनटों में) के बाद, पाइप Q और R को बंद कर देना चाहिए ताकि टंकी केवल आधे घंटे में पूरी भर जाए?</p>",
                    options_en: [
                        "<p>14</p>",
                        "<p>10</p>",
                        "<p>16</p>",
                        "<p>12</p>"
                    ],
                    options_hi: [
                        "<p>14</p>",
                        "<p>10</p>",
                        "<p>16</p>",
                        "<p>12</p>"
                    ],
                    solution_en: "<p>51.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799596424.png\" alt=\"rId46\" width=\"183\" height=\"125\"><br>Let pipes Q and R be turned off for t minutes<br>According to question <br>(6 + 3 + 2) &times; t + 6 &times; (30 - t) = 240<br>11t + 180 - 6t = 240<br>5t = 60<br>&rArr; t = 12 minutes</p>",
                    solution_hi: "<p>51.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799596593.png\" alt=\"rId47\" width=\"222\" height=\"151\"><br>माना , पाइप Q और R को t मिनट के लिए बंद कर दिया गया है<br>प्रश्न के अनुसार <br>(6 + 3 + 2) &times; t + 6 &times; (30 - t) = 240<br>11t + 180 - 6t = 240<br>5t = 60<br>&rArr; t = 12 मिनट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. If x<sup>3</sup> = 184 + y<sup>3</sup> and x = 4 + y, then the value of (x + y) is (given that x &gt; 0 and y &gt; 0):</p>",
                    question_hi: "<p>52. यदि x<sup>3</sup> = 184 + y<sup>3</sup> और x = 4 + y है, तो (x + y) का मान ज्ञात कीजिए। (दिया गया है कि x &gt; 0 और y &gt; 0 है)</p>",
                    options_en: [
                        "<p>-2<math display=\"inline\"><msqrt><mn>11</mn></msqrt></math></p>",
                        "<p>2<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math></p>",
                        "<p>-2<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math></p>",
                        "<p>2<math display=\"inline\"><msqrt><mn>11</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p>-2<math display=\"inline\"><msqrt><mn>11</mn></msqrt></math></p>",
                        "<p>2<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math></p>",
                        "<p>-2<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math></p>",
                        "<p>2<math display=\"inline\"><msqrt><mn>11</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>52.(b) <strong>Given:</strong><br>x = 4 + y <br>&rArr; x - y = 4<br>&rArr; x<sup>2</sup> + y<sup>2</sup> - 2xy = 16 &hellip;&hellip;&hellip;&hellip;(i)<br>and <br>x<sup>3</sup> = 184 + y<sup>3</sup><br>&rArr; x<sup>3</sup> - y<sup>3</sup> = 184&nbsp;<br>&rArr; (x - y) (x<sup>2</sup> + y<sup>2</sup> + xy) = 184<br>&rArr; (4)(x<sup>2</sup> + y<sup>2</sup> + xy) = 184<br>&rArr; (x<sup>2</sup> + y<sup>2</sup> + xy) = 46 &hellip;&hellip;&hellip;&hellip;(ii)<br>On solving eq . (i) and (ii)<br>xy = 10<br>&rArr; (x + y)<sup>2</sup> = (x<sup>2</sup> + y<sup>2</sup> + xy) + xy <br>= 46 + 10 = 56<br>So, x + y = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>14</mn></msqrt></math></p>",
                    solution_hi: "<p>52.(b) <strong>दिया गया है :</strong><br>x = 4 + y <br>&rArr; x - y = 4<br>&rArr; x<sup>2</sup> + y<sup>2</sup> - 2xy = 16 &hellip;&hellip;&hellip;&hellip;(i)<br>और,<br>x<sup>3</sup> = 184 + y<sup>3</sup><br>&rArr; x<sup>3</sup> - y<sup>3</sup> = 184&nbsp;<br>&rArr; (x - y) (x<sup>2</sup> + y<sup>2</sup> + xy) = 184<br>&rArr; (4)(x<sup>2</sup> + y<sup>2</sup> + xy) = 184<br>&rArr; (x<sup>2</sup> + y<sup>2</sup> + xy) = 46 &hellip;&hellip;&hellip;&hellip;(ii)<br>समीकरण (i) और (ii) को हल करने पर, <br>xy = 10<br>&rArr; (x + y)<sup>2</sup> = (x<sup>2</sup> + y<sup>2</sup> + xy) + xy <br>= 46 + 10 = 56<br>तो, x + y = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>14</mn></msqrt></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Rahna decided to buy a scooty that costs ₹40,000. The shopkeeper agreed to sell the scooty under the condition of ₹25,000 cash down payment of ₹4,000 each month for four months. Find the rate of interest at which the shopkeeper sold the scooty under the installment scheme.</p>",
                    question_hi: "<p>53. राहना ने एक स्कूटी खरीदने का फैसला किया जिसका मूल्य ₹40,000 है। दुकानदार ₹25,000 के नकद भुगतान और चार महीने तक हर महीने ₹4,000 के किश्त के भुगतान की शर्त के अंतर्गत स्कूटी बेचने पर सहमत हुआ। किश्त योजना के अंतर्गत दुकानदार ने जिस ब्याज दर पर स्कूटी बेची वह ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>33%</p>",
                        "<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                        "<p>18<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                        "<p>10<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>33%</p>",
                        "<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                        "<p>18<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                        "<p>10<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>53.(a)<br>Remaining amount = 40,000 -&nbsp;25,000 = 15,000 ₹<br>Amount = 15,000 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>,</mo><mn>000</mn><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>4</mn></mrow><mrow><mn>12</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac></math> = 15,000 + 50R<br>Sum of Amounts of these installments <br>=&nbsp;(4000 + S.I. on 4000 for 3 months) + (4000 + S.I. on 4000 for 2 months)&nbsp;+ (4000 + S.I. on 4000 for 1 months) + 4000 <br>= (4000 &times; 4) + (S.I. on 4000 for 6 months) <br>= 16000 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4000</mn><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>6</mn></mrow><mrow><mn>12</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac></math> <br>= 16000 + 20R<br>Now , equating amount :- <br>15,000 + 50R = 16000 + 20R<br>&rArr; 30R = 1,000 <br>&rArr; R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>30</mn></mfrac></math> = 33.33% or 33%</p>",
                    solution_hi: "<p>53.(a)<br>शेष राशि = 40,000 -&nbsp;25,000 = 15,000 ₹<br>राशि = 15,000 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>,</mo><mn>000</mn><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>4</mn></mrow><mrow><mn>12</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac></math>&nbsp;= 15,000 + 50R<br>इन किस्तों की रकम का योग <br>=&nbsp;(4000 + 3 महीने के लिए 4000 पर SI) + (4000 + 2 महीने के लिए 4000 पर SI)&nbsp;+ (4000 + 1 महीने के लिए 4000 पर SI) + 4000 <br>= (4000 &times; 4) + (6 महीने के लिए 4000 पर SI) <br>= 16000 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4000</mn><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>6</mn></mrow><mrow><mn>12</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac></math>&nbsp;<br>= 16000 + 20R<br>अब, राशि बराबर करें:-<br>15,000 + 50R = 16000 + 20R<br>&rArr; 30R = 1,000 <br>&rArr; R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>30</mn></mfrac></math> = 33.33% या 33%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. A cuboid with dimensions 20cm, 12cm, and 10 cm is cut into 8 identical pieces by 3 cuts. What will be the total surface area of all the pieces?</p>",
                    question_hi: "<p>54. 20 सेमी, 12 सेमी और 10 सेमी आयाम वाले एक घनाभ को 3 कट द्वारा 8 समान टुकड़ों में काटा जाता है। सभी टुकड़ों का कुल पृष्ठीय क्षेत्रफल कितना होगा ?</p>",
                    options_en: [
                        "<p>2240 cm&sup2;</p>",
                        "<p>1220 cm&sup2;</p>",
                        "<p>3360 cm&sup2;</p>",
                        "<p>1680 cm&sup2;</p>"
                    ],
                    options_hi: [
                        "<p>2240 सेमी<sup>2</sup></p>",
                        "<p>1220 सेमी<sup>2</sup></p>",
                        "<p>3360 सेमी<sup>2</sup></p>",
                        "<p>1680 सेमी<sup>2</sup></p>"
                    ],
                    solution_en: "<p>54.(a)<br>New length of cuboid = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 10 cm<br>New breadth of cuboid = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 6 cm<br>New height of cuboid = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5 cm<br>TSA of all the pieces = 8 &times; 2(10 &times; 6 + 6 &times; 5 + 5 &times; 10) <br>= 8 &times; 2(60 + 30 + 50) = 2240 cm<sup>2</sup></p>",
                    solution_hi: "<p>54.(a)<br>घनाभ की नई लंबाई = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 10 सेमी <br>घनाभ की नई चौड़ाई = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 6 सेमी <br>घनाभ की नई ऊंचाई = <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5 सेमी <br>सभी टुकड़ों का कुल प्रष्ठीय क्षेत्रफल = 8 &times; 2(10 &times; 6 + 6 &times; 5 + 5 &times; 10) <br>= 8 &times; 2(60 + 30 + 50) = 2240 सेमी<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. P pays Q a sum of ₹150 using coins of ₹2, ₹5 and ₹10. He uses a total of 50 coins. If&nbsp;the ratio of ₹2 and ₹5 coins used is 5 : 2, then how many coins of ₹10 are used in the&nbsp;payment?</p>",
                    question_hi: "<p>55. P, Q को ₹2, ₹5 और ₹10 के सिक्कों का उपयोग करके ₹150 का भुगतान करता है। वह कुल 50 सिक्कों का उपयोग करता है। यदि उपयोग किए गए ₹2 और ₹5 के सिक्कों का अनुपात 5 : 2 है, तो भुगतान में ₹10 के कितने सिक्के उपयोग किए गए हैं?</p>\n<p>&nbsp;</p>\n<p>&nbsp;</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p>55.(b)<br>Ratio&nbsp; &nbsp;-&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;₹2 : ₹5 : ₹10<br>Number of coins - 5x&nbsp;: 2x : 50 - 7x<br>Total amount = 10x&nbsp;+ 10x + 500 - 70x = 150<br>50x&nbsp;= 350, x = 7<br>So the number of 10 rupees coins = 50 - (7 &times; 7) = 1</p>",
                    solution_hi: "<p>55.(b)<br>अनुपात&nbsp; &nbsp;-&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ₹2 : ₹5 : ₹10<br>सिक्कों की संख्या - 5x&nbsp;: 2x : 50 - 7x<br>कुल राशि = 10x&nbsp;+ 10x + 500 - 70x = 150<br>50x&nbsp;= 350, x = 7<br>अतः 10 रुपये के सिक्कों की संख्या = 50 - (7 &times; 7) = 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. The sum of two natural numbers is 80 and their HCF is 10. How many such pairs of numbers are possible?</p>",
                    question_hi: "<p>56. दो प्राकृत संख्याओं का योग 80 है और उनका एचसीएफ (HCF) 10 है। संख्&zwj;याओं के ऐसे कितने युग्&zwj;म संभव हैं?</p>",
                    options_en: [
                        "<p>7</p>",
                        "<p>3</p>",
                        "<p>2</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>7</p>",
                        "<p>3</p>",
                        "<p>2</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>56.(c) Let , the numbers are 10x and 10y <br>&rArr; 10x + 10y = 80<br>&rArr;10(x + y ) = 80 <br>&rArr; (x + y ) = 8<br>Possible pairs (x, y ) = (1 , 7 ) (3 , 5)<br>Possible pair are (10 , 70) and (30 , 50 )</p>",
                    solution_hi: "<p>56.(c) माना, संख्याएँ 10x और 10y हैं&nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>10x + 10y = 80<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>10(x + y ) = 80 &nbsp;(x + y ) = 8<br>संभावित युग्&zwj;म (x, y) = &nbsp;(1, 7), &nbsp;(3, 5)&nbsp;<br>संभावित युग्&zwj;म (10, 70) और (30, 50) हैं</p>\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. In a mixture of 220 litre the ratio of water and milk is 6 : 5 respectively. How much water must be added to make the ratio of water and milk as 5 : 4 respectively ?</p>",
                    question_hi: "<p>57. 220 लीटर के मिश्रण में पानी और दूध का अनुपात क्रमशः 6 : 5 है। पानी और दूध का अनुपात क्रमशः 5 : 4 करने के लिए मिश्रण में कितना पानी मिलाना होगा?</p>",
                    options_en: [
                        "<p>3 litre</p>",
                        "<p>5 litre</p>",
                        "<p>2 litre</p>",
                        "<p>10 litre</p>"
                    ],
                    options_hi: [
                        "<p>3 लीटर</p>",
                        "<p>5 लीटर</p>",
                        "<p>2 लीटर</p>",
                        "<p>10 लीटर</p>"
                    ],
                    solution_en: "<p>57.(b)<br>Balancing the given ratio, we have ; <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Water&nbsp; &nbsp; Milk<br>Initial mixture&nbsp; &nbsp; 6<sub>&times;4&nbsp; &nbsp; </sub>:&nbsp; &nbsp;5<sub>&times;4</sub> = 24 : 20<br>Final mixture&nbsp; &nbsp; &nbsp;5<sub>&times;5</sub>&nbsp; &nbsp;:&nbsp; &nbsp;4<sub>&times;5</sub> = 25 : 20<br>Total mixture = 24 + 20 = 44 unit<br>Quantity of water to be added = <math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>24</mn></mrow><mrow><mn>44</mn></mrow></mfrac></math> &times; 220 = 5 litres</p>",
                    solution_hi: "<p>57.(b)<br>दिए गए अनुपात को संतुलित करते हुए, हमारे पास है;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; पानी&nbsp; &nbsp; &nbsp; दूध<br>प्रारंभिक मिश्रण&nbsp; &nbsp; 6<sub>&times;4</sub>&nbsp; :&nbsp; &nbsp;5<sub>&times;4</sub> = 24 : 20<br>अंतिम मिश्रण&nbsp; &nbsp; &nbsp; &nbsp;5<sub>&times;5</sub>&nbsp; :&nbsp; &nbsp;4<sub>&times;5</sub> = 25 : 20<br>कुल मिश्रण = 24 + 20 = 44 इकाई<br>मिलाये जाने वाले पानी की मात्रा = <math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>24</mn></mrow><mrow><mn>44</mn></mrow></mfrac></math> &times; 220 = 5 लीटर</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Given that 2<sup>0.83</sup> = x, 2<sup>0.09</sup> = y and x<sup>z</sup> = y<sup>5</sup> , then the value of z is close to:</p>",
                    question_hi: "<p>58. दिया गया है कि 2<sup>0.83</sup> = x, 2<sup>0.09</sup> = y और x<sup>z</sup> = y<sup>5</sup>&nbsp; है, तो z का निकटतम मान कितना है?</p>",
                    options_en: [
                        "<p>0.54</p>",
                        "<p>0.93</p>",
                        "<p>3.18</p>",
                        "<p>0.87</p>"
                    ],
                    options_hi: [
                        "<p>0.54</p>",
                        "<p>0.93</p>",
                        "<p>3.18</p>",
                        "<p>0.87</p>"
                    ],
                    solution_en: "<p>58.(a)<br><strong>Given: </strong>2<sup>0.83</sup> = x, and 2<sup>0.09</sup> = y<br>Now,<br>x<sup>z</sup> = y<sup>5</sup><br>&rArr; (2<sup>0.83</sup>)<sup>z</sup> = (2<sup>0.09</sup>)<sup>5</sup><br>&rArr; 2<sup>0.83&times;z</sup> = 2<sup>0.45</sup><br>Bases are equal then power will also be equal, <br>So,<br>&rArr; 0.83 &times; z = 0.45<br>&rArr; z = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>45</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>83</mn></mrow></mfrac></math> = 0.54</p>",
                    solution_hi: "<p>58.(a)<br><strong>दिया गया है:</strong> 2<sup>0.83</sup> = x, और 2<sup>0.09</sup> = y&nbsp;<br>अब,<br>x<sup>z</sup> = y<sup>5</sup><br>&rArr; (2<sup>0.83</sup>)<sup>z</sup> = (2<sup>0.09</sup>)<sup>5</sup><br>&rArr; 2<sup>0.83&times;z</sup> = 2<sup>0.45</sup><br>आधार बराबर हैं तो घात भी बराबर होगी, <br>इसलिए,<br>&rArr; 0.83 &times; z = 0.45<br>&rArr; z = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>45</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>83</mn></mrow></mfrac></math> = 0.54</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A and B together can complete a piece of work in 25 days, B and C together can complete the same piece of work in 36 days, while C and A together can complete it in 30 days. If A, B, C, and D together can complete this piece of work in 18 days, then in how many days can D alone complete this piece of work?</p>",
                    question_hi: "<p>59. A और B मिलकर किसी कार्य को 25 दिनों में पूरा कर सकते हैं, B और C मिलकर उसी कार्य को 36 दिनों में पूरा कर सकते हैं, जबकि C और A मिलकर उसी कार्य को 30 दिनों में पूरा कर सकते हैं। यदि A, B, C और D मिलकर इस कार्य को 18 दिनों में पूरा कर सकते हैं, तो D अकेला इस कार्य को कितने दिनों में पूरा कर सकता है?</p>",
                    options_en: [
                        "<p>225</p>",
                        "<p>210</p>",
                        "<p>200</p>",
                        "<p>180</p>"
                    ],
                    options_hi: [
                        "<p>225</p>",
                        "<p>210</p>",
                        "<p>200</p>",
                        "<p>180</p>"
                    ],
                    solution_en: "<p>59.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799596839.png\" alt=\"rId48\" width=\"243\" height=\"100\"><br>Efficiency of A + B + C = <math display=\"inline\"><mfrac><mrow><mn>91</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 45.5 unit<br>Efficiency of A + B + C + D = 50<br>Efficiency of D = 50 - 45.5 = 4.5 unit<br>Time taken by D alone = <math display=\"inline\"><mfrac><mrow><mn>900</mn></mrow><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 200 days</p>",
                    solution_hi: "<p>59.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799597146.png\" alt=\"rId49\" width=\"247\" height=\"108\"><br>A + B + C की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>91</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 45.5 इकाई<br>A + B + C + D की दक्षता = 50<br>D की दक्षता = 50 - 45.5 = 4.5 इकाई<br>अकेले D द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>900</mn></mrow><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 200 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. PQ and RS are two chords of a circle such that PQ = 12 cm and RS = 20 cm and PQ is parallel to RS. If the distance between PQ and RS is 4 cm, find the diameter of the&nbsp;Circle.</p>",
                    question_hi: "<p>60. PQ और RS एक वृत्त की दो जीवाएँ इस प्रकार हैं कि PQ = 12 cm और RS = 20 cm है और PQ, RS के समानांतर है। यदि PQ और RS के बीच की दूरी 4 cm है, तो वृत्त का व्यास ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>4 <math display=\"inline\"><msqrt><mn>34</mn><mi>&#160;</mi><mi>c</mi><mi>m</mi></msqrt></math></p>",
                        "<p>2<math display=\"inline\"><msqrt><mn>34</mn><mi>&#160;</mi><mi>c</mi><mi>m</mi></msqrt></math></p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>34</mn><mi>&#160;</mi><mi>c</mi><mi>m</mi></msqrt></math></p>",
                        "<p>6<math display=\"inline\"><msqrt><mn>34</mn><mi>&#160;</mi><mi>c</mi><mi>m</mi></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p>4 <math display=\"inline\"><msqrt><mn>34</mn><mi>&#160;</mi><mi>c</mi><mi>m</mi></msqrt></math></p>",
                        "<p>2<math display=\"inline\"><msqrt><mn>34</mn><mi>&#160;</mi><mi>c</mi><mi>m</mi></msqrt></math></p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>34</mn><mi>&#160;</mi><mi>c</mi><mi>m</mi></msqrt></math></p>",
                        "<p>6<math display=\"inline\"><msqrt><mn>34</mn><mi>&#160;</mi><mi>c</mi><mi>m</mi></msqrt></math></p>"
                    ],
                    solution_en: "<p>60.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799597470.png\" alt=\"rId50\" width=\"155\" height=\"145\"><br>OP<sup>2</sup> = x<sup>2</sup>&nbsp;+ 36 --------(i)<br>OR<sup>2</sup> = (4 - x)<sup>2</sup> + 100<br>OP = OR (Radius)<br>Then, x<sup>2</sup> + 36 = (4 - x)<sup>2</sup> + 100<br>x<sup>2</sup> + 36 = 16 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup></math> - 8x + 100<br>- 8x&nbsp;= - 80 <br>x = 10<br>Putting x = 10 in equation (i) we get;<br>OP<sup>2</sup> = 100 + 36 = 136<br>OP = <math display=\"inline\"><msqrt><mn>136</mn></msqrt></math> = 2<math display=\"inline\"><msqrt><mn>34</mn></msqrt></math> cm<br>Diameter = 2OP = 4<math display=\"inline\"><msqrt><mn>34</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>60.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799597470.png\" alt=\"rId50\" width=\"167\" height=\"156\"><br>OP<sup>2</sup> = x<sup>2</sup>&nbsp;+ 36 --------(i)<br>OR<sup>2</sup> = (4 - x)<sup>2</sup> + 100<br>OP = OR (त्रिज्या)<br>फिर, x<sup>2</sup> + 36 = (4 - x)<sup>2</sup> + 100<br>x<sup>2</sup> + 36 = 16 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>2</mn></msup></math> - 8x + 100<br>- 8x&nbsp;= - 80 <br>x = 10<br>समीकरण (i) में <math display=\"inline\"><mi>x</mi></math> = 10 रखने पर हमें प्राप्त होता है;<br>OP<sup>2</sup> = 100 + 36 = 136<br>OP = <math display=\"inline\"><msqrt><mn>136</mn></msqrt></math> = 2<math display=\"inline\"><msqrt><mn>34</mn></msqrt></math> cm<br>व्यास = 2OP = 4<math display=\"inline\"><msqrt><mn>34</mn></msqrt></math> cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A man walking at the speed of 5 km/h covers a certain distance in 3 hours 45 minutes.&nbsp;If he covers the same distance by a cycle in 2 hours 10 minutes, then the speed of the&nbsp;cycle in km/h is:</p>",
                    question_hi: "<p>61. एक व्यक्ति 5 km/h की चाल से चलते हुए एक निश्चित दूरी को 3 घंटे 45 मिनट में तय करता है। यदि वह समान दूरी को एक साइकिल द्वारा 2 घंटे 10 मिनट में तय करता है, तो साइकिल की km/h में चाल क्या है?</p>",
                    options_en: [
                        "<p>7<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>",
                        "<p>8<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>",
                        "<p>5<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>",
                        "<p>9<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>7<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>",
                        "<p>8<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>",
                        "<p>5<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>",
                        "<p>9<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>61.(b)<br>Distance = speed &times; time<br>Distance covered by walking = 5 &times; 3<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> km = 18.75 km<br>Time taken by cycle = 2 hours 10 min. = 130 min.<br>Required speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>.</mo><mn>75</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>130</mn></mfrac></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>26</mn></mfrac></math> km/h</p>",
                    solution_hi: "<p>61.(b)<br>दूरी = गति &times; समय<br>पैदल चलकर तय की गई दूरी = 5 &times; 3<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> km = 18.75 km<br>साइकिल द्वारा लिया गया समय = 2 घंटे 10 मिनट = 130 मिनट<br>आवश्यक गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>.</mo><mn>75</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>130</mn></mfrac></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>26</mn></mfrac></math>&nbsp;km/h</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Ashu can row 7.5 km an hour in still water. He finds that it takes four times as much time to row upstream, as it takes to row downstream. The speed of the stream is:</p>",
                    question_hi: "<p>62. आशु शान्त जल में 7.5 Km प्रति घंटा की गति से नाव चला सकता है। वह पाता है कि उसे धारा के प्रतिकूल नाव चलाने में, धारा के अनुकूल नाव चलाने में लगने वाले समय से चार गुना अधिक समय लगता है। धारा की गति ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>3.5 km/h</p>",
                        "<p>1.5 km/h</p>",
                        "<p>4.5 km/h</p>",
                        "<p>2.5 km/h</p>"
                    ],
                    options_hi: [
                        "<p>3.5 km/h</p>",
                        "<p>1.5 km/h</p>",
                        "<p>4.5 km/h</p>",
                        "<p>2.5 km/h</p>"
                    ],
                    solution_en: "<p>62.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Downstream&nbsp; &nbsp; &nbsp;Upstream<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (B + S)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(B - S)<br>Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 4 <br>Speed&nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 1<br>Speed of boat in still water = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> unit = 7.5 km/hr<br>Then, 1 unit = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>5</mn></mfrac></math> = 3 km/hr<br>Speed of stream = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> unit <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &times; 3 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math> = 4.5 km/hr</p>",
                    solution_hi: "<p>62.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;धारा के अनुकूल&nbsp; &nbsp; &nbsp; &nbsp; धारा के प्रतिकूल <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(B + S)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (B - S)<br>समय&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4 <br>गति&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1<br>शांत पानी में नाव की गति = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> इकाई = 7.5 किमी/घंटा<br>फिर, 1 इकाई = <math display=\"inline\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 3 किमी/घंटा<br>धारा की गति = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> इकाई&nbsp;<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &times; 3 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math> = किमी/घंटा</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. In a triangle PQR, RS intersects PQ at point S. The sides of the triangle QR = 36 cm, SQ = 27 cm, RS = 18 cm and &ang;QRS = &ang;QPR. What is the ratio of the perimeter of ∆PRS to that of ∆QSR?</p>",
                    question_hi: "<p>63. त्रिभुज PQR में, RS, PQ को बिंदु S पर प्रतिच्छेदित करती है। त्रिभुज की भुजाएं QR = 36 cm, SQ = 27 cm, RS = 18 cm हैं और &ang;QRS = &ang;QPR है। ∆PRS के परिमाप और ∆QSR के परिमाप का अनुपात कितना है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>63.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799597724.png\" alt=\"rId51\" width=\"136\" height=\"124\"><br>In &Delta;QPR and &Delta;QRS<br>&ang;QPR = &ang;QRS &hellip; (given)<br>&ang;PQR = &ang;SQR &hellip;(common)<br>So, &Delta;QPR &sim; &Delta;QRS by AA similarity<br>Hence, <math display=\"inline\"><mfrac><mrow><mi>Q</mi><mi>P</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>QR</mi><mi>QS</mi></mfrac></math> <br>&rArr; QP = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&#215;</mo><mn>36</mn></mrow><mn>27</mn></mfrac></math> = 48 cm<br>and <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>R</mi></mrow><mrow><mi>R</mi><mi>S</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>QR</mi><mi>QS</mi></mfrac></math><br>&rArr; PR = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&#215;</mo><mn>18</mn></mrow><mn>27</mn></mfrac></math> = 24 cm<br>&rArr; PS = PQ - SQ = 48 - 27 = 21 cm<br>In <math display=\"inline\"><mi>&#916;</mi></math>PRS and &Delta;QSR,<br><math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mo>(</mo><mo>&#9651;</mo><mi>P</mi><mi>R</mi><mi>S</mi><mo>)</mo></mrow><mrow><mi>P</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mo>(</mo><mo>&#9651;</mo><mi>Q</mi><mi>S</mi><mi>R</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>PR</mi><mo>+</mo><mi>RS</mi><mo>+</mo><mi>PS</mi></mrow><mrow><mi>QS</mi><mo>+</mo><mi>SR</mi><mo>+</mo><mi>QR</mi></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>+</mo><mn>18</mn><mo>+</mo><mn>21</mn></mrow><mrow><mn>27</mn><mo>+</mo><mn>18</mn><mo>+</mo><mn>36</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>81</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>9</mn></mfrac></math> or 7 : 9</p>",
                    solution_hi: "<p>63.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799597724.png\" alt=\"rId51\" width=\"136\" height=\"124\"><br>&Delta;QPR और &Delta;QRS में,<br>&ang;QPR = &ang;QRS &hellip; (दिया गया)<br>&ang;PQR = &ang;SQR &hellip; (उभयनिष्ठ)<br>इसलिए, &Delta;QPR &sim; &Delta;QRS, AA नियम के द्वारा <br>अतः <math display=\"inline\"><mfrac><mrow><mi>Q</mi><mi>P</mi></mrow><mrow><mi>Q</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>QR</mi><mi>QS</mi></mfrac></math> <br>&rArr; QP = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&#215;</mo><mn>36</mn></mrow><mn>27</mn></mfrac></math> = 48 cm<br>और <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>R</mi></mrow><mrow><mi>R</mi><mi>S</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>QR</mi><mi>QS</mi></mfrac></math><br>&rArr; PR = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&#215;</mo><mn>18</mn></mrow><mn>27</mn></mfrac></math> = 24 cm<br>&rArr; PS = PQ - SQ = 48 - 27 = 21 cm<br><math display=\"inline\"><mi>&#916;</mi></math>PRS और &Delta;QSR में,<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mo>&#9651;</mo><mi>PRS</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi></mrow><mrow><mo>(</mo><mo>&#9651;</mo><mi>QSR</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>PR</mi><mo>+</mo><mi>RS</mi><mo>+</mo><mi>PS</mi></mrow><mrow><mi>QS</mi><mo>+</mo><mi>SR</mi><mo>+</mo><mi>QR</mi></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>+</mo><mn>18</mn><mo>+</mo><mn>21</mn></mrow><mrow><mn>27</mn><mo>+</mo><mn>18</mn><mo>+</mo><mn>36</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>81</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>9</mn></mfrac></math>या 7 : 9</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. At the beginning of day 1 of a month, Rajesh has 500 eggs. He sells 20% of the eggs by the end of the day and added b% of the eggs at the beginning of the next day and sold 20% of the eggs at the end of the next day. This pattern continued up to the end of the third day of the month when he is left with 1024 eggs. The value of b is equal to:</p>",
                    question_hi: "<p>64. महीने के पहले दिन की शुरुआत में राजेश के पास 500 अंडे थे। वह दिन के अंत तक 20% अंडे बेच देता है और अगले दिन की शुरुआत में उपलब्ध अंडों में b% अंडे और मिला लेता है और अगले दिन के अंत तक 20% अंडे बेच देता है। यह पैटर्न महीने के तीसरे दिन के अंत तक जारी रहता है और तब उसके पास 1024 अंडे बचते है। निम्न में से b का मान किसके बराबर है?</p>",
                    options_en: [
                        "<p>500</p>",
                        "<p>20</p>",
                        "<p>100</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>500</p>",
                        "<p>20</p>",
                        "<p>100</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>64.(c)<br>According to the question,<br>500 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math>&times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 1024<br>4 &times; (100 + b)&nbsp;&times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 1024<br>(100 + b)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1024</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 16 &times; 25 &times; 100<br>100 + b = 4 &times; 5 &times; 10 = 200<br>b = 100</p>",
                    solution_hi: "<p>64.(c)<br>प्रश्न के अनुसार,<br>500 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math>&times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 1024<br>4 &times; (100 + b)&nbsp;&times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 1024<br>(100 + b)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1024</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 16 &times; 25 &times; 100<br>100 + b = 4 &times; 5 &times; 10 = 200<br>b = 100</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. If p cosA = 2q sinA and 2p cosecA - q secA = 3, then the value of p<sup>2</sup> + 4q<sup>2</sup> is:</p>",
                    question_hi: "<p>65. यदि p cosA = 2q sinA और 2p cosecA - q secA = 3 है, तो p<sup>2</sup> + 4q<sup>2</sup> का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>1</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>1</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>65.(b) <strong>Given:</strong> p cosA = 2q sinA <br>&rArr; p = 2q &times; <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math>&hellip;&hellip;(i)<br>2p cosecA - q secA = 3<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">p</mi></mrow><mi>sinA</mi></mfrac><mo>&#160;</mo></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">q</mi><mi>cosA</mi></mfrac></math> = 3<br>&rArr; 2p &times; cosA - q &times; sinA = 3 &times; sinA.cosA<br>&rArr; 2 &times; 2q &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math> &times; cosA - q &times; sinA = 3 &times; sinA.cosA [from eq . (i)]<br>&rArr; 4q &times; sinA - q &times; sinA = 3 &times; sinA.cosA<br>&rArr; 3q &times; sinA = 3 &times; sinA.cosA<br>&rArr; q = cosA <br>Put the value of q in eq . (i)<br>&rArr; p = 2 &times; cosA &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math><br>&rArr; p = 2sinA<br>Now,<br>p<sup>2</sup> + 4q<sup>2</sup><br>= (2sinA)<sup>2</sup> + 4(cosA)<sup>2</sup><br>= 4(sin<sup>2</sup>A + cos<sup>2</sup>A)<br>= 4</p>",
                    solution_hi: "<p>65.(b) दिया गया है : p cosA = 2q sinA <br>&rArr; p = 2q &times; <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math>&hellip;&hellip;(i)<br>2p cosecA - q secA = 3<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">p</mi></mrow><mi>sinA</mi></mfrac><mo>&#160;</mo></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">q</mi><mi>cosA</mi></mfrac></math> = 3<br>&rArr; 2p &times; cosA - q &times; sinA = 3 &times; sinA.cosA<br>&rArr; 2 &times; 2q &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math> &times; cosA - q &times; sinA = 3 &times; sinA.cosA&nbsp; &nbsp; <strong>&nbsp;</strong>[समीकरण&nbsp; (i) से ]<br>&rArr; 4q &times; sinA - q &times; sinA = 3 &times; sinA.cosA<br>&rArr; 3q &times; sinA = 3 &times; sinA.cosA<br>&rArr; q = cosA<br>समीकरण (i) मे q का मान रखने पर <br>&rArr; p = 2 &times; cosA &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math><br>&rArr; p = 2sinA<br>Now,<br>p<sup>2</sup> + 4q<sup>2</sup><br>= (2sinA)<sup>2</sup> + 4(cosA)<sup>2</sup><br>= 4(sin<sup>2</sup>A + cos<sup>2</sup>A)<br>= 4</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If two numbers are each divided by the same divisor, then the remainders are 6 and 7, respectively. If the sum of the two numbers be divided by the same divisor, then the remainder is 5. The divisor is:</p>",
                    question_hi: "<p>66. यदि दो संख्याओं में से प्रत्येक को एक ही भाजक से विभाजित किया जाता है, तो शेषफल क्रमशः 6 और 7 होते हैं। यदि उन दो संख्याओं के योग को उसी भाजक से विभाजित किया जाए, तो शेषफल 5 प्राप्त होता है। भाजक कितना है?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>13</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>13</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>66.(d)<br>Let the numbers be a and b and the divisor be d. The remainders when a and b are divided by d are 6 and 7 respectively. Thus,<br>a = dq<sub>1</sub>&nbsp;+ 6<br>b = dq<sub>2</sub>&nbsp;+ 7<br>For their sum<br>a + b = d(q<sub>1</sub>&nbsp;+ q<sub>2</sub>) + 13 &hellip; (i)<br>When a + b is divided by d, the remainder is 5,<br>a + b = dq + 5 &hellip; (ii)<br>Equating (i) and (ii) we get,<br>d(q<sub>1</sub>&nbsp;+ q<sub>2</sub>) + 13 = dq + 5<br>d(q<sub>1</sub>&nbsp;+ q<sub>2 </sub>- q) = - 8<br>Thus, d must divide 8. Testing values, we find that d = 8 satisfies the condition.<br>Therefore, the divisor is 8</p>",
                    solution_hi: "<p>66.(d)<br>माना संख्याएँ a और b हैं और भाजक d है। जब a और b को d से विभाजित किया जाता है तो शेषफल क्रमशः 6 और 7 होते हैं। इस प्रकार,<br>a = dq<sub>1</sub>&nbsp;+ 6<br>b = dq<sub>2</sub>&nbsp;+ 7<br>उनके योग के लिए<br>a + b = d(q<sub>1</sub>&nbsp;+ q<sub>2</sub>) + 13&hellip; (i)<br>जब a + b को d से विभाजित किया जाता है, तो शेषफल 5 होता है,<br>a + b = dq + 5 &hellip; (ii)<br>(i) और (ii) की तुलना करने पर,<br>d(q<sub>1</sub>&nbsp;+ q<sub>2</sub>) + 13 = dq + 5<br>d(q<sub>1</sub>&nbsp;+ q<sub>2 </sub>- q) = - 8<br>इस प्रकार, 8, d से विभाजित होगा। मानों का परीक्षण करने पर, हम पाते हैं कि d = 8 शर्त को पूरा करता है।<br>अत: भाजक 8 है ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A shopkeeper marks an article at ₹150 and sells it at a discount of 25%. He also gives a gift worth ₹2.5. If he still makes a 10% profit, then the cost price (in ₹) of the article is:</p>",
                    question_hi: "<p>67. एक दुकानदार एक वस्तु पर ₹150 मूल्य अंकित करता है और उसे 25% की छूट पर बेचता है। साथ ही वह वस्तु की खरीद पर ₹2.5 का गिफ्ट भी देता है। यदि वह फिर भी 10% का लाभ अर्जित करता है, तो वस्तु का क्रय मूल्य (₹ में) क्या है?</p>",
                    options_en: [
                        "<p>175</p>",
                        "<p>100</p>",
                        "<p>125</p>",
                        "<p>112</p>"
                    ],
                    options_hi: [
                        "<p>175</p>",
                        "<p>100</p>",
                        "<p>125</p>",
                        "<p>112</p>"
                    ],
                    solution_en: "<p>67.(b) <br>According to the question,<br>SP of the article = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - 2.5 = ₹ 110 <br>CP of the article = 110 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> = ₹ 100</p>",
                    solution_hi: "<p>67.(b) <br>प्रश्न के अनुसार,<br>वस्तु का विक्रय मूल्य = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - 2.5 = ₹ 110 <br>वस्तु का क्रय मूल्य = 110 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> = ₹ 100</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. The average salary of a group of 12 employees in an institution is ₹3,950 per month&nbsp;and that of another group of employees is ₹1,850. If the average salary of all employees is ₹2,150, then the total number of employees is:</p>",
                    question_hi: "<p>68. एक संस्थान में 12 कर्मचारियों के समूह का औसत वेतन ₹3,950 प्रति माह और कर्मचारियों के एक दूसरे समूह का औसत वेतन ₹1,850 प्रति माह है। यदि सभी कर्मचारियों का औसत वेतन ₹2,150 है, तो कर्मचारियों की कुल संख्या ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>84</p>",
                        "<p>100</p>",
                        "<p>88</p>",
                        "<p>72</p>"
                    ],
                    options_hi: [
                        "<p>84</p>",
                        "<p>100</p>",
                        "<p>88</p>",
                        "<p>72</p>"
                    ],
                    solution_en: "<p>68.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799597968.png\" alt=\"rId52\" width=\"177\" height=\"146\"><br><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math><br>&rArr; x = 72<br>Total employees = (x&nbsp;+ 12) = 84</p>",
                    solution_hi: "<p>68.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799598218.png\" alt=\"rId53\" width=\"179\" height=\"145\"><br><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> <br>&rArr; x = 72<br>कुल कर्मचारी = (x&nbsp;+ 12) = 84</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Express the following into a vulgar fraction.<br>0.1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>4</mn><mo>&#175;</mo></mover></math> + 0.2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>3</mn><mo>&#175;</mo></mover></math> + 2</p>",
                    question_hi: "<p>69. निम्नलिखित को साधारण भिन्न में व्यक्त करें।<br>0.1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>4</mn><mo>&#175;</mo></mover></math> + 0.2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>3</mn><mo>&#175;</mo></mover></math> + 2</p>",
                    options_en: [
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>34</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>34</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>69.(d)<br>0.1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>4</mn><mo>&#175;</mo></mover></math> + 0.2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>3</mn><mo>&#175;</mo></mover></math> + 2<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14</mn><mo>-</mo><mn>1</mn></mrow><mn>90</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>23</mn><mo>-</mo><mn>2</mn></mrow><mn>90</mn></mfrac></math> + 2 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>90</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>90</mn></mfrac></math> + 2<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>+</mo><mn>21</mn><mo>+</mo><mn>180</mn></mrow><mn>90</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>214</mn><mn>90</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>90</mn></mfrac></math></p>",
                    solution_hi: "<p>69.(d)<br>0.1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>4</mn><mo>&#175;</mo></mover></math> + 0.2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>3</mn><mo>&#175;</mo></mover></math> + 2<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14</mn><mo>-</mo><mn>1</mn></mrow><mn>90</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>23</mn><mo>-</mo><mn>2</mn></mrow><mn>90</mn></mfrac></math> + 2 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>90</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>90</mn></mfrac></math> + 2<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>13</mn><mo>+</mo><mn>21</mn><mo>+</mo><mn>180</mn></mrow><mn>90</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>214</mn><mn>90</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>90</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Study the given table and answer the question that follows. <br>The table shows loans disbursed (in crores) by four banks in different years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799598547.png\" alt=\"rId54\" width=\"293\" height=\"128\"> <br>What was the percentage increase of disbursement of loans of all banks together from 1994 to 1995?</p>",
                    question_hi: "<p>70. दी गई तालिका का अध्ययन कीजिए और प्रश्न का उत्तर दीजिए।<br>तालिका, विभिन्न वर्षों में चार बैंकों द्वारा संवितरित ऋण (₹ करोड़ में) को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740799598715.png\" alt=\"rId55\" width=\"241\" height=\"129\"> <br>1994 से 1995 तक सभी बैंकों को मिलाकर उनके ऋण संवितरण में कितने प्रतिशत की वृद्धि हुई थी?</p>",
                    options_en: [
                        "<p>7%</p>",
                        "<p>8%</p>",
                        "<p>5%</p>",
                        "<p>10%</p>"
                    ],
                    options_hi: [
                        "<p>7%</p>",
                        "<p>8%</p>",
                        "<p>5%</p>",
                        "<p>10%</p>"
                    ],
                    solution_en: "<p>70.(c)<br>Total amount of loan in 1994 = 120 cr.<br>Total amount of loan in 1995 = 126 cr.<br>% increase = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>126</mn><mo>-</mo><mn>120</mn></mrow><mn>120</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>12</mn></mfrac></math> = 5%</p>",
                    solution_hi: "<p>70.(c)<br>1994 में ऋण की कुल राशि = 120 करोड़<br>1995 में ऋण की कुल राशि = 126 करोड़<br>% वृद्धि =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>126</mn><mo>-</mo><mn>120</mn></mrow><mn>120</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>12</mn></mfrac></math> = 5%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. The least number which must be subtracted from 7278745 so as to obtain a sum divisible by 11 is:</p>",
                    question_hi: "<p>71. 11 से विभाज्य संख्या प्राप्त करने के लिए 7278745 में से घटाई जाने वाली छोटी से छोटी संख्या कौन-सी है?</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>5</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>5</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>71.(b) Divisibility of 11 : if the difference between the sum of the digits at odd places and the sum of the digits at even places is 0 or multiple of 11<br>7278745<br>&rArr; (7 + 7 + 7 + 5) - (2 + 8 + 4)&nbsp;<br>&rArr; 26 - 14 = 12 <br>It is clear that if 1 is subtracted from this number (786452), then this number will become divisible by 11.</p>",
                    solution_hi: "<p>71.(b) 11 की विभाज्यता: यदि विषम स्थानों पर अंकों के योग और सम स्थानों पर अंकों के योग के बीच का अंतर 0 या 11 का गुणक है<br>7278745<br>&rArr; (7 + 7 + 7 + 5) - (2 + 8 + 4)&nbsp;<br>&rArr; 26 - 14 = 12<br>स्पष्ट है कि यदि इस संख्या (786452) में से 1 घटा दिया जाये तो यह संख्या 11 से विभाज्य हो जायेगी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The volume of the rectangular block is 12288 m<sup>3</sup>. Its dimensions are in the ratio of 4 : 3: 2. If the entire surface is polished at the rate 2 paise per m<sup>2</sup>,then find the total cost of polishing :</p>",
                    question_hi: "<p>72. एक आयताकार खंड का आयतन 12288 m<sup>3</sup> है। इसके आयाम 4 : 3 : 2 के अनुपात में है। यदि संपूर्ण पृष्ठ को 2 पैसे/m<sup>2</sup> की दर से पॉलिश किया जाए, तो पॉलिश करने की कुल लागत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>₹44.42</p>",
                        "<p>₹11.14</p>",
                        "<p>₹66.56</p>",
                        "<p>₹33.28</p>"
                    ],
                    options_hi: [
                        "<p>₹44.42</p>",
                        "<p>₹11.14</p>",
                        "<p>₹66.56</p>",
                        "<p>₹33.28</p>"
                    ],
                    solution_en: "<p>72.(c)<br>Length = 4x, Breadth = 3x, Height = 2x<br>Volume = 12288<br>l &times; b &times; h = 12288<br>4x &times; 3x &times; 2x = 12288<br>24x<sup>3</sup>&nbsp;= 12288<br>x<sup>3</sup> = 512<br>x = 8 <br>Hence,<br>Length = 4x&nbsp;= 4 &times; 8 = 32 m<br>Breadth = 3x&nbsp;= 3 &times; 8 = 24 m <br>Height = 2x&nbsp;= 2 &times; 8 = 16 m<br>Total surface area of box = 2(lb&nbsp;+ bh + hl)<br>= 2(4x &times; 3x + 3x &times; 2x + 2x &times; 4x)<br>= 2(12x<sup>2</sup> + 6x<sup>2</sup> + 8x<sup>2</sup>)<br>= 2 (26x<sup>2</sup>)<br>= 52 &times; 8<sup>2</sup><br>= 52 &times; 64<br>= 3328 m<sup>2</sup><br>Total cost of polishing = 2 &times; 3328<br>= 6656 paise or Rs. 66.56</p>",
                    solution_hi: "<p>72.(c)<br>लंबाई = 4x, चौड़ाई = 3x, ऊंचाई = 2x<br>आयतन = 12288<br>l &times; b &times; h = 12288<br>4x &times; 3x &times; 2x = 12288<br>24x<sup>3</sup>&nbsp;= 12288<br>x<sup>3</sup> = 512<br>x = 8<br>इस तरह,<br>लंबाई = 4x&nbsp;= 4 &times; 8 = 32 m<br>चौड़ाई = 3x&nbsp;= 3 &times; 8 = 24 m <br>ऊंचाई = 2x&nbsp;= 2 &times; 8 = 16 m<br>डिब्बे का कुल पृष्ठीय क्षेत्रफल = 2(lb&nbsp;+ bh + hl)<br>= 2(4x &times; 3x + 3x &times; 2x + 2x &times; 4x)<br>= 2(12x<sup>2</sup> + 6x<sup>2</sup> + 8x<sup>2</sup>)<br>= 2 (26x<sup>2</sup>)<br>= 52 &times; 8<sup>2</sup><br>= 52 &times; 64<br>= 3328 m<sup>2</sup><br>पॉलिश करने की कुल लागत = 2 &times; 3328<br>= 6656 पैसे या रु. Rs. 66.56</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A policeman received information that a thief is at a distance of 1.5 km from him. The thief starts moving by car and the policeman chases him by car. The thief and the policeman are moving at the speeds of 90 km/h and 120 km/h respectively. At what distance (in km) will the police catch the thief?</p>",
                    question_hi: "<p>73. एक पुलिसकर्मी को सूचना मिली कि एक चोर उससे 1.5 km की दूरी पर है। चोर कार से चलना शुरू करता है और पुलिसकर्मी कार से उसका पीछा करता है। चोर और पुलिसकर्मी क्रमशः 90 km/h और 120 km/h की चाल से आगे बढ़ रहे हैं। पुलिसकर्मी किस दूरी (km में) पर चोर को पकड़ लेगा?</p>",
                    options_en: [
                        "<p>45.0 km</p>",
                        "<p>4.5 km</p>",
                        "<p>6 km</p>",
                        "<p>5 km</p>"
                    ],
                    options_hi: [
                        "<p>45.0 km</p>",
                        "<p>4.5 km</p>",
                        "<p>6 km</p>",
                        "<p>5 km</p>"
                    ],
                    solution_en: "<p>73.(c) <br>Relative speed = 120 - 90 = 30 km/h<br>Time taken to caught thief = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> hour<br>1 hour = 120 km<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> hours = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>1</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 6 km</p>",
                    solution_hi: "<p>73.(c) <br>सापेक्ष गति = 120 - 90 = 30 km/h<br>चोर को पकड़ने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> घंटा<br>1 घंटा = 120 km<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> घंटा = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>1</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 6 km</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn><mi>cos&#952;</mi></mrow><mrow><mn>9</mn><mi>sin&#952;</mi><mo>-</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mn>5</mn></mrow><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> is :</p>",
                    question_hi: "<p>74. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn><mi>cos&#952;</mi></mrow><mrow><mn>9</mn><mi>sin&#952;</mi><mo>-</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mn>5</mn></mrow><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> का मान ____________ है।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2121</mn></mrow><mrow><mn>769</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>2121</mn></mrow><mrow><mn>769</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>769</mn></mrow><mrow><mn>2121</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>769</mn></mrow><mrow><mn>2121</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2121</mn></mrow><mrow><mn>769</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>2121</mn></mrow><mrow><mn>769</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>769</mn></mrow><mrow><mn>2121</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>769</mn></mrow><mrow><mn>2121</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>74.(b) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn><mi>cos&#952;</mi></mrow><mrow><mn>9</mn><mi>sin&#952;</mi><mo>-</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>28sin&theta; + 16cos&theta; = 45sin&theta; - 10cos&theta;<br>17sin&theta; = 26cos&theta;<br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>17</mn></mfrac></math><br>tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>17</mn></mfrac></math><br>Now, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mn>5</mn></mrow><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><mn>26</mn><mn>17</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>5</mn></mrow><mrow><msup><mrow><mo>(</mo><mfrac><mn>26</mn><mn>17</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>5</mn><mo>&#160;</mo></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>676</mn></mrow><mrow><mn>289</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mfrac><mrow><mn>676</mn></mrow><mrow><mn>289</mn></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mn>676</mn><mo>+</mo><mn>1445</mn></mrow><mn>289</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mrow><mn>676</mn><mo>-</mo><mn>1445</mn></mrow><mn>289</mn></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>2121</mn><mn>289</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mrow><mo>-</mo><mn>769</mn></mrow><mn>289</mn></mfrac></mstyle></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>2121</mn></mrow><mrow><mn>289</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>289</mn><mrow><mo>-</mo><mn>769</mn></mrow></mfrac></math> = -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2121</mn><mn>769</mn></mfrac></math></p>",
                    solution_hi: "<p>74.(b) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn><mi>cos&#952;</mi></mrow><mrow><mn>9</mn><mi>sin&#952;</mi><mo>-</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>28sin&theta; + 16cos&theta; = 45sin&theta; - 10cos&theta;<br>17sin&theta; = 26cos&theta;<br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>17</mn></mfrac></math><br>tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>17</mn></mfrac></math><br>अब , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mn>5</mn></mrow><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><mn>26</mn><mn>17</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mn>5</mn></mrow><mrow><msup><mrow><mo>(</mo><mfrac><mn>26</mn><mn>17</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>5</mn><mo>&#160;</mo></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>676</mn></mrow><mrow><mn>289</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mfrac><mrow><mn>676</mn></mrow><mrow><mn>289</mn></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mn>676</mn><mo>+</mo><mn>1445</mn></mrow><mn>289</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mrow><mn>676</mn><mo>-</mo><mn>1445</mn></mrow><mn>289</mn></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>2121</mn><mn>289</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mrow><mo>-</mo><mn>769</mn></mrow><mn>289</mn></mfrac></mstyle></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>2121</mn></mrow><mrow><mn>289</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>289</mn><mrow><mo>-</mo><mn>769</mn></mrow></mfrac></math> = -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2121</mn><mn>769</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. During the sale, Raghav bought a notebook marked for ₹50 at 78% discount and a pen marked for ₹100 at 8% discount. How much (in ₹) did he save due to sale?</p>",
                    question_hi: "<p>75. सेल के दौरान, राघव ने ₹50 अंकित मूल्य की एक नोटबुक को 78% छूट पर और ₹100 अंकित मूल्य की एक पेन को 8% की छूट पर खरीदा। सेल के कारण उसने कितनी बचत (₹ में) की?</p>",
                    options_en: [
                        "<p>50</p>",
                        "<p>44</p>",
                        "<p>45</p>",
                        "<p>47</p>"
                    ],
                    options_hi: [
                        "<p>50</p>",
                        "<p>44</p>",
                        "<p>45</p>",
                        "<p>47</p>"
                    ],
                    solution_en: "<p>75.(d) 78% = <math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math>, 8% = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>Saving on Notebook = <math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 50 = ₹39<br>Saving on Pen = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = ₹8<br>Total saving = 39 + 8 = ₹47</p>",
                    solution_hi: "<p>75.(d) 78% = <math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math>, 8% = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>नोटबुक पर बचत = <math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 50 = ₹39<br>पेन पर बचत = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = ₹8<br>कुल बचत = 39 + 8 = ₹47</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence, which contains the grammatical error.<br>Headmaster has instructed the teachers to follow the rules of the school.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence, which contains the grammatical error.<br>Headmaster has instructed the teachers to follow the rules of the school.</p>",
                    options_en: [
                        "<p>Headmaster</p>",
                        "<p>has instructed</p>",
                        "<p>the teachers to follow the rules of the school</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>Headmaster</p>",
                        "<p>has instructed</p>",
                        "<p>the teachers to follow the rules of the school</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>76.(a) Headmaster<br>The &lsquo;headmaster&rsquo; mentioned in the given sentence is specific and we generally use the definite article &lsquo;the&rsquo; before any specific or particular noun. Hence, &lsquo;The headmaster&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(a) Headmaster<br>दिए गए वाक्य में उल्लिखित &lsquo;headmaster&rsquo; specific है और, हम आम तौर पर किसी specific or particular noun के पहले definite article &lsquo;the&rsquo; उपयोग करते हैं अतः, &lsquo;The headmaster&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the word which means the same as the group of words given<br>The art of good eating</p>",
                    question_hi: "<p>77. Select the word which means the same as the group of words given<br>The art of good eating</p>",
                    options_en: [
                        "<p>gastronomy</p>",
                        "<p>astronomy</p>",
                        "<p>vegetarianism</p>",
                        "<p>gourmet</p>"
                    ],
                    options_hi: [
                        "<p>gastronomy</p>",
                        "<p>astronomy</p>",
                        "<p>vegetarianism</p>",
                        "<p>gourmet</p>"
                    ],
                    solution_en: "<p>77.(a) <strong>Gastronomy</strong> - the art or science of good eating.<br><strong>Astronomy -</strong> the science that deals with the material universe beyond the earth\'s atmosphere.<br><strong>Vegetarianism - </strong>The practice of consuming vegetables, fruits, nuts, grain etc.<br><strong>Gourmet -</strong> a connoisseur of fine food and drink; epicure.</p>",
                    solution_hi: "<p>77.(a) <strong>Gastronomy -</strong> अच्छे खाने की कला या विज्ञान।<br><strong>Astronomy -</strong>विज्ञान जो पृथ्वी के वायुमंडल से परे भौतिक ब्रह्मांड से संबंधित है।<br><strong>Vegetarianism - </strong>शाकाहार।<br><strong>Gourmet </strong>- बढ़िया खाने-पीने का पारखी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate ANTONYM of the given word.<br>Lethargic</p>",
                    question_hi: "<p>78. Select the most appropriate ANTONYM of the given word.<br>Lethargic</p>",
                    options_en: [
                        "<p>Intelligent</p>",
                        "<p>Active</p>",
                        "<p>Cute</p>",
                        "<p>Exhausted</p>"
                    ],
                    options_hi: [
                        "<p>Intelligent</p>",
                        "<p>Active</p>",
                        "<p>Cute</p>",
                        "<p>Exhausted</p>"
                    ],
                    solution_en: "<p>78.(b) <strong>Active -</strong> engaging or ready to engage in physically energetic pursuits.<br><strong>Lethargic - </strong>lacking energy or enthusiasm.<br><strong>Intelligent - </strong>having the ability to acquire and apply knowledge and skills.<br><strong>Cute - </strong>attractive in a pretty or endearing way.<br><strong>Exhausted - </strong>extremely tired or worn out.</p>",
                    solution_hi: "<p>78.(b) <strong>Active</strong> (सक्रिय) - engaging or ready to engage in physically energetic pursuits.<br><strong>Lethargic</strong> (सुस्त) - lacking energy or enthusiasm.<br><strong>Intelligent </strong>(बुद्धिमान) - having the ability to acquire and apply knowledge and skills.<br><strong>Cute</strong> (प्यारा) - attractive in a pretty or endearing way.<br><strong>Exhausted </strong>(थका हुआ) - extremely tired or worn out.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Given below are four jumbled sentences. Select the option that gives their correct logical sequence.<br>A. However, the most consumed tea in the world is Indian tea.<br>B. Tea was originated in China by its ruler, Shennong.<br>C. India also has the credit of being the world\'s largest producer.<br>D. This is because of the tropical and sub-tropical climate present here.</p>",
                    question_hi: "<p>79. Given below are four jumbled sentences. Select the option that gives their correct logical sequence.<br>A. However, the most consumed tea in the world is Indian tea.<br>B. Tea was originated in China by its ruler, Shennong.<br>C. India also has the credit of being the world\'s largest producer.<br>D. This is because of the tropical and sub-tropical climate present here.</p>",
                    options_en: [
                        "<p>BACD</p>",
                        "<p>CBAD</p>",
                        "<p>DCAB</p>",
                        "<p>ABCD</p>"
                    ],
                    options_hi: [
                        "<p>BACD</p>",
                        "<p>CBAD</p>",
                        "<p>DCAB</p>",
                        "<p>ABCD</p>"
                    ],
                    solution_en: "<p>79.(a) BACD<br>Sentence B will be the starting line as it introduces the main idea of the parajumble, i.e. &lsquo;Origin of Tea in China&rsquo;. And, Sentence A states a contrasting idea that most consumed tea in the world is Indian tea. So, A will follow B. Further, Sentence C states that India also has the credit of being the world&rsquo;s largest producer &amp; Sentence D states that the reason for India being the largest producer. So, D will follow C. Going through the options, option (a) has the correct sequence.</p>",
                    solution_hi: "<p>79.(a) BACD<br>Sentence B आरंभिक line होगी क्योंकि यह parajumble के मुख्य विचार, यानी &lsquo;Origin of Tea in China&rsquo; को प्रस्तुत करता है। और, Sentence A एक विपरीत विचार बताता है कि दुनिया में सबसे अधिक खपत की जाने वाली चाय भारतीय चाय है। इसलिए, B के बाद A आयेगा। इसके अलावा, Sentence C बताता है कि भारत को दुनिया का सबसे बड़ा उत्पादक होने का श्रेय भी प्राप्त है और Sentence D बताता है कि भारत के सबसे बड़े उत्पादक होने का कारण क्या है। इसलिए, C के बाद D आयेगा। विकल्पों को देखते हुए, option (a) में सही क्रम है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select \'No substitution required\'.<br>Whether she wishes to relocate to Mumbai or continues to live here <span style=\"text-decoration: underline;\">are her personal decisions</span>.</p>",
                    question_hi: "<p>80. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select \'No substitution required\'.<br>Whether she wishes to relocate to Mumbai or continues to live here <span style=\"text-decoration: underline;\">are her personal decisions</span>.</p>",
                    options_en: [
                        "<p>is her personal decision</p>",
                        "<p>were her personal decisions</p>",
                        "<p>No substitution required</p>",
                        "<p>have her personal decision</p>"
                    ],
                    options_hi: [
                        "<p>is her personal decision</p>",
                        "<p>were her personal decisions</p>",
                        "<p>No substitution required</p>",
                        "<p>have her personal decision</p>"
                    ],
                    solution_en: "<p>80.(a) is her personal decision<br>&lsquo;Whether &hellip; or&rsquo; is a pair of conjunctions. According to the &ldquo;Rule of Proximity&rdquo;, the verb is supplied according to the subject closest to the verb. In the given sentence, the close subject is a verb phrase &lsquo;continues to live&rsquo;, which denotes a single action. Therefore, singular verb &lsquo;is&rsquo; will be used. Hence, &lsquo;is her personal decision&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>80.(a) is her personal decision<br>&lsquo;Whether &hellip; or&rsquo; conjunctions का सही pair है। &ldquo;Rule of Proximity&rdquo; के अनुसार, verb को verb के सबसे निकट के subject के अनुसार आपूर्ति की जाती है। दिए गए sentence में, निकट subject एक phrase &lsquo;continues to live&rsquo; है जो single action को दर्शाता है। इसलिए, singular verb &lsquo;is&rsquo; का उपयोग किया जाएगा। इसलिए, &lsquo;is her personal decision&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate option to fill in the blank. <br>Very carefully I ______ the tree I have referred to, and avoiding contact with the creepers, the upper _______ and leaves of which might have been visible from where the tiger was lying, I climbed to the fork, where I had a comfortable seat and perfect _________.</p>",
                    question_hi: "<p>81. Select the most appropriate option to fill in the blank. <br>Very carefully I ______ the tree I have referred to, and avoiding contact with the creepers, the upper _______ and leaves of which might have been visible from where the tiger was lying, I climbed to the fork, where I had a comfortable seat and perfect _________.</p>",
                    options_en: [
                        "<p>stalled, roots, visibility</p>",
                        "<p>starved, stem, disclosure</p>",
                        "<p>stormed, fruits, revelation</p>",
                        "<p>stalked, tendrils, concealment</p>"
                    ],
                    options_hi: [
                        "<p>stalled, roots, visibility</p>",
                        "<p>starved, stem, disclosure</p>",
                        "<p>stormed, fruits, revelation</p>",
                        "<p>stalked, tendrils, concealmen</p>"
                    ],
                    solution_en: "<p>81.(d) The words written in option d will perfectly fit in the blanks according to the grammatical context of the sentence. <br><strong>Stalk -</strong> to follow someone<br><strong>Concealment </strong>- hide<br><strong>Tendrils - </strong>a long thin part that grows from a climbing plant</p>",
                    solution_hi: "<p>81.(d) Option (d) में लिखे गए शब्द sentence के grammatical संदर्भ के अनुसार रिक्त स्थान में पूरी तरह से फिट होंगे।<br><strong>Stalk -</strong> पीछा करना<br><strong>Concealment -</strong> आड़<br><strong>Tendrils - </strong>लता का वह हिस्सा जिसके सहारे वह दीवार आदि पर चढ़ती है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the most appropriate meaning of the idiom given in the following Question<br>Blowing your own trumpet</p>",
                    question_hi: "<p>82. Select the most appropriate meaning of the idiom given in the following Question<br>Blowing your own trumpet</p>",
                    options_en: [
                        "<p>playing your own trumpet to produce music</p>",
                        "<p>making too much noise</p>",
                        "<p>praising your own abilities and achievements</p>",
                        "<p>None of these</p>"
                    ],
                    options_hi: [
                        "<p>playing your own trumpet to produce music</p>",
                        "<p>making too much noise</p>",
                        "<p>praising your own abilities and achievements</p>",
                        "<p>None of these</p>"
                    ],
                    solution_en: "<p>82.(c) praising your own abilities and achievements. <br>Example- I don\'t know why Ram has put an end to blowing his own trumpet</p>",
                    solution_hi: "<p>82.(c) praising your own abilities and achievements./ अपनी क्षमताओं और उपलब्धियों की प्रशंसा करना <br>उदाहरण - I don\'t know when Ram will put an end to blowing his own trumpet./ मुझे नहीं पता कि राम कब अपनी क्षमताओं और उपलब्धियों की प्रशंसा करना बंद करेगा ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate synonym of the given word. <br>Destitute</p>",
                    question_hi: "<p>83. Select the most appropriate synonym of the given word. <br>Destitute</p>",
                    options_en: [
                        "<p>Broke</p>",
                        "<p>Corrupt</p>",
                        "<p>Broken</p>",
                        "<p>Firm</p>"
                    ],
                    options_hi: [
                        "<p>Broke</p>",
                        "<p>Corrupt</p>",
                        "<p>Broken</p>",
                        "<p>Firm</p>"
                    ],
                    solution_en: "<p>83.(a) <strong>Broke-</strong> having no money.<br><strong>Destitute-</strong> extremely poor and lacking basic necessities.<br><strong>Corrupt-</strong> acting dishonestly for personal gain.<br><strong>Broken-</strong> damaged or not functioning.<br><strong>Firm-</strong> solid, strong, or unyielding.</p>",
                    solution_hi: "<p>83.(a)<strong> Broke</strong> (कड़का) - having no money.<br><strong>Destitute </strong>(निःसहाय) - extremely poor and lacking basic necessities.<br><strong>Corrupt </strong>(भ्रष्ट) - acting dishonestly for personal gain.<br><strong>Broken</strong> (टूटा हुआ) - damaged or not functioning.<br><strong>Firm </strong>(अटल) - solid, strong, or unyielding.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the option that expresses the given sentence in active voice.<br>My request should be considered.</p>",
                    question_hi: "<p>84. Select the option that expresses the given sentence in active voice.<br>My request should be considered.</p>",
                    options_en: [
                        "<p>Please have consider my request.</p>",
                        "<p>Please do consider mine request.</p>",
                        "<p>Consider my request.</p>",
                        "<p>Please consider my request.</p>"
                    ],
                    options_hi: [
                        "<p>Please have consider my request.</p>",
                        "<p>Please do consider mine request.</p>",
                        "<p>Consider my request.</p>",
                        "<p>Please consider my request.</p>"
                    ],
                    solution_en: "<p>84.(d) Please consider my request.(Correct)<br>(a) Please <span style=\"text-decoration: underline;\">have</span> consider my request.(Incorrect Verb)<br>(b) Please <span style=\"text-decoration: underline;\">do</span> consider <span style=\"text-decoration: underline;\">mine</span> request.(Incorrect Verb &amp; Incorrect Pronoun)<br>(c) Consider my request.(&lsquo;Please&rsquo; is missing)</p>",
                    solution_hi: "<p>84.(d) Please consider my request. (Correct)<br>(a) Please <span style=\"text-decoration: underline;\">have</span> consider my request. (गलत Verb)<br>(b) Please <span style=\"text-decoration: underline;\">do</span> consider <span style=\"text-decoration: underline;\">mine</span> request. (Verb और Pronoun गलत है)<br>(c) Consider my request. (&lsquo;Please&rsquo; गायब है)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate synonym of the underlined word in the given sentence.<br>The environmental organisation issued a statement to <span style=\"text-decoration: underline;\">condemn</span> the deforestation happening in the protected area.</p>",
                    question_hi: "<p>85. Select the most appropriate synonym of the underlined word in the given sentence.<br>The environmental organisation issued a statement to <span style=\"text-decoration: underline;\">condemn</span> the deforestation happening in the protected area.</p>",
                    options_en: [
                        "<p>Persuade</p>",
                        "<p>Censure</p>",
                        "<p>Compel</p>",
                        "<p>Foster</p>"
                    ],
                    options_hi: [
                        "<p>Persuade</p>",
                        "<p>Censure</p>",
                        "<p>Compel</p>",
                        "<p>Foster</p>"
                    ],
                    solution_en: "<p>85.(b) <strong>Censure- </strong>to express severe disapproval of something.<br><strong>Condemn-</strong> to strongly disapprove or criticize something.<br><strong>Persuade-</strong> to convince someone to do something.<br><strong>Compel-</strong> to force someone to do something.<br><strong>Foster-</strong> to encourage or promote the development of something.</p>",
                    solution_hi: "<p>85.(b) <strong>Censure </strong>(तिरस्कार करना) - to express severe disapproval of something.<br><strong>Condemn </strong>(निंदा करना) - to strongly disapprove or criticize something.<br><strong>Persuade </strong>(मनाना) - to convince someone to do something.<br><strong>Compel </strong>(विवश) - to force someone to do something.<br><strong>Foster</strong> (प्रोत्साहित करना) - to encourage or promote the development of something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that expresses the given sentence in passive voice.<br>Granny had given Uncle Ken a good lecture on how to be a responsible adult.</p>",
                    question_hi: "<p>86. Select the option that expresses the given sentence in passive voice.<br>Granny had given Uncle Ken a good lecture on how to be a responsible adult.</p>",
                    options_en: [
                        "<p>Uncle Ken was giving a good lecture to Granny on how to be a responsible adult.</p>",
                        "<p>Granny was given a good lecture by Uncle Ken on how to be a responsible adult.</p>",
                        "<p>Uncle Ken was being given a good lecture by Granny on how to be a responsible adult.</p>",
                        "<p>Uncle Ken had been given a good lecture by Granny on how to be a responsible adult.</p>"
                    ],
                    options_hi: [
                        "<p>Uncle Ken was giving a good lecture to Granny on how to be a responsible adult.</p>",
                        "<p>Granny was given a good lecture by Uncle Ken on how to be a responsible adult.</p>",
                        "<p>Uncle Ken was being given a good lecture by Granny on how to be a responsible adult.</p>",
                        "<p>Uncle Ken had been given a good lecture by Granny on how to be a responsible adult.</p>"
                    ],
                    solution_en: "<p>86.(d) Uncle Ken had been given a good lecture by Granny on how to be a responsible adult. (Correct)<br>(a) Uncle Ken <span style=\"text-decoration: underline;\">was giving</span> a good lecture to Granny on how to be a responsible adult. (Incorrect tense)<br>(b) Granny was given a good lecture by Uncle Ken on how to be a responsible adult. (Meaning of sentence changed)<br>(c) Uncle Ken <span style=\"text-decoration: underline;\">was being</span> given a good lecture by Granny on how to be a responsible adult. (Incorrect Tense)</p>",
                    solution_hi: "<p>86.(d) Uncle Ken had been given a good lecture by Granny on how to be a responsible adult. (Correct)<br>(a) Uncle Ken <span style=\"text-decoration: underline;\">was giving</span> a good lecture to Granny on how to be a responsible adult. (गलत tense)<br>(b) Granny was given a good lecture by Uncle Ken on how to be a responsible adult. (Sentence का अर्थ बदल गया)<br>(c) Uncle Ken <span style=\"text-decoration: underline;\">was being</span> given a good lecture by Granny on how to be a responsible adult. (गलत tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Parts of the following sentences have been given as options. Select the option that contains a grammatical error.<br>Ms. Malathi is living lonely in an apartment.</p>",
                    question_hi: "<p>87. Parts of the following sentences have been given as options. Select the option that contains a grammatical error.<br>Ms. Malathi is living lonely in an apartment.</p>",
                    options_en: [
                        "<p>an apartment</p>",
                        "<p>lonely in</p>",
                        "<p>Ms. Malathi</p>",
                        "<p>is living</p>"
                    ],
                    options_hi: [
                        "<p>an apartment</p>",
                        "<p>lonely in</p>",
                        "<p>Ms. Malathi</p>",
                        "<p>is living</p>"
                    ],
                    solution_en: "<p>87.(b) lonely in<br>The given sentence needs an adverb &lsquo;lonely&rsquo; to modify the verb &lsquo;lives&rsquo;, not the adjective &lsquo;lonely&rsquo;. Hence, &lsquo;alone in&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>87.(b) lonely in<br>दिए गए sentence में verb &lsquo;lives&rsquo; को modify करने के लिए adverb &lsquo;lonely&rsquo; की आवश्यकता है, न कि adjective &lsquo;lonely&rsquo; की। इसलिए, &lsquo;alone in&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Sentences of a paragraph are given below in jumbled order. Select the most logical sequence of sentences from the options to construct a meaningful and cohesive paragraph.<br>A. Researchers from five medical centres around the country found that insomniacs were also significantly likely to have poor health, such as chest pain, arthritis, and depression, as well as difficulties doing daily duties.<br>B. Even temporary sleep loss can disrupt the body\'s capacity to break down carbohydrates, interfere with hormones, and aggravate diabetes and high blood pressure.<br>C. So, make time for whatever helps you sleep, whether it\'s exercise, massages, yoga, meditation, or a fragrance bath.<br>D. Sleep deprivation has frightening consequences for many biological processes.</p>",
                    question_hi: "<p>88. Sentences of a paragraph are given below in jumbled order. Select the most logical sequence of sentences from the options to construct a meaningful and cohesive paragraph.<br>A. Researchers from five medical centres around the country found that insomniacs were also significantly likely to have poor health, such as chest pain, arthritis, and depression, as well as difficulties doing daily duties.<br>B. Even temporary sleep loss can disrupt the body\'s capacity to break down carbohydrates, interfere with hormones, and aggravate diabetes and high blood pressure.<br>C. So, make time for whatever helps you sleep, whether it\'s exercise, massages, yoga, meditation, or a fragrance bath.<br>D. Sleep deprivation has frightening consequences for many biological processes.</p>",
                    options_en: [
                        "<p>D, A, C, B</p>",
                        "<p>D, A, B, C</p>",
                        "<p>D, C, A, B</p>",
                        "<p>D, C, B, A</p>"
                    ],
                    options_hi: [
                        "<p>D, A, C, B</p>",
                        "<p>D, A, B, C</p>",
                        "<p>D, C, A, B</p>",
                        "<p>D, C, B, A</p>"
                    ],
                    solution_en: "<p>88.(b) D, A, B, C <br>Sentence (D) will be the starting line as it introduces the main idea of the parajumble, i.e. &lsquo;frightening consequences of sleep deprivation for many biological processes&rsquo;. And, Sentence (A) states the finding of the researchers that insomniacs were likely to have poor health. So, (A) will follow (D). Further, Sentence (B) talks about the impact of temporary sleep loss &amp; Sentence (C) talks about making time for activities that help us sleep. So, (C) will follow (B). Going through the options, option (B) has the correct sequence.</p>",
                    solution_hi: "<p>88.(b) d, a, b, c<br>Sentence (D) आरंभिक पंक्ति होगी क्योंकि यह parajumble के main idea &lsquo;frightening consequences of sleep deprivation for many biological processes&rsquo; को प्रस्तुत करता है। और, Sentence (A) शोधकर्ताओं के निष्कर्ष को बताता है कि अनिद्रा से पीड़ित लोगों का स्वास्थ्य खराब होने की संभावना होती है। इसलिए, (D) के बाद (A) आयेगा। इसके अलावा, Sentence (B) अस्थायी नींद की कमी के प्रभाव के बारे में बात करता है और Sentence (C) उन गतिविधियों के लिए समय निकालने के बारे में बात करता है जो हमें सोने में मदद करती हैं। इसलिए, (B) के बाद (C) आयेगा । विकल्पों के माध्यम से जाने पर, option (B) में सही क्रम है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>89. Select the INCORRECTLY spelt word.</p>",
                    options_en: [
                        "<p>Responsibilities</p>",
                        "<p>Cheerfull</p>",
                        "<p>Tsunami</p>",
                        "<p>Efficient</p>"
                    ],
                    options_hi: [
                        "<p>Responsibilities</p>",
                        "<p>Cheerfull</p>",
                        "<p>Tsunami</p>",
                        "<p>Efficient</p>"
                    ],
                    solution_en: "<p>89.(b) Cheerfull <br>&ldquo;Cheerful&rdquo; is the correct spelling.</p>",
                    solution_hi: "<p>89.(b) Cheerfull <br>&ldquo;Cheerful&rdquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate option that can substitute the underlined segment in the&nbsp;given sentence.<br>What do you like to do on your free days other than <span style=\"text-decoration: underline;\">to watching movies</span>?</p>",
                    question_hi: "<p>90. Select the most appropriate option that can substitute the underlined segment in the&nbsp;given sentence.<br>What do you like to do on your free days other than <span style=\"text-decoration: underline;\">to watching movies </span>?</p>",
                    options_en: [
                        "<p>watch movie</p>",
                        "<p>watching movies</p>",
                        "<p>watches movies</p>",
                        "<p>having watched movies</p>"
                    ],
                    options_hi: [
                        "<p>watch movie</p>",
                        "<p>watching movies</p>",
                        "<p>watches movies</p>",
                        "<p>having watched movies</p>"
                    ],
                    solution_en: "<p>90.(b) watching movies<br>We use a noun, pronoun or a gerund(V<sub>ing</sub>) after the phrase &lsquo;other than&rsquo;. Therefore, &lsquo;to&rsquo; will be removed. Hence, &lsquo;watching(V<sub>ing</sub>) movies&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>90.(b) watching movies<br>हम phrase &lsquo;other than&rsquo; के बाद noun, pronoun या gerund(V<sub>ing</sub>) का उपयोग करते हैं। इसलिए, &lsquo;to&rsquo; हटा दिया जाएगा। इसलिए, &lsquo;watching(V<sub>ing</sub>) movies&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the word which means the same as the group of words given.<br>One who believes in offering equal opportunities to women in all spheres.</p>",
                    question_hi: "<p>91. Select the word which means the same as the group of words given.<br>One who believes in offering equal opportunities to women in all spheres.</p>",
                    options_en: [
                        "<p>chauvinist</p>",
                        "<p>feminist</p>",
                        "<p>fatalist</p>",
                        "<p>futurist</p>"
                    ],
                    options_hi: [
                        "<p>chauvinist</p>",
                        "<p>feminist</p>",
                        "<p>fatalist</p>",
                        "<p>futurist</p>"
                    ],
                    solution_en: "<p>91.(b) <strong>Feminist -</strong> advocating social, legal, and economic rights for women equal to those of men<br><strong>Chauvinist </strong>- a person who believes one gender is superior to the other<br><strong>Fatalist -</strong> the acceptance of all things and events as inevitable; submission to fate<br><strong>Futurist -</strong> a person whose occupation or specialty is the forecasting of future events</p>",
                    solution_hi: "<p>91.(b)<strong> Feminist -</strong> पुरुषों के बराबर महिलाओं के लिए सामाजिक, कानूनी और आर्थिक अधिकारों की वकालत करना<br><strong>Chauvinist -</strong> एक व्यक्ति जो मानता है कि एक लिंग दूसरे से श्रेष्ठ है<br><strong>Fatalist - </strong>अपरिहार्य के रूप में सभी चीजों और घटनाओं की स्वीकृति<br><strong>Futurist -</strong> एक व्यक्ति जिसका व्यवसाय या विशेषता भविष्य की घटनाओं का पूर्वानुमान है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Choose the alternative which best expresses the meaning&nbsp; of the idiom/phrase. <br>Under duress</p>",
                    question_hi: "<p>92. Choose the alternative which best expresses the meaning&nbsp; of the idiom/phrase. <br>Under duress</p>",
                    options_en: [
                        "<p>Under pressure</p>",
                        "<p>Under shelter</p>",
                        "<p>Under confusions</p>",
                        "<p>Under support</p>"
                    ],
                    options_hi: [
                        "<p>Under pressure</p>",
                        "<p>Under shelter</p>",
                        "<p>Under confusions</p>",
                        "<p>Under support</p>"
                    ],
                    solution_en: "<p>92.(a) Under duress - under pressure.<br>E.g.- Sonu is completely under duress as he has to submit his project anyhow by tomorrow.</p>",
                    solution_hi: "<p>92.(a) Under duress - under pressure&nbsp;(दबाव में).<br>E.g.- Sonu is completely under duress as he has to submit his project anyhow by tomorrow.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate ANTONYM of the underlined word.<br>Amrit felt a chill run down his spine as Mrityu <span style=\"text-decoration: underline;\">gazed</span> at him mid-conversation.</p>",
                    question_hi: "<p>93. Select the most appropriate ANTONYM of the underlined word.<br>Amrit felt a chill run down his spine as Mrityu <span style=\"text-decoration: underline;\">gazed</span> at him mid-conversation.</p>",
                    options_en: [
                        "<p>shot</p>",
                        "<p>glanced</p>",
                        "<p>stared</p>",
                        "<p>shouted</p>"
                    ],
                    options_hi: [
                        "<p>shot</p>",
                        "<p>glanced</p>",
                        "<p>stared</p>",
                        "<p>shouted</p>"
                    ],
                    solution_en: "<p>93.(b) <strong>Glanced- </strong>looked quickly and briefly.<br><strong>Gazed- </strong>looked steadily and intently.<br><strong>Shot- </strong>moved suddenly or quickly.<br><strong>Stared-</strong> looked at something for a long time.<br><strong>Shouted- </strong>spoke in a loud voice.</p>",
                    solution_hi: "<p>93.(b) <strong>Glanced </strong>(झलक देखी) - looked quickly and briefly.<br><strong>Gazed </strong>(एकटक देखा) - looked steadily and intently.<br><strong>Shot </strong>(तेजी से बढ़ा) - moved suddenly or quickly.<br><strong>Stared </strong>(घूरा) - looked at something for a long time.<br><strong>Shouted </strong>(चिल्लाया) - spoke in a loud voice.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>94. Select the INCORRECTLY spelt word.</p>",
                    options_en: [
                        "<p>Bueraucracy</p>",
                        "<p>Acquaintance</p>",
                        "<p>Rhinoceros</p>",
                        "<p>Miscellaneous</p>"
                    ],
                    options_hi: [
                        "<p>Bueraucracy</p>",
                        "<p>Acquaintance</p>",
                        "<p>Rhinoceros</p>",
                        "<p>Miscellaneous</p>"
                    ],
                    solution_en: "<p>94.(a) Bueraucracy<br>&lsquo;Bureaucracy&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>94.(a) Bueraucracy<br>&lsquo;Bureaucracy&rsquo; सही spelling है। </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95.&nbsp;Select the most appropriate option to fill in the blank.&nbsp;.<br>He was called_________ to prove the correctness of the press reports.</p>",
                    question_hi: "<p>95.&nbsp;Select the most appropriate option to fill in the blank.&nbsp;.<br>He was called_________ to prove the correctness of the press reports.</p>",
                    options_en: [
                        "<p>up</p>",
                        "<p>on</p>",
                        "<p>upon</p>",
                        "<p>None</p>"
                    ],
                    options_hi: [
                        "<p>up</p>",
                        "<p>on</p>",
                        "<p>upon</p>",
                        "<p>None</p>"
                    ],
                    solution_en: "<p>95.(c) upon<br>&lsquo;Called upon&rsquo; is a phrasal verb which means to ask formally for someone to do something. The given sentence states that &ldquo;he was called upon(formally asked) to prove the correctness of the press reports&rdquo;. Hence, &lsquo;upon&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(c) upon<br>&lsquo;Called upon&rsquo; एक phrasal verb है जिसका अर्थ औपचारिक रूप से किसी से कुछ करने के लिए कहना है। दिए गए वाक्य में कहा गया है कि &ldquo;he was called upon (formally asked) to prove the correctness of the press reports&rdquo;/ &ldquo;उन्हें प्रेस रिपोर्टों की यथार्थता/ सच्चाई साबित करने के लिए बुलाया गया था (औपचारिक रूप से पूछा गया)&rdquo;। इसलिए, \'upon\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :-</strong><br>The period from 1858-1905 was the seed time of Indian nationalism; and the early nationalists (96) ________ the seeds well and deep. Instead of basing their nationalism or appeals to (97) ___________ sentiments and passing emotions, or abstract rights of freedom and liberty, or on obscurantist appeals to the past, they _______ (98) it in a hard-headed and penetrating analysis of the complex mechanism of modern imperialism and the chief contradiction between the interests of the Indian people and British rule. The result was that they (99) ___________ a common political and economic programme which (100) ___________ rather than divided the different sections of the people. Later on, the Indian people could gather around this programme and wage powerful struggles.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test :-</strong><br>The period from 1858-1905 was the seed time of Indian nationalism; and the early nationalists (96) ________ the seeds well and deep. Instead of basing their nationalism or appeals to (97) ___________ sentiments and passing emotions, or abstract rights of freedom and liberty, or on obscurantist appeals to the past, they _______ (98) it in a hard-headed and penetrating analysis of the complex mechanism of modern imperialism and the chief contradiction between the interests of the Indian people and British rule. The result was that they (99) ___________ a common political and economic programme which (100) ___________ rather than divided the different sections of the people. Later on, the Indian people could gather around this programme and wage powerful struggles.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: [
                        "<p>sowed</p>",
                        "<p>scattered</p>",
                        "<p>buried</p>",
                        "<p>threw</p>"
                    ],
                    options_hi: [
                        "<p>sowed</p>",
                        "<p>scattered</p>",
                        "<p>buried</p>",
                        "<p>threw</p>"
                    ],
                    solution_en: "<p>96.(a) sowed<br>The idiom &lsquo;sow the seeds&rsquo; means to cause an idea or feeling to be in someone&rsquo;s mind. The given passage states that the early nationalists sowed the seeds of Indian nationalism well and deep. Hence, \'sowed\' is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(a) sowed<br>Idiom &lsquo;sow the seeds&rsquo; का अर्थ है किसी के मन में (someone&rsquo;s mind) कोई विचार या भावना उत्पन्न करना। दिए गए passage में बताया गया है कि शुरुआती राष्ट्रवादियों (early nationalists) ने भारतीय राष्ट्रवाद के बीज अच्छी तरह एवं गहराई से बोए। अतः, \'sowed\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97.<strong>Cloze Test :-</strong><br>The period from 1858-1905 was the seed time of Indian nationalism; and the early nationalists (96) ________ the seeds well and deep. Instead of basing their nationalism or appeals to (97) ___________ sentiments and passing emotions, or abstract rights of freedom and liberty, or on obscurantist appeals to the past, they _______ (98) it in a hard-headed and penetrating analysis of the complex mechanism of modern imperialism and the chief contradiction between the interests of the Indian people and British rule. The result was that they (99) ___________ a common political and economic programme which (100) ___________ rather than divided the different sections of the people. Later on, the Indian people could gather around this programme and wage powerful struggles.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test :-</strong><br>The period from 1858-1905 was the seed time of Indian nationalism; and the early nationalists (96) ________ the seeds well and deep. Instead of basing their nationalism or appeals to (97) ___________ sentiments and passing emotions, or abstract rights of freedom and liberty, or on obscurantist appeals to the past, they _______ (98) it in a hard-headed and penetrating analysis of the complex mechanism of modern imperialism and the chief contradiction between the interests of the Indian people and British rule. The result was that they (99) ___________ a common political and economic programme which (100) ___________ rather than divided the different sections of the people. Later on, the Indian people could gather around this programme and wage powerful struggles.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: [
                        "<p>shallow</p>",
                        "<p>deep</p>",
                        "<p>grounded</p>",
                        "<p>profound</p>"
                    ],
                    options_hi: [
                        "<p>shallow</p>",
                        "<p>deep</p>",
                        "<p>grounded</p>",
                        "<p>profound</p>"
                    ],
                    solution_en: "<p>97.(a) shallow<br>&lsquo;Shallow&rsquo; means not deep or strong. The given passage states that the early nationalists did not base their nationalism or appeals to shallow sentiments and passing emotions, or abstract rights of freedom and liberty, or on obscurantist appeals to the past. Hence, &lsquo;shallow&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(a) shallow<br>&lsquo;Shallow&rsquo; का अर्थ है not deep या strong. दिए गए passage में बताया गया है कि प्रारम्भिक राष्ट्रवादियों (early nationalists) ने अपने राष्ट्रवाद या अपील को उथली भावनाओं और क्षणिक भावनाओं, या स्वतंत्रता (liberty) और आज़ादी (freedom) के अमूर्त अधिकारों (abstract rights), या अतीत की रूढ़िवादी अपीलों (obscurantist appeals) पर आधारित नहीं किया। अतः, &lsquo;shallow&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98.<strong>Cloze Test :-</strong><br>The period from 1858-1905 was the seed time of Indian nationalism; and the early nationalists (96) ________ the seeds well and deep. Instead of basing their nationalism or appeals to (97) ___________ sentiments and passing emotions, or abstract rights of freedom and liberty, or on obscurantist appeals to the past, they _______ (98) it in a hard-headed and penetrating analysis of the complex mechanism of modern imperialism and the chief contradiction between the interests of the Indian people and British rule. The result was that they (99) ___________ a common political and economic programme which (100) ___________ rather than divided the different sections of the people. Later on, the Indian people could gather around this programme and wage powerful struggles.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test :-</strong><br>The period from 1858-1905 was the seed time of Indian nationalism; and the early nationalists (96) ________ the seeds well and deep. Instead of basing their nationalism or appeals to (97) ___________ sentiments and passing emotions, or abstract rights of freedom and liberty, or on obscurantist appeals to the past, they _______ (98) it in a hard-headed and penetrating analysis of the complex mechanism of modern imperialism and the chief contradiction between the interests of the Indian people and British rule. The result was that they (99) ___________ a common political and economic programme which (100) ___________ rather than divided the different sections of the people. Later on, the Indian people could gather around this programme and wage powerful struggles.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: [
                        "<p>rooted</p>",
                        "<p>drove</p>",
                        "<p>controlled</p>",
                        "<p>minded</p>"
                    ],
                    options_hi: [
                        "<p>rooted</p>",
                        "<p>drove</p>",
                        "<p>controlled</p>",
                        "<p>minded</p>"
                    ],
                    solution_en: "<p>98.(a) rooted<br>&lsquo;Root&rsquo; means to establish deeply and firmly. The given passage states that they rooted it in a hard-headed and penetrating analysis of the complex mechanism of modern imperialism and the chief contradiction between the interests of the Indian people and British rule. Hence, \'rooted\' is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(a) rooted<br>&lsquo;Root&rsquo; का अर्थ है गहराई से और दृढ़तापूर्वक establish करना। दिए गए passage में बताया गया है कि उन्होंने आधुनिक साम्राज्यवाद (modern imperialism) के जटिल तंत्र तथा भारतीय जनता (Indian people) एवं ब्रिटिश शासन (British rule) के हितों के बीच मुख्य विरोधाभास (contradiction) के कठोर एवं गहन विश्लेषण में इसकी जड़ें जमाईं। अतः, \'rooted\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99.<strong>Cloze Test :-</strong><br>The period from 1858-1905 was the seed time of Indian nationalism; and the early nationalists (96) ________ the seeds well and deep. Instead of basing their nationalism or appeals to (97) ___________ sentiments and passing emotions, or abstract rights of freedom and liberty, or on obscurantist appeals to the past, they _______ (98) it in a hard-headed and penetrating analysis of the complex mechanism of modern imperialism and the chief contradiction between the interests of the Indian people and British rule. The result was that they (99) ___________ a common political and economic programme which (100) ___________ rather than divided the different sections of the people. Later on, the Indian people could gather around this programme and wage powerful struggles.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test :-</strong><br>The period from 1858-1905 was the seed time of Indian nationalism; and the early nationalists (96) ________ the seeds well and deep. Instead of basing their nationalism or appeals to (97) ___________ sentiments and passing emotions, or abstract rights of freedom and liberty, or on obscurantist appeals to the past, they _______ (98) it in a hard-headed and penetrating analysis of the complex mechanism of modern imperialism and the chief contradiction between the interests of the Indian people and British rule. The result was that they (99) ___________ a common political and economic programme which (100) ___________ rather than divided the different sections of the people. Later on, the Indian people could gather around this programme and wage powerful struggles.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: [
                        "<p>collapsed</p>",
                        "<p>evolved</p>",
                        "<p>banished</p>",
                        "<p>uprooted</p>"
                    ],
                    options_hi: [
                        "<p>collapsed</p>",
                        "<p>evolved</p>",
                        "<p>banished</p>",
                        "<p>uprooted</p>"
                    ],
                    solution_en: "<p>99.(b) evolved<br>&lsquo;Evolved&rsquo; means to develop gradually. The given passage states that they evolved a common political and economic programme. Hence, \'evolved\' is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b) evolved<br>&lsquo;Evolved&rsquo; का अर्थ है धीरे-धीरे विकसित होना। दिए गए passage में बताया गया है कि उन्होंने एक सामान्य राजनीतिक (common political) एवं आर्थिक कार्यक्रम (economic programme) विकसित किया। अतः, \'evolved\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100.<strong>Cloze Test :-</strong><br>The period from 1858-1905 was the seed time of Indian nationalism; and the early nationalists (96) ________ the seeds well and deep. Instead of basing their nationalism or appeals to (97) ___________ sentiments and passing emotions, or abstract rights of freedom and liberty, or on obscurantist appeals to the past, they _______ (98) it in a hard-headed and penetrating analysis of the complex mechanism of modern imperialism and the chief contradiction between the interests of the Indian people and British rule. The result was that they (99) ___________ a common political and economic programme which (100) ___________ rather than divided the different sections of the people. Later on, the Indian people could gather around this programme and wage powerful struggles.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test :-</strong><br>The period from 1858-1905 was the seed time of Indian nationalism; and the early nationalists (96) ________ the seeds well and deep. Instead of basing their nationalism or appeals to (97) ___________ sentiments and passing emotions, or abstract rights of freedom and liberty, or on obscurantist appeals to the past, they _______ (98) it in a hard-headed and penetrating analysis of the complex mechanism of modern imperialism and the chief contradiction between the interests of the Indian people and British rule. The result was that they (99) ___________ a common political and economic programme which (100) ___________ rather than divided the different sections of the people. Later on, the Indian people could gather around this programme and wage powerful struggles.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: [
                        "<p>complicated</p>",
                        "<p>united</p>",
                        "<p>classified</p>",
                        "<p>dissolved</p>"
                    ],
                    options_hi: [
                        "<p>complicated</p>",
                        "<p>united</p>",
                        "<p>classified</p>",
                        "<p>dissolved</p>"
                    ],
                    solution_en: "<p>100.(b) united <br>&lsquo;Unite&rsquo; means to bring together a group of people. The given passage states that they evolved a common political and economic programme which united rather than divided the different sections of the people. Hence, &lsquo;united&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(b) united <br>&lsquo;Unite&rsquo; का अर्थ है लोगों के एक समूह को एक साथ लाना। दिए गए passage में बताया गया है कि उन्होंने एक सामान्य राजनीतिक (common political) और आर्थिक कार्यक्रम विकसित किया जिसने लोगों के विभिन्न वर्गों को विभाजित करने के बजाय एकजुट किया। अतः, &lsquo;united&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>