<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which is Satyajit Ray&lsquo;s famous film about the decline of the aristocratic zamindari style of living?</p>",
                    question_hi: "<p>1. कुलीन जमींदारी शैली के पतन के बारे में सत्यजीत रे की प्रसिद्ध फिल्म कौन सी है?</p>",
                    options_en: ["<p>Apur Sansar</p>", "<p>Charulata</p>", 
                                "<p>Pather Panchali</p>", "<p>Jalsaghar</p>"],
                    options_hi: ["<p>अपुर संसार</p>", "<p>चारुलता</p>",
                                "<p>पाथेर पांचाली</p>", "<p>जलसाघर</p>"],
                    solution_en: "<p>1.(d) <strong>Jalsaghar </strong>(\"The Music Room\") - 1958 drama, Film series: The Apu Series, Actor: Chhabi Biswas, Story written by: Tarasankar Bandyopadhyay. <strong>India\'s history of film:</strong> First silent film - Raja Harishchandra (1913), First sound film - Alam Ara (1931), First colour film - Kisan Kanya (1937), First 3D film - My Dear Kuttichathan (1984).</p>",
                    solution_hi: "<p>1.(d) <strong>जलसाघर </strong>(\"द म्यूजिक रूम\") - 1958 ड्रामा, फिल्म श्रृंखला: द अपू सीरीज, अभिनेता: छवि बिस्वास, लेखक : ताराशंकर बंद्योपाध्याय। <strong>भारतीय फिल्म इतिहास-</strong> पहली मूक फिल्म (silent film) - राजा हरिश्चंद्र (1913), पहली बोलती फिल्म - आलम आरा (1931), पहली रंगीन फिल्म - किसान कन्या (1937), पहली 3D फिल्म - माई डियर कुट्टीचथन (1984)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. Identify the name of the traditional folk theatre form of Maharashtra.",
                    question_hi: "2. महाराष्ट्र के पारंपरिक लोक रंगमंच के नाम की पहचान कीजिये ?",
                    options_en: [" Tamasha ", " Rasleela ", 
                                " Nautanki ", " Swang "],
                    options_hi: [" तमाशा", " रासलीला",
                                " नौटंकी", " स्वांग "],
                    solution_en: "<p>2.(a) <strong>Tamasha</strong>. Its performances are accompanied by<strong> Lavani songs.</strong> Its two main branches - <strong>Dholki Bhaari</strong> and <strong>Sangeet Bhaari.</strong> The unique feature - Female actors play even male roles. Other folk dances of Maharashtra - Dhangari Gaja, Folk Dances, Koli Dance, Lavani Dance, Povadas Dance. <strong>Rasleela </strong>and <strong>Nautanki </strong>dance - Uttar Pradesh. <strong>Swang </strong>- Haryana.</p>",
                    solution_hi: "<p>2.(a) <strong>तमाशा</strong>। इसकी प्रस्तुतियाँ <strong>लावणी गीतों</strong> के साथ होती हैं। इसकी दो मुख्य शाखाएँ हैं - <strong>ढोलकी भारी </strong>और <strong>संगीत भारी।</strong> अनूठी विशेषता - महिला कलाकार पुरुष भूमिकाएं भी निभाती हैं। महाराष्ट्र के अन्य लोक नृत्य - धनगारी गाजा, लोक नृत्य, कोली नृत्य, लावणी नृत्य, पोवाडास नृत्य। <strong>रासलीला </strong>और <strong>नौटंकी नृत्य</strong> - उत्तर प्रदेश। <strong>स्वांग </strong>- हरियाणा।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following ﬁlms was NOT nominated for an Oscar award?</p>",
                    question_hi: "<p>3. निम्नलिखित में से किस फिल्म को ऑस्कर पुरस्कार के लिए नामांकित नहीं किया गया था?</p>",
                    options_en: ["<p>Salaam Bombay</p>", "<p>Mother India</p>", 
                                "<p>Lagaan</p>", "<p>Karma</p>"],
                    options_hi: ["<p>सलाम बॉम्बे</p>", "<p>मदर इंडिया</p>",
                                "<p>लगान</p>", "<p>कर्मा</p>"],
                    solution_en: "<p>3.(d) <strong>Karma movie</strong> (Release - 1986, Director - Subhash Ghai). <strong>Oscar nominated movies of India -</strong> Mother India (1957), Salaam Bombay (1988), Lagaan (2001). The First Oscar Awarded Movie - Wings. The Academy Awards (started in 1929) are awards for artistic and technical merit for the film industry. These are presented annually by the <strong>Academy of Motion Picture Arts and Sciences</strong>, in recognition of excellence in cinematic achievements as assessed by the Academy\'s voting membership.</p>",
                    solution_hi: "<p>3.(d) <strong>कर्मा फिल्म</strong> (रिलीज़ - 1986, निर्देशक - सुभाष घई)। <strong>भारत की ऑस्कर नामांकित फिल्में - </strong>मदर इंडिया (1957), सलाम बॉम्बे (1988), लगान (2001)। प्रथम ऑस्कर पुरस्कार प्राप्त फिल्म - विंग्स। अकादमी पुरस्कार (1929 में प्रारंभ) फिल्म उद्योग के लिए कलात्मक और तकनीकी योग्यता के लिए पुरस्कार हैं। अकादमी की वोटिंग सदस्यता द्वारा मूल्यांकन की गई सिनेमाई उपलब्धियों में उत्कृष्टता की मान्यता में, इन्हें अ<strong>कादमी ऑफ मोशन पिक्चर आर्ट्स एंड साइंसेज </strong>द्वारा प्रतिवर्ष प्रस्तुत किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following films was directed by Satyajit Ray?</p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन सी फिल्म सत्यजीत रे द्वारा निर्देशित थी?</p>",
                    options_en: ["<p>Mahal</p>", "<p>Pyaasa</p>", 
                                "<p>Pather Panchali</p>", "<p>Kora Kagaz</p>"],
                    options_hi: ["<p>महल</p>", "<p>प्यासा</p>",
                                "<p>पाथेर पांचाली</p>", "<p>कोरा कागज़</p>"],
                    solution_en: "<p>4.(c) <strong>Pather Panchali. </strong>Other famous films by Satyajit Ray - &ldquo;The Music Room&rdquo;, &ldquo;The Big City, Charulata&rdquo;, &ldquo;Days and Nights in the Forest&rdquo;. Satyajit Ray became the first Indian to receive an Honorary Academy Award in 1992. <strong>Other Awards of Satyajit Ray - </strong>Padamshri (1958), Padam Bhusan (1965), Padam Vibhushan (1976), Bharat Ratan (1992). &ldquo;Mahal&rdquo; - Director - Kamal Amrohi. &ldquo;Kora Kagaz&rdquo; - Director - Anil Ganguly.</p>",
                    solution_hi: "<p>4.(c) <strong>पाथेर पांचाली।</strong> सत्यजीत रे की अन्य प्रसिद्ध फ़िल्में - \"द म्यूज़िक रूम\", \"द बिग सिटी, चारुलता\", \"डेज़ एंड नाइट्स इन द फ़ॉरेस्ट\"। सत्यजीत रे 1992 में मानद अकादमी पुरस्कार (Honorary Academy Award) प्राप्त करने वाले पहले भारतीय बने। <strong>सत्यजीत रे के अन्य पुरस्कार - </strong>पदमश्री (1958), पदम भूषण (1965), पदम विभूषण (1976), भारत रतन (1992)। \"महल\" - निर्देशक - कमाल अमरोही। \"कोरा कागज़\" - निर्देशक - अनिल गांगुली।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following novels is written by RK Narayan and also adapted into a Hindi film by the same name ?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन-सा उपन्यास आर. के. नारायण द्वारा लिखा गया है और इसी नाम से एक हिंदी फिल्म में रूपांतरित भी किया गया है ?</p>",
                    options_en: ["<p>Hello</p>", "<p>The Guide</p>", 
                                "<p>Devdas</p>", "<p>3 Idiots</p>"],
                    options_hi: ["<p>हैलो</p>", "<p>द गाइड</p>",
                                "<p>देवदास</p>", "<p>थ्री इडियट्स</p>"],
                    solution_en: "<p>5.(b) <strong>The Guide</strong> (1965). Movies based on R.K Narayan&rsquo;s Novels: Banker Margayya (1983), Mr Sampath (1972).</p>",
                    solution_hi: "<p>5.(b) <strong>द गाइड </strong>(1965)। आर.के. नारायण के उपन्यासों पर आधारित फिल्में: बैंकर मार्गय्या (1983), मिस्टर संपत (1972)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Who was the music director of the famous film \'Sholay\' released in 1975?</p>",
                    question_hi: "<p>6. 1975 में रिलीज़ हुई प्रसिद्ध फिल्म \'शोले\' के संगीत निर्देशक कौन थे?</p>",
                    options_en: ["<p>Madan Mohan</p>", "<p>Jatin - Lalit</p>", 
                                "<p>Nadeem-Shravan</p>", "<p>RD Burman</p>"],
                    options_hi: ["<p>मदन मोहन</p>", "<p>जतिन-ललित</p>",
                                "<p>नदीम-श्रवण</p>", "<p>आर. डी. बर्मन</p>"],
                    solution_en: "<p>6.(d) <strong>RD Burman</strong>. Sholay Movie: Director (Ramesh Sippy), Producer (GP Sippy), Writer (Salim-Javed).</p>",
                    solution_hi: "<p>6.(d) <strong>आर.डी. बर्मन। </strong>शोले फिल्म : निर्देशक (रमेश सिप्पी), निर्माता (GP सिप्पी), लेखक (सलीम-जावेद)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Who among the following singers had a Guinness World Record for recording 28 songs in a single day? (As of December 2022.)</p>",
                    question_hi: "<p>7. निम्नलिखित गायकों में से किस गायक के नाम एक ही दिन में 28 गाने रिकॉर्ड करने का गिनीज वर्ल्ड रिकॉर्ड था? (दिसंबर 2022 तक ।)</p>",
                    options_en: ["<p>Sonu Nigam</p>", "<p>Udit Narayan</p>", 
                                "<p>Kumar Sanu</p>", "<p>Arijit Singh</p>"],
                    options_hi: ["<p>सोनू निगम</p>", "<p>उदित नारायण</p>",
                                "<p>कुमार सानू</p>", "<p>अरिजीत सिंह</p>"],
                    solution_en: "<p>7.(c) <strong>Kumar Sanu: </strong>Awards and Honours - Maharashtra Ratna Awards (2014), Padma Shri (2009). Udit Narayan (Playback singer): Awards - Padma Shri (2009), Padma Bhushan (2016). Sonu Nigam: Awards - Padma Shri (2022). Arijit Singh - Singer and music composer.</p>",
                    solution_hi: "<p>7.(c) <strong>कुमार शानू:</strong> पुरस्कार और&nbsp;सम्मान - महाराष्ट्र रत्न पुरस्कार (2014), पद्म श्री (2009)। उदित नारायण (पार्श्व गायक): पुरस्कार - पद्म श्री (2009), पद्म भूषण (2016)। सोनू निगम: पुरस्कार - पद्म श्री (2022)। अरिजीत सिंह - गायक और संगीतकार।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. The Telugu movie RRR created history by winning a Golden Globe for the Natu Natu song composed by whom among the following personalities?</p>",
                    question_hi: "<p>8. तेलुगु फिल्म आरआरआर (RRR) ने निम्नलिखित में से किसके द्वारा रचित नाटू नाटू गीत के लिए गोल्डन ग्लोब जीतकर इतिहास रच दिया ?</p>",
                    options_en: ["<p>Shankar - Ehsaan - Loy</p>", "<p>Mervin Solomon</p>", 
                                "<p>MM Keeravani</p>", "<p>AR Rahman</p>"],
                    options_hi: ["<p>शंकर-एहसान-लॉय</p>", "<p>मर्विन सोलोमन</p>",
                                "<p>एम. एम. कीरावनी</p>", "<p>ए. आर. रहमान</p>"],
                    solution_en: "<p>8.(c) <strong>MM Keeravani. RRR film: </strong>Director - S. S. Rajamouli. Academy Awards (Oscars Awards) were first awarded in 1929.</p>",
                    solution_hi: "<p>8.(c) <strong>एम. एम. कीरावनी। </strong>RRR फिल्म: निर्देशक - S. S. राजामौली। अकादमी पुरस्कार (ऑस्कर पुरस्कार) 1929 में प्रथम&nbsp;पुरस्कार प्रदान किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Azhar (2016), a sports-related movie, is based on which of the following sports?</p>",
                    question_hi: "<p>9. एक खेल से संबंधित फिल्म, अज़हर (2016) निम्नलिखित में से किस खेल पर आधारित है?</p>",
                    options_en: ["<p>Cricket</p>", "<p>Mixed Martial arts</p>", 
                                "<p>Boxing</p>", "<p>Hockey</p>"],
                    options_hi: ["<p>क्रिकेट</p>", "<p>मिक्स्ड मार्शल आर्ट्स</p>",
                                "<p>मुक्केबाज़ी</p>", "<p>हॉकी</p>"],
                    solution_en: "<p>9.(a) <strong>Cricket</strong>. Azhar (2016) - The story inspired by Mohammad Azharuddin (Former national cricket team captain). Movies based on Indian Cricketers: Sachin Tendulkar: A Billion Dreams (2017) - Based on Former Indian Cricketer Sachin Tendulkar. 83 (2021) - Based on the India national cricket team led by Kapil Dev, which won the 1983 Cricket World Cup. M S Dhoni: The Untold Story (2016) - Mahendra Singh Dhoni (Former captain of the Indian national cricket team).</p>",
                    solution_hi: "<p>9.(a) <strong>क्रिकेट</strong>। अज़हर (2016) - मोहम्मद अज़हरुद्दीन (पूर्व राष्ट्रीय क्रिकेट टीम के कप्तान) से प्रेरित कहानी। भारतीय क्रिकेटरों पर आधारित फिल्में: सचिन तेंदुलकर: ए बिलियन ड्रीम्स (2017) - पूर्व भारतीय क्रिकेटर सचिन तेंदुलकर पर आधारित। 83 (2021) - कपिल देव के नेतृत्व वाली भारत की राष्ट्रीय क्रिकेट टीम पर आधारित, जिसने 1983 क्रिकेट विश्व कप जीता था। एम एस धोनी: द अनटोल्ड स्टोरी (2016) - महेंद्र सिंह धोनी (भारतीय राष्ट्रीय क्रिकेट टीम के पूर्व कप्तान)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following is NOT an example of Mass Media?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन-सा जनसंचार (मास मीडिया) का एक उदाहरण नहीं है?</p>",
                    options_en: ["<p>Radio</p>", "<p>Door to door campaign</p>", 
                                "<p>Television</p>", "<p>Newspapers</p>"],
                    options_hi: ["<p>रेडियो</p>", "<p>घर-घर जाकर अभियान करना</p>",
                                "<p>टेलीविज़न</p>", "<p>समाचार पत्र</p>"],
                    solution_en: "<p>10.(b) <strong>Door to door campaign. </strong>Mass Media: A technology that is intended to reach a mass audience. It is the primary means of communication used to reach the general public.</p>",
                    solution_hi: "<p>10.(b) <strong>घर-घर जाकर अभियान करना । </strong>मास मीडिया (जनसंचार) : एक ऐसी तकनीक जिसका उद्देश्य बड़े पैमाने पर दर्शकों तक पहुंचना है। यह आम जनता तक पहुँचने के लिए उपयोग किया जाने वाला संचार का प्राथमिक साधन है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Who among the following composers won the Grammy in 2015 for his album \'Winds of Samsara\' - a collaboration with South African flautist Wouter Kellerman?</p>",
                    question_hi: "<p>11. निम्नलिखित संगीतकारों में से किसने दक्षिण अफ्रीका के बांसुरी वादक वूंटर केलरमैन (Wouter Kellerman) के साथ मिलकर बनाए गए अपने एल्बम \'विंड्स ऑफ संसार के लिए 2015 में ग्रैमी पुरस्कार जीता?</p>",
                    options_en: ["<p>Mano Murthy</p>", "<p>Raghu Dixit</p>", 
                                "<p>Subhashish Ghosh</p>", "<p>Ricky Kej</p>"],
                    options_hi: ["<p>मनो मूर्ति</p>", "<p>रघु दीक्षित</p>",
                                "<p>सुभाशीष घोष</p>", "<p>रिकी केज</p>"],
                    solution_en: "<p>11.(d) <strong>Ricky Kej. </strong>He won his second Grammy award in 2022 for his album &lsquo;<strong>Divine Tides&rsquo;</strong> in category of Best New age album.</p>",
                    solution_hi: "<p>11.(d) <strong>रिकी केज।</strong> उन्होंने बेस्ट न्यू एज एल्बम की श्रेणी में अपने एल्बम \'<strong>डिवाइन टाइड्स\' </strong>के लिए 2022 में अपना दूसरा ग्रैमी पुरस्कार जीता।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Who among the following was the winner of the National Film Award in 2002 for his choreography in the Hindi movie \'Lagaan\' ?</p>",
                    question_hi: "<p>12. निम्नलिखित में से किसे हिंदी फिल्म \'लगान\' में अपनी कोरियोग्राफी के लिए वर्ष 2002 में राष्ट्रीय फिल्म पुरस्कार से सम्मानित किया गया था?</p>",
                    options_en: ["<p>Ahmed Khan</p>", "<p>Raju Khan</p>", 
                                "<p>Dinesh Kumar</p>", "<p>Shiamak Davar</p>"],
                    options_hi: ["<p>अहमद खान</p>", "<p>राजू खान</p>",
                                "<p>दिनेश कुमार</p>", "<p>श्यामक दावर</p>"],
                    solution_en: "<p>12.(b) <strong>Raju Khan. Lagaan:</strong> {Director: Ashutosh Gowariker, Actor: Aamir Khan, music director: A. R. Rahman}. Terence Lewis won the 2002 American Choreography Award for his work in this movie.</p>",
                    solution_hi: "<p>12.(b) <strong>राजू खान। लगान :</strong> {निर्देशक: आशुतोष गोवारिकर, अभिनेता: आमिर खान, संगीत निर्देशक: ए.आर. रहमान}। इस फिल्म में अपने काम के लिए टेरेंस लुईस ने 2002 का अमेरिकी कोरियोग्राफी पुरस्कार जीता।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Who among the following became the first Asian to win the award for best sound for the documentary \'India&rsquo;s Daughter\' at the coveted Motion Picture Sound Editors&rsquo; 63rd annual Golden Reel Awards?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन प्रतिष्ठित मोशन पिक्चर साउंड एडिटर्स के 63 वें वार्षिक गोल्डन रील अवार्ड्स में वृत्तचित्र \'इंडियाज डॉटर\' के लिए सर्वश्रेष्ठ साउंड का पुरस्कार जीतने वाले पहले एशियाई बने?</p>",
                    options_en: ["<p>Justin Jose</p>", "<p>Resul Pookutty</p>", 
                                "<p>P.M.Satheesh</p>", "<p>Boby John</p>"],
                    options_hi: ["<p>जस्टिन जोस</p>", "<p>रेसुल पुकुट्टी</p>",
                                "<p>पी. एम. सतीश</p>", "<p>बॉबी जॉन</p>"],
                    solution_en: "<p>13.(b) <strong>Resul Pookutty</strong> (Indian film sound designer, sound editor, and audio mixer). <strong>Awards</strong>: Golden Reel Award (2016), Padma Shri (2010), National Film Award for Best Audiography (2010), Asianet Film Awards (2009).</p>",
                    solution_hi: "<p>13.(b) <strong>रेसुल पुकुट्टी </strong>(भारतीय फिल्म साउंड डिजाइनर, साउंड संपादक और ऑडियो मिक्सर)। <strong>पुरस्कार</strong>: गोल्डन रील अवार्ड (2016), पद्म श्री (2010), सर्वश्रेष्ठ ऑडियोग्राफी के लिए राष्ट्रीय फिल्म पुरस्कार (2010), एशियानेट फिल्म अवार्ड्स (2009)। फिल्म - इंडियाज डॉटर (निर्देशक और निर्माता : लेस्ली उडविन)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. In 2012, for which of the following films did Pandit Birju Maharaj win the &lsquo;Best Choreography&rsquo; category National Award?</p>",
                    question_hi: "<p>14. 2012 में पंडित बिरजू महाराज को निम्नलिखित में से किस फिल्म के लिए \'सर्वश्रेष्ठ कोरियोग्राफी\' श्रेणी में राष्ट्रीय पुरस्कार मिला?</p>",
                    options_en: ["<p>Nanban</p>", "<p>Vishwaroopam</p>", 
                                "<p>Ambuli</p>", "<p>Aravaan</p>"],
                    options_hi: ["<p>नन्बन</p>", "<p>विश्वरूपम</p>",
                                "<p>अम्बुली</p>", "<p>अरवान</p>"],
                    solution_en: "<p>14.(b) <strong>Vishwaroopam </strong>(for Unnai Kaanaatha song). Filmfare Award for Best Choreography for Mohe Rang Do Laal, Bajirao Mastani (2016).</p>",
                    solution_hi: "<p>14.(b) <strong>विश्वरूपम </strong>(उन्नई कानाथु गीत के लिए)। बाजीराव मस्तानी (2016) फिल्म के मोहे रंग दो लाल गीत के लिए सर्वश्रेष्ठ कोरियोग्राफी का फिल्मफेयर पुरस्कार।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Who among the following has sung the famous Bollywood song &lsquo;Channa Mereya&rsquo;?</p>",
                    question_hi: "<p>15. निम्नलिखित में से किसने प्रसिद्ध बॉलीवुड गीत \'चन्ना मेरेया\' गाया है?</p>",
                    options_en: ["<p>Neeti Mohan</p>", "<p>Arijit Singh</p>", 
                                "<p>Shreya Ghoshal</p>", "<p>Sukhbir</p>"],
                    options_hi: ["<p>नीति मोहन</p>", "<p>अरिजीत सिंह</p>",
                                "<p>श्रेया घोषाल</p>", "<p>सुखबीर</p>"],
                    solution_en: "<p>15.(b) <strong>Arijit Singh.</strong> The song \"Channa Mereya\" is from \"Ae Dil Hai Mushkil\" ( Indian film directed by Karan Johar in 2016).</p>",
                    solution_hi: "<p>15.(b) <strong>अरिजीत सिंह। </strong>गाना \"चन्ना मेरेया\" \"ऐ दिल है मुश्किल\" (2016 में करण जौहर द्वारा निर्देशित भारतीय फिल्म) से है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>