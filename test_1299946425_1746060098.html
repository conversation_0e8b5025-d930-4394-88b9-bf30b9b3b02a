<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. ADFI is related to CFHK in a certain way based on the English alphabetical order. In the same way, EHJM is related to GJLO. To which of the following is ILNQ related to, following the same logic?</p>",
                    question_hi: "<p>1. अंग्रेजी वर्णमाला क्रम के आधार पर ADFI, CFHK से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, EHJM, GJLO से संबंधित है। समान तर्क का अनुसरण करते हुए, ILNQ निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: [
                        "<p>KNQT</p>",
                        "<p>KMPS</p>",
                        "<p>KNQS</p>",
                        "<p>KNPS</p>"
                    ],
                    options_hi: [
                        "<p>KNQT</p>",
                        "<p>KMPS</p>",
                        "<p>KNQS</p>",
                        "<p>KNPS</p>"
                    ],
                    solution_en: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124074.png\" alt=\"rId4\" width=\"115\" height=\"92\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124190.png\" alt=\"rId5\" width=\"117\" height=\"89\"><br>Similarly,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124288.png\" alt=\"rId6\" width=\"110\" height=\"97\"></p>",
                    solution_hi: "<p>1.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124074.png\" alt=\"rId4\" width=\"126\" height=\"100\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124190.png\" alt=\"rId5\" width=\"127\" height=\"96\"><br>इसी प्रकार,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124288.png\" alt=\"rId6\" width=\"112\" height=\"98\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. How many semi circles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124481.png\" alt=\"rId7\" width=\"123\" height=\"104\"></p>",
                    question_hi: "<p>2. दी गई आकृति में कितने अर्धवृत्त हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124481.png\" alt=\"rId7\" width=\"123\" height=\"104\"></p>",
                    options_en: [
                        "<p>7</p>",
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>7</p>",
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124596.png\" alt=\"rId8\" width=\"141\" height=\"109\"><br>There are 5 semicircle</p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124596.png\" alt=\"rId8\" width=\"141\" height=\"109\"><br>5 अर्धवृत्त हैं</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Which of the following letter-clusters will replace the question mark (?) in the given series to make it logically complete?<br>JZI 3&nbsp; &nbsp; LVL 9&nbsp; &nbsp; ?&nbsp; &nbsp; PNR 81&nbsp; &nbsp; RJU 243</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन-सा अक्षर-समूह, दी गई श्रृंखला को तार्किक रूप से पूर्ण बनाने के लिए प्रश्न चिह्न (?) के स्थान पर आएगा?<br>JZI 3&nbsp; &nbsp;LVL 9&nbsp; &nbsp;?&nbsp; &nbsp;PNR 81&nbsp; &nbsp;RJU 243</p>",
                    options_en: [
                        "<p>MPO 15</p>",
                        "<p>NRO 27</p>",
                        "<p>BYR 21</p>",
                        "<p>NGR 17</p>"
                    ],
                    options_hi: [
                        "<p>MPO 15</p>",
                        "<p>NRO 27</p>",
                        "<p>BYR 21</p>",
                        "<p>NGR 17</p>"
                    ],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124765.png\" alt=\"rId9\" width=\"276\" height=\"81\"></p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551124765.png\" alt=\"rId9\" width=\"276\" height=\"81\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the correct mirror image of the given figure when the mirror is placed at MN, as shown.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125034.png\" alt=\"rId10\" width=\"99\" height=\"121\"></p>",
                    question_hi: "<p>4. दर्पण को दर्शाए गए अनुसार MN पर रखे जाने पर दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125034.png\" alt=\"rId10\" width=\"99\" height=\"121\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125143.png\" alt=\"rId11\" width=\"89\" height=\"89\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125266.png\" alt=\"rId12\" width=\"89\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125391.png\" alt=\"rId13\" width=\"89\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125500.png\" alt=\"rId14\" width=\"90\" height=\"89\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125143.png\" alt=\"rId11\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125266.png\" alt=\"rId12\" width=\"90\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125391.png\" alt=\"rId13\" width=\"89\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125500.png\" alt=\"rId14\" width=\"89\" height=\"88\"></p>"
                    ],
                    solution_en: "<p>4.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125143.png\" alt=\"rId11\" width=\"91\" height=\"91\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125143.png\" alt=\"rId11\" width=\"91\" height=\"91\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different. <br>Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>5. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एकसमान हैं और एक उनसे असंगत है। उस असंगत अक्षर-समूह का चयन कीजिए।<br>नोट: अक्षर समूह में, असंगत व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।.</p>",
                    options_en: [
                        "<p>BEHI</p>",
                        "<p>TWYZ</p>",
                        "<p>PSUV</p>",
                        "<p>FIKL</p>"
                    ],
                    options_hi: [
                        "<p>BEHI</p>",
                        "<p>TWYZ</p>",
                        "<p>PSUV</p>",
                        "<p>FIKL</p>"
                    ],
                    solution_en: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125606.png\" alt=\"rId15\" width=\"141\" height=\"79\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125700.png\" alt=\"rId16\" width=\"140\" height=\"74\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125787.png\" alt=\"rId17\" width=\"124\" height=\"78\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125876.png\" alt=\"rId18\" width=\"141\" height=\"76\"></p>",
                    solution_hi: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125606.png\" alt=\"rId15\" width=\"141\" height=\"79\">,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125700.png\" alt=\"rId16\" width=\"140\" height=\"74\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125787.png\" alt=\"rId17\" width=\"124\" height=\"78\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125876.png\" alt=\"rId18\" width=\"141\" height=\"76\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last. How would the paper look when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125971.png\" alt=\"rId19\" width=\"237\" height=\"81\"></p>",
                    question_hi: "<p>6. कागज की एक वर्गाकार शीट को दर्शाई गई दिशाओ में बिंदीदार रेखा पर अनुक्रमशः मोड़ा जाता है और आखिर में उसमें छेद किया जाता है। खोलने पर यह कागज कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551125971.png\" alt=\"rId19\" width=\"237\" height=\"81\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126082.png\" alt=\"rId20\" width=\"91\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126245.png\" alt=\"rId21\" width=\"91\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126342.png\" alt=\"rId22\" width=\"91\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126450.png\" alt=\"rId23\" width=\"91\" height=\"82\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126082.png\" alt=\"rId20\" width=\"91\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126245.png\" alt=\"rId21\" width=\"91\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126342.png\" alt=\"rId22\" width=\"90\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126450.png\" alt=\"rId23\" width=\"90\" height=\"81\"></p>"
                    ],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126342.png\" alt=\"rId22\" width=\"91\" height=\"85\"></p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126342.png\" alt=\"rId22\" width=\"91\" height=\"85\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. 80 is related to 99 following certain logic. Following the same logic, 0 is related to 3. To which of the following numbers is 35 related, following the same logic? (NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13-Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>7. किसी निश्चित तर्क के अनुसार 80 का संबंध 99 से है। उसी तर्क के अनुसार 0 का संबंध 3 से है। उसी तर्क के अनुसार 35 निम्नलिखित में से किस संख्या से संबंधित है?<strong>&nbsp;नोट:</strong> संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: [
                        "<p>46</p>",
                        "<p>44</p>",
                        "<p>42</p>",
                        "<p>48</p>"
                    ],
                    options_hi: [
                        "<p>46</p>",
                        "<p>44</p>",
                        "<p>42</p>",
                        "<p>48</p>"
                    ],
                    solution_en: "<p>7.(d)<br><strong>Logic:- </strong>(1<sup>st</sup> no +1) + 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>1</mn><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo><mo>+</mo><mn>1</mn></msqrt></math> = 2<sup>nd</sup>no. <br>(80, 99) :- (80 + 1) + 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>80</mn><mo>+</mo><mn>1</mn></msqrt></math> <br>&rArr; 81 + 2 &times; 9 &rArr; 81 + 18 = 99<br>(0, 3) :- (0 + 1) + 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>+</mo><mn>1</mn></msqrt></math> <br>&rArr; 1 + 2 &times; 1 &rArr; 1 + 2 = 3<br>similarly<br>(35, ?) :- (35 + 1) + 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>35</mn><mo>+</mo><mn>1</mn></msqrt></math> <br>&rArr; 36 + 2 &times; 6 &rArr; 36 + 12 = 48</p>",
                    solution_hi: "<p>7.(d)<br><strong>तर्क :-</strong> पहली संख्या +1) + 2 &times; <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>+</mo><mn>1</mn></msqrt></math> = दूसरी संख्या&nbsp;<br>(80, 99) :- (80 + 1) + 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>80</mn><mo>+</mo><mn>1</mn></msqrt></math> <br>&rArr; 81 + 2 &times; 9 &rArr; 81 + 18 = 99<br>(0, 3) :- (0 + 1) + 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>+</mo><mn>1</mn></msqrt></math> <br>&rArr; 1 + 2 &times; 1 &rArr; 1 + 2 = 3<br>इसी प्रकार <br>(35, ?) :- (35 + 1) + 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>35</mn><mo>+</mo><mn>1</mn></msqrt></math> <br>&rArr; 36 + 2 &times; 6 &rArr; 36 + 12 = 48</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain code language, &lsquo;PAID&rsquo; is coded as &lsquo;3274&rsquo; and &lsquo;APED&rsquo; is coded as<br>&lsquo;6437&rsquo;.What is the code for &lsquo;E&rsquo; in that language?</p>",
                    question_hi: "<p>8. एक निश्चित कूट भाषा में, \'PAID\' को \'3274\' के रूप में कूटबद्ध किया जाता है और \'APED\' को \'6437\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'E\' के लिए कूट क्या है?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>7</p>",
                        "<p>4</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>7</p>",
                        "<p>4</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>8.(a)&nbsp;<br>PAID :- 3274&hellip;..(i)<br>APED :- 6437&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;APD&rsquo; and &lsquo;347&rsquo; are common. The code of &lsquo;E&rsquo; = &lsquo;6&rsquo;.</p>",
                    solution_hi: "<p>8.(a)&nbsp;<br>PAID :- 3274&hellip;..(i)<br>APED :- 6437&hellip;&hellip;.(ii)<br>(i) और (ii) से &lsquo;APD&rsquo; और &lsquo;347&rsquo; उभयनिष्ठ हैं। &lsquo;E&rsquo; का कोड = &lsquo;6&rsquo; है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Three of the following four number triads are alike in a certain way and thus form a group. Which is the number triad that does not belong to that group?&nbsp;(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>9. निम्नलिखित चार संख्या त्रिकों में से तीन संख्या त्रिक किसी निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह संख्या त्रिक कौन-सा है, जो उस समूह से संबंधित नहीं है?<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>76 - 67 - 54</p>",
                        "<p>87 - 78 - 69</p>",
                        "<p>112 - 103 - 94</p>",
                        "<p>94 - 85 - 76</p>"
                    ],
                    options_hi: [
                        "<p>76 - 67 - 54</p>",
                        "<p>87 - 78 - 69</p>",
                        "<p>112 - 103 - 94</p>",
                        "<p>94 - 85 - 76</p>"
                    ],
                    solution_en: "<p>9.(a) <strong>Logic:-</strong> (1st number + 3rd number) &divide; 2 = 2nd number<br>(87 - 78 - 69):- (87 + 69) &divide; 2 &rArr; (156) &divide; 2 = 78<br>(112 - 103 - 94) :- (112 + 94) &divide; 2 &rArr; (206) &divide; 2 = 103<br>(94 - 85 - 76) :- (94 + 76) &divide; 2 &rArr; (170) &divide; 2 = 85<br>But, <br>(76 - 67 - 54) :- (76 + 54) &divide; 2 &rArr; (130) &divide; 2 = 65 &ne; 67</p>",
                    solution_hi: "<p>9.(a) <strong>तर्क:- </strong>(पहली संख्या + तीसरी संख्या) &divide; 2 = दूसरी संख्या<br>(87 - 78 - 69):- (87 + 69) &divide; 2 &rArr; (156) &divide; 2 = 78<br>(112 - 103 - 94) :- (112 + 94) &divide; 2 &rArr; (206) &divide; 2 = 103<br>(94 - 85 - 76) :- (94 + 76) &divide; 2 &rArr; (170) &divide; 2 = 85<br>लेकिन,<br>(76 - 67 - 54) :- (76 + 54) &divide; 2 &rArr; (130) &divide; 2 = 65 &ne; 67</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the option that represents the letters that, when sequentially placed from left to right in the blanks, will complete the letter series.<br>r _ m _ s _ t _ i _ m s s _ r i m _ _ st</p>",
                    question_hi: "<p>10. उस विकल्प का चयन करें, जो उन अक्षरों का प्रतिनिधित्व करता है, जो रिक्त स्थानों में क्रमिक रूप से<br>बाएँ से दाएँ रखे जाने पर दी गई अक्षर शृंखला को पूरा करेंगे।<br>r _ m _ s _ t _ i _ m s s _ r i m _ _ st</p>",
                    options_en: [
                        "<p>i m s r m t m s</p>",
                        "<p>i m s r s t s s</p>",
                        "<p>i s m r m t m s</p>",
                        "<p>i m s r t t m s</p>"
                    ],
                    options_hi: [
                        "<p>i m s r m t m s</p>",
                        "<p>i m s r s t s s</p>",
                        "<p>i s m r m t m s</p>",
                        "<p>i m s r t t m s</p>"
                    ],
                    solution_en: "<p>10.(a) r <span style=\"text-decoration: underline;\"><strong>i</strong></span> m<span style=\"text-decoration: underline;\"><strong>m</strong></span> s <span style=\"text-decoration: underline;\"><strong>s</strong></span> t / <span style=\"text-decoration: underline;\"><strong>r</strong></span> i <span style=\"text-decoration: underline;\"><strong>m</strong></span> m s s<strong><span style=\"text-decoration: underline;\">t</span></strong> / r i m <span style=\"text-decoration: underline;\"><strong>m s</strong></span> st</p>",
                    solution_hi: "<p>10.(a) r <span style=\"text-decoration: underline;\"><strong>i</strong></span> m<span style=\"text-decoration: underline;\"><strong>m</strong></span> s <span style=\"text-decoration: underline;\"><strong>s</strong></span> t / <span style=\"text-decoration: underline;\"><strong>r</strong></span> i <span style=\"text-decoration: underline;\"><strong>m</strong></span> m s s<strong><span style=\"text-decoration: underline;\">t</span></strong> / r i m <span style=\"text-decoration: underline;\"><strong>m s</strong></span> st</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>All oranges are apples.<br>All apples are kiwis.<br>Some apples are grapes.<br><strong>Conclusions:</strong><br>(I) Some kiwis are grapes.<br>(II) Some oranges are grapes.</p>",
                    question_hi: "<p>11. दिए गए कथनों और निष्कर्षों को ध्यान से पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष, कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथनः</strong><br>सभी संतरे, सेब हैं।<br>सभी सेब, कीवी हैं।<br>कुछ सेब, अंगूर हैं।<br><strong>निष्कर्षः</strong><br>(I) कुछ कीवी, अंगूर हैं।<br>(II) कुछ संतरे, अंगूर हैं।</p>",
                    options_en: [
                        "<p>Only conclusion II follows</p>",
                        "<p>Only conclusion I follows</p>",
                        "<p>Both conclusions I and II follow</p>",
                        "<p>Neither conclusion I nor II follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है</p>",
                        "<p>निष्कर्ष । और ।। दोनों अनुसरण करते हैं</p>",
                        "<p>न तो निष्कर्ष । और न ही ।। अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126607.png\" alt=\"rId24\" width=\"193\" height=\"77\"><br>Hence, the only conclusion I follow.</p>",
                    solution_hi: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126747.png\" alt=\"rId25\" width=\"203\" height=\"86\"><br>अतः, केवल निष्कर्ष I अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Which of the following numbers will replace the question mark (?) in the given series? <br>119&nbsp; &nbsp;171&nbsp; &nbsp;223&nbsp; &nbsp;275&nbsp; &nbsp;327&nbsp; &nbsp;?</p>",
                    question_hi: "<p>12. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?<br>119&nbsp; &nbsp;171&nbsp; &nbsp;223&nbsp; &nbsp;275&nbsp; &nbsp;327&nbsp; &nbsp;?</p>",
                    options_en: [
                        "<p>386</p>",
                        "<p>357</p>",
                        "<p>369</p>",
                        "<p>379</p>"
                    ],
                    options_hi: [
                        "<p>386</p>",
                        "<p>357</p>",
                        "<p>369</p>",
                        "<p>379</p>"
                    ],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126869.png\" alt=\"rId26\" width=\"260\" height=\"68\"></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126869.png\" alt=\"rId26\" width=\"260\" height=\"68\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. A $ B means &lsquo;A is the sister of B&rsquo;,<br>A @ B means &lsquo;A is the daughter of B&rsquo;,<br>A * B means &lsquo;A is the father of B&rsquo; and<br>A - B means &lsquo;A is the son of B&rsquo;.<br>Based on the above information, which of the following means that D is the daughter\'s husband of M?</p>",
                    question_hi: "<p>13. A $ B का अर्थ है कि \'A, B की बहन है\',<br>A @ B का अर्थ है कि \'A, B की पुत्री है\',<br>A * B का अर्थ है कि \'A, B का पिता है\' और <br>A - B का अर्थ है कि \'A, B का पुत्र है\'।<br>उपरोक्त जानकारी के आधार पर, निम्नलिखित में से किसका अर्थ यह है कि D, M की पुत्री का पति है?</p>",
                    options_en: [
                        "<p>D * R @ E $ A - M</p>",
                        "<p>D $ R - E * A @ M</p>",
                        "<p>D @ R - E $ A * M</p>",
                        "<p>D $ R * E - A @ M</p>"
                    ],
                    options_hi: [
                        "<p>D * R @ E $ A - M</p>",
                        "<p>D $ R - E * A @ M</p>",
                        "<p>D @ R - E $ A * M</p>",
                        "<p>D $ R * E - A @ M</p>"
                    ],
                    solution_en: "<p>13.(a)<br>After checking options one by one,<br>Only option (a) gives the correct result.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126991.png\" alt=\"rId27\" width=\"181\" height=\"140\"><br>We can see that &lsquo;D&rsquo; is the daughter&rsquo;s husband of &lsquo;M&rsquo;.</p>",
                    solution_hi: "<p>13.(a)<br>एक-एक करके विकल्पों की जाँच करने के बाद,<br>केवल विकल्प (a) सही परिणाम देता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551126991.png\" alt=\"rId27\" width=\"153\" height=\"118\"><br>हम देख सकते हैं कि \'D\', \'M\' की बेटी का पति है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the Venn diagram that best illustrates the relationship between the following classes.<br>Men, Engineers and Fathers</p>",
                    question_hi: "<p>14. उस वेन आरेख का चयन करें जो निम्&zwj;नलिखित वर्गों के बीच के संबंध को सर्वोत्&zwj;तम रूप से दर्शाता है। पुरुष, इंजीनियर और पिता</p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551127241.png\" alt=\"rId28\" width=\"88\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551127579.png\" alt=\"rId29\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551127823.png\" alt=\"rId30\" width=\"91\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551128182.png\" alt=\"rId31\" width=\"90\" height=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551127241.png\" alt=\"rId28\" width=\"87\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551127579.png\" alt=\"rId29\" width=\"91\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551127823.png\" alt=\"rId30\" width=\"92\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551128182.png\" alt=\"rId31\" width=\"90\" height=\"90\"></p>"
                    ],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551128385.png\" alt=\"rId32\" width=\"135\" height=\"101\"></p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551128492.png\" alt=\"rId33\" width=\"148\" height=\"109\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. A cube is constructed by folding the given sheet along the lines shown. In the cube so formed, what would be the symbol on the opposite side of \'#\'?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551128599.png\" alt=\"rId34\" width=\"96\" height=\"123\"></p>",
                    question_hi: "<p>15. दी गई शीट को दर्शाई गई रेखाओं के अनुदिश मोड़कर एक घन का निर्माण किया जाता है। इस प्रकार बनने वाले घन में, \'#\' के विपरीत फलक पर कौन-सा चिह्न होगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551128599.png\" alt=\"rId34\" width=\"96\" height=\"123\"></p>",
                    options_en: [
                        "<p>@</p>",
                        "<p><math display=\"inline\"><mi>&#8734;</mi></math></p>",
                        "<p>&euro;</p>",
                        "<p>%</p>"
                    ],
                    options_hi: [
                        "<p>@</p>",
                        "<p><math display=\"inline\"><mi>&#8734;</mi></math></p>",
                        "<p>&euro;</p>",
                        "<p>%</p>"
                    ],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551128766.png\" alt=\"rId35\" width=\"100\" height=\"126\"><br>The opposite faces are :- <br># &harr; &euro; , % &harr; &infin; , @ &harr; *</p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551128766.png\" alt=\"rId35\" width=\"100\" height=\"126\"><br>विपरीत फलक हैं:-<br># &harr; &euro; , % &harr; &infin; , @ &harr; *</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number results in the second number. Similarly, certain mathematical operation(s) on the second number in the third number and so on. Which of the given options follows the same set of operations as in the given sets?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding / subtracting /multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>79 - 71 - 65 - 61<br>84 - 76 - 70 - 66</p>",
                    question_hi: "<p>16. नीचे संख्याओं के दो समुच्चय दिए गए हैं। संख्याओं केप्रत्येक समुच्चय में, पहली संख्या पर कुछ गणितीय संक्रिया करने पर दूसरी संख्या प्राप्त होती है। इसी तरह, दूसरी संख्या पर कुछ गणितीय संक्रिया करने पर तीसरी संख्या प्राप्त होती है और इसी तरह आगे की संख्याएँ प्राप्त होती हैं। दिए गए विकल्पों में से किसमें, संक्रियाओं का वही सेट है, जैसा नीचे दिए गए समुच्चयों में है?<br>(नोट: संख्याओं को उनकेघटक अंकों में तोड़े बिना, संक्रियाएँ पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण केलिए 13 लीजिए - 13 पर संक्रिया जैसे कि 13 में जोड़ना/घटाना/गुणा करना 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रिया करना अनुमत नहीं है।)<br>79 - 71 - 65 - 61<br>84 - 76 - 70 - 66</p>",
                    options_en: [
                        "<p>93 - 85 - 79 - 75</p>",
                        "<p>70 - 62 - 54 - 50</p>",
                        "<p>64 - 56 - 40 - 36</p>",
                        "<p>99 - 92 - 86 - 81</p>"
                    ],
                    options_hi: [
                        "<p>93 - 85 - 79 - 75</p>",
                        "<p>70 - 62 - 54 - 50</p>",
                        "<p>64 - 56 - 40 - 36</p>",
                        "<p>99 - 92 - 86 - 81</p>"
                    ],
                    solution_en: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551128880.png\" alt=\"rId36\" width=\"155\" height=\"47\"> &nbsp;and&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129034.png\" alt=\"rId37\" width=\"152\" height=\"45\"><br>Similarly&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129210.png\" alt=\"rId38\" width=\"161\" height=\"48\"></p>",
                    solution_hi: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551128880.png\" alt=\"rId36\" width=\"155\" height=\"47\"> &nbsp;और <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129034.png\" alt=\"rId37\" width=\"152\" height=\"45\"><br>&nbsp;इसी प्रकार, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129210.png\" alt=\"rId38\" width=\"161\" height=\"48\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. P, Q, R, S, T and U are sitting around a circular table facing the centre. Only one person sits between T and R. Only one person sits between R and U. Only two people sit between U and Q . S sits to the immediate left of T. Who sits second to the left of P?</p>",
                    question_hi: "<p>17. P, Q, R, S, T और U एक वृत्ताकार मेज के चारो ओर केंद्र की ओर मुख करके बैठे हैं। T और R के बीच केवल एक व्यक्ति बैठा है। केवल एक व्यक्ति R और U के बीच बैठा है। U और Q के बीच केवल दो व्यक्ति बैठे हैं। S, T के ठीक बाएं बैठा है। P के बाएं से दूसरे स्थान पर कौन बैठा है?</p>",
                    options_en: [
                        "<p>U</p>",
                        "<p>Q</p>",
                        "<p>R</p>",
                        "<p>S</p>"
                    ],
                    options_hi: [
                        "<p>U</p>",
                        "<p>Q</p>",
                        "<p>R</p>",
                        "<p>S</p>"
                    ],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129384.png\" alt=\"rId39\" width=\"162\" height=\"124\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129384.png\" alt=\"rId39\" width=\"162\" height=\"124\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Which two numbers should be interchanged to make the given equation correct?<br>21 &times; 4 + (84 &divide; 7) &times; 2 - 15 &times; 5 + 42 = 105<br>(Note: Interchange should be done of entire number and not individual digits of a given number.)</p>",
                    question_hi: "<p>18. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए?<br>21 &times; 4 + (84 &divide; 7) &times; 2 - 15 &times; 5 + 42 = 105<br>(ध्यान दें: संपूर्ण संख्या को आपस में बदला जाना चाहिए, न कि दी गई संख्या के अलग-अलग अंकों को।)</p>",
                    options_en: [
                        "<p>4 and 2</p>",
                        "<p>84 and 42</p>",
                        "<p>4 and 5</p>",
                        "<p>7 and 21</p>"
                    ],
                    options_hi: [
                        "<p>4 और 2</p>",
                        "<p>84 और 42</p>",
                        "<p>4 और 5</p>",
                        "<p>7 और 21</p>"
                    ],
                    solution_en: "<p>18.(b)<br>Checking all the options given in the question one by one, option (b) gets satisfied.<br>21 &times; 4 + (84 &divide; 7) &times; 2 - 15 &times; 5 + 42 = 105<br>Interchanging 84 and 42 in the above expression, we have;<br>21 &times; 4 + (42 &divide; 7) &times; 2 - 15 &times; 5 + 84 = 105<br>84 + 12 - 75 + 84 = 105<br>105 = 105<br>LHS = RHS</p>",
                    solution_hi: "<p>18.(b)<br>एक -एक करके प्रश्न में दिए गए सभी विकल्पों की जाँच करने पर , विकल्प (b) संतुष्ट करता है।<br>21 &times; 4 + (84 &divide; 7) &times; 2 - 15 &times; 5 + 42 = 105<br>उपरोक्त समीकरण में 84 और 42 को आपस में बदलने पर <br>21 &times; 4 + (42 &divide; 7) &times; 2 - 15 &times; 5 + 84 = 105<br>84 + 12 - 75 + 84 = 105<br>105 = 105<br>LHS = RHS</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. The question contains pairs of words that are related to each other in a certain way. Three of the following four word pairs are alike as these have the same relationship and thus form a group. Which word pair is the one that DOES NOT belong to that group?<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters / number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>19. प्रश्न में ऐसे शब्द युग्म हैं जो एक निश्चित तरीके से एक-दूसरे से संबंधित हैं। निम्नलिखित चार शब्द युग्मों में से तीन शब्द युग्म एक समान हैं क्योंकि इनमें संबंध समान है और इस प्रकार वे एक समूह बनाते हैं। निम्नलिखित में से कौन-सा शब्द युग्म उस समूह से संबंधित नहीं है?<br>(शब्दों को अर्थपूर्ण अंग्रेजी/हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों / व्यंजनों / स्वरों की संख्या के आधार पर एक-दूसरे से संबंधित नहीं होने चाहिए।)</p>",
                    options_en: [
                        "<p>Bone &ndash; orthopaedic</p>",
                        "<p>Uterus &ndash; urologist</p>",
                        "<p>Stomach &ndash; gastroenterologist</p>",
                        "<p>Eye &ndash; ophthalmologist</p>"
                    ],
                    options_hi: [
                        "<p>हड्डी &ndash; अस्थि रोग विशेषज्ञ</p>",
                        "<p>गर्भाशय &ndash; मूत्र रोग विशेषज्ञ</p>",
                        "<p>पेट &ndash; जठरांत्र रोग विशेषज्ञ</p>",
                        "<p>आँख &ndash; नेत्र रोग विशेषज्ञ</p>"
                    ],
                    solution_en: "<p>19.(b) a urologist specializes in the urinary system and male reproductive organs, not the uterus. The correct specialist for uterus-related issues is a gynecologist. Thus, option (b) does not fit the pattern.</p>",
                    solution_hi: "<p>19.(b)&nbsp;एक मूत्र रोग विशेषज्ञ मूत्र प्रणाली और पुरुष प्रजनन अंगों में विशेषज्ञ होता है, गर्भाशय में नहीं। गर्भाशय से संबंधित समस्याओं के लिए सही विशेषज्ञ एक स्त्री रोग विशेषज्ञ है। इस प्रकार, विकल्प (b) पैटर्न में फिट नहीं बैठता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Each vowel in the word ICTERID is changed to the letter immediately following it in the English alphabetical order and each consonant is changed to the letter immediately preceding it in the English alphabetical order. How many letters will appear exactly twice in the group of letters thus formed?</p>",
                    question_hi: "<p>20. शब्द ICTERID में प्रत्येक स्वर को अंग्रेजी वर्णमाला क्रम के अनुसार उसके ठीक बाद वाले अक्षर में बदल दिया जाता है और प्रत्येक व्यंजन को अंग्रेजी वर्णमाला क्रम केअनुसार उसके ठीक पहले वाले अक्षर में बदल दिया जाता है। इस प्रकार बने अक्षरों के समूह में कितने अक्षर ठीक दो बार दिखाई देंगे?</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>20.(c)<br>Original word: ICTERID (I - C - T - E - R - I - D)<br>Applying the given rules:<br>Each vowel + 1 And Each consonant -1. <br>Here\'s the transformed word: J - B - S - F - Q - J - C. <br>No. of letters appears twice = 1 (&lsquo;J&rsquo;)</p>",
                    solution_hi: "<p>20.(c)<br>मूल शब्द: ICTERID (I - C - T - E - R - I - D)<br>नियमों को लागू करने के लिए:<br>प्रत्येक स्वर + 1 और प्रत्येक व्यंजन - 1 .<br>यहाँ परिवर्तित शब्द है: J - B - S - F - Q - J - C. <br>दो बार आने वाले अक्षरों की संख्या = 1 (&lsquo;J&rsquo;)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Identify the figure given in the options, which when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129512.png\" alt=\"rId40\" width=\"320\" height=\"66\"></p>",
                    question_hi: "<p>21. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे प्रश्न-चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129512.png\" alt=\"rId40\" width=\"320\" height=\"66\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129603.png\" alt=\"rId41\" width=\"91\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129707.png\" alt=\"rId42\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129806.png\" alt=\"rId43\" width=\"91\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129929.png\" alt=\"rId44\" width=\"90\" height=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129603.png\" alt=\"rId41\" width=\"91\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129707.png\" alt=\"rId42\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129806.png\" alt=\"rId43\" width=\"90\" height=\"89\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129929.png\" alt=\"rId44\" width=\"90\" height=\"90\"></p>"
                    ],
                    solution_en: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129929.png\" alt=\"rId44\" width=\"88\" height=\"88\"></p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551129929.png\" alt=\"rId44\" width=\"88\" height=\"88\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. In a code language, &lsquo;MARS&rsquo; is written as &lsquo;KXPP&rsquo;, and &lsquo;BODY&rsquo; is written as &lsquo;ZLBV&rsquo;. How will &lsquo;TEST&rsquo; be written in that language?</p>",
                    question_hi: "<p>22. एक कूट भाषा में &lsquo;MARS&rsquo; को &lsquo;KXPP&rsquo; के रूप में लिखा जाता है और &lsquo;BODY&rsquo; को &lsquo;ZLBV&rsquo; के रूप में लिखा जाता है। इसी कूट भाषा में &lsquo;TEST&rsquo; को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>QRQB</p>",
                        "<p>RBQQ</p>",
                        "<p>RQBQ</p>",
                        "<p>QQRB</p>"
                    ],
                    options_hi: [
                        "<p>QRQB</p>",
                        "<p>RBQQ</p>",
                        "<p>RQBQ</p>",
                        "<p>QQRB</p>"
                    ],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130040.png\" alt=\"rId45\" width=\"124\" height=\"101\">,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130155.png\" alt=\"rId46\" width=\"123\" height=\"100\"><br>Similarly,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130244.png\" alt=\"rId47\" width=\"134\" height=\"109\"></p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130040.png\" alt=\"rId45\" width=\"124\" height=\"101\">,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130155.png\" alt=\"rId46\" width=\"123\" height=\"100\"><br>इसी प्रकार,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130244.png\" alt=\"rId47\" width=\"134\" height=\"109\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. If \'P\' stands for \'&times;\', \'Q\' stands for \'&divide;\', \'R\' stands for \'-\' and \'S\' stands for \'+\', what will come in place of question mark &lsquo;?&rsquo; in the following equation?<br>15 P 6 R 11 P 4 S 38 R 41 S 2 P 5 = ?</p>",
                    question_hi: "<p>23. यदि \'P\' का अर्थ \'&times;\', \'Q\' का अर्थ \'&divide;\', \'R\' का अर्थ &lsquo;-&rsquo; और \'S\' का अर्थ \'+\' है, तो निम्नलिखित समीकरण में प्रश्न चिह्न ?\' के स्थान पर क्या आएगा?<br>15 P 6 R 11 P 4 S 38 R 41 S 2 P 5 = ?</p>",
                    options_en: [
                        "<p>61</p>",
                        "<p>41</p>",
                        "<p>49</p>",
                        "<p>53</p>"
                    ],
                    options_hi: [
                        "<p>61</p>",
                        "<p>41</p>",
                        "<p>49</p>",
                        "<p>53</p>"
                    ],
                    solution_en: "<p>23.(d) 15 P 6 R 11 P 4 S 38 R 41 S 2 P 5<br>As per given instruction change the letters with sign, we get,<br>15 &times; 6 - 11 &times; 4 + 38 - 41 + 2 &times; 5<br>90 - 44 + 38 - 41 + 10<br>138 - 85 = 53</p>",
                    solution_hi: "<p>23.(d) 15 P 6 R 11 P 4 S 38 R 41 S 2 P 5<br>दिए गए निर्देश के अनुसार अक्षरों को चिन्हों से बदलने पर, हमें प्राप्त होता है <br>15 &times; 6 - 11 &times; 4 + 38 - 41 + 2 &times; 5<br>90 - 44 + 38 - 41 + 10<br>138 - 85 = 53</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Rayan starts from Point A and drives 13 km towards East. He then takes a left turn, drives 5 km, turns left and drives 15 km. He then takes a left turn and drives 11 km. He takes a final left turn, drives 2 km and stops at Point P. How far (shortest distance) and towards which direction should he drive in order to reach Point A again? (All turns are 90 degree turns only unless specified.)</p>",
                    question_hi: "<p>24. रेयान बिंदु A से शुरू करता है और 13 km पूर्व की ओर ड्राइव करता है। फिर वह बाएं मुड़ता है, 5 km ड्राइव करता है, फिर बाएं मुड़ता है और 15 km ड्राइव करता है। फिर वह बाएं मुड़ता है और 11 km ड्राइव करता है। वह अंतिम बार बाएं मुड़ता है, 2 km ड्राइव करता है और बिंदु P पर रुकता है। बिंदु A पर फिर से पहुँचने के लिए उसे कितनी दूर। (न्यूनतम दूरी) और किस दिशा में ड्राइव करना चाहिए? (अन्यथा निर्दिष्ट न किए जाने की स्थिति में, सभी मोड़ केवल 90 डिग्री के मोड़ हैं।)</p>",
                    options_en: [
                        "<p>5 km to the South</p>",
                        "<p>5 km to the North</p>",
                        "<p>6 km to the North</p>",
                        "<p>6 km to the South</p>"
                    ],
                    options_hi: [
                        "<p>5 km, दक्षिण में</p>",
                        "<p>5 km, उत्तर में</p>",
                        "<p>6 km, उत्तर में</p>",
                        "<p>6 km, दक्षिण में</p>"
                    ],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130439.png\" alt=\"rId48\" width=\"148\" height=\"139\"><br>He should drive 6 km toward the North .</p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130439.png\" alt=\"rId48\" width=\"148\" height=\"139\"><br>उसे उत्तर की ओर 6 किमी गाड़ी चलानी चाहिए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. In a certain code language, &lsquo;BLEACHERS&rsquo; is coded as &lsquo;63&rsquo; and &lsquo;ABACAS&rsquo; is coded as &lsquo;42&rsquo;. How will &lsquo;FIZZY&rsquo; be coded in the same language?</p>",
                    question_hi: "<p>25. एक निश्चित कूट भाषा में \'BLEACHERS&rsquo; को \'63&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;ABACAS&rsquo; को &lsquo;42&rsquo; के रूप में कूटबद्ध किया जाता है। उस भाषा में \'FIZZY&rsquo; को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: [
                        "<p>44</p>",
                        "<p>45</p>",
                        "<p>35</p>",
                        "<p>52</p>"
                    ],
                    options_hi: [
                        "<p>44</p>",
                        "<p>45</p>",
                        "<p>35</p>",
                        "<p>52</p>"
                    ],
                    solution_en: "<p>25.(c)<br><strong>Logic:-</strong> no. of letters &times; 7 <br>BLEACHERS &rarr;&nbsp;9 letters &rArr; 9 &times; 7 = 63<br>ABACUS &rarr;&nbsp;&nbsp;6 letters &rArr; 6 &times; 7 = 42<br>FIZZY &rarr;&nbsp;&nbsp;5 letters &rArr; 5 &times; 7 = 35</p>",
                    solution_hi: "<p>25.(c) <br><strong>तर्क:- </strong>अक्षरों की संख्या &times; 7 <br>BLEACHERS &rarr;&nbsp;&nbsp;9 अक्षर &rArr; 9 &times; 7 = 63<br>ABACUS &rarr;&nbsp;&nbsp;6 अक्षर &rArr; 6 &times; 7 = 42<br>FIZZY &rarr;&nbsp;&nbsp;5 अक्षर &rArr; 5 &times; 7 = 35</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. With which of the following gharanas is Ustad Bade Ghulam Ali Khan associated ?</p>",
                    question_hi: "<p>26. उस्ताद बड़े गुलाम अली खान निम्नलिखित में से किस घराने से संबंधित हैं?</p>",
                    options_en: [
                        "<p>Kirana gharana</p>",
                        "<p>Patiala gharana</p>",
                        "<p>Agra gharana</p>",
                        "<p>Banaras gharana</p>"
                    ],
                    options_hi: [
                        "<p>किराना घराना</p>",
                        "<p>पटियाला घराना</p>",
                        "<p>आगरा घराना</p>",
                        "<p>बनारस घराना</p>"
                    ],
                    solution_en: "<p>26.(b) <strong>Patiala gharana.</strong> Ustad Bade Ghulam Ali Khan was awarded Sangeet Natak Akademi Award (1962), Sangeet Natak Akademi fellowship (1967), Padma Bhushan (1962). Famous Hindustani Gharanas in Music: Patiala Gharana- Fateh Ali Khan and Ustad Ali Baksh. Agra Gharana- Haji Sujan Khan and Ustad Ghaghe Khuda Baksh. Kirana Gharana- Firoz Dastur, Bhimsen Joshi and Ustad Bade Ali Khan.</p>",
                    solution_hi: "<p>26.(b) <strong>पटियाला घराना। </strong>उस्ताद बड़े गुलाम अली खान को संगीत नाटक अकादमी पुरस्कार (1962), संगीत नाटक अकादमी फ़ेलोशिप (1967), पद्म भूषण (1962) से सम्मानित किया गया। संगीत में प्रसिद्ध हिंदुस्तानी घराने: पटियाला घराना- फतेह अली खान और उस्ताद अली बख्श। आगरा घराना- हाजी सुजान खान और उस्ताद घाघे खुदा बख्श। किराना घराना- फ़िरोज़ दस्तूर, भीमसेन जोशी और उस्ताद बड़े अली खान।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which of the following palaces is located in Mysore, Karnataka?</p>",
                    question_hi: "<p>27. निम्नलिखित में से कौन-सा महल कर्नाटक के मैसूर में स्थित है?</p>",
                    options_en: [
                        "<p>Kowdiar Palace</p>",
                        "<p>Amba Vilas Palace</p>",
                        "<p>ujjayanta Palace</p>",
                        "<p>Kangla Palace</p>"
                    ],
                    options_hi: [
                        "<p>कौडियार महल</p>",
                        "<p>अंबा विलास महल</p>",
                        "<p>उज्जयंत महल</p>",
                        "<p>कंगला महल</p>"
                    ],
                    solution_en: "<p>27.(b) <strong>Amba Vilas Palace,</strong> constructed in Indo-Saracenic style between 1897-1912. Kowdiar Palace in Thiruvananthapuram, Kerala. Ujjayanta Palace in Tripura. Kangla Palace in Imphal, Manipur.</p>",
                    solution_hi: "<p>27.(b) <strong>अंबा विलास महल</strong>, अंबा विलास महल, 1897-1912 के बीच इंडो-सारसेनिक शैली में निर्मित। केरल के तिरुवनंतपुरम में कौडियार महल। त्रिपुरा में उज्जयंता महल। इम्फाल, मणिपुर में कांगला महल।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The Prarthana Samaj was established in ________ by Dr. Atma Ram Pandurang.</p>",
                    question_hi: "<p>28. प्रार्थना समाज की स्थापना डॉ. आत्माराम पांडुरंग द्वारा ________ में की गई थी।</p>",
                    options_en: [
                        "<p>Bombay</p>",
                        "<p>Calcutta</p>",
                        "<p>Adyar</p>",
                        "<p>Delhi</p>"
                    ],
                    options_hi: [
                        "<p>बॉम्बे</p>",
                        "<p>कलकत्ता</p>",
                        "<p>अड्यार</p>",
                        "<p>दिल्ली</p>"
                    ],
                    solution_en: "<p>28.(a) <strong>Bombay. </strong>The Prarthana Samaj (Founded - 1867) started Subodhapatrika newspaper for social awareness. Arya Samaj was founded in Bombay in 1875 by Swami Dayananda Saraswati. The Brahmo Samaj was founded in Calcutta in 1828 by Ram Mohan Roy.</p>",
                    solution_hi: "<p>28.(a)<strong> बॉम्बे।</strong> प्रार्थना समाज (स्थापना - 1867) ने सामाजिक जागरूकता के लिए सुबोधपत्रिका अखबार शुरू किया। आर्य समाज की स्थापना 1875 में स्वामी दयानंद सरस्वती ने बॉम्बे में की थी। ब्रह्म समाज की स्थापना 1828 में राम मोहन राय ने कलकत्ता में की थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. What kind of phyllotaxy was examined in the Guava plant?</p>",
                    question_hi: "<p>29. अमरूद के पौधे में किस प्रकार का पर्णविन्यास पाया जाता है?</p>",
                    options_en: [
                        "<p>Superimposed</p>",
                        "<p>Whorled</p>",
                        "<p>Alternate</p>",
                        "<p>Opposite</p>"
                    ],
                    options_hi: [
                        "<p>अध्यारोपित</p>",
                        "<p>चक्करदार</p>",
                        "<p>एकांतर</p>",
                        "<p>विपरीत</p>"
                    ],
                    solution_en: "<p>29.(d) <strong>Opposite.</strong> Phyllotaxy refers to the arrangement of leaves on the stem or branch of a plant. In an opposite leaf arrangement, two leaves arise at the same point, with the leaves connecting opposite each other along the branch. If there are three or more leaves connected at a node, the leaf arrangement is classified as whorled.</p>",
                    solution_hi: "<p>29.(d) <strong>विपरीत।</strong> पर्णविन्यास (Phyllotaxy) का तात्पर्य पौधे के तने या शाखा पर पत्तियों की व्यवस्था से है। विपरीत पत्ती व्यवस्था में, दो पत्तियाँ एक ही बिंदु पर उगती हैं, जिसमें पत्तियाँ शाखा के साथ एक दूसरे के विपरीत जुड़ती हैं। यदि एक नोड पर तीन या अधिक पत्तियाँ जुड़ी हुई हैं, तो पत्ती व्यवस्था को चक्राकार (whorled) के रूप में वर्गीकृत किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Who became the first Indian prime minister to make a state visit to Ukraine?</p>",
                    question_hi: "<p>30. यूक्रेन की राज्य यात्रा करने वाले पहले भारतीय प्रधानमंत्री कौन बने ?</p>",
                    options_en: [
                        "<p>Jawaharlal Nehru</p>",
                        "<p>Atal Bihari Vajpayee</p>",
                        "<p>Narendra Modi</p>",
                        "<p>Manmohan Singh</p>"
                    ],
                    options_hi: [
                        "<p>जवाहरलाल नेहरू</p>",
                        "<p>अटल बिहारी वाजपेयी</p>",
                        "<p>नरेंद्र मोदी</p>",
                        "<p>मनमोहन सिंह</p>"
                    ],
                    solution_en: "<p>30.(c) <strong>Narendra Modi. </strong>This visit marked a historic moment, as it was the first time an Indian Prime Minister visited Ukraine since the establishment of diplomatic relations between the two countries in 1992.</p>",
                    solution_hi: "<p>30.(c) <strong>नरेंद्र मोदी। </strong>यह यात्रा ऐतिहासिक थी क्योंकि यह पहली बार था जब 1992 में दोनों देशों के बीच राजनयिक संबंध स्थापित होने के बाद किसी भारतीय प्रधानमंत्री ने यूक्रेन की यात्रा की।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Mahatma Gandhi National Rural Employment Guarantee Act (2005) provides work for how many days?</p>",
                    question_hi: "<p>31. महात्मा गांधी राष्ट्रीय ग्रामीण रोजगार गारंटी अधिनियम (2005) कितने दिनों के लिए कार्य प्रदान करता है?</p>",
                    options_en: [
                        "<p>300</p>",
                        "<p>200</p>",
                        "<p>100</p>",
                        "<p>125</p>"
                    ],
                    options_hi: [
                        "<p>300</p>",
                        "<p>200</p>",
                        "<p>100</p>",
                        "<p>125</p>"
                    ],
                    solution_en: "<p>31.(c) <strong>100.</strong> The Mahatma Gandhi National Rural Employment Guarantee Act (MGNREGA), passed in September 2005, is a labor law that guarantees the \'Right to Work\' and was officially launched on 2 February 2006. Initially proposed by P.V. Narasimha Rao in 1991, its name was changed from NREGA to MGNREGA on 2 October 2009.</p>",
                    solution_hi: "<p>31.(c) <strong>100.</strong> सितंबर 2005 में पारित महात्मा गांधी राष्ट्रीय ग्रामीण रोजगार गारंटी अधिनियम (MGNREGA) एक श्रम कानून है जो \'काम के अधिकार\' की गारंटी देता है और इसे आधिकारिक तौर पर 2 फरवरी 2006 को लॉन्च किया गया था। शुरुआत में 1991 में पी.वी. नरसिम्हा राव द्वारा प्रस्तावित, इसका नाम 2 अक्टूबर 2009 को NREGA से बदलकर MGNREGA कर दिया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Name the high-intensity tri-services exercise conducted by the Indian Army from 10th to 18th November 2024.</p>",
                    question_hi: "<p>32. 10 से 18 नवंबर 2024 तक भारतीय सेना द्वारा आयोजित उच्च-तीव्रता त्रिसेवा अभ्यास का नाम क्या था?</p>",
                    options_en: [
                        "<p>Tiger Triumph</p>",
                        "<p>INDRA</p>",
                        "<p>Poorvi Prahar</p>",
                        "<p>Vajra Prahar</p>"
                    ],
                    options_hi: [
                        "<p>टाइगर ट्रायंफ</p>",
                        "<p>INDRA</p>",
                        "<p>पूर्वी प्रहार</p>",
                        "<p>वज्र प्रहार</p>"
                    ],
                    solution_en: "<p>32.(c) <strong>Poorvi Prahar</strong>. This joint exercise aims to sharpen the combat effectiveness of the Indian Army, Navy, and Air Force in implementing Integrated Joint Operations in the difficult mountainous terrain of the region. The exercise, Poorvi Prahar, will involve innovative technological advancements such as, First Person View (FPV) Drones, and Loiter Munitions.</p>",
                    solution_hi: "<p>32.(c) <strong>पूर्वी प्रहार। </strong>यह संयुक्त अभ्यास भारतीय सेना, नौसेना और वायु सेना की मुकाबला क्षमता को बढ़ाने के लिए आयोजित किया गया था। यह विशेष रूप से कठिन पर्वतीय क्षेत्र में एकीकृत संयुक्त संचालन की क्षमता को बेहतर बनाने का उद्देश्य था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. \"Who moved my interest rate\' is the autobiography of D Subbarao who was a__________ .</p>",
                    question_hi: "<p>33. \'हू मूव्ड माई इंटरेस्ट रेट\' डी. सुब्बाराव की आत्मकथा हैजो एक ______ थे।</p>",
                    options_en: [
                        "<p>journalist</p>",
                        "<p>political leader</p>",
                        "<p>businessman</p>",
                        "<p>banker</p>"
                    ],
                    options_hi: [
                        "<p>पत्रकार</p>",
                        "<p>राजनीतिक नेता</p>",
                        "<p>व्यवसायी</p>",
                        "<p>बैंकर</p>"
                    ],
                    solution_en: "<p>33.(d) <strong>banker.</strong> Duvvuri Subbarao was the 22nd Governor of the Reserve Bank of India. Other RBI governor&rsquo;s books: Raghuram Govind Rajan - &ldquo;I Do What I Do&rdquo;, &ldquo;Fault Lines&rdquo;. Manmohan Singh - &ldquo;Changing India&rdquo;, &ldquo;Collateral and Financial Plumbing&rdquo;. Yaga Venugopal Reddy - &ldquo;India and The Global Financial Crisis&rdquo;.</p>",
                    solution_hi: "<p>33.(d) <strong>बैंकर।</strong> दुव्वुरी सुब्बाराव भारतीय रिजर्व बैंक के 22वें गवर्नर थे । अन्य RBI गवर्नर की पुस्तकें: रघुराम गोविंद राजन - \"आई डू व्हॉट आई डू&rdquo;, \"फॉल्ट लाइन्स\"। मनमोहन सिंह - \"चेंजिंग इंडिया\", \"कोलैटरल एंड फाइनेंसियल प्लंबिंग\"। यागा वेणुगोपाल रेड्डी - \"इंडिया एंड द ग्लोबल फाइनेंसियल क्राइसिस\"।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. With reference to the Directive Principles of State policy contained in the Constitution of India, which of the following statements is/are correct?<br>1.Article 37 of the Constitution of India provides that the Directive Principles of State Policy are enforceable in the High Courts only.<br>2.Article 38 provides that the State shall strive to minimise the inequalities in income amongst individuals and groups of people.<br>3.Article 39 provides that the State shall direct its policy towards securing that there is equal pay for equal work for both men and women.</p>",
                    question_hi: "<p>34. भारत के संविधान में निहित राज्य नीति के निदेशक तत्वों के संदर्भ में, निम्नलिखित में से कौन सा/से कथन सही है/हैं?<br>1.भारत के संविधान के अनुच्छेद 37 में यह प्रावधान है कि राज्य के नीति निदेशक तत्व केवल उच्च न्यायालयों में लागू करने योग्य हैं।<br>2.अनुच्छेद 38 में यह प्रावधान है कि राज्य व्यक्तियों और लोगों के समूहों के बीच आय में असमानताओं को कम करने का प्रयास करेगा।<br>3.अनुच्छेद 39 में यह प्रावधान है कि राज्य अपनी नीति को यह सुनिश्चित करने की दिशा में निर्देशित करेगा कि पुरुषों और महिलाओं दोनों के लिए समान कार्य के लिए समान वेतन हो।</p>",
                    options_en: [
                        "<p>Only 2 and 3</p>",
                        "<p>Only 1 and 3</p>",
                        "<p>Only 2</p>",
                        "<p>Only 1</p>"
                    ],
                    options_hi: [
                        "<p>केवल 2 और 3</p>",
                        "<p>केवल 1 और 3</p>",
                        "<p>केवल 2</p>",
                        "<p>केवल 1</p>"
                    ],
                    solution_en: "<p>34.(a) <strong>Only 2 and 3.</strong> Article 37 of the Constitution of India provides that the Directive Principles of State Policy (DPSP, Part IV - Article 36 to 51) are not enforceable in any court.</p>",
                    solution_hi: "<p>34.(a) <strong>केवल 2 और 3.</strong> भारत के संविधान का अनुच्छेद 37 यह प्रावधान करता है कि राज्य नीति के निर्देशक सिद्धांत (DPSP, भाग IV - अनुच्छेद 36 से 51) किसी भी न्यायालय में लागू नहीं होंगे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. With how many countries does India share a sea border?</p>",
                    question_hi: "<p>35. भारत कितने देशों के साथ समुद्री सीमा साझा करता है?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>8</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>8</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>35.(d) <strong>2 </strong>(Sri Lanka and Maldives). Sri Lanka is separated from India by a narrow channel of sea formed by the Palk Strait and the Gulf of Mannar, while Maldives Islands are situated to the south of the Lakshadweep Islands. India shares its land borders with seven countries: Pakistan and Afghanistan to the northwest, China (Tibet), Nepal and Bhutan to the north, Myanmar and Bangladesh to the east.</p>",
                    solution_hi: "<p>35.(d) <strong>2</strong> (श्रीलंका और मालदीव)। श्रीलंका पाक जलडमरूमध्य और मन्नार की खाड़ी द्वारा निर्मित समुद्र के एक संकीर्ण चैनल द्वारा भारत से अलग होता है, जबकि मालदीव द्वीप, लक्षद्वीप द्वीप समूह के दक्षिण में स्थित हैं। भारत अपनी स्थलीय सीमाएँ सात देशों के साथ साझा करता है: उत्तर-पश्चिम में पाकिस्तान एवं अफगानिस्तान, उत्तर में चीन (तिब्बत), नेपाल और भूटान, तथा पूर्व में म्यांमार और बांग्लादेश।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Who among the following was primarily an integral part of the Indian National Army (INA)?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन मुख्य रूप से भारतीय राष्ट्रीय सेना (INA) के अभिन्न अंग थे?</p>",
                    options_en: [
                        "<p>Rabindranath Tagore</p>",
                        "<p>Subhas Chandra Bose</p>",
                        "<p>Chittaranjan Das</p>",
                        "<p>Abanindranath Tagore</p>"
                    ],
                    options_hi: [
                        "<p>रवीन्द्र नाथ टैगोर</p>",
                        "<p>सुभाष चंद्र बोस</p>",
                        "<p>चितरंजन दास</p>",
                        "<p>अवनींद्रनाथ टैगोर</p>"
                    ],
                    solution_en: "<p>36.(b) <strong>Subhas Chandra Bose. </strong>Indian National Army (INA) was a collaborationist armed unit of Indian collaborators that fought under the command of the Japanese Empire. It was founded by Mohan Singh in September 1942 in Southeast Asia during World War II. Some notable people associated: Mohan Singh, Habib ur Rahman, Lt. Col Hori Lal Verma.</p>",
                    solution_hi: "<p>36.(b)<strong> सुभाष चंद्र बोस। </strong>इंडियन नेशनल आर्मी (INA) भारतीय सहयोगियों की एक सहयोगी सशस्त्र यूनिट थी जो जापानी साम्राज्य की कमान के तहत लड़ी थी। इसकी स्थापना मोहन सिंह ने सितंबर 1942 में द्वितीय विश्व युद्ध के दौरान दक्षिण पूर्व एशिया में की थी। इससे जुड़े कुछ प्रसिद्ध व्यक्तित्व: मोहन सिंह, हबीब उर रहमान, लेफ्टिनेंट कर्नल होरी लाल वर्मा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which of the following is a popular festival celebrated in the state of Bihar ?</p>",
                    question_hi: "<p>37. निम्नलिखित में से कौन सा बिहार राज्य में मनाया जाने वाला एक लोकप्रिय त्योहार है?</p>",
                    options_en: [
                        "<p>Me-Dam-Me-Phi</p>",
                        "<p>Sama Chakeva</p>",
                        "<p>Harela</p>",
                        "<p>Ali-Ai-Ligang</p>"
                    ],
                    options_hi: [
                        "<p>मे-दम-मे-फि (Me-Dam-Me-Phi)</p>",
                        "<p>सामा चकेवा (Sama Chakeva)</p>",
                        "<p>हरेला (Harela)</p>",
                        "<p>अली-ऐ-लिगांग (Ali-Ai-Ligang)</p>"
                    ],
                    solution_en: "<p>37.(b) <strong>Sama Chakeva.</strong> It is a festival deeply rooted in the Maithili community in India, and Tharu community in Nepal. Festivals in India: Assam - Baishagu, Ali-Ai-Ligang, Baikho, Rongker, Rajini Gabra Harni Gabra, Bohaggiyo Bishu, Ambubashi Mela and Jonbill Mela. Uttarakhand - Harela. Tamil Nadu - Pongal. Kerala - Onam.</p>",
                    solution_hi: "<p>37.(b) <strong>सामा चकेवा (Sama Chakeva)। </strong>यह भारत में मैथिली समुदाय और नेपाल में थारू समुदाय में मूलरूप से निहित एक त्योहार है। भारत में त्यौहार: असम - बैशागु, अली-ऐ-लिगांग, बैखो, रोंगकेर, रजनी गबरा हरनी गबरा, बोहागियो बिशु, अंबुबाची मेला और जोनबिल मेला। उत्तराखंड - हरेला। तमिलनाडु - पोंगल। केरल - ओणम ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. What is the unit of specific resistance?</p>",
                    question_hi: "<p>38. विशिष्ट प्रतिरोध का मात्रक क्या होता है?</p>",
                    options_en: [
                        "<p>Farad</p>",
                        "<p>Ampere</p>",
                        "<p>Coulomb</p>",
                        "<p>Ohm meter</p>"
                    ],
                    options_hi: [
                        "<p>फैरड</p>",
                        "<p>एंपियर</p>",
                        "<p>कूलॉम</p>",
                        "<p>ओम मीटर</p>"
                    ],
                    solution_en: "<p>38.(d) <strong>Ohm meter. </strong>Specific resistance is the resistance per unit length and cross-sectional area when a known voltage is applied. Farad is the unit of capacitance, and a capacitor has 1 F when 1 coulomb (C) of charge changes the potential between its plates by 1 volt (V). The ampere (A) is the SI unit of electric current. Coulomb (C) is the standard unit of electric charge in the International System of Units (SI).</p>",
                    solution_hi: "<p>38.(d) <strong>ओम मीटर। </strong>विशिष्ट प्रतिरोध एक ज्ञात वोल्टेज लागू होने पर प्रति इकाई लंबाई और क्रॉस-सेक्शनल क्षेत्र का प्रतिरोध है। फैराड धारिता की इकाई है, और एक संधारित्र में 1 F होता है जब 1 कूलम्ब (C) आवेश इसकी प्लेटों के बीच के विभव को 1 वोल्ट (V) से बदल देता है। एम्पीयर (A) विद्युत धारा की SI इकाई है। कूलम्ब (C) अंतर्राष्ट्रीय इकाइयों की प्रणाली (SI) में विद्युत आवेश की मानक इकाई है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Lai Haraoba, which literally means &lsquo;the festival of gods&rsquo;, is primarily celebrated in which state of India?</p>",
                    question_hi: "<p>39. लाई हराओबा, जिसका शाब्दिक अर्थ \'देवताओं का त्योहार\' है, मुख्य रूप से भारत के किस राज्य में मनाया जाता है?</p>",
                    options_en: [
                        "<p>Kerala</p>",
                        "<p>Uttarakhand</p>",
                        "<p>Himachal Pradesh</p>",
                        "<p>Manipur</p>"
                    ],
                    options_hi: [
                        "<p>केरल</p>",
                        "<p>उत्तराखंड</p>",
                        "<p>हिमाचल प्रदेश</p>",
                        "<p>मणिपुर</p>"
                    ],
                    solution_en: "<p>39.(d) <strong>Manipur.</strong> Lai Haraoba festival: It is an important festival of the Meitei tribe. Other festivals of Manipur - Yaoshang, Cheiraoba (The Manipuri New Year), Kang (The Ratha Yatra of Manipur), Heikru Hitongba (Boat race). Kerala - Onam, Theyyam, Attukal Pongala, Makaravilakku. Uttarakhand - Harela, Bhitauli, Phool Dei, Uttarayani, Himachal Pradesh - Halda, Jagra, Kullu Dussehra.</p>",
                    solution_hi: "<p>39.(d) <strong>मणिपुर।</strong> लाई हराओबा त्यौहार: यह मैतेई जनजाति का एक महत्वपूर्ण त्यौहार है। मणिपुर के अन्य त्योहार - याओशांग, चेइराओबा (मणिपुरी नव वर्ष), कांग (मणिपुर की रथ यात्रा), हेइक्रू हितोंगबा (नाव दौड़)। केरल - ओणम, थेय्यम, अट्टुकल पोंगाला, मकरविलक्कू। उत्तराखंड - हरेला, भिटौली, फूल देई, उत्तरायणी। हिमाचल प्रदेश - हलदा, जागरा, कुल्लू दशहरा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which of the following is a non-perishable food?</p>",
                    question_hi: "<p>40. निम्नलिखित में से कौन-सा अविकारीय भोजन है?</p>",
                    options_en: [
                        "<p>Pulses</p>",
                        "<p>Meat</p>",
                        "<p>Milk</p>",
                        "<p>Curds</p>"
                    ],
                    options_hi: [
                        "<p>दाल</p>",
                        "<p>मांस</p>",
                        "<p>दूध</p>",
                        "<p>दही</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Pulses. </strong>Non-perishable foods are items that can be stored at room temperature for extended periods without spoiling. Examples include beans, coffee, honey, powdered milk, rice, and wheat. In contrast, frozen foods are not classified as non-perishable.</p>",
                    solution_hi: "<p>40.(a) <strong>दाल।</strong> अविकारीय खाद्य पदार्थ वे पदार्थ हैं जिन्हें बिना खराब हुए लंबे समय तक कमरे के तापमान पर रखा जा सकता है। उदाहरणों में बीन्स, कॉफी, शहद, पाउडर वाला दूध, चावल और गेहूं शामिल हैं। इसके विपरीत, फ्रोज़ेन खाद्य पदार्थ को अविकारीय के रूप में वर्गीकृत नहीं किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. In which year was Aatmanirbhar Bharat Rojgar Yojana launched?</p>",
                    question_hi: "<p>41. आत्मनिर्भर भारत रोजगार योजना किस वर्ष शुरू की गई थी?</p>",
                    options_en: [
                        "<p>2018</p>",
                        "<p>2020</p>",
                        "<p>2017</p>",
                        "<p>2019</p>"
                    ],
                    options_hi: [
                        "<p>2018</p>",
                        "<p>2020</p>",
                        "<p>2017</p>",
                        "<p>2019</p>"
                    ],
                    solution_en: "<p>41.(b) <strong>2020. </strong>Aatmanirbhar Bharat Rojgar Yojana (ABRY) : A Central Sector Scheme launched to boost job creation in the formal sector, particularly after the economic downturn caused by the COVID-19 pandemic. Maharashtra has topped the list of states with a maximum number of beneficiaries under this scheme.</p>",
                    solution_hi: "<p>41.(b) <strong>2020 </strong>। आत्मनिर्भर भारत रोजगार योजना (ABRY): कोविड-19 महामारी के कारण आर्थिक मंदी के बाद औपचारिक क्षेत्र में रोजगार सृजन को बढ़ावा देने के लिए शुरू की गई एक केंद्रीय क्षेत्र योजना है। इस योजना के तहत सबसे अधिक लाभार्थियों वाले राज्यों की सूची में महाराष्ट्र शीर्ष पर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following is the darkest, or least reflective object in the Solar System with an albedo of 0.03 ?</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन-सा विकल्प, 0.03 अल्बेडो के साथ सौर मंडल में सबसे काला (dark) या सबसे कम परावर्तक वस्तु है?</p>",
                    options_en: [
                        "<p>9P/Tempel 1</p>",
                        "<p>2P/Encke</p>",
                        "<p>1P/Halley</p>",
                        "<p>19P/Borrelly</p>"
                    ],
                    options_hi: [
                        "<p>9P/टेम्पल 1 (9P/Tempel 1)</p>",
                        "<p>2P/एंकके (2P/Encke)</p>",
                        "<p>1P/हैली (1P/Halley)</p>",
                        "<p>19P/बोरेली (19P/Borrelly) </p>"
                    ],
                    solution_en: "<p>42.(c) <strong>1P/Halley.</strong> Comet Halley takes about 76 years to orbit the Sun once. It has an albedo of 0.03, which means that it reflects only 3% of the light that falls on it. It is named for Edmond Halley, who discovered it in 1705. Comet 19P/Borrelly was discovered by Alphonse Louis Nicolas Borrelly in 1904. Comet 2P/Encke was first discovered by Pierre F. A. Mechain in 1786.</p>",
                    solution_hi: "<p>42.(c) <strong>1P/हैली।</strong> धूमकेतु हैली को सूर्य की एक परिक्रमा करने में लगभग 76 वर्ष लगते हैं। इसका अल्बेडो 0.03 है, जिसका अर्थ है कि यह अपने ऊपर पड़ने वाले प्रकाश का केवल 3% ही परावर्तित करता है। इसका नाम एडमंड हैली के नाम पर रखा गया है, जिन्होंने इसे 1705 में खोजा था। धूमकेतु 19P/बोरेल्ली की खोज अल्फोंस लुइस निकोलस बोर्रेली ने 1904 में की थी। धूमकेतु 2P/एनके की खोज सबसे पहले 1786 में पियरे एफ.ए. मेचेन ने की थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which of the following is NOT a condition for acquiring the citizenship of India?</p>",
                    question_hi: "<p>43. निम्नलिखित में से कौन-सी भारत की नागरिकता प्राप्त करने की एक शर्त नहीं है?</p>",
                    options_en: [
                        "<p>Registration</p>",
                        "<p>Descent</p>",
                        "<p>Domicile</p>",
                        "<p>Holding property</p>"
                    ],
                    options_hi: [
                        "<p>पंजीकरण</p>",
                        "<p>मूलनिवास</p>",
                        "<p>वंशज</p>",
                        "<p>संपत्ति धारण करना</p>"
                    ],
                    solution_en: "<p>43.(d) <strong>Holding property.</strong> The Citizenship Act of 1955 is the fundamental law governing the acquisition and termination of Indian citizenship. It outlines five ways to become an Indian citizen: birth, descent, registration, naturalization, and incorporation of territory. Citizenship (Amendment) Act, 2019: It provides an accelerated pathway for the citizenship of the members of six communities (Hindus, Sikhs, Buddhists, Jains, Parsis, and Christians) from Pakistan, Bangladesh, and Afghanistan if they have arrived before December 31, 2014.</p>",
                    solution_hi: "<p>43.(d) <strong>संपत्ति धारण करना। </strong>नागरिकता अधिनियम 1955 भारतीय नागरिकता के अधिग्रहण और समाप्ति को नियंत्रित करने वाला मौलिक कानून है। इसमें भारतीय नागरिक बनने के पाँच तरीके बताए गए हैं: जन्म, वंश, पंजीकरण, प्राकृतिककरण और क्षेत्र का समावेश। नागरिकता (संशोधन) अधिनियम, 2019: यह पाकिस्तान, बांग्लादेश और अफ़गानिस्तान से आए छह समुदायों (हिंदू, सिख, बौद्ध, जैन, पारसी और ईसाई) के सदस्यों के लिए नागरिकता के लिए एक त्वरित मार्ग प्रदान करता है, अगर वे 31 दिसंबर, 2014 से पहले आए हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. What is the main objective of Fiscal Responsibility and Budget Management Act (FRBMA), 2003?</p>",
                    question_hi: "<p>44. राजकोषीय उत्तरदायित्व और बजट प्रबंधन अधिनियम (FRBMA), 2003 का मुख्य उद्देश्य क्या है?</p>",
                    options_en: [
                        "<p>To increase exports</p>",
                        "<p>To increase excise duty</p>",
                        "<p>To reduce fiscal deficit</p>",
                        "<p>To reduce subsidies</p>"
                    ],
                    options_hi: [
                        "<p>निर्यात बढ़ाना</p>",
                        "<p>उत्पाद शुल्क बढ़ाना</p>",
                        "<p>राजकोषीय घाटे को कम करना</p>",
                        "<p>अनुदान कम करना</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>To reduce fiscal deficit.</strong> The Fiscal Responsibility and Budget Management (FRBM) Act is a law enacted by the Government of India in 2003 to ensure fiscal discipline &ndash; by setting targets including reduction of fiscal deficits and elimination of revenue deficit. Enacted - 26 August 2003. Introduced by - Mr.Yashwant Sinha.</p>",
                    solution_hi: "<p>44.(c) <strong>राजकोषीय घाटे को कम करना।</strong> राजकोषीय उत्तरदायित्व और बजट प्रबंधन (FRBM) अधिनियम, भारत सरकार द्वारा 2003 में राजकोषीय अनुशासन सुनिश्चित करने के लिए बनाया गया एक कानून है जिसका उद्देश्य राजकोषीय घाटे में कमी और राजस्व घाटे को समाप्त करने सहित लक्ष्य निर्धारित करके राजकोषीय अनुशासन सुनिश्चित करना है। अधिनियमित - 26 अगस्त 2003। श्री यशवंत सिन्हा द्वारा प्रस्तुत किया गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. With which sport is the Burdwan Trophy associated?</p>",
                    question_hi: "<p>45. बर्दवान ट्रॉफी का संबंध किस खेल से है?</p>",
                    options_en: [
                        "<p>Powerlifting</p>",
                        "<p>Weightlifting</p>",
                        "<p>Boxing</p>",
                        "<p>Wrestling</p>"
                    ],
                    options_hi: [
                        "<p>पावर लिफ्टिंग</p>",
                        "<p>भारोत्तोलन</p>",
                        "<p>मुक्केबाज़ी</p>",
                        "<p>कुश्ती</p>"
                    ],
                    solution_en: "<p>45.(b) <strong>Weightlifting.</strong> Sports and Trophies : Badminton - Agarwal Cup, Amrit Diwan Cup, Asia Cup. Basketball - Basalat Jha Trophy, B. C. Gupta Trophy, Federation Cup. Boxing - Aspy Adjahia Trophy, Federation Cup, Val iBaker Trophy.</p>",
                    solution_hi: "<p>45.(b) <strong>भारोत्तोलन।</strong> खेल और ट्राफियां : बैडमिंटन - अग्रवाल कप, अमृत दीवान कप, एशिया कप। बास्केटबॉल - बसालत झा ट्रॉफी, बी. सी. गुप्ता ट्रॉफी, फेडरेशन कप। बॉक्सिंग - एस्पी अदजाहिया ट्रॉफी, फेडरेशन कप, वैल आईबेकर ट्रॉफी।&rsquo;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. What is observed annually on December 4?</p>",
                    question_hi: "<p>46. 4 दिसंबर को प्रतिवर्ष क्या मनाया जाता है?</p>",
                    options_en: [
                        "<p>International Wildlife Day</p>",
                        "<p>International Cheetah Day</p>",
                        "<p>World Endangered Species Day</p>",
                        "<p>Global Conservation Day</p>"
                    ],
                    options_hi: [
                        "<p>अंतर्राष्ट्रीय वन्यजीव दिवस</p>",
                        "<p>अंतर्राष्ट्रीय चीता दिवस</p>",
                        "<p>विश्व लुप्तप्राय प्रजाति दिवस</p>",
                        "<p>वैश्विक संरक्षण दिवस</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>International Cheetah Day. </strong>To raise awareness about the challenges faced by cheetahs and to promote conservation efforts. Established by the Cheetah Conservation Fund (CCF).</p>",
                    solution_hi: "<p>46.(b) <strong>अंतर्राष्ट्रीय चीता दिवस।</strong> चीतों के सामने आने वाली चुनौतियों के बारे में जागरूकता बढ़ाने और संरक्षण प्रयासों को बढ़ावा देने के लिए। चीता संरक्षण कोष (CCF) द्वारा स्थापित।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. What is the process of a substance changing directly from a solid to a gas without passing through the liquid state called ?</p>",
                    question_hi: "<p>47. किसी पदार्थ के द्रव अवस्था से गुजरे बिना सीधे ठोस से गैस में बदलने की प्रक्रिया क्या कहलाती है?</p>",
                    options_en: [
                        "<p>Sublimation</p>",
                        "<p>Fusion</p>",
                        "<p>Evaporation</p>",
                        "<p>Condensation</p>"
                    ],
                    options_hi: [
                        "<p>ऊर्ध्वपातन</p>",
                        "<p>संलयन</p>",
                        "<p>वाष्पीकरण</p>",
                        "<p>संघनन</p>"
                    ],
                    solution_en: "<p>47.(a)<strong> Sublimation.</strong> Melting, or fusion, is the process where a substance changes from a solid to a liquid state. Evaporation occurs when a liquid turns into a gas or vapor, typically when heated or exposed to air. Condensation is the reverse of evaporation, where water vapor transforms into liquid water.</p>",
                    solution_hi: "<p>47.(a) <strong>ऊर्ध्वपातन।</strong> गलन, या संलयन, वह प्रक्रिया है जिसमें कोई पदार्थ ठोस अवस्था से द्रव अवस्था में परिवर्तित होता है। वाष्पीकरण तब होता है जब कोई द्रव पदार्थ गैस या वाष्प में बदलता है, आमतौर पर जब उसे गर्म किया जाता है या वायु के संपर्क में आता है। संघनन वाष्पीकरण का उल्टा है, जहाँ जलवाष्प द्रव जल में परिवर्तित होती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Sriharikota island is located in which of the following states?</p>",
                    question_hi: "<p>48. श्रीहरिकोटा द्वीप निम्नलिखित में से किस राज्य में स्थित है?</p>",
                    options_en: [
                        "<p>West Bengal</p>",
                        "<p>Tamil Nadu</p>",
                        "<p>Odisha</p>",
                        "<p>Andhra Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिम बंगाल</p>",
                        "<p>तमिलनाडु</p>",
                        "<p>ओडिशा</p>",
                        "<p>आंध्र प्रदेश</p>"
                    ],
                    solution_en: "<p>48.(d)&nbsp;<strong>Andhra Pradesh.</strong> Satish Dhawan Space Centre (SDSC) or Sriharikota Range (SHAR) is a rocket launch center (spaceport) operated by the Indian Space Research Organisation (ISRO). It is located in Sriharikota, district Nellore. Other space center: Vikram Sarabhai Space Centre: Located in Thiruvananthapuram, Kerala. Indian Institute of Space Science and Technology (IIST): Located in Thiruvananthapuram, Kerala. Dr. Abdul Kalam Island (formerly Wheeler Island): Located off the coast of Odisha.</p>",
                    solution_hi: "<p>48.(d)&nbsp;<strong>आंध्र प्रदेश। </strong>सतीश धवन अंतरिक्ष केंद्र (SDSC) या श्रीहरिकोटा रेंज (SHAR) भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO) द्वारा संचालित एक रॉकेट प्रक्षेपण केंद्र (स्पेसपोर्ट) है। यह श्रीहरिकोटा, जिला नेल्लोर में स्थित है। अन्य अंतरिक्ष केंद्र: विक्रम साराभाई अंतरिक्ष केंद्र: तिरुवनंतपुरम, केरल में स्थित है। भारतीय अंतरिक्ष विज्ञान और प्रौद्योगिकी संस्थान (IIST): तिरुवनंतपुरम, केरल में स्थित है। डॉ. अब्दुल कलाम द्वीप (पूर्व नाम व्हीलर द्वीप): ओडिशा के तट पर स्थित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. When did India host the Asian Games for the first time?</p>",
                    question_hi: "<p>49. भारत ने पहली बार एशियाई खेलों की मेजबानी कब की थी?</p>",
                    options_en: [
                        "<p>1951</p>",
                        "<p>1954</p>",
                        "<p>1962</p>",
                        "<p>1990</p>"
                    ],
                    options_hi: [
                        "<p>1951</p>",
                        "<p>1954</p>",
                        "<p>1962</p>",
                        "<p>1990</p>"
                    ],
                    solution_en: "<p>49.(a) <strong>1951 </strong>(New Delhi). Asian Games: It is a continental multi-sport event held every fourth year among athletes from all over Asia. The Games are recognized by the International Olympic Committee (IOC) and are described as the second largest multi-sport event after the Olympic Games. Motto - Ever Onward.</p>",
                    solution_hi: "<p>49.(a) <strong>1951 </strong>(नई दिल्ली)। एशियाई खेल: यह एक महाद्वीपीय बहु-खेल प्रतियोगिता है जो प्रत्येक चौथे वर्ष सम्पूर्ण एशिया के एथलीटों के बीच आयोजित की जाती है। खेलों को अंतर्राष्ट्रीय ओलंपिक समिति (IOC) द्वारा मान्यता प्राप्त है और ओलंपिक खेलों के बाद दूसरा सबसे बड़ा बहु-खेल आयोजन बताया गया है। आदर्श वाक्य - एवर ऑनवर्ड।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which festival is celebrated to commemorate the birth anniversary of Lord Buddha?</p>",
                    question_hi: "<p>50. भगवान बुद्ध की जयंती मनाने के लिए कौन-सा त्योहार मनाया जाता है?</p>",
                    options_en: [
                        "<p>Parinirvana Day</p>",
                        "<p>Vesak</p>",
                        "<p>Ullambana</p>",
                        "<p>Kathina</p>"
                    ],
                    options_hi: [
                        "<p>परिनिर्वाण दिवस</p>",
                        "<p>वेसाक</p>",
                        "<p>उल्लम्बाना</p>",
                        "<p>कथिना</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>Vesak.</strong> Buddha Purnima: It is celebrated to mark the birth of Gautam Buddha, the founder of Buddhism. It falls on a full moon day in May and is considered the most sacred day in the Buddhist calendar. Parinirvana Day: It commemorates Buddha\'s death and his entrance into complete Nirvana.</p>",
                    solution_hi: "<p>50.(b) <strong>वेसाक। </strong>बुद्ध पूर्णिमा: यह बौद्ध धर्म के संस्थापक गौतम बुद्ध के जन्म के उपलक्ष्य में मनाया जाता है। यह मई में पूर्णिमा के दिन पड़ता है और इसे बौद्ध कैलेंडर में सबसे पवित्र दिन माना जाता है। परिनिर्वाण दिवस: यह बुद्ध की मृत्यु और उनके पूर्ण निर्वाण में प्रवेश की याद दिलाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If Tanya walks at the speed of 6 km/h, she misses a train by 2 minutes. However, if she walks at the speed of 8 km/h, she reaches the station 3 minutes before the arrival of the train. The distance covered by Tanya to reach the station is:</p>",
                    question_hi: "<p>51. यदि तान्या 6 km/h की चाल से चलती है, तो उसकी रेलगाड़ी 2 मिनट से छूट जाती है। हालांकि, यदि वह 8 km/h की चाल से चलती है, तो वह रेलगाड़ी के आगमन से 3 मिनट पहले स्टेशन पर पहुंच जाती है। तान्या द्वारा स्टेशन तक पहुंचने के लिए तय की गई दूरी कितनी है?</p>",
                    options_en: [
                        "<p>2.5 km</p>",
                        "<p>2.75 km</p>",
                        "<p>3 km</p>",
                        "<p>2 km</p>"
                    ],
                    options_hi: [
                        "<p>2.5 km</p>",
                        "<p>2.75 km</p>",
                        "<p>3 km</p>",
                        "<p>2 km</p>"
                    ],
                    solution_en: "<p>51.(d) <br>Distance of station = <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>8</mn></mrow><mrow><mn>8</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>60</mn></mfrac></math> = 2 km</p>",
                    solution_hi: "<p>51.(d) <br>स्टेशन की दूरी = <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>8</mn></mrow><mrow><mn>8</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>60</mn></mfrac></math> = 2 km</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The difference (in ₹) between a discount of 37% on ₹2,635 and two successive discounts of 23% and 7% on the same amount is (rounded off to 2 decimal places):</p>",
                    question_hi: "<p>52. ₹2,635 की धनराशि पर 37% की छूट और समान धनराशि पर 23% और 7% की दो क्रमागत छूटों के बीच का अंतर (₹ में) कितना है? (दशमलव के 2 स्थानों तक सन्त्रिकटित)</p>",
                    options_en: [
                        "<p>226.87</p>",
                        "<p>248.36</p>",
                        "<p>242.25</p>",
                        "<p>235.65</p>"
                    ],
                    options_hi: [
                        "<p>226.87</p>",
                        "<p>248.36</p>",
                        "<p>242.25</p>",
                        "<p>235.65</p>"
                    ],
                    solution_en: "<p>52.(a) First discount = 37%<br>Second two successive discount (effective rate) = 23 + 7 - <math display=\"inline\"><mfrac><mrow><mn>23</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 30 - 1.61 = 28.39%<br>Required amount = 2635 &times; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>37</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>28</mn><mo>.</mo><mn>39</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2635 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>61</mn></mrow><mn>100</mn></mfrac></math> = 226.87</p>",
                    solution_hi: "<p>52.(a) पहली छूट = 37%<br>दूसरी दो क्रमिक छूट (प्रभावी दर) = 23 + 7 - <math display=\"inline\"><mfrac><mrow><mn>23</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 30 - 1.61 = 28.39%<br>आवश्यक राशि = 2635 &times; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>37</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>28</mn><mo>.</mo><mn>39</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2635 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>61</mn></mrow><mn>100</mn></mfrac></math> = 226.87</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. There are two classrooms, A and B. If 7 students are shifted from A to B, then B will have twice the number of students as A. If 3 students are sent from B to A, then both the classrooms will have the same number of students. The positive difference between the number of students in the two classrooms is:</p>",
                    question_hi: "<p>53. दो कक्षाएँ A और B हैं। यदि 7 विद्यार्थियों को A से B में स्थानांतरित किया जाता है, तो B में A से दोगुनी संख्या में विद्यार्थी होंगे। यदि 3 विद्यार्थियों को B से A में भेजा जाता है, तो दोनों कक्षाओं में विद्यार्थियों की संख्या समान हो जाएगी। दोनों कक्षाओं में विद्यार्थियों की संख्या के बीच धनात्मक अंतर ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>5</p>",
                        "<p>8</p>",
                        "<p>7</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>5</p>",
                        "<p>8</p>",
                        "<p>7</p>"
                    ],
                    solution_en: "<p>53.(a)<br>Let no. of students in classrooms A and B are &lsquo;a&rsquo; and &lsquo;b&rsquo; respectively.<br>According to the first condition,<br>2 (a-7) = b + 7<br>&rArr; 2a - b = 21<br>According to the second condition,<br>a + 3 = b - 3<br>&rArr; b - a = 6<br>Hence, a = 27 and b = 33<br>Required difference = 33 - 27 = 6</p>",
                    solution_hi: "<p>53.(a)<br>माना कक्षाओं में छात्रों की संख्या A और B क्रमशः \'a\' और \'b\' हैं।<br>पहली स्थिति के अनुसार,<br>2 (a - 7) = b + 7<br>&rArr; 2a - b = 21<br>दूसरी स्थिति के अनुसार,<br>a + 3 = b - 3<br>&rArr; b - a = 6<br>अतः, a = 27 और b = 33<br>आवश्यक अंतर = 33 - 27 = 6</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Time taken (in hrs) by two trains X and Y to travel the distance from Lucknow to Chandigarh via the same route on four different days is given in the following bar graph. Study the graph carefully and answer the question given below:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130569.png\" alt=\"rId49\" width=\"286\" height=\"173\"> <br>If the distance between Lucknow and Chandigarh is 754 km, then what is the difference between the average speed (in km/hr) of the trains X and Y on Day 3?</p>",
                    question_hi: "<p>54. दो ट्रेनों X और Y द्वारा चार अलग-अलग दिनों में एक ही मार्ग से लखनऊ से चंडीगढ़ की दूरी तय करने में लिया गया समय (hrs में) निम्नलिखित बार ग्राफ में दिया गया है। ग्राफ का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130705.png\" alt=\"rId50\" width=\"282\" height=\"170\"> <br>यदि लखनऊ और चंडीगढ़ के बीच की दूरी 754 km है, तो तीसरे दिन (Day 3) ट्रेन X और Y की औसत चाल (km/h मे) के बीच कितना अंतर है?</p>",
                    options_en: [
                        "<p>18.5</p>",
                        "<p>15.8</p>",
                        "<p>20.3</p>",
                        "<p>23.5</p>"
                    ],
                    options_hi: [
                        "<p>18.5</p>",
                        "<p>15.8</p>",
                        "<p>20.3</p>",
                        "<p>23.5</p>"
                    ],
                    solution_en: "<p>54.(c)<br>Speed of train X on day 3 = <math display=\"inline\"><mfrac><mrow><mn>754</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 58 km/h<br>Speed of train Y on day 3 = <math display=\"inline\"><mfrac><mrow><mn>754</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 37.7 km/h<br>Required difference = 58 - 37.7 = 20.3 km/h</p>",
                    solution_hi: "<p>54.(c)<br>तीसरे दिन ट्रेन X की गति = <math display=\"inline\"><mfrac><mrow><mn>754</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 58 km/h<br>तीसरे दिन ट्रेन Y की गति = <math display=\"inline\"><mfrac><mrow><mn>754</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 37.7 km/h<br>आवश्यक अंतर = 58 - 37.7 = 20.3 km/h</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Find the length of the plank which can be used to measure exactly the lengths 6 m 50 cm, 3 m 50 cm, and 7 m 50 cm in the least time.</p>",
                    question_hi: "<p>55. उस तख्ते की लंबाई ज्ञात कीजिए जिसका उपयोग कम से कम समय में 6 m 50 cm, 3 m 50 cm और 7m 50 cm की लंबाई सटीक रूप से मापने के लिए किया जा सकता है।</p>",
                    options_en: [
                        "<p>15 cm</p>",
                        "<p>50 cm</p>",
                        "<p>100 cm</p>",
                        "<p>25 cm</p>"
                    ],
                    options_hi: [
                        "<p>15 cm</p>",
                        "<p>50 cm</p>",
                        "<p>100 cm</p>",
                        "<p>25 cm</p>"
                    ],
                    solution_en: "<p>55.(b)<br>6 m 50 cm = 650 cm<br>3 m 50 cm = 350 cm<br>7 m 50 cm = 750 cm<br>HCF (650 , 350 , 750) = 50<br>Hence, required length = 50 cm</p>",
                    solution_hi: "<p>55.(b)<br>6 मीटर 50 सेमी = 650 सेमी <br>3 मीटर 50 सेमी = 350 सेमी <br>7 मीटर 50 सेमी = 750 सेमी <br>HCF (650 , 350 , 750) = 50<br>अतः आवश्यक लंबाई = 50 सेमी</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. In an election between two candidates, one got 65% of the total valid votes. 20% of the votes were invalid. If the total number of votes was 7,500, the number of valid votes that the other candidate got was:</p>",
                    question_hi: "<p>56. दो उम्मीदवारों के बीच एक चुनाव में एक उम्मीदवार को कुल वैध मतों के 65% मत प्राप्त हुए। 20% मत अवैध थे। यदि मतों की कुल संख्या 7,500 थी, तो दूसरे उम्मीदवार को मिले वैध मतों की संख्या ______ थी।</p>",
                    options_en: [
                        "<p>4,300</p>",
                        "<p>1,900</p>",
                        "<p>2,100</p>",
                        "<p>1,700</p>"
                    ],
                    options_hi: [
                        "<p>4,300</p>",
                        "<p>1,900</p>",
                        "<p>2,100</p>",
                        "<p>1,700</p>"
                    ],
                    solution_en: "<p>56.(c)<br>Total number of votes = 7500<br>Total number of valid votes = 7500 &times; 80% = 6000 <br>Required number of votes = 6000 &times; <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2100</p>",
                    solution_hi: "<p>56.(c)<br>कुल वोटों की संख्या = 7500<br>वैध मतों की कुल संख्या = 7500 &times; 80% = 6000 <br>आवश्यक वोटों की संख्या = 6000 &times; <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2100</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. What is the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>9</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>26</mn><mo>&#176;</mo><mo>+</mo><mn>9</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>64</mn><mo>&#176;</mo><mo>+</mo><mn>36</mn></mrow><mrow><mn>32</mn><mo>-</mo><mn>7</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>32</mn><mo>&#176;</mo><mo>-</mo><mn>7</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></msqrt></math> ?</p>",
                    question_hi: "<p>57. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>9</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>26</mn><mo>&#176;</mo><mo>+</mo><mn>9</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>64</mn><mo>&#176;</mo><mo>+</mo><mn>36</mn></mrow><mrow><mn>32</mn><mo>-</mo><mn>7</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>32</mn><mo>&#176;</mo><mo>-</mo><mn>7</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></msqrt></math>&nbsp;का मान क्&zwj;या है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>5</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn><msqrt><mn>5</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>5</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn><msqrt><mn>5</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>57.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>9</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>26</mn><mo>&#176;</mo><mo>+</mo><mn>9</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>64</mn><mo>&#176;</mo><mo>+</mo><mn>36</mn></mrow><mrow><mn>32</mn><mo>-</mo><mn>7</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>32</mn><mo>&#176;</mo><mo>-</mo><mn>7</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>9</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>64</mn><mo>&#176;</mo><mo>+</mo><mn>9</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>64</mn><mo>&#176;</mo><mo>+</mo><mn>36</mn></mrow><mrow><mn>32</mn><mo>-</mo><mo>(</mo><mn>7</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>58</mn><mo>&#176;</mo><mo>+</mo><mn>7</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>58</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>9</mn><mo>+</mo><mn>36</mn></mrow><mrow><mn>32</mn><mo>-</mo><mn>7</mn></mrow></mfrac></msqrt></math> <br>= <math display=\"inline\"><msqrt><mfrac><mrow><mn>45</mn></mrow><mrow><mn>25</mn></mrow></mfrac></msqrt></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>5</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>57.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>9</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>26</mn><mo>&#176;</mo><mo>+</mo><mn>9</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>64</mn><mo>&#176;</mo><mo>+</mo><mn>36</mn></mrow><mrow><mn>32</mn><mo>-</mo><mn>7</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>32</mn><mo>&#176;</mo><mo>-</mo><mn>7</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>9</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>64</mn><mo>&#176;</mo><mo>+</mo><mn>9</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>64</mn><mo>&#176;</mo><mo>+</mo><mn>36</mn></mrow><mrow><mn>32</mn><mo>-</mo><mo>(</mo><mn>7</mn><msup><mi>sin</mi><mn>2</mn></msup><mn>58</mn><mo>&#176;</mo><mo>+</mo><mn>7</mn><msup><mi>cos</mi><mn>2</mn></msup><mn>58</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></msqrt></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>9</mn><mo>+</mo><mn>36</mn></mrow><mrow><mn>32</mn><mo>-</mo><mn>7</mn></mrow></mfrac></msqrt></math> <br>= <math display=\"inline\"><msqrt><mfrac><mrow><mn>45</mn></mrow><mrow><mn>25</mn></mrow></mfrac></msqrt></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>3</mn><msqrt><mn>5</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Rahul covers 734 km in a boat in 30 hours against the stream and he takes 12 hours with the stream then finds the speed of the stream?</p>",
                    question_hi: "<p>58. राहुल, नाव से 734 km की दूरी धारा की विपरीत दिशा में 30 घंटे में तय करता है और धारा की दिशा में 12 घंटे में तय करता है, तो धारा की चाल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>18.35 km/h</p>",
                        "<p>11.88 km/h</p>",
                        "<p>19.07 km/h</p>",
                        "<p>28.44 km/h</p>"
                    ],
                    options_hi: [
                        "<p>18.35 km/h</p>",
                        "<p>11.88 km/h</p>",
                        "<p>19.07 km/h</p>",
                        "<p>28.44 km/h</p>"
                    ],
                    solution_en: "<p>58.(a)<br>Let speed of Boat = x&nbsp;km/h<br>Speed of stream = y&nbsp;km/h<br>According to question<br>x - y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>734</mn><mn>30</mn></mfrac></math> &hellip; (i)<br>x + y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>734</mn><mn>12</mn></mfrac></math> &hellip; (ii)<br>From (i) and (ii)<br>- 2y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>734</mn><mn>30</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>734</mn><mn>12</mn></mfrac></math><br>- 2y = 734 (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>-</mo><mn>5</mn></mrow><mn>60</mn></mfrac></math>)<br>- y = 367 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>20</mn></mfrac></math>)<br>y = 18.35 km/h<br>Hence speed of stream = 18.35 km/h</p>",
                    solution_hi: "<p>58.(a)<br>माना नाव की गति = x&nbsp;km/h<br>धारा की गति = y&nbsp;km/h<br>प्रश्न के अनुसार<br>x - y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>734</mn><mn>30</mn></mfrac></math> &hellip; (i)<br>x + y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>734</mn><mn>12</mn></mfrac></math> &hellip; (ii)<br>(i) और (ii) से<br>- 2y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>734</mn><mn>30</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>734</mn><mn>12</mn></mfrac></math><br>- 2y = 734 (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>-</mo><mn>5</mn></mrow><mn>60</mn></mfrac></math>)<br>- y = 367 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>20</mn></mfrac></math>)<br>y = 18.35 km/h<br>अतः धारा की गति = 18.35 km/h</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Given that 40<sup>0.11</sup>&nbsp;= x, 40<sup>0.1 </sup>= y and x<sup>z</sup> = y<sup>2</sup>, then the value of z is close to:</p>",
                    question_hi: "<p>59. दिया गया है कि 40<sup>0.11</sup>&nbsp;= x, 40<sup>0.1 </sup>= y और x<sup>z</sup> = y<sup>2</sup>&nbsp;है, तो z का निकटतम मान कितना है</p>",
                    options_en: [
                        "<p>1.82</p>",
                        "<p>0.09</p>",
                        "<p>1.16</p>",
                        "<p>2.72</p>"
                    ],
                    options_hi: [
                        "<p>1.82</p>",
                        "<p>0.09</p>",
                        "<p>1.16</p>",
                        "<p>2.72</p>"
                    ],
                    solution_en: "<p>59.(a)<br><strong>Given:</strong> 40<sup>0.11</sup>&nbsp;= x, 40<sup>0.1 </sup>= y<br>Now,<br>x<sup>z</sup> = y<sup>2</sup><br>&rArr; 40<sup>0.11&times;z</sup> = 40<sup>0.1&times;2</sup><br>Base are same then power will also be same<br>&rArr; 0.11 &times; z = 0.1 &times; 2<br>&rArr; z = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>1</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>11</mn></mrow></mfrac></math> = 1.82</p>",
                    solution_hi: "<p>59.(a)<br><strong>दिया गया है:</strong> 40<sup>0.11</sup>&nbsp;= x, 40<sup>0.1 </sup>= y<br>अब,<br>x<sup>z</sup> = y<sup>2</sup><br>&rArr; 40<sup>0.11&times;z</sup> = 40<sup>0.1&times;2</sup><br>आधार समान है तो घात भी समान होगी,<br>इसलिए, <br>&rArr; 0.11 &times; z = 0.1 &times; 2<br>&rArr; z = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>1</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>11</mn></mrow></mfrac></math> = 1.82</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The diameter of the base of a solid cone is 24 cm and its height is 21 cm. It is cut into three parts by two cuts which are parallel to its base. The cuts are at the height of 7 cm and 14 cm from the base, respectively. Find the curved surface area (in cm<sup>2</sup>) of the top part. (use &pi; = 3.14)</p>",
                    question_hi: "<p>60. एक ठोस शंकु के आधार का व्यास 24 cm है और इसकी ऊंचाई 21 cm है। इसे आधार के समानांतर दो कट लगाकर, तीन भागों में काटा जाता है। कट क्रमशः आधार से 7 cm और 14 cm की ऊंचाई पर हैं। शंकु के शीर्ष भाग का वक्र पृष्ठीय क्षेत्रफल (cm<sup>2</sup> में) ज्ञात कीजिए। (&pi; = 3.14 लीजिए )</p>",
                    options_en: [
                        "<p>12.56<math display=\"inline\"><msqrt><mn>30</mn></msqrt></math></p>",
                        "<p>15.46<math display=\"inline\"><msqrt><mn>10</mn></msqrt></math></p>",
                        "<p>14.65<math display=\"inline\"><msqrt><mn>15</mn></msqrt></math></p>",
                        "<p>12.56<math display=\"inline\"><msqrt><mn>65</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p>12.56<math display=\"inline\"><msqrt><mn>30</mn></msqrt></math></p>",
                        "<p>15.46<math display=\"inline\"><msqrt><mn>10</mn></msqrt></math></p>",
                        "<p>14.65<math display=\"inline\"><msqrt><mn>15</mn></msqrt></math></p>",
                        "<p>12.56<math display=\"inline\"><msqrt><mn>65</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>60.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130892.png\" alt=\"rId51\" width=\"212\" height=\"180\"><br>Slant height (AD) = <math display=\"inline\"><msqrt><mi>r</mi><mi>a</mi><mi>d</mi><mi>i</mi><mi>u</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>H</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>1</mn><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>2</mn><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>144</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>441</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>585</mn></msqrt></math> = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>65</mn></msqrt></math> cm<br>&Delta;ABE &sim; &Delta;ACD<br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>E</mi></mrow><mrow><mn>3</mn><msqrt><mn>65</mn></msqrt></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>21</mn></mfrac></math> &hellip;. (BPT)<br>AE (I) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mn>3</mn><msqrt><mn>65</mn></msqrt></mrow><mn>21</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>65</mn></msqrt></math> cm<br>Similarly <br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>E</mi></mrow><mrow><mi>C</mi><mi>D</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>E</mi></mrow><mn>12</mn></mfrac></math><br>BE (r<sub>3</sub>) = 4 cm<br>Now,<br>C.S.A. of top part = &pi;r<sub>3</sub>l&nbsp;= 3.14 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>65</mn></msqrt></math> &times; 4 = 12.56<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>65</mn></msqrt></math> cm<sup>2</sup></p>",
                    solution_hi: "<p>60.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551130892.png\" alt=\"rId51\" width=\"170\" height=\"145\"><br>तिर्यक ऊँचाई (AD) = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2340;&#2381;&#2352;&#2367;</mi><msup><mi>&#2332;&#2381;&#2351;&#2366;</mi><mn>2</mn></msup><mo>+</mo><msup><mi>&#2314;&#2305;&#2330;&#2366;&#2312;</mi><mn>2</mn></msup></msqrt></math> <br>= <math display=\"inline\"><msqrt><mi>r</mi><mi>a</mi><mi>d</mi><mi>i</mi><mi>u</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>H</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>1</mn><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>2</mn><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>144</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>441</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>585</mn></msqrt></math> = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>65</mn></msqrt></math> cm<br>&Delta;ABE &sim; &Delta;ACD<br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>E</mi></mrow><mrow><mn>3</mn><msqrt><mn>65</mn></msqrt></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>21</mn></mfrac></math> &hellip;. (BPT)<br>AE (I) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mn>3</mn><msqrt><mn>65</mn></msqrt></mrow><mn>21</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>65</mn></msqrt></math> cm<br>इसी प्रकार <br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>A</mi><mi>C</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>E</mi></mrow><mrow><mi>C</mi><mi>D</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>E</mi></mrow><mn>12</mn></mfrac></math><br>BE (r<sub>3</sub>) = 4 cm<br>अब,<br>शीर्ष भाग का वक्र पृष्ठीय क्षेत्र = &pi;r<sub>3</sub>l&nbsp;= 3.14 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>65</mn></msqrt></math> &times; 4 = 12.56<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>65</mn></msqrt></math> cm<sup>2</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. If a regular polygon has 20 diagonals, then the number of sides of this polygon is:</p>",
                    question_hi: "<p>61. यदि एक सम बहुभुज में 20 विकर्ण हैं, तो इस बहुभुज की भुजाओं की संख्या कितनी है?</p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>61.(c)<br>Number of diagonals = <math display=\"inline\"><mfrac><mrow><mi>n</mi><mo>(</mo><mi>n</mi><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>Where , n = number of sides.<br>&rArr; 20 = <math display=\"inline\"><mfrac><mrow><mi>n</mi><mo>(</mo><mi>n</mi><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>&rArr; n(n - 3) = 40 <br>&rArr; n = 8</p>",
                    solution_hi: "<p>61.(c)<br>विकर्णों की संख्या = <math display=\"inline\"><mfrac><mrow><mi>n</mi><mo>(</mo><mi>n</mi><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>जहां, n = भुजाओं की संख्या।<br>&rArr; 20 = <math display=\"inline\"><mfrac><mrow><mi>n</mi><mo>(</mo><mi>n</mi><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>&rArr; n(n - 3) = 40 <br>&rArr; n = 8</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Let a + b = 1, then the value of (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math>) is:</p>",
                    question_hi: "<p>62. मान लीजिए a + b = 1 है, तो (<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math>) का मान कितना है ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>b</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>b</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>62.(a)<br><strong>Given :-</strong> a + b = 1<br>(<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mi>a</mi><mi>b</mi></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                    solution_hi: "<p>62.(a)<br>a + b = 1 ( दिया गया है )<br>(<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mi>a</mi><mi>b</mi></mrow></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mi>a</mi><mi>b</mi></mrow><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi>a</mi><mn>2</mn></msup><msup><mi>b</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If A : B = 3 : 7 and B : C = 2 : 5, find A : B : C.</p>",
                    question_hi: "<p>63. यदि A : B = 3 :7 और B : C = 2 : 5 है, तो A : B : C का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>4 : 10 : 3</p>",
                        "<p>10 : 3 : 4</p>",
                        "<p>6 : 14 : 35</p>",
                        "<p>4 : 3 : 10</p>"
                    ],
                    options_hi: [
                        "<p>4 : 10 : 3</p>",
                        "<p>10 : 3 : 4</p>",
                        "<p>6 : 14 : 35</p>",
                        "<p>4 : 3 : 10</p>"
                    ],
                    solution_en: "<p>63.(c) <br>Ratio - A : B : C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3 : 7 :<strong> 7</strong><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<strong> 2</strong> : 2 : 5<br>----------------------------<br>Final - 6 : 14 : 35</p>",
                    solution_hi: "<p>63.(c) <br>अनुपात- A : B : C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3 : 7 : <strong>7</strong><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<strong>2</strong> : 2 : 5<br>----------------------------<br>अंतिम - 6 : 14 : 35</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. The value of the expression <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">t</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>-</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow></mfrac></math> is</p>",
                    question_hi: "<p>64. व्यंजक <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">t</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>-</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow></mfrac></math> का मान ___________ है।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>64.(b)<br><strong>Identity used:</strong><br>sin2A = 2sin A cosA and sin<sup>2</sup>A + cos<sup>2</sup>A = 1<br>Now,<br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi></mrow><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>-</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>-</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>cos</mi><mi>t</mi><mo>-</mo><mi>sin</mi><mi>t</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>cos</mi><mi>t</mi><mo>+</mo><mi>sin</mi><mi>t</mi><mo>)</mo></mrow></mfrac></math>&nbsp;<br>On dividing numerator and denominator by <math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi></math> we get,<br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>&#160;</mi><mn>1</mn><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math></p>",
                    solution_hi: "<p>64.(b)<br><strong>प्रयुक्त सूत्र :</strong><br>sin2A = 2sin A cosA and sin<sup>2</sup>A + cos<sup>2</sup>A = 1<br>अब <br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi></mrow><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>-</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo><mo>-</mo><mi>sin</mi><mo>(</mo><mi mathvariant=\"normal\">t</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>cos</mi><mi>t</mi><mo>-</mo><mi>sin</mi><mi>t</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>cos</mi><mi>t</mi><mo>+</mo><mi>sin</mi><mi>t</mi><mo>)</mo></mrow></mfrac></math>&nbsp;<br>अंश और हर को <math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi></math> से विभाजित करने पर हमें प्राप्त होता है,<br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>&#160;</mi><mn>1</mn><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A grocer has 15 kg of sugar at ₹45 per kg. How much sugar at ₹34.5 per kg should she add to it, so that the mixture is worth ₹38 per kg?</p>",
                    question_hi: "<p>65. एक पंसारी के पास ₹45 प्रति kg की दर वाली 15 kg चीनी है। उसे ₹34.5 प्रति kg की दर वाली कितनी चीनी मिलानी चाहिए, ताकि मिश्रण का मूल्य ₹38 प्रति kg हो जाए?</p>",
                    options_en: [
                        "<p>32 kg</p>",
                        "<p>30 kg</p>",
                        "<p>35 kg</p>",
                        "<p>28 kg</p>"
                    ],
                    options_hi: [
                        "<p>32 kg</p>",
                        "<p>30 kg</p>",
                        "<p>35 kg</p>",
                        "<p>28 kg</p>"
                    ],
                    solution_en: "<p>65.(b)<br>Using Alligation method, we have ;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551131102.png\" alt=\"rId52\"><br>1 unit -------------- 15 kg<br>2 unit -------------- 15 &times; 2 = 30 kg</p>",
                    solution_hi: "<p>65.(b)<br>एलीगेशन विधि का उपयोग करते हुए, हमारे पास है;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551131102.png\" alt=\"rId52\"><br>1 इकाई -------------- 15 kg<br>2 इकाई -------------- 15 &times; 2 = 30 kg</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A cuboid of dimensions 50 cm, 150 cm, 175 cm can be divided into how many identical largest cubes ?</p>",
                    question_hi: "<p>66. 50 cm, 150 cm, 175 cm विमाओं वाले एक घनाभ को एक जैसे कितने विशालतम घनों में विभाजित किया जा सकता है?</p>",
                    options_en: [
                        "<p>75</p>",
                        "<p>84</p>",
                        "<p>85</p>",
                        "<p>90</p>"
                    ],
                    options_hi: [
                        "<p>75</p>",
                        "<p>84</p>",
                        "<p>85</p>",
                        "<p>90</p>"
                    ],
                    solution_en: "<p>66.(b) <br>Dimension of largest cube = HCF of (50, 150, 175) = 25<br>Required no = <math display=\"inline\"><mfrac><mrow><mi>v</mi><mi>o</mi><mi>l</mi><mi>u</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>c</mi><mi>u</mi><mi>b</mi><mi>o</mi><mi>i</mi><mi>d</mi><mi>&#160;</mi></mrow><mrow><mi>v</mi><mi>o</mi><mi>l</mi><mi>u</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>c</mi><mi>u</mi><mi>b</mi><mi>e</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>&#215;</mo><mn>150</mn><mo>&#215;</mo><mn>175</mn></mrow><mrow><mn>25</mn><mo>&#215;</mo><mn>25</mn><mo>&#215;</mo><mn>25</mn></mrow></mfrac></math> = 84</p>",
                    solution_hi: "<p>66.(b) <br>बड़े घनो की विमा = HCF का (50, 150, 175) = 25<br>आवश्यक संखाा = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2328;&#2344;&#2366;&#2349;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2310;&#2351;&#2340;&#2344;</mi></mrow><mrow><mi>&#2328;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2310;&#2351;&#2340;&#2344;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>&#215;</mo><mn>150</mn><mo>&#215;</mo><mn>175</mn></mrow><mrow><mn>25</mn><mo>&#215;</mo><mn>25</mn><mo>&#215;</mo><mn>25</mn></mrow></mfrac></math> = 84</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. 8 men and 12 women can build a wall in 10 days. 6 men and 8 women can build the same wall in 14 days. How many more days will 1 woman take to build that wall alone compared to the number of days 1 man will take to build the same wall alone?</p>",
                    question_hi: "<p>67. 8 पुरुष और 12 महिलाएं एक दीवार 10 दिनों में बना सकते हैं। 6 पुरुष और 8 महिलाएँ इसे 14 दिनों में बना सकते हैं। इसे एक पुरुष द्वारा अकेले बनाने की तुलना में, एक महिला को अकेले बनाने में कितने दिन अधिक लगेंगे?</p>",
                    options_en: [
                        "<p>35 days</p>",
                        "<p>280 days</p>",
                        "<p>70 days</p>",
                        "<p>140 days</p>"
                    ],
                    options_hi: [
                        "<p>35 दिन</p>",
                        "<p>280 दिन</p>",
                        "<p>70 दिन</p>",
                        "<p>140 दिन</p>"
                    ],
                    solution_en: "<p>67.(d)<br>(8M + 12W) &times; 10 = (6M + 8W) &times; 14<br>80M + 120W = 84M + 112W <br>8W = 4M <br><math display=\"inline\"><mfrac><mrow><mi>W</mi></mrow><mrow><mi>M</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>Then , total work = ( 8 &times; 2 + 12 &times; 1 ) &times; 10<br>= 28 &times; 10 = 280<br>Time taken by 1 woman to complete the total work = <math display=\"inline\"><mfrac><mrow><mn>280</mn><mi>&#160;</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi></mrow></mfrac></math> = 280<br>Time taken by 1 man to complete the total work = <math display=\"inline\"><mfrac><mrow><mn>280</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn><mi>&#160;</mi></mrow></mfrac></math>= 140<br>More days taken to make one woman alone = 280 &ndash; 140 = 140</p>",
                    solution_hi: "<p>67.(d)<br>(8M + 12W) &times; 10 = (6M + 8W) &times; 14<br>80M + 120W = 84M + 112W <br>8W = 4M <br><math display=\"inline\"><mfrac><mrow><mi>W</mi></mrow><mrow><mi>M</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>अब , कुल कार्य = ( 8 &times; 2 + 12 &times; 1 ) &times; 10<br>= 28 &times; 10 = 280<br>1 महिला द्वारा कुल कार्य को पूरा करने मे लगा समय = <math display=\"inline\"><mfrac><mrow><mn>280</mn><mi>&#160;</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi></mrow></mfrac></math> = 280<br>1 पुरुष द्वारा कुल कार्य को पूरा करने मे लगा समय = <math display=\"inline\"><mfrac><mrow><mn>280</mn><mi>&#160;</mi></mrow><mrow><mn>2</mn><mi>&#160;</mi><mi>&#160;</mi></mrow></mfrac></math> = 140<br>एक महिला को अकेले बनाने में लगे अधिक दिन = 280 - 140 = 140</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. The radii of the two circles are 7 cm and 4 cm. If the distance between their centres is&nbsp;25 cm, then the length of the transverse common tangent is equal to:</p>",
                    question_hi: "<p>68. दो वृत्तोंकी त्रिज्याएं 7 cm और 4 cm हैं। यदि उनके केंद्रों के बीच की दूरी 25 cm है, तो अनुप्रस्थ उभयनिष्ठ स्पर्श रेखा (transverse common tangent) की लंबाई _______ होगी।</p>",
                    options_en: [
                        "<p>6<math display=\"inline\"><msqrt><mn>11</mn></msqrt></math> cm</p>",
                        "<p>6<math display=\"inline\"><msqrt><mn>12</mn></msqrt></math> cm</p>",
                        "<p>6<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math> cm</p>",
                        "<p>6<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm</p>"
                    ],
                    options_hi: [
                        "<p>6<math display=\"inline\"><msqrt><mn>11</mn></msqrt></math> cm</p>",
                        "<p>6<math display=\"inline\"><msqrt><mn>12</mn></msqrt></math> cm</p>",
                        "<p>6<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math> cm</p>",
                        "<p>6<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm</p>"
                    ],
                    solution_en: "<p>68.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551131286.png\" alt=\"rId53\" width=\"264\" height=\"133\"><br>l = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msub><mi>r</mi><mn>1</mn></msub><mo>+</mo><mo>&#160;</mo><msub><mi>r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mn>25</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mo>(</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>4</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>625</mn><mo>-</mo><mn>121</mn></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>504</mn></msqrt></math> <br>= 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>14</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>68.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551131286.png\" alt=\"rId53\" width=\"264\" height=\"133\"><br>l = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msub><mi>r</mi><mn>1</mn></msub><mo>+</mo><mo>&#160;</mo><msub><mi>r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mn>25</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mo>(</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>4</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>625</mn><mo>-</mo><mn>121</mn></msqrt></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>504</mn></msqrt></math> <br>= 6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>14</mn></msqrt></math> cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The price (per litre) of petrol increases by 75%. By what percent should its consumption be reduced such that the expenditure on it increases by 33% only?</p>",
                    question_hi: "<p>69. पेट्रोल की कीमत (प्रति लीटर) 75% की वृद्धि होती है। इसकी खपत कितने प्रतिशत कर दी जाए ताकि इस पर होने वाले व्यय में केवल 33% की वृद्धि हो ?</p>",
                    options_en: [
                        "<p>70%</p>",
                        "<p>75%</p>",
                        "<p>82%</p>",
                        "<p>76%</p>"
                    ],
                    options_hi: [
                        "<p>70%</p>",
                        "<p>75%</p>",
                        "<p>82%</p>",
                        "<p>76%</p>"
                    ],
                    solution_en: "<p>69.(d)<br>Expenditure = price &times; consumption<br>According to the question,<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr; Initial :&nbsp; final <br>Price&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 7<br>Consumption &rarr;&nbsp; &nbsp; x&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;y<br>---------------------------------------<br>Expenditure &rarr; 100 : 133<br>x : y = 100 &times; 7 : 133 &times; 4 = 25 : 19<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = 76%</p>",
                    solution_hi: "<p>69.(d)<br>व्यय = कीमत &times; खपत<br>प्रश्न के अनुसार,<br>अनुपात &rarr; आरंभिक : अंतिम <br>कीमत&nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;7<br>खपत&nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp; x&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; y<br>---------------------------------------<br>व्यय&nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;133<br>x : y = 100 &times; 7 : 133 &times; 4 = 25 : 19<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = 76%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. During sale, Raghav bought a notebook marked for ₹70 at 40% discount and a pen marked for ₹20 at 35% discount. How much (in₹) did he save due to sale?</p>",
                    question_hi: "<p>70. सेल के दौरान, राघव ने ₹70 अंकित मूल्य की एक नोटबुक को 40% छूट पर और ₹20 अंकित मूल्य की एक पेन को 35% की छूट पर खरीदा। सेल के कारण उसने कितनी बचत (₹ मैं) की?</p>",
                    options_en: [
                        "<p>38</p>",
                        "<p>34</p>",
                        "<p>35</p>",
                        "<p>32</p>"
                    ],
                    options_hi: [
                        "<p>38</p>",
                        "<p>34</p>",
                        "<p>35</p>",
                        "<p>32</p>"
                    ],
                    solution_en: "<p>70.(c) 40% = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>, 35% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>20</mn></mfrac></math><br>Saving on Notebook = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 70 = ₹28<br>Saving on Pen = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 20 = ₹7<br>Total saving = 28 + 7 = ₹35</p>",
                    solution_hi: "<p>70.(c) 40% = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>, 35% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>20</mn></mfrac></math><br>नोटबुक पर बचत = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 70 = ₹28<br>पेन पर बचत = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 20 = ₹7<br>कुल बचत = 28 + 7 = ₹35</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. The difference between the simple interest and the compound interest, compounded annually, on a certain sum of money for 2 years at 6% per annum is ₹ 78. Find the sum. [Give your answer to the integral number without rounded off.]</p>",
                    question_hi: "<p>71. किसी निश्चित धनराशि पर 6% वार्षिक ब्याज दर से 2 वर्षों में प्राप्त साधारण ब्याज तथा वार्षिक रूप से संयोजित होने वाले चक्रवृद्धि ब्याज के बीच का अंतर ₹78 है। धनराशि ज्ञात कीजिए। [पूर्णांकित किए बिना पूर्णांक में उत्तर दीजिए।]</p>",
                    options_en: [
                        "<p>₹ 21672</p>",
                        "<p>₹ 21682</p>",
                        "<p>₹ 21652</p>",
                        "<p>₹ 21666</p>"
                    ],
                    options_hi: [
                        "<p>₹ 21672</p>",
                        "<p>₹ 21682</p>",
                        "<p>₹ 21652</p>",
                        "<p>₹ 21666</p>"
                    ],
                    solution_en: "<p>71.(d)<br>Difference of CI and SI for 2 years = (CI - SI)<sub>2yrs</sub> = P(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>100</mn></mfrac></math>)<sup>2</sup><br>78 = P(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>100</mn></mfrac></math>)<sup>2</sup><br>P = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>78</mn><mo>&#215;</mo><mn>10000</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>130000</mn><mn>6</mn></mfrac></math> = ₹21666</p>",
                    solution_hi: "<p>71.(d)<br>2 वर्षों के लिए CI और SI का अंतर = (CI - SI)<sub>2yrs</sub> = P(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>R</mi><mn>100</mn></mfrac></math>)<sup>2</sup><br>78 = P(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>100</mn></mfrac></math>)<sup>2</sup><br>P = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>78</mn><mo>&#215;</mo><mn>10000</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>130000</mn><mn>6</mn></mfrac></math> = ₹21666</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Lalit\'s average earning per month in the first three months of a year was ₹22272. In April, his earning was 75% more than the average earning in the first three months. If his average earning per month for the whole year is ₹ 84460, then what will be Lalit\'s average earning (in ₹ ) per month from May to December?</p>",
                    question_hi: "<p>72. वर्ष के प्रथम तीन महीनों में ललित की प्रति माह औसत आय ₹22272 थी। अप्रैल में उसकी आय, प्रथम तीन महीनों की औसत आय से 75% अधिक थी। यदि पूरे वर्ष के लिए उसकी प्रति माह औसत आय ₹84460 है, तो मई से दिसंबर तक ललित की प्रति माह औसत आय (₹ में) कितनी होगी?</p>",
                    options_en: [
                        "<p>113463</p>",
                        "<p>113471</p>",
                        "<p>113466</p>",
                        "<p>113470</p>"
                    ],
                    options_hi: [
                        "<p>113463</p>",
                        "<p>113471</p>",
                        "<p>113466</p>",
                        "<p>113470</p>"
                    ],
                    solution_en: "<p>72.(c)<br>Sum of first three months = 22272 &times; 3 = ₹66816<br>His April month earning = 22272 &times; <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹38976<br>Sum of the money of May to December <br>= 84460 &times; 12 - (66816 + ₹38976) <br>= 1013520 - 105792 = ₹907728<br>Required Average = <math display=\"inline\"><mfrac><mrow><mn>907728</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = ₹113466</p>",
                    solution_hi: "<p>72.(c)<br>पहले तीन महीनों का योग = 22272 &times; 3 = ₹66816<br>अप्रैल महीने की कमाई = 22272 &times; <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹38976<br>मई से दिसंबर तक पैसे का योग <br>= 84460 &times; 12 - (66816 + ₹38976) <br>= 1013520 - 105792 = ₹907728<br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>907728</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = ₹113466</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. From the given numbers A, B, C and D, which number is NOT divisible by 11? <br>A = 712712 <br>B = 177210 <br>C = 64614 <br>D = 756148</p>",
                    question_hi: "<p>73. दी गई संख्याओं A, B, C ओं और D में से कौन-सी संख्या 11 से विभाज्य नहीं है?<br>A = 712712 <br>B = 177210 <br>C = 64614 <br>D = 756148</p>",
                    options_en: [
                        "<p>C</p>",
                        "<p>D</p>",
                        "<p>B</p>",
                        "<p>A</p>"
                    ],
                    options_hi: [
                        "<p>C</p>",
                        "<p>D</p>",
                        "<p>B</p>",
                        "<p>A</p>"
                    ],
                    solution_en: "<p>73.(b) Divisibility rule of 11 : Difference between sum of digit odd places and sum of digits at even places is either 0 or multiple of 11 <br>By checking all options one by one option (b) does not satisfy this condition <br>756148 = (7 + 6 + 4) - (5 + 1 + 8 ) <br>= 17 - 14 = 3 &ne; 0 , Multiple of 11<br><strong id=\"docs-internal-guid-363b66b1-7fff-dd8c-2cb2-33c5383a2334\"></strong>&there4; Option (b) is not divisible by 11</p>",
                    solution_hi: "<p>73.(b) 11 का विभाज्यता नियम: विषम स्थानों के अंकों के योग और सम स्थानों के अंकों के योग के बीच का अंतर या तो 0 है या 11 का गुणक है <br>सभी विकल्पों को एक-एक करके जांचने पर विकल्प (b) इस शर्त को पूरा नहीं करता है <br>756148 = (7 + 6 + 4) - (5 + 1 + 8 ) <br>= 17 - 14 = 3 &ne; 0 , 11 से विभाज्य<br><strong id=\"docs-internal-guid-af7628d2-7fff-68fd-60db-fc45763fde37\"></strong>&there4; विकल्प (b) 11 से विभाज्य नहीं है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The given pie-charts show the distribution of Graduate and Post-Graduate level students in five different colleges A, B, C, D and E.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551131409.png\" alt=\"rId54\" width=\"241\" height=\"160\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551131532.png\" alt=\"rId55\" width=\"258\" height=\"162\"> <br>How many students of colleges A and B are studying at Graduate level?</p>",
                    question_hi: "<p>74. दिए गए पाई-चार्ट पांच अलग-अलग महावि&zwnj;द्यालयों A, B, C, D और E में स्नातक और स्नातकोत्तर स्तर के छात्रों के वितरण को दर्शाते हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551131628.png\" alt=\"rId56\" width=\"256\" height=\"150\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1740551131738.png\" alt=\"rId57\" width=\"238\" height=\"157\"> <br>महाविद्यालय A और B के कितने छात्र स्नातक स्तर पर अध्ययन कर रहे हैं?</p>",
                    options_en: [
                        "<p>5,987</p>",
                        "<p>4,520</p>",
                        "<p>6,993</p>",
                        "<p>7,052</p>"
                    ],
                    options_hi: [
                        "<p>5,987</p>",
                        "<p>4,520</p>",
                        "<p>6,993</p>",
                        "<p>7,052</p>"
                    ],
                    solution_en: "<p>74.(c)<br>Students who studying at graduate level in both collage = 18% + 19% = 37%<br>100% = 18900<br>37% = 189 &times; 37 = 6993</p>",
                    solution_hi: "<p>74.(c)<br>दोनों महाविद्यालयों में स्नातक स्तर पर अध्ययन करने वाले छात्र = 18% + 19% = 37%<br>100% = 18900<br>37% = 189 &times; 37 = 6993</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. A seller marks an item for ₹500 and sells it at a discount of 25%. He also gives a gift worth ₹65. If he still makes 24% profit, then the cost price of the item (in ₹) is:</p>",
                    question_hi: "<p>75. एक विक्रेता एक वस्तु पर ₹500 का मूल्य अंकित करता है और उसे 25% की छूट पर बेचता है। वह ₹65 का उपहार भी देता है। यदि वह फिर भी 24% लाभ कमाता है, तो वस्तु का लागत मूल्य / क्रय मूल्य (₹ में) ज्ञात करें।</p>",
                    options_en: [
                        "<p>280</p>",
                        "<p>270</p>",
                        "<p>250</p>",
                        "<p>320</p>"
                    ],
                    options_hi: [
                        "<p>280</p>",
                        "<p>270</p>",
                        "<p>250</p>",
                        "<p>320</p>"
                    ],
                    solution_en: "<p>75.(c)<br>Marked price = 500 <br>selling price = 500 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> - 65<br>= 375 - 65 = 310<br>According to question,<br>124% (selling price) = 310<br>100% (cost price) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>310</mn><mn>124</mn></mfrac></math> &times; 100 = 250</p>",
                    solution_hi: "<p>75.(c)<br>अंकित मूल्य = 500 <br>विक्रय मूल्य = 500 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> - 65<br>= 375 - 65 = 310<br>प्रश्न के अनुसार,<br>124% (विक्रय मूल्य) = 310<br>100% (क्रय मूल्य) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>310</mn><mn>124</mn></mfrac></math> &times; 100 = 250</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The passengers were afraid, but the captain <span style=\"text-decoration: underline;\"><strong>consoled</strong></span> them that there was no danger.</p>",
                    question_hi: "<p>76. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The passengers were afraid, but the captain <span style=\"text-decoration: underline;\"><strong>consoled</strong></span> them that there was no danger.</p>",
                    options_en: [
                        "<p>guaranteed</p>",
                        "<p>assured</p>",
                        "<p>confided</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>guaranteed</p>",
                        "<p>assured</p>",
                        "<p>confided</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>76.(b) assured<br>&lsquo;Assure&rsquo; means to make a statement, assertion, etc, intended to inspire confidence or give encouragement. The given sentence states that the passengers were afraid, but the captain inspired confidence in them. Hence, &lsquo;assured&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(b) assured<br>&lsquo;Assure&rsquo; का अर्थ आत्मविश्वास को प्रेरित करने के उद्देश्य से एक बयान या दावा आदि करना । दिए गए वाक्य में कहा गया है कि यात्री डरे हुए थे, लेकिन कप्तान ने उनमें विश्वास जगाया। इसलिए, &lsquo;Assure&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate synonym of the given word.<br>Filthy</p>",
                    question_hi: "<p>77. Select the most appropriate synonym of the given word.<br>Filthy</p>",
                    options_en: [
                        "<p>Tidy</p>",
                        "<p>Shiny</p>",
                        "<p>Dirty</p>",
                        "<p>Visual</p>"
                    ],
                    options_hi: [
                        "<p>Tidy</p>",
                        "<p>Shiny</p>",
                        "<p>Dirty</p>",
                        "<p>Visual</p>"
                    ],
                    solution_en: "<p>77.(c) <strong>Dirty-</strong> covered with or characterized by dirt or impurities.<br><strong>Filthy-</strong> extremely dirty or unpleasant.<br><strong>Tidy-</strong> neat and well-organized.<br><strong>Shiny- </strong>reflecting light, bright, or polished.<br><strong>Visual-</strong> relating to seeing or sight.</p>",
                    solution_hi: "<p>77.(c) <strong>Dirty (गंदा) </strong>- covered with or characterized by dirt or impurities.<br><strong>Filthy (अपवित्र)</strong> - extremely dirty or unpleasant.<br><strong>Tidy (साफ)</strong> - neat and well-organized.<br><strong>Shiny (चमकदार) - </strong>reflecting light, bright, or polished.<br><strong>Visual (दृश्य)</strong> - relating to seeing or sight.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>If I have recovered from fever, / I will start going / for swimming classes / from tomorrow.</p>",
                    question_hi: "<p>78. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>If I have recovered from fever, / I will start going / for swimming classes / from tomorrow.</p>",
                    options_en: [
                        "<p>I will start going</p>",
                        "<p>If I have recovered from fever</p>",
                        "<p>from tomorrow</p>",
                        "<p>for swimming classes</p>"
                    ],
                    options_hi: [
                        "<p>I will start going</p>",
                        "<p>If I have recovered from fever</p>",
                        "<p>from tomorrow</p>",
                        "<p>for swimming classes</p>"
                    ],
                    solution_en: "<p>78.(b) If I have recovered from fever <br>The given sentence is an example of the first conditional sentence and &lsquo;If + Simple present &hellip; will + V1&rsquo; is the correct grammatical structure for it. Hence, &lsquo;If I recover from fever&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(b) If I have recovered from fever <br>दिया गया sentence first conditional sentence का example है और &lsquo;If + Simple present &hellip; will + V1&rsquo; इसके लिए सही grammatical structure है। इसलिए, &lsquo;If I recover from fever&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>The beggar was satisfied with his <span style=\"text-decoration: underline;\"><strong>lowly</strong></span> meal.</p>",
                    question_hi: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>The beggar was satisfied with his <span style=\"text-decoration: underline;\"><strong>lowly</strong></span> meal.</p>",
                    options_en: [
                        "<p>miserly</p>",
                        "<p>mean</p>",
                        "<p>meager</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>miserly</p>",
                        "<p>mean</p>",
                        "<p>meager</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>79.(c) meager<br>An adjective(meager) is to be used in the given sentence as it describes how the meal is. However, &lsquo;Lowly&rsquo; and &lsquo;miserly&rsquo; are adverbs. Hence, &lsquo;meager&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>79.(c) meager<br>दिए गए वाक्य में एक adjective(meager) का उपयोग किया जाना है क्योंकि यह वर्णन करता है कि भोजन कैसा है- (how the meal is?) हालाँकि, &lsquo;Lowly&rsquo; और &lsquo;miserly&rsquo; adverbs है। अतः, &lsquo;meager&rsquo; सर्वाधिक उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Identify the segment in the sentence, which contains the grammatical error.<br>The boy entered into the college premises by jumping over the high walls near the badminton courts.</p>",
                    question_hi: "<p>80. Identify the segment in the sentence, which contains the grammatical error.<br>The boy entered into the college premises by jumping over the high walls near the badminton courts.</p>",
                    options_en: [
                        "<p>The boy entered into the college</p>",
                        "<p>premises by jumping over the high walls</p>",
                        "<p>near the badminton courts</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>The boy entered into the college</p>",
                        "<p>premises by jumping over the high walls</p>",
                        "<p>near the badminton courts</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>80.(a) The boy entered into the college<br>We don\'t use the prepositions &lsquo;into/in&rsquo; after the verb &lsquo;entered&rsquo;. Hence, &lsquo;into&rsquo; will be removed from the given sentence and &lsquo;The boy entered the school&rsquo; becomes the most appropriate answer.</p>",
                    solution_hi: "<p>80.(a) The boy entered into the college<br>Verb \'enter\' के बाद हम \'into/in\' preposition का उपयोग नहीं करते हैं। अतः दिए गए वाक्य में से &lsquo;into&rsquo; हटा दिया जायेगा और &lsquo;The boy entered the school&rsquo; सबसे उपयुक्त उत्तर हो जायेगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Rajan&rsquo;s caused <span style=\"text-decoration: underline;\"><strong>extremely painful</strong></span> headaches.</p>",
                    question_hi: "<p>81. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Rajan&rsquo;s caused <span style=\"text-decoration: underline;\"><strong>extremely painful</strong></span> headaches.</p>",
                    options_en: [
                        "<p>excruciating</p>",
                        "<p>devastating</p>",
                        "<p>horrible</p>",
                        "<p>extreme</p>"
                    ],
                    options_hi: [
                        "<p>excruciating</p>",
                        "<p>devastating</p>",
                        "<p>horrible</p>",
                        "<p>extreme</p>"
                    ],
                    solution_en: "<p>81.(a)<strong> Excruciating </strong>- extremely painful.</p>",
                    solution_hi: "<p>81.(a) <strong>Excruciating</strong> - extremely painful./अत्यंत दर्दनाक</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the option that expresses the given sentence in passive voice <br>Paddy is grown by the farmers of this village.</p>",
                    question_hi: "<p>82. Select the option that expresses the given sentence in passive voice <br>Paddy is grown by the farmers of this village.</p>",
                    options_en: [
                        "<p>The farmers of this village grew paddy.</p>",
                        "<p>The farmers of this village have grown paddy.</p>",
                        "<p>The farmers of this village grow paddy.</p>",
                        "<p>The farmers of this village had grown Paddy</p>"
                    ],
                    options_hi: [
                        "<p>The farmers of this village grew paddy.</p>",
                        "<p>The farmers of this village have grown paddy.</p>",
                        "<p>The farmers of this village grow paddy.</p>",
                        "<p>The farmers of this village had grown Paddy</p>"
                    ],
                    solution_en: "<p>82.(c) The farmers of this village grow paddy. (Correct)<br>(a) The farmers of this village <span style=\"text-decoration: underline;\">grew</span> paddy. (Incorrect Tense)<br>(b) The farmers of this village <span style=\"text-decoration: underline;\">have grown</span> paddy. (Incorrect Helping Verb)<br>(d) The farmers of this village <span style=\"text-decoration: underline;\">had grown</span> paddy. (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>82.(c) The farmers of this village grow paddy. (Correct)<br>(a) The farmers of this village <span style=\"text-decoration: underline;\">grew</span> paddy. (गलत Tense)<br>(b) The farmers of this village <span style=\"text-decoration: underline;\">have grown</span> paddy. (गलत Helping Verb)<br>(d) The farmers of this village <span style=\"text-decoration: underline;\">had grown</span> paddy. (गलत Helping Verb)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83.Given below are four jumbled sentences. Pick the option that gives their correct order. <br>P. It is the ornament of a gentleman and a weapon in the hands of a boss.<br>Q. To fulfil your assignments on time is punctuality.<br>R. They finish their job in time and are able to give finishing touches needed for the job.<br>S. Those who are punctual are loved and liked by all.</p>",
                    question_hi: "<p>83.Given below are four jumbled sentences. Pick the option that gives their correct order. <br>P. It is the ornament of a gentleman and a weapon in the hands of a boss.<br>Q. To fulfil your assignments on time is punctuality.<br>R. They finish their job in time and are able to give finishing touches needed for the job.<br>S. Those who are punctual are loved and liked by all.</p>",
                    options_en: [
                        "<p>PRSQ</p>",
                        "<p>PQRS</p>",
                        "<p>QSPR</p>",
                        "<p>QPSR</p>"
                    ],
                    options_hi: [
                        "<p>PRSQ</p>",
                        "<p>PQRS</p>",
                        "<p>QSPR</p>",
                        "<p>QPSR</p>"
                    ],
                    solution_en: "<p>83.(d) <strong>QPSR.</strong> <br>Sentence Q will be the starting line as it contains the main idea of the parajumble i.e. punctuality. However, Sentence P states that it is the ornament of a gentleman and a weapon in the hands of a boss. So, P will follow Q . Further, Sentence S states that those who are punctual are loved and liked by all &amp; Sentence R states that they finish their job in time and are able to give finishing touches needed for the job. So, R will follow S. Going through the options, only option d has the correct sequence.</p>",
                    solution_hi: "<p>83.(d) <strong>QPSR.</strong><br>वाक्य Q शुरुआती line होगी क्योंकि इसमें parajumble का मुख्य विचार है - समय की पाबंदी । वाक्य P कहता है कि Punctuality मालिक (boss) के हाथ में एक हथियार है और यह एक सज्जन का आभूषण है । तो, Q के बाद P आयेगा । आगे, वाक्य S कहता है कि जो लोग समय के पाबंद हैं वे सभी के द्वारा पसंद किए जाते हैं और वाक्य R बताता है कि वे अपना काम समय पर पूरा करते हैं और अपने काम को आवश्यक अंतिम रूप देने में सक्षम हैं। तो S के बाद R आयेगा । विकल्पों के माध्यम से जाने पर, केवल विकल्प d में सही क्रम है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the option that expresses the given sentence in passive voice.<br>Doctors saved many lives during the pandemic.</p>",
                    question_hi: "<p>84. Select the option that expresses the given sentence in passive voice.<br>Doctors saved many lives during the pandemic.</p>",
                    options_en: [
                        "<p>Many lives are saved by doctors during the pandemic.</p>",
                        "<p>Many lives were saved by doctors during the pandemic.</p>",
                        "<p>Many lives have been saved by doctors during the pandemic.</p>",
                        "<p>Many lives were being saved by doctors during the pandemic.</p>"
                    ],
                    options_hi: [
                        "<p>Many lives are saved by doctors during the pandemic.</p>",
                        "<p>Many lives were saved by doctors during the pandemic.</p>",
                        "<p>Many lives have been saved by doctors during the pandemic.</p>",
                        "<p>Many lives were being saved by doctors during the pandemic.</p>"
                    ],
                    solution_en: "<p>84.(b) Many lives were saved by doctors during the pandemic. (Correct)<br>(a) Many lives <span style=\"text-decoration: underline;\">are saved</span> by doctors during the pandemic. (Incorrect Tense)<br>(c) Many lives <span style=\"text-decoration: underline;\">have been saved</span> by doctors during the pandemic. (Incorrect Tense)<br>(d) Many lives <span style=\"text-decoration: underline;\">were being</span> saved by doctors during the pandemic. (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>84.(b) Many lives were saved by doctors during the pandemic. (Correct)<br>(a) Many lives <span style=\"text-decoration: underline;\">are saved</span> by doctors during the pandemic. (गलत Tense)<br>(c) Many lives <span style=\"text-decoration: underline;\">have been saved</span> by doctors during the pandemic. (गलत Tense)<br>(d) Many lives <span style=\"text-decoration: underline;\">were being</span> saved by doctors during the pandemic. (गलत Helping Verb)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Identify the misspelled word</p>",
                    question_hi: "<p>85. Identify the misspelled word</p>",
                    options_en: [
                        "<p>Hallucination</p>",
                        "<p>Dubiuos</p>",
                        "<p>Argot</p>",
                        "<p>Placebo</p>"
                    ],
                    options_hi: [
                        "<p>Hallucination</p>",
                        "<p>Dubiuos</p>",
                        "<p>Argot</p>",
                        "<p>Placebo</p>"
                    ],
                    solution_en: "<p>85.(b) Dubiuos<br>&lsquo;Dubious&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>85.(b) Dubiuos<br>&lsquo;Dubious&rsquo; यह सही spelling होगी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Parts of a paragraph are given below in jumbled order. Arrange the parts in the correct order to form a meaningful paragraph. The first part \'R.\' is fixed.<br>R. The knowledge so far<br>A. Available about the endocrine gland is very limited.<br>B. Hormones are the body&rsquo;s chemical messengers.<br>C. These are the glands that make all the hormones.<br>D. They influence almost every cell, organ and function of our bodies.</p>",
                    question_hi: "<p>86. Parts of a paragraph are given below in jumbled order. Arrange the parts in the correct order to form a meaningful paragraph. The first part \'R.\' is fixed.<br>R. The knowledge so far<br>A. Available about the endocrine gland is very limited.<br>B. Hormones are the body&rsquo;s chemical messengers.<br>C. These are the glands that make all the hormones.<br>D. They influence almost every cell, organ and function of our bodies.</p>",
                    options_en: [
                        "<p>CABD</p>",
                        "<p>BCDA</p>",
                        "<p>ACBD</p>",
                        "<p>ABCD</p>"
                    ],
                    options_hi: [
                        "<p>CABD</p>",
                        "<p>BCDA</p>",
                        "<p>ACBD</p>",
                        "<p>ABCD</p>"
                    ],
                    solution_en: "<p>86.(c) ACBD<br>The given sentence starts with part R as it introduces the main subject of the sentence, i.e. &lsquo;Knowledge&rsquo;. Part R will be followed by part A as it states that the knowledge about the endocrine gland is limited. Part C states that these glands make all the hormones &amp; part B defines hormones. So, B will follow C. Part D concludes the parajumble by stating that hormones influence almost every cell, organ and function of our bodies. Going through the options, option (c) has the correct sequence.</p>",
                    solution_hi: "<p>86.(c) ACBD<br>दिया गया sentence part R से शुरू होता है क्योंकि यह sentence के main subject &lsquo;Knowledge&rsquo; का परिचय देता है। part R के बाद part A आएगा क्योंकि यह बताता है कि अंतःस्रावी ग्रंथि के बारे में ज्ञान सीमित है। part C बताता है कि ये ग्रंथियाँ सभी हार्मोन बनाती हैं और part B हार्मोन को परिभाषित करता है। इसलिए, C के बाद B आएगा। part D यह बताकर parajumble का समापन करता है कि हार्मोन हमारे शरीर की लगभग हर कोशिका, अंग और कार्य को प्रभावित करते हैं। विकल्पों को देखते हुए, option (c) में सही क्रम है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Based on the situation in the sentence, select the most appropriate idiom for the underlined segment. After the Pandemic, his business crumbled and <span style=\"text-decoration: underline;\">he is in a difficult situation</span>.</p>",
                    question_hi: "<p>87. Based on the situation in the sentence, select the most appropriate idiom for the underlined segment. After the Pandemic, his business crumbled and <span style=\"text-decoration: underline;\">he is in a difficult situation</span>.</p>",
                    options_en: [
                        "<p>Be in a tight corner</p>",
                        "<p>Give a cold shoulder</p>",
                        "<p>Bolt from the blue</p>",
                        "<p>Pull the last straw</p>"
                    ],
                    options_hi: [
                        "<p>Be in a tight corner</p>",
                        "<p>Give a cold shoulder</p>",
                        "<p>Bolt from the blue</p>",
                        "<p>Pull the last straw</p>"
                    ],
                    solution_en: "<p>87.(a) <strong>Be in a tight corner</strong> - in a difficult situation.</p>",
                    solution_hi: "<p>87.(a) <strong>Be in a tight corner </strong>- in a difficult situation./ किसी कठिन परिस्थिति में होना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>88. Select the INCORRECTLY spelt word.</p>",
                    options_en: [
                        "<p>Knowledgeable</p>",
                        "<p>Hierarchy</p>",
                        "<p>Neccessity</p>",
                        "<p>Perseverance</p>"
                    ],
                    options_hi: [
                        "<p>Knowledgeable</p>",
                        "<p>Hierarchy</p>",
                        "<p>Neccessity</p>",
                        "<p>Perseverance</p>"
                    ],
                    solution_en: "<p>88.(c) <strong>Neccessity</strong><br>&lsquo;Necessity&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>88.(c) <strong>Neccessity</strong><br>&lsquo;Necessity&rsquo; सही spelling है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate meaning of the idiom.<br>Show the white feather</p>",
                    question_hi: "<p>89. Select the most appropriate meaning of the idiom.<br>Show the white feather</p>",
                    options_en: [
                        "<p>show signs of cowardice</p>",
                        "<p>act arrogantly</p>",
                        "<p>show intimacy</p>",
                        "<p>act impudently</p>"
                    ],
                    options_hi: [
                        "<p>show signs of cowardice</p>",
                        "<p>act arrogantly</p>",
                        "<p>show intimacy</p>",
                        "<p>act impudently</p>"
                    ],
                    solution_en: "<p>89.(a) show signs of cowardice<br>Show the white feather- show signs of cowardice<br>E.g.- A brave soldier will never show the white feather in the face of his enemy.</p>",
                    solution_hi: "<p>89.(a) show signs of cowardice<br>Show the white feather- कायरता के लक्षण दिखना।<br>E.g.- A brave soldier will never show the white feather in the face of his enemy./ एक बहादुर सैनिक अपने दुश्मन के सामने कायरता के लक्षण कभी नहीं दिखाएगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that can be used as a one-word substitute for the given group of words. <br>A story intended to teach moral lessons</p>",
                    question_hi: "<p>90. Select the option that can be used as a one-word substitute for the given group of words. <br>A story intended to teach moral lessons</p>",
                    options_en: [
                        "<p>Parable</p>",
                        "<p>Parole</p>",
                        "<p>Parasite</p>",
                        "<p>Parboil</p>"
                    ],
                    options_hi: [
                        "<p>Parable</p>",
                        "<p>Parole</p>",
                        "<p>Parasite</p>",
                        "<p>Parboil</p>"
                    ],
                    solution_en: "<p>90.(a) <strong>Parable-</strong> a story intended to teach moral lessons.<br><strong>Parole- </strong>the temporary or permanent release of a prisoner before the expiry of a sentence, on the promise of good behaviour.<br><strong>Parasite- </strong>an animal or plant that lives on or in another animal or plant of a different type and feeds from it.<br><strong>Parboil- </strong>to boil food for a short time until it is partly cooked.</p>",
                    solution_hi: "<p>90.(a) <strong>Parable</strong> (दृष्टान्त)- a story intended to teach moral lessons.<br><strong>Parole </strong>(पैरोल)- the temporary or permanent release of a prisoner before the expiry of a sentence, on the promise of good behaviour.<br><strong>Parasite</strong> (परजीवी)- an animal or plant that lives on or in another animal or plant of a different type and feeds from it.<br><strong>Parboil</strong> (आंशिक उबालना) - to boil food for a short time until it is partly cooked.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate synonym of the given word.<br>Esoteric</p>",
                    question_hi: "<p>91. Select the most appropriate synonym of the given word.<br>Esoteric</p>",
                    options_en: [
                        "<p>setback</p>",
                        "<p>pondered</p>",
                        "<p>recondite</p>",
                        "<p>fruitful</p>"
                    ],
                    options_hi: [
                        "<p>setback</p>",
                        "<p>pondered</p>",
                        "<p>recondite</p>",
                        "<p>fruitful</p>"
                    ],
                    solution_en: "<p>91.(c) <strong>Recondite - </strong>little known or abstruse<br><strong>Esoteric- </strong>having a hidden or secret meaning or known about or understood by very few people.<br><strong>Pondered-</strong> to think about something carefully for a long time<br><strong>Setback-</strong> a reversal or check - in progress or a change from better to worse.<br><strong>Fruitful-</strong> producing good and useful results.</p>",
                    solution_hi: "<p>91.(c) <strong>Recondite -</strong> अल्पज्ञात <br><strong>Esoteric- </strong>एक छिपा हुआ या गुप्त अर्थ होना या जिसके बारे में बहुत कम लोग जानते या समझते हैं।<br><strong>Pondered- </strong>किसी बात के बारे में ध्यान से बहुत देर तक सोचना<br><strong>Setback-</strong> नाकामयाबी, बाधा या बेहतर से बदतर में परिवर्तन।<br><strong>Fruitful- </strong>अच्छे और उपयोगी परिणाम देने वाला।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate ANTONYM of the underlined word.<br>Rukhsana was left alone in the house again, but fear was now her <span style=\"text-decoration: underline;\">constant</span> companion.</p>",
                    question_hi: "<p>92. Select the most appropriate ANTONYM of the underlined word.<br>Rukhsana was left alone in the house again, but fear was now her <span style=\"text-decoration: underline;\">constant</span> companion.</p>",
                    options_en: [
                        "<p>boring</p>",
                        "<p>steady</p>",
                        "<p>changing</p>",
                        "<p>friendly</p>"
                    ],
                    options_hi: [
                        "<p>boring</p>",
                        "<p>steady</p>",
                        "<p>changing</p>",
                        "<p>friendly</p>"
                    ],
                    solution_en: "<p>92.(c) <strong>Changing-</strong> becoming different.<br><strong>Constant- </strong>staying the same without change.<br><strong>Boring-</strong> not interesting.<br><strong>Steady- </strong>firmly fixed and not changing.<br><strong>Friendly- </strong>acting in a kind and supportive way.</p>",
                    solution_hi: "<p>92.(c) <strong>Changing</strong> (परिवर्तनशील) - becoming different.<br><strong>Constant</strong> (अपरिवर्तनीय) - staying the same without change.<br><strong>Boring </strong>(उबाऊ) - not interesting.<br><strong>Steady</strong> (स्थिर) - firmly fixed and not changing.<br><strong>Friendly </strong>(मिलनसार) - acting in a kind and supportive way.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate ANTONYM of the underlined word.<br>This book is written by a <span style=\"text-decoration: underline;\">famous</span> author named G.H. Sullivan.</p>",
                    question_hi: "<p>93. Select the most appropriate ANTONYM of the underlined word.<br>This book is written by a <span style=\"text-decoration: underline;\">famous</span> author named G.H. Sullivan.</p>",
                    options_en: [
                        "<p>illegible</p>",
                        "<p>unknown</p>",
                        "<p>uniform</p>",
                        "<p>illiterate</p>"
                    ],
                    options_hi: [
                        "<p>illegible</p>",
                        "<p>unknown</p>",
                        "<p>uniform</p>",
                        "<p>illiterate</p>"
                    ],
                    solution_en: "<p>93.(b) <strong>Unknown- </strong>not recognized or famous.<br><strong>Famous- </strong>widely known and respected.<br><strong>Illegible-</strong> not clear enough to be read.<br><strong>Uniform- </strong>always the same without change.<br><strong>Illiterate-</strong> unable to read or write.</p>",
                    solution_hi: "<p>93.(b) <strong>Unknown</strong> (अज्ञात) - not recognized or famous.<br><strong>Famous</strong> (प्रसिद्ध) - widely known and respected.<br><strong>Illegible</strong> (अपठनीय) - not clear enough to be read.<br><strong>Uniform </strong>(एकसमान) - always the same without change.<br><strong>Illiterate </strong>(निरक्षर) - unable to read or write.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank.<br>The roof of the old building _______ during the storm.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank.<br>The roof of the old building _______ during the storm.</p>",
                    options_en: [
                        "<p>demolished</p>",
                        "<p>collapsed</p>",
                        "<p>scratched</p>",
                        "<p>destroyed</p>"
                    ],
                    options_hi: [
                        "<p>demolished</p>",
                        "<p>collapsed</p>",
                        "<p>scratched</p>",
                        "<p>destroyed</p>"
                    ],
                    solution_en: "<p>94.(b) <strong>Collapsed</strong> - breakdown, fail suddenly and completely<br>(a) <strong>Demolished </strong>- pull or knockdown (a building)<br>(c) <strong>Scratched -</strong> score or mark the surface of (something) with a sharp or pointed object.<br>(d) <strong>Destroyed -</strong> end the existence by damaging or attacking it.</p>",
                    solution_hi: "<p>94.(b) <strong>Collapsed -</strong> ढहना - breakdown, fail suddenly and completely<br>(a) <strong>Demolished - </strong>ध्वस्त - pull or knockdown (a building)<br>(c) <strong>Scratched -</strong> खुरचा हुआ - score or mark the surface of (something) with a sharp or pointed object.<br>(d)<strong> Destroyed -</strong> तबाह - end the existence by damaging or attacking it.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>Even a _______ glance will reveal the mystery.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>Even a _______ glance will reveal the mystery.</p>",
                    options_en: [
                        "<p>crude</p>",
                        "<p>critical</p>",
                        "<p>cursory</p>",
                        "<p>curious</p>"
                    ],
                    options_hi: [
                        "<p>crude</p>",
                        "<p>critical</p>",
                        "<p>cursory</p>",
                        "<p>curious</p>"
                    ],
                    solution_en: "<p>95.(c) cursory<br>&lsquo;Cursory&rsquo; means done in a hurry. The given sentence states that even a cursory glance will reveal the mystery. Hence, &lsquo;cursory&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(c) cursory/ सरसरी<br>\'Cursory\' का अर्थ है जल्दी में किया हुआ। दिए गए वाक्य में कहा गया है कि सरसरी निगाह डालने से भी रहस्य खुल जाएगा। इसलिए, \'cursory\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test</strong><br>While threatening the ___(96)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(97)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(98)___borders. Even within the ranks of territorial nation-states, the conditions for___(99)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(100)___for arithmetical majorities.<br>Select the most appropriate option to fill in the blank no. 96</p>",
                    question_hi: "<p>96. <strong>Cloze Test</strong><br>While threatening the ___(96)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(97)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(98)___borders. Even within the ranks of territorial nation-states, the conditions for___(99)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(100)___for arithmetical majorities.<br>Select the most appropriate option to fill in the blank no. 96</p>",
                    options_en: [
                        "<p>integration</p>",
                        "<p>integrity</p>",
                        "<p>ingratiation</p>",
                        "<p>inability</p>"
                    ],
                    options_hi: [
                        "<p>integration</p>",
                        "<p>integrity</p>",
                        "<p>ingratiation</p>",
                        "<p>inability</p>"
                    ],
                    solution_en: "<p>96.(b) integrity<br>We will use a &ldquo;noun&rdquo; after the article &ldquo;the.&rdquo;</p>",
                    solution_hi: "<p>96.(b) integrity /अखंडता<br>हम Article \"the के बाद \"noun\" का उपयोग करेंगे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test</strong><br>While threatening the ___(96)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(97)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(98)___borders. Even within the ranks of territorial nation-states, the conditions for___(99)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(100)___for arithmetical majorities.<br>Select the most appropriate option to fill in the blank no. 97</p>",
                    question_hi: "<p>97. <strong>Cloze Test</strong><br>While threatening the ___(96)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(97)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(98)___borders. Even within the ranks of territorial nation-states, the conditions for___(99)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(100)___for arithmetical majorities.<br>Select the most appropriate option to fill in the blank no. 97</p>",
                    options_en: [
                        "<p>destabilized</p>",
                        "<p>disintegrated</p>",
                        "<p>demonstrated</p>",
                        "<p>disdained</p>"
                    ],
                    options_hi: [
                        "<p>destabilized</p>",
                        "<p>disintegrated</p>",
                        "<p>demonstrated</p>",
                        "<p>disdained</p>"
                    ],
                    solution_en: "<p>97.(c) <strong>demonstrated.</strong><br>Demonstrated means to show something clearly by giving proof.</p>",
                    solution_hi: "<p>97.(c) <strong>demonstrated.</strong><br>Demonstrated का अर्थ है प्रमाण देकर किसी बात को स्पष्ट रूप से दिखाना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test</strong><br>While threatening the ___(96)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(97)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(98)___borders. Even within the ranks of territorial nation-states, the conditions for___(99)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(100)___for arithmetical majorities.<br>Select the most appropriate option to fill in the blank no. 98</p>",
                    question_hi: "<p>98. <strong>Cloze Test</strong><br>While threatening the ___(96)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(97)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(98)___borders. Even within the ranks of territorial nation-states, the conditions for___(99)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(100)___for arithmetical majorities.<br>Select the most appropriate option to fill in the blank no. 98</p>",
                    options_en: [
                        "<p>under</p>",
                        "<p>across</p>",
                        "<p>over</p>",
                        "<p>cross</p>"
                    ],
                    options_hi: [
                        "<p>under</p>",
                        "<p>across</p>",
                        "<p>over</p>",
                        "<p>cross</p>"
                    ],
                    solution_en: "<p>98.(b) across.</p>",
                    solution_hi: "<p>98.(b) across.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test</strong><br>While threatening the ___(96)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(97)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(98)___borders. Even within the ranks of territorial nation-states, the conditions for___(99)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(100)___for arithmetical majorities.<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    question_hi: "<p>99. <strong>Cloze Test</strong><br>While threatening the ___(96)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(97)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(98)___borders. Even within the ranks of territorial nation-states, the conditions for___(99)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(100)___for arithmetical majorities.<br>Select the most appropriate option to fill in the blank no. 99</p>",
                    options_en: [
                        "<p>effable</p>",
                        "<p>effective</p>",
                        "<p>effusive</p>",
                        "<p>effervescent</p>"
                    ],
                    options_hi: [
                        "<p>effable</p>",
                        "<p>effective</p>",
                        "<p>effusive</p>",
                        "<p>effervescent</p>"
                    ],
                    solution_en: "<p>99.(b) effective.</p>",
                    solution_hi: "<p>99.(b) effective./प्रभावी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test</strong><br>While threatening the ___(96)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(97)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(98)___borders. Even within the ranks of territorial nation-states, the conditions for___(99)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(100)___for arithmetical majorities.<br>Select the most appropriate option to fill in the blank no. 100</p>",
                    question_hi: "<p>100. <strong>Cloze Test</strong><br>While threatening the ___(96)___ of universal values, the campaign to spread democracy will not succeed.The twentieth century___(97)___that states could not simply remake the world or abbreviate historical transformations. Nor can they easily effect social change by transferring institutions ___(98)___borders. Even within the ranks of territorial nation-states, the conditions for___(99)___ democratic governments are rare: an existing state enjoying legitimacy, consent, and the ability to mediate conflicts between groups. Without such consensus, there are no single sovereign people, and therefore no___(100)___for arithmetical majorities.<br>Select the most appropriate option to fill in the blank no. 100</p>",
                    options_en: [
                        "<p>decency</p>",
                        "<p>parity</p>",
                        "<p>legitimacy</p>",
                        "<p>effectiveness</p>"
                    ],
                    options_hi: [
                        "<p>decency</p>",
                        "<p>parity</p>",
                        "<p>legitimacy</p>",
                        "<p>effectiveness</p>"
                    ],
                    solution_en: "<p>100.(c) legitimacy.</p>",
                    solution_hi: "<p>100.(c) legitimacy./वैधता।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>