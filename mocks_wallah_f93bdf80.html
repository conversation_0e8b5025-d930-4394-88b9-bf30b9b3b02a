<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["6"] = {
                name: "Reasoning",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "1. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br />Since the little girl answers / a few simple questions, / her parents say that / she is too intelligent.  ",
                    question_hi: "1. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br />Since the little girl answers / a few simple questions, / her parents say that / she is too intelligent.  ",
                    options_en: [" her parents say that  ", " a few simple questions  ", 
                                " Since the little girl answers  ", " she is too intelligent"],
                    options_hi: [" her parents say that  ", " a few simple questions  ",
                                " Since the little girl answers  ", " she is too intelligent"],
                    solution_en: "1.(d) she is too intelligent<br />‘Too’ must be replaced with ‘very’. ‘Too’ is used in a sentence with a negative sense. Hence ‘she is very intelligent’ is the most appropriate answer.",
                    solution_hi: "1.(d) she is too intelligent<br />‘Too’ के स्थान पर ‘very’ का प्रयोग होगा। ‘Too’ का प्रयोग किसी sentence में Negative sense में किया जाता है। अतः ‘she is very intelligent’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "2. Select the option that corrects the following sentence. <br />What are you looking around?",
                    question_hi: "2. Select the option that corrects the following sentence. <br />What are you looking around?",
                    options_en: [" What are you looking among? ", " What are you looking for?", 
                                " What are you looking through? ", " What are you looking across?"],
                    options_hi: [" What are you looking among? ", " What are you looking for?",
                                " What are you looking through? ", " What are you looking across?"],
                    solution_en: "2.(b) What are you looking for<br />‘Looking for’ is the correct phrasal verb, which means to search for something. Hence, the sentence given in option (b) is correct.",
                    solution_hi: "2.(b) What are you looking for<br />‘Looking for’ सही phrasal verb है, जिसका अर्थ है किसी चीज़ की खोज करना। अतः, option (b) में दिया गया sentence सही है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />It is difficult to live in / the societal expectations / when one has / fewer resources. <br />A B C D ",
                    question_hi: "3. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />It is difficult to live in / the societal expectations / when one has / fewer resources. <br />A B C D ",
                    options_en: [" C ", " A ", 
                                " B ", " D"],
                    options_hi: [" C ", " A ",
                                " B ", " D"],
                    solution_en: "3.(b) A - It is difficult to live in<br />‘Live in’ must be replaced by ‘Live up to’. ‘Live up to’ means to meet or match the expectations set by someone or something. Hence ‘It is difficult to live up to’ is the most appropriate answer.",
                    solution_hi: "3.(b) A - It is difficult to live in<br />‘Live in’ के स्थान पर ‘Live up to’ का प्रयोग होगा। ‘Live up to’ का अर्थ है किसी व्यक्ति या चीज़ द्वारा निर्धारित अपेक्षाओं (expectations) को पूरा करना या उनसे मेल खाना। अतः ‘It is difficult to live up to’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. The following sentence has been divided into four segments. Identify the segment that contains an error in the usage of indefinite articles. <br>We took / an ice / and applied it on / the wound.</p>",
                    question_hi: "<p>4. The following sentence has been divided into four segments. Identify the segment that contains an error in the usage of indefinite articles. <br>We took / an ice / and applied it on / the wound.</p>",
                    options_en: ["<p>and applied it on</p>", "<p>the wound.</p>", 
                                "<p>We took</p>", "<p>an ice</p>"],
                    options_hi: ["<p>and applied it on</p>", "<p>the wound.</p>",
                                "<p>We took</p>", "<p>an ice</p>"],
                    solution_en: "<p>4.(d)<strong> an ice</strong><br>We do not use indefinite articles (a/an) before uncountable nouns such as ice, luggage, information, furniture. To express the quantity of uncountable nouns, we use expressions like &lsquo;some, a lot of, much, a piece of&rsquo;. Hence &lsquo;a piece of ice&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(d)<strong> an ice</strong><br>हम ice, luggage, information, furniture जैसे uncountable nouns से पहले indefinite articles (a/an) का प्रयोग नहीं करते हैं। Uncountable nouns की quantity को व्यक्त (express) करने के लिए, हम &lsquo;some, a lot of, much, a piece of&rsquo; जैसे expressions का प्रयोग करते हैं। अतः &lsquo;a piece of ice&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <strong>Cloze Test :</strong> <br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (5)._____ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (6).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (7)._____ interests and inclinations. We are not likely to abandon the city as a (8).______ institution, but we need to make sure that our transport arrangements do not (9)._____ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 5.</p>",
                    question_hi: "<p>5. <strong>Cloze Test :</strong> <br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (5)._____ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (6).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (7)._____ interests and inclinations. We are not likely to abandon the city as a (8).______ institution, but we need to make sure that our transport arrangements do not (9)._____ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 5.</p>",
                    options_en: ["<p>lazy</p>", "<p>small</p>", 
                                "<p>large</p>", "<p>dump</p>"],
                    options_hi: ["<p>lazy</p>", "<p>small</p>",
                                "<p>large</p>", "<p>dump</p>"],
                    solution_en: "<p>5.(c) large<br>The given passage states that Vibrant cultures are found in cities because it takes a large population to support museums, concert halls, sports teams, and night-life districts. Hence &lsquo;large&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(c) large<br>दिए गए passage में बताया गया है कि जीवंत संस्कृतियाँ (Vibrant cultures) शहरों में पाई जाती हैं क्योंकि संग्रहालयों (museums), concert halls, sports teams और night-life districts का समर्थन करने के लिए एक बड़ी आबादी (large population) की आवश्यकता होती है। अतः &lsquo;large&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <strong>Cloze Test :</strong> <br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (5)._____ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (6).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (7)._____ interests and inclinations. We are not likely to abandon the city as a (8).______ institution, but we need to make sure that our transport arrangements do not (9)._____ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 6</p>",
                    question_hi: "<p>6. <strong>Cloze Test :</strong> <br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (5)._____ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (6).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (7)._____ interests and inclinations. We are not likely to abandon the city as a (8).______ institution, but we need to make sure that our transport arrangements do not (9)._____ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 6</p>",
                    options_en: ["<p>of</p>", "<p>with</p>", 
                                "<p>for</p>", "<p>off</p>"],
                    options_hi: ["<p>of</p>", "<p>with</p>",
                                "<p>for</p>", "<p>off</p>"],
                    solution_en: "<p>6.(a) of<br>We generally use &lsquo;of &rsquo; with &lsquo;because&rsquo; to present the reason for an action. Hence &lsquo;of&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(a) of<br>हम सामान्यतः किसी Action के reason को बताने के लिए &lsquo;of&rsquo; के साथ &lsquo;because&rsquo; का प्रयोग करते हैं। अतः &lsquo;of&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <strong>Cloze Test :</strong> <br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (5)._____ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (6).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (7)._____ interests and inclinations. We are not likely to abandon the city as a (8).______ institution, but we need to make sure that our transport arrangements do not (9)._____ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 7</p>",
                    question_hi: "<p>7. <strong>Cloze Test :</strong> <br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (5)._____ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (6).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (7)._____ interests and inclinations. We are not likely to abandon the city as a (8).______ institution, but we need to make sure that our transport arrangements do not (9)._____ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 7</p>",
                    options_en: ["<p>declining</p>", "<p>feigning</p>", 
                                "<p>wasteful</p>", "<p>similar</p>"],
                    options_hi: ["<p>declining</p>", "<p>feigning</p>",
                                "<p>wasteful</p>", "<p>similar</p>"],
                    solution_en: "<p>7.(d) similar<br>&lsquo;Similar&rsquo; means almost the same. The given passage states that city dwellers can choose their friends and mates from among a large number of people of similar interests and inclinations. Hence &lsquo;similar&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(d) similar<br>&lsquo;Similar&rsquo; का अर्थ है लगभग समान। दिए गए passage में बताया गया है कि शहरवासी (city dwellers) बड़ी संख्या में समान रुचियों (similar interests) और प्रवृत्तियों (inclinations) वाले लोगों में से अपने मित्र और साथी चुन सकते हैं। अतः &lsquo;similar&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <strong>Cloze Test :</strong> <br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (5)._____ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (6).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (7)._____ interests and inclinations. We are not likely to abandon the city as a (8).______ institution, but we need to make sure that our transport arrangements do not (9)._____ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 8</p>",
                    question_hi: "<p>8. <strong>Cloze Test :</strong> <br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (5)._____ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (6).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (7)._____ interests and inclinations. We are not likely to abandon the city as a (8).______ institution, but we need to make sure that our transport arrangements do not (9)._____ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 8</p>",
                    options_en: ["<p>religious</p>", "<p>psychological</p>", 
                                "<p>cultural</p>", "<p>educational</p>"],
                    options_hi: ["<p>religious</p>", "<p>psychological</p>",
                                "<p>cultural</p>", "<p>educational</p>"],
                    solution_en: "<p>8.(c) cultural<br>&lsquo;Cultural&rsquo; mean relating to the ideas, customs, and social behaviour of a society. The given passage states that we are not likely to abandon the city as a cultural institution. Hence &lsquo;cultural&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(c) cultural<br>&lsquo;Cultural&rsquo; का अर्थ है समाज के विचारों, रीति-रिवाजों (customs) और सामाजिक व्यवहार (social behaviour) से संबंधित। दिए गए passage में बताया गया है कि हम शहर को एक सांस्कृतिक संस्था (cultural institution) के रूप में त्यागने (abandon) की संभावना नहीं रखते हैं। अतः &lsquo;cultural&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <strong>Cloze Test :</strong> <br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (5)._____ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (6).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (7)._____ interests and inclinations. We are not likely to abandon the city as a (8).______ institution, but we need to make sure that our transport arrangements do not (9)._____ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 9.</p>",
                    question_hi: "<p>9. <strong>Cloze Test :</strong> <br>Cities are the great havens for knowledge, culture, and social life. Vibrant cultures are found in cities because it takes a (5)._____ population to support museums, concert halls, sports teams, and night-life districts. Cities also offer rich social opportunities. People in rural areas enjoy only limited social opportunities because (6).________ the small local population. City dwellers can choose their friends and mates from among a large number of people of (7)._____ interests and inclinations. We are not likely to abandon the city as a (8).______ institution, but we need to make sure that our transport arrangements do not (9)._____ the city\'s other functions.<br>Select the most appropriate option to fill in blank No. 9.</p>",
                    options_en: ["<p>support</p>", "<p>boost</p>", 
                                "<p>help</p>", "<p>damage</p>"],
                    options_hi: ["<p>support</p>", "<p>boost</p>",
                                "<p>help</p>", "<p>damage</p>"],
                    solution_en: "<p>9.(d) damage<br>Damage means to cause harm to something or someone. The given passage states that we need to make sure that our transport arrangements do not damage the city\'s other functions. Hence &lsquo;damage&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(d) damage<br>&lsquo;Damage&rsquo; का अर्थ है किसी चीज़ (something) या किसी व्यक्ति (someone) को हानि या नुकसान पहुँचाना। दिए गए passage में बताया गया है कि हमें यह सुनिश्चित करने की आवश्यकता है कि हमारी परिवहन व्यवस्था (transport arrangements) शहर के अन्य कार्यों को नुकसान न पहुँचाए। अतः &lsquo;damage&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate idiom to fill in the blank. <br>After a day long trek, we were so tired that we were ready to ________.</p>",
                    question_hi: "<p>10. Select the most appropriate idiom to fill in the blank. <br>After a day long trek, we were so tired that we were ready to _______.</p>",
                    options_en: ["<p>face the music</p>", "<p>hit the sack</p>", 
                                "<p>get into deep water</p>", "<p>go from rags to riches</p>"],
                    options_hi: ["<p>face the music</p>", "<p>hit the sack</p>",
                                "<p>get into deep water</p>", "<p>go from rags to riches</p>"],
                    solution_en: "<p>10.(b) <strong>Hit the sack- </strong>to go to bed or sleep.</p>",
                    solution_hi: "<p>10.(b) <strong>Hit the sack-</strong> to go to bed or sleep./बिस्तर पर जाना या सोना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "11.  Select the most appropriate homonym to fill in the blank. <br />Ravi is _______.  ",
                    question_hi: "11.  Select the most appropriate homonym to fill in the blank. <br />Ravi is _______.  ",
                    options_en: [" bald ", " bawled  ", 
                                " baled", " balled"],
                    options_hi: [" bald ", " bawled  ",
                                " baled", " balled"],
                    solution_en: "11.(a) bald<br />‘Bald’ means having no hair. The given sentence states that Ravi is bald. Hence ‘bald’ is the most appropriate answer.",
                    solution_hi: "11.(a) bald<br />‘Bald’ का अर्थ है बाल न होना। दिए गए sentence में बताया गया है कि रवि गंजा (bald) है। अतः ‘bald’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate idiom to fill in the blank. <br>The two brothers ______when their father died.</p>",
                    question_hi: "<p>12. Select the most appropriate idiom to fill in the blank. <br>The two brothers ______when their father died.</p>",
                    options_en: ["<p>fell down</p>", "<p>look down upon</p>", 
                                "<p>got the sack</p>", "<p>fell out</p>"],
                    options_hi: ["<p>fell down</p>", "<p>look down upon</p>",
                                "<p>got the sack</p>", "<p>fell out</p>"],
                    solution_en: "<p>12.(d) <strong>fell out - </strong>to have an argument that ends a relationship.</p>",
                    solution_hi: "<p>12.(d) <strong>fell out -</strong> to have an argument that ends a relationship./ऐसा तर्क-वितर्क करना जिससे रिश्ता ख़त्म हो जाए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate ANTONYM of the underlined word. <br>She was known for her <span style=\"text-decoration: underline;\">refined</span> manners.</p>",
                    question_hi: "<p>13. Select the most appropriate ANTONYM of the underlined word. <br>She was known for her <span style=\"text-decoration: underline;\">refined</span> manners.</p>",
                    options_en: ["<p>Brood</p>", "<p>Shrewd</p>", 
                                "<p>Crude</p>", "<p>Elegant</p>"],
                    options_hi: ["<p>Brood</p>", "<p>Shrewd</p>",
                                "<p>Crude</p>", "<p>Elegant</p>"],
                    solution_en: "<p>13.(c) <strong>Crude</strong>- offensive or rude.<br><strong>Refined</strong>- elegant and cultured in appearance.<br><strong>Brood</strong>- a group of young birds all born at the same time.<br><strong>Shrewd</strong>- showing sharp power of judgement.<br><strong>Elegant</strong>- graceful and stylish in appearance.</p>",
                    solution_hi: "<p>13.(c) <strong>Crude </strong>(अपरिष्कृत) - offensive or rude.<br><strong>Refined </strong>(परिष्कृत) - elegant and cultured in appearance.<br><strong>Brood </strong>(पक्षियों के बच्चे) - a group of young birds all born at the same time.<br><strong>Shrewd </strong>(चतुर) - showing sharp power of judgement.<br><strong>Elegant </strong>(सुंदर/सहज) - graceful and stylish in appearance.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate ANTONYM of the underlined word. <br>We should <span style=\"text-decoration: underline;\">desecrate</span> our national flag.</p>",
                    question_hi: "<p>14. Select the most appropriate ANTONYM of the underlined word. <br>We should <span style=\"text-decoration: underline;\">desecrate</span> our national flag.</p>",
                    options_en: ["<p>Damage</p>", "<p>Sanctify</p>", 
                                "<p>Integrate</p>", "<p>Violate</p>"],
                    options_hi: ["<p>Damage</p>", "<p>Sanctify</p>",
                                "<p>Integrate</p>", "<p>Violate</p>"],
                    solution_en: "<p>14.(b) <strong>Sanctify</strong>- to make holy or set apart as sacred.<br><strong>Desecrate</strong>- to treat (a sacred place or thing) with violent disrespect.<br><strong>Damage</strong>- physical harm that impairs value, usefulness, or normal function.<br><strong>Integrate</strong>- to combine or bring parts together into a whole.<br><strong>Violate-</strong> to break or fail to comply with (a rule or formal agreement).</p>",
                    solution_hi: "<p>14.(b) <strong>Sanctify </strong>(पवित्र करना) - to make holy or set apart as sacred.<br><strong>Desecrate </strong>(अपवित्र करना) - to treat (a sacred place or thing) with violent disrespect.<br><strong>Damage</strong> (क्षति) - physical harm that impairs value, usefulness, or normal function.<br><strong>Integrate </strong>(एकीकृत करना) - to combine or bring parts together into a whole.<br><strong>Violate</strong> (उल्लंघन करना) - to break or fail to comply with (a rule or formal agreement).</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate ANTONYM of the underlined word in the given sentence. <br>The truth was <span style=\"text-decoration: underline;\">revealed</span> in the interrogation.</p>",
                    question_hi: "<p>15. Select the most appropriate ANTONYM of the underlined word in the given sentence. <br>The truth was <span style=\"text-decoration: underline;\">revealed</span> in the interrogation.</p>",
                    options_en: ["<p>Found</p>", "<p>Declared</p>", 
                                "<p>Evident</p>", "<p>Concealed</p>"],
                    options_hi: ["<p>Found</p>", "<p>Declared</p>",
                                "<p>Evident</p>", "<p>Concealed</p>"],
                    solution_en: "<p>15.(d) <strong>Concealed</strong>- kept hidden or secret.<br><strong>Revealed</strong>- to make something publicly known.<br><strong>Found</strong>- to bring something to existence.<br><strong>Declared</strong>- openly announced.<br><strong>Evident</strong>- clearly visible or understood.</p>",
                    solution_hi: "<p>15.(d) <strong>Concealed </strong>(छिपा हुआ) - kept hidden or secret.<br><strong>Revealed</strong> (उजागर) - to make something publicly known.<br><strong>Found</strong> (पाना/मिलना) - to bring something to existence.<br><strong>Declared</strong> (घोषित करना) - openly announced.<br><strong>Evident</strong> (सुस्पष्ट) - clearly visible or understood.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "16. Select the most appropriate option to fill in the blank. <br />The teacher asked the students to ______ electricity by switching off lights when they left the room.",
                    question_hi: "16. Select the most appropriate option to fill in the blank. <br />The teacher asked the students to ______ electricity by switching off lights when they left the room.",
                    options_en: [" deposit ", " save ", 
                                " rescue ", " retain"],
                    options_hi: [" deposit ", " save ",
                                " rescue ", " retain"],
                    solution_en: "16.(b) save<br />The given sentence states that the teacher asked the students to save electricity by switching off lights when they left the room. Hence ‘save’ is the most appropriate answer.",
                    solution_hi: "16.(b) save<br />दिए गए sentence में बताया गया है कि teacher ने students से कहा कि वे कमरे (room) से बाहर निकलते समय लाइट (lights) बंद (off) करके बिजली (electricity) बचाएं। अतः ‘save’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the option that will improve the underlined part of the given sentence. In case no improvement is needed, select &lsquo;No improvement&rsquo;.<br>The Privacy Shield establishes a process to allow companies to transfer consumer data from European Union countries to the United States <span style=\"text-decoration: underline;\">in&nbsp;compliance in</span> EU law.</p>",
                    question_hi: "<p>17. Select the option that will improve the underlined part of the given sentence. In case no improvement is needed, select &lsquo;No improvement&rsquo;.<br>The Privacy Shield establishes a process to allow companies to transfer consumer data from European Union countries to the United States&nbsp;<span style=\"text-decoration: underline;\">in&nbsp;compliance in</span> EU law. in EU law.</p>",
                    options_en: ["<p>in compliance with</p>", "<p>in compliance under the</p>", 
                                "<p>in compliance for the</p>", "<p>No improvement</p>"],
                    options_hi: ["<p>in compliance with</p>", "<p>in compliance under the</p>",
                                "<p>in compliance for the</p>", "<p>No improvement</p>"],
                    solution_en: "<p>17.(a) in compliance with<br>&lsquo;With&rsquo; is a fixed preposition used with &lsquo;comply/ compliance&rsquo;. Hence &lsquo;in compliance with&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>17.(a) in compliance with<br>&lsquo;With&rsquo; एक fixed preposition है जिसका प्रयोग &lsquo;comply/compliance&rsquo; के साथ किया जाता है। अतः &lsquo;in compliance with&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>One of my <span style=\"text-decoration: underline;\">best friends are here.</span></p>",
                    question_hi: "<p>18. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>One of my <span style=\"text-decoration: underline;\">best friends are here.</span></p>",
                    options_en: ["<p>best friend is here</p>", "<p>better friends is here</p>", 
                                "<p>better friends are here</p>", "<p>best friends is here</p>"],
                    options_hi: ["<p>best friend is here</p>", "<p>better friends is here</p>",
                                "<p>better friends are here</p>", "<p>best friends is here</p>"],
                    solution_en: "<p>18.(d) best friends is here<br>&lsquo;One of + superlative degree + plural noun + singular verb&rsquo; is the correct grammatical structure. &lsquo;Friends&rsquo; is a plural noun and &lsquo;best&rsquo; is the superlative degree. Hence, &lsquo;best friends is here&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>18.(d) best friends is here<br>&lsquo;One of + superlative degree + plural noun + singular verb&rsquo; सही grammatical structure है। &lsquo;Friends&rsquo; एक plural noun है और &lsquo;best&rsquo; superlative degree है। अतः, &lsquo;best friends is here&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "19. Select the INCORRECTLY spelt word. ",
                    question_hi: "19. Select the INCORRECTLY spelt word. ",
                    options_en: [" Demolish ", " Destroy ", 
                                " Develop  ", " Betrey "],
                    options_hi: [" Demolish ", " Destroy ",
                                " Develop  ", " Betrey "],
                    solution_en: "19.(d) betrey<br />‘Betray’ is the correct spelling.",
                    solution_hi: "19.(d) betrey<br />‘Betray’ सही spelling है। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the most appropriate synonym of the underlined word in the given sentence. <br>We should not <span style=\"text-decoration: underline;\">discriminate</span> against people who are different from us.</p>",
                    question_hi: "<p>20. Select the most appropriate synonym of the underlined word in the given sentence. <br>We should not <span style=\"text-decoration: underline;\">discriminate</span> against people who are different from us.</p>",
                    options_en: ["<p>part</p>", "<p>differentiate</p>", 
                                "<p>divide</p>", "<p>sever</p>"],
                    options_hi: ["<p>part</p>", "<p>differentiate</p>",
                                "<p>divide</p>", "<p>sever</p>"],
                    solution_en: "<p>20.(b) <strong>Differentiate</strong>- to identify the difference between the things.<br><strong>Discriminate</strong>- to treat someone unfairly because of their differences, such as race, gender, or age.<br><strong>Part</strong>- a piece or portion of a whole.<br><strong>Divide</strong>- to separate something into parts or groups.<br><strong>Sever</strong>- divide by cutting or slicing, especially suddenly or forcibly.</p>",
                    solution_hi: "<p>20.(b) <strong>Differentiate </strong>(अंतर करना) - to identify the difference between the things.<br><strong>Discriminate </strong>(भेदभाव करना) - to treat someone unfairly because of their differences, such as race, gender, or age.<br><strong>Part </strong>(भाग) - a piece or portion of a whole.<br><strong>Divide </strong>(विभाजित करना) - to separate something into parts or groups.<br><strong>Sever </strong>(अलग करना) - divide by cutting or slicing, especially suddenly or forcibly.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. Select the most appropriate synonym of the word &lsquo;Abandon&rsquo; from the given sentence. <br>Ramita has decided to forsake her participation in college activities until she improved her scores.</p>",
                    question_hi: "<p>21. Select the most appropriate synonym of the word &lsquo;Abandon&rsquo; from the given sentence. <br>Ramita has decided to forsake her participation in college activities until she improved her scores.</p>",
                    options_en: ["<p>improved</p>", "<p>forsake</p>", 
                                "<p>decided</p>", "<p>participation</p>"],
                    options_hi: ["<p>improved</p>", "<p>forsake</p>",
                                "<p>decided</p>", "<p>participation</p>"],
                    solution_en: "<p>21.(b) <strong>Forsake</strong>- to give up or leave someone.<br><strong>Abandon</strong>- to leave a place, thing, or person, usually for ever.<br><strong>Improved</strong>- having become better<br><strong>Decided</strong>- clear and definite<br><strong>Participation</strong>- the action of taking part in something.</p>",
                    solution_hi: "<p>21.(b) <strong>Forsake </strong>(छोड़ देना) - to give up or leave someone.<br><strong>Abandon </strong>(त्यागना) - to leave a place, thing, or person, usually for ever.<br><strong>Improved </strong>(बेहतर करना) - having become better<br><strong>Decided </strong>(निर्णय लिया) - clear and definite<br><strong>Participation </strong>(भागीदारी) - the action of taking part in something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. Select the most appropriate synonym of the word &lsquo;intuitive&rsquo; to fill in the blank. <br>In a split second, her _______ reaction kicked in as she swiftly reached out to catch the falling glass.</p>",
                    question_hi: "<p>22. Select the most appropriate synonym of the word &lsquo;intuitive&rsquo; to fill in the blank. <br>In a split second, her ________ reaction kicked in as she swiftly reached out to catch the falling glass.</p>",
                    options_en: ["<p>shrewd</p>", "<p>insightful</p>", 
                                "<p>instinctive</p>", "<p>voluntary</p>"],
                    options_hi: ["<p>shrewd</p>", "<p>insightful</p>",
                                "<p>instinctive</p>", "<p>voluntary</p>"],
                    solution_en: "<p>22.(c) <strong>Instinctive</strong>- something you do without thinking about it<br><strong>Intuitive</strong>- know something without needing to think about it or learn it.<br><strong>Shrewd</strong>- showing sharp power of judgement.<br><strong>Insightful</strong>- having or showing an accurate and deep understanding.<br><strong>Voluntary-</strong> done by choice or willingly.</p>",
                    solution_hi: "<p>22.(c) <strong>Instinctive </strong>(स्वाभाविक) - something you do without thinking about it<br><strong>Intuitive</strong> (सहज) - know something without needing to think about it or learn it.<br><strong>Shrewd</strong> (चालाक) - showing sharp power of judgement.<br><strong>Insightful</strong> (ज्ञानवर्धक) - having or showing an accurate and deep understanding.<br><strong>Voluntary</strong> (स्वैच्छिक) - done by choice or willingly.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. Select the option that will improve the underlined part of the given sentence. <br>This book provides an <span style=\"text-decoration: underline;\">unrestrained</span> account of the natural resources in India.</p>",
                    question_hi: "<p>23. Select the option that will improve the underlined part of the given sentence. <br>This book provides an <span style=\"text-decoration: underline;\">unrestrained</span>&nbsp;account of the natural resources in India.</p>",
                    options_en: ["<p>exhaustive</p>", "<p>exhausting</p>", 
                                "<p>limlited</p>", "<p>excessive</p>"],
                    options_hi: ["<p>exhaustive</p>", "<p>exhausting</p>",
                                "<p>limlited</p>", "<p>excessive</p>"],
                    solution_en: "<p>23.(a) exhaustive<br>&lsquo;Exhaustive&rsquo; means complete and including everything. The given sentence states that this book provides an exhaustive account of the natural resources in India. Hence, &lsquo;exhaustive&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(a) exhaustive<br>&lsquo;Exhaustive&rsquo; का अर्थ है Complete और सब कुछ शामिल करना। दिए गए Sentence में बताया गया है कि यह पुस्तक (book) भारत में प्राकृतिक संसाधनों (Natural resources) का विस्तृत विवरण (Exhaustive account) प्रदान करती है। अतः, &lsquo;exhaustive&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution required&rsquo;. <br>Then I unfurled the Indian tri-colour and <span style=\"text-decoration: underline;\">keep</span> it aloft at the roof of the world.</p>",
                    question_hi: "<p>24. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution required&rsquo;. <br>Then I unfurled the Indian tri-colour and <span style=\"text-decoration: underline;\">keep</span> it aloft at the roof of the world.</p>",
                    options_en: ["<p>No substitution required</p>", "<p>held</p>", 
                                "<p>hold</p>", "<p>keeping</p>"],
                    options_hi: ["<p>No substitution required</p>", "<p>held</p>",
                                "<p>hold</p>", "<p>keeping</p>"],
                    solution_en: "<p>24.(b) held<br>The given sentence is in the past tense so the verb must be used in the past form (V<sub>2</sub>) and not in the present form(keep). Hence, &lsquo;held (V<sub>2</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(b) held<br>दिया गया sentence Past tense में है, इसलिए verb का प्रयोग past form (V<sub>2</sub>) में होना चाहिए, न कि present form (keep) में। अतः, &lsquo;held (V<sub>2</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "25. Identify the incorrectly spelt word and select its correct spelling. <br />This will sound weired, but bear with me. ",
                    question_hi: "25. Identify the incorrectly spelt word and select its correct spelling. <br />This will sound weired, but bear with me. ",
                    options_en: [" wired  ", " bier ", 
                                " beer", " weird"],
                    options_hi: [" wired  ", " bier ",
                                " beer", " weird"],
                    solution_en: "25.(d) Weird<br />‘Weird’ is the correct spelling.",
                    solution_hi: "25.(d) Weird<br />‘Weird’ सही spelling है। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "6",
                    question_en: "<p>26. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272268.png\" alt=\"rId6\" width=\"114\" height=\"143\"></p>",
                    question_hi: "<p>26. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272268.png\" alt=\"rId6\" width=\"114\" height=\"143\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272421.png\" alt=\"rId7\" width=\"86\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272528.png\" alt=\"rId8\" width=\"86\" height=\"86\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272680.png\" alt=\"rId9\" width=\"86\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272804.png\" alt=\"rId10\" width=\"86\" height=\"86\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272421.png\" alt=\"rId7\" width=\"86\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272528.png\" alt=\"rId8\" width=\"86\" height=\"86\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272680.png\" alt=\"rId9\" width=\"86\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272804.png\" alt=\"rId10\" width=\"86\" height=\"86\"></p>"],
                    solution_en: "<p>26.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272680.png\" alt=\"rId9\" width=\"87\" height=\"87\"></p>",
                    solution_hi: "<p>26.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272680.png\" alt=\"rId9\" width=\"87\" height=\"87\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "6",
                    question_en: "<p>27. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement. <br><strong>Statements :</strong> <br>Some bottles are pens. <br>All pens are tables. <br>All tables are glasses. <br><strong>Conclusion (I) : </strong>Some glasses are bottles. <br><strong>Conclusion (II) : </strong>Some pens are glasses.</p>",
                    question_hi: "<p>27. तीन कथनों के बाद ।, ।I क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong><br>कुछ बोतलें, कलमें हैं।<br>सभी कलमें, मेजें हैं।<br>सभी मेजें, ग्लास हैं।<br><strong>निष्कर्ष (I) :</strong> कुछ ग्लास, बोतलें हैं।<br><strong>निष्कर्ष (II) :</strong> कुछ कलमें, ग्लास हैं।</p>",
                    options_en: ["<p>Neither conclusion (I) nor (II) follows.</p>", "<p>Only conclusion (II) follows.</p>", 
                                "<p>Only conclusion (I) follows</p>", "<p>Both conclusions (I) and (II) follow.</p>"],
                    options_hi: ["<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>", "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>",
                                "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>", "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>"],
                    solution_en: "<p>27.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379272961.png\" alt=\"rId11\" width=\"343\" height=\"114\"><br>Both conclusions (I) and (II) follow. .</p>",
                    solution_hi: "<p>27.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273090.png\" alt=\"rId12\" width=\"361\" height=\"118\"><br>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "6",
                    question_en: "<p>28. In a certain code language, &lsquo;INDULGE&rsquo; is coded as &lsquo;48&rsquo; and &lsquo;PURGE&rsquo; is coded as &lsquo;24&rsquo;. How will &lsquo;Puny&rsquo; be coded in that language ?</p>",
                    question_hi: "<p>28. एक निश्चित कूट भाषा में, \'INDULGE\' को \'48\' लिखा जाता है और \'PURGE\' को \'24\' लिखा जाता है। उसी कूट भाषा में \'PUNY\' को कैसे लिखा जाएगा ?</p>",
                    options_en: ["<p>15</p>", "<p>18</p>", 
                                "<p>20</p>", "<p>25</p>"],
                    options_hi: ["<p>15</p>", "<p>18</p>",
                                "<p>20</p>", "<p>25</p>"],
                    solution_en: "<p>28.(a) <strong>Logic :-</strong> (Number of letter)<sup>2</sup> -1<br>INDULGE :- (7)<sup>2</sup> - 1 &rArr; 49 - 1 = 48<br>PURGE :- (5)<sup>2</sup> -1 &rArr; 25 -1 = 24<br>Similarly,<br>PUNY :- (4)<sup>2</sup> - 1 &rArr; 16 -1 = 15</p>",
                    solution_hi: "<p>28.(a) <strong>तर्क :- </strong>(अक्षर की संख्या)<sup>2</sup> -1<br>INDULGE :- (7)<sup>2</sup> - 1 &rArr; 49 - 1 = 48<br>PURGE :- (5)<sup>2</sup> -1 &rArr; 25 -1 = 24<br>इसी प्रकार,<br>PUNY :- (4)<sup>2</sup> - 1 &rArr; 16 -1 = 15</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "6",
                    question_en: "<p>29. 45 is related to 100 following a certain logic. Following the same logic, 65 is related to 140. To which of the following is 105 related, following the same logic ?<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /subtracting /multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>29. एक निश्चित तर्क के आधार पर 45 का संबंध 100 से है। उसी तर्क के आधार पर 65 का संबंध 140 से है। समान तर्क के आधार पर निम्नलिखित में से किसका संबंध 105 से है ?&nbsp;<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>210</p>", "<p>215</p>", 
                                "<p>220</p>", "<p>230</p>"],
                    options_hi: ["<p>210</p>", "<p>215</p>",
                                "<p>220</p>", "<p>230</p>"],
                    solution_en: "<p>29.(c)<strong> Logic :- </strong>(1st number) &times; 2 + 10 = 2nd number<br>(45 , 100) :- (45) &times; 2 + 10 = 100<br>(65, 140) :- (65) &times; 2 + 10 = 140<br>Similarly,<br>(105, ?) :- (105) &times; 2 + 10 = 220</p>",
                    solution_hi: "<p>29.(c)<strong> तर्क :-</strong> (पहली संख्या) &times; 2 + 10 = दूसरी संख्या<br>(45 , 100) :- (45) &times; 2 + 10 = 100<br>(65, 140) :- (65) &times; 2 + 10 = 140<br>इसी प्रकार,<br>(105, ?) :- (105) &times; 2 + 10 = 220</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "6",
                    question_en: "<p>30. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273206.png\" alt=\"rId13\" width=\"104\" height=\"89\"></p>",
                    question_hi: "<p>30. उस विकल्प आकृति का चयन कीजिए, जिसमें दी गई आकृति (X) उसके एक भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273206.png\" alt=\"rId13\" width=\"104\" height=\"89\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273369.png\" alt=\"rId14\" width=\"99\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273460.png\" alt=\"rId15\" width=\"95\" height=\"89\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273551.png\" alt=\"rId16\" width=\"98\" height=\"93\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273667.png\" alt=\"rId17\" width=\"105\" height=\"94\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273369.png\" alt=\"rId14\" width=\"102\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273460.png\" alt=\"rId15\" width=\"96\" height=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273551.png\" alt=\"rId16\" width=\"98\" height=\"94\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273667.png\" alt=\"rId17\" width=\"105\" height=\"94\"></p>"],
                    solution_en: "<p>30.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273460.png\" alt=\"rId15\" width=\"97\" height=\"91\"></p>",
                    solution_hi: "<p>30.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273460.png\" alt=\"rId15\" width=\"97\" height=\"91\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "6",
                    question_en: "<p>31. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;, <br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is the brother of B&rsquo;, <br>&lsquo;A &times; B&rsquo; means &lsquo;A is the father of B&rsquo;, <br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the daughter of B&rsquo; <br>Based on the above, how is E related to A if &lsquo;A &times; B &ndash; C + D &divide; E&rsquo; ?</p>",
                    question_hi: "<p>31. एक निश्चित कूट भाषा में, <br>&lsquo;A + B&rsquo; का अर्थ है &lsquo;A, B की माँ है&rsquo;, <br>&lsquo;A &ndash; B&rsquo; का अर्थ है &lsquo;A, B का भाई है&rsquo;,<br>&lsquo;A &times; B&rsquo; का अर्थ है &lsquo;A, B के पिता है&rsquo;, <br>&lsquo;A &divide; B&rsquo; का अर्थ है &lsquo;A, B की बेटी है&rsquo;. <br>उपरोक्त के आधार पर, यदि&lsquo;A &times; B &ndash; C + D &divide; E&rsquo; है, तो E का A से क्या संबंध है ?</p>",
                    options_en: ["<p>Brother</p>", "<p>Son</p>", 
                                "<p>Daughter&rsquo;s husband</p>", "<p>Wife&rsquo;s brother</p>"],
                    options_hi: ["<p>भाई</p>", "<p>बेटा</p>",
                                "<p>बेटी का पति</p>", "<p>पत्नी का भाई</p>"],
                    solution_en: "<p>31.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273769.png\" alt=\"rId18\" width=\"178\" height=\"171\"><br>E is the husband of A&rsquo;s daughter.</p>",
                    solution_hi: "<p>31.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273769.png\" alt=\"rId18\" width=\"178\" height=\"171\"><br>E, A की बेटी का पति है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "6",
                    question_en: "32. If 24 August 1932 was a Wednesday, then what was the day of the week on 11 September 1943 ? ",
                    question_hi: "32. यदि 24 अगस्त 1932 को बुधवार था, तो 11 सितंबर 1943 को सप्ताह का कौन-सा दिन था? ",
                    options_en: [" Sunday  ", " Wednesday  ", 
                                " Saturday ", " Monday"],
                    options_hi: [" रविवार", " बुधवार",
                                " शनिवार", " सोमवार "],
                    solution_en: "32.(c) 24 August 1932 is Wednesday. On moving from 1932 to 1943 the number of odd days = <br />1 + 1 + 1 + 2 + 1 + 1 + 1  + 2  + 1  + 1 + 1 = 13. We have reached till 24 August 1943, we have to reach till 11 september 1942. Number of days in between = 7 + 11 = 18. Total number of odd days = 18 + 13 = 31. On dividing 31 by 7,remainder = 3. Wednesday + 3 = Saturday.",
                    solution_hi: "32.(c) 24 अगस्त 1932 को बुधवार है. 1932 से 1943 तक जाने पर विषम दिनों की संख्या = <br />1 + 1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 + 1 = 13. हम 24 अगस्त 1943 तक पहुँच चुके हैं, हमें 11 सितम्बर 1942 तक पहुँचना है। बीच में दिनों की संख्या = 7 + 11 = 18. विषम दिनों की कुल संख्या = 18 + 13 = 31. 31 को 7 से विभाजित करने पर शेषफल = 3. बुधवार + 3 = शनिवार.",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "6",
                    question_en: "33. In a certain code language, ‘tiffin is heavy’ is written as ’ct qm lb’ and ‘heavy weight gain’ is written as ‘rx yb ct’ . How is ‘heavy’ written in the given language ?",
                    question_hi: "33. एक निश्चित कूट भाषा में, \'tiffin is heavy\' को \'’ct qm lb\' लिखा जाता है और \'heavy weight gain\' को \'rx yb ct\' लिखा जाता है। उसी कूट भाषा में \'heavy\' को कैसे लिखा जाएगा ?",
                    options_en: [" lb ", " qm ", 
                                " rx ", " ct"],
                    options_hi: [" lb ", " qm ",
                                " rx ", " ct"],
                    solution_en: "33.(d) tiffin is heavy → ct qm lb…… (i)<br />                 heavy weight gain → rx yb ct……(ii)<br />From (i) and (ii) ‘heavy’ and ‘ct’ are common. The code of ‘heavy’ = ‘ct’.",
                    solution_hi: "33.(d) tiffin is heavy → ct qm lb…… (i)<br />                  heavy weight gain → rx yb ct……(ii)<br />(i) और (ii) से ‘heavy’ और \'ct\' उभयनिष्ठ हैं। ‘heavy’ का कोड = \'ct\'.",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "6",
                    question_en: "<p>34. The position of how many letters will remain unchanged, if each of the letters in the word DICTIONARY is arranged in alphabetical order ?</p>",
                    question_hi: "<p>34. यदि शब्द DICTIONARY के प्रत्येक अक्षर को वर्णानुक्रम में व्यवस्थित किया जाए, तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा ?</p>",
                    options_en: ["<p>4</p>", "<p>3</p>", 
                                "<p>2</p>", "<p>1</p>"],
                    options_hi: ["<p>4</p>", "<p>3</p>",
                                "<p>2</p>", "<p>1</p>"],
                    solution_en: "<p>34.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273873.png\" alt=\"rId19\" width=\"267\" height=\"143\"><br>The position of two letter remain unchanged.</p>",
                    solution_hi: "<p>34.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273873.png\" alt=\"rId19\" width=\"267\" height=\"143\"><br>दो अक्षरों का स्थान अपरिवर्तित रहता है ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "6",
                    question_en: "<p>35. The sequence of folding a piece of paper and the manner in which the folded paper is punched is shown in the following figures. How would this paper look when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273978.png\" alt=\"rId20\" width=\"273\" height=\"84\"></p>",
                    question_hi: "<p>35. निम्नलिखित आकृतियों में एक कागज़ को मोड़ने के क्रम और आखिर में उसमें छेद करने के तरीके को दर्शाया गया है। खोलने पर यह कागज़ कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379273978.png\" alt=\"rId20\" width=\"273\" height=\"84\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274076.png\" alt=\"rId21\" width=\"85\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274446.png\" alt=\"rId22\" width=\"86\" height=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274551.png\" alt=\"rId23\" width=\"88\" height=\"87\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274653.png\" alt=\"rId24\" width=\"90\" height=\"70\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274076.png\" alt=\"rId21\" width=\"85\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274446.png\" alt=\"rId22\" width=\"84\" height=\"78\"></p>\n<p>&nbsp;</p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274551.png\" alt=\"rId23\" width=\"81\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274653.png\" alt=\"rId24\" width=\"90\" height=\"70\"></p>"],
                    solution_en: "<p>35.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274076.png\" alt=\"rId21\" width=\"83\" height=\"78\"></p>",
                    solution_hi: "<p>35.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274076.png\" alt=\"rId21\" width=\"83\" height=\"78\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "6",
                    question_en: "<p>36. LH3 is related to RN21 in a certain way. In the same way, JF5 is related to PL35. To which of the following is ZV6 related following the same logic ?</p>",
                    question_hi: "<p>36. LH3, RN21 से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, JF5, PL35 से संबंधित है। समान तर्क का अनुसरण करते हुए ZV6 निम्नलिखित में से किससे संबंधित है ?</p>",
                    options_en: ["<p>EA45</p>", "<p>EA24</p>", 
                                "<p>FB42</p>", "<p>FB45</p>"],
                    options_hi: ["<p>EA45</p>", "<p>EA24</p>",
                                "<p>FB42</p>", "<p>FB45</p>"],
                    solution_en: "<p>36.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274763.png\" alt=\"rId25\" width=\"106\" height=\"135\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274937.png\" alt=\"rId26\" width=\"108\" height=\"128\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275046.png\" alt=\"rId27\" width=\"107\" height=\"133\"></p>",
                    solution_hi: "<p>36.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274763.png\" alt=\"rId25\" width=\"106\" height=\"135\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379274937.png\" alt=\"rId26\" width=\"108\" height=\"128\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275046.png\" alt=\"rId27\" width=\"107\" height=\"133\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "6",
                    question_en: "<p>37. What should come in the place of ? in the given series based on the English alphabetical order ?<br>YQI&nbsp; &nbsp;CZQ&nbsp; &nbsp;GIY&nbsp; &nbsp;KRG&nbsp; &nbsp;?</p>",
                    question_hi: "<p>37. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में &lsquo;?\' के स्थान पर क्या आना चाहिए ?<br>YQI&nbsp; &nbsp;CZQ&nbsp; &nbsp;GIY&nbsp; &nbsp;KRG&nbsp; &nbsp;?</p>",
                    options_en: ["<p>OAO</p>", "<p>DMX</p>", 
                                "<p>CNY</p>", "<p>EOZ</p>"],
                    options_hi: ["<p>OAO</p>", "<p>DMX</p>",
                                "<p>CNY</p>", "<p>EOZ</p>"],
                    solution_en: "<p>37.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275186.png\" alt=\"rId28\" width=\"419\" height=\"120\"></p>",
                    solution_hi: "<p>37.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275186.png\" alt=\"rId28\" width=\"419\" height=\"120\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "6",
                    question_en: "<p>38. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275318.png\" alt=\"rId29\" width=\"187\" height=\"58\"></p>",
                    question_hi: "<p>38. नीचे दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275318.png\" alt=\"rId29\" width=\"187\" height=\"58\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275416.png\" alt=\"rId30\" width=\"111\" height=\"24\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275499.png\" alt=\"rId31\" width=\"115\" height=\"26\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275591.png\" alt=\"rId32\" width=\"114\" height=\"26\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275730.png\" alt=\"rId33\" width=\"111\" height=\"27\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275416.png\" alt=\"rId30\" width=\"111\" height=\"24\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275499.png\" alt=\"rId31\" width=\"115\" height=\"26\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275591.png\" alt=\"rId32\" width=\"114\" height=\"26\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275730.png\" alt=\"rId33\" width=\"111\" height=\"27\"></p>"],
                    solution_en: "<p>38.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275591.png\" alt=\"rId32\" width=\"110\" height=\"25\"></p>",
                    solution_hi: "<p>38.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275591.png\" alt=\"rId32\" width=\"110\" height=\"25\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "6",
                    question_en: "<p>39. What should come in place of the question mark (?) in the given series ? <br>27, 32, 42, 47, 57,?</p>",
                    question_hi: "<p>39. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>27, 32, 42, 47, 57,?</p>",
                    options_en: ["<p>63</p>", "<p>62</p>", 
                                "<p>61</p>", "<p>60</p>"],
                    options_hi: ["<p>63</p>", "<p>62</p>",
                                "<p>61</p>", "<p>60</p>"],
                    solution_en: "<p>39.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275827.png\" alt=\"rId34\" width=\"311\" height=\"95\"></p>",
                    solution_hi: "<p>39.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275827.png\" alt=\"rId34\" width=\"311\" height=\"95\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "6",
                    question_en: "<p>40. ETHICS\' is related to AVDKYU\' in a certain way based on the English alphabetical order. In the same way. \'CUSTOM\' is related to \"YWOVKO\'. To which of the following is PRINCIPLES\' related, following the same logic ?</p>",
                    question_hi: "<p>40. अंग्रेजी वर्णमाला क्रम के आधार पर \'ETHICS\', \'AVDKYU\' से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, \'CUSTOM\', \'YWOVKO\' से संबंधित है। समान तर्क का अनुसरण करते हुए, \'PRINCIPLES\' निम्नलिखित में से किससे संबंधित है ?</p>",
                    options_en: ["<p>LTEPYKLMAU</p>", "<p>LSEPYKLNAU</p>", 
                                "<p>LTEPYKLNAU</p>", "<p>LTFPYKLNAU</p>"],
                    options_hi: ["<p>LTEPYKLMAU</p>", "<p>LSEPYKLNAU</p>",
                                "<p>LTEPYKLNAU</p>", "<p>LTFPYKLNAU</p>"],
                    solution_en: "<p>40.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275929.png\" alt=\"rId35\" width=\"193\" height=\"96\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276034.png\" alt=\"rId36\" width=\"203\" height=\"95\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276159.png\" alt=\"rId37\" width=\"360\" height=\"113\"></p>",
                    solution_hi: "<p>40.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379275929.png\" alt=\"rId35\" width=\"193\" height=\"96\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276034.png\" alt=\"rId36\" width=\"203\" height=\"95\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276159.png\" alt=\"rId37\" width=\"360\" height=\"113\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "6",
                    question_en: "<p>41. If &lsquo;A&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&ndash;&rsquo;, then the resultant of which of the following will be 233 ?</p>",
                    question_hi: "<p>41. यदि &lsquo;A&rsquo; का अर्थ &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; का अर्थ &lsquo;&times;&rsquo;, &lsquo;C&rsquo; का अर्थ &lsquo;+&rsquo; और &lsquo;D&rsquo; का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित में से किसका परिणाम 233 होगा ?</p>",
                    options_en: ["<p>21 A 11 D 33 B 11 C 5</p>", "<p>21 B 11 D 33 A 11 C 5</p>", 
                                "<p>21 C 11 D 33 A 11 B 5</p>", "<p>21 B 11 A 33 D 11 C 5</p>"],
                    options_hi: ["<p>21 A 11 D 33 B 11 C 5</p>", "<p>21 B 11 D 33 A 11 C 5</p>",
                                "<p>21 C 11 D 33 A 11 B 5</p>", "<p>21 B 11 A 33 D 11 C 5</p>"],
                    solution_en: "<p>41.(b) After checking all the options ,only option (b) is satisfied. <br>21 B 11 D 33 A 11 C 5<br>As per given instruction after interchanging letter with sign we get<br>21 &times; 11 - 33 <math display=\"inline\"><mo>&#247;</mo></math> 11 + 5<br>231 - 3 + 5 = 233</p>",
                    solution_hi: "<p>41.(b) सभी विकल्पों की जांच करने के बाद, केवल विकल्प (b) ही संतुष्ट है।<br>21 B 11 D 33 A 11 C 5<br>दिए गए निर्देश के अनुसार अक्षर को चिह्न से बदलने पर हमें प्राप्त होता है<br>21 &times; 11 - 33 <math display=\"inline\"><mo>&#247;</mo></math> 11 + 5<br>231 - 3 + 5 = 233</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "6",
                    question_en: "<p>42. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>90 &times; 2 - 43 + 7 &divide; 7 = ?</p>",
                    question_hi: "<p>42. यदि \'+\' और \' - \' को आपस में बदल दिया जाए तथा \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा ?<br>90 &times; 2 - 43 + 7 &divide; 7 = ?</p>",
                    options_en: ["<p>29</p>", "<p>19</p>", 
                                "<p>39</p>", "<p>49</p>"],
                    options_hi: ["<p>29</p>", "<p>19</p>",
                                "<p>39</p>", "<p>49</p>"],
                    solution_en: "<p>42.(c) <strong>Given :-</strong> 90 &times; 2 - 43 + 7 <math display=\"inline\"><mo>&#247;</mo></math> 7<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get,<br>90 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 43 - 7 &times; 7<br>45 + 43 - 49 = 39</p>",
                    solution_hi: "<p>42.(c) <strong>दिया गया :- </strong>90 &times; 2 - 43 + 7 <math display=\"inline\"><mo>&#247;</mo></math> 7<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने के बाद हमें प्राप्त होता है,<br>90 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 43 - 7 &times; 7<br>45 + 43 - 49 = 39</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "6",
                    question_en: "<p>43. Three of the following letter-clusters are alike in some manner and hence form a group. Which letter-cluster does not belong to that group ?&nbsp;<br>(<strong>Note : </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>43. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है ?<br>(<strong>ध्यान दें : </strong>असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: ["<p>CKP</p>", "<p>TBG</p>", 
                                "<p>EMS</p>", "<p>LTY</p>"],
                    options_hi: ["<p>CKP</p>", "<p>TBG</p>",
                                "<p>EMS</p>", "<p>LTY</p>"],
                    solution_en: "<p>43.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276257.png\" alt=\"rId38\" width=\"106\" height=\"74\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276358.png\" alt=\"rId39\" width=\"107\" height=\"76\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276450.png\" alt=\"rId40\" width=\"107\" height=\"74\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276543.png\" alt=\"rId41\" width=\"104\" height=\"70\"></p>",
                    solution_hi: "<p>43.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276257.png\" alt=\"rId38\" width=\"106\" height=\"74\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276358.png\" alt=\"rId39\" width=\"107\" height=\"76\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276450.png\" alt=\"rId40\" width=\"107\" height=\"74\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276543.png\" alt=\"rId41\" width=\"104\" height=\"70\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "6",
                    question_en: "<p>44. Which of the following numbers will replace the question mark (?) in the given series ?<br>12, 26, 54, ?, 222, 446</p>",
                    question_hi: "<p>44. दी गई श्रृंखला में निम्नलिखित में से कौन-सी संख्या प्रश्न चिन्ह (?) के स्थान पर आएगी ?<br>12, 26, 54, ?, 222, 446</p>",
                    options_en: ["<p>110</p>", "<p>94</p>", 
                                "<p>88</p>", "<p>98</p>"],
                    options_hi: ["<p>110</p>", "<p>94</p>",
                                "<p>88</p>", "<p>98</p>"],
                    solution_en: "<p>44.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276637.png\" alt=\"rId42\" width=\"246\" height=\"81\"></p>",
                    solution_hi: "<p>44.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276637.png\" alt=\"rId42\" width=\"246\" height=\"81\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "6",
                    question_en: "<p>45. Select the set in which the numbers are related in the same way as are the numbers of the following sets. <br>(26, 38, 128) <br>(42, 19, 122)<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /subtracting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>45. उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुच्&zwj;चयों की संख्याएं संबंधित हैं।<br>(26, 38, 128) <br>(42, 19, 122)<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(71, 9, 320)</p>", "<p>(33, 23, 112)</p>", 
                                "<p>(25, 36, 183)</p>", "<p>(18, 52, 70)</p>"],
                    options_hi: ["<p>(71, 9, 320)</p>", "<p>(33, 23, 112)</p>",
                                "<p>(25, 36, 183)</p>", "<p>(18, 52, 70)</p>"],
                    solution_en: "<p>45.(b) <strong>Logic :-</strong> (1st number + 2nd number) &times; 2 = 3rd number<br>(26, 38, 128) :- (26 + 38) &times; 2 &rArr; (64) &times; 2 = 128<br>(42, 19, 122) :- (42 + 19) &times; 2 &rArr; (61) &times; 2 = 122<br>Similarly,<br>(33 ,23 ,112) :- (33 + 23) &times; 2 &rArr; (56) &times; 2 = 112</p>",
                    solution_hi: "<p>45.(b) <strong>तर्क :-</strong> (पहली संख्या + दूसरी संख्या) &times; 2 = तीसरी संख्या<br>(26, 38, 128) :- (26 + 38) &times; 2 &rArr; (64) &times; 2 = 128<br>(42, 19, 122) :- (42 + 19) &times; 2 &rArr; (61) &times; 2 = 122<br>इसी प्रकार,<br>(33 ,23 ,112) :- (33 + 23) &times; 2 &rArr; (56) &times; 2 = 112</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "6",
                    question_en: "<p>46. Select the option in which the given figure is embedded (rotation is not allowed). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276749.png\" alt=\"rId43\" width=\"111\" height=\"110\"></p>",
                    question_hi: "<p>46. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति निहित है (घूर्णन की अनुमति नहीं है)। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276749.png\" alt=\"rId43\" width=\"111\" height=\"110\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276837.png\" alt=\"rId44\" width=\"104\" height=\"104\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276965.png\" alt=\"rId45\" width=\"104\" height=\"95\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277043.png\" alt=\"rId46\" width=\"105\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277150.png\" alt=\"rId47\" width=\"104\" height=\"103\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276837.png\" alt=\"rId44\" width=\"104\" height=\"104\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379276965.png\" alt=\"rId45\" width=\"104\" height=\"95\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277043.png\" alt=\"rId46\" width=\"104\" height=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277150.png\" alt=\"rId47\" width=\"104\" height=\"103\"></p>"],
                    solution_en: "<p>46.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277400.png\" alt=\"rId48\" width=\"104\" height=\"102\"></p>",
                    solution_hi: "<p>46.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277400.png\" alt=\"rId48\" width=\"104\" height=\"102\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "6",
                    question_en: "<p>47. Which letter-cluster will replace the question mark (?) to complete the given series ?&nbsp;<br>NDGB, OFJF, ?, TMSQ, XRYX</p>",
                    question_hi: "<p>47. दी गई शृंखला को पूरा करने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सा अक्षर-समूह आएगा ?<br>NDGB, OFJF, ?, TMSQ, XRYX</p>",
                    options_en: ["<p>QJNK</p>", "<p>QINK</p>", 
                                "<p>RINJ</p>", "<p>RHNK</p>"],
                    options_hi: ["<p>QJNK</p>", "<p>QINK</p>",
                                "<p>RINJ</p>", "<p>RHNK</p>"],
                    solution_en: "<p>47.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277531.png\" alt=\"rId49\" width=\"387\" height=\"140\"></p>",
                    solution_hi: "<p>47.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277531.png\" alt=\"rId49\" width=\"387\" height=\"140\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "6",
                    question_en: "<p>48. How many semi circles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277716.png\" alt=\"rId50\" width=\"117\" height=\"118\"></p>",
                    question_hi: "<p>48. दी गई आकृति में कितने अर्धवृत्त हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277716.png\" alt=\"rId50\" width=\"117\" height=\"118\"></p>",
                    options_en: ["<p>9</p>", "<p>12</p>", 
                                "<p>10</p>", "<p>8</p>"],
                    options_hi: ["<p>9</p>", "<p>12</p>",
                                "<p>10</p>", "<p>8</p>"],
                    solution_en: "<p>48.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277829.png\" alt=\"rId51\" width=\"177\" height=\"180\"><br>There are 8 semicircle<br>ABC , CDA, ABD, BCD, EFG, GHE, EFH, FGH</p>",
                    solution_hi: "<p>48.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277829.png\" alt=\"rId51\" width=\"177\" height=\"180\"><br>8 अर्धवृत्त हैं<br>ABC , CDA, ABD, BCD, EFG, GHE, EFH, FGH</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "6",
                    question_en: "<p>49. Identify the figure in the options that when put in place of the question mark (?) will logically complete the series? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277954.png\" alt=\"rId52\" width=\"391\" height=\"89\"></p>",
                    question_hi: "<p>49. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379277954.png\" alt=\"rId52\" width=\"391\" height=\"89\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278052.png\" alt=\"rId53\" width=\"95\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278156.png\" alt=\"rId54\" width=\"95\" height=\"88\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278258.png\" alt=\"rId55\" width=\"95\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278397.png\" alt=\"rId56\" width=\"95\" height=\"95\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278052.png\" alt=\"rId53\" width=\"95\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278156.png\" alt=\"rId54\" width=\"95\" height=\"88\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278258.png\" alt=\"rId55\" width=\"95\" height=\"92\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278397.png\" alt=\"rId56\" width=\"94\" height=\"94\"></p>"],
                    solution_en: "<p>49.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278397.png\" alt=\"rId56\" width=\"95\" height=\"95\"></p>",
                    solution_hi: "<p>49.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278397.png\" alt=\"rId56\" width=\"95\" height=\"95\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which two numbers should be interchanged to make the given equation correct ?&nbsp;<br>8 &times; (7 + 5 &minus; 3) + (46 &divide; 23) &times; 6 &minus; 69 = 44<br>(<strong>NOTE :</strong> Numbers must be interchanged and not the constituent digits e.g. if 2 and 3 are to be interchanged in the equation 43 &times; 3 + 4 &divide; 2, then interchanged equation is 43 &times; 2 + 4 &divide; 3)</p>",
                    question_hi: "<p>50. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए ?&nbsp;<br>8 &times; (7 + 5 &minus; 3) + (46 &divide; 23) &times; 6 &minus; 69 = 44<br>(<strong>ध्यान दें : </strong>संख्याओं को आपस में बदला जाना चाहिए और घटक अंकों को नहीं बदला जाना चाहिए। उदा. यदि समीकरण 43 &times; 3 + 4 &divide; 2 में 2 और 3 को आपस में बदलना है, तो बदला हुआ समीकरण 43 &times; 2 + 4 &divide; 3 होगा)</p>",
                    options_en: ["<p>6 and 7</p>", "<p>3 and 5</p>", 
                                "<p>46 and 69</p>", "<p>8 and 6</p>"],
                    options_hi: ["<p>6 और 7</p>", "<p>3 और 5</p>",
                                "<p>46 और 69</p>", "<p>8 और 6</p>"],
                    solution_en: "<p>50.(c) <strong>Given :-</strong> 8 &times; (7 + 5 &minus; 3) + (46 &divide; 23) &times; 6 &minus; 69 = 44<br>After checking all the options,only option (c) satisfies. After interchanging 46 and 69<br>8 &times; (9) + (69 <math display=\"inline\"><mo>&#247;</mo></math> 23) &times; 6 - 46 <br>72 + 3 &times; 6 - 46<br>72 + 18 - 46 = 44<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>50.(c) <strong>दिया गया :- </strong>8 &times; (7 + 5 &minus; 3) + (46 &divide; 23) &times; 6 &minus; 69 = 44<br>सभी विकल्पों की जांच करने के बाद, केवल विकल्प (c) ही संतुष्ट करता है। 46 और 69 को आपस में बदलने के बाद<br>8 &times; (9) + (69 <math display=\"inline\"><mo>&#247;</mo></math> 23) &times; 6 - 46 <br>72 + 3 &times; 6 - 46<br>72 + 18 - 46 = 44<br>L.H.S. = R.H.S.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "51. Two circles of diameters 16 cm and 20 cm are such that the distance between their centers is 13 cm. What is the length (in cm) of a common tangent to these circles that does not intersect the line joining the centers ? ",
                    question_hi: "51. 16 cm और 20 cm व्यास वाले दो वृत्त इस प्रकार हैं कि उनके केंद्रों के बीच की दूरी 13 cm है। इन वृत्तों की उस उभयनिष्ठ स्पर्शरेखा की लंबाई (cm में) कितनी है जो केंद्रों को मिलाने वाली रेखा को प्रतिच्छेद नहीं करती है? ",
                    options_en: [" <math display=\"inline\"><msqrt><mn>167</mn></msqrt></math> ", " <math display=\"inline\"><msqrt><mn>165</mn></msqrt></math> ", 
                                " <math display=\"inline\"><msqrt><mn>163</mn></msqrt></math> ", " <math display=\"inline\"><msqrt><mn>173</mn></msqrt></math>"],
                    options_hi: [" <math display=\"inline\"><msqrt><mn>167</mn></msqrt></math> ", " <math display=\"inline\"><msqrt><mn>165</mn></msqrt></math> ",
                                " <math display=\"inline\"><msqrt><mn>163</mn></msqrt></math> ", " <math display=\"inline\"><msqrt><mn>173</mn></msqrt></math>"],
                    solution_en: "51.(b)<br />Length of the common tangent = <math display=\"inline\"><msqrt><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mi>R</mi><mo>-</mo><mi>r</mi></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br />                                                  = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>13</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>10</mn><mo>-</mo><mn>8</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br />                                                  = <math display=\"inline\"><msqrt><mn>169</mn><mo>-</mo><mn>4</mn></msqrt></math><br />                                                  = <math display=\"inline\"><msqrt><mn>165</mn></msqrt></math> cm",
                    solution_hi: "51.(b)<br />उभयनिष्ठ स्पर्शरेखा की लंबाई = <math display=\"inline\"><msqrt><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mi>R</mi><mo>-</mo><mi>r</mi></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br />                                         = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>13</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>10</mn><mo>-</mo><mn>8</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br />                                         = <math display=\"inline\"><msqrt><mn>169</mn><mo>-</mo><mn>4</mn></msqrt></math><br />                                         = <math display=\"inline\"><msqrt><mn>165</mn></msqrt></math> cm",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "52. An iron box with external dimensions 60 cm, 40 cm, and 20 cm is made of 1 cm thick sheet. If 1 cm3 of iron weighs 50 gm, the weight of the empty box is:",
                    question_hi: "52. 60 cm, 40 cm और 20 cm बाहरी आयामों वाला एक लोहे का बक्सा 1 cm मोटी चादर से बना है। यदि 1 cm3 लोहे का भार 50 gm है, तो खाली बक्से का भार कितना होगा ? ",
                    options_en: [" 416.40 kg ", " 240 kg  ", 
                                " 214.05 kg  ", " 400 kg"],
                    options_hi: [" 416.40 kg ", " 240 kg  ",
                                " 214.05 kg  ", " 400 kg"],
                    solution_en: "52.(a)<br />Weight of the empty box = [(60 × 40 × 20) - (58 × 38 × 18)] × 50<br />                                        = [48000  - 39672] × 50<br />                                        =  [8328] × 50<br />                                        = 416400 gm<br />Weight of the empty box = <math display=\"inline\"><mfrac><mrow><mn>416400</mn><mi>&nbsp;</mi></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 416.4 kg",
                    solution_hi: "52.(a)<br />खाली डिब्बे का वजन = [(60 × 40 × 20) - (58 × 38 × 18)] × 50<br />                              = [48000  - 39672] × 50<br />                              =  [8328] × 50<br />                              = 416400 gm<br />खाली डिब्बे का वजन = <math display=\"inline\"><mfrac><mrow><mn>416400</mn><mi>&nbsp;</mi></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 416.4 kg",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "53. If price of an article increases successively by 15% and 10%, respectively, then what is the percentage equivalent to a single price increase of that article? ",
                    question_hi: "53. यदि किसी वस्तु के मूल्य में क्रमागत रूप से 15% और 10% की वृद्धि होती है, तो उस वस्तु की एकल मूल्य वृद्धि के समतुल्‍य प्रतिशत क्या है?",
                    options_en: [" 26.5% ", " 25% ", 
                                " 23.5% ", " 12.5%"],
                    options_hi: [" 26.5% ", " 25% ",
                                " 23.5% ", " 12.5%"],
                    solution_en: "53.(a)<br />increase % = 15 + 10 + <math display=\"inline\"><mfrac><mrow><mn>15</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 26.5%",
                    solution_hi: "53.(a)<br />वृद्धि%  = 15 + 10 + <math display=\"inline\"><mfrac><mrow><mn>15</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 26.5%",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Find the total amount of debt that will be discharged by 5 equal installments of ₹200 each, if the debt is due in 5 years at 5% p.a. at simple interest.</p>",
                    question_hi: "<p>54. ऋण की वह राशि ज्ञात कीजिए जो प्रत्येक ₹200 की 5 बराबर किस्&zwj;तों में चुकाई जाएगी, यदि ऋण 5% वार्षिक साधारण ब्याज की दर से 5 वर्षों में देय है।</p>",
                    options_en: ["<p>₹1,100</p>", "<p>₹1,200</p>", 
                                "<p>₹1,255</p>", "<p>₹1,400</p>"],
                    options_hi: ["<p>₹1,100</p>", "<p>₹1,200</p>",
                                "<p>₹1,255</p>", "<p>₹1,400</p>"],
                    solution_en: "<p>54.(a)<br>Rate = 5%<br>Let the installment = 100 units<br>Ratio - installment : amount<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 100<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 105<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 110<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 115<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 120<br>Total amount = 100 + 105 + 110 + 115 + 120 = 550 units<br>100 units = 200<br>550 units = ₹ 1100</p>",
                    solution_hi: "<p>54.(a)<br>दर = 5%<br>माना किस्त = 100 इकाई<br>अनुपात - किस्त&nbsp; :&nbsp; &nbsp;राशि <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp;:&nbsp; &nbsp;100<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp;:&nbsp; &nbsp;105<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp;:&nbsp; &nbsp;110<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp;:&nbsp; &nbsp;115<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp;:&nbsp; &nbsp;120<br>कुल राशि = 100 + 105 + 110 + 115 + 120 = 550 इकाई<br>100 इकाई = 200<br>550 इकाई = ₹ 1100</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The given table shows the number of 5 types of cars (Swift, Sx4, Ertiga, Zen, Echo) manufactured (in thousand) by Maruti over the years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278729.png\" alt=\"rId57\" width=\"315\" height=\"168\"> <br>Which type of cars manufactured by Maruti during 2007 to 2012 is the minimum ?</p>",
                    question_hi: "<p>55. दी गई तालिका कुछ वर्षों में मारुति द्वारा निर्मित 5 प्रकार की कारों (स्विफ्ट, Sx4, एर्टिगा, ज़ेन, इको) की संख्या (हजार में) दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278837.png\" alt=\"rId58\" width=\"306\" height=\"174\"> <br>2007 से 2012 के दौरान मारुति द्वारा निर्मित किस प्रकार की कारें सबसे कम हैं ?</p>",
                    options_en: ["<p>Echo</p>", "<p>Swift</p>", 
                                "<p>Zen</p>", "<p>Sx4</p>"],
                    options_hi: ["<p>इको</p>", "<p>स्विफ्ट</p>",
                                "<p>ज़ेन</p>", "<p>Sx4</p>"],
                    solution_en: "<p>55.(a)<br>Number of Echo cars manufactured between 2007 to 2012 = 115 + 120 + 135 + 125 + 130 + 120 = 745<br>Number of Swift cars manufactured between 2007 to 2012 = 250 + 200 + 230 + 245 + 260 + 275 = 1460<br>Number of Zen cars manufactured between 2007 to 2012 = 140 + 155 + 160 + 175 + 185 + 220 = 1035<br>Number of Sx4 cars manufactured between 2007 to 2012 = 200 + 230 + 225 + 210 + 135 + 155 = 1155<br>We can clearly see that the Echo is the minimum manufactured between 2007 to 2012.</p>",
                    solution_hi: "<p>55.(a)<br>2007 से 2012 के बीच निर्मित इको कारों की संख्या = 115 + 120 + 135 + 125 + 130 + 120 = 745<br>2007 से 2012 के बीच निर्मित स्विफ्ट कारों की संख्या = 250 + 200 + 230 + 245 + 260 + 275 = 1460<br>2007 से 2012 के बीच निर्मित ज़ेन कारों की संख्या = 140 + 155 + 160 + 175 + 185 + 220 = 1035<br>2007 से 2012 के बीच निर्मित Sx4 कारों की संख्या = 200 + 230 + 225 + 210 + 135 + 155 = 1155<br>हम स्पष्ट रूप से देख सकते हैं कि इको 2007 से 2012 के बीच सबसे कम निर्मित है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Study the given bar-graph and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379278936.png\" alt=\"rId59\" width=\"378\" height=\"279\"> <br>How many students obtained more than the average marks of the class ?</p>",
                    question_hi: "<p>56. दिए गए दंड आलेख का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379279064.png\" alt=\"rId60\" width=\"370\" height=\"265\"> <br>No. of Students = छात्रों की संख्या<br>Marks Obtained = प्राप्त अंक<br>कितने छात्रों ने कक्षा के औसत अंक से अधिक अंक प्राप्त किए ?</p>",
                    options_en: ["<p>8</p>", "<p>6</p>", 
                                "<p>10</p>", "<p>11</p>"],
                    options_hi: ["<p>8</p>", "<p>6</p>",
                                "<p>10</p>", "<p>11</p>"],
                    solution_en: "<p>56.(c)<br>Average marks of the class = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>125</mn><mo>&#215;</mo><mn>1</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>130</mn><mo>&#215;</mo><mn>3</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>135</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>140</mn><mo>&#215;</mo><mn>8</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>145</mn><mo>&#215;</mo><mn>5</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>150</mn><mo>&#215;</mo><mn>4</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>160</mn><mo>&#215;</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>1</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>8</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>3390</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> <br>= 141.25 <br>Number of students whose marks is more than average = 5 + 4 + 1 = 10</p>",
                    solution_hi: "<p>56.(c)<br>कक्षा के औसत अंक =<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>125</mn><mo>&#215;</mo><mn>1</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>130</mn><mo>&#215;</mo><mn>3</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>135</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>140</mn><mo>&#215;</mo><mn>8</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>145</mn><mo>&#215;</mo><mn>5</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>150</mn><mo>&#215;</mo><mn>4</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>160</mn><mo>&#215;</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>1</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>8</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>3390</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> <br>= 141.25<br>उन विद्यार्थियों की संख्या जिनके अंक औसत अंक से अधिक हैं = 5 + 4 + 1 = 10</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If the diameter of a hemisphere is 42 cm, then the volume of hemisphere (in cm<sup>3</sup>) is:<br>Take <math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></p>",
                    question_hi: "<p>57. यदि एक अर्धगोले का व्यास 42 cm है, तो अर्धगोले का आयतन (cm&sup3; में) _______है।<br>(<math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;का प्रयोग करें)</p>",
                    options_en: ["<p>19154</p>", "<p>19254</p>", 
                                "<p>19444</p>", "<p>19404</p>"],
                    options_hi: ["<p>19154</p>", "<p>19254</p>",
                                "<p>19444</p>", "<p>19404</p>"],
                    solution_en: "<p>57.(d) <br>Volume of the hemisphere = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>3</mn></mrow></msup></math><br>Volume of the hemisphere = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 21 &times; 21 &times; 21<br>Volume of the hemisphere = 19404 cm&sup3;</p>",
                    solution_hi: "<p>57.(d) <br>अर्धगोले का आयतन = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>3</mn></mrow></msup></math><br>अर्धगोले का आयतन = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 21 &times; 21 &times; 21<br>अर्धगोले का आयतन = 19404 cm&sup3;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Simplify:<br>{(3<sup>3</sup> + 2<sup>3</sup>) &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>} &times; {(6<sup>2</sup> + 3<sup>2</sup> - 4<sup>2</sup>) &divide; (14<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)}</p>",
                    question_hi: "<p>58. सरल कीजिए I<br>{(3<sup>3</sup> + 2<sup>3</sup>) &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>} &times; {(6<sup>2</sup> + 3<sup>2</sup> - 4<sup>2</sup>) &divide; (14<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)}</p>",
                    options_en: ["<p>220</p>", "<p>145</p>", 
                                "<p>110</p>", "<p>198</p>"],
                    options_hi: ["<p>220</p>", "<p>145</p>",
                                "<p>110</p>", "<p>198</p>"],
                    solution_en: "<p>58.(a)<br><math display=\"inline\"><mo>&#8658;</mo></math> {(3<sup>3</sup> + 2<sup>3</sup>) &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>} &times; {(6<sup>2</sup> + 3<sup>2</sup> - 4<sup>2</sup>) &divide; (14<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)}<br><math display=\"inline\"><mo>&#8658;</mo></math> {(27 + 8) &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>} &times; {(36 + 9 - 16) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>2</mn></mfrac></math>)}<br><math display=\"inline\"><mo>&#8658;</mo></math> {35 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>} &times; {89 &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>2</mn></mfrac></math>)}<br><math display=\"inline\"><mo>&#8658;</mo></math> {110} &times; {29 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>29</mn></mfrac></math>}<br><math display=\"inline\"><mo>&#8658;</mo></math> {110 &times; 2}<br><math display=\"inline\"><mo>&#8658;</mo></math> 220</p>",
                    solution_hi: "<p>58.(a)<br><math display=\"inline\"><mo>&#8658;</mo></math> {(3<sup>3</sup> + 2<sup>3</sup>) &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>} &times; {(6<sup>2</sup> + 3<sup>2</sup> - 4<sup>2</sup>) &divide; (14<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)}<br><math display=\"inline\"><mo>&#8658;</mo></math> {(27 + 8) &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>} &times; {(36 + 9 - 16) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>2</mn></mfrac></math>)}<br><math display=\"inline\"><mo>&#8658;</mo></math> {35 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>} &times; {89 &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>2</mn></mfrac></math>)}<br><math display=\"inline\"><mo>&#8658;</mo></math> {110} &times; {29 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>29</mn></mfrac></math>}<br><math display=\"inline\"><mo>&#8658;</mo></math> {110 &times; 2}<br><math display=\"inline\"><mo>&#8658;</mo></math> 220</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The price of an article is first increased by 40% and then decreased by 45%, due to reduction in sales. Find the net percentage change in the final price of the article.</p>",
                    question_hi: "<p>59. किसी वस्तु की कीमत में पहले 40% की वृद्धि की जाती है और फिर बिक्री में कमी होने के कारण, 45% की कमी की जाती है। वस्तु के अंतिम मूल्य में कुल प्रतिशत परिवर्तन ज्ञात कीजिए।</p>",
                    options_en: ["<p>-23%</p>", "<p>23%</p>", 
                                "<p>13%</p>", "<p>-13%</p>"],
                    options_hi: ["<p>-23%</p>", "<p>23%</p>",
                                "<p>13%</p>", "<p>-13%</p>"],
                    solution_en: "<p>59.(a)<br>%Change = 40 - 45 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mn>45</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> = - 23%</p>",
                    solution_hi: "<p>59.(a)<br>%परिवर्तन = 40 - 45 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mo>(</mo><mo>-</mo><mn>45</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math> = - 23%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A and B, working together, take 15 days to complete a piece of work. If A alone can do this work in 18 days, then how long would B take to complete the same work ?</p>",
                    question_hi: "<p>60. A और B, एक साथ काम करते हुए, एक काम को पूरा करने में 15 दिन लेते हैं। यदि A अकेले इस काम को 18 दिनों में पूरा कर सकता है, तो B को उसी काम को पूरा करने में कितना समय लगेगा ?</p>",
                    options_en: ["<p>33 days</p>", "<p>15 days</p>", 
                                "<p>8 days</p>", "<p>90 days</p>"],
                    options_hi: ["<p>33 दिन</p>", "<p>15 दिन</p>",
                                "<p>8 दिन</p>", "<p>90 दिन</p>"],
                    solution_en: "<p>60.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379279185.png\" alt=\"rId61\" width=\"237\" height=\"160\"><br>Efficiency of B = 6 - 5 = 1 units<br>Time taken by B to done whole work = <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 90 days</p>",
                    solution_hi: "<p>60.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379279317.png\" alt=\"rId62\" width=\"239\" height=\"180\"><br>B की क्षमता = 6 - 5 = 1 इकाई<br>B द्वारा पूरा कार्य करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 90 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The shortest length of the intercept between the x-axis and y-axis of the equation 6x + 8y - 48 = 0 is:</p>",
                    question_hi: "<p>61. समीकरण 6 x + 8y - 48 = 0 के x-अक्ष और y-अक्ष के बीच अंतः खंड की सबसे छोटी लंबाई कितनी है ?</p>",
                    options_en: ["<p>14 units</p>", "<p>6 units</p>", 
                                "<p>10 units</p>", "<p>12 units.</p>"],
                    options_hi: ["<p>14 इकाई</p>", "<p>6 इकाई</p>",
                                "<p>10 इकाई</p>", "<p>12 इकाई</p>"],
                    solution_en: "<p>61.(c)<br>To find the shortest length of the intercept between the <math display=\"inline\"><mi>x</mi></math>-axis and y-axis for the equation 6x + 8y - 48 = 0<br>The general intercept form of a linear equation is:<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>a</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mi>b</mi></mfrac></math> = 1<br><math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>x</mi></mrow><mrow><mn>48</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mi>y</mi></mrow><mn>48</mn></mfrac></math> = 1<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mn>6</mn></mfrac></math> = 1<br>identify intercept : <br><math display=\"inline\"><mi>x</mi></math>-intercept: 8<br><math display=\"inline\"><mi>y</mi></math>-intercept : 6<br>We use the pythagorean theorem to find this length:<br>Length = <math display=\"inline\"><msqrt><msup><mrow><mn>8</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn></msqrt></math> = 10 units<br>Thus, the shortest length of the intercept between the <math display=\"inline\"><mi>x</mi></math>-axis and y-axis is 10 units.</p>",
                    solution_hi: "<p>61.(c)<br>समीकरण 6<math display=\"inline\"><mi>x</mi></math> + 8y - 48 = 0 के लिए x-अक्ष और y-अक्ष के बीच अंतःखंड की न्यूनतम लंबाई ज्ञात करना<br>एक रेखीय समीकरण का सामान्य अंतःखंड का रूप है: <br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>a</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mi>b</mi></mfrac></math> = 1<br><math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>x</mi></mrow><mrow><mn>48</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mi>y</mi></mrow><mn>48</mn></mfrac></math> = 1<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mn>6</mn></mfrac></math> = 1<br>अंतःखंड की पहचान करें: <br><math display=\"inline\"><mi>x</mi></math>-अंतःखंड: 8<br><math display=\"inline\"><mi>y</mi></math>-अंतःखंड : 6<br>हम इस लंबाई को खोजने के लिए पाइथागोरस प्रमेय का उपयोग करते हैं:<br>लंबाई = <math display=\"inline\"><msqrt><msup><mrow><mn>8</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn></msqrt></math> = 10 इकाई<br>इस प्रकार, <math display=\"inline\"><mi>x</mi></math>-अक्ष और y-अक्ष के बीच अंतःखंड की न्यूनतम लंबाई 10 इकाई है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "62. Simplify the following expression.<br />[40 - 48 ÷ 4 of 3] ÷ {(6 + 4) - 4 of 4 + 4 + (6 - 4) + (4 × 4) ÷ 4} × 3",
                    question_hi: "62. निम्नलिखित व्यंजक को सरल कीजिए।<br />[40 - 48 ÷ 4 of 3] ÷ {(6 + 4) - 4 of 4 + 4 + (6 - 4) + (4 × 4) ÷ 4} × 3",
                    options_en: [" 30 ", " 26", 
                                " 25 ", " 27"],
                    options_hi: [" 30 ", " 26",
                                " 25 ", " 27"],
                    solution_en: "62.(d)<br /><math display=\"inline\"><mo>⇒</mo></math> [40 - 48 ÷ 4 of 3] ÷ {(6 + 4) - 4 of 4 + 4 + (6 - 4) + (4 × 4) ÷ 4} × 3<br /><math display=\"inline\"><mo>⇒</mo></math> [40 - 48 ÷ 12] ÷ {10 - 16 + 4 + 2 + 4} × 3<br /><math display=\"inline\"><mo>⇒</mo></math> 36 ÷ 4 × 3<br /><math display=\"inline\"><mo>⇒</mo></math> 27",
                    solution_hi: "62.(d)<br /><math display=\"inline\"><mo>⇒</mo></math> [40 - 48 ÷ 4 of 3] ÷ {(6 + 4) - 4 of 4 + 4 + (6 - 4) + (4 × 4) ÷ 4} × 3<br /><math display=\"inline\"><mo>⇒</mo></math> [40 - 48 ÷ 12] ÷ {10 - 16 + 4 + 2 + 4} × 3<br /><math display=\"inline\"><mo>⇒</mo></math> 36 ÷ 4 × 3<br /><math display=\"inline\"><mo>⇒</mo></math> 27",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. What is the number which needs to be added to 20% of 460 to have the sum as 70% of 920 ?</p>",
                    question_hi: "<p>63. वह संख्या कौन सी है, जिसे 460 के 20% में जोड़ने पर योग 920 के 70% के बराबर हो जाए ?</p>",
                    options_en: ["<p>762</p>", "<p>926</p>", 
                                "<p>335</p>", "<p>552</p>"],
                    options_hi: ["<p>762</p>", "<p>926</p>",
                                "<p>335</p>", "<p>552</p>"],
                    solution_en: "<p>63.(d)<br>Let the number be <math display=\"inline\"><mi>x</mi></math><br>460 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> + x = 920 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>100</mn></mfrac></math><br>92 + <math display=\"inline\"><mi>x</mi></math> = 644<br><math display=\"inline\"><mi>x</mi></math> = 644 - 92 = 552</p>",
                    solution_hi: "<p>63.(d)<br>माना संख्या <math display=\"inline\"><mi>x</mi></math> है<br>460 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> + x = 920 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>100</mn></mfrac></math><br>92 + <math display=\"inline\"><mi>x</mi></math> = 644<br><math display=\"inline\"><mi>x</mi></math> = 644 - 92 = 552</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Two quantities are in the ratio 3 : 5. If the first quantity is 155 kg. Find the other quantity (approximately).</p>",
                    question_hi: "<p>64. दो राशियाँ 3 : 5 के अनुपात में हैं। यदि पहली राशि 155 kg है, तो दूसरी राशि (लगभग) ज्ञात कीजिए।</p>",
                    options_en: ["<p>150.5 kg</p>", "<p>200.4 kg</p>", 
                                "<p>240.6 kg</p>", "<p>258.3 kg</p>"],
                    options_hi: ["<p>150.5 kg</p>", "<p>200.4 kg</p>",
                                "<p>240.6 kg</p>", "<p>258.3 kg</p>"],
                    solution_en: "<p>64.(d)<br>Let the first and the 2nd number be <math display=\"inline\"><mn>3</mn><mi>x</mi><mi>&#160;</mi><mi>a</mi><mi>n</mi><mi>d</mi><mi>&#160;</mi><mn>5</mn><mi>x</mi></math><br>3<math display=\"inline\"><mi>x</mi></math> = 155 kg <br>5<math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>53</mn></mfrac></math> &times; 5 = 258.3 kg</p>",
                    solution_hi: "<p>64.(d)<br>माना कि पहली और दूसरी संख्या 3<math display=\"inline\"><mi>x</mi></math> और 5x है<br>3<math display=\"inline\"><mi>x</mi></math> = 155 kg <br>5<math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>53</mn></mfrac></math> &times; 5 = 258.3 kg</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. If &Delta; ABC ~ &Delta; QRP; area of &Delta; ABC : area of &Delta; PQR = 9 : 4; AB = 18 cm and BC = 15 cm, then find the length of PR.</p>",
                    question_hi: "<p>65. यदि &Delta; ABC ~ &Delta; QRP; &Delta; ABC का क्षेत्रफल : &Delta; PQR का क्षेत्रफल = 9 : 4; AB = 18 cm और BC = 15 cm हैं, तो PR की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>15 cm</p>", "<p>12 cm</p>", 
                                "<p>10 cm</p>", "<p>14 cm</p>"],
                    options_hi: ["<p>15 cm</p>", "<p>12 cm</p>",
                                "<p>10 cm</p>", "<p>14 cm</p>"],
                    solution_en: "<p>65.(c)<br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">f</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">&#916;</mi><mi mathvariant=\"bold-italic\">A</mi><mi mathvariant=\"bold-italic\">B</mi><mi mathvariant=\"bold-italic\">C</mi></mrow><mrow><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">a</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">f</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">&#916;</mi><mi mathvariant=\"bold-italic\">Q</mi><mi mathvariant=\"bold-italic\">R</mi><mi mathvariant=\"bold-italic\">P</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>R</mi><mi>P</mi></mrow></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><msqrt><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>R</mi><mi>P</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mi mathvariant=\"bold-italic\">R</mi><mi mathvariant=\"bold-italic\">P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> <br>PR = 10 cm</p>",
                    solution_hi: "<p>65.(c)<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;ABC</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;</mi><mi>QRP</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>R</mi><mi>P</mi></mrow></mfrac></math>)<sup>2</sup><br><math display=\"inline\"><msqrt><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>R</mi><mi>P</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mi mathvariant=\"bold-italic\">R</mi><mi mathvariant=\"bold-italic\">P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math><br>PR = 10 cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Let C be a circle with Centre O and P be an external point to C. Let PQ and PR be the tangents to C such that Q and R be the points of tangency, respectively. If &ang;ROQ = 110&deg;, find &ang;RPQ.</p>",
                    question_hi: "<p>66. माना C केंद्र O वाला एक वृत्त है और P, C का एक बाहरी बिंदु है। माना PQ और PR, C की स्पर्श रेखाएं इस प्रकार हैं कि Q और R क्रमशः स्पर्श रेखा (tangency) के बिंदु हैं। यदि &ang;ROQ = 110&deg; है, तो &ang;RPQ का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>80&deg;</p>", "<p>90&deg;</p>", 
                                "<p>70&deg;</p>", "<p>110&deg;</p>"],
                    options_hi: ["<p>80&deg;</p>", "<p>90&deg;</p>",
                                "<p>70&deg;</p>", "<p>110&deg;</p>"],
                    solution_en: "<p>66.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379279461.png\" alt=\"rId63\" width=\"198\" height=\"145\"><br>We know that,<br>&ang;RPQ = 180&deg; - 110&deg; = 70&deg;</p>",
                    solution_hi: "<p>66.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379279461.png\" alt=\"rId63\" width=\"198\" height=\"145\"><br>हम जानते है कि, <br>&ang;RPQ = 180&deg; - 110&deg; = 70&deg;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. If&nbsp;&nbsp;is an acute angle and tan&theta; + cot&theta; = 2, then find the value of tan<sup>3</sup>&theta; + cot<sup>3</sup>&theta; + 6tan<sup>3</sup>&theta;cot<sup>2</sup>&theta;.</p>",
                    question_hi: "<p>67. यदि <math display=\"inline\"><mi>&#952;</mi></math> एक न्यूनकोण है और tan&theta; + cot&theta; = 2 है, तो tan<sup>3</sup>&theta; + cot<sup>3</sup>&theta; + 6tan<sup>3</sup>&theta;cot<sup>2</sup>&theta;. का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>8</p>", "<p>10</p>", 
                                "<p>12</p>", "<p>6</p>"],
                    options_hi: ["<p>8</p>", "<p>10</p>",
                                "<p>12</p>", "<p>6</p>"],
                    solution_en: "<p>67.(a) <br>According to the question,<br>tan<math display=\"inline\"><mi>&#952;</mi></math> + cot&theta; = 2 <br>Let <math display=\"inline\"><mi>&#952;</mi></math> = 45&deg; then,<br>1 + 1 = 2 (satisfied)<br>Now, put the value of <math display=\"inline\"><mi>&#952;</mi></math> = 45&deg; we get,<br><math display=\"inline\"><mo>&#8658;</mo></math> tan<sup>3</sup>&theta; + cot<sup>3</sup>&theta; + 6tan<sup>3</sup>&theta;cot<sup>2</sup>&theta;.<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 + 1 + 6 = 8</p>",
                    solution_hi: "<p>67.(a) <br>प्रश्न के अनुसार,<br>tan<math display=\"inline\"><mi>&#952;</mi></math> + cot&theta; = 2 <br>माना <math display=\"inline\"><mi>&#952;</mi></math> = 45&deg; तो,<br>1 + 1 = 2 (संतुष्ट)<br>अब, <math display=\"inline\"><mi>&#952;</mi></math> = 45&deg; रखने पर हमें प्राप्त होता है,<br><math display=\"inline\"><mo>&#8658;</mo></math> tan<sup>3</sup>&theta; + cot<sup>3</sup>&theta; + 6tan<sup>3</sup>&theta;cot<sup>2</sup>&theta;.<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 + 1 + 6 = 8</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Mithila covers 50 km by bus in 90 minutes. After deboarding the bus, she takes rest for 15 minutes and covers another 30 km by a taxi in 35 minutes. Find the average speed (in km/h) for the whole journey.</p>",
                    question_hi: "<p>68. मिथिला बस से 50 km की दूरी 90 मिनट में तय करती है। बस से उतरने के बाद, वह 15 मिनट आराम करती है और टैक्सी द्वारा 35 मिनट में 30 km की एक और दूरी तय करती है। पूरी यात्रा के लिए औसत चाल (km/h में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>34<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>32<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p>31<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>33<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>34<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>32<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p>31<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p>33<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>68.(a)<br>Average speed = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow></mfrac></math><br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>50</mn><mo>+</mo><mn>30</mn><mo>)</mo><mo>&#215;</mo><mn>60</mn></mrow><mrow><mo>(</mo><mn>90</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>35</mn><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>140</mn></mfrac><mo>=</mo><mfrac><mn>240</mn><mn>7</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>34</mn><mfrac><mn>2</mn><mn>7</mn></mfrac></math> km/h</p>",
                    solution_hi: "<p>68.(a)<br>औसत चाल = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math><br>औसत चाल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>50</mn><mo>+</mo><mn>30</mn><mo>)</mo><mo>&#215;</mo><mn>60</mn></mrow><mrow><mo>(</mo><mn>90</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>35</mn><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>140</mn></mfrac><mo>=</mo><mfrac><mn>240</mn><mn>7</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>34</mn><mfrac><mn>2</mn><mn>7</mn></mfrac></math> km/h</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A policeman received information that a thief is at a distance of 1.5 km from him. The thief starts moving by car and the policeman chases him by car. The thief and the policeman are moving at the speeds of 90 km/h and 120 km/h respectively. At what distance (in km) will the police catch the thief ?</p>",
                    question_hi: "<p>69. एक पुलिसकर्मी को सूचना मिली कि एक चोर उससे 1.5 km की दूरी पर है। चोर कार से चलना शुरू करता है और पुलिसकर्मी कार से उसका पीछा करता है। चोर और पुलिसकर्मी क्रमशः 90 km/h और 120 km/h की चाल से आगे बढ़ रहे हैं। पुलिसकर्मी किस दूरी (km में) पर चोर को पकड़ लेगा ?</p>",
                    options_en: ["<p>45.0 km</p>", "<p>4.5 km</p>", 
                                "<p>6 km</p>", "<p>5 km</p>"],
                    options_hi: ["<p>45.0 km</p>", "<p>4.5 km</p>",
                                "<p>6 km</p>", "<p>5 km</p>"],
                    solution_en: "<p>69.(c) <br>Relative speed = 120 - 90 = 30 km/h<br>Time taken to caught thief = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math> hour<br>1 hour = 120 km<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> hours = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>1</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math> = 6 km</p>",
                    solution_hi: "<p>69.(c) <br>सापेक्ष गति = 120 - 90 = 30 km/h<br>चोर को पकड़ने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math> घंटा<br>1 घंटा = 120 km<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> घंटा = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>1</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math> = 6 km</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Study the given bar-graph and answer the question that follows. <br>An electric vehicle company produces three different car models &ndash; A, B and C. The production of three different models over a period of four years (in lakhs) has been shown the bar-graph. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379279617.png\" alt=\"rId64\" width=\"336\" height=\"295\"> <br>The total production of model C in 2021 and 2022 is what percentage of the total production of model A in 2019 and 2020 ?</p>",
                    question_hi: "<p>70. नीचे दिए गए बार-ग्राफ का अध्ययन कीजिए और नीचे दिए गए प्रश्नो का उत्तर दीजिए। <br>एक विद्युत वाहन बनाने वाली कंपनी तीन विभिन्न कार मोडेल &ndash; A, B और C बनाती है। बार-ग्राफ में दिए गए चार वर्ष के दौरान तीन विभिन्न कार मोडेल का उत्पादन (लाख में) दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379279743.png\" alt=\"rId65\" width=\"369\" height=\"339\"> <br>संदर्भ : Production of three different electric car model - A, B, C by a company over the years (In Lakhs) = दिए गए चार वर्ष के दौरान एक कंपनी द्वारा तीन विभिन्न विद्यु त कार मोडेल &ndash; A, B और C का उत्पादन (लाख में)<br>2021 और 2022 में मोडेल C का कुल उत्पादन, 2019 और 2020 में मोडेल A के कुल उत्पादन का कितना प्रतिशत है?</p>",
                    options_en: ["<p>102.26%</p>", "<p>122.22%</p>", 
                                "<p>105.22%</p>", "<p>115.57%</p>"],
                    options_hi: ["<p>102.26%</p>", "<p>122.22%</p>",
                                "<p>105.22%</p>", "<p>115.57%</p>"],
                    solution_en: "<p>70.(b) <br>total production of model C in 2021 and 2022 = 6 + 5 = 11<br>total production of model A in 2019 and 2020 = 5 + 4 = 9<br>required% = <math display=\"inline\"><mfrac><mrow><mn>1100</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 122.22</p>",
                    solution_hi: "<p>70.(b) <br>2021 और 2022 में मॉडल C का कुल उत्पादन = 6 + 5 = 11<br>2019 और 2020 में मॉडल A का कुल उत्पादन = 5 + 4 = 9<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>1100</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 122.22</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "71. A shopkeeper marks an article at ₹150 and sells it at a discount of 25%. He also gives a gift worth ₹2.5. If he still makes a 10% profit, then the cost price (in ₹) of the article is:",
                    question_hi: "71. एक दुकानदार एक वस्तु पर ₹150 मूल्य अंकित करता है और उसे 25% की छूट पर बेचता है। साथ ही वह वस्तु की खरीद पर ₹2.5 का गिफ्ट भी देता है। यदि वह फिर भी 10% का लाभ अर्जित करता है, तो वस्तु का क्रय मूल्य (₹ में) क्या है?",
                    options_en: [" 175 ", " 100 ", 
                                " 125 ", " 112"],
                    options_hi: [" 175 ", " 100 ",
                                " 125 ", " 112"],
                    solution_en: "71.(b) <br />According to the question,<br />SP of the article = 150 × <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - 2.5 = ₹ 110 <br />CP of the article = 110 × <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> = ₹ 100",
                    solution_hi: "71.(b) <br />प्रश्न के अनुसार,<br />वस्तु का विक्रय मूल्य = 150 × <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> - 2.5 = ₹ 110 <br />वस्तु का क्रय मूल्य = 110 × <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> = ₹ 100",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "72. A shopkeeper bought 288 oranges for ₹115.20. He sold 50 of them at 70 paise each and the remaining at ₹46.20. Find his profit or loss.",
                    question_hi: "72. एक दुकानदार ने ₹115.20 में 288 संतरे खरीदे। उसने उनमें से 50 को 70 पैसे प्रति संतरे की दर से और शेष ₹46.20 में बेच दिए। उसका लाभ या हानि ज्ञात कीजिए।",
                    options_en: [" Loss, ₹36 ", " Loss, ₹34 ", 
                                " Profit, ₹37 ", " Profit, ₹25"],
                    options_hi: [" ₹36 की हानि", " ₹34 की हानि",
                                " ₹37 का लाभ", " ₹25 का लाभ"],
                    solution_en: "72.(b)<br />SP of 50 oranges = 50 × 0.7 = ₹ 35<br />SP of remaining oranges = 46.20<br />Total SP = 35 + 46.20 = 81.2<br />Loss = 115.20 - 81.2 = ₹ 34",
                    solution_hi: "72.(b)<br />50 संतरो का विक्रय मूल्य = 50 × 0.7 = ₹ 35<br />शेष संतरो का विक्रय मूल्य = 46.20<br />कुल विक्रय मूल्य = 35 + 46.20 = 81.2<br />हानि = 115.20 - 81.2 = ₹ 34",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Find the difference between the curved surface area and total surface area of a right circular cylinder having a length of 68 cm and base diameter of 28 cm.</p>",
                    question_hi: "<p>73. 68 cm लंबाई और 28 cm के आधार व्यास वाले एक लंब वृत्तीय बेलन के वक्र पृष्ठीय क्षेत्रफल और संपूर्ण पृष्ठीय क्षेत्रफल के बीच का अंतर ज्ञात करें।</p>",
                    options_en: ["<p>1432 cm<sup>2</sup></p>", "<p>1332 cm<sup>2</sup></p>", 
                                "<p>1132 cm<sup>2</sup></p>", "<p>1232 cm<sup>2</sup></p>"],
                    options_hi: ["<p>1432 cm<sup>2</sup></p>", "<p>1332 cm<sup>2</sup></p>",
                                "<p>1132 cm<sup>2</sup></p>", "<p>1232 cm<sup>2</sup></p>"],
                    solution_en: "<p>73.(d)<br>Difference between TSA and CSA of cylinder = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mo>(</mo><mi>h</mi><mo>+</mo><mi>r</mi><mo>)</mo><mo>-</mo><mn>2</mn><mi>&#960;</mi><mi>r</mi><mi>h</mi></math><br>= 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi></math>[h + r - h]<br>= 2<math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>Required difference (2<math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math>) = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 14 &times; 14 <br>= 1232 cm<sup>2</sup></p>",
                    solution_hi: "<p>73.(d)<br>बेलन के संपूर्ण पृष्ठीय क्षेत्रफल और पृष्ठीय क्षेत्रफल के बीच का अंतर = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mo>(</mo><mi>h</mi><mo>+</mo><mi>r</mi><mo>)</mo><mo>-</mo><mn>2</mn><mi>&#960;</mi><mi>r</mi><mi>h</mi></math><br>= 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi></math>[h + r - h]<br>= 2<math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>आवश्यक अंतर (2<math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math>) = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 14 &times; 14<br>= 1232 cm<sup>2</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The perimeter of a triangle is 36 units. Its area cannot be more than ______ square units.</p>",
                    question_hi: "<p>74. एक त्रिभुज का परिमाप 36 इकाई है। इसका क्षेत्रफल ____वर्ग इकाई से अधिक नहीं हो सकता।</p>",
                    options_en: ["<p>12<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>36<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", 
                                "<p>36<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>18<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    options_hi: ["<p>12<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>36<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>",
                                "<p>36<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>18<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    solution_en: "<p>74.(c)<br>To find the maximum possible area of a triangle with a perimeter of 36 units, we assume it&rsquo;s an equilateral triangle.<br>Perimeter = 3a = 36, a = 12 units<br>Area of the equilateral triangle = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac><msup><mrow><mi>s</mi><mi>i</mi><mi>d</mi><mi>e</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 12 &times; 12<br>= 36<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;square units</p>",
                    solution_hi: "<p>74.(c)<br>36 इकाई की परिधि वाले त्रिभुज का अधिकतम संभव क्षेत्रफल ज्ञात करने के लिए, हम मानते हैं कि यह एक समबाहु त्रिभुज है।<br>परिमाप = 3a = 36, a = 12 इकाई<br>समबाहु त्रिभुज का क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>भुजा<sup>2</sup><br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 12 &times; 12<br>= 36<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> वर्ग इकाई</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "18",
                    question_en: "<p>75. Study the given bar-graphs and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379279882.png\" alt=\"rId66\" width=\"366\" height=\"317\"> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379280027.png\" alt=\"rId67\" width=\"363\" height=\"313\"> <br>Turnover of Company X (Product-wise: A,B,C,D and E)<br>Approximately what is the share in the total profit enjoyed by A and E together ?</p>",
                    question_hi: "<p>75. दिए गए दंड-आलेख का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379280146.png\" alt=\"rId68\" width=\"323\" height=\"275\"> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379280285.png\" alt=\"rId69\" width=\"319\" height=\"275\"> <br>Turnover of Company product-wise = उत्पाद के अनुसार कंपनी का कारोबार<br>Profitability percentage of Product = उत्पाद का लाभप्रदता प्रतिशत<br>कंपनी x का कारोबार (उत्पाद-वार: A,B,C,D और E)<br>कुल लाभ में A और E को मिलाकर प्राप्त हुआ लाभ लगभग कितना है ?</p>",
                    options_en: ["<p>50%</p>", "<p>80%</p>", 
                                "<p>70%</p>", "<p>60%</p>"],
                    options_hi: ["<p>50%</p>", "<p>80%</p>",
                                "<p>70%</p>", "<p>60%</p>"],
                    solution_en: "<p>75.(c) <br>Profit of company A = 25% <math display=\"inline\"><mo>&#215;</mo></math> 30 = 7.5<br>Profit of company B = 7.5% <math display=\"inline\"><mo>&#215;</mo></math> 40 = 3<br>Profit of company C = 20% <math display=\"inline\"><mo>&#215;</mo></math> 20 = 4<br>Profit of company D = 7.5% <math display=\"inline\"><mo>&#215;</mo></math> 50 = 3.75<br>Profit of company E = 25% <math display=\"inline\"><mo>&#215;</mo></math> 60 = 15<br>Total profit = 7.5 + 3 + 4 + 3.75 + 15 = 33.25<br>Profit by company A and E = 7.5 + 15 = 22.5<br>profit % enjoyed by A and E together = <math display=\"inline\"><mfrac><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>33</mn><mo>.</mo><mn>25</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 67.66<br>By going through option one by one we can see that only option (c) satisfies the value.</p>",
                    solution_hi: "<p>75.(c) <br>कंपनी A का लाभ = 25% <math display=\"inline\"><mo>&#215;</mo></math> 30 = 7.5<br>कंपनी B का लाभ = 7.5% <math display=\"inline\"><mo>&#215;</mo></math> 40 = 3<br>कंपनी C का लाभ = 20% <math display=\"inline\"><mo>&#215;</mo></math> 20 = 4<br>कंपनी D का लाभ = 7.5% <math display=\"inline\"><mo>&#215;</mo></math> 50 = 3.75<br>कंपनी E का लाभ = 25% <math display=\"inline\"><mo>&#215;</mo></math> 60 = 15<br>कुल लाभ = 7.5 + 3 + 4 + 3.75 + 15 = 33.25<br>कंपनी A और E द्वारा लाभ = 7.5 + 15 = 22.5<br>A और E द्वारा प्राप्त लाभ %= <math display=\"inline\"><mfrac><mrow><mn>22</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>33</mn><mo>.</mo><mn>25</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 67.66<br>एक-एक करके विकल्प की जाॅंच करने पर हम देख सकते हैं कि केवल विकल्प (c) ही मान को संतुष्ट करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "18",
                    question_en: "<p>76. In April 2023, Wing Commander Deepika Misra received which of the following awards ?</p>",
                    question_hi: "<p>76. अप्रैल 2023 में, विंग कमांडर दीपिका मिश्रा को निम्नलिखित में से कौन-सा पुरस्कार मिला ?</p>",
                    options_en: ["<p>Vir Chakra</p>", "<p>Vayu Sena Medal</p>", 
                                "<p>Uttam Yudh Seva Medal</p>", "<p>Shaurya Chakra</p>"],
                    options_hi: ["<p>वीर चक्र</p>", "<p>वायु सेना पदक</p>",
                                "<p>उत्तम युद्ध सेवा पदक</p>", "<p>शौर्य चक्र</p>"],
                    solution_en: "<p>76.(b) <strong>Vayu Sena Medal. </strong>It is a military decoration given to members of the Indian Air Force for exceptional devotion to duty or acts of courage. Deepika Misra received the award for providing humanitarian assistance and operating disaster relief operations in response to flash floods in northern Madhya Pradesh in August 2021.</p>",
                    solution_hi: "<p>76.(b)<strong> वायु सेना पदक।</strong> यह भारतीय वायु सेना के सैनिकों को कर्तव्य के प्रति असाधारण समर्पण और साहसिक कार्यों के लिए दिया जाने वाला एक सैन्य सम्मान है। दीपिका मिश्रा को अगस्त 2021 में उत्तरी मध्य प्रदेश में अचानक आई बाढ़ में मानवीय सहायता प्रदान करने और आपदा राहत अभियान चलाने के लिए इस पुरस्कार से सम्मानित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "18",
                    question_en: "<p>77. Where is Birsa Munda International Hockey Stadium located ?</p>",
                    question_hi: "<p>77. बिरसा मुंडा अंतरराष्ट्रीय हॉकी स्टेडियम कहां स्थित है ?</p>",
                    options_en: ["<p>Ranchi</p>", "<p>Rourkela</p>", 
                                "<p>Bhubaneswar</p>", "<p>Jamshedpur</p>"],
                    options_hi: ["<p>रांची</p>", "<p>राउरकेला</p>",
                                "<p>भुवनेश्वर</p>", "<p>जमशेदपुर</p>"],
                    solution_en: "<p>77.(b) <strong>Rourkela </strong>(India&rsquo;s largest hockey stadium). Birsa Munda was a tribal leader who led the Munda Rebellion against British rule and the exploitation of his people in Jharkhand. Stadiums in India named after famous Personalities: Indira Gandhi Stadium (New Delhi), Rajiv Gandhi International Cricket Stadium (Hyderabad), Narendra Modi Stadium (Ahmedabad), Lal Bahadur Shastri Stadium (Hyderabad), Arun Jaitley Stadium (New Delhi), Major Dhyan Chand Hockey Stadium (Lucknow).</p>",
                    solution_hi: "<p>77.(b) <strong>राउरकेला</strong> (भारत का सबसे बड़ा हॉकी स्टेडियम)। बिरसा मुंडा एक आदिवासी नेता थे जिन्होंने झारखंड में ब्रिटिश शासन और अपने लोगों के शोषण के खिलाफ मुंडा विद्रोह का नेतृत्व किया था। भारत में प्रसिद्ध हस्तियों के नाम पर बने स्टेडियम: इंदिरा गांधी स्टेडियम (नई दिल्ली), राजीव गांधी अंतर्राष्ट्रीय क्रिकेट स्टेडियम (हैदराबाद), नरेंद्र मोदी स्टेडियम (अहमदाबाद), लाल बहादुर शास्त्री स्टेडियम (हैदराबाद), अरुण जेटली स्टेडियम (नई दिल्ली), मेजर ध्यानचंद हॉकी स्टेडियम (लखनऊ) इत्यादि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "18",
                    question_en: "<p>78. Which of the following methods measures poverty on the basis of income and<br>consumption levels ?</p>",
                    question_hi: "<p>78. निम्न में से कौन-सी विधि आय और उपभोग स्तरों के आधार पर गरीबी को मापती है ?</p>",
                    options_en: ["<p>Poverty band</p>", "<p>Poverty limit</p>", 
                                "<p>Poverty bar</p>", "<p>Poverty line</p>"],
                    options_hi: ["<p>गरीबी बैंड</p>", "<p>गरीबी सीमा</p>",
                                "<p>गरीबी बार</p>", "<p>गरीबी रेखा</p>"],
                    solution_en: "<p>78.(d) <strong>Poverty line.</strong> Poverty Estimation in India : It is based on the income or consumption levels and if the income or consumption falls below a given minimum level, then the household is said to be Below the Poverty Line (BPL). It is carried out by NITI Aayog&rsquo;s task force through the calculation of poverty line based on the data captured by the National Sample Survey Office under the Ministry of Statistics and Programme Implementation (MOSPI).</p>",
                    solution_hi: "<p>78.(d) <strong>गरीबी रेखा। </strong>भारत में गरीबी का अनुमान: यह आय या उपभोग के स्तर पर आधारित है और यदि आय या उपभोग किसी न्यूनतम स्तर से नीचे चला जाता है, तो ऐसे परिवार को गरीबी रेखा से नीचे (BPL) कहा जाता है। यह सांख्यिकी और कार्यक्रम कार्यान्वयन मंत्रालय (MOSPI) के तहत राष्ट्रीय प्रतिदर्श सर्वेक्षण कार्यालय द्वारा एकत्र किए गए डेटा के आधार पर गरीबी रेखा की गणना के माध्यम से नीति आयोग के टास्क फोर्स द्वारा किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "18",
                    question_en: "<p>79. What is the colour of the sending off card in football ?</p>",
                    question_hi: "<p>79. फुटबॉल में सेंडिंग ऑफ कार्ड (sending off card) का रंग कैसा होता है ?</p>",
                    options_en: ["<p>Yellow</p>", "<p>Black</p>", 
                                "<p>Red</p>", "<p>Blue</p>"],
                    options_hi: ["<p>पीला</p>", "<p>काला</p>",
                                "<p>लाल</p>", "<p>नीला</p>"],
                    solution_en: "<p>79.(c) <strong>Red. </strong>Yellow colour Card - A card used to warn a player in Football. In football, there are 11 players on each side. A standard football match is 90 minutes made up of two 45-minute halves. FIFA recommendations for field dimensions in professional football are 105 metres in length and 68 metres in width.</p>",
                    solution_hi: "<p>79.(c) <strong>लाल।</strong> पीले रंग का कार्ड - फुटबॉल में खिलाड़ी को चेतावनी देने के लिए इस्तेमाल किया जाने वाला कार्ड। फुटबॉल में, प्रत्येक पक्ष में 11 खिलाड़ी होते हैं। एक मानक फुटबॉल मैच 90 मिनट का होता है जिसमे 45-45 मिनट की दो पारियाँ होती है। FIFA द्वारा अनुशंसित फुटबॉल के मैदान का आयाम 105 मीटर लंबा और 68 मीटर चौड़ा है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "18",
                    question_en: "<p>80. Fighting in the upright position in judo is called ________.</p>",
                    question_hi: "<p>80. जूडो में सीधी स्थिति में खड़े होकर लड़ना _______कहलाता है।</p>",
                    options_en: ["<p>ne-waza</p>", "<p>tachi-waza</p>", 
                                "<p>koshi-waza</p>", "<p>ashi-waza </p>"],
                    options_hi: ["<p>ने-वाजा (ne-waza)</p>", "<p>ताची-वाजा (tachi-waza)</p>",
                                "<p>कोशी-वाजा (koshi-waza)</p>", "<p>अशी-वाजा (ashi-waza)</p>"],
                    solution_en: "<p>80.(b)<strong> tachi-waza.</strong> Judo has its origin in the ancient Japanese art of Ju-jitsu. Kano Jigoro invented Judo in 1882. It was used in war and practiced in full body armor. Other Terminology: Kumi Kata - a gripping pattern. Shintai - forwards, sideways and backward movement of the body during Judo. Ukemi - Breakfall techniques.</p>",
                    solution_hi: "<p>80.(b) <strong>ताची-वाजा</strong> (tachi-waza)। जूडो की उत्पत्ति प्राचीन जापानी कला जू-जित्सु से हुई है। कानो जिगोरो ने 1882 में जूडो का आविष्कार किया था। इसका प्रयोग युद्ध में किया जाता था और पूरे शरीर पर कवच पहनकर अभ्यास किया जाता था। अन्य शब्दावली: कुमी काटा - प्रतिद्वंदी को पकड़कर नियंत्रित करने का तरीका। शिंताई - जूडो के दौरान शरीर की आगे, बगल और पीछे की ओर गति। उकेमी - ब्रेकफॉल तकनीक।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "18",
                    question_en: "<p>81. Muharram is the _______ month of the Islamic Calendar and is considered a holy month.</p>",
                    question_hi: "<p>81. मुहर्रम इस्लामिक कलेंडर का _____महीना है और इसे एक पवित्र महीना माना जाता है।</p>",
                    options_en: ["<p>fourth</p>", "<p>second</p>", 
                                "<p>first</p>", "<p>third</p>"],
                    options_hi: ["<p>चौथा</p>", "<p>दूसरा</p>",
                                "<p>पहला</p>", "<p>तीसरा</p>"],
                    solution_en: "<p>81.(c) <strong>first</strong>. Muharram : It is a sacred festival that commemorates the martyrdom of Hazrat Imam Hussein. During this occasion, a relic called Alam is taken out as a procession. This festival is known as Peerla Panduga in Telangana. Other festivals of Muslims : Id-ul-Zuha, Shab-e-Barat, Milad-un-Nabi, Ramadan, Eid al-Adha.</p>",
                    solution_hi: "<p>81.(c) <strong>पहला।</strong> मुहर्रम: यह एक पवित्र त्योहार है जो हज़रत इमाम हुसैन की शहादत की याद में मनाया जाता है। इस अवसर पर अलम नामक अवशेष को जुलूस के रूप में निकाला जाता है। इस त्योहार को तेलंगाना में पीरला पांडुगा के नाम से जाना जाता है। मुसलमानों के अन्य प्रमुख त्योहार: ईद-उल-जुहा, शब-ए-बारात, मिलाद-उन-नबी, रमजान, ईद-उल-अज़हा आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "18",
                    question_en: "<p>82. Which of the following animals can change their gender during their lifespan ?</p>",
                    question_hi: "<p>82. निम्नलिखित में से कौन सा जानवर अपने जीवन काल के दौरान अपना लिंग बदल सकता है?</p>",
                    options_en: ["<p>Humans</p>", "<p>Ascaris</p>", 
                                "<p>nereis</p>", "<p>Snail</p>"],
                    options_hi: ["<p>मनुष्य (Humans)</p>", "<p>एस्केरिस (Ascaris)</p>",
                                "<p>नेरीस (nereis)</p>", "<p>घोंघा (Snail)</p>"],
                    solution_en: "<p>82.(d) <strong>Snail. </strong>Most land snails are hermaphrodites, meaning they have both male and female reproductive organs. They are able to produce sperm and eggs while mating with a partner. The male reproductive organ of the snail can never fertilize the female genitals of the snail. Animals who can change their sex during their life : Clownfish, Bearded dragons, Banana slugs.</p>",
                    solution_hi: "<p>82.(d) <strong>घोंघा।</strong> अधिकांश स्थल घोंघे उभयलिंगी होते हैं, जिसका अर्थ है कि उनमें नर और मादा दोनों प्रजनन अंग होते हैं। वे साथी के साथ यौन क्रिया करते समय शुक्राणु और अंडे उत्पन्न करने में सक्षम होते हैं। घोंघे का नर प्रजनन अंग कभी भी घोंघे के मादा जननांगों को निषेचित नहीं कर सकता है। वे जानवर जो अपने जीवन के दौरान अपना लिंग परिवर्तित कर सकते हैं: क्लाउनफ़िश, बियर्डेड ड्रैगन (पोगोना विटिसेप्स), बनाना स्लग आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "18",
                    question_en: "<p>83. Which of the following is the maximum file size that can be attached to an email message in Gmail ?</p>",
                    question_hi: "<p>83. जीमेल (Gmail) में एक ईमेल संदेश से जोड़ा जा सकने वाला अधिकतम फ़ाइल साइज़ निम्न में से कौन-सा है ?</p>",
                    options_en: ["<p>100 MB</p>", "<p>200 MB</p>", 
                                "<p>25 MB</p>", "<p>50 MB</p>"],
                    options_hi: ["<p>100 MB</p>", "<p>200 MB</p>",
                                "<p>25 MB</p>", "<p>50 MB</p>"],
                    solution_en: "<p>83.(c)<strong> 25 MB.</strong> Email (electronic mail) is the exchange of computer-stored messages from one user to one or more recipients via the internet. An email message consists of two main components: header and body. Each email message comes with a header that\'s structured into various fields. These fields contain important information regarding the sender and the recipient(s).</p>",
                    solution_hi: "<p>83.(c) <strong>25 MB. </strong>ईमेल (इलेक्ट्रॉनिक मेल) इंटरनेट के माध्यम से एक उपयोगकर्ता से एक या अधिक प्राप्तकर्ताओं को कंप्यूटर-संग्रहित संदेशों का आदान-प्रदान है। एक ईमेल संदेश में दो मुख्य घटक होते हैं: हेडर और बॉडी। प्रत्येक ईमेल संदेश एक हेडर के साथ आता है जो विभिन्न क्षेत्रों में संरचित होता है। इन क्षेत्रों में प्रेषक और प्राप्तकर्ता के बारे में महत्वपूर्ण जानकारी होती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "18",
                    question_en: "<p>84. Which of the following is the correct formula for calculating NNP ?</p>",
                    question_hi: "<p>84. NNP की गणना के लिए निम्नलिखित में से कौन-सा सही सूत्र है ?</p>",
                    options_en: ["<p>NNP = GNP &ndash; DEPRECIATION</p>", "<p>NNP = GNP + DEPRECIATION</p>", 
                                "<p>NNP = GDP &ndash; DEPRECIATION</p>", "<p>NNP = GDP &ndash; INCOME</p>"],
                    options_hi: ["<p>NNP = GNP - मूल्यह्रास</p>", "<p>NNP = GNP + मूल्यह्रास</p>",
                                "<p>NNP = GDP - मूल्यह्रास</p>", "<p>NNP = GDP - आय</p>"],
                    solution_en: "<p>84.(a)<strong> NNP = GNP &ndash; Depreciation. </strong>Net National Product (NNP) - Total value of finished goods and services produced by a country\'s citizens overseas and domestically, minus depreciation. Gross National Product (GNP) is Gross Domestic Product (GDP) plus net factor income from abroad. Depreciation is an annual decrease in the price of a capital goods over a year due to wear and tear and some other reasons.</p>",
                    solution_hi: "<p>84.(a) <strong>NNP = GNP - मूल्यह्रास। </strong>शुद्ध राष्ट्रीय उत्पाद (NNP) - किसी देश के नागरिकों द्वारा विदेशों और घरेलू स्तर पर उत्पादित तैयार सामान और सेवाओं के कुल मूल्य से मूल्यह्रास घटाने पर प्राप्त होता है। सकल राष्ट्रीय उत्पाद (GNP) , सकल घरेलू उत्पाद (GDP) और विदेशों से शुद्ध कारक आय का योग है। मूल्यह्रास एक पूंजीगत वस्तु की कीमत में एक वर्ष में टूट-फूट और कुछ अन्य कारणों से होने वाली वार्षिक कमी है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "18",
                    question_en: "<p>85. The British Government passed a law in the year 1856. It was meant for which among the following social reforms?</p>",
                    question_hi: "<p>85. ब्रिटिश सरकार ने वर्ष 1856 में एक कानून पारित किया था। यह निम्नलिखित में से किस सामाजिक सुधार के लिए था ?</p>",
                    options_en: ["<p>Widow Remarriage</p>", "<p>Abolition of the Practice of Sati</p>", 
                                "<p>Abolition of Practice of Child Marriage</p>", "<p>Law against Murdering of Female Child</p>"],
                    options_hi: ["<p>विधवा पुनर्विवाह</p>", "<p>सती प्रथा का उन्मूलन</p>",
                                "<p>बाल विवाह प्रथा का उन्मूलन</p>", "<p>कन्या शिशु की हत्या के विरुद्ध कानून</p>"],
                    solution_en: "<p>85.(a) <strong>Widow Remarriage.</strong> The Hindu Widow Remarriage Act of 1856 was enacted with the efforts of Ishwar Chandra Vidyasagar.<br>The draft of the Hindu Widows\' Remarriage Act, 1856 was prepared by Lord Dalhousie and passed by Lord Canning.</p>",
                    solution_hi: "<p>85.(a) <strong>विधवा पुनर्विवाह।</strong> ईश्वर चंद्र विद्यासागर के प्रयासों से हिंदू विधवा पुनर्विवाह अधिनियम, 1856 बनाया गया था। हिंदू विधवा पुनर्विवाह अधिनियम, 1856 का प्रारूप लॉर्ड डलहौजी द्वारा तैयार किया गया था तथा इस प्रारूप को लॉर्ड कैनिंग द्वारा पारित किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "18",
                    question_en: "<p>86. The Citizenship Act, 1955 does NOT deal with ______.</p>",
                    question_hi: "<p>86. नागरिकता अधिनियम, 1955, ______से संबंधित नहीं है।</p>",
                    options_en: ["<p>acquisition</p>", "<p>determination</p>", 
                                "<p>election</p>", "<p>termination</p>"],
                    options_hi: ["<p>अर्जन (acquisition)</p>", "<p>पर्यवसान (determination)</p>",
                                "<p>निर्वाचन (election)</p>", "<p>समापन (termination)</p>"],
                    solution_en: "<p>86.(c) <strong>election. </strong>The Citizenship Act, 1955 deals with the acquisition, determination, and termination of Indian citizenship. Citizenship Acquisition: The Act provides for the acquisition of citizenship by birth, descent, registration, naturalization, and incorporation of territory. Citizenship Determination: The Act defines how to determine Indian citizenship. Citizenship Termination: The Act provides for the termination and deprivation of citizenship. Election Commission of India: PART XV (Article 324-329) of the Constitution.</p>",
                    solution_hi: "<p>86.(c) <strong>निर्वाचन</strong> (election)। नागरिकता अधिनियम, 1955 भारतीय नागरिकता के अधिग्रहण, निर्धारण और समाप्ति से संबंधित है। नागरिकता अधिग्रहण: यह अधिनियम जन्म, वंश, पंजीकरण, देशीकरण और क्षेत्र के समावेश द्वारा नागरिकता के अधिग्रहण का प्रावधान करता है। नागरिकता निर्धारण: यह अधिनियम परिभाषित करता है कि भारतीय नागरिकता का निर्धारण कैसे किया जाये। नागरिकता समाप्ति: यह अधिनियम नागरिकता की समाप्ति और वंचित करने का प्रावधान करता है। भारत निर्वाचन आयोग: संविधान का भाग XV (अनुच्छेद 324-329)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "18",
                    question_en: "<p>87. A circular coil having &lsquo;n&rsquo; turns produces a field _____times large as that produced by a single turn</p>",
                    question_hi: "<p>87. \'n\' फेरों वाली एक वृत्ताकार कुण्डली एक फेरे द्वारा उत्पन्न क्षेत्र की तुलना में_____गुना बड़ा क्षेत्र उत्पन्न करती है।</p>",
                    options_en: ["<p>2n</p>", "<p>3n</p>", 
                                "<p>4n</p>", "<p>n</p>"],
                    options_hi: ["<p>2n</p>", "<p>3n</p>",
                                "<p>4n</p>", "<p>n</p>"],
                    solution_en: "<p>87.(d)<strong> n. </strong>The magnetic field produced by a circular coil depends on the number of turns, the current passing through the coil, and other factors like the radius of the coil and the medium in which the coil is placed. For a given current and coil geometry, the strength of the magnetic field at the center of a coil is proportional to the number of turns in the coil.</p>",
                    solution_hi: "<p>87.(d) <strong>n. </strong>एक वृत्ताकार कुंडली द्वारा उत्पादित चुंबकीय क्षेत्र, फेरो की संख्या, कुंडली से गुजरने वाली धारा और कुंडली की त्रिज्या और जिस माध्यम में कुंडली रखी जाती है जैसे अन्य कारकों पर निर्भर करता है। किसी दी गई धारा और कुंडली ज्यामिति के लिए, कुंडली के केंद्र में चुंबकीय क्षेत्र की क्षमता कुंडली में फेरों की संख्या के समानुपाती होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "18",
                    question_en: "<p>88. The Lushai Hills are also known as_______.</p>",
                    question_hi: "<p>88. लुशाई पहाड़ियों (Lushai Hills) को _____के नाम से भी जाना जाता है।</p>",
                    options_en: ["<p>Khasi Hills</p>", "<p>Mizo Hills</p>", 
                                "<p>Naga Hills</p>", "<p>Patkai Hills</p>"],
                    options_hi: ["<p>खासी पहाड़ियों (Khasi Hills)</p>", "<p>मिज़ो पहाड़ियों (Mizo Hills)</p>",
                                "<p>नागा पहाड़ियों (Naga Hills)</p>", "<p>पटकाई पहाड़ियों (Patkai Hills)</p>"],
                    solution_en: "<p>88.(b) <strong>Mizo Hills</strong> (Mizoram and Manipur). The highest point of Mizo hills is the Blue Mountain. Eastern Hills (The Purvanchal) are the southward extension of Himalayas running along the north-eastern edge of India. At the Dihang gorge, the Himalayas take a sudden southward bend and form a series of comparatively low hills which are collectively called as the Purvanchal. These are known by various local names such as Patkai Bum, Naga hills, Kohima hills, Manipur hills, Mizo hills, Tripura hills and Barail range.</p>",
                    solution_hi: "<p>88.(b) <strong>मिज़ो पहाड़ियों </strong>(मिजोरम और मणिपुर)। मिज़ो पहाड़ियों का सबसे ऊँचा स्थान ब्लू माउंटेन है। पूर्वी पहाड़ियाँ (पूर्वांचल) भारत के उत्तर-पूर्वी किनारे से लेकर हिमालय की दक्षिण दिशा तक इन पहाड़ियों का विस्तार हैं। दिहांग घाटी में, हिमालय दक्षिण की ओर मुड़ जाता है और कम ऊँचाई वाली पहाड़ियों की एक श्रृंखला बनाता है, जिन्हें सामूहिक रूप से पूर्वांचल कहा जाता है। इन्हें विभिन्न स्थानीय नामों से जाना जाता है जैसे पटकाई बम, नागा पहाड़ियाँ, कोहिमा पहाड़ियाँ, मणिपुर पहाड़ियाँ, मिज़ो पहाड़ियाँ, त्रिपुरा पहाड़ियाँ और बरेल पर्वतमाला आदि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "18",
                    question_en: "<p>89. Which of the following is NOT one of the consequences of the Green Revolution ?</p>",
                    question_hi: "<p>89. निम्नलिखित में से कौन-सा हरित क्रांति के परिणामों में से एक नहीं है ?</p>",
                    options_en: ["<p>Employment of agricultural workers increased</p>", "<p>Rising prices and a shift in the mode of payment of agricultural workers from payment in&nbsp;cash to payment in kind (grain)</p>", 
                                "<p>Wages of agricultural workers increased</p>", "<p>The rich grew richer and many of the poor stagnated or grew poorer</p>"],
                    options_hi: ["<p>मज़दूरों के रोज़गार में बढ़ोतरी हुई</p>", "<p>बढ़ती कीमतें और कृषि श्रमिकों के भुगतान के तरीके में बदलाव, नकद भुगतान से लेकर वस्तु (अनाज) के रूप में भुगतान की ओर बदलाव।</p>",
                                "<p>मज़दूरों की दिहाड़ी में बढ़ोतरी हुई</p>", "<p>धनी अधिक धनी हो गए और कई निर्धन पूर्ववत रहे या अधिक गरीब हो गए</p>"],
                    solution_en: "<p>89.(b) <strong>Green Revolution</strong> was initiated by Norman Borlaug in 1960s. He is known as the &lsquo;Father of Green Revolution&rsquo; in the world. M.S. Swaminathan - Father of Green Revolution in India. Impact of Green Revolution : Positive - Increase in production and productivity of food grains, reduction in hunger, and technological advancements in agriculture. Negative - Decline in Soil fertility, High production costs, Regional disparities।</p>",
                    solution_hi: "<p>89.(b)<strong> हरित क्रांति </strong>की शुरुआत 1960 के दशक में नॉर्मन बोरलॉग ने की थी। उन्हें दुनिया में \'हरित क्रांति के जनक\' के रूप में जाना जाता है। एम.एस. स्वामीनाथन को भारत में हरित क्रांति के जनक के रूप में जाना जाता है। हरित क्रांति का प्रभाव: सकारात्मक - खाद्यान्नों के उत्पादन और उत्पादकता में वृद्धि, भुखमरी में कमी और कृषि में तकनीकी उन्नति। नकारात्मक - मिट्टी की उर्वरता में कमी, उच्च उत्पादन लागत, क्षेत्रीय असमानताएँ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "18",
                    question_en: "<p>90. Which of the following statements is INCORRECT regarding the Coriolis force ?</p>",
                    question_hi: "<p>90. कोरिऑलिस बल (Coriolis force) के संबंध में निम्नलिखित में से कौन-सा कथन गलत है ?</p>",
                    options_en: ["<p>It deflects the wind to the right in the north and left in the south.</p>", "<p>It is directly proportional to the angle of altitude</p>", 
                                "<p>Deflection is less when the wind is high</p>", "<p>It is absent at the equator.</p>"],
                    options_hi: ["<p>यह पवन को उत्तर में दायीं ओर और दक्षिण में बायीं ओर विक्षेपित करता है।</p>", "<p>यह तुंगता कोण के समानुपाती होता है</p>",
                                "<p>पवन तेज होने पर विक्षेपण कम होता है।</p>", "<p>यह भूमध्य रेखा पर अनुपस्थित होता है।</p>"],
                    solution_en: "<p>90.(c) <strong>Deflection is less when the wind is high.</strong> Coriolis force : An apparent force caused by the earth&rsquo;s rotation. The Coriolis force acts perpendicular to the pressure gradient force (pressure gradient force is perpendicular to an isobar). It is maximum at the poles and is absent at the equator. The Coriolis force also affects ocean currents.</p>",
                    solution_hi: "<p>90.(c) <strong>पवन तेज होने पर विक्षेपण कम होता है।</strong> कोरिओलिस बल, पृथ्वी के घूर्णन के कारण लगने वाला एक काल्पनिक बल है। कोरिओलिस बल दाब प्रवणता बल (दाब प्रवणता बल एक समदाब रेखा के लंबवत होता है) के लंबवत कार्य करता है । यह ध्रुवों पर अधिकतम होता है तथा भूमध्य रेखा पर अनुपस्थित होता है। कोरिओलिस बल समुद्री धाराओं को भी प्रभावित करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "18",
                    question_en: "<p>91. Match the following renewable sources of energy with their producing regions in India correctly. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379280448.png\" alt=\"rId70\" width=\"422\" height=\"90\"></p>",
                    question_hi: "<p>91. ऊर्जा के निम्नलिखित नवीकरणीय स्रोतों का भारत में उनके उत्पादक क्षेत्रों के साथ सही मिलान कीजिये।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733379280538.png\" alt=\"rId71\" width=\"377\" height=\"102\"></p>",
                    options_en: ["<p>1-c, 2-b, 3-a</p>", "<p>1-a, 2-b, 3-c</p>", 
                                "<p>1-b, 2-a, 3-c</p>", "<p>1-b, 2-c, 3-a</p>"],
                    options_hi: ["<p>1-c, 2-b, 3-a</p>", "<p>1-a, 2-b, 3-c</p>",
                                "<p>1-b, 2-a, 3-c</p>", "<p>1-b, 2-c, 3-a</p>"],
                    solution_en: "<p>91.(b)<strong> 1-a, 2-b, 3-c. </strong>Renewable energy comes from natural sources that are regenerated more quickly than they are used up. They are natural and self-replenishing, and usually have a low- or zero-carbon footprint. Examples of renewable energy sources include wind power, solar power, bioenergy (organic matter burned as a fuel) and hydroelectric.</p>",
                    solution_hi: "<p>91.(b) <strong>1-a, 2-b, 3-c .</strong> नवीकरणीय ऊर्जा प्राकृतिक स्रोतों से प्राप्त ऊर्जा है, जो खपत की तुलना में अधिक तेजी से पुनर्निर्मित हो जाती है। वे प्राकृतिक और स्व-पुनःपूर्ति करने वाली होती हैं, और आमतौर पर उनका कार्बन फुटप्रिंट कम या शून्य होता है। नवीकरणीय ऊर्जा स्रोतों के उदाहरणों में पवन ऊर्जा, सौर ऊर्जा, जैव ऊर्जा (ईंधन के रूप में जलाए जाने वाले कार्बनिक पदार्थ) और जलविद्युत ऊर्जा शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "18",
                    question_en: "<p>92. To enhance the interoperability between the Indian and Russian Armies, a joint training exercise called ______ commenced at Prudboy Ranges, Volgograd in the year 2021.</p>",
                    question_hi: "<p>92. भारतीय और रूसी सेनाओं के बीच आपस में सहयोग करके कार्रवाई करने की क्षमता में इजाफा करने के लिए, वर्ष 2021 में प्रूडबॉय रेंज, वोल्गोग्राद (Prudboy Ranges, Volgograd) में _______नामक एक संयुक्त सैन्याभ्यास शुरू किया गया।</p>",
                    options_en: ["<p>INDRA 2021</p>", "<p>SURYA KIRAN 2021</p>", 
                                "<p>VARUNA 2021</p>", "<p>SHAKTI 2021</p>"],
                    options_hi: ["<p>इंद्र 2021</p>", "<p>सूर्य किरण 2021</p>",
                                "<p>वरुण 2021</p>", "<p>शक्ति 2021</p>"],
                    solution_en: "<p>92.(a) <strong>INDRA 2021.</strong> 12th Edition of Indo-Russia Joint Military Exercise INDRA 2021 : Objective - Conduct counter-terror operations under UN mandate. Participation - 250 personnel from India and Russia. Indian Army Contingent - Mechanised Infantry Battalion. The 13th edition of the Indra naval exercise was hosted by India in the Bay of Bengal in November 2023.</p>",
                    solution_hi: "<p>92.(a) <strong>इंद्र 2021. </strong>भारत-रूस संयुक्त सैन्य अभ्यास इंद्र 2021 का 12वां संस्करण: उद्देश्य - संयुक्त राष्ट्र के आदेश के तहत आतंकवाद विरोधी अभियान चलाना। भागीदारी - भारत और रूस के 250 सैन्यकर्मी । भारतीय सेना दल - मैकेनाइज्ड इन्फैंट्री बटालियन। इंद्र नौसैनिक अभ्यास के 13वें संस्करण की मेजबानी भारत ने नवंबर 2023 में बंगाल की खाड़ी में की थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "18",
                    question_en: "<p>93. The famous folk music &lsquo;Hekialeu&rsquo; has its origin in which state ?</p>",
                    question_hi: "<p>93. प्रसिद्ध लोक संगीत \'हेकियालु (Hekialeu)\' का उद्गम किस राज्य में हुआ है ?</p>",
                    options_en: ["<p>Assam</p>", "<p>Goa</p>", 
                                "<p>Nagaland</p>", "<p>Tami Nadu</p>"],
                    options_hi: ["<p>असम</p>", "<p>गोवा</p>",
                                "<p>नागालैंड</p>", "<p>तमिलनाडु</p>"],
                    solution_en: "<p>93.(c)<strong> Nagaland. </strong>The Hekialeu song is known as the war song of Nagaland. This is because the aged people of Nagaland narrate their achievements in past battles through this form of music. Folk music in India : Baul (West Bengal), Mando (Goa), Sohar (Uttar Pradesh and Bihar), Chhakri (Jammu and Kashmir), and Powada (Maharashtra).</p>",
                    solution_hi: "<p>93.(c) <strong>नागालैंड। </strong>\'हेकियालु गीत को नागालैंड के युद्ध गीत के रूप में जाना जाता है। ऐसा इसलिए है क्योंकि नागालैंड के बुजुर्ग लोग इस संगीत के माध्यम से पिछले युद्धों में अपनी उपलब्धियों का वर्णन करते हैं। भारत के प्रसिद्ध लोक गीत : बाउल (पश्चिम बंगाल), मांडो (गोवा), सोहर (उत्तर प्रदेश और बिहार), छकरी (जम्मू और कश्मीर), और पोवाड़ा (महाराष्ट्र)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "18",
                    question_en: "<p>94. Gugga is the famous ritualistic dance of ______, which is performed in the procession taken out in memory of Gugga Pir.</p>",
                    question_hi: "<p>94. गुग्गा (Gugga) _______का एक प्रसिद्ध कर्मकांडीय नृत्य है, जो गुग्गा पीर की याद में निकाले गए जुलूस में किया जाता है।</p>",
                    options_en: ["<p>Uttar Pradesh</p>", "<p>Jammu &amp; Kashmir</p>", 
                                "<p>Haryana</p>", "<p>Puducherry</p>"],
                    options_hi: ["<p>उत्तर प्रदेश</p>", "<p>जम्मू और कश्मीर</p>",
                                "<p>हरियाणा</p>", "<p>पुदुचेरी</p>"],
                    solution_en: "<p>94.(c) <strong>Haryana</strong>. Gugga dance - It is a religious dance (in the month of Bhadon) performed by men. Dancers carry instruments such as Thali, Chimta, Deru. Folk dances of Haryana - Phalgun dance, Loor dance, Daph dance, Khoria dance, Saang dance, Dhamal dance, Jhumar dance.</p>",
                    solution_hi: "<p>94.(c) <strong>हरियाणा।</strong> गुग्गा नृत्य - यह पुरुषों द्वारा किया जाने वाला एक धार्मिक नृत्य (भादों के महीने में) है । नर्तक थाली, चिमटा, डेरू जैसे वाद्य यंत्र लेकर चलते हैं। हरियाणा के लोक नृत्य - फाल्गुन नृत्य, लूर नृत्य, डफ नृत्य, खोरिया नृत्य, सांग नृत्य, धमाल नृत्य, झूमर नृत्य।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "18",
                    question_en: "<p>95. At which place was the magnificent temple of Saiva constructed by the great Rashtrakuta King &lsquo;Krishna I&rsquo; ?</p>",
                    question_hi: "<p>95. महान राष्ट्रकूट राजा \'कृष्ण प्रथम\' द्वारा शैव (Saiva) के भव्य मंदिर का निर्माण किस स्थान पर करवाया गया था ?</p>",
                    options_en: ["<p>Badami</p>", "<p>Kanchi</p>", 
                                "<p>Kannauj</p>", "<p>Ellora</p>"],
                    options_hi: ["<p>बादामी</p>", "<p>कांची</p>",
                                "<p>कन्नौज</p>", "<p>एलोरा</p>"],
                    solution_en: "<p>95.(d) <strong>Ellora. </strong>The temples at Ellora Caves (Maharashtra) were built by the Rashtrakuta and Yadava dynasties. Rashtrakuta dynasty built the Hindu and Buddhist caves during their reign. The Kailasa Temple (dedicated to Lord Shiva) was built by Krishna I of the Rashtrakuta dynasty. The Kailasa Temple is the largest single-rock excavation in the world. Yadava dynasty built many of the Jain caves. The Ellora Caves are a UNESCO World Heritage Site. The caves include 12 Buddhist caves, 17 Hindu caves, and 5 Jain caves.</p>",
                    solution_hi: "<p>95.(d) <strong>एलोरा।</strong> एलोरा गुफाओं (महाराष्ट्र) के मंदिरों का निर्माण राष्ट्रकूट और यादव राजवंशों द्वारा किया गया था। राष्ट्रकूट राजवंश ने अपने शासनकाल के दौरान हिंदू और बौद्ध गुफाओं का निर्माण किया। कैलाश मंदिर (भगवान शिव को समर्पित) का निर्माण राष्ट्रकूट राजवंश के कृष्ण प्रथम ने करवाया था। कैलाश मंदिर दुनिया का सबसे बड़ा एकल-चट्टान उत्खनन है। यादव राजवंश ने कई जैन गुफाओं का निर्माण करवाया था। एलोरा गुफाएँ UNESCO की विश्व धरोहर स्थल हैं। गुफाओं में 12 बौद्ध गुफाएँ, 17 हिंदू गुफाएँ और 5 जैन गुफाएँ शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "18",
                    question_en: "<p>96. Khubakeshei is a kind of song accompanied by clapping, sung in the state of _____.</p>",
                    question_hi: "<p>96. _______राज्य का खुबकएशेई (Khubakeshei) एक ऐसा गीत है जो तालियां बजाकर (clapping) गाया जाता है।</p>",
                    options_en: ["<p>Bihar</p>", "<p>Karnataka</p>", 
                                "<p>Manipur</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>बिहार</p>", "<p>कर्नाटक</p>",
                                "<p>मणिपुर</p>", "<p>ओडिशा</p>"],
                    solution_en: "<p>96.(c) <strong>Manipur.</strong> Khubakeshei is a traditional folk song from Manipur, typically sung with clapping and accompanied by traditional instruments like the Pena (a stringed instrument) or the Khartal (a pair of cymbals). Popular folk songs of Manipur : Pena Khongba, Khulang Eshei, Lai Haraoba Eshei.</p>",
                    solution_hi: "<p>96.(c) <strong>मणिपुर।</strong> खुबकएशेई, मणिपुर का एक पारंपरिक लोकगीत है, जिसे आम तौर पर ताली बजाकर और पेना (एक तार वाद्ययंत्र) या खरताल ( एक जोड़ी झांझ) जैसे पारंपरिक वाद्ययंत्रों के साथ गाया जाता है। मणिपुर के लोकप्रिय लोकगीत: पेना खोंगबा, खुलंग एशेई, लाई हराओबा एशेई।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "18",
                    question_en: "<p>97. Which of the following Articles of the Constitution states that &lsquo;It shall be the duty of every citizen of India to promote harmony and the spirit of common brotherhood amongst all the people of India transcending religious, linguistic and regional or sectional diversities; to renounce practices derogatory to the dignity of women.&rsquo;?</p>",
                    question_hi: "<p>97. संविधान के निम्नलिखित में से किस अनुच्छेद में कहा गया है कि \'प्रत्येक भारतीय नागरिक का यह कर्तव्य होगा कि वह भारत के सभी लोगों के बीच धार्मिक, भाषाई और क्षेत्रीय या अनुभागीय विविधताओं के परे सद्भाव और समान भाईचारे की भावना को बढ़ावा दें; महिलाओं की गरिमा के लिए अपमानजनक व्यवहार न करें।\' ?</p>",
                    options_en: ["<p>51 A (b)</p>", "<p>51 A (c)</p>", 
                                "<p>51 A (e)</p>", "<p>51 A (d)</p>"],
                    options_hi: ["<p>51 A (b)</p>", "<p>51 A (c)</p>",
                                "<p>51 A (e)</p>", "<p>51 A (d)</p>"],
                    solution_en: "<p>97.(c) <strong>51 A (e).</strong> Fundamental duties - Part IVA, Article 51A : (a) to abide by the Constitution and respect its ideals and institutions, the National Flag and the National Anthem; (b) to cherish and follow the noble ideals which inspired our national struggle for freedom; (c) to uphold and protect the sovereignty, unity, and integrity of India; (d) to defend the country and render national service when called upon to do so.</p>",
                    solution_hi: "<p>97.(c) <strong>51 A (e). </strong>मूल कर्तव्य - भाग IVA, अनुच्छेद 51A: (a) संविधान का पालन करें और उसके आदर्शों, संस्थाओं, राष्ट्र ध्वज और राष्ट्रगान का आदर करें;<br>; (b) स्वतंत्रता के लिए हमारे राष्ट्रीय आंदोलन को प्रेरित करने वाले उच्च आदर्शों को हृदय में संजोए रखें और उनका पालन करें; (c) भारत की प्रभुता, एकता और अखंडता की रक्षा करें और उसे अक्षुण्ण रखें ; (d) देश की रक्षा करें और आह्वान किए जाने पर राष्ट्र की सेवा करें।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "18",
                    question_en: "<p>98. Maldives are located to the south of which island of India ?</p>",
                    question_hi: "<p>98. मालदीव भारत के किस द्वीप के दक्षिण में स्थित है ?</p>",
                    options_en: ["<p>Japan</p>", "<p>Lakshadweep</p>", 
                                "<p>Sri Lanka</p>", "<p>Andaman and Nicobar</p>"],
                    options_hi: ["<p>जापान</p>", "<p>लक्षद्वीप</p>",
                                "<p>श्रीलंका</p>", "<p>अण्डमान और निकोबार</p>"],
                    solution_en: "<p>98.(b)<strong> Lakshadweep. </strong>Maldives : Its capital is Male. The Maldives are a group of coral atolls that were formed from the crowns of an ancient volcanic mountain range. Eight Degree Channel separates Minicoy Island (India) from Maldives. Indonesia is situated on the south of Andaman and Nicobar Islands.</p>",
                    solution_hi: "<p>98.(b) <strong>लक्षद्वीप। </strong>मालदीव: इसकी राजधानी माले है। मालदीव कोरल प्रवाल द्वीप का एक समूह है जो एक प्राचीन ज्वालामुखी पर्वत श्रृंखला के शिखरों से बना है। आठ डिग्री चैनल, मिनिकॉय द्वीप (भारत) को मालदीव से अलग करता है। इंडोनेशिया, अंडमान और निकोबार द्वीप समूह के दक्षिण में स्थित है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "18",
                    question_en: "<p>99. Which of the following state is the best governed state in the large states category as per the Public Affairs Index, 2020, which was released by the Public Affairs Centre, a non-profit organisation ?</p>",
                    question_hi: "<p>99. पब्लिक अफेयर्स इंडेक्स, 2020 के अनुसार, जो एक गैर-लाभकारी संगठन, पब्लिक अफेयर्स सेंटर द्वारा जारी किया गया था, बड़े राज्यों की श्रेणी में निम्नलिखित में से कौन सा राज्य सबसे अच्छा शासित राज्य है ?</p>",
                    options_en: ["<p>Odisha</p>", "<p>Kerala</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Bihar</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>केरल</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>बिहार</p>"],
                    solution_en: "<p>99.(b) <strong>Kerala.</strong> The Public Affairs Index for 2020 ranked states and union territories based on governance performance, considering three pillars: equity, growth, and sustainability. Large States Category: Kerala topped the list, followed by Tamil Nadu, Andhra Pradesh, and Karnataka. Uttar Pradesh ranked the lowest in this category. Small States Category: Goa emerged as the best-governed state. Union Territories - Chandigarh was ranked the highest.</p>",
                    solution_hi: "<p>99.(b)<strong> केरल। </strong>2020 के पब्लिक अफेयर्स इंडेक्स ने तीन स्तंभों समानता, विकास और स्थिरता को ध्यान में रखते हुए शासन प्रदर्शन के आधार पर राज्यों और केंद्र शासित प्रदेशों को स्थान दिया। बड़े राज्यों की श्रेणी: केरल सूची में शीर्ष पर है, उसके बाद तमिलनाडु, आंध्र प्रदेश और कर्नाटक हैं। इस श्रेणी में उत्तर प्रदेश सबसे निचले स्थान पर है। छोटे राज्यों की श्रेणी: गोवा सबसे बेहतर शासित राज्य के रूप में उभरा। केंद्र शासित प्रदेश - चंडीगढ़ को सर्वोच्च स्थान दिया गया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. What takes place when you click the Save button or press Ctrl + S in Microsoft Word ?</p>",
                    question_hi: "<p>100. जब आप माइक्रोसॉफ्ट वर्ड (Microsoft Word) में सेव बटन (Save button) पर क्लिक करते हैं या&nbsp;Ctrl + S दबाते हैं तो क्या होता है ?</p>",
                    options_en: ["<p>The document is printed.</p>", "<p>The document is copied to the clipboard.</p>", 
                                "<p>The document is closed.</p>", "<p>The document is saved in a disk file.</p>"],
                    options_hi: ["<p>दस्तावेज़ प्रिंट हो जाता है।</p>", "<p>दस्तावेज़ क्लिपबोर्ड पर कॉपी हो जाता है।</p>",
                                "<p>दस्तावेज़ बंद हो जाता है।</p>", "<p>दस्तावेज़ एक डिस्क फ़ाइल में सेव हो जाता है।</p>"],
                    solution_en: "<p>100.(d) <strong>The document is saved in a disk file. </strong>Shortcut keys in MS world: Ctrl + C - Copy, Ctrl + X - Cut, Ctrl + V - Paste, Ctrl + Z - Undo, Ctrl + A - Select all, Ctrl + B - Bold, Ctrl + I - Italic, Ctrl + U - Underline, Ctrl + Shift + &gt; - Increase font size, Ctrl + Shift + &lt; - Decrease font size.</p>",
                    solution_hi: "<p>100.(d)<strong> दस्तावेज़ एक डिस्क फ़ाइल में सेव हो जाता है। </strong>MS वर्ल्ड में शॉर्टकट की (keys) : Ctrl + C - कॉपी, Ctrl + X - कट, Ctrl + V - पेस्ट, Ctrl + Z - अन ड़ू (Undo), Ctrl + A - सेलेक्ट ऑल , Ctrl + B - बोल्ड, Ctrl + I - इटैलिक, Ctrl + U - अंडरलाइन, Ctrl + Shift + &gt; - फ़ॉन्टसाइज़ बढ़ाना, Ctrl + Shift + &lt; - फ़ॉन्ट साइज़ घटाना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>