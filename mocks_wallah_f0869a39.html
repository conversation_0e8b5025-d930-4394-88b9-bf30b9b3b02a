<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Six letters S, T, M, P, R and C are written on different faces of a dice. Two positions of this dice are shown in the figures below. Find the letter on the face opposite to M.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378129.png\" alt=\"rId4\" width=\"200\"></p>",
                    question_hi: "<p>1. एक पासे के विभिन्न फलकों पर छह अक्षर S, T, M, P, R और C लिखे गए हैं। इस पासे की दो स्थितियाँ नीचे चित्र में दर्शाई गई हैं। M के विपरीत वाले फलक पर आने वाला अक्षर ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378129.png\" alt=\"rId4\" width=\"200\"></p>",
                    options_en: ["<p>R</p>", "<p>P</p>", 
                                "<p>C</p>", "<p>S</p>"],
                    options_hi: ["<p>R</p>", "<p>P</p>",
                                "<p>C</p>", "<p>S</p>"],
                    solution_en: "<p>1.(a) From two dice the opposite face are <br>M &harr; R , P &harr; S</p>",
                    solution_hi: "<p>1.(a) दो पासों से विपरीत फलक हैं<br>M &harr; R , P &harr; S</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Identify the figure given in the options which when put in place of the question mark (?) will logically complete the series?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378307.png\" alt=\"rId5\" width=\"350\" height=\"85\"></p>",
                    question_hi: "<p>2. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378307.png\" alt=\"rId5\" width=\"350\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378396.png\" alt=\"rId6\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378523.png\" alt=\"rId7\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378645.png\" alt=\"rId8\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378748.png\" alt=\"rId9\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378396.png\" alt=\"rId6\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378523.png\" alt=\"rId7\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378645.png\" alt=\"rId8\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378748.png\" alt=\"rId9\" width=\"90\"></p>"],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378396.png\" alt=\"rId6\" width=\"90\"></p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378396.png\" alt=\"rId6\" width=\"90\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. &lsquo;Blossom&rsquo; is related to &lsquo;Wither&rsquo; in the same way as &lsquo;Stagnate&rsquo; is related to &lsquo;________&rsquo;.<br>(The words must be considered as meaningful English words and must not be related to each&nbsp;other based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>3. \'खिलना\' (Blossom),\'मुरझाना\' (Wither) से उसी प्रकार संबंधित है, जिस प्रकार \'स्थिर होना\' (Stagnate) \'________\' से संबंधित है।<br>(शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्&zwj;दों को अक्षरों की संख्या/व्यंजनों/नोंस्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं&nbsp;किया जाना चाहिए।)</p>",
                    options_en: ["<p>Stand</p>", "<p>Rest</p>", 
                                "<p>Languish</p>", "<p>Flow</p>"],
                    options_hi: ["<p>खड़े रहना (Stand)</p>", "<p>विराम (Rest)</p>",
                                "<p>मंद पड़ना (Languish)</p>", "<p>बहना (Flow)</p>"],
                    solution_en: "<p>3.(d) As &lsquo;Blossom&rsquo; and &lsquo;Wither&rsquo; are opposite of each other similarly, &lsquo;Stagnate&rsquo; and &lsquo;Flow&rsquo; are opposite of each other.</p>",
                    solution_hi: "<p>3.(d) जैसे \'खिलना\' और \'मुरझाना\' एक दूसरे के विपरीत हैं, उसी प्रकार \'स्थिर\' और \'बहना\' एक दूसरे के विपरीत हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Which two signs should be interchanged to make the following equation correct?<br>18 &minus; 23 &times; 455 &divide; 7 + 28 = 377</p>",
                    question_hi: "<p>4. निम्नलिखित समीकरण को सही करने के लिए किन दो चिह्नों को आपस में बदलना चाहिए?<br>18 - 23 &times; 455 &divide; 7 + 28 = 377</p>",
                    options_en: ["<p>+ and &times;</p>", "<p>&minus; and &times;</p>", 
                                "<p>&minus; and +</p>", "<p>&divide; and &minus;</p>"],
                    options_hi: ["<p>+ और &times;</p>", "<p>&minus; और &times;</p>",
                                "<p>&minus; और +</p>", "<p>&divide; और &minus;</p>"],
                    solution_en: "<p>4.(b) Given:- 18 - 23 &times; 455 &divide;&nbsp;7 + 28 = 377<br>After checking all the options, option (b) satisfies. After interchanging &lsquo;-&rsquo; and &lsquo;&times;&rsquo; we get, <br>18 &times; 23 - 455 &divide;&nbsp;7 + 28<br>414 - 65 + 28 = 377<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>4.(b) दिया गया :- 18 - 23 &times; 455 &divide; 7 + 28 = 377<br>सभी विकल्पों की जांच करने पर विकल्प (b) संतुष्ट करता है। \'-\' और \'&times;\' को आपस में बदलने पर हमें प्राप्त होता है,<br>18 &times; 23 - 455 &divide;&nbsp;7 + 28<br>414 - 65 + 28 = 377<br>L.H.S. = R.H.S.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. What will come in place of the question mark (?) in the following equation if &lsquo;+&rsquo; and &lsquo;&divide;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&ndash;&rsquo; are interchanged ?<br>4 &minus; 12 + 3 &divide; 12 &times; 6 = ?</p>",
                    question_hi: "<p>5. यदि \'+\' और \'&divide;\' को आपस में बदल दिया जाए और \'&times;\' और \'&ndash;\' को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>4 &minus; 12 + 3 &divide; 12 &times; 6 = ?</p>",
                    options_en: ["<p>13</p>", "<p>46</p>", 
                                "<p>11</p>", "<p>22</p>"],
                    options_hi: ["<p>13</p>", "<p>46</p>",
                                "<p>11</p>", "<p>22</p>"],
                    solution_en: "<p>5.(d) <strong>Given:- </strong>4 - 12 + 3 &divide;&nbsp;12 &times; 6<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;&divide;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;-&rsquo; we get, <br>4 &times; 12 &divide;&nbsp;3 + 12 - 6<br>16 + 6 = 22</p>",
                    solution_hi: "<p>5.(d) <strong>दिया गया :-</strong> 4 - 12 + 3 &divide;&nbsp;12 &times; 6<br>दिए गए निर्देश के अनुसार \'+\' और \'&divide;\' तथा \'&times;\' और \'-\' को आपस में बदलने के बाद हमें प्राप्त होता है,<br>4 &times; 12 &divide;&nbsp;3 + 12 - 6<br>16 + 6 = 22</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. The position of how many letters will remain unchanged if each of the letters in the word &lsquo;PRANKED&rsquo; is arranged in the English alphabetical order?</p>",
                    question_hi: "<p>6. यदि शब्द \'PRANKED\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा?</p>",
                    options_en: ["<p>One</p>", "<p>Three</p>", 
                                "<p>None</p>", "<p>Two</p>"],
                    options_hi: ["<p>एक</p>", "<p>तीन</p>",
                                "<p>कोई नहीं</p>", "<p>दो</p>"],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378849.png\" alt=\"rId10\" width=\"200\" height=\"103\"><br>The position of all the letters are changed</p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378849.png\" alt=\"rId10\" width=\"200\" height=\"103\"><br>सभी अक्षरों का स्थान बदल गया है ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. 10 is related to 15 following certain logic. Following the same logic, 42 is related to 63. To which of the following numbers is 22 related, following the same logic?<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>7. किसी निश्चित तर्क के अनुसार 10 का संबंध 15 से है। उसी तर्क के अनुसार 42 का संबंध 63 से है। उसी तर्क के अनुसार 22 निम्नलिखित में से किस संख्या से संबंधित है?<br>नोट : संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>28</p>", "<p>30</p>", 
                                "<p>33</p>", "<p>31</p>"],
                    options_hi: ["<p>28</p>", "<p>30</p>",
                                "<p>33</p>", "<p>31</p>"],
                    solution_en: "<p>7.(c) <strong>Logic</strong>:- <math display=\"inline\"><mfrac><mrow><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>&#160;</mi><mi>n</mi><mi>o</mi><mo>.</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 3 = 2<sup>nd</sup>no.<br>(10 , 15) : <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5 &times; 3 = 15<br>(42 , 63) :- <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 21 &times; 3 = 63<br>Similarly,<br>(22 , 33) :- <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 11 &times; 3 = 33</p>",
                    solution_hi: "<p>7.(c) <strong>तर्क:- </strong><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>2</mn></mfrac></math> &times; 3 = दूसरी संख्या <br>(10 , 15) : <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5 &times; 3 = 15<br>(42 , 63) :- <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 21 &times; 3 = 63<br>इसी प्रकार,<br>(22 , 33) :- <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 11 &times; 3 = 33</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. How many squares are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378954.png\" alt=\"rId11\" width=\"150\" height=\"129\"></p>",
                    question_hi: "<p>8. दी गई आकृति में कितने वर्ग हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777378954.png\" alt=\"rId11\" width=\"150\"></p>",
                    options_en: ["<p>14</p>", "<p>12</p>", 
                                "<p>15</p>", "<p>13</p>"],
                    options_hi: ["<p>14</p>", "<p>12</p>",
                                "<p>15</p>", "<p>13</p>"],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379132.png\" alt=\"rId12\" width=\"200\"><br>There are 14 squares<br>ABDC, EFGH,LHDG, LIJK, MQSR, QNTS, STOU, RSUP, MNOP , XWVS, SVZA&rsquo; , D&rsquo;SA&rsquo;B&rsquo; , D&rsquo;RPE&rsquo; , LYZC&rsquo; .</p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379132.png\" alt=\"rId12\" width=\"200\"><br>14 वर्ग हैं<br>ABDC, EFGH,LHDG, LIJK, MQSR, QNTS, STOU, RSUP, MNOP , XWVS, SVZA&rsquo; , D&rsquo;SA&rsquo;B&rsquo; , D&rsquo;RPE&rsquo; , LYZC&rsquo; .</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br>Temerity : Shyness :: Pique : ?</p>",
                    question_hi: "<p>9. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है। (शब्दों को अर्थपूर्ण अंग्रेजी शब्दों के रूप में माना जाना चाहिए और शब्द आपस में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होना चाहिए)<br>उतावलापन : शर्मीला :: मनमुटाव : ?</p>",
                    options_en: ["<p>Resentment</p>", "<p>Anger</p>", 
                                "<p>Delight</p>", "<p>Offence</p>"],
                    options_hi: ["<p>नाराज़गी</p>", "<p>गुस्सा</p>",
                                "<p>आनंद</p>", "<p>अपराध</p>"],
                    solution_en: "<p>9.(c) As &lsquo;Temerity&rsquo; and &lsquo;Shyness&rsquo; are opposite of each other. Similarly &lsquo;Pique&rsquo; and &lsquo;Delight&rsquo; are opposite of each other.</p>",
                    solution_hi: "<p>9.(c) चूँकि \'उतावलापन\' और \'शर्मीला\' एक दूसरे के विपरीत हैं। इसी प्रकार \'मनमुटाव\' और \'आनंद\' एक दूसरे के विपरीत हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the option that represents the letters that when placed from left to right in the blanks below will complete the given letter series.<br>L M B A _ H O L M B A C _ O L M _ A C H O L _ B A C H O L _</p>",
                    question_hi: "<p>10.उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्&zwj;हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर दी गई अक्षर श्रृंखला पूरी हो जाएगी।<br>L M B A _ H O L M B A C _ O L M _ A C H O L _ B A C H O L _</p>",
                    options_en: ["<p>CHMBM</p>", "<p>CMHBM</p>", 
                                "<p>CHBMM</p>", "<p>CHMMB</p>"],
                    options_hi: ["<p>CHMBM</p>", "<p>CMHBM</p>",
                                "<p>CHBMM</p>", "<p>CHMMB</p>"],
                    solution_en: "<p>10.(c) L M B A <span style=\"text-decoration: underline;\"><strong>C</strong></span> H O / L M B A C <span style=\"text-decoration: underline;\"><strong>H</strong></span> O / L M <span style=\"text-decoration: underline;\"><strong>B</strong></span> A C H O / L <span style=\"text-decoration: underline;\"><strong>M</strong></span> B A C H O / L <span style=\"text-decoration: underline;\"><strong>M</strong></span></p>",
                    solution_hi: "<p>10.(c) L M B A <span style=\"text-decoration: underline;\"><strong>C</strong></span> H O / L M B A C <span style=\"text-decoration: underline;\"><strong>H</strong></span> O / L M <span style=\"text-decoration: underline;\"><strong>B</strong></span> A C H O / L <span style=\"text-decoration: underline;\"><strong>M</strong></span> B A C H O / L <span style=\"text-decoration: underline;\"><strong>M</strong></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the set in which the numbers are related in the same way as are the numbers of the following set.<br>(7, 9, 48) <br>(6, 8, 42)<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>11. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित समुच्चयों की संख्याएँ संबंधित हैं।<br>(7, 9, 48)<br>(6, 8, 42)<br>(नोट : पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(8, 10, 47)</p>", "<p>(9, 11, 56)</p>", 
                                "<p>(11, 13, 72)</p>", "<p>(10, 12, 54)</p>"],
                    options_hi: ["<p>(8, 10, 47)</p>", "<p>(9, 11, 56)</p>",
                                "<p>(11, 13, 72)</p>", "<p>(10, 12, 54)</p>"],
                    solution_en: "<p>11.(c)<strong> Logic :- </strong>(1st number + 2nd number) &times; 3 = 3rd number<br>(7 , 9, 48) :- (16) &times; 3 = 48<br>(6, 8, 42) :- (14) &times; 3 = 42<br>Similarly,<br>(11, 13, 72) :- (24) &times; 3 = 72</p>",
                    solution_hi: "<p>11.(c) <strong>तर्क </strong>:- (पहली संख्या + दूसरी संख्या) &times; 3 = तीसरी संख्या<br>(7 , 9, 48) :- (16) &times; 3 = 48<br>(6, 8, 42) :- (14) &times; 3 = 42<br>इसी प्रकार,<br>(11, 13, 72) :- (24) &times; 3 = 72</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Three of the following four options are alike in a certain way and thus form a group. Which is the option that does NOT belong to that group?<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed).</p>",
                    question_hi: "<p>12. निम्नलिखित चार विकल्पों में से तीन एक निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन सा विकल्प है जो उस समूह से संबंधित नहीं है?<br>नोट : संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>2 &ndash; 4 &ndash; 8</p>", "<p>2 &ndash; 2 &ndash; 4</p>", 
                                "<p>3 &ndash; 2 &ndash; 5</p>", "<p>1 &ndash; 3 &ndash; 3</p>"],
                    options_hi: ["<p>2 &ndash; 4 &ndash; 8</p>", "<p>2 &ndash; 2 &ndash; 4</p>",
                                "<p>3 &ndash; 2 &ndash; 5</p>", "<p>1 &ndash; 3 &ndash; 3</p>"],
                    solution_en: "<p>12.(c) <strong>Logic :-</strong> (1st number &times; 2nd number) = 3rd number <br>(2 - 4 - 8):- (2 &times; 4) = 8<br>(2 - 2 - 4) :- (2 &times; 2) = 4<br>(1 - 3 - 3) :- (1 &times; 3) = 3<br>But,<br>(3 - 2 - 5) :- (3 &times; 2) = 6 (Not 5)</p>",
                    solution_hi: "<p>12.(c) <strong>तर्क :- </strong>(पहली संख्या &times; दूसरी संख्या) = तीसरी संख्या<br>(2 - 4 - 8):- (2 &times; 4) = 8<br>(2 - 2 - 4) :- (2 &times; 2) = 4<br>(1 - 3 - 3) :- (1 &times; 3) = 3<br>लेकिन,<br>(3 - 2 - 5) :- (3 &times; 2) = 6 (5 नहीं)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>Some boxes are cartons.&nbsp;<br>All cartons are papers. <br>Some cartons are utensils.<br><strong>Conclusion (I) :</strong> No box is a utensil.<br><strong>Conclusion (II) : </strong>Some papers are utensils.</p>",
                    question_hi: "<p>13. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढिए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही यह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्णय लीजिए कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं।<br><strong>कथन :</strong><br>कुछ बक्से, कार्टन हैं।&nbsp;<br>सभी कार्टन, कागज हैं। <br>कुछ कार्टन, बर्तन हैं।<br><strong>निष्कर्ष (I) :</strong> कोई बक्सा, बर्तन नहीं है।<br><strong>निष्कर्ष (II) :</strong> कुछ कागज, बर्तन हैं।</p>",
                    options_en: ["<p>Both conclusions (I) and (II) follow</p>", "<p>Neither conclusion (I) nor (II) follows</p>", 
                                "<p>Only conclusion (II) follows</p>", "<p>Only conclusion (I) follows</p>"],
                    options_hi: ["<p>निष्कर्ष (I) और (II) दोनों अनुसरण करते हैं</p>", "<p>न तो निष्कर्ष (I) और न ही (II) अनुसरण करता है</p>",
                                "<p>केवल निष्कर्ष (II) अनुसरण करता है</p>", "<p>केवल निष्कर्ष (I) अनुसरण करता है</p>"],
                    solution_en: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379267.png\" alt=\"rId13\" width=\"300\"><br>Only conclusion II follows.</p>",
                    solution_hi: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379376.png\" alt=\"rId14\" width=\"300\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the set in which the numbers are related in the same way as are the numbers of the following set.<br>(79, 58, 62)<br>(54, 33, 37)<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding / deleting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>14. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित समुच्चयों की संख्याएँ संबंधित हैं।<br>(79, 58, 62)<br>(54, 33, 37)<br>(नोट : पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(59, 33, 43)</p>", "<p>(47, 36, 41)</p>", 
                                "<p>(66, 45, 49)</p>", "<p>(43, 25, 30)</p>"],
                    options_hi: ["<p>(59, 33, 43)</p>", "<p>(47, 36, 41)</p>",
                                "<p>(66, 45, 49)</p>", "<p>(43, 25, 30)</p>"],
                    solution_en: "<p>14.(c) <strong>Logic :-</strong> (1st number - 2nd number) = 21 , (3rd number - 2nd number) = 4<br>(79, 58, 62) :- (79 - 58) = 21 , (62 - 58) = 4<br>(54, 33, 37) :- (54 - 33) = 21 ,(37 - 33) = 4<br>Similarly,<br>(66, 45, 49) :- (66 - 45) = 21 , (49 - 45) = 4</p>",
                    solution_hi: "<p>14.(c) <strong>तर्क :- </strong>(पहली संख्या - दूसरी संख्या) = 21, (तीसरी संख्या - दूसरी संख्या) = 4<br>(79, 58, 62) :- (79 - 58) = 21 , (62 - 58) = 4<br>(54, 33, 37) :- (54 - 33) = 21 ,(37 - 33) = 4<br>इसी प्रकार,<br>(66, 45, 49) :- (66 - 45) = 21 , (49 - 45) = 4</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. In a certain code language, &lsquo;sit on chair&rsquo; is coded as \'sk mi cn\' and &lsquo;the chair broke&rsquo; is coded as &lsquo;gm fr mi&rsquo;. How is &lsquo;chair&rsquo; coded in the given language?</p>",
                    question_hi: "<p>15. एक निश्चित कूट भाषा में, &lsquo;sit on chair&rsquo; को \'sk mi cn\' के रूप में कूटबद्ध किया गया है और &lsquo;the chair broke&rsquo; को &lsquo;gm fr mi&rsquo; के रूप में कूटबद्ध किया गया है। दी गई भाषा में &lsquo;chair&rsquo; को किस प्रकार से कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>sk</p>", "<p>cn</p>", 
                                "<p>mi</p>", "<p>gm</p>"],
                    options_hi: ["<p>sk</p>", "<p>cn</p>",
                                "<p>mi</p>", "<p>gm</p>"],
                    solution_en: "<p>15.(c) sit on chair &rarr; sk mi cn&hellip;&hellip;(i)<br>the chair broke &rarr; gm fr mi&hellip;..(ii)<br>From (i) and (ii) &lsquo;chair&rsquo; and &lsquo;mi&rsquo; are common. The code of &lsquo;chair&rsquo; = &lsquo;mi&rsquo;</p>",
                    solution_hi: "<p>15.(c) sit on chair &rarr; sk mi cn&hellip;&hellip;(i)<br>the chair broke &rarr; gm fr mi&hellip;..(ii)<br>(i) और (ii) से \'chair\' और \'mi\' उभयनिष्ठ हैं। \'chair\' का कोड = \'mi\'</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379490.png\" alt=\"rId15\"></p>",
                    question_hi: "<p>16. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379490.png\" alt=\"rId15\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379628.png\" alt=\"rId16\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379732.png\" alt=\"rId17\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379841.png\" alt=\"rId18\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379942.png\" alt=\"rId19\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379628.png\" alt=\"rId16\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379732.png\" alt=\"rId17\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379841.png\" alt=\"rId18\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379942.png\" alt=\"rId19\"></p>"],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379732.png\" alt=\"rId17\"></p>",
                    solution_hi: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777379732.png\" alt=\"rId17\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Which figure should replace the question mark (?) if the following series were to be continued?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380046.png\" alt=\"rId20\" width=\"350\" height=\"82\"></p>",
                    question_hi: "<p>17. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380046.png\" alt=\"rId20\" width=\"350\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380166.png\" alt=\"rId21\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380255.png\" alt=\"rId22\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380395.png\" alt=\"rId23\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380521.png\" alt=\"rId24\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380166.png\" alt=\"rId21\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380255.png\" alt=\"rId22\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380395.png\" alt=\"rId23\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380521.png\" alt=\"rId24\" width=\"90\"></p>"],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380255.png\" alt=\"rId22\" width=\"90\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380255.png\" alt=\"rId22\" width=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In a certain code language, \'BELL\' is written as \'MKFA\' and \'BOND\' is written as \'EMPA\'. How will \'BLEW&rsquo; be written in that language?</p>",
                    question_hi: "<p>18. एक निश्चित कूट भाषा में, \'BELL\' को \'MKFA\' लिखा जाता है और &lsquo;BOND\' को \'EMPA\' लिखा जाता है। इसी कूट भाषा में \'BLEW&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>XMDA</p>", "<p>XMAD</p>", 
                                "<p>XDAM</p>", "<p>XDMA</p>"],
                    options_hi: ["<p>XMDA</p>", "<p>XMAD</p>",
                                "<p>XDAM</p>", "<p>XDMA</p>"],
                    solution_en: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380648.png\" alt=\"rId25\" width=\"125\" height=\"137\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380767.png\" alt=\"rId26\" width=\"125\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380883.png\" alt=\"rId27\" width=\"125\"></p>",
                    solution_hi: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380648.png\" alt=\"rId25\" width=\"125\" height=\"137\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380767.png\" alt=\"rId26\" width=\"125\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777380883.png\" alt=\"rId27\" width=\"125\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381012.png\" alt=\"rId28\" width=\"300\" height=\"80\"></p>",
                    question_hi: "<p>19. एक कागज को नीचे दिए गए चित्र के अनुसार मोड़ा और काटा जाता है। यह कागज खोलने पर कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381012.png\" alt=\"rId28\" width=\"300\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381127.png\" alt=\"rId29\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381223.png\" alt=\"rId30\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381317.png\" alt=\"rId31\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381403.png\" alt=\"rId32\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381127.png\" alt=\"rId29\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381223.png\" alt=\"rId30\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381317.png\" alt=\"rId31\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381403.png\" alt=\"rId32\" width=\"90\"></p>"],
                    solution_en: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381317.png\" alt=\"rId31\" width=\"90\"></p>",
                    solution_hi: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381317.png\" alt=\"rId31\" width=\"90\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different. The odd one out is not based on the number of consonants/vowels or their position in the letter cluster</p>",
                    question_hi: "<p>20. चार अक्षर समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक समान हैं। उस असमान विकल्प को चुनिए। अक्षर समूह में, असमान विकल्प व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: ["<p>LQV</p>", "<p>DIN</p>", 
                                "<p>PTX</p>", "<p>AFK</p>"],
                    options_hi: ["<p>LQV</p>", "<p>DIN</p>",
                                "<p>PTX</p>", "<p>AFK</p>"],
                    solution_en: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381495.png\" alt=\"rId33\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381596.png\" alt=\"rId34\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381725.png\" alt=\"rId35\" width=\"100\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381820.png\" alt=\"rId36\" width=\"100\"></p>",
                    solution_hi: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381495.png\" alt=\"rId33\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381596.png\" alt=\"rId34\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381725.png\" alt=\"rId35\" width=\"100\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381820.png\" alt=\"rId36\" width=\"100\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Which letter-cluster will replace the question mark (?) to complete the given series?<br>ZVRN, VSPM, RPNL, ?, JJJJ</p>",
                    question_hi: "<p>21. निम्नलिखित में से कौन सा अक्षर-समूह दी गई श्रृंखला को पूरा करने के लिए प्रश्न चिह्न (?) को प्रतिस्थापित करेगा?<br>ZVRN, VSPM, RPNL, ?, JJJJ</p>",
                    options_en: ["<p>RMLN</p>", "<p>RNLL</p>", 
                                "<p>RNLK</p>", "<p>NMLK</p>"],
                    options_hi: ["<p>RMLN</p>", "<p>RNLL</p>",
                                "<p>RNLK</p>", "<p>NMLK</p>"],
                    solution_en: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381921.png\" alt=\"rId37\" width=\"300\" height=\"87\"></p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777381921.png\" alt=\"rId37\" width=\"300\" height=\"87\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the number from among the given options that can replace the question mark (?) in the following series. <br>23, 27, 43, 79, 143, ?</p>",
                    question_hi: "<p>22. दिए गए विकल्पों में से उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आ सकती है।<br>23, 27, 43, 79, 143, ?</p>",
                    options_en: ["<p>240</p>", "<p>243</p>", 
                                "<p>250</p>", "<p>200</p>"],
                    options_hi: ["<p>240</p>", "<p>243</p>",
                                "<p>250</p>", "<p>200</p>"],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382053.png\" alt=\"rId38\" width=\"250\" height=\"128\"></p>",
                    solution_hi: "<p>22.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382053.png\" alt=\"rId38\" width=\"250\" height=\"128\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. In a certain code language,<br>A # B means A is the sister of B.<br>A @ B means A is the son of B.<br>A &amp; B means A is the wife of B.<br>A % B means A is the father of B.<br>How is Q related to R if P &amp; Q % L # R &amp; K ?</p>",
                    question_hi: "<p>23. एक निश्चित कूट भाषा में,<br>A # B का अर्थ है A, B की बहन है।<br>A @ B का अर्थ है A, B का पुत्र है।<br>A &amp; B का अर्थ है A, B की पत्नी है।।<br>A % B का अर्थ है A, B का पिता है।<br>यदि P &amp; Q % L # R &amp; K है, तो Q, R से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Sister</p>", "<p>Wife</p>", 
                                "<p>Father</p>", "<p>Mother</p>"],
                    options_hi: ["<p>बहन</p>", "<p>पत्नी</p>",
                                "<p>पिता</p>", "<p>माँ</p>"],
                    solution_en: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382146.png\" alt=\"rId39\" width=\"180\"><br>Q is the father of R.</p>",
                    solution_hi: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382146.png\" alt=\"rId39\" width=\"180\"><br>Q, R का पिता है.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the option that can replace the question mark (?) in the following series.<br>FM61, EK58, DI55, CG52, ?</p>",
                    question_hi: "<p>24. उस विकल्प का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आ सकता है।<br>FM61, EK58, DI55, CG52, ?</p>",
                    options_en: ["<p>BE49</p>", "<p>CE46</p>", 
                                "<p>CD44</p>", "<p>BF48</p>"],
                    options_hi: ["<p>BE49</p>", "<p>CE46</p>",
                                "<p>CD44</p>", "<p>BF48</p>"],
                    solution_en: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382278.png\" alt=\"rId40\" width=\"300\"></p>",
                    solution_hi: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382278.png\" alt=\"rId40\" width=\"300\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382410.png\" alt=\"rId41\" width=\"150\" height=\"139\"></p>",
                    question_hi: "<p>25. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382410.png\" alt=\"rId41\" width=\"150\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382527.png\" alt=\"rId42\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382623.png\" alt=\"rId43\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382747.png\" alt=\"rId44\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382903.png\" alt=\"rId45\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382527.png\" alt=\"rId42\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382623.png\" alt=\"rId43\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382747.png\" alt=\"rId44\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382903.png\" alt=\"rId45\"></p>"],
                    solution_en: "<p>25.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382903.png\" alt=\"rId45\"></p>",
                    solution_hi: "<p>25.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777382903.png\" alt=\"rId45\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. In which of the following cities of Manipur is the Jawaharlal Nehru Manipur Dance Academy located ?</p>",
                    question_hi: "<p>26. मणिपुर के निम्नलिखित में से किस शहर में जवाहरलाल नेहरू मणिपुर नृत्य अकादमी स्थित है?</p>",
                    options_en: ["<p>Imphal</p>", "<p>Ukhrul</p>", 
                                "<p>Chandel</p>", "<p>Thoubal</p>"],
                    options_hi: ["<p>इंफाल</p>", "<p>उखरूल</p>",
                                "<p>चंदेल</p>", "<p>थौबल</p>"],
                    solution_en: "<p>26.(a) <strong>Imphal</strong>. Jawaharlal Nehru Manipur Dance Academy, a unit of the Sangeet Natak Academy in New Delhi, is dedicated to teaching Manipuri dance and music. Established in 1954. Other renowned Dance Institutes : National Institute of Kathak Dance (New Delhi), Nalanda Nritya Kala Mahavidyalaya (Mumbai), Ballet Repertoire Academy of India (Mumbai), Sri Thyagaraja College of Music and Dance (Hyderabad), and Nrityanjali Institute of Performing Arts (Mumbai).</p>",
                    solution_hi: "<p>26.(a) <strong>इंफाल</strong>। जवाहरलाल नेहरू मणिपुर नृत्य अकादमी, नई दिल्ली में संगीत नाटक अकादमी की एक इकाई है, जो मणिपुरी नृत्य और संगीत सिखाने के लिए समर्पित है। जिसकी स्थापना 1954 में हुई थी। अन्य प्रसिद्ध नृत्य संस्थान: राष्ट्रीय कथक नृत्य संस्थान (नई दिल्ली), नालंदा नृत्य कला महाविद्यालय (मुंबई), बैले रिपर्टरी एकेडमी ऑफ इंडिया (मुंबई), श्री त्यागराज कॉलेज ऑफ म्यूजिक एंड डांस (हैदराबाद) और नृत्यांजलि इंस्टीट्यूट ऑफ परफॉर्मिंग आर्ट्स (मुंबई)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. By which of the following state governments is the Tansen Music Festival organised?</p>",
                    question_hi: "<p>27. निम्नलिखित में से किस राज्य सरकार द्वारा तानसेन संगीत समारोह आयोजित किया जाता है?</p>",
                    options_en: ["<p>Maharashtra Government</p>", "<p>Madhya Pradesh Government</p>", 
                                "<p>Rajasthan Government</p>", "<p>Uttar Pradesh Government</p>"],
                    options_hi: ["<p>महाराष्ट्र सरकार</p>", "<p>मध्य प्रदेश सरकार</p>",
                                "<p>राजस्थान सरकार</p>", "<p>उत्तर प्रदेश सरकार</p>"],
                    solution_en: "<p>27.(b)<strong> Madhya Pradesh Government.</strong> Tansen Samaroh, an annual event in Behat village, Gwalior, Madhya Pradesh, honors the legendary singer Tansen. States and their music festival : Madhya Pradesh - Malwa Utsav, Lokrang Festival, Pachmarhi Utsav, Akhil Bhartiya Kalidas Samaroh. Maharashtra - Banganga, Ellora Ajanta, Elephanta. Rajasthan - Udaipur World Music Festival, Mewar Festival, Jaisalmer Desert Festival. Uttar Pradesh - Taj Mahotsav, Lucknow Mahotsav, Vrindavan Sharadotsav, Sankat Mochan Sangeet Samaroh.</p>",
                    solution_hi: "<p>27.(b) <strong>मध्य प्रदेश सरकार</strong>। तानसेन समारोह, मध्य प्रदेश के ग्वालियर के बेहट गांव में एक वार्षिक कार्यक्रम है, जो महान गायक तानसेन का सम्मान करता है। राज्य एवं उनके संगीत समारोह: मध्य प्रदेश - मालवा उत्सव, लोकरंग महोत्सव, पचमढ़ी उत्सव, अखिल भारतीय कालिदास समारोह। महाराष्ट्र - बाणगंगा, एलोरा अजंता, एलीफेंटा। राजस्थान - उदयपुर विश्व संगीत महोत्सव, मेवाड़ महोत्सव, जैसलमेर रेगिस्तान महोत्सव। उत्तर प्रदेश - ताज महोत्सव, लखनऊ महोत्सव, वृन्दावन शरदोत्सव, संकट मोचन संगीत समारोह।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following places is related to Gandhi&rsquo;s Satyagraha of the year 1917?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन-सा स्थान वर्ष 1917 के गांधीजी के सत्याग्रह से संबंधित है?</p>",
                    options_en: ["<p>Kheda</p>", "<p>Ahmedabad</p>", 
                                "<p>Champaran</p>", "<p>Bardoli</p>"],
                    options_hi: ["<p>खेड़ा</p>", "<p>अहमदाबाद</p>",
                                "<p>चंपारण</p>", "<p>बारदोली</p>"],
                    solution_en: "<p>28.(c) <strong>Champaran</strong>. India\'s first Civil Disobedience movement, launched by Mahatma Gandhi in 1917, was prompted by Pandit Raj Kumar Shukla, who urged him to address the plight of Indigo farmers. Mahatma Gandhi organised the Kheda Satyagraha (1918) in Gujarat. Gandhi also led the Ahmedabad Mill Strike (1918) after returning from South Africa. The Bardoli Satyagraha (1928) was led by Sardar Vallabhbhai Patel for the farmers of Bardoli.</p>",
                    solution_hi: "<p>28.(c) <strong>चंपारण</strong>। भारत का प्रथम सविनय अवज्ञा आंदोलन, जिसे महात्मा गांधी ने 1917 में शुरू किया था, पंडित राज कुमार शुक्ला द्वारा प्रेरित था, जिन्होंने उनसे नील किसानों की दुर्दशा को संबोधित करने का आग्रह किया था। महात्मा गांधी ने गुजरात में खेड़ा सत्याग्रह (1918) का आयोजन किया था। गांधी ने दक्षिण अफ्रीका से लौटने के बाद अहमदाबाद मिल हड़ताल (1918) का भी नेतृत्व किया। बारदोली सत्याग्रह (1928) का नेतृत्व सरदार वल्लभभाई पटेल ने बारदोली के किसानों के लिए किया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Hubbardia heptaneuron, which has become endangered, is a species of which of the&nbsp;following?</p>",
                    question_hi: "<p>29. हब्बार्डिया हेप्टेन्यूरोन (Hubbardia heptaneuron), जो लुप्तप्राय हो गया है, निम्नलिखित में से किसकी प्रजाति है?</p>",
                    options_en: ["<p>Grass</p>", "<p>Bamboo</p>", 
                                "<p>Tiger</p>", "<p>Crane</p>"],
                    options_hi: ["<p>घास</p>", "<p>बाँस</p>",
                                "<p>बाघ</p>", "<p>सारस</p>"],
                    solution_en: "<p>29.(a) <strong>Grass</strong>. Hubbardia heptaneuron is nearing extinction due to its environmental insensitivity. Other endangered plant species include the Gran Canaria Drago Tree, Mandragora, Castela senticosa, Musli, and Assam Catkin. The IUCN\'s \"Red Data Books\" offer the most comprehensive global inventory of the conservation status of plant and animal species.</p>",
                    solution_hi: "<p>29.(a) <strong>घास</strong>। हब्बार्डिया हेप्टेन्यूरोन अपनी पर्यावरण असंवेदनशीलता के कारण विलुप्त होने के कगार पर है। अन्य लुप्तप्राय पौधों की प्रजातियों में ग्रैन कैनरिया ड्रैगो ट्री, मैनड्रैगोरा, कास्टेला सेंटिकोसा, मूसली और असम कैटकिन शामिल हैं। IUCN की \"रेड डेटा बुक्स\" पौधों तथा जानवरों की प्रजातियों की संरक्षण स्थिति की सबसे व्यापक वैश्विक सूची प्रदान करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which of the following is in geographical proximity to Sri Lanka?</p>",
                    question_hi: "<p>30. निम्नलिखित में से कौन-सा स्थान भौगोलिक रूप से श्रीलंका के समीप है?</p>",
                    options_en: ["<p>Only Karaikal</p>", "<p>Karaikal and Yanam</p>", 
                                "<p>Only Mahe</p>", "<p>Only Yanam</p>"],
                    options_hi: ["<p>केवल कराईकल</p>", "<p>कराईकल और यानम</p>",
                                "<p>केवल माहे</p>", "<p>केवल यानम</p>"],
                    solution_en: "<p>30.(a) <strong>Only Karaikal. </strong>Karaikal is a town in the Union Territory of Puducherry. Sri Lanka, formerly known as Ceylon, is an island nation in South Asia. Located in the Indian Ocean, southwest of the Bay of Bengal, it is separated from the Indian peninsula by the Gulf of Mannar and the Palk Strait. Sri Lanka shares a maritime border with the Maldives to the southwest and India to the northwest.</p>",
                    solution_hi: "<p>30.(a) <strong>केवल कराईकल</strong>। कराईकल पुडुचेरी के केंद्र शासित प्रदेश का एक शहर है। श्रीलंका, जिसे पहले सीलोन के नाम से जाना जाता था, दक्षिण एशिया में एक द्वीप राष्ट्र है। बंगाल की खाड़ी के दक्षिण-पश्चिम में हिंद महासागर में स्थित, यह मन्नार की खाड़ी और पाक जलडमरूमध्य द्वारा भारतीय प्रायद्वीप से अलग होता है। श्रीलंका दक्षिण-पश्चिम में मालदीव और उत्तर-पश्चिम में भारत के साथ समुद्री सीमा साझा करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which Five-Year Plan primarily focused on the \'Garibi Hatao\' initiative?</p>",
                    question_hi: "<p>31. कौन-सी पंचवर्षीय योजना मुख्य रूप से \'गरीबी हटाओ\' पहल पर केंद्रित थी?</p>",
                    options_en: ["<p>Sixth Five-Year Plan</p>", "<p>Fourth Five-Year Plan</p>", 
                                "<p>Fifth Five-Year Plan</p>", "<p>Third Five-Year Plan</p>"],
                    options_hi: ["<p>छठी पंचवर्षीय योजना</p>", "<p>चौथी पंचवर्षीय योजना</p>",
                                "<p>पाँचवीं पंचवर्षीय योजना</p>", "<p>तीसरी पंचवर्षीय योजना</p>"],
                    solution_en: "<p>31.(c) <strong>Fifth Five-Year Plan. </strong>\"Garibi Hatao\" was a slogan coined by Indira Gandhi, aimed at poverty alleviation and job creation for a better life. The 5th Five Year Plan (1974-1979) had a targeted growth rate of 4.4% and achieved 4.8%. It was terminated a year early, in March 1978. The plan\'s final draft, prepared by D.P. Dhar, focused on eliminating poverty and achieving self-reliance.</p>",
                    solution_hi: "<p>31.(c) <strong>पाँचवीं पंचवर्षीय योजना</strong>। \"गरीबी हटाओ\" इंदिरा गांधी द्वारा दिया गया नारा था, जिसका उद्देश्य गरीबी उन्मूलन और बेहतर जीवन के लिए रोजगार सृजन करना था। 5वीं पंचवर्षीय योजना (1974-1979) में 4.4% की विकास दर का लक्ष्य रखा गया था और 4.8% हासिल किया गया। इसे एक वर्ष पहले, मार्च 1978 में समाप्त कर दिया गया था। डी.पी. धर द्वारा तैयार योजना का अंतिम मसौदा गरीबी को खत्म करने और आत्मनिर्भरता हासिल करने पर केंद्रित था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following statements is INCORRECT about the Directive Principles of State Policy?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन-सा कथन राज्य के नीति निर्देशक सिद्धांतों के बारे में सही नहीं है?</p>",
                    options_en: ["<p>They promote the welfare of individuals. Hence, they are personal and individualistic.</p>", "<p>These have moral and political sanctions.</p>", 
                                "<p>They aim at establishing social and economic democracy in the country.</p>", "<p>They are positive, as they require the State to do certain things.</p>"],
                    options_hi: ["<p>ये लोगों के कल्याण को बढ़ावा देते हैं। इसलिए ये निजी और व्यक्तिपरक हैं।</p>", "<p>इन पर नैतिक और राजनीतिक प्रतिबंध हैं।</p>",
                                "<p>इनका उद्देश्य देश में सामाजिक और आर्थिक लोकतंत्र की स्थापना करना है।</p>", "<p>ये प्रकृति में सकारात्मक होते हैं, क्योंकि राज्यों को कुछ मामलों में इनकी आवश्यकता होती है।</p>"],
                    solution_en: "<p>32.(a) The Directive Principles of State Policy (Articles 36-51, Part IV) are guidelines in the Indian Constitution designed to establish a welfare state in India. They are borrowed from the Irish Constitution. DPSPs are vital for the country\'s social, economic, and political development though they are non-justiciable.</p>",
                    solution_hi: "<p>32.(a) राज्य के नीति निर्देशक सिद्धांत (अनुच्छेद 36-51, भाग IV) भारतीय संविधान में दिशा-निर्देश हैं, जिन्हें भारत में कल्याणकारी राज्य की स्थापना के लिए डिज़ाइन किया गया है। इन्हें आयरिश संविधान से अपनाया गया है। DPSP देश के सामाजिक, आर्थिक और राजनीतिक विकास के लिए महत्वपूर्ण हैं और गैर-न्यायसंगत हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. After the death of Humayun, the 13 year-old Akbar was coronated in 1556 at Kalanaur in&nbsp;________.</p>",
                    question_hi: "<p>33. हुमायूँ की मृत्यु के बाद, 13 वर्षीय अकबर का 1556 में ________ के कलानौर में राज्याभिषेक हुआ।</p>",
                    options_en: ["<p>Rajasthan</p>", "<p>Gujarat</p>", 
                                "<p>Bengal</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>गुजरात</p>",
                                "<p>बंगाल</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>33.(d) <strong>Punjab</strong>. Akbar was crowned at Kalanaur on February 14, 1556, following the sudden death of Humayun, who died after falling from the stairs of the Dinpanah library. At just 13 years old, Akbar came to power under the guidance of his guardian, Bairam Khan. He later defeated Hemu in the Second Battle of Panipat, securing control over Delhi.</p>",
                    solution_hi: "<p>33.(d) <strong>पंजाब</strong>। हुमायूँ की अचानक मृत्यु के बाद 14 फरवरी, 1556 को कलानौर में अकबर की राज्याभिषेक की गई, जिनकी मृत्यु दीनपनाह पुस्तकालय की सीढ़ियों से गिरकर हुई थी। महज 13 वर्ष की उम्र में अकबर अपने संरक्षक बैरम खान के मार्गदर्शन में सत्ता में आया था। बाद में उसने पानीपत की दूसरी लड़ाई में हेमू को हराया और दिल्ली पर नियंत्रण हासिल किया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. The climate of a place is NOT affected by which of the following?</p>",
                    question_hi: "<p>34. किसी स्थान की जलवायु निम्नलिखित में से किसके द्वारा प्रभावित नहीं होती है?</p>",
                    options_en: ["<p>Relief</p>", "<p>Type of soil</p>", 
                                "<p>Location</p>", "<p>Distance from the sea</p>"],
                    options_hi: ["<p>उच्चावच</p>", "<p>मिट्टी के प्रकार</p>",
                                "<p>स्थान</p>", "<p>समुद्र से दूरी</p>"],
                    solution_en: "<p>34.(b) <strong>Type of soil.</strong> Climate refers to the long-term conditions of a specific region. The changes in climate within a region are influenced by its atmospheric and environmental conditions. The six major factors that influence the climate of a location are latitude, altitude, pressure, and wind systems, distance from the sea (continentality), ocean currents, and relief features.</p>",
                    solution_hi: "<p>34.(b) <strong>मिट्टी के प्रकार</strong>। जलवायु किसी विशिष्ट क्षेत्र की दीर्घकालिक स्थितियों को संदर्भित करता है। किसी क्षेत्र के भीतर जलवायु में परिवर्तन उसके वायुमंडलीय और पर्यावरणीय स्थितियों से प्रभावित होते हैं। किसी स्थान की जलवायु को प्रभावित करने वाले छह प्रमुख साधन अक्षांश, ऊँचाई, दाब और पवन प्रणाली, समुद्र से दूरी (महाद्वीपीयता), महासागरीय धाराएँ और उच्चावच विशेषताएँ हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. In which musical note did Newland put the metals Co and Ni with halogens?</p>",
                    question_hi: "<p>35. न्यूलैंड ने धातु Co और Ni को हैलोजन के साथ किस म्यूजिकल नोट में रखा था?</p>",
                    options_en: ["<p>Fa</p>", "<p>Do</p>", 
                                "<p>Re</p>", "<p>Mi</p>"],
                    options_hi: ["<p>Fa</p>", "<p>Do</p>",
                                "<p>Re</p>", "<p>Mi</p>"],
                    solution_en: "<p>35.(b) <strong>Do.</strong> The Newland table was created by John Newlands in 1866 based on his Law of Octaves. He observed that every eighth element exhibited properties similar to the first, drawing an analogy to the octaves in music.</p>",
                    solution_hi: "<p>35.(b) <strong>Do. </strong>न्यूलैंड तालिका जॉन न्यूलैंड द्वारा 1866 में उनके अष्टक नियम के आधार पर बनाई गई थी। उन्होंने देखा कि प्रत्येक आठवाँ तत्व पहले तत्व के समान गुण प्रदर्शित करता है, जो संगीत में अष्टक के समान है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following is NOT correct with regards to the history of calculating National Income (NI) in India?</p>",
                    question_hi: "<p>36. भारत में राष्ट्रीय आय (NI)की गणना के इतिहास के संबंध में निम्नलिखित में से कौन-सा सही नहीं है?</p>",
                    options_en: ["<p>First attempt to compute NI was made by Dadabhai Naoroji.</p>", "<p>First official attempt to compute NI was made by PC Mahalanobis.</p>", 
                                "<p>Dadabhai Naoroji divided the Indian economy into two parts: primary sector and secondary sector.</p>", "<p>First scientific method to compute NI was used by Dr VKRV Rao.</p>"],
                    options_hi: ["<p>NI की गणना करने का पहला प्रयास दादाभाई नौरोजी ने किया था।</p>", "<p>NI की गणना करने का पहला आधिकारिक प्रयास पीसी महालनोबिस द्वारा किया गया था।</p>",
                                "<p>दादाभाई नौरोजी ने भारतीय अर्थव्यवस्था को दो भागों में विभाजित किया: प्राथमिक क्षेत्र और द्वितीयक क्षेत्र।</p>", "<p>NI की गणना करने के लिए पहली वैज्ञानिक पद्धति का उपयोग डॉ वीकेआरवी राव ने किया था।</p>"],
                    solution_en: "<p>36.(c) Dr. VKRV Rao in 1931, divided the Indian economy into two sectors: the agricultural sector and the corporate sector. The agricultural sector encompassed fishing, hunting, forests, and agriculture, while the corporate sector included industries, business, transport, construction, and public services. National income represents the total value of all goods and services produced in a country during a specific period. In 1867, Dadabhai Naoroji, known as the Grand Old Man of India, proposed the drain theory, also referred to as the \"drain of wealth\" theory.</p>",
                    solution_hi: "<p>36.(c) डॉ. वी.के.आर.वी. राव ने 1931 में भारतीय अर्थव्यवस्था को दो क्षेत्रों में विभाजित किया: कृषि क्षेत्र और कॉर्पोरेट क्षेत्र। कृषि क्षेत्र में मछली पकड़ना, शिकार करना, जंगल और कृषि शामिल थे, जबकि कॉर्पोरेट क्षेत्र में उद्योग, व्यवसाय, परिवहन, निर्माण और सार्वजनिक सेवाएँ शामिल थीं। राष्ट्रीय आय एक विशिष्ट अवधि के दौरान किसी देश में उत्पादित सभी वस्तुओं और सेवाओं के कुल मूल्य का प्रतिनिधित्व करती है। 1867 में, दादाभाई नौरोजी, जिन्हें भारत के ग्रैंड ओल्ड मैन के रूप में जाना जाता है, ने ड्रेन थ्योरी का प्रस्ताव रखा, जिसे \"धन की निकासी\" सिद्धांत भी कहा जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. According to the Indian Railways (2019-2020), which state has the largest railway track in India?</p>",
                    question_hi: "<p>37. भारतीय रेलवे (2019-2020) के अनुसार, भारत में सबसे बड़ा रेलवे ट्रैक किस राज्य में है?</p>",
                    options_en: ["<p>Punjab</p>", "<p>Haryana</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Uttar Pradesh</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>हरियाणा</p>",
                                "<p>मध्य प्रदेश</p>", "<p>उत्तर प्रदेश</p>"],
                    solution_en: "<p>37.(d) <strong>Uttar Pradesh.</strong> The longest railway track in India is the Dibrugarh-Kanyakumari Vivek Express (approx 4200 km), running from Dibrugarh in Assam to Kanyakumari in Tamil Nadu. The shortest railway line is the 3-kilometer route between Nagpur and Ajni in Maharashtra. The five states with the largest route kilometers are Uttar Pradesh, Rajasthan, Maharashtra, Gujarat, and Andhra Pradesh.</p>",
                    solution_hi: "<p>37.(d) <strong>उत्तर प्रदेश</strong>। भारत में सबसे लंबा रेलवे ट्रैक डिब्रूगढ़-कन्याकुमारी विवेक एक्सप्रेस (लगभग 4200 किमी) है, जो असम के डिब्रूगढ़ से तमिलनाडु के कन्याकुमारी तक चलती है। सबसे छोटी रेलवे लाइन महाराष्ट्र के नागपुर और अजनी के बीच 3 किलोमीटर का मार्ग है। सबसे बड़े मार्ग किलोमीटर वाले पाँच राज्य उत्तर प्रदेश, राजस्थान, महाराष्ट्र, गुजरात और आंध्र प्रदेश हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which government honoured a distinguished percussionist named Pandit Anindo Chatterjee with the Banga Vibhushan Award in 2022 ?</p>",
                    question_hi: "<p>38. किस सरकार ने वर्ष 2022 में पंडित अनिंदो चटर्जी नामक प्रतिष्ठित ताल वादक को बंग विभूषण पुरस्कार से सम्मानित किया ?</p>",
                    options_en: ["<p>Assam</p>", "<p>Tripura</p>", 
                                "<p>West Bengal</p>", "<p>Jharkhand</p>"],
                    options_hi: ["<p>असम</p>", "<p>त्रिपुरा</p>",
                                "<p>पश्चिम बंगाल</p>", "<p>झारखंड</p>"],
                    solution_en: "<p>38.(c) <strong>West Bengal.</strong> Pandit Anindo Chatterjee is an Indian tabla player from the Farrukhabad Gharana school. The Banga Bibhushan Samman is a title established by the West Bengal government on July 25, 2011, to honor individuals for their contributions in various fields. The first recipient of this award was Amala Shankar.</p>",
                    solution_hi: "<p>38.(c) <strong>पश्चिम बंगाल</strong>। पंडित अनिंदो चटर्जी फर्रुखाबाद घराने के एक भारतीय तबला वादक हैं। बंग विभूषण सम्मान पश्चिम बंगाल सरकार द्वारा 25 जुलाई, 2011 को स्थापित एक उपाधि है, जिसका उद्देश्य विभिन्न क्षेत्रों में योगदान देने वाले व्यक्तियों को सम्मानित करना है। इस पुरस्कार की प्रथम प्राप्तकर्ता अमला शंकर थीं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which of the following festivals is celebrated as the birth anniversary of Guru Nanak Dev?</p>",
                    question_hi: "<p>39. निम्नलिखित में से कौन-सा त्योहार गुरु नानक देव की जयंती के रूप में मनाया जाता है?</p>",
                    options_en: ["<p>Hola Mohalla</p>", "<p>Baisakhi</p>", 
                                "<p>Gurpurab</p>", "<p>Chappar Mela</p>"],
                    options_hi: ["<p>होला मोहल्ला</p>", "<p>बैसाखी</p>",
                                "<p>गुरुपर्व (गुरपुरब)</p>", "<p>छप्पर मेला</p>"],
                    solution_en: "<p>39.(c) <strong>Gurpurab</strong>. Guru Nanak Jayanti, also known as Gurpurab, celebrates the birth anniversary of the first Sikh Guru, Guru Nanak Dev. This festival occurs on Kartik Poornima, the fifteenth lunar day of the month of Kartik, typically in November on the Gregorian calendar. Hola Mohalla, or Hola, is a three-day Sikh festival that usually takes place in March. Vaisakhi, celebrated on April 13 or sometimes April 14, marks the first day of the month of Vaisakh. Chhapar Mela, a popular Punjabi fair, occurs annually in September in the village of Chhapar in the Ludhiana district of Punjab.</p>",
                    solution_hi: "<p>39.(c) <strong>गुरुपर्व </strong>(<strong>गुरपुरब</strong>)। गुरु नानक जयंती, जिसे गुरुपर्व के नाम से भी जाना जाता है, पहले सिख गुरु, गुरु नानक देव की जयंती के रूप में मनाई जाती है। यह त्योहार कार्तिक पूर्णिमा को मनाया जाता है, जो कार्तिक महीने के पंद्रहवें चंद्र दिवस पर पड़ता है, जो सामान्यतः ग्रेगोरियन कैलेंडर के अनुसार नवंबर में होता है। होला मोहल्ला, या होला, तीन दिवसीय सिख त्योहार है जो सामान्यतः मार्च में मनाया जाता है। वैसाखी, जिसे 13 अप्रैल या कभी-कभी 14 अप्रैल को मनाया जाता है, वैसाख महीने के पहले दिन को चिह्नित करता है। छपार मेला, एक लोकप्रिय पंजाबी मेला, पंजाब के लुधियाना जिले के छपर गाँव में प्रत्येक वर्ष सितंबर में होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Who was appointed as India&rsquo;s 28th Controller General of Accounts (CGA) in March 2023?</p>",
                    question_hi: "<p>40. मार्च 2023 में भारत के 28वें लेखा महानियंत्रक (CGA) के रूप में किसे नियुक्त किया गया था?</p>",
                    options_en: ["<p>Arun Goel</p>", "<p>Praveen Sharma</p>", 
                                "<p>Vikram Devdutt</p>", "<p>SS Dubey</p>"],
                    options_hi: ["<p>अरुण गोयल</p>", "<p>प्रवीण शर्मा</p>",
                                "<p>विक्रम देवदत्त</p>", "<p>एस. एस. दुबे</p>"],
                    solution_en: "<p>40.(d) <strong>SS Dubey.</strong> The Controller General of Accounts (CGA) is the Principal Accounting Adviser to the Government of India, operating within the Department of Expenditure, Ministry of Finance. The CGA is responsible for establishing a sound management accounting system, preparing and submitting the accounts of the Central Government, and overseeing exchequer control and internal audits. Its mandate comes from Article 150 of the Constitution, with duties outlined in the Allocation of Business Rules of 1961.</p>",
                    solution_hi: "<p>40.(d) <strong>एस. एस. दुबे</strong>। लेखा महानियंत्रक (CGA) भारत सरकार का मुख्य लेखा सलाहकार है, जो वित्त मंत्रालय के व्यय विभाग के अंतर्गत कार्य करता है। CGA एक सुदृढ़ प्रबंधन लेखा प्रणाली स्थापित करने, केंद्र सरकार के खातों को तैयार करने और प्रस्तुत करने तथा राजकोष नियंत्रण और आंतरिक लेखापरीक्षा की देखरेख करने के लिए जिम्मेदार है। इसका अधिदेश संविधान के अनुच्छेद 150 से प्राप्त होता है, तथा इसके कर्तव्यों का उल्लेख 1961 के कार्य आवंटन नियमों में किया गया है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. The Parliament passed the Marine Aids to Navigation Bill, 2021 to repeal and replace which of the following Acts?</p>",
                    question_hi: "<p>41. निम्नलिखित में से किस अधिनियम को निरस्त करने और प्रतिस्&zwj;थापित करने के लिए संसद ने नौवहन सामुद्रिक सहायता विधेयक, 2021 पारित किया?</p>",
                    options_en: ["<p>The Dangerous Machines (Regulation) Act, 1983</p>", "<p>The Personal Injuries (Emergency Provisions) Act, 1962</p>", 
                                "<p>The Lighthouse Act, 1927</p>", "<p>The Dourine Act, 1910</p>"],
                    options_hi: ["<p>खतरनाक मशीन (विनियमन) अधिनियम, 1983</p>", "<p>वैयक्&zwj;तिक क्षति (आपात उपबंध) अधिनियम, 1962</p>",
                                "<p>प्रकाशस्तंभ अधिनियम, 1927</p>", "<p>डुरीन अधिनियम, 1910</p>"],
                    solution_en: "<p>41.(c) <strong>The Lighthouse Act, 1927</strong> : An Act to consolidate and amend the law relating to the provision, maintenance and control of lighthouses by the Government. The Dangerous Machines (Regulation) Act, 1983, regulates the production, supply, distribution, and use of dangerous machines in India. The Personal Injuries (Emergency Provisions) Act, 1962, provides for relief regarding certain personal injuries sustained during the emergency period. The Dourine Act of 1910 was enacted to prevent the spread of dourine (a contagious disease that affects horses and asses) in India.</p>",
                    solution_hi: "<p>41.(c) <strong>प्रकाशस्तंभ अधिनियम, 1927</strong> : सरकार द्वारा प्रकाशस्तंभ (लाइटहाउस) के प्रावधान, रखरखाव और नियंत्रण से संबंधित कानून को समेकित और संशोधित करने के लिए एक अधिनियम है। खतरनाक मशीन (विनियमन) अधिनियम, 1983, भारत में खतरनाक मशीनों के उत्पादन, आपूर्ति, वितरण और उपयोग को नियंत्रित करता है। वैयक्&zwj;तिक क्षति (आपात उपबंध) अधिनियम, 1962, आपातकालीन अवधि के दौरान लगी कुछ वैयक्&zwj;तिक क्षति के संबंध में राहत प्रदान करता है। भारत में डुरीन (एक संक्रामक रोग जो घोड़ों और गधों को प्रभावित करता है) के प्रसार को रोकने के लिए 1910 का डुरीन अधिनियम बनाया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following is a non-perishable food?</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन-सा अविकारीय भोजन है?</p>",
                    options_en: ["<p>Pulses</p>", "<p>Meat</p>", 
                                "<p>Milk</p>", "<p>Curds</p>"],
                    options_hi: ["<p>दाल</p>", "<p>मांस</p>",
                                "<p>दूध</p>", "<p>दही</p>"],
                    solution_en: "<p>42.(a) <strong>Pulses</strong>. Non-perishable foods are items that can be stored at room temperature for extended periods without spoiling. Examples include beans, coffee, honey, powdered milk, rice, and wheat. In contrast, frozen foods are not classified as non-perishable.</p>",
                    solution_hi: "<p>42.(a) <strong>दाल</strong>। अविकारीय खाद्य पदार्थ वे पदार्थ हैं जिन्हें बिना खराब हुए लंबे समय तक कमरे के तापमान पर रखा जा सकता है। उदाहरणों में बीन्स, कॉफी, शहद, पाउडर वाला दूध, चावल और गेहूं शामिल हैं। इसके विपरीत, फ्रोज़ेन खाद्य पदार्थ को अविकारीय के रूप में वर्गीकृत नहीं किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. How many awards were presented at the National Sports and Adventure Awards-2022 ceremony held at Rashtrapati Bhavan on 30 November 2022?</p>",
                    question_hi: "<p>43. 30 नवंबर 2022 को राष्ट्रपति भवन में आयोजित राष्ट्रीय खेल एवं साहसिक पुरस्कार-2022 समारोह में कितने पुरस्कार प्रदान किए गए?</p>",
                    options_en: ["<p>32</p>", "<p>44</p>", 
                                "<p>52</p>", "<p>28</p>"],
                    options_hi: ["<p>32</p>", "<p>44</p>",
                                "<p>52</p>", "<p>28</p>"],
                    solution_en: "<p>43.(b) <strong>44</strong>. The Ministry of Youth Affairs and Sports, Government of India, administers the National Sports Awards, which are presented by the President of India. These awards are given annually to recognize excellence in sports and adventure, including the Major Dhyan Chand Khel Ratna Award, Arjuna Award, Dronacharya Award, Tenzing Norgay National Adventure Award, Rashtriya Khel Protsahan Puruskar, and Maulana Abul Kalam Azad Trophy, Dhyan Chand Award.</p>",
                    solution_hi: "<p>43.(b) <strong>44</strong>. भारत सरकार का युवा मामले और खेल मंत्रालय राष्ट्रीय खेल पुरस्कारों का प्रबंधन करता है, जिन्हें भारत के राष्ट्रपति द्वारा प्रदान किया जाता है। ये पुरस्कार खेल और साहसिक कार्यों में उत्कृष्टता को मान्यता देने के लिए प्रतिवर्ष दिए जाते हैं, जिनमें मेजर ध्यानचंद खेल रत्न पुरस्कार, अर्जुन पुरस्कार, द्रोणाचार्य पुरस्कार, तेनजिंग नोर्गे राष्ट्रीय साहसिक पुरस्कार, राष्ट्रीय खेल प्रोत्साहन पुरस्कार और मौलाना अबुल कलाम आज़ाद ट्रॉफी, ध्यानचंद पुरस्कार शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which Governor-General of British India helped Raja Ram Mohan Roy legally abolish the&nbsp;Sati practice?</p>",
                    question_hi: "<p>44. ब्रिटिश भारत के किस गवर्नर-जनरल ने सती प्रथा को कानूनी रूप से समाप्त करने में राजा राम मोहन राय की मदद की?</p>",
                    options_en: ["<p>Lord Ripon</p>", "<p>Lord Curzon</p>", 
                                "<p>Lord Cornwallis</p>", "<p>Lord William Bentinck</p>"],
                    options_hi: ["<p>लॉर्ड रिपन (Lord Ripon)</p>", "<p>लॉर्ड कर्जन (Lord Curzon)</p>",
                                "<p>लार्ड कार्नवालिस (Lord Cornwallis)</p>", "<p>लॉर्ड विलियम बेंटिक (Lord William Bentinck)</p>"],
                    solution_en: "<p>44.(d) <strong>Lord William Bentinck. </strong>He was appointed Governor of Madras in 1803 and assumed the office of Governor-General in 1828. He passed the Bengal Sati Regulation on December 4, 1829, which banned the practice of Sati throughout British India. Raja Ram Mohan Roy was one of the founders of the Brahmo Sabha in 1828.</p>",
                    solution_hi: "<p>44.(d) <strong>लॉर्ड विलियम बेंटिक</strong> (Lord William Bentinck)। उन्हें 1803 में मद्रास का गवर्नर नियुक्त किया गया और 1828 में उन्होंने गवर्नर-जनरल का पद संभाला। उन्होंने 4 दिसंबर, 1829 को बंगाल सती विनियमन पारित किया, जिसने पूरे ब्रिटिश भारत में सती प्रथा पर प्रतिबंध लगा दिया। राजा राम मोहन राय 1828 में ब्रह्म सभा के संस्थापकों में से एक थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. When was the 1st Asian Kabaddi Championship held?</p>",
                    question_hi: "<p>45. पहली एशियाई कबड्डी चैम्पियनशिप कब आयोजित की गई थी?</p>",
                    options_en: ["<p>1970</p>", "<p>1985</p>", 
                                "<p>1980</p>", "<p>1975</p>"],
                    options_hi: ["<p>1970</p>", "<p>1985</p>",
                                "<p>1980</p>", "<p>1975</p>"],
                    solution_en: "<p>45.(c) <strong>1980</strong>. The 1st Asian Kabaddi Championship was held in 1980 and included as a demonstration game in the 9th Asian Games in New Delhi in 1982. The 9th Championship took place in the Republic of Korea, where India secured its 8th title. Kabaddi was also included in the South Asian Federation (SAF) Games started in 1984 in Dhaka, Bangladesh.</p>",
                    solution_hi: "<p>45.(c) <strong>1980</strong>. पहली एशियाई कबड्डी चैंपियनशिप 1980 में आयोजित की गई थी और इसे 1982 में नई दिल्ली में आयोजित 9वें एशियाई खेलों में एक प्रदर्शन खेल के रूप में शामिल किया गया था। 9वीं चैंपियनशिप कोरिया गणराज्य में हुई, जहाँ भारत ने अपना 8वाँ खिताब जीता। 1984 में बांग्लादेश के ढाका में शुरू हुए दक्षिण एशियाई महासंघ (SAF) खेलों में कबड्डी को भी शामिल किया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. The decomposition of gaseous Ammonia on a hot platinum surface is a________ order reaction at high pressure.</p>",
                    question_hi: "<p>46. एक गर्म प्लेटिनम सतह पर गैसीय अमोनिया का अपघटन उच्च दाब पर _______ क्रम की अभिक्रिया है।</p>",
                    options_en: ["<p>two</p>", "<p>zero</p>", 
                                "<p>three</p>", "<p>one</p>"],
                    options_hi: ["<p>दो</p>", "<p>शून्य</p>",
                                "<p>तीन</p>", "<p>एक</p>"],
                    solution_en: "<p>46.(b) <strong>zero</strong>. In this reaction, platinum serves as a catalyst. Under high pressure, the metal surface becomes saturated with gas molecules. Chemical decomposition, also known as chemical breakdown, is the process of breaking down a single chemical entity (such as a molecule or reaction intermediate) into two or more fragments. It is generally considered the opposite of chemical synthesis.</p>",
                    solution_hi: "<p>46.(b) <strong>शून्य</strong>। इस अभिक्रिया में प्लेटिनम उत्प्रेरक का कार्य करता है। उच्च दाब में, धातु की सतह गैस के अणुओं से संतृप्त हो जाती है। रासायनिक अपघटन, जिसे रासायनिक विखंडन के रूप में भी जाना जाता है, एक एकल रासायनिक इकाई (जैसे अणु या प्रतिक्रिया मध्यवर्ती) को दो या अधिक भागों में तोड़ने की प्रक्रिया है। इसे सामान्यतः रासायनिक संश्लेषण के विपरीत माना जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following was the mascot of the FIH men&rsquo;s Hockey World Cup &ndash; 2023?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन एफ. आई. एच. (FIH) पुरुष हॉकी विश्व कप 2023 का शुभंकर था?</p>",
                    options_en: ["<p>Jitu</p>", "<p>Bheema</p>", 
                                "<p>Olly</p>", "<p>Asha</p>"],
                    options_hi: ["<p>जीतू</p>", "<p>भीमा</p>",
                                "<p>ऑली</p>", "<p>आशा</p>"],
                    solution_en: "<p>47.(c) <strong>Olly</strong>. The Men\'s FIH Hockey World Cup is an international field hockey tournament organized by the International Hockey Federation, first held in 1971. The competition takes place every four years, filling the gap between the Summer Olympics. The 2023 tournament was hosted in Bhubaneswar, India, from January 13 to 29. Germany claimed their third World Cup title by defeating Belgium 5-4 in a penalty shoot-out after a 3-3 draw.</p>",
                    solution_hi: "<p>47.(c) <strong>ऑली</strong>। पुरुषों का FIH हॉकी विश्व कप अंतर्राष्ट्रीय हॉकी महासंघ द्वारा आयोजित एक अंतरराष्ट्रीय फील्ड हॉकी टूर्नामेंट है, जो पहली बार 1971 में आयोजित किया गया था। यह प्रतियोगिता प्रत्येक चार वर्ष में आयोजित की जाती है, जो ग्रीष्मकालीन ओलंपिक के बीच के अंतराल को भरती है। 2023 का टूर्नामेंट 13 से 29 जनवरी तक भारत के भुवनेश्वर में आयोजित किया गया था। जर्मनी ने 3-3 से ड्रॉ के बाद पेनल्टी शूट-आउट में बेल्जियम को 5-4 से हराकर अपना तीसरा विश्व कप खिताब जीता।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. The Indian Constitution has established _______.</p>",
                    question_hi: "<p>48. भारतीय संविधान ने ______ की स्थापना की है।</p>",
                    options_en: ["<p>partial Judicial system</p>", "<p>dual judicial system</p>", 
                                "<p>independent judicial system</p>", "<p>plural judicial system</p>"],
                    options_hi: ["<p>आंशिक न्यायपालिका प्रणाली (partial Judicial system)</p>", "<p>दोहरी न्यायपालिका प्रणाली (dual judicial system)</p>",
                                "<p>स्वतंत्र न्यायपालिका प्रणाली (independent judicial system)</p>", "<p>बहुल न्यायपालिका प्रणाली (plural judicial system)</p>"],
                    solution_en: "<p>48.(c) <strong>independent judicial system. </strong>The Constitution of India establishes a single, integrated, and independent judicial system, with the Supreme Court serving as the nation\'s Apex court. Key features of the Indian Constitution include Federalism, Separation of Powers, Fundamental Rights, Secularism, Single Citizenship, a Parliamentary form of government, Universal Adult Franchise, an Independent Judiciary, the Lengthiest Written Constitution, Directive Principles of State Policy, and Fundamental Duties.</p>",
                    solution_hi: "<p>48.(c) <strong>स्वतंत्र न्यायपालिका प्रणाली</strong> (independent judicial system)। भारत का संविधान एकल, एकीकृत और स्वतंत्र न्यायिक प्रणाली स्थापित करता है, जिसमें सर्वोच्च न्यायालय देश के सर्वोच्च न्यायालय के रूप में कार्य करता है। भारतीय संविधान की प्रमुख विशेषताओं में संघवाद, शक्तियों का पृथक्करण, मौलिक अधिकार, धर्मनिरपेक्षता, एकल नागरिकता, संसदीय शासन प्रणाली, सार्वभौमिक वयस्क मताधिकार, स्वतंत्र न्यायपालिका, सबसे बड़ा लिखित संविधान, राज्य के नीति निर्देशक सिद्धांत और मौलिक कर्तव्य शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which organisms are classified as Aves?</p>",
                    question_hi: "<p>49. कौन से जीवों को एवीज़ (Aves) के रूप में वर्गीकृत किया गया है?</p>",
                    options_en: ["<p>Fishes</p>", "<p>Frogs</p>", 
                                "<p>Snakes</p>", "<p>Birds</p>"],
                    options_hi: ["<p>मछली</p>", "<p>मेंढक</p>",
                                "<p>साँप</p>", "<p>पक्षी</p>"],
                    solution_en: "<p>49.(d) <strong>Birds</strong>. Aves, commonly known as birds, are a class of endothermic vertebrates under the phylum Chordata. They are characterized by features such as toothless beaks and laying hard-shelled eggs. Fish belong to the Animalia kingdom, phylum Chordata, and subphylum Vertebrata, and are aquatic vertebrates equipped with gills and fins. Frogs are amphibians, a group that also includes toads, newts, salamanders, and caecilians. Snakes fall under the class Reptilia, order Squamata, and suborder Serpentes.</p>",
                    solution_hi: "<p>49.(d) <strong>पक्षी</strong>। एवीज़, जिन्हें आम तौर पर पक्षी के रूप में जाना जाता है, कॉर्डेटा संघ के अंतर्गत एंडोथर्मिक कशेरुकियों का एक वर्ग है। इनकी विशेषता दांत रहित चोंच और कठोर छिलके वाले अंडे देने जैसी विशेषताएं हैं। मछलियाँ एनिमेलिया जगत, कॉर्डेटा संघ और वर्टेब्रेटा उपसंघ से संबंधित हैं, तथा गलफड़ों और पंखों से सुसज्जित जलीय कशेरुकी हैं। मेंढक उभयचर हैं, जो एक ऐसा समूह जिसमें टोड, न्यूट, सैलामैंडर और सीसिलियन भी शामिल हैं। साँप रेप्टिलिया वर्ग, स्क्वामाटा गण, और सर्पेन्टेस उपगण के अंतर्गत आते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Mahabalipuram emerged as an important centre of temple architecture under which of the following kingdoms of south India?</p>",
                    question_hi: "<p>50. दक्षिण भारत के निम्नलिखित में से किस साम्राज्य के अंतर्गत महाबलीपुरम मंदिर वास्तुकला के एक महत्वपूर्ण केंद्र के रूप में उभरा?</p>",
                    options_en: ["<p>Pallava</p>", "<p>Chola</p>", 
                                "<p>Chera</p>", "<p>Chalukya</p>"],
                    options_hi: ["<p>पल्लव</p>", "<p>चोल</p>",
                                "<p>चेरा</p>", "<p>चालुक्य</p>"],
                    solution_en: "<p>50.(a) <strong>Pallava</strong>. The Pallava dynasty, which ruled from the 3rd to the 9th century CE, oversaw the rise of Dravidian architecture, marked by the creation of numerous rock-cut and structural temples. Mahabalipuram (Mamallapuram), situated along southeastern India\'s Coromandel Coast, was a prominent port city under the Pallavas. The site features a group of monuments, including rock-cut cave temples, monolithic temples, bas-relief sculptures, structural temples, and excavated temple remains.</p>",
                    solution_hi: "<p>50.(a) <strong>पल्लव</strong>। पल्लव राजवंश, जिसने तीसरी से 9वीं शताब्दी ई. तक शासन किया, ने द्रविड़ वास्तुकला के उदय की देखरेख की, जिसमें कई चट्टानों को काटकर बनाए गए और संरचनात्मक मंदिरों का निर्माण शामिल था। दक्षिण-पूर्वी भारत के कोरोमंडल तट पर स्थित महाबलीपुरम (मामल्लापुरम) पल्लवों के अधीन एक प्रमुख बंदरगाह शहर था। इस स्थल पर स्मारकों का एक समूह है, जिसमें चट्टानों को काटकर बनाए गए गुफा मंदिर, मोनोलिथ मंदिर, बेस-रिलीफ मूर्तियां, संरचनात्मक मंदिर और उत्खनित मंदिर अवशेष शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The ages of two friends, Ram and Ravi, differ by 7 years. Ram&rsquo;s mother Mayadevi is three times as old as Ram, and Ravi is four times as old as his brother Soham. The ages of Mayadevi and Soham differ by 65 years. Find the age of Mayadevi (in years).</p>",
                    question_hi: "<p>51. दो मित्रों, राम और रवि की आयु में 7 वर्ष का अंतर है। राम की माता मायादेवी की आयु, राम की आयु की तीन गुनी है और रवि की आयु अपने भाई सोहम की आयु की चार गुनी है। मायादेवी और सोहम की आयु में 65 वर्ष का अंतर है। मायादेवी की आयु (वर्षों में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>69</p>", "<p>36</p>", 
                                "<p>85</p>", "<p>45</p>"],
                    options_hi: ["<p>69</p>", "<p>36</p>",
                                "<p>85</p>", "<p>45</p>"],
                    solution_en: "<p>51.(a)&nbsp;Let the age of Soham be x<br>Then, age of Ravi = 4x<br>Age of Mayadevi = 65 + x<br>Age of Ram = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>(65 + x)<br>According to question,<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>(65 + x) - 4x = 7<br><math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>3</mn></mfrac></math> - 4x = 7<br>- <math display=\"inline\"><mfrac><mrow><mn>11</mn><mi>x</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 7 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>3</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>11</mn><mi>x</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>3</mn></mfrac></math>&rArr; x = 4<br>So, the age of Mayadevi = 65 + 4 = 69 yrs</p>",
                    solution_hi: "<p>51.(a)&nbsp;माना, सोहम की आयु = x<br>तो, रवि की आयु = 4x<br>मायादेवी की आयु = 65 + x<br>राम की आयु = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>(65 + x)<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>(65 + x) - 4x = 7<br><math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>3</mn></mfrac></math> - 4x = 7<br>- <math display=\"inline\"><mfrac><mrow><mn>11</mn><mi>x</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 7 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>3</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>11</mn><mi>x</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>3</mn></mfrac></math>&rArr; x = 4<br>तो, मायादेवी की आयु = 65 + 4 = 69 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The following table represents the total number of research scholars and the respective number of female research scholars in various departments of a university. Study the&nbsp;table carefully and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383041.png\" alt=\"rId46\" width=\"450\" height=\"119\"> <br>What is the ratio of male research scholars in Mathematics to Statistics?</p>",
                    question_hi: "<p>52. निम्नलिखित तालिका एक विश्वविद्यालय के विभिन्न विभागों में शोधार्थियों की कुल संख्या और महिला शोधार्थियों की संबंधित संख्या को दर्शाती है। तालिका का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383185.png\" alt=\"rId47\" width=\"400\"> <br>गणित और सांख्यिकी विभाग में पुरुष शोधार्थियों का अनुपात कितना है?</p>",
                    options_en: ["<p>14 : 9</p>", "<p>7 : 6</p>", 
                                "<p>6 : 7</p>", "<p>9 : 14</p>"],
                    options_hi: ["<p>14 : 9</p>", "<p>7 : 6</p>",
                                "<p>6 : 7</p>", "<p>9 : 14</p>"],
                    solution_en: "<p>52.(a)<br>No of male research scholars in Mathematics = 18 - 4 = 14<br>No of male research scholars in Statistics = 12 - 3 = 9<br>Required ratio = 14 : 9</p>",
                    solution_hi: "<p>52.(a)<br>गणित में पुरुष शोधार्थियों की संख्या = 18 - 4 = 14<br>सांख्यिकी में पुरुष शोधार्थियों की संख्या = 12 - 3 = 9<br>अभीष्ट अनुपात = 14 : 9</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. What is the value of D, if A : C = B : D in the given table?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383298.png\" alt=\"rId48\" width=\"200\" height=\"52\"></p>",
                    question_hi: "<p>53. दी गई तालिका में यदि A : C = B : D है, तो D का मान कितना होगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383298.png\" alt=\"rId48\" width=\"200\" height=\"52\"></p>",
                    options_en: ["<p>56</p>", "<p>48</p>", 
                                "<p>45</p>", "<p>42</p>"],
                    options_hi: ["<p>56</p>", "<p>48</p>",
                                "<p>45</p>", "<p>42</p>"],
                    solution_en: "<p>53.(b) According to question,<br><math display=\"inline\"><mfrac><mrow><mi>A</mi></mrow><mrow><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">B</mi><mi mathvariant=\"normal\">D</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mi mathvariant=\"normal\">D</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> D = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>40</mn></mrow><mn>25</mn></mfrac></math> = 48</p>",
                    solution_hi: "<p>53.(b) प्रश्न के अनुसार ,<br><math display=\"inline\"><mfrac><mrow><mi>A</mi></mrow><mrow><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">B</mi><mi mathvariant=\"normal\">D</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mi mathvariant=\"normal\">D</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> D = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>40</mn></mrow><mn>25</mn></mfrac></math> = 48</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. By applying which of the following criteria can two triangles NOT be proved congruent?</p>",
                    question_hi: "<p>54. निम्नलिखित में से किस मानदंड को लागू करने से दो त्रिभुजों को सर्वांगसम सिद्ध नहीं किया जा सकता है?</p>",
                    options_en: ["<p>angle-side-angle</p>", "<p>angle-angle-angle</p>", 
                                "<p>side-side-side</p>", "<p>side-angle-side</p>"],
                    options_hi: ["<p>कोण-भुजा-कोण</p>", "<p>कोण-कोण-कोण</p>",
                                "<p>भुजा-भुजा-भुजा</p>", "<p>भुजा-कोण-भुजा</p>"],
                    solution_en: "<p>54.(b)<br>Applying angle-angle-angle(AAA) criteria , two triangles can never be proved congruent.</p>",
                    solution_hi: "<p>54.(b)<br>सर्वांगसमता , कोण-कोण-कोण (AAA) मानदंड लागू करने पर, दो त्रिभुजों को कभी भी सर्वांगसम सिद्ध नहीं किया जा सकता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Two circles of radii 18 cm and 12 cm touch each other externally. Find the length (in cm) of their direct common tangent.</p>",
                    question_hi: "<p>55. 18 cm और 12 cm त्रिज्या वाले दो वृत्त एक दूसरे को बाह्य रूप से स्पर्श करते हैं। उनकी सीधी उभयनिष्ठ स्पर्शरेखा की लंबाई (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>15</mn><msqrt><mn>6</mn></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>10</mn><msqrt><mn>6</mn></msqrt></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><msqrt><mn>6</mn></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18</mn><msqrt><mn>6</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>15</mn><msqrt><mn>6</mn></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>10</mn><msqrt><mn>6</mn></msqrt></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><msqrt><mn>6</mn></msqrt></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>18</mn><msqrt><mn>6</mn></msqrt></math></p>"],
                    solution_en: "<p>55.(c)&nbsp;Distance between centres (d) = 18 + 12 = 30 cm<br>Direct common tangent = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>R</mi><mo>-</mo><mi>r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn><msup><mn>0</mn><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>18</mn><mo>-</mo><mn>12</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>900</mn><mo>-</mo><mn>36</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>864</mn></msqrt></math> = 12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>55.(c)&nbsp;केन्द्रों के बीच की दूरी (d) = 18 + 12 = 30 सेमी<br>सीधी उभयनिष्ठ स्पर्शरेखा = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>R</mi><mo>-</mo><mi>r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn><msup><mn>0</mn><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>18</mn><mo>-</mo><mn>12</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>900</mn><mo>-</mo><mn>36</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>864</mn></msqrt></math> = 12<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math> सेमी</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> is equal to _______.</p>",
                    question_hi: "<p>56. <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> का मान ________ के बराबर है।</p>",
                    options_en: ["<p>sec2A + tan2A</p>", "<p>sec2A + cosec2A</p>", 
                                "<p>cot2A - cosec2A</p>", "<p>sec2A.cosec2A</p>"],
                    options_hi: ["<p>sec2A + tan2A</p>", "<p>sec2A + cosec2A</p>",
                                "<p>cot2A - cosec2A</p>", "<p>sec2A.cosec2A</p>"],
                    solution_en: "<p>56.(d) <strong>Formula used :</strong> Sec&sup2;&theta; = 1 + tan&sup2;&theta; and cosec&sup2;&theta; = 1 + cot&sup2;&theta;<br>now,<br><math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mo>+</mo><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mo>.</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo></mrow><mrow><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>2</mn><mi>A</mi></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow></mfrac></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></mstyle></mfrac></mstyle></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> = sec2A.cosec2A</p>",
                    solution_hi: "<p>56.(d) <strong>सूत्र का प्रयोग करने पर : </strong>Sec&sup2;&theta; = 1 + tan&sup2;&theta; और cosec&sup2;&theta; = 1 + cot&sup2;&theta;<br>अब,<br><math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mo>+</mo><mn>1</mn><mo>+</mo><mi>c</mi><mi>o</mi><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mo>+</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mo>.</mo></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo></mrow><mrow><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>2</mn><mi>A</mi></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow></mfrac></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></mstyle></mfrac></mstyle></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> = sec2A.cosec2A</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Kiran lent ₹4,000 to two persons such that one part of it at the rate of 8% per annum and the remaining part at the rate of 10% per annum. He got an annual simple interest of ₹352. Then the sum lent at 10% is:</p>",
                    question_hi: "<p>57. किरण ने दो व्यक्तियों को ₹ 4,000 का एक भाग 8% प्रति वर्ष की दर से और शेष भाग 10% प्रति वर्ष की दर से उधार दिए । उसे ₹ 352 का वार्षिक साधारण ब्याज मिला। तो 10% पर उधार दी गई राशि ज्ञात करें।</p>",
                    options_en: ["<p>₹1800</p>", "<p>₹2200</p>", 
                                "<p>₹2400</p>", "<p>₹1600</p>"],
                    options_hi: ["<p>₹1800</p>", "<p>₹2200</p>",
                                "<p>₹2400</p>", "<p>₹1600</p>"],
                    solution_en: "<p>57.(d) <br>Overall rate% = <math display=\"inline\"><mfrac><mrow><mn>352</mn></mrow><mrow><mn>4000</mn></mrow></mfrac></math> &times; 100 = 8.8%<br>Using Alligation method, we have ;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383397.png\" alt=\"rId49\" width=\"150\"><br>So, the sum lent at 10% = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> &times; 4000 = ₹1600</p>",
                    solution_hi: "<p>57.(d) <br>कुल दर % = <math display=\"inline\"><mfrac><mrow><mn>352</mn></mrow><mrow><mn>4000</mn></mrow></mfrac></math> &times; 100 = 8.8%<br>एलीगेशन विधि का उपयोग करने पर - <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383397.png\" alt=\"rId49\" width=\"150\"><br>तो, 10% पर उधार दी गई राशि = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> &times; 4000 = ₹1600</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. X, Y, and Z are three points on a plane with XY = 11 cm, YZ = 13 cm, and XZ = 24 cm. The number of circles that travel through points X, Y, and Z is:</p>",
                    question_hi: "<p>58. X, Y और Z, किसी तल पर तीन बिंदु हैं, जिसमें XY = 11 cm, YZ = 13 cm और XZ = 24 cm है। बिंदु X, Y और Z से होकर गुजरने वाले वृत्तों क संख्या कितनी होगी?</p>",
                    options_en: ["<p>0</p>", "<p>2</p>", 
                                "<p>1</p>", "<p>3</p>"],
                    options_hi: ["<p>0</p>", "<p>2</p>",
                                "<p>1</p>", "<p>3</p>"],
                    solution_en: "<p>58.(a)&nbsp;As, XY + YZ = XZ (11 + 13 = 24) implies that X, Y, Z are collinear points. So, the number of circles that travel through points X, Y, Z is 0 .</p>",
                    solution_hi: "<p>58.(a)&nbsp;चूँकि, XY + YZ = XZ (11 + 13 = 24) का अर्थ है कि X, Y, Z संरेख बिंदु हैं। तो, बिंदु X, Y, Z से होकर जाने वाले वृत्तों की संख्या 0 है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The value of 15 &divide;&nbsp;3 of 2 &times; 2 + 9 &divide; 18 of 2 &times; 4 - 4 &divide; 8 &times; 2 is:</p>",
                    question_hi: "<p>59.15 &divide; 3 का 2 &times; 2 + 9 &divide; 18 का 2 &times; 4 - 4 &divide; 8 &times; 2 का मान ज्ञात करें।</p>",
                    options_en: ["<p>8</p>", "<p>5</p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7</mn><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>"],
                    options_hi: ["<p>8</p>", "<p>5</p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7</mn><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>"],
                    solution_en: "<p>59.(b) 15 &divide;&nbsp;3 of 2 &times; 2 + 9 &divide; 18 of 2 &times; 4 - 4 &divide; 8 &times; 2<br>= 15 &divide; 6 &times; 2 + 9 &divide; 36 &times; 4 - 1<br>= 5 + 1 - 1 = 5</p>",
                    solution_hi: "<p>59.(b) 15 &divide;&nbsp;3 का 2 &times; 2 + 9 &divide; 18 का 2 &times; 4 - 4 &divide; 8 &times; 2<br>= 15 &divide; 6 &times; 2 + 9 &divide; 36 &times; 4 - 1<br>= 5 + 1 - 1 = 5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. If, on a marked price, the difference between selling prices with a discount of 65% and with two successive discounts of 30% and 40% is ₹105, then the marked price (in ₹) is:</p>",
                    question_hi: "<p>60. यदि अंकित मूल्य पर 65% की छूट और 30% और 40% की दो क्रमिक छूट के बाद विक्रय मूल्यों के बीच का अंतर ₹105 है, तो अंकित मूल्य (₹ में) क्या है?</p>",
                    options_en: ["<p>1,650</p>", "<p>1,050</p>", 
                                "<p>1,500</p>", "<p>1,320</p>"],
                    options_hi: ["<p>1,650</p>", "<p>1,050</p>",
                                "<p>1,500</p>", "<p>1,320</p>"],
                    solution_en: "<p>60.(c).<br>Effective discount of 30% &amp; 40% = (30 + 40 - <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 70 - 12 = 58%<br>Now, MP &times; (65 - 58)% = 105<br>MP &times; 7% = 105<br>MP = <math display=\"inline\"><mfrac><mrow><mn>105</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = ₹1500</p>",
                    solution_hi: "<p>60.(c).<br>30% और 40% की प्रभावी छूट = (30 + 40 - <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 70 - 12 = 58%<br>अब, अंकित मूल्य &times; (65 - 58)% = 105<br>अंकित मूल्य &times; 7% = 105<br>अंकित मूल्य = <math display=\"inline\"><mfrac><mrow><mn>105</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = ₹1500</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A pole 10 m long rests slantly against a vertical wall AB making an angle 30&deg; with the horizontal (ground). Find how far the foot of the pole is from the wall (in metres).</p>",
                    question_hi: "<p>61.10 m लंबा एक खंभा क्षैतिज (जमीन) से 30&deg; का कोण बनाते हुए एक ऊर्ध्वाधर दीवार AB पर तिरछा टिका हुआ है। ज्ञात कीजिए कि खंभे का पाद दीवार से कितनी दूर (मीटर में) है।</p>",
                    options_en: ["<p>15</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>", 
                                "<p>10</p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>5</mn><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p>15</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                                "<p>10</p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>5</mn><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p>61.(d)&nbsp;Let AB and AC be the length of wall and pole respectively.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383515.png\" alt=\"rId50\" width=\"150\"><br>In <math display=\"inline\"><mo>&#9651;</mo></math>ABC, cos30&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mn>10</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mn>10</mn></mfrac></math><br>BC = <math display=\"inline\"><mfrac><mrow><mn>10</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m<br>So, the the foot of the pole is 5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m away from the wall.</p>",
                    solution_hi: "<p>61.(d)&nbsp;मान लीजिए AB और AC क्रमशः दीवार और खंभे की लंबाई हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383515.png\" alt=\"rId50\" width=\"150\"><br><math display=\"inline\"><mo>&#9651;</mo></math>ABC में , cos30&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mn>10</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mn>10</mn></mfrac></math><br>BC = <math display=\"inline\"><mfrac><mrow><mn>10</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m<br>अतः, खंभे का आधार दीवार से 5<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m मीटर दूर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The bar graph given below shows the sales of books from six schools during two consecutive years, 2013 and 2014.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383628.png\" alt=\"rId51\" width=\"350\" height=\"240\"><br>What percentage of the average sales of the schools A, B, and C in 2014 is the average sales of schools A, C, and F in 2013 ?</p>",
                    question_hi: "<p>62. नीचे दिया गया बार ग्राफ क्रमागत दो वर्षों, 2013 और 2014 के दौरान छह स्कूलों की पुस्तकों की बिक्री को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383752.png\" alt=\"rId52\" width=\"350\"> <br>2014 में स्कूल A, B, और C की औसत बिक्री, 2013 में स्कूल A, C, और F की औसत बिक्री का कितना प्रतिशत है?</p>",
                    options_en: ["<p>81.08%</p>", "<p>81%</p>", 
                                "<p>80%</p>", "<p>80.08%</p>"],
                    options_hi: ["<p>81.08%</p>", "<p>81%</p>",
                                "<p>80%</p>", "<p>80.08%</p>"],
                    solution_en: "<p>62.(a)<br>Average sales of the schools A, B, and C in 2014 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>70</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>35</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>80</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>185</mn><mn>3</mn></mfrac></math><br>Average sales of the schools A, C, and F in 2013 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>60</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>40</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>3</mn></mfrac></math><br>Ratio = <math display=\"inline\"><mfrac><mrow><mn>185</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>3</mn></mfrac></math> = 37 : 30<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math> &times; 100 = 81.08%</p>",
                    solution_hi: "<p>62.(a)<br>2014 में स्कूल A, B और C की औसत बिक्री = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>70</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>35</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>80</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>185</mn><mn>3</mn></mfrac></math><br>2013 में स्कूल A, C और F की औसत बिक्री = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>60</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>40</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>3</mn></mfrac></math><br>अनुपात = <math display=\"inline\"><mfrac><mrow><mn>185</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>3</mn></mfrac></math>&nbsp;= 37 : 30<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math> &times; 100 = 81.08%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. A money bag contains one rupee, 5 rupee and 10 rupee coins in the ratio of 3 : 5 : 7 respectively. If the total amount in the bag is ₹980, then find the number of coins of ₹10.</p>",
                    question_hi: "<p>63. एक मनी बैग में क्रमश: 1 रुपये, 5 रुपये और 10 रुपये के सिक्के 3 : 5 : 7 के अनुपात में हैं। यदि उस बैग में कुल राशि ₹980 हो, तो ₹10 के सिक्कों की संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>69</p>", "<p>70</p>", 
                                "<p>71</p>", "<p>68</p>"],
                    options_hi: ["<p>69</p>", "<p>70</p>",
                                "<p>71</p>", "<p>68</p>"],
                    solution_en: "<p>63.(b)<br>Denomination &rarr; ₹1 : ₹5 : ₹10<br>No of coins &rarr;&nbsp; &nbsp; &nbsp;3x : 5x : 7x<br>Total amount in the bag = 3x + 5&times;5x + 10&times;7x = 3x + 25x + 70x = 98x<br>Now, 98x = 980 <math display=\"inline\"><mo>&#8658;</mo></math> x = 10<br>No of coins of ₹10 = 7 &times; 10 = 70</p>",
                    solution_hi: "<p>63.(b)<br>मूल्यवर्ग &rarr; ₹1 : ₹5 : ₹10<br>सिक्कों की संख्या &rarr; 3x : 5x : 7x<br>बैग में कुल राशि = 3x + 5&times;5x + 10&times;7x = 3x + 25x + 70x = 98x<br>अब, 98x = 980 <math display=\"inline\"><mo>&#8658;</mo></math> x = 10<br>₹10 के सिक्कों की संख्या = 7 &times; 10 = 70</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Which of the following is the largest 5-digit number divisible by 47 ?</p>",
                    question_hi: "<p>64. निम्नलिखित में से कौन-सी, 47 से विभाज्य 5 अंकों की सबसे बड़ी संख्या है?</p>",
                    options_en: ["<p>99999</p>", "<p>98888</p>", 
                                "<p>99969</p>", "<p>10000</p>"],
                    options_hi: ["<p>99999</p>", "<p>98888</p>",
                                "<p>99969</p>", "<p>10000</p>"],
                    solution_en: "<p>64.(c)<br>Largest 5-digit number = 99999<br>On dividing 99999 by 47, we get 30 as a remainder.<br>So, the required no which is divisible by 47 = 99999 - 30 = 99969</p>",
                    solution_hi: "<p>64.(c)<br>5 अंकों की सबसे बड़ी संख्या = 99999<br>99999 को 47 से विभाजित करने पर प्राप्त शेषफल = 30<br>तो, आवश्यक संख्या जो 47 से विभाज्य हो = 99999 - 30 = 99969</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. If a right circular cone of height 24 cm has a volume of 1232 cm&sup3; , then the total surface area of the cone is ( use <math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>) :</p>",
                    question_hi: "<p>65. यदि 24 cm ऊँचाई वाले एक लम्ब वृत्तीय शंकु का आयतन 1232 cm&sup3; है, तो शंकु का संपूर्ण पृष्ठीय क्षेत्रफल क्या है( <math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>का उपयोग कीजिए)?</p>",
                    options_en: ["<p>806 cm&sup2;</p>", "<p>704 cm&sup2;</p>", 
                                "<p>904 cm&sup2;</p>", "<p>608 cm&sup2;</p>"],
                    options_hi: ["<p>806 cm&sup2;</p>", "<p>704 cm&sup2;</p>",
                                "<p>904 cm&sup2;</p>", "<p>608 cm&sup2;</p>"],
                    solution_en: "<p>65.(b)<br>Volume of cone = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math>h<br>1232 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; r&sup2; &times; 24<br>1232 = <math display=\"inline\"><mfrac><mrow><mn>176</mn></mrow><mrow><mn>7</mn></mrow></mfrac><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br><math display=\"inline\"><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1232</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>7</mn></mrow><mn>176</mn></mfrac></math> = 49<br><math display=\"inline\"><mo>&#8658;</mo></math> r = 7 cm<br>Slant height = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mn>4</mn><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn></msqrt></math> = 25 cm<br>TSA of cone = <math display=\"inline\"><mi>&#960;</mi></math>r(r + l)<br>= <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 7(7 + 25)<br>= 22 &times; 32 = 704 <math display=\"inline\"><mi>c</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>",
                    solution_hi: "<p>65.(b)<br>शंकु का आयतन = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math>h<br>1232 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; r&sup2; &times; 24<br>1232 = <math display=\"inline\"><mfrac><mrow><mn>176</mn></mrow><mrow><mn>7</mn></mrow></mfrac><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br><math display=\"inline\"><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1232</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>7</mn></mrow><mn>176</mn></mfrac></math> = 49<br><math display=\"inline\"><mo>&#8658;</mo></math> r = 7 cm<br>तिर्यक ऊँचाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><msup><mn>4</mn><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn></msqrt></math> = 25 सेमी<br>शंकु का सम्पूर्ण पृष्ठ का क्षेत्रफल = <math display=\"inline\"><mi>&#960;</mi></math>r(r + l)<br>= <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 7(7 + 25)<br>= 22 &times; 32 = 704 सेमी&sup2;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Let 0&deg; &lt; t &lt; 90&deg;. Then which of the followings is true ?</p>",
                    question_hi: "<p>66. मान लीजिए 0&deg; &lt; t &lt; 90&deg; है। तो निम्नलिखित में से कौन-सा सत्य है?</p>",
                    options_en: ["<p>sin(t) &ne;&nbsp;cos (t) when t = 45&deg;</p>", "<p>sin(t) &gt; cos (t) when t &lt; 45&deg;</p>", 
                                "<p>sin(t) &lt; cos (t) when t &lt; 45&deg;</p>", "<p>sin(t) &lt; cos (t) when t &gt; 45&deg;</p>"],
                    options_hi: ["<p>sin(t) &ne; cos (t) जब t = 45&deg;</p>", "<p>sin(t) &gt; cos (t) जब t &lt; 45&deg;</p>",
                                "<p>sin(t) &lt; cos (t) जब t &lt; 45&deg;</p>", "<p>sin(t) &lt; cos (t) जब t &gt; 45&deg;</p>"],
                    solution_en: "<p>66.(c)&nbsp;Using the given options, we have ;<br>(a) when t = 45&deg;, sin45&deg; = cos45&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math>; sin(t) &ne; cos (t) [false] <br>(b) when t = 30&deg;, sin30&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>, cos30&deg; = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>; sin30&deg; &gt; cos30&deg; [false]<br>(c) when t = 30&deg;, sin30&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>, cos30&deg; = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>; sin30&deg; &lt; cos30&deg; [true]<br>(d) when t = 60&deg;, sin60&deg; = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>, cos60&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>; sin60&deg; &lt; cos60&deg; [false]<br>Clearly, we can see that only option(c) is the correct one.</p>",
                    solution_hi: "<p>66.(c)&nbsp;दिए गए विकल्पों का उपयोग करते हुए,<br>(a) जब t = 45&deg;, sin45&deg; = cos45&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> ; sin(t) &ne; cos (t) [गलत]<br>(b) जब t = 30&deg;, sin30&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>, cos30&deg; = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>; sin30&deg; &gt; cos30&deg; [गलत]<br>(c) जब t = 30&deg;, sin30&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>, cos30&deg; = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>; sin30&deg; &lt; cos30&deg; [सही]<br>(d) जब t = 60&deg;, sin60&deg; = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>, cos60&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>; sin60&deg; &lt; cos60&deg; [गलत]<br>स्पष्ट रूप से, हम देख सकते हैं कि केवल विकल्प (c) ही सही है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Ramesh purchased some items from a dealer on 19 November 2019. The bill generated was for ₹5,480. He had also purchased some items on 10 November 2019 having the bill value of ₹9,800. He cleared both the bills this time and paid the total amount in cash. If the dealer gives a scheme of 2% discount on the payments done within 10 days and 5% for the payments made in cash at the time of purchase, then find the amount paid by Ramesh on 19 November<br>2019.</p>",
                    question_hi: "<p>67. रमेश ने 19 नवंबर 2019 को एक डीलर से कुछ सामान खरीदा। बिल ₹5,480 का हुआ। उसने 10 नवंबर को भी कुछ सामान खरीदा था, जिसका बिल मूल्य 9,800 रुपये था। उन्होंने ने इस बार दोनों बिलों का भुगतान 2019 किया और कुल राशि का नकद भुगतान किया। यदि डीलर 10 दिनों के भीतर किए गए भुगतान पर 2% छूट और खरीद के समय नकद भुगतान के लिए 5% छूट की योजना देता है, तो रमेश द्वारा 19 नवंबर 2019 को भुगतान की गई राशि ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹ 14,850</p>", "<p>₹ 14,810</p>", 
                                "<p>₹ 14,890</p>", "<p>₹ 14,950</p>"],
                    options_hi: ["<p>₹ 14,850</p>", "<p>₹ 14,810</p>",
                                "<p>₹ 14,890</p>", "<p>₹ 14,950</p>"],
                    solution_en: "<p>67.(b)&nbsp;Amount paid by Ramesh on 19 november 2019 = 9800 &times; 98% + 5480 &times; 95% = 9604 + 5206 = ₹14810</p>",
                    solution_hi: "<p>67.(b)&nbsp;रमेश द्वारा 19 नवंबर 2019 को भुगतान की गई राशि = 9800 &times; 98% + 5480 &times; 95% = 9604 + 5206 = ₹14810</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Find the gain percentage, given that Priya sold her scooter for ₹ 42564 gaining <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>th of the selling price.</p>",
                    question_hi: "<p>68. प्रिया अपने स्कूटर को ₹42564 में बेचकर, विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> भाग के बराबर लाभ अर्जित करती है। उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>30%</p>", "<p>20%</p>", 
                                "<p>15%</p>", "<p>40%</p>"],
                    options_hi: ["<p>30%</p>", "<p>20%</p>",
                                "<p>15%</p>", "<p>40%</p>"],
                    solution_en: "<p>68.(b) <br>Gain = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>&#8594;</mo><mi>&#160;</mi><mi>g</mi><mi>a</mi><mi>i</mi><mi>n</mi></mrow><mrow><mn>6</mn><mi>&#160;</mi><mo>&#8594;</mo><mi>&#160;</mi><mi>S</mi><mi>P</mi></mrow></mfrac></math> Then, CP = 6 - 1 = 5<br>gain% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    solution_hi: "<p>68.(b) <br>लाभ = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#8594;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2354;&#2366;&#2349;</mi></mrow><mrow><mn>6</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#8594;</mo><mo>&#160;&#160;</mo><mi>&#2357;&#2367;</mi><mo>.</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;</mi><mo>.</mo><mo>&#160;&#160;</mo><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> तब , क्रय मूल्य = 6 - 1 = 5<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. If a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math> = 12, then find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac></math>.</p>",
                    question_hi: "<p>69. यदि a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math> = 12 है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>144</p>", "<p>146</p>", 
                                "<p>142</p>", "<p>140</p>"],
                    options_hi: ["<p>144</p>", "<p>146</p>",
                                "<p>142</p>", "<p>140</p>"],
                    solution_en: "<p>69.(c) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn></math> <br>= <math display=\"inline\"><mn>1</mn><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></math> - 2 = 142</p>",
                    solution_hi: "<p>69.(c) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn></math><br>= <math display=\"inline\"><mn>1</mn><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></math> - 2 = 142</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The side of an equilateral triangle is 28 cm. Taking each vertex as the centre, a circle is described with a radius equal to half the length of the side of the triangle. Find the area of that&nbsp;part of the triangle which is not included in the circles (use <math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 1.73 ).</p>",
                    question_hi: "<p>70. एक समबाहु त्रिभुज की भुजा 28 cm है। प्रत्येक शीर्ष को केंद्र मानकर, त्रिभुज की भुजा की आधी लंबाई के बराबर त्रिज्या वाले एक वृत्त को चित्रित किया गया है। त्रिभुज के उस भाग का क्षेत्रफल ज्ञात कीजिए, जो वृत्त में शामिल नहीं है (&nbsp;<math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 1.73 का उपयोग कीजिए )।</p>",
                    options_en: ["<p>30.89 cm&sup2;</p>", "<p>38.08 cm&sup2;</p>", 
                                "<p>31.08 cm&sup2;</p>", "<p>39.08 cm&sup2;</p>"],
                    options_hi: ["<p>30.89 cm&sup2;</p>", "<p>38.08 cm&sup2;</p>",
                                "<p>31.08 cm&sup2;</p>", "<p>39.08 cm&sup2;</p>"],
                    solution_en: "<p>70.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383854.png\" alt=\"rId53\" width=\"200\"><br>Area of 3 sectors = 3 &times; (<math display=\"inline\"><mfrac><mrow><mn>60</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 14&sup2; ) = 308 cm&sup2;<br>Area of <math display=\"inline\"><mo>&#9651;</mo></math>ABC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> &times; 28&sup2; = 339.08 cm&sup2;<br>Then, area of shaded region = 339.08 - 308 = 31.08 cm&sup2;</p>",
                    solution_hi: "<p>70.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383854.png\" alt=\"rId53\" width=\"200\"><br>3 सेक्टरों का क्षेत्रफल = 3 &times; (<math display=\"inline\"><mfrac><mrow><mn>60</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 14&sup2; ) = 308 cm&sup2;<br><math display=\"inline\"><mo>&#9651;</mo></math>ABC का क्षेत्रफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> &times; 28&sup2; = 339.08 cm&sup2;<br>फिर, छायांकित क्षेत्र का क्षेत्रफल = 339.08 - 308 = 31.08 cm&sup2;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Study the given table and answer the question that follows.<br>The given table shows the production of three types of cars (A, B and C) manufactured ( in thousands ) by an automobile company over the years<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777383955.png\" alt=\"rId54\" width=\"400\" height=\"107\"> <br>For all the years from 2015 to 2018, the average production of B-type cars is how much less ( in thousands ) than that of the combined average production of A-type and C-type cars?</p>",
                    question_hi: "<p>71. दी गई तालिका का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए। दी गई तालिका पिछले कुछ वर्षों में एक ऑटोमोबाइल कंपनी द्वारा निर्मित तीन प्रकार की कारों (A, B और C) का उत्पादन (हजार में) दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777384113.png\" alt=\"rId55\" width=\"350\" height=\"105\"> <br>वर्ष 2015 से 2018 तक सभी वर्षों के लिए, B प्रकार की कारों का औसत उत्पादन A प्रकार और C प्रकार की कारों के संयुक्त औसत उत्पादन से कितना कम है ।</p>",
                    options_en: ["<p>238.5</p>", "<p>271.25</p>", 
                                "<p>314.75</p>", "<p>284.55</p>"],
                    options_hi: ["<p>238.5</p>", "<p>271.25</p>",
                                "<p>314.75</p>", "<p>284.55</p>"],
                    solution_en: "<p>71.(b). Average production of A-type cars = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>840</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>900</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>760</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>800</mn></mrow><mn>4</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3300</mn><mn>4</mn></mfrac></math> = 825<br>Average production of B-type cars = <math display=\"inline\"><mfrac><mrow><mn>680</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>750</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>620</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>540</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2590</mn><mn>4</mn></mfrac></math> = 647.5<br>Average production of C-type cars =<math display=\"inline\"><mfrac><mrow><mn>890</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>960</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1000</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1200</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4050</mn><mn>4</mn></mfrac></math> = 1,012.5<br>Combined average production of A-type &amp; C-type cars = <math display=\"inline\"><mfrac><mrow><mn>825</mn><mo>+</mo><mn>1012</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>,</mo><mn>837</mn><mo>.</mo><mn>5</mn></mrow><mn>2</mn></mfrac></math> = 918.75<br>Required difference = 918.75 - 647.5 = 271.25</p>",
                    solution_hi: "<p>71.(b). A-प्रकार की कारों का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>840</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>900</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>760</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>800</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3300</mn><mn>4</mn></mfrac></math> = 825<br>B-प्रकार की कारों का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>680</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>750</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>620</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>540</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2590</mn><mn>4</mn></mfrac></math> = 647.5<br>C-प्रकार की कारों का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>890</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>960</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1000</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1200</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4050</mn><mn>4</mn></mfrac></math> = 1,012.5<br>A-टाइप और C-टाइप कारों का संयुक्त औसत उत्पादन= <math display=\"inline\"><mfrac><mrow><mn>825</mn><mo>+</mo><mn>1012</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>,</mo><mn>837</mn><mo>.</mo><mn>5</mn></mrow><mn>2</mn></mfrac></math> = 918.75<br>आवश्यक अंतर = 918.75 - 647.5 = 271.25</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. In a circular race of 750 m, X and Y start from the same point and at the same time with speeds of 9 km/h and 13.5 km/h, respectively. If they are running in the same direction then, when will they meet again for the first time on the track?</p>",
                    question_hi: "<p>72. 750 m वाली एक वृत्&zwj;ताकार दौड़ में, X और Y एक ही बिंदु से और एक ही समय पर क्रमशः 9 km/h और 13.5 km/h की चाल से दौड़ना शुरू करते हैं। यदि वे एक ही दिशा में दौड़ रहे हैं, तो वे पुन: ट्रैक पर पहली बार कब मिलेंगे?</p>",
                    options_en: ["<p>750 seconds</p>", "<p>900 seconds</p>", 
                                "<p>500 seconds</p>", "<p>600 seconds</p>"],
                    options_hi: ["<p>750 सेकंड में</p>", "<p>900 सेकंड में</p>",
                                "<p>500 सेकंड में</p>", "<p>600 सेकंड में</p>"],
                    solution_en: "<p>72.(d)<br>Relative speed when running in same direction =13.5 - 9 = 4.5 km/hr = 4.5 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 1.25 m/sec <br>Time taken to meet again for the first time = <math display=\"inline\"><mfrac><mrow><mn>750</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = 600 sec</p>",
                    solution_hi: "<p>72.(d)<br>समान दिशा में चलने पर सापेक्ष गति = 13.5 - 9 = 4.5 किमी/घंटा = 4.5 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 1.25 मीटर/सेकंड <br>पहली बार दोबारा मिलने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>750</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = 600 सेकंड</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A bus travels <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of the distance initially at a speed of 40 km/h, the next <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>of the distance 50 km/h, and the final <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>of the distance at 60 km/h. Find the average speed of the bus for the entire journey.<br>(Rounded off to two decimal places)</p>",
                    question_hi: "<p>73. कोई बस, शुरू में किसी दूरी का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> भाग 40 km/h की चाल से तय करती है, दूरी का अगला <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> भाग 50 km/h की चाल से तय करती है और दूरी का अंतिम <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>भाग 60 km/h की चाल से तय करती है। पूरी यात्रा के लिए बस की औसत चाल ज्ञात कीजिए। (दो दशमलव स्थान तक पूर्णांकित)</p>",
                    options_en: ["<p>48.65 km/h</p>", "<p>51.43 km/h</p>", 
                                "<p>54.13 km/h</p>", "<p>46.85 km/h</p>"],
                    options_hi: ["<p>48.65 km/h</p>", "<p>51.43 km/h</p>",
                                "<p>54.13 km/h</p>", "<p>46.85 km/h</p>"],
                    solution_en: "<p>73.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777384244.png\" alt=\"rId56\" width=\"250\" height=\"171\"><br>Average speed of the bus = <math display=\"inline\"><mfrac><mrow><mn>600</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>15</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>12</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1800</mn><mn>37</mn></mfrac></math> = 48.65 km/hr</p>",
                    solution_hi: "<p>73.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777384346.png\" alt=\"rId57\" width=\"250\"><br>बस की औसत गति = <math display=\"inline\"><mfrac><mrow><mn>600</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>15</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>12</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1800</mn><mn>37</mn></mfrac></math> = 48.65 किमी/घंटा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Three pipes P, Q and R can fill a cistern in 40 minutes, 80 minutes and 120 minutes, respectively. Initially, all the pipes are opened. After how much time (in minutes) should the pipes Q and R be turned off so that the cistern will be completely filled in just half an hour?</p>",
                    question_hi: "<p>74. तीन पाइप P, Q और R एक टंकी को क्रमशः 40 मिनट, 80 मिनट और 120 मिनट में भर सकते हैं। शुरुआत में, सभी पाइप खोल दिए जाते हैं। कितने समय (मिनटों में) के बाद, पाइप Q और R को बंद कर देना चाहिए ताकि टंकी केवल आधे घंटे में पूरी भर जाए?</p>",
                    options_en: ["<p>14</p>", "<p>10</p>", 
                                "<p>16</p>", "<p>12</p>"],
                    options_hi: ["<p>14</p>", "<p>10</p>",
                                "<p>16</p>", "<p>12</p>"],
                    solution_en: "<p>74.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777384467.png\" alt=\"rId58\" width=\"200\"><br>Let pipes Q and R be turned off for t minutes<br>According to question <br>(6 + 3 + 2) &times; t + 6 &times; (30 - t) = 240<br>11t + 180 - 6t = 240<br>5t = 60 <math display=\"inline\"><mo>&#8658;</mo></math> t = 12</p>",
                    solution_hi: "<p>74.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730777384580.png\" alt=\"rId59\" width=\"250\"><br>माना , पाइप Q और R को t मिनट के लिए बंद कर दिया गया है<br>प्रश्न के अनुसार <br>(6 + 3 + 2) &times; t + 6 &times; (30 - t) = 240<br>11t + 180 - 6t = 240<br>5t = 60 <math display=\"inline\"><mo>&#8658;</mo></math> t = 12</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. If the sum of three numbers is 18 and the sum of their squares is 36, then find the difference between the sum of their cubes and three times of their product.</p>",
                    question_hi: "<p>75. यदि तीन संख्याओं का योग 18 है और उनके वर्गों का योग 36 है, तो उनके घनों के योग और उनके गुणनफल के तीन गुना के बीच अंतर ज्ञात कीजिए।</p>",
                    options_en: ["<p>1449</p>", "<p>&minus;1944</p>", 
                                "<p>&minus;1494</p>", "<p>4149</p>"],
                    options_hi: ["<p>1449</p>", "<p>&minus;1944</p>",
                                "<p>&minus;1494</p>", "<p>4149</p>"],
                    solution_en: "<p>75.(b)&nbsp;Given that, a + b + c = 18 &amp; a&sup2; + b&sup2; + c&sup2; = 36<br>Then, a&sup3; + b&sup3; + c&sup3; - 3abc = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>(a + b + c) [3(a&sup2; + b&sup2; + c&sup2;) - (a + b + c)&sup2;]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 18[3 &times; 36 - 18&sup2;]<br>= 9[108 - 324] = 9 &times; (-216) = -1944</p>",
                    solution_hi: "<p>75.(b) दिया गया है ,&nbsp;a + b + c = 18 &amp; a&sup2; + b&sup2; + c&sup2; = 36<br>तब, a&sup3; + b&sup3; + c&sup3; - 3abc = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>(a + b + c) [3(a&sup2; + b&sup2; + c&sup2;) - (a + b + c)&sup2;]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 18[3 &times; 36 - 18&sup2;]<br>= 9[108 - 324]&nbsp;= 9 &times; (-216) = -1944</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the most appropriate antonym of the given word.<br>Timid</p>",
                    question_hi: "<p>76. Identify the most appropriate antonym of the given word.<br>Timid</p>",
                    options_en: ["<p>Meek</p>", "<p>Anxious</p>", 
                                "<p>Daring</p>", "<p>Spooky</p>"],
                    options_hi: ["<p>Meek</p>", "<p>Anxious</p>",
                                "<p>Daring</p>", "<p>Spooky</p>"],
                    solution_en: "<p>76.(c) <strong>Daring</strong>- willing to take risks<br><strong>Timid</strong>- lacking in courage or confidence.<br><strong>Meek</strong>- quiet, gentle, and submissive.<br><strong>Anxious</strong>- experiencing worry, unease, or nervousness.<br><strong>Spooky</strong>- strange and frightening.</p>",
                    solution_hi: "<p>76.(c) <strong>Daring </strong>(साहसी)- willing to take risks<br><strong>Timid </strong>(कायर)- lacking in courage or confidence.<br><strong>Meek </strong>(विनम्र)- quiet, gentle, and submissive.<br><strong>Anxious </strong>(चिंतित)- experiencing worry, unease, or nervousness.<br><strong>Spooky </strong>(साहसी)- strange and frightening.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate meaning of the given idiom.<br>On the ball</p>",
                    question_hi: "<p>77. Select the most appropriate meaning of the given idiom.<br>On the ball</p>",
                    options_en: ["<p>Unaware of any changes or developments and lazy to react to them</p>", "<p>Aware of any changes or developments but slow to react to them</p>", 
                                "<p>Aware of any changes or developments and quick to react to them</p>", "<p>Aware of any changes or developments but doing nothing</p>"],
                    options_hi: ["<p>Unaware of any changes or developments and lazy to react to them</p>", "<p>Aware of any changes or developments but slow to react to them</p>",
                                "<p>Aware of any changes or developments and quick to react to them</p>", "<p>Aware of any changes or developments but doing nothing</p>"],
                    solution_en: "<p>77.(c) <strong>On the ball-</strong> aware of any changes or developments and quick to react to them.<br>E.g.- The manager is really on the ball. She noticed the issues before they became major problems.</p>",
                    solution_hi: "<p>77.(c) <strong>On the ball-</strong> aware of any changes or developments and quick to react to them./शीघ्र प्रतिक्रिया देने वाला। <br>E.g.- The manager is really on the ball. She noticed the issues before they became major problems.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option to substitute the underlined segment in the given&nbsp;sentence.<br>I have been living in Hyderabad <span style=\"text-decoration: underline;\">since I am born</span> .</p>",
                    question_hi: "<p>78. Select the most appropriate option to substitute the underlined segment in the given&nbsp;sentence.<br>I have been living in Hyderabad <span style=\"text-decoration: underline;\">since I am born</span> .</p>",
                    options_en: ["<p>since I have born</p>", "<p>since I will be born</p>", 
                                "<p>since I born</p>", "<p>since I was born</p>"],
                    options_hi: ["<p>since I have born</p>", "<p>since I will be born</p>",
                                "<p>since I born</p>", "<p>since I was born</p>"],
                    solution_en: "<p>78.(d) since I was born<br>&lsquo;Since&rsquo; is used to indicate the point of time in the past when something began. Therefore, past tense will be used in the clause beginning with &lsquo;since&rsquo;. Hence, &lsquo;since I was born&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(d) since I was born<br>&lsquo;Since&rsquo; का प्रयोग past में उस समय को इंगित करने के लिए किया जाता है जब कुछ शुरू हुआ था। इसलिए, &lsquo;&lsquo;since&rsquo;&rsquo; से शुरू होने वाले clause में past tense का प्रयोग किया जाएगा। अतः, &lsquo;since I was born&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who sells and arranges cut flowers.</p>",
                    question_hi: "<p>79. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who sells and arranges cut flowers.</p>",
                    options_en: ["<p>Horticulturist</p>", "<p>Botanist</p>", 
                                "<p>Gardener</p>", "<p>Florist</p>"],
                    options_hi: ["<p>Horticulturist</p>", "<p>Botanist</p>",
                                "<p>Gardener</p>", "<p>Florist</p>"],
                    solution_en: "<p>79.(d) <strong>Florist</strong>- a person who sells and arranges cut flowers.<br><strong>Horticulturist</strong>- a person who studies or grows garden plants.<br><strong>Botanist</strong>- an expert in or student of the scientific study of plants.<br><strong>Gardener</strong>- someone who works in a garden, growing and taking care of plants.</p>",
                    solution_hi: "<p>79.(d) <strong>Florist </strong>(पुष्प-विक्रेता)- a person who sells and arranges cut flowers.<br><strong>Horticulturist </strong>(उद्यान विशेषज्ञ)- a person who studies or grows garden plants.<br><strong>Botanist </strong>(वनस्पतिशास्त्री)- an expert in or student of the scientific study of plants.<br><strong>Gardener </strong>(माली)- someone who works in a garden, growing and taking care of plants.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>After the concert was over, / they go to a restaurant, / had dinner together / and talked until midnight.</p>",
                    question_hi: "<p>80. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>After the concert was over, / they go to a restaurant, / had dinner together / and talked until midnight.</p>",
                    options_en: ["<p>had dinner together,</p>", "<p>they go to a restaurant,</p>", 
                                "<p>After the concert was over,</p>", "<p>and talked until midnight </p>"],
                    options_hi: ["<p>had dinner together,</p>", "<p>they go to a restaurant,</p>",
                                "<p>After the concert was over,</p>", "<p>and talked until midnight</p>"],
                    solution_en: "<p>80.(b) they go to a restaurant,<br>The given sentence is in the past tense, so it must have the verb in the past form (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>), not the present form (go). Hence, &lsquo;they went to a restaurant&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>80.(b) they go to a restaurant,<br>दिया गया sentence, past tense में है, इसलिए इसमें इसमें verb, present form में नहीं बल्कि past form (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>) में होनी चाहिए। अतः , &lsquo;they went to a restaurant&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that can be used as a one-word substitute for the given group of words.<br>A small group of people who spend their time together and do not welcome other people into that group</p>",
                    question_hi: "<p>81. Select the option that can be used as a one-word substitute for the given group of words.<br>A small group of people who spend their time together and do not welcome other people into that group</p>",
                    options_en: ["<p>Squad</p>", "<p>Clique</p>", 
                                "<p>Employees</p>", "<p>Lobby</p>"],
                    options_hi: ["<p>Squad</p>", "<p>Clique</p>",
                                "<p>Employees</p>", "<p>Lobby</p>"],
                    solution_en: "<p>81.(b) <strong>Clique</strong>- a small group of people who spend their time together and do not welcome other people into that group.<br><strong>Squad</strong>- a small organized group of military personnel.<br><strong>Employees</strong>- someone who is paid to work for someone else.<br><strong>Lobby</strong>- a group of people who try to persuade the government or an official group to do something.</p>",
                    solution_hi: "<p>81.(b) <strong>Clique </strong>(गुट/गिरोह)- a small group of people who spend their time together and do not welcome other people into that group.<br><strong>Squad </strong>(सैन्य दल)- a small organized group of military personnel.<br><strong>Employees </strong>(कर्मचारी)- someone who is paid to work for someone else.<br><strong>Lobby </strong>(सभाकक्ष)- a group of people who try to persuade the government or an official group to do something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. The following sentence contains a word with a spelling error. Rectify the sentence by selecting the correct spelling of the identified word from the given options.<br>He has to be conscious of his public manners as he is a decendent of a reputed family in this region.</p>",
                    question_hi: "<p>82. The following sentence contains a word with a spelling error. Rectify the sentence by selecting the correct spelling of the identified word from the given options.<br>He has to be conscious of his public manners as he is a decendent of a reputed family in this region.</p>",
                    options_en: ["<p>Dicsendent</p>", "<p>Descendant</p>", 
                                "<p>Discendent</p>", "<p>Decendant</p>"],
                    options_hi: ["<p>Dicsendent</p>", "<p>Descendant</p>",
                                "<p>Discendent</p>", "<p>Decendant</p>"],
                    solution_en: "<p>82.(b) Descendant<br>\'Descendant\' is the correct spelling.</p>",
                    solution_hi: "<p>82.(b) Descendant<br>\'Descendant\' सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate synonym of the underlined word in the given sentence.<br>The Morbi bridge mishap has left Gujarat in a <span style=\"text-decoration: underline;\">deplorable</span> state.</p>",
                    question_hi: "<p>83. Select the most appropriate synonym of the underlined word in the given sentence.<br>The Morbi bridge mishap has left Gujarat in a <span style=\"text-decoration: underline;\">deplorable</span> state.</p>",
                    options_en: ["<p>Jubilation</p>", "<p>Despicable</p>", 
                                "<p>Complaining</p>", "<p>Rejoicing</p>"],
                    options_hi: ["<p>Jubilation</p>", "<p>Despicable</p>",
                                "<p>Complaining</p>", "<p>Rejoicing</p>"],
                    solution_en: "<p>83.(b) <strong>Despicable</strong>- deserving hatred and contempt.<br><strong>Deplorable</strong>- shockingly bad in quality.<br><strong>Jubilation</strong>- a feeling of great happiness and triumph.<br><strong>Complaining</strong>- expressing dissatisfaction or annoyance about something.<br><strong>Rejoicing</strong>- feeling or showing great joy or delight.</p>",
                    solution_hi: "<p>83.(b) <strong>Despicable </strong>(घृणित)- deserving hatred and contempt.<br><strong>Deplorable </strong>(निंदनीय)- shockingly bad in quality.<br><strong>Jubilation </strong>(उल्लास)- a feeling of great happiness and triumph.<br><strong>Complaining </strong>(शिकायत करना)- expressing dissatisfaction or annoyance about something.<br><strong>Rejoicing </strong>(आनन्दित होना)- feeling or showing great joy or delight.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate synonym of the underlined word.<br>He was <span style=\"text-decoration: underline;\">trembling</span> with fear.</p>",
                    question_hi: "<p>84. Select the most appropriate synonym of the underlined word.<br>He was <span style=\"text-decoration: underline;\">trembling</span> with fear.</p>",
                    options_en: ["<p>Shivering</p>", "<p>Weeping</p>", 
                                "<p>Running</p>", "<p>Intrepid</p>"],
                    options_hi: ["<p>Shivering</p>", "<p>Weeping</p>",
                                "<p>Running</p>", "<p>Intrepid</p>"],
                    solution_en: "<p>84.(a) <strong>Shivering</strong>- shaking slightly due to cold, fear, or excitement.<br><strong>Trembling</strong>- shaking involuntarily, typically as a result of anxiety or excitement.<br><strong>Intrepid</strong>- extremely brave and showing no fear of dangerous situations.</p>",
                    solution_hi: "<p>84.(a) <strong>Shivering </strong>(कांपना)- shaking slightly due to cold, fear, or excitement.<br><strong>Trembling </strong>(कांपना)- shaking involuntarily, typically as a result of anxiety or excitement.<br><strong>Intrepid </strong>(साहसी)- extremely brave and showing no fear of dangerous situations.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. More children go to school than at any time in the past. But more children today are out of school than any time in the past.<br>B. But it is not enough to blame the high birth rate for this state of affairs.<br>C. Indeed, it can be reasonably argued that continued mass illiteracy is not the result but the cause of the high birth rate.<br>D. There are more literate people in India today than ever before. But there are also more illiterates than ever before.</p>",
                    question_hi: "<p>85. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. More children go to school than at any time in the past. But more children today are out of school than any time in the past.<br>B. But it is not enough to blame the high birth rate for this state of affairs.<br>C. Indeed, it can be reasonably argued that continued mass illiteracy is not the result but the cause of the high birth rate.<br>D. There are more literate people in India today than ever before. But there are also more illiterates than ever before.</p>",
                    options_en: ["<p>ABCD</p>", "<p>CBAD</p>", 
                                "<p>DABC</p>", "<p>BADC</p>"],
                    options_hi: ["<p>ABCD</p>", "<p>CBAD</p>",
                                "<p>DABC</p>", "<p>BADC</p>"],
                    solution_en: "<p>85.(c) <strong>DABC</strong><br>Sentence D will be the starting line as it introduces the main idea of the parajumble, i.e. &lsquo;more number of literate and illiterate people than ever before&rsquo;. And, Sentence A states that more children go to school while at the same time more children are out of school, which is the reason for more number of literates and illiterates. So, A will follow D. Further, Sentence B states that it is not enough to blame the high birth rate for this situation &amp; Sentence C states that continued mass illiteracy is not the result but the cause of the high birth rate. So, C will follow B. Going through the options, option &lsquo;c&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>85.(c) <strong>DABC</strong><br>Sentence D प्रारम्भिक line होगी क्योंकि यह parajumble के मुख्य विचार &lsquo;more number of literate and illiterate people than ever before&rsquo; को प्रस्तुत करता है। और, Sentence A में कहा गया है कि अधिक बच्चे स्कूल जाते हैं जबकि साथ ही अधिक बच्चे स्कूल से बाहर रहते हैं, जो साक्षर (literates) और निरक्षरों(illiterates) की अधिक संख्या का कारण है। इसलिए, D के बाद A आएगा। इसके अलावा, Sentence B बताता है कि इस स्थिति के लिए उच्च जन्म दर (high birth rate) को दोष देना पर्याप्त नहीं है और Sentence C में कहा गया है कि निरंतर व्यापक निरक्षरता (mass illiteracy) उच्च जन्म दर का परिणाम नहीं, बल्कि कारण है। इसलिए, B के बाद C आएगा। अतः options के माध्यम से जाने पर, option &lsquo;c&rsquo; में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate synonym of the bold word in the following sentence.<br>He tried to <strong>alleviate</strong> the sufferings of his neighbours.</p>",
                    question_hi: "<p>86. Select the most appropriate synonym of the bold word in the following sentence.<br>He tried to <strong>alleviate </strong>the sufferings of his neighbours.</p>",
                    options_en: ["<p>Swell</p>", "<p>Add</p>", 
                                "<p>Intensify</p>", "<p>Relieve</p>"],
                    options_hi: ["<p>Swell</p>", "<p>Add</p>",
                                "<p>Intensify</p>", "<p>Relieve</p>"],
                    solution_en: "<p>86.(d) <strong>Relieve</strong>- to free from a burden or distress.<br><strong>Alleviate</strong>- to make something less severe.<br><strong>Swell</strong>- to increase in size or volume.<br><strong>Add</strong>- to join something to another, increasing its size or quantity.<br><strong>Intensify</strong>- to make something stronger or more extreme.</p>",
                    solution_hi: "<p>86.(d) <strong>Relieve </strong>(कार्य मुक्त करना)- to free from a burden or distress.<br><strong>Alleviate </strong>(हल्का करना)- to make something less severe.<br><strong>Swell </strong>(बढ़ाना)- to increase in size or volume.<br><strong>Add </strong>(जोड़ना)- to join something to another, increasing its size or quantity.<br><strong>Intensify </strong>(अधिक मजबूत बनाना)- to make something stronger or more extreme.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>Renewable energy / sources like solar and wind power / will replaced traditional fossil fuels / and help reduce carbon emissions.</p>",
                    question_hi: "<p>87. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>Renewable energy / sources like solar and wind power / will replaced traditional fossil fuels / and help reduce carbon<br>emissions.</p>",
                    options_en: ["<p>and help reduce carbon emissions</p>", "<p>Renewable energy</p>", 
                                "<p>will replaced traditional fossil fuels</p>", "<p>sources like solar and wind power</p>"],
                    options_hi: ["<p>and help reduce carbon emissions</p>", "<p>Renewable energy</p>",
                                "<p>will replaced traditional fossil fuels</p>", "<p>sources like solar and wind power</p>"],
                    solution_en: "<p>87.(c) will replaced traditional fossil fuels<br>&lsquo;Modal verbs&rsquo; are auxiliary verbs(also called helping verbs) like can, will, could, shall, must, would, might, and should. However, after a modal verb, we generally use the first form of the verb (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>). Hence, &lsquo;will replace(<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>) traditional fossil fuels&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>87.(c) will replaced traditional fossil fuels<br>&lsquo;Modal verbs&rsquo; auxiliary verbs (जिन्हें helping verbs भी कहा जाता है) होते हैं जैसे कि can, will, could, should, must, would, might, and should. हालाँकि, modal verb के बाद, आम तौर पर verb के first form (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>) का प्रयोग किया जाता हैं। अतः , &lsquo;will replace (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>) traditional fossil fuels&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Identify from the given options the word which is similar in meaning to the following word.<br>Equivocal</p>",
                    question_hi: "<p>88. Identify from the given options the word which is similar in meaning to the following word.<br>Equivocal</p>",
                    options_en: ["<p>Clear</p>", "<p>Ambiguous</p>", 
                                "<p>Representative</p>", "<p>Balanced</p>"],
                    options_hi: ["<p>Clear</p>", "<p>Ambiguous</p>",
                                "<p>Representative</p>", "<p>Balanced</p>"],
                    solution_en: "<p>88.(b) <strong>Ambiguous</strong>- having or expressing more than one possible meaning.<br><strong>Equivocal</strong>- open to more than one interpretation.<br><strong>Clear</strong>- easy to perceive or understand.<br><strong>Representative</strong>- serving as an example or symbol.<br><strong>Balanced</strong>- evenly distributed or stable.</p>",
                    solution_hi: "<p>88.(b) <strong>Ambiguous </strong>(अनेकार्थक)- having or expressing more than one possible meaning.<br><strong>Equivocal </strong>(संदिग्धार्थक)- open to more than one interpretation.<br><strong>Clear </strong>(स्पष्ट)- easy to perceive or understand.<br><strong>Representative </strong>(प्रतिनिधि)- serving as an example or symbol.<br><strong>Balanced </strong>(संतुलित)- evenly distributed or stable.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate ANTONYM of the underlined word.<br>She was <span style=\"text-decoration: underline;\">elated</span> about her promotion, but her colleague felt quite disheartened.</p>",
                    question_hi: "<p>89. Select the most appropriate ANTONYM of the underlined word.<br>She was <span style=\"text-decoration: underline;\">elated</span> about her promotion, but her colleague felt quite disheartened.</p>",
                    options_en: ["<p>cheerful</p>", "<p>proud</p>", 
                                "<p>discouraged</p>", "<p>delighted</p>"],
                    options_hi: ["<p>cheerful</p>", "<p>proud</p>",
                                "<p>discouraged</p>", "<p>delighted</p>"],
                    solution_en: "<p>89.(c) <strong>Discouraged</strong>- lacking confidence or enthusiasm.<br><strong>Elated</strong>- extremely happy or excited.<br><strong>Cheerful</strong>- noticeably happy or optimistic.<br><strong>Proud</strong>- feeling deep pleasure or satisfaction.<br><strong>Delighted-</strong> greatly pleased or happy.</p>",
                    solution_hi: "<p>89.(c) <strong>Discouraged </strong>(हतोत्साहित)- lacking confidence or enthusiasm.<br><strong>Elated </strong>(प्रसन्न)- extremely happy or excited.<br><strong>Cheerful </strong>(प्रसन्नचित्त) - noticeably happy or optimistic.<br><strong>Proud </strong>(गर्व)- feeling deep pleasure or satisfaction.<br><strong>Delighted </strong>(प्रसन्न)- greatly pleased or happy.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that expresses the given sentence in active voice.<br>The contract was grabbed by the biggest telecom company in India.</p>",
                    question_hi: "<p>90. Select the option that expresses the given sentence in active voice.<br>The contract was grabbed by the biggest telecom company in India.</p>",
                    options_en: ["<p>The biggest telecom company in India grabbed the contract.</p>", "<p>The biggest telecom company in India grabs the contract.</p>", 
                                "<p>The biggest telecom company in India will grab the contract.</p>", "<p>The biggest telecom company in India has grabbed the contract.</p>"],
                    options_hi: ["<p>The biggest telecom company in India grabbed the contract.</p>", "<p>The biggest telecom company in India grabs the contract.</p>",
                                "<p>The biggest telecom company in India will grab the contract.</p>", "<p>The biggest telecom company in India has grabbed the contract.</p>"],
                    solution_en: "<p>90.(a) The biggest telecom company in India grabbed the contract. (Correct)<br>(b) The biggest telecom company in India <span style=\"text-decoration: underline;\">grabs</span> the contract. (Incorrect Tense)<br>(c) The biggest telecom company in India <span style=\"text-decoration: underline;\">will grab</span> the contract. (Incorrect Tense)<br>(d) The biggest telecom company in India <span style=\"text-decoration: underline;\">has grabbed</span> the contract. (Incorrect Tense)</p>",
                    solution_hi: "<p>90.(a) The biggest telecom company in India grabbed the contract. (Correct)<br>(b) The biggest telecom company in India <span style=\"text-decoration: underline;\">grabs</span> the contract. (गलत Tense)<br>(c) The biggest telecom company in India <span style=\"text-decoration: underline;\">will grab</span> the contract. (गलत Tense)<br>(d) The biggest telecom company in India <span style=\"text-decoration: underline;\">has grabbed</span> the contract. (गलत Tense)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. I\'m ________ about whether to accept the job offer or continue freelancing. Both options have their advantages.</p>",
                    question_hi: "<p>91. I\'m _______ about whether to accept the job offer or continue freelancing. Both options have their advantages.</p>",
                    options_en: ["<p>on the fence</p>", "<p>cutting corners</p>", 
                                "<p>left out in cold</p>", "<p>up in arms</p>"],
                    options_hi: ["<p>on the fence</p>", "<p>cutting corners</p>",
                                "<p>left out in cold</p>", "<p>up in arms</p>"],
                    solution_en: "<p>91.(a) <strong>On the fence</strong>- not able to decide something.</p>",
                    solution_hi: "<p>91.(a) <strong>On the fence</strong>- not able to decide something./कुछ निर्णय न ले पाना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the correctly spelt word.</p>",
                    question_hi: "<p>92. Select the correctly spelt word.</p>",
                    options_en: ["<p>Conscensus</p>", "<p>Entrepreneurship</p>", 
                                "<p>Collaegue</p>", "<p>Bizzaire</p>"],
                    options_hi: ["<p>Conscensus</p>", "<p>Entrepreneurship</p>",
                                "<p>Collaegue</p>", "<p>Bizzaire</p>"],
                    solution_en: "<p>92.(b) Entrepreneurship<br>\'Entrepreneurship\' is the correct spelling.</p>",
                    solution_hi: "<p>92.(b) Entrepreneurship<br>\'Entrepreneurship\' सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the option that expresses the given sentence in active voice.<br>By whom was grammar taught to you?</p>",
                    question_hi: "<p>93. Select the option that expresses the given sentence in active voice.<br>By whom was grammar taught to you?</p>",
                    options_en: ["<p>Did he teach you grammar?</p>", "<p>Who had taught you grammar?</p>", 
                                "<p>You were taught grammar by whom?</p>", "<p>Who taught you grammar?</p>"],
                    options_hi: ["<p>Did he teach you grammar?</p>", "<p>Who had taught you grammar?</p>",
                                "<p>You were taught grammar by whom?</p>", "<p>Who taught you grammar?</p>"],
                    solution_en: "<p>93.(d) Who taught you grammar? (Correct)<br>(a) Did he teach you grammar? (Incorrect Sentence Structure)<br>(b) Who <span style=\"text-decoration: underline;\">had taught</span> you grammar? (Incorrect Tense)<br>(c) You were taught grammar by whom? (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>93.(d) Who taught you grammar? (Correct)<br>(a) Did he teach you grammar? (गलत Sentence Structure)<br>(b) Who <span style=\"text-decoration: underline;\">had taught</span> you grammar? (गलत Tense)<br>(c) You were taught grammar by whom? (गलत Sentence Structure)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the option that can be used as a one-word substitute for the given group of words.<br>A place where fruit trees are grown</p>",
                    question_hi: "<p>94. Select the option that can be used as a one-word substitute for the given group of words.<br>A place where fruit trees are grown</p>",
                    options_en: ["<p>Orchard</p>", "<p>Garden</p>", 
                                "<p>Quay</p>", "<p>Museum</p>"],
                    options_hi: ["<p>Orchard</p>", "<p>Garden</p>",
                                "<p>Quay</p>", "<p>Museum</p>"],
                    solution_en: "<p>94.(a) <strong>Orchard</strong>- a place where fruit trees are grown.<br><strong>Garden</strong>- a piece of land, usually near a home, where flowers and other plants are grown.<br><strong>Quay</strong>- a long structure, usually built of stone, where boats can be tied up to take on and off their goods.<br><strong>Museum</strong>- a building in which objects of historical, scientific, artistic, or cultural interest are stored and exhibited.</p>",
                    solution_hi: "<p>94.(a) <strong>Orchard </strong>(फलोद्यान)- a place where fruit trees are grown.<br><strong>Garden </strong>(बगीचा)- a piece of land, usually near a home, where flowers and other plants are grown.<br><strong>Quay </strong>(घाट)- a long structure, usually built of stone, where boats can be tied up to take on and off their goods.<br><strong>Museum </strong>(संग्रहालय)- a building in which objects of historical, scientific, artistic, or cultural interest are stored and exhibited.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the option that can be used as a one-word substitute for the given group of words.<br>A government by the officials</p>",
                    question_hi: "<p>95. Select the option that can be used as a one-word substitute for the given group of words.<br>A government by the officials</p>",
                    options_en: ["<p>Autocracy</p>", "<p>Bureaucracy</p>", 
                                "<p>Monarchy</p>", "<p>Plutocracy</p>"],
                    options_hi: ["<p>Autocracy</p>", "<p>Bureaucracy</p>",
                                "<p>Monarchy</p>", "<p>Plutocracy</p>"],
                    solution_en: "<p>95.(b) <strong>Bureaucracy</strong>- a government by the officials.<br><strong>Autocracy</strong>- a system of government by one person with absolute power.<br><strong>Monarchy</strong>- a form of government with a monarch at the head.<br><strong>Plutocracy</strong>- a system of government in which the richest people in a country rule or have power.</p>",
                    solution_hi: "<p>95.(b) <strong>Bureaucracy </strong>(नौकरशाही)- a government by the officials.<br><strong>Autocracy </strong>(एकतंत्र)- a system of government by one person with absolute power.<br><strong>Monarchy </strong>(राजतंत्र)- a form of government with a monarch at the head.<br><strong>Plutocracy </strong>(धनिकतंत्र)- a system of government in which the richest people in a country rule or have power.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96.<strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (96)______ the Global Wildlife Programme conference. The plan recognises (97)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (98)______ survival and sustainable development. It also emphasises preservation of genetic (99)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (100)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96.<strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (96)______ the Global Wildlife Programme conference. The plan recognises (97)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (98)______ survival and sustainable development. It also emphasises preservation of genetic (99)______and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (100)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: ["<p>at</p>", "<p>for</p>", 
                                "<p>in</p>", "<p>to</p>"],
                    options_hi: ["<p>at</p>", "<p>for</p>",
                                "<p>in</p>", "<p>to</p>"],
                    solution_en: "<p>96.(a) <strong>at</strong><br>&lsquo;At&rsquo; is used to indicate the specific place or an event. Similarly, in the given passage, the Global Wildlife Programme conference is the specific event. Hence, &lsquo;at&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(a) <strong>at</strong><br>&lsquo;At&rsquo; का प्रयोग किसी विशिष्ट स्थान या कार्यक्रम को इंगित करने के लिए किया जाता है। इसी प्रकार, दिए गए passage में, Global Wildlife Programme conference एक विशिष्ट कार्यक्रम है। अतः, &lsquo;at&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (96)______ the Global Wildlife Programme conference. The plan recognises (97)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (98)______ survival and sustainable development. It also emphasises preservation of genetic (99)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (100)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97.<strong> Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (96)______ the Global Wildlife Programme conference. The plan recognises (97)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (98)______ survival and sustainable development. It also emphasises preservation of genetic (99)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (100)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: ["<p>to</p>", "<p>and</p>", 
                                "<p>in</p>", "<p>for</p>"],
                    options_hi: ["<p>to</p>", "<p>and</p>",
                                "<p>in</p>", "<p>for</p>"],
                    solution_en: "<p>97.(b) <strong>and</strong><br>&lsquo;And&rsquo; is used to connect to similar parts of speech. Similarly, in the given passage, &lsquo;and&rsquo; has been used to connect two verbs &lsquo;recognises&rsquo; &amp; &lsquo;addresses&rsquo;. Hence, &lsquo;and&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) <strong>and</strong><br>&lsquo;And&rsquo; का प्रयोग same parts of speech को जोड़ने के लिए किया जाता है। इसी तरह, दिए गए passage में, &lsquo;and&rsquo; का प्रयोग दो verb &lsquo;recognises&rsquo; और &lsquo;addresses&rsquo; को जोड़ने के लिए किया गया है। अतः, &lsquo;and&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (96)______ the Global Wildlife Programme conference. The plan recognises (97)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (98)______ survival and sustainable development. It also emphasises preservation of genetic (99)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (100)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (96)______ the Global Wildlife Programme conference. The plan recognises (97)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (98)______ survival and sustainable development. It also emphasises preservation of genetic (99)______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (100)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: ["<p>group</p>", "<p>human</p>", 
                                "<p>species</p>", "<p>breed</p>"],
                    options_hi: ["<p>group</p>", "<p>human</p>",
                                "<p>species</p>", "<p>breed</p>"],
                    solution_en: "<p>98.(b) <strong>human</strong><br>The given passage states that the plan details the importance of ecosystems for food production, health and other aspects of human survival and sustainable development. Hence, \'human\' is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(b) <strong>human</strong><br>दिए गए passage में कहा गया है कि योजना खाद्य उत्पादन, स्वास्थ्य और मानव अस्तित्व एवं सतत विकास(sustainable development) के अन्य पहलुओं के लिए पारिस्थितिकी तंत्र (ecosystems) के महत्व का विवरण देती है। अतः , \'human\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99.<strong> Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (96)______ the Global Wildlife Programme conference. The plan recognises (97)_______ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (98)______ survival and sustainable development. It also emphasises preservation of genetic (99)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (100)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (96)______ the Global Wildlife Programme conference. The plan recognises (97)_______ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (98)______ survival and sustainable development. It also emphasises preservation of genetic (99)______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (100)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: ["<p>conflict</p>", "<p>diversity</p>", 
                                "<p>focus</p>", "<p>rejection</p>"],
                    options_hi: ["<p>conflict</p>", "<p>diversity</p>",
                                "<p>focus</p>", "<p>rejection</p>"],
                    solution_en: "<p>99.(b) <strong>diversity</strong><br>&lsquo;Diversity&rsquo; means the state of being different from one another. The given passage states that it also emphasises preservation of genetic diversity and sustainable utilisation of species and ecosystems. Hence, &lsquo;diversity&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b) <strong>diversity</strong><br>&lsquo;Diversity&rsquo; का अर्थ है एक दूसरे से अलग होने की अवस्था। दिए गए passage में कहा गया है कि यह आनुवंशिक विविधता (genetic diversity) के संरक्षण और प्रजातियों (species) एवं पारिस्थितिकी तंत्र (ecosystems) के सतत उपयोग पर भी जोर देता है। अतः, &lsquo;diversity&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100.<strong> Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (96)______ the Global Wildlife Programme conference. The plan recognises (97)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (98)______ survival and sustainable development. It also emphasises preservation of genetic (99)______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (100)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze test:</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (96)______ the Global Wildlife Programme conference. The plan recognises (97)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (98)______ survival and sustainable development. It also emphasises preservation of genetic (99)______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (100)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: ["<p>including</p>", "<p>gathering</p>", 
                                "<p>counting</p>", "<p>mixing</p>"],
                    options_hi: ["<p>including</p>", "<p>gathering</p>",
                                "<p>counting</p>", "<p>mixing</p>"],
                    solution_en: "<p>100.(a) <strong>including</strong><br>&lsquo;Including&rsquo; means having something as a part. According to the passage, inland aquatic, coastal and marine ecosystems are also a part of the rehabilitation plan. Hence, &lsquo;including&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(a) <strong>including</strong><br>&lsquo;Including&rsquo; का अर्थ है कोई चीज़ शामिल होना। दिए गए passage के अनुसार, अंतर्देशीय(inland) जलीय, तटीय और समुद्री पारिस्थितिकी तंत्र भी पुनर्वास योजना (rehabilitation plan) का एक हिस्सा हैं। अतः, &lsquo;including&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>