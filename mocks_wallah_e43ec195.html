<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> Which of the following statements is NOT correct regarding the rabi crops? </span></p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Nirmala UI;\">&#2352;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2360;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Southern and southeastern states are important for the production of wheat and other rabi crops.</p>\n", "<p>These crops are harvested in summer from April to June.</p>\n", 
                                "<p>Some of the important rabi crops are wheat, barley, peas, gram and mustard.</p>\n", "<p>Rabi crops are sown in winter from October to December.</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2361;&#2370;</span><span style=\"font-family: Nirmala UI;\">&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2360;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2340;&#2381;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2360;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2352;&#2368;&#2359;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2315;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2381;&#2352;&#2376;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2370;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2361;&#2370;&#2305;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2380;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2360;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2331;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2360;&#2354;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\">.</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2360;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2315;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2340;&#2370;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2360;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2379;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(a)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Option (a) is correct.</strong><span style=\"font-weight: 400;\"> </span><strong>Rabi crops</strong><span style=\"font-weight: 400;\"> - Sown in October to December and harvested in April to June. Example - wheat, barley, peas, gram and mustard. </span><strong>Kharif crops </strong><span style=\"font-weight: 400;\">- Sown in June to July and harvested in September to October. Example - Paddy, maize, bajra, jowar. </span><strong>Zaid crops </strong><span style=\"font-weight: 400;\">- Sown in March and harvested by June end. Example - melon, pepper, tomato.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">.(a) </span><strong>&#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (a) &#2360;&#2361;&#2368; &#2361;&#2376;&#2404; &#2352;&#2348;&#2368; &#2347;&#2360;&#2354;&#2375;&#2306; - </strong><span style=\"font-weight: 400;\">&#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352; &#2360;&#2375; &#2342;&#2367;&#2360;&#2306;&#2348;&#2352; &#2350;&#2375;&#2306; &#2348;&#2379;&#2312; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306; &#2324;&#2352; &#2309;&#2346;&#2381;&#2352;&#2376;&#2354; &#2360;&#2375; &#2332;&#2370;&#2344; &#2350;&#2375;&#2306; &#2325;&#2366;&#2335;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2327;&#2375;&#2361;&#2370;&#2305;, &#2332;&#2380;, &#2350;&#2335;&#2352;, &#2330;&#2344;&#2366; &#2324;&#2352; &#2360;&#2352;&#2360;&#2379;&#2306;&#2404; </span><strong>&#2326;&#2352;&#2368;&#2347; &#2347;&#2360;&#2354;&#2375;&#2306;</strong><span style=\"font-weight: 400;\"> - &#2332;&#2370;&#2344; &#2360;&#2375; &#2332;&#2369;&#2354;&#2366;&#2312; &#2350;&#2375;&#2306; &#2348;&#2379;&#2312; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306; &#2324;&#2352; &#2360;&#2367;&#2340;&#2306;&#2348;&#2352; &#2360;&#2375; &#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352; &#2350;&#2375;&#2306; &#2325;&#2366;&#2335;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2343;&#2366;&#2344;, &#2350;&#2325;&#2381;&#2325;&#2366;, &#2348;&#2366;&#2332;&#2352;&#2366;, &#2332;&#2381;&#2357;&#2366;&#2352;&#2404; </span><strong>&#2332;&#2366;&#2351;&#2342; &#2325;&#2368; &#2347;&#2360;&#2354;&#2375;&#2306; </strong><span style=\"font-weight: 400;\">- &#2350;&#2366;&#2352;&#2381;&#2330; &#2350;&#2375;&#2306; &#2348;&#2379;&#2312; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306; &#2324;&#2352; &#2332;&#2370;&#2344; &#2325;&#2375; &#2309;&#2306;&#2340; &#2340;&#2325; &#2325;&#2366;&#2335;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2326;&#2352;&#2348;&#2370;&#2332;&#2366;, &#2325;&#2366;&#2354;&#2368; &#2350;&#2367;&#2352;&#2381;&#2330;, &#2335;&#2350;&#2366;&#2335;&#2352;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> W</span><span style=\"font-family: Cambria Math;\">hich of the following pairs is correct regarding the East India Company army?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> I. Sawar &mdash; Men on horses</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> II. Musket &mdash; A heavy gun used by infantry soldiers </span></p>\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2312;&#2360;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2369;&#2327;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &mdash; </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2369;&#2337;&#2364;&#2360;&#2357;&#2366;&#2352;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2360;&#2381;&#2325;&#2335;</span><span style=\"font-family: Cambria Math;\"> &ndash; </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2376;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2376;&#2344;&#2367;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2342;&#2370;&#2325;</span></p>\n",
                    options_en: ["<p>Only <span style=\"font-family: Cambria Math;\">I</span></p>\n", "<p>Only <span style=\"font-family: Cambria Math;\">I</span><span style=\"font-family: Cambria Math;\">I</span></p>\n", 
                                "<p>Both <span style=\"font-family: Cambria Math;\">I</span> and <span style=\"font-family: Cambria Math;\">I</span><span style=\"font-family: Cambria Math;\">I</span></p>\n", "<p>Neither <span style=\"font-family: Cambria Math;\">I</span> nor <span style=\"font-family: Cambria Math;\">I</span><span style=\"font-family: Cambria Math;\">I</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> I</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> II</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">I </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> II</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> I </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> II</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2</span><span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><strong>Both l and II.&nbsp; </strong><span style=\"font-weight: 400;\">East India Company was established on 31 December 1600 AD. </span><span style=\"font-weight: 400;\">The massive British corporation was founded under Queen Elizabeth I and rose to exploit overseas trade and become a dominating global player. The EIC was the means by which Britain conducted its imperialistic policies in Asia, and it made millions through its global trade in spices, tea, textiles, and opium. The first English factory of India - Masulipatnam (1611). The first English factory of Bengal: Hugli (1651).</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2</span><span style=\"font-family: Cambria Math;\">.(c) </span><strong>l &#2324;&#2352; II &#2342;&#2379;&#2344;&#2379;&#2306;</strong><span style=\"font-weight: 400;\">&#2404; &#2312;&#2360;&#2381;&#2335; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; 31 &#2342;&#2367;&#2360;&#2306;&#2348;&#2352; 1600 &#2312;. &#2325;&#2379; &#2361;&#2369;&#2312; &#2341;&#2368;&#2404; &#2348;&#2337;&#2364;&#2375; &#2346;&#2376;&#2350;&#2366;&#2344;&#2375; &#2346;&#2352; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2344;&#2367;&#2327;&#2350; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2352;&#2366;&#2344;&#2368; &#2319;&#2354;&#2367;&#2332;&#2366;&#2348;&#2375;&#2341; I &#2325;&#2375; &#2340;&#2361;&#2340; &#2325;&#2368; &#2327;&#2312; &#2341;&#2368; &#2324;&#2352; &#2357;&#2367;&#2342;&#2375;&#2358;&#2368; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2325;&#2366; &#2347;&#2366;&#2351;&#2342;&#2366; &#2313;&#2336;&#2366;&#2344;&#2375; &#2324;&#2352; &#2319;&#2325; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368; &#2348;&#2344;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2349;&#2352;&#2366;&#2404; &#2312;&#2360;&#2381;&#2335; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2325;&#2306;&#2346;&#2344;&#2368; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2381;&#2352;&#2367;&#2335;&#2375;&#2344; &#2344;&#2375; &#2319;&#2358;&#2367;&#2351;&#2366; &#2350;&#2375;&#2306; &#2309;&#2346;&#2344;&#2368; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351;&#2357;&#2366;&#2342;&#2368; &#2344;&#2368;&#2340;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2360;&#2306;&#2330;&#2366;&#2354;&#2344; &#2325;&#2367;&#2351;&#2366; &#2324;&#2352; &#2311;&#2360;&#2344;&#2375; &#2350;&#2360;&#2366;&#2354;&#2379;&#2306;, &#2330;&#2366;&#2351;, &#2325;&#2346;&#2337;&#2364;&#2366; &#2324;&#2352; &#2309;&#2347;&#2368;&#2350; &#2350;&#2375;&#2306; &#2309;&#2346;&#2344;&#2375; &#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2354;&#2366;&#2326;&#2379;&#2306; &#2325;&#2350;&#2366;&#2319;&#2404; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368; &#2347;&#2376;&#2325;&#2381;&#2335;&#2381;&#2352;&#2368; - &#2350;&#2360;&#2370;&#2354;&#2368;&#2346;&#2335;&#2381;&#2335;&#2344;&#2350; (1611)&#2404; &#2348;&#2306;&#2327;&#2366;&#2354; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368; &#2347;&#2376;&#2325;&#2381;&#2335;&#2381;&#2352;&#2368;: &#2361;&#2369;&#2327;&#2354;&#2368; (1651)&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Which of the following is related to the concurrent list of the seventh schedule of the Indian constitution? </span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2340;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2370;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2357;&#2352;&#2381;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Taxes on agricultural income</p>\n", "<p>Tolls</p>\n", 
                                "<p>Factories</p>\n", "<p>Taxes on income other than agricultural income.</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2371;&#2359;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2335;&#2379;&#2354;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2326;&#2366;&#2344;&#2379;&#2306;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2371;&#2359;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2404;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\">(c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Factories. 7th Schedule of Indian Constitution - Union List Subjects</strong><span style=\"font-weight: 400;\"> - Defence, Army, International, Ports, Railways, Highways, Communication etc. </span><strong>State List Subjects</strong><span style=\"font-weight: 400;\"> - Public order, Police, Public health and sanitation, Hospitals and dispensaries etc. </span><strong>Concurrent List Subjects</strong><span style=\"font-weight: 400;\"> - Education, Forest, Trade, unions Marriage, Adoption, Succession etc. The Union list has 100 subjects, the State list has 61 subjects and the Concurrent list has 52 subjects.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\">(c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2325;&#2366;&#2352;&#2326;&#2366;&#2344;&#2379;&#2306;&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2368; 7&#2357;&#2368;&#2306; &#2309;&#2344;&#2369;&#2360;&#2370;&#2330;&#2368;</strong><span style=\"font-weight: 400;\"> - </span><strong>&#2360;&#2306;&#2328; &#2360;&#2370;&#2330;&#2368; &#2357;&#2367;&#2359;&#2351;</strong><span style=\"font-weight: 400;\"> - &#2352;&#2325;&#2381;&#2359;&#2366;, &#2360;&#2375;&#2344;&#2366;, &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;, &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361;, &#2352;&#2375;&#2354;&#2357;&#2375;, &#2352;&#2366;&#2332;&#2350;&#2366;&#2352;&#2381;&#2327;, &#2360;&#2306;&#2330;&#2366;&#2352; &#2310;&#2342;&#2367;&#2404; </span><strong>&#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2370;&#2330;&#2368; &#2357;&#2367;&#2359;&#2351;</strong><span style=\"font-weight: 400;\"> - &#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;, &#2346;&#2369;&#2354;&#2367;&#2360;, &#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325; &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2324;&#2352; &#2360;&#2381;&#2357;&#2330;&#2381;&#2331;&#2340;&#2366;, &#2309;&#2360;&#2381;&#2346;&#2340;&#2366;&#2354; &#2324;&#2352; &#2324;&#2359;&#2343;&#2366;&#2354;&#2351; &#2310;&#2342;&#2367;&#2404; </span><strong>&#2360;&#2350;&#2357;&#2352;&#2381;&#2340;&#2368; &#2360;&#2370;&#2330;&#2368; &#2357;&#2367;&#2359;&#2351;</strong><span style=\"font-weight: 400;\"> - &#2358;&#2367;&#2325;&#2381;&#2359;&#2366;, &#2357;&#2344;, &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;, &#2360;&#2306;&#2328; &#2357;&#2367;&#2357;&#2366;&#2361;, &#2342;&#2340;&#2381;&#2340;&#2325; &#2327;&#2381;&#2352;&#2361;&#2339;, &#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2343;&#2367;&#2325;&#2366;&#2352; &#2310;&#2342;&#2367;&#2404; &#2360;&#2306;&#2328; &#2360;&#2370;&#2330;&#2368; &#2350;&#2375;&#2306; 100 &#2357;&#2367;&#2359;&#2351; &#2361;&#2376;&#2306;, &#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2370;&#2330;&#2368; &#2350;&#2375;&#2306; 61 &#2357;&#2367;&#2359;&#2351; &#2361;&#2376;&#2306; &#2324;&#2352; &#2360;&#2350;&#2357;&#2352;&#2381;&#2340;&#2368; &#2360;&#2370;&#2330;&#2368; &#2350;&#2375;&#2306; 52 &#2357;&#2367;&#2359;&#2351; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\"> As on 1st November 2022, who is the chairperson of SEBI&rsquo;s Market Data Advisory Committee (MDAC)?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2357;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> (SEBI) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2332;&#2364;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2354;&#2366;&#2361;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> (Market Data Advisory Committee (MDAC)) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Suresh N Patel</p>\n", "<p>M.S. Sahoo</p>\n", 
                                "<p>K.V. Kamath</p>\n", "<p>Ajay Tyagi</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2352;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2335;&#2375;&#2354;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2319;&#2350;</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Nirmala UI;\">&#2319;&#2360;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2361;&#2370;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;&#2341;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2332;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2351;&#2366;&#2327;&#2368;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(b)&nbsp;</span><strong>M.S. Sahoo. </strong><span style=\"font-weight: 400;\">The Securities and Exchange Board of India was constituted as a non-statutory body on April 12, 1988 through a resolution of the Government of India. The Securities and Exchange Board of India was established as a statutory body in the year 1992 and the provisions of the Securities and Exchange Board of India Act, 1992 came into force on January 30, 1992. </span><strong>Functions : </strong><span style=\"font-weight: 400;\">To protect the interests of investors in securities and to promote the development of, and to regulate the securities market and for matters connected therewith or incidental thereto.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(b)&nbsp;</span><strong>&#2319;&#2350; &#2319;&#2360; &#2360;&#2366;&#2361;&#2370;&#2404; </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2319;&#2325; &#2360;&#2306;&#2325;&#2354;&#2381;&#2346; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; 12 &#2309;&#2346;&#2381;&#2352;&#2376;&#2354;, 1988 &#2325;&#2379; &#2319;&#2325; &#2327;&#2376;&#2352;-&#2360;&#2366;&#2306;&#2357;&#2367;&#2343;&#2367;&#2325; &#2344;&#2367;&#2325;&#2366;&#2351; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367; &#2324;&#2352; &#2357;&#2367;&#2344;&#2367;&#2350;&#2351; &#2348;&#2379;&#2352;&#2381;&#2337; &#2325;&#2366; &#2327;&#2336;&#2344; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367; &#2324;&#2352; &#2357;&#2367;&#2344;&#2367;&#2350;&#2351; &#2348;&#2379;&#2352;&#2381;&#2337; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2357;&#2352;&#2381;&#2359; 1992 &#2350;&#2375;&#2306; &#2319;&#2325; &#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325; &#2344;&#2367;&#2325;&#2366;&#2351; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2361;&#2369;&#2312; &#2341;&#2368; &#2324;&#2352; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367; &#2324;&#2352; &#2357;&#2367;&#2344;&#2367;&#2350;&#2351; &#2348;&#2379;&#2352;&#2381;&#2337; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 1992 &#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2357;&#2343;&#2366;&#2344; 30 &#2332;&#2344;&#2357;&#2352;&#2368;, 1992 &#2325;&#2379; &#2354;&#2366;&#2327;&#2370; &#2361;&#2369;&#2319;&#2404; </span><strong>&#2325;&#2366;&#2352;&#2381;&#2351;</strong><span style=\"font-weight: 400;\">: &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2344;&#2367;&#2357;&#2375;&#2358;&#2325;&#2379;&#2306; &#2325;&#2375; &#2361;&#2367;&#2340;&#2379;&#2306; &#2325;&#2368; &#2352;&#2325;&#2381;&#2359;&#2366; &#2325;&#2352;&#2344;&#2366; &#2324;&#2352; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367; &#2348;&#2366;&#2332;&#2366;&#2352; &#2325;&#2375; &#2357;&#2367;&#2325;&#2366;&#2360; &#2325;&#2379; &#2348;&#2338;&#2364;&#2366;&#2357;&#2366; &#2342;&#2375;&#2344;&#2375; &#2324;&#2352; &#2313;&#2360;&#2375; &#2357;&#2367;&#2344;&#2367;&#2351;&#2350;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2324;&#2352; &#2313;&#2360;&#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2351;&#2366; &#2346;&#2381;&#2352;&#2366;&#2360;&#2306;&#2327;&#2367;&#2325; &#2350;&#2366;&#2350;&#2354;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">An isotope of which of the following is used in the treatment of cancer?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2360;&#2381;&#2341;&#2366;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> ( isotope ) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2306;&#2360;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Cobalt</p>\n", "<p>Aluminum</p>\n", 
                                "<p>Nickel</p>\n", "<p>Iron</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2348;&#2366;&#2354;&#2381;&#2335;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2319;&#2354;&#2381;&#2351;&#2370;&#2350;&#2368;&#2344;&#2367;&#2351;&#2350;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2325;&#2375;&#2354;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2361;&#2366;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\">(a) </span><strong>Cobalt. </strong><span style=\"font-weight: 400;\">Cobalt (27) is used in the treatment of cancer. </span><strong>&nbsp;Isotopes -</strong><span style=\"font-weight: 400;\"> Same atomic number but different mass numbers. </span><strong>Example </strong><span style=\"font-weight: 400;\">- Isotopes of hydrogen - protium <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>1</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts></math></span><span style=\"font-weight: 400;\">, deuterium <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>2</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts><mo>&nbsp;</mo></math></span><span style=\"font-weight: 400;\">&nbsp;or&nbsp; and&nbsp; tritium <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>3</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts></math></span><span style=\"font-weight: 400;\">&nbsp;. </span><strong>Isobars </strong><span style=\"font-weight: 400;\">are atoms of different elements with different atomic numbers but have the same mass number. </span><strong>Example </strong><span style=\"font-weight: 400;\">of a pair of isobar i.e.,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi>Ca</mi><mn>20</mn><none></none><mprescripts></mprescripts><none></none><mn>40</mn></mmultiscripts><mo>&nbsp;</mo><mi>and</mi><mo>&nbsp;</mo><mmultiscripts><mi>Ar</mi><mn>18</mn><none></none><mprescripts></mprescripts><none></none><mn>40</mn></mmultiscripts></math></span><span style=\"font-weight: 400;\">.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\">(a) </span><strong><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2348;&#2366;&#2354;&#2381;&#2335;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2348;&#2366;&#2354;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> 27 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2306;&#2360;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2354;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2360;&#2381;&#2341;&#2366;&#2344;&#2367;&#2325;</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2350;&#2366;&#2339;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2360;&#2381;&#2341;&#2366;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;&#2335;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>1</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts></math><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2381;&#2351;&#2370;&#2335;&#2375;&#2352;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>2</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2367;&#2335;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi mathvariant=\"normal\">H</mi><none></none><mn>3</mn><mprescripts></mprescripts><mn>1</mn><none></none></mmultiscripts></math><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2349;&#2366;&#2352;&#2367;&#2325;</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2340;&#2381;&#2357;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2350;&#2366;&#2339;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2350;&#2366;&#2339;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2349;&#2366;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2369;&#2327;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2366;&#2340;&#2381;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mmultiscripts><mi>Ca</mi><mn>20</mn><none></none><mprescripts></mprescripts><none></none><mn>40</mn></mmultiscripts></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mmultiscripts><mi>Ar</mi><mn>18</mn><none></none><mprescripts></mprescripts><none></none><mn>40</mn></mmultiscripts></math><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> How many non-major (minor) ports are there in India as on 31st October 2022?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> 31 </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2335;</span><span style=\"font-family: Nirmala UI;\">&#2370;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2376;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2354;&#2328;&#2369;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>154</p>\n", "<p>168</p>\n", 
                                "<p>200</p>\n", "<p>246</p>\n"],
                    options_hi: ["<p>154</p>\n", "<p>168</p>\n",
                                "<p>200</p>\n", "<p>246</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\">(c)<strong> 200.&nbsp;</strong></span><span style=\"font-weight: 400;\">India has 13 </span><strong>major ports</strong><span style=\"font-weight: 400;\"> and more than 200 </span><strong>notified minor </strong><span style=\"font-weight: 400;\">and intermediate ports. </span><strong>Kolkata Port </strong><span style=\"font-weight: 400;\">is the only riverine port in India. </span><strong>Mumbai Port</strong><span style=\"font-weight: 400;\"> is the biggest port in India in terms of size and shipping traffic.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\">(c)&nbsp;</span><strong>&nbsp;200 </strong><span style=\"font-weight: 400;\">&#2404;</span><strong>&nbsp; </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; </span><strong>13 &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; </strong><span style=\"font-weight: 400;\">&#2324;&#2352;</span><strong> 200</strong><span style=\"font-weight: 400;\"> &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2309;&#2343;&#2367;&#2360;&#2370;&#2330;&#2367;&#2340; &#2331;&#2379;&#2335;&#2375; &#2324;&#2352; &#2350;&#2343;&#2381;&#2351;&#2357;&#2352;&#2381;&#2340;&#2368; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; &#2361;&#2376;&#2306;&#2404; &#2325;&#2379;&#2354;&#2325;&#2366;&#2340;&#2366; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; </span><strong>&#2319;&#2325;&#2350;&#2366;&#2340;&#2381;&#2352; </strong><span style=\"font-weight: 400;\">&#2344;&#2342;&#2368; &#2325;&#2366; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; &#2361;&#2376;&#2404; &#2350;&#2369;&#2306;&#2348;&#2312; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; </span><strong>&#2310;&#2325;&#2366;&#2352; </strong><span style=\"font-weight: 400;\">&#2324;&#2352; </span><strong>&#2358;&#2367;&#2346;&#2367;&#2306;&#2327; </strong><span style=\"font-weight: 400;\">&#2351;&#2366;&#2340;&#2366;&#2351;&#2366;&#2340; &#2325;&#2375; &#2350;&#2366;&#2350;&#2354;&#2375; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2348;&#2306;&#2342;&#2352;&#2327;&#2366;&#2361; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Who won the best actress award in the NEXA International Indian Film Academy Awards 2022? </span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">NEXA </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2358;&#2344;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2354;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;&#2375;&#2337;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2366;&#2352;&#2381;&#2337;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2367;&#2344;&#2375;&#2340;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Kriti Sanon</p>\n", "<p>Jhanvi Kapoor</p>\n", 
                                "<p>Alia Bhatt</p>\n", "<p>Deepika Padukone</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2344;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2361;&#2381;&#2344;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2346;&#2370;&#2352;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2335;&#2381;&#2335;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;&#2346;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2342;&#2369;&#2325;&#2379;&#2339;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>Kriti Sanon. Nexa iifa awards 2022&nbsp; : -&nbsp; Best Picture -</strong><span style=\"font-weight: 400;\"> Shershah, </span><strong>&nbsp;Best Direction -</strong><span style=\"font-weight: 400;\"> Vishnuvardhan (Shershaah),</span><strong>&nbsp; Best Actor - </strong><span style=\"font-weight: 400;\">Vicky Kaushal (Sardar Udham),</span><strong>&nbsp; Best Actress - </strong><span style=\"font-weight: 400;\">Kriti Sanon (Mimi), </span><strong>&nbsp;Best Male Playback Singer -</strong><span style=\"font-weight: 400;\"> Jubin Nautiyal ( \"Rattan Lamiyan\"), &nbsp; </span><strong>Best Female Playback Singer -</strong><span style=\"font-weight: 400;\"> Asees Kaur.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>&#2325;&#2371;&#2340;&#2367; &#2360;&#2375;&#2344;&#2344;&#2404; </strong><span style=\"font-weight: 400;\">&#2344;&#2375;&#2325;&#2381;&#2360;&#2366;(Nexa ) IIFA&#2309;&#2357;&#2366;&#2352;&#2381;&#2337;&#2381;&#2360; 2022: - </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2346;&#2367;&#2325;&#2381;&#2330;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2358;&#2375;&#2352;&#2358;&#2366;&#2361;, </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2337;&#2366;&#2351;&#2352;&#2375;&#2325;&#2381;&#2358;&#2344;</strong><span style=\"font-weight: 400;\"> - &#2357;&#2367;&#2359;&#2381;&#2339;&#2369;&#2357;&#2352;&#2381;&#2343;&#2344; (&#2358;&#2375;&#2352;&#2358;&#2366;&#2361;), </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2309;&#2349;&#2367;&#2344;&#2375;&#2340;&#2366;</strong><span style=\"font-weight: 400;\"> - &#2357;&#2367;&#2325;&#2381;&#2325;&#2368; &#2325;&#2380;&#2358;&#2354; (&#2360;&#2352;&#2342;&#2366;&#2352; &#2313;&#2343;&#2350;), </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2309;&#2349;&#2367;&#2344;&#2375;&#2340;&#2381;&#2352;&#2368;</strong><span style=\"font-weight: 400;\"> - &#2325;&#2371;&#2340;&#2367; &#2360;&#2375;&#2344;&#2344; (&#2350;&#2367;&#2350;&#2368;), </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2350;&#2375;&#2354; &#2346;&#2381;&#2354;&#2375;&#2348;&#2376;&#2325; &#2360;&#2367;&#2306;&#2327;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2332;&#2369;&#2348;&#2367;&#2344; &#2344;&#2380;&#2335;&#2367;&#2351;&#2366;&#2354; (\"&#2352;&#2340;&#2344; &#2354;&#2366;&#2350;&#2367;&#2351;&#2366;&#2344;\" ), </span><strong>&#2348;&#2375;&#2360;&#2381;&#2335; &#2347;&#2368;&#2350;&#2375;&#2354; &#2346;&#2381;&#2354;&#2375;&#2348;&#2376;&#2325; &#2360;&#2367;&#2306;&#2327;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2309;&#2360;&#2368;&#2360; &#2325;&#2380;&#2352;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> Indian Army&rsquo;s 1st women combat aviator is ___________ . </span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2337;&#2364;&#2366;&#2325;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2350;&#2366;&#2344;&#2357;&#2366;&#2361;&#2325;</span><span style=\"font-family: Cambria Math;\"> (combat aviator) _________ </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>Punita Arora</p>\n", "<p>Divya Ajit Kumar</p>\n", 
                                "<p>Abhilasha Barak</p>\n", "<p>Priya Semwal</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2344;&#2368;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2379;&#2337;&#2364;&#2366;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2357;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2350;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2367;&#2354;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2325;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2350;&#2357;&#2366;&#2354;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\">(c)&nbsp;</span><strong>Abhilasha Barak.&nbsp; </strong><span style=\"font-weight: 400;\">The Army Aviation Corps is a form of the Army that was formed in November 1986.&nbsp; </span><strong>Punita Arora</strong><span style=\"font-weight: 400;\"> was the first woman Lieutenant General of the Indian Army. She held the ranks of Lieutenant General in the Indian Army and Surgeon Vice Admiral in the Navy.</span><strong> Divya Ajit Kumar</strong><span style=\"font-weight: 400;\"> is the first woman to be conferred by the Army with the Sword of Honour.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\">(c)&nbsp;</span><strong>&#2309;&#2349;&#2367;&#2354;&#2366;&#2359;&#2366; &#2348;&#2352;&#2366;&#2325;&#2404; </strong><span style=\"font-weight: 400;\">&#2310;&#2352;&#2381;&#2350;&#2368; &#2319;&#2357;&#2367;&#2319;&#2358;&#2344; &#2325;&#2377;&#2352;&#2381;&#2346;&#2381;&#2360; &#2360;&#2375;&#2344;&#2366; &#2325;&#2366; &#2319;&#2325; &#2352;&#2370;&#2346; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2327;&#2336;&#2344; &#2344;&#2357;&#2306;&#2348;&#2352; 1986 &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404;&nbsp; </span><strong>&#2346;&#2369;&#2344;&#2368;&#2340;&#2366; &#2309;&#2352;&#2379;&#2337;&#2364;&#2366; </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2375;&#2344;&#2366; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2350;&#2361;&#2367;&#2354;&#2366; &#2354;&#2375;&#2347;&#2381;&#2335;&#2367;&#2344;&#2375;&#2306;&#2335; &#2332;&#2344;&#2352;&#2354; &#2341;&#2368;&#2306;&#2404; &#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2375;&#2344;&#2366; &#2350;&#2375;&#2306; &#2354;&#2375;&#2347;&#2381;&#2335;&#2367;&#2344;&#2375;&#2306;&#2335; &#2332;&#2344;&#2352;&#2354; &#2324;&#2352; &#2344;&#2380;&#2360;&#2375;&#2344;&#2366; &#2350;&#2375;&#2306; &#2360;&#2352;&#2381;&#2332;&#2344; &#2357;&#2366;&#2311;&#2360; &#2319;&#2337;&#2350;&#2367;&#2352;&#2354; &#2325;&#2366; &#2346;&#2342; &#2360;&#2306;&#2349;&#2366;&#2354;&#2366;&#2404; </span><strong>&#2342;&#2367;&#2357;&#2381;&#2351;&#2366; &#2309;&#2332;&#2368;&#2340; &#2325;&#2369;&#2350;&#2366;&#2352; </strong><span style=\"font-weight: 400;\">&#2360;&#2375;&#2344;&#2366; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2360;&#2381;&#2357;&#2377;&#2352;&#2381;&#2337; &#2321;&#2347; &#2321;&#2344;&#2352; &#2360;&#2375; &#2360;&#2350;&#2381;&#2350;&#2366;&#2344;&#2367;&#2340; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2346;&#2361;&#2354;&#2368; &#2350;&#2361;&#2367;&#2354;&#2366; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Raja Parba, als</span><span style=\"font-family: Cambria Math;\">o known as Mithuna Sankranti, is a ____________-day-long festival of womanhood celebrated in Odisha, India. </span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2348;&#2366;</span><span style=\"font-family: Cambria Math;\"> (Raja Parba), </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2341;&#2369;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2337;&#2364;&#2368;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;</span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> ______________ </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2352;&#2368;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2351;&#2379;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>five</p>\n", "<p>six</p>\n", 
                                "<p>four</p>\n", "<p>three</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2305;&#2330;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2331;&#2361;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2352;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2344;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>three.</strong><span style=\"font-weight: 400;\"> </span><strong>&nbsp;Raja Parba</strong><strong> </strong><strong>festival </strong><strong>(Odisha)</strong><strong> - </strong><span style=\"font-weight: 400;\">The first day is called Pahili Raja, the second day is Mithun Sankranti, the third day is called Bhoodah or Basi Raja.&nbsp; </span><strong>Other festivals of Odisha - </strong><span style=\"font-weight: 400;\">Ratha Jatra, Magha Saptami, Makara Mela, Chhau festival, Puri Beach festival, Naukhai, Chatar Jatra, Durga Puja, Kalinga Mahotsav, Chandan Yatra,Konark Dance festival, Mahabisuva Sankranti &#2404;&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>&#2340;&#2368;&#2344;&#2404; &#2352;&#2366;&#2332;&#2366; &#2346;&#2352;&#2348;&#2366; &#2351;&#2366;&nbsp; &#2352;&#2332; &#2346;&#2352;&#2381;&#2357; &#2313;&#2340;&#2381;&#2360;&#2357;</strong><span style=\"font-weight: 400;\"> </span><strong>(&#2323;&#2337;&#2367;&#2358;&#2366;)</strong><span style=\"font-weight: 400;\"> - &#2346;&#2361;&#2354;&#2375; &#2342;&#2367;&#2344; &#2325;&#2379; &#2346;&#2361;&#2367;&#2354;&#2368; &#2352;&#2332; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2342;&#2370;&#2360;&#2352;&#2375; &#2342;&#2367;&#2344; &#2325;&#2379; &#2350;&#2367;&#2341;&#2369;&#2344; &#2360;&#2306;&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367;, &#2340;&#2368;&#2360;&#2352;&#2375; &#2342;&#2367;&#2344; &#2325;&#2379; &#2349;&#2370;&#2342;&#2366;&#2361; &#2351;&#2366; &#2348;&#2360;&#2368; &#2352;&#2366;&#2332;&#2366; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2323;&#2337;&#2367;&#2358;&#2366; &#2325;&#2375; &#2309;&#2344;&#2381;&#2351; &#2340;&#2381;&#2351;&#2380;&#2361;&#2366;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2352;&#2341; &#2332;&#2366;&#2340;&#2381;&#2352;&#2366;, &#2350;&#2366;&#2328; &#2360;&#2346;&#2381;&#2340;&#2350;&#2368;, &#2350;&#2325;&#2352; &#2350;&#2375;&#2354;&#2366;, &#2331;&#2314; &#2313;&#2340;&#2381;&#2360;&#2357;, &#2346;&#2369;&#2352;&#2368; &#2348;&#2368;&#2330; &#2313;&#2340;&#2381;&#2360;&#2357;, &#2344;&#2380;&#2326;&#2366;&#2312;, &#2330;&#2340;&#2352; &#2332;&#2366;&#2340;&#2381;&#2352;&#2366;, &#2342;&#2369;&#2352;&#2381;&#2327;&#2366; &#2346;&#2370;&#2332;&#2366;, &#2325;&#2354;&#2367;&#2306;&#2327; &#2350;&#2361;&#2379;&#2340;&#2381;&#2360;&#2357;, &#2330;&#2306;&#2342;&#2344; &#2351;&#2366;&#2340;&#2381;&#2352;&#2366;, &#2325;&#2379;&#2339;&#2366;&#2352;&#2381;&#2325; &#2344;&#2371;&#2340;&#2381;&#2351; &#2313;&#2340;&#2381;&#2360;&#2357;, &#2350;&#2361;&#2366;&#2348;&#2367;&#2360;&#2369;&#2357;&#2366; &#2360;&#2306;&#2325;&#2381;&#2352;&#2366;&#2306;&#2340;&#2367; &#2404;&nbsp;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> Which of the following statements is correct regarding inertia? </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. Inertia is the natural tendency of an object to resist a change in its state of motion or of rest. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. The mass of an object is a measure of its inertia. </span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2337;&#2364;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I .</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2337;&#2364;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2352;&#2379;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2337;&#2364;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Only <span style=\"font-family: Cambria Math;\">I</span></p>\n", "<p>Neither <span style=\"font-family: Cambria Math;\">I</span> nor <span style=\"font-family: Cambria Math;\">I</span><span style=\"font-family: Cambria Math;\">I</span></p>\n", 
                                "<p>Only <span style=\"font-family: Cambria Math;\">I</span><span style=\"font-family: Cambria Math;\">I</span></p>\n", "<p>Both <span style=\"font-family: Cambria Math;\">I</span> and <span style=\"font-family: Cambria Math;\">I</span><span style=\"font-family: Cambria Math;\">I</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> I</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> I </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> II</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> II</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\"><span style=\"font-family: Cambria Math;\">I</span>&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> II</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>Both l and Il. Newton\'s first law (Law of Inertia) - </strong><span style=\"font-weight: 400;\">Inertia is the natural tendency of an object to resist a change in its state of motion or of rest. The mass of an object is a measure of its inertia. </span><strong>Second law of motion -</strong><span style=\"font-weight: 400;\"> Force is equal to the rate of change of momentum. When the mass is constant, the force (F) on the body equals mass (m) times acceleration (a) i.e, F = ma. </span><strong>Third law of motion -</strong><span style=\"font-weight: 400;\"> When two objects interact, they apply forces to each other of equal magnitude and opposite direction.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\">(d) </span><strong>l&nbsp; &#2324;&#2352; &#2405; &#2342;&#2379;&#2344;&#2379;&#2306; </strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2344;&#2381;&#2351;&#2370;&#2335;&#2344; &#2325;&#2366; &#2346;&#2361;&#2354;&#2366; &#2344;&#2367;&#2351;&#2350; (&#2332;&#2337;&#2364;&#2340;&#2381;&#2357; &#2325;&#2366; &#2344;&#2367;&#2351;&#2350;) - </strong><span style=\"font-weight: 400;\">&#2332;&#2337;&#2364;&#2340;&#2366; &#2325;&#2367;&#2360;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2368; &#2309;&#2346;&#2344;&#2368; &#2327;&#2340;&#2367; &#2351;&#2366; &#2310;&#2352;&#2366;&#2350; &#2325;&#2368; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2350;&#2375;&#2306; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; &#2325;&#2366; &#2357;&#2367;&#2352;&#2379;&#2343; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325; &#2346;&#2381;&#2352;&#2357;&#2371;&#2340;&#2381;&#2340;&#2367; &#2361;&#2376;&#2404; &#2325;&#2367;&#2360;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344; &#2313;&#2360;&#2325;&#2375; &#2332;&#2337;&#2364;&#2340;&#2381;&#2357; &#2325;&#2366; &#2350;&#2366;&#2346; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2327;&#2340;&#2367; &#2325;&#2366; &#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351; &#2344;&#2367;&#2351;&#2350; </strong><span style=\"font-weight: 400;\">- &#2348;&#2354; &#2360;&#2306;&#2357;&#2375;&#2327; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; &#2325;&#2368; &#2342;&#2352; &#2325;&#2375; &#2348;&#2352;&#2366;&#2348;&#2352; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2332;&#2348; &#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344; &#2360;&#2381;&#2341;&#2367;&#2352; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;, &#2340;&#2379; &#2357;&#2360;&#2381;&#2340;&#2369; &#2346;&#2352; &#2348;&#2354; (F) ,&#2342;&#2381;&#2352;&#2357;&#2381;&#2351;&#2350;&#2366;&#2344; (m) &#2340;&#2341;&#2366;&nbsp; &#2340;&#2381;&#2357;&#2352;&#2339; (a)&#2325;&#2375; &#2327;&#2369;&#2339;&#2344;&#2347;&#2354;&nbsp; &#2325;&#2375; &#2348;&#2352;&#2366;&#2348;&#2352; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376; &#2309;&#2352;&#2381;&#2341;&#2366;&#2340;,</span><strong> F = ma</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2327;&#2340;&#2367; &#2325;&#2366; &#2340;&#2368;&#2360;&#2352;&#2366; &#2344;&#2367;&#2351;&#2350;</strong><span style=\"font-weight: 400;\"> - &#2332;&#2348; &#2342;&#2379; &#2357;&#2360;&#2381;&#2340;&#2369;&#2319;&#2305; &#2346;&#2352;&#2360;&#2381;&#2346;&#2352; &#2325;&#2381;&#2352;&#2367;&#2351;&#2366; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2306;, &#2340;&#2379; &#2357;&#2375; &#2319;&#2325; &#2342;&#2370;&#2360;&#2352;&#2375; &#2346;&#2352; &#2360;&#2350;&#2366;&#2344; &#2346;&#2352;&#2367;&#2350;&#2366;&#2339; &#2324;&#2352; &#2357;&#2367;&#2346;&#2352;&#2368;&#2340; &#2342;&#2367;&#2358;&#2366; &#2350;&#2375;&#2306; &#2348;&#2354; &#2354;&#2327;&#2366;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">How many gold medals did Maharashtra win in the fourth edition of Khelo India Youth Games? </span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Nirmala UI;\">&#2326;&#2375;&#2354;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2350;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>41</p>\n", "<p>38</p>\n", 
                                "<p>54</p>\n", "<p>45</p>\n"],
                    options_hi: ["<p>41</p>\n", "<p>38</p>\n",
                                "<p>54</p>\n", "<p>45</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11.</span><span style=\"font-family: Cambria Math;\">(d) <strong>45. </strong><span style=\"font-weight: 400;\">The fifth edition of Khelo India Youth Games 2023 was held in Madhya Pradesh from January 30 to February 11, 2023. Maharashtra continues to top the KIYG 2023 medal tally with 56 gold medals, 55 silver and 50 bronze. </span><strong>Khelo India</strong><span style=\"font-weight: 400;\">, which translates to &lsquo;Let&rsquo;s play India&rsquo;, was proposed by the government of India in </span><strong>2017 </strong><span style=\"font-weight: 400;\">to revive India&rsquo;s sporting culture by engaging with children at the grassroots level. The initiative also focused on building better sporting infrastructure and academies across the country for various sports.</span></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11.</span><span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>45 </strong><span style=\"font-weight: 400;\">&#2404;</span><strong>&nbsp; </strong><span style=\"font-weight: 400;\">&#2326;&#2375;&#2354;&#2379; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2351;&#2370;&#2341; &#2327;&#2375;&#2350;&#2381;&#2360; 2023 &#2325;&#2366; &#2346;&#2366;&#2306;&#2330;&#2357;&#2366;&#2306; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339; &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; &#2350;&#2375;&#2306; 30 &#2332;&#2344;&#2357;&#2352;&#2368; &#2360;&#2375; 11 &#2347;&#2352;&#2357;&#2352;&#2368;, 2023 &#2340;&#2325; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; KIYG 2023 &#2346;&#2342;&#2325; &#2340;&#2366;&#2354;&#2367;&#2325;&#2366; &#2350;&#2375;&#2306; 56 &#2360;&#2381;&#2357;&#2352;&#2381;&#2339; &#2346;&#2342;&#2325;, 55 &#2352;&#2332;&#2340; &#2324;&#2352; 50 &#2325;&#2366;&#2306;&#2360;&#2381;&#2351; &#2325;&#2375; &#2360;&#2366;&#2341; &#2358;&#2368;&#2352;&#2381;&#2359; &#2346;&#2352; &#2348;&#2344;&#2366; &#2361;&#2369;&#2310; &#2361;&#2376;&#2404; </span><strong>&#2326;&#2375;&#2354;&#2379; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</strong><span style=\"font-weight: 400;\">, &#2332;&#2367;&#2360;&#2325;&#2366; &#2309;&#2344;&#2369;&#2357;&#2366;&#2342; \'&#2354;&#2375;&#2335;&#2381;&#2360; &#2346;&#2381;&#2354;&#2375; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;\' &#2361;&#2376;, &#2325;&#2379; </span><strong>2017</strong><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2332;&#2350;&#2368;&#2344;&#2368; &#2360;&#2381;&#2340;&#2352; &#2346;&#2352; &#2348;&#2330;&#2381;&#2330;&#2379;&#2306; &#2325;&#2375; &#2360;&#2366;&#2341; &#2332;&#2369;&#2337;&#2364;&#2325;&#2352; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2326;&#2375;&#2354; &#2360;&#2306;&#2360;&#2381;&#2325;&#2371;&#2340;&#2367; &#2325;&#2379; &#2346;&#2369;&#2344;&#2352;&#2381;&#2332;&#2368;&#2357;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2311;&#2360; &#2346;&#2361;&#2354; &#2344;&#2375; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2326;&#2375;&#2354;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2342;&#2375;&#2358; &#2349;&#2352; &#2350;&#2375;&#2306; &#2348;&#2375;&#2361;&#2340;&#2352; &#2326;&#2375;&#2354; &#2310;&#2343;&#2366;&#2352;&#2349;&#2370;&#2340; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2324;&#2352; &#2309;&#2325;&#2366;&#2342;&#2350;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2346;&#2352; &#2349;&#2368; &#2343;&#2381;&#2351;&#2366;&#2344; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">Which of the following is NOT a part of western coastal plains? </span></p>\n",
                    question_hi: "<p>12. <span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2335;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Konkan</p>\n", "<p>Coromandel Coast</p>\n", 
                                "<p>Kannad Plain</p>\n", "<p>Malabar coast</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2306;&#2325;&#2339;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2352;&#2379;&#2350;&#2306;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2335;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2344;&#2381;&#2344;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2335;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2342;&#2366;&#2344;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;&#2366;&#2348;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2335;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>Coromandel Coast. Part of western coastal plains :-</strong><span style=\"font-weight: 400;\"> It includes the states of Gujarat, Maharashtra, Goa, Karnataka and Kerala .The Northern part of the coast is called the </span><strong>Konkan </strong><span style=\"font-weight: 400;\">(Mumbai to Goa), the central stretch is called the </span><strong>Kanara </strong><span style=\"font-weight: 400;\">or the \"</span><strong>Karavali</strong><span style=\"font-weight: 400;\">\", while the southern stretch is referred to as the </span><strong>Malabar </strong><span style=\"font-weight: 400;\">Coast.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>&#2325;&#2379;&#2352;&#2379;&#2350;&#2306;&#2337;&#2354; &#2340;&#2335;&#2404; &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2340;&#2335;&#2368;&#2351; &#2350;&#2376;&#2342;&#2366;&#2344;&#2379;&#2306; &#2325;&#2366; &#2361;&#2367;&#2360;&#2381;&#2360;&#2366; :-</strong><span style=\"font-weight: 400;\"> &#2311;&#2360;&#2350;&#2375;&#2306; &#2327;&#2369;&#2332;&#2352;&#2366;&#2340;, &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2327;&#2379;&#2357;&#2366;, &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; &#2324;&#2352; &#2325;&#2375;&#2352;&#2354; &#2352;&#2366;&#2332;&#2381;&#2351; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2306;&#2404; &#2340;&#2335; &#2325;&#2375; &#2313;&#2340;&#2381;&#2340;&#2352;&#2368; &#2349;&#2366;&#2327; &#2325;&#2379; </span><strong>&#2325;&#2379;&#2306;&#2325;&#2339; </strong><span style=\"font-weight: 400;\">(&#2350;&#2369;&#2306;&#2348;&#2312; &#2360;&#2375; &#2327;&#2379;&#2357;&#2366;) &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351; &#2326;&#2306;&#2337; &#2325;&#2379; </span><strong>&#2325;&#2375;&#2344;&#2352;&#2366; </strong><span style=\"font-weight: 400;\">&#2351;&#2366; \"</span><strong>&#2325;&#2352;&#2366;&#2357;&#2354;&#2368;</strong><span style=\"font-weight: 400;\">\" &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; \", &#2332;&#2348;&#2325;&#2367; &#2342;&#2325;&#2381;&#2359;&#2367;&#2339;&#2368; &#2326;&#2306;&#2337; &#2325;&#2379; </span><strong>&#2350;&#2366;&#2354;&#2366;&#2348;&#2366;&#2352; </strong><span style=\"font-weight: 400;\">&#2340;&#2335; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">Pick the odd one out in the Central Processing Unit (CPU) of a computer. </span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2381;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2306;&#2335;&#2381;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2360;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> (CPU) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2342;&#2352;&#2381;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2369;&#2344;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>ALU and Control Unit both</p>\n", "<p>Control Unit</p>\n", 
                                "<p>ALU (Arithmetic Logic Unit)</p>\n", "<p>Output Unit</p>\n"],
                    options_hi: ["<p>ALU <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">ALU (&#2309;&#2352;&#2367;&#2341;&#2350;&#2376;&#2335;&#2367;&#2325; &#2354;&#2377;&#2332;&#2367;&#2325; &#2351;&#2370;&#2344;&#2367;&#2335;)</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2313;&#2335;&#2346;&#2369;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2367;&#2335;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>Output Unit. The Central Processing Unit (CPU) </strong><span style=\"font-weight: 400;\">is considered as the brain of the computer.</span><strong> </strong><span style=\"font-weight: 400;\">The CPU has two units:-</span><strong> Arithmetic Logic Unit (ALU)</strong><span style=\"font-weight: 400;\"> performs all the tasks of logical and arithmetic operations. CU stands for </span><strong>Control Unit </strong><span style=\"font-weight: 400;\">which works to control all the parts of the computer and give proper instructions.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\">(d)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2310;&#2313;&#2335;&#2346;&#2369;&#2335; &#2351;&#2370;&#2344;&#2367;&#2335;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2360;&#2375;&#2306;&#2335;&#2381;&#2352;&#2354; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327; &#2351;&#2370;&#2344;&#2367;&#2335;(CPU) </strong><span style=\"font-weight: 400;\">&#2325;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2366; &#2350;&#2360;&#2381;&#2340;&#2367;&#2359;&#2381;&#2325; &#2350;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; CPU &#2325;&#2368; &#2342;&#2379; Units &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2306;:- </span><strong>Arithmetic Logic Unit (ALU) </strong><span style=\"font-weight: 400;\">&#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325; &#2324;&#2352; &#2309;&#2306;&#2325;&#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2323;&#2306; &#2325;&#2375; &#2360;&#2349;&#2368; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404; CU &#2325;&#2366; &#2350;&#2340;&#2354;&#2348; &#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; &#2351;&#2370;&#2344;&#2367;&#2335; &#2361;&#2376; &#2332;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2360;&#2349;&#2368; &#2349;&#2366;&#2327;&#2379;&#2306; &#2325;&#2379; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2313;&#2330;&#2367;&#2340; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358; &#2342;&#2375;&#2344;&#2375; &#2325;&#2366; &#2325;&#2366;&#2350; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. ___________ diseases last for only shorter periods of time. </span></p>\n",
                    question_hi: "<p>14. ________ <span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2361;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>Hereditary</p>\n", "<p>Chronic</p>\n", 
                                "<p>Acute</p>\n", "<p>Genetic</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2306;&#2358;&#2366;&#2344;&#2369;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;&#2352;&#2381;&#2328;&#2325;&#2366;&#2354;&#2367;&#2325;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2357;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2344;&#2369;&#2357;&#2306;&#2358;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\">(c)&nbsp; </span><strong>Acute. </strong><span style=\"font-weight: 400;\">Acute conditions are often caused by a virus or an infection.&nbsp; </span><strong>Example -</strong><span style=\"font-weight: 400;\"> Common cold, typhoid, jaundice, cholera etc. </span><strong>Genetic disorder : </strong><span style=\"font-weight: 400;\">An inherited medical condition caused by a DNA abnormality. </span><strong>Eg; </strong><span style=\"font-weight: 400;\">Congenital deafness, Cystic fibrosis, Beta thalassemia and Spinal muscular atrophy (SMA). A </span><strong>chronic </strong><span style=\"font-weight: 400;\">condition is a health condition or disease that is persistent or otherwise long-lasting in its effects or a disease that comes with time. </span><strong>Eg; </strong><span style=\"font-weight: 400;\">Arthritis, Asthma, Cancer etc.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\">(c)&nbsp;</span><strong>&#2340;&#2368;&#2357;&#2381;&#2352;&#2404; </strong><span style=\"font-weight: 400;\">&#2340;&#2368;&#2357;&#2381;&#2352; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367;&#2351;&#2366;&#2306; &#2309;&#2325;&#2381;&#2360;&#2352; &#2357;&#2366;&#2351;&#2352;&#2360; &#2351;&#2366; &#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2339; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; </strong><span style=\"font-weight: 400;\">- &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351; &#2360;&#2352;&#2381;&#2342;&#2368;, &#2335;&#2366;&#2311;&#2347;&#2366;&#2311;&#2337;, &#2346;&#2368;&#2354;&#2367;&#2351;&#2366;, &#2361;&#2376;&#2332;&#2366; &#2310;&#2342;&#2367;&#2404;</span><strong> &#2310;&#2344;&#2369;&#2357;&#2306;&#2358;&#2367;&#2325; &#2357;&#2367;&#2325;&#2366;&#2352;</strong><span style=\"font-weight: 400;\">:&nbsp; DNA &#2309;&#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2366; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2357;&#2306;&#2358;&#2366;&#2327;&#2340; &#2350;&#2375;&#2306; &#2350;&#2367;&#2354;&#2368; &#2330;&#2367;&#2325;&#2367;&#2340;&#2381;&#2360;&#2366; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367;&#2404; </span><strong>&#2332;&#2376;&#2360;&#2375;</strong><span style=\"font-weight: 400;\">; &#2332;&#2344;&#2381;&#2350;&#2332;&#2366;&#2340; &#2348;&#2361;&#2352;&#2366;&#2346;&#2344;, &#2360;&#2367;&#2360;&#2381;&#2335;&#2367;&#2325; &#2347;&#2366;&#2311;&#2348;&#2381;&#2352;&#2379;&#2360;&#2367;&#2360;, &#2348;&#2368;&#2335;&#2366; &#2341;&#2376;&#2354;&#2375;&#2360;&#2368;&#2350;&#2367;&#2351;&#2366; &#2324;&#2352; &#2360;&#2381;&#2346;&#2366;&#2311;&#2344;&#2354; &#2350;&#2360;&#2381;&#2325;&#2369;&#2354;&#2352; &#2319;&#2335;&#2381;&#2352;&#2379;&#2347;&#2368; (SMA)&#2404; &#2319;&#2325; </span><strong>&#2342;&#2368;&#2352;&#2381;&#2328;&#2325;&#2366;&#2354;&#2367;&#2325; </strong><span style=\"font-weight: 400;\">&#2360;&#2381;&#2341;&#2367;&#2340;&#2367; (chronic condition) &#2319;&#2325; &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2351;&#2366; &#2348;&#2368;&#2350;&#2366;&#2352;&#2368; &#2361;&#2376; &#2332;&#2379; &#2311;&#2360;&#2325;&#2375; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2379;&#2306; &#2351;&#2366; &#2360;&#2350;&#2351; &#2325;&#2375; &#2360;&#2366;&#2341; &#2310;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2348;&#2368;&#2350;&#2366;&#2352;&#2368; &#2350;&#2375;&#2306; &#2354;&#2327;&#2366;&#2340;&#2366;&#2352; &#2351;&#2366; &#2309;&#2344;&#2381;&#2351;&#2341;&#2366; &#2354;&#2306;&#2348;&#2375; &#2360;&#2350;&#2351; &#2340;&#2325; &#2330;&#2354;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2332;&#2376;&#2360;&#2375;</strong><span style=\"font-weight: 400;\">; &#2327;&#2336;&#2367;&#2351;&#2366;, &#2342;&#2350;&#2366;, &#2325;&#2376;&#2306;&#2360;&#2352; &#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\"> In which year was the Insurance Regulatory and Development Authority of India set up? </span></p>\n",
                    question_hi: "<p>15. <span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2348;&#2368;&#2350;&#2366; &#2357;&#2367;&#2344;&#2367;&#2351;&#2366;&#2350;&#2325; &#2324;&#2352; &#2357;&#2367;&#2325;&#2366;&#2360; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2325;&#2366; &#2327;&#2336;&#2344; &#2325;&#2367;&#2360; &#2357;&#2352;&#2381;&#2359; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366; ?</span></p>\n",
                    options_en: ["<p>1999</p>\n", "<p>2011</p>\n", 
                                "<p>1995</p>\n", "<p>2002</p>\n"],
                    options_hi: ["<p>1999</p>\n", "<p>2011</p>\n",
                                "<p>1995</p>\n", "<p>2002</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.</span><span style=\"font-family: Cambria Math;\">(a) <strong>1999. </strong></span><strong>The Insurance Regulatory and Development Authority (IRDA) </strong><span style=\"font-weight: 400;\">of India is headquartered in Hyderabad, Telangana, where it moved from Delhi in 2001. At present (January, 2023), the authority is chaired by </span><strong>Mr. Debasish Panda. </strong><span style=\"font-weight: 400;\">The Insurance Regulatory and Development Authority of India is a statutory body under the jurisdiction of the Ministry of Finance, Government of India and is tasked with regulating and licensing the insurance and re-insurance industries in India.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.</span><span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>1999 </strong><span style=\"font-weight: 400;\">&#2404;</span><strong> &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2348;&#2368;&#2350;&#2366; &#2357;&#2367;&#2344;&#2367;&#2351;&#2366;&#2350;&#2325; &#2324;&#2352; &#2357;&#2367;&#2325;&#2366;&#2360; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; (IRDA) </strong><span style=\"font-weight: 400;\">&#2325;&#2366; &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; &#2361;&#2376;&#2342;&#2352;&#2366;&#2348;&#2366;&#2342;, &#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366; &#2350;&#2375;&#2306; &#2361;&#2376;, &#2332;&#2361;&#2366;&#2305; &#2351;&#2361; 2001 &#2350;&#2375;&#2306; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; &#2360;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;&#2367;&#2340; &#2361;&#2369;&#2310; &#2341;&#2366;&#2404; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2350;&#2375;&#2306; (&#2332;&#2344;&#2357;&#2352;&#2368;, 2023), &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2325;&#2375; &#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359; </span><strong>&#2358;&#2381;&#2352;&#2368; &#2342;&#2375;&#2348;&#2366;&#2358;&#2368;&#2359; &#2346;&#2366;&#2306;&#2337;&#2366; </strong><span style=\"font-weight: 400;\">&#2361;&#2376;&#2306;&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2348;&#2368;&#2350;&#2366; &#2357;&#2367;&#2344;&#2367;&#2351;&#2366;&#2350;&#2325; &#2324;&#2352; &#2357;&#2367;&#2325;&#2366;&#2360; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2357;&#2367;&#2340;&#2381;&#2340; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351; &#2325;&#2375; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2319;&#2325; &#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325; &#2344;&#2367;&#2325;&#2366;&#2351; &#2361;&#2376; &#2324;&#2352; &#2311;&#2360;&#2375; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2348;&#2368;&#2350;&#2366; &#2324;&#2352; &#2346;&#2369;&#2344;: &#2348;&#2368;&#2350;&#2366; &#2313;&#2342;&#2381;&#2351;&#2379;&#2327;&#2379;&#2306; &#2325;&#2379; &#2357;&#2367;&#2344;&#2367;&#2351;&#2350;&#2367;&#2340; &#2324;&#2352; &#2354;&#2366;&#2311;&#2360;&#2375;&#2306;&#2360; &#2342;&#2375;&#2344;&#2375; &#2325;&#2366; &#2325;&#2366;&#2350; &#2360;&#2380;&#2306;&#2346;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16.<span style=\"font-family: Cambria Math;\"> Which of the following is the largest freshwater lake in India? </span></p>\n",
                    question_hi: "<p>16.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2336;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2333;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>Barapani</p>\n", "<p>Wular</p>\n", 
                                "<p>Bhimtal</p>\n", "<p>Loktak</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2352;&#2366;&#2346;&#2366;&#2344;&#2368;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2370;&#2354;&#2352;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;&#2350;&#2340;&#2366;&#2354;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2325;&#2340;&#2366;&#2325;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>Wular.</strong><span style=\"font-weight: 400;\"> </span><strong>Chilika Lake</strong><span style=\"font-weight: 400;\"> in Odisha is the largest&nbsp; brackish water lake in&nbsp; india.</span><strong> Umiam Lake</strong><span style=\"font-weight: 400;\"> is a reservoir located in&nbsp; the North of Shillong in the state of Meghalaya, India. </span><strong>Keibul Lamjao National Park</strong><span style=\"font-weight: 400;\">, the only floating national park in the world, is located on</span><strong> Loktak Lake</strong><span style=\"font-weight: 400;\"> in Manipur. </span><strong>Bhimtal </strong><span style=\"font-weight: 400;\">is the largest lake of Nainital district, in uttarakhand.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>&#2357;&#2369;&#2354;&#2352;&#2404; </strong><span style=\"font-weight: 400;\">&#2313;&#2337;&#2364;&#2368;&#2360;&#2366; &#2350;&#2375;&#2306;</span><strong> &#2330;&#2367;&#2354;&#2381;&#2325;&#2366; &#2333;&#2368;&#2354;</strong><span style=\"font-weight: 400;\"> &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2326;&#2366;&#2352;&#2375; &#2346;&#2366;&#2344;&#2368; &#2325;&#2368; &#2333;&#2368;&#2354; &#2361;&#2376;&#2404; </span><strong>&#2313;&#2350;&#2367;&#2351;&#2350; &#2333;&#2368;&#2354;</strong><span style=\"font-weight: 400;\"> &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2350;&#2375;&#2328;&#2366;&#2354;&#2351; &#2352;&#2366;&#2332;&#2381;&#2351; &#2350;&#2375;&#2306; &#2358;&#2367;&#2354;&#2366;&#2306;&#2327; &#2325;&#2375; &#2313;&#2340;&#2381;&#2340;&#2352; &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2367;&#2340; &#2319;&#2325; &#2332;&#2354;&#2366;&#2358;&#2351; &#2361;&#2376;&#2404; </span><strong>&#2325;&#2375;&#2348;&#2369;&#2354; &#2354;&#2366;&#2350;&#2332;&#2366;&#2323; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2313;&#2342;&#2381;&#2351;&#2366;&#2344;</strong><span style=\"font-weight: 400;\">, &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2325;&#2366; &#2319;&#2325;&#2350;&#2366;&#2340;&#2381;&#2352; &#2340;&#2376;&#2352;&#2340;&#2366; &#2361;&#2369;&#2310; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2313;&#2342;&#2381;&#2351;&#2366;&#2344;, &#2350;&#2339;&#2367;&#2346;&#2369;&#2352; &#2350;&#2375;&#2306; </span><strong>&#2354;&#2379;&#2325;&#2335;&#2325; &#2333;&#2368;&#2354;</strong><span style=\"font-weight: 400;\"> &#2346;&#2352; &#2360;&#2381;&#2341;&#2367;&#2340; &#2361;&#2376;&#2404; </span><strong>&#2349;&#2368;&#2350;&#2340;&#2366;&#2354;</strong><span style=\"font-weight: 400;\"> &#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2326;&#2306;&#2337; &#2325;&#2375; &#2344;&#2376;&#2344;&#2368;&#2340;&#2366;&#2354; &#2332;&#2367;&#2354;&#2375; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2333;&#2368;&#2354; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17.<span style=\"font-family: Cambria Math;\"> LTE technology is used in which of the following generations of wireless technologies? </span></p>\n",
                    question_hi: "<p>17. <span style=\"font-family: Cambria Math;\">LTE </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;&#2344;&#2368;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2351;&#2352;&#2354;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2380;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2368;&#2338;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>3G</p>\n", "<p>2G</p>\n", 
                                "<p>4G</p>\n", "<p>1G</p>\n"],
                    options_hi: ["<p>3G</p>\n", "<p>2G</p>\n",
                                "<p>4G</p>\n", "<p>1G</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">17.</span><span style=\"font-family: Cambria Math;\">(c)&nbsp;</span><strong>4G. LTE (Long-Term Evolution)</strong><span style=\"font-weight: 400;\"> is a fourth-generation </span><strong>(4G)</strong><span style=\"font-weight: 400;\"> wireless standard that provides increased network capacity and speed for cellphones and other cellular devices compared with third-generation (3G) technology. </span><strong>1G </strong><span style=\"font-weight: 400;\">was the first generation of cell phone technology. 1G is an analog technology and the phones generally had poor battery life&nbsp; and would sometimes experience dropped calls.&nbsp; </span><strong>2G </strong><span style=\"font-weight: 400;\">networks are digital. Main motive of this generation was to provide secure and reliable communication.&nbsp; The </span><strong>3G </strong><span style=\"font-weight: 400;\">standard utilizes a new technology called UMTS (Universal Mobile Telecommunications System) as its core network architecture.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17.</span><span style=\"font-family: Cambria Math;\">(c)&nbsp;</span><strong>4G </strong><span style=\"font-weight: 400;\">&#2404;</span><strong>&nbsp; LTE (&#2354;&#2377;&#2344;&#2381;&#2327;-&#2335;&#2352;&#2381;&#2350; &#2311;&#2357;&#2379;&#2354;&#2381;&#2351;&#2370;&#2358;&#2344;) </strong><span style=\"font-weight: 400;\">&#2330;&#2380;&#2341;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368;</span><strong> (4G)</strong><span style=\"font-weight: 400;\"> &#2325;&#2366; &#2357;&#2366;&#2351;&#2352;&#2354;&#2375;&#2360; &#2350;&#2366;&#2344;&#2325; &#2361;&#2376; &#2332;&#2379; &#2340;&#2368;&#2360;&#2352;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368; (3G) &#2340;&#2325;&#2344;&#2368;&#2325; &#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2350;&#2375;&#2306; &#2360;&#2375;&#2354;&#2347;&#2379;&#2344; &#2324;&#2352; &#2309;&#2344;&#2381;&#2351; &#2360;&#2375;&#2354;&#2369;&#2354;&#2352; &#2313;&#2346;&#2325;&#2352;&#2339;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2338;&#2364;&#2368; &#2361;&#2369;&#2312; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2325;&#2381;&#2359;&#2350;&#2340;&#2366; &#2324;&#2352; &#2327;&#2340;&#2367; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>1G </strong><span style=\"font-weight: 400;\">&#2360;&#2375;&#2354; &#2347;&#2379;&#2344; &#2340;&#2325;&#2344;&#2368;&#2325; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368; &#2341;&#2368;&#2404; </span><strong>1G</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2319;&#2344;&#2366;&#2354;&#2377;&#2327; &#2340;&#2325;&#2344;&#2368;&#2325; &#2361;&#2376; &#2324;&#2352; &#2347;&#2379;&#2344; &#2350;&#2375;&#2306; &#2310;&#2350; &#2340;&#2380;&#2352; &#2346;&#2352; &#2326;&#2352;&#2366;&#2348; &#2348;&#2376;&#2335;&#2352;&#2368; &#2354;&#2366;&#2311;&#2347; &#2361;&#2379;&#2340;&#2368; &#2341;&#2368; &#2324;&#2352; &#2325;&#2349;&#2368;-&#2325;&#2349;&#2368; &#2325;&#2377;&#2354; &#2337;&#2381;&#2352;&#2377;&#2346; &#2361;&#2379;&#2344;&#2375; &#2325;&#2366; &#2309;&#2344;&#2369;&#2349;&#2357; &#2361;&#2379;&#2340;&#2366; &#2341;&#2366;&#2404; </span><strong>2G</strong><span style=\"font-weight: 400;\"> &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360; &#2346;&#2368;&#2338;&#2364;&#2368; &#2325;&#2366; &#2350;&#2369;&#2326;&#2381;&#2351; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2367;&#2340; &#2324;&#2352; &#2357;&#2367;&#2358;&#2381;&#2357;&#2360;&#2344;&#2368;&#2351; &#2360;&#2306;&#2330;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2344;&#2366; &#2341;&#2366;&#2404; </span><strong>3G</strong><span style=\"font-weight: 400;\"> &#2350;&#2366;&#2344;&#2325; &#2309;&#2346;&#2344;&#2375; &#2325;&#2379;&#2352; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2310;&#2352;&#2381;&#2325;&#2367;&#2335;&#2375;&#2325;&#2381;&#2330;&#2352; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; UMTS (&#2351;&#2370;&#2344;&#2367;&#2357;&#2352;&#2381;&#2360;&#2354; &#2350;&#2379;&#2348;&#2366;&#2311;&#2354; &#2335;&#2375;&#2354;&#2368;&#2325;&#2377;&#2350; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;) &#2344;&#2366;&#2350;&#2325; &#2319;&#2325; &#2344;&#2312; &#2340;&#2325;&#2344;&#2368;&#2325; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18.<span style=\"font-family: Cambria Math;\"> Macroeconomics deals with which of the following studies?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> I. Reasons behind the unemployment of resources</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. Determination of total output </span></p>\n",
                    question_hi: "<p>18. <span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2359;&#2381;&#2335;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> (Macroeconomics ) </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2360;&#2366;&#2343;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2339;&#2379;&#2306;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2381;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2381;&#2343;&#2366;&#2352;&#2339;</span></p>\n",
                    options_en: ["<p>Only <span style=\"font-family: Cambria Math;\">I</span></p>\n", "<p>Only <span style=\"font-family: Cambria Math;\">I</span><span style=\"font-family: Cambria Math;\">I</span></p>\n", 
                                "<p>Neither <span style=\"font-family: Cambria Math;\">I</span> nor <span style=\"font-family: Cambria Math;\">I</span><span style=\"font-family: Cambria Math;\">I</span></p>\n", "<p>Both <span style=\"font-family: Cambria Math;\">I</span> and <span style=\"font-family: Cambria Math;\">I</span><span style=\"font-family: Cambria Math;\">I</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> I</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> II</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> I </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> II</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">I </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> II</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">18. </span><span style=\"font-family: Cambria Math;\">(d) </span><strong>Both I and II. </strong><span style=\"font-weight: 400;\">&nbsp;</span><strong>Macroeconomics</strong><span style=\"font-weight: 400;\"> is that part of economics which deals with the individual units of the economy. It takes into account the demand and supply of individual units, which deals with the economic issues that take place on a large scale. It takes into account aggregate demand and aggregate supply.&nbsp;&nbsp;&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18.</span><span style=\"font-family: Cambria Math;\">(d) </span><strong>I &#2324;&#2352; II &#2342;&#2379;&#2344;&#2379;&#2306; </strong><span style=\"font-weight: 400;\">&#2404;</span><strong>&nbsp; </strong><span style=\"font-weight: 400;\">&#2360;&#2350;&#2359;&#2381;&#2335;&#2367; &#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352; (Macroeconomics )</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">&#2309;&#2352;&#2381;&#2341;&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352; </span><span style=\"font-weight: 400;\">&#2357;&#2361; &#2361;&#2367;&#2360;&#2381;&#2360;&#2366; &#2361;&#2376; &#2332;&#2379; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2368; &#2309;&#2354;&#2327;-&#2309;&#2354;&#2327; &#2311;&#2325;&#2366;&#2311;&#2351;&#2379;&#2306; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2351;&#2361; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2327;&#2340; &#2311;&#2325;&#2366;&#2311;&#2351;&#2379;&#2306; &#2325;&#2368; &#2350;&#2366;&#2306;&#2327; &#2324;&#2352; &#2310;&#2346;&#2370;&#2352;&#2381;&#2340;&#2367; &#2325;&#2379; &#2343;&#2381;&#2351;&#2366;&#2344; &#2350;&#2375;&#2306; &#2352;&#2326;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2379; &#2348;&#2337;&#2364;&#2375; &#2346;&#2376;&#2350;&#2366;&#2344;&#2375; &#2346;&#2352; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2350;&#2369;&#2342;&#2381;&#2342;&#2379;&#2306; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2351;&#2361; &#2325;&#2369;&#2354; &#2350;&#2366;&#2306;&#2327; &#2324;&#2352; &#2360;&#2350;&#2327;&#2381;&#2352; &#2310;&#2346;&#2370;&#2352;&#2381;&#2340;&#2367; &#2325;&#2379; &#2343;&#2381;&#2351;&#2366;&#2344; &#2350;&#2375;&#2306; &#2352;&#2326;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19.<span style=\"font-family: Cambria Math;\"> Indian cricke</span><span style=\"font-family: Cambria Math;\">t team lost the 2021 ICC World Test Championship Final to which country? </span></p>\n",
                    question_hi: "<p>19. <span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2368;&#2350;</span><span style=\"font-family: Cambria Math;\"> 2021 </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2312;&#2360;&#2368;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2375;&#2360;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2376;&#2350;&#2381;&#2346;&#2367;&#2351;&#2344;&#2358;&#2367;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2366;&#2311;&#2344;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>New Zealand</p>\n", "<p>Australia</p>\n", 
                                "<p>England</p>\n", "<p>Pakistan</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2381;&#2351;&#2370;&#2332;&#2368;&#2354;&#2376;&#2306;&#2337;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2366;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2327;&#2381;&#2354;&#2376;&#2306;&#2337;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19.</span><span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>New Zealand. </strong><span style=\"font-weight: 400;\">The final of the </span><strong>2019&ndash;2021 (ICC) World Test Championship, </strong><span style=\"font-weight: 400;\">&nbsp;was played from 18 to 23 June 2021 between India and New Zealand at the Rose Bowl, Southampton, England. New Zealand won the match by eight wickets.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19.</span><span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>&#2344;&#2381;&#2351;&#2370;&#2332;&#2364;&#2368;&#2354;&#2376;&#2306;&#2337;&#2404; 2019-2021 (ICC) &#2357;&#2367;&#2358;&#2381;&#2357; &#2335;&#2375;&#2360;&#2381;&#2335; &#2330;&#2376;&#2350;&#2381;&#2346;&#2367;&#2351;&#2344;&#2358;&#2367;&#2346;</strong><span style=\"font-weight: 400;\"> &#2325;&#2366; &#2347;&#2366;&#2311;&#2344;&#2354;, 18 &#2360;&#2375; 23 &#2332;&#2370;&#2344; 2021 &#2340;&#2325; &#2349;&#2366;&#2352;&#2340; &#2324;&#2352; &#2344;&#2381;&#2351;&#2370;&#2332;&#2368;&#2354;&#2376;&#2306;&#2337; &#2325;&#2375; &#2348;&#2368;&#2330; &#2352;&#2379;&#2332;&#2364; &#2348;&#2366;&#2313;&#2354;, &#2360;&#2366;&#2313;&#2341;&#2375;&#2350;&#2381;&#2346;&#2381;&#2335;&#2344;, &#2311;&#2306;&#2327;&#2381;&#2354;&#2376;&#2306;&#2337; &#2350;&#2375;&#2306; &#2326;&#2375;&#2354;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2344;&#2381;&#2351;&#2370;&#2332;&#2368;&#2354;&#2376;&#2306;&#2337; &#2344;&#2375; &#2310;&#2336; &#2357;&#2367;&#2325;&#2375;&#2335; &#2360;&#2375; &#2350;&#2376;&#2330; &#2332;&#2368;&#2340; &#2354;&#2367;&#2351;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>2<span style=\"font-family: Cambria Math;\">0. </span><span style=\"font-family: Cambria Math;\">&lsquo;281 and beyond&rsquo; is the autobiography of which veteran cricketer? </span></p>\n",
                    question_hi: "<p>20. \'<span style=\"font-family: Cambria Math;\">281 </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2351;&#2379;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2359;&#2381;&#2336;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Kapil Dev</p>\n", "<p>VVS Laxman</p>\n", 
                                "<p>Sourav Ganguly</p>\n", "<p>Ajinkya Rahane</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2346;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2357;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2368;&#2357;&#2368;&#2319;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2325;&#2381;&#2359;&#2381;&#2350;&#2339;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2380;&#2352;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2366;&#2306;&#2327;&#2369;&#2354;&#2368;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2332;&#2367;&#2306;&#2325;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2361;&#2366;&#2339;&#2375;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">20.</span><span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>VVS Laxman. &lsquo;281 and beyond&rsquo;</strong><span style=\"font-weight: 400;\"> is the autobiography of VVS Laxman.</span><strong> Kapil Dev\'s autobiography -</strong><span style=\"font-weight: 400;\"> He has written four books &ndash; three autobiographical and one book on Sikhism. Autobiographical works include &mdash; By God\'s Decree which came out in 1985, Cricket My Style in 1987, and Straight from the Heart in 2004. His latest book titled We, The Sikhs was released in 2019.&nbsp; </span><strong>A Century is Not Enough</strong><span style=\"font-weight: 400;\"> -&nbsp; Sourav Ganguly,&nbsp; </span><strong>Playing It My Way</strong><span style=\"font-weight: 400;\"> -&nbsp; Sachin Tendulkar.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">20.</span><span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>&#2357;&#2368;&#2357;&#2368;&#2319;&#2360; &#2354;&#2325;&#2381;&#2359;&#2381;&#2350;&#2339;&#2404; \'281 &#2319;&#2306;&#2337; &#2348;&#2367;&#2351;&#2377;&#2344;&#2381;&#2337;\'</strong><span style=\"font-weight: 400;\"> &#2357;&#2368;&#2357;&#2368;&#2319;&#2360; &#2354;&#2325;&#2381;&#2359;&#2381;&#2350;&#2339; &#2325;&#2368; &#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366; &#2361;&#2376;&#2404;</span><strong> &#2325;&#2346;&#2367;&#2354; &#2342;&#2375;&#2357; &#2325;&#2368; &#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366;</strong><span style=\"font-weight: 400;\"> - &#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2330;&#2366;&#2352; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325;&#2375;&#2306; &#2354;&#2367;&#2326;&#2368; &#2361;&#2376;&#2306; - &#2340;&#2368;&#2344; &#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366;&#2340;&#2381;&#2350;&#2325; &#2324;&#2352; &#2319;&#2325; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2360;&#2367;&#2326; &#2343;&#2352;&#2381;&#2350; &#2346;&#2352;&#2404; &#2310;&#2340;&#2381;&#2350;&#2325;&#2341;&#2366;&#2340;&#2381;&#2350;&#2325; &#2352;&#2330;&#2344;&#2366;&#2323;&#2306; &#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2306; - &#2348;&#2366;&#2351; &#2327;&#2377;&#2337;&#2381;&#2360; &#2337;&#2367;&#2325;&#2381;&#2352;&#2368; &#2332;&#2379; 1985 &#2350;&#2375;&#2306; &#2360;&#2366;&#2350;&#2344;&#2375; &#2310;&#2312; &#2404; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2350;&#2366;&#2312; &#2360;&#2381;&#2335;&#2366;&#2311;&#2354; 1987 &#2350;&#2375;&#2306;, &#2324;&#2352; &#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2335; &#2347;&#2381;&#2352;&#2377;&#2350; &#2342; &#2361;&#2366;&#2352;&#2381;&#2335; 2004 &#2350;&#2375;&#2306;&#2404; &#2313;&#2344;&#2325;&#2368; &#2344;&#2357;&#2368;&#2344;&#2340;&#2350; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2332;&#2367;&#2360;&#2325;&#2366; &#2358;&#2368;&#2352;&#2381;&#2359;&#2325; &#2357;&#2368;, &#2342; &#2360;&#2367;&#2326; 2019 &#2350;&#2375;&#2306; &#2332;&#2366;&#2352;&#2368; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; </span><strong>&#2319; &#2360;&#2375;&#2306;&#2330;&#2369;&#2352;&#2368; &#2311;&#2332; &#2344;&#2377;&#2335; &#2311;&#2344;&#2347;</strong><span style=\"font-weight: 400;\"> - &#2360;&#2380;&#2352;&#2357; &#2327;&#2366;&#2306;&#2327;&#2369;&#2354;&#2368;, </span><strong>&#2346;&#2381;&#2354;&#2375;&#2311;&#2306;&#2327; &#2311;&#2335; &#2350;&#2366;&#2351; &#2357;&#2375;</strong><span style=\"font-weight: 400;\"> - &#2360;&#2330;&#2367;&#2344; &#2340;&#2375;&#2306;&#2342;&#2369;&#2354;&#2325;&#2352;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. <span style=\"font-family: Cambria Math;\">In which year was the Citizenship Ac</span><span style=\"font-family: Cambria Math;\">t passed in India? </span></p>\n",
                    question_hi: "<p>21. <span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>1951</p>\n", "<p>1955</p>\n", 
                                "<p>1959</p>\n", "<p>1964</p>\n"],
                    options_hi: ["<p>1951</p>\n", "<p>1955</p>\n",
                                "<p>1959</p>\n", "<p>1964</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21.</span><span style=\"font-family: Cambria Math;\">(b) <strong>1955.&nbsp;</strong></span><span style=\"font-weight: 400;\">The Constitution of India along with the Indian Citizenship Act 1955 governs the citizenship status of a person. Commenced on 30 December 1955. </span><strong>Citizenship </strong><span style=\"font-weight: 400;\">can be defined as a relationship between a nation and an individual of that specific nation. The concept of single citizenship was adopted from </span><strong>England</strong><span style=\"font-weight: 400;\">.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21.</span><span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>1955 </strong><span style=\"font-weight: 400;\">&#2404;</span><strong>&nbsp; </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 1955 &#2325;&#2375; &#2360;&#2366;&#2341; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2367;&#2360;&#2368; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2325;&#2368; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366; &#2325;&#2368; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2325;&#2379; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; 30 &#2342;&#2367;&#2360;&#2306;&#2348;&#2352; 1955 &#2325;&#2379; &#2358;&#2369;&#2352;&#2370; &#2361;&#2369;&#2310;&#2404; </span><strong>&#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366; </strong><span style=\"font-weight: 400;\">&#2325;&#2379; &#2319;&#2325; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2324;&#2352; &#2313;&#2360; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2325;&#2375; &#2319;&#2325; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2325;&#2375; &#2348;&#2368;&#2330; &#2360;&#2306;&#2348;&#2306;&#2343; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2346;&#2352;&#2367;&#2349;&#2366;&#2359;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; &#2319;&#2325;&#2354; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366; &#2325;&#2368; &#2309;&#2357;&#2343;&#2366;&#2352;&#2339;&#2366; </span><strong>&#2311;&#2306;&#2327;&#2381;&#2354;&#2376;&#2306;&#2337; </strong><span style=\"font-weight: 400;\">&#2360;&#2375; &#2309;&#2346;&#2344;&#2366;&#2312; &#2327;&#2312; &#2341;&#2368;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. _________<span style=\"font-family: Cambria Math;\"> is an Act of the Parliament of the United Kingdom that partitioned British India into two new independent dominions of In</span><span style=\"font-family: Cambria Math;\">dia and Pakistan in 1947. </span></p>\n",
                    question_hi: "<p>22. <span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2366;&#2311;&#2335;&#2375;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2306;&#2327;&#2337;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2360;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> 1947 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2344;&#2367;&#2357;&#2375;&#2358;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> (dominions) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span></p>\n",
                    options_en: ["<p>Rowlatt Act</p>\n", "<p>Indian Independence Act</p>\n", 
                                "<p>Government of India Act</p>\n", "<p>Pitt\'s India Act</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2377;&#2354;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2335;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22.</span><span style=\"font-family: Cambria Math;\">(b)</span><strong>&nbsp;Indian Independence Act. </strong><span style=\"font-weight: 400;\">The Indian Independence Act 1947 is an Act of the Parliament of the United Kingdom that partitioned British India into the two new independent dominions of India and Pakistan. </span><strong>Rowlatt Act : </strong><span style=\"font-weight: 400;\">The act allowed certain political cases to be tried without juries and permitted internment of suspects without trial. </span><strong>Government of India Act 1858</strong><span style=\"font-weight: 400;\">, established India as a nation consisting of British India and princely states.&nbsp; </span><strong>Pitt\'s India Act (EIC Act 1784), </strong><span style=\"font-weight: 400;\">intended to address the shortcomings of the Regulating Act of 1773 by bringing the East India Company\'s rule in India under the control of the British Government.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22.</span><span style=\"font-family: Cambria Math;\">(b)&nbsp;</span><strong>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;&#2340;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</strong><span style=\"font-weight: 400;\">&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;&#2340;&#2366; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 1947 &#2351;&#2370;&#2344;&#2366;&#2311;&#2335;&#2375;&#2337; &#2325;&#2367;&#2306;&#2327;&#2337;&#2350; &#2325;&#2368; &#2360;&#2306;&#2360;&#2342; &#2325;&#2366; &#2319;&#2325; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2361;&#2376; &#2332;&#2367;&#2360;&#2344;&#2375; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2349;&#2366;&#2352;&#2340; &#2325;&#2379; &#2349;&#2366;&#2352;&#2340; &#2324;&#2352; &#2346;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344; &#2325;&#2375; &#2342;&#2379; &#2344;&#2319; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352; &#2346;&#2381;&#2352;&#2349;&#2369;&#2340;&#2381;&#2357;&#2379;&#2306; &#2350;&#2375;&#2306; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366;&#2404; </span><strong>&#2352;&#2379;&#2354;&#2375;&#2335; &#2319;&#2325;&#2381;&#2335;</strong><span style=\"font-weight: 400;\">: &#2311;&#2360; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2344;&#2375; &#2325;&#2369;&#2331; &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2350;&#2366;&#2350;&#2354;&#2379;&#2306; &#2325;&#2379; &#2344;&#2367;&#2352;&#2381;&#2339;&#2366;&#2351;&#2325; &#2350;&#2306;&#2337;&#2354; &#2325;&#2375; &#2348;&#2367;&#2344;&#2366; &#2330;&#2354;&#2366;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2368; &#2324;&#2352; &#2348;&#2367;&#2344;&#2366; &#2350;&#2369;&#2325;&#2342;&#2350;&#2375; &#2325;&#2375; &#2360;&#2306;&#2342;&#2367;&#2327;&#2381;&#2343;&#2379;&#2306; &#2325;&#2379; &#2344;&#2332;&#2364;&#2352;&#2348;&#2306;&#2342; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2368;&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 1858</strong><span style=\"font-weight: 400;\">, &#2344;&#2375; &#2349;&#2366;&#2352;&#2340; &#2325;&#2379; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2349;&#2366;&#2352;&#2340; &#2324;&#2352; &#2352;&#2367;&#2351;&#2366;&#2360;&#2340;&#2379;&#2306; &#2360;&#2375; &#2350;&#2367;&#2354;&#2325;&#2352; &#2319;&#2325; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366;&#2404;</span><strong> &#2346;&#2367;&#2335;&#2381;&#2360; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2319;&#2325;&#2381;&#2335; (EIC Act 1784</strong><span style=\"font-weight: 400;\">), &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2339; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2312;&#2360;&#2381;&#2335; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2325;&#2306;&#2346;&#2344;&#2368; &#2325;&#2375; &#2358;&#2366;&#2360;&#2344; &#2325;&#2379; &#2354;&#2366;&#2325;&#2352; 1773 &#2325;&#2375; &#2352;&#2375;&#2327;&#2369;&#2354;&#2375;&#2335;&#2367;&#2306;&#2327; &#2319;&#2325;&#2381;&#2335; &#2325;&#2368; &#2325;&#2350;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2342;&#2370;&#2352; &#2325;&#2352;&#2344;&#2366; &#2341;&#2366;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23.<span style=\"font-family: Cambria Math;\"> Who won the women\'s singles title in Australian Open 2022?</span></p>\n",
                    question_hi: "<p>23. <span style=\"font-family: Nirmala UI;\">&#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2323;&#2346;&#2344;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2367;&#2340;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Ashley barty</p>\n", "<p>Elena Rybakina</p>\n", 
                                "<p>Ons jebure</p>\n", "<p>Serena Williams</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2319;&#2358;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2352;&#2381;&#2335;&#2368;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2320;&#2354;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2367;&#2348;&#2366;&#2325;&#2367;&#2344;&#2366;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2323;&#2344;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2375;&#2348;&#2381;&#2351;&#2369;&#2352;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2352;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2354;&#2367;&#2351;&#2350;&#2381;&#2360;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23.</span><span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><strong>Ashley Barty.&nbsp; Ashleigh Barty</strong><span style=\"font-weight: 400;\"> defeated </span><strong>Danielle Collins</strong><span style=\"font-weight: 400;\"> in the final to win the women\'s singles tennis title at the 2022 Australian Open.&nbsp; The 2022 Australian Open was a Grand Slam tennis tournament that took place at Melbourne Park, Australia from 17 to 30 January 2022.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23.</span><span style=\"font-family: Cambria Math;\">(a)&nbsp;</span><span style=\"font-weight: 400;\">&nbsp;</span><strong>&#2319;&#2358;&#2354;&#2375; &#2348;&#2366;&#2352;&#2381;&#2335;&#2368;&#2404;</strong><span style=\"font-weight: 400;\"> </span><strong>&#2319;&#2358;&#2354;&#2375; &#2348;&#2366;&#2352;&#2381;&#2335;&#2368; </strong><span style=\"font-weight: 400;\">&#2344;&#2375; 2022 &#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2344; &#2323;&#2346;&#2344; &#2350;&#2375;&#2306; &#2350;&#2361;&#2367;&#2354;&#2366; &#2319;&#2325;&#2354; &#2335;&#2375;&#2344;&#2367;&#2360; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; </span><strong>&#2337;&#2375;&#2344;&#2367;&#2351;&#2354; &#2325;&#2379;&#2354;&#2367;&#2344;&#2381;&#2360;</strong><span style=\"font-weight: 400;\"> &#2325;&#2379; &#2361;&#2352;&#2366;&#2351;&#2366;&#2404; 2022 &#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2344; &#2323;&#2346;&#2344; &#2319;&#2325; &#2327;&#2381;&#2352;&#2376;&#2306;&#2337; &#2360;&#2381;&#2354;&#2376;&#2350; &#2335;&#2375;&#2344;&#2367;&#2360; &#2335;&#2370;&#2352;&#2381;&#2344;&#2366;&#2350;&#2375;&#2306;&#2335; &#2341;&#2366; &#2332;&#2379; 17 &#2360;&#2375; 30 &#2332;&#2344;&#2357;&#2352;&#2368; 2022 &#2340;&#2325; &#2350;&#2375;&#2354;&#2348;&#2352;&#2381;&#2344; &#2346;&#2366;&#2352;&#2381;&#2325;, &#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2366; &#2350;&#2375;&#2306; &#2361;&#2369;&#2310; &#2341;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24.<span style=\"font-family: Cambria Math;\"> Birju Maharaj</span><span style=\"font-family: Cambria Math;\"> was a noted dancer of ________ .</span></p>\n",
                    question_hi: "<p>24.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2352;&#2332;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2352;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> _________ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2352;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>Bharatnatyam</p>\n", "<p>Manipuri</p>\n", 
                                "<p>Kathak</p>\n", "<p>Sattriya</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2349;&#2352;&#2340;&#2344;&#2366;&#2335;&#2381;&#2351;&#2350;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2339;&#2367;&#2346;&#2369;&#2352;&#2368;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2325;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2340;&#2381;&#2352;&#2367;&#2351;&#2366;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24.(c)&nbsp;</span><strong>Kathak.</strong><span style=\"font-weight: 400;\"> He was from&nbsp; Lucknow Gharana and recipient of the Padma Vibhushan (1986). </span><strong>Manipuri dancers -</strong><span style=\"font-weight: 400;\"> Guru Amubi Singh, Elam Endira Devi. </span><strong>Bharatnatyam dancers -</strong><span style=\"font-weight: 400;\"> Padma Subrahmanyam, R Muthukannamal; </span><strong>Sattriya dancer - </strong><span style=\"font-weight: 400;\">Indira Bora.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24</span><span style=\"font-family: Cambria Math;\">.(c)&nbsp;</span><strong>&#2325;&#2341;&#2325;</strong><span style=\"font-weight: 400;\">&#2404; &#2357;&#2375; &#2354;&#2326;&#2344;&#2314; &#2328;&#2352;&#2366;&#2344;&#2375; &#2360;&#2375; &#2341;&#2375; &#2324;&#2352; &#2346;&#2342;&#2381;&#2350; &#2357;&#2367;&#2349;&#2370;&#2359;&#2339; (1986) &#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2325;&#2352;&#2381;&#2340;&#2366; &#2341;&#2375;&#2404; </span><strong>&#2350;&#2339;&#2367;&#2346;&#2369;&#2352;&#2368; &#2344;&#2352;&#2381;&#2340;&#2325;</strong><span style=\"font-weight: 400;\"> - &#2327;&#2369;&#2352;&#2369; &#2309;&#2350;&#2369;&#2348;&#2368; &#2360;&#2367;&#2306;&#2361;, &#2319;&#2354;&#2350; &#2319;&#2306;&#2337;&#2367;&#2352;&#2366; &#2342;&#2375;&#2357;&#2368;; </span><strong>&#2349;&#2352;&#2340;&#2344;&#2366;&#2335;&#2381;&#2351;&#2350; &#2344;&#2352;&#2381;&#2340;&#2325;</strong><span style=\"font-weight: 400;\"> - &#2346;&#2342;&#2381;&#2350; &#2360;&#2369;&#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2339;&#2381;&#2351;&#2350;, &#2310;&#2352; &#2350;&#2369;&#2341;&#2369;&#2325;&#2344;&#2381;&#2344;&#2350;&#2354;; </span><strong>&#2360;&#2340;&#2381;&#2352;&#2367;&#2351;&#2366; &#2344;&#2352;&#2381;&#2340;&#2325;&#2368;</strong><span style=\"font-weight: 400;\"> - &#2311;&#2306;&#2342;&#2367;&#2352;&#2366; &#2348;&#2379;&#2352;&#2366;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25.<span style=\"font-family: Cambria Math;\"> Who among the following was famously known as the \"The parrot of India\"? </span></p>\n",
                    question_hi: "<p>25.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> \"</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2376;&#2352;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2321;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> \" </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Amir Khusro</p>\n", "<p>Lata Mangeshkar</p>\n", 
                                "<p>Pandit Ravishankar</p>\n", "<p>Kalidas</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2350;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2369;&#2360;&#2352;&#2379;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2327;&#2375;&#2358;&#2325;&#2352;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2337;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2306;&#2325;&#2352;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2354;&#2367;&#2342;&#2366;&#2360;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">25</span><span style=\"font-family: Cambria Math;\">.(a)&nbsp;</span><strong>Amir Khusro : </strong><span style=\"font-weight: 400;\">He is a poet in Delhi Sultanate.</span><strong> </strong><strong>Lata Mangeshkar</strong><strong> -</strong><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">Queen of Melody\", \"Nightingale of India\", and \"Voice of the Millennium\". </span><strong>Pandit Ravi Shankar</strong><span style=\"font-weight: 400;\"> was an Indian musician and composer, best known for popularizing the Indian classical instrument Sitar all over the world. </span><strong>Kalidasa </strong><span style=\"font-weight: 400;\">was a Classical Sanskrit author who is often considered ancient India\'s greatest poet and playwright.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">25</span><span style=\"font-family: Cambria Math;\">.(a)&nbsp;</span><strong>&#2309;&#2350;&#2368;&#2352; &#2326;&#2369;&#2360;&#2352;&#2379; : </strong><span style=\"font-weight: 400;\">&#2357;&#2361; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; &#2360;&#2354;&#2381;&#2340;&#2344;&#2340; &#2350;&#2375;&#2306; &#2319;&#2325; &#2325;&#2357;&#2367; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2354;&#2340;&#2366; &#2350;&#2306;&#2327;&#2375;&#2358;&#2325;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2350;&#2375;&#2354;&#2379;&#2337;&#2368; &#2325;&#2368; &#2352;&#2366;&#2344;&#2368;\", \"&#2344;&#2366;&#2311;&#2335;&#2367;&#2306;&#2327;&#2375;&#2354; &#2321;&#2347; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;\", &#2324;&#2352; \"&#2357;&#2377;&#2351;&#2360; &#2321;&#2347; &#2342; &#2350;&#2367;&#2354;&#2375;&#2344;&#2367;&#2351;&#2350;\"&#2404; </span><strong>&#2346;&#2306;&#2337;&#2367;&#2340; &#2352;&#2357;&#2367;&#2358;&#2306;&#2325;&#2352; </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2306;&#2327;&#2368;&#2340;&#2325;&#2366;&#2352; &#2324;&#2352; &#2352;&#2330;&#2351;&#2367;&#2340;&#2366; &#2341;&#2375;, &#2332;&#2367;&#2344;&#2381;&#2361;&#2375;&#2306; &#2346;&#2370;&#2352;&#2368; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351; &#2357;&#2366;&#2342;&#2381;&#2351; &#2351;&#2306;&#2340;&#2381;&#2352; &#2360;&#2367;&#2340;&#2366;&#2352; &#2325;&#2379; &#2354;&#2379;&#2325;&#2346;&#2381;&#2352;&#2367;&#2351; &#2348;&#2344;&#2366;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2325;&#2366;&#2354;&#2367;&#2342;&#2366;&#2360; </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351; &#2360;&#2306;&#2360;&#2381;&#2325;&#2371;&#2340; &#2354;&#2375;&#2326;&#2325; &#2341;&#2375; &#2332;&#2367;&#2344;&#2381;&#2361;&#2375;&#2306; &#2309;&#2325;&#2381;&#2360;&#2352; &#2346;&#2381;&#2352;&#2366;&#2330;&#2368;&#2344; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2360;&#2348;&#2360;&#2375; &#2350;&#2361;&#2366;&#2344; &#2325;&#2357;&#2367; &#2324;&#2352; &#2344;&#2366;&#2335;&#2325;&#2325;&#2366;&#2352; &#2350;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>