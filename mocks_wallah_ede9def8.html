<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. What is the maximum number of rectangles in the given figure?<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175013910.png\" alt=\"rId5\" width=\"147\" height=\"83\"></p>",
                    question_hi: "<p>1. दी गई आकृति में आयतों की अधिकतम संख्या कितनी है?<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175013910.png\" alt=\"rId5\" width=\"147\" height=\"83\"></p>",
                    options_en: ["<p>9</p>", "<p>12</p>", 
                                "<p>11</p>", "<p>8</p>"],
                    options_hi: ["<p>9</p>", "<p>12</p>",
                                "<p>11</p>", "<p>8</p>"],
                    solution_en: "<p>1.(c)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175014090.png\" alt=\"rId6\" width=\"180\" height=\"115\"><br>There are 11 rectangle :- ABKJ, JKLI, ILGH, ABLI , JKGH, ABHG, BCFG, CDEF , BDEG , ADEH, ACFH</p>",
                    solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175014090.png\" alt=\"rId6\" width=\"180\" height=\"115\"><br>11 आयत हैं :- ABKJ, JKLI, ILGH, ABLI , JKGH, ABHG, BCFG, CDEF , BDEG , ADEH, ACFH</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the combination of letters that when sequentially placed in the blanks of the given series, will complete the series.<br>REMRRE_R_EEMRRE_M_</p>",
                    question_hi: "<p>2. अक्षरों के उस संयोजन का चयन कीजिए जिसे दी गई श्रृंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर श्रृंखला पूरी हो जाएगी।<br>REMRRE_R_EEMRRE_M_</p>",
                    options_en: ["<p>EMER</p>", "<p>MRME</p>", 
                                "<p>REME</p>", "<p>MREM</p>"],
                    options_hi: ["<p>EMER</p>", "<p>MRME</p>",
                                "<p>REME</p>", "<p>MREM</p>"],
                    solution_en: "<p>2.(d) REM/RRE<span style=\"text-decoration: underline;\"><strong>M</strong></span><strong>/</strong>R<span style=\"text-decoration: underline;\"><strong>R</strong></span>EEM/RRE<span style=\"text-decoration: underline;\"><strong>E</strong></span>M<span style=\"text-decoration: underline;\"><strong>M</strong></span></p>",
                    solution_hi: "<p>2.(d) REM/RRE<span style=\"text-decoration: underline;\"><strong>M</strong></span><strong>/</strong>R<span style=\"text-decoration: underline;\"><strong>R</strong></span>EEM/RRE<span style=\"text-decoration: underline;\"><strong>E</strong></span>M<span style=\"text-decoration: underline;\"><strong>M</strong></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. 17 is related to 102 following a certain logic. Following the same logic, 29 is related to 174. To which of the following is 39 related, following the same logic?<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13- Operations on 13 such as adding / deleting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>3. एक निश्चित तर्क का अनुसरण करते हुए 17, 102 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 29, 174 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 39 निम्नलिखित में से किससे संबंधित है?<br>(नोट : संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें -13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>238</p>", "<p>234</p>", 
                                "<p>228</p>", "<p>230</p>"],
                    options_hi: ["<p>238</p>", "<p>234</p>",
                                "<p>228</p>", "<p>230</p>"],
                    solution_en: "<p>3.(b)<br><strong>Logic:- </strong><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mn>1</mn><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo><mo>)</mo></math> &times; 6 = 2<sup>nd</sup>no.<br>(17, 102) :- 17<math display=\"inline\"><mo>&#215;</mo></math> 6 = 102<br>(29, 174) :- 29 <math display=\"inline\"><mo>&#215;</mo></math> 6 = 174<br>similarly<br>(39, ?) :- 39 <math display=\"inline\"><mo>&#215;</mo></math> 6 = 234</p>",
                    solution_hi: "<p>3.(b)<br><strong>तर्क:-</strong> (पहली संख्या) <math display=\"inline\"><mo>&#215;</mo></math> 6 = दूसरी संख्या <br>(17, 102) :- 17<math display=\"inline\"><mo>&#215;</mo></math> 6 = 102<br>(29, 174) :- 29 <math display=\"inline\"><mo>&#215;</mo></math> 6 = 174<br>इसी प्रकार <br>(39, ?) :- 39 <math display=\"inline\"><mo>&#215;</mo></math> 6 = 234</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Four letter-clusters have been given out of which three are alike in some manner and one is different. Select the one that is different.<br>Note : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster</p>",
                    question_hi: "<p>4. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। भिन्न समूह का चयन कीजिए।<br>नोट : अक्षर समूह में, भिन्न व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: ["<p>GDI</p>", "<p>XUZ</p>", 
                                "<p>LKP</p>", "<p>SPU</p>"],
                    options_hi: ["<p>GDI</p>", "<p>XUZ</p>",
                                "<p>LKP</p>", "<p>SPU</p>"],
                    solution_en: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175014260.png\" alt=\"rId7\" width=\"113\" height=\"60\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175014408.png\" alt=\"rId8\" width=\"115\" height=\"60\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175014555.png\" alt=\"rId9\" width=\"114\" height=\"60\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175014694.png\" alt=\"rId10\" width=\"114\" height=\"60\"></p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175014260.png\" alt=\"rId7\" width=\"113\" height=\"60\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175014408.png\" alt=\"rId8\" width=\"115\" height=\"60\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175014555.png\" alt=\"rId9\" width=\"114\" height=\"60\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175014694.png\" alt=\"rId10\" width=\"114\" height=\"60\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>Some rice is white.<br>Some rice is brown.<br>No white is wheat.<br><strong>Conclusions:</strong><br>I) Some wheat is brown.<br>II) Some rice is not wheat.<br>III) Some brown is not white.</p>",
                    question_hi: "<p>5. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, चाहे वह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो और निर्णय लीजिए कि दिए गए निष्कर्षों में से कौन-सा/ कौन-से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/ करते हैं।<br><strong>कथनः</strong><br>कुछ चावल, सफेद है।<br>कुछ चावल, भूरे है।<br>कोई सफेद, गेहूँ नहीं है।<br><strong>निष्कर्षः</strong><br>I) कुछ गेहूँ, भूरे हैं।<br>II) कुछ चावल, गेहूँ नहीं है।<br>III) कुछ भूरे, सफेद नहीं है।</p>",
                    options_en: ["<p>Only III</p>", "<p>Only II</p>", 
                                "<p>Only II and III</p>", "<p>Only I and III</p>"],
                    options_hi: ["<p>केवल III</p>", "<p>केवल II</p>",
                                "<p>केवल II और III</p>", "<p>केवल । और III</p>"],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175014851.png\" alt=\"rId11\" width=\"369\" height=\"45\"><br>Only conclusion II follow.</p>",
                    solution_hi: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175015045.png\" alt=\"rId12\" width=\"435\" height=\"53\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the pair which is related to each other in the same way as the following -<br>14 : 205<br>5 : 34<br>(NOTE : Operations should performed on the whole numbers , without breaking down the numbers into its constituent digits . E.g. 13 - Operations on 13 such as adding / subtracting / multiplying etc . to 13 can be performed . breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed .)</p>",
                    question_hi: "<p>6. उस युग्म का चयन कीजिए जो एक दूसरे से उसी प्रकार संबंधित है जिस प्रकार नीचे दिया गया युग्म संबंधित है<br>14 : 205<br>5 : 34<br>(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना संक्रियाएँ केवल पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लीजिए- 13 पर की जाने वाली संक्रियाएँ, जैसे 13 में जोड़ना / घटाना / गुणा करना इत्यादि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>25 : 636</p>", "<p>16 : 247</p>", 
                                "<p>9 : 91</p>", "<p>28 : 793</p>"],
                    options_hi: ["<p>25 : 636</p>", "<p>16 : 247</p>",
                                "<p>9 : 91</p>", "<p>28 : 793</p>"],
                    solution_en: "<p>6.(d) <strong>Logic :-</strong> (1st number)<sup>2</sup> + 9 = 2nd number<br>14: 205 :- (14)<sup>2</sup> + 9 &rArr; 196 + 9 = 205<br>5 : 34 :- (5)<sup>2</sup> + 9 &rArr; 25 + 9 = 34<br>Similarly,<br>28 : ? :- (28)<sup>2</sup> + 9 &rArr; 784 + 9 = 793</p>",
                    solution_hi: "<p>6.(d) <strong>तर्क :-</strong> (पहली संख्या)<sup>2</sup> + 9 = दूसरी संख्या<br>14: 205 :- (14)<sup>2</sup> + 9 &rArr; 196 + 9 = 205<br>5 : 34 :- (5)<sup>2</sup> + 9 &rArr; 25 + 9 = 34<br>इसी प्रकार,<br>28 : ? :- (28)<sup>2</sup> + 9 &rArr; 784 + 9 = 793</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. According to the series given below, fill in the blank with a suitable letter that would satisfy the series.<br>Z, B, D, F, H, _________.</p>",
                    question_hi: "<p>7. नीचे दी गई शृंखला के अनुसार रिक्त स्थान की पूर्ति उस उपयुक्त अक्षर से कीजिए, जो शृंखला को पूरा करे। <br>Z, B, D, F, H, _________.</p>",
                    options_en: ["<p>J</p>", "<p>N</p>", 
                                "<p>K</p>", "<p>I</p>"],
                    options_hi: ["<p>J</p>", "<p>N</p>",
                                "<p>K</p>", "<p>I</p>"],
                    solution_en: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175015207.png\" alt=\"rId13\" width=\"185\" height=\"45\"></p>",
                    solution_hi: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175015207.png\" alt=\"rId13\" width=\"185\" height=\"45\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain code language, \'all fell down\' is written as \'jm ib fc\', and \'down the road\' is coded as \'ib gb tf\'. How will \'down\' be coded in that language ?</p>",
                    question_hi: "<p>8. एक निश्चित कूट भाषा में, \'all fell down\' को \'jm ib fc\' के रूप में लिखा जाता है, और \'down the road\' को \'ib gb tf\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'down\' को कैसे कोडित किया जाएगा?</p>",
                    options_en: ["<p>gb</p>", "<p>ib</p>", 
                                "<p>tf</p>", "<p>fc</p>"],
                    options_hi: ["<p>gb</p>", "<p>ib</p>",
                                "<p>tf</p>", "<p>fc</p>"],
                    solution_en: "<p>8.(b) <br>&lsquo;all fell down&rsquo; &rarr; &lsquo;jm ib fc&rsquo;.........(i)<br>&lsquo;down the road&rsquo; &rarr; &lsquo;ib gb tf&rsquo;....... (ii)<br>From (i) and (ii) &lsquo;down&rsquo; and &lsquo;ib&rsquo; are common. The code of &lsquo;down&rsquo; = &lsquo;ib&rsquo;</p>",
                    solution_hi: "<p>8.(b)<br>&lsquo;all fell down&rsquo; &rarr; &lsquo;jm ib fc&rsquo;.........(i)<br>&lsquo;down the road&rsquo; &rarr; &lsquo;ib gb tf&rsquo;....... (ii)<br>(i) और (ii) से \'down\' और \'ib\' उभय-निष्ठ हैं। \'down\' का कोड = \'ib\'</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the option that is related to the third term in the same way as the second term is&nbsp;related to the first term.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)<br>TEAM : SUDFZBLN :: PLAY : ?</p>",
                    question_hi: "<p>9. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है। (शब्दों को अंग्रेजी का शब्द मानें और शब्द को उसमें अक्षरों संख्या/व्यंजनों की संख्या/स्वरों की संख्या के आधार पर एक-दूसरे से संबंधित नहीं किए जाने चाहिए।)<br>TEAM: SUDFZBLN :: PLAY: ?</p>",
                    options_en: ["<p>OQKMZBXZ</p>", "<p>OQMKZBXZ</p>", 
                                "<p>QOKMBZXZ</p>", "<p>QOMKBZZX</p>"],
                    options_hi: ["<p>OQKMZBXZ</p>", "<p>OQMKZBXZ</p>",
                                "<p>QOKMBZXZ</p>", "<p>QOMKBZZX</p>"],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175015390.png\" alt=\"rId14\" width=\"199\" height=\"74\"> Similarly <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175015506.png\" alt=\"rId15\" width=\"211\" height=\"81\"></p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175015390.png\" alt=\"rId14\" width=\"199\" height=\"74\"> इसी प्रकार&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175015506.png\" alt=\"rId15\" width=\"211\" height=\"81\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10.Three of the following numbers are alike in a certain way and one is different. Pick the odd one out.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing&nbsp;mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>10. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए।<br>(नोट : गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>12 &ndash; 7 &ndash; 209</p>", "<p>19 &ndash; 11 &ndash; 330</p>", 
                                "<p>23 &ndash; 8 &ndash; 343</p>", "<p>15 &ndash; 9 &ndash; 264</p>"],
                    options_hi: ["<p>12 &ndash; 7 &ndash; 209</p>", "<p>19 &ndash; 11 &ndash; 330</p>",
                                "<p>23 &ndash; 8 &ndash; 343</p>", "<p>15 &ndash; 9 &ndash; 264</p>"],
                    solution_en: "<p>10.(c) <br><strong>Logic :</strong> (1st number &times; 10 + 2nd number &times; 10) + (1st number +2nd number) = 3rd number.<br>12 &ndash; 7 &ndash; 209 :- (12 &times; 10 + 7 &times; 10) + (12 + 7) = 209<br>19 &ndash; 11 &ndash; 330 :- (19 &times; 10 + 11 &times; 10) + (19 +11) = 330<br>15 &ndash; 9 &ndash; 264 :- (15 &times; 10 + 9 &times; 10) + (15 + 9) = 264<br>But<br>23 &ndash; 8 &ndash; 343 :- (23 &times; 10 + 8 &times; 10) +(23 +8) = 341 (<math display=\"inline\"><mo>&#8800;</mo></math>343)</p>",
                    solution_hi: "<p>10.(c) <strong>तर्क :</strong> (पहली संख्या &times; 10 + दूसरी संख्या &times; 10) + (पहली संख्या + दूसरी संख्या) = तीसरी संख्या।<br>12 &ndash; 7 &ndash; 209 :- (12 &times; 10 + 7 &times; 10) + (12 + 7) = 209<br>19 &ndash; 11 &ndash; 330 :- (19 &times; 10 + 11 &times; 10) + (19 +11) = 330<br>15 &ndash; 9 &ndash; 264 :- (15 &times; 10 + 9 &times; 10) + (15 + 9) = 264<br>लेकिन<br>23 &ndash; 8 &ndash; 343 :- (23 &times; 10 + 8 &times; 10) +(23 +8) = 341 (<math display=\"inline\"><mo>&#8800;</mo></math>343)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>Some crayon is colour.<br>No colour is water.<br>Some water is liquid.<br><strong>Conclusions:</strong><br>I) Some crayon is liquid.<br>II) Some crayon is not water.<br>III) Some liquid is not colour.</p>",
                    question_hi: "<p>11. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, चाहे वह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो और निर्णय लीजिए कि दिए गए निष्कर्षों में से कौन-सा/ कौन-से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है / करते हैं।<br><strong>कथनः</strong><br>कुछ क्रेयॉन, रंग है।<br>कोई रंग, पानी नहीं है।<br>कुछ पानी, तरल है।<br><strong>निष्कर्षः</strong><br>I) कुछ क्रेयॉन, तरल है।<br>II) कुछ क्रेयॉन, पानी नहीं है।<br>III) कुछ तरल, रंग नहीं है।</p>",
                    options_en: ["<p>Only II and III</p>", "<p>Only I and III</p>", 
                                "<p>Only III</p>", "<p>Only II</p>"],
                    options_hi: ["<p>केवल II और III</p>", "<p>केवल I और III</p>",
                                "<p>केवल III</p>", "<p>केवल II</p>"],
                    solution_en: "<p>11.(a)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175015677.png\" alt=\"rId16\" width=\"357\" height=\"53\"><br>Only II and III follow.</p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175015833.png\" alt=\"rId17\" width=\"371\" height=\"55\"><br>केवल II और III अनुसरण करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "12. Which of the following numbers will replace the question mark (?) in the given series?<br />12, 26, 54, ?, 222, 446",
                    question_hi: "12. दी गई श्रृंखला में निम्नलिखित में से कौन-सी संख्या प्रश्न चिन्ह (?) के स्थान पर आएगी?<br />12, 26, 54, ?, 222, 446",
                    options_en: [" 110 ", " 94 ", 
                                " 88 ", " 98"],
                    options_hi: [" 110 ", " 94 ",
                                " 88 ", " 98"],
                    solution_en: "12.(a)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175015956.png\" alt=\"rId18\" />",
                    solution_hi: "12.(a)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175015956.png\" alt=\"rId18\" />",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13. In a certain code language, BUG is coded as 24 and TWO is coded as 52. What is the code for ONE in that language?",
                    question_hi: "13. एक निश्चित कूट भाषा में, BUG को 24 के रूप में कूटबद्ध किया जाता है और TWO को 52 के रूप में कूटबद्ध किया जाता है। उसी भाषा में ONE के लिए कूट क्या है?",
                    options_en: [" 32 ", " 28 ", 
                                " 33 ", " 27"],
                    options_hi: [" 32 ", " 28 ",
                                " 33 ", " 27"],
                    solution_en: "13.(b) Logic :- (Sum of the place value of letter) - (6) <br />BUG :- (2 + 21 + 7) - (6) ⇒ (30) - (6) = 24<br />TWO :- (20 + 23 + 15) - (6) ⇒ (58) - (6) = 52<br />Similarly,<br />ONE :- (15 + 14 + 5) - (6) ⇒ (34) - (6) = 28",
                    solution_hi: "13.(b) तर्क :- (अक्षर के स्थानीय मान का योग) - (6)<br />BUG :- (2 + 21 + 7) - (6) ⇒ (30) - (6) = 24<br />TWO :- (20 + 23 + 15) - (6) ⇒ (58) - (6) = 52<br />इसी प्रकार,<br />ONE :- (15 + 14 + 5) - (6) ⇒ (34) - (6) = 28",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Identify the figure in the options that when put in place of the question mark (?) will logically complete the series?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016102.png\" alt=\"rId19\" width=\"363\" height=\"84\"></p>",
                    question_hi: "<p>14. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016102.png\" alt=\"rId19\" width=\"354\" height=\"82\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016256.png\" alt=\"rId20\" width=\"90\" height=\"91\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016373.png\" alt=\"rId21\" width=\"92\" height=\"89\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016486.png\" alt=\"rId22\" width=\"90\" height=\"96\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016625.png\" alt=\"rId23\" width=\"91\" height=\"95\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016256.png\" alt=\"rId20\" width=\"95\" height=\"96\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016373.png\" alt=\"rId21\" width=\"92\" height=\"89\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016486.png\" alt=\"rId22\" width=\"94\" height=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016625.png\" alt=\"rId23\" width=\"91\" height=\"95\"></p>"],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016373.png\" alt=\"rId21\" width=\"92\" height=\"89\"></p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016373.png\" alt=\"rId21\" width=\"91\" height=\"88\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the option that is related to the third number in the same way as the second number is related to the first number and the sixth number is related to the fifth number.<br>84 : 196 : 51: ? :: 96 : 224<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13-Operations on 13 such as adding / subtracting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>15. उस विकल्प का चयन कीजिए जो तीसरी संख्या से उसी प्रकार संबंधित है जिस प्रकार दूसरी संख्या पहली संख्या से संबंधित है और छठी संख्या पांचवीं संख्या से संबंधित है।<br>84 : 196 :: 51 : ? :: 96 : 224<br>(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना संक्रियाएँ केवल पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लीजिए 13 पर की जाने वाली संक्रियाएँ, जैसे 13 में जोड़ना / घटाना / गुणा करना इत्यादि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>119</p>", "<p>98</p>", 
                                "<p>103</p>", "<p>143</p>"],
                    options_hi: ["<p>119</p>", "<p>98</p>",
                                "<p>103</p>", "<p>143</p>"],
                    solution_en: "<p>15.(a) <strong>Logic :-</strong> <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup></math>no. &times; 2 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mn>1</mn><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></mrow><mn>3</mn></mfrac></math> = 2<sup>nd </sup>no.<br>(84 : 196):- 84 <math display=\"inline\"><mo>&#215;</mo></math> 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>3</mn></mfrac></math>&rarr; 168 + 28 = 196<br>(96 : 224):- 96 <math display=\"inline\"><mo>&#215;</mo></math> 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>3</mn></mfrac></math>&rarr; 192 + 32 = 224<br>Similarly,<br>(51 : ?):- 51 <math display=\"inline\"><mo>&#215;</mo></math> 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>3</mn></mfrac></math>&rarr; 102 + 17 = 119</p>",
                    solution_hi: "<p>15.(a) <strong>तर्क</strong> :- पहली संख्या <math display=\"inline\"><mo>&#215;</mo></math> 2 +&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow><mn>3</mn></mfrac></math> = दूसरी संख्या<br>(84 : 196):- 84 <math display=\"inline\"><mo>&#215;</mo></math> 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>3</mn></mfrac></math>&rarr; 168 + 28 = 196<br>(96 : 224):- 96 <math display=\"inline\"><mo>&#215;</mo></math> 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>3</mn></mfrac></math>&rarr; 192 + 32 = 224<br>इसी प्रकार,<br>(51 : ?):- 51 <math display=\"inline\"><mo>&#215;</mo></math> 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>3</mn></mfrac></math>&rarr; 102 + 17 = 119</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In a certain code language,<br>A + B means &lsquo;A is the son of B&rsquo;,<br>A &minus; B means &lsquo;A is the wife of B&rsquo;,<br>A &times; B means &lsquo;A is the brother of B&rsquo; and<br>A &divide; B means &lsquo;A is the daughter of B&rsquo;.<br>Based on the above, how is P related to R if &lsquo;P &times; S &divide; M &minus; T + R&rsquo;?</p>",
                    question_hi: "<p>16. एक निश्चित कूट भाषा में,<br>A + B का अर्थ है \'A, B का पुत्र है\',<br>A &minus; B का अर्थ है \'A, B की पत्नी है\',<br>A &times; B का अर्थ है \'A, B का भाई है\' और<br>A &divide; B का अर्थ है \'A, B की पुत्री है\'।<br>उपरोक्त के आधार पर, यदि \'P &times; S &divide; M - T + R\' है, तो P, R से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Brother</p>", "<p>Son&rsquo;s son</p>", 
                                "<p>Father&rsquo;s mother</p>", "<p>Daughter</p>"],
                    options_hi: ["<p>भाई</p>", "<p>पोता</p>",
                                "<p>दादी</p>", "<p>पुत्री</p>"],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016777.png\" alt=\"rId24\" width=\"104\" height=\"152\"><br>&lsquo;P&rsquo; is the son&rsquo;s son of &lsquo;R&rsquo;</p>",
                    solution_hi: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175016777.png\" alt=\"rId24\" width=\"104\" height=\"152\"><br>\'P\', \'R\' का पोता है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Which of the following letter-clusters will replace the question mark (?) in the given series?<br>PTXL, OSWK, NRVJ, ?, LPTH</p>",
                    question_hi: "<p>17. निम्नलिखित में से कौन सा अक्षर-समूह दी गई श्रृंखला में प्रश्न चिह्न (?) का स्थान लेगा ?<br>PTXL, OSWK, NRVJ, ?, LPTH</p>",
                    options_en: ["<p>MQVI</p>", "<p>MQUI</p>", 
                                "<p>NQUI</p>", "<p>MQUJ</p>"],
                    options_hi: ["<p>MQVI</p>", "<p>MQUI</p>",
                                "<p>NQUI</p>", "<p>MQUJ</p>"],
                    solution_en: "<p>17.(b)<br><strong id=\"docs-internal-guid-0748d315-7fff-483f-3072-fd3d6a81ff73\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcu92bDqyGkuOOtbPXyyNiWOW3BkG0WCNGvVfKL_Z96r0U7bbKIj6i-WXT20nUjXKHLynFUze9bA6TU7hSyAyp6pqnKub4bcUb8_HQVAn47uXlAykEJmrQkF0wiIyOYxdq00dM?key=hmbMC86mz8F70D6AEbfoVNI5\" width=\"431\" height=\"118\"></strong></p>",
                    solution_hi: "<p>17.(b)<br><strong id=\"docs-internal-guid-0748d315-7fff-483f-3072-fd3d6a81ff73\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcu92bDqyGkuOOtbPXyyNiWOW3BkG0WCNGvVfKL_Z96r0U7bbKIj6i-WXT20nUjXKHLynFUze9bA6TU7hSyAyp6pqnKub4bcUb8_HQVAn47uXlAykEJmrQkF0wiIyOYxdq00dM?key=hmbMC86mz8F70D6AEbfoVNI5\" width=\"431\" height=\"118\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In a certain code language, &lsquo;LIKE&rsquo; is coded as &lsquo;4682&rsquo; and &lsquo;BEAM&rsquo; is coded as &lsquo;1358&rsquo;.What is the code for &lsquo;E&rsquo; in the given code language?</p>",
                    question_hi: "<p>18. एक निश्चित कूट भाषा में, \'LIKE\' को \'4682\' के रूप में कूटबद्ध किया जाता है और \'BEAM\' को \'1358\' के रूप में कूटबद्ध किया जाता है। दी गई कूट भाषा में \'E\' के लिए कूट क्या है?</p>",
                    options_en: ["<p>5</p>", "<p>8</p>", 
                                "<p>2</p>", "<p>3</p>"],
                    options_hi: ["<p>5</p>", "<p>8</p>",
                                "<p>2</p>", "<p>3</p>"],
                    solution_en: "<p>18.(b)<br>&lsquo;LIKE&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;4682&rsquo; &hellip;&hellip;.. (i)<br>&lsquo;BEAM&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;1358&rsquo; &hellip;&hellip;&hellip; (ii)<br>From (i) and (ii) &lsquo;E&rsquo; and &lsquo;8&rsquo; are common.<br>So, the code of &lsquo;E&rsquo; is &lsquo;8&rsquo;.</p>",
                    solution_hi: "<p>18.(b)<br>&lsquo;LIKE&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;4682&rsquo; &hellip;&hellip;.. (i)<br>&lsquo;BEAM&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;1358&rsquo; &hellip;&hellip;&hellip; (ii)<br>(i) और (ii) से \'E\' और \'8\' उभयनिष्ठ हैं।<br>तो, \'E\' का कोड \'8\' है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017047.png\" alt=\"rId26\" width=\"141\" height=\"122\"></p>",
                    question_hi: "<p>19. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017047.png\" alt=\"rId26\" width=\"141\" height=\"122\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017201.png\" alt=\"rId27\" width=\"154\" height=\"26\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017308.png\" alt=\"rId28\" width=\"152\" height=\"24\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017462.png\" alt=\"rId29\" width=\"156\" height=\"25\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017627.png\" alt=\"rId30\" width=\"157\" height=\"24\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017201.png\" alt=\"rId27\" width=\"154\" height=\"26\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017308.png\" alt=\"rId28\" width=\"152\" height=\"24\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017462.png\" alt=\"rId29\" width=\"156\" height=\"25\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017627.png\" alt=\"rId30\" width=\"156\" height=\"24\"></p>"],
                    solution_en: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017627.png\" alt=\"rId30\" width=\"150\" height=\"23\"></p>",
                    solution_hi: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017627.png\" alt=\"rId30\" width=\"150\" height=\"23\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. A and C are sisters. A is the daughter of D. D is the mother of B. How is A related to B?</p>",
                    question_hi: "<p>20. A और C बहनें हैं। A, D की पुत्री है। D, B की माता है। A का B से क्या रिश्&zwj;ता है?</p>",
                    options_en: ["<p>Aunt</p>", "<p>Sister</p>", 
                                "<p>Mother</p>", "<p>Grandmother</p>"],
                    options_hi: ["<p>चाची</p>", "<p>बहन</p>",
                                "<p>माता</p>", "<p>दादी/नानी</p>"],
                    solution_en: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017754.png\" alt=\"rId31\" width=\"154\" height=\"94\"><br>A is the sister of B.</p>",
                    solution_hi: "<p>20.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017754.png\" alt=\"rId31\" width=\"154\" height=\"94\"><br>A, B की बहन है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "21. What will come in the place of \'?’ in the following equation, if ‘+\' and \'-’ are interchanged?<br /> 47 + 10 × 3 - 32 ÷ 8 = ?",
                    question_hi: "21. यदि \'+\' और ‘-’ को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा?<br />47 + 10 × 3 - 32 ÷ 8 = ?",
                    options_en: [" 19 ", " 21 ", 
                                " 31 ", " 27<br /> "],
                    options_hi: [" 19 ", " 21 ",
                                " 31 ", " 27"],
                    solution_en: "21.(b) Given :- 47 + 10 × 3 - 32 <math display=\"inline\"><mo>÷</mo></math> 8<br />As per given instruction after interchanging ‘+’ and ‘-’ we get<br />47 - 10 × 3 + 32 <math display=\"inline\"><mo>÷</mo></math> 8<br />47 - 30 + 4<br />47 - 26 = 21",
                    solution_hi: "21.(b) दिया गया :- 47 + 10 × 3 - 32 <math display=\"inline\"><mo>÷</mo></math> 8<br />दिए गए निर्देश के अनुसार \'+\' और \'-\' को आपस में बदलने पर हमें प्राप्त होता है<br />47 - 10 × 3 + 32 <math display=\"inline\"><mo>÷</mo></math> 8<br />47 - 30 + 4<br />47 - 26 = 21",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Identify the figure given in the options that when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017930.png\" alt=\"rId32\" width=\"396\" height=\"82\"></p>",
                    question_hi: "<p>22. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175017930.png\" alt=\"rId32\" width=\"333\" height=\"69\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018058.png\" alt=\"rId33\" width=\"77\" height=\"78\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018229.png\" alt=\"rId34\" width=\"76\" height=\"77\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018331.png\" alt=\"rId35\" width=\"77\" height=\"77\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018423.png\" alt=\"rId36\" width=\"75\" height=\"76\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018058.png\" alt=\"rId33\" width=\"74\" height=\"75\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018229.png\" alt=\"rId34\" width=\"76\" height=\"77\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018331.png\" alt=\"rId35\" width=\"73\" height=\"73\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018423.png\" alt=\"rId36\" width=\"75\" height=\"76\"></p>"],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018229.png\" alt=\"rId34\" width=\"74\" height=\"75\"></p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018229.png\" alt=\"rId34\" width=\"76\" height=\"77\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. If &lsquo;A&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&ndash;&rsquo;, what will come in place of the question mark (?) in the following equation ? <br>8 B 9 C 49 A 7 D 25 = ?</p>",
                    question_hi: "<p>23. यदि \'A\' का अर्थ \'&divide;\' है, \'B\' का अर्थ \'&times;\' है, \'C\' का अर्थ \'+\' है और \'D\' का अर्थ \'&ndash;\' है, तो निम्नलिखित समीकरण में प्रश्नवाचक चिह्न के स्थान पर क्या आएगा?<br>8 B 9 C 49 A 7 D 25 = ?</p>",
                    options_en: ["<p>54</p>", "<p>25</p>", 
                                "<p>62</p>", "<p>57 </p>"],
                    options_hi: ["<p>54</p>", "<p>25</p>",
                                "<p>62</p>", "<p>57</p>"],
                    solution_en: "<p>23.(a) <strong>Given :-</strong> 8 B 9 C 49 A 7 D 25<br>As per given instruction after interchanging the letter with sign we get,<br>8 &times; 9 + 49 <math display=\"inline\"><mo>&#247;</mo></math> 7 - 25<br>72 + 7 - 25<br>79 - 25 = 54</p>",
                    solution_hi: "<p>23.(a)<strong> दिया गया :- </strong>8 B 9 C 49 A 7 D 25<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने के बाद हमें प्राप्त होता है,<br>8 &times; 9 + 49 <math display=\"inline\"><mo>&#247;</mo></math> 7 - 25<br>72 + 7 - 25<br>79 - 25 = 54</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. The position of how many letters will remain unchanged if each of the letter in the word &lsquo;CERTAIN&rsquo; is arranged in English alphabetical order?</p>",
                    question_hi: "<p>24. यदि शब्द \'CERTAIN\' के प्रत्येक अक्षर को अंग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित रहेगी?</p>",
                    options_en: ["<p>One</p>", "<p>Zero</p>", 
                                "<p>Two</p>", "<p>Four</p>"],
                    options_hi: ["<p>एक</p>", "<p>शून्य</p>",
                                "<p>दो</p>", "<p>चार</p>"],
                    solution_en: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018529.png\" alt=\"rId37\" width=\"161\" height=\"89\"><br>All letters are changed.</p>",
                    solution_hi: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018529.png\" alt=\"rId37\" width=\"161\" height=\"89\"><br>सभी अक्षर बदल दिए गए हैंI</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Six letters G, H, I, J, K and L are written on different faces of a dice. Two positions of this dice are shown in the figure below. Find the letter on the face opposite to G.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018644.png\" alt=\"rId38\" width=\"204\" height=\"84\"></p>",
                    question_hi: "<p>25. एक पासे के विभिन्न फलकों पर छ: अक्षर G, H, I, J, K और L लिखे गए हैं। नीचे दिए गए चित्र में इस पासे की दो स्थितियों को दर्शाया गया है। दिए गए विकल्पों में से G के विपरीत फलक पर लिखा अक्षर ज्ञात कीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018644.png\" alt=\"rId38\" width=\"204\" height=\"84\"></p>",
                    options_en: ["<p>K</p>", "<p>I</p>", 
                                "<p>L</p>", "<p>J</p>"],
                    options_hi: ["<p>K</p>", "<p>I</p>",
                                "<p>L</p>", "<p>J</p>"],
                    solution_en: "<p>25.(c) from both dices opposite faces are <br>L <math display=\"inline\"><mo>&#8596;</mo></math> G, J &harr; I, K &harr; H</p>",
                    solution_hi: "<p>25.(c) दोनों पासों के विपरीत फलक हैं <br>L <math display=\"inline\"><mo>&#8596;</mo></math> G, J &harr; I, K &harr; H</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Who were the first foreign rulers of northwest India in the post-Mauryan period?</p>",
                    question_hi: "<p>26. मौर्योत्तर काल में उत्तर पश्चिम भारत के प्रथम विदेशी शासक कौन थे?</p>",
                    options_en: ["<p>Kushanas</p>", "<p>Parthians</p>", 
                                "<p>Bactrian Greeks</p>", "<p>Sakas</p>"],
                    options_hi: ["<p>कुषाण (Kushanas)</p>", "<p>पार्थियन (Parthians)</p>",
                                "<p>बैक्ट्रियन यूनानी (Bactrian Greeks)</p>", "<p>शक (Sakas)</p>"],
                    solution_en: "<p>26.(c) The correct answer is <strong>\"Bactrian Greeks\".</strong></p>\n<p>They established the Greco-Bactrian Kingdom around the 2nd century BCE, after the decline of the Maurya Empire, and ruled over regions in modern-day Afghanistan and parts of northwest India. The first Kushana dynasty was founded by Kujula Kadphises. The last ruler of the Kushan dynasty was Vasudeva I. The most famous Saka ruler in India was Rudradaman.</p>\n<h3 class=\"\" data-start=\"90\" data-end=\"108\">🗝️ <span style=\"text-decoration: underline;\">Key Points</span> :-&nbsp;</h3>\n<ul data-start=\"110\" data-end=\"535\">\n<li class=\"\" data-start=\"110\" data-end=\"214\">\n<p class=\"\" data-start=\"112\" data-end=\"214\"><strong data-start=\"112\" data-end=\"212\">The Bactrian Greeks were the first foreign rulers of northwest India in the post-Mauryan period.</strong></p>\n</li>\n<li class=\"\" data-start=\"215\" data-end=\"305\">\n<p class=\"\" data-start=\"217\" data-end=\"305\">They entered India around the 2nd century BCE after the decline of the Mauryan Empire.</p>\n</li>\n<li class=\"\" data-start=\"306\" data-end=\"402\">\n<p class=\"\" data-start=\"308\" data-end=\"402\">Their rule marked the beginning of Indo-Greek influence on Indian art, culture, and coinage.</p>\n</li>\n<li class=\"\" data-start=\"403\" data-end=\"535\">\n<p class=\"\" data-start=\"405\" data-end=\"535\">The Bactrian Greeks laid the foundation for further invasions and settlements by other foreign powers like the Sakas and Kushanas.</p>\n</li>\n</ul>\n<hr class=\"\" data-start=\"537\" data-end=\"540\">\n<h3 class=\"\" data-start=\"542\" data-end=\"571\">📘<span style=\"text-decoration: underline;\"> Furthur Information</span> :-&nbsp;</h3>\n<ul data-start=\"573\" data-end=\"1098\">\n<li class=\"\" data-start=\"573\" data-end=\"889\">\n<p class=\"\" data-start=\"575\" data-end=\"889\"><strong data-start=\"575\" data-end=\"594\">Bactrian Greeks</strong><br data-start=\"594\" data-end=\"597\">○ Originally part of Alexander the Great\'s empire, they ruled over Bactria (modern-day Afghanistan).<br data-start=\"699\" data-end=\"702\">○ They crossed the Hindu Kush and established control over northwestern India.<br data-start=\"782\" data-end=\"785\">○ Famous rulers include Demetrius and Menander (Milinda), who embraced Indian religions like Buddhism.</p>\n</li>\n<li class=\"\" data-start=\"891\" data-end=\"1098\">\n<p class=\"\" data-start=\"893\" data-end=\"1098\"><strong data-start=\"893\" data-end=\"916\">Post-Mauryan Period</strong><br data-start=\"916\" data-end=\"919\">○ This period saw the fragmentation of the Mauryan Empire and rise of regional and foreign powers.<br data-start=\"1019\" data-end=\"1022\">○ It led to significant cultural exchanges between India and Central Asia.</p>\n</li>\n</ul>",
                    solution_hi: "<p>26.(c)सही उत्तर है <strong>\"बैक्ट्रियन ग्रीक\"।</strong></p>\n<p class=\"\" data-start=\"34\" data-end=\"359\">मौर्य साम्राज्य के पतन के बाद, लगभग दूसरी शताब्दी ईसा पूर्व में ग्रेको-बैक्ट्रियन साम्राज्य की स्थापना की और आधुनिक अफगानिस्तान तथा उत्तर-पश्चिम भारत के कुछ हिस्सों पर शासन किया। कुजुला कडफिसेस ने पहले कुशाण वंश की स्थापना की थी। कुशाण वंश के अंतिम शासक वासुदेव प्रथम थे। भारत में सबसे प्रसिद्ध शक शासक रुद्रदामन था।</p>\n<p class=\"\" data-start=\"361\" data-end=\"380\">🗝️ <span style=\"text-decoration: underline;\"><strong>मुख्य बिंदु</strong></span> :-&nbsp;</p>\n<p class=\"\" data-start=\"382\" data-end=\"472\">○ बैक्ट्रियन ग्रीक मौर्य काल के बाद उत्तर-पश्चिम भारत पर शासन करने वाले पहले विदेशी शासक थे।</p>\n<p class=\"\" data-start=\"474\" data-end=\"568\">○ उन्होंने मौर्य साम्राज्य के पतन के बाद, दूसरी शताब्दी ईसा पूर्व के आसपास भारत में प्रवेश किया।</p>\n<p class=\"\" data-start=\"570\" data-end=\"654\">○ उनके शासनकाल में भारतीय कला, संस्कृति और सिक्कों पर इंडो-ग्रीक प्रभाव की शुरुआत हुई।</p>\n<p class=\"\" data-start=\"656\" data-end=\"753\">○ बैक्ट्रियन ग्रीकों ने शकों और कुशाणों जैसे अन्य विदेशी शक्तियों के आक्रमणों और बसावट की नींव रखी।</p>\n<p class=\"\" data-start=\"755\" data-end=\"778\">📘 <span style=\"text-decoration: underline;\"><strong>अतिरिक्त जानकारी </strong></span>:-&nbsp;</p>\n<p class=\"\" data-start=\"780\" data-end=\"1094\"><strong data-start=\"780\" data-end=\"800\">बैक्ट्रियन ग्रीक</strong><br data-start=\"800\" data-end=\"803\">○ मूल रूप से सिकंदर महान के साम्राज्य का हिस्सा थे और बाख़त्रिय (आधुनिक अफगानिस्तान) पर शासन करते थे।<br data-start=\"904\" data-end=\"907\">○ उन्होंने हिंदूकुश पार कर उत्तर-पश्चिम भारत पर नियंत्रण स्थापित किया।<br data-start=\"977\" data-end=\"980\">○ प्रसिद्ध शासकों में डेमेट्रियस और मेनांडर (मिलिंद) शामिल हैं, जिन्होंने बौद्ध धर्म जैसे भारतीय धर्मों को अपनाया।</p>\n<p class=\"\" data-start=\"1096\" data-end=\"1278\"><strong data-start=\"1096\" data-end=\"1115\">उत्तर-मौर्य काल</strong><br data-start=\"1115\" data-end=\"1118\">○ इस काल में मौर्य साम्राज्य का विघटन हुआ और क्षेत्रीय तथा विदेशी शक्तियों का उदय हुआ।<br data-start=\"1204\" data-end=\"1207\">○ इससे भारत और मध्य एशिया के बीच महत्वपूर्ण सांस्कृतिक आदान-प्रदान हुआ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. During which centuries did several Buddhist pilgrims and scholars travel to China on the historic \'silk route\'?",
                    question_hi: "27. किन शताब्दियों के दौरान कई बौद्ध तीर्थयात्रियों और विद्वानों ने ऐतिहासिक \'सिल्क रूट (silk route)\' पर चीन की यात्रा की?",
                    options_en: [" First, second and third centuries AD", " Sixth and seventh centuries AD", 
                                " Fourth and fifth centuries AD", " Eighth and ninth centuries AD"],
                    options_hi: [" पहली, दूसरी और तीसरी शताब्दी ईस्वी", " छठी और सातवीं शताब्दी ईस्वी",
                                " चौथी और पाँचवीं शताब्दी ईस्वी", " आठवीं और नौवीं शताब्दी ईस्वी"],
                    solution_en: "<p>27.(a) <strong>First, second and third centuries AD. </strong>During these centuries, Buddhist pilgrims and scholars traveled to China via the Silk Route, a network of trade routes connecting Asia with Europe. This was a period when Buddhism was spreading from India to China. The Silk Route played a critical role in this cultural and religious exchange. Key Buddhist figures, such as Faxian (who traveled to India) and Xuanzang.</p>",
                    solution_hi: "<p>27.(a) <strong>पहली, दूसरी और तीसरी शताब्दी ईस्वी।</strong> इन शताब्दियों के दौरान, बौद्ध तीर्थयात्री और विद्वान सिल्क रूट के माध्यम से चीन की यात्रा करते थे, जो एशिया को यूरोप से जोड़ने वाले व्यापार मार्गों का एक नेटवर्क था। यह वह समय था जब बौद्ध धर्म का विस्तार भारत से चीन तक हो रहा था। सिल्क रूट ने इस सांस्कृतिक और धार्मिक आदान-प्रदान में महत्वपूर्ण भूमिका निभाई। प्रमुख बौद्ध व्यक्ति, जैसे फैक्सियन (जिन्होंने भारत की यात्रा की) और जुआनज़ैंग।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. With which field was Madhuri Barthwal associated ?</p>",
                    question_hi: "<p>28. माधुरी बड़थ्वाल का संबंध किस क्षेत्र से था ?</p>",
                    options_en: ["<p>Sports</p>", "<p>Music</p>", 
                                "<p>Literature and education</p>", "<p>Medicine</p>"],
                    options_hi: ["<p>खेल</p>", "<p>संगीत</p>",
                                "<p>साहित्य और शिक्षा</p>", "<p>चिकित्सा</p>"],
                    solution_en: "<p>28.(b) <strong>Music.</strong> Madhuri Barthwal is a folk singer from Uttarakhand. She is the first woman to be a music composer in All India Radio. Awards of Madhuri Barthwal : Padma Shri (2022), Nari Shakti Puraskar (2018). Other Folk singers of Uttrakhand : Narendra Singh Negi; Meena Rana; Pritam Bhartwan; Santosh Khetwal.</p>",
                    solution_hi: "<p>28.(b) <strong>संगीत।</strong> माधुरी बड़थ्वाल उत्तराखंड की लोक गायिका हैं। वह ऑल इंडिया रेडियो में संगीतकार बनने वाली पहली महिला हैं। माधुरी बड़थ्वाल को प्राप्त पुरस्कार: पद्म श्री (2022), नारी शक्ति पुरस्कार (2018)। उत्तराखंड के अन्य लोक गायक: नरेंद्र सिंह नेगी, मीना राणा, प्रीतम भरतवाण, संतोष खेतवाल।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following statements about Akbar are true?<br>1 He introduced the Mansabdari system.<br>2 He defeated and merged the state of Bijapur in his empire.<br>3 He made Fatehpur Sikri his capital.</p>",
                    question_hi: "<p>29. अकबर के बारे में निम्नलिखित में से कौन सा कथन सत्य है?<br>1 उसने मनसबदारी प्रणाली की शुरुआत की।<br>2 उसने बीजापुर राज्य को पराजित कर अपने साम्राज्य में मिला लिया।<br>3 उसने फतेहपुर सीकरी को अपनी राजधानी बनाया।</p>",
                    options_en: ["<p>All 1, 2, 3</p>", "<p>Only 1 and 3</p>", 
                                "<p>Only 2 and 3</p>", "<p>Only 1 and 2</p>"],
                    options_hi: ["<p>सभी 1, 2, 3</p>", "<p>केवल 1 और 3</p>",
                                "<p>केवल 2 और 3</p>", "<p>केवल 1 और 2</p>"],
                    solution_en: "<p>29.(b) <strong>Only 1 and 3.</strong> Akbar (also known as Jalal-ud-din Muhammad Akbar) was the third Mughal emperor. He ruled from 1556 to 1605 and became emperor at the age of 13. Aurangzeb, the sixth Mughal emperor (1658-1707), sent his armies to conquer the Deccan Sultanates. He annexed Bijapur in 1685 and Golconda in 1687. The Mansabdari system was a system of ranking government officials and military generals in the Mughal Empire, which was introduced by Emperor Akbar in 1571.</p>",
                    solution_hi: "<p>29.(b) <strong>केवल 1 और 3 . </strong>अकबर (जिसे जलाल-उद-दीन मुहम्मद अकबर के नाम से भी जाना जाता है) तीसरा मुगल सम्राट था। उन्होंने 1556 से 1605 तक शासन किया और 13 वर्ष की आयु में सम्राट पद धारण किया था। छठे मुगल सम्राट औरंगजेब (1658-1707) ने दक्कन सल्तनत पर विजयी पाने के लिए अपनी सेना भेजी थी। उन्होंने 1685 में बीजापुर और 1687 में गोलकुंडा पर कब्ज़ा कर लिया था। मनसबदारी प्रणाली मुगल साम्राज्य में सरकारी अधिकारियों और सैन्य जनरलों की रैंकिंग की एक प्रणाली थी, जिसकी शुरुआत सम्राट अकबर ने 1571 की थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Who inaugurated the National Turmeric Board in New Delhi in January 2025?</p>",
                    question_hi: "<p>30. जनवरी 2025 में नई दिल्ली में राष्ट्रीय हल्दी बोर्ड का उद्घाटन किसने किया?</p>",
                    options_en: ["<p>Nirmala Sitharaman</p>", "<p>Narendra Modi</p>", 
                                "<p>Piyush Goyal</p>", "<p>Amit Shah</p>"],
                    options_hi: ["<p>निर्मला सीतारमण</p>", "<p>नरेंद्र मोदी</p>",
                                "<p>पीयूष गोयल</p>", "<p>अमित शाह</p>"],
                    solution_en: "<p>30.(c)<strong> Piyush Goyal.</strong> Shri Goyal announced Shri Palle Ganga Reddy as its first Chairperson. The headquarters of the Board has been set up at Nizamabad. India is the largest producer, consumer and exporter of turmeric in the world. India has more than 62% share of world trade. During 2023-24, 1.62 lakh tonnes of turmeric and turmeric products valued at 226.5 million USD was exported.</p>",
                    solution_hi: "<p>30.(c) <strong>पीयूष गोयल।</strong> श्री गोयल ने श्री पल्ले गंगा रेड्डी को इसके पहले अध्यक्ष के रूप में घोषित किया। बोर्ड का मुख्यालय निजामाबाद में स्थापित किया गया है। भारत दुनिया में हल्दी का सबसे बड़ा उत्पादक, उपभोक्ता और निर्यातक है। भारत का विश्व व्यापार में 62% से अधिक हिस्सा है। 2023-24 के दौरान, 226.5 मिलियन अमरीकी डॉलर मूल्य की 1.62 लाख टन हल्दी और हल्दी उत्पादों का निर्यात किया गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which law states that endothermic animals from cold climates have smaller extremities or appendages than closely related species from warm climates?</p>",
                    question_hi: "<p>31.कौन-सा सिद्धांत यह उल्लेख करता है कि शीत जलवायु के आंतरोष्मी प्राणियों में ऊष्म जलवायु से संबंधित प्रजातियों की तुलना में छोटे अंग या उपांग होते हैं?</p>",
                    options_en: ["<p>Harper\'s rule</p>", "<p>Reich\'s rule</p>", 
                                "<p>Allen\'s rule</p>", "<p>Moller\'s rule</p>"],
                    options_hi: ["<p>हार्पर का नियम (Harper&rsquo;s rule)</p>", "<p>रीच का नियम (Reich\'s rule)</p>",
                                "<p>एलन का नियम (Allen\'s rule)</p>", "<p>मोलर का नियम (Moller&rsquo;s rule)</p>"],
                    solution_en: "<p>31.(c) <strong>Allen\'s rule. </strong>It was established by Joel Asaph Allen in 1877, is an ecogeographical principle that states animals adapted to cold climates possess shorter and thicker limbs compared to those adapted to warm climates. In contrast, animals in warmer environments tend to have longer appendages and more elongated body shapes to dissipate excess body heat. For instance, the fennec fox, which inhabits the hot deserts of North Africa, has longer ears and limbs than its Arctic relative, the Arctic fox.</p>",
                    solution_hi: "<p>31.(c) <strong>एलन का नियम </strong>(Allen\'s rule) । यह जोएल असाफ एलेन द्वारा 1877 में स्थापित किया गया था, यह एक पारिस्थितिक-भौगोलिक सिद्धांत है जो कहता है कि ठंडे जलवायु के अनुकूल जानवरों के अंग छोटे और मोटे होते हैं, जबकि गर्म जलवायु के अनुकूल जानवरों की तुलना में। इसके विपरीत, गर्म वातावरण में रहने वाले जानवरों के अंग लंबे होते हैं और शरीर का आकार अधिक लंबा होता है, जो अतिरिक्त शरीर की गर्मी को फैलाने में मदद करता है। उदाहरण के लिए, फेनेक लोमड़ी, जो उत्तरी अफ्रीका के गर्म रेगिस्तानों में रहती है, के कान और अंग उसके आर्कटिक रिश्तेदार, आर्कटिक लोमड़ी, की तुलना में लंबे होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Who among the following Indian musicians is known for his contribution to the field of Hindustani music for the classification of Ragas into ten Thatas?</p>",
                    question_hi: "<p>32. निम्नलिखित भारतीय संगीतकारों में से कौन हिंदुस्तानी संगीत के क्षेत्र में रागों को दस थाटों में वर्गीकृत करने के लिए उनके योगदान के लिए जाना जाता है?</p>",
                    options_en: ["<p>Pandit Vishnu Narayan Bhatkhande</p>", "<p>Abdul Karim Khan</p>", 
                                "<p>Pandit Vishnu Digambar Paluskar</p>", "<p>Allauddin Khan</p>"],
                    options_hi: ["<p>पंडित विष्णु नारायण भातखंडे</p>", "<p>अब्दुल करीम खान</p>",
                                "<p>पंडित विष्णु दिगंबर पलुस्कर</p>", "<p>अलाउद्दीन खान</p>"],
                    solution_en: "<p>32.(a) <strong>Pandit Vishnu Narayan Bhatkhande. </strong>He was a prominent musician who wrote the first modern treatise on Hindustani classical music. In 1916, he reorganized the Baroda state music school. He set up Marris College of Music in Lucknow in 1926, which is now known as Bhatkhande Music Institute. He published books Swar Malika and Shri Mallakshaya Sangeetam (pen name Chatur Pandit).</p>",
                    solution_hi: "<p>32.(a) <strong>पंडित विष्णु नारायण भातखंडे</strong> एक प्रमुख संगीतकार थे जिन्होंने हिंदुस्तानी शास्त्रीय संगीत पर प्रथम आधुनिक आलेख लिखा था। 1916 में, उन्होंने बड़ौदा राज्य संगीत विद्यालय का पुनर्गठन किया था। और 1926 में लखनऊ में मैरिस कॉलेज ऑफ़ म्यूज़िक की स्थापना की, जिसे अब भातखंडे संगीत संस्थान के नाम से जाना जाता है। उन्होंने स्वर मालिका और श्री मल्लक्षय संगीतम (उपनाम चतुर पंडित) नामक पुस्तकें प्रकाशित कीं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "33. Norman Ernest Borlaug was awarded the _____ in 1970 for his contributions to the green revolution.",
                    question_hi: "33. नॉर्मन अर्नेस्ट बोरलॉग (Norman Ernest Borlaug) को हरित क्रांति (green revolution) में उनके योगदान के लिए 1970 में _____ से सम्मानित किया गया था।     ",
                    options_en: [" Nobel Prize in Physics ", " Nobel Peace Prize ", 
                                " Nobel Prize in Chemistry", " Nobel Prize in Physiology"],
                    options_hi: [" भौतिकी में नोबेल पुरस्कार  ", " नोबेल शांति पुरस्कार  ",
                                " रसायन शास्त्र में नोबेल पुरस्कार  ", " फिजियोलॉजी में नोबेल पुरस्कार  "],
                    solution_en: "<p>33.(b) <strong>Nobel Peace Prize.</strong> Norman Borlaug was a renowned American agronomist and humanitarian, often referred to as the Father of the Green Revolution. Norman E Borlaug Award : recognizes exceptional scientists under 40 and someone who works in the field of food and nutrition security and hunger eradication. It is given in the honor of Norman Borlaug.</p>",
                    solution_hi: "<p>33.(b) <strong>नोबेल शांति पुरस्कार। </strong>नॉर्मन बोरलॉग एक प्रसिद्ध अमेरिकी कृषिविज्ञानी और मानवतावादी थे, जिन्हें हरित क्रांति का जनक कहा जाता है। नॉर्मन ई बोरलॉग पुरस्कार: 40 वर्ष से कम आयु के असाधारण वैज्ञानिकों और खाद्य एवं पोषण सुरक्षा तथा भूख उन्मूलन के क्षेत्र में कार्य करने वाले व्यक्ति को दिया जाता है। यह नॉर्मन बोरलॉग के सम्मान में दिया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. Which of the following species was discovered in Tale Valley Wildlife Sanctuary of Arunachal Pradesh in April 2024?",
                    question_hi: "34. अप्रैल 2024 में अरुणाचल प्रदेश के ताल घाटी वन्यजीव अभयारण्य में खोजी गई प्रजाति का नाम बताएं।",
                    options_en: [" Neptis philyra", " Brucethoa isro", 
                                " Melanochlamys Droupadi", " Sphaerotheca Varshaabhu"],
                    options_hi: [" Neptis philyra", " Brucethoa Isro",
                                " Melanochlamys Droupadi", " Sphaerotheca Varshaabhu"],
                    solution_en: "<p>34.(a) <strong>Neptis philyra.</strong> The species was documented for the 1st time in India, representing a new record for India.</p>",
                    solution_hi: "<p>34.(a) <strong>Neptis philyra</strong>। इस प्रजाति को पहली बार भारत में दर्ज किया गया, जो भारत के लिए एक नया रिकॉर्ड है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. Who became the youngest to conquer Mount Kilimanjaro at the age of five?",
                    question_hi: "35. माउंट किलिमंजारो को सबसे कम उम्र में (पांच साल) फतह करने वाले कौन बने?",
                    options_en: [" Teghbir Singh     ", " Arjun Vajpai", 
                                " Krushnaa Patil    ", " Premlata Agarwal"],
                    options_hi: [" तेगबीर सिंह", " अर्जुन वाजपेयी",
                                " कृश्ना पाटिल", " प्रेमलता अग्रवाल"],
                    solution_en: "<p>35.(a) <strong>Teghbir Singh. </strong>He has become the youngest Asian to successfully scale Mount Kilimanjaro, Africa&rsquo;s highest peak, standing at an impressive 5,895 meters above sea level.</p>",
                    solution_hi: "<p>35.(a) <strong>तेगबीर सिंह।</strong> तेगबीर सिंह ने माउंट किलिमंजारो (अफ्रीका की सबसे ऊंची चोटी) को सफलतापूर्वक फतह किया, जो समुद्र तल से 5,895 मीटर की ऊंचाई पर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. At which place was the magnificent temple of Saiva constructed by the great Rashtrakuta King &lsquo;Krishna I&rsquo;?</p>",
                    question_hi: "<p>36. महान राष्ट्रकूट राजा \'कृष्ण प्रथम\' द्वारा शैव (Saiva) के भव्य मंदिर का निर्माण किस स्थान पर करवाया गया था?</p>",
                    options_en: ["<p>Badami</p>", "<p>Kanchi</p>", 
                                "<p>Kannauj</p>", "<p>Ellora</p>"],
                    options_hi: ["<p>बादामी</p>", "<p>कांची</p>",
                                "<p>कन्नौज</p>", "<p>एलोरा</p>"],
                    solution_en: "<p>36.(d) <strong>Ellora</strong>. The temples at Ellora Caves (Maharashtra) were built by the Rashtrakuta and Yadava dynasties. Rashtrakuta dynasty built the Hindu and Buddhist caves during their reign. The Kailasa Temple (dedicated to Lord Shiva) was built by Krishna I of the Rashtrakuta dynasty. The Kailasa Temple is the largest single-rock excavation in the world. Yadava dynasty built many of the Jain caves. The Ellora Caves are a UNESCO World Heritage Site. The caves include 12 Buddhist caves, 17 Hindu caves, and 5 Jain caves.</p>",
                    solution_hi: "<p>36.(d) <strong>एलोरा।</strong> एलोरा गुफाओं (महाराष्ट्र) के मंदिरों का निर्माण राष्ट्रकूट और यादव राजवंशों द्वारा किया गया था। राष्ट्रकूट राजवंश ने अपने शासनकाल के दौरान हिंदू और बौद्ध गुफाओं का निर्माण किया। कैलाश मंदिर (भगवान शिव को समर्पित) का निर्माण राष्ट्रकूट राजवंश के कृष्ण प्रथम ने करवाया था। कैलाश मंदिर दुनिया का सबसे बड़ा एकल-चट्टान उत्खनन है। यादव राजवंश ने कई जैन गुफाओं का निर्माण करवाया था। एलोरा गुफाएँ UNESCO की विश्व धरोहर स्थल हैं। गुफाओं में 12 बौद्ध गुफाएँ, 17 हिंदू गुफाएँ और 5 जैन गुफाएँ शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which of the given options is rod-shaped, carries genetic information, is visible only at cell division and is present in the nucleus?</p>",
                    question_hi: "<p>37. दिए गए विकल्पों में से क्या, छड़ के आकार (rod-shaped) का, आनुवांशिक सूचना रखने वाला, केवल कोशिका विभाजन के समय दृश्य (visible) होता है और केंद्रक (nucleus) में उपस्थित होता है?</p>",
                    options_en: ["<p>Chromosomes</p>", "<p>Lysosomes</p>", 
                                "<p>Ribosomes</p>", "<p>Cytoplasm</p>"],
                    options_hi: ["<p>क्रोमोसोम (Chromosomes)</p>", "<p>लाइसोसोम (Lysosomes)</p>",
                                "<p>राइबोसोम (Ribosomes)</p>", "<p>साइटोप्लाज्म (Cytoplasm)</p>"],
                    solution_en: "<p>37.(a) <strong>Chromosomes.</strong> They carry genetic information in the form of DNA. Chromosomes are visible only during cell division (mitosis or meiosis), when they condense and become more distinguishable. Lysosomes: These are membrane-bound organelles responsible for digesting waste materials and cellular debris. Ribosomes: These are the sites of protein synthesis, either floating freely in the cytoplasm or attached to the rough endoplasmic reticulum. Cytoplasm: This is the gel-like substance inside the cell, excluding the nucleus, where most cellular processes occur.</p>",
                    solution_hi: "<p>37.(a) <strong>क्रोमोसोम</strong> (Chromosomes)। वे DNA के रूप में आनुवंशिक जानकारी ले जाते हैं। क्रोमोसोम केवल कोशिका विभाजन (माइटोसिस या मीयोसिस) के दौरान दृश्यमान होते हैं, जब वे संकुचित होते हैं और अधिक पहचान योग्य हो जाते हैं। लाइसोसोम: ये झिल्ली से घिरे हुए अंग होते हैं जो अपशिष्ट पदार्थों और कोशिका अवशेषों को पचाने के लिए जिम्मेदार होते हैं। राइबोसोम: ये प्रोटीन संश्लेषण के स्थल हैं, जो या तो साइटोप्लाज्म में स्वतंत्र रूप से तैरते हैं या कठोर एंडोप्लाज्मिक जालिका से जुड़े होते हैं। साइटोप्लाज्म : यह कोशिका के अंदर एक जेल जैसा पदार्थ है, जिसमें न्यूक्लियस को छोड़कर अधिकांश कोशिकीय प्रक्रियाएं होती हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. With reference to &lsquo;slash and burn&rsquo; agriculture, which of the following names is NOT related to Odisha?</p>",
                    question_hi: "<p>38. \'कर्तन दहन प्रणाली (slash and burn)\' कृषि के संदर्भ में, निम्नलिखित में से कौन-सा नाम ओडिशा से संबंधित नहीं है?</p>",
                    options_en: ["<p>Bringa</p>", "<p>Roca</p>", 
                                "<p>Pama Dabi</p>", "<p>Koman</p>"],
                    options_hi: ["<p>ब्रिंगा (Bringa)</p>", "<p>रोका (Roca)</p>",
                                "<p>पामा डाबी (Pama Dabi)</p>", "<p>कोमान (Koman)</p>"],
                    solution_en: "<p>38.(b) <strong>Roca.</strong> The &lsquo;slash and burn&rsquo; agriculture is known as &lsquo;Milpa&rsquo; in Mexico and Central America, &lsquo;Conuco&rsquo; in Venezuela, &lsquo;Roca&rsquo; in Brazil, &lsquo;Masole&rsquo; in Central Africa, &lsquo;Ladang&rsquo; in Indonesia, &lsquo;Ray&rsquo; in Vietnam. In India, this primitive form of cultivation is called &lsquo;Bewar&rsquo; or &lsquo;Dahiya&rsquo; in Madhya Pradesh, &lsquo;Podu&rsquo; or &lsquo;Penda&rsquo; in Andhra Pradesh, &lsquo;Kumari&rsquo; in Western Ghats, &lsquo;Valre&rsquo; or &lsquo;Waltre&rsquo; in South-eastern Rajasthan.</p>",
                    solution_hi: "<p>38.(b) <strong>रोका।</strong> &lsquo;स्लैश एंड बर्न&rsquo; कृषि को मैक्सिको और मध्य अमेरिका में &lsquo;मिल्पा&rsquo;, वेनेजुएला में &lsquo;कोनुको&rsquo;, ब्राजील में &lsquo;रोका&rsquo;, मध्य अफ्रीका में &lsquo;मासोल&rsquo;, इंडोनेशिया में &lsquo;लदांग&rsquo;, वियतनाम में &lsquo;रे&rsquo; के नाम से जाना जाता है। भारत में, खेती के इस प्राचीन रूप को मध्य प्रदेश में &lsquo;बेवार&rsquo; या &lsquo;दहिया&rsquo;, आंध्र प्रदेश में &lsquo;पोडु&rsquo; या &lsquo;पेंडा&rsquo;, पश्चिमी घाट में &lsquo;कुमारी&rsquo;, दक्षिण-पूर्वी राजस्थान में &lsquo;वालरे&rsquo; या &lsquo;वालट्रे&rsquo; कहा जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which city is referred to as the &lsquo;Manchester of India&rsquo;?</p>",
                    question_hi: "<p>39. किस शहर को \'भारत का मैनचेस्टर\' कहा जाता है?</p>",
                    options_en: ["<p>Kanpur</p>", "<p>Kolkata</p>", 
                                "<p>Ahmedabad</p>", "<p>Mumbai</p>"],
                    options_hi: ["<p>कानपुर</p>", "<p>कोलकाता</p>",
                                "<p>अहमदाबाद</p>", "<p>मुंबई</p>"],
                    solution_en: "<p>39.(c) <strong>Ahmedabad.</strong> It is located in Gujarat on the banks of the Sabarmati river. The first mill was established in 1859. It soon became the second largest textile city of India, after Mumbai. It is referred to as the \"Manchester of India\" because of its similarities to the well-known cotton textile center of Manchester, Great Britain. Cities and their nicknames: Kanpur - Leather City of the World, Kolkata - City of Joy, Mumbai - City of Dreams.</p>",
                    solution_hi: "<p>39.(c) <strong>अहमदाबाद।</strong> यह गुजरात में साबरमती नदी के तट पर स्थित है। प्रथम मिल 1859 में स्थापित की गई थी। यह शीघ्र ही मुंबई के बाद भारत का दूसरा सबसे बड़ा टेक्सटाइल सिटी बन गया। इसे ग्रेट ब्रिटेन के प्रसिद्ध सूती कपड़ा केंद्र मैनचेस्टर से समानता के कारण \"भारत का मैनचेस्टर\" कहा जाता है। शहर और उनके उपनाम: कानपुर - विश्व का चमड़ा शहर, कोलकाता - खुशी का शहर, मुंबई - सपनों का शहर।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which famous book was written by &lsquo;Haripala&rsquo;?</p>",
                    question_hi: "<p>40. कौन-सी प्रसिद्ध पुस्तक, \'हरिपाल\' द्वारा लिखी गई थी?</p>",
                    options_en: ["<p>Gita Govinda</p>", "<p>Sangeet Darpan</p>", 
                                "<p>Sangeet Ratnakara</p>", "<p>Sangeeta Sudhakara</p>"],
                    options_hi: ["<p>गीत गोविंद (Gita Govinda)</p>", "<p>संगीत दर्पण (Sangeet Darpan)</p>",
                                "<p>संगीत रत्नाकर (Sangeet Ratnakara)</p>", "<p>संगीत सुधाकर (Sangeeta Sudhakara)</p>"],
                    solution_en: "<p>40.(d) <strong>Sangeeta Sudhakara. </strong>The term &lsquo;Carnatic&rsquo; and &lsquo;Hindustani&rsquo; to classify musical styles first appeared in it. The Gita Govinda is a work composed by the 12th-century Hindu poet, Jayadeva. Sangeet Darpan is a musical treatise on Indian classical music written by Damodar Pandit. The Sangita-Ratnakara (Ocean of Music and Dance) is one of the most important musicological texts from India.</p>",
                    solution_hi: "<p>40.(d) <strong>संगीत सुधाकर।</strong> संगीत शैलियों को वर्गीकृत करने के लिए &lsquo;कर्नाटक&rsquo; और &lsquo;हिंदुस्तानी&rsquo; शब्द सबसे पहले इसमें दिखाई दिए। गीत गोविंद 12वीं सदी के हिंदू कवि जयदेव द्वारा रचित एक रचना है। संगीत दर्पण, दामोदर पंडित द्वारा लिखित भारतीय शास्त्रीय संगीत पर एक संगीत ग्रंथ है। संगीत-रत्नाकर (संगीत और नृत्य का सागर), भारत के सबसे महत्वपूर्ण संगीत संबंधी ग्रंथों में से एक है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. To which Hindustani music Gharana are the Gundecha brothers related?</p>",
                    question_hi: "<p>41. गुंडेचा बंधु (Gundecha brothers) किस हिंदुस्तानी संगीत घराने से संबंधित हैं?</p>",
                    options_en: ["<p>Patiala Gharana</p>", "<p>Agra Gharana</p>", 
                                "<p>Dagari Gharana</p>", "<p>Indore Gharana</p>"],
                    options_hi: ["<p>पटियाला घराना (Patiala Gharana)</p>", "<p>आगरा घराना (Agra Gharana)</p>",
                                "<p>डागरी घराना (Dagari Gharana)</p>", "<p>इंदौर घराना (Indore Gharana)</p>"],
                    solution_en: "<p>41.(c) <strong>Dagari Gharana.</strong> Umakant and Ramakant Gundecha are together known as the Gundecha Brothers. They received the Sangeet natak academy award in 2017. Hindustani Music Gharanas and founders: Gwalior (Nanthan Khan); Agra (Hajisujan Khan); Rangeela (Faiyyaz Khan); Jaipur Atrauli (Alladiya Khan); Kirana (Abdul Wahid Khan).</p>",
                    solution_hi: "<p>41.(c) <strong>डागरी घराना।</strong> उमाकांत और रमाकांत गुंदेचा को एक साथ गुंदेचा ब्रदर्स के नाम से जाना जाता है। उन्हें 2017 में संगीत नाटक अकादमी पुरस्कार मिला। हिंदुस्तानी संगीत घराने और संस्थापक: ग्वालियर (नंथन खान); आगरा (हजीसुजान खान); रंगीला (फ़ैय्याज़ खान); जयपुर अतरौली (अल्लादिया खान); किराना (अब्दुल वाहिद खान)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. The elements that are liquid at room temperature (25&deg;C) are:</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन-सा तत्व कमरे के तापमान (25&deg;C) पर द्रव अवस्था में होता है?</p>",
                    options_en: ["<p>bromine and chlorine</p>", "<p>helium and mercury</p>", 
                                "<p>bromine and mercury</p>", "<p>helium and chlorine </p>"],
                    options_hi: ["<p>ब्रोमीन और क्लोरीन</p>", "<p>हीलियम और पारा (मर्करी)</p>",
                                "<p>ब्रोमीन और पारा (मर्करी)</p>", "<p>ब्रोमीन और क्लोरीन</p>"],
                    solution_en: "<p>42.(c) <strong>bromine and mercury.</strong> Bromine (Br, atomic number-35), a halogen, is a reddish-brown liquid at room temperature, while mercury, a metal, is silvery-white and liquid at this temperature. Chlorine and helium are gases at room temperature, and helium remains in a gaseous state even at extremely low temperatures. Liquid bromine and mercury are exceptions among elements that are typically solids or gases.</p>",
                    solution_hi: "<p>42.(c) <strong>ब्रोमीन और पारा (मर्करी)</strong>। ब्रोमीन (Br, परमाणु संख्या-35), एक हैलोजन है और कमरे के तापमान पर यह लाल-भूरे रंग का द्रव पदार्थ है, जबकि पारा, एक धातु है, जो इस तापमान पर चांदी-सफेद रंग का द्रव पदार्थ है। क्लोरीन और हीलियम कमरे के तापमान पर गैसीय अवस्था में रहते हैं, और हीलियम अत्यधिक कम तापमान पर भी गैसीय अवस्था में रहता है। द्रव अवस्था में पाए जाने वाले तत्त्व में ब्रोमीन और पारा एक अपवाद हैं, जो सामान्यतः ठोस या गैस के रूप में मौजूद होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which of the following acts introduced the system of dyarchy in the provincial administration in India?</p>",
                    question_hi: "<p>43. निम्नलिखित में से किस अधिनियम से भारत में प्रांतीय प्रशासन में द्वैध शासन प्रणाली (system of dyarchy) की शुरुआत हुई?</p>",
                    options_en: ["<p>The Government of India Act of 1909</p>", "<p>The Government of India Act of 1919</p>", 
                                "<p>The Government of India Act of 1935</p>", "<p>The Government of India Act of 1947</p>"],
                    options_hi: ["<p>1909 का भारत सरकार अधिनियम</p>", "<p>1919 का भारत सरकार अधिनियम</p>",
                                "<p>1935 का भारत सरकार अधिनियम</p>", "<p>1947 का भारत सरकार अधिनियम</p>"],
                    solution_en: "<p>43.(b) <strong>The Government of India Act of 1919. </strong>It is also known as the Montagu-Chelmsford Reforms. Edwin Montagu was the Secretary of State and Lord Chelmsford was the Viceroy. The Act introduced a Darchy (rule of two individuals/parties) for the executive at the level of the provincial government. Provincial subjects were divided into two parts - Reserved subjects and Transferred subjects. It also introduced Bicameralism (upper house and lower house) in the central legislative assembly. It provided for the establishment of a Central Public Service Commission.</p>",
                    solution_hi: "<p>43.(b) <strong>1919 का भारत सरकार अधिनियम</strong>। इसे मांटेग्यू-चेम्सफोर्ड सुधार के रूप में भी जाना जाता है। एडविन मोंटेग्यू राज्य सचिव थे और लॉर्ड चेम्सफ़ोर्ड वायसराय थे। इस अधिनियम ने प्रांतीय सरकार के स्तर पर कार्यकारी के लिए द्वैध शासन (दो व्यक्तियों/पार्टियों का शासन) की शुरुआत की। प्रांतीय विषयों को दो भागों में विभाजित किया गया - आरक्षित विषय और हस्तांतरित विषय। इसने केंद्रीय विधान सभा में द्विसदनीयता (उच्च सदन और निम्न सदन) की भी शुरुआत की। इसने एक केंद्रीय लोक सेवा आयोग की स्थापना का प्रावधान किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. In which year was MS Dhoni awarded Padma Shri Award?</p>",
                    question_hi: "<p>44. एम. एस. धोनी को किस वर्ष पद्म श्री पुरस्कार से सम्मानित किया गया था?</p>",
                    options_en: ["<p>2009</p>", "<p>2010</p>", 
                                "<p>2007</p>", "<p>2012</p>"],
                    options_hi: ["<p>2009</p>", "<p>2010</p>",
                                "<p>2007</p>", "<p>2012</p>"],
                    solution_en: "<p>44.(a) <strong>2009.</strong> The Padma Shri, is the fourth-highest civilian award of the Republic of India, after the Bharat Ratna, the Padma Vibhushan and the Padma Bhushan. Established - 1954. Mahendra Singh Dhoni: Awards - Padma bhushan (2018), Rajiv Gandhi Khel Ratna Award (2007).</p>",
                    solution_hi: "<p>44.(a) <strong>2009.</strong> पद्म श्री, भारत गणराज्य का चौथा सर्वोच्च नागरिक पुरस्कार है, भारत रत्न, पद्म विभूषण और पद्म भूषण के बाद। इसे 1954 में स्थापित किया गया था। महेन्द्र सिंह धोनी को प्राप्त पुरस्कार: पद्म भूषण (2018) और राजीव गांधी खेल रत्न पुरस्कार (2007)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. What is the primary function of the excretory system in animals?</p>",
                    question_hi: "<p>45. जंतुओं में उत्सर्जन तंत्र का प्राथमिक कार्य क्या है?</p>",
                    options_en: ["<p>Respiration</p>", "<p>Removal of waste products</p>", 
                                "<p>Digestion</p>", "<p>Reproduction</p>"],
                    options_hi: ["<p>श्वसन</p>", "<p>अपशिष्ट उत्पादों का निष्कासन</p>",
                                "<p>पाचन</p>", "<p>प्रजनन</p>"],
                    solution_en: "<p>45.(b) <strong>Removal of waste products. </strong>The excretory system plays a crucial role in maintaining homeostasis by filtering out metabolic wastes like urea, carbon dioxide, and excess salts, as well as regulating water balance.</p>",
                    solution_hi: "<p>45(b) <strong>अपशिष्ट उत्पादों का निष्कासन।</strong> उत्सर्जन तंत्र उपापचय के अपशिष्ट पदार्थों जैसे यूरिया, कार्बन डाइऑक्साइड और अतिरिक्त लवणों को छानकर तथा जल संतुलन को नियंत्रित करके समस्थिति (होमियोस्टैसिस) बनाए रखने में महत्वपूर्ण भूमिका निभाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. In which state is the Raika tribe found?</p>",
                    question_hi: "<p>46. रायका (Raika) जनजाति किस राज्य में पाई जाती है?</p>",
                    options_en: ["<p>Kerala</p>", "<p>Manipur</p>", 
                                "<p>West Bengal</p>", "<p>Rajasthan</p>"],
                    options_hi: ["<p>केरल</p>", "<p>मणिपुर</p>",
                                "<p>पश्चिम बंगाल</p>", "<p>राजस्थान</p>"],
                    solution_en: "<p>46.(d) <strong>Rajasthan.</strong> The Raika tribe is renowned for herding animals, particularly camels, sheep, and goats. Other tribes of Rajasthan: Minas, Damor, Dhanka, Garasia, Kathodi etc.</p>",
                    solution_hi: "<p>46.(d) <strong>राजस्थान।</strong> रायका जनजाति पशुओं, विशेष रूप से ऊँट, भेड़ और बकरियों को पालने के लिए प्रसिद्ध है। राजस्थान की अन्य जनजातियाँ: मीना, डामोर, धानका, गरासिया, कथौड़ी आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following is NOT an example of inertia at rest?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन-सा विरामावस्था में जड़त्व का उदाहरण नहीं है?</p>",
                    options_en: ["<p>Falling down backward when a vehicle starts immediately</p>", "<p>Falling down of dust particle while dusting a cloth</p>", 
                                "<p>The detachment of leaves/fruits due to shaking</p>", "<p>The sudden application of brakes in a vehicle</p>"],
                    options_hi: ["<p>वाहन के अचानक चल पड़ने पर पीछे की ओर गिरना</p>", "<p>कपड़े को झाड़ते समय धूल के कणों का नीचे गिरना</p>",
                                "<p>झटकों के कारण पत्तियों/फलों का टूट जाना</p>", "<p>किसी वाहन में अचानक ब्रेक लगाना</p>"],
                    solution_en: "<p>47.(d) <strong>The sudden application of brakes in a vehicle. </strong>Newton\'s First Law of Motion, also known as the law of inertia, states that an object will remain at rest or in uniform motion in a straight line unless acted upon by an external force. Inertia is the body\'s resistance to changes in its state. Inertia of rest means a body at rest stays at rest until an external force acts on it. Inertia of motion means a body in motion continues moving until an external force stops it.</p>",
                    solution_hi: "<p>47.(d) <strong>किसी वाहन में अचानक ब्रेक लगाना। </strong>न्यूटन का गति का पहला नियम, जिसे जड़त्व का नियम भी कहा जाता है, कहता है कि जब तक कोई वस्तु किसी बाहरी बल द्वारा प्रभावित न हो, तब तक वह स्थिर या एकसमान गति में रहेगी। जड़त्व, शरीर की अपनी अवस्था में होने वाले परिवर्तनों के प्रति प्रतिरोध है। विश्राम का जड़त्व का अर्थ है कि एक स्थिर वस्तु तब तक स्थिर रहती है जब तक कि उस पर कोई बाहरी बल कार्य न करे। गति का जड़त्व का अर्थ है कि एक गतिशील वस्तु तब तक चलती रहती है जब तक कि कोई बाहरी बल उसे रोक न दे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Match the following items.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018826.png\" alt=\"rId39\" width=\"515\" height=\"130\"></p>",
                    question_hi: "<p>48. निम्नलिखित मदों का मिलान कीजिए I<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175018999.png\" alt=\"rId40\" width=\"424\" height=\"165\"></p>",
                    options_en: ["<p>i (b), ii (d), iii (a), iv (c)</p>", "<p>i (d), ii (a), iii (b), iv (c)</p>", 
                                "<p>i (b), ii (c), iii (d), iv (a)</p>", "<p>i (c), ii (d), iii (b), iv (a)</p>"],
                    options_hi: ["<p>i (b), ii (d), iii (a), iv (c)</p>", "<p>i (d), ii (a), iii (b), iv (c)</p>",
                                "<p>i (b), ii (c), iii (d), iv (a)</p>", "<p>i (c), ii (d), iii (b), iv (a)</p>"],
                    solution_en: "<p>48.(a) <strong>i (b), ii (d), iii (a), iv (c).</strong> The Kasturba Gandhi Balika Vidyalaya (KGBV) is a government-run residential school for girls from weaker sections of society. The Mid-Day Meal Scheme (MDM) provides free, nutritious meals to school children across India. The National Assessment and Accreditation Council (NAAC) accredits higher education institutions, while the All India Council for Technical Education (AICTE) oversees technical education under the Department of Higher Education.</p>",
                    solution_hi: "<p>48.(a)<strong> i (b), ii (d), iii (a), iv (c). </strong>कस्तूरबा गांधी बालिका विद्यालय (KGBV) समाज के कमजोर वर्गों की लड़कियों के लिए सरकार द्वारा संचालित आवासीय विद्यालय है। मध्याह्न भोजन योजना (MDM) पूरे भारत में स्कूली बच्चों को निःशुल्क, पौष्टिक भोजन प्रदान करती है। राष्ट्रीय मूल्यांकन और प्रत्यायन परिषद (NAAC) उच्च शिक्षा संस्थानों को मान्यता देती है, जबकि अखिल भारतीय तकनीकी शिक्षा परिषद (AICTE) उच्च शिक्षा विभाग के तहत तकनीकी शिक्षा की देखरेख करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following pairs of physicists and their contributions/discoveries is INCORRECT ?</p>",
                    question_hi: "<p>49. निम्नलिखित में से भौतिकविदों और उनके योगदान / अविष्कार का कौन-सा युग्म गलत है ?</p>",
                    options_en: ["<p>Galileo Galilei &ndash; Wave theory of light</p>", "<p>Michael Faraday &ndash; Laws of electromagnetic induction</p>", 
                                "<p>Heinrich Rudolf Hertz &ndash; Generation of electromagnetic waves</p>", "<p>Albert Einstein &ndash; Explanation of photoelectric effect</p>"],
                    options_hi: ["<p>गैलीलियो गैलीली - प्रकाश का तरंग सिद्धांत</p>", "<p>माइकल फैराडे - विद्युत चुम्बकीय प्रेरण के नियम</p>",
                                "<p>हेनरिक रुडोल्फ हर्ट्ज़ - विद्युत चुम्बकीय तरंगों की उत्पत्ति</p>", "<p>अल्बर्ट आइंस्टीन - फोटोइलेक्ट्रिक प्रभाव की व्याख्या</p>"],
                    solution_en: "<p>49.(a). The wave theory of light proposed by Christian Huygens. He proposed that light consists of waves vibrating up and down perpendicular to the direction of light. Galileo Galilei was born on 15 February 1564 in Pisa, Italy. In 1586, he wrote his first scientific book &lsquo;The Little Balance (La Balancitta)&rsquo;, in which he described Archimedes&rsquo; method of finding the relative densities (or specific gravities) of substances using a balance. He developed the Telescope and first Pendulum clock. Using his own telescopes on Saturn and Venus, Galileo argued that all the planets must orbit the Sun and not the earth, contrary to what was believed at that time.</p>",
                    solution_hi: "<p>49.(a). प्रकाश का तरंग सिद्धांत, क्रिश्चियन ह्यूजेंस द्वारा प्रस्तुत किया गया था। उन्होंने प्रस्तावित किया कि प्रकाश में , प्रकाश की दिशा के लंबवत ऊपर और नीचे कंपन करने वाली तरंगें होती हैं। गैलीलियो गैलीली का जन्म 15 फरवरी 1564 को इटली के पीसा में हुआ था। 1586 में, उन्होंने अपनी पहली वैज्ञानिक पुस्तक \'द लिटिल बैलेंस (ला बैलेंसिटा)\' लिखी, जिसमें उन्होंने संतुलन का उपयोग करके पदार्थों के सापेक्ष घनत्व (या विशिष्ट गुरुत्वाकर्षण) को खोजने की आर्किमिडीज़ की विधि का वर्णन किया। उन्होंने दूरबीन और पहली पेंडुलम घड़ी विकसित की। शनि और शुक्र पर अपनी स्वयं की दूरबीनों का उपयोग करते हुए, गैलीलियो ने तर्क दिया कि सभी ग्रहों को सूर्य की परिक्रमा करनी चाहिए, न कि पृथ्वी की, जो उस समय की धारणा के विपरीत था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which of the following is the compound with the chemical formula <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>H</mi><mn>2</mn></msub><mi>S</mi><msub><mi>O</mi><mn>4</mn></msub></math>?</p>",
                    question_hi: "<p>50. निम्नलिखित में से कौन-सा, रासायनिक सूत्र <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>H</mi><mn>2</mn></msub><mi>S</mi><msub><mi>O</mi><mn>4</mn></msub></math> वाला यौगिक है?</p>",
                    options_en: ["<p>Nitric Acid</p>", "<p>Sulfuric Acid</p>", 
                                "<p>Sulfurous Acid</p>", "<p>Hydrogen Sulfide</p>"],
                    options_hi: ["<p>नाइट्रिक अम्ल (Nitric Acid)</p>", "<p>सल्फ्यूरिक अम्ल (Sulfuric Acid)</p>",
                                "<p>सल्फ्यूरस अम्ल (Sulfurous Acid)</p>", "<p>हाइड्रोजन सल्फाइड (Hydrogen Sulfide)</p>"],
                    solution_en: "<p>50.(b) <strong>Sulfuric Acid</strong> is also known as Mattling acid or Oil of vitriol. It is a colorless oily liquid. It is soluble in water with the release of heat. It is used to make fertilizers and other chemicals, in petroleum refining, in iron and steel production. Other Compounds : Nitric acid (HNO<sub>3</sub>), Sulfurous acid (H<sub>2</sub>SO<sub>3</sub>), Hydrogen sulfide (H<sub>2</sub>S).</p>",
                    solution_hi: "<p>50.(b) सल्फ्यूरिक अम्ल को मैटलिंग अम्ल या ऑयल ऑफ विट्रियल के नाम से भी जाना जाता है। यह एक रंगहीन तैलीय तरल पदार्थ है। यह ऊष्मा मुक्त करने के साथ जल में घुलनशील है। इसका उपयोग उर्वरक और अन्य रसायन बनाने, पेट्रोलियम शोधन, लोहा और इस्पात उत्पादन में किया जाता है। अन्य यौगिक : नाइट्रिक अम्ल (HNO<sub>3</sub>), सल्फ्यूरस अम्ल (H<sub>2</sub>SO<sub>3</sub>), हाइड्रोजन सल्फाइड (H<sub>2</sub>S)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. What is the value of a so that the seven&minus;digit number 4645a52 is divisible by 72?</p>",
                    question_hi: "<p>51. a का वह कौन-सा मान है ताकि सात अंकों की संख्या 4645a52, 72 से विभाज्य हो ?</p>",
                    options_en: ["<p>2</p>", "<p>0</p>", 
                                "<p>3</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>0</p>",
                                "<p>3</p>", "<p>1</p>"],
                    solution_en: "<p>51.(d)<br><strong>72 = 8 &times; 9</strong><br>For 4645a52 to be divisible by 9, the sum of its digits i.e. 4 + 6 + 4 + 5 + a + 5 + 2 = 26+a, should be divisible by 9.<br>For 4645a52 to be divisible by 8, its last 3 digits i.e. a52 must be divisible by 8.<br>For this &lsquo;a&rsquo; must be 1. As , 152 is divisible by 8 and 27 is divisible by 9.</p>",
                    solution_hi: "<p>51.(d)<br>72 = 8 &times; 9<br>4645a52 को 9 से विभाज्य होने के लिए, इसके अंकों का योग, यानी. 4 + 6 + 4 + 5 + a + 5 + 2 = 26+a, 9 से विभाज्य होना चाहिए।<br>4645a52 को 8 से विभाज्य होने के लिए, इसके अंतिम 3 अंक यानी a52 को 8 से विभाज्य होना चाहिए।<br>इसके लिए &lsquo;a&rsquo; का 1 होना आवश्यक है। चूँकि, 152, 8 से विभाज्य है और 27, 9 से विभाज्य है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The average of runs of a cricket player in 12 innings was 42. How many runs must he make in his next innings to increase his average of runs by 2?</p>",
                    question_hi: "<p>52. एक क्रिकेट खिलाड़ी द्वारा 12 पारियों में बनाए गए रनों का औसत 42 था। अपने रनों के औसत में 2 की वृद्धि करने के लिए उसे अपनी अगली पारी में कितने रन बनाने चाहिए?</p>",
                    options_en: ["<p>64</p>", "<p>68</p>", 
                                "<p>72</p>", "<p>78</p>"],
                    options_hi: ["<p>64</p>", "<p>68</p>",
                                "<p>72</p>", "<p>78</p>"],
                    solution_en: "<p>52.(b)<br>Let the total run in next innings be <math display=\"inline\"><mi>x</mi></math><br>12 &times; 42 + <math display=\"inline\"><mi>x</mi></math> = 44 &times;13<br>504 + <math display=\"inline\"><mi>x</mi></math> = 572<br><math display=\"inline\"><mi>x</mi></math> = 572 - 504 = 68</p>",
                    solution_hi: "<p>52.(b)<br>माना कि अगली पारी में कुल रन = <math display=\"inline\"><mi>x</mi></math> <br>12 &times; 42 + <math display=\"inline\"><mi>x</mi></math> = 44 &times;13<br>504 + <math display=\"inline\"><mi>x</mi></math> = 572<br><math display=\"inline\"><mi>x</mi></math> = 572 - 504 = 68</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. From a point A, which is at a distance of 17 cm from the centre C of a circle with radius 8 cm, the pair of tangents AB and AD to the circle are drawn. The area of the quadrilateral ABCD is _____ cm<sup>2</sup></p>",
                    question_hi: "<p>53. एक बिंदु A से, जो 8 cm त्रिज्या वाले एक वृत्त के केंद्र C से 17 cm की दूरी पर है, वृत्त पर स्पर्शरेखा AB और AD का युग्म खींचा जाता है। चतुर्भुज ABCD का क्षेत्रफल _____ cm<sup>2</sup> है।</p>",
                    options_en: ["<p>192</p>", "<p>120</p>", 
                                "<p>60</p>", "<p>360</p>"],
                    options_hi: ["<p>192</p>", "<p>120</p>",
                                "<p>60</p>", "<p>360</p>"],
                    solution_en: "<p>53.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175019191.png\" alt=\"rId41\" width=\"159\" height=\"94\"><br>In <math display=\"inline\"><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math> <br>&ang;ABC = <math display=\"inline\"><msup><mrow><mn>90</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> &hellip; (radius &perp; tangent)<br>hence,<br><math display=\"inline\"><mi>A</mi><mi>B</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>A</mi><msup><mi>C</mi><mn>2</mn></msup><mo>-</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mn>17</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mn>8</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>289</mn><mo>-</mo><mn>64</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>225</mn></msqrt></math> = 15 cm<br>Area of <math display=\"inline\"><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&times; base &times; height = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; AB &times; BC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&times; 15&times; 8 = 60 cm<sup>2</sup><br>Area of quadrilateral ABCD = 2 <math display=\"inline\"><mo>&#215;</mo></math> Area of &Delta;ABC = 2 &times; 60 = 120 cm<sup>2</sup></p>",
                    solution_hi: "<p>53.(b)<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175019191.png\" alt=\"rId41\" width=\"159\" height=\"94\"><br>In <math display=\"inline\"><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math> <br>&ang;ABC = <math display=\"inline\"><msup><mrow><mn>90</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> &hellip; (त्रिज्या &perp; स्पर्श रेखा)<br>इस तरह,<br><math display=\"inline\"><mi>A</mi><mi>B</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>A</mi><msup><mi>C</mi><mn>2</mn></msup><mo>-</mo><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mn>17</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mn>8</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>289</mn><mo>-</mo><mn>64</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>225</mn></msqrt></math> = 15 cm<br><math display=\"inline\"><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi></math> का क्षेत्रफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&times; आधार &times; ऊँचाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&times; AB &times; BC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&times; 15&times; 8 = 60 cm<sup>2</sup><br>चतुर्भुज ABCD का क्षेत्रफल = 2 <math display=\"inline\"><mo>&#215;</mo></math> &Delta;ABC का क्षेत्रफल = 2 &times; 60 = 120 cm<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The bar graph given below shows the sales of books from six schools during two consecutive years, 2013 and 2014.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175019418.png\" alt=\"rId42\" width=\"343\" height=\"235\"> <br>What percentage of the average sales of the schools A, B, and C in 2014 is the average sales of schools A, C, and F in 2013 ?</p>",
                    question_hi: "<p>54. नीचे दिया गया बार ग्राफ क्रमागत दो वर्षों, 2013 और 2014 के दौरान छह स्कूलों की पुस्तकों की बिक्री को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175019539.png\" alt=\"rId43\" width=\"360\" height=\"246\"> <br>2014 में स्कूल A, B, और C की औसत बिक्री, 2013 में स्कूल A, C, और F की औसत बिक्री का कितना प्रतिशत है?</p>",
                    options_en: ["<p>81.08%</p>", "<p>81%</p>", 
                                "<p>80%</p>", "<p>80.08%</p>"],
                    options_hi: ["<p>81.08%</p>", "<p>81%</p>",
                                "<p>80%</p>", "<p>80.08%</p>"],
                    solution_en: "<p>54.(a)<br>Average sales of the schools A, B, and C in 2014 = <math display=\"inline\"><mfrac><mrow><mn>70</mn><mo>+</mo><mn>35</mn><mo>+</mo><mn>80</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>185</mn><mn>3</mn></mfrac></math><br>Average sales of the schools A, C, and F in 2013 = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>+</mo><mn>60</mn><mo>+</mo><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>3</mn></mfrac></math><br>Ratio = <math display=\"inline\"><mfrac><mrow><mn>185</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>3</mn></mfrac></math>= 37 : 30<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math>&times;100 = 81.08%</p>",
                    solution_hi: "<p>54.(a)<br>2014 में स्कूल A, B और C की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>70</mn><mo>+</mo><mn>35</mn><mo>+</mo><mn>80</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>185</mn><mn>3</mn></mfrac></math><br>2013 में स्कूल A, C और F की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>+</mo><mn>60</mn><mo>+</mo><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>3</mn></mfrac></math><br>अनुपात = <math display=\"inline\"><mfrac><mrow><mn>185</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>3</mn></mfrac></math>= 37 : 30<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math>&times;100 = 81.08%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>M</mi></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>N</mi></mfrac></math>: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>O</mi></mfrac></math>= 3 : 4 : 5, then M : N : O is equal to :</p>",
                    question_hi: "<p>55. यदि <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>M</mi></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>N</mi></mfrac></math>: <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>O</mi></mfrac></math>= 3 : 4 : 5 है, तो M : N : O का मान क्या होगा?</p>",
                    options_en: ["<p>12 : 15 : 20</p>", "<p>20 : 15 : 12</p>", 
                                "<p>20 : 12 : 15</p>", "<p>15 : 20 : 12</p>"],
                    options_hi: ["<p>12 : 15 : 20</p>", "<p>20 : 15 : 12</p>",
                                "<p>20 : 12 : 15</p>", "<p>15 : 20 : 12</p>"],
                    solution_en: "<p>55.(b)&nbsp;</p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>M</mi></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>N</mi></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>O</mi></mfrac></math> = 3 : 4 : 5&nbsp;&nbsp;</p>\n<p dir=\"ltr\">Let <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>M</mi></mfrac></math> = 3k ,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>N</mi></mfrac></math> = 4k , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>O</mi></mfrac></math> = 5k</p>\n<p dir=\"ltr\">M = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>3</mn><mi>k</mi></mrow></mfrac></math>&nbsp; , N = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>4</mn><mi>k</mi></mrow></mfrac></math> , O = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>5</mn><mi>k</mi></mrow></mfrac></math></p>\n<p dir=\"ltr\">Now,&nbsp; LCM(3k,4k,5k) = 60k</p>\n<p dir=\"ltr\">Hence, required ratio =&nbsp; M : N : O = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mi>k</mi></mrow><mrow><mn>3</mn><mi>k</mi></mrow></mfrac></math>&nbsp; : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mi>k</mi></mrow><mrow><mn>4</mn><mi>k</mi></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mi>k</mi></mrow><mrow><mn>5</mn><mi>k</mi></mrow></mfrac></math>&nbsp;= 20 : 15 : 12</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>55.(b)&nbsp;</p>\n<p dir=\"ltr\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>M</mi></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>N</mi></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>O</mi></mfrac></math> = 3 : 4 : 5&nbsp;&nbsp;</p>\n<p dir=\"ltr\">माना <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>M</mi></mfrac></math> = 3k ,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>N</mi></mfrac></math> = 4k , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mi>O</mi></mfrac></math> = 5k</p>\n<p dir=\"ltr\">M = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>3</mn><mi>k</mi></mrow></mfrac></math>&nbsp; , N = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>4</mn><mi>k</mi></mrow></mfrac></math> , O = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>5</mn><mi>k</mi></mrow></mfrac></math></p>\n<p dir=\"ltr\">अब,&nbsp; LCM(3k,4k,5k) = 60k</p>\n<p dir=\"ltr\">अतः, आवश्यक अनुपात =&nbsp; M : N : O = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mi>k</mi></mrow><mrow><mn>3</mn><mi>k</mi></mrow></mfrac></math>&nbsp; : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mi>k</mi></mrow><mrow><mn>4</mn><mi>k</mi></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mi>k</mi></mrow><mrow><mn>5</mn><mi>k</mi></mrow></mfrac></math> = 20 : 15 : 12</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. If (sin <math display=\"inline\"><mi>x</mi></math> + sin y) = a and (cos x + cos y) = b, then find the value of (sin x sin y + cos x cos y).</p>",
                    question_hi: "<p>56. यदि (sin <math display=\"inline\"><mi>x</mi></math> + sin y) = a और (cos x + cos y) = b है, तो (sin x sin y + cos x cos y) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>56.(c)<br>(sin x + sin y) = a ----------------- (i)<br>Squaring both side, <br><math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + sin<sup>2</sup>y + 2sinxsiny = a<sup>2</sup> ----------------- (ii)<br>(cos x + cos y) = b ----------------- (iii)<br>Squaring both side, <br><math display=\"inline\"><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>y + 2cosxcosy = b<sup>2</sup> ----------------- (iv)<br>Adding eqn (ii) &amp; (iv) we get ;<br>(<math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>x) + (sin<sup>2</sup>y + cos<sup>2</sup>y) + 2sinxsiny + 2cosxcosy = a<sup>2</sup>+ b<sup>2</sup><br>1 + 1 + 2(sinxsiny + cosxcosy) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ b<sup>2</sup><br>2(1 + sinxsiny + cosxcosy) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ b<sup>2</sup><br>(1 + sinxsiny + cosxcosy) = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>sinxsiny + cosxcosy = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>56.(c)<br>(sin x + sin y) = a ----------------- (i)<br>दोनों पक्षों का वर्ग करने पर, <br><math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + sin<sup>2</sup>y + 2sinxsiny = a<sup>2</sup> ----------------- (ii)<br>(cos x + cos y) = b ----------------- (iii)<br>दोनों पक्षों का वर्ग करने पर, <br><math display=\"inline\"><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>y + 2cosxcosy = b<sup>2</sup> ----------------- (iv)<br>समीकरण (ii) और (iv) जोड़ने पर हमें प्राप्त होता है;<br>(<math display=\"inline\"><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></math>x + cos<sup>2</sup>x) + (sin<sup>2</sup>y + cos<sup>2</sup>y) + 2sinxsiny + 2cosxcosy = a<sup>2</sup>+ b<sup>2</sup><br>1+1+2(sinxsiny + cosxcosy) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ b<sup>2</sup><br>2(1 + sinxsiny + cosxcosy) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup><br>(1 + sinxsiny + cosxcosy) = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>sinxsiny + cosxcosy = <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 1 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Find the maximum number of students among whom 1003 chocolates and 2703 candies can be distributed such that each student gets the same number of each.</p>",
                    question_hi: "<p>57. विद्यार्थियों की वह अधिकतम संख्या ज्ञात कीजिए जिनके बीच 1003 चॉकलेट और 2703 कैंडी इस प्रकार बांटी जा सकती है कि प्रत्येक विद्यार्थी को प्रत्येक (चॉकलेट और कैंडी) की समान संख्या मिले I</p>",
                    options_en: ["<p>17</p>", "<p>29</p>", 
                                "<p>19</p>", "<p>33</p>"],
                    options_hi: ["<p>17</p>", "<p>29</p>",
                                "<p>19</p>", "<p>33</p>"],
                    solution_en: "<p>57.(a)<br>Maximum no of students who got equal no of chocolates and candies <br>= HCF of (1003, 2703) = 17</p>",
                    solution_hi: "<p>57.(a)<br>छात्रों की अधिकतम संख्या जिन्हें बराबर संख्या में चॉकलेट और कैंडी मिलीं<br>= (1003, 2703) का महत्तम समापवर्तक = 17</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A shopkeeper allows a discount of 22% to his customers and still gains 36%. Find the marked price (in₹) of an article which costs ₹1,092 to the shopkeeper.</p>",
                    question_hi: "<p>58. एक दुकानदार अपने ग्राहकों को 22% की छूट देता है और फिर भी 36% का लाभ अर्जित करता है। यदि दुकानदार के लिए वस्तु की लागत ₹1,092 है, तो वस्तु का अंकित मूल्य (₹ में) ज्ञात कीजिए |</p>",
                    options_en: ["<p>1,792</p>", "<p>2,024</p>", 
                                "<p>1,904</p>", "<p>1,872</p>"],
                    options_hi: ["<p>1,792</p>", "<p>2,024</p>",
                                "<p>1,904</p>", "<p>1,872</p>"],
                    solution_en: "<p>58.(c) <br>Marked price = 1092 &times; <math display=\"inline\"><mfrac><mrow><mn>136</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math> = ₹1904</p>",
                    solution_hi: "<p>58.(c) <br>अंकित मूल्य = 1092 &times; <math display=\"inline\"><mfrac><mrow><mn>136</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math> = ₹1904</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. In &Delta;ABC, O is the point of intersection of the bisectors of angle B and angle A. If the angle BOC = 108&deg;, then angle BAO is:</p>",
                    question_hi: "<p>59. △ABC में, O, कोण B और कोण A के सम&zwnj;द्विभाजक का प्रतिच्छेदन बिंदु है। यदि कोण BOC = 108&deg; है, तो कोण BAO होगाः</p>",
                    options_en: ["<p>18&deg;</p>", "<p>26&deg;</p>", 
                                "<p>22&deg;</p>", "<p>16&deg;</p>"],
                    options_hi: ["<p>18&deg;</p>", "<p>26&deg;</p>",
                                "<p>22&deg;</p>", "<p>16&deg;</p>"],
                    solution_en: "<p>59.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175019781.png\" alt=\"rId44\" width=\"178\" height=\"118\"><br>In <math display=\"inline\"><mi>&#916;</mi></math>ABC,<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;BOC = 90&deg; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"10px\"><mfrac><mrow><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>C</mi></mrow><mn>2</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 108&deg; - 90&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"10px\"><mfrac><mrow><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>C</mi></mrow><mn>2</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;BAC = 36&deg;&nbsp;</p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math>&ang;BAO = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"10px\"><mfrac><mrow><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>C</mi></mrow><mn>2</mn></mfrac></mstyle></math> = 18&deg;<br>So, &ang;BAO = 18&deg;</p>",
                    solution_hi: "<p>59.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175019781.png\" alt=\"rId44\" width=\"178\" height=\"118\"><br><math display=\"inline\"><mi>&#916;</mi></math>ABC में,</p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> &ang;BOC = 90&deg; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"10px\"><mfrac><mrow><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>C</mi></mrow><mn>2</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 108&deg; - 90&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"10px\"><mfrac><mrow><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>C</mi></mrow><mn>2</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;BAC = 36&deg;&nbsp;</p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math>&ang;BAO =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"10px\"><mfrac><mrow><mo>&#8736;</mo><mi>B</mi><mi>A</mi><mi>C</mi></mrow><mn>2</mn></mfrac></mstyle></math> = 18&deg;<br>इसलिए, &ang;BAO = 18&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A, B and C invested money in a business such that 2 times A\'s share, 4 times B\'s share and 6 times C\'s share are all equal. If they earned ₹44,000, find the share of A.</p>",
                    question_hi: "<p>60. A, B और C ने एक व्यवसाय में धनराशि का निवेश इस प्रकार किया कि A के हिस्से का 2 गुना, B के हिस्से का 4 गुना और C के हिस्से का 6 गुना सभी बराबर हैं। यदि उन्होंने ₹44,000 अर्जित किए, तो A का हिस्सा ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹22,000</p>", "<p>₹12,000</p>", 
                                "<p>₹24,000</p>", "<p>₹20,000</p>"],
                    options_hi: ["<p>₹22,000</p>", "<p>₹12,000</p>",
                                "<p>₹24,000</p>", "<p>₹20,000</p>"],
                    solution_en: "<p>60.(c) If 2 times A\'s share, 4 times B\'s share and 6 times C\'s share are all equal<br>&there4; 2A = 4B = 6C <br>Hence , A : B : C = 6 : 3 : 2<br>Now , A&rsquo;s share = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 44000 = 24,000</p>",
                    solution_hi: "<p>60.(c) यदि A के हिस्से का 2 गुना, B के हिस्से का 4 गुना और C के हिस्से का 6 गुना सभी बराबर हैं<br>&there4; 2A = 4B = 6C <br>अतः , A : B : C = 6 : 3 : 2<br>अब, A का हिस्सा = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 44000 = 24,000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The pie chart given below shows the number of hours spent by a student for different activities on a working day.<br>The percentage of hours spent at school in a day is _____.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175020012.png\" alt=\"rId45\" width=\"238\" height=\"234\"></p>",
                    question_hi: "<p>61. नीचे दिया गया पाई चार्ट एक कार्य दिवस में एक छात्र द्वारा विभिन्न गतिविधियों में बिताए गए घंटों की संख्या को दर्शाता है।<br>एक दिन में स्कूल में बिताए गए घंटों का प्रतिशत _____ है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175020112.png\" alt=\"rId46\" width=\"185\" height=\"189\"></p>",
                    options_en: ["<p>25%</p>", "<p>35%</p>", 
                                "<p>40%</p>", "<p>30%</p>"],
                    options_hi: ["<p>25%</p>", "<p>35%</p>",
                                "<p>40%</p>", "<p>30%</p>"],
                    solution_en: "<p>61.(a)<br>The percentage of hours spent at school in a day = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> &times; 100<br>= 25 %</p>",
                    solution_hi: "<p>61.(a)<br>एक दिन में स्कूल में बिताए गए घंटों का प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> &times; 100<br>= 25 %</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. A laptop charger is sold for Rs.2,540 in cash or for a down payment of Rs.1,340 in cash together with Rs.1,205 to be paid after one month. Find the rate of interest charged in the installment scheme.</p>",
                    question_hi: "<p>62. एक लैपटॉप चार्जर को Rs.2,540 नकद में या Rs.1,340 नकद और एक महीने के बाद Rs.1,205 के भुगतान पर बेचा जाता है। किश्त योजना में लिए जाने वाले ब्याज की दर ज्ञात कीजिए।</p>",
                    options_en: ["<p>5% p.a.</p>", "<p>10% p.a.</p>", 
                                "<p>15% p.a.</p>", "<p>20% p.a.</p>"],
                    options_hi: ["<p>5% प्रति वर्ष</p>", "<p>10% प्रति वर्ष</p>",
                                "<p>15% प्रति वर्ष</p>", "<p>20% प्रति वर्ष</p>"],
                    solution_en: "<p>62.(a) <br>Amount to be paid = 2540 - 1340 = ₹1200<br>SI = 1205 - 1200 = ₹5<br>SI = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>5 = <math display=\"inline\"><mfrac><mrow><mn>1200</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>1</mn></mrow><mrow><mn>12</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac></math><br>R = 5%</p>",
                    solution_hi: "<p>62.(a) <br>भुगतान की जाने वाली राशि = 2540 - 1340 = ₹1200<br>साधारण ब्याज = 1205 - 1200 = ₹5<br>साधारण ब्याज = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>5 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1200</mn><mo>&#215;</mo><mi>&#160;</mi><mi>&#2342;</mi><mi>&#2352;</mi><mo>&#215;</mo><mn>1</mn></mrow><mrow><mn>12</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac></math><br>दर = 5%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. 18 workers can complete a piece of work in 96 days. They start working together and after 26 days 10 more workers join them. In how many days in all will the work be completed?</p>",
                    question_hi: "<p>63. 18 कर्मचारी एक कार्य को 96 दिनों में पूरा कर सकते हैं। वे एक साथ काम करना शुरू करते हैं और 26 दिनों के बाद 10 और कर्मचारी उनके साथ जुड़ जाते हैं। कार्य को कुल मिलाकर कितने दिनों में पूरा किया जाएगा?</p>",
                    options_en: ["<p>69</p>", "<p>71</p>", 
                                "<p>72</p>", "<p>70</p>"],
                    options_hi: ["<p>69</p>", "<p>71</p>",
                                "<p>72</p>", "<p>70</p>"],
                    solution_en: "<p>63.(b)<br>Formula used : - <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>D</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>H</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>W</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>M</mi><mn>2</mn></msub><msub><mi>D</mi><mn>2</mn></msub><msub><mi>H</mi><mn>2</mn></msub></mrow><msub><mi>W</mi><mn>2</mn></msub></mfrac></math><br>According to the question,<br>18 &times; 96 = 18 &times; 26 + (28 &times; x)<br>18 &times; 96 - 18 &times; 26 = (28 &times; x)<br><math display=\"inline\"><mn>18</mn><mo>(</mo><mn>96</mn></math> - 26) = (28 &times; x)<br><math display=\"inline\"><mn>18</mn></math> &times; 70 = (28 &times; x)<br><math display=\"inline\"><mi>x</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>70</mn></mrow><mn>28</mn></mfrac></math>&nbsp;= 45 days<br>Time taken to complete the whole work = 45 + 26 = 71 days</p>",
                    solution_hi: "<p>63.(b)<br>प्रयुक्त सूत्र:-- <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>M</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>D</mi></mrow><mrow><mn>1</mn></mrow></msub><msub><mrow><mi>H</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>W</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msub><mi>M</mi><mn>2</mn></msub><msub><mi>D</mi><mn>2</mn></msub><msub><mi>H</mi><mn>2</mn></msub></mrow><msub><mi>W</mi><mn>2</mn></msub></mfrac></math><br>प्रश्न के अनुसार,<br>18 &times; 96 = 18 &times; 26 + (28 &times; x)<br>18 &times; 96 - 18 &times; 26 = (28 &times; x)<br><math display=\"inline\"><mn>18</mn><mo>(</mo><mn>96</mn></math> - 26) = (28 &times; x)<br><math display=\"inline\"><mn>18</mn></math> &times; 70 = (28 &times; x)<br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>70</mn></mrow><mn>28</mn></mfrac></math> = 45 दिन<br>संपूर्ण कार्य को पूरा करने में लगा समय = 45 + 26 = 71 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Average age of 22 teachers is 64 years. After involving a new teacher What is the age of the new teacher? in that group the average increases by 1 year</p>",
                    question_hi: "<p>64. 22 शिक्षकों की औसत आयु 64 वर्ष है। उस समूह में एक नए शिक्षक को शामिल करने के बाद औसत में 1 वर्ष की वृद्धि हो जाती है। नए शिक्षक की आयु कितनी है?</p>",
                    options_en: ["<p>93 years</p>", "<p>87 years</p>", 
                                "<p>89 years</p>", "<p>91 years</p>"],
                    options_hi: ["<p>93 वर्ष</p>", "<p>87 वर्ष</p>",
                                "<p>89 वर्ष</p>", "<p>91 वर्ष</p>"],
                    solution_en: "<p>64.(b)<br>Age of new teacher = 64 + 1 &times; 23 = 87 yrs</p>",
                    solution_hi: "<p>64.(b)<br>नये शिक्षक की आयु = 64 + 1 &times; 23 = 87 वर्ष</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Simplify <br><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>5</mn><mn>4</mn></mfrac></mrow></mfenced><mo>&#215;</mo><mfenced separators=\"|\"><mrow><mfrac><mn>9</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>4</mn><mn>5</mn></mfrac></mrow></mfenced><mo>&#247;</mo><mfenced separators=\"|\"><mrow><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></mrow></mfenced></math></p>",
                    question_hi: "<p>65. <math display=\"inline\"><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></mrow></mfenced><mo>&#215;</mo><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>9</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></mrow></mfenced><mo>&#247;</mo><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfenced></math> को सरल कीजिए I</p>",
                    options_en: ["<p>2</p>", "<p>&minus;1</p>", 
                                "<p>&minus;2</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>&minus;1</p>",
                                "<p>&minus;2</p>", "<p>1</p>"],
                    solution_en: "<p>65.(d)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>5</mn><mn>4</mn></mfrac></mrow></mfenced><mo>&#215;</mo><mfenced separators=\"|\"><mrow><mfrac><mn>9</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>4</mn><mn>5</mn></mfrac></mrow></mfenced><mo>&#247;</mo><mfenced separators=\"|\"><mrow><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></mrow></mfenced></math><br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>5</mn></mfrac></math>&divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>2</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>4</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>5</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math>= 1</p>",
                    solution_hi: "<p>65.(d)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>5</mn><mn>4</mn></mfrac></mrow></mfenced><mo>&#215;</mo><mfenced separators=\"|\"><mrow><mfrac><mn>9</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>4</mn><mn>5</mn></mfrac></mrow></mfenced><mo>&#247;</mo><mfenced separators=\"|\"><mrow><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></mrow></mfenced></math><br><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>5</mn></mfrac></math>&divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>2</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>4</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>5</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>4</mn></mfrac></math>= 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If 2<math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></math> + 3cot<sup>2</sup>&theta; = 17, then the value of &lsquo;&theta;&rsquo; when 0&deg;&le; &theta; &le; 90&deg; is:</p>",
                    question_hi: "<p>66. यदि 2<math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></math> + 3cot<sup>2</sup>&theta; = 17 है, तो 0&deg;&le; &theta; &le; 90&deg; होने पर, &lsquo;&theta;&rsquo; का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>45&deg;</p>", "<p>90&deg;</p>", 
                                "<p>30&deg;</p>", "<p>75&deg;</p>"],
                    options_hi: ["<p>45&deg;</p>", "<p>90&deg;</p>",
                                "<p>30&deg;</p>", "<p>75&deg;</p>"],
                    solution_en: "<p>66.(c)<br>2<math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></math> + 3cot<sup>2</sup>&theta; = 17<br>By putting the values in the options one by one</p>\n<p><math display=\"inline\"><mi>&#952;</mi></math> = 30&deg; satisfies the equation:<br>&rArr; 2<math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></math> + 3cot<sup>2</sup>&theta; = 2cosec<sup>2</sup>30&deg; + 3cot<sup>2</sup>30&deg; <br>= 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>1</mn></mfrac></math>+ 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>1</mn></mfrac></math><br>= 8 + 9 = 17</p>",
                    solution_hi: "<p>66.(c)<br>2<math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></math> + 3cot<sup>2</sup>&theta; = 17<br>विकल्पों की एक-एक करके जांच करने पर,&nbsp;</p>\n<p><math display=\"inline\"><mi>&#952;</mi></math> = 30&deg; समीकरण को संतुष्ट कर रहा है।&nbsp;<br>&rArr; 2<math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></math> + 3cot<sup>2</sup>&theta; = 2cosec<sup>2</sup>30&deg; + 3cot<sup>2</sup>30&deg;<br>= 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>1</mn></mfrac></math>+ 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>1</mn></mfrac></math><br>= 8 + 9 = 17</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The average temperature of a city for the first four days of the week is 25&deg;C, and the average temperature for the last four days is 30&deg;C. If the average temperature for the entire week is 27&deg;C, then what is the temperature on the fourth day?</p>",
                    question_hi: "<p>67. एक सप्ताह के पहले चार दिनों के लिए एक शहर का औसत तापमान 25&deg;C है, और अंतिम चार दिनों का औसत तापमान 30&deg;C है। यदि पूरे सप्ताह का औसत तापमान 27&deg;C है, तो चौथे दिन का तापमान कितना है ?</p>",
                    options_en: ["<p>29&deg;C</p>", "<p>31&deg;C</p>", 
                                "<p>30&deg;C</p>", "<p>28&deg;C</p>"],
                    options_hi: ["<p>29&deg;C</p>", "<p>31&deg;C</p>",
                                "<p>30&deg;C</p>", "<p>28&deg;C</p>"],
                    solution_en: "<p>67.(b)<br>Total temperature of a city for the first four days of the week = 100&deg;C<br>Total temperature of a city for the last four days of the week = 120&deg;C<br>Total temperature of a city for eight days of the week = 100 + 120 = 220&deg;C<br>Total temperature for the seven days of the week = 27 <math display=\"inline\"><mo>&#215;</mo></math> 7 = 189&deg;C<br><math display=\"inline\"><mo>&#8658;</mo></math> the temperature on the fourth day = 220 - 189 =31&deg;C</p>",
                    solution_hi: "<p>67.(b)<br>सप्ताह के पहले चार दिनों के लिए शहर का कुल तापमान = 100&deg;C<br>सप्ताह के अंतिम चार दिनों के लिए शहर का कुल तापमान = 120&deg;C<br>सप्ताह के आठ दिनों में शहर का कुल तापमान = 100 + 120 = 220&deg;C<br>सप्ताह के सातों दिनों का कुल तापमान = 27 &times; 7 = 189&deg;C<br>चौथे दिन का तापमान = 220 - 189 = 31&deg;C</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Riya offers 12% discount on all her goods and offers an additional discount of 6% to those customers who pay in cash. How much will a customer have to pay for a fan marked for ₹2,400 if he pays in cash?</p>",
                    question_hi: "<p>68. रिया अपनी सभी वस्&zwj;तुओं पर 12% की छूट प्रदान करती है और नकद भुगतान करने वाले ग्राहकों को 6% का अतिरिक्त छूट प्रदान करती है। एक ग्राहक को ₹2,400 अंकित मूल्&zwj;य वाले पंखे के लिए कितना भुगतान करना होगा यदि वह नकद भुगतान करता है?</p>",
                    options_en: ["<p>₹1,985.28</p>", "<p>₹1,900</p>", 
                                "<p>₹2,300.50</p>", "<p>₹2,122.50</p>"],
                    options_hi: ["<p>₹1,985.28</p>", "<p>₹1,900</p>",
                                "<p>₹2,300.50</p>", "<p>₹2,122.50</p>"],
                    solution_en: "<p>68.(a)<br>Money paid by customer = 2400 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>47</mn><mn>50</mn></mfrac></math> = ₹1,985.28</p>",
                    solution_hi: "<p>68.(a)<br>ग्राहक द्वारा किया गया भुगतान = 2400 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>47</mn><mn>50</mn></mfrac></math> = ₹1,985.28</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. If a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math> = 12, then find the value of a<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac></math>.</p>",
                    question_hi: "<p>69. यदि a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math> = 12 है, तो a<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>144</p>", "<p>146</p>", 
                                "<p>142</p>", "<p>140</p>"],
                    options_hi: ["<p>144</p>", "<p>146</p>",
                                "<p>142</p>", "<p>140</p>"],
                    solution_en: "<p>69.(c) <br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac></math>= (a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>a</mi></mfrac></math>)<sup>2</sup> - 2&nbsp;<br>= <math display=\"inline\"><mn>1</mn><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></math> - 2 = 142</p>",
                    solution_hi: "<p>69.(c) <br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac></math>= (a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>a</mi></mfrac></math>)<sup>2</sup> - 2&nbsp;<br>= <math display=\"inline\"><mn>1</mn><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></math> - 2 = 142</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A hemispherical bowl has a 21 cm radius. It is to be painted inside as well as outside. The cost of painting it at the rate of ₹0.05 per cm<sup>2</sup> and assuming that the thickness of the bowl is negligible, is: (Use &pi; =<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)</p>",
                    question_hi: "<p>70. एक अर्द्धगोलाकार कटोरे की त्रिज्या 21 cm है। इसे अंदर के साथ-साथ बाहर भी पेंट किया जाना है। ₹0.05 प्रति cm<sup>2</sup> की दर से इसे पेंट करने की लागत की गणना करें, यह मान लें कि कटोरे की मोटाई नगण्य है। (&pi; =<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> लें)</p>",
                    options_en: ["<p>₹410.10</p>", "<p>₹188.30</p>", 
                                "<p>₹277.20</p>", "<p>₹388.20</p>"],
                    options_hi: ["<p>₹410.10</p>", "<p>₹188.30</p>",
                                "<p>₹277.20</p>", "<p>₹388.20</p>"],
                    solution_en: "<p>70.(c)<br>Required area of hemisphere = 2<math display=\"inline\"><mi>&#960;</mi></math>r<sup>2 </sup>+ 2&pi;r<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> A = 4&pi;r<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> A = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 21 &times; 21 = 5544<br>Hence,<br>cost of painting = 5544 <math display=\"inline\"><mo>&#215;</mo></math> 0.05 = 277.2 Rs.</p>",
                    solution_hi: "<p>70.(c)<br>गोलार्ध का आवश्यक क्षेत्रफल = 2<math display=\"inline\"><mi>&#960;</mi></math>r<sup>2 </sup>+ 2&pi;r<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> A = 4&pi;r<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> A = 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 21 &times; 21 = 5544<br>इस तरह,<br>पेंटिंग की लागत = 5544 <math display=\"inline\"><mo>&#215;</mo></math> 0.05 = 277.2 रु.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. In a circle, O is the centre and AOB is the diameter. AT is a tangent to the circle. Line TB intersects the circle at Q . Given that &ang;AOQ = 94&deg;, find &ang;ATQ .</p>",
                    question_hi: "<p>71. एक वृत्त में, O केंद्र है और AOB व्यास है। AT वृत्त की स्पर्श रेखा है। रेखा TB वृत्त को Q पर प्रतिच्छेद करती है। दिया गया है कि &ang;AOQ = 94&deg; है, तो &ang;ATQ ज्ञात कीजिए।</p>",
                    options_en: ["<p>133&deg;</p>", "<p>43&deg;</p>", 
                                "<p>86&deg;</p>", "<p>47&deg;</p>"],
                    options_hi: ["<p>133&deg;</p>", "<p>43&deg;</p>",
                                "<p>86&deg;</p>", "<p>47&deg;</p>"],
                    solution_en: "<p>71.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175020362.png\" alt=\"rId47\" width=\"173\" height=\"104\"><br>&ang;AQB = 90&deg; (angle subtended by a diameter on any point of a circle is always 90&deg;)<br>So, &ang;TQA = 90&deg;<br>&ang;OAQ = <math display=\"inline\"><mfrac><mrow><mn>180</mn><mo>&#176;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>94</mn><mo>&#176;</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 43&deg;<br>&ang;QAT = 90&deg; - 43&deg; = 47&deg;<br>In <math display=\"inline\"><mi>&#916;</mi></math>TQA,<br>&ang;QTA = 180&deg; - (90&deg; + 47&deg;)<br>&ang;QTA = 180&deg; - 137&deg; = 43&deg;<br>Required angle(&ang;QTA) = 43&deg;</p>",
                    solution_hi: "<p>71.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744175020362.png\" alt=\"rId47\" width=\"173\" height=\"104\"><br>&ang;AQB = 90&deg; (वृत्त के किसी भी बिंदु पर व्यास द्वारा बनाया गया कोण हमेशा 90&deg; होता है)<br>So, &ang;TQA = 90&deg;<br>&ang;OAQ = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>180</mn><mo>&#176;</mo><mo>-</mo><mn>94</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math> = 43&deg;<br>&ang;QAT = 90&deg; - 43&deg; = 47&deg;<br><math display=\"inline\"><mi>&#916;</mi></math>TQA में,<br>&ang;QTA = 180&deg; - (90&deg; + 47&deg;)<br>&ang;QTA = 180&deg; - 137&deg; = 43&deg;<br>आवश्यक कोण (&ang;QTA) = 90&deg; - 47&deg; = 43&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. At a school building, there is an overhead tank. To fill this tank 50 buckets of water are required. Assume that the capacity of the bucket is reduced to two-fifth of the present. How many buckets of water are required to fill the same tank?</p>",
                    question_hi: "<p>72. एक स्कूल बिल्डिंग में एक ओवरहेड टैंक है। इस टैंक को भरने के लिए 50 बाल्टी पानी की आवश्यकता होती है। मान लीजिए कि बाल्टी की क्षमता इसकी वर्तमान क्षमता का दो-पाँचवां भाग कर दी जाती है। तो उसी टैंक को भरने के लिए कितनी बाल्टी पानी की आवश्यकता होगी?</p>",
                    options_en: ["<p>62.5</p>", "<p>20</p>", 
                                "<p>125</p>", "<p>60</p>"],
                    options_hi: ["<p>62.5</p>", "<p>20</p>",
                                "<p>125</p>", "<p>60</p>"],
                    solution_en: "<p>72.(c) Let the initial capacity of bucket = 5 <br>After reducing two-fifth of the initial , the capacity of bucket = 2 <br>Total capacity of the tank = 5 &times; 50 = 250 <br>After reduction , required no of bucket to fill the same tank = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>250</mn><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math> = 125</p>",
                    solution_hi: "<p>72.(c) <br>माना बाल्टी की प्रारंभिक क्षमता = 5 <br>प्रारंभिक का दो-पाँचवाँ हिस्सा कम करने के बाद, बाल्टी की क्षमता = 2 <br>टैंक की कुल क्षमता = 5 &times; 50 = 250 <br>कमी के बाद, उसी टंकी को भरने के लिए आवश्यक बाल्टी की संख्या = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>250</mn><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math> = 125</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A truck driver drove for 4 hours at 40 km/h. He drove for another 3 hours at 50 km/h after taking rest for 1 h. What was the driver\'s average speed, in km/h, during the whole journey?</p>",
                    question_hi: "<p>73. एक ट्रक ड्राइवर 40 km/h की चाल से 4 घंटे तक गाड़ी चलाता है। 1 घंटे आराम करने के बाद वह 50 km/h की रफ्तार से और 3 घंटे के लिए गाड़ी चलाता है। पूरी यात्रा के दौरान चालक की औसत चाल km/h में क्या थी?</p>",
                    options_en: ["<p>40</p>", "<p>38.75</p>", 
                                "<p>39.25</p>", "<p>36.50</p>"],
                    options_hi: ["<p>40</p>", "<p>38.75</p>",
                                "<p>39.25</p>", "<p>36.50</p>"],
                    solution_en: "<p>73.(b)<br>Average speed = <math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>50</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>160</mn><mo>+</mo><mn>150</mn></mrow><mn>8</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>310</mn><mn>8</mn></mfrac></math>= 38.75 km/hr</p>",
                    solution_hi: "<p>73.(b)<br>औसत गति = <math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>50</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>160</mn><mo>+</mo><mn>150</mn></mrow><mn>8</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>310</mn><mn>8</mn></mfrac></math>= 38.75 km/hr</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. 45% of Samita\'s monthly income is equal to 63% of Anil\'s monthly income. If Samita\'s monthly income is ₹84,000, what is Anil\'s monthly income (in ₹)?</p>",
                    question_hi: "<p>74. समिता की मासिक आय का 45% अनिल की मासिक आय के 63% के बराबर है। यदि समिता की मासिक आय ₹84,000 है, तो अनिल की मासिक आय (₹ में) क्या है?</p>",
                    options_en: ["<p>59,400</p>", "<p>61,500</p>", 
                                "<p>58,500</p>", "<p>60,000</p>"],
                    options_hi: ["<p>59,400</p>", "<p>61,500</p>",
                                "<p>58,500</p>", "<p>60,000</p>"],
                    solution_en: "<p>74.(d)<br>Monthly income of Samita = ₹ 84,000<br>According to the question,<br>45% of 84,000 = 63% of Anil<br>Monthly income of Anil = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>84</mn><mo>,</mo><mn>000</mn><mo>&#215;</mo><mn>45</mn></mrow><mn>63</mn></mfrac></math> = ₹ 60,000</p>",
                    solution_hi: "<p>74.(d)<br>समिता की मासिक आय = ₹ 84,000<br>प्रश्न के अनुसार,<br>84,000 का 45% = अनिल का 63%<br>अनिल की मासिक आय = <math display=\"inline\"><mfrac><mrow><mn>84</mn><mo>,</mo><mn>000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>45</mn></mrow><mrow><mn>63</mn></mrow></mfrac></math> = ₹ 60,000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. A person spends 40% of his salary on food items, 50% of the remaining on transport, and 30% of the remaining on clothes. After spending on food, transport and clothes, he saves ₹1,260 every month, what is his monthly salary?</p>",
                    question_hi: "<p>75. एक व्यक्ति अपने वेतन का 40% खाद्य पदार्थों पर, शेष का 50% परिवहन पर और शेष का 30% कपड़ों पर खर्च करता है। भोजन, परिवहन और कपड़ों पर खर्च करने के बाद, वह हर महीने ₹1,260 बचाता है, उसका मासिक वेतन कितना है?</p>",
                    options_en: ["<p>₹8,000</p>", "<p>₹6,000</p>", 
                                "<p>₹7,000</p>", "<p>₹10,000</p>"],
                    options_hi: ["<p>₹8,000</p>", "<p>₹6,000</p>",
                                "<p>₹7,000</p>", "<p>₹10,000</p>"],
                    solution_en: "<p>75.(b)<br>Let the monthly salary be x<br>ATQ,<br>x &times;<math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>100</mn></mfrac></math>= 1260<br>x &times;<math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1260<br>x = 1260 &times;<math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> = 6000<br>So, the monthly salary of a person is ₹6,000</p>",
                    solution_hi: "<p>75.(b)<br>माना मासिक वेतन x है<br>प्रश्ननानुसार,<br>x &times;<math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>100</mn></mfrac></math>= 1260<br>x &times;<math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 1260<br>x = 1260 &times;<math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> = 6000<br>तो, एक व्यक्ति का मासिक वेतन ₹6,000 हैI</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has been divided into five segments. The first segment has no error. Select the option that has the segment with the grammatical error. <br>Due to the pandemic, / the prices of / medicines increased / every day since / last December.</p>",
                    question_hi: "<p>76. The following sentence has been divided into five segments. The first segment has no error. Select the option that has the segment with the grammatical error. <br>Due to the pandemic, / the prices of / medicines increased / every day since / last December.</p>",
                    options_en: ["<p>every day since</p>", "<p>medicines increased</p>", 
                                "<p>last December.</p>", "<p>the prices of</p>"],
                    options_hi: ["<p>every day since</p>", "<p>medicines increased</p>",
                                "<p>last December.</p>", "<p>the prices of</p>"],
                    solution_en: "<p>76.(b) medicines increased<br>We use present continuous tense with &lsquo;for/since&rsquo; to show that something has been happening for a period of time. &lsquo;Plural subject (prices) + have been + Ving&rsquo; is the correct grammatical structure for it. Hence, &lsquo;medicines have been increasing&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(b) medicines increased<br>\'For/since\' के साथ present continuous tense का प्रयोग यह दर्शाने के लिए किया जाता हैं कि कोई चीज कुछ समय से घटित हो रही है। इसके लिए सही grammatical structure, &lsquo;Plural subject (prices) + have been + Ving&rsquo; है। अतः, &lsquo;medicines have been increasing&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Identify the segment in the sentence, which contains the grammatical error.<br>In this novel I came across some words which meaning I do not know.</p>",
                    question_hi: "<p>77. Identify the segment in the sentence, which contains the grammatical error.<br>In this novel I came across some words which meaning I do not know.</p>",
                    options_en: ["<p>came across</p>", "<p>which meaning</p>", 
                                "<p>In this novel</p>", "<p>do not know</p>"],
                    options_hi: ["<p>came across</p>", "<p>which meaning</p>",
                                "<p>In this novel</p>", "<p>do not know</p>"],
                    solution_en: "<p>77.(b) &ldquo;which meaning&rdquo;is incorrect. <br>According to the context of the sentence, the phrase &lsquo;meanings of which&rsquo; will be the correct sentence structure. Because the word &ldquo;meanings&rdquo; refers to the meaning of different words.</p>",
                    solution_hi: "<p>77.(b) &ldquo;which meaning&rdquo;is incorrect. <br>वाक्य के सन्दर्भ के अनुसार &lsquo;meanings of which&rsquo; सही होगा। क्योंकि &ldquo;meanings&rdquo; शब्द विभिन्न शब्दों के अर्थ को दर्शाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>He was a failure at art but his last piece was <span style=\"text-decoration: underline;\"><strong>so beautiful that no one could believe he had painted it.</strong></span></p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>He was a failure at art but his last piece was<strong> </strong><span style=\"text-decoration: underline;\"><strong>so beautiful that no one could believe he had painted it.</strong></span></p>",
                    options_en: ["<p>indeed a masterpiece !</p>", "<p>No improvement</p>", 
                                "<p>breath talking enough to be unreal.</p>", "<p>very realistic and unbelievable.</p>"],
                    options_hi: ["<p>indeed a masterpiece !</p>", "<p>No improvement</p>",
                                "<p>breath talking enough to be unreal.</p>", "<p>very realistic and unbelievable.</p>"],
                    solution_en: "<p>78.(b) No improvement.</p>",
                    solution_hi: "<p>78.(b) No improvement./कोई सुधार नहीं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate meaning of the given idiom.<br>Keep your pants on</p>",
                    question_hi: "<p>79. Select the most appropriate meaning of the given idiom.<br>Keep your pants on</p>",
                    options_en: ["<p>To keep calm and quiet</p>", "<p>To wear the dress neatly</p>", 
                                "<p>To work like a tailor</p>", "<p>To make things proper</p>"],
                    options_hi: ["<p>To keep calm and quiet</p>", "<p>To wear the dress neatly</p>",
                                "<p>To work like a tailor</p>", "<p>To make things proper</p>"],
                    solution_en: "<p>79.(a) <strong>Keep your pants on </strong>- to keep calm and quiet.<br>E.g.- Keep your pants on! The movie will start in just a few minutes.</p>",
                    solution_hi: "<p>79.(a) <strong>Keep your pants on</strong> - to keep calm and quiet./शांत और चुप रहना।<br>E.g.- Keep your pants on! The movie will start in just a few minutes.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that expresses the given sentence in active voice.<br>The meal was not prepared by us.</p>",
                    question_hi: "<p>80. Select the option that expresses the given sentence in active voice.<br>The meal was not prepared by us.</p>",
                    options_en: ["<p>We had not prepare the meal.</p>", "<p>We did not prepare the meal.</p>", 
                                "<p>We did not preparing the meal.</p>", "<p>We did prepare the meal.</p>"],
                    options_hi: ["<p>We had not prepare the meal.</p>", "<p>We did not prepare the meal.</p>",
                                "<p>We did not preparing the meal.</p>", "<p>We did prepare the meal.</p>"],
                    solution_en: "<p>80.(b) We did not prepare the meal. (Correct)<br>(a) We <span style=\"text-decoration: underline;\">had</span> not prepare the meal. (Incorrect Helping Verb)<br>(c) We did not <span style=\"text-decoration: underline;\">preparing</span> the meal. (Incorrect Form of the Verb)<br>(d) We did prepare the meal. (&lsquo;Not&rsquo; is missing)</p>",
                    solution_hi: "<p>80.(b) We did not prepare the meal. (Correct)<br>(a) We <span style=\"text-decoration: underline;\">had</span> not prepare the meal. (गलत Helping Verb)<br>(c) We did not <span style=\"text-decoration: underline;\">preparing</span> the meal. (Verb की गलत Form)<br>(d) We did prepare the meal. (&lsquo;Not&rsquo;, missing है)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "81. Select the word which means the same as the group of words given<br />The act of killing a whole group of people, especially a whole race.",
                    question_hi: "81. Select the word which means the same as the group of words given<br />The act of killing a whole group of people, especially a whole race.",
                    options_en: [" Patricide ", " Parricide", 
                                " Matricide ", " Genocide"],
                    options_hi: [" Patricide ", " Parricide",
                                " Matricide ", " Genocide"],
                    solution_en: "81.(d) Genocide<br />Patricide- the crime of killing your father.<br />Parricide- the killing of a parent or other near relative.<br />Matricide- the crime of killing your mother.",
                    solution_hi: "81.(d) Genocide<br />Patricide- अपने पिता की हत्या का अपराध। <br />Parricide- माता-पिता या अन्य निकट संबंधी की हत्या।<br />Matricide- अपनी माँ की हत्‍या का अपराध। <br />             ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Find the misspelt world.</p>",
                    question_hi: "<p>82. Find the misspelt world.</p>",
                    options_en: ["<p>Complement</p>", "<p>Compliment</p>", 
                                "<p>Supplement</p>", "<p>Requirment</p>"],
                    options_hi: ["<p>Complement</p>", "<p>Compliment</p>",
                                "<p>Supplement</p>", "<p>Requirment</p>"],
                    solution_en: "<p>82.(d) Requirment<br>Requirement is the correct spelling.</p>",
                    solution_hi: "<p>82.(d) Requirment<br>Requirement सही वर्तनी है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "83. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) He made only what was ordered, and what he made never failed to fit.<br />(B) The shop had a certain quiet distinction.<br />(C ) There was no sign upon it other than the name of Gessler Brothers.<br />(D) And in the window were displayed a few pairs of boots.",
                    question_hi: "83. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) He made only what was ordered, and what he made never failed to fit.<br />(B) The shop had a certain quiet distinction.<br />(C ) There was no sign upon it other than the name of Gessler Brothers.<br />(D) And in the window were displayed a few pairs of boots.",
                    options_en: [" DCBA ", " BCAD", 
                                " BCDA     ", " CBAD"],
                    options_hi: [" DCBA ", " BCAD",
                                " BCDA     ", " CBAD"],
                    solution_en: "83.(c) BCDA<br />Sentence B will be the starting line as it contains the main idea of the parajumble i.e. the distinction which the shop had. Sentence C states that there was no other sign on it other than the name of Gessler Brothers . So, C will follow B. Further, Sentence D states that there were a few pair of boots in the display and Sentence A talks about the boots mentioned in D  . So, A will follow D . Going through the options, option ( c) BCDA has the correct sequence.",
                    solution_hi: "83.(c) BCDA<br />Sentence B  प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार शामिल है यानी the distinction which the shop had.  Sentence C में कहा गया है कि Gessler Brothers के नाम के अलावा इस पर कोई अन्य चिन्ह नहीं था। तो,  B के बाद C आएगा । आगे, वाक्य D कहता है कि प्रदर्शन में कुछ जोड़ी जूते थे और वाक्य A, D में उल्लिखित बूटों के बारे में बात करता है। अत: D के बाद A आएगा । विकल्पों को देखते हुए, option ( c) BCDA का सही क्रम है।. ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "84. Find the misspelt world.",
                    question_hi: "84. Find the misspelt world.",
                    options_en: [" Begining ", " Winning", 
                                " Mining ", " Running"],
                    options_hi: [" Begining ", " Winning",
                                " Mining ", " Running"],
                    solution_en: "84.(a) Begining.<br />Beginning is the correct spelling.",
                    solution_hi: "84. (a) Begining. <br />Beginning सही spelling है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "85. Pick a word opposite in meaning to<br />Wary",
                    question_hi: "85. Pick a word opposite in meaning to<br />Wary",
                    options_en: [" Free ", " Careless ", 
                                " Watchful                         ", " Kind"],
                    options_hi: [" Free ", " Careless ",
                                " Watchful                         ", " Kind"],
                    solution_en: "85.(b) Careless<br />Wary means to be careful.                                         ",
                    solution_hi: "85.(b) Careless<br />Wary अर्थ है सावधान रहना ।         ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Choose the best alternative which best expresses the meaning of the idiom/ phrase.<br><span style=\"text-decoration: underline;\">bury the hatchet</span></p>",
                    question_hi: "<p>86. Choose the best alternative which best expresses the meaning of the idiom/ phrase.<br><span style=\"text-decoration: underline;\">bury the hatchet</span></p>",
                    options_en: ["<p>obtain</p>", "<p>influence friends</p>", 
                                "<p>make peace</p>", "<p>keep a secret</p>"],
                    options_hi: ["<p>obtain</p>", "<p>influence friends</p>",
                                "<p>make peace</p>", "<p>keep a secret</p>"],
                    solution_en: "<p>86.(c) make peace<br>Eg- After not speaking to each other for years, the two brothers decided to bury the hatchet.</p>",
                    solution_hi: "<p>86.(c) make peace/ शांति रखना <br>बरसों तक एक-दूसरे से बात न करने के बाद दोनों भाइयों ने मनमुटाव मिटाने का फैसला किया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the word which means the same as the group of words given.<br>Continuing for a very long time.</p>",
                    question_hi: "<p>87. Select the word which means the same as the group of words given.<br>Continuing for a very long time.</p>",
                    options_en: ["<p>laconic</p>", "<p>interim</p>", 
                                "<p>concise</p>", "<p>interminable</p>"],
                    options_hi: ["<p>laconic</p>", "<p>interim</p>",
                                "<p>concise</p>", "<p>interminable</p>"],
                    solution_en: "<p>87.(d) <strong>interminable</strong>-endless or <br>apparently endless (often used hyperbolically).<br>(a)<strong>laconic</strong>- using very few words.<br>(b)<strong>interim</strong>-the intervening time.<br>(c)<strong>concise</strong>- brief but comprehensive.</p>",
                    solution_hi: "<p>87.(d) <strong>interminable</strong>-अनंत-endless or apparently endless (often used hyperbolically).<br>(a) <strong>laconic</strong> -संक्षिप्त- using very few words.<br>(b) <strong>interim</strong> -अन्तरिम-the intervening time.<br>(c) <strong>concise</strong> -संक्षिप्त- brief but comprehensive.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Children were often helping their families at work from the age of five or six.<br>(B) The early factory system depended on the labour of children.<br>(C) As late as the 19th century it was considered proper that children start to work as soon as they were able to.<br>(D) It was during the 19th and early 20th centuries that ideas about childhood as a special stage of life gained influence.</p>",
                    question_hi: "<p>88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Children were often helping their families at work from the age of five or six.<br>(B) The early factory system depended on the labour of children.<br>(C) As late as the 19th century it was considered proper that children start to work as soon as they were able to.<br>(D) It was during the 19th and early 20th centuries that ideas about childhood as a special stage of life gained influence.</p>",
                    options_en: ["<p>BDCA</p>", "<p>CBAD</p>", 
                                "<p>BCAD</p>", "<p>CABD</p>"],
                    options_hi: ["<p>BDCA</p>", "<p>CBAD</p>",
                                "<p>BCAD</p>", "<p>CABD</p>"],
                    solution_en: "<p>88 (d) CABD<br>Sentence C will be the starting line as it contains the main idea of the parajumble i.e. till the 19th century, children were assumed to start work as soon as they were able to. Sentence A states that from the age of five or six they started helping their families . So, A will follow C . Further, Sentence B states that the early factory system was dependent on child labour and Sentence D states that after 19th century childhood was considered as a special stage of life . So, D will follow B . Going through the options, option (d) has the correct sequence.</p>",
                    solution_hi: "<p>88 (d) CABD<br>Sentence C प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार शामिल है यानी 19वीं शताब्दी तक यह माना जाता था कि बच्चे जैसे ही काम करने में सक्षम होते हैं वैसे ही काम शुरू कर देते हैं। Sentence A बताता है कि पांच या छह साल की उम्र से वे अपने परिवारों की मदद करना शुरू देते थे । अतः C के बाद A आएगा । इसके अलावा, Sentence B बताता है कि प्रारंभिक कारखाना प्रणाली बाल श्रम पर निर्भर थी और Sentence D कहता है कि 19वीं शताब्दी के बाद बचपन को जीवन का एक विशेष चरण माना जाता था। तो, B के बाद D आएगा । विकल्पों को देखते हुए, option (d) में सही क्रम है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "89.   Select the most appropriate option to fill in the blank.  <br />The Singapore government has deported ten Indian students for ______ the government rules. ",
                    question_hi: "89.   Select the most appropriate option to fill in the blank.  <br />The Singapore government has deported ten Indian students for ______ the government rules. ",
                    options_en: [" vandalising ", " aiding ", 
                                " verifying ", " violating "],
                    options_hi: [" vandalising ", " aiding ",
                                " verifying ", " violating "],
                    solution_en: "89.(d) violating <br />‘Violate’ means to break a rule, an agreement, etc. Similarly, the students in the given sentence break the government rules. Hence ‘violating’ is the most appropriate answer. ",
                    solution_hi: "89.(d) \'Violate\' का अर्थ है एक नियम, एक समझौता, आदि को तोड़ना। इसी तरह, दिए गए वाक्य में छात्र सरकारी नियमों को तोड़ते हैं। इसलिए \'violating\' सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br><span style=\"text-decoration: underline;\"><strong>Since setting up,</strong></span> the charity has raised a million dollars.</p>",
                    question_hi: "<p>90. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br><span style=\"text-decoration: underline;\"><strong>Since setting up,</strong></span> the charity has raised a million dollars.</p>",
                    options_en: ["<p>Since being set up</p>", "<p>From setting up</p>", 
                                "<p>Since setting</p>", "<p>No improvement required</p>"],
                    options_hi: ["<p>Since being set up</p>", "<p>From setting up</p>",
                                "<p>Since setting</p>", "<p>No improvement required</p>"],
                    solution_en: "<p>90.(a) Since being set up<br>The initial part of the given sentence is incomplete as it requires a gerund(being). Hence, &lsquo;since being set up&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>90.(a) Since being set up<br>दिए गए वाक्य का initial part अधूरा है क्योंकि इसके लिए एक gerund(being) की आवश्यकता होती है। इसलिए, \'since being set up\' सबसे उपयुक्त है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p dir=\"ltr\">91. Identify the most appropriate ANTONYM of the given word.&nbsp;</p>\n<p dir=\"ltr\">Reliable</p>",
                    question_hi: "<p>91. Identify the most appropriate ANTONYM of the given word.</p>\n<p dir=\"ltr\">Reliable</p>",
                    options_en: ["<p>Unknown</p>", "<p>Untrustworthy</p>", 
                                "<p>Uncommon</p>", "<p>Unfamiliar</p>"],
                    options_hi: ["<p>Unknown</p>", "<p>Untrustworthy</p>",
                                "<p>Uncommon</p>", "<p>Unfamiliar</p>"],
                    solution_en: "<p>91.(b) Untrustworthy&nbsp;</p>\n<p><strong>Untrustworthy-</strong> not able to be trusted.</p>\n<p dir=\"ltr\"><strong>Reliable- </strong>able to be trusted.</p>\n<p dir=\"ltr\"><strong>Unknown-</strong> not known.</p>\n<p dir=\"ltr\"><strong>Uncommon-</strong> not seen very often.</p>\n<p dir=\"ltr\"><strong>Unfamiliar-</strong> not well known.&nbsp;</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>91.(b) Untrustworthy&nbsp;</p>\n<p dir=\"ltr\"><strong>Untrustworthy -</strong> (विश्वास न करने योग्य व्यक्ति) - not able to be trusted.</p>\n<p dir=\"ltr\"><strong>Reliable -</strong> (भरोसेमंद) - able to be trusted.</p>\n<p dir=\"ltr\"><strong>Unknown -</strong> (अनजान) - not known.</p>\n<p dir=\"ltr\"><strong>Uncommon -</strong> (असामान्य) - not seen very often.</p>\n<p dir=\"ltr\"><strong>Unfamiliar -</strong> (अपरिचित) - not well known.</p>\n<p>&nbsp;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Replace the underlined word in the given sentence with the most appropriate synonym from the options listed below.<br>Morality is contextual; therefore, it is foolish to believe that one\'s moral judgement would be perfect and <span style=\"text-decoration: underline;\">immaculate.</span></p>",
                    question_hi: "<p>92. Replace the underlined word in the given sentence with the most appropriate synonym from the options listed below.<br>Morality is contextual; therefore, it is foolish to believe that one\'s moral judgement would be perfect and <span style=\"text-decoration: underline;\">immaculate.</span></p>",
                    options_en: ["<p>indelicate</p>", "<p>tarnished</p>", 
                                "<p>impeccable</p>", "<p>grubby</p>"],
                    options_hi: ["<p>indelicate</p>", "<p>tarnished</p>",
                                "<p>impeccable</p>", "<p>grubby</p>"],
                    solution_en: "<p>92.(c) <strong>Impeccable-</strong> flawless and without mistakes.<br><strong>Immaculate-</strong> perfectly clean and without flaws.<br><strong>Indelicate-</strong> lacking sensitivity and refinement.<br><strong>Tarnished-</strong> damaged in quality or appearance.<br><strong>Grubby-</strong> dirty and unclean.</p>",
                    solution_hi: "<p>92.(c) <strong>Impeccable</strong> (निर्दोष) - flawless and without mistakes.<br><strong>Immaculate</strong> (निर्मल) - perfectly clean and without flaws.<br><strong>Indelicate</strong> (असभ्य) - lacking sensitivity and refinement.<br><strong>Tarnished</strong> (कलंकित) - damaged in quality or appearance.<br><strong>Grubby</strong> (गंदा) - dirty and unclean.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "93. Select the most appropriate option to fill in the blank.  <br />Gifting is generally taken to be the easiest act to ________, if only you have the heart to part with something in your ________.",
                    question_hi: "93.   Select the most appropriate option to fill in the blank.  <br /> Gifting is generally taken to be the easiest act to ________, if only you have the heart to part with something in your ________.",
                    options_en: [" forsake, capacity", " perform, possession", 
                                " forgive, discretion", " observe, control"],
                    options_hi: [" forsake, capacity", " perform, possession",
                                " forgive, discretion", " observe, control"],
                    solution_en: "93.(b)  Perform, Possession. <br />‘Perform’ means to do a piece of work or something that you have been ordered to do & ‘possession’ means the state of having or owning something. The given sentence states that gifting is generally taken to be the easiest act to perform, if only you have the heart to part with something in your possession. Hence, ‘perform, possession’ is the most appropriate answer.",
                    solution_hi: "93.(b)  Perform, Possession. <br />‘Perform’ का अर्थ है- वह काम करना जिसे करने का आपको आदेश दिया गया हो और ‘possession’ का अर्थ है किसी चीज के होने, स्वामित्व या नियंत्रण की अवस्था या भाव। दिए गए वाक्य में कहा गया है कि उपहार देने को आमतौर पर प्रदर्शन करने के लिए सबसे आसान तरीका माना जाता है, यदि आपके पास अपनी किसी चीज से अलग होने का दिल है। इसलिए,  ‘perform, possession’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Could you pass the salt ?</p>",
                    question_hi: "<p>94. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Could you pass the salt ?</p>",
                    options_en: ["<p>Could the salt been passed?</p>", "<p>Could the salt be passed by anyone?</p>", 
                                "<p>Could the salt be past?</p>", "<p>Could the salt be passed?</p>"],
                    options_hi: ["<p>Could the salt been passed?</p>", "<p>Could the salt be passed by anyone?</p>",
                                "<p>Could the salt be past?</p>", "<p>Could the salt be passed?</p>"],
                    solution_en: "<p>94.(d) Could the salt be passed? <br>a. Could the salt <strong>been</strong> passed ? (Incorrect word)<br>b. Could the salt be passed by <strong>anyone</strong> ? (Incorrect word)<br>c. Could the salt be past? (Incorrect word used)</p>",
                    solution_hi: "<p>94.(d) Could the salt be passed? <br>a. Could the salt <strong>been</strong> passed ? (गलत शब्द)<br>b. Could the salt be passed by <strong>anyone</strong> ? (गलत शब्द)<br>c. Could the salt be past? (गलत शब्द प्रयोग)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate synonym of the word &lsquo;Abandon&rsquo; from the given sentence. <br>Ramita has decided to forsake her participation in college activities until she improved her scores.</p>",
                    question_hi: "<p>95. Select the most appropriate synonym of the word &lsquo;Abandon&rsquo; from the given sentence. <br>Ramita has decided to forsake her participation in college activities until she improved her scores.</p>",
                    options_en: ["<p>improved</p>", "<p>forsake</p>", 
                                "<p>decided</p>", "<p>participation</p>"],
                    options_hi: ["<p>improved</p>", "<p>forsake</p>",
                                "<p>decided</p>", "<p>participation</p>"],
                    solution_en: "<p>95.(b) <strong>Forsake-</strong> to give up or leave someone.<br><strong>Abandon-</strong> to leave a place, thing, or person, usually for ever.<br><strong>Improved-</strong> having become better<br><strong>Decided-</strong> clear and definite<br><strong>Participation-</strong> the action of taking part in something.</p>",
                    solution_hi: "<p>95.(b) <strong>Forsake</strong> (छोड़ देना) - to give up or leave someone.<br><strong>Abandon</strong> (त्यागना) - to leave a place, thing, or person, usually for ever.<br><strong>Improved</strong> (बेहतर करना) - having become better<br><strong>Decided</strong> (निर्णय लिया) - clear and definite<br><strong>Participation</strong> (भागीदारी) - the action of taking part in something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:-</strong> <br>Claims have been made that vaccines are responsible for certain (96)__________ health conditions, particularly autism, speech disorders and inflammatory bowel disease. Some of those claims focused on thimerosal, a mercury-containing compound used as a preservative in vaccines. Some people believed that autism (97)__________ a form of mercury poisoning, caused specifically by thimerosal in childhood vaccines. Those claims have been discredited. Still, misinformation and fear generated by false claims about associations (98)__________ autism and vaccines had a significant impact on individuals&rsquo; perceptions about vaccine safety. (99)__________, most individuals in countries where vaccination is widespread have never personally experienced vaccine-preventable disease. Thus, the focus of concern for some people shifted from the negative effects of vaccine-preventable disease to the possible negative effects of the vaccines (100)__________.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:-</strong> <br>Claims have been made that vaccines are responsible for certain (96)__________ health conditions, particularly autism, speech disorders and inflammatory bowel disease. Some of those claims focused on thimerosal, a mercury-containing compound used as a preservative in vaccines. Some people believed that autism (97)__________ a form of mercury poisoning, caused specifically by thimerosal in childhood vaccines. Those claims have been discredited. Still, misinformation and fear generated by false claims about associations (98)__________ autism and vaccines had a significant impact on individuals&rsquo; perceptions about vaccine safety. (99)__________, most individuals in countries where vaccination is widespread have never personally experienced vaccine-preventable disease. Thus, the focus of concern for some people shifted from the negative effects of vaccine-preventable disease to the possible negative effects of the vaccines (100)__________.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    options_en: ["<p>contemptuous</p>", "<p>adverse</p>", 
                                "<p>affirm</p>", "<p>disperse</p>"],
                    options_hi: ["<p>contemptuous</p>", "<p>adverse</p>",
                                "<p>affirm</p>", "<p>disperse</p>"],
                    solution_en: "<p>96.(b) &lsquo;Adverse&rsquo; means making something difficult for somebody. The given passage states that claims have been made that vaccines are responsible for certain adverse health conditions, particularly autism, speech disorders and inflammatory bowel disease. Hence, &lsquo;adverse&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(b) \'Adverse\' का अर्थ है प्रतिकूल । दिए गए passage में कहा गया है कि टीके(vaccines) कुछ प्रतिकूल स्वास्थ्य स्थितियों, विशेष रूप से (autism), भाषण विकार(speech disorders) और सूजन आंत्र रोग(inflammatory bowel disease) के लिए जिम्मेदार हैं। इसलिए, \'adverse\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:-</strong> <br>Claims have been made that vaccines are responsible for certain (96)__________ health conditions, particularly autism, speech disorders and inflammatory bowel disease. Some of those claims focused on thimerosal, a mercury-containing compound used as a preservative in vaccines. Some people believed that autism (97)__________ a form of mercury poisoning, caused specifically by thimerosal in childhood vaccines. Those claims have been discredited. Still, misinformation and fear generated by false claims about associations (98)__________ autism and vaccines had a significant impact on individuals&rsquo; perceptions about vaccine safety. (99)__________, most individuals in countries where vaccination is widespread have never personally experienced vaccine-preventable disease. Thus, the focus of concern for some people shifted from the negative effects of vaccine-preventable disease to the possible negative effects of the vaccines (100)__________.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    question_hi: "<p>97.<strong> Cloze Test:-</strong> <br>Claims have been made that vaccines are responsible for certain (96)__________ health conditions, particularly autism, speech disorders and inflammatory bowel disease. Some of those claims focused on thimerosal, a mercury-containing compound used as a preservative in vaccines. Some people believed that autism (97)__________ a form of mercury poisoning, caused specifically by thimerosal in childhood vaccines. Those claims have been discredited. Still, misinformation and fear generated by false claims about associations (98)__________ autism and vaccines had a significant impact on individuals&rsquo; perceptions about vaccine safety. (99)__________, most individuals in countries where vaccination is widespread have never personally experienced vaccine-preventable disease. Thus, the focus of concern for some people shifted from the negative effects of vaccine-preventable disease to the possible negative effects of the vaccines (100)__________.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    options_en: ["<p>will</p>", "<p>shall</p>", 
                                "<p>was</p>", "<p>were</p>"],
                    options_hi: ["<p>will</p>", "<p>shall</p>",
                                "<p>was</p>", "<p>were</p>"],
                    solution_en: "<p>97.(c) &lsquo;Autism&rsquo; is a singular subject so it will take &lsquo;was&rsquo; as a singular verb. Hence, &lsquo;was&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(c)\'Autism\' एकवचन है, इसलिए इसके साथ \'was\' एकवचन क्रिया लगेगी । अतः \'was\' सर्वाधिक उपयुक्त उत्तर है I.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:-</strong> <br>Claims have been made that vaccines are responsible for certain (96)__________ health conditions, particularly autism, speech disorders and inflammatory bowel disease. Some of those claims focused on thimerosal, a mercury-containing compound used as a preservative in vaccines. Some people believed that autism (97)__________ a form of mercury poisoning, caused specifically by thimerosal in childhood vaccines. Those claims have been discredited. Still, misinformation and fear generated by false claims about associations (98)__________ autism and vaccines had a significant impact on individuals&rsquo; perceptions about vaccine safety. (99)__________, most individuals in countries where vaccination is widespread have never personally experienced vaccine-preventable disease. Thus, the focus of concern for some people shifted from the negative effects of vaccine-preventable disease to the possible negative effects of the vaccines (100)__________.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:-</strong> <br>Claims have been made that vaccines are responsible for certain (96)__________ health conditions, particularly autism, speech disorders and inflammatory bowel disease. Some of those claims focused on thimerosal, a mercury-containing compound used as a preservative in vaccines. Some people believed that autism (97)__________ a form of mercury poisoning, caused specifically by thimerosal in childhood vaccines. Those claims have been discredited. Still, misinformation and fear generated by false claims about associations (98)__________ autism and vaccines had a significant impact on individuals&rsquo; perceptions about vaccine safety. (99)__________, most individuals in countries where vaccination is widespread have never personally experienced vaccine-preventable disease. Thus, the focus of concern for some people shifted from the negative effects of vaccine-preventable disease to the possible negative effects of the vaccines (100)__________.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    options_en: ["<p>one another</p>", "<p>between</p>", 
                                "<p>each other</p>", "<p>through</p>"],
                    options_hi: ["<p>one another</p>", "<p>between</p>",
                                "<p>each other</p>", "<p>through</p>"],
                    solution_en: "<p>98.(b) &lsquo;Between&rsquo; means in the space in the middle of two things, people, places, etc. The given passage states that misinformation and fear are generated by false claims about associations between autism and vaccines. Hence, &lsquo;between&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(b) \'Between\' का अर्थ है दो चीजों, लोगों, स्थानों आदि &lsquo;के बीच में&rsquo;। दिए गए passage में कहा गया है कि autism और टीकों(vaccines) के बीच संबंधों के झूठे दावों से गलत सूचना और भय उत्पन्न होता है। इसलिए, \'between\' सबसे उपयुक्त उत्तर है।.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:-</strong> <br>Claims have been made that vaccines are responsible for certain (96)__________ health conditions, particularly autism, speech disorders and inflammatory bowel disease. Some of those claims focused on thimerosal, a mercury-containing compound used as a preservative in vaccines. Some people believed that autism (97)__________ a form of mercury poisoning, caused specifically by thimerosal in childhood vaccines. Those claims have been discredited. Still, misinformation and fear generated by false claims about associations (98)__________ autism and vaccines had a significant impact on individuals&rsquo; perceptions about vaccine safety. (99)__________, most individuals in countries where vaccination is widespread have never personally experienced vaccine-preventable disease. Thus, the focus of concern for some people shifted from the negative effects of vaccine-preventable disease to the possible negative effects of the vaccines (100)__________.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:-</strong> <br>Claims have been made that vaccines are responsible for certain (96)__________ health conditions, particularly autism, speech disorders and inflammatory bowel disease. Some of those claims focused on thimerosal, a mercury-containing compound used as a preservative in vaccines. Some people believed that autism (97)__________ a form of mercury poisoning, caused specifically by thimerosal in childhood vaccines. Those claims have been discredited. Still, misinformation and fear generated by false claims about associations (98)__________ autism and vaccines had a significant impact on individuals&rsquo; perceptions about vaccine safety. (99)__________, most individuals in countries where vaccination is widespread have never personally experienced vaccine-preventable disease. Thus, the focus of concern for some people shifted from the negative effects of vaccine-preventable disease to the possible negative effects of the vaccines (100)__________.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    options_en: ["<p>For instance</p>", "<p>Therefore</p>", 
                                "<p>In addition</p>", "<p>Likewise</p>"],
                    options_hi: ["<p>For instance</p>", "<p>Therefore</p>",
                                "<p>In addition</p>", "<p>Likewise</p>"],
                    solution_en: "<p>99.(a) &lsquo;For instance&rsquo; means for an example. The given passage states that for instance, most individuals in countries where vaccination is widespread have never personally experienced the vaccine-preventable disease. Hence, &lsquo;For instance&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a) \'For instance\' का अर्थ &lsquo;उदाहरण के लिए&rsquo; है। दिए गए passage में कहा गया है कि उदाहरण के लिए, जिन देशों में टीकाकरण व्यापक है, वहां अधिकांश व्यक्तियों ने व्यक्तिगत रूप से कभी भी टीका-रोकथाम योग्य बीमारी का अनुभव नहीं किया है। इसलिए, \'for instance\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:-</strong> <br>Claims have been made that vaccines are responsible for certain (96)__________ health conditions, particularly autism, speech disorders and inflammatory bowel disease. Some of those claims focused on thimerosal, a mercury-containing compound used as a preservative in vaccines. Some people believed that autism (97)__________ a form of mercury poisoning, caused specifically by thimerosal in childhood vaccines. Those claims have been discredited. Still, misinformation and fear generated by false claims about associations (98)__________ autism and vaccines had a significant impact on individuals&rsquo; perceptions about vaccine safety. (99)__________, most individuals in countries where vaccination is widespread have never personally experienced vaccine-preventable disease. Thus, the focus of concern for some people shifted from the negative effects of vaccine-preventable disease to the possible negative effects of the vaccines (100)__________.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:-</strong> <br>Claims have been made that vaccines are responsible for certain (96)__________ health conditions, particularly autism, speech disorders and inflammatory bowel disease. Some of those claims focused on thimerosal, a mercury-containing compound used as a preservative in vaccines. Some people believed that autism (97)__________ a form of mercury poisoning, caused specifically by thimerosal in childhood vaccines. Those claims have been discredited. Still, misinformation and fear generated by false claims about associations (98)__________ autism and vaccines had a significant impact on individuals&rsquo; perceptions about vaccine safety. (99)__________, most individuals in countries where vaccination is widespread have never personally experienced vaccine-preventable disease. Thus, the focus of concern for some people shifted from the negative effects of vaccine-preventable disease to the possible negative effects of the vaccines (100)__________.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    options_en: ["<p>themselves</p>", "<p>yourself</p>", 
                                "<p>ourselves</p>", "<p>itself</p>"],
                    options_hi: ["<p>themselves</p>", "<p>yourself</p>",
                                "<p>ourselves</p>", "<p>itself</p>"],
                    solution_en: "<p>100.(a) &lsquo;Themselves&rsquo; is used when the people or things who do an action are also affected by it. The given passage states that the focus of concern for some people shifted from the negative effects of the vaccine-preventable disease to the possible negative effects of the vaccines themselves. Hence, &lsquo;themselves&rsquo; is the most approp riate answer.</p>",
                    solution_hi: "<p>100.(a) \'Themselves\' का प्रयोग तब किया जाता है जब कोई कार्य करने वाले लोग या वस्तुएँ भी इससे प्रभावित होती हैं। दिए गए passage में कहा गया है कि कुछ लोगों के लिए चिंता का कारण टीका-रोकथाम बीमारी के नकारात्मक प्रभावों से हटकर टीकों के संभावित नकारात्मक प्रभावों पर स्थानांतरित हो गया है। इसलिए, \'themselves\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>